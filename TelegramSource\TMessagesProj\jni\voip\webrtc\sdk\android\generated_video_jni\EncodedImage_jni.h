// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/EncodedImage

#ifndef org_webrtc_EncodedImage_JNI
#define org_webrtc_EncodedImage_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_EncodedImage[];
const char kClassPath_org_webrtc_EncodedImage[] = "org/webrtc/EncodedImage";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_EncodedImage_00024FrameType[];
const char kClassPath_org_webrtc_EncodedImage_00024FrameType[] =
    "org/webrtc/EncodedImage$FrameType";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_EncodedImage_clazz(nullptr);
#ifndef org_webrtc_EncodedImage_clazz_defined
#define org_webrtc_EncodedImage_clazz_defined
inline jclass org_webrtc_EncodedImage_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_EncodedImage,
      &g_org_webrtc_EncodedImage_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_EncodedImage_00024FrameType_clazz(nullptr);
#ifndef org_webrtc_EncodedImage_00024FrameType_clazz_defined
#define org_webrtc_EncodedImage_00024FrameType_clazz_defined
inline jclass org_webrtc_EncodedImage_00024FrameType_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_EncodedImage_00024FrameType,
      &g_org_webrtc_EncodedImage_00024FrameType_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_EncodedImage_Constructor8(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_EncodedImage_Constructor(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& buffer,
    const jni_zero::JavaRef<jobject>& releaseCallback,
    JniIntWrapper encodedWidth,
    JniIntWrapper encodedHeight,
    jlong captureTimeNs,
    const jni_zero::JavaRef<jobject>& frameType,
    JniIntWrapper rotation,
    const jni_zero::JavaRef<jobject>& qp) {
  jclass clazz = org_webrtc_EncodedImage_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_EncodedImage_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
"(Ljava/nio/ByteBuffer;Ljava/lang/Runnable;IIJLorg/webrtc/EncodedImage$FrameType;ILjava/lang/Integer;)V",
          &g_org_webrtc_EncodedImage_Constructor8);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, buffer.obj(), releaseCallback.obj(), as_jint(encodedWidth),
              as_jint(encodedHeight), captureTimeNs, frameType.obj(), as_jint(rotation), qp.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_EncodedImage_getBuffer0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_EncodedImage_getBuffer(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_EncodedImage_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_EncodedImage_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getBuffer",
          "()Ljava/nio/ByteBuffer;",
          &g_org_webrtc_EncodedImage_getBuffer0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_EncodedImage_getCaptureTimeNs0(nullptr);
static jlong Java_EncodedImage_getCaptureTimeNs(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj)
    {
  jclass clazz = org_webrtc_EncodedImage_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_EncodedImage_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getCaptureTimeNs",
          "()J",
          &g_org_webrtc_EncodedImage_getCaptureTimeNs0);

  jlong ret =
      env->CallLongMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_org_webrtc_EncodedImage_getEncodedHeight0(nullptr);
static jint Java_EncodedImage_getEncodedHeight(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_EncodedImage_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_EncodedImage_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getEncodedHeight",
          "()I",
          &g_org_webrtc_EncodedImage_getEncodedHeight0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_org_webrtc_EncodedImage_getEncodedWidth0(nullptr);
static jint Java_EncodedImage_getEncodedWidth(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_EncodedImage_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_EncodedImage_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getEncodedWidth",
          "()I",
          &g_org_webrtc_EncodedImage_getEncodedWidth0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_org_webrtc_EncodedImage_getFrameType0(nullptr);
static jint Java_EncodedImage_getFrameType(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_EncodedImage_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_EncodedImage_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getFrameType",
          "()I",
          &g_org_webrtc_EncodedImage_getFrameType0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_org_webrtc_EncodedImage_getQp0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_EncodedImage_getQp(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_EncodedImage_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_EncodedImage_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getQp",
          "()Ljava/lang/Integer;",
          &g_org_webrtc_EncodedImage_getQp0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_EncodedImage_getRotation0(nullptr);
static jint Java_EncodedImage_getRotation(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_EncodedImage_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_EncodedImage_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getRotation",
          "()I",
          &g_org_webrtc_EncodedImage_getRotation0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_org_webrtc_EncodedImage_00024FrameType_fromNativeIndex1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_FrameType_fromNativeIndex(JNIEnv* env,
    JniIntWrapper nativeIndex) {
  jclass clazz = org_webrtc_EncodedImage_00024FrameType_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_EncodedImage_00024FrameType_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "fromNativeIndex",
          "(I)Lorg/webrtc/EncodedImage$FrameType;",
          &g_org_webrtc_EncodedImage_00024FrameType_fromNativeIndex1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(nativeIndex));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_EncodedImage_JNI
