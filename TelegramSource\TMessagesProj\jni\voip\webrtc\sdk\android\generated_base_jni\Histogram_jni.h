// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/Histogram

#ifndef org_webrtc_Histogram_JNI
#define org_webrtc_Histogram_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_Histogram[];
const char kClassPath_org_webrtc_Histogram[] = "org/webrtc/Histogram";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_Histogram_clazz(nullptr);
#ifndef org_webrtc_Histogram_clazz_defined
#define org_webrtc_Histogram_clazz_defined
inline jclass org_webrtc_Histogram_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_Histogram,
      &g_org_webrtc_Histogram_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {

static void JNI_Histogram_AddSample(JNIEnv* env, jlong handle,
    jint sample);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_Histogram_nativeAddSample(
    JNIEnv* env,
    jclass jcaller,
    jlong handle,
    jint sample) {
  return JNI_Histogram_AddSample(env, handle, sample);
}

static jlong JNI_Histogram_CreateCounts(JNIEnv* env, const jni_zero::JavaParamRef<jstring>& name,
    jint min,
    jint max,
    jint bucketCount);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_Histogram_nativeCreateCounts(
    JNIEnv* env,
    jclass jcaller,
    jstring name,
    jint min,
    jint max,
    jint bucketCount) {
  return JNI_Histogram_CreateCounts(env, jni_zero::JavaParamRef<jstring>(env, name), min, max,
      bucketCount);
}

static jlong JNI_Histogram_CreateEnumeration(JNIEnv* env, const jni_zero::JavaParamRef<jstring>&
    name,
    jint max);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_Histogram_nativeCreateEnumeration(
    JNIEnv* env,
    jclass jcaller,
    jstring name,
    jint max) {
  return JNI_Histogram_CreateEnumeration(env, jni_zero::JavaParamRef<jstring>(env, name), max);
}


}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_Histogram_JNI
