// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/JNILogging

#ifndef org_webrtc_JNILogging_JNI
#define org_webrtc_JNILogging_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_JNILogging[];
const char kClassPath_org_webrtc_JNILogging[] = "org/webrtc/JNILogging";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_JNILogging_clazz(nullptr);
#ifndef org_webrtc_JNILogging_clazz_defined
#define org_webrtc_JNILogging_clazz_defined
inline jclass org_webrtc_JNILogging_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_JNILogging,
      &g_org_webrtc_JNILogging_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.

static std::atomic<jmethodID> g_org_webrtc_JNILogging_logToInjectable3(nullptr);
static void Java_JNILogging_logToInjectable(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jstring>& message,
    const jni_zero::JavaRef<jobject>& severity,
    const jni_zero::JavaRef<jstring>& tag) {
  jclass clazz = org_webrtc_JNILogging_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_JNILogging_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "logToInjectable",
          "(Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/String;)V",
          &g_org_webrtc_JNILogging_logToInjectable3);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, message.obj(), severity.obj(), tag.obj());
}

#endif  // org_webrtc_JNILogging_JNI
