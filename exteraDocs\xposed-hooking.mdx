---
title: Xposed Method Hooking
description: Xposed method hooking to intercept and modify app behavior in your plugins.
icon: Webhook
---

## Introduction

Xposed method hooking allows your plugin to intercept calls to methods (or constructors) within the application, modify their parameters, change their behavior, or replace their implementation entirely. This is a powerful technique for altering app functionality at a low level.

## Basic Concepts

### Method Hooks

A method hook intercepts a specific method or constructor call and allows you to execute your own Python code at different stages of its execution:

- **Before Hook**: Executes your code *before* the original method/constructor is called. You can inspect and modify the arguments.
- **After Hook**: Executes your code *after* the original method/constructor has completed. You can inspect the arguments, the original instance, and the result (or modify the result).
- **Replace Hook (Method Replacement)**: Completely replaces the original method/constructor with your Python implementation. You are responsible for providing any return value.

### Hook Handlers (Callback Classes)

To define the logic for your hook, you create a Python class with specific callback methods. The plugin system will instantiate this class and call its methods at the appropriate times. The primary callback methods are:

- `before_hooked_method(self, param: XC_MethodHook.MethodHookParam)`
- `after_hooked_method(self, param: XC_MethodHook.MethodHookParam)`
- `replace_hooked_method(self, param: XC_MethodHook.MethodHookParam) -> Any`

The `param` object (an instance of `de.robv.android.xposed.XC_MethodHook.MethodHookParam`) provides access to:
- `param.thisObject`: The instance on which the method was called (or `None` for static methods or constructors during `before_hooked_method` before superclass constructor is called).
- `param.args`: A Python list-like object representing the arguments passed to the method. You can read and modify these (modifications in `before_hooked_method` affect the original call).
- `param.result`: The value returned by the original method. Available in `after_hooked_method`. You can read and modify this.
- `param.method`: The `java.lang.reflect.Member` object representing the hooked method or constructor.

You can also set `param.returnEarly = True` in `before_hooked_method` to skip the original method call and `after_hooked_method`, and immediately return a value set via `param.result`.

## Using Method Hooks

### 1. Finding Methods or Constructors to Hook

First, you need a reference to the `java.lang.reflect.Method` or `java.lang.reflect.Constructor` object you want to hook. This is typically done using Java reflection:

```python
from java import jclass, jint, jboolean # Import common Java types
from java.lang import String as JString # Example for Java String type

# Find a class
try:
    # Example: org.telegram.ui.ActionBar.ActionBar
    SomeClass = jclass("org.telegram.ui.ActionBar.ActionBar")
except Exception as e:
    self.log(f"Failed to find class: {e}")
    # Handle error: class not found, cannot proceed with hooking

# Find a method
# For a method like: public void setTitle(CharSequence title)
try:
    # CharSequence is an interface, often java.lang.String is accepted
    # Or find the exact CharSequence class: jclass("java.lang.CharSequence")
    method_to_hook = SomeClass.getDeclaredMethod("setTitle", jclass("java.lang.CharSequence"))
    method_to_hook.setAccessible(True) # Important for non-public methods
except Exception as e:
    self.log(f"Failed to find method 'setTitle': {e}")

# Find a constructor
# For a constructor like: public ActionBar(Context context)
try:
    ContextClass = jclass("android.content.Context")
    constructor_to_hook = SomeClass.getDeclaredConstructor(ContextClass)
    constructor_to_hook.setAccessible(True) # Important for non-public constructors
except Exception as e:
    self.log(f"Failed to find constructor: {e}")

# Parameter types for getDeclaredMethod/getDeclaredConstructor:
# - For primitive types: Integer.TYPE (for int), Boolean.TYPE (for boolean), etc.
#   In Python Chaquopy: jint, jboolean, jlong, jfloat, jdouble, jbyte, jshort, jchar
# - For object types: The class object itself (e.g., jclass("java.lang.String") or JString)
```

<Callout type="info" title="Using hook_utils">
The `hook_utils.py` file provides helpers like `find_class`, `get_private_field`, `set_private_field`, etc.
- `find_class(class_name)` is a safe way to get a Java class object (returns `None` if not found).
- Functions like `get_private_field` are useful for accessing or modifying the *values of fields (variables)* within a class or object, often used *inside* your hook logic or elsewhere. They are **not** used to obtain the `Method` or `Constructor` object needed for `self.hook_method`.
</Callout>

```python
# Example using hook_utils to find a class
from hook_utils import find_class
SecureRandomClass = find_class("java.security.SecureRandom")
if SecureRandomClass:
    # Now you can use SecureRandomClass.getDeclaredMethod(...) etc.
    pass
```

### 2. Creating a Hook Handler Class

This class will contain your hook logic. You only need to implement the methods relevant to your hook type.

```python
class MyHookHandler:
    def __init__(self, plugin_instance):
        self.plugin = plugin_instance # Optional: pass your plugin instance for logging or settings access

    # Called if you intend to run code BEFORE the original method
    def before_hooked_method(self, param):
        self.plugin.log(f"Before {param.method.getName()} called with args: {list(param.args)}")
        # Example: Modify an argument if it's a string
        # if len(param.args) > 0 and isinstance(param.args[0], str):
        #    param.args[0] = "Modified: " + param.args[0]

    # Called if you intend to run code AFTER the original method
    def after_hooked_method(self, param):
        self.plugin.log(f"After {param.method.getName()} completed. Result: {param.result}")
        # Example: Change the result
        # if isinstance(param.result, bool):
        #    param.result = not param.result # Invert boolean result

    # Called if you intend to REPLACE the original method
    def replace_hooked_method(self, param):
        self.plugin.log(f"Replacing {param.method.getName()}. Original call skipped.")
        return "Replaced!" # Example for a method returning String
```

### 3. Hooking the Method/Constructor

Use the `self.hook_method()` from your `BasePlugin` instance.

```python
# To hook with before/after logic:
handler_instance = MyHookHandler(self)
unhook_object = self.hook_method(method_to_hook, handler_instance, priority=10)

# To hook with replacement logic:
replacer_instance = MyReplacementHandler(self)
unhook_object = self.hook_method(method_to_hook, replacer_instance)

# 'unhook_object' is an XC_MethodHook.Unhook instance.
# Hooks are automatically unregistered when your plugin is unloaded.
# You can manually unhook earlier if needed:
if unhook_object:
  self.unhook_method(unhook_object)
```
- `method_or_constructor`: The `java.lang.reflect.Member` object to hook.
- `xposed_hook`: An instance of your hook handler class.
- `priority`: Optional integer. Hooks with higher priority are executed closer to the original method call (for `before_hooked_method`) or further away (for `after_hooked_method`).

## Examples

### Example 1: Modifying Method Parameters

This example modifies the flags parameter to remove the FLAG_SECURE flag:

```python
class Remove:
    def before_hooked_method(self, param):
        flags_arg_index = 0
        mask_arg_index = 1

        original_flags = int(param.args[flags_arg_index])
        original_mask = int(param.args[mask_arg_index])

        if (original_mask & FLAG_SECURE) != 0:
            modified_flags = original_flags & ~FLAG_SECURE
            param.args[flags_arg_index] = Integer(modified_flags)

        param.args[mask_arg_index] = Integer(original_mask)

self.hook_method(Window.getClass().getDeclaredMethod("setFlags", Integer.TYPE, Integer.TYPE), Remove())
```

### Example 2: Replacing Method Implementation

This example completely replaces a method with an empty implementation:

```python
class Remove:
    def replace_hooked_method(self, param):
        return  # Method will do nothing

self.hook_method(FlagSecureReason.getClass().getDeclaredMethod("attach"), Remove())
```

<Callout type="warn" title="Return Values in Replacement">
When using `replace_hooked_method`, your Python method becomes the *entire* implementation. You are responsible for returning a value that is compatible with the original Java method's return type.
- For `void` Java methods, `return` or `return None`.
- For Java methods returning primitive types (int, boolean, etc.), return a Python value that can be converted (e.g., Python `int` for Java `int`, Python `bool` for Java `boolean`).
- For Java methods returning object types, return a suitable Python object (which will be bridged to Java) or `None` (which becomes `null` in Java).
</Callout>
