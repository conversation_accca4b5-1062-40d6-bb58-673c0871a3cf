#канал разраба: https://t.me/DevPluginsEUG
#канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG #канал разраба: https://t.me/DevPluginsEUG


from base_plugin import BasePlugin, MenuItemData, MenuItemType
from ui.alert import AlertDialogBuilder
from client_utils import get_last_fragment
from android_utils import run_on_ui_thread
from hook_utils import find_class
from java import dynamic_proxy

TextView = find_class("android.widget.TextView")
GridLayout = find_class("android.widget.GridLayout")
LinearLayout = find_class("android.widget.LinearLayout")
EditText = find_class("android.widget.EditText")
Button = find_class("android.widget.Button")
Gravity = find_class("android.view.Gravity")
OnClickListenerInterface = find_class("android.view.View$OnClickListener")
GridLayoutLayoutParams = find_class("android.widget.GridLayout$LayoutParams")
AndroidUtilities = find_class("org.telegram.messenger.AndroidUtilities")

__id__ = "calculator with UI interface"
__name__ = "calculator with UI interface"
__description__ = "calculator with UI interface"
__author__ = "@SaturnFake"
__version__ = "1.1.5"
__min_version__ = "11.12.0"
__icon__ = "msg_mini_calculator"

class CalcButtonClickListener(dynamic_proxy(OnClickListenerInterface)):
    def __init__(self, plugin, text_view, button_text):
        super().__init__()
        self.plugin = plugin
        self.text_view = text_view
        self.button_text = button_text

    def onClick(self, view):
        self.plugin.on_button_click(self.text_view, self.button_text)


class CalcPlugin(BasePlugin):
    def __init__(self):
        super().__init__()

    def on_plugin_load(self):
        self.add_menu_item(MenuItemData(
            menu_type=MenuItemType.CHAT_ACTION_MENU,
            text="Калькулятор",
            icon="msg_mini_calculator",
            on_click=self.open_calculator
        ))

    def open_calculator(self, context):
        chat_id = context.get("dialog_id") or context.get("chatId")
        run_on_ui_thread(lambda: self.show_calculator_dialog(chat_id))

    def show_calculator_dialog(self, chat_id):
        fragment = get_last_fragment()
        activity = fragment and fragment.getParentActivity()
        if not activity:
            return

        builder = AlertDialogBuilder(activity)
        builder.set_title("Калькулятор")

        main_layout = LinearLayout(activity)
        main_layout.setOrientation(LinearLayout.VERTICAL)

        text_view = EditText(activity)
        text_view.setHint("0")
        text_view.setTextSize(24)
        text_view.setGravity(Gravity.END)
        text_view.setInputType(2)
        text_view.setFocusable(True); text_view.setFocusableInTouchMode(True);
        main_layout.addView(text_view)

        button_layout = GridLayout(activity)
        button_layout.setColumnCount(4)

        buttons = [
            "1", "2", "3", "/",
            "4", "5", "6", "*",
            "7", "8", "9", "-",
            "0", ".", "=", "+"
        ]

        row = 0
        col = 0

        button_size = AndroidUtilities.dp(72)
        margin = AndroidUtilities.dp(2)

        for button_text in buttons:
            button = Button(activity)
            button.setText(button_text)
            button.setTextSize(20)
            button.setOnClickListener(CalcButtonClickListener(self, text_view, button_text))

            params = GridLayoutLayoutParams()
            params.width = button_size
            params.height = button_size
            params.setMargins(margin, margin, margin, margin)
            params.rowSpec = GridLayout.spec(row)
            params.columnSpec = GridLayout.spec(col)
            button.setLayoutParams(params)

            button_layout.addView(button)

            col += 1
            if col > 3:
                col = 0
                row += 1

        button_c = Button(activity)
        button_c.setText("C")
        button_c.setTextSize(20)
        button_c.setOnClickListener(CalcButtonClickListener(self, text_view, "C"))

        params_c = GridLayoutLayoutParams()
        params_c.width = button_size * 2 + margin * 2
        params_c.height = button_size
        params_c.setMargins(margin, margin, margin, margin)
        params_c.rowSpec = GridLayout.spec(row + 1)
        params_c.columnSpec = GridLayout.spec(0, 2)
        button_c.setLayoutParams(params_c)
        button_layout.addView(button_c)

        button_backspace = Button(activity)
        button_backspace.setText("<-")
        button_backspace.setTextSize(20)
        button_backspace.setOnClickListener(CalcButtonClickListener(self, text_view, "<-"))

        params_backspace = GridLayoutLayoutParams()
        params_backspace.width = button_size * 2 + margin * 2
        params_backspace.height = button_size
        params_backspace.setMargins(margin, margin, margin, margin)
        params_backspace.rowSpec = GridLayout.spec(row + 1)
        params_backspace.columnSpec = GridLayout.spec(2, 2)
        button_backspace.setLayoutParams(params_backspace)
        button_layout.addView(button_backspace)

        main_layout.addView(button_layout)

        builder.set_view(main_layout)
        builder.set_negative_button("Закрыть", lambda b, w: b.dismiss())

        builder.show()

    def on_button_click(self, text_view, button_text):
        current_text = text_view.getText().toString()

        if button_text == "=":
            try:
                result = str(eval(current_text))
                text_view.setText(result)
            except Exception as e:
                text_view.setText("Ошибка")
        elif button_text == "C":
            text_view.setText("")
        elif button_text == "<-":
            if len(current_text) > 0:
                new_text = current_text[:-1]
                text_view.setText(new_text)
        else:
            new_text = current_text + button_text
            text_view.setText(new_text)
