{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 458, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "MissingZero": "Some implementations of ECDSA and DSA incorrectly encode r and s by not including leading zeros in the ASN encoding of integers when necessary. Hence, some implementations (e.g. jdk) allow signatures with incorrect ASN encodings assuming that the signature is otherwise valid.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5", "wx": "00eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7", "wy": "00eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE6tqTvhCyRJ4ei7WDBdUgCAE8VxB8GiCj\nF6bLp+ymcjQMA9HS4JZjKGaR31UGn6JUkMndn5wLsrU=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "303d021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021c352507aabd0f9bc223e1ac97a4ccb33b9de8ad3df447037367aa413b", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Legacy:ASN encoding of r misses leading 0", "msg": "313233343030", "sig": "303d021cfba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 3, "comment": "Legacy:ASN encoding of s misses leading 0", "msg": "313233343030", "sig": "303d021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021ccadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 4, "comment": "valid", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "valid", "flags": []}, {"tcId": 5, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "30813e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": ["BER"]}, {"tcId": 6, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "3082003e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": ["BER"]}, {"tcId": 7, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "303f021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "303d021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "3085010000003e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "308901000000000000003e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3080021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "303e028000fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264028000cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "3040021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9020000", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "30400000021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9020000", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "3040021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9020500", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "3043498177303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "30422500303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "3040303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9020004deadbeef", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "30432222498177021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "304222212500021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "3046221f021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52640004deadbeef021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "3043021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52642222498177021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including garbage", "msg": "313233343030", "sig": "3042021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa526422212500021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including garbage", "msg": "313233343030", "sig": "3046021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264221f021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9020004deadbeef", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "3046aa00bb00cd00303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "3044aa02aabb303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "30462225aa00bb00cd00021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "30442223aa02aabb021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "including undefined tags", "msg": "313233343030", "sig": "3046021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52642225aa00bb00cd00021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "including undefined tags", "msg": "313233343030", "sig": "3044021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52642223aa02aabb021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3080303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9020000", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30422280021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52640000021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3042021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52642280021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9020000", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3080313e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9020000", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30422280031d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52640000021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3042021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52642280031d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9020000", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e3e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f3e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "313e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "323e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff3e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "using composition for sequence", "msg": "313233343030", "sig": "3042300102303d1d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "truncated sequence", "msg": "313233343030", "sig": "303d021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "truncated sequence", "msg": "313233343030", "sig": "303d1d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 57, "comment": "indefinite length", "msg": "313233343030", "sig": "3080021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9020000", "result": "invalid", "flags": ["BER"]}, {"tcId": 58, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "3080021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e90200", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "3080021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e90205000000", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "3080021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902060811220000", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "3080021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9020000fe02beef", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "3080021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9020002beef", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "30403000021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "append empty sequence", "msg": "313233343030", "sig": "3040021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9023000", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "3041021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902bf7f00", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "sequence of sequence", "msg": "313233343030", "sig": "3040303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "301f021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "305d021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 69, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "303f02811d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "303f021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa526402811d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "30400282001d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": ["BER"]}, {"tcId": 72, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "3040021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52640282001d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": ["BER"]}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303e021e00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303e021c00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021e00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021c00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "30430285010000001d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "3043021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52640285010000001d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3047028901000000000000001d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3047021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264028901000000000000001d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304202847fffffff00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "3042021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa526402847fffffff00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30420284ffffffff00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "3042021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52640284ffffffff00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "30430285ffffffffff00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "3043021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52640285ffffffffff00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "30460288ffffffffffffffff00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "3046021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52640288ffffffffffffffff00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "303e02ff00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa526402ff00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "removing integer", "msg": "313233343030", "sig": "301f021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "lonely integer tag", "msg": "313233343030", "sig": "302002021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "lonely integer tag", "msg": "313233343030", "sig": "3020021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa526402", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3040021f00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52640000021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3040021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021f00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9020000", "result": "invalid", "flags": []}, {"tcId": 96, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "3040021f000000fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": ["BER"]}, {"tcId": 97, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "3040021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021f000000cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": ["BER"]}, {"tcId": 98, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "3040021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52640000021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3040021f00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52640500021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3040021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021f00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9020500", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "truncated length of integer", "msg": "313233343030", "sig": "30210281021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "truncated length of integer", "msg": "313233343030", "sig": "3021021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52640281", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "30210500021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "3021021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52640500", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e001d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e011d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e031d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e041d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303eff1d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264001d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264011d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264031d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264041d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264ff1d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "dropping value of integer", "msg": "313233343030", "sig": "30210200021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "dropping value of integer", "msg": "313233343030", "sig": "3021021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52640200", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "using composition for integer", "msg": "313233343030", "sig": "30422221020100021cfba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "using composition for integer", "msg": "313233343030", "sig": "3042021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52642221020100021ccadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "303e021d02fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d02cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52e4021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e982", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "303d021c00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa52021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "313233343030", "sig": "303d021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021c00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e9", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "303f021eff00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "313233343030", "sig": "303f021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021eff00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3022090180021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3022021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3022020100021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3022021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d01fba71f1257bc26e0a99d33024c3ffcddb31f81919d99d14a2a067ca1021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021cfba71f1257bc26e0a99d33024c41cf97f1ada11575df7ebf714e2827021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021dff0458e0eda843d91f5662ccfdb3bf19c52d996eac764357fb3255ad9c021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c0458e0eda843d91f5662ccfdb3be30680e525eea8a2081408eb1d7d9021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021dfe0458e0eda843d91f5662ccfdb3c003224ce07e6e62662eb5d5f9835f021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d01fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c0458e0eda843d91f5662ccfdb3bf19c52d996eac764357fb3255ad9c021d00cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d01cadaf85542f0643ddc1e53685b317a0a2389333e33734f17510e133f", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021ccadaf85542f0643ddc1e53685b334cc4621752c20bb8fc8c9855bec5", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021dff352507aabd0f9bc223e1ac97a4cd9c98bd2fbcffe069da2e0b4e16fe", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021dfe352507aabd0f9bc223e1ac97a4ce85f5dc76ccc1cc8cb0e8aef1ecc1", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021d01cadaf85542f0643ddc1e53685b32636742d043001f9625d1f4b1e902", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021d00fba71f1257bc26e0a99d33024c40e63ad266915389bca804cdaa5264021c352507aabd0f9bc223e1ac97a4cd9c98bd2fbcffe069da2e0b4e16fe", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff0000000000000000000000010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffffffff000000000000000000000001090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff0000000000000000000000020201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffffffff000000000000000000000002090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3531313036", "sig": "303d021c326bc06353f7f9c9f77b8f4b55464e8619944e7879402cca572e041a021d0091589658ccb7ceaf1f017492450e2915bb8d863bb7f398c8f5bc1387", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "32373239373236343137", "sig": "303d021c5ee27f9264ab53661d3a7b8c858300c6ee5978a30ae9b7e413dd680d021d00f9e6c2aab2afed523b0172ba110b5b0d663a8f77220bb865cef20f4f", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "343331343737363137", "sig": "303c021c1bb6ce51b7e504ccd69a910a11a9c29ba77100d5bccb179de19a93dd021c770a160b2635bb293c73a07fb1fd5e0314b50127ab3e70d1ab974de6", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "36363033343338303333", "sig": "303e021d00fd8a1f6ba87941c7a4cb8e9844fdba099bc30036cab0b82dce6802ac021d009732c1f24fe6c3fa8113b756c6b7e1523375dbda788895018ebf0245", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "32383239363231343535", "sig": "303d021d00cfd828e8c9b0b17363136530af0a54a21a6cbffa7a20a257385118ff021c7bc8791627fbb74a63a76c99f8bee1325987d0ab2a15b0d57604149f", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "34333131383231373336", "sig": "303d021c35e45149bdcdb55ae37a3a2937d2d03a65754e29605dcc5076d20aa7021d00ba6f5eb05da4fa57217efcc03e8f59a35afb6c2043da8ffcdbfea1c5", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "3131373730373734313735", "sig": "303d021c3f5dfba448339287d1bac93830c83222408a7b511aad1d5f530dc6ee021d00f4726edab91264106792e45f30c8b489d268291d3c2a166555dedfd3", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "31353938353135353635", "sig": "303e021d00ab65412c5db588242b51644dfe78d72570e66ff846feb28112d2651a021d0087ba5d4a58ed7999f502b79626b45a0fb19fd8e4572bc1b5d49b3b76", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "32383831313031363138", "sig": "303d021d009ee1f0edcf13e952d21c30fc3c0c05a7b7460603893d9530d8e90258021c48efc43fc23f36f84970c9df74fb1a3e163e95a1063fb3ba966e3e8e", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "32303034373833333332", "sig": "303d021d00b279452ae24eb13cdf7f0854409a7a6c556ae42f590ad5eeb874e256021c2cc96e8ff6e523c2f109080351c8e1542fe405227ff7542f2f924e95", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "39353030323437373837", "sig": "303c021c398b59d0b6a7171d3518d1d3ef6796f10a14251aca5630fa32655741021c4259d0edc9c2aabc09c37a69444df24a5191e21d387b98b080745279", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "32323039353030303630", "sig": "303e021d00c0604841fb5caf4f4f2f91fc7804729349f796adeb304486ea579e02021d00dab85b543047ef1f2c2bf9feab25a6c1bfc89e5734edd69a3f8b09fb", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "38313933373839323237", "sig": "303c021c7f5127a83c76554fe4fcc00ea6e62b14c8a675168e60ea9d6b1f034d021c7ab4aca6a9315c795936fb41bcf46a4840a1e41a874ddb1ad5b550a7", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "33363530363033323938", "sig": "303e021d00aca0bf2ba598af7c37adb7617da4b1b2ef93b82b58f1161db8949ce2021d0086d0e4743c15c74b1ab334e236fcf23976c731491c808cc55027521f", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "3136333833393533353233", "sig": "303d021c5fd0efe2e36041cd23431bb85e2bc038994a706fb9a40ee8eaf1e664021d00c494185ab2c6dff620f3d48bca6130d4d507e35a0090eb27f167bdaf", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "32303931373638323035", "sig": "303d021c0f173d33840439b0072b8315e735f3732a5657334b9d8a90faba6919021d009be335cd9d18cf81a4faae6110f2532881ab57ec71ded448390ea224", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "39333634373032383235", "sig": "303d021c601f7bf10bf200a48369d222d9e4076671339aa98955de31ae7f4503021d00a11b5f8b770d78aab8ce1918a60b496e3775e9f9097bdbc598c9e99b", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "393236383638373931", "sig": "303d021c07c32dca0c28e0b3a142944b4f37a1b9293175fbb577ce38642154cc021d00e48d47870afc0f169327cfbacf49df87bf20a13f5c2a57854d214023", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "35313738313334383231", "sig": "303d021d00ca328dba129ae9a7b273fd1b67d843e02d5bd2c4d77514891f2c08d5021c44ae98af8fa9d515995ee5a6fdf56856c67c4fe5f8832781704dacfd", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "34373335303130373531", "sig": "303c021c6b00ebcedc8c30521024d15dfa14304f8c594ab8a17eb3df68393b2e021c3a53183103dd1b1ef8631e7cafe510eac9ab4c10164df208c598e20f", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "3134333533393131363839", "sig": "303d021c4cf261aa6616509319653090abd707202026733fd64ddb14f2ab26d3021d00d8e37fac62420a7e9944e4b319d5e73d03b87a589971fed7012ada54", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "31333834353439323034", "sig": "303c021c504f2f50ca931e2a4f8cda0f08c2b79ef051677d76a08f1806c53af5021c736248501f062b14bd3fee6528700d1a64a8ae30d7c76e1a92b66bad", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "32373632313932373839", "sig": "303d021c79a548e4da3b3df4df774e7fb9dc4af0706635c454e9e23c2aa29752021d00b74069eccaefba00a71f33b43f69eaa321dc58436ebafbcd574660b0", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "31383331363534333331", "sig": "303c021c32377296ba47114b5f3dcff841416bca3e5e98d64dd987badc457655021c29a37a143b47b029e256c7506a679ee8f1962ccbbb1c83f4088501b2", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "32343336383939303330", "sig": "303c021c75d62c172f26e6e6831c233b49895c1a87c1ee3ef57b711856199e46021c27830a8d1fc44993990a9d1b2b147ff8792b0b0823bc30fb012ce9ee", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "323034303431323232", "sig": "303d021d00fab62ad526e88f6b9ca7e2d23f38d9fcf861cb7cc275faa25ca6a09b021c29b371dda6ef29f037181b9c3bd92c78578b43dddb74a029bea7f238", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "33333337313139393735", "sig": "303d021d00939e3d378767fc069ee193f001f9c84e4f82dd40747e037df3fb200d021c7db50ec017098cab3ad054530cee5238b0a315cdc80a3d8a7dd0991f", "result": "valid", "flags": []}, {"tcId": 257, "comment": "special case hash", "msg": "36363935363230363738", "sig": "303c021c1a71aa363800731f4c5a9f0a4235198a7658f1b425972ecac9a2a340021c21b61939e575b1a5ff487473ba3803683c8b20e89844a55785630f89", "result": "valid", "flags": []}, {"tcId": 258, "comment": "special case hash", "msg": "32303933303137373437", "sig": "303e021d00e7bd4ddedd412bdc8482f08e2df540576b1bf768933e1adda30ab963021d00b7d647170f0cfb0324f9ec2e299403dc6cf1589be3dfc6fc228234f1", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case hash", "msg": "313233343137393136", "sig": "303e021d00ac96c0158f3f939d14238f744c9c2b5736c026db414c5d9c019d0562021d00a27c8a34065c71b92af11d77dad533f9cc62667ea09d50057fbea9b2", "result": "valid", "flags": []}, {"tcId": 260, "comment": "special case hash", "msg": "31373634333530363837", "sig": "303e021d00c526a888a4f74f01f037b7556955dfcae72266e5df55ac6cecbfb85d021d00d85a5bc24cb73aba3188098c11f7087a39e5e62271e016cbca2b5588", "result": "valid", "flags": []}, {"tcId": 261, "comment": "special case hash", "msg": "3131343137323431343431", "sig": "303d021c37206ede6a2f6bf468a91152503eb90837456a3b1d6c937b0efbf60d021d0096d35182fd97438e4a978c2223eac6a4f7005926e895ff17d667a2da", "result": "valid", "flags": []}, {"tcId": 262, "comment": "special case hash", "msg": "32323638323436343933", "sig": "303e021d00c42106cf72c3632d1d4f15a08b7bb72c57ae82c099953d324149b03c021d00fad8d9d9da94259b719ff6b6c8e6ab24bb365602ec2ffe12fe0e02ec", "result": "valid", "flags": []}, {"tcId": 263, "comment": "special case hash", "msg": "32373234373936373737", "sig": "303c021c0fc1582e0b3ebe74b1fa4425e321a5b2d16b1c3ab060e98cf530defa021c115eae025fa3422621a49fd78486ca245894a2d83c644e31abe21b5b", "result": "valid", "flags": []}, {"tcId": 264, "comment": "special case hash", "msg": "393733333935313139", "sig": "303d021d00c96588e99831484962138ee5cd487ddee8c2850c6e82db35f8d5ad4b021c26ee8a0787b6c5bad7598e5d41e59880b32cfa125c8930fdb0c16480", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case hash", "msg": "31353037303032373036", "sig": "303e021d00d144ba07fe4c7f96353dc8b171384b4492cebe74c86cdffe6213f796021d00c19fba0a58850f2ddad8ac81b404ef18f5b1b48198e48d069dd9c2ae", "result": "valid", "flags": []}, {"tcId": 266, "comment": "special case hash", "msg": "33373433353638373832", "sig": "303d021c48f0d2b55006514280ce7ad28336ab31a0536deef767f00ea3419b97021d00918326b9337f76f3b3b3b789031e5c6d3000f7215b5d8eb38546797f", "result": "valid", "flags": []}, {"tcId": 267, "comment": "special case hash", "msg": "393437363731323438", "sig": "303d021c44d3a182c9a8a207a5dcc2811e30eb26037bb5627870a1e3a754a1ef021d0082a5978d2fdea3875cfea57b3178742c42ceedd3b396d5014185317e", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case hash", "msg": "373235343434333234", "sig": "303d021d00eeddc59d60f2757081c3b915984e739ce31ccbf184fef27d1025c711021c686f704834047ae4b8beef8fcb67c94a05b7f4769d6dc98021c21d6e", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case hash", "msg": "35353334303231323139", "sig": "303d021d00ba4c612540922f0a1b770d9373c518993fd445105057cf26c0aeff8c021c4d6565ff7317d861b2f3c07df628a48d42d21a0ea99e8945486ca37d", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case hash", "msg": "3132333031383133373933", "sig": "303e021d00e67640a782681968972376b0fd72e54206af328a59be671e6158448a021d00f2a76ed2674b1f9142f54c8fe76cb8dbff71f4df3775a2abeb120a2f", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case hash", "msg": "39313135333137363130", "sig": "303e021d00c520faa7574e13bec8b3f997ee9b844b6367d61d26ca84dfe4eb245b021d00820be3e48aa4e569dd6d7a50e74d65b59fe603c9651dd3abe8ce9720", "result": "valid", "flags": []}, {"tcId": 272, "comment": "special case hash", "msg": "32383934333937393632", "sig": "303e021d00d86d566ff5fcccf3a356786e614b9340c242648c15ec4b670ea11f07021d00abb0e7da14332b7742636b66b8b7beb2ac4a155fe6308aae6c70660b", "result": "valid", "flags": []}, {"tcId": 273, "comment": "special case hash", "msg": "31323333353833303333", "sig": "303d021c6612d845fa2d59be90a0cd7f75a3509430ac8078317d385fbef568a5021d00c5df1cd8920cd3b872e2335522bef8295cb544082d269b83aff84eca", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case hash", "msg": "323131323639323536", "sig": "303d021c098f5293e6122fd450e1f3182e6f2323b5878b39afa4184883977c3d021d00936a9af99a3c54e1b94a04fef86cc7a16526fea66f6a1c3d40da1604", "result": "valid", "flags": []}, {"tcId": 275, "comment": "special case hash", "msg": "33303035343833383634", "sig": "303e021d00f5a524acbbade6852c29132c32d61b06707a388cb3cebd8eeb59d1ad021d00d7de65c50b21a84e6f49b71fdaf977e3d2ee8fec008e477428b0857b", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case hash", "msg": "38333536373632373235", "sig": "303e021d00d7c5da509bf42983f7ef6d7578eb297f58b51b2c47001c6fdf3aafe8021d00c6ac0690050f22e9fb9f0fdaa24bd1558f793a0a9a5692f8e57959fa", "result": "valid", "flags": []}, {"tcId": 277, "comment": "special case hash", "msg": "38333239353030373630", "sig": "303d021d00d6a32076b2f85398959f54218befefc3ffaaa3b7a7272bb85ed06768021c1ba42ebb3927b3a77be0bb8359a91566728fb6b79c206bf0fda960ed", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case hash", "msg": "31393237303834313635", "sig": "303d021d008707126e07ab9635cb48bad9e9b03b432329ee158b173bd8753e3f44021c1214617e1547956f34f9701d4a73958ffe91d9fafeb8b28fa38516d9", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case hash", "msg": "37333032333538323838", "sig": "303e021d00d22d8e4cd378fced312d893e6b3b4c0904fe2932eb805bc0fa2ebce2021d00f0eaa7c6d10fba23cde05be6dfd7a12ca7782cbe2639f12154009494", "result": "valid", "flags": []}, {"tcId": 280, "comment": "special case hash", "msg": "3133343733373337333833", "sig": "303e021d00f6e406ee55ffc2a61f2adb63e3cb19284b1c067c128cf445b68dc259021d00e17e2c98588ce6cab01ac7bba1e3ebe1b45266563d1482c920ec8e8d", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case hash", "msg": "32353031353533363839", "sig": "303e021d00d91613f7e2f1456d833f2c9aac0796366079e92f2e30985440a29409021d00e67217bd2b578172b9f225c7014d6cdcf875cde0428e51551b2959de", "result": "valid", "flags": []}, {"tcId": 282, "comment": "special case hash", "msg": "33353335303430303239", "sig": "303d021d009d0d161352c289f9b1021b88c467cdd1e6d384e3e4019da7c56c492d021c1c4c4be4f3c9483b5027b5fc7d4729cd74c3b9cef751ef62d2250199", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case hash", "msg": "31343230333537323930", "sig": "303d021d00ac98a4d298b096e78461602120d1660311fb8ab1f18b46eab3da4d8d021c56ff93d698fda5d8d01a5916ea6d348510f2080c8d784e801a83be98", "result": "valid", "flags": []}, {"tcId": 284, "comment": "special case hash", "msg": "393035313735303638", "sig": "303e021d00f61ab89eac55f6224381071c2343df902170b58389edcf7e5499cec4021d00efdc8158e8392dfea1c55c5d62ae7b695f822a6407ce52c3dcd2f823", "result": "valid", "flags": []}, {"tcId": 285, "comment": "special case hash", "msg": "31363133373734323935", "sig": "303d021d00e73556f65fe40b2599b36abd679dad75fe951f32b7771ee6269b7470021c687fb6342c4bd2f3a215c0405941be22602297164fa9115fa97f58c5", "result": "valid", "flags": []}, {"tcId": 286, "comment": "special case hash", "msg": "383733363733343931", "sig": "303d021c6b1201494285b63e42b59ca8bd67bb655f41d020487c6d4b45c5268a021d009528c4ecfb75628ecbde99aa0a130463a1df91b0205de0b726fe6fdf", "result": "valid", "flags": []}, {"tcId": 287, "comment": "special case hash", "msg": "34333535313036343035", "sig": "303d021c4e029b6b69d41ffac3c9d46c79604466fa42a7a66a6cc873dea2f554021d00d554c422d03c2642848e99400aaf411d6ec81fa0eb9b43c4c0bceb1b", "result": "valid", "flags": []}, {"tcId": 288, "comment": "special case hash", "msg": "34353339353735383736", "sig": "303d021c7b98c9dee7ac12a6c2f59b20548a86cafd7775a15fe290d48484dd44021d00e6690c32b6b1aec825cff9d2d47a1bf5d6d20000983397bf48c04ce2", "result": "valid", "flags": []}, {"tcId": 289, "comment": "special case hash", "msg": "383933363633323031", "sig": "303e021d008ed3385dfe122f21259b55028c6ba2f9ac94594bcb5847b764677dc7021d00b5a12a3f4b088561e1a007e4f052bcffaad05b5e727f3e9385154063", "result": "valid", "flags": []}, {"tcId": 290, "comment": "special case hash", "msg": "33383036303638303338", "sig": "303d021d00a3fe5bf1eae043dec8021e5bc9090fe8d5911c1d6d5a5b98b0f888ea021c7ed55be1e9ae728fe954d34c3effeae42d5f03092d7eeb0710bf1d5a", "result": "valid", "flags": []}, {"tcId": 291, "comment": "special case hash", "msg": "32323332383137343937", "sig": "303d021c3e6573e0dc060ec88be161ecfb2200c6f7ff5127a13820bbc4602a54021d00c852d88755d4bd244f0acafd418e499245f2e6417dc8be725143b1c9", "result": "valid", "flags": []}, {"tcId": 292, "comment": "special case hash", "msg": "34303734333232353538", "sig": "303e021d00ccddf99cfb3ae06c478e62b499cdb2e5a98c665d5fd323cd6733f262021d00eb3b93b35ddd9262e3174aaa32bcf46aef4becd9916166fa3d7cb0b4", "result": "valid", "flags": []}, {"tcId": 293, "comment": "special case hash", "msg": "363130363735383237", "sig": "303d021c088d461e34993c86d1520bfb3a90f1190e08868988bc623cde1eb879021d00a118136bc47181a4edf9e05d3db4eb5ac73da50a95b751e48939be39", "result": "valid", "flags": []}, {"tcId": 294, "comment": "special case hash", "msg": "3137343138373339323133", "sig": "303d021d00b51bf389a898407322d5fe142ab2f81439005ab9b4ac3f4031c06d43021c5930035a00f6a1bb805d8c7acdf42b18b020a9426ab7e2ce41f3694a", "result": "valid", "flags": []}, {"tcId": 295, "comment": "special case hash", "msg": "35313237383432323837", "sig": "303c021c565c6827c8f18daa21a6b1edb8af81193e8e391f3e8360b346c7b98a021c468b8b2e16c51e41e26651493e02f0db3af5c9981edfff2991d10599", "result": "valid", "flags": []}, {"tcId": 296, "comment": "special case hash", "msg": "35303338363930383739", "sig": "303c021c6030ec07ae1cd991dfbc6b48da43c6240b5bb8f7fe19832df596754d021c54b93d4ae52e4cb7eb969a1eb62f0be395d30a43e7d5736df6b508fc", "result": "valid", "flags": []}, {"tcId": 297, "comment": "special case hash", "msg": "33383737303432333937", "sig": "303d021c75c39954f9f95618cb890b1a90b9e4fb5b8cec968f8968f53819d497021d00cd93fb7f8aad56fce1734c81229c6b1845635631209fe4d35a4be97e", "result": "valid", "flags": []}, {"tcId": 298, "comment": "special case hash", "msg": "333231373038313738", "sig": "303d021c1fa389d2f4e586d5c1e9fcf34b56ed6d35558714d565a61924c59edf021d00ee1a1f53d0f65e00c6fccdeef35566e3d3cbb5b910127d0a40025318", "result": "valid", "flags": []}, {"tcId": 299, "comment": "special case hash", "msg": "37363637303434323730", "sig": "303d021d00f1ab78cbc3c60bf97e583ab50abe7c96996b2af0c71e1371a94f9235021c21ec7108be398f859f8f75a8b9b527914685c5f3c1eb26f9b0d44414", "result": "valid", "flags": []}, {"tcId": 300, "comment": "special case hash", "msg": "31313034373435303432", "sig": "303d021c50e0e423fca4194680ffad95d1e5df534793d5524099addfdaeac0f4021d0091c4495ad79929332b1ca31a3b7d4b94a05b544d029f8df64e54d170", "result": "valid", "flags": []}, {"tcId": 301, "comment": "special case hash", "msg": "313533383730313534", "sig": "303c021c5dd7d39e95169cefd77a5a5dfd435e2a879576faf3a845d266d402ca021c63225084b9219b15884e0cd665aca92ef1f4b541987b98cbd03edbfb", "result": "valid", "flags": []}, {"tcId": 302, "comment": "special case hash", "msg": "32323631333835303439", "sig": "303d021d00902fbc7eed7ca442c94240c75323db9f690a84817f824bbe12c2f9e9021c12e49c4ed88909344b61c4eefeae4aae0494044bd01653e66dbc480e", "result": "valid", "flags": []}, {"tcId": 303, "comment": "special case hash", "msg": "37353538373437363632", "sig": "303c021c16881c4ae60f32a6b81d445f7bb921ce4f4dcd349695efe0fc94d213021c06066332f61edc98a88a21a42f26e912c99c5471093aca9b1c11d54e", "result": "valid", "flags": []}, {"tcId": 304, "comment": "special case hash", "msg": "33343939333634313832", "sig": "303c021c3d0af397602fc0d18d8a733377cc3e596c143f75d6a9d2c62c99f327021c30ad8bf0bef3b8a2bb4158db621de4570d75c5fec7bb8c3e78af8445", "result": "valid", "flags": []}, {"tcId": 305, "comment": "special case hash", "msg": "32333639323733393835", "sig": "303d021d00f95c0fbf6c90b300d80765d7a5902ef690d4dc9ae9acd20a3a7b6d78021c73f3ad08dadaf5c5854d351d47e4ec328013eb7ae9c7e83e110024ad", "result": "valid", "flags": []}, {"tcId": 306, "comment": "special case hash", "msg": "343933383339353635", "sig": "303e021d00b324aec4420d11a7da67d287198abbaf2c9e6606a231afa0924d5902021d00db1ed6b990f65ebcdb8c5bc38c7e1b83cfc8f8d00ba4ced39bb565c8", "result": "valid", "flags": []}, {"tcId": 307, "comment": "special case hash", "msg": "32353334333739393337", "sig": "303d021c7c2088076ab28c40279abea3fda0a2286e0afe963bacb5f70ded6555021d00da358407c1cc5c2e776854500f44a904b2624ab94ce101fb2b326eb0", "result": "valid", "flags": []}, {"tcId": 308, "comment": "special case hash", "msg": "383334383031353938", "sig": "303e021d00abbf69e074a39e167d6c4a61fe0eb2f42a42afdbd840f63ddb3e057d021d00b17ec9b01070d7d8825969bf5f72aa047fb4557bf4525b683a2dd061", "result": "valid", "flags": []}, {"tcId": 309, "comment": "special case hash", "msg": "32343131303537343836", "sig": "303d021c787af603539b58520ab74999fa2fc8fe90ea10f3c1b16a181fdc0954021d00fe25a9b16e66aa1c50a59042c1ff0a85e2f4811a8d7540f83a632684", "result": "valid", "flags": []}, {"tcId": 310, "comment": "special case hash", "msg": "373836343638363335", "sig": "303e021d00c6fe299f384a978be18fd5dd2e94ca1df248cf17819c95d1b317b257021d009c2bd111dd41fb56a9ded5ce44cf616c3e735e868a1b19d2575fe8ec", "result": "valid", "flags": []}, {"tcId": 311, "comment": "special case hash", "msg": "33303534373733373638", "sig": "303d021d00a68d4678ce9e0cee7ddb75bf4b5fd162ced09c1bdc366336181e4dcd021c2898aecfbdfb33e6be1ef59a20f307729c9954ebff5f119adabb642b", "result": "valid", "flags": []}, {"tcId": 312, "comment": "special case hash", "msg": "31393237303137373338", "sig": "303c021c32c28f726d79d4610f6382c329a3a955059f5876ef343585098f40db021c20d7df0ae11f3909ffdc54b12d05bd294505ab76058c79c61dfceedd", "result": "valid", "flags": []}, {"tcId": 313, "comment": "special case hash", "msg": "31303531333235363334", "sig": "303d021d00900b8cb66d0e2bf2f0c4bc3bf269b521801e888ff17c5b8286409ac2021c6a0e9736ee8a570fbf7686630c9dc2613cb084c645100b3924b2b8f3", "result": "valid", "flags": []}, {"tcId": 314, "comment": "special case hash", "msg": "34303139383636363832", "sig": "303d021d00fad47b9ca7acb94da34cc59586a9a42b95a52d809e3d5816cd1f2af8021c42c895f05a25a443ca32a070aff72225114b557734b966293b657eac", "result": "valid", "flags": []}, {"tcId": 315, "comment": "special case hash", "msg": "3130343530323537333530", "sig": "303c021c431e666843a0d5e131567ab13ec0accd1fef8d441e96c4b8834d4e40021c0e7a8bb3cef5a676cf4148da42b61457cf9fd43c8a33241d4bb87181", "result": "valid", "flags": []}, {"tcId": 316, "comment": "special case hash", "msg": "333236393538353830", "sig": "303d021c41327e3497a615192c697ae6a6f8622ba493987f617613e76d134f26021d00e2539f0d6121b27d4f7177714ea5ead4f4d49284243e3ebe964f9c47", "result": "valid", "flags": []}, {"tcId": 317, "comment": "special case hash", "msg": "33303734363533323431", "sig": "303d021c37506ccd77f8097e83d63c3d52af634cc0fe98535688d962b0bc3689021d00bf89ecc570436c2fb85beef396422626cf03f7c2dc16d46951104496", "result": "valid", "flags": []}, {"tcId": 318, "comment": "special case hash", "msg": "37373134363833343830", "sig": "303e021d00c97fbc5027204b811f4c250ccab741e22876d1a8f8c6237ea915ca38021d00c51cefcf82a303935b015f0551af07b94bb2203e2bc7cbd34520a35b", "result": "valid", "flags": []}, {"tcId": 319, "comment": "special case hash", "msg": "31373933333831333230", "sig": "303d021c0c1f93e9bcb1cbddd0508bda3dad7a96dd09f1321136708b586a1eb9021d00947ef9a508ba969b422c1d8e16a82e8a06a0014afb73bc1f1b6d3a31", "result": "valid", "flags": []}, {"tcId": 320, "comment": "special case hash", "msg": "34383830363235353636", "sig": "303d021d00fb0ca1572c10b3961e9539a9423a55dd08dd991fc04e13207b801a7e021c7c9c049836e1566b889c94416c04a4a861f3573de771be3ecfc0c6ca", "result": "valid", "flags": []}, {"tcId": 321, "comment": "special case hash", "msg": "3439343337363438383537", "sig": "303c021c762764cf2163759dfcf7863b20849c6deb57808cb208479814a243f8021c7b41a973df219ea717a653bdbe27669bba57fc7c7afc3afe9c87d62f", "result": "valid", "flags": []}, {"tcId": 322, "comment": "special case hash", "msg": "34373038363839373836", "sig": "303d021c5cc108138881be0cf7035dcad94db36063eaf148d24fedfe44723e5d021d00d11637a4eb1e2e438593eda38970537a05590b3a9a5054d3afc9e9bc", "result": "valid", "flags": []}, {"tcId": 323, "comment": "special case hash", "msg": "33303239383732393531", "sig": "303c021c531c06348905bbf16d85b8eba22f5d5ca33e490107ea979f6f322a65021c0109215bf6d2afbedf4efe481f704963355f09f59e1977be330972a7", "result": "valid", "flags": []}, {"tcId": 324, "comment": "special case hash", "msg": "33303137313930333835", "sig": "303e021d00bdc683a5e7cbe5df332c85af8c33d7ede2e272dce6b40d360d666acf021d00a779a6f1fe92ba3f799595289d7f37d47a1187285d7636330a217a7a", "result": "valid", "flags": []}, {"tcId": 325, "comment": "special case hash", "msg": "393536333633393339", "sig": "303c021c70ba414a666f89e84064b2f9b08d500c6bb12d0a5ef9b23f4fa46ccf021c43d88b7e943fe8792ff62ed462ea308d28f7f03e4dc59d6214f30507", "result": "valid", "flags": []}, {"tcId": 326, "comment": "special case hash", "msg": "35333030373634333530", "sig": "303d021c126507852f3606bae527749982c5b303870cbf999d2f0191f5fb7c62021d00f4c92989775dd4183c62fba423e628fc124db1718647a69316e0614e", "result": "valid", "flags": []}, {"tcId": 327, "comment": "special case hash", "msg": "31393334363634383434", "sig": "303d021d009342dbbf6c92387699c84cc0f8b011690c072f8f067cfd5de213ecff021c09326ae7030d262085de892da4cfdbf787cffeaaa71dfe0059e28b6f", "result": "valid", "flags": []}, {"tcId": 328, "comment": "special case hash", "msg": "3335353435303535393632", "sig": "303d021c1e6e376f2143d020848ef2653aedf7123e27fa8c67585655e7f9e5c6021d0094c8ea3567ed7726d5a145f417ba44bf7afd586e38c83748dc227830", "result": "valid", "flags": []}, {"tcId": 329, "comment": "special case hash", "msg": "31333031373232313038", "sig": "303d021d0099f67a076a74ef92d33cb89e39944423d6b60d6479f22a63c2b2e6a1021c1cbcf7baa7702cdc175d4dcc3d6a016e90046ebf74de51bddf41deac", "result": "valid", "flags": []}, {"tcId": 330, "comment": "special case hash", "msg": "35363137363931363932", "sig": "303d021c61ec8a8a6ea82e842202f327ca3e5971521be13c6dc605e779fa9b47021d009f87294c702676ccab8a6b31aef5aa104139da7cbc69b1a6d4d77a6d", "result": "valid", "flags": []}, {"tcId": 331, "comment": "special case hash", "msg": "33353831393332353334", "sig": "303d021c467ffbd3372c95547537212af707e412919eb3731ba661a2f20eca59021d00beeef3b4b325d5004f0dbd99a735778db53e0aa381a069fa68f00852", "result": "valid", "flags": []}, {"tcId": 332, "comment": "special case hash", "msg": "3135373136363738373434", "sig": "303d021d00d4bbeb81922f0e5f10d637a48dbe3d80cb53524876821dc2fabb3457021c23f8ff0919d566e05a26ac97d51bdc79ba9c920e2d6c777b8d7cd4e4", "result": "valid", "flags": []}, {"tcId": 333, "comment": "special case hash", "msg": "33313939373833333630", "sig": "303d021d00c151ab28d7a23986021cad0f2f937b61c4adab21acf5081cade56585021c386aa40927cbc45216568b23f6a4c49aeab525e94b074b8ef16aad64", "result": "valid", "flags": []}, {"tcId": 334, "comment": "special case hash", "msg": "373430343735303832", "sig": "303d021c40a4b6106960c1b249c116d539c9ab27f992f0c1e92d8e04a919a749021d00811d34da166b2447a2da0e3821cff12cd47ae0fb273fb18df830d179", "result": "valid", "flags": []}, {"tcId": 335, "comment": "special case hash", "msg": "343137343336353339", "sig": "303c021c6808f8c1cf3a051724d074ebdc85fcdc14987324605a81db7591baa6021c026f5efef2c657bc048c88a165f885628efcad293d80a5cf72855491", "result": "valid", "flags": []}, {"tcId": 336, "comment": "special case hash", "msg": "31323335363538383839", "sig": "303d021d00af7177ec82b907cafe3ac3bfb0ae2f2a2a8355380ce8b31217fdb3f0021c05282a1dc81cb458f1c556877dc23301cd8f69a5269df683ba1d959b", "result": "valid", "flags": []}, {"tcId": 337, "comment": "special case hash", "msg": "32343239323535343034", "sig": "303c021c4cb87503bde0aca323f93b04e1735f84bc0e101ba8b71332fb40ce4c021c0bbc59dac149a5644771119b00d1d72905e4b080f9aff5d6a42e1565", "result": "valid", "flags": []}, {"tcId": 338, "comment": "special case hash", "msg": "3531383033303235343636", "sig": "303e021d00ae09718e0e6e7fb0237056f531221c0c93306b426a985ac51a70a831021d00b50599a416553c34102a917990c70c3f9f6ed01719fa9e009c14a230", "result": "valid", "flags": []}, {"tcId": 339, "comment": "special case hash", "msg": "34343736333938323030", "sig": "303d021c7bd61f75610ead312a227d5368e093606a4630663825490b392a5a65021d0086552bb6f4f3564349fc3fb47073c3c28d10d9daf5b9166a82f8a6bb", "result": "valid", "flags": []}, {"tcId": 340, "comment": "special case hash", "msg": "39303630303335323132", "sig": "303e021d00a3daf123bc8a3e393e8d669bd76c4486a3e65bd16fc390e09b2308c9021d009c680cdbca9d3aa378cfe60481b2e68d3b3687363e6716542da50a72", "result": "valid", "flags": []}, {"tcId": 341, "comment": "special case hash", "msg": "31373536303533303938", "sig": "303e021d00b782f0af6552a3f30530942f35160c09ec77d06595aceb9da8d246ac021d00f41b8216ff29fc92655527c6aefac0bab839c18525c03316b46a4edf", "result": "valid", "flags": []}, {"tcId": 342, "comment": "special case hash", "msg": "34393830383630303338", "sig": "303d021c3ee945c87a9d4e74d87471f83e4f791945b7733a1bc75fe6738f23ec021d00ab06858d113616b694d36ffaf1736567d3895e005e80f521310b5ed0", "result": "valid", "flags": []}, {"tcId": 343, "comment": "special case hash", "msg": "33363231383735333335", "sig": "303c021c733752fa2b90121c01878170f2019d9c9a4b1c95e82acab18ae95d71021c358eb5d69942b4b5d0d900bb843d79f90af54663b3a926fabd081d42", "result": "valid", "flags": []}, {"tcId": 344, "comment": "special case hash", "msg": "36363433333334373231", "sig": "303c021c72fa0cb38863c06e535402e010bac709ef4beb0b38118c7b13308e83021c33eb21f6c081afa7db62c2f8cf78ed1982cb762a130509bdb53f1774", "result": "valid", "flags": []}, {"tcId": 345, "comment": "special case hash", "msg": "34343534393432373832", "sig": "303d021d009277ca6db735f44bff16c7ce43ad45707bc6a6b451e64532b86b54af021c433c69a072349f3814c6366418d3eb7fb05509ad099afeef411c6e11", "result": "valid", "flags": []}, {"tcId": 346, "comment": "special case hash", "msg": "3230313834343032", "sig": "303c021c37e05ebb376ede0d3fc7528f19dbe4a38e4f310295058e2173f88900021c4f8d5ca627d43c54db6674e1462d12c42603cddc6f3067307ab04d4f", "result": "valid", "flags": []}, {"tcId": 347, "comment": "special case hash", "msg": "3538313332313733", "sig": "303d021d00a419d09f64d28e4ca617187790d046e3b29b87dcd6007eaeef08aef0021c397fae569961478fd7d075a30398a646d95c00f8171600e1ca2335c1", "result": "valid", "flags": []}, {"tcId": 348, "comment": "special case hash", "msg": "31313833383631383131", "sig": "303c021c05ea0f04e9b4eb3b62fcf8f1f1d120e58a932ca9a04e8c64bbee537e021c166633a3c8d2b5c6246da69e069f2f09a7912efc355c405302b2984b", "result": "valid", "flags": []}, {"tcId": 349, "comment": "special case hash", "msg": "31393232363032393036", "sig": "303e021d00fa2a6265e9fbd317fcda0adeac307fe2f0a94afd6303c91985d84d10021d00db93f0201284b5b66a15ea177f34c27c6d80479c18db44f386e85d37", "result": "valid", "flags": []}, {"tcId": 350, "comment": "special case hash", "msg": "393735313433323037", "sig": "303d021c2d58c2d0cbbe7215424b943006dd56d23159ea10140569dc22663bfc021d008d9bdec0639a236e95fdb35dc2ce3d63f7d5447b6dd3d95227fba0a1", "result": "valid", "flags": []}, {"tcId": 351, "comment": "special case hash", "msg": "38333135313136333833", "sig": "303c021c1bc64aaf83515275fd3187a18d3651c75ce85b53f5579ecf5f190334021c2ed9fc723bd723cd4c8b62b7968e5f058d25aa6f6bb45e6b09337793", "result": "valid", "flags": []}, {"tcId": 352, "comment": "special case hash", "msg": "333236333136383132", "sig": "303c021c240d8b05889274d716f57588536320775afed824d79f41f32c35e728021c25031a073d638165f8c0b46a28e008f1de9fa0cf726d316269f669d6", "result": "valid", "flags": []}, {"tcId": 353, "comment": "special case hash", "msg": "34303239363837313336", "sig": "303e021d00fb2f81bd4e22230c0c95a7a08eaf9a668f6b5c6d32bffaf538bed19d021d008ae8c126b3168129a720a674fa4dbb81d94f4a3e862b834ea328ed4a", "result": "valid", "flags": []}, {"tcId": 354, "comment": "special case hash", "msg": "36333230383831313931", "sig": "303d021d00fdcdf3940d5b9b3841fa0ee667d4ed9ac74e0cdcde9641270f2e658d021c3167f2b6601b737bc4a700dd0940ef86107eb57c39efaf6b3d71fd38", "result": "valid", "flags": []}, {"tcId": 355, "comment": "special case hash", "msg": "35323235333930373830", "sig": "303d021c46408d1ac74a5bd03c3fc08685f2743eb3f7c42203f63606775a4250021d009813e4704b565f94e563a653a9d4e0310ebc7055729f89ca4b02d9f0", "result": "valid", "flags": []}, {"tcId": 356, "comment": "special case hash", "msg": "31333439333933363934", "sig": "303d021d00c9096071c92973198efac69d6b3912e4e14248bb077602870c543cf2021c631c995cab8f0ccf5e3dcb30d4b995dc96d2573facc6ed9561cb9d93", "result": "valid", "flags": []}, {"tcId": 357, "comment": "special case hash", "msg": "3130333937393630373631", "sig": "303d021d00b55bb2da01e99b08f64dcec6d6d746b5626aec6eae2378fd1db72107021c07f36d4edf6c8112e2a6b9665b19605f41c9449a7eab7839d638ce10", "result": "valid", "flags": []}, {"tcId": 358, "comment": "Signature generated without truncating the hash", "msg": "313233343030", "sig": "303d021d00e630949b06fde06a763cd457fc1776a006932c0fe08131f44aa1ca41021c3de7e43d3671fa00db71d03e7f4d9f80b0d6d7edb695032e09b93d44", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04078e4d1a56e6f485c785d01a8cb9bb2ef5c7646fb1e88228abcf5e01ca1fb26c9366cd918628033dad9518a8f8a9ff4a513c6605f5cdaae9", "wx": "078e4d1a56e6f485c785d01a8cb9bb2ef5c7646fb1e88228abcf5e01", "wy": "00ca1fb26c9366cd918628033dad9518a8f8a9ff4a513c6605f5cdaae9"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004078e4d1a56e6f485c785d01a8cb9bb2ef5c7646fb1e88228abcf5e01ca1fb26c9366cd918628033dad9518a8f8a9ff4a513c6605f5cdaae9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEB45NGlbm9IXHhdAajLm7LvXHZG+x6IIo\nq89eAcofsmyTZs2RhigDPa2VGKj4qf9KUTxmBfXNquk=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 359, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "3030020f00e95c1f470fc1ec22d6baa3a3d5c1021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a", "result": "valid", "flags": []}, {"tcId": 360, "comment": "r too large", "msg": "313233343030", "sig": "303e021d00fffffffffffffffffffffffffffffffefffffffffffffffffffffffe021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04225298c3aab4ee639834fe16c42896f6478f3517589064717d40959e72881bddefd6ef724fec33f92c92d4fa2fffe77ce20a327845b1201f", "wx": "225298c3aab4ee639834fe16c42896f6478f3517589064717d40959e", "wy": "72881bddefd6ef724fec33f92c92d4fa2fffe77ce20a327845b1201f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004225298c3aab4ee639834fe16c42896f6478f3517589064717d40959e72881bddefd6ef724fec33f92c92d4fa2fffe77ce20a327845b1201f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEIlKYw6q07mOYNP4WxCiW9kePNRdYkGRx\nfUCVnnKIG93v1u9yT+wz+SyS1Pov/+d84goyeEWxIB8=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 361, "comment": "r,s are large", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04448bad17b638f55372b256c6717ac0fe443c20af133348be8f728577421c701ae6015da98a2ec4140122091588c013a74fd78b3814c981b2", "wx": "448bad17b638f55372b256c6717ac0fe443c20af133348be8f728577", "wy": "421c701ae6015da98a2ec4140122091588c013a74fd78b3814c981b2"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004448bad17b638f55372b256c6717ac0fe443c20af133348be8f728577421c701ae6015da98a2ec4140122091588c013a74fd78b3814c981b2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAERIutF7Y49VNyslbGcXrA/kQ8IK8TM0i+\nj3KFd0IccBrmAV2pii7EFAEiCRWIwBOnT9eLOBTJgbI=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 362, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021c3d5052691b8dc89debad360466f2a39e82e8ae2aefb77c3c92ad7cd1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04146e29fe57ebbfb91f2562de124e62408e6c3e951c96fabb83d243ca801067848d29410a1611656866e3cececfc569091375b674d7e4d167", "wx": "146e29fe57ebbfb91f2562de124e62408e6c3e951c96fabb83d243ca", "wy": "00801067848d29410a1611656866e3cececfc569091375b674d7e4d167"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004146e29fe57ebbfb91f2562de124e62408e6c3e951c96fabb83d243ca801067848d29410a1611656866e3cececfc569091375b674d7e4d167", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEFG4p/lfrv7kfJWLeEk5iQI5sPpUclvq7\ng9JDyoAQZ4SNKUEKFhFlaGbjzs7PxWkJE3W2dNfk0Wc=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 363, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "303d021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021d00bf19ab4d3ebf5a1a49d765909308daa88c2b7be3969db552ea30562b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04fad98df6a5fbd9f3c023f2ab809c57c72de3396d1cae4bc35d78d8d1889e372a90189ece8fbc690fe3cdeeef38ab12ffda721f32f849ae50", "wx": "00fad98df6a5fbd9f3c023f2ab809c57c72de3396d1cae4bc35d78d8d1", "wy": "00889e372a90189ece8fbc690fe3cdeeef38ab12ffda721f32f849ae50"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004fad98df6a5fbd9f3c023f2ab809c57c72de3396d1cae4bc35d78d8d1889e372a90189ece8fbc690fe3cdeeef38ab12ffda721f32f849ae50", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE+tmN9qX72fPAI/KrgJxXxy3jOW0crkvD\nXXjY0YieNyqQGJ7Oj7xpD+PN7u84qxL/2nIfMvhJrlA=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 364, "comment": "small r and s", "msg": "313233343030", "sig": "3006020103020101", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d2eaeb6be6b5dced48d595b95f73b069f49a078572c74dbc557fa25aa6388c4362e6aa12ab9c1350cd6789d0955445d9198ecc3a4618da73", "wx": "00d2eaeb6be6b5dced48d595b95f73b069f49a078572c74dbc557fa25a", "wy": "00a6388c4362e6aa12ab9c1350cd6789d0955445d9198ecc3a4618da73"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d2eaeb6be6b5dced48d595b95f73b069f49a078572c74dbc557fa25aa6388c4362e6aa12ab9c1350cd6789d0955445d9198ecc3a4618da73", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE0urra+a13O1I1ZW5X3OwafSaB4Vyx028\nVX+iWqY4jENi5qoSq5wTUM1nidCVVEXZGY7MOkYY2nM=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 365, "comment": "small r and s", "msg": "313233343030", "sig": "3006020103020103", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "042df6ce6019d77e3c6e04908a3fde5b8833f9c493bf3cc6568e3d40cb0ced2e9cad2ed363fd20aeb9983bcb22091fcb559c1f6fd538a0ae04", "wx": "2df6ce6019d77e3c6e04908a3fde5b8833f9c493bf3cc6568e3d40cb", "wy": "0ced2e9cad2ed363fd20aeb9983bcb22091fcb559c1f6fd538a0ae04"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00042df6ce6019d77e3c6e04908a3fde5b8833f9c493bf3cc6568e3d40cb0ced2e9cad2ed363fd20aeb9983bcb22091fcb559c1f6fd538a0ae04", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAELfbOYBnXfjxuBJCKP95biDP5xJO/PMZW\njj1AywztLpytLtNj/SCuuZg7yyIJH8tVnB9v1TigrgQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 366, "comment": "small r and s", "msg": "313233343030", "sig": "3006020103020104", "result": "valid", "flags": []}, {"tcId": 367, "comment": "r is larger than n", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a40020104", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0493aa938387897826db23b19903c58a32f8269d93a5366a7a673e30da27ac5a515b7081cd74bf69528114241728da26ae6895e83fea25bec4", "wx": "0093aa938387897826db23b19903c58a32f8269d93a5366a7a673e30da", "wy": "27ac5a515b7081cd74bf69528114241728da26ae6895e83fea25bec4"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000493aa938387897826db23b19903c58a32f8269d93a5366a7a673e30da27ac5a515b7081cd74bf69528114241728da26ae6895e83fea25bec4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEk6qTg4eJeCbbI7GZA8WKMvgmnZOlNmp6\nZz4w2iesWlFbcIHNdL9pUoEUJBco2iauaJXoP+olvsQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 368, "comment": "s is larger than n", "msg": "313233343030", "sig": "3022020103021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c6f00c4", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0409cdd0119a392a271c4055fac2fa71fcc678b9c318bfb90dd1ad0ad64dbd418726f0d3f0bf0cee7a1b366d9711793c839206e6205c416ebd", "wx": "09cdd0119a392a271c4055fac2fa71fcc678b9c318bfb90dd1ad0ad6", "wy": "4dbd418726f0d3f0bf0cee7a1b366d9711793c839206e6205c416ebd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000409cdd0119a392a271c4055fac2fa71fcc678b9c318bfb90dd1ad0ad64dbd418726f0d3f0bf0cee7a1b366d9711793c839206e6205c416ebd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAECc3QEZo5KiccQFX6wvpx/MZ4ucMYv7kN\n0a0K1k29QYcm8NPwvwzuehs2bZcReTyDkgbmIFxBbr0=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 369, "comment": "small r and s^-1", "msg": "313233343030", "sig": "302302020100021d00c993264c993264c993264c99326411d2e55b3214a8d67528812a55ab", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048f711e9fbea41bd25aadf584999926a29c2801e7dbb2eb1dbe0d1586841a143bdeeac9969be87f57b4d71a466b2b6aed24e854b07570e629", "wx": "008f711e9fbea41bd25aadf584999926a29c2801e7dbb2eb1dbe0d1586", "wy": "00841a143bdeeac9969be87f57b4d71a466b2b6aed24e854b07570e629"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048f711e9fbea41bd25aadf584999926a29c2801e7dbb2eb1dbe0d1586841a143bdeeac9969be87f57b4d71a466b2b6aed24e854b07570e629", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEj3Een76kG9JarfWEmZkmopwoAefbsusd\nvg0VhoQaFDve6smWm+h/V7TXGkZrK2rtJOhUsHVw5ik=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 370, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "302702072d9b4d347952cc021c3e85d56474b5c55fbe86608442a84b2bf093b7d75f53a47250e1c70c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c288ddd81714a9e766b35a03c835fbe1a52c540b747b11ba3ebabdccb26bd049bd3088b553162261eb93ab5a75d873bfca8122a229e2cfc7", "wx": "00c288ddd81714a9e766b35a03c835fbe1a52c540b747b11ba3ebabdcc", "wy": "00b26bd049bd3088b553162261eb93ab5a75d873bfca8122a229e2cfc7"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004c288ddd81714a9e766b35a03c835fbe1a52c540b747b11ba3ebabdccb26bd049bd3088b553162261eb93ab5a75d873bfca8122a229e2cfc7", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEwojd2BcUqedms1oDyDX74aUsVAt0exG6\nPrq9zLJr0Em9MIi1UxYiYeuTq1p12HO/yoEioiniz8c=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 371, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "302d020d1033e67e37b32b445580bf4efb021c02fd02fd02fd02fd02fd02fd02fd0043a4fd2da317247308c74dc6b8", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04fd931e8f08b12ca82a6b050e2c281b0069258ac851131008f9c2853c8beabb4bfeff98be4ee23f90b552ccc38a72976bc71e7637dad27fba", "wx": "00fd931e8f08b12ca82a6b050e2c281b0069258ac851131008f9c2853c", "wy": "008beabb4bfeff98be4ee23f90b552ccc38a72976bc71e7637dad27fba"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004fd931e8f08b12ca82a6b050e2c281b0069258ac851131008f9c2853c8beabb4bfeff98be4ee23f90b552ccc38a72976bc71e7637dad27fba", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE/ZMejwixLKgqawUOLCgbAGklishRExAI\n+cKFPIvqu0v+/5i+TuI/kLVSzMOKcpdrxx52N9rSf7o=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 372, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "302302020100021d00d05434abacd859ed74185e75b751c6d9f60c7921dacfbb8e19cdba8e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "041dfdd350e45903ef44a28acf73999a22a706a589a3dd20c051dbe4e541c1021ff3e9646ead3e9858c9c1bdae1e1532b69b2ec52f8546f573", "wx": "1dfdd350e45903ef44a28acf73999a22a706a589a3dd20c051dbe4e5", "wy": "41c1021ff3e9646ead3e9858c9c1bdae1e1532b69b2ec52f8546f573"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00041dfdd350e45903ef44a28acf73999a22a706a589a3dd20c051dbe4e541c1021ff3e9646ead3e9858c9c1bdae1e1532b69b2ec52f8546f573", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEHf3TUORZA+9EoorPc5maIqcGpYmj3SDA\nUdvk5UHBAh/z6WRurT6YWMnBva4eFTK2my7FL4VG9XM=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 373, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "302e020d062522bbd3ecbe7c39e93e7c24021d00d05434abacd859ed74185e75b751c6d9f60c7921dacfbb8e19cdba8e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04fc06e8424839eb95203195f9ee12687c790bbcc9a740d40c1bbbcf660d1afa473637d708952954a18067b4cbffe584e9e891403541c21fa7", "wx": "00fc06e8424839eb95203195f9ee12687c790bbcc9a740d40c1bbbcf66", "wy": "0d1afa473637d708952954a18067b4cbffe584e9e891403541c21fa7"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004fc06e8424839eb95203195f9ee12687c790bbcc9a740d40c1bbbcf660d1afa473637d708952954a18067b4cbffe584e9e891403541c21fa7", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE/AboQkg565UgMZX57hJofHkLvMmnQNQM\nG7vPZg0a+kc2N9cIlSlUoYBntMv/5YTp6JFANUHCH6c=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 374, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "303d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c29bd021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c847d6757702db5d47caf6129e865aa22d6b55c545f70ce7e7ad0ecaf69bf4c624111ef1ce92e9e10d126f0ceda9851dcc53993930324d3e", "wx": "00c847d6757702db5d47caf6129e865aa22d6b55c545f70ce7e7ad0eca", "wy": "00f69bf4c624111ef1ce92e9e10d126f0ceda9851dcc53993930324d3e"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004c847d6757702db5d47caf6129e865aa22d6b55c545f70ce7e7ad0ecaf69bf4c624111ef1ce92e9e10d126f0ceda9851dcc53993930324d3e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEyEfWdXcC211HyvYSnoZaoi1rVcVF9wzn\n560Oyvab9MYkER7xzpLp4Q0SbwztqYUdzFOZOTAyTT4=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 375, "comment": "s == 1", "msg": "313233343030", "sig": "3021021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14020101", "result": "valid", "flags": []}, {"tcId": 376, "comment": "s == 0", "msg": "313233343030", "sig": "3021021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0461c0d09fc40dd59a7b5281af54fd4db8819997cf31212a4b35cf6078a55ef14cafacd57ca50195aa1962aa7d01f7595d76d342d83fed44ec", "wx": "61c0d09fc40dd59a7b5281af54fd4db8819997cf31212a4b35cf6078", "wy": "00a55ef14cafacd57ca50195aa1962aa7d01f7595d76d342d83fed44ec"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000461c0d09fc40dd59a7b5281af54fd4db8819997cf31212a4b35cf6078a55ef14cafacd57ca50195aa1962aa7d01f7595d76d342d83fed44ec", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEYcDQn8QN1Zp7UoGvVP1NuIGZl88xISpL\nNc9geKVe8UyvrNV8pQGVqhliqn0B91lddtNC2D/tROw=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 377, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151e021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0466539275c7d5ae5dad2507a7169c48a9cff067c4d6afeb22561239b612eec9a7e63d602cf87f3581bdeb19ce994c48b41079ef8186339dfd", "wx": "66539275c7d5ae5dad2507a7169c48a9cff067c4d6afeb22561239b6", "wy": "12eec9a7e63d602cf87f3581bdeb19ce994c48b41079ef8186339dfd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000466539275c7d5ae5dad2507a7169c48a9cff067c4d6afeb22561239b612eec9a7e63d602cf87f3581bdeb19ce994c48b41079ef8186339dfd", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEZlOSdcfVrl2tJQenFpxIqc/wZ8TWr+si\nVhI5thLuyafmPWAs+H81gb3rGc6ZTEi0EHnvgYYznf0=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 378, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0443a45adf3cd2a866e01ec45269f604af026a4483973cc4356b4739662a8403f5f14fcd737d2aedec136a534417e0d19e1b98d53944100d0f", "wx": "43a45adf3cd2a866e01ec45269f604af026a4483973cc4356b473966", "wy": "2a8403f5f14fcd737d2aedec136a534417e0d19e1b98d53944100d0f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000443a45adf3cd2a866e01ec45269f604af026a4483973cc4356b4739662a8403f5f14fcd737d2aedec136a534417e0d19e1b98d53944100d0f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEQ6Ra3zzSqGbgHsRSafYErwJqRIOXPMQ1\na0c5ZiqEA/XxT81zfSrt7BNqU0QX4NGeG5jVOUQQDQ8=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 379, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "042e55f92435ee396708e4805e3a2c4c6152c69e1671e9d6387c56f58d3b4a173ad9f475448586ae0f531ee07f15553eba9828b2bc8d72c5f1", "wx": "2e55f92435ee396708e4805e3a2c4c6152c69e1671e9d6387c56f58d", "wy": "3b4a173ad9f475448586ae0f531ee07f15553eba9828b2bc8d72c5f1"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00042e55f92435ee396708e4805e3a2c4c6152c69e1671e9d6387c56f58d3b4a173ad9f475448586ae0f531ee07f15553eba9828b2bc8d72c5f1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAELlX5JDXuOWcI5IBeOixMYVLGnhZx6dY4\nfFb1jTtKFzrZ9HVEhYauD1Me4H8VVT66mCiyvI1yxfE=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 380, "comment": "u1 == 1", "msg": "313233343030", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c342dae751a63a3ca8189cf342b3b34eaaa2565e2c7e26121c1bfd543", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "045bc1eb0ab6349146bf13d2aafd9d44c909397d39c12371ff7e56b1264c7f85004a22aa08c93b7b14c06970fa19625e671c1fb5b16e8a0bd5", "wx": "5bc1eb0ab6349146bf13d2aafd9d44c909397d39c12371ff7e56b126", "wy": "4c7f85004a22aa08c93b7b14c06970fa19625e671c1fb5b16e8a0bd5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00045bc1eb0ab6349146bf13d2aafd9d44c909397d39c12371ff7e56b1264c7f85004a22aa08c93b7b14c06970fa19625e671c1fb5b16e8a0bd5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEW8HrCrY0kUa/E9Kq/Z1EyQk5fTnBI3H/\nflaxJkx/hQBKIqoIyTt7FMBpcPoZYl5nHB+1sW6KC9U=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 381, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "303d021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021d00cbd2518ae59c5c357e7630cbd4c3e1b836938a5b4bfac8239a9c54fa", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04144765b9d78724babc4358b229df98e9d64da22724381ebfe41d7e7286e958c2f6779b8ea76433cd4dce0ae4cd300875ac1c7ad218e57a55", "wx": "144765b9d78724babc4358b229df98e9d64da22724381ebfe41d7e72", "wy": "0086e958c2f6779b8ea76433cd4dce0ae4cd300875ac1c7ad218e57a55"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004144765b9d78724babc4358b229df98e9d64da22724381ebfe41d7e7286e958c2f6779b8ea76433cd4dce0ae4cd300875ac1c7ad218e57a55", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEFEdludeHJLq8Q1iyKd+Y6dZNoickOB6/\n5B1+cobpWML2d5uOp2QzzU3OCuTNMAh1rBx60hjlelU=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 382, "comment": "u2 == 1", "msg": "313233343030", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04e3fc6fee216eb1fa8aea73fc2802709b2c5cd29f99ed964bcc0806aa2dfc634b9050acf568963e3996acf0f2c7656a3bc0f091d7aa1adf7a", "wx": "00e3fc6fee216eb1fa8aea73fc2802709b2c5cd29f99ed964bcc0806aa", "wy": "2dfc634b9050acf568963e3996acf0f2c7656a3bc0f091d7aa1adf7a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004e3fc6fee216eb1fa8aea73fc2802709b2c5cd29f99ed964bcc0806aa2dfc634b9050acf568963e3996acf0f2c7656a3bc0f091d7aa1adf7a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE4/xv7iFusfqK6nP8KAJwmyxc0p+Z7ZZL\nzAgGqi38Y0uQUKz1aJY+OZas8PLHZWo7wPCR16oa33o=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 383, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "303d021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021d00aaaaaaaaaaaaaaaaaaaaaaaaaaaa0f17407b4ad40d3e1b8392e81c29", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04e57bf59d8236b7b789d45912eebaa69dba563bc790fca99ece5da6658bdd80b86d6e9caa2e9086d226614281cc038a2323083a4184057a37", "wx": "00e57bf59d8236b7b789d45912eebaa69dba563bc790fca99ece5da665", "wy": "008bdd80b86d6e9caa2e9086d226614281cc038a2323083a4184057a37"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004e57bf59d8236b7b789d45912eebaa69dba563bc790fca99ece5da6658bdd80b86d6e9caa2e9086d226614281cc038a2323083a4184057a37", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE5Xv1nYI2t7eJ1FkS7rqmnbpWO8eQ/Kme\nzl2mZYvdgLhtbpyqLpCG0iZhQoHMA4ojIwg6QYQFejc=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 384, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00bc0f3a2708cbe1438083451163bdcb6579326cca4fdee68ed37d633f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d2612ed20fe786484b8955d658200776ec219426479c69194f8c1bafa3af3aa778c1751f4474cc0ff2db2a834c232ec49e9ce142a51170b7", "wx": "00d2612ed20fe786484b8955d658200776ec219426479c69194f8c1baf", "wy": "00a3af3aa778c1751f4474cc0ff2db2a834c232ec49e9ce142a51170b7"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d2612ed20fe786484b8955d658200776ec219426479c69194f8c1bafa3af3aa778c1751f4474cc0ff2db2a834c232ec49e9ce142a51170b7", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE0mEu0g/nhkhLiVXWWCAHduwhlCZHnGkZ\nT4wbr6OvOqd4wXUfRHTMD/LbKoNMIy7EnpzhQqURcLc=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 385, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c2d4465a55b282f252f2b47f9f7bfc0ba40ec4dca29f567940b2d90f4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044c70cd0aa06d9ee36a16202bcfc891ceb41a26e0bcdde9d93da8001aab77b507c77931fc7ee290cda7b7af6ad90d0c97b836ae1b65b6eccb", "wx": "4c70cd0aa06d9ee36a16202bcfc891ceb41a26e0bcdde9d93da8001a", "wy": "00ab77b507c77931fc7ee290cda7b7af6ad90d0c97b836ae1b65b6eccb"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044c70cd0aa06d9ee36a16202bcfc891ceb41a26e0bcdde9d93da8001aab77b507c77931fc7ee290cda7b7af6ad90d0c97b836ae1b65b6eccb", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAETHDNCqBtnuNqFiArz8iRzrQaJuC83enZ\nPagAGqt3tQfHeTH8fuKQzae3r2rZDQyXuDauG2W27Ms=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 386, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fe808b87e0e6e76364ce32fde12f692d69dd3b362ef4cf499e03418", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04efb7722fceaa8cd19162eee8b293b58dcbd0fdd3dcfcd6e6ba37baa5659d47ecd7d2b7f65faed3e9a9b554db19e96948a1c5b954351f23a3", "wx": "00efb7722fceaa8cd19162eee8b293b58dcbd0fdd3dcfcd6e6ba37baa5", "wy": "659d47ecd7d2b7f65faed3e9a9b554db19e96948a1c5b954351f23a3"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004efb7722fceaa8cd19162eee8b293b58dcbd0fdd3dcfcd6e6ba37baa5659d47ecd7d2b7f65faed3e9a9b554db19e96948a1c5b954351f23a3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE77dyL86qjNGRYu7ospO1jcvQ/dPc/Nbm\nuje6pWWdR+zX0rf2X67T6am1VNsZ6WlIocW5VDUfI6M=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 387, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00b836c957342d757ecbd2518ae59bb4489ecce8b658ca10e822ecc823", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0479e577f8303d4ed74c3a19cabe3ec35bd39cb789bc7309335a5710e643cc437d5793931f92eea56039b22088165693a7d9ed0ffc6188aa24", "wx": "79e577f8303d4ed74c3a19cabe3ec35bd39cb789bc7309335a5710e6", "wy": "43cc437d5793931f92eea56039b22088165693a7d9ed0ffc6188aa24"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000479e577f8303d4ed74c3a19cabe3ec35bd39cb789bc7309335a5710e643cc437d5793931f92eea56039b22088165693a7d9ed0ffc6188aa24", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEeeV3+DA9TtdMOhnKvj7DW9Oct4m8cwkz\nWlcQ5kPMQ31Xk5Mfku6lYDmyIIgWVpOn2e0P/GGIqiQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 388, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c6c957342d757ecbd2518ae59c5c2f4ebdd95caedf61c68b89387f3c9", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04735e5bc29ee55242a71953f4d31954bc8c173c40bd84017dbc076d4ace15cfa865a1b3762e61dd24cf61022bb571a5fc8b5d5e7ea80b2405", "wx": "735e5bc29ee55242a71953f4d31954bc8c173c40bd84017dbc076d4a", "wy": "00ce15cfa865a1b3762e61dd24cf61022bb571a5fc8b5d5e7ea80b2405"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004735e5bc29ee55242a71953f4d31954bc8c173c40bd84017dbc076d4ace15cfa865a1b3762e61dd24cf61022bb571a5fc8b5d5e7ea80b2405", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEc15bwp7lUkKnGVP00xlUvIwXPEC9hAF9\nvAdtSs4Vz6hlobN2LmHdJM9hAiu1caX8i11efqgLJAU=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 389, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00d92ae685aeafd97a4a315cb38b85e9d7bb2b95dbec38d171270fe792", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ee2558b8b7642287cdeedc8bca740974264a667e5edefe79e7a039005cf362e47faca649cf9b8f6c9214539ef2eee6b86935185c1fdb3492", "wx": "00ee2558b8b7642287cdeedc8bca740974264a667e5edefe79e7a03900", "wy": "5cf362e47faca649cf9b8f6c9214539ef2eee6b86935185c1fdb3492"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ee2558b8b7642287cdeedc8bca740974264a667e5edefe79e7a039005cf362e47faca649cf9b8f6c9214539ef2eee6b86935185c1fdb3492", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE7iVYuLdkIofN7tyLynQJdCZKZn5e3v55\n56A5AFzzYuR/rKZJz5uPbJIUU57y7ua4aTUYXB/bNJI=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 390, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c342d757ecbd2518ae59c5c357e76013b79f087e62e58923f7ea5c045", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04f8b4c378bd5f417b562ae127f39825a2b05770089b7caf9324d7ce1a50708175ba245e9c152bff3ca95743e261b6f579328f30680fb6a2a0", "wx": "00f8b4c378bd5f417b562ae127f39825a2b05770089b7caf9324d7ce1a", "wy": "50708175ba245e9c152bff3ca95743e261b6f579328f30680fb6a2a0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004f8b4c378bd5f417b562ae127f39825a2b05770089b7caf9324d7ce1a50708175ba245e9c152bff3ca95743e261b6f579328f30680fb6a2a0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE+LTDeL1fQXtWKuEn85glorBXcAibfK+T\nJNfOGlBwgXW6JF6cFSv/PKlXQ+JhtvV5Mo8waA+2oqA=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 391, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c21f862ec7b9a0f5e3fbe5d774e20a59eb3c341b9e1ff215b446f637f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "045eb63f0f7b7ec667588670022e2f0b1124b132ebe16c143d6ed24602af2b27b92a449a4edc20267d25d0a238b43aa7289348a1378db079de", "wx": "5eb63f0f7b7ec667588670022e2f0b1124b132ebe16c143d6ed24602", "wy": "00af2b27b92a449a4edc20267d25d0a238b43aa7289348a1378db079de"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00045eb63f0f7b7ec667588670022e2f0b1124b132ebe16c143d6ed24602af2b27b92a449a4edc20267d25d0a238b43aa7289348a1378db079de", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEXrY/D3t+xmdYhnACLi8LESSxMuvhbBQ9\nbtJGAq8rJ7kqRJpO3CAmfSXQoji0Oqcok0ihN42wed4=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 392, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c43f0c5d8f7341ebc7f7cbaee9c414b3d67868373c3fe42b688dec6fe", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04280ac7f11afa66465c37eba820aa8f393d13c5634022333c7a2524bd5c63b1e9ed18115b589b56270c5c3281fa97060f1d30aef465f445e2", "wx": "280ac7f11afa66465c37eba820aa8f393d13c5634022333c7a2524bd", "wy": "5c63b1e9ed18115b589b56270c5c3281fa97060f1d30aef465f445e2"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004280ac7f11afa66465c37eba820aa8f393d13c5634022333c7a2524bd5c63b1e9ed18115b589b56270c5c3281fa97060f1d30aef465f445e2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEKArH8Rr6ZkZcN+uoIKqPOT0TxWNAIjM8\neiUkvVxjsentGBFbWJtWJwxcMoH6lwYPHTCu9GX0ReI=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 393, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c65e928c572ce2e1abf3b1865ea61f0dc1b49c52da5fd6411cd4e2a7d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04cdc63e73d8d6f4f8770a97d93bde4e1974a277adad53d500cb070907533c09379488cb344ce01bb29a2f12d6e0b946a279c7f5d47831477c", "wx": "00cdc63e73d8d6f4f8770a97d93bde4e1974a277adad53d500cb070907", "wy": "533c09379488cb344ce01bb29a2f12d6e0b946a279c7f5d47831477c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004cdc63e73d8d6f4f8770a97d93bde4e1974a277adad53d500cb070907533c09379488cb344ce01bb29a2f12d6e0b946a279c7f5d47831477c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEzcY+c9jW9Ph3CpfZO95OGXSid62tU9UA\nywcJB1M8CTeUiMs0TOAbspovEtbguUaiecf11HgxR3w=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 394, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3ff4045c3f07373b1b267197ef097b496b4ee9d9b177a67a4cf01a0c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "045e963ce90708539b001d821b9884f46ee4ba452bfcd539741d8fe8384c8dc874329bd0b78fe04e0291f13907ff5723e8d15146c5469d7d9b", "wx": "5e963ce90708539b001d821b9884f46ee4ba452bfcd539741d8fe838", "wy": "4c8dc874329bd0b78fe04e0291f13907ff5723e8d15146c5469d7d9b"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00045e963ce90708539b001d821b9884f46ee4ba452bfcd539741d8fe8384c8dc874329bd0b78fe04e0291f13907ff5723e8d15146c5469d7d9b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEXpY86QcIU5sAHYIbmIT0buS6RSv81Tl0\nHY/oOEyNyHQym9C3j+BOApHxOQf/VyPo0VFGxUadfZs=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 395, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fffffffffffffffffffffffffffb2364ae85014b149b86c741eb8be", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04aa7f2f92f8ae99c9bdac065e659a4a2a0540f21369383334613b608694da15e113f211a6e7939caf1f20acc279a3b5b5b0186451ec8603b4", "wx": "00aa7f2f92f8ae99c9bdac065e659a4a2a0540f21369383334613b6086", "wy": "0094da15e113f211a6e7939caf1f20acc279a3b5b5b0186451ec8603b4"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004aa7f2f92f8ae99c9bdac065e659a4a2a0540f21369383334613b608694da15e113f211a6e7939caf1f20acc279a3b5b5b0186451ec8603b4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEqn8vkviumcm9rAZeZZpKKgVA8hNpODM0\nYTtghpTaFeET8hGm55Ocrx8grMJ5o7W1sBhkUeyGA7Q=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 396, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00855f5b2dc8e46ec428a593f73219cf65dae793e8346e30cc3701309c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b97529fb4a1e02b5e81d77b0e7909a2fde857a1edc9fa660d600c89ae5a8c9c55885cc20f54f5c8f423f785caf423d77ba0afc31fc16f131", "wx": "00b97529fb4a1e02b5e81d77b0e7909a2fde857a1edc9fa660d600c89a", "wy": "00e5a8c9c55885cc20f54f5c8f423f785caf423d77ba0afc31fc16f131"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b97529fb4a1e02b5e81d77b0e7909a2fde857a1edc9fa660d600c89ae5a8c9c55885cc20f54f5c8f423f785caf423d77ba0afc31fc16f131", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEuXUp+0oeArXoHXew55CaL96Feh7cn6Zg\n1gDImuWoycVYhcwg9U9cj0I/eFyvQj13ugr8MfwW8TE=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 397, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c2db5f61aea817276af2064e104c7a30e32034cb526dd0aacfa56566f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04a4b344981d0a826546855d219ab4ab59d3ff330676d93b955380427841370e4dd0de4d096c20881fc35a9a0ff377b5f06deeedc19feb48ae", "wx": "00a4b344981d0a826546855d219ab4ab59d3ff330676d93b9553804278", "wy": "41370e4dd0de4d096c20881fc35a9a0ff377b5f06deeedc19feb48ae"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004a4b344981d0a826546855d219ab4ab59d3ff330676d93b955380427841370e4dd0de4d096c20881fc35a9a0ff377b5f06deeedc19feb48ae", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEpLNEmB0KgmVGhV0hmrSrWdP/MwZ22TuV\nU4BCeEE3Dk3Q3k0JbCCIH8Namg/zd7Xwbe7twZ/rSK4=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 398, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0084a6c7513e5f48c07fffffffffff8713f3cba1293e4f3e95597fe6bd", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d656048bfd65af83d6dc3f7cb7d240c9e0cdee6e946b15abb13f4b33d61580a159caf9fd87194b7c9d78753e0e00560a208d34c7dd4c6a63", "wx": "00d656048bfd65af83d6dc3f7cb7d240c9e0cdee6e946b15abb13f4b33", "wy": "00d61580a159caf9fd87194b7c9d78753e0e00560a208d34c7dd4c6a63"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d656048bfd65af83d6dc3f7cb7d240c9e0cdee6e946b15abb13f4b33d61580a159caf9fd87194b7c9d78753e0e00560a208d34c7dd4c6a63", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE1lYEi/1lr4PW3D98t9JAyeDN7m6UaxWr\nsT9LM9YVgKFZyvn9hxlLfJ14dT4OAFYKII00x91MamM=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 399, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c6c7513e5f48c07ffffffffffffff9d21fd1b31544cb13ca86a75b25e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ea6b3100290627d3e5d8a07d7d167022c58e7bdac2701735931899ac39bca56a702c508dd51bc96c63967c359e92db790f72631d875e0719", "wx": "00ea6b3100290627d3e5d8a07d7d167022c58e7bdac2701735931899ac", "wy": "39bca56a702c508dd51bc96c63967c359e92db790f72631d875e0719"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ea6b3100290627d3e5d8a07d7d167022c58e7bdac2701735931899ac39bca56a702c508dd51bc96c63967c359e92db790f72631d875e0719", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE6msxACkGJ9Pl2KB9fRZwIsWOe9rCcBc1\nkxiZrDm8pWpwLFCN1RvJbGOWfDWektt5D3JjHYdeBxk=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 400, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00d8ea27cbe9180fffffffffffffff3a43fa3662a899627950d4eb64bc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04912cff3ab4338f1f09b71f909dd68186cfabeb746beae33700dd923ff832461ccff9b83c25754bc1def8f10c5ffbdca0127914cf24184823", "wx": "00912cff3ab4338f1f09b71f909dd68186cfabeb746beae33700dd923f", "wy": "00f832461ccff9b83c25754bc1def8f10c5ffbdca0127914cf24184823"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004912cff3ab4338f1f09b71f909dd68186cfabeb746beae33700dd923ff832461ccff9b83c25754bc1def8f10c5ffbdca0127914cf24184823", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEkSz/OrQzjx8Jtx+QndaBhs+r63Rr6uM3\nAN2SP/gyRhzP+bg8JXVLwd748Qxf+9ygEnkUzyQYSCM=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 401, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3e5f48c07fffffffffffffffffffc724968c0ecf9ed783744a7337b3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "046952914ce90a0e65b03fffa7263f04a3f7be03cc6b801e1204dad313f04726e76989b5b0eabf53787d8c9f07549506d128a148a6b4e94610", "wx": "6952914ce90a0e65b03fffa7263f04a3f7be03cc6b801e1204dad313", "wy": "00f04726e76989b5b0eabf53787d8c9f07549506d128a148a6b4e94610"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00046952914ce90a0e65b03fffa7263f04a3f7be03cc6b801e1204dad313f04726e76989b5b0eabf53787d8c9f07549506d128a148a6b4e94610", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEaVKRTOkKDmWwP/+nJj8Eo/e+A8xrgB4S\nBNrTE/BHJudpibWw6r9TeH2MnwdUlQbRKKFIprTpRhA=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 402, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00bfffffffffffffffffffffffffff3d87bb44c833bb384d0f224ccdde", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0486ec9d3ba7c6d9498069a07761a9d85e04fa21fd599c6a2664e86254c5900aa4b9882b32983c5e029fe6a5578ac818e79acb20beef73e8ee", "wx": "0086ec9d3ba7c6d9498069a07761a9d85e04fa21fd599c6a2664e86254", "wy": "00c5900aa4b9882b32983c5e029fe6a5578ac818e79acb20beef73e8ee"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000486ec9d3ba7c6d9498069a07761a9d85e04fa21fd599c6a2664e86254c5900aa4b9882b32983c5e029fe6a5578ac818e79acb20beef73e8ee", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEhuydO6fG2UmAaaB3YanYXgT6If1ZnGom\nZOhiVMWQCqS5iCsymDxeAp/mpVeKyBjnmssgvu9z6O4=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 403, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fffffffffffffffffffffffffff646c95d0a029629370d8e83d717f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b08c34e9416743b6a3f52c42041a5fc3011f64dffb54875bc690393e6a7c67833682e81719f928df55faac24f488028515b26e03c1c9a02c", "wx": "00b08c34e9416743b6a3f52c42041a5fc3011f64dffb54875bc690393e", "wy": "6a7c67833682e81719f928df55faac24f488028515b26e03c1c9a02c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b08c34e9416743b6a3f52c42041a5fc3011f64dffb54875bc690393e6a7c67833682e81719f928df55faac24f488028515b26e03c1c9a02c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsIw06UFnQ7aj9SxCBBpfwwEfZN/7VIdb\nxpA5Pmp8Z4M2gugXGfko31X6rCT0iAKFFbJuA8HJoCw=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 404, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e1520", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "040a07d4a64b42e61096d0b7dd9674800ce46916617159476ecae940586f0b0584dba9d9fb5cda23a2c82e3b28b1c1486150a3419feecc4504", "wx": "0a07d4a64b42e61096d0b7dd9674800ce46916617159476ecae94058", "wy": "6f0b0584dba9d9fb5cda23a2c82e3b28b1c1486150a3419feecc4504"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00040a07d4a64b42e61096d0b7dd9674800ce46916617159476ecae940586f0b0584dba9d9fb5cda23a2c82e3b28b1c1486150a3419feecc4504", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAECgfUpktC5hCW0LfdlnSADORpFmFxWUdu\nyulAWG8LBYTbqdn7XNojosguOyixwUhhUKNBn+7MRQQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 405, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0096dafb0d7540b93b5790327082635cd8895e1e799d5d19f92b594056", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04dcb6044f8d2c6e3bab60f13e6da1896e2504d0769c2af824732b5326b80a5437b9d5ebe6d8eb49e70f49220dd3e2c3c7865b87ec98b53466", "wx": "00dcb6044f8d2c6e3bab60f13e6da1896e2504d0769c2af824732b5326", "wy": "00b80a5437b9d5ebe6d8eb49e70f49220dd3e2c3c7865b87ec98b53466"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004dcb6044f8d2c6e3bab60f13e6da1896e2504d0769c2af824732b5326b80a5437b9d5ebe6d8eb49e70f49220dd3e2c3c7865b87ec98b53466", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE3LYET40sbjurYPE+baGJbiUE0HacKvgk\ncytTJrgKVDe51evm2OtJ5w9JIg3T4sPHhluH7Ji1NGY=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 406, "comment": "point duplication during verification", "msg": "313233343030", "sig": "303e021d00c44503dae85dd5210780f02928b3d927171c578f8603d16b240663c7021d00a724b1fe4251d5b6cfa15eea5c648bfed7732fbbadc9300c8ba40032", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04dcb6044f8d2c6e3bab60f13e6da1896e2504d0769c2af824732b532647f5abc8462a14192714b618f0b6ddf12c1d3c3879a47813674acb9b", "wx": "00dcb6044f8d2c6e3bab60f13e6da1896e2504d0769c2af824732b5326", "wy": "47f5abc8462a14192714b618f0b6ddf12c1d3c3879a47813674acb9b"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004dcb6044f8d2c6e3bab60f13e6da1896e2504d0769c2af824732b532647f5abc8462a14192714b618f0b6ddf12c1d3c3879a47813674acb9b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE3LYET40sbjurYPE+baGJbiUE0HacKvgk\ncytTJkf1q8hGKhQZJxS2GPC23fEsHTw4eaR4E2dKy5s=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 407, "comment": "duplication bug", "msg": "313233343030", "sig": "303e021d00c44503dae85dd5210780f02928b3d927171c578f8603d16b240663c7021d00a724b1fe4251d5b6cfa15eea5c648bfed7732fbbadc9300c8ba40032", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b08c10bbd277ee6ab501aa71141733cb196b74de463ead5ea224022c7f68bcdeead7d74cb6186e32219846424a926dec60b2a227411ae805", "wx": "00b08c10bbd277ee6ab501aa71141733cb196b74de463ead5ea224022c", "wy": "7f68bcdeead7d74cb6186e32219846424a926dec60b2a227411ae805"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b08c10bbd277ee6ab501aa71141733cb196b74de463ead5ea224022c7f68bcdeead7d74cb6186e32219846424a926dec60b2a227411ae805", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsIwQu9J37mq1AapxFBczyxlrdN5GPq1e\noiQCLH9ovN7q19dMthhuMiGYRkJKkm3sYLKiJ0Ea6AU=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 408, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b1babefd03f6f738fc86d32ca245f5db21f67e7bd919079bd735f35bd9ad5e6886dcfc7f2272b98c48a7c43f1c57a024f2f054fc59bb4354", "wx": "00b1babefd03f6f738fc86d32ca245f5db21f67e7bd919079bd735f35b", "wy": "00d9ad5e6886dcfc7f2272b98c48a7c43f1c57a024f2f054fc59bb4354"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b1babefd03f6f738fc86d32ca245f5db21f67e7bd919079bd735f35bd9ad5e6886dcfc7f2272b98c48a7c43f1c57a024f2f054fc59bb4354", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsbq+/QP29zj8htMsokX12yH2fnvZGQeb\n1zXzW9mtXmiG3Px/InK5jEinxD8cV6Ak8vBU/Fm7Q1Q=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 409, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04222b7566f26c8776066f2f4065e4e6ac45a306c306aeab8250b652fcbc63004961a554047c03655cea7dac88e573fd45ca4b407b63967290", "wx": "222b7566f26c8776066f2f4065e4e6ac45a306c306aeab8250b652fc", "wy": "00bc63004961a554047c03655cea7dac88e573fd45ca4b407b63967290"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004222b7566f26c8776066f2f4065e4e6ac45a306c306aeab8250b652fcbc63004961a554047c03655cea7dac88e573fd45ca4b407b63967290", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEIit1ZvJsh3YGby9AZeTmrEWjBsMGrquC\nULZS/LxjAElhpVQEfANlXOp9rIjlc/1FyktAe2OWcpA=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 410, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021d00db6db6db6db6db6db6db6db6db6ceed4c09e84c77ebd9116e17391eb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04f9973f688705dc1025beb72663df6f84bda4a14a79e5953d699269ba886f01e609345596494468378d758c618f49e216287d85554af8eb68", "wx": "00f9973f688705dc1025beb72663df6f84bda4a14a79e5953d699269ba", "wy": "00886f01e609345596494468378d758c618f49e216287d85554af8eb68"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004f9973f688705dc1025beb72663df6f84bda4a14a79e5953d699269ba886f01e609345596494468378d758c618f49e216287d85554af8eb68", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE+Zc/aIcF3BAlvrcmY99vhL2koUp55ZU9\naZJpuohvAeYJNFWWSURoN411jGGPSeIWKH2FVUr462g=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 411, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c108ca8adeef82f140cf158a70d31f0a5b66d86612b5d4f00e2433865cf0f2fa4466eb35279ca99def17864835954a74d12e6c7e8494060b", "wx": "00c108ca8adeef82f140cf158a70d31f0a5b66d86612b5d4f00e243386", "wy": "5cf0f2fa4466eb35279ca99def17864835954a74d12e6c7e8494060b"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004c108ca8adeef82f140cf158a70d31f0a5b66d86612b5d4f00e2433865cf0f2fa4466eb35279ca99def17864835954a74d12e6c7e8494060b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEwQjKit7vgvFAzxWKcNMfCltm2GYStdTw\nDiQzhlzw8vpEZus1J5ypne8Xhkg1lUp00S5sfoSUBgs=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 412, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021d00cccccccccccccccccccccccccccc121be6fa59cb431754377d168831", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "040ac469e816565a6a74bec821ca20b88a61018a0335199b5d7bd969e9463355c73a2d9c51049f7a94509a9ac4ea0165763ba113a30db999e2", "wx": "0ac469e816565a6a74bec821ca20b88a61018a0335199b5d7bd969e9", "wy": "463355c73a2d9c51049f7a94509a9ac4ea0165763ba113a30db999e2"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00040ac469e816565a6a74bec821ca20b88a61018a0335199b5d7bd969e9463355c73a2d9c51049f7a94509a9ac4ea0165763ba113a30db999e2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAECsRp6BZWWmp0vsghyiC4imEBigM1GZtd\ne9lp6UYzVcc6LZxRBJ96lFCamsTqAWV2O6ETow25meI=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 413, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c249249249249249249249249249227ce201a6b76951f982e7ae89852", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04662eb328d24fb548f51792b5563ac3cb237f0a65199460eebfcaa5c3e758d13e7b419b417c8c97c4727c39fd373045e18792ad8076b072b4", "wx": "662eb328d24fb548f51792b5563ac3cb237f0a65199460eebfcaa5c3", "wy": "00e758d13e7b419b417c8c97c4727c39fd373045e18792ad8076b072b4"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004662eb328d24fb548f51792b5563ac3cb237f0a65199460eebfcaa5c3e758d13e7b419b417c8c97c4727c39fd373045e18792ad8076b072b4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEZi6zKNJPtUj1F5K1VjrDyyN/CmUZlGDu\nv8qlw+dY0T57QZtBfIyXxHJ8Of03MEXhh5KtgHawcrQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 414, "comment": "extreme value for k", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c0eb10e5ab95facded4061029d63a46f46f12947411f2ea561a592057", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ae6d9c0656d363b21e562b56f3c85b6d77a6acc664ad5705341ac262dd3a814ff8f9049e4635351d25669b50b51b30e2a5bafc9fce171e90", "wx": "00ae6d9c0656d363b21e562b56f3c85b6d77a6acc664ad5705341ac262", "wy": "00dd3a814ff8f9049e4635351d25669b50b51b30e2a5bafc9fce171e90"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ae6d9c0656d363b21e562b56f3c85b6d77a6acc664ad5705341ac262dd3a814ff8f9049e4635351d25669b50b51b30e2a5bafc9fce171e90", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAErm2cBlbTY7IeVitW88hbbXemrMZkrVcF\nNBrCYt06gU/4+QSeRjU1HSVmm1C1GzDipbr8n84XHpA=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 415, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04aa93b40a88f172a1481aad312f4bcc0a7d92103e42feeb986bcdc4a4e62a7c2e4dd34fb7e518630feea54a6581ce5ffaaca7deea3b26d647", "wx": "00aa93b40a88f172a1481aad312f4bcc0a7d92103e42feeb986bcdc4a4", "wy": "00e62a7c2e4dd34fb7e518630feea54a6581ce5ffaaca7deea3b26d647"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004aa93b40a88f172a1481aad312f4bcc0a7d92103e42feeb986bcdc4a4e62a7c2e4dd34fb7e518630feea54a6581ce5ffaaca7deea3b26d647", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEqpO0CojxcqFIGq0xL0vMCn2SED5C/uuY\na83EpOYqfC5N00+35RhjD+6lSmWBzl/6rKfe6jsm1kc=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 416, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303e021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021d00db6db6db6db6db6db6db6db6db6ceed4c09e84c77ebd9116e17391eb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0442f4cf939e3aa83c2bf17cbe8be930695aed3feea7e64adf6842d71ba17b9dd00589212e33bc190051be877a455622c22adcce8ee098fbee", "wx": "42f4cf939e3aa83c2bf17cbe8be930695aed3feea7e64adf6842d71b", "wy": "00a17b9dd00589212e33bc190051be877a455622c22adcce8ee098fbee"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000442f4cf939e3aa83c2bf17cbe8be930695aed3feea7e64adf6842d71ba17b9dd00589212e33bc190051be877a455622c22adcce8ee098fbee", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEQvTPk546qDwr8Xy+i+kwaVrtP+6n5krf\naELXG6F7ndAFiSEuM7wZAFG+h3pFViLCKtzOjuCY++4=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 417, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0455a918f3c19077432b22ceee7c2ff87dfe9ba7323c55237ee75f02de110e99ed5be3ba0e0306257712168712afaee008163a693b601f5039", "wx": "55a918f3c19077432b22ceee7c2ff87dfe9ba7323c55237ee75f02de", "wy": "110e99ed5be3ba0e0306257712168712afaee008163a693b601f5039"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000455a918f3c19077432b22ceee7c2ff87dfe9ba7323c55237ee75f02de110e99ed5be3ba0e0306257712168712afaee008163a693b601f5039", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEVakY88GQd0MrIs7ufC/4ff6bpzI8VSN+\n518C3hEOme1b47oOAwYldxIWhxKvruAIFjppO2AfUDk=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 418, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303e021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021d00cccccccccccccccccccccccccccc121be6fa59cb431754377d168831", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04bf184f88a452b3bf85edaea150dd770e90f49c871b5020a0a40deadc28e7f6a690338ef4fec8847e6f085dc470c64012b7a747624bc9c90a", "wx": "00bf184f88a452b3bf85edaea150dd770e90f49c871b5020a0a40deadc", "wy": "28e7f6a690338ef4fec8847e6f085dc470c64012b7a747624bc9c90a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004bf184f88a452b3bf85edaea150dd770e90f49c871b5020a0a40deadc28e7f6a690338ef4fec8847e6f085dc470c64012b7a747624bc9c90a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEvxhPiKRSs7+F7a6hUN13DpD0nIcbUCCg\npA3q3Cjn9qaQM470/siEfm8IXcRwxkASt6dHYkvJyQo=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 419, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c249249249249249249249249249227ce201a6b76951f982e7ae89852", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04872f112ce4aa0ff8efe70543dc1c6b0bd14d068bc88eb6436245481ba2aeb521e472e5e1921af16210fa63d3eee42df5d5ff31e6d0761b5e", "wx": "00872f112ce4aa0ff8efe70543dc1c6b0bd14d068bc88eb6436245481b", "wy": "00a2aeb521e472e5e1921af16210fa63d3eee42df5d5ff31e6d0761b5e"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004872f112ce4aa0ff8efe70543dc1c6b0bd14d068bc88eb6436245481ba2aeb521e472e5e1921af16210fa63d3eee42df5d5ff31e6d0761b5e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEhy8RLOSqD/jv5wVD3BxrC9FNBovIjrZD\nYkVIG6KutSHkcuXhkhrxYhD6Y9Pu5C311f8x5tB2G14=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 420, "comment": "extreme value for k", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c0eb10e5ab95facded4061029d63a46f46f12947411f2ea561a592057", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34", "wx": "00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21", "wy": "00bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEtw4MvWu0v38yE5C5SgPB01bCESI0MoDW\nEVwdIb03Y4i19yP7TCLf5s1DdaBaB0dkRNWBmYUAfjQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 421, "comment": "testing point duplication", "msg": "313233343030", "sig": "303c021c342dae751a63a3ca8189cf342b3b34eaaa2565e2c7e26121c1bfd543021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}, {"tcId": 422, "comment": "testing point duplication", "msg": "313233343030", "sig": "303d021d00cbd2518ae59c5c357e7630cbd4c3e1b836938a5b4bfac8239a9c54fa021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d2142c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd", "wx": "00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21", "wy": "42c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d2142c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEtw4MvWu0v38yE5C5SgPB01bCESI0MoDW\nEVwdIULInHdKCNwEs90gGTK8il6l+Libuyp+Znr/gc0=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 423, "comment": "testing point duplication", "msg": "313233343030", "sig": "303c021c342dae751a63a3ca8189cf342b3b34eaaa2565e2c7e26121c1bfd543021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}, {"tcId": 424, "comment": "testing point duplication", "msg": "313233343030", "sig": "303d021d00cbd2518ae59c5c357e7630cbd4c3e1b836938a5b4bfac8239a9c54fa021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176", "wx": "4c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466", "wy": "00ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAETCRmcGWKHUH113vOJGy+OGrCKEjiabnU\nzWfEZt3ZRxU9ObLUJTOkYN7yaIBAjK8t091I/oiM0XY=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 425, "comment": "pseudorandom signature", "msg": "", "sig": "303d021d008de3b662a51308a2dc0651a2f50bb3475376e90bb8418256cd791bcb021c0910c5c50a32a24aad84da25559dbf077e5337f3c3f626fb15d376dc", "result": "valid", "flags": []}, {"tcId": 426, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "303d021d00dcedb02afdffdd199b47e545e1396a1b170fdf96a10ee4d3add2b496021c4a3c894b80de1bcda509ab58752e0056dc78d6683a85ca9f15c251b9", "result": "valid", "flags": []}, {"tcId": 427, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "303e021d00a7cab432dc7abf269c13632589d9d9cda37482db9e8dcf411c2344a4021d00fb38ff0bea5f81595615608ad33494cceaec48ee6007d4951d2d5bd0", "result": "valid", "flags": []}, {"tcId": 428, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "303c021c0316a2259370447c1b06f51f6189b033ead956c11ebbc3edbdc2b5e4021c24bda2d065a0475c9fc5ee300f6c4c826b07a740d4990fcc146006aa", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf8a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000", "wx": "00aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf", "wy": "008a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf8a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAErtb8rSQAxNlOVdu2sBLOPUwrRoQ/vpnU\nKJ5uz4okqJ5xND19FR0ljSy2kDScLVazZt0QpgAAAAA=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 429, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303e021d00c8a93a8f1b3aaaa9bb8e2d8cc4f5b5033a909b15ad81b2dae14c1620021d00e686820912f295c9c1b31e60c1fa27d8d0f49c017249380f928ecff2", "result": "valid", "flags": []}, {"tcId": 430, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021c3989321934702e535635459d9409606cfc4634c3b282fa86a93c8016021d00c1f6c399ccb2ae5a22e806f1413874fe57cec917b576d3ed887e38dc", "result": "valid", "flags": []}, {"tcId": 431, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303e021d008c97232cd430bb32dd89f5155205f6ae03f173f88a1be96a2c33d9e1021d00fb3b4eb539553d6645e434befa760c9fab3d2ae645ced03fe7713aa1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f173d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff", "wx": "00bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f1", "wy": "73d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f173d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEvxns/kP/4on2mfR5MWFFuaf3Nwuezlqx\nISF08XPVKJSa6RQvgYut5xqWBAeWO+C2SCpqYP////8=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 432, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021c72791a86439f76ee409629d923fab73936510ef89349ee4cc304b915021d00ed67c5eedc4a59af13fe80905199de42fbc45eaad6bd44329c7c75cf", "result": "valid", "flags": []}, {"tcId": 433, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021c24575ff9e5ea779d95644b5ef9affa6727978553df3b51fbc5a27820021d008b2000b4c02a1fcc7880a42c6dbe07e322ebe20940d6ff32a08eadd9", "result": "valid", "flags": []}, {"tcId": 434, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021d00e55819d63e0cdb616676d16d1ad6ca2bb979be94924534dba3fd6f3a021c4dbc47c830fd85e16d013e056b2f0b1646d048cd6fc21757af428f05", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0426e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0", "wx": "26e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000", "wy": "00eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000426e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEJuWr8TXLVOqqFraeSwspInU0Toignfbf\ngAAAAOq4kd5U4/Jv9Qq5ifMz2sVRWD1GiuYjxZZDSvA=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 435, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303e021d00d12c22b4ef1ff2de70c6e1a2c18f7dc87dfcfec225cb3f324a76654b021d00b64bd7dd3e3184073acf584bfd33dbc3712a89f201386312f713e1da", "result": "valid", "flags": []}, {"tcId": 436, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021d00c8d06f039201c03995813152a19fe52e3c38cf32b0f13b8d8cec87ba021c449307f7924fa276ac1ca82973d5f55fca7c6690c8581dbbe5500128", "result": "valid", "flags": []}, {"tcId": 437, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021d00a370d277f28e3c86c9367e2c1d7ae07e6b0da333da65ef2780b39e00021c134f5f55e3752543f960e0e7257cffe3ec417b9bc5da3b7557de44ea", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd", "wx": "00ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff", "wy": "41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE7GJ/NFVF0D+Mbb0I5XVScRZWf+N1+eyq\n/////0G/cFaX1fcWvPeHGNU5O2OphpH0ofJCRjdVOP0=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 438, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021c2336af69da825fec3510681c716c9b5000b17ef1e6db73707817b145021d009ef59c120ce39e4c83e341af71d4a91a34c4bdd12c92caf4405b794d", "result": "valid", "flags": []}, {"tcId": 439, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303c021c3fdb17cc44c9a0e995290a458c2f64b8565541cf56139575212ec168021c5e51c778b560f3c61bc3bf0eb50ff8d34dcb5eb85cc25b4ae6a7443a", "result": "valid", "flags": []}, {"tcId": 440, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303c021c1d98253a63b83f0e042ed2c342fd4217c29f990fcd5b5cbb9e51721f021c49fcf5e4d680cdd36405c514414cc47d9731a97c4edcbac7b7b27f89", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a00000000762d28f1fdc219184f81681fbff566d465b5f1f31e872df5", "wx": "15016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a", "wy": "762d28f1fdc219184f81681fbff566d465b5f1f31e872df5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a00000000762d28f1fdc219184f81681fbff566d465b5f1f31e872df5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEFQFuUrNkctU2R3YF+4Bd05AwgqBi0eow\nr55VWgAAAAB2LSjx/cIZGE+BaB+/9WbUZbXx8x6HLfU=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 441, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d00a0f6aa5f607f34e49af976d8b0fb4f42da17fd1c2b03b8119f7b834b021d00ab5395c0faa4ee5310625d501d6a0af96644040a335ff8f42bad65d3", "result": "valid", "flags": []}, {"tcId": 442, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d009a0f67792672dcba4b19899ba2e35ac0b54ca00ca4957270e7a43f8c021d00a702887888511dd12e950eddd239b4a3c423da673bba882082954a0a", "result": "valid", "flags": []}, {"tcId": 443, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303c021c4d96e743ac0dbbf2413725a193c7e70947b59501601e337665023ee2021c764ef71a184f0244c0e1de1b729b8421be53bd0ded2015dc3d1f1a13", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555affffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c", "wx": "15016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a", "wy": "00ffffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555affffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEFQFuUrNkctU2R3YF+4Bd05AwgqBi0eow\nr55VWv////+J0tcOAj3m57B+l99ACpkrmkoODOF40gw=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 444, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303c021c060ac961eff4e5053f7045b9ea1db338dd4b3a6cce331386d3988655021c0cfc61a43a67d0660fc386efc1b603b28f651885bff519c632e11a8c", "result": "valid", "flags": []}, {"tcId": 445, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303c021c3415d0790d9b9ba8ffa82f447a4fed58cce3ebbaf43b68c162495c85021c1e0e20ac93d19dbd9ddd632e5c0910560e941acf2ba5d7f3f2abe27f", "result": "valid", "flags": []}, {"tcId": 446, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303c021c2066593be10db4111e032cce79004a2e66aff1774d595ad4c37b61d6021c2aaddfe2abcbb137ebf80d35c86c7f81fc760640eec39eb3abe9ee7d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0400000000f7e4713d085112112c37cdf4601ff688da796016b71a727ade5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1", "wx": "00f7e4713d085112112c37cdf4601ff688da796016b71a727a", "wy": "00de5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000400000000f7e4713d085112112c37cdf4601ff688da796016b71a727ade5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEAAAAAPfkcT0IURIRLDfN9GAf9ojaeWAW\ntxpyet5ansFlBUzJh/nch+mZG5Lk+mScplXurp8qMOE=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 447, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021d00d5751d00e69e86302d95f3a485c357867b31c1f726021f8318330eeb021c36e15c9ab9152ca24bd32edbd0de3a10e7ccb23b493c3827009798bd", "result": "valid", "flags": []}, {"tcId": 448, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303c021c2d738785b04282261457a9b149a6c51c4c611f29e14113bbd2625981021c6cacb7671751394b931ab86a09631ee26cd077c01af8c3491b535982", "result": "valid", "flags": []}, {"tcId": 449, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303c021c121f5992ff2892a8e0b36cf4f31331fddcb9b53d1aec2c7d9cd887cb021c406a219f572c58baab779d8c386fe8e84857024a21c01f949505a668", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f72586c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e", "wx": "00ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f725", "wy": "0086c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f72586c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE/////+rffO6NNNBM8iyPfeNWdPsvUB0k\nKnb3JYbECTCdOY5gzh4KTJ4FqdMmJ1d+jOLMfzr6LD4=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 450, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303e021d00c28e4e9a7f4a96f22bab95a3f564099ec46dda7ed0d1568b3474fa1f021d00a7d93ad705f4604bf82dc029d2257917d1eb7e09d4799637bbc31661", "result": "valid", "flags": []}, {"tcId": 451, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021d008e5a9b74c35d07b2dc788a9baf13764bccf2570a07cb4b51e52c36f4021c1ebaa0563536f7ae6337e446f9ff9940901b4b1e6c8a6af283d56b5e", "result": "valid", "flags": []}, {"tcId": 452, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303e021d00975b1ef86996b5362792d0444e1cb8c64fb583b91477853162ed7914021d00abca04859e3df4308d9d40b33798c2f0907dd073ee7587646acb2f2e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1000000000e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc", "wx": "00b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1", "wy": "0e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1000000000e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsAE8b7/28J/s2hwmPvZTmdTPmJyl/E+P\n/w/p4QAAAAAOKrDoSV6FnrKvsAdp1uf+YmoRkWfAtrw=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 453, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d00dcfda67794874a4613c1597658dc52ff8504de9db45a2909894052fd021d00eb8fbc6e67b20309b9c0c1315bd2883029e049b77033fdc0be6a0e89", "result": "valid", "flags": []}, {"tcId": 454, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021d00ad39c764452e94c39dc651bca149bff4e37b7e1856ab3d40625f952e021c0f454f1ed191e8cedcb9c290758bd4b9747a32b814852b1da419d1b6", "result": "valid", "flags": []}, {"tcId": 455, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d009ff602d8037ef12b2f22858411a992a21c554fe10e07567338a03412021d0092d174d805bd0eae1093b20c4b3a74f9e09dab0a292d4147173874ea", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945", "wx": "00b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1", "wy": "00fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsAE8b7/28J/s2hwmPvZTmdTPmJyl/E+P\n/w/p4f/////x1U8XtqF6YU1QT/eWKRgBnZXubpg/SUU=\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 456, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021c5041006f1dd06d3bebcaaf4d10e1371998d871c04fe2f730b43ec025021d00cd2ac83465809b3c658115d286a8a00f67db8a1068f84ecab418bb0d", "result": "valid", "flags": []}, {"tcId": 457, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021d00ee587c28d2210b0cff7d47ac3509f590053320e547d3e034df9c9a50021c215374b4e8541ae4974974ed7575d2c1550d924708e303c0b744ee77", "result": "valid", "flags": []}, {"tcId": 458, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303e021d00b66a579ea2dca40e3a48074567af7daad34fd784e3ed097d1b39569c021d0094a2b72c713c109153863ebb71a8cc80f57094d811c13fa269e14a42", "result": "valid", "flags": []}]}]}