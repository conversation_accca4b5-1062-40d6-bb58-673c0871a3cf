---
title: QR Generator
description: Example and explanation of a plugin that generates QR codes using the .qr command.
icon: QrCode
---

## Introduction

This page explains how to create a plugin that generates QR codes from text or URLs using the `.qr` command. The plugin demonstrates how to use network APIs, handle files, manage plugin settings, and interact with the Telegram UI.

## Basic Structure

The QR Generator plugin follows the standard plugin structure:

- Metadata variables (`__id__`, `__name__`, etc.)
- Inherits from `BasePlugin`
- Implements methods for plugin lifecycle and message hooks

```python
__id__ = "qrcode"
__name__ = "QR Generator"
__description__ = "Generate QR codes with .qr command"
__author__ = "@exteraDev"
__min_version__ = "11.9.0"
```

## How It Works

When a user sends a message starting with `.qr`, the plugin:

1. Extracts the text after `.qr`.
2. Calls an external API to generate a QR code image.
3. Saves the image to a temporary directory.
4. Sends the image as a photo in the chat, optionally with a caption.

All network and file operations are performed in a background thread to avoid blocking the UI.

## Key Components

### Settings

The plugin provides customizable settings for QR code size, error correction, format, margin, and message options. These are defined in the `create_settings` method and support multiple languages.

```python
def create_settings(self):
    return [
        Header(text="QR Code Settings"),
        Selector(key="qr_size", text="QR Code Size", default=1, items=["Small (256x256)", "Medium (384x384)", "Large (512x512)", "Extra Large (768x768)"]),
        Selector(key="error_correction", text="Error Correction Level", default=1, items=["Low (L)", "Medium (M)", "Quartile (Q)", "High (H)"]),
        Selector(key="format", text="QR Format", default=0, items=["PNG", "GIF", "JPEG", "JPG"]),
        Switch(key="add_margin", text="Add Margin", default=True, subtext="Add a white margin around the QR code"),
        Divider(),
        Header(text="Message Options"),
        Switch(key="show_caption", text="Add Caption", default=False, subtext="Include the encoded text as a caption"),
        Divider(text="Usage: .qr [text or URL to encode]"),
    ]
```

### Handling the .qr Command

The plugin intercepts outgoing messages using `on_send_message_hook`. If the message starts with `.qr`, it processes the command:

```python
def on_send_message_hook(self, account, params):
    if not hasattr(params, 'message') or not isinstance(params.message, str):
        return HookResult()
    msg = params.message.strip()
    if not msg.startswith(".qr"):
        return HookResult()
    text_to_encode = msg[3:].strip()
    if not text_to_encode:
        show_error_bulletin("No text provided after .qr")
        return HookResult(strategy=HookStrategy.CANCEL)
    # Show progress dialog and start QR generation in a background thread
    progress_dialog = AlertDialog(get_last_fragment().getParentActivity(), 3)
    progress_dialog.show()
    threading.Thread(target=self._process_qr, args=(account, params, text_to_encode), daemon=True).start()
    return HookResult(strategy=HookStrategy.CANCEL)
```

### Generating and Sending the QR Code

The `_process_qr` method:

- Calls the QR API to generate the image.
- Saves the image to a temporary file.
- Uses Telegram's API to send the image as a photo.
- Optionally adds a caption.
- Cleans up temporary files after sending.

All UI updates (like showing bulletins) are performed on the UI thread.

## Example Usage

Send a message like:

```
.qr https://github.com
```

The plugin will reply with a QR code image representing the provided URL.

## Complete Example

```python
import requests
import os
import uuid
import time
import threading
from java.io import File
from java.util import Locale
from org.telegram.messenger import ApplicationLoader
from org.telegram.ui.ActionBar import AlertDialog
from settings import Header, Selector, Switch, Divider
from base_plugin import BasePlugin, HookResult, HookStrategy
from android_utils import log
from client_utils import get_send_messages_helper, get_last_fragment, show_error_bulletin, show_success_bulletin

__id__ = "qrcode"
__name__ = "QR Generator"
__description__ = "Generate QR codes with .qr command"
__author__ = "@exteraDev"
__min_version__ = "11.9.0"
__icon__ = "SpottyAnimated/43"

QR_API_URL = "https://api.qrserver.com/v1/create-qr-code/"
TEMP_IMAGES_DIR_NAME = "exteraGram Images"
QR_FORMATS = ["png", "gif", "jpeg", "jpg"]

progress_dialog = None

class QRGeneratorPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self._images_dir = None

    def on_plugin_load(self):
        self._images_dir = self._get_images_dir()
        if self._images_dir:
            log("QR Generator plugin loaded successfully")
            self._cleanup_old_files()
        else:
            log("Failed to initialize images directory for QR Generator plugin")

    def on_plugin_unload(self):
        log("QR Generator plugin unloaded")

    def create_settings(self):
        lang = Locale.getDefault().getLanguage()
        if lang.startswith("pt"):
            header = "Configurações do QR Code"
            size_label = "Tamanho do QR Code"
            size_items = ["Pequeno (256x256)", "Médio (384x384)", "Grande (512x512)", "Extra (768x768)"]
            ecc_label = "Nível de Correção de Erro"
            ecc_items = ["Baixo (L)", "Médio (M)", "Quartil (Q)", "Alto (H)"]
            format_label = "Formato"
            format_items = ["PNG", "GIF", "JPEG", "JPG"]
            margin_label = "Adicionar Margem"
            margin_sub = "Adiciona uma margem branca ao redor do QR"
            msg_header = "Opções de Mensagem"
            caption_label = "Adicionar Legenda"
            caption_sub = "Inclui o texto codificado como legenda"
            usage = "Uso: .qr [texto ou URL]"
        elif lang.startswith("ru"):
            header = "Настройки QR-кода"
            size_label = "Размер QR-кода"
            size_items = ["Маленький (256x256)", "Средний (384x384)", "Большой (512x512)", "Очень большой (768x768)"]
            ecc_label = "Уровень коррекции ошибок"
            ecc_items = ["Низкий (L)", "Средний (M)", "Квартиль (Q)", "Высокий (H)"]
            format_label = "Формат"
            format_items = ["PNG", "GIF", "JPEG", "JPG"]
            margin_label = "Добавить отступ"
            margin_sub = "Добавить белую рамку вокруг QR-кода"
            msg_header = "Параметры сообщения"
            caption_label = "Добавить подпись"
            caption_sub = "Включить закодированный текст как подпись"
            usage = "Использование: .qr [текст или URL]"
        else:
            header = "QR Code Settings"
            size_label = "QR Code Size"
            size_items = ["Small (256x256)", "Medium (384x384)", "Large (512x512)", "Extra Large (768x768)"]
            ecc_label = "Error Correction Level"
            ecc_items = ["Low (L)", "Medium (M)", "Quartile (Q)", "High (H)"]
            format_label = "QR Format"
            format_items = ["PNG", "GIF", "JPEG", "JPG"]
            margin_label = "Add Margin"
            margin_sub = "Add a white margin around the QR code"
            msg_header = "Message Options"
            caption_label = "Add Caption"
            caption_sub = "Include the encoded text as a caption"
            usage = "Usage: .qr [text or URL to encode]"
        return [
            Header(text=header),
            Selector(key="qr_size", text=size_label, default=1, items=size_items),
            Selector(key="error_correction", text=ecc_label, default=1, items=ecc_items),
            Selector(key="format", text=format_label, default=0, items=format_items),
            Switch(key="add_margin", text=margin_label, default=True, subtext=margin_sub),
            Divider(),
            Header(text=msg_header),
            Switch(key="show_caption", text=caption_label, default=False, subtext=caption_sub),
            Divider(text=usage),
        ]

    def _get_images_dir(self):
        try:
            base_dir = ApplicationLoader.getFilesDirFixed()
            if not base_dir:
                log("Failed to get base directory via ApplicationLoader")
                return None
            images_dir = File(base_dir, TEMP_IMAGES_DIR_NAME)
            if not images_dir.exists():
                if not images_dir.mkdirs():
                    log(f"Failed to create images directory: {images_dir.getAbsolutePath()}")
                    return None
            if not images_dir.isDirectory():
                log(f"Images path is not a directory: {images_dir.getAbsolutePath()}")
                return None
            return images_dir
        except Exception as e:
            log(f"Error getting/creating images directory: {e}")
            return None

    def _cleanup_old_files(self, max_age_hours=24):
        try:
            images_dir = self._get_images_dir()
            if not images_dir or not images_dir.isDirectory():
                return
            now = time.time()
            max_age_seconds = max_age_hours * 3600
            for file in images_dir.listFiles():
                if file.getName().startswith("qr_") and now - file.lastModified() / 1000 > max_age_seconds:
                    file.delete()
        except Exception as e:
            log(f"Error cleaning up old QR files: {e}")

    def _get_qr_size(self):
        return ["256x256", "384x384", "512x512", "768x768"][self.get_setting("qr_size", 1)]

    def _get_format_extension(self):
        return QR_FORMATS[self.get_setting("format", 0)]

    def _get_error_correction(self):
        return ["L", "M", "Q", "H"][self.get_setting("error_correction", 1)]

    def _generate_qr_code(self, text):
        temp_photo_path = None
        try:
            images_dir = self._get_images_dir()
            if not images_dir or not images_dir.isDirectory():
                show_error_bulletin("Images dir is invalid ou não é um diretório")
                return None
            format_ext = self._get_format_extension()
            filename = f"qr_{uuid.uuid4()}.{format_ext}"
            temp_photo_path = File(images_dir, filename).getAbsolutePath()
            params = {
                'data': text,
                'size': self._get_qr_size(),
                'ecc': self._get_error_correction(),
                'margin': 10 if self.get_setting("add_margin", True) else 0,
                'format': format_ext
            }
            response = requests.get(QR_API_URL, params=params, stream=True, timeout=15)
            if response.status_code != 200:
                show_error_bulletin(f"QR API error: {response.status_code}")
                return None
            content_type = response.headers.get('Content-Type', '')
            if not content_type.startswith('image/'):
                show_error_bulletin("API did not return an image")
                return None
            with open(temp_photo_path, 'wb') as f:
                wrote = False
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        wrote = True
                if not wrote:
                    show_error_bulletin("Failed to save QR file")
                    return None
            if not os.path.exists(temp_photo_path) or os.path.getsize(temp_photo_path) == 0:
                show_error_bulletin("QR file not created or is empty")
                return None
            return temp_photo_path
        except Exception as e:
            show_error_bulletin(f"Error generating QR: {e}")
            if temp_photo_path and os.path.exists(temp_photo_path):
                try:
                    os.remove(temp_photo_path)
                except Exception:
                    pass
            return None

    def _delete_temp_file_async(self, file_path, delay_seconds=5):
        def delete_action():
            try:
                time.sleep(delay_seconds)
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                log(f"Error deleting temp file: {e}")
        threading.Thread(target=delete_action, daemon=True).start()

    def _dismiss_dialog(self):
        global progress_dialog
        try:
            if progress_dialog is not None and progress_dialog.isShowing():
                progress_dialog.dismiss()
        except Exception:
            pass
        finally:
            progress_dialog = None

    def _process_qr(self, account, params, text_to_encode):
        global progress_dialog
        try:
            temp_file_path = self._generate_qr_code(text_to_encode)
            if not temp_file_path:
                self._dismiss_dialog()
                return
            send_helper = get_send_messages_helper()
            generated_photo = send_helper.generatePhotoSizes(temp_file_path, None)
            if not generated_photo:
                show_error_bulletin("Failed to process QR image")
                self._delete_temp_file_async(temp_file_path)
                self._dismiss_dialog()
                return
            params.photo = generated_photo
            params.path = temp_file_path
            if self.get_setting("show_caption", False):
                params.caption = text_to_encode
            params.message = None
            if not hasattr(params, "entities") or params.entities is None:
                params.entities = []
            show_success_bulletin("QR code generated successfully")
            self._delete_temp_file_async(temp_file_path)
            self._dismiss_dialog()
            from android_utils import run_on_ui_thread
            run_on_ui_thread(lambda: send_helper.sendMessage(params))
        except Exception as e:
            show_error_bulletin(f"Unexpected error: {e}")
            if 'temp_file_path' in locals() and temp_file_path:
                self._delete_temp_file_async(temp_file_path)
            self._dismiss_dialog()

    def on_send_message_hook(self, account, params):
        if not hasattr(params, 'message') or not isinstance(params.message, str):
            return HookResult()
        msg = params.message.strip()
        lang = Locale.getDefault().getLanguage()
        if lang.startswith("pt"):
            no_text_msg = "Nenhum texto informado após .qr"
        else:
            no_text_msg = "No text provided after .qr"
        if not msg.startswith(".qr"):
            return HookResult()
        text_to_encode = msg[3:].strip()
        if not text_to_encode:
            show_error_bulletin(no_text_msg)
            return HookResult(strategy=HookStrategy.CANCEL)
        global progress_dialog
        try:
            progress_dialog = AlertDialog(get_last_fragment().getParentActivity(), 3)
            progress_dialog.show()
        except Exception as e:
            log(f"Failed to show progress dialog: {e}")
        threading.Thread(target=self._process_qr, args=(account, params, text_to_encode), daemon=True).start()
        return HookResult(strategy=HookStrategy.CANCEL)
```

## Tips

- All network and file operations are done in background threads to keep the UI responsive.
- The plugin cleans up old QR images automatically.
- Settings allow users to customize the QR code output.

## What’s Next?

Try sending `.qr Hello World` or any URL to see the plugin in action. Explore the code to learn about background processing, file management, and Telegram API integration for plugins.
