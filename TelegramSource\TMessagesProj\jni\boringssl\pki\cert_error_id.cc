// Copyright 2016 The Chromium Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "cert_error_id.h"

BSSL_NAMESPACE_BEGIN

const char *CertErrorIdToDebugString(CertErrorId id) {
  // The CertErrorId is simply a pointer for a C-string literal.
  return reinterpret_cast<const char *>(id);
}

BSSL_NAMESPACE_END
