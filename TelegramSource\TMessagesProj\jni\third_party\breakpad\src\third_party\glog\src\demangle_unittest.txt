# Test caces for demangle_unittest.  Each line consists of a
# tab-separated pair of mangled and demangled symbol names.

# Constructors and destructors.
_ZN3FooC1Ev	Foo::Foo()
_ZN3FooD1Ev	Foo::~Foo()
_ZNSoD0Ev	std::ostream::~ostream()

# G++ extensions.
_ZTCN10LogMessage9LogStreamE0_So	LogMessage::LogStream
_ZTv0_n12_N10LogMessage9LogStreamD0Ev	LogMessage::LogStream::~LogStream()
_ZThn4_N7icu_3_410UnicodeSetD0Ev	icu_3_4::UnicodeSet::~UnicodeSet()

# A bug in g++'s C++ ABI version 2 (-fabi-version=2).
_ZN7NSSInfoI5groupjjXadL_Z10getgrgid_rEELZ19nss_getgrgid_r_nameEEC1Ei	NSSInfo<>::NSSInfo()

# C linkage symbol names.  Should keep them untouched.
main	main
Demangle	Demangle
_ZERO	_ZERO

# Cast operator.
_Zcviv	operator int()
_ZN3foocviEv	foo::operator int()

# Versioned symbols.
_Z3Foo@GLIBCXX_3.4	Foo@GLIBCXX_3.4
_Z3Foo@@GLIBCXX_3.4	Foo@@GLIBCXX_3.4

# Abbreviations.
_ZNSaE	std::allocator
_ZNSbE	std::basic_string
_ZNSdE	std::iostream
_ZNSiE	std::istream
_ZNSoE	std::ostream
_ZNSsE	std::string

# Substitutions.  We just replace them with ?.
_ZN3fooS_E	foo::?
_ZN3foo3barS0_E	foo::bar::?
_ZNcvT_IiEEv	operator ?<>()

# "<< <" case.
_ZlsI3fooE	operator<< <>

# Random things we found interesting.
_ZN3FooISt6vectorISsSaISsEEEclEv	Foo<>::operator()()
_ZTI9Callback1IiE	Callback1<>
_ZN7icu_3_47UMemorynwEj	icu_3_4::UMemory::operator new()
_ZNSt6vectorIbE9push_backE	std::vector<>::push_back
_ZNSt6vectorIbSaIbEE9push_backEb	std::vector<>::push_back()
_ZlsRSoRK15PRIVATE_Counter	operator<<()
_ZSt6fill_nIPPN9__gnu_cxx15_Hashtable_nodeISt4pairIKPKcjEEEjS8_ET_SA_T0_RKT1_	std::fill_n<>()
_ZZ3FoovE3Bar	Foo()::Bar
_ZGVZ7UpTimervE8up_timer	UpTimer()::up_timer

# Test cases from gcc-4.1.0/libstdc++-v3/testsuite/demangle.
# Collected by:
# % grep verify_demangle **/*.cc | perl -nle 'print $1 if /"(_Z.*?)"/' |
#   sort | uniq
#
# Note that the following symbols are invalid.
# That's why they are not demangled.
# - _ZNZN1N1fEiE1X1gE
# - _ZNZN1N1fEiE1X1gEv
# - _Z1xINiEE
_Z1fA37_iPS_	f()
_Z1fAszL_ZZNK1N1A1fEvE3foo_0E_i	f()
_Z1fI1APS0_PKS0_EvT_T0_T1_PA4_S3_M1CS8_	f<>()
_Z1fI1XENT_1tES2_	f<>()
_Z1fI1XEvPVN1AIT_E1TE	f<>()
_Z1fILi1ELc120EEv1AIXplT_cviLd4028ae147ae147aeEEE	f<>()
_Z1fILi1ELc120EEv1AIXplT_cviLf3f800000EEE	f<>()
_Z1fILi5E1AEvN1CIXqugtT_Li0ELi1ELi2EEE1qE	f<>()
_Z1fILi5E1AEvN1CIXstN1T1tEEXszsrS2_1tEE1qE	f<>()
_Z1fILi5EEvN1AIXcvimlT_Li22EEE1qE	f<>()
_Z1fIiEvi	f<>()
_Z1fKPFiiE	f()
_Z1fM1AFivEPS0_	f()
_Z1fM1AKFivE	f()
_Z1fM1AKFvvE	f()
_Z1fPFPA1_ivE	f()
_Z1fPFYPFiiEiE	f()
_Z1fPFvvEM1SFvvE	f()
_Z1fPKM1AFivE	f()
_Z1fi	f()
_Z1fv	f()
_Z1jM1AFivEPS1_	j()
_Z1rM1GFivEMS_KFivES_M1HFivES1_4whatIKS_E5what2IS8_ES3_	r()
_Z1sPA37_iPS0_	s()
_Z1xINiEE	_Z1xINiEE
_Z3absILi11EEvv	abs<>()
_Z3foo3bar	foo()
_Z3foo5Hello5WorldS0_S_	foo()
_Z3fooA30_A_i	foo()
_Z3fooIA6_KiEvA9_KT_rVPrS4_	foo<>()
_Z3fooILi2EEvRAplT_Li1E_i	foo<>()
_Z3fooIiFvdEiEvv	foo<>()
_Z3fooPM2ABi	foo()
_Z3fooc	foo()
_Z3fooiPiPS_PS0_PS1_PS2_PS3_PS4_PS5_PS6_PS7_PS8_PS9_PSA_PSB_PSC_	foo()
_Z3kooPA28_A30_i	koo()
_Z4makeI7FactoryiET_IT0_Ev	make<>()
_Z5firstI3DuoEvS0_	first<>()
_Z5firstI3DuoEvT_	first<>()
_Z9hairyfuncM1YKFPVPFrPA2_PM1XKFKPA3_ilEPcEiE	hairyfunc()
_ZGVN5libcw24_GLOBAL__N_cbll.cc0ZhUKa23compiler_bug_workaroundISt6vectorINS_13omanip_id_tctINS_5debug32memblk_types_manipulator_data_ctEEESaIS6_EEE3idsE	libcw::(anonymous namespace)::compiler_bug_workaround<>::ids
_ZN12libcw_app_ct10add_optionIS_EEvMT_FvPKcES3_cS3_S3_	libcw_app_ct::add_option<>()
_ZN1AIfEcvT_IiEEv	A<>::operator ?<>()
_ZN1N1TIiiE2mfES0_IddE	N::T<>::mf()
_ZN1N1fE	N::f
_ZN1f1fE	f::f
_ZN3FooIA4_iE3barE	Foo<>::bar
_ZN5Arena5levelE	Arena::level
_ZN5StackIiiE5levelE	Stack<>::level
_ZN5libcw5debug13cwprint_usingINS_9_private_12GlobalObjectEEENS0_17cwprint_using_tctIT_EERKS5_MS5_KFvRSt7ostreamE	libcw::debug::cwprint_using<>()
_ZN6System5Sound4beepEv	System::Sound::beep()
_ZNKSt14priority_queueIP27timer_event_request_base_ctSt5dequeIS1_SaIS1_EE13timer_greaterE3topEv	std::priority_queue<>::top()
_ZNKSt15_Deque_iteratorIP15memory_block_stRKS1_PS2_EeqERKS5_	std::_Deque_iterator<>::operator==()
_ZNKSt17__normal_iteratorIPK6optionSt6vectorIS0_SaIS0_EEEmiERKS6_	std::__normal_iterator<>::operator-()
_ZNSbIcSt11char_traitsIcEN5libcw5debug27no_alloc_checking_allocatorEE12_S_constructIPcEES6_T_S7_RKS3_	std::basic_string<>::_S_construct<>()
_ZNSt13_Alloc_traitsISbIcSt18string_char_traitsIcEN5libcw5debug9_private_17allocator_adaptorIcSt24__default_alloc_templateILb0ELi327664EELb1EEEENS5_IS9_S7_Lb1EEEE15_S_instancelessE	std::_Alloc_traits<>::_S_instanceless
_ZNSt3_In4wardE	std::_In::ward
_ZNZN1N1fEiE1X1gE	_ZNZN1N1fEiE1X1gE
_ZNZN1N1fEiE1X1gEv	_ZNZN1N1fEiE1X1gEv
_ZSt1BISt1DIP1ARKS2_PS3_ES0_IS2_RS2_PS2_ES2_ET0_T_SB_SA_PT1_	std::B<>()
_ZSt5state	std::state
_ZTI7a_class	a_class
_ZZN1N1fEiE1p	N::f()::p
_ZZN1N1fEiEs	N::f()
_ZlsRK1XS1_	operator<<()
_ZlsRKU3fooU4bart1XS0_	operator<<()
_ZlsRKU3fooU4bart1XS2_	operator<<()
_ZlsRSoRKSs	operator<<()
_ZngILi42EEvN1AIXplT_Li2EEE1TE	operator-<>()
_ZplR1XS0_	operator+()
_Zrm1XS_	operator%()
