Name: CityHash
URL: https://github.com/google/cityhash
Version: 1.1.1
Revision: 8af9b8c
License: MIT
License File: COPYING
Security Critical: yes

Description:
This is a fast non-cryptographic hash function.

There are currently two distinct sets of CityHash functions:
v1.0.3 and v1.1+ that produce distinct outputs.

The version in //third_party/smhasher is 1.0.3 and has some hash
quality issues that led to non-backwards compatible changes in v1.1+.

Local changes:
- guarded function declaration (i.e. CityHash64) within a namespace
(base::internal::cityhash_v111).
- defined bswap_32/bswap_64 to use compiler builtins to make 'native_client'
build pass.
- remove unneeded CRC32 stuff.
- formating to make 'git cl format' happy.
