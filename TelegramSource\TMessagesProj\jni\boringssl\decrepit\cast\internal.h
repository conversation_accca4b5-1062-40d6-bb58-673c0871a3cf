// Copyright 1995-2016 The OpenSSL Project Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#ifndef OPENSSL_HEADER_CAST_INTERNAL_H
#define OPENSSL_HEADER_CAST_INTERNAL_H

#include <openssl/base.h>

#if defined(__cplusplus)
extern "C" {
#endif


extern const uint32_t CAST_S_table0[256];
extern const uint32_t CAST_S_table1[256];
extern const uint32_t CAST_S_table2[256];
extern const uint32_t CAST_S_table3[256];
extern const uint32_t CAST_S_table4[256];
extern const uint32_t CAST_S_table5[256];
extern const uint32_t CAST_S_table6[256];
extern const uint32_t CAST_S_table7[256];


#if defined(__cplusplus)
}  // extern C
#endif

#endif  // OPENSSL_HEADER_CAST_INTERNAL_H
