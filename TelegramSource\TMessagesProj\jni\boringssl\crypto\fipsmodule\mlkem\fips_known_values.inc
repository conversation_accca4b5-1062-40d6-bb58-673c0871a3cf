const uint8_t kTestEntropy[BCM_MLKEM_SEED_BYTES] = {
    0xc8, 0x77, 0x34, 0x04, 0xb3, 0xe5, 0x3e, 0x31, 0x7b, 0xab, 0x41,
    0x08, 0xa8, 0x88, 0x9c, 0x90, 0xbe, 0xfb, 0x38, 0x0f, 0x63, 0x89,
    0x70, 0xbc, 0xdc, 0x33, 0xeb, 0x2d, 0xe2, 0x99, 0x6b, 0x1a};

const uint8_t kExpectedPublicKeyBytes[BCM_MLKEM768_PUBLIC_KEY_BYTES] = {
    0xcb, 0x0b, 0x33, 0xa2, 0xac, 0xc4, 0x94, 0x56, 0x1a, 0x36, 0x1c, 0x0d,
    0xa3, 0x57, 0x5b, 0x63, 0x6c, 0x0d, 0x1f, 0xe1, 0xa8, 0x36, 0xd5, 0x5d,
    0x38, 0x5c, 0xac, 0xf4, 0x67, 0x39, 0x9a, 0x12, 0x5b, 0xef, 0xa1, 0x0a,
    0x57, 0x76, 0x87, 0x83, 0xaa, 0xaf, 0x1d, 0x9c, 0xce, 0x2b, 0x78, 0x9b,
    0x40, 0x82, 0x1e, 0x34, 0xb3, 0x55, 0x2d, 0xd0, 0x20, 0xcd, 0x5b, 0x46,
    0x8d, 0xda, 0xb8, 0x83, 0xa8, 0xab, 0xfb, 0x36, 0x45, 0xcb, 0x40, 0xae,
    0x69, 0x77, 0x03, 0x22, 0xa8, 0xb4, 0xaa, 0x17, 0x53, 0x90, 0x05, 0x6a,
    0x69, 0xb9, 0xc5, 0x2d, 0xbc, 0x9a, 0x81, 0xf6, 0x2a, 0x3c, 0x57, 0x96,
    0x8c, 0xb2, 0x72, 0xa7, 0xd4, 0x84, 0x77, 0xf9, 0x0b, 0x80, 0xe7, 0x1e,
    0x75, 0x61, 0x0e, 0xf9, 0xb0, 0xcf, 0x4c, 0x03, 0xca, 0x30, 0x45, 0x50,
    0x25, 0x82, 0x56, 0x02, 0xbb, 0x58, 0xab, 0x99, 0xc7, 0x8d, 0xd5, 0x37,
    0x12, 0xc8, 0x36, 0x5b, 0x00, 0x5f, 0x91, 0xf6, 0x2d, 0x9b, 0x91, 0xc2,
    0x3e, 0x75, 0x56, 0x83, 0xd2, 0xaa, 0xd4, 0x03, 0xcd, 0xe8, 0xe8, 0x3f,
    0x09, 0xc1, 0x8e, 0x84, 0xb1, 0x75, 0x85, 0x06, 0xa7, 0x4a, 0x40, 0xc2,
    0xc9, 0x90, 0xb0, 0x0f, 0x59, 0x84, 0xe5, 0xf7, 0xa8, 0xd8, 0x35, 0x33,
    0xa5, 0x82, 0x4e, 0xf6, 0xb5, 0xaf, 0xd5, 0xaa, 0xbb, 0x52, 0x30, 0x0b,
    0xb1, 0x67, 0x7b, 0xbb, 0xdc, 0x38, 0x58, 0xf4, 0x2f, 0xe4, 0xeb, 0x47,
    0xf0, 0x49, 0x76, 0x45, 0x10, 0x02, 0x19, 0xa7, 0xc6, 0x1f, 0x8a, 0x71,
    0xda, 0x51, 0x61, 0x79, 0xc4, 0x10, 0xeb, 0x3a, 0x11, 0xd9, 0x93, 0x1d,
    0x83, 0x81, 0x48, 0x3d, 0x68, 0x35, 0xff, 0xe3, 0xa6, 0xa8, 0x40, 0x74,
    0xdb, 0x7c, 0xb4, 0xb2, 0x05, 0x89, 0xc8, 0x09, 0x42, 0x27, 0x28, 0x55,
    0x01, 0x57, 0xc9, 0xf9, 0xfa, 0x36, 0xd3, 0x5c, 0x51, 0x40, 0xca, 0x1e,
    0x34, 0xc8, 0xb9, 0x27, 0xf1, 0x15, 0xc9, 0xa4, 0x18, 0x9c, 0xa8, 0x4c,
    0x55, 0xf8, 0x4d, 0x90, 0xbc, 0x88, 0xd1, 0x46, 0x01, 0x80, 0x96, 0x54,
    0x8d, 0x01, 0x6e, 0xe4, 0x26, 0x0c, 0x7a, 0x08, 0x3d, 0x7c, 0x26, 0x66,
    0xb1, 0xd1, 0x15, 0xc7, 0xc0, 0x0b, 0x06, 0x92, 0x9c, 0x26, 0xa6, 0x6c,
    0x3a, 0x46, 0x47, 0x15, 0x37, 0xb0, 0x0a, 0xc1, 0x5d, 0xe0, 0x51, 0xae,
    0x17, 0x87, 0x2f, 0x75, 0x2c, 0x64, 0xd5, 0x39, 0x09, 0xe6, 0xd0, 0x7c,
    0x3d, 0x79, 0x47, 0x78, 0x0a, 0x82, 0xc9, 0xba, 0x37, 0x07, 0x93, 0x94,
    0xa4, 0x45, 0x7c, 0x07, 0x69, 0x24, 0x1d, 0xc9, 0x75, 0x41, 0x6b, 0xc1,
    0x42, 0x28, 0x06, 0xe3, 0xfc, 0x7b, 0xd2, 0xf8, 0xbf, 0xe7, 0x5a, 0xc2,
    0xc0, 0x81, 0x63, 0xf3, 0x84, 0x32, 0x31, 0x4a, 0x8d, 0x6b, 0xe3, 0x3e,
    0x22, 0x67, 0x9a, 0x3a, 0x96, 0x09, 0xa6, 0xf9, 0x53, 0x40, 0x41, 0x2a,
    0x04, 0xa7, 0xc5, 0x7b, 0x86, 0xcd, 0xed, 0xec, 0xae, 0xfd, 0xc7, 0x11,
    0x48, 0x56, 0x77, 0x0b, 0xb7, 0xbb, 0xaf, 0xfb, 0x6c, 0x9e, 0xd6, 0x5c,
    0xbc, 0xf7, 0x9a, 0x75, 0xb5, 0xb2, 0x20, 0xd9, 0x60, 0x90, 0x65, 0x02,
    0xf9, 0x16, 0x34, 0xdf, 0x0b, 0x16, 0x2a, 0x97, 0x00, 0x5d, 0xc1, 0x65,
    0xc3, 0xb9, 0x1f, 0x2e, 0x85, 0x97, 0xbd, 0xca, 0xaa, 0x1d, 0xf2, 0x4f,
    0x74, 0xe5, 0x56, 0x94, 0xdc, 0x53, 0xbb, 0xc0, 0x75, 0x1c, 0x2b, 0x59,
    0x6b, 0x50, 0x2c, 0x25, 0x4c, 0x78, 0xd7, 0x68, 0x40, 0x0b, 0x91, 0x7a,
    0x93, 0x02, 0x83, 0x5e, 0x6c, 0x03, 0xc7, 0xc4, 0x24, 0x6f, 0x8b, 0xa4,
    0x70, 0x37, 0x1f, 0xe5, 0x33, 0x59, 0xf6, 0xbb, 0x5f, 0xb8, 0x58, 0x50,
    0x62, 0x16, 0x95, 0x2a, 0x88, 0xa3, 0xc6, 0x24, 0x94, 0x4b, 0xb0, 0x03,
    0x8e, 0xe6, 0x50, 0xd1, 0x63, 0xa2, 0xab, 0x00, 0x16, 0xf4, 0x0a, 0x7d,
    0xe6, 0xe0, 0xb9, 0x0c, 0xd8, 0x08, 0xae, 0xf6, 0x80, 0xb7, 0x48, 0xb8,
    0x10, 0xd1, 0x77, 0x57, 0x48, 0x2a, 0xc1, 0xf1, 0x4e, 0x61, 0xb4, 0x06,
    0x5a, 0xa1, 0xba, 0x32, 0x4a, 0x9a, 0xab, 0x8c, 0xa8, 0xe3, 0xa0, 0x68,
    0x39, 0xa3, 0x25, 0x55, 0x48, 0xcd, 0x12, 0x98, 0x14, 0x1b, 0x39, 0x5b,
    0xf4, 0x88, 0xbe, 0x5f, 0xac, 0xba, 0x18, 0x20, 0x74, 0x2c, 0xa1, 0x0b,
    0xfc, 0xf1, 0x00, 0x85, 0x24, 0x27, 0x3d, 0x80, 0xb6, 0x30, 0x42, 0x7b,
    0x74, 0xb0, 0xbf, 0x87, 0x51, 0x9b, 0xa8, 0x1c, 0x79, 0xbd, 0x50, 0x85,
    0x66, 0xe1, 0x93, 0xdd, 0xc5, 0x5d, 0x5d, 0xd0, 0xbe, 0xcb, 0x90, 0x36,
    0xf8, 0xc6, 0xc2, 0xea, 0xea, 0x40, 0xce, 0xa7, 0xb0, 0xae, 0xc5, 0x44,
    0x3a, 0x27, 0xc5, 0x4a, 0xf0, 0x21, 0x4f, 0xf6, 0x63, 0xb1, 0x8c, 0x20,
    0x76, 0x65, 0xb5, 0x79, 0x55, 0x10, 0xa5, 0xa7, 0x4b, 0x57, 0x96, 0xb3,
    0x03, 0xe6, 0xa8, 0x93, 0xf5, 0xc5, 0xca, 0xe0, 0x1a, 0x23, 0x30, 0x6d,
    0x09, 0x07, 0x81, 0x5e, 0x05, 0xc6, 0x1b, 0x7a, 0x0e, 0xde, 0xf3, 0x00,
    0x91, 0xf3, 0xbb, 0x4b, 0xa6, 0x8e, 0x96, 0xc3, 0x3a, 0xc8, 0x72, 0x19,
    0x23, 0x23, 0xc7, 0x94, 0x46, 0x75, 0xa2, 0xc5, 0x9e, 0xa3, 0x15, 0x86,
    0xb4, 0xa7, 0x95, 0xda, 0x16, 0xae, 0x42, 0xe8, 0xcc, 0xf6, 0x41, 0xbc,
    0x9c, 0xe8, 0x07, 0xc9, 0x05, 0x3c, 0x74, 0xb2, 0xad, 0x77, 0xc4, 0x08,
    0x56, 0x78, 0xb8, 0x65, 0x2a, 0x33, 0x39, 0x82, 0x16, 0x3c, 0x2b, 0x73,
    0x41, 0x2a, 0xbc, 0xc8, 0x36, 0x23, 0x2c, 0x23, 0x29, 0xce, 0x94, 0x8a,
    0xba, 0x13, 0x3f, 0xfa, 0xcc, 0x6e, 0x17, 0x40, 0xb0, 0xb4, 0x9c, 0xae,
    0xf9, 0x72, 0x40, 0xf7, 0x2a, 0xaf, 0x00, 0x17, 0x7e, 0x88, 0x44, 0x4e,
    0x32, 0xa4, 0x80, 0x96, 0x53, 0x20, 0xe4, 0xaa, 0x18, 0x9c, 0x69, 0x63,
    0xdb, 0x81, 0x73, 0x0e, 0x64, 0x40, 0x12, 0xc1, 0x26, 0x0e, 0xac, 0x4d,
    0x38, 0xa6, 0x4c, 0x05, 0xeb, 0x42, 0x1f, 0x88, 0x6e, 0x6c, 0xa3, 0x98,
    0x95, 0xc3, 0x5a, 0x31, 0xfc, 0x8a, 0x7c, 0xd6, 0xa5, 0x6c, 0x5a, 0xc9,
    0xf5, 0xf2, 0xb0, 0xf7, 0x05, 0x8d, 0x3a, 0x0b, 0x3f, 0x4d, 0x1b, 0x0e,
    0x38, 0x7b, 0x1c, 0xe5, 0xd0, 0x75, 0x40, 0x77, 0xc2, 0xe0, 0x8b, 0xa9,
    0xfb, 0xa6, 0xa7, 0x13, 0xf0, 0xbc, 0x0c, 0x13, 0xb0, 0x70, 0x3a, 0x2f,
    0x20, 0xe7, 0x38, 0xd8, 0x82, 0xa4, 0xc0, 0x06, 0x96, 0x95, 0x75, 0x5c,
    0xbc, 0x68, 0xce, 0xee, 0xa6, 0x44, 0xbb, 0xf2, 0x62, 0x14, 0x43, 0x10,
    0xe6, 0x0b, 0x5c, 0x66, 0x02, 0xbf, 0x38, 0xe9, 0x8f, 0x19, 0x69, 0x6c,
    0x08, 0xfa, 0x93, 0x23, 0xf1, 0x35, 0x5f, 0x33, 0xcf, 0xe2, 0x32, 0x11,
    0xda, 0x31, 0xbb, 0xd8, 0xf5, 0x7e, 0x55, 0xc3, 0x6a, 0xa3, 0x04, 0x8c,
    0xef, 0x50, 0x67, 0x0f, 0xe9, 0xbb, 0x11, 0x6b, 0xca, 0x5a, 0x23, 0x15,
    0xf7, 0x52, 0x8c, 0x69, 0xbb, 0x2d, 0x26, 0xd3, 0x78, 0x32, 0x33, 0x9b,
    0x1d, 0x02, 0x7c, 0x22, 0x4b, 0x57, 0x19, 0xec, 0xcd, 0x16, 0x63, 0x5d,
    0x1f, 0x0c, 0x67, 0xda, 0x6a, 0xc7, 0x2c, 0xf1, 0x0b, 0xcf, 0x92, 0x10,
    0xcb, 0x78, 0x57, 0x67, 0x61, 0x0e, 0xb3, 0x55, 0xb0, 0x17, 0x32, 0x4e,
    0x95, 0xf1, 0xcb, 0xb5, 0xdb, 0x5d, 0x8e, 0x38, 0x97, 0x28, 0x46, 0x1e,
    0x37, 0x4b, 0x5c, 0x03, 0xf4, 0x84, 0x0c, 0xfc, 0x49, 0x3b, 0xd2, 0xc6,
    0x97, 0xd4, 0x4f, 0xef, 0xe6, 0x5c, 0xb2, 0x96, 0x8e, 0xbc, 0x18, 0x9f,
    0xec, 0x8a, 0x67, 0x53, 0xe8, 0x2a, 0x41, 0x5c, 0x6e, 0xfe, 0xca, 0x1a,
    0xd0, 0x03, 0x4f, 0xe8, 0xd9, 0xb0, 0xe2, 0xa1, 0x6c, 0x32, 0xd5, 0x37,
    0x68, 0x9c, 0x6b, 0x30, 0x87, 0xb4, 0x6e, 0xa8, 0xc5, 0x45, 0xe4, 0x74,
    0x4a, 0x80, 0x02, 0x84, 0x78, 0x0e, 0x2c, 0x9c, 0x37, 0x07, 0x26, 0x31,
    0x88, 0x0a, 0x89, 0xd2, 0xba, 0x8b, 0x09, 0xb2, 0x16, 0x7b, 0x23, 0xa8,
    0x0c, 0x82, 0xc4, 0x22, 0xa2, 0x21, 0x2d, 0xa4, 0xa0, 0xeb, 0xe4, 0xa6,
    0xd4, 0x16, 0xa0, 0xa7, 0x19, 0x8b, 0x8f, 0x87, 0x9b, 0x53, 0x6c, 0xba,
    0x14, 0xa7, 0xc4, 0x64, 0x08, 0xb4, 0x68, 0xda, 0x85, 0xa6, 0x47, 0xbc,
    0x58, 0x34, 0xcd, 0x60, 0x43, 0xad, 0x14, 0xd4, 0xca, 0xd7, 0xb1, 0x6c,
    0xc0, 0x05, 0x68, 0xaf, 0xa7, 0x08, 0x9d, 0x6a, 0xb0, 0x70, 0x96, 0x81,
    0xb8, 0x55, 0xbf, 0xa1, 0xcc, 0xb1, 0x37, 0xe0, 0x94, 0x42, 0x05, 0x0c,
    0x83, 0xef, 0x6d, 0xf0, 0x4e, 0xba, 0xe6, 0x78, 0x49, 0xaf, 0x1f, 0x88,
    0xf0, 0x18, 0x0c, 0x17, 0x66, 0x92, 0xd8, 0x50, 0x57, 0x3b, 0x8e, 0xc9,
    0xd5, 0x8c, 0xfb, 0x9b, 0x78, 0x65, 0x01, 0x76};

const uint8_t kExpectedPrivateKeyBytes[2400] = {
    0xd8, 0xc9, 0x39, 0x7c, 0x31, 0x30, 0xd8, 0xec, 0xb4, 0x11, 0xa6, 0x8e,
    0xfc, 0xc8, 0x9a, 0x55, 0x3c, 0xb7, 0xe6, 0x81, 0x7e, 0x02, 0x88, 0xbd,
    0x06, 0x91, 0x60, 0x9b, 0xf5, 0x57, 0x6a, 0xf8, 0x73, 0x9e, 0x52, 0xa8,
    0x03, 0xf0, 0x97, 0xb4, 0x22, 0xb7, 0x02, 0x23, 0xcd, 0x3e, 0xa1, 0x03,
    0x20, 0x66, 0x6e, 0x20, 0x20, 0xcb, 0x42, 0xec, 0xb6, 0xc2, 0x14, 0x2c,
    0xe7, 0xa8, 0x88, 0x3d, 0xd2, 0x26, 0x9c, 0xf7, 0xb3, 0x4f, 0x00, 0x27,
    0x61, 0xc6, 0x10, 0xb2, 0x72, 0x34, 0x39, 0xf1, 0x79, 0x03, 0xcc, 0x06,
    0xd2, 0xe4, 0x29, 0x57, 0x91, 0x2f, 0x0d, 0x45, 0x19, 0x30, 0x27, 0x47,
    0xcc, 0xba, 0xbe, 0x19, 0x01, 0x1a, 0xbb, 0xd8, 0xcb, 0x74, 0xca, 0x4a,
    0xfa, 0x32, 0x2f, 0xb8, 0xbb, 0x5c, 0x19, 0x2a, 0xbf, 0x31, 0x0a, 0x82,
    0xf3, 0x29, 0x74, 0x30, 0xa4, 0x95, 0x4c, 0x42, 0x59, 0x8c, 0xb3, 0xcb,
    0xfe, 0xa9, 0xb4, 0x2e, 0x60, 0x71, 0x64, 0x51, 0x6d, 0xf9, 0x73, 0x5b,
    0xf5, 0x59, 0x9b, 0xf5, 0xd9, 0x9d, 0x3f, 0x86, 0x3a, 0x50, 0x6c, 0x23,
    0x8c, 0x96, 0xc0, 0x5e, 0x7c, 0x48, 0x43, 0xec, 0x08, 0xf5, 0xf7, 0x7e,
    0x6b, 0x54, 0x84, 0xb2, 0x6c, 0x52, 0xd5, 0x2b, 0x38, 0x85, 0xf4, 0x40,
    0x36, 0x09, 0x2f, 0xe6, 0xd0, 0x9e, 0xe6, 0xb6, 0x96, 0x7a, 0xe1, 0xc3,
    0xf7, 0xfa, 0x89, 0xc6, 0x75, 0x6f, 0x65, 0xea, 0x4a, 0x6d, 0x55, 0x33,
    0xe2, 0xc6, 0x30, 0xfb, 0x7b, 0xbd, 0xcb, 0x33, 0x4d, 0xd6, 0x84, 0x1b,
    0x28, 0x77, 0x60, 0xbf, 0x4b, 0x82, 0xd0, 0xb0, 0xc5, 0x88, 0x0c, 0x51,
    0x00, 0x22, 0x5e, 0xd9, 0x41, 0x2e, 0x7e, 0x43, 0xa6, 0x9d, 0x34, 0x5d,
    0x24, 0x80, 0xad, 0x7f, 0xd3, 0xae, 0xc7, 0x5a, 0x51, 0x74, 0x06, 0xc0,
    0x6f, 0x04, 0xae, 0x9b, 0x79, 0x6e, 0x35, 0xfa, 0x0d, 0xf3, 0xda, 0xa4,
    0xfa, 0x38, 0x4a, 0xee, 0xb3, 0xa4, 0x87, 0xa7, 0x27, 0xe2, 0x02, 0x2a,
    0x29, 0x27, 0xcf, 0xca, 0x4a, 0x20, 0x01, 0x15, 0x3d, 0x12, 0xc7, 0xcc,
    0x74, 0x99, 0x17, 0xa6, 0x7b, 0x04, 0xd7, 0xea, 0x64, 0x88, 0xa3, 0x54,
    0xb5, 0xc2, 0xa3, 0xea, 0x69, 0x94, 0xb9, 0xc3, 0x45, 0xf8, 0x2b, 0x3c,
    0xe5, 0xc1, 0x25, 0xf6, 0xc9, 0x82, 0xe3, 0x45, 0x25, 0xca, 0x71, 0x3d,
    0x73, 0x10, 0x99, 0xa5, 0xb5, 0x79, 0xdc, 0x61, 0x8f, 0x04, 0x3c, 0x61,
    0x6b, 0x90, 0x4d, 0x83, 0xa3, 0x0d, 0xb4, 0xba, 0xb7, 0x3b, 0x81, 0xca,
    0xbb, 0x59, 0x16, 0x46, 0xf4, 0x75, 0x7c, 0xaa, 0x0f, 0x90, 0xec, 0x37,
    0xa4, 0x50, 0xa8, 0x41, 0xb4, 0x33, 0xd2, 0xb7, 0x28, 0xe1, 0xda, 0x74,
    0x36, 0x3a, 0xa6, 0x96, 0x76, 0x06, 0x67, 0xd3, 0x8e, 0xda, 0xd5, 0x29,
    0x24, 0x1a, 0xbe, 0x82, 0x83, 0x6b, 0x3a, 0x90, 0xc1, 0x86, 0x34, 0x2d,
    0xd8, 0xa3, 0xa5, 0x7e, 0xe6, 0xca, 0x3f, 0xc5, 0xbf, 0xba, 0x24, 0x14,
    0xd2, 0xfc, 0x3f, 0x97, 0x8c, 0xca, 0x59, 0xa5, 0x52, 0xf1, 0x8c, 0x62,
    0x8d, 0x8a, 0x7d, 0x1b, 0x35, 0x76, 0x79, 0x98, 0x39, 0x57, 0x53, 0x1f,
    0x78, 0x82, 0x7d, 0xcd, 0x9c, 0xab, 0xb3, 0x12, 0x52, 0x86, 0x87, 0xbf,
    0x01, 0x12, 0x58, 0x45, 0x37, 0x75, 0x1a, 0xe0, 0x03, 0xbd, 0x67, 0x4c,
    0x76, 0xfa, 0x06, 0x59, 0xa2, 0x1f, 0x48, 0x73, 0x7f, 0x0c, 0x90, 0xc1,
    0x82, 0xc0, 0x98, 0x2f, 0x6a, 0x1c, 0x6f, 0x6c, 0x98, 0x70, 0x16, 0x8e,
    0x8c, 0x5b, 0x60, 0x92, 0x42, 0x59, 0xd6, 0xb2, 0x13, 0x7a, 0x11, 0xb6,
    0x15, 0x1b, 0x07, 0x50, 0x52, 0x73, 0x86, 0xbc, 0x3a, 0xff, 0x66, 0x71,
    0x57, 0xf8, 0x89, 0x7a, 0x3a, 0x95, 0xc5, 0xa1, 0x31, 0x48, 0x15, 0xc9,
    0xe0, 0x4b, 0x41, 0xb6, 0x07, 0x5a, 0x70, 0x88, 0x11, 0xce, 0xdc, 0xa7,
    0x84, 0x23, 0x85, 0x96, 0xd8, 0x20, 0x9f, 0x89, 0x42, 0xbc, 0x0b, 0xd0,
    0xd9, 0x11, 0x9f, 0x2f, 0x71, 0x37, 0xa2, 0x10, 0x98, 0x95, 0xa7, 0x1f,
    0x99, 0x48, 0x8e, 0x5b, 0x40, 0x65, 0x9f, 0x08, 0x4e, 0x04, 0x16, 0x35,
    0xc8, 0x56, 0xa1, 0x02, 0xa1, 0xaf, 0x0e, 0x5b, 0xaf, 0x02, 0x66, 0x2e,
    0xc8, 0xa5, 0x49, 0xe7, 0xa4, 0x6b, 0x03, 0xe2, 0x42, 0xb3, 0x37, 0x76,
    0x2c, 0x89, 0x00, 0x76, 0x83, 0xb3, 0x93, 0xdc, 0x78, 0x56, 0xc9, 0x02,
    0xf1, 0xd3, 0x92, 0xe5, 0x14, 0x36, 0xba, 0x37, 0x1d, 0x86, 0x87, 0x81,
    0x5f, 0x30, 0x16, 0x7f, 0xdb, 0x76, 0xe2, 0x46, 0x3d, 0x32, 0xe8, 0x47,
    0x35, 0x5b, 0x68, 0x39, 0x70, 0xa3, 0xc2, 0xb8, 0xb3, 0x6e, 0xd0, 0x22,
    0x6b, 0x39, 0x9c, 0xd9, 0xa3, 0x93, 0x69, 0xbc, 0xcb, 0xe0, 0xbc, 0x82,
    0x1b, 0xfb, 0x9f, 0x72, 0x17, 0x40, 0x72, 0xeb, 0xb2, 0x97, 0x31, 0x66,
    0x18, 0x48, 0x9a, 0xe5, 0x3a, 0x2a, 0x41, 0x13, 0x3e, 0xfc, 0xfb, 0xb4,
    0xb0, 0x96, 0x6e, 0x53, 0x37, 0x18, 0xbb, 0x26, 0x1d, 0xbe, 0x33, 0x0f,
    0x06, 0x8b, 0x8c, 0xb1, 0xfc, 0x8c, 0x24, 0x19, 0x8d, 0x7d, 0x76, 0x52,
    0x35, 0x75, 0x71, 0x36, 0x61, 0x7d, 0x75, 0x3a, 0x31, 0x9a, 0x16, 0x05,
    0x2d, 0x51, 0x63, 0x6f, 0x07, 0x64, 0xfd, 0x80, 0x16, 0x2d, 0xa3, 0x3a,
    0x15, 0xca, 0xaa, 0x76, 0xec, 0x2f, 0x91, 0x47, 0xb2, 0x29, 0x61, 0x04,
    0x80, 0x20, 0x8d, 0x3c, 0x31, 0xa6, 0x4f, 0x8b, 0xc8, 0xc0, 0x71, 0xbb,
    0x0a, 0xec, 0xbc, 0xdf, 0xdc, 0x91, 0x8c, 0xb9, 0x80, 0xb7, 0x67, 0xa9,
    0x7f, 0x30, 0x18, 0x4e, 0xd3, 0x68, 0x27, 0x50, 0x15, 0xad, 0x8c, 0x6a,
    0xab, 0xc9, 0xbe, 0xad, 0x41, 0x4b, 0xcd, 0x23, 0x73, 0x9f, 0x77, 0x58,
    0xe3, 0xd8, 0x2e, 0x16, 0xcb, 0xa0, 0x17, 0x89, 0x51, 0x31, 0x53, 0x01,
    0xa8, 0xf1, 0x9b, 0x61, 0xc3, 0x0a, 0x5b, 0x0c, 0x70, 0x36, 0x48, 0x1b,
    0xae, 0x17, 0x51, 0x1f, 0xc7, 0x7b, 0xce, 0xd4, 0x45, 0xff, 0xea, 0xb0,
    0x5f, 0x48, 0x07, 0x4f, 0x79, 0x26, 0x5c, 0xb6, 0xce, 0x4c, 0xc4, 0x02,
    0xd1, 0x31, 0x88, 0xa9, 0xc0, 0x53, 0x6a, 0x42, 0x6f, 0x9c, 0x32, 0xaa,
    0x86, 0xec, 0x25, 0xa7, 0x5b, 0x31, 0x68, 0xc9, 0xa9, 0xd8, 0x2c, 0x70,
    0xa0, 0x02, 0x31, 0x52, 0x8a, 0x2c, 0xc0, 0x0a, 0xbe, 0x8c, 0x59, 0x6c,
    0x72, 0x42, 0x21, 0xcb, 0x44, 0x07, 0x02, 0x27, 0xa0, 0x71, 0xa3, 0x7f,
    0xe3, 0x97, 0x24, 0x70, 0x47, 0x4d, 0x10, 0xfc, 0x54, 0x83, 0xdc, 0x44,
    0x09, 0x36, 0x23, 0x4a, 0x26, 0x95, 0x60, 0x96, 0xa4, 0x11, 0x1b, 0x18,
    0x9d, 0x01, 0x9c, 0x8c, 0xd7, 0x37, 0x60, 0x08, 0x02, 0xef, 0xe3, 0x81,
    0xf3, 0x48, 0x5c, 0x9c, 0xac, 0x4f, 0xb6, 0xc7, 0x66, 0xd6, 0xda, 0x25,
    0x78, 0x4a, 0xca, 0x91, 0xf1, 0x16, 0x75, 0x69, 0x8b, 0x09, 0x63, 0x0d,
    0x74, 0x49, 0x57, 0x33, 0xe1, 0x23, 0x87, 0xc5, 0x0e, 0x85, 0x27, 0x1e,
    0x5a, 0xfa, 0x3f, 0xa8, 0x82, 0xbc, 0x49, 0xc7, 0x99, 0x15, 0xe1, 0x2f,
    0x04, 0x36, 0x88, 0xd4, 0xa1, 0x9a, 0xe5, 0xb3, 0x70, 0x95, 0x85, 0x43,
    0x76, 0xb9, 0x7d, 0x3b, 0x9c, 0x61, 0xf3, 0x30, 0x59, 0x4e, 0x2a, 0xc6,
    0x3f, 0x52, 0x35, 0x81, 0x43, 0x61, 0x72, 0xc0, 0x89, 0x98, 0x67, 0x1e,
    0x6f, 0xec, 0xc6, 0x2a, 0x9a, 0x14, 0x36, 0x34, 0x4c, 0x9f, 0xfb, 0x2e,
    0xd1, 0xd2, 0x4e, 0x6b, 0xb9, 0xc9, 0x56, 0x74, 0xbe, 0xab, 0xa4, 0x7c,
    0x48, 0x84, 0x07, 0xf6, 0x04, 0xb1, 0x89, 0x0b, 0x13, 0x79, 0x13, 0xb5,
    0x31, 0xd6, 0x2c, 0x40, 0x47, 0xbd, 0xba, 0x37, 0xb1, 0x65, 0x7c, 0xb3,
    0xa3, 0x45, 0x1e, 0x0f, 0x41, 0x62, 0x07, 0xf9, 0x1e, 0x25, 0xa5, 0x78,
    0x47, 0xd2, 0xa6, 0x4b, 0x20, 0x7b, 0x9b, 0x39, 0x07, 0x09, 0xf3, 0x9e,
    0x09, 0xfb, 0xac, 0x87, 0xb6, 0xb5, 0x9b, 0xd8, 0x09, 0xa3, 0xd6, 0x69,
    0xb3, 0x3a, 0x87, 0x09, 0xb0, 0x19, 0xb3, 0xc1, 0x47, 0x8e, 0xd7, 0x3a,
    0x6e, 0xb5, 0x48, 0x89, 0x56, 0x87, 0x60, 0xb1, 0xba, 0x52, 0xd5, 0x94,
    0x22, 0x54, 0xbd, 0x7c, 0xf1, 0xa4, 0x54, 0xaa, 0x38, 0x5c, 0x76, 0xa8,
    0x52, 0x19, 0x97, 0x9a, 0x71, 0x47, 0x6c, 0xa0, 0x39, 0xd1, 0x0b, 0xd0,
    0xa6, 0xb3, 0x65, 0xdc, 0xbc, 0xa4, 0x4d, 0x62, 0x44, 0xb2, 0x47, 0x5e,
    0x77, 0x77, 0x32, 0x2f, 0x78, 0xa6, 0x97, 0xdb, 0xb5, 0xba, 0x3b, 0x4a,
    0xcb, 0x0b, 0x33, 0xa2, 0xac, 0xc4, 0x94, 0x56, 0x1a, 0x36, 0x1c, 0x0d,
    0xa3, 0x57, 0x5b, 0x63, 0x6c, 0x0d, 0x1f, 0xe1, 0xa8, 0x36, 0xd5, 0x5d,
    0x38, 0x5c, 0xac, 0xf4, 0x67, 0x39, 0x9a, 0x12, 0x5b, 0xef, 0xa1, 0x0a,
    0x57, 0x76, 0x87, 0x83, 0xaa, 0xaf, 0x1d, 0x9c, 0xce, 0x2b, 0x78, 0x9b,
    0x40, 0x82, 0x1e, 0x34, 0xb3, 0x55, 0x2d, 0xd0, 0x20, 0xcd, 0x5b, 0x46,
    0x8d, 0xda, 0xb8, 0x83, 0xa8, 0xab, 0xfb, 0x36, 0x45, 0xcb, 0x40, 0xae,
    0x69, 0x77, 0x03, 0x22, 0xa8, 0xb4, 0xaa, 0x17, 0x53, 0x90, 0x05, 0x6a,
    0x69, 0xb9, 0xc5, 0x2d, 0xbc, 0x9a, 0x81, 0xf6, 0x2a, 0x3c, 0x57, 0x96,
    0x8c, 0xb2, 0x72, 0xa7, 0xd4, 0x84, 0x77, 0xf9, 0x0b, 0x80, 0xe7, 0x1e,
    0x75, 0x61, 0x0e, 0xf9, 0xb0, 0xcf, 0x4c, 0x03, 0xca, 0x30, 0x45, 0x50,
    0x25, 0x82, 0x56, 0x02, 0xbb, 0x58, 0xab, 0x99, 0xc7, 0x8d, 0xd5, 0x37,
    0x12, 0xc8, 0x36, 0x5b, 0x00, 0x5f, 0x91, 0xf6, 0x2d, 0x9b, 0x91, 0xc2,
    0x3e, 0x75, 0x56, 0x83, 0xd2, 0xaa, 0xd4, 0x03, 0xcd, 0xe8, 0xe8, 0x3f,
    0x09, 0xc1, 0x8e, 0x84, 0xb1, 0x75, 0x85, 0x06, 0xa7, 0x4a, 0x40, 0xc2,
    0xc9, 0x90, 0xb0, 0x0f, 0x59, 0x84, 0xe5, 0xf7, 0xa8, 0xd8, 0x35, 0x33,
    0xa5, 0x82, 0x4e, 0xf6, 0xb5, 0xaf, 0xd5, 0xaa, 0xbb, 0x52, 0x30, 0x0b,
    0xb1, 0x67, 0x7b, 0xbb, 0xdc, 0x38, 0x58, 0xf4, 0x2f, 0xe4, 0xeb, 0x47,
    0xf0, 0x49, 0x76, 0x45, 0x10, 0x02, 0x19, 0xa7, 0xc6, 0x1f, 0x8a, 0x71,
    0xda, 0x51, 0x61, 0x79, 0xc4, 0x10, 0xeb, 0x3a, 0x11, 0xd9, 0x93, 0x1d,
    0x83, 0x81, 0x48, 0x3d, 0x68, 0x35, 0xff, 0xe3, 0xa6, 0xa8, 0x40, 0x74,
    0xdb, 0x7c, 0xb4, 0xb2, 0x05, 0x89, 0xc8, 0x09, 0x42, 0x27, 0x28, 0x55,
    0x01, 0x57, 0xc9, 0xf9, 0xfa, 0x36, 0xd3, 0x5c, 0x51, 0x40, 0xca, 0x1e,
    0x34, 0xc8, 0xb9, 0x27, 0xf1, 0x15, 0xc9, 0xa4, 0x18, 0x9c, 0xa8, 0x4c,
    0x55, 0xf8, 0x4d, 0x90, 0xbc, 0x88, 0xd1, 0x46, 0x01, 0x80, 0x96, 0x54,
    0x8d, 0x01, 0x6e, 0xe4, 0x26, 0x0c, 0x7a, 0x08, 0x3d, 0x7c, 0x26, 0x66,
    0xb1, 0xd1, 0x15, 0xc7, 0xc0, 0x0b, 0x06, 0x92, 0x9c, 0x26, 0xa6, 0x6c,
    0x3a, 0x46, 0x47, 0x15, 0x37, 0xb0, 0x0a, 0xc1, 0x5d, 0xe0, 0x51, 0xae,
    0x17, 0x87, 0x2f, 0x75, 0x2c, 0x64, 0xd5, 0x39, 0x09, 0xe6, 0xd0, 0x7c,
    0x3d, 0x79, 0x47, 0x78, 0x0a, 0x82, 0xc9, 0xba, 0x37, 0x07, 0x93, 0x94,
    0xa4, 0x45, 0x7c, 0x07, 0x69, 0x24, 0x1d, 0xc9, 0x75, 0x41, 0x6b, 0xc1,
    0x42, 0x28, 0x06, 0xe3, 0xfc, 0x7b, 0xd2, 0xf8, 0xbf, 0xe7, 0x5a, 0xc2,
    0xc0, 0x81, 0x63, 0xf3, 0x84, 0x32, 0x31, 0x4a, 0x8d, 0x6b, 0xe3, 0x3e,
    0x22, 0x67, 0x9a, 0x3a, 0x96, 0x09, 0xa6, 0xf9, 0x53, 0x40, 0x41, 0x2a,
    0x04, 0xa7, 0xc5, 0x7b, 0x86, 0xcd, 0xed, 0xec, 0xae, 0xfd, 0xc7, 0x11,
    0x48, 0x56, 0x77, 0x0b, 0xb7, 0xbb, 0xaf, 0xfb, 0x6c, 0x9e, 0xd6, 0x5c,
    0xbc, 0xf7, 0x9a, 0x75, 0xb5, 0xb2, 0x20, 0xd9, 0x60, 0x90, 0x65, 0x02,
    0xf9, 0x16, 0x34, 0xdf, 0x0b, 0x16, 0x2a, 0x97, 0x00, 0x5d, 0xc1, 0x65,
    0xc3, 0xb9, 0x1f, 0x2e, 0x85, 0x97, 0xbd, 0xca, 0xaa, 0x1d, 0xf2, 0x4f,
    0x74, 0xe5, 0x56, 0x94, 0xdc, 0x53, 0xbb, 0xc0, 0x75, 0x1c, 0x2b, 0x59,
    0x6b, 0x50, 0x2c, 0x25, 0x4c, 0x78, 0xd7, 0x68, 0x40, 0x0b, 0x91, 0x7a,
    0x93, 0x02, 0x83, 0x5e, 0x6c, 0x03, 0xc7, 0xc4, 0x24, 0x6f, 0x8b, 0xa4,
    0x70, 0x37, 0x1f, 0xe5, 0x33, 0x59, 0xf6, 0xbb, 0x5f, 0xb8, 0x58, 0x50,
    0x62, 0x16, 0x95, 0x2a, 0x88, 0xa3, 0xc6, 0x24, 0x94, 0x4b, 0xb0, 0x03,
    0x8e, 0xe6, 0x50, 0xd1, 0x63, 0xa2, 0xab, 0x00, 0x16, 0xf4, 0x0a, 0x7d,
    0xe6, 0xe0, 0xb9, 0x0c, 0xd8, 0x08, 0xae, 0xf6, 0x80, 0xb7, 0x48, 0xb8,
    0x10, 0xd1, 0x77, 0x57, 0x48, 0x2a, 0xc1, 0xf1, 0x4e, 0x61, 0xb4, 0x06,
    0x5a, 0xa1, 0xba, 0x32, 0x4a, 0x9a, 0xab, 0x8c, 0xa8, 0xe3, 0xa0, 0x68,
    0x39, 0xa3, 0x25, 0x55, 0x48, 0xcd, 0x12, 0x98, 0x14, 0x1b, 0x39, 0x5b,
    0xf4, 0x88, 0xbe, 0x5f, 0xac, 0xba, 0x18, 0x20, 0x74, 0x2c, 0xa1, 0x0b,
    0xfc, 0xf1, 0x00, 0x85, 0x24, 0x27, 0x3d, 0x80, 0xb6, 0x30, 0x42, 0x7b,
    0x74, 0xb0, 0xbf, 0x87, 0x51, 0x9b, 0xa8, 0x1c, 0x79, 0xbd, 0x50, 0x85,
    0x66, 0xe1, 0x93, 0xdd, 0xc5, 0x5d, 0x5d, 0xd0, 0xbe, 0xcb, 0x90, 0x36,
    0xf8, 0xc6, 0xc2, 0xea, 0xea, 0x40, 0xce, 0xa7, 0xb0, 0xae, 0xc5, 0x44,
    0x3a, 0x27, 0xc5, 0x4a, 0xf0, 0x21, 0x4f, 0xf6, 0x63, 0xb1, 0x8c, 0x20,
    0x76, 0x65, 0xb5, 0x79, 0x55, 0x10, 0xa5, 0xa7, 0x4b, 0x57, 0x96, 0xb3,
    0x03, 0xe6, 0xa8, 0x93, 0xf5, 0xc5, 0xca, 0xe0, 0x1a, 0x23, 0x30, 0x6d,
    0x09, 0x07, 0x81, 0x5e, 0x05, 0xc6, 0x1b, 0x7a, 0x0e, 0xde, 0xf3, 0x00,
    0x91, 0xf3, 0xbb, 0x4b, 0xa6, 0x8e, 0x96, 0xc3, 0x3a, 0xc8, 0x72, 0x19,
    0x23, 0x23, 0xc7, 0x94, 0x46, 0x75, 0xa2, 0xc5, 0x9e, 0xa3, 0x15, 0x86,
    0xb4, 0xa7, 0x95, 0xda, 0x16, 0xae, 0x42, 0xe8, 0xcc, 0xf6, 0x41, 0xbc,
    0x9c, 0xe8, 0x07, 0xc9, 0x05, 0x3c, 0x74, 0xb2, 0xad, 0x77, 0xc4, 0x08,
    0x56, 0x78, 0xb8, 0x65, 0x2a, 0x33, 0x39, 0x82, 0x16, 0x3c, 0x2b, 0x73,
    0x41, 0x2a, 0xbc, 0xc8, 0x36, 0x23, 0x2c, 0x23, 0x29, 0xce, 0x94, 0x8a,
    0xba, 0x13, 0x3f, 0xfa, 0xcc, 0x6e, 0x17, 0x40, 0xb0, 0xb4, 0x9c, 0xae,
    0xf9, 0x72, 0x40, 0xf7, 0x2a, 0xaf, 0x00, 0x17, 0x7e, 0x88, 0x44, 0x4e,
    0x32, 0xa4, 0x80, 0x96, 0x53, 0x20, 0xe4, 0xaa, 0x18, 0x9c, 0x69, 0x63,
    0xdb, 0x81, 0x73, 0x0e, 0x64, 0x40, 0x12, 0xc1, 0x26, 0x0e, 0xac, 0x4d,
    0x38, 0xa6, 0x4c, 0x05, 0xeb, 0x42, 0x1f, 0x88, 0x6e, 0x6c, 0xa3, 0x98,
    0x95, 0xc3, 0x5a, 0x31, 0xfc, 0x8a, 0x7c, 0xd6, 0xa5, 0x6c, 0x5a, 0xc9,
    0xf5, 0xf2, 0xb0, 0xf7, 0x05, 0x8d, 0x3a, 0x0b, 0x3f, 0x4d, 0x1b, 0x0e,
    0x38, 0x7b, 0x1c, 0xe5, 0xd0, 0x75, 0x40, 0x77, 0xc2, 0xe0, 0x8b, 0xa9,
    0xfb, 0xa6, 0xa7, 0x13, 0xf0, 0xbc, 0x0c, 0x13, 0xb0, 0x70, 0x3a, 0x2f,
    0x20, 0xe7, 0x38, 0xd8, 0x82, 0xa4, 0xc0, 0x06, 0x96, 0x95, 0x75, 0x5c,
    0xbc, 0x68, 0xce, 0xee, 0xa6, 0x44, 0xbb, 0xf2, 0x62, 0x14, 0x43, 0x10,
    0xe6, 0x0b, 0x5c, 0x66, 0x02, 0xbf, 0x38, 0xe9, 0x8f, 0x19, 0x69, 0x6c,
    0x08, 0xfa, 0x93, 0x23, 0xf1, 0x35, 0x5f, 0x33, 0xcf, 0xe2, 0x32, 0x11,
    0xda, 0x31, 0xbb, 0xd8, 0xf5, 0x7e, 0x55, 0xc3, 0x6a, 0xa3, 0x04, 0x8c,
    0xef, 0x50, 0x67, 0x0f, 0xe9, 0xbb, 0x11, 0x6b, 0xca, 0x5a, 0x23, 0x15,
    0xf7, 0x52, 0x8c, 0x69, 0xbb, 0x2d, 0x26, 0xd3, 0x78, 0x32, 0x33, 0x9b,
    0x1d, 0x02, 0x7c, 0x22, 0x4b, 0x57, 0x19, 0xec, 0xcd, 0x16, 0x63, 0x5d,
    0x1f, 0x0c, 0x67, 0xda, 0x6a, 0xc7, 0x2c, 0xf1, 0x0b, 0xcf, 0x92, 0x10,
    0xcb, 0x78, 0x57, 0x67, 0x61, 0x0e, 0xb3, 0x55, 0xb0, 0x17, 0x32, 0x4e,
    0x95, 0xf1, 0xcb, 0xb5, 0xdb, 0x5d, 0x8e, 0x38, 0x97, 0x28, 0x46, 0x1e,
    0x37, 0x4b, 0x5c, 0x03, 0xf4, 0x84, 0x0c, 0xfc, 0x49, 0x3b, 0xd2, 0xc6,
    0x97, 0xd4, 0x4f, 0xef, 0xe6, 0x5c, 0xb2, 0x96, 0x8e, 0xbc, 0x18, 0x9f,
    0xec, 0x8a, 0x67, 0x53, 0xe8, 0x2a, 0x41, 0x5c, 0x6e, 0xfe, 0xca, 0x1a,
    0xd0, 0x03, 0x4f, 0xe8, 0xd9, 0xb0, 0xe2, 0xa1, 0x6c, 0x32, 0xd5, 0x37,
    0x68, 0x9c, 0x6b, 0x30, 0x87, 0xb4, 0x6e, 0xa8, 0xc5, 0x45, 0xe4, 0x74,
    0x4a, 0x80, 0x02, 0x84, 0x78, 0x0e, 0x2c, 0x9c, 0x37, 0x07, 0x26, 0x31,
    0x88, 0x0a, 0x89, 0xd2, 0xba, 0x8b, 0x09, 0xb2, 0x16, 0x7b, 0x23, 0xa8,
    0x0c, 0x82, 0xc4, 0x22, 0xa2, 0x21, 0x2d, 0xa4, 0xa0, 0xeb, 0xe4, 0xa6,
    0xd4, 0x16, 0xa0, 0xa7, 0x19, 0x8b, 0x8f, 0x87, 0x9b, 0x53, 0x6c, 0xba,
    0x14, 0xa7, 0xc4, 0x64, 0x08, 0xb4, 0x68, 0xda, 0x85, 0xa6, 0x47, 0xbc,
    0x58, 0x34, 0xcd, 0x60, 0x43, 0xad, 0x14, 0xd4, 0xca, 0xd7, 0xb1, 0x6c,
    0xc0, 0x05, 0x68, 0xaf, 0xa7, 0x08, 0x9d, 0x6a, 0xb0, 0x70, 0x96, 0x81,
    0xb8, 0x55, 0xbf, 0xa1, 0xcc, 0xb1, 0x37, 0xe0, 0x94, 0x42, 0x05, 0x0c,
    0x83, 0xef, 0x6d, 0xf0, 0x4e, 0xba, 0xe6, 0x78, 0x49, 0xaf, 0x1f, 0x88,
    0xf0, 0x18, 0x0c, 0x17, 0x66, 0x92, 0xd8, 0x50, 0x57, 0x3b, 0x8e, 0xc9,
    0xd5, 0x8c, 0xfb, 0x9b, 0x78, 0x65, 0x01, 0x76, 0x7a, 0x0e, 0xf0, 0x7f,
    0x2b, 0xc4, 0x48, 0x81, 0xc2, 0xb7, 0xc1, 0x6e, 0x4b, 0xc7, 0x33, 0xa3,
    0x9b, 0xf1, 0xcf, 0x63, 0x77, 0x52, 0x07, 0xe1, 0x9f, 0xa9, 0x6b, 0xce,
    0x02, 0xbf, 0x06, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};

const uint8_t kExpectedCiphertext[BCM_MLKEM768_CIPHERTEXT_BYTES] = {
    0x8c, 0xfa, 0xd2, 0x09, 0x06, 0x1b, 0xff, 0x23, 0x9f, 0x84, 0x61, 0xfd,
    0x69, 0x7a, 0xa6, 0x2c, 0x57, 0x43, 0x64, 0x6b, 0x6b, 0x6b, 0xa3, 0xe1,
    0xa9, 0x4f, 0x3e, 0x29, 0xff, 0x3d, 0xb1, 0x84, 0x51, 0x1d, 0x48, 0xe3,
    0xf2, 0xf9, 0x5c, 0x9b, 0xf8, 0x05, 0xa1, 0x4c, 0xf4, 0x78, 0xce, 0xd8,
    0xc8, 0x0a, 0xc9, 0x71, 0xa7, 0x50, 0x89, 0xde, 0x66, 0x11, 0x68, 0x2c,
    0x70, 0x0f, 0xa3, 0x15, 0xa7, 0x8f, 0xa7, 0xd1, 0x49, 0x32, 0x2b, 0xe7,
    0xe3, 0x63, 0xff, 0x6b, 0x8c, 0x77, 0xd8, 0x97, 0x6f, 0x6c, 0x80, 0xc2,
    0x73, 0x7d, 0xe2, 0x39, 0x6f, 0x61, 0x4f, 0xa3, 0x85, 0x14, 0x8e, 0x68,
    0x58, 0x18, 0xba, 0x1c, 0x19, 0x01, 0x4c, 0x06, 0x73, 0x58, 0x4a, 0x9d,
    0xdd, 0x2e, 0x78, 0x82, 0x62, 0xbf, 0x15, 0x0c, 0x8e, 0xa7, 0xcf, 0xee,
    0xd7, 0x9a, 0x42, 0x30, 0xf5, 0x44, 0xdc, 0x3f, 0xdc, 0x67, 0x5c, 0x06,
    0xf0, 0xcf, 0x3b, 0x24, 0x9e, 0xfa, 0xe8, 0x4b, 0x3b, 0x00, 0x01, 0x7c,
    0x4d, 0x50, 0xa8, 0xac, 0x30, 0x74, 0xf4, 0x73, 0x98, 0x5e, 0x09, 0x92,
    0xbd, 0xe1, 0xc4, 0x3a, 0x9f, 0xd0, 0x62, 0xc8, 0x4e, 0x7f, 0xb0, 0xaa,
    0xb3, 0x8c, 0xb5, 0xf9, 0x57, 0xe3, 0x90, 0x9a, 0x94, 0x0d, 0xdb, 0x9e,
    0xf7, 0x78, 0xbf, 0x18, 0xd0, 0x02, 0x8e, 0x02, 0x04, 0xbe, 0xee, 0x87,
    0x49, 0xb1, 0xfe, 0x28, 0xd2, 0xdb, 0xd0, 0x7a, 0x12, 0x50, 0xa3, 0xc6,
    0x32, 0xd6, 0x06, 0xa6, 0xc5, 0xb0, 0xa8, 0xbe, 0x49, 0x8a, 0x8b, 0xfe,
    0xf6, 0xb1, 0xe5, 0xb6, 0xf0, 0x61, 0xc6, 0x1f, 0xe7, 0xcb, 0x4d, 0x66,
    0xdd, 0xe4, 0xd2, 0x2b, 0x73, 0xbd, 0x03, 0x79, 0x76, 0x05, 0x87, 0x58,
    0x6f, 0x6f, 0x8f, 0x20, 0x4f, 0x1f, 0x81, 0xa9, 0x9c, 0x22, 0x8d, 0xbd,
    0xef, 0x41, 0x5e, 0x39, 0x9e, 0x90, 0x4b, 0x63, 0x8b, 0x50, 0xac, 0x4f,
    0x20, 0x08, 0x82, 0x53, 0x52, 0x86, 0xce, 0x55, 0xf5, 0x21, 0xd7, 0x8c,
    0xdf, 0xf3, 0x54, 0x90, 0xed, 0x27, 0xa9, 0x95, 0xbb, 0xec, 0x7a, 0xe2,
    0xeb, 0x80, 0x29, 0xca, 0xc9, 0x4b, 0x1a, 0xfe, 0xe8, 0x40, 0xcf, 0x70,
    0x37, 0xb3, 0xb8, 0xb3, 0x04, 0xf6, 0xc3, 0x44, 0x1e, 0xf8, 0x40, 0x95,
    0xf9, 0x92, 0x97, 0x63, 0xab, 0x35, 0x11, 0x7b, 0x2d, 0x9e, 0x5b, 0xeb,
    0xfe, 0x80, 0xfe, 0xc3, 0xc6, 0xa4, 0x8c, 0xac, 0xd2, 0xa5, 0x0d, 0x11,
    0xcd, 0xbc, 0xdd, 0x57, 0x9d, 0xf4, 0xbc, 0xdf, 0xbe, 0xa2, 0xfc, 0xcd,
    0x2b, 0x83, 0x0e, 0x4e, 0x77, 0x44, 0x10, 0x93, 0xeb, 0xca, 0xec, 0xb6,
    0x26, 0x48, 0x45, 0x08, 0x72, 0xd4, 0xfe, 0xa2, 0xe5, 0xc8, 0x12, 0x8f,
    0x38, 0xcd, 0xd6, 0xaa, 0x97, 0xd4, 0xad, 0xb3, 0x0b, 0x19, 0x5b, 0x70,
    0x50, 0xbf, 0x9d, 0x45, 0xd6, 0x0d, 0xf6, 0x72, 0x08, 0x0f, 0x98, 0xbd,
    0x1f, 0x39, 0xea, 0x4c, 0x76, 0xa8, 0x6e, 0xc2, 0x24, 0xe3, 0x3d, 0xf4,
    0x40, 0xe8, 0x68, 0xfe, 0xaf, 0xeb, 0xae, 0x65, 0x84, 0x86, 0x10, 0xb6,
    0x58, 0x75, 0xb9, 0x22, 0x6d, 0x74, 0x5d, 0xa7, 0xc0, 0x17, 0x10, 0x0f,
    0x36, 0x5f, 0x99, 0xf3, 0x60, 0x39, 0xeb, 0x6e, 0x04, 0x4d, 0x29, 0xc7,
    0xec, 0x2a, 0x1d, 0x8f, 0x5c, 0x23, 0x84, 0xba, 0x65, 0xe9, 0xab, 0x32,
    0xd4, 0x62, 0xd1, 0x53, 0xb8, 0x71, 0x40, 0x9a, 0xf0, 0x05, 0xdb, 0xa0,
    0x5a, 0xca, 0xeb, 0xb0, 0xbf, 0xfe, 0x7e, 0x19, 0x32, 0x42, 0xef, 0xab,
    0xf3, 0x49, 0x13, 0x50, 0x08, 0x98, 0xcd, 0xcf, 0x5a, 0x77, 0x5d, 0xe6,
    0x3f, 0x9d, 0xa0, 0x21, 0x98, 0xcb, 0x78, 0x69, 0xd8, 0x22, 0xdb, 0xae,
    0x87, 0x2c, 0x38, 0x0a, 0x96, 0xa2, 0x30, 0x8f, 0x37, 0xe4, 0xc5, 0x94,
    0x57, 0x40, 0x30, 0xa0, 0x4e, 0x7c, 0xfe, 0x6f, 0x2e, 0x15, 0x88, 0x7b,
    0xf2, 0x22, 0x9c, 0x95, 0x7e, 0xd6, 0x2d, 0x37, 0xd4, 0x13, 0x95, 0x31,
    0xf6, 0xbb, 0xf4, 0xaf, 0x33, 0x42, 0xf0, 0x45, 0xb1, 0xa8, 0xb4, 0x09,
    0x9a, 0x6d, 0x7a, 0xdb, 0xb0, 0xf2, 0x00, 0xdc, 0x77, 0x6a, 0x43, 0xf1,
    0xbb, 0x56, 0xc5, 0x7c, 0xee, 0xa6, 0xd6, 0x10, 0xa0, 0x81, 0xca, 0x28,
    0xab, 0x48, 0xb4, 0x72, 0xbc, 0xe6, 0xe2, 0x24, 0xef, 0x49, 0xdc, 0xb5,
    0x1f, 0x83, 0xe7, 0xda, 0xdd, 0x73, 0x5c, 0x7b, 0x85, 0x4a, 0x15, 0x7b,
    0xcb, 0xf1, 0x14, 0x6b, 0x32, 0xef, 0xce, 0xaf, 0x37, 0x04, 0xa4, 0x1e,
    0xcb, 0x1b, 0x84, 0x1d, 0xdb, 0xbf, 0x2f, 0x88, 0x89, 0xf4, 0x5e, 0xb0,
    0x32, 0x99, 0x81, 0x9c, 0x8a, 0xb0, 0xfd, 0x28, 0x6f, 0xc9, 0xe9, 0xaf,
    0x60, 0x11, 0xa5, 0x8d, 0xa4, 0xfb, 0x93, 0x91, 0x7a, 0xa0, 0xd2, 0xcd,
    0xda, 0x4d, 0xf3, 0xfe, 0xc8, 0x55, 0x4f, 0x26, 0x9d, 0x56, 0x87, 0x12,
    0xfe, 0x93, 0x3e, 0x34, 0xf5, 0x3d, 0x4f, 0x6e, 0x26, 0x56, 0xf7, 0x80,
    0xa2, 0xb9, 0x0f, 0xa1, 0xf8, 0x72, 0xb0, 0xaa, 0xec, 0xe2, 0x97, 0xd0,
    0x3a, 0x6d, 0xe5, 0xe9, 0x12, 0x1b, 0x32, 0x0a, 0xdb, 0x52, 0x8f, 0x9d,
    0xd0, 0xff, 0x67, 0xcc, 0x63, 0x41, 0x32, 0x2f, 0xe7, 0x0c, 0xb0, 0xa5,
    0x73, 0xc5, 0xc3, 0x54, 0x75, 0x06, 0x8e, 0x36, 0x54, 0xea, 0x9c, 0x6b,
    0x60, 0xee, 0x10, 0x06, 0xd2, 0xb3, 0x8c, 0xe3, 0x09, 0xfe, 0x1e, 0x47,
    0xc3, 0xd5, 0x6c, 0x0d, 0x0c, 0x84, 0x5e, 0x4f, 0x01, 0xfa, 0xcf, 0xae,
    0xc1, 0xac, 0xd9, 0xb0, 0x03, 0x74, 0x8e, 0xc5, 0x57, 0x51, 0x96, 0x23,
    0x72, 0xc6, 0x81, 0x7f, 0xf1, 0x6c, 0x29, 0xea, 0x31, 0x2a, 0x23, 0xbb,
    0x88, 0x6f, 0x01, 0xa2, 0x2f, 0x69, 0xd2, 0x1c, 0xe8, 0x1e, 0x63, 0x44,
    0xd9, 0x90, 0x0b, 0x57, 0x78, 0xc6, 0xd1, 0xb3, 0xf6, 0x97, 0x99, 0xd8,
    0x09, 0xe0, 0x9d, 0x69, 0x00, 0xa0, 0xd4, 0xe0, 0x80, 0xfd, 0xdf, 0x23,
    0x3a, 0xf8, 0xc0, 0x97, 0xea, 0xb6, 0xbf, 0x87, 0x75, 0x40, 0x1e, 0x0d,
    0x1e, 0x6c, 0x84, 0xa4, 0x7e, 0xbb, 0xa3, 0x02, 0x76, 0x76, 0xc8, 0x4e,
    0x8b, 0x21, 0x3d, 0x27, 0xbc, 0x38, 0x0f, 0x79, 0xfb, 0xc5, 0xdd, 0x37,
    0x67, 0xcb, 0x61, 0x5a, 0x12, 0x85, 0x31, 0x91, 0x3e, 0x7a, 0x4b, 0x6c,
    0xfd, 0x4e, 0x54, 0x47, 0xfc, 0xc5, 0x2e, 0x34, 0xe4, 0xaa, 0x3d, 0xa2,
    0xd3, 0x1d, 0x15, 0xbc, 0xea, 0xa7, 0xe3, 0xcd, 0xd6, 0x49, 0xee, 0x06,
    0x11, 0x2b, 0xf1, 0x58, 0x81, 0xdd, 0x99, 0xe1, 0xbd, 0x63, 0xa0, 0xd7,
    0xa0, 0x4b, 0x01, 0xab, 0xf3, 0x5c, 0x35, 0xf0, 0xb0, 0xdc, 0xd0, 0x87,
    0x78, 0x02, 0xec, 0x99, 0x0e, 0xb2, 0x23, 0x8b, 0xb1, 0x44, 0x93, 0x65,
    0xd5, 0x1c, 0x00, 0x7e, 0x98, 0xb3, 0x5e, 0xf9, 0xca, 0xbc, 0x26, 0x38,
    0x18, 0x22, 0x14, 0x3c, 0xed, 0x8d, 0x54, 0xa4, 0x05, 0x00, 0x7f, 0xfb,
    0xd5, 0x73, 0x77, 0xf4, 0x98, 0xa0, 0xf7, 0x60, 0xb4, 0x47, 0x10, 0x75,
    0x30, 0x2d, 0xde, 0x9c, 0x3e, 0x08, 0x8f, 0xe5, 0xc9, 0x5a, 0xd9, 0x20,
    0xf3, 0x97, 0xb5, 0xd1, 0xb3, 0x90, 0x23, 0x6f, 0x9f, 0x5e, 0xf1, 0x0f,
    0x76, 0x18, 0xbf, 0x2b, 0x23, 0x8e, 0x45, 0x3f, 0xaf, 0x2b, 0x53, 0x78,
    0x27, 0xa2, 0xf6, 0x07, 0x2b, 0x61, 0x24, 0x5b, 0xc7, 0x2e, 0x25, 0xf1,
    0xb3, 0x4c, 0x50, 0xe8, 0x6d, 0xee, 0x56, 0x52, 0x37, 0xd0, 0x6e, 0xd6,
    0xcb, 0xc8, 0x2a, 0xb1, 0xba, 0x49, 0xc7, 0x5a, 0x55, 0x3c, 0x6f, 0x16,
    0x64, 0x08, 0xa6, 0x46, 0x09, 0x37, 0x86, 0x0b, 0xe7, 0x7e, 0x2d, 0xf4,
    0x96, 0x80, 0x41, 0x77, 0x1a, 0xf9, 0xd2, 0xe0, 0xf3, 0x64, 0x0e, 0x3f,
    0x3d, 0xe1, 0xec, 0x63, 0x40, 0x10, 0x15, 0xcf, 0x4c, 0xc9, 0x1c, 0x9b,
    0x9f, 0xe8, 0x50, 0x27, 0xc2, 0x54, 0x44, 0x14, 0xb6, 0x2e, 0xe4, 0x53,
    0xf2, 0x60, 0x8a, 0xb6, 0x1c, 0x14, 0xb6, 0x25, 0xf2, 0x44, 0x59, 0xb7,
    0x67, 0x3b, 0x94, 0x88, 0x15, 0x70, 0x6f, 0xa8};

const uint8_t kExpectedSharedSecret[BCM_MLKEM_SHARED_SECRET_BYTES] = {
    0x7d, 0x9f, 0x1c, 0xb4, 0xae, 0x04, 0xd7, 0x5f, 0xa6, 0x57, 0x5a,
    0xe0, 0xe4, 0x29, 0xb5, 0x73, 0xa9, 0x74, 0xb7, 0xa1, 0x25, 0xbd,
    0xfb, 0x8a, 0x6e, 0x0f, 0x19, 0xba, 0xe1, 0x16, 0xae, 0x81};

const uint8_t
    kExpectedImplicitRejectionSharedSecret[BCM_MLKEM_SHARED_SECRET_BYTES] = {
        0xa3, 0x19, 0x2a, 0x8c, 0x88, 0xfc, 0x99, 0x6d, 0x2d, 0xf9, 0x85,
        0x8d, 0x2c, 0x55, 0x36, 0x39, 0x93, 0xf0, 0x49, 0x4d, 0x7e, 0xc0,
        0xbe, 0x5a, 0x56, 0x7b, 0x8a, 0x42, 0x43, 0xa5, 0x74, 0x5d};
