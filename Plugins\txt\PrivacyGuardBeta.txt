from base_plugin import Hook<PERSON><PERSON>ult, HookStrategy, BasePlugin
import base64
import hashlib
from java.util import Locale
import re
import random

__id__ = "privacy_guard"
__name__ = "Privacy Guard"
__description__ = "Simple plugin for encrypting messages in any chat"
__author__ = "@mi<PERSON><PERSON><PERSON><PERSON><PERSON> & @mishabotov"
__min_version__ = "11.9.0"
__icon__ = "NiggerDuck/14"
__version__ = "1.5.0 [beta]"

class LocalizationManager:
    def __init__(self):
        self.language = Locale.getDefault().getLanguage()
        self.language = self.language if self.language in self._get_supported_languages() else "en"
        
    def get_string(self, string):
        return self.strings[self.language][string]

    def _get_supported_languages(self):
        return self.strings.keys()
    
    strings = {
        "ru": {
            "ENCRYPTION_ENABLED": "✅ Шифрование сообщений включено",
            "ENCRYPTION_DISABLED": "❌ Шифрование сообщений отключено",
            "DECRYPTION_SUCCESS": "✅ Сообщение расшифровано",
            "DECRYPTION_FAILED": "❌ Не удалось расшифровать сообщение",
            "INVALID_KEY": "❌ Неверный ключ шифрования",
            "COMPLEXITY_LOW": "Сложность: Низкая",
            "COMPLEXITY_MEDIUM": "Сложность: Средняя",
            "COMPLEXITY_HIGH": "Сложность: Высокая",
            "COMPLEXITY_VERY_HIGH": "Сложность: Очень высокая",
            "SHOW_LOCK_EMOJI": "Показывать замок 🔒",
            "SHOW_LOCK_EMOJI_SUBTEXT": "Добавлять эмодзи замка в начале зашифрованных сообщений",
            "SHOW_METHOD_INFO": "Показывать информацию о методе шифрования",
            "SHOW_METHOD_INFO_SUBTEXT": "Добавлять название метода шифрования в конце сообщения"
        },
        "en": {
            "ENCRYPTION_ENABLED": "✅ Message encryption enabled",
            "ENCRYPTION_DISABLED": "❌ Message encryption disabled",
            "DECRYPTION_SUCCESS": "✅ Message decrypted",
            "DECRYPTION_FAILED": "❌ Failed to decrypt message",
            "INVALID_KEY": "❌ Invalid encryption key",
            "COMPLEXITY_LOW": "Complexity: Low",
            "COMPLEXITY_MEDIUM": "Complexity: Medium",
            "COMPLEXITY_HIGH": "Complexity: High",
            "COMPLEXITY_VERY_HIGH": "Complexity: Very High",
            "SHOW_LOCK_EMOJI": "Show lock emoji 🔒",
            "SHOW_LOCK_EMOJI_SUBTEXT": "Add lock emoji at the beginning of encrypted messages",
            "SHOW_METHOD_INFO": "Show encryption method info",
            "SHOW_METHOD_INFO_SUBTEXT": "Add encryption method name at the end of the message"
        }
    }
    
localization = LocalizationManager()

def encrypt_caesar(text, shift):
    result = ""
    for char in text:
        if char.isalpha():
            is_upper = char.isupper()
            start_ord = ord('A') if is_upper else ord('a')
            shifted_char_ord = (ord(char) - start_ord + shift) % 26
            result += chr(start_ord + shifted_char_ord)
        else:
            result += char
    return result

def decrypt_caesar(text, shift):
    return encrypt_caesar(text, (26 - shift) % 26)

def encrypt_xor(text, key):
    key_bytes = key.encode('utf-8')
    key_len = len(key_bytes)
    text_bytes = text.encode('utf-8')
    encrypted = bytearray()
    for i in range(len(text_bytes)):
        encrypted.append(text_bytes[i] ^ key_bytes[i % key_len])
    return base64.b64encode(encrypted).decode('utf-8')

def decrypt_xor(text, key):
    try:
        encrypted = base64.b64decode(text.encode('utf-8'))
        key_bytes = key.encode('utf-8')
        key_len = len(key_bytes)
        decrypted = bytearray()
        for i in range(len(encrypted)):
            decrypted.append(encrypted[i] ^ key_bytes[i % key_len])
        return decrypted.decode('utf-8')
    except: return None

def encrypt_aes(text, key):
    key_hash = hashlib.sha256(key.encode('utf-8')).digest()
    text_bytes = text.encode('utf-8')
    encrypted = bytearray()
    for i in range(len(text_bytes)):
        encrypted.append(text_bytes[i] ^ key_hash[i % len(key_hash)])
    return base64.b64encode(encrypted).decode('utf-8')

def decrypt_aes(text, key):
    try:
        key_hash = hashlib.sha256(key.encode('utf-8')).digest()
        encrypted = base64.b64decode(text.encode('utf-8'))
        decrypted = bytearray()
        for i in range(len(encrypted)):
            decrypted.append(encrypted[i] ^ key_hash[i % len(key_hash)])
        return decrypted.decode('utf-8')
    except: return None

def encrypt_rot13(text, _):
    result = ""
    for char in text:
        if char.isalpha():
            start_ord = ord('a') if char.islower() else ord('A')
            rotated_char_ord = (ord(char) - start_ord + 13) % 26
            result += chr(start_ord + rotated_char_ord)
        else: result += char
    return result

def decrypt_rot13(text, _): return encrypt_rot13(text, None)

def encrypt_atbash(text, _):
    result = ""
    for char in text:
        if char.isalpha():
            if char.islower(): result += chr(ord('a') + (ord('z') - ord(char)))
            else: result += chr(ord('A') + (ord('Z') - ord(char)))
        else: result += char
    return result

def decrypt_atbash(text, _): return encrypt_atbash(text, None)

def encrypt_vigenere(text, key):
    if not key: return text
    key_only_alpha = ''.join(c for c in key if c.isalpha()).lower()
    if not key_only_alpha: return text
    result = ""; key_idx = 0
    for char_in_text in text:
        if char_in_text.isalpha():
            key_c = key_only_alpha[key_idx % len(key_only_alpha)]
            key_s = ord(key_c) - ord('a')
            start_o = ord('A') if char_in_text.isupper() else ord('a')
            enc_o = (ord(char_in_text) - start_o + key_s) % 26
            result += chr(start_o + enc_o)
            key_idx +=1
        else: result += char_in_text
    return result

def decrypt_vigenere(text, key):
    if not key: return text
    key_only_alpha = ''.join(c for c in key if c.isalpha()).lower()
    if not key_only_alpha: return text
    result = ""; key_idx = 0
    for char_in_text in text:
        if char_in_text.isalpha():
            key_c = key_only_alpha[key_idx % len(key_only_alpha)]
            key_s = ord(key_c) - ord('a')
            start_o = ord('A') if char_in_text.isupper() else ord('a')
            dec_o = (ord(char_in_text) - start_o - key_s + 26) % 26
            result += chr(start_o + dec_o)
            key_idx += 1
        else: result += char_in_text
    return result

def encrypt_base64(text, _):
    try: return base64.b64encode(text.encode('utf-8')).decode('utf-8')
    except: return text

def decrypt_base64(text, _):
    try: return base64.b64decode(text.encode('utf-8')).decode('utf-8')
    except: return None

MORSE_DICT_ENCRYPT = {
    'A':'.-','B':'-...','C':'-.-.','D':'-..','E':'.','F':'..-.','G':'--.','H':'....','I':'..','J':'.---','K':'-.-','L':'.-..',
    'M':'--','N':'-.','O':'---','P':'.--.','Q':'--.-','R':'.-.','S':'...','T':'-','U':'..-','V':'...-','W':'.--','X':'-..-',
    'Y':'-.--','Z':'--..','0':'-----','1':'.----','2':'..---','3':'...--','4':'....-','5':'.....','6':'-....','7':'--...',
    '8':'---..','9':'----.','А':'.-','Б':'-...','В':'.--','Г':'--.','Д':'-..','Е':'.','Ё':'.','Ж':'...-.','З':'--..',
    'И':'..','Й':'.---','К':'-.-','Л':'.-..','М':'--','Н':'-.','О':'---','П':'.--.','Р':'.-.','С':'...','Т':'-',
    'У':'..-','Ф':'..-.','Х':'....','Ц':'-.-.','Ч':'---.','Ш':'----','Щ':'--.-','Ъ':'--.--','Ы':'-.--','Ь':'-..-',
    'Э':'..-..','Ю':'..--','Я':'.-.-',' ':'/'
}
def encrypt_morse(text, _):
    res = []
    for char_in_text in text.upper():
        if char_in_text in MORSE_DICT_ENCRYPT: res.append(MORSE_DICT_ENCRYPT[char_in_text])
        elif char_in_text.isspace() and res and res[-1] != MORSE_DICT_ENCRYPT[' ']: res.append(MORSE_DICT_ENCRYPT[' '])
    return " ".join(filter(None, res))

REVERSE_MORSE_DUAL_LOOKUP = {
    '.-':{'en':'A','ru':'А'},'-...':{'en':'B','ru':'Б'},'.--':{'en':'W','ru':'В'},'--.':{'en':'G','ru':'Г'},
    '-..':{'en':'D','ru':'Д'},'.':{'en':'E','ru':'Е'},'.---':{'en':'J','ru':'Й'},'-.-':{'en':'K','ru':'К'},
    '.-..':{'en':'L','ru':'Л'},'--':{'en':'M','ru':'М'},'-.':{'en':'N','ru':'Н'},'---':{'en':'O','ru':'О'},
    '.--.':{'en':'P','ru':'П'},'.-.':{'en':'R','ru':'Р'},'...':{'en':'S','ru':'С'},'-':{'en':'T','ru':'Т'},
    '..-':{'en':'U','ru':'У'},'..-.':{'en':'F','ru':'Ф'},'....':{'en':'H','ru':'Х'},'--..':{'en':'Z','ru':'З'},
    '..':{'en':'I','ru':'И'},'-.--':{'en':'Y','ru':'Ы'},'-..-':{'en':'X','ru':'Ь'},'-.-.':{'en':'C','ru':'Ц'},
    '--.-':{'en':'Q','ru':'Щ'}
}
REVERSE_MORSE_UNIQUE_LOOKUP = {
    '...-':'V','...-.':'Ж','---.':'Ч','----':'Ш','--.--':'Ъ','..-..':'Э','..--':'Ю','.-.-':'Я',
    '-----':'0','.----':'1','..---':'2','...--':'3','....-':'4','.....':'5','-....':'6','--...':'7',
    '---..':'8','----.':'9','/':' '
}
def decrypt_morse(text, _):
    try:
        res = []; morse_chars = text.strip().split(' '); pref_lang = localization.language
        for mc in morse_chars:
            if not mc: continue
            cf = False
            if mc in REVERSE_MORSE_DUAL_LOOKUP:
                opts = REVERSE_MORSE_DUAL_LOOKUP[mc]
                if pref_lang in opts: res.append(opts[pref_lang])
                elif 'en' in opts: res.append(opts['en'])
                elif 'ru' in opts: res.append(opts['ru'])
                else: res.append('?')
                cf = True
            if not cf and mc in REVERSE_MORSE_UNIQUE_LOOKUP: res.append(REVERSE_MORSE_UNIQUE_LOOKUP[mc]); cf = True
            if not cf: res.append(mc)
        return "".join(res)
    except: return None

def encrypt_rc4(text, key):
    if not key: return text
    S = list(range(256)); j = 0; key_b = key.encode('utf-8')
    for i in range(256): j = (j + S[i] + key_b[i % len(key_b)]) % 256; S[i], S[j] = S[j], S[i]
    i = j = 0; res_b = bytearray(); text_b = text.encode('utf-8')
    for char_b in text_b:
        i = (i + 1) % 256; j = (j + S[i]) % 256; S[i], S[j] = S[j], S[i]
        k_b = S[(S[i] + S[j]) % 256]; res_b.append(char_b ^ k_b)
    return base64.b64encode(res_b).decode('utf-8')

def decrypt_rc4(text, key):
    try:
        if not key: return None
        decoded_b = base64.b64decode(text.encode('utf-8'))
        try: return encrypt_rc4(decoded_b.decode('latin-1'), key)
        except: return encrypt_rc4(decoded_b.decode('utf-8', errors='ignore'), key)
    except: return None

def encrypt_blowfish(text, key):
    if not key: return text
    h_obj = hashlib.sha512(key.encode('utf-8')); seed = int(h_obj.hexdigest(),16) % (2**32)
    _rand = random.Random(seed); text_b = text.encode('utf-8'); enc = bytearray()
    for byte_val in text_b: mix_b = _rand.randint(0,255); enc.append((byte_val+mix_b)%256)
    return base64.b64encode(enc).decode('utf-8')

def decrypt_blowfish(text, key):
    try:
        if not key: return None
        h_obj = hashlib.sha512(key.encode('utf-8')); seed = int(h_obj.hexdigest(),16) % (2**32)
        _rand = random.Random(seed); enc_b = base64.b64decode(text.encode('utf-8')); dec = bytearray()
        for byte_val in enc_b: mix_b = _rand.randint(0,255); dec.append((byte_val-mix_b+256)%256)
        return dec.decode('utf-8')
    except: return None

def _apply_custom_des_round(data_b, key_b, is_enc_r):
    val = data_b
    for _ in range(8):
        if is_enc_r: val = ((val<<1)|(val>>7))&0xFF; val = (val^key_b)&0xFF
        else: val = (val^key_b)&0xFF; val = ((val>>1)|(val<<7))&0xFF
    return val
def _custom_des_process(data_bs, key_h, is_enc_op):
    proc_bs = bytearray()
    for i, byte_val in enumerate(data_bs):
        key_b_fr = key_h[i%len(key_h)]
        proc_bs.append(_apply_custom_des_round(byte_val, key_b_fr, is_enc_op))
    return proc_bs
def encrypt_des3(text, key):
    if not key: return text
    text_b = text.encode('utf-8')
    k_h1=hashlib.md5((key+"1").encode('utf-8')).digest(); k_h2=hashlib.md5((key+"2").encode('utf-8')).digest(); k_h3=hashlib.md5((key+"3").encode('utf-8')).digest()
    int1=_custom_des_process(text_b,k_h1,True); int2=_custom_des_process(int1,k_h2,False); final=_custom_des_process(int2,k_h3,True)
    return base64.b64encode(final).decode('utf-8')
def decrypt_des3(text, key):
    try:
        if not key: return None
        enc_b = base64.b64decode(text.encode('utf-8'))
        k_h1=hashlib.md5((key+"1").encode('utf-8')).digest(); k_h2=hashlib.md5((key+"2").encode('utf-8')).digest(); k_h3=hashlib.md5((key+"3").encode('utf-8')).digest()
        int1=_custom_des_process(enc_b,k_h3,False); int2=_custom_des_process(int1,k_h2,True); final=_custom_des_process(int2,k_h1,False)
        return final.decode('utf-8')
    except: return None

def modInverse(e, phi_n):
    if phi_n == 0: return None
    try: return pow(e, -1, phi_n)
    except ValueError: return None

_miller_rabin_random_source = random.Random()
def is_prime_miller_rabin(n, k=10):
    if n < 2: return False
    if n == 2 or n == 3: return True
    if n % 2 == 0 or n % 3 == 0: return False
    d, s = n - 1, 0
    while d % 2 == 0: d //= 2; s += 1
    for _ in range(k):
        a = _miller_rabin_random_source.randrange(2, n - 1)
        x = pow(a, d, n)
        if x == 1 or x == n - 1: continue
        for _ in range(s - 1):
            x = pow(x, 2, n)
            if x == n - 1: break
        else: return False
    return True

ALL_DEFAULT_FALLBACK_PRIMES = [
    2003, 2011, 2017, 2027, 2029, 2039, 2053, 2063, 2069, 2081, 2083, 2087, 2089, 2099,
    3001, 3011, 3019, 3023, 3037, 3041, 3049, 3061, 3067, 3079, 3083, 3089,
    4001, 4003, 4007, 4013, 4019, 4021, 4027, 4049, 4051, 4057, 4969, 4973, 4987, 4993, 4999,
    65539, 65543, 65551, 65557, 65563, 65579, 65581, 65587, 65599, 65609, 
    65617, 65621, 65629, 65633, 65641, 65651, 70001, 70009, 70019,
    75011, 75013, 75017, 75029, 75031, 75037, 75041, 75043, 75047, 75049,
    79999, 79987, 79963, 79957, 79931, 79927, 79921, 79919, 79909, 79907
]

def get_random_prime_in_range(min_val, max_val, prng_instance, e_condition_val=None):
    if min_val > max_val: return None
    n_candidate = prng_instance.randint(min_val, max_val)
    if n_candidate % 2 == 0 and n_candidate > 2: n_candidate += 1
    if n_candidate < min_val : n_candidate = min_val
    if n_candidate % 2 == 0 and n_candidate > 2: n_candidate += 1
    
    max_attempts = (max_val - min_val) // 2 + 20
    for _ in range(max_attempts):
        if n_candidate > max_val:
            n_candidate = prng_instance.randint(min_val, max_val)
            if n_candidate % 2 == 0 and n_candidate > 2: n_candidate += 1
            if n_candidate < min_val : n_candidate = min_val
            if n_candidate % 2 == 0 and n_candidate > 2: n_candidate += 1


        is_p = is_prime_miller_rabin(n_candidate)
        passes_e_cond = True
        if e_condition_val and is_p:
            if (n_candidate - 1) % e_condition_val == 0:
                passes_e_cond = False
        
        if is_p and passes_e_cond:
            return n_candidate
        
        n_candidate += 2

    fallback_options = [p for p in ALL_DEFAULT_FALLBACK_PRIMES 
                        if min_val <= p <= max_val and \
                           ((p-1) % e_condition_val != 0 if e_condition_val else True)]
    return prng_instance.choice(fallback_options) if fallback_options else None


def encrypt_rsa(text, key):
    if not key or not text: return text
    
    e = 65537
    seed_val = sum(ord(c) for c in key)
    _random_gen = random.Random(seed_val)
    
    n_min_val = e + 2
    n_max_val = n_min_val + 15000
                                  

    n = get_random_prime_in_range(n_min_val, n_max_val, _random_gen, e_condition_val=e)
    
    if n is None:
        return text

    result_parts = []
    for char_byte in text.encode('utf-8'):
        m = char_byte
        c = pow(m, e, n)
        result_parts.append(str(c))
    
    final_payload = ",".join(result_parts)
    return base64.b64encode(final_payload.encode('utf-8')).decode('utf-8') + f"|{n}"

def decrypt_rsa(text_with_n, key):
    try:
        if not key or not text_with_n: return None
        parts = text_with_n.split('|')
        if len(parts) != 2: return None
        b64_text, n_str = parts
        if not b64_text and not n_str: return ""
        if not b64_text and n_str:
            
            
            
            
            
            
            if not b64_text:
                 return ""

        n = int(n_str)
        e = 65537
        phi_n = n - 1 
        if phi_n <= 0: return None 

        d = modInverse(e, phi_n)
        if d is None: return None

        encrypted_values_str = base64.b64decode(b64_text.encode('utf-8')).decode('utf-8')
        if not encrypted_values_str:
            return ""

        encrypted_values = encrypted_values_str.split(',')
        
        decrypted_bytes = bytearray()
        for val_str in encrypted_values:
            if not val_str: continue
            c = int(val_str)
            m = pow(c, d, n)
            decrypted_bytes.append(m)
        return decrypted_bytes.decode('utf-8', errors='replace')
    except Exception:
        return None

def encrypt_elgamal(text, key):
    if not key or not text: return text
    key_hash = hashlib.sha256(key.encode('utf-8')).hexdigest()
    
    prime_gen_seed = int(key_hash[:8], 16) 
    _prime_prng = random.Random(prime_gen_seed)

    p_min_val = 2000
    p_max_val = 5000
    p = get_random_prime_in_range(p_min_val, p_max_val, _prime_prng)
    if p is None: return text

    g = _prime_prng.randint(2, p-1)
    a = (int(key_hash[8:24], 16) % (p - 2)) + 1
    h = pow(g, a, p)

    k_prng_seed = int(key_hash[24:32], 16) if len(key_hash) >= 32 else prime_gen_seed + 1
    _k_prng = random.Random(k_prng_seed)

    result_pairs = []
    for char_byte in text.encode('utf-8'):
        m = char_byte
        if m >= p: result_pairs.append(f"0,{m}"); continue
        
        k = _k_prng.randint(1, p-2) 
        c1 = pow(g, k, p)
        s_shared = pow(h, k, p) 
        c2 = (m * s_shared) % p
        result_pairs.append(f"{c1},{c2}")
    
    final_payload = ",".join(result_pairs)
    return base64.b64encode(final_payload.encode('utf-8')).decode('utf-8') + f"|{p}|{g}"

def decrypt_elgamal(text_with_params, key):
    try:
        if not key or not text_with_params: return None
        parts = text_with_params.split('|')
        if len(parts) != 3: return None
        b64_cipher, p_str, g_str = parts
        if not b64_cipher and not p_str and not g_str : return ""

        p = int(p_str)
        
        key_hash = hashlib.sha256(key.encode('utf-8')).hexdigest()
        a = (int(key_hash[8:24], 16) % (p - 2)) + 1

        ciphertext_b64 = base64.b64decode(b64_cipher.encode('utf-8')).decode('utf-8')
        if not ciphertext_b64 : return ""

        pairs_str = ciphertext_b64.split(',')
        decrypted_bytes = bytearray()
        i = 0
        while i < len(pairs_str) -1 :
            c1_str, c2_str = pairs_str[i], pairs_str[i+1]
            if not c1_str or not c2_str: i += 2; continue
            c1 = int(c1_str); c2 = int(c2_str)
            if c1 == 0: decrypted_bytes.append(c2); i += 2; continue
            s_dec = pow(c1, a, p)
            s_inv = modInverse(s_dec, p)
            if s_inv is None: return None
            m = (c2 * s_inv) % p
            decrypted_bytes.append(m)
            i += 2
        return decrypted_bytes.decode('utf-8', errors='replace')
    except: return None

def encrypt_rail_fence(text, key):
    try:
        if not key:
            rails = 3
        else:
            rails = max(2, sum(ord(c) for c in key) % 10 + 2)
        
        if len(text) <= rails:
            return text
        
        fence = [[] for _ in range(rails)]
        rail = 0
        direction = 1
        
        for char in text:
            fence[rail].append(char)
            rail += direction
            
            if rail == 0 or rail == rails-1:
                direction = -direction
        
        result = ''.join(''.join(rail) for rail in fence)
        return result
    except:
        return text

def decrypt_rail_fence(text, key):
    try:
        if not key:
            rails = 3
        else:
            rails = max(2, sum(ord(c) for c in key) % 10 + 2)
        
        if len(text) <= rails:
            return text
        
        fence = [[''] * len(text) for _ in range(rails)]
        rail = 0
        direction = 1
        
        
        for i in range(len(text)):
            fence[rail][i] = '*'
            rail += direction
            
            if rail == 0 or rail == rails-1:
                direction = -direction
        
        
        index = 0
        for i in range(rails):
            for j in range(len(text)):
                if fence[i][j] == '*' and index < len(text):
                    fence[i][j] = text[index]
                    index += 1
        
        
        result = []
        rail = 0
        direction = 1
        
        for i in range(len(text)):
            result.append(fence[rail][i])
            rail += direction
            
            if rail == 0 or rail == rails-1:
                direction = -direction
        
        return ''.join(result)
    except:
        return None

def _gcd(a, b):
    while b:
        a, b = b, a % b
    return a

def encrypt_affine(text, key):
    try:
        
        key_hash = hashlib.md5(key.encode('utf-8')).hexdigest()
        a = (int(key_hash[:8], 16) % 25) + 1
        if a % 2 == 0:  
            a += 1
        b = int(key_hash[8:16], 16) % 26
        
        result = ""
        for char in text:
            if char.isalpha():
                is_upper = char.isupper()
                char_code = ord(char.lower()) - ord('a')
                
                new_code = (a * char_code + b) % 26
                new_char = chr(new_code + ord('a'))
                result += new_char.upper() if is_upper else new_char
            else:
                result += char
        return result
    except:
        return text

def decrypt_affine(text, key):
    try:
        
        key_hash = hashlib.md5(key.encode('utf-8')).hexdigest()
        a = (int(key_hash[:8], 16) % 25) + 1
        if a % 2 == 0:  
            a += 1
        b = int(key_hash[8:16], 16) % 26
        
        
        for a_inv in range(1, 26):
            if (a * a_inv) % 26 == 1:
                break
        else:
            return None  
        
        result = ""
        for char in text:
            if char.isalpha():
                is_upper = char.isupper()
                char_code = ord(char.lower()) - ord('a')
                
                new_code = (a_inv * (char_code - b)) % 26
                new_char = chr(new_code + ord('a'))
                result += new_char.upper() if is_upper else new_char
            else:
                result += char
        return result
    except:
        return None

def _prepare_playfair_key(key):
    
    alphabet = "abcdefghiklmnopqrstuvwxyz"  
    key = key.lower().replace('j', 'i')
    key = ''.join(dict.fromkeys(key + alphabet))  
    matrix = []
    for i in range(0, 25, 5):
        matrix.append(key[i:i+5])
    return matrix

def _find_position(matrix, char):
    char = char.lower()
    if char == 'j':
        char = 'i'
    for row in range(5):
        for col in range(5):
            if matrix[row][col] == char:
                return row, col
    return -1, -1

def encrypt_playfair(text, key):
    if not key or len(text) < 2:
        return text
        
    
    clean_text = ''.join(c for c in text.lower() if c.isalpha())
    prepared_text = ""
    i = 0
    while i < len(clean_text):
        if i == len(clean_text) - 1:
            prepared_text += clean_text[i] + 'x'
            i += 1
        elif clean_text[i] == clean_text[i+1]:
            prepared_text += clean_text[i] + 'x'
            i += 1
        else:
            prepared_text += clean_text[i:i+2]
            i += 2
    
    
    if len(prepared_text) % 2 != 0:
        prepared_text += 'x'
    
    matrix = _prepare_playfair_key(key)
    result = ""
    
    for i in range(0, len(prepared_text), 2):
        char1, char2 = prepared_text[i], prepared_text[i+1]
        row1, col1 = _find_position(matrix, char1)
        row2, col2 = _find_position(matrix, char2)
        
        if row1 == row2:  
            result += matrix[row1][(col1+1)%5] + matrix[row2][(col2+1)%5]
        elif col1 == col2:  
            result += matrix[(row1+1)%5][col1] + matrix[(row2+1)%5][col2]
        else:  
            result += matrix[row1][col2] + matrix[row2][col1]
            
    
    final_result = ""
    text_idx = 0
    result_idx = 0
    
    while text_idx < len(text) and result_idx < len(result):
        if not text[text_idx].isalpha():
            final_result += text[text_idx]
            text_idx += 1
        else:
            final_result += result[result_idx].upper() if text[text_idx].isupper() else result[result_idx]
            text_idx += 1
            result_idx += 1
            
    return final_result

def decrypt_playfair(text, key):
    try:
        if not key:
            return None
            
        
        clean_text = ''.join(c for c in text.lower() if c.isalpha())
        if len(clean_text) % 2 != 0:
            clean_text += 'x'
            
        matrix = _prepare_playfair_key(key)
        result = ""
        
        for i in range(0, len(clean_text), 2):
            char1, char2 = clean_text[i], clean_text[i+1]
            row1, col1 = _find_position(matrix, char1)
            row2, col2 = _find_position(matrix, char2)
            
            if row1 == row2:  
                result += matrix[row1][(col1-1)%5] + matrix[row2][(col2-1)%5]
            elif col1 == col2:  
                result += matrix[(row1-1)%5][col1] + matrix[(row2-1)%5][col2]
            else:  
                result += matrix[row1][col2] + matrix[row2][col1]
                
        
        final_result = ""
        text_idx = 0
        result_idx = 0
        
        while text_idx < len(text) and result_idx < len(result):
            if not text[text_idx].isalpha():
                final_result += text[text_idx]
                text_idx += 1
            else:
                final_result += result[result_idx].upper() if text[text_idx].isupper() else result[result_idx]
                text_idx += 1
                result_idx += 1
        
        
        if final_result and final_result[-1] == 'x':
            final_result = final_result[:-1]
            
        return final_result
    except:
        return None

def _polybius_square(key=None, is_cyrillic=False):
    if is_cyrillic:
        
        base_chars = "абвгдеёжзийклмнопрстуфхцчшщъыьэюя"
        rows, cols = 5, 6
    else:
        
        base_chars = "abcdefghiklmnopqrstuvwxyz"  
        rows, cols = 5, 5
    
    
    if key:
        key = key.lower()
        if is_cyrillic:
            key = ''.join(c for c in key if 'а' <= c <= 'я' or c == 'ё')
        else:
            key = ''.join(c for c in key if c.isalpha())
            key = key.replace('j', 'i')
        
        
        unique_chars = []
        for c in key + base_chars:
            if c not in unique_chars:
                unique_chars.append(c)
        chars = ''.join(unique_chars)
    else:
        chars = base_chars
    
    
    square = []
    for i in range(rows):
        row = []
        for j in range(cols):
            idx = i * cols + j
            if idx < len(chars):
                row.append(chars[idx])
            else:
                row.append('')  
        square.append(row)
    
    return square, rows, cols

def _find_in_polybius(char, key=None):
    
    is_cyrillic = 'а' <= char.lower() <= 'я' or char.lower() == 'ё'
    
    square, rows, cols = _polybius_square(key, is_cyrillic)
    char = char.lower()
    if not is_cyrillic and char == 'j':
        char = 'i'
    
    for row in range(rows):
        for col in range(cols):
            if row < len(square) and col < len(square[row]) and square[row][col] == char:
                return row + 1, col + 1, is_cyrillic
    
    return -1, -1, is_cyrillic

def encrypt_bifid(text, key):
    if not key:
        return text
    
    
    result_positions = []
    char_types = []  
    
    for char in text:
        if char.isalpha() or 'а' <= char.lower() <= 'я' or char.lower() == 'ё':
            row, col, is_cyrillic = _find_in_polybius(char, key)
            if row != -1:
                result_positions.append((row, col))
                char_types.append((char.isupper(), is_cyrillic))
        else:
            result_positions.append(None)
            char_types.append(None)
    
    
    if not any(pos for pos in result_positions if pos is not None):
        return text
    
    
    rows = []
    cols = []
    for pos in result_positions:
        if pos is not None:
            rows.append(pos[0])
            cols.append(pos[1])
    
    
    all_coords = rows + cols
    
    
    result = []
    idx = 0
    for i, pos in enumerate(result_positions):
        if pos is None:
            result.append(text[i])  
        else:
            if idx + 1 < len(all_coords):
                row, col = all_coords[idx], all_coords[idx + 1]
                is_upper, is_cyr = char_types[i]
                
                square, _, _ = _polybius_square(key, is_cyr)
                if 0 < row <= len(square) and 0 < col <= len(square[0]):
                    char = square[row-1][col-1]
                    result.append(char.upper() if is_upper else char)
                else:
                    result.append(text[i])  
                idx += 2
            else:
                result.append(text[i])  
    
    return ''.join(result)

def decrypt_bifid(text, key):
    try:
        if not key:
            return None
        
        
        clean_chars = []
        char_positions = []
        original_format = []
        
        for char in text:
            if char.isalpha() or 'а' <= char.lower() <= 'я' or char.lower() == 'ё':
                row, col, is_cyrillic = _find_in_polybius(char, key)
                if row != -1:
                    clean_chars.append(char.lower())
                    char_positions.append((row, col))
                    original_format.append((len(clean_chars)-1, char.isupper(), is_cyrillic))
                else:
                    original_format.append((-1, None, None))
            else:
                original_format.append((-1, None, None))
        
        
        coords = []
        for row, col in char_positions:
            coords.append(row)
            coords.append(col)
        
        
        half_len = len(coords) // 2
        row_coords = coords[:half_len]
        col_coords = coords[half_len:]
        
        
        decrypted_chars = []
        for i in range(min(len(row_coords), len(col_coords))):
            row, col = row_coords[i], col_coords[i]
            _, is_cyrillic = original_format[i][1:]
            square, rows, cols = _polybius_square(key, is_cyrillic if is_cyrillic is not None else False)
            
            if 0 < row <= rows and 0 < col <= cols:
                decrypted_chars.append(square[row-1][col-1])
            else:
                decrypted_chars.append('?')  
        
        
        result = list(text)  
        for idx, is_upper, is_cyrillic in original_format:
            if idx != -1 and idx < len(decrypted_chars):
                char = decrypted_chars[idx]
                result[original_format.index((idx, is_upper, is_cyrillic))] = char.upper() if is_upper else char
        
        return ''.join(result)
    except Exception as e:
        return None

def _one_time_pad_key(text_length, seed_key):
    random_gen = random.Random(
        int.from_bytes(hashlib.md5(seed_key.encode()).digest()[:4], byteorder='little')
    )
    return [random_gen.randint(0, 255) for _ in range(text_length)]

def encrypt_one_time_pad(text, key):
    if not key:
        return text
        
    text_bytes = text.encode('utf-8')
    pad = _one_time_pad_key(len(text_bytes), key)
    
    encrypted = bytearray()
    for i in range(len(text_bytes)):
        encrypted.append(text_bytes[i] ^ pad[i])
        
    return base64.b64encode(encrypted).decode('utf-8')

def decrypt_one_time_pad(text, key):
    try:
        if not key:
            return None
            
        encrypted = base64.b64decode(text.encode('utf-8'))
        pad = _one_time_pad_key(len(encrypted), key)
        
        decrypted = bytearray()
        for i in range(len(encrypted)):
            decrypted.append(encrypted[i] ^ pad[i])
            
        return decrypted.decode('utf-8')
    except:
        return None


RU_TO_EN = {
    'а': 'f', 'б': ',', 'в': 'd', 'г': 'u', 'д': 'l', 'е': 't', 'ё': '`', 'ж': ';', 'з': 'p', 'и': 'b',
    'й': 'q', 'к': 'r', 'л': 'k', 'м': 'v', 'н': 'y', 'о': 'j', 'п': 'g', 'р': 'h', 'с': 'c', 'т': 'n',
    'у': 'e', 'ф': 'a', 'х': '[', 'ц': 'w', 'ч': 'x', 'ш': 'i', 'щ': 'o', 'ъ': ']', 'ы': 's', 'ь': 'm',
    "'": 'э', '.': 'ю', 'я': 'z',
    'А': 'F', '<': 'Б', 'В': 'D', 'Г': 'U', 'Д': 'L', 'Е': 'T', 'Ё': '~', ':': 'Ж', 'З': 'P', 'И': 'B',
    'Й': 'Q', 'К': 'R', 'Л': 'K', 'М': 'V', 'Н': 'Y', 'О': 'J', 'П': 'G', 'Р': 'H', 'С': 'C', 'Т': 'N',
    'У': 'E', 'Ф': 'A', '{': 'Х', 'Ц': 'W', 'Ч': 'X', 'Ш': 'I', 'Щ': 'O', 'Ъ': '}', 'Ы': 'S', 'Ь': 'M',
    '"': 'Э', '>': 'Ю', 'Я': 'Z'
}

EN_TO_RU = {
    'f': 'а', ',': 'б', 'd': 'в', 'u': 'г', 'l': 'д', 't': 'е', '`': 'ё', ';': 'ж', 'p': 'з', 'b': 'и',
    'q': 'й', 'r': 'к', 'k': 'л', 'v': 'м', 'y': 'н', 'j': 'о', 'g': 'п', 'h': 'р', 'c': 'с', 'n': 'т',
    'e': 'у', 'a': 'ф', '[': 'х', 'w': 'ц', 'x': 'ч', 'i': 'ш', 'o': 'щ', ']': 'ъ', 's': 'ы', 'm': 'ь',
    "'": 'э', '.': 'ю', 'z': 'я',
    'F': 'А', '<': 'Б', 'D': 'В', 'U': 'Г', 'L': 'Д', 'T': 'Е', '~': 'Ё', ':': 'Ж', 'P': 'З', 'B': 'И',
    'Q': 'Й', 'R': 'К', 'K': 'Л', 'V': 'М', 'Y': 'Н', 'J': 'О', 'G': 'П', 'H': 'Р', 'C': 'С', 'N': 'Т',
    'E': 'У', 'A': 'Ф', '{': 'Х', 'W': 'Ц', 'X': 'Ч', 'I': 'Ш', 'O': 'Щ', '}': 'Ъ', 'S': 'Ы', 'M': 'Ь',
    '"': 'Э', '>': 'Ю', 'Z': 'Я'
}

def encrypt_keyboard_layout(text, _):
    """Convert text between Russian and English keyboard layouts."""
    result = ""
    for char in text:
        
        if 'а' <= char.lower() <= 'я' or char.lower() == 'ё':
            
            result += RU_TO_EN.get(char, char)
        elif char in EN_TO_RU:
            
            result += EN_TO_RU.get(char, char)
        else:
            
            result += char
    return result

def decrypt_keyboard_layout(text, _):
    """Identical to encrypt - this method switches keyboard layouts both ways."""
    return encrypt_keyboard_layout(text, _)

class PrivacyGuardPlugin(BasePlugin):
    def create_settings(self):
        from ui.settings import Header, Divider, Switch, Selector, Input, Text
        lang = Locale.getDefault().getLanguage()
        
        if lang.startswith("ru"):
            header = "Настройки шифрования"; enabled_text = "Включить шифрование сообщений"; enabled_subtext = "Автоматически шифровать все исходящие сообщения"
            method_text = "Метод шифрования"; key_text = "Ключ шифрования"; key_subtext = "Ключ, используемый для шифрования и расшифровки сообщений"
            info_text = "Используйте .decrypt для расшифровки сообщений"; complexity_header = "Уровни сложности шифрования"; appearance_header = "Внешний вид"
        else:
            header = "Encryption Settings"; enabled_text = "Enable message encryption"; enabled_subtext = "Automatically encrypt all outgoing messages"
            method_text = "Encryption method"; key_text = "Encryption key"; key_subtext = "Key used for encrypting and decrypting messages"
            info_text = "Use .decrypt to decrypt messages"; complexity_header = "Encryption Complexity Levels"; appearance_header = "Appearance"
            
        
        methods = [
            
            "Caesar cipher (Low)", 
            "ROT13 (Low)", 
            "Atbash cipher (Low)", 
            "Base64 encoding (Low)", 
            "Morse code (Low)", 
            "Rail Fence cipher (Low)",
            "Keyboard layout (Low)",
            
            "XOR encryption (Medium)", 
            "Vigenère cipher (Medium)", 
            "AES encryption (Medium)",
            "RC4 encryption (Medium)",
            "Affine cipher (Medium)",
            "Playfair cipher (Medium)", 
            
            "Blowfish encryption (High)", 
            "Triple DES (High)",
            "Bifid cipher (High)", 
            
            "RSA encryption (Very High)", 
            "ElGamal encryption (Very High)",
            "One-time pad (Very High)"
        ]
        settings = [
            Header(text=header),
            Switch(key="enabled", text=enabled_text, icon="ic_lock_24dp", default=False, subtext=enabled_subtext),
            Selector(key="method", text=method_text, icon="ic_keyboard_24dp", default=0, items=methods),
            Input(key="key", text=key_text, default="mySecretKey", subtext=key_subtext),
            Divider(text=info_text), Header(text=appearance_header),
            Switch(key="show_lock", text=localization.get_string("SHOW_LOCK_EMOJI"), default=True, subtext=localization.get_string("SHOW_LOCK_EMOJI_SUBTEXT")),
            Switch(key="show_method", text=localization.get_string("SHOW_METHOD_INFO"), default=False, subtext=localization.get_string("SHOW_METHOD_INFO_SUBTEXT")),
            Header(text=complexity_header), Text(text=localization.get_string("COMPLEXITY_LOW")), Text(text=localization.get_string("COMPLEXITY_MEDIUM")),
            Text(text=localization.get_string("COMPLEXITY_HIGH")), Text(text=localization.get_string("COMPLEXITY_VERY_HIGH"))
        ]
        return settings
        
    def on_plugin_load(self): self.add_on_send_message_hook()
    
    def on_send_message_hook(self, account, params) -> HookResult:
        if not hasattr(params, "message") or not isinstance(params.message, str): return HookResult()
        msg_content = params.message
        
        if msg_content.strip() == ".fpg":
            curr_enabled = self.get_setting("enabled", False); self.set_setting("enabled", not curr_enabled)
            params.message = localization.get_string("ENCRYPTION_ENABLED") if not curr_enabled else localization.get_string("ENCRYPTION_DISABLED")
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
        
        if msg_content.startswith(".decrypt"):
            enc_text_to_dec = ""
            if len(msg_content) > 9 and msg_content[8] == " ": enc_text_to_dec = msg_content[9:].strip()
            elif len(msg_content) > 8 and msg_content[8] != " ": enc_text_to_dec = msg_content[8:].strip()
            
            if not enc_text_to_dec:
                params.message = "Укажите текст после .decrypt"
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

            if self.get_setting("show_lock", True) and enc_text_to_dec.startswith("🔒 "):
                enc_text_to_dec = enc_text_to_dec[2:]
            
            
            method_names_strip = ["Caesar", "ROT13", "Atbash", "Base64", "Morse", "Rail Fence", "Keyboard layout",
                                 "XOR", "Vigenère", "AES", "RC4", "Affine", "Playfair", 
                                 "Blowfish", "3DES", "Bifid", 
                                 "RSA", "ElGamal", "One-time pad"]
            if self.get_setting("show_method", False):
                for m_name in method_names_strip:
                    suffix = f" [{m_name}]"
                    if enc_text_to_dec.endswith(suffix): enc_text_to_dec = enc_text_to_dec[:-len(suffix)].strip(); break
            
            key = self.get_setting("key", "mySecretKey"); method_idx = self.get_setting("method", 0)
            
            dec_funcs = [
                
                decrypt_caesar, decrypt_rot13, decrypt_atbash, decrypt_base64, decrypt_morse, decrypt_rail_fence,
                decrypt_keyboard_layout,
                
                decrypt_xor, decrypt_vigenere, decrypt_aes, decrypt_rc4, decrypt_affine, decrypt_playfair,
                
                decrypt_blowfish, decrypt_des3, decrypt_bifid,
                
                decrypt_rsa, decrypt_elgamal, decrypt_one_time_pad
            ]
            dec_func = dec_funcs[method_idx] if 0 <= method_idx < len(dec_funcs) else None
            dec_text = None

            if dec_func:
                if method_idx == 0:  
                    s = sum(ord(c) for c in key) % 26
                    if s == 0 and key: s = 1 
                    elif not key: s = 13
                    dec_text = dec_func(enc_text_to_dec, s)
                else: dec_text = dec_func(enc_text_to_dec, key)
            
            params.message = f"🔓 {dec_text}" if dec_text is not None else localization.get_string("DECRYPTION_FAILED")
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
            
        if self.get_setting("enabled", False):
            key = self.get_setting("key", "mySecretKey"); method_idx = self.get_setting("method", 0)
            
            enc_funcs = [
                
                encrypt_caesar, encrypt_rot13, encrypt_atbash, encrypt_base64, encrypt_morse, encrypt_rail_fence,
                encrypt_keyboard_layout,
                
                encrypt_xor, encrypt_vigenere, encrypt_aes, encrypt_rc4, encrypt_affine, encrypt_playfair,
                
                encrypt_blowfish, encrypt_des3, encrypt_bifid,
                
                encrypt_rsa, encrypt_elgamal, encrypt_one_time_pad
            ]
            enc_func = enc_funcs[method_idx] if 0 <= method_idx < len(enc_funcs) else None
            enc_msg_body = msg_content 

            if enc_func:
                if method_idx == 0:  
                    s = sum(ord(c) for c in key) % 26
                    if s == 0 and key: s = 1
                    elif not key: s = 13
                    enc_msg_body = enc_func(msg_content, s)
                else: enc_msg_body = enc_func(msg_content, key)
            
            if enc_msg_body is None or enc_msg_body == msg_content:
                 final_msg = msg_content if enc_msg_body is None else enc_msg_body
            else:
                
                method_names = ["Caesar", "ROT13", "Atbash", "Base64", "Morse", "Rail Fence", "Keyboard layout",
                              "XOR", "Vigenère", "AES", "RC4", "Affine", "Playfair", 
                              "Blowfish", "3DES", "Bifid", 
                              "RSA", "ElGamal", "One-time pad"]
                show_l = self.get_setting("show_lock", True); show_m = self.get_setting("show_method", False)
                final_msg = ""
                if show_l: final_msg += "🔒 "
                final_msg += enc_msg_body
                if show_m and 0 <= method_idx < len(method_names): final_msg += f" [{method_names[method_idx]}]"

            params.message = final_msg
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
        return HookResult()