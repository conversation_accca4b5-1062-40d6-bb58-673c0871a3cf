// This file was generated by
//     //third_party/jni_zero/jni_zero.py
// For
//     java.util.Arrays

#ifndef java_util_Arrays_JNI
#define java_util_Arrays_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "../../../../../../../third_party/jni_zero/jni_zero_internal.h"

// Class Accessors
#ifndef java_util_Arrays_clazz_defined
#define java_util_Arrays_clazz_defined
inline jclass java_util_Arrays_clazz(JNIEnv* env) {
  static const char kClassName[] = "java/util/Arrays";
  static std::atomic<jclass> cached_class;
  return jni_zero::internal::LazyGetClass(env, kClassName, &cached_class);
}
#endif


namespace JNI_Arrays {

// Native to Java functions
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_Constructor(JNIEnv* env) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "<init>",
      "()V",
      &cached_method_id);
  auto _ret = env->NewObject(clazz, call_context.method_id());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_asList(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "asList",
      "([Ljava/lang/Object;)Ljava/util/List;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jint Java_Arrays_binarySearch__byteArray__byte(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0,
    jbyte p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([BB)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1);
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch__byteArray__int__int__byte(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    jbyte p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([BIIB)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3);
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch__charArray__char(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0,
    jchar p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([CC)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1);
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch__charArray__int__int__char(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    jchar p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([CIIC)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3);
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch__doubleArray__double(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    jdouble p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([DD)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1);
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch__doubleArray__int__int__double(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    jdouble p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([DIID)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3);
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch__floatArray__float(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0,
    jfloat p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([FF)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1);
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch__floatArray__int__int__float(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    jfloat p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([FIIF)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3);
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch__intArray__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    JniIntWrapper p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([II)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch__intArray__int__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    JniIntWrapper p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([IIII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      as_jint(p3));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch__longArray__int__int__long(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    jlong p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([JIIJ)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3);
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch__longArray__long(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    jlong p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([JJ)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1);
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch__shortArray__int__int__short(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    jshort p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([SIIS)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3);
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch__shortArray__short(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0,
    jshort p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([SS)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1);
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch__ObjectArray__int__int__Object(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jobject>& p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([Ljava/lang/Object;IILjava/lang/Object;)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([Ljava/lang/Object;IILjava/lang/Object;Ljava/util/Comparator;)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      p4.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch__ObjectArray__Object(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([Ljava/lang/Object;Ljava/lang/Object;)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_binarySearch(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "binarySearch",
      "([Ljava/lang/Object;Ljava/lang/Object;Ljava/util/Comparator;)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      p1.obj(),
      p2.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__booleanArray__int__int__booleanArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jbooleanArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jbooleanArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([ZII[ZII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__booleanArray__booleanArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jbooleanArray>& p0,
    const jni_zero::JavaRef<jbooleanArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([Z[Z)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__byteArray__int__int__byteArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jbyteArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([BII[BII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__byteArray__byteArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0,
    const jni_zero::JavaRef<jbyteArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([B[B)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__charArray__int__int__charArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jcharArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([CII[CII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__charArray__charArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0,
    const jni_zero::JavaRef<jcharArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([C[C)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__doubleArray__int__int__doubleArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jdoubleArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([DII[DII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__doubleArray__doubleArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    const jni_zero::JavaRef<jdoubleArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([D[D)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__floatArray__int__int__floatArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jfloatArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([FII[FII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__floatArray__floatArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0,
    const jni_zero::JavaRef<jfloatArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([F[F)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__intArray__int__int__intArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jintArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([III[III)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__intArray__intArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    const jni_zero::JavaRef<jintArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([I[I)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__longArray__int__int__longArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jlongArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([JII[JII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__longArray__longArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    const jni_zero::JavaRef<jlongArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([J[J)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__shortArray__int__int__shortArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jshortArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([SII[SII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__shortArray__shortArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0,
    const jni_zero::JavaRef<jshortArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([S[S)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__ComparableArray__int__int__ComparableArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jobjectArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([Ljava/lang/Comparable;II[Ljava/lang/Comparable;II)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare__ComparableArray__ComparableArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    const jni_zero::JavaRef<jobjectArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([Ljava/lang/Comparable;[Ljava/lang/Comparable;)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jobjectArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5,
    const jni_zero::JavaRef<jobject>& p6) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([Ljava/lang/Object;II[Ljava/lang/Object;IILjava/util/Comparator;)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5),
      p6.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compare(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    const jni_zero::JavaRef<jobjectArray>& p1,
    const jni_zero::JavaRef<jobject>& p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compare",
      "([Ljava/lang/Object;[Ljava/lang/Object;Ljava/util/Comparator;)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      p1.obj(),
      p2.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compareUnsigned__byteArray__int__int__byteArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jbyteArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compareUnsigned",
      "([BII[BII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compareUnsigned__byteArray__byteArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0,
    const jni_zero::JavaRef<jbyteArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compareUnsigned",
      "([B[B)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compareUnsigned__intArray__int__int__intArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jintArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compareUnsigned",
      "([III[III)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compareUnsigned__intArray__intArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    const jni_zero::JavaRef<jintArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compareUnsigned",
      "([I[I)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compareUnsigned__longArray__int__int__longArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jlongArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compareUnsigned",
      "([JII[JII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compareUnsigned__longArray__longArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    const jni_zero::JavaRef<jlongArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compareUnsigned",
      "([J[J)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compareUnsigned__shortArray__int__int__shortArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jshortArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compareUnsigned",
      "([SII[SII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_compareUnsigned__shortArray__shortArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0,
    const jni_zero::JavaRef<jshortArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "compareUnsigned",
      "([S[S)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jbooleanArray> Java_Arrays_copyOf__booleanArray__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jbooleanArray>& p0,
    JniIntWrapper p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOf",
      "([ZI)[Z",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1));
  jbooleanArray _ret2 = static_cast<jbooleanArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jbooleanArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jbyteArray> Java_Arrays_copyOf__byteArray__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0,
    JniIntWrapper p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOf",
      "([BI)[B",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1));
  jbyteArray _ret2 = static_cast<jbyteArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jbyteArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jcharArray> Java_Arrays_copyOf__charArray__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0,
    JniIntWrapper p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOf",
      "([CI)[C",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1));
  jcharArray _ret2 = static_cast<jcharArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jcharArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jdoubleArray> Java_Arrays_copyOf__doubleArray__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    JniIntWrapper p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOf",
      "([DI)[D",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1));
  jdoubleArray _ret2 = static_cast<jdoubleArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jdoubleArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jfloatArray> Java_Arrays_copyOf__floatArray__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0,
    JniIntWrapper p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOf",
      "([FI)[F",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1));
  jfloatArray _ret2 = static_cast<jfloatArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jfloatArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jintArray> Java_Arrays_copyOf__intArray__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    JniIntWrapper p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOf",
      "([II)[I",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1));
  jintArray _ret2 = static_cast<jintArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jintArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jlongArray> Java_Arrays_copyOf__longArray__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    JniIntWrapper p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOf",
      "([JI)[J",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1));
  jlongArray _ret2 = static_cast<jlongArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jlongArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jshortArray> Java_Arrays_copyOf__shortArray__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0,
    JniIntWrapper p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOf",
      "([SI)[S",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1));
  jshortArray _ret2 = static_cast<jshortArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jshortArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobjectArray> Java_Arrays_copyOf__ObjectArray__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOf",
      "([Ljava/lang/Object;I)[Ljava/lang/Object;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1));
  jobjectArray _ret2 = static_cast<jobjectArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jobjectArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobjectArray> Java_Arrays_copyOf(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    const jni_zero::JavaRef<jclass>& p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOf",
      "([Ljava/lang/Object;ILjava/lang/Class;)[Ljava/lang/Object;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      p2.obj());
  jobjectArray _ret2 = static_cast<jobjectArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jobjectArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jbooleanArray> Java_Arrays_copyOfRange__booleanArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jbooleanArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOfRange",
      "([ZII)[Z",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  jbooleanArray _ret2 = static_cast<jbooleanArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jbooleanArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jbyteArray> Java_Arrays_copyOfRange__byteArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOfRange",
      "([BII)[B",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  jbyteArray _ret2 = static_cast<jbyteArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jbyteArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jcharArray> Java_Arrays_copyOfRange__charArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOfRange",
      "([CII)[C",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  jcharArray _ret2 = static_cast<jcharArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jcharArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jdoubleArray> Java_Arrays_copyOfRange__doubleArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOfRange",
      "([DII)[D",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  jdoubleArray _ret2 = static_cast<jdoubleArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jdoubleArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jfloatArray> Java_Arrays_copyOfRange__floatArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOfRange",
      "([FII)[F",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  jfloatArray _ret2 = static_cast<jfloatArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jfloatArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jintArray> Java_Arrays_copyOfRange__intArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOfRange",
      "([III)[I",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  jintArray _ret2 = static_cast<jintArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jintArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jlongArray> Java_Arrays_copyOfRange__longArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOfRange",
      "([JII)[J",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  jlongArray _ret2 = static_cast<jlongArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jlongArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jshortArray> Java_Arrays_copyOfRange__shortArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOfRange",
      "([SII)[S",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  jshortArray _ret2 = static_cast<jshortArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jshortArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobjectArray> Java_Arrays_copyOfRange__ObjectArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOfRange",
      "([Ljava/lang/Object;II)[Ljava/lang/Object;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  jobjectArray _ret2 = static_cast<jobjectArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jobjectArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobjectArray> Java_Arrays_copyOfRange(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jclass>& p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOfRange",
      "([Ljava/lang/Object;IILjava/lang/Class;)[Ljava/lang/Object;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj());
  jobjectArray _ret2 = static_cast<jobjectArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jobjectArray>(env, _ret2);
}

[[maybe_unused]] static jboolean Java_Arrays_deepEquals(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    const jni_zero::JavaRef<jobjectArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "deepEquals",
      "([Ljava/lang/Object;[Ljava/lang/Object;)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_deepHashCode(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "deepHashCode",
      "([Ljava/lang/Object;)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Arrays_deepToString(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "deepToString",
      "([Ljava/lang/Object;)Ljava/lang/String;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  jstring _ret2 = static_cast<jstring>(_ret);
  return jni_zero::ScopedJavaLocalRef<jstring>(env, _ret2);
}

[[maybe_unused]] static jboolean Java_Arrays_equals__booleanArray__int__int__booleanArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jbooleanArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jbooleanArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([ZII[ZII)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__booleanArray__booleanArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jbooleanArray>& p0,
    const jni_zero::JavaRef<jbooleanArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([Z[Z)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__byteArray__int__int__byteArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jbyteArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([BII[BII)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__byteArray__byteArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0,
    const jni_zero::JavaRef<jbyteArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([B[B)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__charArray__int__int__charArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jcharArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([CII[CII)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__charArray__charArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0,
    const jni_zero::JavaRef<jcharArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([C[C)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__doubleArray__int__int__doubleArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jdoubleArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([DII[DII)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__doubleArray__doubleArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    const jni_zero::JavaRef<jdoubleArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([D[D)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__floatArray__int__int__floatArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jfloatArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([FII[FII)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__floatArray__floatArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0,
    const jni_zero::JavaRef<jfloatArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([F[F)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__intArray__int__int__intArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jintArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([III[III)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__intArray__intArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    const jni_zero::JavaRef<jintArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([I[I)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__longArray__int__int__longArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jlongArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([JII[JII)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__longArray__longArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    const jni_zero::JavaRef<jlongArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([J[J)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__shortArray__int__int__shortArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jshortArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([SII[SII)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__shortArray__shortArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0,
    const jni_zero::JavaRef<jshortArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([S[S)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__ObjectArray__int__int__ObjectArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jobjectArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([Ljava/lang/Object;II[Ljava/lang/Object;II)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jobjectArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5,
    const jni_zero::JavaRef<jobject>& p6) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([Ljava/lang/Object;II[Ljava/lang/Object;IILjava/util/Comparator;)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5),
      p6.obj());
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals__ObjectArray__ObjectArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    const jni_zero::JavaRef<jobjectArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([Ljava/lang/Object;[Ljava/lang/Object;)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jboolean Java_Arrays_equals(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    const jni_zero::JavaRef<jobjectArray>& p1,
    const jni_zero::JavaRef<jobject>& p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "equals",
      "([Ljava/lang/Object;[Ljava/lang/Object;Ljava/util/Comparator;)Z",
      &cached_method_id);
  auto _ret = env->CallStaticBooleanMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      p1.obj(),
      p2.obj());
  return _ret;
}

[[maybe_unused]] static void Java_Arrays_fill__booleanArray__boolean(
    JNIEnv* env,
    const jni_zero::JavaRef<jbooleanArray>& p0,
    jboolean p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([ZZ)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1);
}

[[maybe_unused]] static void Java_Arrays_fill__booleanArray__int__int__boolean(
    JNIEnv* env,
    const jni_zero::JavaRef<jbooleanArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    jboolean p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([ZIIZ)V",
      &cached_method_id);
  env->CallStaticVoidMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3);
}

[[maybe_unused]] static void Java_Arrays_fill__byteArray__byte(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0,
    jbyte p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([BB)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1);
}

[[maybe_unused]] static void Java_Arrays_fill__byteArray__int__int__byte(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    jbyte p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([BIIB)V",
      &cached_method_id);
  env->CallStaticVoidMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3);
}

[[maybe_unused]] static void Java_Arrays_fill__charArray__char(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0,
    jchar p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([CC)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1);
}

[[maybe_unused]] static void Java_Arrays_fill__charArray__int__int__char(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    jchar p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([CIIC)V",
      &cached_method_id);
  env->CallStaticVoidMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3);
}

[[maybe_unused]] static void Java_Arrays_fill__doubleArray__double(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    jdouble p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([DD)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1);
}

[[maybe_unused]] static void Java_Arrays_fill__doubleArray__int__int__double(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    jdouble p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([DIID)V",
      &cached_method_id);
  env->CallStaticVoidMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3);
}

[[maybe_unused]] static void Java_Arrays_fill__floatArray__float(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0,
    jfloat p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([FF)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1);
}

[[maybe_unused]] static void Java_Arrays_fill__floatArray__int__int__float(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    jfloat p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([FIIF)V",
      &cached_method_id);
  env->CallStaticVoidMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3);
}

[[maybe_unused]] static void Java_Arrays_fill__intArray__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    JniIntWrapper p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([II)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1));
}

[[maybe_unused]] static void Java_Arrays_fill__intArray__int__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    JniIntWrapper p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([IIII)V",
      &cached_method_id);
  env->CallStaticVoidMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      as_jint(p3));
}

[[maybe_unused]] static void Java_Arrays_fill__longArray__int__int__long(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    jlong p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([JIIJ)V",
      &cached_method_id);
  env->CallStaticVoidMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3);
}

[[maybe_unused]] static void Java_Arrays_fill__longArray__long(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    jlong p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([JJ)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1);
}

[[maybe_unused]] static void Java_Arrays_fill__shortArray__int__int__short(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    jshort p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([SIIS)V",
      &cached_method_id);
  env->CallStaticVoidMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3);
}

[[maybe_unused]] static void Java_Arrays_fill__shortArray__short(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0,
    jshort p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([SS)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1);
}

[[maybe_unused]] static void Java_Arrays_fill__ObjectArray__int__int__Object(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jobject>& p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([Ljava/lang/Object;IILjava/lang/Object;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj());
}

[[maybe_unused]] static void Java_Arrays_fill__ObjectArray__Object(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "fill",
      "([Ljava/lang/Object;Ljava/lang/Object;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
}

[[maybe_unused]] static jint Java_Arrays_hashCode__booleanArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jbooleanArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "hashCode",
      "([Z)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_hashCode__byteArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "hashCode",
      "([B)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_hashCode__charArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "hashCode",
      "([C)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_hashCode__doubleArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "hashCode",
      "([D)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_hashCode__floatArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "hashCode",
      "([F)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_hashCode__intArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "hashCode",
      "([I)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_hashCode__longArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "hashCode",
      "([J)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_hashCode__shortArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "hashCode",
      "([S)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_hashCode__ObjectArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "hashCode",
      "([Ljava/lang/Object;)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__booleanArray__int__int__booleanArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jbooleanArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jbooleanArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([ZII[ZII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__booleanArray__booleanArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jbooleanArray>& p0,
    const jni_zero::JavaRef<jbooleanArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([Z[Z)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__byteArray__int__int__byteArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jbyteArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([BII[BII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__byteArray__byteArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0,
    const jni_zero::JavaRef<jbyteArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([B[B)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__charArray__int__int__charArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jcharArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([CII[CII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__charArray__charArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0,
    const jni_zero::JavaRef<jcharArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([C[C)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__doubleArray__int__int__doubleArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jdoubleArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([DII[DII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__doubleArray__doubleArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    const jni_zero::JavaRef<jdoubleArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([D[D)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__floatArray__int__int__floatArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jfloatArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([FII[FII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__floatArray__floatArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0,
    const jni_zero::JavaRef<jfloatArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([F[F)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__intArray__int__int__intArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jintArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([III[III)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__intArray__intArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    const jni_zero::JavaRef<jintArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([I[I)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__longArray__int__int__longArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jlongArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([JII[JII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__longArray__longArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    const jni_zero::JavaRef<jlongArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([J[J)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__shortArray__int__int__shortArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jshortArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([SII[SII)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__shortArray__shortArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0,
    const jni_zero::JavaRef<jshortArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([S[S)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__ObjectArray__int__int__ObjectArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jobjectArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([Ljava/lang/Object;II[Ljava/lang/Object;II)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5));
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jobjectArray>& p3,
    JniIntWrapper p4,
    JniIntWrapper p5,
    const jni_zero::JavaRef<jobject>& p6) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([Ljava/lang/Object;II[Ljava/lang/Object;IILjava/util/Comparator;)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj(),
      as_jint(p4),
      as_jint(p5),
      p6.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch__ObjectArray__ObjectArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    const jni_zero::JavaRef<jobjectArray>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([Ljava/lang/Object;[Ljava/lang/Object;)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return _ret;
}

[[maybe_unused]] static jint Java_Arrays_mismatch(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    const jni_zero::JavaRef<jobjectArray>& p1,
    const jni_zero::JavaRef<jobject>& p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mismatch",
      "([Ljava/lang/Object;[Ljava/lang/Object;Ljava/util/Comparator;)I",
      &cached_method_id);
  auto _ret = env->CallStaticIntMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      p1.obj(),
      p2.obj());
  return _ret;
}

[[maybe_unused]] static void Java_Arrays_parallelPrefix__doubleArray__int__int__java_util_function_DoubleBinaryOperator(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jobject>& p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelPrefix",
      "([DIILjava/util/function/DoubleBinaryOperator;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelPrefix__doubleArray__java_util_function_DoubleBinaryOperator(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelPrefix",
      "([DLjava/util/function/DoubleBinaryOperator;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelPrefix__intArray__int__int__java_util_function_IntBinaryOperator(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jobject>& p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelPrefix",
      "([IIILjava/util/function/IntBinaryOperator;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelPrefix__intArray__java_util_function_IntBinaryOperator(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelPrefix",
      "([ILjava/util/function/IntBinaryOperator;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelPrefix__longArray__int__int__java_util_function_LongBinaryOperator(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jobject>& p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelPrefix",
      "([JIILjava/util/function/LongBinaryOperator;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelPrefix__longArray__java_util_function_LongBinaryOperator(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelPrefix",
      "([JLjava/util/function/LongBinaryOperator;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelPrefix__ObjectArray__int__int__java_util_function_BinaryOperator(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jobject>& p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelPrefix",
      "([Ljava/lang/Object;IILjava/util/function/BinaryOperator;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelPrefix__ObjectArray__java_util_function_BinaryOperator(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelPrefix",
      "([Ljava/lang/Object;Ljava/util/function/BinaryOperator;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelSetAll__doubleArray__java_util_function_IntToDoubleFunction(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSetAll",
      "([DLjava/util/function/IntToDoubleFunction;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelSetAll__intArray__java_util_function_IntUnaryOperator(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSetAll",
      "([ILjava/util/function/IntUnaryOperator;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelSetAll__longArray__java_util_function_IntToLongFunction(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSetAll",
      "([JLjava/util/function/IntToLongFunction;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelSetAll__ObjectArray__java_util_function_IntFunction(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSetAll",
      "([Ljava/lang/Object;Ljava/util/function/IntFunction;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelSort__byteArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([B)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelSort__byteArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([BII)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1), as_jint(p2));
}

[[maybe_unused]] static void Java_Arrays_parallelSort__charArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([C)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelSort__charArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([CII)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1), as_jint(p2));
}

[[maybe_unused]] static void Java_Arrays_parallelSort__doubleArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([D)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelSort__doubleArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([DII)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1), as_jint(p2));
}

[[maybe_unused]] static void Java_Arrays_parallelSort__floatArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([F)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelSort__floatArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([FII)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1), as_jint(p2));
}

[[maybe_unused]] static void Java_Arrays_parallelSort__intArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([I)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelSort__intArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([III)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1), as_jint(p2));
}

[[maybe_unused]] static void Java_Arrays_parallelSort__longArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([J)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelSort__longArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([JII)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1), as_jint(p2));
}

[[maybe_unused]] static void Java_Arrays_parallelSort__shortArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([S)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelSort__shortArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([SII)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1), as_jint(p2));
}

[[maybe_unused]] static void Java_Arrays_parallelSort__ComparableArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([Ljava/lang/Comparable;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelSort__ComparableArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([Ljava/lang/Comparable;II)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1), as_jint(p2));
}

[[maybe_unused]] static void Java_Arrays_parallelSort(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jobject>& p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([Ljava/lang/Object;IILjava/util/Comparator;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj());
}

[[maybe_unused]] static void Java_Arrays_parallelSort(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "parallelSort",
      "([Ljava/lang/Object;Ljava/util/Comparator;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
}

[[maybe_unused]] static void Java_Arrays_setAll__doubleArray__java_util_function_IntToDoubleFunction(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "setAll",
      "([DLjava/util/function/IntToDoubleFunction;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
}

[[maybe_unused]] static void Java_Arrays_setAll__intArray__java_util_function_IntUnaryOperator(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "setAll",
      "([ILjava/util/function/IntUnaryOperator;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
}

[[maybe_unused]] static void Java_Arrays_setAll__longArray__java_util_function_IntToLongFunction(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "setAll",
      "([JLjava/util/function/IntToLongFunction;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
}

[[maybe_unused]] static void Java_Arrays_setAll__ObjectArray__java_util_function_IntFunction(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "setAll",
      "([Ljava/lang/Object;Ljava/util/function/IntFunction;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
}

[[maybe_unused]] static void Java_Arrays_sort__byteArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([B)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_Arrays_sort__byteArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([BII)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1), as_jint(p2));
}

[[maybe_unused]] static void Java_Arrays_sort__charArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([C)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_Arrays_sort__charArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([CII)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1), as_jint(p2));
}

[[maybe_unused]] static void Java_Arrays_sort__doubleArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([D)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_Arrays_sort__doubleArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([DII)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1), as_jint(p2));
}

[[maybe_unused]] static void Java_Arrays_sort__floatArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([F)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_Arrays_sort__floatArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([FII)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1), as_jint(p2));
}

[[maybe_unused]] static void Java_Arrays_sort__intArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([I)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_Arrays_sort__intArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([III)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1), as_jint(p2));
}

[[maybe_unused]] static void Java_Arrays_sort__longArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([J)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_Arrays_sort__longArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([JII)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1), as_jint(p2));
}

[[maybe_unused]] static void Java_Arrays_sort__shortArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([S)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_Arrays_sort__shortArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([SII)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1), as_jint(p2));
}

[[maybe_unused]] static void Java_Arrays_sort__ObjectArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([Ljava/lang/Object;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_Arrays_sort__ObjectArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([Ljava/lang/Object;II)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), as_jint(p1), as_jint(p2));
}

[[maybe_unused]] static void Java_Arrays_sort(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    const jni_zero::JavaRef<jobject>& p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([Ljava/lang/Object;IILjava/util/Comparator;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2),
      p3.obj());
}

[[maybe_unused]] static void Java_Arrays_sort(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "sort",
      "([Ljava/lang/Object;Ljava/util/Comparator;)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_spliterator__ObjectArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "spliterator",
      "([Ljava/lang/Object;)Ljava/util/Spliterator;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_spliterator__ObjectArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "spliterator",
      "([Ljava/lang/Object;II)Ljava/util/Spliterator;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_spliterator__doubleArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "spliterator",
      "([D)Ljava/util/Spliterator$OfDouble;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_spliterator__doubleArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "spliterator",
      "([DII)Ljava/util/Spliterator$OfDouble;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_spliterator__intArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "spliterator",
      "([I)Ljava/util/Spliterator$OfInt;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_spliterator__intArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "spliterator",
      "([III)Ljava/util/Spliterator$OfInt;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_spliterator__longArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "spliterator",
      "([J)Ljava/util/Spliterator$OfLong;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_spliterator__longArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "spliterator",
      "([JII)Ljava/util/Spliterator$OfLong;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_stream__doubleArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "stream",
      "([D)Ljava/util/stream/DoubleStream;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_stream__doubleArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "stream",
      "([DII)Ljava/util/stream/DoubleStream;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_stream__intArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "stream",
      "([I)Ljava/util/stream/IntStream;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_stream__intArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "stream",
      "([III)Ljava/util/stream/IntStream;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_stream__longArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "stream",
      "([J)Ljava/util/stream/LongStream;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_stream__longArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "stream",
      "([JII)Ljava/util/stream/LongStream;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_stream__ObjectArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "stream",
      "([Ljava/lang/Object;)Ljava/util/stream/Stream;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Arrays_stream__ObjectArray__int__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "stream",
      "([Ljava/lang/Object;II)Ljava/util/stream/Stream;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      as_jint(p1),
      as_jint(p2));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Arrays_toString__booleanArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jbooleanArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "toString",
      "([Z)Ljava/lang/String;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  jstring _ret2 = static_cast<jstring>(_ret);
  return jni_zero::ScopedJavaLocalRef<jstring>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Arrays_toString__byteArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "toString",
      "([B)Ljava/lang/String;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  jstring _ret2 = static_cast<jstring>(_ret);
  return jni_zero::ScopedJavaLocalRef<jstring>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Arrays_toString__charArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jcharArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "toString",
      "([C)Ljava/lang/String;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  jstring _ret2 = static_cast<jstring>(_ret);
  return jni_zero::ScopedJavaLocalRef<jstring>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Arrays_toString__doubleArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jdoubleArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "toString",
      "([D)Ljava/lang/String;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  jstring _ret2 = static_cast<jstring>(_ret);
  return jni_zero::ScopedJavaLocalRef<jstring>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Arrays_toString__floatArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jfloatArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "toString",
      "([F)Ljava/lang/String;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  jstring _ret2 = static_cast<jstring>(_ret);
  return jni_zero::ScopedJavaLocalRef<jstring>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Arrays_toString__intArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jintArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "toString",
      "([I)Ljava/lang/String;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  jstring _ret2 = static_cast<jstring>(_ret);
  return jni_zero::ScopedJavaLocalRef<jstring>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Arrays_toString__longArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jlongArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "toString",
      "([J)Ljava/lang/String;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  jstring _ret2 = static_cast<jstring>(_ret);
  return jni_zero::ScopedJavaLocalRef<jstring>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Arrays_toString__shortArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jshortArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "toString",
      "([S)Ljava/lang/String;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  jstring _ret2 = static_cast<jstring>(_ret);
  return jni_zero::ScopedJavaLocalRef<jstring>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Arrays_toString__ObjectArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_Arrays_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "toString",
      "([Ljava/lang/Object;)Ljava/lang/String;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  jstring _ret2 = static_cast<jstring>(_ret);
  return jni_zero::ScopedJavaLocalRef<jstring>(env, _ret2);
}



}  // namespace JNI_Arrays

#endif  // java_util_Arrays_JNI
