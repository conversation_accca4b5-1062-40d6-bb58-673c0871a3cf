# CheckHost Network Checker Plugin для exteraGram

## Описание
Полнофункциональный плагин для проверки сетевых соединений, DNS, доступности сайтов и серверов через API check-host.net. Поддерживает множественные проверки с разных географических точек мира.

## Возможности
- 🏓 **Ping проверки** - проверка доступности хостов
- 🌐 **HTTP проверки** - проверка доступности веб-сайтов
- 🔌 **TCP проверки** - проверка доступности TCP портов
- 🔍 **DNS проверки** - проверка DNS записей
- 📡 **UDP проверки** - проверка доступности UDP портов
- 🌍 **Список узлов** - просмотр доступных точек проверки
- 📋 **Кэширование** - избежание повторных запросов
- ⚡ **Асинхронность** - неблокирующие проверки с прогресс-диалогами

## Команды

### .ping <host>
Выполняет ping проверку указанного хоста
```
.ping google.com
.ping *******
```

### .http <url>
Проверяет доступность веб-сайта по HTTP/HTTPS
```
.http google.com
.http https://example.com
```

### .tcp <host:port>
Проверяет доступность TCP порта
```
.tcp google.com:80
.tcp smtp.gmail.com:587
```

### .dns <domain>
Проверяет DNS записи домена
```
.dns google.com
.dns example.org
```

### .udp <host:port>
Проверяет доступность UDP порта
```
.udp *******:53
.udp cloudflare.com:53
```

### .nodes
Показывает список всех доступных узлов проверки
```
.nodes
```

## Настройки плагина
- **Максимальное количество узлов** - количество точек проверки (1-20)
- **Таймаут запроса** - время ожидания ответа от API
- **Время кэширования** - время хранения результатов в кэше
- **Показывать расширенную информацию** - дополнительные детали
- **Показывать местоположение узлов** - страна и город узлов

## Примеры использования

### Проверка доступности сайта
```
.ping github.com
```
Результат покажет время отклика с разных узлов мира.

### Проверка веб-сервера
```
.http https://api.github.com
```
Покажет HTTP статус коды и время ответа.

### Проверка почтового сервера
```
.tcp smtp.gmail.com:587
```
Проверит доступность SMTP порта Gmail.

### Проверка DNS
```
.dns cloudflare.com
```
Покажет A и AAAA записи домена.

## Технические особенности
- Использует официальный API check-host.net
- Асинхронная обработка запросов без блокировки UI
- Интеллектуальное кэширование результатов
- Красивое форматирование с эмодзи и цветовой индикацией
- Обработка ошибок и таймаутов
- Поддержка множественных узлов проверки

## Автор
@extera_plugin

## Версия
1.0.0