// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/ApplicationContextProvider

#ifndef org_webrtc_ApplicationContextProvider_JNI
#define org_webrtc_ApplicationContextProvider_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_ApplicationContextProvider[];
const char kClassPath_org_webrtc_ApplicationContextProvider[] =
    "org/webrtc/ApplicationContextProvider";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_ApplicationContextProvider_clazz(nullptr);
#ifndef org_webrtc_ApplicationContextProvider_clazz_defined
#define org_webrtc_ApplicationContextProvider_clazz_defined
inline jclass org_webrtc_ApplicationContextProvider_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_ApplicationContextProvider,
      &g_org_webrtc_ApplicationContextProvider_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID>
    g_org_webrtc_ApplicationContextProvider_getApplicationContext0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject>
    Java_ApplicationContextProvider_getApplicationContext(JNIEnv* env) {
  jclass clazz = org_webrtc_ApplicationContextProvider_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_ApplicationContextProvider_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getApplicationContext",
          "()Landroid/content/Context;",
          &g_org_webrtc_ApplicationContextProvider_getApplicationContext0);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_ApplicationContextProvider_JNI
