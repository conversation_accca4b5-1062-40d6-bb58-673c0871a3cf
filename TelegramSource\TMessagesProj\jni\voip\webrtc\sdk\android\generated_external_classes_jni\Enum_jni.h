// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     java/lang/Enum

#ifndef java_lang_Enum_JNI
#define java_lang_Enum_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_java_lang_Enum[];
const char kClassPath_java_lang_Enum[] = "java/lang/Enum";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_java_lang_Enum_clazz(nullptr);
#ifndef java_lang_Enum_clazz_defined
#define java_lang_Enum_clazz_defined
inline jclass java_lang_Enum_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_java_lang_Enum, &g_java_lang_Enum_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace JNI_Enum {


static std::atomic<jmethodID> g_java_lang_Enum_Constructor2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Enum_Constructor(JNIEnv* env,
    const jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Enum_Constructor(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Enum_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Enum_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Ljava/lang/String;I)V",
          &g_java_lang_Enum_Constructor2);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, p0.obj(), as_jint(p1));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Enum_clone0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Enum_clone(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Enum_clone(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Enum_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Enum_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "clone",
          "()Ljava/lang/Object;",
          &g_java_lang_Enum_clone0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Enum_compareTo__Enum1(nullptr);
[[maybe_unused]] static jint Java_Enum_compareTo__Enum(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jint Java_Enum_compareTo__Enum(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_lang_Enum_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Enum_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "compareTo",
          "(Ljava/lang/Enum;)I",
          &g_java_lang_Enum_compareTo__Enum1);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Enum_compareTo__Object1(nullptr);
[[maybe_unused]] static jint Java_Enum_compareTo__Object(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jint Java_Enum_compareTo__Object(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_lang_Enum_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Enum_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "compareTo",
          "(Ljava/lang/Object;)I",
          &g_java_lang_Enum_compareTo__Object1);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Enum_equals1(nullptr);
[[maybe_unused]] static jboolean Java_Enum_equals(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_Enum_equals(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_lang_Enum_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Enum_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "equals",
          "(Ljava/lang/Object;)Z",
          &g_java_lang_Enum_equals1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Enum_finalize0(nullptr);
[[maybe_unused]] static void Java_Enum_finalize(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj);
static void Java_Enum_finalize(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Enum_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Enum_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "finalize",
          "()V",
          &g_java_lang_Enum_finalize0);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID> g_java_lang_Enum_getDeclaringClass0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jclass> Java_Enum_getDeclaringClass(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jclass> Java_Enum_getDeclaringClass(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Enum_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Enum_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getDeclaringClass",
          "()Ljava/lang/Class;",
          &g_java_lang_Enum_getDeclaringClass0);

  jclass ret =
      static_cast<jclass>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jclass>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Enum_hashCode0(nullptr);
[[maybe_unused]] static jint Java_Enum_hashCode(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj);
static jint Java_Enum_hashCode(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Enum_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Enum_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "hashCode",
          "()I",
          &g_java_lang_Enum_hashCode0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Enum_name0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Enum_name(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Enum_name(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Enum_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Enum_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "name",
          "()Ljava/lang/String;",
          &g_java_lang_Enum_name0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Enum_ordinal0(nullptr);
[[maybe_unused]] static jint Java_Enum_ordinal(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj);
static jint Java_Enum_ordinal(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Enum_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Enum_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "ordinal",
          "()I",
          &g_java_lang_Enum_ordinal0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Enum_toString0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Enum_toString(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Enum_toString(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Enum_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Enum_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "toString",
          "()Ljava/lang/String;",
          &g_java_lang_Enum_toString0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Enum_valueOf2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Enum_valueOf(JNIEnv* env, const
    jni_zero::JavaRef<jclass>& p0,
    const jni_zero::JavaRef<jstring>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Enum_valueOf(JNIEnv* env, const
    jni_zero::JavaRef<jclass>& p0,
    const jni_zero::JavaRef<jstring>& p1) {
  jclass clazz = java_lang_Enum_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Enum_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "valueOf",
          "(Ljava/lang/Class;Ljava/lang/String;)Ljava/lang/Enum;",
          &g_java_lang_Enum_valueOf2);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace JNI_Enum

#endif  // java_lang_Enum_JNI
