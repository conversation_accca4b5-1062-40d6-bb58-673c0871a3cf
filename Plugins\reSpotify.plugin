"""



                            ДИСКЛЕЙМЕР

Если при создании своего плагина вы решили использовать готовые кодовые решения 
нашего плагина у себя, то не забудьте упомянуть в описании своего плагина 
канал @MeeowPlugins в качестве кредитов за помощь в разработке плагина. Спасибо 


                  ⣾⡇⣿⣿⡇⣾⣿⣿⣿⣿⣿⣿⣿⣿⣄⢻⣦⡀⠁⢸⡌⠻⣿⣿⣿⡽⣿⣿
                  ⡇⣿⠹⣿⡇⡟⠛⣉⠁⠉⠉⠻⡿⣿⣿⣿⣿⣿⣦⣄⡉⠂⠈⠙⢿⣿⣝⣿
                  ⠤⢿⡄⠹⣧⣷⣸⡇⠄⠄⠲⢰⣌⣾⣿⣿⣿⣿⣿⣿⣶⣤⣤⡀⠄⠈⠻⢮
                  ⠄⢸⣧⠄⢘⢻⣿⡇⢀⣀⠄⣸⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣧⡀⠄⢀
                  ⠄⠈⣿⡆⢸⣿⣿⣿⣬⣭⣴⣿⣿⣿⣿⣿⣿⣿⣯⠝⠛⠛⠙⢿⡿⠃⠄⢸
                  ⠄⠄⢿⣿⡀⣿⣿⣿⣾⣿⣿⣿⣿⣿⣿⣿⣿⣿⣷⣿⣿⣿⣿⡾⠁⢠⡇⢀
                  ⠄⠄⢸⣿⡇⠻⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣏⣫⣻⡟⢀⠄⣿⣷⣾
                  ⠄⠄⢸⣿⡇⠄⠈⠙⠿⣿⣿⣿⣮⣿⣿⣿⣿⣿⣿⣿⣿⡿⢠⠊⢀⡇⣿⣿
                  ⠒⠤⠄⣿⡇⢀⡲⠄⠄⠈⠙⠻⢿⣿⣿⠿⠿⠟⠛⠋⠁⣰⠇⠄⢸⣿⣿⣿



                            DISCLAIMER

If, when creating your plugin, you decided to use the ready-made code solutions
of our plugin, then do not forget to mention the @MeeowPlugins channel in the description
of your plugin as credits for help in developing your plugin. Thanks



"""

import plugins_manager, time, threading, requests, datetime, textwrap, math, re, os, traceback, random
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance

from base_plugin import BasePlugin, HookR<PERSON>ult, HookStrategy
from android_utils import log, run_on_ui_thread
from client_utils import get_send_messages_helper, get_last_fragment, get_account_instance, \
    send_request, get_user_config, get_messages_controller, send_message
from markdown_utils import parse_markdown

from ui.settings import Header, Switch, Divider, Input, Selector, Text
from ui.bulletin import BulletinHelper
from ui.alert import AlertDialogBuilder

from java import jclass
from java.lang import Class, Integer, Object
from java.lang.reflect import Array
from java.io import File
from java.util import Locale
from java.util import ArrayList


from android.content import Intent
from android.net import Uri

from com.exteragram.messenger.plugins import PluginsController
from com.exteragram.messenger.plugins.ui import PluginSettingsActivity
from org.telegram.ui.ActionBar import AlertDialog
from org.telegram.messenger import ApplicationLoader, SendMessagesHelper, AndroidUtilities, R, NotificationCenter
from org.telegram.tgnet.tl import TL_account

__id__ = "respotify_renightly"
__name__ = "reSpotify"
__description__ = "Now play plugin for Spotify.\n\nUse:\n   .now — send now play card\n   .nowt — send now play track\n\nThanks @itsv1eds, @zwylair & @exteradev for the help"
__icon__ = "remusic/2"
__version__ = "1.3.4"
__author__ = "@reNightly, @qmrrchh"
__min_version__ = "11.12.0"

API_URL = "https://co.itsv1eds.ru"
TEMP_DIR_NAME = "respotify_temp"

FAMILY_PLUGIN_ID = "rehuyandex"
FAMILY_ENABLED = False

DEFAULT_STREAM_STRING = "🎵 {title} — {artists}"
DEFAULT_STREAM_TEXT = "Hi, I use exteraGram"
DEFAULT_USERNAME_TEXT = "stats.fm/you"

FONTS = {
    0: "Onest",
    1: "Circular",
    2: "NotoSansJP"
}
DEFAULT_COLOR = {
    "background_color": "#000000",
    "title_text_color": "#FFFFFF",
    "subtext_color": "#A0A0A0"
}
DEFAULT_COMMANDS = {
    "pic": [".now", ". now"],
    "track": [".nowt", ". nowt"],
}
SPECIAL_COMMANDS = {
    "pic": [".snow", ". snow"],
    "track": [".snowt", ". snowt"],
}
INSTANT_SUBTEXT = "Powered by"
INSTANT_MAIN_TEXT = "reSpotify"
zwylib = None


class LanguageController:
    def __init__(self):
        self.lang_code = Locale.getDefault().getLanguage()

    def get_controller(self):
        if self.lang_code == "ru":
            return self.lang_ru()
        elif self.lang_code == "pt":
            return self.lang_ptbr()
        else:
            return self.lang_en()

    class lang_en:
        _lang = 'en'

        Settings_ZwyLibNeed = "To auto-update the plugin, it is recommended to install the auxiliary plugin ZwyLib. You can find it in the @ZwyPlugins channel."
        Settings_reFamily = "A neighboring plugin from the re[] family was detected. A prefix s (.snow/.snowt) was added to reSpotify commands to avoid conflicts between plugins."

        Family_Mode_Enabled = f"[{__name__}] reFamily mode enabled."
        Family_Mode_Disabled = f"[{__name__}] reFamily mode disabled."
        Family_Mode_Changed = f"[{__name__}] reFamily mode changed."
        Family_Mode_About = "About mode"

        Settings_AuthToken_Header = "Authorization"

        Settings_LoginGuide_Text = "Help"
        Settings_LoginGuide_Title = "Authorization in reSpotify"
        Settings_LoginGuide_Go = "stats.fm"

        Settings_LoginGuide = "Go to stats.fm, log in to your account, and paste the public link to your account or its username below."
        Settings_Username_Text = "Link/Username"
        Settings_Username_Subtext = "Paste your username or profile link from stats.fm here."

        Settings_CardSettings_Header = "Customization"

        Settings_BackgroundMode_Text = "Background"
        Settings_BackgroundMode_Item_1 = "Track Cover"
        Settings_BackgroundMode_Item_2 = "Cover Color"

        Settings_Font_Text = "Font"
        Settings_Font_Item1 = "Onest"
        Settings_Font_Item2 = "Spotify"
        Settings_Font_Item3 = "NotoSansJP"

        Settings_SpotifyLink_Text = "Insert link to ..."
        Settings_SpotifyLink_Item_1 = "—"
        Settings_SpotifyLink_Item_2 = "Track"
        Settings_SpotifyLink_Item_3 = "Album"

        Settings_SongLinkInclude_Text = "Link to other platforms"
        Settings_SongLinkInclude_Subtext = "Adds a link to the song page on song.link"
        Settings_FastCardRender_Text = "Pre-render cards"
        Settings_FastCardRender_Subtext = "May increase battery consumption"

        Setting_AdvancedMode_Text = "Advanced Settings"
        Setting_AdvancedMode_Subtext = "Additional customization settings"
        Setting_AdvancedMode_Title = "Customization"

        Settings_BackgroundColor_Text = "Background Color"
        Settings_BackgroundColor_Subtext = "Background color if 'Cover Color' is selected in 'Background'"

        Settings_AccentColor_Text = "Accent Color"
        Settings_AccentColor_Subtext = "Text color used in track title and active progress bar"

        Settings_SecondaryColor_Text = "Secondary Color"
        Settings_SecondaryColor_Subtext = "Text color used for artist line, inactive progress bar, and timers"

        Settings_InstantCardSubtext_Text = "Secondary Text"
        Settings_InstantCardSubtext_Subtext = "Text displayed at the top of the bottom block of pre-rendered cards"

        Settings_InstantCardMainText_Text = "Main Text"
        Settings_InstantCardMainText_Subtext = "Text displayed at the bottom of the bottom block of pre-rendered cards"

        Settings_Stream_Header = "Profile Streaming"

        Setting_Stream_Title = "Stream settings"
        Settings_Stream_Text = "Stream track to profile"
        Settings_Stream_Subtext = "Updates your bio/location with the currently playing track"

        Settings_StreamAlert_Title = "⚠️⚠️WARNING⚠️⚠️"
        Settings_StreamAlert_Text = "This feature may work inconsistently due to Telegram's profile change limits. As a result, your profile information may not update immediately. Use at your own risk."

        Setting_TrackStream_Text = "Stream to..."
        Setting_TrackStream_Item1 = "Bio"
        Setting_TrackStream_Item2 = "Location (Recommended)"

        Settings_InStream_Text = "Default Text"
        Settings_InStream_Subtext = "Text displayed when the player is unavailable or no track is playing"

        Settings_FormatInStream_Text = "Format"
        Settings_FormatInStream_Subtext = "Customize track display. {title} — track name, {artists} — artist(s)"

        Settings_Other_Header = "Other"

        Setting_Other_SourceCheck = "File Integrity Check"
        Setting_Other_ForceDownload = "Download Full Resource Package"
        Setting_Other_Donate = "Support Development"

        Alert_HEX_Title = "HEX Error"
        Alert_HEX_Text = "Invalid HEX color code"

        Alert_SourceError_Title = "Integrity Check Error"
        Alert_SourceError_FontsNotFound = "Font files not found"
        Alert_SourceError_FontNotFound = "Font {0} not found"
        Alert_SourceError_FontApplyError = "Failed to apply font {0}: {1}"

        Alert_SourceSuccess_Title = "Success"
        Alert_SourceSuccess_Text = "No issues detected during resource check"

        Alert_SourceNotFound_Text = "{0} resources not found."
        Alert_SourceNotFound_Button = "Download"

        Alert_UserNotLogined_Text = "Set your Stats.FM username using guide from settings"
        Alert_UserNotLogined_Button = "Authorize"

        Alert_SourceDownload_Title = "Downloading Resources"
        Alert_SourceCheck_Title = "Checking Resources"

        Alert_Donate_Title = "Support Development"
        Alert_Donate_Text = "Below you can copy the TON address of the reSpotify developers and support the development with your donation."
        Alert_Donate_Button = "Copy"

        Alert_Trigger_Title = "⚠️ rePlugins Family Error"
        Alert_Trigger_Text = "Command {0} is ambiguous for reSpotify & reMusic plugins.\n\nTo get information from the service you need, use the commands:\n   .snow/.snowt - for reSpotify \n   .ynow/.ynowt - for reMusic"

        Card_PlayerInactive = "Player not started"
        Card_PlayingIn = "Playing on"

        Message_CaptionLink_Text = "[Spotify]({0})"
        Message_CaptionSongLink_Text = "[song.link]({0})"
        Message_CaptionDivider = " • "
        Message_PlayerNotActive = "Spotify player unavailable or not started"
        Message_Trigger_Text = "⚠️ *rePlugins Family Error* \nCommand *{0}* is ambiguous for reSpotify & reMusic plugins.\n\nTo get information from the service you need, use the commands:\n   .snow/.snowt - for *reSpotify* \n   .ynow/.ynowt - for *reMusic*"

        Bulletin_NowPlay = "Now playing: {0}"

        Bulletin_ErrorMessage = "[reSpotify] Error: {0}"
        Bulletin_ErrorMessageCopy = "[reSpotify] An error occurred"
        Bulletin_FailedProcessImage = "[reSpotify] Image processing error"
        Bulletin_FailedGetYouTubeLink = "[reSpotify] Failed to get YouTube/SoundCloud link"
        Bulletin_ErrorSendingAudio = "[reSpotify] Audio sending error"
        Bulletin_InvalidCobaltResponse = "Invalid response from Cobalt API"
        Bulletin_NoItemsToDownload = "No items to download"
        Bulletin_CobaltErrorCode = "[reSpotify]: Cobalt Error Code — {0}"

    class lang_ru:
        _lang = 'ru'

        Settings_ZwyLibNeed = "Для автообновления плагина рекомендовано установить вспомогательный плагин ZwyLib. Найти его можно в канале @ZwyPlugins"
        Settings_reFamily = "Обнаружен соседний плагин из семейства плагинов re[]. В команды плагина reSpotify добавлен префикс s (.snow/.snowt) для предотвращения конфликтов между плагинами"

        Family_Mode_Enabled = f"[{__name__}] Режим reFamily активирован."
        Family_Mode_Disabled = f"[{__name__}] Режим reFamily выключен."
        Family_Mode_Changed = f"[{__name__}] Режим reFamily изменён."
        Family_Mode_About = "О режиме"

        Settings_AuthToken_Header = "Авторизация"

        Settings_LoginGuide_Text = "Справка"
        Settings_LoginGuide_Title = "Авторизация в reSpotify"
        Settings_LoginGuide_Go = "stats.fm"

        Settings_LoginGuide = "Перейдите на сайт stats.fm, авторизируйтесь в свой аккаунт и вставьте в поле ниже публичную ссылку на свой аккаунт или его юзернейм"
        Settings_Username_Text = "Ссылка/юзернейм"
        Settings_Username_Subtext = "Вставьте сюда юзернейм или ссылку своего профиля stats.fm"

        Settings_CardSettings_Header = "Кастомизация"

        Settings_BackgroundMode_Text = "Фон"
        Settings_BackgroundMode_Item_1 = "Обложка трека"
        Settings_BackgroundMode_Item_2 = "Цвет обложки"

        Settings_Font_Text = "Шрифт"
        Settings_Font_Item1 = "Onest"
        Settings_Font_Item2 = "Spotify"
        Settings_Font_Item3 = "NotoSansJP"

        Settings_SpotifyLink_Text = "Вставлять ссылку на ..."
        Settings_SpotifyLink_Item_1 = "—"
        Settings_SpotifyLink_Item_2 = "Трек"
        Settings_SpotifyLink_Item_3 = "Альбом"

        Settings_SongLinkInclude_Text = "Ссылка на другие площадки"
        Settings_SongLinkInclude_Subtext = "Добавляет ссылку на страницу трека в song.link"
        Settings_FastCardRender_Text = "Pre-render карточки"
        Settings_FastCardRender_Subtext = "Может увеличить расход заряда аккумулятора"

        Setting_AdvancedMode_Text = "Расширенные настройки"
        Setting_AdvancedMode_Subtext = "Дополнительные настройки кастомизации"
        Setting_AdvancedMode_Title = "Кастомизация"

        Settings_BackgroundColor_Text = "Цвет фона"
        Settings_BackgroundColor_Subtext = "Цвет фона, если в параметре \"Фон\" выбрано \"Цвет обложки\""

        Settings_AccentColor_Text = "Акцентный цвет"
        Settings_AccentColor_Subtext = "Цвет шрифта, используемого в названии трека и активном прогрессбаре"

        Settings_SecondaryColor_Text = "Второстепенный цвет"
        Settings_SecondaryColor_Subtext = "Цвет шрифта, используемого в строке артистов, неактивном прогрессбаре и таймерах"

        Settings_InstantCardSubtext_Text = "Второстепенный текст"
        Settings_InstantCardSubtext_Subtext = "Текст, используемый в верхней части нижнего блока pre-render карточки"

        Settings_InstantCardMainText_Text = "Основной текст"
        Settings_InstantCardMainText_Subtext = "Текст, используемый в нижней части нижнего блока pre-render карточки"

        Settings_Stream_Header = "Трансляция в профиль"

        Settings_Stream_Text = "Транслировать трек в профиль"
        Settings_Stream_Subtext = "Изменяет био/геолокацию в вашем профиле, вставляя в него информацию о текущем треке"

        Setting_Stream_Title = "Настройки трансляции"
        Settings_StreamAlert_Title = "⚠️⚠️ВНИМАНИЕ⚠️⚠️"
        Settings_StreamAlert_Text = "Данная функция может работать нестабильно ввиду ограничений Telegram на определенное кол-во изменений своего профиля за определённый промежуток времени \
из-за чего информация в вашем профиле может некоторое время не обновляться. Используйте на свое усмотрение"

        Setting_TrackStream_Text = "Транслировать в ..."
        Setting_TrackStream_Item1 = "Био"
        Setting_TrackStream_Item2 = "Геолокацию (Рекомендуется)"

        Settings_InStream_Text = "Текст по умолчанию"
        Settings_InStream_Subtext = "Вставляет текст из этого поля, когда плеер не доступен и/или трек не проигрывается"

        Settings_FormatInStream_Text = "Формат"
        Settings_FormatInStream_Subtext = "Задайте вид отображения трека. {title} — название трека, {artists} — исполнитель(-и)"

        Settings_Other_Header = "Прочее"

        Setting_Other_SourceCheck = "Проверка целостности файлов"
        Setting_Other_ForceDownload = "Скачать полный пакет ресурсов"
        Setting_Other_Donate = "Поддержать разработку"

        Alert_HEX_Title = "Ошибка HEX"
        Alert_HEX_Text = "Неверный HEX-код цвета"

        Alert_SourceError_Title = "Ошибка проверки"
        Alert_SourceError_FontsNotFound = "Файлы шрифтов не найдены"
        Alert_SourceError_FontNotFound = "Шрифт {0} не найден"
        Alert_SourceError_FontApplyError = "Ошибка применения шрифта {0}: {1}"

        Alert_SourceSuccess_Title = "Успешно"
        Alert_SourceSuccess_Text = "Во время проверки ресурсов проблемы не обнаружены"

        Alert_SourceNotFound_Text = "Файл {0} не найден."
        Alert_SourceNotFound_Button = "Скачать"

        Alert_UserNotLogined_Text = "Введите юзернейм stats.fm для начала работы с плагином"
        Alert_UserNotLogined_Button = "Перейти"

        Alert_SourceDownload_Title = "Загрузка ресурсов"
        Alert_SourceCheck_Title = "Проверка ресурсов"

        Alert_Donate_Title = "Поддержать разработку"
        Alert_Donate_Text = "Ниже вы можете скопировать TON-адрес разработчиков плагина reSpotify и поддержать разработку своим донатом"
        Alert_Donate_Button = "Копировать"

        Alert_Trigger_Title = "⚠️ rePlugins Family Error"
        Alert_Trigger_Text = "Команда {0} неоднозначна для плагинов reSpotify & reMusic.\n\nДля получение информации с конкретного сервиса используйте команды:\n   .snow/.snowt - для reSpotify \n   .ynow/.ynowt - для reMusic"

        Card_PlayerInactive = "Плеер не запущен"
        Card_PlayingIn = "Играет на"

        Message_CaptionLink_Text = "[Spotify]({0})"
        Message_CaptionSongLink_Text = "[song.link]({0})"
        Message_CaptionDivider = " • "
        Message_PlayerNotActive = "Плеер Spotify недоступен или не запущен"
        Message_Trigger_Text = "⚠️ *rePlugins Family Error* \nКоманда *{0}* неоднозначна для плагинов reSpotify & reMusic.\n\nДля получения информации из необходимого Вам сервиса, используйте команды:\n   .snow/.snowt - для *reSpotify* \n   .ynow/.ynowt - для *reMusic*"


        Bulletin_NowPlay = "Сейчас играет: {0}"

        Bulletin_ErrorMessage = "[reSpotify] Ошибка: {0}"
        Bulletin_ErrorMessageCopy = "[reSpotify] Возникла ошибка"
        Bulletin_FailedProcessImage = "[reSpotify] Ошибка обработки изображения"
        Bulletin_FailedGetYouTubeLink = "[reSpotify] Ошибка получения ссылки YouTube/SoundCloud"
        Bulletin_ErrorSendingAudio = "[reSpotify] Ошибка отправки аудио"
        Bulletin_InvalidCobaltResponse = "Некорректный ответ от Cobalt API"
        Bulletin_NoItemsToDownload = "Нет элементов для загрузки"
        Bulletin_CobaltErrorCode = "[reSpotify]: Ошибка Cobalt, код — {0}"

    class lang_ptbr:
        _lang = 'pt'

        Settings_ZwyLibNeed = "Para atualizar o plugin automaticamente, é recomendável instalar o plugin auxiliar ZwyLib. Você pode encontrá-lo no canal @ZwyPlugins."
        Settings_reFamily = "Foi detectado um plugin vizinho da família re[]. Um prefixo s (.snow/.snowt) foi adicionado aos comandos do reSpotify para evitar conflitos entre plugins."

        Family_Mode_Enabled = f"[{__name__}] modo familiar ativado."
        Family_Mode_Disabled = f"[{__name__}] modo familiar desativado."
        Family_Mode_Changed = f"[{__name__}] modo familiar alterado."
        Family_Mode_About = "Sobre o modo familiar"

        Settings_AuthToken_Header = "Autorização"

        Settings_LoginGuide_Text = "Ajuda"
        Settings_LoginGuide_Title = "Autorização no reSpotify"
        Settings_LoginGuide_Go = "stats.fm"

        Settings_LoginGuide = "Acesse o site stats.fm, faça login na sua conta e cole o link público do seu perfil ou seu nome de usuário abaixo."
        Settings_Username_Text = "Link/Nome de usuário"
        Settings_Username_Subtext = "Cole seu nome de usuário ou link do perfil do stats.fm aqui."

        Settings_CardSettings_Header = "Personalização"

        Settings_BackgroundMode_Text = "Fundo"
        Settings_BackgroundMode_Item_1 = "Capa da música"
        Settings_BackgroundMode_Item_2 = "Cor da capa"

        Settings_Font_Text = "Fonte"
        Settings_Font_Item1 = "Onest"
        Settings_Font_Item2 = "Spotify"
        Settings_Font_Item3 = "NotoSansJP"

        Settings_SpotifyLink_Text = "Inserir link para ..."
        Settings_SpotifyLink_Item_1 = "—"
        Settings_SpotifyLink_Item_2 = "Música"
        Settings_SpotifyLink_Item_3 = "Álbum"

        Settings_SongLinkInclude_Text = "Link para outras plataformas"
        Settings_SongLinkInclude_Subtext = "Adiciona um link para a página da música no song.link"
        Settings_FastCardRender_Text = "Pré-renderizar cartões"
        Settings_FastCardRender_Subtext = "Pode aumentar o consumo de bateria"

        Setting_AdvancedMode_Text = "Configurações avançadas"
        Setting_AdvancedMode_Subtext = "Configurações adicionais de personalização"
        Setting_AdvancedMode_Title = "Customização avançada"

        Settings_BackgroundColor_Text = "Cor de fundo"
        Settings_BackgroundColor_Subtext = "Cor de fundo se 'Cor da capa' estiver selecionado em 'Fundo'"

        Settings_AccentColor_Text = "Cor de destaque"
        Settings_AccentColor_Subtext = "Cor do texto usada no título da música e na barra de progresso ativa"

        Settings_SecondaryColor_Text = "Cor secundária"
        Settings_SecondaryColor_Subtext = "Cor do texto usada na linha de artistas, barra de progresso inativa e temporizadores"

        Settings_InstantCardSubtext_Text = "Texto secundário"
        Settings_InstantCardSubtext_Subtext = "Texto exibido na parte superior do bloco inferior dos cartões pré-renderizados"

        Settings_InstantCardMainText_Text = "Texto principal"
        Settings_InstantCardMainText_Subtext = "Texto exibido na parte inferior do bloco inferior dos cartões pré-renderizados"

        Settings_Stream_Header = "Transmissão para o perfil"

        Setting_Stream_Title = "Definições de transmissão"
        Settings_Stream_Text = "Transmitir música para o perfil"
        Settings_Stream_Subtext = "Atualiza sua biografia/localização com a música que está tocando"

        Settings_StreamAlert_Title = "⚠️⚠️ATENÇÃO⚠️⚠️"
        Settings_StreamAlert_Text = "Esta funcionalidade pode funcionar de forma inconsistente devido aos limites de alterações de perfil do Telegram. Como resultado, as informações do seu perfil podem não ser atualizadas imediatamente. Use por sua conta e risco."

        Setting_TrackStream_Text = "Transmitir para..."
        Setting_TrackStream_Item1 = "Biografia"
        Setting_TrackStream_Item2 = "Localização (Recomendado)"

        Settings_InStream_Text = "Texto padrão"
        Settings_InStream_Subtext = "Texto exibido quando o player está indisponível ou nenhuma música está tocando"

        Settings_FormatInStream_Text = "Formato"
        Settings_FormatInStream_Subtext = "Personalize a exibição da música. {title} — nome da música, {artists} — artista(s)"

        Settings_Other_Header = "Outros"

        Setting_Other_SourceCheck = "Verificação de integridade dos arquivos"
        Setting_Other_ForceDownload = "Baixar pacote completo de recursos"
        Setting_Other_Donate = "Apoiar o desenvolvimento"

        Alert_HEX_Title = "Erro HEX"
        Alert_HEX_Text = "Código de cor HEX inválido"

        Alert_SourceError_Title = "Erro de verificação"
        Alert_SourceError_FontsNotFound = "Arquivos de fontes não encontrados"
        Alert_SourceError_FontNotFound = "Fonte {0} não encontrada"
        Alert_SourceError_FontApplyError = "Erro ao aplicar fonte {0}: {1}"

        Alert_SourceSuccess_Title = "Sucesso"
        Alert_SourceSuccess_Text = "Nenhum problema detectado durante a verificação dos recursos"

        Alert_SourceDownload_Title = "Baixando recursos"
        Alert_SourceCheck_Title = "Verificando recursos"

        Alert_SourceNotFound_Text = "{0} recursos não encontrados."
        Alert_SourceNotFound_Button = "Download"

        Alert_UserNotLogined_Text = "Defina seu Stats.FM nome de usuário usando o Guia de Configurações"
        Alert_UserNotLogined_Button = "Autorizar"

        Alert_Donate_Title = "Apoiar o desenvolvimento"
        Alert_Donate_Text = "Abaixo você pode copiar o endereço TON dos desenvolvedores do reSpotify e apoiar o desenvolvimento com sua doação."
        Alert_Donate_Button = "Copiar"

        Alert_Trigger_Title = "⚠️ Erro da Família rePlugins"
        Alert_Trigger_Text = "O comando {0} é ambíguo entre os plugins reSpotify e reMusic.\n\nPara obter informações do serviço desejado, use os comandos:\n   .snow/.snowt - para o reSpotify \n   .ynow/.ynowt - para o reMusic"

        Card_PlayerInactive = "Player não iniciado"
        Card_PlayingIn = "Tocando em"

        Message_CaptionLink_Text = "[Spotify]({0})"
        Message_CaptionSongLink_Text = "[song.link]({0})"
        Message_CaptionDivider = " • "
        Message_PlayerNotActive = "Player do Spotify indisponível ou não iniciado"
        Message_Trigger_Text = "⚠️ *Erro da Família rePlugins* \nO comando *{0}* é ambíguo entre os plugins reSpotify e reMusic.\n\nPara obter informações do serviço desejado, use os comandos:\n   .snow/.snowt - para o *reSpotify* \n   .ynow/.ynowt - para o *reMusic*"

        Bulletin_NowPlay = "Tocando agora: {0}"

        Bulletin_ErrorMessage = "[reSpotify] Erro: {0}"
        Bulletin_ErrorMessageCopy = "[reSpotify] Ocorreu um erro"
        Bulletin_FailedProcessImage = "[reSpotify] Erro ao processar imagem"
        Bulletin_FailedGetYouTubeLink = "[reSpotify] Falha ao obter link do YouTube/SoundCloud"
        Bulletin_ErrorSendingAudio = "[reSpotify] Erro ao enviar áudio"
        Bulletin_InvalidCobaltResponse = "Resposta inválida do Cobalt API"
        Bulletin_NoItemsToDownload = "Nenhum item para download"
        Bulletin_CobaltErrorCode = "[reSpotify]: Código de Erro do Cobalt — {0}"


string = LanguageController().get_controller()


def import_zwylib():
    global zwylib

    try:
        import zwylib
    except ImportError:
        pass


def is_zwylib_present() -> bool:
    return zwylib is not None

class Track:
        def __init__(
                self,
                active: bool,
                id: int = None,
                title: str = None,
                artist: list = None,
                album: str = None,
                thumb: str = None,
                duration: int = None,
                progress: int = None,
                link: str = None,
                device: str = None
        ):
            self.active = active
            self.id = id
            self.title = title
            self.artist = artist
            self.album = album
            self.thumb = thumb
            self.duration = duration
            self.progress = progress
            self.link = link
            self.device = device

class Spotify:

    def __init__(self, username):
        username = username.strip() if username else ""
        if '/' in username:
            username = username.split('/')[-1]
        self.username = username
        self.headers = {"User-Agent": "Mozilla/5.0", "Accept": "application/json"}
        self.now_track = Track(active=False)
        self.memory_id = "Default"
        self.polling = False
        threading.Thread(target=self.now, name="SpotifyNowTrackTracker").start()

    def getCurrentlyPlayingTrack(self):
        data = requests.get(f"https://api.stats.fm/api/v1/users/{self.username}/streams/current", headers=self.headers)
        if data.status_code == 204:
            return Track(active=False)
        elif data.status_code != 200:
            return Track(active=False)
        else:
            data = data.json()['item']
            if data:
                track_id = data['track']['externalIds']['spotify']
                track_id = track_id[0] if track_id else 0
                track = Track(
                    active=True,
                    id=track_id,
                    title=data['track']['name'],
                    artist=[artist['name'] for artist in data['track']['artists']],
                    album=self.get_album_link(data['track']['id']),
                    thumb=data['track']['albums'][0]['image'],
                    duration=data['track']['durationMs'] // 1000,
                    progress=data['progressMs'] // 1000,
                    link=f"https://open.spotify.com/track/{track_id}",
                    device=data['deviceName']
                )
            else:
                track = Track(active=False)

            return track

    def get_album_link(self, internal_track_id):
        data = requests.get(f"https://api.stats.fm/api/v1/tracks/{internal_track_id}/albums", headers=self.headers)
        if data.status_code != 200:
            return None
        else:
            data = data.json()['items']
            if data:
                try:
                    album_id = data[0]['externalIds']['spotify'][0]
                    return f"https://open.spotify.com/album/{album_id}"
                except:
                    return None
            else:
                return None

    def now(self, force_request=False):
        if self.username is None or self.username == "" or self.username == DEFAULT_USERNAME_TEXT:
            return
        while self.polling or force_request:
            try:
                data = self.getCurrentlyPlayingTrack()
                self.now_track = data

                if not (data.active):
                    time.sleep(2.5)
            except Exception as e:
                try:
                    self.now_track.active
                except:
                    self.now_track = Track(active=False)
                if "retries exceeded" not in str(e):
                    log(f"[exteraGram reSpotify]: Internal Error {e}")
            if force_request:
                return self.now_track
            time.sleep(2.5)

    def change_polling_state(self, new_polling):
        self.polling = new_polling
        log(f"respotify new polling {new_polling}")
        if new_polling and "SpotifyNowTrackTracker" not in [thread.name for thread in threading.enumerate()]:
            threading.Thread(target=self.now, name="SpotifyNowTrackTracker").start()


def show_with_copy(message, submsg):
    def copy():
        if AndroidUtilities.addToClipboard(str(submsg)):
            BulletinHelper.show_copied_to_clipboard()

    BulletinHelper.show_with_button(message, R.raw.error, "Copy", lambda: copy())


class ReSpotifyPlugin(BasePlugin):
    class NotificationsHook:
        def __init__(self, plugin):
            self.plugin = plugin

        def after_hooked_method(self, param):
            update_id: int = param.args[0]
            if update_id == NotificationCenter.pluginsUpdated:
                plugin_id: str = param.args[1][0]
                log(f"[{__name__}] Hooked 'pluginsUpdated' notification for '{plugin_id}'")

                if plugin_id == FAMILY_PLUGIN_ID:
                    self.plugin.set_family_mode_enabled()
                else:
                    log(f"[{__name__}] Notification 'pluginsUpdated' for '{plugin_id}' ignored")

    def __init__(self):
        super().__init__()
        self.spotify = None
        self.zwy = None
        self.for_metadata = None
        self._temp_dir = None
        self.alert_builder_instance: AlertDialogBuilder = None
        self.spinner: AlertDialogBuilder = None
        self.stream: bool = False

    def on_plugin_load(self):
        global FAMILY_ENABLED
        self.add_on_send_message_hook(priority=42)

        self._temp_dir = self._get_temp_dir()
        if self._temp_dir:
            log("reSpotify plugin loaded successfully")
        else:
            log("Failed to initialize temp directory for reSpotify")

        self.spotify = Spotify(self.get_setting('statsfm__link_username'))

        if "reSpotifyStreamer" not in [thread.name for thread in threading.enumerate()]:
            threading.Thread(target=self._streamer, name="reSpotifyStreamer", daemon=True).start()
        import_zwylib()

        FAMILY_ENABLED = self.search_family_plugin(FAMILY_PLUGIN_ID)
        log(f"[{__name__}] reFamily on_plugin_load: {FAMILY_ENABLED}")
        self.hook_plugin_update()

        self.autocard_render(self.get_setting('fast_card_render', False))
        self.stream = self.get_setting("update_bio", False)

        self.spotify.change_polling_state(
            self.get_setting("fast_card_render", False) or self.get_setting("update_bio", False)
        )

        if is_zwylib_present():
            self.zwy = True
            zwylib.add_autoupdater_task(__id__, 2672610568, "meeowPlugins", 12)
            log("[reSpotify] ZwyLib Loaded successfully")
        else:
            self.zwy = False
            log("[reSpotify] Loaded successfully without zwylib")

    def on_plugin_unload(self):
        self._unactive()
        self.spotify.polling = False
        if is_zwylib_present():
            zwylib.remove_autoupdater_task(__id__)

    def create_settings(self):
        valid_username = self.get_setting("statsfm__link_username", "") not in [None, "", DEFAULT_USERNAME_TEXT]

        settings = [
                Divider(
                    text=string.Settings_ZwyLibNeed
                ) if not (self.zwy) else None,
                Divider(
                    text=string.Settings_reFamily
                ) if FAMILY_ENABLED else None,

                Header(text=string.Settings_AuthToken_Header),
                Input(
                    key="statsfm__link_username",
                    text=string.Settings_Username_Text,
                    default=DEFAULT_USERNAME_TEXT,
                    subtext=string.Settings_Username_Subtext,
                    on_change=lambda new_value: self.update_spotify_object(new_value),
                    icon="menu_username_change"
                ),
                Text(
                    text=string.Settings_LoginGuide_Text,
                    icon="msg_info",
                    on_click=lambda view: self.show_my_info_alert(
                        title=string.Settings_LoginGuide_Title,
                        message=string.Settings_LoginGuide,
                        positive_button="OK",
                        neutral_button=string.Settings_LoginGuide_Go,
                        neutral_link="https://www.stats.fm/",
                        neutral_type="link"
                    )
                )
            ]

        if not valid_username:
            return settings

        advanced = self.get_setting('advanced_mode', False)
        update_bio = self.get_setting("update_bio", False)

        settings += [
            Divider(),

            Header(text=string.Settings_CardSettings_Header),
            Selector(
                key="background",
                text=string.Settings_BackgroundMode_Text,
                default=1,
                items=[
                    string.Settings_BackgroundMode_Item_1,
                    string.Settings_BackgroundMode_Item_2
                ],
                icon="msg_photos"
            ),
            Selector(
                key="font",
                text=string.Settings_Font_Text,
                default=0,
                items=[
                    string.Settings_Font_Item1,
                    string.Settings_Font_Item2,
                    string.Settings_Font_Item3
                ],
                icon="msg_photo_text_regular"
            ),
            Selector(
                key="spotify_link",
                text=string.Settings_SpotifyLink_Text,
                default=1,
                items=[
                    string.Settings_SpotifyLink_Item_1,
                    string.Settings_SpotifyLink_Item_2,
                    string.Settings_SpotifyLink_Item_3
                ],
                icon="msg_link2"
            ),
            Switch(
                key="songlink_link_include",
                text=string.Settings_SongLinkInclude_Text,
                default=True,
                subtext=string.Settings_SongLinkInclude_Subtext,
                on_change=lambda new_value: self.LogCBO(new_value),
                icon="msg_language"
            ),
            Switch(
                key="fast_card_render",
                text=string.Settings_FastCardRender_Text,
                default=False,
                subtext=string.Settings_FastCardRender_Subtext,
                on_change=lambda new_value: self.autocard_render(new_value),
                icon="boosts_solar"
            ),

            Divider(),

            Switch(
                key="advanced_mode",
                text=string.Setting_AdvancedMode_Text,
                default=False,
                subtext=string.Setting_AdvancedMode_Subtext,
                on_change=None,
                icon="msg_palette"
            ),
            Text(
                text=string.Setting_AdvancedMode_Title,
                icon="msg_download_settings",
                create_sub_fragment=self.create_customization_settings
            ) if advanced else None,

            Divider(),

            Header(text=string.Settings_Stream_Header),
            Switch(
                key="update_bio",
                text=string.Settings_Stream_Text,
                default=False,
                subtext=string.Settings_Stream_Subtext,
                on_change=lambda new_value: self._unactive(new_value),
                icon="msg_online"
            ),
            Text(
                text=string.Setting_Stream_Title,
                icon="msg_download_settings",
                create_sub_fragment=self.create_stream_settings
            ) if update_bio else None,

            Header(text=string.Settings_Other_Header),
            Text(
                text=string.Setting_Other_SourceCheck,
                icon="msg_noise_on",
                on_click=lambda view: self.source_check()
            ),
            Text(
                text=string.Setting_Other_ForceDownload,
                icon="msg_download",
                on_click=lambda view: self.force_source_downloader()
            ),
            Text(
                text=string.Setting_Other_Donate,
                icon="msg_ton",
                accent=True,
                on_click=lambda view: self.show_my_info_alert(
                    title=string.Alert_Donate_Title,
                    message=string.Alert_Donate_Text,
                    neutral_button=string.Alert_Donate_Button,
                    neutral_link="UQBVxjueXqAEpALX_b0yr-ytXN26LOTpSBn26b9VRHKrmm5F",
                    neutral_type="copy"
                )
            )
        ]
        return settings

    def create_customization_settings(self):
        instant_card = self.get_setting("fast_card_render", False)
        return [
            Input(
                key="background_color",
                text=string.Settings_BackgroundColor_Text,
                default=DEFAULT_COLOR["background_color"],
                subtext=string.Settings_BackgroundColor_Subtext,
                icon="menu_feature_custombg",
                on_change=lambda new_value: self.HEX_check(new_value, "background_color")
            ) if self.get_setting("background", 1) == 1 else None,
            Input(
                key="title_text_color",
                text=string.Settings_AccentColor_Text,
                default=DEFAULT_COLOR["title_text_color"],
                subtext=string.Settings_AccentColor_Subtext,
                icon="msg_photo_text_framed",
                on_change=lambda new_value: self.HEX_check(new_value, "title_text_color")
            ),
            Input(
                key="subtext_color",
                text=string.Settings_SecondaryColor_Text,
                default=DEFAULT_COLOR["subtext_color"],
                subtext=string.Settings_SecondaryColor_Subtext,
                icon="msg_photo_text_framed2",
                on_change=lambda new_value: self.HEX_check(new_value, "subtext_color")
            ),
            Input(
                key="instant_subtext",
                text=string.Settings_InstantCardSubtext_Text,
                default=INSTANT_SUBTEXT,
                subtext=string.Settings_InstantCardSubtext_Subtext,
                icon="menu_feature_intro"
            ) if instant_card else None,
            Input(
                key="instant_main_text",
                text=string.Settings_InstantCardMainText_Text,
                default=INSTANT_MAIN_TEXT,
                subtext=string.Settings_InstantCardMainText_Subtext,
                icon="menu_feature_cover"
            ) if instant_card else None,
        ]

    def create_stream_settings(self):
        update_bio = self.get_setting("update_bio", False)
        return [
            Selector(
                key="stream_place",
                text=string.Setting_TrackStream_Text,
                default=0,
                items=[
                    string.Setting_TrackStream_Item1,
                    string.Setting_TrackStream_Item2,
                ],
                icon="menu_premium_location" if self.get_setting("stream_place", 0) else "msg_openprofile"
            ) if get_user_config().isPremium() and update_bio else None,
            Input(
                key="default_stream_text",
                text=string.Settings_InStream_Text,
                default=DEFAULT_STREAM_TEXT,
                subtext=string.Settings_InStream_Subtext,
                icon="msg_photo_text_framed3"
            ) if update_bio else None,
            Input(
                key="track_display_format",
                text=string.Settings_FormatInStream_Text,
                default=DEFAULT_STREAM_STRING,
                subtext=string.Settings_FormatInStream_Subtext,
                icon="msg_view_file"
            ) if update_bio else None,
        ]

    def on_send_message_hook(self, account, params):
        commands = SPECIAL_COMMANDS if FAMILY_ENABLED else DEFAULT_COMMANDS

        if hasattr(params, 'message') and isinstance(params.message, str):
            if params.message in commands["pic"]:
                if self.spotify.username is None or self.spotify.username == "" or self.spotify.username == DEFAULT_USERNAME_TEXT:
                    BulletinHelper.show_with_button(
                        string.Alert_UserNotLogined_Text,
                        R.raw.error,
                        string.Alert_UserNotLogined_Button,
                        on_click=lambda: self._open_plugin_settings()
                    )
                    return HookResult(strategy=HookStrategy.CANCEL)

                if not self.source_checker():
                    BulletinHelper.show_with_button(
                        string.Alert_SourceNotFound_Text.format(__name__),
                        R.raw.error,
                        string.Alert_SourceNotFound_Button,
                        on_click=lambda: self.force_source_downloader()
                    )
                    return HookResult(strategy=HookStrategy.CANCEL)

                run_on_ui_thread(lambda: self.show_spinner())
                threading.Thread(target=self.image_processor, args=(params,)).start()
                return HookResult(strategy=HookStrategy.CANCEL)

            if params.message in commands["track"]:
                if self.spotify.username is None or self.spotify.username == "" or self.spotify.username == DEFAULT_USERNAME_TEXT:
                    BulletinHelper.show_with_button(
                        string.Alert_UserNotLogined_Text,
                        R.raw.error,
                        string.Alert_UserNotLogined_Button,
                        on_click=lambda: self._open_plugin_settings()
                    )
                    return HookResult(strategy=HookStrategy.CANCEL)

                if not self.source_checker():
                    BulletinHelper.show_with_button(
                        string.Alert_SourceNotFound_Text.format(__name__),
                        R.raw.error,
                        string.Alert_SourceNotFound_Button,
                        on_click=lambda: self.force_source_downloader()
                    )
                    return HookResult(strategy=HookStrategy.CANCEL)

                run_on_ui_thread(lambda: self.show_spinner())

                try:
                    if not self.spotify.polling:
                        self.spotify.now(force_request=True)

                    if not self.spotify.now_track.active:
                        run_on_ui_thread(lambda: self.dismiss_spinner())
                        params.message = string.Message_PlayerNotActive
                        return HookResult(strategy=HookStrategy.MODIFY, params=params)
                    thread = threading.Thread(target=self._process_download_and_send,
                                              args=(params.peer, params.replyToMsg, params.replyToTopMsg), daemon=True)
                    thread.start()
                    return HookResult(strategy=HookStrategy.CANCEL)
                except Exception as e:
                    run_on_ui_thread(lambda: self.dismiss_spinner())
                    params.message = string.Bulletin_ErrorMessage.format(e)
                    log(f"Internal reSpotify error: {e}")
                    show_with_copy(string.Bulletin_ErrorMessageCopy, e)
                    return

            if params.message in DEFAULT_COMMANDS["pic"] + DEFAULT_COMMANDS["track"] and params.message not in commands["pic"] + commands["track"]:
                self.show_my_info_alert(
                    title=string.Alert_Trigger_Title,
                    message=string.Alert_Trigger_Text.format(params.message)
                )

                return HookResult(strategy=HookStrategy.CANCEL)

        elif hasattr(params, 'caption') and isinstance(params.caption, str):

            if "reSpotify_flag_metadata" in params.caption:

                if self.for_metadata:
                    track = self.for_metadata
                    self.for_metadata = None
                else:
                    track = self.spotify.now_track

                for i in range(params.document.attributes.__str__().split(",").__len__()):
                    if "title" in dir(params.document.attributes.get(i)):
                        params.document.attributes.get(i).title = track.title if track.active else "[reSpotify] ERROR"
                        params.document.attributes.get(i).performer = ", ".join(
                            track.artist) if track.active else string.Message_PlayerNotActive
                        params.document.attributes.get(i).duration = track.duration if track.active else 0
                markdown = "reSpotify_flag_markdown" in params.caption
                if "reSpotify_flag_metadata" in params.caption:
                    params.caption = params.caption.replace(" reSpotify_flag_metadata", "")
                if markdown:
                    params.caption = params.caption.replace(" reSpotify_flag_markdown", "")
                    caption = parse_markdown(params.caption)
                    params.caption = caption.text
                    params.entities = ArrayList()
                    for i in caption.entities:
                        params.entities.add(i.to_tlrpc_object())
                return HookResult(HookStrategy.MODIFY, params=params)
            return HookResult()

    def hook_plugin_update(self):
        try:
            clazz = jclass("org.telegram.messenger.NotificationCenter").getClass()

            if clazz is None:
                log(f"[{__name__}] Clazz not found")
                return

            post_notification_name = clazz.getDeclaredMethod("postNotificationName", Integer.TYPE, Array.newInstance(Object, 0).getClass())
            post_notification_name.setAccessible(True)
            self.hook_method(post_notification_name, self.NotificationsHook(self))

            log(f"[{__name__}] Hook method {clazz} found")
        except Exception as e:
            log(f"[{__name__}] Hook method error: {e}")

    def copy(self, value):
        if AndroidUtilities.addToClipboard(value):
            BulletinHelper.show_copied_to_clipboard()

    def show_spinner(self):
        fragment = get_last_fragment()
        ctx = fragment.getParentActivity() if fragment else ApplicationLoader.applicationContext
        if ctx is None: return

        self.spinner = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_SPINNER)
        self.spinner.create()
        self.spinner.show()

    def dismiss_spinner(self):
        if self.spinner and self.spinner.get_dialog() and self.spinner.get_dialog().isShowing():
            self.spinner.dismiss()

        self.spinner = None

    def show_my_info_alert(self, title="TITLE", message="MESSAGE", positive_button="OK", neutral_button=None,
                           neutral_link=None, neutral_type=None):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        builder = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
        builder.set_title(title)
        builder.set_message(message)
        builder.set_positive_button(positive_button, self._dismiss_dialog(self.alert_builder_instance))
        if neutral_button:
            builder.set_neutral_button(neutral_button, lambda builder, which: self._open_link(
                neutral_link) if neutral_type == "link" else self.copy(neutral_link))
        self.alert_builder_instance = builder.show()

    def show_my_loading_alert(self, title="Дайте денег пожалуйста"):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        builder = AlertDialog(ctx, AlertDialogBuilder.ALERT_TYPE_LOADING)
        builder.setTitle(title)
        builder.setCancelable(False)
        builder.setCanceledOnTouchOutside(False)
        self.alert_builder_instance = builder
        self.alert_builder_instance.show()
        self.alert_builder_instance.setProgress(0)

    def _update_dialog_progress(self, builder_instance: AlertDialogBuilder, progress: int):
        if builder_instance:
            builder_instance.setProgress(progress)

    def _dismiss_dialog(self, builder_instance: AlertDialogBuilder):
        def action():
            if builder_instance is not None:
                try:
                    dlg = builder_instance.getDialog() if hasattr(builder_instance, 'getDialog') else builder_instance
                    if dlg and dlg.isShowing():
                        dlg.dismiss()
                except Exception:
                    pass
                finally:
                    self.alert_builder_instance = None

        run_on_ui_thread(action)

    def _open_link(self, url):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        run_on_ui_thread(lambda: ctx.startActivity(intent))

    def _open_plugin_settings(self):
        try:
            java_plugin = PluginsController.getInstance().plugins.get(self.id)
            if java_plugin:
                run_on_ui_thread(lambda: get_last_fragment().presentFragment(PluginSettingsActivity(java_plugin)))
        except Exception as e:
            log(f"[exteraGram reSpotify] Error opening plugin settings: {e}")

    def LogCBO(self, new):
        log(f"[reSpotify] Test CBO log; CBO - [{new}]")

    def HEX_check(self, new, variable):
        if not re.findall("^#[A-Fa-f0-9]{6}$", new):
            run_on_ui_thread(lambda: self.show_my_info_alert(
                title=string.Alert_HEX_Title,
                message=string.Alert_HEX_Text
            ))
            self.set_setting(variable, DEFAULT_COLOR[variable])

    def update_spotify_object(self, nickname):
        self.spotify = Spotify(nickname)

    def source_check(self):
        self.show_my_loading_alert(
            title=string.Alert_SourceCheck_Title
        )
        plugin_dir = [file.getName() for file in self._temp_dir.listFiles()]
        if len(plugin_dir) == 0:
            self.show_my_info_alert(title=string.Alert_SourceError_Title,
                                    message=string.Alert_SourceError_FontsNotFound)
            return
        for font in FONTS.values():
            for font_type in ["Regular", "Bold"]:
                nigger = 100 // (len(FONTS.values()) * 2)
                if f"{font}-{font_type}.ttf" not in plugin_dir:
                    self._dismiss_dialog(self.alert_builder_instance)
                    self.show_my_info_alert(title=string.Alert_SourceError_Title,
                                            message=string.Alert_SourceError_FontNotFound.format(
                                                f"{font}-{font_type}.ttf"))
                    return
                try:
                    test_img = Image.new("RGBA", (100, 100), (0, 0, 0, 0))
                    img_font = ImageFont.truetype(File(self._temp_dir, f"{font}-{font_type}.ttf").getAbsolutePath(), 42)
                    draw = ImageDraw.Draw(test_img)
                    draw.text((0, 0), "reSpotify", font=img_font, fill=(255, 255, 255))
                    test_img = None
                except Exception as e:
                    self._dismiss_dialog(self.alert_builder_instance)
                    self.show_my_info_alert(title=string.Alert_SourceError_Title,
                                            message=string.Alert_SourceError_FontApplyError.format(
                                                f"{font}-{font_type}.ttf", e))
                    return
                niggers_counter = nigger + (nigger * (list(FONTS.values()).index(font))) * 2 + (
                    nigger if font_type == "Bold" else 0)
                run_on_ui_thread(lambda: self._update_dialog_progress(self.alert_builder_instance, niggers_counter))
        self.show_my_info_alert(title=string.Alert_SourceSuccess_Title, message=string.Alert_SourceSuccess_Text)

    def source_checker(self) -> bool:
        plugin_dir = [file.getName() for file in self._temp_dir.listFiles()]
        if len(plugin_dir) == 0:
            return False
        for font in FONTS.values():
            for font_type in ["Regular", "Bold"]:
                if f"{font}-{font_type}.ttf" not in plugin_dir:
                    return False
                try:
                    test_img = Image.new("RGBA", (100, 100), (0, 0, 0, 0))
                    img_font = ImageFont.truetype(File(self._temp_dir, f"{font}-{font_type}.ttf").getAbsolutePath(), 42)
                    draw = ImageDraw.Draw(test_img)
                    draw.text((0, 0), "reSpotify", font=img_font, fill=(255, 255, 255))
                except Exception as e:
                    log(f"[{__name__}] {str(e)}")
                    return False
        return True

    def force_source_downloader(self):
        threading.Thread(target=self._source_downloader, args=(self._temp_dir, True,)).start()

    def _unactive(self, value=None):
        setting_value = False
        if value:
            run_on_ui_thread(lambda: self.show_my_info_alert(
                title=string.Settings_StreamAlert_Title,
                message=string.Settings_StreamAlert_Text,
            ))
            setting_value = value
            self.stream = value
            self.spotify.change_polling_state(value)
            if "reSpotifyStreamer" not in [thread.name for thread in threading.enumerate()]:
                threading.Thread(target=self._streamer, name="reSpotifyStreamer", daemon=True).start()
        elif value is None:
            setting_value = self.get_setting("update_bio", False)
        else:
            self.spotify.change_polling_state(self.get_setting("fast_card_render", False))
        if setting_value:
            self.set_default_stream_text()

    def set_default_stream_text(self):
        if not self.stream: return

        user_full = get_messages_controller().getUserFull(get_user_config().getClientUserId())
        stream_place = self.get_setting("stream_place", 0)
        default_bio = self.get_setting("default_stream_text", DEFAULT_STREAM_TEXT)

        if default_bio == DEFAULT_STREAM_TEXT:
            user_bio = user_full.about
            if user_bio:
                default_bio = user_bio
                self.set_setting("default_stream_text", default_bio)

        try:
            if stream_place == 0:
                max_len = 140 if get_user_config().isPremium() else 70
                if user_full.about != default_bio[:max_len]:
                    req = TL_account.updateProfile()
                    req.flags = 4
                    req.about = default_bio[:max_len]

                    send_request(req, ())
            elif stream_place == 1:
                if user_full.business_location is not None and user_full.business_location.address is not None:
                    if user_full.business_location.address != default_bio[:96]:
                        req = TL_account.updateBusinessLocation()
                        req.address = default_bio[:96]
                        req.flags = 1

                        send_request(req, ())
        except Exception as e:
            log(f"[{__id__}] set_default_stream_text: {str(e)}")

    def autocard_render(self, value):
        if value:
            threading.Thread(target=self._autocard_render).start()
        self.spotify.change_polling_state(value or self.get_setting("update_bio", False))

    def _autocard_render(self):
        while True:
            if self.spotify.memory_id != self.spotify.now_track.id:
                self.spotify.memory_id = self.spotify.now_track.id
                self._make_card()
            time.sleep(1)

    def _streamer(self):
        while self.stream:
            log(f"respotify translation {self.spotify.now_track.title=}")
            try:
                if "now_track" not in dir(self.spotify):
                    time.sleep(1)

                user_full = get_messages_controller().getUserFull(get_user_config().getClientUserId())
                if not user_full: continue

                track = self.spotify.now_track
                stream_place = self.get_setting("stream_place", 0)
                max_len = 140 if get_user_config().isPremium() else 70

                if track.active:
                    new_about_text = self.get_setting("track_display_format", DEFAULT_STREAM_STRING)
                    new_about_text = new_about_text.replace("{title}", track.title)
                    new_about_text = new_about_text.replace("{artists}", ", ".join(track.artist))
                    if stream_place == 0:
                        if user_full.about != new_about_text[:max_len]:
                            try:
                                req = TL_account.updateProfile()
                                req.flags = 4
                                req.about = new_about_text[:max_len]
                                send_request(req, ())
                            except:
                                pass
                    elif stream_place == 1:
                        if user_full.business_location is not None and user_full.business_location.address is not None:
                            if user_full.business_location.address != new_about_text[:96]:
                                try:
                                    req = TL_account.updateBusinessLocation()
                                    req.address = new_about_text[:96]
                                    req.flags = 1
                                    send_request(req, ())
                                except:
                                    pass
                    else:
                        pass
                else:
                    default_bio = self.get_setting("default_stream_text", DEFAULT_STREAM_TEXT)

                    if stream_place == 0:
                        if user_full.about != default_bio[:max_len]:
                            try:
                                req = TL_account.updateProfile()
                                req.flags = 4
                                req.about = default_bio[:max_len]
                                send_request(req, ())
                            except:
                                pass
                    elif stream_place == 1:
                        if user_full.business_location.address != default_bio[:96]:
                            try:
                                req = TL_account.updateBusinessLocation()
                                req.address = default_bio[:96]
                                req.flags = 1
                                send_request(req, ())
                            except:
                                pass

                time.sleep(5 if stream_place else 30)
            except Exception as e:
                log(f"reSpotify Bio Error: {e}")
                time.sleep(10)

    def _source_downloader(self, temp_dir, force_load=False):

        try:
            plugin_dir = [file.getName() for file in temp_dir.listFiles()]
            not_need_download = any([font.endswith(".ttf") for font in plugin_dir])
            if not_need_download and not force_load:
                return
            log("[reSpotify] Downloading fonts...")
            run_on_ui_thread(lambda: self.show_my_loading_alert(
                string.Alert_SourceDownload_Title
            ))
            nigger = 100 // (len(FONTS.values()) * 2)

            for font in FONTS.values():
                for font_type in ["Regular", "Bold"]:
                    if f"{font}-{font_type}.ttf" not in plugin_dir or force_load:
                        with open(File(temp_dir, f"{font}-{font_type}.ttf").getAbsolutePath(), "wb") as f:
                            f.write(requests.get(
                                f"https://github.com/itsNightly/font_link/raw/refs/heads/main/{font}-{font_type}.ttf")
                                    .content)

                        niggers_counter = nigger + (nigger * (list(FONTS.values()).index(font))) * 2 + (
                            nigger if font_type == "Bold" else 0)
                        run_on_ui_thread(
                            lambda: self._update_dialog_progress(self.alert_builder_instance, niggers_counter))

            run_on_ui_thread(lambda: self._dismiss_dialog(self.alert_builder_instance))

        except Exception as e:

            run_on_ui_thread(lambda: self._dismiss_dialog(self.alert_builder_instance))
            show_with_copy(string.Bulletin_ErrorMessageCopy, e)
            log(f"Error downloading font: {e}")

    def _get_temp_dir(self):
        base_dir = ApplicationLoader.getFilesDirFixed()
        temp_dir = File(base_dir, "reFamily")
        try:
            base_dir = ApplicationLoader.getFilesDirFixed()
            if not base_dir:
                return None
            temp_dir = File(base_dir, "reFamily")
            if not temp_dir.exists() and not temp_dir.mkdirs():
                return None
            threading.Thread(target=self._source_downloader, args=(temp_dir,)).start()
            return temp_dir
        except Exception as e:
            show_with_copy(string.Bulletin_ErrorMessageCopy, e)
            log(f"Error getting/creating temp directory: {e}")
            return None

    def _make_card(self):
        track = self.spotify.now_track
        font_family = self.get_setting("font", 0)
        font_family = FONTS[font_family]
        width, height = 1440, 600

        advanced_mode = self.get_setting("advanced_mode", False)
        if advanced_mode == False:
            background_color = DEFAULT_COLOR["background_color"]
            title_text_color = DEFAULT_COLOR["title_text_color"]
            subtext_color = DEFAULT_COLOR["subtext_color"]
        else:
            background_color = self.get_setting("background_color", DEFAULT_COLOR["background_color"])
            title_text_color = self.get_setting("title_text_color", DEFAULT_COLOR["title_text_color"])
            subtext_color = self.get_setting("subtext_color", DEFAULT_COLOR["subtext_color"])

        if not (track.active):
            if string._lang == 'ru' and font_family == "Circular":
                font_family = "Onest"
            card = Image.new('RGB', (width, height), background_color)
            draw = ImageDraw.Draw(card)
            reSpotifyFont = ImageFont.truetype(File(self._temp_dir, f"{font_family}-Regular.ttf").getAbsolutePath(), 40)
            notActiveFont = ImageFont.truetype(File(self._temp_dir, f"{font_family}-Bold.ttf").getAbsolutePath(), 80)
            draw.text((width // 2, 45), "reSpotify", font=reSpotifyFont, fill=title_text_color, align="center",
                      anchor="mm")
            draw.text((width // 2, height // 2), string.Card_PlayerInactive, font=notActiveFont, fill=title_text_color,
                      align="center", anchor="mm")
            filename = f"now_reSpotify.png"
            temp_photo_path = File(self._temp_dir, filename).getAbsolutePath()
            card.save(temp_photo_path)
            return temp_photo_path
        background_setting = self.get_setting("background", 1)
        thumb = requests.get(track.thumb, stream=True).raw
        background = Image.open(thumb)
        thumbnail = background.copy()
        if background_setting == 0:
            background = background.resize((width, width)).crop((0, (width - height) // 2, width, width)).filter(
                ImageFilter.GaussianBlur(radius=14))
            background = ImageEnhance.Brightness(background).enhance(0.3)
            card = Image.new('RGB', (width, height), background_color)
            card.paste(background, (0, 0))
        elif advanced_mode == False:
            img = background.resize((16, 16), Image.LANCZOS)
            pixels = img.load()
            lWidth, lHeight = img.size
            for y in range(lHeight):
                for x in range(lWidth):
                    if img.mode == 'L':
                        r = pixels[x, y]
                        r = math.pow(r / 255.0, 1 / 2.2) * 255.0
                        pixels[x, y] = int(r)
                    else:
                        r, g, b = pixels[x, y][:3]
                        r = math.pow(r / 255.0, 1 / 2.2) * 255.0
                        g = math.pow(g / 255.0, 1 / 2.2) * 255.0
                        b = math.pow(b / 255.0, 1 / 2.2) * 255.0
                        if img.mode == 'RGB':
                            pixels[x, y] = (int(r), int(g), int(b))
                        elif img.mode == 'RGBA':
                            a = pixels[x, y][3]
                            pixels[x, y] = (int(r), int(g), int(b), a)
            if img.mode == 'L':
                img = img.convert('RGB')
            elif img.mode == 'RGBA':
                rgb_img = Image.new('RGB', img.size)
                rgb_img.paste(img, mask=img.split()[3])
                img = rgb_img

            pixels = list(img.getdata())
            iWidth, iHeight = img.size

            if img.mode == 'RGB':
                total_r, total_g, total_b = 0, 0, 0
                darkness_index = 1.9
                for r, g, b in pixels:
                    total_r += int(r // darkness_index)
                    total_g += int(g // darkness_index)
                    total_b += int(b // darkness_index)
                count = iWidth * iHeight
                average = (total_r // count, total_g // count, total_b // count)
            else:
                total = sum(pixels)
                average = (total // (iWidth * iHeight),) * 3
            card = Image.new('RGB', (width, height), average)
        else:
            card = Image.new('RGB', (width, height), background_color)

        thumbnail = thumbnail.resize((450, 450))
        mask = Image.new('L', thumbnail.size, 0)
        draw = ImageDraw.Draw(mask)
        draw.rounded_rectangle((0, 0, *thumbnail.size), 30, fill=255)
        thumbnail = thumbnail.copy()
        thumbnail.putalpha(mask)
        card.paste(thumbnail, (75, 75), thumbnail)
        draw = ImageDraw.Draw(card)
        local_font_family = None
        if re.findall(r"[А-Яа-яЁё]", track.title) and font_family == "Circular":
            local_font_family = "Onest"
        titleFont = ImageFont.truetype(File(self._temp_dir,
                                            f"{local_font_family if local_font_family else font_family}-Bold.ttf").getAbsolutePath(),
                                       60)
        x, y = 590, 85
        artistsPlusY = 0
        lines = textwrap.wrap(track.title, width=21)
        if len(lines) > 1:
            lines[1] = lines[1] + "..." if len(lines) > 2 else lines[1]
            artistsPlusY = 70
        else:
            pass
        lines = lines[:2]
        for line in lines:
            draw.text((x, y), line, font=titleFont, fill=title_text_color)
            y += 70
        local_font_family = None
        if re.findall(r"[А-Яа-яЁё]", "".join(track.title)) and font_family == "Circular":
            local_font_family = "Onest"
        artistFont = ImageFont.truetype(File(self._temp_dir,
                                             f"{local_font_family if local_font_family else font_family}-Regular.ttf").getAbsolutePath(),
                                        40)
        artists = textwrap.wrap(" • ".join(track.artist), width=32)
        if len(artists) > 1:
            if "•" in artists[0][-2:]:
                artists[0] = artists[0][:artists[0].rfind("•") - 1]
            artists[0] = artists[0]
        artists = artists[0]
        draw.text((590, 170 + artistsPlusY), artists, subtext_color, font=artistFont)
        if not (self.get_setting("fast_card_render", False)):
            progressBarEmpty = Image.new('RGBA', (width - 665, 10), (0, 0, 0, 0))
            progressDraw = ImageDraw.Draw(progressBarEmpty)
            progressDraw.rounded_rectangle((0, 0, *progressBarEmpty.size), 7, fill=subtext_color)
            progressDraw.rounded_rectangle((0, 0, progressBarEmpty.width * (track.progress / track.duration), 10), 7,
                                           fill=title_text_color)
            card.paste(progressBarEmpty, (590, 460), progressBarEmpty)
            timersFont = ImageFont.truetype(File(self._temp_dir, f"{font_family}-Regular.ttf").getAbsolutePath(), 30)
            draw.text((590, 490), f"{datetime.datetime.fromtimestamp(track.progress).strftime('%M:%S')}", subtext_color,
                      font=timersFont, anchor="la")
            draw.text((1365, 490), f"{datetime.datetime.fromtimestamp(track.duration).strftime('%M:%S')}",
                      subtext_color, font=timersFont, anchor="ra")
        else:
            local_font_family = None

            if advanced_mode:
                subtext = self.get_setting("instant_subtext", string.Card_PlayingIn)
                maintext = self.get_setting("instant_main_text", track.device)
            else:
                subtext = string.Card_PlayingIn
                maintext = track.device

            subtext = subtext[:26] + "..." if len(subtext) > 26 else subtext
            maintext = maintext[:21] + "..." if len(maintext) > 21 else maintext

            ru_flag_subtext = True if re.findall(r"[А-Яа-яЁё]", subtext) else False
            ru_flag_maintext = True if re.findall(r"[А-Яа-яЁё]", maintext) else False

            if ru_flag_subtext and font_family == "Circular":
                local_font_family = "Onest"

            infoFont = ImageFont.truetype(File(self._temp_dir,
                                               f"{local_font_family if local_font_family else font_family}-Regular.ttf").getAbsolutePath(),
                                          42)
            local_font_family = None

            if ru_flag_maintext and font_family == "Circular":
                local_font_family = "Onest"

            deviceFont = ImageFont.truetype(File(self._temp_dir,
                                                 f"{local_font_family if local_font_family else font_family}-Bold.ttf").getAbsolutePath(),
                                            52)
            draw.text((590, 415), subtext, subtext_color, font=infoFont, anchor="ls")
            draw.text((590, 485), maintext, title_text_color, font=deviceFont, anchor="ls")

        filename = f"now_reSpotify.png"
        temp_photo_path = File(self._temp_dir, filename).getAbsolutePath()
        card.save(temp_photo_path)
        return temp_photo_path

    def _download_track(self, url, track):
        payload = {
            "url": url,
            "downloadMode": "audio",
            "audioBitrate": "320",
            "audioFormat": "best"
        }
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        try:
            response = requests.post(f"{API_URL}/", json=payload, headers=headers, timeout=30)
            response.raise_for_status()
        except Exception as e:
            run_on_ui_thread(lambda: self.dismiss_spinner())
            BulletinHelper.show_error(string.Bulletin_InvalidCobaltResponse)
            log(f"exteraGram reSpotify Cobalt Error {e} | data = {payload}")
            return None
        data = response.json()
        status = data.get("status")
        if status == "error":
            code = data.get("error", {}).get("code", "Unknown error")
            BulletinHelper.show_error(string.Bulletin_CobaltErrorCode.format(code))
            return None
        if status == "picker":
            items = data.get("picker", [])
            if not items:
                BulletinHelper.show_error(string.Bulletin_NoItemsToDownload)
                return None
            item = items[0]
            direct_url = item.get("url")
            filename = item.get("filename")
        else:
            direct_url = data.get("url")
            filename = data.get("filename")

        if not direct_url:
            BulletinHelper.show_error(string.Bulletin_InvalidCobaltResponse)
            return None

        filename = f"{track.title} (via rеSpotify).{filename.split('.')[-1] if filename else 'mp4a'}"
        file_path = File(self._temp_dir, filename).getAbsolutePath()

        log(f"[reSpotify] Downloading audio in background thread: {filename}")

        try:
            track_resp = requests.get(direct_url, stream=True, timeout=60)
            track_resp.raise_for_status()
        except Exception as e:
            run_on_ui_thread(lambda: self.dismiss_spinner())
            BulletinHelper.show_error(string.Bulletin_InvalidCobaltResponse)
            return None

        with open(file_path, "wb") as f:
            for chunk in track_resp.iter_content(chunk_size=8192):
                f.write(chunk)

        try:
            size = os.path.getsize(file_path)
            log(f"[reSpotify] Downloaded audio: {filename}, size: {size}")
        except Exception as dbg_e:
            log(f"[reSpotify] Download error: {dbg_e}")

        return file_path

    def send_audio(self, audio_path: str, dialog_id: int, replyTo: int, replyToTop: str, caption: str = None,
                   markdown: bool = False,
                   notify: bool = True, schedule_date: int = 0):
        try:
            account_instance = get_account_instance()
            if account_instance is None:
                log("[reSpotify] Error: Could not get AccountInstance.")
                return
            import mimetypes
            mime, _ = mimetypes.guess_type(audio_path)
            if mime is None:
                ext = os.path.splitext(audio_path)[1].lower()
                if ext == ".mp3":
                    mime = "audio/mpeg"
                elif ext == ".wav":
                    mime = "audio/wav"
                elif ext in [".ogg", ".opus", ".m4a"]:
                    mime = "audio/ogg"
                else:
                    mime = "application/octet-stream"
            ext_cache_root = ApplicationLoader.applicationContext.getExternalCacheDir()
            plugin_ext_dir = File(ext_cache_root, TEMP_DIR_NAME)
            if not plugin_ext_dir.exists() and not plugin_ext_dir.mkdirs():
                log("[reSpotify] Failed to create external temp dir")
            external_path = File(plugin_ext_dir, File(audio_path).getName()).getAbsolutePath()
            with open(audio_path, 'rb') as f_in, open(external_path, 'wb') as f_out:
                while True:
                    chunk = f_in.read(8192)
                    if not chunk:
                        break
                    f_out.write(chunk)
            audio_path = external_path
            SendMessagesHelper.prepareSendingDocument(
                account_instance, audio_path, audio_path, None,
                f"{caption} reSpotify_flag_metadata{' reSpotify_flag_markdown' if markdown else ''}", mime,
                dialog_id, replyTo, replyToTop, None, None, None,
                notify, schedule_date, None, None, 0, False
            )
        except Exception as e:
            log(f"[reSpotify] Error preparing audio for sending: {e}, type: {type(e)}")
            log(traceback.format_exc())
            show_with_copy(string.Bulletin_ErrorSendingAudio, e)

    def _delete_file_delayed(self, path, delay=60):
        def action():
            try:
                time.sleep(delay)
                if os.path.exists(path):
                    os.remove(path)
                    log(f"Deleted temp file: {path}")
            except Exception as e:
                log(f"Delayed delete error: {e}")

        threading.Thread(target=action, daemon=True).start()

    def _process_download_and_send(self, dialog_id, replyTo, replyToTop):
        track = self.spotify.now_track
        self.for_metadata = track
        url = self.get_download_link(track.id)
        if not url:
            run_on_ui_thread(lambda: self.dismiss_spinner())
            BulletinHelper.show_error(string.Bulletin_FailedGetYouTubeLink)
            log(f"Internal reSpotify error")
            return
        path = self._download_track(url, track)
        if path and os.path.exists(path):
            markdown_need = False
            spotify_link = self.get_setting("spotify_link", 1)
            songlink = self.get_setting("songlink_link_include", True)
            caption = None
            if any([spotify_link != 0, songlink]):
                markdown_need = True
                caption = random.choice(["[🎵](5188621441926438751) | ", "[🎶](5188705588925702510) | "])
                if all([spotify_link != 0, songlink]):
                    link = track.link if (spotify_link == 1 or track == None) else track.album
                    caption += string.Message_CaptionLink_Text.format(
                        link) + string.Message_CaptionDivider + string.Message_CaptionSongLink_Text.format(
                        f"https://song.link/s/{track.id}")
                else:
                    if spotify_link != 0:
                        link = track.link if (spotify_link == 1 or track == None) else track.album
                        caption += string.Message_CaptionLink_Text.format(link)
                    elif songlink != 0:
                        caption += string.Message_CaptionSongLink_Text.format(f"https://song.link/s/{track.id}")
            ext = os.path.splitext(path)[1].lower()
            audio_exts = [".mp3", ".wav", ".ogg", ".opus", ".m4a"]
            if ext in audio_exts:
                self.send_audio(path, dialog_id, replyTo, replyToTop, caption if caption else "", markdown_need)
                self._delete_file_delayed(path)
                run_on_ui_thread(lambda: self.dismiss_spinner())
        run_on_ui_thread(lambda: self.dismiss_spinner())

    def get_download_link(self, track_id):
        songlink = requests.get(f"https://song.link/s/{track_id}")
        if songlink.status_code == 200:
            youtube_link = re.findall(r"(https://(www\.)?youtube.com/.+?=.+?\")", songlink.text)
            soundcloud_link = re.findall(r"(https://(www\.)?soundcloud\.com/.+?\")", songlink.text)
            if soundcloud_link or youtube_link:
                return soundcloud_link[0][0][:-1] if soundcloud_link else youtube_link[0][0][:-1]
            else:
                return None
        else:
            return None

    def image_processor(self, msg_params):
        params = {
            "message": None,
            "peer": msg_params.peer
        }
        if not self.spotify.polling: track = self.spotify.now(force_request=True)
        else: track = self.spotify.now_track
        try:
            answer = self._make_card() if not (self.get_setting("fast_card_render", False)) else True
            if answer:
                temp_file_path = File(self._temp_dir, "now_reSpotify.png").getAbsolutePath()

            send_helper = get_send_messages_helper()
            generated_photo = send_helper.generatePhotoSizes(temp_file_path, None)

            if not generated_photo:
                run_on_ui_thread(lambda: self.dismiss_spinner())
                BulletinHelper.show_error(string.Bulletin_FailedProcessImage)
                return HookResult(strategy=HookStrategy.CANCEL)

            params["photo"] = generated_photo
            params["path"] = temp_file_path
            params["replyToMsg"] = msg_params.replyToMsg
            params["replyToTopMsg"] = msg_params.replyToTopMsg
            if track.active:
                spotify_link = self.get_setting("spotify_link", 1)
                songlink = self.get_setting("songlink_link_include", True)
                if any([spotify_link != 0, songlink]):
                    caption = random.choice(["[🎵](5188621441926438751) | ", "[🎶](5188705588925702510) | "])
                    if all([spotify_link != 0, songlink]):
                        link = track.link if (spotify_link == 1 or track is None) else track.album
                        caption += string.Message_CaptionLink_Text.format(
                            link) + string.Message_CaptionDivider + string.Message_CaptionSongLink_Text.format(
                            f"https://song.link/s/{track.id}")
                    else:
                        if spotify_link:
                            link = track.link if (spotify_link == 1 or track is None) else track.album
                            caption += string.Message_CaptionLink_Text.format(link)
                        elif songlink != 0:
                            caption += string.Message_CaptionSongLink_Text.format(f"https://song.link/s/{track.id}")
                    caption = parse_markdown(caption)
                    params["caption"] = caption.text
                    params["entities"] = set()
                    for i in caption.entities:
                        params["entities"].add(i.to_tlrpc_object())
            else:
                params["caption"] = string.Message_PlayerNotActive

        except Exception as e:
            params["message"] = string.Bulletin_ErrorMessage.format(e)
            log(f"Internal reSpotify error: {e}")
            show_with_copy(string.Bulletin_ErrorMessageCopy, e)

        run_on_ui_thread(lambda: self.dismiss_spinner())
        send_message(params)

    def search_family_plugin(self, prompt: str) -> bool:
        prompt = prompt.lower()
        plugins = [p for p in plugins_manager.PluginsManager._plugins.values()]
        plugins_names = [p.name.lower() for p in plugins_manager.PluginsManager._plugins.values()]
        plugins_ids = [p.id.lower() for p in plugins_manager.PluginsManager._plugins.values()]

        if prompt in plugins_names:
            plugin = [p for p in plugins if prompt == p.name.lower() and p.enabled]
            if len(plugin) == 1 and plugin[0]:
                return True
            else:
                return False

        elif prompt in plugins_ids:
            plugin = [p for p in plugins if prompt == p.id.lower() and p.enabled]
            if len(plugin) == 1 and plugin[0]:
                return True
            else:
                return False

        else:
            return False

    def set_family_mode_enabled(self):
        global FAMILY_ENABLED

        old_value: bool = FAMILY_ENABLED
        new_value: bool = self.search_family_plugin(FAMILY_PLUGIN_ID)
        FAMILY_ENABLED = new_value

        log(f"[{__name__}] old_value: {old_value}, new_value: {new_value}")

        if old_value is True and new_value is False:
            self.show_family_bulletin(False)
        elif old_value is False and new_value is True:
            self.show_family_bulletin(True)
        else:
            self.show_family_bulletin(False, True)

    def show_family_bulletin(self, enabled: bool, changed: bool = False):
        if changed:
            BulletinHelper.show_info(string.Family_Mode_Changed)
            return

        BulletinHelper.show_with_button(
            string.Family_Mode_Enabled if enabled else string.Family_Mode_Disabled,
            R.raw.info,
            string.Family_Mode_About,
            on_click=lambda: self.show_my_info_alert(
                    title=string.Alert_Trigger_Title,
                    message=string.Alert_Trigger_Text.format(".now")
            )
        )

"""



                            ДИСКЛЕЙМЕР

Если при создании своего плагина вы решили использовать готовые кодовые решения 
нашего плагина у себя, то не забудьте упомянуть в описании своего плагина 
канал @MeeowPlugins в качестве кредитов за помощь в разработке плагина. Спасибо 

                        
                  ⣾⡇⣿⣿⡇⣾⣿⣿⣿⣿⣿⣿⣿⣿⣄⢻⣦⡀⠁⢸⡌⠻⣿⣿⣿⡽⣿⣿ 
                  ⡇⣿⠹⣿⡇⡟⠛⣉⠁⠉⠉⠻⡿⣿⣿⣿⣿⣿⣦⣄⡉⠂⠈⠙⢿⣿⣝⣿ 
                  ⠤⢿⡄⠹⣧⣷⣸⡇⠄⠄⠲⢰⣌⣾⣿⣿⣿⣿⣿⣿⣶⣤⣤⡀⠄⠈⠻⢮ 
                  ⠄⢸⣧⠄⢘⢻⣿⡇⢀⣀⠄⣸⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣧⡀⠄⢀ 
                  ⠄⠈⣿⡆⢸⣿⣿⣿⣬⣭⣴⣿⣿⣿⣿⣿⣿⣿⣯⠝⠛⠛⠙⢿⡿⠃⠄⢸ 
                  ⠄⠄⢿⣿⡀⣿⣿⣿⣾⣿⣿⣿⣿⣿⣿⣿⣿⣿⣷⣿⣿⣿⣿⡾⠁⢠⡇⢀ 
                  ⠄⠄⢸⣿⡇⠻⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣿⣏⣫⣻⡟⢀⠄⣿⣷⣾ 
                  ⠄⠄⢸⣿⡇⠄⠈⠙⠿⣿⣿⣿⣮⣿⣿⣿⣿⣿⣿⣿⣿⡿⢠⠊⢀⡇⣿⣿ 
                  ⠒⠤⠄⣿⡇⢀⡲⠄⠄⠈⠙⠻⢿⣿⣿⠿⠿⠟⠛⠋⠁⣰⠇⠄⢸⣿⣿⣿ 


                  
                            DISCLAIMER

If, when creating your plugin, you decided to use the ready-made code solutions 
of our plugin, then do not forget to mention the @MeeowPlugins channel in the description 
of your plugin as credits for help in developing your plugin. Thanks



"""
