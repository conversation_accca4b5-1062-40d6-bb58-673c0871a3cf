import os
import time
import json
import requests
import threading
import traceback
import ast
from typing import Any, Dict, Op<PERSON>, Tu<PERSON>, List

from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from client_utils import (
    get_file_loader, run_on_queue, send_message, get_last_fragment, 
    get_messages_controller
)
from markdown_utils import parse_markdown
from ui.settings import Header, Input, Divider, Switch, Selector, Text
from ui.bulletin import BulletinHelper
from ui.alert import Alert<PERSON>ialogBuilder
from android_utils import run_on_ui_thread, log

from java.util import Locale
from org.telegram.tgnet import TLRPC
from org.telegram.messenger import MessageObject, FileLoader, AndroidUtilities

__id__ = "gemini_plugin_security"
__name__ = "Gemini Plugin Security"
__description__ = "Checks Extera plugin code for security risks using Google Gemini API."
__author__ = "@mihailkotovski & @mishabotov"
__version__ = "3.0.0"
__min_version__ = "11.9.1"
__icon__ = "DateRegBot_by_MoiStikiBot/6"

AUTOUPDATE_CHANNEL_ID = 2349438816
AUTOUPDATE_CHANNEL_USERNAME = "mishabotov"
AUTOUPDATE_MESSAGE_ID = 96

zwylib: Optional[Any] = None

GEMINI_BASE_URL = "https://generativelanguage.googleapis.com/v1beta/models/"
MODEL_DISPLAY_NAMES = [
    "Gemini 2.5 Pro",
    "Gemini 2.5 Flash",
    "Gemini 2.5 Flash Lite"
]
MODEL_API_NAMES = [
    "gemini-2.5-pro",
    "gemini-2.5-flash",
    "gemini-2.5-flash-lite-preview-06-17"
]
DEFAULT_COMMAND = ".gpa"

PREMIUM_EMOJI_MAP = {
    "❌": "[❌](5210952531676504517)",
    "📛": "[❗️](5834951734358707402)",
    "⚠️": "[⚠️](5447644880824181073)",
    "❔": "[❔](5467461928647399673)",
    "✅": "[✅](5219899949281453881)",
    "🛡️": "[ℹ️](5404366668635865453)",
    "◈": "[❌](5258453452631056344)",
    "☶": "[📝](5256230583717079814)",
    "❏": "[✍️](5337063551854999885)",
    "•": "[🟦](5404724576850573443)"
}

def replace_with_premium_emoji(text: str) -> str:
    result = text
    for regular_emoji, premium_emoji in PREMIUM_EMOJI_MAP.items():
        result = result.replace(regular_emoji, premium_emoji)
    return result

def get_regular_emoji_for_bulletin(text: str) -> str:
    result = text
    for regular_emoji, premium_emoji in PREMIUM_EMOJI_MAP.items():
        result = result.replace(premium_emoji, regular_emoji)
    return result

DEFAULT_PROMPT_MARKDOWN = (
    "Ты — ведущий аналитик по кибербезопасности, специализирующийся именно на аудите плагинов для фреймворка ExteraGram. Твоя задача — провести строгий технический аудит кода плагина '{plugin_name}' (v{plugin_version}), опираясь на знание его API, и вынести точный вердикт по 5-уровневой шкале риска. Обрати внимание: код может быть предварительно обработан для анонимизации (переименованы переменные и строки), анализируй только логику.\n\n"
    "--- Контекст ExteraGram API (Это считается БЕЗОПАСНЫМ) ---\n"
    "Любое использование следующих официальных API ExteraGram является стандартной и безопасной практикой, а не угрозой:\n\n"
    "• БАЗОВЫЙ ПЛАГИН API (base_plugin):\n"
    "  - BasePlugin класс и его методы (on_plugin_load, on_plugin_unload, create_settings)\n"
    "  - HookResult, HookStrategy для перехвата и модификации вызовов\n"
    "  - Хуки: add_hook, add_on_send_message_hook, pre_request_hook, post_request_hook\n"
    "  - Настройки: get_setting, set_setting для сохранения конфигурации\n"
    "  - Логирование: self.log() для отладочных сообщений\n\n"
    "• КЛИЕНТСКИЕ УТИЛИТЫ (client_utils):\n"
    "  - Отправка сообщений: send_message с параметрами peer, message, entities, replyToMsg\n"
    "  - API запросы: send_request с TLRPC объектами и RequestCallback\n"
    "  - Фоновые задачи: run_on_queue с очередями PLUGINS_QUEUE, GLOBAL_QUEUE, EXTERNAL_NETWORK_QUEUE\n"
    "  - Контроллеры: get_messages_controller, get_user_config, get_file_loader, get_last_fragment\n"
    "  - Получение данных: get_account_instance, get_connections_manager\n\n"
    "• ANDROID УТИЛИТЫ (android_utils):\n"
    "  - UI поток: run_on_ui_thread для обновления интерфейса\n"
    "  - Буфер обмена: AndroidUtilities.addToClipboard\n"
    "  - Логирование: log() функция для отладки\n\n"
    "• ПОЛЬЗОВАТЕЛЬСКИЙ ИНТЕРФЕЙС:\n"
    "  - Настройки (ui.settings): Header, Input, Switch, Selector, Text, Divider\n"
    "  - Диалоги (ui.alert): AlertDialogBuilder с типами ALERT_TYPE_MESSAGE, ALERT_TYPE_SPINNER\n"
    "  - Уведомления (ui.bulletin): BulletinHelper.show_info, show_error, show_success\n\n"
    "• TELEGRAM API (TLRPC):\n"
    "  - Создание запросов: TLRPC.TL_messages_*, TLRPC.TL_users_*, TLRPC.TL_channels_*\n"
    "  - Объекты сообщений: MessageObject, TLRPC.Message\n"
    "  - Пользователи и чаты: TLRPC.User, TLRPC.Chat, TLRPC.UserFull\n"
    "  - Медиа: TLRPC.Document, TLRPC.Photo, FileLoader для загрузки файлов\n\n"
    "• РАЗМЕТКА И ФОРМАТИРОВАНИЕ:\n"
    "  - markdown_utils: parse_markdown для обработки разметки\n"
    "  - Entities: TLRPC.MessageEntity для форматирования текста\n\n"
    "• БЕЗОПАСНЫЕ СЕТЕВЫЕ ЗАПРОСЫ:\n"
    "  - requests.get/post к известным API (wttr.in, check-host.net, GitHub, Google APIs)\n"
    "  - Таймауты и обработка ошибок с try/except\n"
    "  - User-Agent заголовки с идентификацией плагина\n\n"
    "• РАБОТА С ФАЙЛАМИ:\n"
    "  - Чтение/запись в подпапки плагина внутри кеша приложения\n"
    "  - get_file_loader().getPathToAttach() для доступа к вложениям\n"
    "  - os.path операции в безопасных директориях\n\n"
    "• JAVA ИНТЕГРАЦИЯ:\n"
    "  - java.util классы: ArrayList, Locale\n"
    "  - dynamic_proxy для создания Java интерфейсов\n"
    "  - Импорты из org.telegram.* пакетов\n"
    "--- Конец контекста ---\n\n"
    "Принципы анализа и шкала рисков (приоритет от высшего к низшему):\n\n"
    "1. ❌ Опасно - Явный вредоносный код:\n"
    "   • Отправка критичных данных на сторонние серверы: содержимое сообщений, пароли, токены сессий, ключи API\n"
    "   • Исполнение произвольного кода: eval(), exec(), os.system(), subprocess с пользовательским вводом\n"
    "   • Деструктивные файловые операции: удаление, шифрование файлов пользователя вне папки плагина\n"
    "   • Обход безопасности: попытки получить root права, отключить защиту\n"
    "   • Кража данных: скрытая отправка личной информации без согласия пользователя\n\n"
    "2. 📛 Высокий риск - Серьезная угроза приватности:\n"
    "   • Сбор личных данных: ID пользователя, номер телефона, имя, список чатов/контактов\n"
    "   • Отправка метаданных на сторонние серверы без явной необходимости\n"
    "   • Доступ к чувствительным API: контакты, SMS, геолокация, камера, микрофон\n"
    "   • Сохранение личных данных в незащищенном виде\n"
    "   • Передача данных по незащищенным каналам (HTTP вместо HTTPS)\n\n"
    "3. ⚠️ Осторожно - Подозрительные действия:\n"
    "   • Сетевые запросы к неизвестным доменам без HTTPS\n"
    "   • Запись файлов в корень общих папок вместо подпапки плагина\n"
    "   • Использование устаревших или небезопасных библиотек\n"
    "   • Отсутствие проверки SSL сертификатов\n"
    "   • Чрезмерные разрешения без обоснования\n"
    "   • Обфускация кода без технической необходимости\n\n"
    "4. ❔ Низкий риск - Незначительные недочеты:\n"
    "   • Доступ к буферу обмена (это нормально для функций копирования)\n"
    "   • Использование устаревших, но не уязвимых библиотек\n"
    "   • Отсутствие обработки ошибок (может привести к сбоям, но не к утечкам)\n"
    "   • Неоптимальная архитектура кода\n"
    "   • Отсутствие валидации пользовательского ввода (если не критично)\n\n"
    "5. ✅ Безопасно - Соответствует стандартам:\n"
    "   • Использует только официальные API ExteraGram из контекста выше\n"
    "   • Стандартные библиотеки Python для обработки данных\n"
    "   • Правильная обработка ошибок и исключений\n"
    "   • HTTPS запросы к известным и надежным API\n"
    "   • Корректная работа с файлами в разрешенных директориях\n"
    "   • Прозрачная логика без скрытых действий\n\n"
    "ВАЖНЫЕ ПРИМЕРЫ БЕЗОПАСНЫХ ПАТТЕРНОВ:\n"
    "• requests.get('https://wttr.in/Moscow?format=j1', timeout=10) - погодный API\n"
    "• send_message({{'peer': peer_id, 'message': text}}) - отправка сообщения\n"
    "• run_on_queue(lambda: background_task()) - фоновая задача\n"
    "• BulletinHelper.show_info('Готово!') - уведомление пользователю\n"
    "• self.get_setting('api_key', '') - чтение настроек\n"
    "• AlertDialogBuilder(context, ALERT_TYPE_MESSAGE) - диалоговое окно\n\n"
    "Формат ответа (используй Markdown):\n"
    "◈ Вердикт: [Эмодзи] [Безопасно / Низкий риск / Осторожно / Высокий риск / Опасно]\n\n"
    "☶ Назначение: [ОДНО ПРЕДЛОЖЕНИЕ, описывающее функцию плагина]\n\n"
    "❏ Анализ:\n"
    "• [Краткий вывод. Если рисков нет, напиши: Анализ не выявил действий, угрожающих безопасности. Плагин использует стандартные API ExteraGram.]\n"
    "• [Краткое описание КАЖДОГО технического риска, если он есть. Укажи его уровень и почему это риск.]\n\n"
    "Код для анализа:\n"
    "```python\n{plugin_code}\n```"
)
DEFAULT_PROMPT_PLAINTEXT = (
    "Ты — ведущий аналитик по кибербезопасности, специализирующийся именно на аудите плагинов для фреймворка ExteraGram. Твоя задача — провести строгий технический аудит кода плагина '{plugin_name}' (v{plugin_version}), опираясь на знание его API, и вынести точный вердикт по 5-уровневой шкале риска. НЕ ИСПОЛЬЗУЙ MARKDOWN. Обрати внимание: код может быть предварительно обработан для анонимизации (переименованы переменные и строки), анализируй только логику.\n\n"
    "--- Контекст ExteraGram API (Это считается БЕЗОПАСНЫМ) ---\n"
    "Любое использование следующих официальных API ExteraGram является стандартной и безопасной практикой, а не угрозой:\n\n"
    "• БАЗОВЫЙ ПЛАГИН API (base_plugin):\n"
    "  - BasePlugin класс и его методы (on_plugin_load, on_plugin_unload, create_settings)\n"
    "  - HookResult, HookStrategy для перехвата и модификации вызовов\n"
    "  - Хуки: add_hook, add_on_send_message_hook, pre_request_hook, post_request_hook\n"
    "  - Настройки: get_setting, set_setting для сохранения конфигурации\n"
    "  - Логирование: self.log() для отладочных сообщений\n\n"
    "• КЛИЕНТСКИЕ УТИЛИТЫ (client_utils):\n"
    "  - Отправка сообщений: send_message с параметрами peer, message, entities, replyToMsg\n"
    "  - API запросы: send_request с TLRPC объектами и RequestCallback\n"
    "  - Фоновые задачи: run_on_queue с очередями PLUGINS_QUEUE, GLOBAL_QUEUE, EXTERNAL_NETWORK_QUEUE\n"
    "  - Контроллеры: get_messages_controller, get_user_config, get_file_loader, get_last_fragment\n"
    "  - Получение данных: get_account_instance, get_connections_manager\n\n"
    "• ANDROID УТИЛИТЫ (android_utils):\n"
    "  - UI поток: run_on_ui_thread для обновления интерфейса\n"
    "  - Буфер обмена: AndroidUtilities.addToClipboard\n"
    "  - Логирование: log() функция для отладки\n\n"
    "• ПОЛЬЗОВАТЕЛЬСКИЙ ИНТЕРФЕЙС:\n"
    "  - Настройки (ui.settings): Header, Input, Switch, Selector, Text, Divider\n"
    "  - Диалоги (ui.alert): AlertDialogBuilder с типами ALERT_TYPE_MESSAGE, ALERT_TYPE_SPINNER\n"
    "  - Уведомления (ui.bulletin): BulletinHelper.show_info, show_error, show_success\n\n"
    "• TELEGRAM API (TLRPC):\n"
    "  - Создание запросов: TLRPC.TL_messages_*, TLRPC.TL_users_*, TLRPC.TL_channels_*\n"
    "  - Объекты сообщений: MessageObject, TLRPC.Message\n"
    "  - Пользователи и чаты: TLRPC.User, TLRPC.Chat, TLRPC.UserFull\n"
    "  - Медиа: TLRPC.Document, TLRPC.Photo, FileLoader для загрузки файлов\n\n"
    "• РАЗМЕТКА И ФОРМАТИРОВАНИЕ:\n"
    "  - markdown_utils: parse_markdown для обработки разметки\n"
    "  - Entities: TLRPC.MessageEntity для форматирования текста\n\n"
    "• БЕЗОПАСНЫЕ СЕТЕВЫЕ ЗАПРОСЫ:\n"
    "  - requests.get/post к известным API (wttr.in, check-host.net, GitHub, Google APIs)\n"
    "  - Таймауты и обработка ошибок с try/except\n"
    "  - User-Agent заголовки с идентификацией плагина\n\n"
    "• РАБОТА С ФАЙЛАМИ:\n"
    "  - Чтение/запись в подпапки плагина внутри кеша приложения\n"
    "  - get_file_loader().getPathToAttach() для доступа к вложениям\n"
    "  - os.path операции в безопасных директориях\n\n"
    "• JAVA ИНТЕГРАЦИЯ:\n"
    "  - java.util классы: ArrayList, Locale\n"
    "  - dynamic_proxy для создания Java интерфейсов\n"
    "  - Импорты из org.telegram.* пакетов\n"
    "--- Конец контекста ---\n\n"
    "Принципы анализа и шкала рисков (приоритет от высшего к низшему):\n\n"
    "1. ❌ Опасно - Явный вредоносный код:\n"
    "   • Отправка критичных данных на сторонние серверы: содержимое сообщений, пароли, токены сессий, ключи API\n"
    "   • Исполнение произвольного кода: eval(), exec(), os.system(), subprocess с пользовательским вводом\n"
    "   • Деструктивные файловые операции: удаление, шифрование файлов пользователя вне папки плагина\n"
    "   • Обход безопасности: попытки получить root права, отключить защиту\n"
    "   • Кража данных: скрытая отправка личной информации без согласия пользователя\n\n"
    "2. 📛 Высокий риск - Серьезная угроза приватности:\n"
    "   • Сбор личных данных: ID пользователя, номер телефона, имя, список чатов/контактов\n"
    "   • Отправка метаданных на сторонние серверы без явной необходимости\n"
    "   • Доступ к чувствительным API: контакты, SMS, геолокация, камера, микрофон\n"
    "   • Сохранение личных данных в незащищенном виде\n"
    "   • Передача данных по незащищенным каналам (HTTP вместо HTTPS)\n\n"
    "3. ⚠️ Осторожно - Подозрительные действия:\n"
    "   • Сетевые запросы к неизвестным доменам без HTTPS\n"
    "   • Запись файлов в корень общих папок вместо подпапки плагина\n"
    "   • Использование устаревших или небезопасных библиотек\n"
    "   • Отсутствие проверки SSL сертификатов\n"
    "   • Чрезмерные разрешения без обоснования\n"
    "   • Обфускация кода без технической необходимости\n\n"
    "4. ❔ Низкий риск - Незначительные недочеты:\n"
    "   • Доступ к буферу обмена (это нормально для функций копирования)\n"
    "   • Использование устаревших, но не уязвимых библиотек\n"
    "   • Отсутствие обработки ошибок (может привести к сбоям, но не к утечкам)\n"
    "   • Неоптимальная архитектура кода\n"
    "   • Отсутствие валидации пользовательского ввода (если не критично)\n\n"
    "5. ✅ Безопасно - Соответствует стандартам:\n"
    "   • Использует только официальные API ExteraGram из контекста выше\n"
    "   • Стандартные библиотеки Python для обработки данных\n"
    "   • Правильная обработка ошибок и исключений\n"
    "   • HTTPS запросы к известным и надежным API\n"
    "   • Корректная работа с файлами в разрешенных директориях\n"
    "   • Прозрачная логика без скрытых действий\n\n"
    "ВАЖНЫЕ ПРИМЕРЫ БЕЗОПАСНЫХ ПАТТЕРНОВ:\n"
    "• requests.get('https://wttr.in/Moscow?format=j1', timeout=10) - погодный API\n"
    "• send_message({{'peer': peer_id, 'message': text}}) - отправка сообщения\n"
    "• run_on_queue(lambda: background_task()) - фоновая задача\n"
    "• BulletinHelper.show_info('Готово!') - уведомление пользователю\n"
    "• self.get_setting('api_key', '') - чтение настроек\n"
    "• AlertDialogBuilder(context, ALERT_TYPE_MESSAGE) - диалоговое окно\n\n"
    "Формат ответа (ТОЛЬКО ОБЫЧНЫЙ ТЕКСТ):\n"
    "◈ Вердикт: [Эмодзи] [Безопасно / Низкий риск / Осторожно / Высокий риск / Опасно]\n"
    "──────────────\n"
    "☶ Назначение: [ОДНО ПРЕДЛОЖЕНИЕ, описывающее функцию плагина]\n"
    "──────────────\n"
    "❏ Анализ:\n"
    "• [Краткий вывод. Если рисков нет, напиши: Анализ не выявил действий, угрожающих безопасности. Плагин использует стандартные API ExteraGram.]\n"
    "• [Краткое описание КАЖДОГО технического риска, если он есть. Укажи его уровень и почему это риск.]\n\n"
    "Код для анализа:\n"
    "python\n{plugin_code}\n"
)

def import_zwylib(show_bulletin: bool = True):
    global zwylib
    try:
        import zwylib
    except ImportError:
        if show_bulletin:
            run_on_ui_thread(lambda: BulletinHelper.show_error(locali.get_string("ZWYLIB_NOT_FOUND")))
        zwylib = None

def is_zwylib_present() -> bool:
    return zwylib is not None

class CodeObfuscator(ast.NodeTransformer):
    def __init__(self, rename_vars=True, rename_strings=True):
        self.rename_vars = rename_vars
        self.rename_strings = rename_strings
        self.var_map = {}
        self.str_map = {}
        self.var_counter = 0
        self.str_counter = 0

    def _get_new_var_name(self, old_name):
        if old_name not in self.var_map:
            self.var_map[old_name] = f"var_{self.var_counter}"
            self.var_counter += 1
        return self.var_map[old_name]

    def _get_new_str_val(self, old_str):
        if old_str not in self.str_map:
            self.str_map[old_str] = f"string_literal_{self.str_counter}"
            self.str_counter += 1
        return self.str_map[old_str]

    def visit_Name(self, node):
        if self.rename_vars and isinstance(node.ctx, (ast.Store, ast.Load, ast.Del)):
            if not (node.id.startswith('__') and node.id.endswith('__')):
                node.id = self._get_new_var_name(node.id)
        return node

    def visit_FunctionDef(self, node):
        if self.rename_vars:
            node.name = self._get_new_var_name(node.name)
        self.generic_visit(node)
        return node

    def visit_ClassDef(self, node):
        if self.rename_vars:
            node.name = self._get_new_var_name(node.name)
        self.generic_visit(node)
        return node

    def visit_arg(self, node):
        if self.rename_vars:
            node.arg = self._get_new_var_name(node.arg)
        return node

    def visit_Constant(self, node):
        if self.rename_strings and isinstance(node.value, str):
            node.value = self._get_new_str_val(node.value)
        return node

class AlertManager:
    def __init__(self):
        self.alert_builder_instance: Optional[AlertDialogBuilder] = None

    def show_info_alert(self, title: str, message: str, positive_button: str):
        last_fragment = get_last_fragment()
        if not last_fragment or not last_fragment.getParentActivity(): return
        context = last_fragment.getParentActivity()
        builder = AlertDialogBuilder(context, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
        self.alert_builder_instance = builder
        builder.set_title(title)
        builder.set_message(message)
        builder.set_positive_button(positive_button, lambda d, w: self.dismiss_dialog())
        builder.set_cancelable(True)
        builder.set_canceled_on_touch_outside(True)
        run_on_ui_thread(builder.show)

    def dismiss_dialog(self):
        if self.alert_builder_instance and self.alert_builder_instance.get_dialog() and self.alert_builder_instance.get_dialog().isShowing():
            self.alert_builder_instance.dismiss()
            self.alert_builder_instance = None

class LocalizationManager:
    strings = {
        "ru": {
            "SETTINGS_HEADER": "Настройки Gemini Security",
            "API_KEY_INPUT": "API Key",
            "API_KEY_SUBTEXT": "Получите ключ в Google AI Studio.",
            "GET_API_KEY_BUTTON": "Ссылка для получения ключа",
            "MODEL_SELECTOR": "Модель",
            "PROMPT_INPUT_MD": "Промпт (Markdown)",
            "PROMPT_INPUT_PLAIN": "Промпт (Цитата)",
            "ENABLE_SWITCH": "Включить сканер",
            "USAGE_INFO_TITLE": "FAQ",
            "USAGE_INFO_TEXT": (
                "Этот плагин помогает проверить код других плагинов на наличие подозрительных или вредоносных действий с помощью нейросети Google Gemini.\n\n"
                "Шаг 1: Настройка\n"
                "1. Получите ваш API-ключ в Google AI Studio.\n"
                "2. Вставьте ключ в соответствующее поле в настройках плагина.\n\n"
                "Шаг 2: Использование\n"
                "1. Найдите сообщение с файлом плагина, который вы хотите проверить (файл должен иметь расширение .plugin или .py).\n"
                f"2. Ответьте на это сообщение командой: {DEFAULT_COMMAND}\n\n"
                "Плагин отправит код на анализ и пришлет вам отчет о безопасности в этот же чат."
            ),
            "API_KEY_MISSING": "❌ API ключ для Gemini не найден. Укажите его в настройках плагина.",
            "NO_REPLY": "❌ Пожалуйста, ответьте на сообщение с файлом плагина.",
            "NOT_A_PLUGIN": "❌ Файл в отвеченном сообщении не является плагином (.plugin или .py).",
            "ANALYZING_MESSAGE": "🛡️ Проверяю плагин на безопасность...",
            "API_ERROR": "⚠️ Ошибка API Gemini: {error}",
            "FILE_DOWNLOAD_ERROR": "❌ Не удалось загрузить файл плагина.",
            "FILE_READ_ERROR": "❌ Не удалось прочитать файл плагина.",
            "UNEXPECTED_ERROR": "❗ Произошла неожиданная ошибка: {error}",
            "SUCCESS_HEADER_MD": "🛡️ Отчет по безопасности: {plugin_name} v{plugin_version}\n\n",
            "SUCCESS_HEADER_PLAIN": "🛡️ Отчет по безопасности: {plugin_name} v{plugin_version}\n\n",
            "ALERT_CLOSE_BUTTON": "Закрыть",
            "USE_BLOCKQUOTE_TITLE": "Использовать цитату",
            "USE_BLOCKQUOTE_SUBTEXT": "Отображать отчет в виде сворачиваемой цитаты для компактности.",
            "USE_PREMIUM_EMOJI_TITLE": "Премиум эмодзи",
            "USE_PREMIUM_EMOJI_SUBTEXT": "Заменять обычные эмодзи на анимированные премиум эмодзи в отчётах.",
            "APPEARANCE_HEADER": "Внешний вид",
            "ADVANCED_HEADER": "Расширенные настройки",
            "GENERATION_HEADER": "Параметры генерации",
            "TEMPERATURE_INPUT": "Температура",
            "TEMPERATURE_SUBTEXT": "0.0-2.0. Контролирует случайность. Более низкие значения делают ответ более предсказуемым.",
            "MAX_TOKENS_INPUT": "Максимум токенов",
            "MAX_TOKENS_SUBTEXT": "Максимальная длина ответа в токенах.",
            "RENAME_VARS_TITLE": "Переменные",
            "RENAME_VARS_SUBTEXT": "Заменяет имена переменных и функций на общие (var_0, func_1).",
            "RENAME_STRINGS_TITLE": "Строки",
            "RENAME_STRINGS_SUBTEXT": "Заменяет содержимое строковых литералов на метки (string_0).",
            "DONATE_HEADER": "Поддержать разработку",
            "DONATE_CRYPTO": "Крипто кошелек",
            "DONATE_INFO": "Другая информация и реквизиты",
            "ZWYLIB_NOT_FOUND": "Для автообновления требуется плагин ZwyLib, но он не установлен."
        },
        "en": {
            "SETTINGS_HEADER": "Gemini Security Settings",
            "API_KEY_INPUT": "API Key",
            "API_KEY_SUBTEXT": "Get your key from Google AI Studio.",
            "GET_API_KEY_BUTTON": "Link to get API Key",
            "MODEL_SELECTOR": "Model",
            "PROMPT_INPUT_MD": "Prompt (Markdown)",
            "PROMPT_INPUT_PLAIN": "Prompt (Blockquote)",
            "ENABLE_SWITCH": "Enable Scanner",
            "USAGE_INFO_TITLE": "FAQ",
            "USAGE_INFO_TEXT": (
                "This plugin helps you check the code of other plugins for suspicious or malicious activity using the Google Gemini neural network.\n\n"
                "Step 1: Setup\n"
                "1. Get your API key from Google AI Studio.\n"
                "2. Paste the key into the corresponding field in the plugin settings.\n\n"
                "Step 2: Usage\n"
                "1. Find a message with the plugin file you want to scan (the file must have a .plugin or .py extension).\n"
                f"2. Reply to this message with the command: {DEFAULT_COMMAND}\n\n"
                "The plugin will send the code for analysis and send you a security report in the same chat."
            ),
            "API_KEY_MISSING": "❌ Gemini API key not found. Please set it in the plugin settings.",
            "NO_REPLY": "❌ Please reply to a message containing a plugin file.",
            "NOT_A_PLUGIN": "❌ The replied message does not contain a plugin file (.plugin or .py).",
            "ANALYZING_MESSAGE": "🛡️ Scanning plugin for safety...",
            "API_ERROR": "⚠️ Gemini API Error: {error}",
            "FILE_DOWNLOAD_ERROR": "❌ Failed to download the plugin file.",
            "FILE_READ_ERROR": "❌ Failed to read the plugin file.",
            "UNEXPECTED_ERROR": "❗ An unexpected error occurred: {error}",
            "SUCCESS_HEADER_MD": "🛡️ Security Report: {plugin_name} v{plugin_version}\n\n",
            "SUCCESS_HEADER_PLAIN": "🛡️ Security Report: {plugin_name} v{plugin_version}\n\n",
            "ALERT_CLOSE_BUTTON": "Close",
            "USE_BLOCKQUOTE_TITLE": "Use blockquote",
            "USE_BLOCKQUOTE_SUBTEXT": "Display the report as a collapsible blockquote for compactness.",
            "USE_PREMIUM_EMOJI_TITLE": "Premium emoji",
            "USE_PREMIUM_EMOJI_SUBTEXT": "Replace regular emoji with animated premium emoji in reports.",
            "APPEARANCE_HEADER": "Appearance",
            "ADVANCED_HEADER": "Advanced Settings",
            "GENERATION_HEADER": "Generation Parameters",
            "TEMPERATURE_INPUT": "Temperature",
            "TEMPERATURE_SUBTEXT": "0.0-2.0. Controls randomness. Lower values are less random.",
            "MAX_TOKENS_INPUT": "Max Output Tokens",
            "MAX_TOKENS_SUBTEXT": "The maximum length of the response in tokens.",
            "RENAME_VARS_TITLE": "Variables",
            "RENAME_VARS_SUBTEXT": "Replaces variable and function names with generic ones (var_0, func_1).",
            "RENAME_STRINGS_TITLE": "Strings",
            "RENAME_STRINGS_SUBTEXT": "Replaces string literal content with labels (string_0).",
            "DONATE_HEADER": "Support Development",
            "DONATE_CRYPTO": "CRYPTO Wallet",
            "DONATE_INFO": "Other info and requisites",
            "ZWYLIB_NOT_FOUND": "The ZwyLib plugin is required for auto-updates but is not installed."
        }
    }

    def __init__(self):
        self.language = Locale.getDefault().getLanguage()
        self.language = self.language if self.language in self.strings else "en"

    def get_string(self, key: str) -> str:
        return self.strings[self.language].get(key, self.strings["en"].get(key, key))

locali = LocalizationManager()

class GeminiAPIHandler:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            "Content-Type": "application/json",
            "User-Agent": f"ExteraPlugin/{__id__}/{__version__}"
        })

    def analyze_plugin_code(self, api_key: str, model_name: str, prompt: str, temperature: float, max_tokens: int) -> Dict[str, Any]:
        url = f"{GEMINI_BASE_URL}{model_name}:generateContent?key={api_key}"
        payload = {
            "contents": [{"parts": [{"text": prompt}]}],
            "generationConfig": {
                "temperature": temperature,
                "maxOutputTokens": max_tokens,
            }
        }
        try:
            response = self.session.post(url, json=payload, timeout=90)
            response.raise_for_status()
            data = response.json()
            if "candidates" in data and data["candidates"][0].get("content", {}).get("parts", [{}])[0].get("text"):
                return {"success": True, "text": data["candidates"][0]["content"]["parts"][0]["text"]}
            else:
                error_details = data.get("error", {}).get("message", "Invalid API response format.")
                return {"success": False, "error": error_details}
        except requests.exceptions.HTTPError as e:
            error_text = f"HTTP {e.response.status_code}"
            try: error_text += f": {e.response.json().get('error',{}).get('message', e.response.text)}"
            except: error_text += f": {e.response.text}"
            return {"success": False, "error": error_text}
        except requests.exceptions.RequestException as e: return {"success": False, "error": f"Network error: {str(e)}"}
        except Exception as e: return {"success": False, "error": f"Unexpected error: {str(e)}"}

class GeminiPluginAnalyzer(BasePlugin):
    def __init__(self):
        super().__init__()
        self.api_handler = GeminiAPIHandler()
        self.alert_manager = AlertManager()

    def on_plugin_load(self):
        self.add_on_send_message_hook()
        import_zwylib()
        if is_zwylib_present():
            zwylib.add_autoupdater_task(__id__, AUTOUPDATE_CHANNEL_ID, AUTOUPDATE_CHANNEL_USERNAME, AUTOUPDATE_MESSAGE_ID)

    def on_plugin_unload(self):
        self.alert_manager.dismiss_dialog()
        if is_zwylib_present():
            zwylib.remove_autoupdater_task(__id__)

    def _open_link(self, url: str):
        from android.content import Intent
        from android.net import Uri
        last_fragment = get_last_fragment()
        if not last_fragment: return
        context = last_fragment.getParentActivity()
        if not context: return
        intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        context.startActivity(intent)

    def _copy_to_clipboard(self, label, text):
        if AndroidUtilities.addToClipboard(text):
            BulletinHelper.show_info(f"Copied {label} to clipboard")
    
    def _show_error_bulletin(self, key: str, **kwargs):
        message = locali.get_string(key).format(**kwargs)
        run_on_ui_thread(lambda: BulletinHelper.show_error(message))

    def _handle_show_info_alert_click(self, view):
        title = locali.get_string("USAGE_INFO_TITLE")
        text = locali.get_string("USAGE_INFO_TEXT")
        close_button = locali.get_string("ALERT_CLOSE_BUTTON")
        parsed_text = parse_markdown(text)
        self.alert_manager.show_info_alert(title, parsed_text.text, close_button)

    def create_settings(self) -> List[Any]:
        return [
            Header(text=locali.get_string("SETTINGS_HEADER")),
            Switch(key="enabled", text=locali.get_string("ENABLE_SWITCH"), icon="menu_privacy_policy", default=True),
            Input(key="gemini_api_key", text=locali.get_string("API_KEY_INPUT"), icon="msg_limit_links", default="", subtext=locali.get_string("API_KEY_SUBTEXT")),
            Text(
                text=locali.get_string("GET_API_KEY_BUTTON"),
                icon="msg_link",
                accent=True,
                on_click=lambda view: self._open_link("https://aistudio.google.com/app/apikey")
            ),
            Divider(),
            Header(text="Model and Prompt"),
            Selector(key="model_selection", text=locali.get_string("MODEL_SELECTOR"), icon="msg_media", default=0, items=MODEL_DISPLAY_NAMES),
            Input(key="custom_prompt_md", text=locali.get_string("PROMPT_INPUT_MD"), icon="filled_unknown", default=DEFAULT_PROMPT_MARKDOWN),
            Input(key="custom_prompt_plain", text=locali.get_string("PROMPT_INPUT_PLAIN"), icon="filled_unknown", default=DEFAULT_PROMPT_PLAINTEXT),
            Divider(),
            Header(text=locali.get_string("GENERATION_HEADER")),
            Input(key="gemini_temperature", text=locali.get_string("TEMPERATURE_INPUT"), icon="msg_photo_settings", default="0.7", subtext=locali.get_string("TEMPERATURE_SUBTEXT")),
            Input(key="gemini_max_tokens", text=locali.get_string("MAX_TOKENS_INPUT"), icon="msg_photo_settings", default="4096", subtext=locali.get_string("MAX_TOKENS_SUBTEXT")),
            Divider(),
            Header(text=locali.get_string("APPEARANCE_HEADER")),
            Switch(
                key="use_blockquote",
                text=locali.get_string("USE_BLOCKQUOTE_TITLE"),
                subtext=locali.get_string("USE_BLOCKQUOTE_SUBTEXT"),
                icon="header_goinline_solar",
                default=True
            ),
            Switch(
                key="use_premium_emoji",
                text=locali.get_string("USE_PREMIUM_EMOJI_TITLE"),
                subtext=locali.get_string("USE_PREMIUM_EMOJI_SUBTEXT"),
                icon="menu_feature_reactions_remix",
                default=False
            ),
            Divider(),
            Header(text=locali.get_string("ADVANCED_HEADER")),
            Switch(key="rename_variables", text=locali.get_string("RENAME_VARS_TITLE"), subtext=locali.get_string("RENAME_VARS_SUBTEXT"), icon="large_hidden", default=True),
            Switch(key="rename_strings", text=locali.get_string("RENAME_STRINGS_TITLE"), subtext=locali.get_string("RENAME_STRINGS_SUBTEXT"), icon="large_hidden", default=True),
            Divider(),
            Text(text=locali.get_string("USAGE_INFO_TITLE"), icon="msg_info", on_click=self._handle_show_info_alert_click),
            Divider(),
            Header(text=locali.get_string("DONATE_HEADER")),
            Text(
                text=locali.get_string("DONATE_CRYPTO"),
                icon="menu_cashtag",
                accent=True,
                on_click=lambda view: run_on_ui_thread(lambda: self._copy_to_clipboard("CRYPTO", "http://t.me/send?start=IVaZsLfW7aSn"))
            ),
            Text(
                text=locali.get_string("DONATE_INFO"),
                icon="msg_info",
                accent=True,
                on_click=lambda view: run_on_ui_thread(lambda: get_messages_controller().openByUserName("mishabotov", get_last_fragment(), 1))
            )
        ]

    def _get_plugin_metadata(self, code: str) -> Tuple[str, str]:
        name = "Unknown Plugin"; version = "Unknown Version"
        try:
            tree = ast.parse(code)
            for node in ast.walk(tree):
                if isinstance(node, ast.Assign):
                    for target in node.targets:
                        if isinstance(target, ast.Name):
                            if target.id == "__name__": name = ast.literal_eval(node.value)
                            elif target.id == "__version__": version = ast.literal_eval(node.value)
        except Exception:
            pass
        return name, version

    def _get_chat_id_from_params(self, params: Any) -> int:
        peer = getattr(params, 'peer', None)
        if not peer:
            log("[Gemini] No peer found in params")
            return 0

        if hasattr(peer, 'channel_id') and peer.channel_id != 0:
            chat_id = -peer.channel_id
            log(f"[Gemini] Found channel_id: {chat_id}")
            return chat_id
        if hasattr(peer, 'chat_id') and peer.chat_id != 0:
            chat_id = -peer.chat_id
            log(f"[Gemini] Found chat_id: {chat_id}")
            return chat_id
        if hasattr(peer, 'user_id') and peer.user_id != 0:
            chat_id = peer.user_id
            log(f"[Gemini] Found user_id: {chat_id}")
            return chat_id

        log(f"[Gemini] Peer object has no recognizable ID fields: {peer}")
        return 0

    def _wait_for_file(self, file_path: str, document: Any) -> bool:
        if os.path.exists(file_path): return True
        get_file_loader().loadFile(document, "gemini_analyzer", FileLoader.PRIORITY_HIGH, 1)
        for _ in range(30):
            if os.path.exists(file_path): return True
            time.sleep(1)
        return False
    
    def _get_obfuscated_code(self, code: str) -> str:
        lines = []
        for line in code.split('\n'):
            stripped_line = line.split('#', 1)[0]
            lines.append(stripped_line)
        clean_code = '\n'.join(lines)

        rename_vars = self.get_setting("rename_variables", True)
        rename_strings = self.get_setting("rename_strings", True)

        if not rename_vars and not rename_strings:
            return '\n'.join(line for line in clean_code.split('\n') if line.strip())

        try:
            tree = ast.parse(clean_code)
            transformer = CodeObfuscator(rename_vars, rename_strings)
            new_tree = transformer.visit(tree)
            ast.fix_missing_locations(new_tree)
            return ast.unparse(new_tree)
        except Exception:
            return '\n'.join(line for line in clean_code.split('\n') if line.strip())

    def _process_analysis_in_background(self, params: Any, document: Any):
        try:
            file_path_obj = get_file_loader().getPathToAttach(document, True)
            if not self._wait_for_file(file_path_obj.getAbsolutePath(), document):
                self._show_error_bulletin("FILE_DOWNLOAD_ERROR")
                return

            try:
                with open(file_path_obj.getAbsolutePath(), "r", encoding="utf-8", errors="ignore") as f:
                    plugin_code = f.read()
                plugin_name, plugin_version = self._get_plugin_metadata(plugin_code)
            except Exception as e:
                self._show_error_bulletin("FILE_READ_ERROR", e=str(e))
                return

            processed_code = self._get_obfuscated_code(plugin_code)

            api_key = self.get_setting("gemini_api_key", "")
            model_idx = self.get_setting("model_selection", 0)
            model_name = MODEL_API_NAMES[model_idx]
            use_blockquote = self.get_setting("use_blockquote", True)
            
            try:
                temperature = float(self.get_setting("gemini_temperature", "0.5"))
            except (ValueError, TypeError):
                temperature = 0.5

            try:
                max_tokens = int(self.get_setting("gemini_max_tokens", "8192"))
            except (ValueError, TypeError):
                max_tokens = 8192
            
            prompt_template = self.get_setting("custom_prompt_plain" if use_blockquote else "custom_prompt_md", 
                                             DEFAULT_PROMPT_PLAINTEXT if use_blockquote else DEFAULT_PROMPT_MARKDOWN)

            full_prompt = prompt_template.format(plugin_code=processed_code, plugin_name=plugin_name, plugin_version=plugin_version)
            result = self.api_handler.analyze_plugin_code(api_key, model_name, full_prompt, temperature, max_tokens)
            
            if result.get("success"):
                self._send_report(params, result["text"], plugin_name, plugin_version, use_blockquote)
            else: 
                self._show_error_bulletin("API_ERROR", error=result.get("error", "Unknown"))

        except Exception as e:
            log(f"[Gemini] Exception in _process_analysis_in_background: {e}")
            log(f"[Gemini] Exception type: {type(e)}")
            log(f"[Gemini] Traceback: {traceback.format_exc()}")
            self._show_error_bulletin("UNEXPECTED_ERROR", error=str(e))
            traceback.print_exc()

    def _send_report(self, params: Any, response_text: str, plugin_name: str, plugin_version: str, use_blockquote: bool):
        try:
            header_key = "SUCCESS_HEADER_PLAIN" if use_blockquote else "SUCCESS_HEADER_MD"
            header = locali.get_string(header_key).format(plugin_name=plugin_name, plugin_version=plugin_version)

            use_premium_emoji = self.get_setting("use_premium_emoji", False)
            if use_premium_emoji:
                enhanced_header = replace_with_premium_emoji(header)
                enhanced_response_text = replace_with_premium_emoji(response_text)
                report_text = enhanced_header + enhanced_response_text
            else:
                report_text = header + response_text

            log(f"[Gemini] Sending report with blockquote={use_blockquote}, text_length={len(report_text)}")

        except Exception as e:
            log(f"[Gemini] Error preparing report text: {e}")
            fallback_text = f"🛡️ Security Report: {plugin_name} v{plugin_version}\n\n{response_text}"
            use_premium_emoji = self.get_setting("use_premium_emoji", False)
            if use_premium_emoji:
                report_text = replace_with_premium_emoji(fallback_text)
            else:
                report_text = fallback_text
        
        try:
            peer_id = getattr(params, "peer", None)
            log(f"[Gemini] Raw peer from params: {peer_id}")

            message_payload = {
                "peer": peer_id,
                "replyToMsg": getattr(params, "replyToMsg", None),
                "replyToTopMsg": getattr(params, "replyToTopMsg", None)
            }
            log(f"[Gemini] Created message_payload successfully")
        except Exception as e:
            log(f"[Gemini] Error in simple peer approach: {e}")
            try:
                last_fragment = get_last_fragment()
                if last_fragment and hasattr(last_fragment, "getDialogId"):
                    peer_id = last_fragment.getDialogId()
                    log(f"[Gemini] Fallback: got peer from fragment: {peer_id}")
                else:
                    peer_id = 0
                    log("[Gemini] Fallback: using peer_id = 0")

                message_payload = {
                    "peer": peer_id,
                    "message": "Fallback message"
                }
            except Exception as e2:
                log(f"[Gemini] Fallback also failed: {e2}")
                return

        try:
            parsed = parse_markdown(report_text)
            entities = []

            if parsed.entities:
                for entity in parsed.entities:
                    try:
                        tlrpc_entity = entity.to_tlrpc_object()
                        if tlrpc_entity is not None:
                            entities.append(tlrpc_entity)
                    except Exception as e:
                        log(f"[Gemini] Entity error: {e}")

            if use_blockquote:
                blockquote_entity = TLRPC.TL_messageEntityBlockquote()
                blockquote_entity.collapsed = True
                blockquote_entity.offset = 0
                blockquote_entity.length = len(parsed.text.encode('utf-16le')) // 2
                entities.append(blockquote_entity)

            message_payload["message"] = parsed.text
            message_payload["entities"] = entities if entities else None
            send_message(message_payload)
            log(f"[Gemini] Report sent with {len(entities)} entities")

        except Exception as e:
            log(f"[Gemini] Send failed, using fallback: {e}")
            try:
                clean_text = report_text.replace('**', '').replace('__', '').replace('`', '')
                clean_text = get_regular_emoji_for_bulletin(clean_text)
                message_payload["message"] = clean_text
                message_payload["entities"] = None
                send_message(message_payload)
                log("[Gemini] Fallback send successful")
            except Exception as e2:
                log(f"[Gemini] Fallback failed: {e2}")
                self._show_error_bulletin("UNEXPECTED_ERROR", error="Failed to send report")

        verdict_line = response_text.split('\n', 1)[0].strip()
        clean_verdict = verdict_line.replace("◈ Вердикт:", "").strip()

        bulletin_verdict = get_regular_emoji_for_bulletin(clean_verdict)

        def show_verdict_bulletin():
            if "✅" in verdict_line:
                BulletinHelper.show_success(bulletin_verdict)
            elif "❌" in verdict_line or "📛" in verdict_line:
                BulletinHelper.show_error(bulletin_verdict)
            else:
                BulletinHelper.show_info(bulletin_verdict)
        run_on_ui_thread(show_verdict_bulletin)

    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        if not hasattr(params, "message") or not isinstance(params.message, str): return HookResult()

        message_text = params.message.strip()
        if message_text.lower() != DEFAULT_COMMAND or not self.get_setting("enabled", True): return HookResult()

        log(f"[Gemini] Processing command {DEFAULT_COMMAND}")

        try:
            params_attrs = [attr for attr in dir(params) if not attr.startswith('_')]
            log(f"[Gemini] Available params attributes: {params_attrs}")

            if hasattr(params, 'peer'):
                log(f"[Gemini] params.peer exists: {params.peer}")
            if hasattr(params, 'dialog_id'):
                log(f"[Gemini] params.dialog_id exists: {params.dialog_id}")
            if hasattr(params, 'chat_id'):
                log(f"[Gemini] params.chat_id exists: {params.chat_id}")
        except Exception as e:
            log(f"[Gemini] Error inspecting params: {e}")

        api_key = self.get_setting("gemini_api_key", "")
        if not api_key:
            self._show_error_bulletin("API_KEY_MISSING")
            return HookResult(strategy=HookStrategy.CANCEL)

        if not hasattr(params, "replyToMsg") or not params.replyToMsg:
            self._show_error_bulletin("NO_REPLY")
            return HookResult(strategy=HookStrategy.CANCEL)

        document = MessageObject.getDocument(params.replyToMsg.messageOwner)
        if not document or not any(str(document.file_name_fixed).endswith(ext) for ext in [".plugin", ".py"]):
            self._show_error_bulletin("NOT_A_PLUGIN")
            return HookResult(strategy=HookStrategy.CANCEL)

        log(f"[Gemini] Starting analysis of plugin file: {document.file_name_fixed}")
        BulletinHelper.show_info(locali.get_string("ANALYZING_MESSAGE"))
        run_on_queue(lambda: self._process_analysis_in_background(params, document))
        return HookResult(strategy=HookStrategy.CANCEL)