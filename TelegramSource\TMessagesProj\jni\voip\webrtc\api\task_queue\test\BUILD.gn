# Copyright (c) 2018 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../../webrtc.gni")

rtc_library("mock_task_queue_base") {
  testonly = true
  sources = [ "mock_task_queue_base.h" ]
  deps = [
    "../../../api/task_queue:task_queue",
    "../../../api/units:time_delta",
    "../../../test:test_support",
  ]
  absl_deps = [ "//third_party/abseil-cpp/absl/functional:any_invocable" ]
}
