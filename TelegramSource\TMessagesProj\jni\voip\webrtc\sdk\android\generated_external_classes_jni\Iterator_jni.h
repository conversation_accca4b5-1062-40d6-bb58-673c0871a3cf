// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     java/util/Iterator

#ifndef java_util_Iterator_JNI
#define java_util_Iterator_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_java_util_Iterator[];
const char kClassPath_java_util_Iterator[] = "java/util/Iterator";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_java_util_Iterator_clazz(nullptr);
#ifndef java_util_Iterator_clazz_defined
#define java_util_Iterator_clazz_defined
inline jclass java_util_Iterator_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_java_util_Iterator, &g_java_util_Iterator_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace JNI_Iterator {


static std::atomic<jmethodID> g_java_util_Iterator_forEachRemaining1(nullptr);
[[maybe_unused]] static void Java_Iterator_forEachRemaining(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static void Java_Iterator_forEachRemaining(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_Iterator_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Iterator_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "forEachRemaining",
          "(Ljava/util/function/Consumer;)V",
          &g_java_util_Iterator_forEachRemaining1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
}

static std::atomic<jmethodID> g_java_util_Iterator_hasNext0(nullptr);
[[maybe_unused]] static jboolean Java_Iterator_hasNext(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jboolean Java_Iterator_hasNext(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_Iterator_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Iterator_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "hasNext",
          "()Z",
          &g_java_util_Iterator_hasNext0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_util_Iterator_next0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Iterator_next(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Iterator_next(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_Iterator_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Iterator_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "next",
          "()Ljava/lang/Object;",
          &g_java_util_Iterator_next0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Iterator_remove0(nullptr);
[[maybe_unused]] static void Java_Iterator_remove(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static void Java_Iterator_remove(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_Iterator_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Iterator_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "remove",
          "()V",
          &g_java_util_Iterator_remove0);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

}  // namespace JNI_Iterator

#endif  // java_util_Iterator_JNI
