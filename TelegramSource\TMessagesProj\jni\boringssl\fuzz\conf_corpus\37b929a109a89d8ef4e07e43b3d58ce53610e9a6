# [ req ]
default_bits		= 2432
default_keyfile 	= cakey.pem
default_md	        = sha256
distinguished_name	= req_DN
string_mask             = utf8only
x509_extensions         = v3_selfsign

####################################################################
[ req ]
default_bits		= 2432
default_keyfile 	= cakey.pem
default_md	        = sha256
distinguished_name	= req_DN
string_mask             = utf8only
x509_extensions         = v3_selfsign

[ req_DN ]
commonName                      = "Common Name"
commonName_value              = "CA"

[ v3_selfsign ]
basicConstraints = critical,CA:true
keyUsage = keyCertSign
subjectKeyIdentifier=hash

####################################################################
[ ca ]
default_ca      = CA_default            # The default ca section

####################################################################
[ CA_default ]

dir             = ./demoCA
certificate	= ./demoCA/cacert.pem
serial		= ./demoCA/serial
private_key	= ./demoCA/private/cakey.pem
new_certs_dir   = ./demoCA/newcerts

certificate     = cacert.pem
private_key     = cakey.pem

x509_extensions = v3_user

name_opt        = ca_default            # Subject Name options
cert_opt        = ca_default            # Certificate field options

policy          = policy_anything

[ policy_anything ]
countryName             = optional
stateOrProvinceName     = optional
localityName            = optional
organizationName        = optional
organizationalUnitName  = optional
commonName              = supplied
emailAddress            = optional

[ v3_user ]
basicConstraints=critical,CA:FALSE
subjectKeyIdentifier=hash
authorityKeyIdentifier=keyid,issuer
issuerAltName=issuer:copy

