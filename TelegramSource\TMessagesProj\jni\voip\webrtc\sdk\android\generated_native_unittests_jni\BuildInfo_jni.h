// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/BuildInfo

#ifndef org_webrtc_BuildInfo_JNI
#define org_webrtc_BuildInfo_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_BuildInfo[];
const char kClassPath_org_webrtc_BuildInfo[] = "org/webrtc/BuildInfo";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_BuildInfo_clazz(nullptr);
#ifndef org_webrtc_BuildInfo_clazz_defined
#define org_webrtc_BuildInfo_clazz_defined
inline jclass org_webrtc_BuildInfo_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_BuildInfo,
      &g_org_webrtc_BuildInfo_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_BuildInfo_getAndroidBuildId0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_BuildInfo_getAndroidBuildId(JNIEnv* env) {
  jclass clazz = org_webrtc_BuildInfo_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_BuildInfo_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getAndroidBuildId",
          "()Ljava/lang/String;",
          &g_org_webrtc_BuildInfo_getAndroidBuildId0);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_BuildInfo_getBrand0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_BuildInfo_getBrand(JNIEnv* env) {
  jclass clazz = org_webrtc_BuildInfo_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_BuildInfo_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getBrand",
          "()Ljava/lang/String;",
          &g_org_webrtc_BuildInfo_getBrand0);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_BuildInfo_getBuildRelease0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_BuildInfo_getBuildRelease(JNIEnv* env) {
  jclass clazz = org_webrtc_BuildInfo_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_BuildInfo_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getBuildRelease",
          "()Ljava/lang/String;",
          &g_org_webrtc_BuildInfo_getBuildRelease0);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_BuildInfo_getBuildType0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_BuildInfo_getBuildType(JNIEnv* env) {
  jclass clazz = org_webrtc_BuildInfo_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_BuildInfo_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getBuildType",
          "()Ljava/lang/String;",
          &g_org_webrtc_BuildInfo_getBuildType0);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_BuildInfo_getDeviceManufacturer0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_BuildInfo_getDeviceManufacturer(JNIEnv* env) {
  jclass clazz = org_webrtc_BuildInfo_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_BuildInfo_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getDeviceManufacturer",
          "()Ljava/lang/String;",
          &g_org_webrtc_BuildInfo_getDeviceManufacturer0);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_BuildInfo_getDeviceModel0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_BuildInfo_getDeviceModel(JNIEnv* env) {
  jclass clazz = org_webrtc_BuildInfo_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_BuildInfo_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getDeviceModel",
          "()Ljava/lang/String;",
          &g_org_webrtc_BuildInfo_getDeviceModel0);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_BuildInfo_getSdkVersion0(nullptr);
static jint Java_BuildInfo_getSdkVersion(JNIEnv* env) {
  jclass clazz = org_webrtc_BuildInfo_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_BuildInfo_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getSdkVersion",
          "()I",
          &g_org_webrtc_BuildInfo_getSdkVersion0);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id);
  return ret;
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_BuildInfo_JNI
