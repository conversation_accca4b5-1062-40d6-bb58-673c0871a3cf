// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/NetworkMonitor

#ifndef org_webrtc_NetworkMonitor_JNI
#define org_webrtc_NetworkMonitor_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_NetworkMonitor[];
const char kClassPath_org_webrtc_NetworkMonitor[] = "org/webrtc/NetworkMonitor";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_NetworkMonitor_clazz(nullptr);
#ifndef org_webrtc_NetworkMonitor_clazz_defined
#define org_webrtc_NetworkMonitor_clazz_defined
inline jclass org_webrtc_NetworkMonitor_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_NetworkMonitor,
      &g_org_webrtc_NetworkMonitor_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {

JNI_BOUNDARY_EXPORT void Java_org_webrtc_NetworkMonitor_nativeNotifyConnectionTypeChanged(
    JNIEnv* env,
    jobject jcaller,
    jlong nativeAndroidNetworkMonitor) {
  AndroidNetworkMonitor* native =
      reinterpret_cast<AndroidNetworkMonitor*>(nativeAndroidNetworkMonitor);
  CHECK_NATIVE_PTR(env, jcaller, native, "NotifyConnectionTypeChanged");
  return native->NotifyConnectionTypeChanged(env, jni_zero::JavaParamRef<jobject>(env, jcaller));
}

JNI_BOUNDARY_EXPORT void Java_org_webrtc_NetworkMonitor_nativeNotifyOfActiveNetworkList(
    JNIEnv* env,
    jobject jcaller,
    jlong nativeAndroidNetworkMonitor,
    jobjectArray networkInfos) {
  AndroidNetworkMonitor* native =
      reinterpret_cast<AndroidNetworkMonitor*>(nativeAndroidNetworkMonitor);
  CHECK_NATIVE_PTR(env, jcaller, native, "NotifyOfActiveNetworkList");
  return native->NotifyOfActiveNetworkList(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      jni_zero::JavaParamRef<jobjectArray>(env, networkInfos));
}

JNI_BOUNDARY_EXPORT void Java_org_webrtc_NetworkMonitor_nativeNotifyOfNetworkConnect(
    JNIEnv* env,
    jobject jcaller,
    jlong nativeAndroidNetworkMonitor,
    jobject networkInfo) {
  AndroidNetworkMonitor* native =
      reinterpret_cast<AndroidNetworkMonitor*>(nativeAndroidNetworkMonitor);
  CHECK_NATIVE_PTR(env, jcaller, native, "NotifyOfNetworkConnect");
  return native->NotifyOfNetworkConnect(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      jni_zero::JavaParamRef<jobject>(env, networkInfo));
}

JNI_BOUNDARY_EXPORT void Java_org_webrtc_NetworkMonitor_nativeNotifyOfNetworkDisconnect(
    JNIEnv* env,
    jobject jcaller,
    jlong nativeAndroidNetworkMonitor,
    jlong networkHandle) {
  AndroidNetworkMonitor* native =
      reinterpret_cast<AndroidNetworkMonitor*>(nativeAndroidNetworkMonitor);
  CHECK_NATIVE_PTR(env, jcaller, native, "NotifyOfNetworkDisconnect");
  return native->NotifyOfNetworkDisconnect(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      networkHandle);
}

JNI_BOUNDARY_EXPORT void Java_org_webrtc_NetworkMonitor_nativeNotifyOfNetworkPreference(
    JNIEnv* env,
    jobject jcaller,
    jlong nativeAndroidNetworkMonitor,
    jobject type,
    jint preference) {
  AndroidNetworkMonitor* native =
      reinterpret_cast<AndroidNetworkMonitor*>(nativeAndroidNetworkMonitor);
  CHECK_NATIVE_PTR(env, jcaller, native, "NotifyOfNetworkPreference");
  return native->NotifyOfNetworkPreference(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      jni_zero::JavaParamRef<jobject>(env, type), preference);
}


static std::atomic<jmethodID> g_org_webrtc_NetworkMonitor_androidSdkInt0(nullptr);
static jint Java_NetworkMonitor_androidSdkInt(JNIEnv* env) {
  jclass clazz = org_webrtc_NetworkMonitor_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_NetworkMonitor_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "androidSdkInt",
          "()I",
          &g_org_webrtc_NetworkMonitor_androidSdkInt0);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_org_webrtc_NetworkMonitor_getInstance0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_NetworkMonitor_getInstance(JNIEnv* env) {
  jclass clazz = org_webrtc_NetworkMonitor_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_NetworkMonitor_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getInstance",
          "()Lorg/webrtc/NetworkMonitor;",
          &g_org_webrtc_NetworkMonitor_getInstance0);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_NetworkMonitor_networkBindingSupported0(nullptr);
static jboolean Java_NetworkMonitor_networkBindingSupported(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_NetworkMonitor_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_NetworkMonitor_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "networkBindingSupported",
          "()Z",
          &g_org_webrtc_NetworkMonitor_networkBindingSupported0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_org_webrtc_NetworkMonitor_startMonitoring3(nullptr);
static void Java_NetworkMonitor_startMonitoring(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& applicationContext,
    jlong nativeObserver,
    const jni_zero::JavaRef<jstring>& fieldTrialsString) {
  jclass clazz = org_webrtc_NetworkMonitor_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_NetworkMonitor_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "startMonitoring",
          "(Landroid/content/Context;JLjava/lang/String;)V",
          &g_org_webrtc_NetworkMonitor_startMonitoring3);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, applicationContext.obj(), nativeObserver,
              fieldTrialsString.obj());
}

static std::atomic<jmethodID> g_org_webrtc_NetworkMonitor_stopMonitoring1(nullptr);
static void Java_NetworkMonitor_stopMonitoring(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    jlong nativeObserver) {
  jclass clazz = org_webrtc_NetworkMonitor_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_NetworkMonitor_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "stopMonitoring",
          "(J)V",
          &g_org_webrtc_NetworkMonitor_stopMonitoring1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, nativeObserver);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_NetworkMonitor_JNI
