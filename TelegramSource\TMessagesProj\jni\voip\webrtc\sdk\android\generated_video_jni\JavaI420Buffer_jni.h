// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/JavaI420Buffer

#ifndef org_webrtc_JavaI420Buffer_JNI
#define org_webrtc_JavaI420Buffer_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_JavaI420Buffer[];
const char kClassPath_org_webrtc_JavaI420Buffer[] = "org/webrtc/JavaI420Buffer";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_JavaI420Buffer_clazz(nullptr);
#ifndef org_webrtc_JavaI420Buffer_clazz_defined
#define org_webrtc_JavaI420Buffer_clazz_defined
inline jclass org_webrtc_JavaI420Buffer_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_JavaI420Buffer,
      &g_org_webrtc_JavaI420Buffer_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {

static void JNI_JavaI420Buffer_CropAndScaleI420(JNIEnv* env, const jni_zero::JavaParamRef<jobject>&
    srcY,
    jint srcStrideY,
    const jni_zero::JavaParamRef<jobject>& srcU,
    jint srcStrideU,
    const jni_zero::JavaParamRef<jobject>& srcV,
    jint srcStrideV,
    jint cropX,
    jint cropY,
    jint cropWidth,
    jint cropHeight,
    const jni_zero::JavaParamRef<jobject>& dstY,
    jint dstStrideY,
    const jni_zero::JavaParamRef<jobject>& dstU,
    jint dstStrideU,
    const jni_zero::JavaParamRef<jobject>& dstV,
    jint dstStrideV,
    jint scaleWidth,
    jint scaleHeight);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_JavaI420Buffer_nativeCropAndScaleI420(
    JNIEnv* env,
    jclass jcaller,
    jobject srcY,
    jint srcStrideY,
    jobject srcU,
    jint srcStrideU,
    jobject srcV,
    jint srcStrideV,
    jint cropX,
    jint cropY,
    jint cropWidth,
    jint cropHeight,
    jobject dstY,
    jint dstStrideY,
    jobject dstU,
    jint dstStrideU,
    jobject dstV,
    jint dstStrideV,
    jint scaleWidth,
    jint scaleHeight) {
  return JNI_JavaI420Buffer_CropAndScaleI420(env, jni_zero::JavaParamRef<jobject>(env, srcY),
      srcStrideY, jni_zero::JavaParamRef<jobject>(env, srcU), srcStrideU,
      jni_zero::JavaParamRef<jobject>(env, srcV), srcStrideV, cropX, cropY, cropWidth, cropHeight,
      jni_zero::JavaParamRef<jobject>(env, dstY), dstStrideY, jni_zero::JavaParamRef<jobject>(env,
      dstU), dstStrideU, jni_zero::JavaParamRef<jobject>(env, dstV), dstStrideV, scaleWidth,
      scaleHeight);
}


}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_JavaI420Buffer_JNI
