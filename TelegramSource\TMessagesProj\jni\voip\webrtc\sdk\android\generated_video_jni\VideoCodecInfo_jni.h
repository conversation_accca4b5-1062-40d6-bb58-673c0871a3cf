// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/VideoCodecInfo

#ifndef org_webrtc_VideoCodecInfo_JNI
#define org_webrtc_VideoCodecInfo_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_VideoCodecInfo[];
const char kClassPath_org_webrtc_VideoCodecInfo[] = "org/webrtc/VideoCodecInfo";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_VideoCodecInfo_clazz(nullptr);
#ifndef org_webrtc_VideoCodecInfo_clazz_defined
#define org_webrtc_VideoCodecInfo_clazz_defined
inline jclass org_webrtc_VideoCodecInfo_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_VideoCodecInfo,
      &g_org_webrtc_VideoCodecInfo_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_VideoCodecInfo_Constructor2(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoCodecInfo_Constructor(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& name,
    const jni_zero::JavaRef<jobject>& params) {
  jclass clazz = org_webrtc_VideoCodecInfo_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_VideoCodecInfo_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Ljava/lang/String;Ljava/util/Map;)V",
          &g_org_webrtc_VideoCodecInfo_Constructor2);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, name.obj(), params.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoCodecInfo_getName0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_VideoCodecInfo_getName(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_VideoCodecInfo_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoCodecInfo_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getName",
          "()Ljava/lang/String;",
          &g_org_webrtc_VideoCodecInfo_getName0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoCodecInfo_getParams0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoCodecInfo_getParams(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_VideoCodecInfo_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoCodecInfo_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getParams",
          "()Ljava/util/Map;",
          &g_org_webrtc_VideoCodecInfo_getParams0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_VideoCodecInfo_JNI
