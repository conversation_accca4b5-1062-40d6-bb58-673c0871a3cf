import io
import requests
import os
import uuid
import time
import math
from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
from java.io import File
from java.util import Locale
from org.telegram.messenger import ApplicationLoader
from org.telegram.tgnet.tl import TL_account
from ui.settings import Header, Input, Switch, Divider, Selector
from client_utils import send_message, get_send_messages_helper, get_last_fragment, get_messages_controller, get_user_config, send_request
from base_plugin import BasePlugin, HookResult, HookStrategy
from org.telegram.ui.ActionBar import AlertDialog
from android_utils import log
import threading

__id__ = "lastfm"
__name__ = "LastFM"
__version__ = "1.4.0"
__description__ = "Показывает актуальную песню LastFM. [.np]"
__author__ = "@ArThirtyFour"
__min_version__ = "11.9.0"
__icon__ = "yunochkaaaaaaa/8"

API_KEY = "460cda35be2fbf4f28e8ea7a38580730"
TEMP_DIR_NAME = "temp_lastfm_covers"
DEFAULT_STREAM_STRING = "🎵 {title} — {artist}"
DEFAULT_STREAM_TEXT = "Hi, I use exteraGram"

FONTS = {
    "NotoSansJP": "https://github.com/itsNightly/font_link/raw/refs/heads/main/NotoSansJP-Regular.ttf"
}

progress_dialog = None

def show_with_copy(message, submsg):
    def copy():
        if AndroidUtilities.addToClipboard(submsg):
            BulletinHelper.show_copied_to_clipboard()

    BulletinHelper.show_with_button(message, R.raw.error, "Copy", lambda: copy())

class LastFMPlugin(BasePlugin):

    def __init__(self):
        super().__init__()
        self._temp_dir = None
        threading.Thread(target=self._streamer, daemon=True).start()

    def _streamer(self):
        log("[LastFM] Streamer started")
        while True:
            try:
                if self.get_setting("update_bio", False):
                    log("[LastFM] Update bio is enabled")
                    userFull = get_messages_controller().getUserFull(get_user_config().getClientUserId())
                    if not userFull:
                        log("[LastFM] Failed to get userFull")
                        time.sleep(5)
                        continue

                    stream_place = self.get_setting("stream_place", 1 if get_user_config().isPremium() else 0)
                    max_len = 140 if get_user_config().isPremium() else 70

                    username = self.get_setting("lastfm_username", "")
                    if not username:
                        log("[LastFM] LastFM username is not set")
                        time.sleep(5)
                        continue

                    log(f"[LastFM] Fetching track for user {username}")
                    current_track_url = f'http://ws.audioscrobbler.com/2.0/?method=user.getrecenttracks&nowplaying=true&user={username}&api_key={API_KEY}&format=json'
                    response = requests.get(current_track_url)
                    
                    if response.status_code == 200:
                        data = response.json()
                        log(f"[LastFM] Got response: {data}")
                        if 'recenttracks' in data and 'track' in data['recenttracks'] and data['recenttracks']['track']:
                            nowplaying_track = None
                            for track in data['recenttracks']['track']:
                                if '@attr' in track and 'nowplaying' in track['@attr']:
                                    nowplaying_track = track
                                    break

                            if nowplaying_track:
                                log(f"[LastFM] Found now playing track: {nowplaying_track}")
                                new_about_text = self.get_setting("track_display_format", DEFAULT_STREAM_STRING)
                                new_about_text = new_about_text.replace("{title}", nowplaying_track.get('name', 'Unknown Track'))
                                new_about_text = new_about_text.replace("{artist}", nowplaying_track.get('artist', {}).get('#text', 'Unknown Artist'))
                                
                                if stream_place == 0:  # Bio
                                    if userFull.about != new_about_text[:max_len]:
                                        try:
                                            req = TL_account.updateProfile()
                                            req.flags = 4
                                            req.about = new_about_text[:max_len]
                                            send_request(req, ())
                                            log("[LastFM] Successfully updated bio")
                                        except Exception as e:
                                            log(f"[LastFM] Error updating bio: {e}")
                                            time.sleep(5)
                                else:  # Business Location
                                    if not get_user_config().isPremium():
                                        log("[LastFM] User is not premium, can't update business location")
                                        time.sleep(5)
                                        continue
                                    try:
                                        req = TL_account.updateBusinessLocation()
                                        req.address = new_about_text[:96]
                                        req.flags = 1
                                        send_request(req, ())
                                        log("[LastFM] Successfully updated business location")
                                    except Exception as e:
                                        log(f"[LastFM] Error updating business location: {e}")
                                        time.sleep(5)
                            else:
                                log("[LastFM] No now playing track found")
                                default_bio = self.get_setting("default_stream_text", DEFAULT_STREAM_TEXT)
                                if stream_place == 0:  # Bio
                                    if userFull.about != default_bio[:max_len]:
                                        try:
                                            req = TL_account.updateProfile()
                                            req.flags = 4
                                            req.about = default_bio[:max_len]
                                            send_request(req, ())
                                            log("[LastFM] Set default bio")
                                        except Exception as e:
                                            log(f"[LastFM] Error setting default bio: {e}")
                                            time.sleep(5)
                                else:  # Business Location
                                    if not get_user_config().isPremium():
                                        log("[LastFM] User is not premium, can't update business location")
                                        time.sleep(5)
                                        continue
                                    try:
                                        req = TL_account.updateBusinessLocation()
                                        req.address = default_bio[:96]
                                        req.flags = 1
                                        send_request(req, ())
                                        log("[LastFM] Set default bio in business location")
                                    except Exception as e:
                                        log(f"[LastFM] Error setting default bio in business location: {e}")
                                        time.sleep(5)
                        else:
                            log("[LastFM] No tracks found in response")
                    else:
                        log(f"[LastFM] Bad response from LastFM API: {response.status_code}")
                        default_bio = self.get_setting("default_stream_text", DEFAULT_STREAM_TEXT)
                        if stream_place == 0:  # Bio
                            if userFull.about != default_bio[:max_len]:
                                try:
                                    req = TL_account.updateProfile()
                                    req.flags = 4
                                    req.about = default_bio[:max_len]
                                    send_request(req, ())
                                    log("[LastFM] Set default bio after bad response")
                                except Exception as e:
                                    log(f"[LastFM] Error setting default bio after bad response: {e}")
                                    time.sleep(5)
                        else:  # Business Location
                            if not get_user_config().isPremium():
                                log("[LastFM] User is not premium, can't update business location")
                                time.sleep(5)
                                continue
                            try:
                                req = TL_account.updateBusinessLocation()
                                req.address = default_bio[:96]
                                req.flags = 1
                                send_request(req, ())
                                log("[LastFM] Set default bio in business location after bad response")
                            except Exception as e:
                                log(f"[LastFM] Error setting default bio in business location after bad response: {e}")
                                time.sleep(5)

                    time.sleep(5 if stream_place == 1 else 30)  
            except Exception as e:
                log(f"[LastFM] Streamer error: {e}")
                time.sleep(10)

    def _dismiss_dialog(self):
        global progress_dialog
        try:
            if progress_dialog is not None and progress_dialog.isShowing():
                progress_dialog.dismiss()
        except Exception:
            pass
        finally:
            progress_dialog = None

    def on_plugin_load(self):
        self.add_on_send_message_hook()
        self._temp_dir = self._get_temp_dir()
        if self._temp_dir:
            threading.Thread(target=self._download_fonts, daemon=True).start()
            log("LastFM plugin loaded successfully")
        else:
            log("Failed to initialize temp directory for LastFM")

    def _download_fonts(self):
        try:
            for font_name, font_url in FONTS.items():
                font_path = File(self._temp_dir, f"{font_name}-Regular.ttf").getAbsolutePath()
                if not os.path.exists(font_path):
                    response = requests.get(font_url)
                    with open(font_path, 'wb') as f:
                        f.write(response.content)
                    log(f"Downloaded font: {font_name}")
        except Exception as e:
            log(f"Error downloading fonts: {e}")

    def create_settings(self):
        lang = Locale.getDefault().getLanguage()
        update_bio = self.get_setting("update_bio", False)

        if lang.startswith('ru'):
            user_header = "Пользователь LastFM"
            user_input_text = "Имя пользователя"
            user_input_subtext = "Укажите ваше имя пользователя LastFM."
            photo_header = "Настройки отображения"
            photo_text = "Отправлять обложку альбома"
            photo_subtext = "Включите, чтобы отправлять обложку альбома вместе с информацией о треке."
            stream_header = "Поток в профиль"
            stream_text = "Стрим трека в профиль"
            stream_subtext = "Обновляет био/геолокацию текущим треком"
            stream_place_text = "Транслировать в ..."
            stream_place_item1 = "Био"
            stream_place_item2 = "Геолокацию (Рекомендуется)"
            stream_format_text = "Формат"
            stream_format_subtext = "Формат отображения трека. {title} — название трека, {artist} — артист"
            stream_default_text = "Текст по умолчанию"
            stream_default_subtext = "Отображается, если ничего не воспроизводится"
        else:
            user_header = "LastFM User"
            user_input_text = "Username"
            user_input_subtext = "Set your LastFM username."
            photo_header = "Display Settings"
            photo_text = "Send album cover"
            photo_subtext = "Enable to send album cover along with track information."
            stream_header = "Profile Streaming"
            stream_text = "Stream track to profile"
            stream_subtext = "Updates bio/location with currently playing track"
            stream_place_text = "Stream to..."
            stream_place_item1 = "Bio"
            stream_place_item2 = "Location (Recommended)"
            stream_format_text = "Format"
            stream_format_subtext = "Customize track display. {title} — track name, {artist} — artist"
            stream_default_text = "Default Text"
            stream_default_subtext = "Displayed when no track is playing"

        settings = [
            Header(text=user_header),
            Input(
                key="lastfm_username",
                text=user_input_text,
                default="",
                subtext=user_input_subtext,
                icon="menu_username_change"
            ),
            Header(text=photo_header),
            Switch(
                key="send_album_cover",
                text=photo_text,
                default=True,
                subtext=photo_subtext,
                icon="msg_photos"
            ),
            Divider(),
            Header(text=stream_header),
            Switch(
                key="update_bio",
                text=stream_text,
                default=False,
                subtext=stream_subtext,
                on_change=lambda new_value: self._show_stream_alert(new_value),
                icon="msg_online"
            ),
            Selector(
                key="stream_place",
                text=stream_place_text,
                default=1 if get_user_config().isPremium() else 0,
                items=[
                    stream_place_item1,
                    stream_place_item2,
                ],
                icon="menu_premium_location" if get_user_config().isPremium() else "msg_openprofile"
            ) if update_bio and get_user_config().isPremium() else None,
            Input(
                key="track_display_format",
                text=stream_format_text,
                default=DEFAULT_STREAM_STRING,
                subtext=stream_format_subtext,
                icon="msg_view_file"
            ) if update_bio else None,
            Input(
                key="default_stream_text",
                text=stream_default_text,
                default=DEFAULT_STREAM_TEXT,
                subtext=stream_default_subtext,
                icon="msg_photo_text_framed3"
            ) if update_bio else None,
        ]
        return [s for s in settings if s is not None]

    def _show_stream_alert(self, value):
        if value:
            lang = Locale.getDefault().getLanguage()
            if lang.startswith('ru'):
                title = "⚠️⚠️ВНИМАНИЕ⚠️⚠️"
                message = "Эта функция может работать нестабильно из-за ограничений Telegram на частую смену профиля. Ваши данные могут обновляться с задержкой. Используйте на свой страх и риск."
            else:
                title = "⚠️⚠️WARNING⚠️⚠️"
                message = "This feature may work inconsistently due to Telegram's profile change limits. Your profile information may not update immediately. Use at your own risk."
            fragment = get_last_fragment()
            ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
            dialog = AlertDialog(ctx, 3)
            dialog.setTitle(title)
            dialog.setMessage(message)
            dialog.setButton("OK", None)
            dialog.show()

    def get_temp_dir(self):
        try:
            base_dir = ApplicationLoader.getFilesDirFixed()
            if not base_dir:
                return None
            temp_dir = File(base_dir, TEMP_DIR_NAME)
            if not temp_dir.exists() and not temp_dir.mkdirs():
                return None
            return temp_dir
        except Exception as e:
            return None

    def download_album_image(self, image_url):
        if not image_url:
            return None
        temp_dir = self.get_temp_dir()
        if not temp_dir or not temp_dir.isDirectory():
            return None
        filename = f"album_{uuid.uuid4()}.jpg"
        temp_photo_path = File(temp_dir, filename).getAbsolutePath()
        try:
            head = requests.head(image_url, timeout=5)
            content_length = int(head.headers.get('content-length', 0))
            if content_length > 5 * 1024 * 1024:
                return None

            resp = requests.get(image_url, stream=True, timeout=10)
            resp.raise_for_status()
            with open(temp_photo_path, 'wb') as f:
                for chunk in resp.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            return temp_photo_path
        except Exception as e:
            try:
                if os.path.exists(temp_photo_path):
                    os.remove(temp_photo_path)
            except Exception:
                pass
            return None

    def delete_temp_file_async(self, file_path, delay_seconds=5):
        def _delete():
            try:
                time.sleep(delay_seconds)
                if os.path.exists(file_path):
                    os.remove(file_path)
            except Exception as e:
                pass
        threading.Thread(target=_delete, daemon=True).start()

    def on_send_message_hook(self, account, params):
        if not hasattr(params, 'message') or not isinstance(params.message, str):
            return HookResult()

        message_text = params.message.strip()
        command_prefix_now = ".np"

        lastfm_username = self.get_setting("lastfm_username", "")

        if message_text == command_prefix_now:
            if not lastfm_username or lastfm_username == "":
                response_text = "❌ Ник LastFM не установлен в настройках плагина."
                params.message = response_text
                return HookResult(strategy=HookStrategy.MODIFY, params=params)
            else:
                def search_and_reply(peer):
                    try:
                        current_track_url = f'http://ws.audioscrobbler.com/2.0/?method=user.getrecenttracks&nowplaying=true&user={lastfm_username}&api_key={API_KEY}&format=json'
                        response = requests.get(current_track_url)
                        
                        if response.status_code != 200:
                            send_message({
                                "peer": peer,
                                "message": f"❌ Ошибка API LastFM: HTTP {response.status_code}"
                            })
                            self._dismiss_dialog()
                            return
                            
                        try:
                            data = response.json()
                        except Exception as e:
                            send_message({
                                "peer": peer,
                                "message": "❌ Ошибка при обработке ответа от LastFM. Попробуйте позже."
                            })
                            self._dismiss_dialog()
                            return

                        if 'recenttracks' in data and 'track' in data['recenttracks'] and data['recenttracks']['track']:
                            nowplaying_track = None
                            for track in data['recenttracks']['track']:
                                if '@attr' in track and 'nowplaying' in track['@attr']:
                                    nowplaying_track = track
                                    break

                            if nowplaying_track:
                                track_info = {
                                    'name': nowplaying_track.get('name', 'Unknown Track'),
                                    'artist': nowplaying_track.get('artist', {}).get('#text', 'Unknown Artist'),
                                    'album_image': None
                                }

                                images = nowplaying_track.get('image', [])
                                for size in ['extralarge', 'large', 'medium', 'small']:
                                    for img in images:
                                        if img.get('size') == size:
                                            track_info['album_image'] = img.get('#text')
                                            if track_info['album_image'] and track_info['album_image'].strip():
                                                break
                                    if track_info['album_image']:
                                        break

                                # Create and send card
                                try:
                                    if self.get_setting("send_album_cover", True):
                                        temp_photo_path = self._make_card(track_info)
                                        if temp_photo_path:
                                            helper = get_send_messages_helper()
                                            generated_photo = helper.generatePhotoSizes(temp_photo_path, None)

                                            if generated_photo is not None:
                                                send_message({
                                                    "peer": peer,
                                                    "photo": generated_photo,
                                                    "path": temp_photo_path,
                                                    "caption": f"🎶 Сейчас играет: {track_info['name']} — {track_info['artist']}",
                                                    "message": None
                                                })
                                                self.delete_temp_file_async(temp_photo_path)
                                                self._dismiss_dialog()
                                                return HookResult(strategy=HookStrategy.CANCEL)
                                            else:
                                                self.delete_temp_file_async(temp_photo_path)
                                    else:
                                        send_message({
                                            "peer": peer,
                                            "message": f"🎶 Сейчас играет: {track_info['name']} — {track_info['artist']}"
                                        })
                                        self._dismiss_dialog()
                                        return HookResult(strategy=HookStrategy.CANCEL)
                                except Exception as e:
                                    send_message({
                                        "peer": peer,
                                        "message": f"❌ Ошибка при создании карточки: {e}"
                                    })
                                    self._dismiss_dialog()
                                    return HookResult(strategy=HookStrategy.CANCEL)

                            else:
                                send_message({
                                    "peer": peer,
                                    "message": "▶️ Сейчас ничего не играет."
                                })
                                self._dismiss_dialog()
                                return

                        else:
                            send_message({
                                "peer": peer,
                                "message": "🤷‍♂️ Не удалось получить информацию о треке. Проверьте ник LastFM в настройках."
                            })
                            self._dismiss_dialog()
                            return

                    except requests.exceptions.RequestException as e:
                        send_message({
                            "peer": peer,
                            "message": f"❌ Ошибка при запросе к LastFM API: {e}"
                        })
                        self._dismiss_dialog()
                        return
                    except Exception as e:
                        send_message({
                            "peer": peer,
                            "message": f"❌ Неожиданная ошибка: {e}"
                        })
                        self._dismiss_dialog()
                        return

                global progress_dialog
                try:
                    if progress_dialog is None or not progress_dialog.isShowing():
                        progress_dialog = AlertDialog(get_last_fragment().getParentActivity(), 3)
                        progress_dialog.setMessage("Загрузка...")
                        progress_dialog.show()
                except Exception as e:
                    pass

                threading.Thread(target=lambda: search_and_reply(params.peer), daemon=True).start()

                params.message = "Загрузка..."
                return HookResult(strategy=HookStrategy.CANCEL)

        return HookResult()

    def _make_card(self, track_info):
        width, height = 1440, 600
        background_color = "#000000"
        title_text_color = "#FFFFFF"
        subtext_color = "#A0A0A0"
        default_cover_url = "https://cdn2.iconfinder.com/data/icons/social-icon-3/512/social_style_3_lastfm-512.png"

        card = Image.new('RGB', (width, height), background_color)
        draw = ImageDraw.Draw(card)

        album_image_url = track_info.get('album_image')
        if not album_image_url or not album_image_url.strip():
            album_image_url = default_cover_url

        if album_image_url:
            try:
                thumb = requests.get(album_image_url, stream=True).raw
                background = Image.open(thumb)
                thumbnail = background.copy()
                thumbnail = thumbnail.resize((450, 450))
                mask = Image.new('L', thumbnail.size, 0)
                draw_mask = ImageDraw.Draw(mask)
                draw_mask.rounded_rectangle((0, 0, *thumbnail.size), 30, fill=255)
                thumbnail = thumbnail.copy()
                thumbnail.putalpha(mask)
                card.paste(thumbnail, (75, 75), thumbnail)
            except Exception as e:
                log(f"Error processing album cover: {e}")

        title = track_info.get('name', 'Unknown Track')
        artist = track_info.get('artist', 'Unknown Artist')
        
        title_font = ImageFont.truetype(File(self._temp_dir, "NotoSansJP-Regular.ttf").getAbsolutePath(), 50)
        max_width = width - 665
        title_lines = []
        current_line = []
        
        words = title.split()
        for word in words:
            test_line = ' '.join(current_line + [word])
            bbox = draw.textbbox((0, 0), test_line, font=title_font)
            if bbox[2] - bbox[0] <= max_width:
                current_line.append(word)
            else:
                if current_line:
                    title_lines.append(' '.join(current_line))
                current_line = [word]
        if current_line:
            title_lines.append(' '.join(current_line))
            
        if len(title_lines) > 2:
            title_lines = title_lines[:2]
            title_lines[1] = title_lines[1][:title_lines[1].rfind(' ')] + '...'
        
        y_position = 85
        for line in title_lines:
            draw.text((590, y_position), line, font=title_font, fill=title_text_color)
            y_position += 60
        
        artist_font = ImageFont.truetype(File(self._temp_dir, "NotoSansJP-Regular.ttf").getAbsolutePath(), 35)
        artist_lines = []
        current_line = []
        
        words = artist.split()
        for word in words:
            test_line = ' '.join(current_line + [word])
            bbox = draw.textbbox((0, 0), test_line, font=artist_font)
            if bbox[2] - bbox[0] <= max_width:
                current_line.append(word)
            else:
                if current_line:
                    artist_lines.append(' '.join(current_line))
                current_line = [word]
        if current_line:
            artist_lines.append(' '.join(current_line))
            
        if len(artist_lines) > 1:
            artist_lines = [artist_lines[0][:artist_lines[0].rfind(' ')] + '...']
        
        draw.text((590, y_position + 10), artist_lines[0], font=artist_font, fill=subtext_color)

        info_font = ImageFont.truetype(File(self._temp_dir, "NotoSansJP-Regular.ttf").getAbsolutePath(), 35)
        draw.text((590, 415), "Now Playing", font=info_font, fill=subtext_color)

        plugins_font = ImageFont.truetype(File(self._temp_dir, "NotoSansJP-Regular.ttf").getAbsolutePath(), 30)
        draw.text((590, 470), "More Plugins in @KangelPlugins", font=plugins_font, fill=subtext_color)

        filename = f"now_lastfm.png"
        temp_photo_path = File(self._temp_dir, filename).getAbsolutePath()
        card.save(temp_photo_path)
        return temp_photo_path

    def _get_temp_dir(self):
        try:
            base_dir = ApplicationLoader.getFilesDirFixed()
            if not base_dir:
                return None
            temp_dir = File(base_dir, TEMP_DIR_NAME)
            if not temp_dir.exists() and not temp_dir.mkdirs():
                return None
            return temp_dir
        except Exception as e:
            log(f"Error getting temp directory: {e}")
            return None 