// This file was generated by
//     //third_party/jni_zero/jni_zero.py
// For
//     org.jni_zero.JniInit

#ifndef org_jni_1zero_JniInit_JNI
#define org_jni_1zero_JniInit_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "../../../../../../../third_party/jni_zero/jni_zero_internal.h"

// Class Accessors
#ifndef org_jni_1zero_JniInit_clazz_defined
#define org_jni_1zero_JniInit_clazz_defined
inline jclass org_jni_1zero_JniInit_clazz(JNIEnv* env) {
  static const char kClassName[] = "org/jni_zero/JniInit";
  static std::atomic<jclass> cached_class;
  return jni_zero::internal::LazyGetClass(env, kClassName, &cached_class);
}
#endif


namespace jni_zero {

// Native to Java functions
static void Java_JniInit_crashIfMultiplexingMisaligned(
    JNIEnv* env,
    jlong wholeHash,
    jlong priorityHash) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = org_jni_1zero_JniInit_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "crashIfMultiplexingMisaligned",
      "(JJ)V",
      &cached_method_id);
  env->CallStaticVoidMethod(clazz, call_context.method_id(), wholeHash, priorityHash);
}



}  // namespace jni_zero

#endif  // org_jni_1zero_JniInit_JNI
