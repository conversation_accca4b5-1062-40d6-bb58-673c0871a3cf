// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     java/math/BigInteger

#ifndef java_math_BigInteger_JNI
#define java_math_BigInteger_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_java_math_BigInteger[];
const char kClassPath_java_math_BigInteger[] = "java/math/BigInteger";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_java_math_BigInteger_clazz(nullptr);
#ifndef java_math_BigInteger_clazz_defined
#define java_math_BigInteger_clazz_defined
inline jclass java_math_BigInteger_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_java_math_BigInteger,
      &g_java_math_BigInteger_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace JNI_BigInteger {


static std::atomic<jmethodID>
    g_java_math_BigInteger_Constructor__int__int__java_util_Random3(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_BigInteger_Constructor__int__int__java_util_Random(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1,
    const jni_zero::JavaRef<jobject>& p2);
static jni_zero::ScopedJavaLocalRef<jobject>
    Java_BigInteger_Constructor__int__int__java_util_Random(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1,
    const jni_zero::JavaRef<jobject>& p2) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(IILjava/util/Random;)V",
          &g_java_math_BigInteger_Constructor__int__int__java_util_Random3);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, as_jint(p0), as_jint(p1), p2.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_Constructor__int__java_util_Random2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_BigInteger_Constructor__int__java_util_Random(JNIEnv* env, JniIntWrapper p0,
    const jni_zero::JavaRef<jobject>& p1);
static jni_zero::ScopedJavaLocalRef<jobject>
    Java_BigInteger_Constructor__int__java_util_Random(JNIEnv* env, JniIntWrapper p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(ILjava/util/Random;)V",
          &g_java_math_BigInteger_Constructor__int__java_util_Random2);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, as_jint(p0), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_Constructor__String1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_BigInteger_Constructor__String(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_Constructor__String(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Ljava/lang/String;)V",
          &g_java_math_BigInteger_Constructor__String1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_Constructor__String__int2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_BigInteger_Constructor__String__int(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_Constructor__String__int(JNIEnv* env,
    const jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Ljava/lang/String;I)V",
          &g_java_math_BigInteger_Constructor__String__int2);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, p0.obj(), as_jint(p1));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_Constructor__int__byteArray2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_BigInteger_Constructor__int__byteArray(JNIEnv* env, JniIntWrapper p0,
    const jni_zero::JavaRef<jbyteArray>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_Constructor__int__byteArray(JNIEnv*
    env, JniIntWrapper p0,
    const jni_zero::JavaRef<jbyteArray>& p1) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(I[B)V",
          &g_java_math_BigInteger_Constructor__int__byteArray2);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, as_jint(p0), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_Constructor4(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_Constructor(JNIEnv*
    env, JniIntWrapper p0,
    const jni_zero::JavaRef<jbyteArray>& p1,
    JniIntWrapper p2,
    JniIntWrapper p3);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_Constructor(JNIEnv* env, JniIntWrapper
    p0,
    const jni_zero::JavaRef<jbyteArray>& p1,
    JniIntWrapper p2,
    JniIntWrapper p3) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(I[BII)V",
          &g_java_math_BigInteger_Constructor4);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, as_jint(p0), p1.obj(), as_jint(p2), as_jint(p3));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_Constructor__byteArray1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_BigInteger_Constructor__byteArray(JNIEnv* env, const jni_zero::JavaRef<jbyteArray>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_Constructor__byteArray(JNIEnv* env,
    const jni_zero::JavaRef<jbyteArray>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "([B)V",
          &g_java_math_BigInteger_Constructor__byteArray1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_Constructor__byteArray__int__int3(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_BigInteger_Constructor__byteArray__int__int(JNIEnv* env, const
    jni_zero::JavaRef<jbyteArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2);
static jni_zero::ScopedJavaLocalRef<jobject>
    Java_BigInteger_Constructor__byteArray__int__int(JNIEnv* env, const
    jni_zero::JavaRef<jbyteArray>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "([BII)V",
          &g_java_math_BigInteger_Constructor__byteArray__int__int3);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, p0.obj(), as_jint(p1), as_jint(p2));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_abs0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_abs(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_abs(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "abs",
          "()Ljava/math/BigInteger;",
          &g_java_math_BigInteger_abs0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_add1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_add(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_add(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "add",
          "(Ljava/math/BigInteger;)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_add1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_and1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_and(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_and(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "and",
          "(Ljava/math/BigInteger;)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_and1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_andNot1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_andNot(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_andNot(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "andNot",
          "(Ljava/math/BigInteger;)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_andNot1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_bitCount0(nullptr);
[[maybe_unused]] static jint Java_BigInteger_bitCount(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jint Java_BigInteger_bitCount(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "bitCount",
          "()I",
          &g_java_math_BigInteger_bitCount0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_bitLength0(nullptr);
[[maybe_unused]] static jint Java_BigInteger_bitLength(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jint Java_BigInteger_bitLength(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "bitLength",
          "()I",
          &g_java_math_BigInteger_bitLength0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_byteValueExact0(nullptr);
[[maybe_unused]] static jbyte Java_BigInteger_byteValueExact(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jbyte Java_BigInteger_byteValueExact(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "byteValueExact",
          "()B",
          &g_java_math_BigInteger_byteValueExact0);

  jbyte ret =
      env->CallByteMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_clearBit1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_clearBit(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_clearBit(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "clearBit",
          "(I)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_clearBit1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_compareTo__Object1(nullptr);
[[maybe_unused]] static jint Java_BigInteger_compareTo__Object(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jint Java_BigInteger_compareTo__Object(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "compareTo",
          "(Ljava/lang/Object;)I",
          &g_java_math_BigInteger_compareTo__Object1);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_compareTo__BigInteger1(nullptr);
[[maybe_unused]] static jint Java_BigInteger_compareTo__BigInteger(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jint Java_BigInteger_compareTo__BigInteger(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "compareTo",
          "(Ljava/math/BigInteger;)I",
          &g_java_math_BigInteger_compareTo__BigInteger1);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_divide1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_divide(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_divide(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "divide",
          "(Ljava/math/BigInteger;)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_divide1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_divideAndRemainder1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobjectArray>
    Java_BigInteger_divideAndRemainder(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobjectArray> Java_BigInteger_divideAndRemainder(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "divideAndRemainder",
          "(Ljava/math/BigInteger;)[Ljava/math/BigInteger;",
          &g_java_math_BigInteger_divideAndRemainder1);

  jobjectArray ret =
      static_cast<jobjectArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj()));
  return jni_zero::ScopedJavaLocalRef<jobjectArray>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_doubleValue0(nullptr);
[[maybe_unused]] static jdouble Java_BigInteger_doubleValue(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jdouble Java_BigInteger_doubleValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "doubleValue",
          "()D",
          &g_java_math_BigInteger_doubleValue0);

  jdouble ret =
      env->CallDoubleMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_equals1(nullptr);
[[maybe_unused]] static jboolean Java_BigInteger_equals(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_BigInteger_equals(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "equals",
          "(Ljava/lang/Object;)Z",
          &g_java_math_BigInteger_equals1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_flipBit1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_flipBit(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_flipBit(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "flipBit",
          "(I)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_flipBit1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_floatValue0(nullptr);
[[maybe_unused]] static jfloat Java_BigInteger_floatValue(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jfloat Java_BigInteger_floatValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "floatValue",
          "()F",
          &g_java_math_BigInteger_floatValue0);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_gcd1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_gcd(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_gcd(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "gcd",
          "(Ljava/math/BigInteger;)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_gcd1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_getLowestSetBit0(nullptr);
[[maybe_unused]] static jint Java_BigInteger_getLowestSetBit(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jint Java_BigInteger_getLowestSetBit(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getLowestSetBit",
          "()I",
          &g_java_math_BigInteger_getLowestSetBit0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_hashCode0(nullptr);
[[maybe_unused]] static jint Java_BigInteger_hashCode(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jint Java_BigInteger_hashCode(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "hashCode",
          "()I",
          &g_java_math_BigInteger_hashCode0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_intValue0(nullptr);
[[maybe_unused]] static jint Java_BigInteger_intValue(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jint Java_BigInteger_intValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "intValue",
          "()I",
          &g_java_math_BigInteger_intValue0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_intValueExact0(nullptr);
[[maybe_unused]] static jint Java_BigInteger_intValueExact(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jint Java_BigInteger_intValueExact(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "intValueExact",
          "()I",
          &g_java_math_BigInteger_intValueExact0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_isProbablePrime1(nullptr);
[[maybe_unused]] static jboolean Java_BigInteger_isProbablePrime(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0);
static jboolean Java_BigInteger_isProbablePrime(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    JniIntWrapper p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "isProbablePrime",
          "(I)Z",
          &g_java_math_BigInteger_isProbablePrime1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_longValue0(nullptr);
[[maybe_unused]] static jlong Java_BigInteger_longValue(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jlong Java_BigInteger_longValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "longValue",
          "()J",
          &g_java_math_BigInteger_longValue0);

  jlong ret =
      env->CallLongMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_longValueExact0(nullptr);
[[maybe_unused]] static jlong Java_BigInteger_longValueExact(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jlong Java_BigInteger_longValueExact(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "longValueExact",
          "()J",
          &g_java_math_BigInteger_longValueExact0);

  jlong ret =
      env->CallLongMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_max1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_max(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_max(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "max",
          "(Ljava/math/BigInteger;)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_max1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_min1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_min(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_min(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "min",
          "(Ljava/math/BigInteger;)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_min1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_mod1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_mod(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_mod(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "mod",
          "(Ljava/math/BigInteger;)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_mod1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_modInverse1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_modInverse(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_modInverse(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "modInverse",
          "(Ljava/math/BigInteger;)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_modInverse1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_modPow2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_modPow(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_modPow(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "modPow",
          "(Ljava/math/BigInteger;Ljava/math/BigInteger;)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_modPow2);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj(), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_multiply1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_multiply(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_multiply(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "multiply",
          "(Ljava/math/BigInteger;)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_multiply1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_negate0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_negate(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_negate(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "negate",
          "()Ljava/math/BigInteger;",
          &g_java_math_BigInteger_negate0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_nextProbablePrime0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_BigInteger_nextProbablePrime(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_nextProbablePrime(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "nextProbablePrime",
          "()Ljava/math/BigInteger;",
          &g_java_math_BigInteger_nextProbablePrime0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_not0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_not(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_not(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "not",
          "()Ljava/math/BigInteger;",
          &g_java_math_BigInteger_not0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_or1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_or(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_or(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "or",
          "(Ljava/math/BigInteger;)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_or1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_pow1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_pow(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_pow(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "pow",
          "(I)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_pow1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_probablePrime2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_probablePrime(JNIEnv*
    env, JniIntWrapper p0,
    const jni_zero::JavaRef<jobject>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_probablePrime(JNIEnv* env,
    JniIntWrapper p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "probablePrime",
          "(ILjava/util/Random;)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_probablePrime2);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(p0), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_remainder1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_remainder(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_remainder(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "remainder",
          "(Ljava/math/BigInteger;)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_remainder1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_setBit1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_setBit(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_setBit(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "setBit",
          "(I)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_setBit1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_shiftLeft1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_shiftLeft(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_shiftLeft(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "shiftLeft",
          "(I)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_shiftLeft1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_shiftRight1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_shiftRight(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_shiftRight(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "shiftRight",
          "(I)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_shiftRight1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_shortValueExact0(nullptr);
[[maybe_unused]] static jshort Java_BigInteger_shortValueExact(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jshort Java_BigInteger_shortValueExact(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "shortValueExact",
          "()S",
          &g_java_math_BigInteger_shortValueExact0);

  jshort ret =
      env->CallShortMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_signum0(nullptr);
[[maybe_unused]] static jint Java_BigInteger_signum(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jint Java_BigInteger_signum(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "signum",
          "()I",
          &g_java_math_BigInteger_signum0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_sqrt0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_sqrt(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_sqrt(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "sqrt",
          "()Ljava/math/BigInteger;",
          &g_java_math_BigInteger_sqrt0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_sqrtAndRemainder0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobjectArray>
    Java_BigInteger_sqrtAndRemainder(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobjectArray> Java_BigInteger_sqrtAndRemainder(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "sqrtAndRemainder",
          "()[Ljava/math/BigInteger;",
          &g_java_math_BigInteger_sqrtAndRemainder0);

  jobjectArray ret =
      static_cast<jobjectArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jobjectArray>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_subtract1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_subtract(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_subtract(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "subtract",
          "(Ljava/math/BigInteger;)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_subtract1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_testBit1(nullptr);
[[maybe_unused]] static jboolean Java_BigInteger_testBit(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0);
static jboolean Java_BigInteger_testBit(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    JniIntWrapper p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "testBit",
          "(I)Z",
          &g_java_math_BigInteger_testBit1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_java_math_BigInteger_toByteArray0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jbyteArray> Java_BigInteger_toByteArray(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jbyteArray> Java_BigInteger_toByteArray(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "toByteArray",
          "()[B",
          &g_java_math_BigInteger_toByteArray0);

  jbyteArray ret =
      static_cast<jbyteArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jbyteArray>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_toString0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_BigInteger_toString(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jstring> Java_BigInteger_toString(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "toString",
          "()Ljava/lang/String;",
          &g_java_math_BigInteger_toString0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_toString1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_BigInteger_toString(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jstring> Java_BigInteger_toString(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "toString",
          "(I)Ljava/lang/String;",
          &g_java_math_BigInteger_toString1);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0)));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_valueOf1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_valueOf(JNIEnv* env,
    jlong p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_valueOf(JNIEnv* env, jlong p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "valueOf",
          "(J)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_valueOf1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_math_BigInteger_xor1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_xor(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BigInteger_xor(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_math_BigInteger_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_math_BigInteger_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "xor",
          "(Ljava/math/BigInteger;)Ljava/math/BigInteger;",
          &g_java_math_BigInteger_xor1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace JNI_BigInteger

#endif  // java_math_BigInteger_JNI
