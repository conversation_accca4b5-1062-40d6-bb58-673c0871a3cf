// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/VideoDecoderFactory

#ifndef org_webrtc_VideoDecoderFactory_JNI
#define org_webrtc_VideoDecoderFactory_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_VideoDecoderFactory[];
const char kClassPath_org_webrtc_VideoDecoderFactory[] = "org/webrtc/VideoDecoderFactory";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_VideoDecoderFactory_clazz(nullptr);
#ifndef org_webrtc_VideoDecoderFactory_clazz_defined
#define org_webrtc_VideoDecoderFactory_clazz_defined
inline jclass org_webrtc_VideoDecoderFactory_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_VideoDecoderFactory,
      &g_org_webrtc_VideoDecoderFactory_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_VideoDecoderFactory_createDecoder1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoDecoderFactory_createDecoder(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& info) {
  jclass clazz = org_webrtc_VideoDecoderFactory_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoDecoderFactory_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "createDecoder",
          "(Lorg/webrtc/VideoCodecInfo;)Lorg/webrtc/VideoDecoder;",
          &g_org_webrtc_VideoDecoderFactory_createDecoder1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, info.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoDecoderFactory_getSupportedCodecs0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobjectArray>
    Java_VideoDecoderFactory_getSupportedCodecs(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj)
    {
  jclass clazz = org_webrtc_VideoDecoderFactory_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoDecoderFactory_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getSupportedCodecs",
          "()[Lorg/webrtc/VideoCodecInfo;",
          &g_org_webrtc_VideoDecoderFactory_getSupportedCodecs0);

  jobjectArray ret =
      static_cast<jobjectArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jobjectArray>(env, ret);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_VideoDecoderFactory_JNI
