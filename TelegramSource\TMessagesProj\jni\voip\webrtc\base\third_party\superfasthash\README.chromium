Name: <PERSON>'s SuperFastHash
Short Name: SuperFastHash
URL: http://www.azillionmonkeys.com/qed/hash.html
Version: 0
Date: 2012-02-21
License: BSD
License File: LICENSE
Security Critical: yes

Description:
A fast string hashing algorithm.

Local Modifications:
- Added LICENSE.
- Added license text as a comment to the top of superfasthash.c.
- #include <stdint.h> instead of "pstdint.h".
- #include <stdlib.h>.

The license is a standard 3-clause BSD license with the following minor changes:

"nor the names of its contributors may be used"
is replaced with:
"nor the names of any other contributors to the code use may not be used"

and

"IN NO EVENT SHALL <COPYRIGHT HOLDER> BE LIABLE"
is replaced with:
"IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE"
