"""
UIHider Plugin for exteraGram
Allows hiding profile avatars, usernames, and dates in Telegram interface
"""

from base_plugin import BasePlugin
from ui.settings import Header, Switch, Text
from android_utils import log
from hook_utils import find_class, get_private_field, set_private_field
from client_utils import get_user_config
from android.view import View
from android.graphics import Canvas

__name__ = "UIHider"
__description__ = "Скрывает аватарки, имена и даты в интерфейсе Telegram"
__icon__ = "baseline_visibility_off_24"
__id__ = "exteraUIHider"
__version__ = "1.0.1"
__author__ = "@exteraGram"
__min_version__ = "11.9.1"

class UIHiderPlugin(BasePlugin):
    def __init__(self):
        super().__init__()

        # Hook references for cleanup
        self.hook_refs = []

        # Settings
        self.hide_avatars = self.get_setting("hide_avatars", False)
        self.hide_names = self.get_setting("hide_names", False)
        self.hide_dates = self.get_setting("hide_dates", False)
        self.hide_chat_list_avatars = self.get_setting("hide_chat_list_avatars", False)
        self.hide_profile_avatars = self.get_setting("hide_profile_avatars", False)
        self.hide_header_avatars = self.get_setting("hide_header_avatars", False)

    def on_plugin_load(self):
        """Called when plugin is loaded"""
        log("UIHider plugin loaded")
        self.setup_hooks()

    def on_plugin_unload(self):
        """Called when plugin is unloaded"""
        log("UIHider plugin unloaded")
        self.cleanup_hooks()

    def cleanup_hooks(self):
        """Clean up all hooks"""
        for hook_ref in self.hook_refs:
            try:
                self.unhook_method(hook_ref)
            except Exception as e:
                log(f"Error unhooking: {e}")
        self.hook_refs.clear()

    def setup_hooks(self):
        """Setup Xposed hooks for hiding UI elements"""
        try:
            # Clean up existing hooks first
            self.cleanup_hooks()

            # Hook ChatMessageCell onDraw method
            chat_message_cell_class = find_class("org.telegram.ui.Cells.ChatMessageCell")
            if chat_message_cell_class:
                try:
                    canvas_class = find_class("android.graphics.Canvas")
                    on_draw_method = chat_message_cell_class.getDeclaredMethod("onDraw", canvas_class)
                    hook_ref = self.hook_method(on_draw_method, ChatMessageCellDrawHook(self))
                    if hook_ref:
                        self.hook_refs.append(hook_ref)
                        log("Hooked ChatMessageCell.onDraw")
                except Exception as e:
                    log(f"Failed to hook ChatMessageCell.onDraw: {e}")

            # Hook DialogCell onDraw method for chat list
            dialog_cell_class = find_class("org.telegram.ui.Cells.DialogCell")
            if dialog_cell_class:
                try:
                    canvas_class = find_class("android.graphics.Canvas")
                    on_draw_method = dialog_cell_class.getDeclaredMethod("onDraw", canvas_class)
                    hook_ref = self.hook_method(on_draw_method, DialogCellDrawHook(self))
                    if hook_ref:
                        self.hook_refs.append(hook_ref)
                        log("Hooked DialogCell.onDraw")
                except Exception as e:
                    log(f"Failed to hook DialogCell.onDraw: {e}")

            # Hook ChatAvatarContainer for header avatars
            chat_avatar_container_class = find_class("org.telegram.ui.Components.ChatAvatarContainer")
            if chat_avatar_container_class:
                try:
                    canvas_class = find_class("android.graphics.Canvas")
                    on_draw_method = chat_avatar_container_class.getDeclaredMethod("onDraw", canvas_class)
                    hook_ref = self.hook_method(on_draw_method, ChatAvatarContainerDrawHook(self))
                    if hook_ref:
                        self.hook_refs.append(hook_ref)
                        log("Hooked ChatAvatarContainer.onDraw")
                except Exception as e:
                    log(f"Failed to hook ChatAvatarContainer.onDraw: {e}")

            log("UIHider hooks setup completed")

        except Exception as e:
            log(f"Error setting up UIHider hooks: {e}")

    def create_settings(self):
        """Return plugin settings UI"""
        settings = []

        # Header
        settings.append(Header(text="Основные настройки"))

        # Hide avatars setting
        settings.append(Switch(
            text="Скрыть аватарки в сообщениях",
            subtext="Скрывает аватарки в сообщениях чата",
            key="hide_avatars",
            default=False,
            on_change=self.on_hide_avatars_changed
        ))

        # Hide names setting
        settings.append(Switch(
            text="Скрыть имена",
            subtext="Скрывает имена пользователей в сообщениях",
            key="hide_names",
            default=False,
            on_change=self.on_hide_names_changed
        ))

        # Hide dates setting
        settings.append(Switch(
            text="Скрыть даты",
            subtext="Скрывает время и даты сообщений",
            key="hide_dates",
            default=False,
            on_change=self.on_hide_dates_changed
        ))

        # Additional settings header
        settings.append(Header(text="Дополнительные настройки"))

        # Hide chat list avatars
        settings.append(Switch(
            text="Скрыть аватарки в списке чатов",
            subtext="Скрывает аватарки в главном списке чатов",
            key="hide_chat_list_avatars",
            default=False,
            on_change=self.on_hide_chat_list_avatars_changed
        ))

        # Hide profile avatars
        settings.append(Switch(
            text="Скрыть аватарки в профилях",
            subtext="Скрывает аватарки в поиске и профилях",
            key="hide_profile_avatars",
            default=False,
            on_change=self.on_hide_profile_avatars_changed
        ))

        # Hide header avatars
        settings.append(Switch(
            text="Скрыть аватарки в заголовках",
            subtext="Скрывает аватарки в заголовках чатов",
            key="hide_header_avatars",
            default=False,
            on_change=self.on_hide_header_avatars_changed
        ))

        settings.append(Text(
            text="⚠️ Изменения применяются после перезапуска чата",
            on_click=lambda: None
        ))

        return settings

    def on_hide_avatars_changed(self, value):
        """Called when hide avatars setting changes"""
        self.hide_avatars = value
        self.set_setting("hide_avatars", value)
        log(f"Hide avatars set to: {value}")
        # Reapply hooks with new settings
        self.setup_hooks()

    def on_hide_names_changed(self, value):
        """Called when hide names setting changes"""
        self.hide_names = value
        self.set_setting("hide_names", value)
        log(f"Hide names set to: {value}")

    def on_hide_dates_changed(self, value):
        """Called when hide dates setting changes"""
        self.hide_dates = value
        self.set_setting("hide_dates", value)
        log(f"Hide dates set to: {value}")

    def on_hide_chat_list_avatars_changed(self, value):
        """Called when hide chat list avatars setting changes"""
        self.hide_chat_list_avatars = value
        self.set_setting("hide_chat_list_avatars", value)
        log(f"Hide chat list avatars set to: {value}")
        # Reapply hooks with new settings
        self.setup_hooks()

    def on_hide_profile_avatars_changed(self, value):
        """Called when hide profile avatars setting changes"""
        self.hide_profile_avatars = value
        self.set_setting("hide_profile_avatars", value)
        log(f"Hide profile avatars set to: {value}")

    def on_hide_header_avatars_changed(self, value):
        """Called when hide header avatars setting changes"""
        self.hide_header_avatars = value
        self.set_setting("hide_header_avatars", value)
        log(f"Hide header avatars set to: {value}")
        # Reapply hooks with new settings
        self.setup_hooks()


# Hook classes for different UI elements
class ChatMessageCellDrawHook:
    def __init__(self, plugin):
        self.plugin = plugin

    def before_hooked_method(self, param):
        """Called before ChatMessageCell.onDraw"""
        try:
            this_obj = param.thisObject

            # Store original values for restoration
            self.original_values = {}

            # Hide avatars if enabled
            if self.plugin.hide_avatars:
                if hasattr(this_obj, 'isAvatarVisible'):
                    self.original_values['isAvatarVisible'] = this_obj.isAvatarVisible
                    this_obj.isAvatarVisible = False

                # Try to hide avatar image
                avatar_image = get_private_field(this_obj, 'avatarImage')
                if avatar_image:
                    self.original_values['avatarVisible'] = avatar_image.getVisible() if hasattr(avatar_image, 'getVisible') else True
                    if hasattr(avatar_image, 'setVisible'):
                        avatar_image.setVisible(False, False)

            # Hide names if enabled
            if self.plugin.hide_names:
                # Try to hide name layout
                if hasattr(this_obj, 'drawNameLayout'):
                    self.original_values['drawNameLayout'] = this_obj.drawNameLayout
                    this_obj.drawNameLayout = False

            # Hide dates/time if enabled
            if self.plugin.hide_dates:
                # Try to hide time
                if hasattr(this_obj, 'drawTime'):
                    self.original_values['drawTime'] = this_obj.drawTime
                    this_obj.drawTime = False

        except Exception as e:
            log(f"Error in ChatMessageCellDrawHook.before_hooked_method: {e}")

    def after_hooked_method(self, param):
        """Called after ChatMessageCell.onDraw"""
        try:
            this_obj = param.thisObject

            # Restore original values
            if hasattr(self, 'original_values'):
                if 'isAvatarVisible' in self.original_values:
                    this_obj.isAvatarVisible = self.original_values['isAvatarVisible']

                if 'drawNameLayout' in self.original_values:
                    this_obj.drawNameLayout = self.original_values['drawNameLayout']

                if 'drawTime' in self.original_values:
                    this_obj.drawTime = self.original_values['drawTime']

                if 'avatarVisible' in self.original_values:
                    avatar_image = get_private_field(this_obj, 'avatarImage')
                    if avatar_image and hasattr(avatar_image, 'setVisible'):
                        avatar_image.setVisible(self.original_values['avatarVisible'], False)

        except Exception as e:
            log(f"Error in ChatMessageCellDrawHook.after_hooked_method: {e}")


class DialogCellDrawHook:
    def __init__(self, plugin):
        self.plugin = plugin

    def before_hooked_method(self, param):
        """Called before DialogCell.onDraw"""
        try:
            if not (self.plugin.hide_avatars or self.plugin.hide_chat_list_avatars):
                return

            this_obj = param.thisObject
            self.original_values = {}

            # Hide avatar in chat list
            avatar_image = get_private_field(this_obj, 'avatarImage')
            if avatar_image:
                self.original_values['avatarVisible'] = avatar_image.getVisible() if hasattr(avatar_image, 'getVisible') else True
                if hasattr(avatar_image, 'setVisible'):
                    avatar_image.setVisible(False, False)

        except Exception as e:
            log(f"Error in DialogCellDrawHook.before_hooked_method: {e}")

    def after_hooked_method(self, param):
        """Called after DialogCell.onDraw"""
        try:
            if hasattr(self, 'original_values') and 'avatarVisible' in self.original_values:
                this_obj = param.thisObject
                avatar_image = get_private_field(this_obj, 'avatarImage')
                if avatar_image and hasattr(avatar_image, 'setVisible'):
                    avatar_image.setVisible(self.original_values['avatarVisible'], False)

        except Exception as e:
            log(f"Error in DialogCellDrawHook.after_hooked_method: {e}")


class ChatAvatarContainerDrawHook:
    def __init__(self, plugin):
        self.plugin = plugin

    def before_hooked_method(self, param):
        """Called before ChatAvatarContainer.onDraw"""
        try:
            if not (self.plugin.hide_avatars or self.plugin.hide_header_avatars):
                return

            this_obj = param.thisObject
            self.original_values = {}

            # Hide avatar in header
            avatar_image_view = get_private_field(this_obj, 'avatarImageView')
            if avatar_image_view:
                self.original_values['avatarVisibility'] = avatar_image_view.getVisibility()
                avatar_image_view.setVisibility(View.GONE)

        except Exception as e:
            log(f"Error in ChatAvatarContainerDrawHook.before_hooked_method: {e}")

    def after_hooked_method(self, param):
        """Called after ChatAvatarContainer.onDraw"""
        try:
            if hasattr(self, 'original_values') and 'avatarVisibility' in self.original_values:
                this_obj = param.thisObject
                avatar_image_view = get_private_field(this_obj, 'avatarImageView')
                if avatar_image_view:
                    avatar_image_view.setVisibility(self.original_values['avatarVisibility'])

        except Exception as e:
            log(f"Error in ChatAvatarContainerDrawHook.after_hooked_method: {e}")


# Create plugin instance
plugin_instance = UIHiderPlugin()
