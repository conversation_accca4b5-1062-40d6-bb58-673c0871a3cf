{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 454, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "MissingZero": "Some implementations of ECDSA and DSA incorrectly encode r and s by not including leading zeros in the ASN encoding of integers when necessary. Hence, some implementations (e.g. jdk) allow signatures with incorrect ASN encodings assuming that the signature is otherwise valid.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b838ff44e5bc177bf21189d0766082fc9d843226887fc9760371100b7ee20a6ff0c9d75bfba7b31a6bca1974496eeb56de357071955d83c4b1badaa0b21832e9", "wx": "00b838ff44e5bc177bf21189d0766082fc9d843226887fc9760371100b7ee20a6f", "wy": "00f0c9d75bfba7b31a6bca1974496eeb56de357071955d83c4b1badaa0b21832e9"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004b838ff44e5bc177bf21189d0766082fc9d843226887fc9760371100b7ee20a6ff0c9d75bfba7b31a6bca1974496eeb56de357071955d83c4b1badaa0b21832e9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEuDj/ROW8F3vyEYnQdmCC/J2EMiaIf8l2\nA3EQC37iCm/wyddb+6ezGmvKGXRJbutW3jVwcZVdg8Sxutqgshgy6Q==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "304402207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022059f100a7e4a774cf8f04577ebd9ab9ab2f09cfc5a6be10ffd0338524e6c26caa", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Legacy:ASN encoding of s misses leading 0", "msg": "313233343030", "sig": "304402207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60220a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 3, "comment": "valid", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "valid", "flags": []}, {"tcId": 4, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "30814502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": ["BER"]}, {"tcId": 5, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "3082004502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": ["BER"]}, {"tcId": 6, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "304602207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "304402207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "3085010000004502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "308901000000000000004502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff02207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff02207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff02207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff02207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff02207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "308002207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "304502807d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6028000a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "304702207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4970000", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "3047000002207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4970000", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "304702207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4970500", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "including garbage", "msg": "313233343030", "sig": "304a498177304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "30492500304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "3047304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4970004deadbeef", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "304a222549817702207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "30492224250002207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "304d222202207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60004deadbeef022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "304a02207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe62226498177022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "304902207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe622252500022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including garbage", "msg": "313233343030", "sig": "304d02207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe62223022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4970004deadbeef", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including undefined tags", "msg": "313233343030", "sig": "304daa00bb00cd00304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "304baa02aabb304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "304d2228aa00bb00cd0002207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "304b2226aa02aabb02207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "304d02207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe62229aa00bb00cd00022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "including undefined tags", "msg": "313233343030", "sig": "304b02207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe62227aa02aabb022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3080304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4970000", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3049228002207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60000022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "304902207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe62280022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4970000", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3080314502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4970000", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3049228003207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60000022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "304902207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe62280032100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4970000", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e4502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f4502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "314502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "324502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff4502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "using composition for sequence", "msg": "313233343030", "sig": "30493001023044207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "truncated sequence", "msg": "313233343030", "sig": "304402207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "truncated sequence", "msg": "313233343030", "sig": "3044207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "indefinite length", "msg": "313233343030", "sig": "308002207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4970000", "result": "invalid", "flags": ["BER"]}, {"tcId": 57, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "308002207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d49700", "result": "invalid", "flags": []}, {"tcId": 58, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "308002207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d49705000000", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "308002207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497060811220000", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "308002207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4970000fe02beef", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "308002207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4970002beef", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "3047300002207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "append empty sequence", "msg": "313233343030", "sig": "304702207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4973000", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "304802207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497bf7f00", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "sequence of sequence", "msg": "313233343030", "sig": "3047304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "302202207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "306802207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "30460281207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": ["BER"]}, {"tcId": 69, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "304602207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe602812100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "3047028200207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "304702207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60282002100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": ["BER"]}, {"tcId": 72, "comment": "wrong length of integer", "msg": "313233343030", "sig": "304502217d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "3045021f7d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022200a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "wrong length of integer", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022000a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "304a028501000000207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "304a02207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60285010000002100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "304e02890100000000000000207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "304e02207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6028901000000000000002100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304902847fffffff7d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304902207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe602847fffffff00a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30490284ffffffff7d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "304902207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60284ffffffff00a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "304a0285ffffffffff7d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "304a02207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60285ffffffffff00a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "304d0288ffffffffffffffff7d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "304d02207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60288ffffffffffffffff00a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "304502ff7d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe602ff00a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "removing integer", "msg": "313233343030", "sig": "3023022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "lonely integer tag", "msg": "313233343030", "sig": "302402022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "lonely integer tag", "msg": "313233343030", "sig": "302302207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe602", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "304702227d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60000022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "304702207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022300a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4970000", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "3047022200007d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": ["BER"]}, {"tcId": 96, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "304702207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60223000000a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": ["BER"]}, {"tcId": 97, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "304702207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60000022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 98, "comment": "appending null value to integer", "msg": "313233343030", "sig": "304702227d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60500022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "appending null value to integer", "msg": "313233343030", "sig": "304702207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022300a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4970500", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "truncated length of integer", "msg": "313233343030", "sig": "30250281022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "truncated length of integer", "msg": "313233343030", "sig": "302402207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60281", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "30250500022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "302402207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60500", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304500207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304501207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304503207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304504207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3045ff207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6002100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6012100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6032100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6042100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6ff2100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "dropping value of integer", "msg": "313233343030", "sig": "30250200022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "dropping value of integer", "msg": "313233343030", "sig": "302402207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60200", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "using composition for integer", "msg": "313233343030", "sig": "3049222402017d021f68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "using composition for integer", "msg": "313233343030", "sig": "304902207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe622250201000220a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "304502207f68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022102a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32ab66022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d417", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "truncated integer", "msg": "313233343030", "sig": "3044021f7d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32ab022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "3044021f68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "313233343030", "sig": "304402207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022000a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d4", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "30460221ff7d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "313233343030", "sig": "304602207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60222ff00a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3026090180022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "302502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3026020100022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "302502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30460221017d68757ac197624ae5c77dfa1b3bdda5c3a93710ad3a9c1137198b5a1f68ed27022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30460221ff7d68757ac197624ae5c77dfa1b3bdda84e4b7d434ea95b99b774ce407efc6aa5022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "3045022082978a853e689db51a388205e4c42258f705a5d6020e042a88b8d332b0cd541a022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304602210082978a853e689db51a388205e4c42257b1b482bcb156a466488b31bf8103955b022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30460221fe82978a853e689db51a388205e4c4225a3c56c8ef52c563eec8e674a5e09712d9022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "30460221017d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304602210082978a853e689db51a388205e4c42258f705a5d6020e042a88b8d332b0cd541a022100a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022101a60eff581b588b3070fba881426546524653ea07b7d32f77af7137f4b9aa15d8", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304402207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60220a60eff581b588b3070fba88142654654d0f6303a5941ef002fcc7adb193d9356", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60221ff59f100a7e4a774cf8f04577ebd9ab9ac745af2def77570c410612698168c2b69", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe60221fe59f100a7e4a774cf8f04577ebd9ab9adb9ac15f8482cd088508ec80b4655ea28", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304502207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022101a60eff581b588b3070fba881426546538ba50d21088a8f3bef9ed967e973d497", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "304402207d68757ac197624ae5c77dfa1b3bdda708fa5a29fdf1fbd577472ccd4f32abe6022059f100a7e4a774cf8f04577ebd9ab9ac745af2def77570c410612698168c2b69", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020100022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30260201ff022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641410201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641400201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641420201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc300201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3028022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "37363338", "sig": "3046022100dd1b7d09a7bd8218961034a39a87fecf5314f00c4d25eb58a07ac85e85eab516022100f5133b41774a185247720d2aa5d8826b6ec5af4c076936c8eaa52ed6cdf59408", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "32373239373236343137", "sig": "3046022100904b2bb6e9de8f73243fa7ecc19a5a9fe034acad2b75b97c8cc84a79c5f3577402210081452342987040d43f50c72f2a5246430aa7559bee6c56663fd12029507a915f", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "343331343737363137", "sig": "3045022100b08122f027076f924cd1dc877c93659ecf942410772fba58881c9109311bca5302200287a0033df7c069fbfab7faefffddd7121908fdef76c04a1a402d599f04942e", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "36363033343338303333", "sig": "3044022048c044b94de8809898184e376a8a0903707679350e37ce290f858c8beef78c6d02206ccd83394e8abf4df2a40afb001ca4284d913d6b9caf6ef225d66bdddf9eb45d", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "32383239363231343535", "sig": "3044022005535fc39278260eb2b8cfba00226cb3155d75c0cf6b418ac56df63b7c0b1e9b022031aebd43e848874347b38ad64ef172fa315fda09645c8752c0e010152e43418b", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "34333131383231373336", "sig": "3044022078957c4f5a000af05fc477cc813cc6dcd9445438215b1c493780b6ccbd39a965022067023127ccde416b92cb3a7560436950ab643bbf08383ec9f4f6862cdb302095", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "3131373730373734313735", "sig": "3045022100ea1b7fd5ba10eeb70a456c149d494fa3757a209093c4dfcf03730d1cd54829e502203cd204bb84656435e3656fa5e76edc4df3ee79615f7f1adfaf388a7eec4f8172", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "31353938353135353635", "sig": "3046022100c9611952ab603753d0601e0c97de91a6d2927d38afeb1b2b622ea384968cbbe8022100dc32c42ce627f286f7d14aa4b037588abfc202b916a7bf9051c3d0527c66dca4", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "32383831313031363138", "sig": "3044022012c960e3fcfee173335123fe15cd7f8c8bc55f6f84a071e440b0e418cc9c0c2c02203f494246c0889b4213150ce77b6f5f2957476d3faf898135d56c145fd3af35ef", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "32303034373833333332", "sig": "3045022100dc921912a76a225819d4956a7ff0660bcdf695bdf286288858ccd5a26001277102206bc5879138b207c3e4e692d4412c9a3fdff91ac58a669ec830b68b1cd562e556", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "39353030323437373837", "sig": "3046022100e31a34cb647ddc65b03f9bd05aa3e5e62a800c6fb56794843b606f1f08890524022100e7f2e4ff291bd309b0617478da0fdba92535376a69dc43061783b3ef928113ad", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "32323039353030303630", "sig": "3044022047b825bdd67cb5d91f017d52ea77f59b1de1e05be48a469978fc88ca577dba2c022053ee369d5b89bf58b3c76218b7cecf12de68127152917c58ee155ee85dc664f2", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "38313933373839323237", "sig": "304502210085b2d5faa65cd162e666df35beb535c797e7a2a20836d351ebcff798c343309b0220196a837aabc19fa4522c5fac4c8e63e7cf3a5819c97fcec10e80f33e41f266a2", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "33363530363033323938", "sig": "3045022100de538dcaf220334aca9fbc11d1576643662c66585e268bc0eb331608ad9523ed02201b1d32790d46c6925ab909408170839f83e6a03a7632ec8877a0c191fffbee79", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "3136333833393533353233", "sig": "3046022100b5d45e7fe1196bf5e076fd76742c1e12eefa7934de826d7395646c94a8013fbc022100f5475abab65d9ef64616fde95b3ca6b75fa864758950015441a4414acdd0c894", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "32303931373638323035", "sig": "3046022100d973f8f20c33772413f214d1b8709cceaad159c9211ccf337e7477da197d49530221008d4c39ee66532e2ceea2f0861ee02781ba0339eefb30bc06218f4a63fd86c11a", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "39333634373032383235", "sig": "3046022100c487ab066b8bdac12b5c728586a61e42d0a1bea16fb544d3bd56fede77bb3db9022100d27022a17957dc23bd760d0f00e93f10189c67d32a9ec5489514f140e49272d3", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "393236383638373931", "sig": "3045022100f7777648b15af89af40cf9ea0a3c875aa0d9d736eca529af4eb6238f78a562d202203b70d46e1439bc5d941bdae8507625971c1c64dcf4c51bcdb43b1ca3ae0373d4", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "35313738313334383231", "sig": "304402207bb4646c887062a6c3e680b1491854c4b26a4728e5f2b2c1cc3087c780dab7b3022062900f55b885617f9b3c7ef34c67ecc37bf78e11a04cd6b9052eb80f9756559c", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "34373335303130373531", "sig": "304502204a96ed67d0950d97a8675c302f6102215ce0ec837ff53fbf06b57b010526b5740221008deb5f7a7c8cbeff01c3443700b8da129088771c8c2bf4ceca2a5fa81138a534", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "3134333533393131363839", "sig": "3046022100d9c3938a4b91d0660f6ddae8540a7bbeae2b0d717bde1f33e6904280197471930221009b022d6547abf9bef980bf8fd67c366e234eff3aebee58a7ee56d335df807a29", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "31333834353439323034", "sig": "3045022100f855a2d237016b1e0ec307672a793668b611d2b6d4e5acfe2b2088aadd6296a7022013c60acb5e6fa51f03ee8cbe3a220f3ac1ce527daee05e9d7430343030dea7e7", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "32373632313932373839", "sig": "3045022100b8ddb1ed6e80056fb9cea2590b1b63cfd1e8098c27547ec8c6cacd78e6ebfddb02207514ac6644955744946fcb5617f8b067850a3ecf829d821f18435a5e11a4a58b", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "31383331363534333331", "sig": "3046022100e903b7ead454ff8de871fdf67b6bae4ddc88254c35b333a98ba3a2f6562678a5022100f713e10293aec60e63c2a79b189129e54919a74cda6a204a2727c13bb63a25ba", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "32343336383939303330", "sig": "30450220615208d06b392c418fba55c752fef7b9ab8ed47d98f40776d86ff4bfa3e95464022100f403d5984d446acb203eba58a5468372267f9004f263aaa1eaa208652297db5b", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "323034303431323232", "sig": "304502200eb65a5bc338cd920a174648dd84ed5443f37417d8d730ceab0dd3fb9a2046f3022100c7a86d7e09f5e142fc6d45a136b4581e081f66a26fe0255f012b812d7cf90189", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "33333337313139393735", "sig": "30440220226464501f1ccc32f1e1482fff4624f4949343c79a820d4b9df637b493ab5b2302204eb47d0fd14db448cedd611e1dae972bdf27ce0fcfc19d9ec5bda9683081598d", "result": "valid", "flags": []}, {"tcId": 257, "comment": "special case hash", "msg": "36363935363230363738", "sig": "3045022100bc0d706b5fdf102805cf83f66ce17a97aebd5e3b6e7d5c008cd51164750af1080220651388d190da29748df343dc61e23c248c753013a5688652ca43d222edd1bfb4", "result": "valid", "flags": []}, {"tcId": 258, "comment": "special case hash", "msg": "32303933303137373437", "sig": "3045022039a1d9927356d6849b7e96cd34a5828bad112687198637e7b84900ed92fad0b70221009b5c753e2e10619e1993b8d8171a7aa4c4813a8721aca1f79bef06c0fa858462", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case hash", "msg": "313233343137393136", "sig": "3045022100d57adda8926a6b75a5c994a0b8abc75534e68bd79b02b8eaeb1a7add4c5c423202203b349431e37740c60f92c55dcc8a9f55e70e27b9f73d82e37d728ba7266cddf6", "result": "valid", "flags": []}, {"tcId": 260, "comment": "special case hash", "msg": "31373634333530363837", "sig": "304402207d5b4c29599e97085686534f843d71d25f22b96d89c70b4030abcbe6abd73559022028797ed87120c4bf50e37b0704d2d3b4e0b1b98c27e618eb99568b08123a1be9", "result": "valid", "flags": []}, {"tcId": 261, "comment": "special case hash", "msg": "3131343137323431343431", "sig": "304402200db6c547a1e7bbb715b48b7974104b6d7618f1dfed77019daa29bd59b273c087022057f054587cdd5628772c14dde0a77dc5c1bad06828410c5c63b9c1b35fe04410", "result": "valid", "flags": []}, {"tcId": 262, "comment": "special case hash", "msg": "32323638323436343933", "sig": "30440220094a1e172012d9367852f228f49c382bbdf19a6b354b8511807e585cd46e907f02200e50740da30419a987ccd56746b8737aa74a1292892eef99a34025aebb6dd209", "result": "valid", "flags": []}, {"tcId": 263, "comment": "special case hash", "msg": "32373234373936373737", "sig": "3045022073a5445895e0796686dc28c3e4cd5bc1f85368b3264b455d470a7cbeb7592d910221008a9ffabf9f65921734934d65053a00bfa3c353ac81cc9b6f60271635026cd0e1", "result": "valid", "flags": []}, {"tcId": 264, "comment": "special case hash", "msg": "393733333935313139", "sig": "304402200630f2c6277e9e0414b7e8da64311850b6f3912193970fd6e2f4df79df720c7c02206ccdaf47a44ee1043b76ef62b3de3aee876c26f4efe492cb3649423e599753d0", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case hash", "msg": "31353037303032373036", "sig": "304502201e1b75a3495bb5ceb2adb84b80f117672b79c5bc71e953d1a274b11dc4238729022100dcf48d930d89c0d301e97f9083487e63cb39127310b0f5e3c8769e98e5b2605d", "result": "valid", "flags": []}, {"tcId": 266, "comment": "special case hash", "msg": "33373433353638373832", "sig": "3045022100eb92e1a80ad7f440d8a2e9d88ac765c5cbf724bd367f414c9d91cc1904afe497022074fe0ec7cc0122eea4e63c4d16c89edf6a57823178c3747b01e7aff3df710007", "result": "valid", "flags": []}, {"tcId": 267, "comment": "special case hash", "msg": "393437363731323438", "sig": "304402207c99e1ae6941f4bcf54ea65491cafca180eee1605a2ed908c476ee67cbdd737302201f94f03ed47cc9674abb294cfe7e7b10ad67101b8cb245a8b5fb883a21a06dd9", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case hash", "msg": "373235343434333234", "sig": "3044022029bd14a9dafbe562e0c4c0d4aecb6491bbd37f707133f78fc5cb3313365377b702202ea1d86246d8edad31132f3dbe642cac488690849bdd7438d5c626cf8c973970", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case hash", "msg": "35353334303231323139", "sig": "3046022100cf7219dcc18e22bd487b6f59c6494f637c5f0ccb18192e9e60178621220b49fc022100a05d5abbe25a610c8e3615cf7d01c69563ddbfe76d7235fda45f046af03e50ff", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case hash", "msg": "3132333031383133373933", "sig": "304402201589de61cd315ba0bf7f0e577f00589d28f72131f4c7af41c6bb31ae8ba271da02202ff82a9a1f52901c4c6f8b8a41c0f6e760f160da266d788da5bdc945084dff8d", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case hash", "msg": "39313135333137363130", "sig": "3046022100a8c59942907039886bc374c17ff452b604385c3537b9b6c6113c1b19d72e68a9022100d376bc866cc82d78ca17b461edf16faea44e577659b1fef02cec0f41bd2e747c", "result": "valid", "flags": []}, {"tcId": 272, "comment": "special case hash", "msg": "32383934333937393632", "sig": "304602210099cc652412dcdfc574b6fc2525615e6711caaf7d558cc781a3a11cf371f40f71022100866dd4ab6b58b5d6379f0b431f9a251399defd5516bf6bb5495511e05b24f801", "result": "valid", "flags": []}, {"tcId": 273, "comment": "special case hash", "msg": "31323333353833303333", "sig": "3045022100b47cad5c88161e29620957061e24cdf46f3fff97c266b1635fcf2e9cb4d92879022013549f3b1b639eeb33f40cb338ecd089b9094d9625b76734f3803e9d40a1eb26", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case hash", "msg": "323131323639323536", "sig": "3045022100f4f3a043b5bdc56bd471876e2df15ea80ccb189b25486e6ab9007cffe121acfc022078e8e3b3589580dac1ce299a18f5704901104cd21205317608c330adc8d35272", "result": "valid", "flags": []}, {"tcId": 275, "comment": "special case hash", "msg": "33303035343833383634", "sig": "304502205df45e90f9413908b47f7cbb05d6c43a81eb62375ab961d2b065c6118023c018022100d720a2a23c34ece3acc1a516070bde6bfcad28fd89c482d72b69d8113b1903f6", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case hash", "msg": "38333536373632373235", "sig": "30440220780bb93f52d53752a9a877dab0578c7702d1ea889960e1682e84f82740b1be5d022008e366ffe8f72d041426aee57aeffce7822b209c34b28255a5de67190870ce42", "result": "valid", "flags": []}, {"tcId": 277, "comment": "special case hash", "msg": "38333239353030373630", "sig": "3045022100f743a77c86dce094cc265a87b2e053c8cc773370851dbc7ba1a52c58df24d55402202516e4614dd7ccd36a23cbbb1b58c29f900d4da2113e7b9ac956bf8879907680", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case hash", "msg": "31393237303834313635", "sig": "304502200b77b2ab21a5c92cb0e3f2f5b8594edcc0dd6c70dbeea9d9d363a3718c64dfb402210091c2d6515ff6f2977fd0a150cb04c600102f0ae07a9061993244783e62f2337a", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case hash", "msg": "37333032333538323838", "sig": "3045022041f0a220d11a014dfe43f89ab647abea430cf5a9703088f28c1222abb77e8857022100f811c584c10a25fba6216113698e5a2dc52d8ea340d92ce7a11d356f6d5a1382", "result": "valid", "flags": []}, {"tcId": 280, "comment": "special case hash", "msg": "3133343733373337333833", "sig": "3045022100ed707e3cac8bd56a4f0a3118558e8402dd477be7dcaa1a7ff448b0bcbfc0fac102203d9933e01d7e9439059973fa499c37b896213b04346bb292f867ff3a58c3d07a", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case hash", "msg": "32353031353533363839", "sig": "30460221008035d10dfd533e01718fa9d0a1773e55b424770415e570aef766ea2cbe577c27022100815b0f14d6f7f4ca45191428d98c9b414871ffecdac3d0717d285a473e5ddb06", "result": "valid", "flags": []}, {"tcId": 282, "comment": "special case hash", "msg": "33353335303430303239", "sig": "3045022100fbc3576a648ebd633152fc896ae6b4827c55824fb0c96fdd217fb2cfe3bbe63602200b37a95b15589663db322e1f089aa8132965ed6490362d243ef749c6094527f2", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case hash", "msg": "31343230333537323930", "sig": "3045022100de08cc84004d3fb30521b8e0ade66b6d52734ccd182cace8a34ba0e390fea89302202c1e3deb79d16117ed84c8982276d43709c5963d57bd2b10a530ebf161da1a3d", "result": "valid", "flags": []}, {"tcId": 284, "comment": "special case hash", "msg": "393035313735303638", "sig": "304502204eef795dc7b17efde95dc52062e3b60ab360d37704800fc915785e7739b834d9022100c68356eacc3509bf4d2b62412b2472f22383d18fa8851527294b1fad194c7bcf", "result": "valid", "flags": []}, {"tcId": 285, "comment": "special case hash", "msg": "31363133373734323935", "sig": "3045022100cb845b9fcfa07e88e9011f0311cae9f3f740516e7d16d9819b7d0f6fc764dac702203d7f3ba5173e130937b02bb7b8da25c506ace6182b8f9ae4ed891f7d216c0378", "result": "valid", "flags": []}, {"tcId": 286, "comment": "special case hash", "msg": "383733363733343931", "sig": "304502207d19ff3efc71258f747e74d76f091107b1fa47c87c638720b55178c0655dcbcc022100b8a653e31931dbd2ffc4e957675c68c28719b8118ec3ed3778c57ce3eff1613f", "result": "valid", "flags": []}, {"tcId": 287, "comment": "special case hash", "msg": "34333535313036343035", "sig": "3045022010d465e03829dad77e8246ad11caf8c6aa8aa918c2bef5a9a9e601c5a919f68f022100f365a7a7540dcb642de90e6d6ccf0c74094a8005deaed4062e394e1cf2bcd8fb", "result": "valid", "flags": []}, {"tcId": 288, "comment": "special case hash", "msg": "34353339353735383736", "sig": "30450220674330ea5a5d45d71fca1f3ba031494dee9a8623e0d9e9adbb2822794acb4f12022100ae4d3830157c820bc23fa792188b94bb559c3a6212768d7ac59dcf36f74402f6", "result": "valid", "flags": []}, {"tcId": 289, "comment": "special case hash", "msg": "383933363633323031", "sig": "3046022100a632c676f667669bda1b0ecf5f33fa67aaaf8aee46bc9e9f2bd5bd10c36cbb95022100a99352cacd6bd42b78a93908008c23eec2d36e5e4fd0aa349d5634fc543199c5", "result": "valid", "flags": []}, {"tcId": 290, "comment": "special case hash", "msg": "33383036303638303338", "sig": "304502200c9d2e8935f855e69dccd877d95248303e1b6daf8f61da500150185f5565fa97022100e4ce328f00e218aa13d496d85b73b5b891cf0b7c66ebefe5e6b92b1da7f837a9", "result": "valid", "flags": []}, {"tcId": 291, "comment": "special case hash", "msg": "32323332383137343937", "sig": "304402204748c00f44a6b3a8726f604d7b933303d98cd458b850ff2d7cb90c11d5950ac70220766df7aa4c8b3f8b03b0eb8aec653fb70eab7433a84e7ef2d57f368a051b704d", "result": "valid", "flags": []}, {"tcId": 292, "comment": "special case hash", "msg": "34303734333232353538", "sig": "304402204e4fee37b0b93d7d6dbee89b47dab0c065186ee81caf2227bb26e85149bbf9ef02203eb83850edda9a1b1011118feccd03e47b3e1ec815837bb7f8867288ad8df831", "result": "valid", "flags": []}, {"tcId": 293, "comment": "special case hash", "msg": "363130363735383237", "sig": "30460221009e8520c5e8296935fe93da2dad55963b9f1f88187f76810fff53c0a6e95b0c07022100d185de4f682a97d28fc3067d56a3d24f743f32d47a6f390068b2ceb71678fadf", "result": "valid", "flags": []}, {"tcId": 294, "comment": "special case hash", "msg": "3137343138373339323133", "sig": "304402203cacb67364a3fc1a379557f7e6f5d0f501977fd4822666956c9356146b7d922f0220686e27be6217045a5010c88003dd3956a864798c8ae687714fc8b7277f7b520f", "result": "valid", "flags": []}, {"tcId": 295, "comment": "special case hash", "msg": "35313237383432323837", "sig": "304502205df702c77d4638a4302d21a9fdd70bbe31a4a4a79c7d531d4f4c8283970f664a022100f72d799abce3cd22985c5cfc68f7afc8f96f4e7dce8485ed5c595edc1e1f1d4b", "result": "valid", "flags": []}, {"tcId": 296, "comment": "special case hash", "msg": "35303338363930383739", "sig": "30450220243a857dfd167b938492c421bd657659d101944736fa79b903cf91ec1c49e8e3022100bab3f04f130d737993ec8f45503376abe816c2b8e5ab3decb0dbca4f5e181d08", "result": "valid", "flags": []}, {"tcId": 297, "comment": "special case hash", "msg": "33383737303432333937", "sig": "3045022067a7996b7680e958480ac2f97084f2055194b38e0ecf82246dc87918ee1954a8022100de187dfbeefce383dcea1a7fd71362385d09c6d25aacdfa34ea270a91ec97cf5", "result": "valid", "flags": []}, {"tcId": 298, "comment": "special case hash", "msg": "333231373038313738", "sig": "3045022100ab6cf003aa7865cf8010ea01944fa0d2f825ac6a997a427f8a4e791e797ac6f6022028a7a6c80582ba8f888d2008fbd696799561e92d9a51922cc602aec2135f08aa", "result": "valid", "flags": []}, {"tcId": 299, "comment": "special case hash", "msg": "37363637303434323730", "sig": "30460221009f9f0be992fdd3e069167599dc55e331c9f189d2c230ae15d1b5b441d3843c74022100f8e5b4ed8ead0352e032f79b5f0475f0975e3738d784d895e9e4002b26762b79", "result": "valid", "flags": []}, {"tcId": 300, "comment": "special case hash", "msg": "31313034373435303432", "sig": "30440220373b8587777d2b4ff461fddf521abd8fc5d3a1caca847f4a5461dab6ba242d83022051a98da2628724018dff804c26a9671f7df3e24490392a2d1a91fdc7f50deab2", "result": "valid", "flags": []}, {"tcId": 301, "comment": "special case hash", "msg": "313533383730313534", "sig": "304502210090a6286a271d8ffa72ddb55e8c924793e03b2af73ce10f2deae857961cb07070022028df2b6379501110c12ac167189f9ea8873e1d62ffc76e6ada83f2cf412ea5d3", "result": "valid", "flags": []}, {"tcId": 302, "comment": "special case hash", "msg": "32323631333835303439", "sig": "30450220107434a824ec05568d52219ab3e847046f01493f1db57a82fabe754555838292022100d1c2f7c1ecb5afbc9b0e63e20fcf34cfc16bb260b0748343eb86c44012449b8f", "result": "valid", "flags": []}, {"tcId": 303, "comment": "special case hash", "msg": "37353538373437363632", "sig": "304502204b737fb724c97348fb67a994e88816b1091951b77234f1904717ec7ef1aa951d022100ff722b669bd4342e6b856232b5a5c03d2cf16f09b3d3842d0a87fc19d910acd4", "result": "valid", "flags": []}, {"tcId": 304, "comment": "special case hash", "msg": "33343939333634313832", "sig": "3046022100abe804f6c76fa3f1470c0f244a7ed96807ddffff4031926cfad9a2c9f73ed773022100e48352653ccc46f5f5a76d7eb3997dfd412001fb1692312788f7c297ef792fa7", "result": "valid", "flags": []}, {"tcId": 305, "comment": "special case hash", "msg": "32333639323733393835", "sig": "304402207d55a5da3201de5343703ff8ee363b7ddbcb3d786afca8f157b25e7c90d09de802203dfcb55acd7def4218abd3f989e901f824cc2d4ed3a37b8794117975103ff004", "result": "valid", "flags": []}, {"tcId": 306, "comment": "special case hash", "msg": "343933383339353635", "sig": "3044022025d2c88f0b79f2289f92d3ecb6de119fab764fb43ab5286391f9a282c82ac1980220105f44fa27afe4dc9800e6a16528314de01b17737e9741862f93ed0cc33b30f3", "result": "valid", "flags": []}, {"tcId": 307, "comment": "special case hash", "msg": "32353334333739393337", "sig": "3045022100d725c2731a6db8d623027a926a665e9dba0f95e90a5fe6807a84b200111f04ec0220346140bffb84731f0f1cf857193dea25a2f721463dc3b85a8e72c73ff72bda94", "result": "valid", "flags": []}, {"tcId": 308, "comment": "special case hash", "msg": "383334383031353938", "sig": "304402204fa9704d3eaa5760da0b97abc0de1f872840e58bfdb1a8f9d8be3f96f950ca7d02206ae15b572c7d1a49c99e1aa54de5fb2bbe055d45686770f579c08ee79924ede9", "result": "valid", "flags": []}, {"tcId": 309, "comment": "special case hash", "msg": "32343131303537343836", "sig": "30450220343353c8be35ca222a38771b19ff3550abe41b91f2ffdea9c9f4887d70b02782022100c285e3761c6645b3db4ff99a7ac40803286f28475ba28b9cd55cacdefb330984", "result": "valid", "flags": []}, {"tcId": 310, "comment": "special case hash", "msg": "373836343638363335", "sig": "30450220529b121d5b3cccb426189d7343d571adf05cfaee843669da6722f728c192bb8202210086490a7686215fb29b18a166bc22c1b8a982fd7d57ab593318adf8355684b45f", "result": "valid", "flags": []}, {"tcId": 311, "comment": "special case hash", "msg": "33303534373733373638", "sig": "3045022100ff65707177bb9aa135c5fd774bf72eb3058e80afc7d8bdea8f7fd18040ea995902200d4ed13f9e01bab8aaaa40d7f5c923a78470888375b4690e1164a83fdf136201", "result": "valid", "flags": []}, {"tcId": 312, "comment": "special case hash", "msg": "31393237303137373338", "sig": "30450220471ebfcc45de07bd4118f94b0284f0c5848dd93149a217b56b49e20baf583ced022100ad498b79d6151bf64e003f502fca8fb7b05b2106a96ca1b977ee002c73bc721b", "result": "valid", "flags": []}, {"tcId": 313, "comment": "special case hash", "msg": "31303531333235363334", "sig": "304402201e279add50ce6148dd4a3d311bad896745a169364ef68b94e6360fc48e949b9f02207230fbe4007fb7d6a4274c396081f37a1c9b2559b526db1efe435ead15e4b74e", "result": "valid", "flags": []}, {"tcId": 314, "comment": "special case hash", "msg": "34303139383636363832", "sig": "3044022052dfbabbcb99651021c025a308530b9cd04732f43463bbc51160cd542d9028df02200ebef4f6870bce1ca302e7120560e5170067c0fb3dc8668448b89dd4821b53fb", "result": "valid", "flags": []}, {"tcId": 315, "comment": "special case hash", "msg": "3130343530323537333530", "sig": "3046022100dabd19aeb87fd56119a3354c468f4429fc14421c54e8be9e9b927941579ae55e022100b10a7396c973e052af2944d37247e9016682d4da7cab2d5428618ce120a21056", "result": "valid", "flags": []}, {"tcId": 316, "comment": "special case hash", "msg": "333236393538353830", "sig": "304402202b9950c4005dea8e603ffe0fd9b3f66b7c0f07509e50913bb825ef7ddf2e6e77022062c9fcdf79026f60f830e7cae4af814db2cb58b5a948562772da130613bbef94", "result": "valid", "flags": []}, {"tcId": 317, "comment": "special case hash", "msg": "33303734363533323431", "sig": "3045022009d881e300448b9eac33c67f10c061c2c985c415a414d09c891847e3eec88598022100e75455a508493506f8746074f8bb3698d4362f98a9daa20fa916f6c4023764ae", "result": "valid", "flags": []}, {"tcId": 318, "comment": "special case hash", "msg": "37373134363833343830", "sig": "304402202db5a6d9b16c61c888a5de064f621e45227ab63efba61ef210fe4ef81c93e00f022004b8e57a7373b3642e58db0cc652d6da541d6d25c7b32c1ed2b408c9e3c39719", "result": "valid", "flags": []}, {"tcId": 319, "comment": "special case hash", "msg": "31373933333831333230", "sig": "304502207bd46dbe132d95aff854bebf1c0dd1e90303328fd84d381e93217723d1a4bc18022100f795a0c68d6c318c038dc37bd22d36bce8083096637e8912c3d01cbabd3feec7", "result": "valid", "flags": []}, {"tcId": 320, "comment": "special case hash", "msg": "34383830363235353636", "sig": "304502200ca03beb6d348d3d36d2e9f6773e5882ded66fe6026c9c27b847e34523c77c2c0221008770b6f0b6aa7c982e84235f1840a6172386a41ae75fd9affac7916cbd19f8c3", "result": "valid", "flags": []}, {"tcId": 321, "comment": "special case hash", "msg": "3439343337363438383537", "sig": "304502203f1a74c1dab6bfc319da38cde7a812695b530c60b36d2ae3fa11a7206b2031d5022100f07f8eb5825b2d5642443185d6afd2264e98996dc392519f812883dbe0e247fc", "result": "valid", "flags": []}, {"tcId": 322, "comment": "special case hash", "msg": "34373038363839373836", "sig": "304402201ae1c3ec96f8591a3a235de7c6f739201104381eebeab5fb5ee523f577b6c7fa022007ac1a9348fc8946964fbef0af11dc8b2da6feb3eee8cf475c4926ca9cd571a3", "result": "valid", "flags": []}, {"tcId": 323, "comment": "special case hash", "msg": "33303239383732393531", "sig": "3046022100c6ef5423c69541caa3bb8f361f4fa9caeca30fb329a0da806ea956270e0a9928022100c045de5205eb8bc861bc159522b41c0d66e62fac0f58730861000cdf9e27bd96", "result": "valid", "flags": []}, {"tcId": 324, "comment": "special case hash", "msg": "33303137313930333835", "sig": "304402203097d229239b4085e3fb3188106d5da53456752976d2c4ba82dcff6ab96d1909022056f60ab76d33bc94ebae3042a1d56a731429f1bed162bf3ef7269d912aaeea71", "result": "valid", "flags": []}, {"tcId": 325, "comment": "special case hash", "msg": "393536333633393339", "sig": "304402206c9ecb8dd5b8badd49ea1b26ae3ae2af7236cbe1c626aa6b27029ef1d6d05901022006c9bf356441f84215b006c721a00697cede6941a18fbc0f9c5b3c267eeaa371", "result": "valid", "flags": []}, {"tcId": 326, "comment": "special case hash", "msg": "35333030373634333530", "sig": "3045022043ae5619f91b711a61be076581f91d382c39fe53d500b136f81be639bea76add022100824594f08185479731ed095367c04def2ff196229c5b136b3835eb8bb819c56d", "result": "valid", "flags": []}, {"tcId": 327, "comment": "special case hash", "msg": "31393334363634383434", "sig": "3045022013e8dc460a0d2af2305a6abfeac834737d4441576b194fe83147b7d7d1247479022100c590a7b7fd6de8c2d658aa2bde97de84505985e2979ab2a527658122cafaf61e", "result": "valid", "flags": []}, {"tcId": 328, "comment": "special case hash", "msg": "3335353435303535393632", "sig": "3045022027feb06a4c5bb046162467e523b4b62c2c8dabf26ded997eb0737d3eafd16c8a0221009ce87c4cd6b93d1ec3b3c5d29fa415cf918edd24e0febd7b200d6c82c91e5f78", "result": "valid", "flags": []}, {"tcId": 329, "comment": "special case hash", "msg": "31333031373232313038", "sig": "30450221009d76a05d5803a2da17dc2782ec469c8024b1d293a38c23ac4eeb3058595a24a70220226595b192ea8336faa44670fef5e808ff9911ccda85a1765c19ec44671b0505", "result": "valid", "flags": []}, {"tcId": 330, "comment": "special case hash", "msg": "35363137363931363932", "sig": "30440220791088736561f41932cd86f066cca6d63d4473aebb869bcc70c923ef80a7fd95022033402973e7a824602712a5abf7030bad2f183e6b4fa40c66399a14ae48e288e3", "result": "valid", "flags": []}, {"tcId": 331, "comment": "special case hash", "msg": "33353831393332353334", "sig": "3044022052685eef5f2168598c294870188c043dff0496638037170763ef5778b7b6fc1f022079a88d3e7b2c3a44be4b3b3305e4bad5fcfe07d179136f5ac926315ff9d4787a", "result": "valid", "flags": []}, {"tcId": 332, "comment": "special case hash", "msg": "3135373136363738373434", "sig": "304502205db883b5a3766a2a14ab26a25ec598f7bd1f97585fe0b55341e7da251a62ec1e022100bf63c66e992c91fde513abdbd59b4f9f542881cfdc2be3fbf3e772b97e505b3d", "result": "valid", "flags": []}, {"tcId": 333, "comment": "special case hash", "msg": "33313939373833333630", "sig": "304502207e2f80300a4c81543e324e9c8973bbb6f16599c3d337ee82aed816624843f37002210080a807e920deaebaf3c3247010cb3c91cfce21b0d5ad695177d934ee5a7f7cbe", "result": "valid", "flags": []}, {"tcId": 334, "comment": "special case hash", "msg": "373430343735303832", "sig": "3046022100de12eb33b717fd887fd684a64af9439a27ee83b28ac5751772249e600856b59e0221008dca367bd7bc83709f25b0fc4e1f4a0e7e747be0b8a2977aabd25750a0ba60c2", "result": "valid", "flags": []}, {"tcId": 335, "comment": "special case hash", "msg": "343137343336353339", "sig": "3045022100dee112ed7000c0776ba4fab09f439a844addb86c5046223397498ad72d059de402207039b715851e4b386ea15e9bb0899ca21a4e6f4ecbbce4f706d29274806400b6", "result": "valid", "flags": []}, {"tcId": 336, "comment": "special case hash", "msg": "31323335363538383839", "sig": "30450221009d01032d95cac596e7df6c75965c3669f29cb8e58cc9a933bc5d60c1a97e946e02201cf2ce39df73cc734bb4180ef09de883bad7c8d82ab1a5861d265b48aa195bb6", "result": "valid", "flags": []}, {"tcId": 337, "comment": "special case hash", "msg": "32343239323535343034", "sig": "304502206f1513d12e2112ec4f396ef4ea38324102ec3c7fb63ee49f485cb421a07dce57022100a78fbd65b2582a4031c34c7a8c28f03f16ef2ba18e2da41ef173ee5a85af1fe3", "result": "valid", "flags": []}, {"tcId": 338, "comment": "special case hash", "msg": "3531383033303235343636", "sig": "3046022100c3fb59671cb8c6db48bd51a667060428f75124b5e990af1e997fd636335072d5022100c797e7245cfc8d98bca3ae1f4239b88684cadeda2c628f09ae61053eeb683771", "result": "valid", "flags": []}, {"tcId": 339, "comment": "special case hash", "msg": "34343736333938323030", "sig": "3046022100e8b5b3f3443d59d7521d093884486e7a6732e275ec13313bd4d178f28128e075022100d299b062928e5f058c705acc3c62f24128ec703c28288b0d294216370cad69b0", "result": "valid", "flags": []}, {"tcId": 340, "comment": "special case hash", "msg": "39303630303335323132", "sig": "3045022100ce43218d44e113ec38d5cbcf402f3dbfa87d58826a760f0bf2c88f11981f77190220648f3ed0d1b76dec5437cb685dda7a1512ef07f4eb078dc50e418efeb1af8849", "result": "valid", "flags": []}, {"tcId": 341, "comment": "special case hash", "msg": "31373536303533303938", "sig": "3046022100fdc99047865eeffcb69f8b8728a008e31f9f6ba78f698fae62b71cce79501888022100f06612f593873a13459695a5c4cb504acc2a8c56179b68553e60f2319c905b4e", "result": "valid", "flags": []}, {"tcId": 342, "comment": "special case hash", "msg": "34393830383630303338", "sig": "30450220256e5c4f4cd121e2dee1987be7b241b6e91f90d483210aab9a4b367db5613174022100f75060db4bd6cd52bb8e6aa94f7ab68488a638873515787ec0d7e61bad58ab7f", "result": "valid", "flags": []}, {"tcId": 343, "comment": "special case hash", "msg": "33363231383735333335", "sig": "3046022100c7a269986b6ad540dd30e620d1606e3267935c7fd5551b3311fd4e840510c2480221008db7c8c464109cf0edb89357e663de6e882b6f5906adde6d58575ce0a2cce257", "result": "valid", "flags": []}, {"tcId": 344, "comment": "special case hash", "msg": "36363433333334373231", "sig": "304502200bea324758ea80ed4a0a56f7d836fc73bf196e43fbc59d953f0ce34abba57b22022100f04027248e83bd48fb1571291ca1a5f088eb3e89f00904d71b9ea8d6677f7893", "result": "valid", "flags": []}, {"tcId": 345, "comment": "special case hash", "msg": "34343534393432373832", "sig": "304402205b53c96d7a195f02cfac2d155aa7e132fbc35d59afb080f649dc13056248addf02202b157db2154bd5dc0ef2fc6eecd867fabdc633d2ca683a48f3f9095745351aa6", "result": "valid", "flags": []}, {"tcId": 346, "comment": "special case hash", "msg": "3230313834343032", "sig": "3044022024174a81d221a4bfd8978f312acec4dacc4f08f8f8cdf29b2024bad2177758df022073fc1bf3388009e3219cf4c7e62e4aeaffc1b9614b2831405a01403c86936452", "result": "valid", "flags": []}, {"tcId": 347, "comment": "special case hash", "msg": "3538313332313733", "sig": "304502205de7e80ce3137f5705d23397197c86e7749e5e682104f13beb5a6365a63780ea022100f3da2bfd6442638da60201be68fe2ad206365af9e40c4c1531cd3a05db1ad3c6", "result": "valid", "flags": []}, {"tcId": 348, "comment": "special case hash", "msg": "31313833383631383131", "sig": "3045022100dcdc28f81ff5ed91fa15f15fc4f1d38f9cddfc3df65bb2d3a55582faf7c0910d0220515b7759a130d667eff7ff3167c305cca101be3a07945fd7cb6a1359c16db678", "result": "valid", "flags": []}, {"tcId": 349, "comment": "special case hash", "msg": "31393232363032393036", "sig": "304502207407261c978018cc6e92a340de80edd3044a6e7116eb9fa9b022a2aeb318c65e022100bb5826953e0b85ad69745249f69507765a93f82198bca1e4475fd5de7dfb15ca", "result": "valid", "flags": []}, {"tcId": 350, "comment": "special case hash", "msg": "393735313433323037", "sig": "304402203962520ea2ef01cba7d5135117a7fcb5ab120b28baf6e31de2e6ec9993d8d5bc02202583659fbdf83399309bddc89ea5f39fe22187671f3149d94f96fa234a6013db", "result": "valid", "flags": []}, {"tcId": 351, "comment": "special case hash", "msg": "38333135313136333833", "sig": "3046022100bec3ea35844452bb739d92b20882e5b672dc6eff323cd31d1a2db37db93791e80221008ef77b3c709d60b9d5d998f81d3f72c466fd7bb99c681ae8bc9c580db1f7c213", "result": "valid", "flags": []}, {"tcId": 352, "comment": "special case hash", "msg": "333236333136383132", "sig": "3045022100b8fbdfedee5376cfc9177d96c45e003f90b7367aa40cf37d63e483bfaa4be95102204dcacdfbe41df7899382607489bb75422f3eb67822890ea3bbfd80cd456fc127", "result": "valid", "flags": []}, {"tcId": 353, "comment": "special case hash", "msg": "34303239363837313336", "sig": "3045022100df228c1a8bb3684b7ffa2e3f777643c369e1bc2299d66a66c8ed27a4ed59b783022047314aaf3629a0de02313df06c0cb363e4e019aafd20ec06be63b7b4c21538ed", "result": "valid", "flags": []}, {"tcId": 354, "comment": "special case hash", "msg": "36333230383831313931", "sig": "30440220528746b280d8a0e54851cb99894afc01ce24cff7edd60116e3d8dae42adb496102204eb264629e5cf4aba77e05c54774bd0cf20b057142a1ac2103099d664c2c5dee", "result": "valid", "flags": []}, {"tcId": 355, "comment": "special case hash", "msg": "35323235333930373830", "sig": "3045022044c391f82cf5eeb80f87c347a5bfe461c49fd8779311e237abc05b19f6aed093022100866853619339b716092df4466aa0cc9e6256fb7e18a79854b60ccc534bb6df10", "result": "valid", "flags": []}, {"tcId": 356, "comment": "special case hash", "msg": "31333439333933363934", "sig": "3045022003d127a6f72465bd1c02109f9605202b246763097c756235d8f8a26848eb609c02210083d551427f31b9572b61180cf18bc85c20306c0de2c39d00430b3fc91dc50c6b", "result": "valid", "flags": []}, {"tcId": 357, "comment": "special case hash", "msg": "3130333937393630373631", "sig": "304502205044ef2362c8e6c32fc14584b0751eda8e8e8901d9382354040d2615d9cc07c1022100dd16765911dce7a7ae5f3b64b3ce3a5e12e548784597dc0a379f7bb8f4fca879", "result": "valid", "flags": []}, {"tcId": 358, "comment": "Signature generated without truncating the hash", "msg": "313233343030", "sig": "3045022100ce645f0f1ccb63844e54bd7603a4280ea065e3c147d26e73cfbe58c6116b0cf1022040ccc23188aa20453d7f95d33868f247b3d77c516b70e4c351b48ca2ebef0027", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040fa36769835d1d215f156d5e82bed2fa76aab134bdb1d3bd40975faf5ac19cc6e67d675a8f0dc4760b3f8fbe0f0853a80b58af8dd2c4a41afbf9cb0c72d016ca", "wx": "0fa36769835d1d215f156d5e82bed2fa76aab134bdb1d3bd40975faf5ac19cc6", "wy": "00e67d675a8f0dc4760b3f8fbe0f0853a80b58af8dd2c4a41afbf9cb0c72d016ca"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200040fa36769835d1d215f156d5e82bed2fa76aab134bdb1d3bd40975faf5ac19cc6e67d675a8f0dc4760b3f8fbe0f0853a80b58af8dd2c4a41afbf9cb0c72d016ca", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAED6NnaYNdHSFfFW1egr7S+naqsTS9sdO9\nQJdfr1rBnMbmfWdajw3Edgs/j74PCFOoC1ivjdLEpBr7+csMctAWyg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 359, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "30360211014551231950b75fc4402da1722fc9baeb022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd036413e", "result": "valid", "flags": []}, {"tcId": 360, "comment": "r too large", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2c022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd036413e", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042f1db0895a8d615779a38ddcbeacad9458f35688b587abeeda1cb5a891e954a5b591478e3b81f5881dd71f93b811130a67ea697f452faccd5c5117fac15f5f93", "wx": "2f1db0895a8d615779a38ddcbeacad9458f35688b587abeeda1cb5a891e954a5", "wy": "00b591478e3b81f5881dd71f93b811130a67ea697f452faccd5c5117fac15f5f93"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200042f1db0895a8d615779a38ddcbeacad9458f35688b587abeeda1cb5a891e954a5b591478e3b81f5881dd71f93b811130a67ea697f452faccd5c5117fac15f5f93", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAELx2wiVqNYVd5o43cvqytlFjzVoi1h6vu\n2hy1qJHpVKW1kUeOO4H1iB3XH5O4ERMKZ+ppf0UvrM1cURf6wV9fkw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 361, "comment": "r,s are large", "msg": "313233343030", "sig": "3046022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd036413f022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd036413e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04462155174b64dd050601b6d7c2297251815d413e26c9b91d123536e76fd3fb135f0a20f86528dff246ecc71d5d005b2935e4d8e0b076fd6792d4a2b3fd2b7bb9", "wx": "462155174b64dd050601b6d7c2297251815d413e26c9b91d123536e76fd3fb13", "wy": "5f0a20f86528dff246ecc71d5d005b2935e4d8e0b076fd6792d4a2b3fd2b7bb9"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004462155174b64dd050601b6d7c2297251815d413e26c9b91d123536e76fd3fb135f0a20f86528dff246ecc71d5d005b2935e4d8e0b076fd6792d4a2b3fd2b7bb9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAERiFVF0tk3QUGAbbXwilyUYFdQT4mybkd\nEjU252/T+xNfCiD4ZSjf8kbsxx1dAFspNeTY4LB2/WeS1KKz/St7uQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 362, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02203e9a7582886089c62fb840cf3b83061cd1cff3ae4341808bb5bdee6191174177", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048e89e0fff2d768c7c139b22b90aa66b24b3b1ced4345c469e439b2c80d6fed084eb9ca1486ff3411db46590f78008d6d6a0a9cf9cf36b2bef833407af5bc883e", "wx": "008e89e0fff2d768c7c139b22b90aa66b24b3b1ced4345c469e439b2c80d6fed08", "wy": "4eb9ca1486ff3411db46590f78008d6d6a0a9cf9cf36b2bef833407af5bc883e"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200048e89e0fff2d768c7c139b22b90aa66b24b3b1ced4345c469e439b2c80d6fed084eb9ca1486ff3411db46590f78008d6d6a0a9cf9cf36b2bef833407af5bc883e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEjong//LXaMfBObIrkKpmsks7HO1DRcRp\n5DmyyA1v7QhOucoUhv80EdtGWQ94AI1tagqc+c82sr74M0B69byIPg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 363, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022024238e70b431b1a64efdf9032669939d4b77f249503fc6905feb7540dea3e6d2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f4c2d7df7ca616bce49212e47ed112106445f47cf114782626740d37e1c596df1088b19bcaf0d10609a46bbdfd626a83d13e62d405775ae3941755b278a443c0", "wx": "00f4c2d7df7ca616bce49212e47ed112106445f47cf114782626740d37e1c596df", "wy": "1088b19bcaf0d10609a46bbdfd626a83d13e62d405775ae3941755b278a443c0"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004f4c2d7df7ca616bce49212e47ed112106445f47cf114782626740d37e1c596df1088b19bcaf0d10609a46bbdfd626a83d13e62d405775ae3941755b278a443c0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE9MLX33ymFrzkkhLkftESEGRF9HzxFHgm\nJnQNN+HFlt8QiLGbyvDRBgmka739YmqD0T5i1AV3WuOUF1WyeKRDwA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 364, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0443053fa57436a0b26f0f887b2403ccd9d18f14b7866e1da593835e93cd103a156039d7ccf6c355ac94ed59225aab8a5aa190c89c422f80e71246b998818ecd54", "wx": "43053fa57436a0b26f0f887b2403ccd9d18f14b7866e1da593835e93cd103a15", "wy": "6039d7ccf6c355ac94ed59225aab8a5aa190c89c422f80e71246b998818ecd54"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000443053fa57436a0b26f0f887b2403ccd9d18f14b7866e1da593835e93cd103a156039d7ccf6c355ac94ed59225aab8a5aa190c89c422f80e71246b998818ecd54", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEQwU/pXQ2oLJvD4h7JAPM2dGPFLeGbh2l\nk4Nek80QOhVgOdfM9sNVrJTtWSJaq4paoZDInEIvgOcSRrmYgY7NVA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 365, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020102", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "049b27e4409c49abe9d8d1b90edc64367daedb43d68a41c501032dba5d73ef10210cd42cc8488eb0588680b94e934ff744f4e6cb079737beb5eeabbe56fd11a7bb", "wx": "009b27e4409c49abe9d8d1b90edc64367daedb43d68a41c501032dba5d73ef1021", "wy": "0cd42cc8488eb0588680b94e934ff744f4e6cb079737beb5eeabbe56fd11a7bb"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200049b27e4409c49abe9d8d1b90edc64367daedb43d68a41c501032dba5d73ef10210cd42cc8488eb0588680b94e934ff744f4e6cb079737beb5eeabbe56fd11a7bb", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEmyfkQJxJq+nY0bkO3GQ2fa7bQ9aKQcUB\nAy26XXPvECEM1CzISI6wWIaAuU6TT/dE9ObLB5c3vrXuq75W/RGnuw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 366, "comment": "small r and s", "msg": "313233343030", "sig": "3006020101020103", "result": "valid", "flags": []}, {"tcId": 367, "comment": "r is larger than n", "msg": "313233343030", "sig": "3026022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142020103", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046e3f0e1b05e5bed7e8a1c8de7b8a1273673731151e3a04ddc83e18c98d842943cf1d41058a6b7272a014c6caff94db3f0233e0f21cc101ea159ab14bc8483745", "wx": "6e3f0e1b05e5bed7e8a1c8de7b8a1273673731151e3a04ddc83e18c98d842943", "wy": "00cf1d41058a6b7272a014c6caff94db3f0233e0f21cc101ea159ab14bc8483745"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046e3f0e1b05e5bed7e8a1c8de7b8a1273673731151e3a04ddc83e18c98d842943cf1d41058a6b7272a014c6caff94db3f0233e0f21cc101ea159ab14bc8483745", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEbj8OGwXlvtfoocjee4oSc2c3MRUeOgTd\nyD4YyY2EKUPPHUEFimtycqAUxsr/lNs/AjPg8hzBAeoVmrFLyEg3RQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 368, "comment": "s is larger than n", "msg": "313233343030", "sig": "3026020101022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd04917c8", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0406b2c1b72f12846bbcceda68183372d3f05ec591c43569361646f5916a00a7202282d15b70f19db914c6fdd8faf15ab90ced3bd4c3f59a247be41610497594c6", "wx": "06b2c1b72f12846bbcceda68183372d3f05ec591c43569361646f5916a00a720", "wy": "2282d15b70f19db914c6fdd8faf15ab90ced3bd4c3f59a247be41610497594c6"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000406b2c1b72f12846bbcceda68183372d3f05ec591c43569361646f5916a00a7202282d15b70f19db914c6fdd8faf15ab90ced3bd4c3f59a247be41610497594c6", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEBrLBty8ShGu8ztpoGDNy0/BexZHENWk2\nFkb1kWoApyAigtFbcPGduRTG/dj68Vq5DO071MP1miR75BYQSXWUxg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 369, "comment": "small r and s^-1", "msg": "313233343030", "sig": "302702020101022100c58b162c58b162c58b162c58b162c58a1b242973853e16db75c8a1a71da4d39d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044e61226e0b1c1e5a40767956c7b530eace83d550038e32bd14c5258c48c939fd57d069215ea210b386820a2d426fc711862bcfb34c7deaaed404d17692892cc4", "wx": "4e61226e0b1c1e5a40767956c7b530eace83d550038e32bd14c5258c48c939fd", "wy": "57d069215ea210b386820a2d426fc711862bcfb34c7deaaed404d17692892cc4"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200044e61226e0b1c1e5a40767956c7b530eace83d550038e32bd14c5258c48c939fd57d069215ea210b386820a2d426fc711862bcfb34c7deaaed404d17692892cc4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAETmEibgscHlpAdnlWx7Uw6s6D1VADjjK9\nFMUljEjJOf1X0GkhXqIQs4aCCi1Cb8cRhivPs0x96q7UBNF2koksxA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 370, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "302c02072d9b4d347952cc022100fcbc5103d0da267477d1791461cf2aa44bf9d43198f79507bd8779d69a13108e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04cf259a52f769eecab5071c0b4676bc4cc474474b74675fe8bd1660df5b70ce1b722f774a601a61f2e8e364477b0ccea457b76977ab300139c4ee0e1fbb7fe8f9", "wx": "00cf259a52f769eecab5071c0b4676bc4cc474474b74675fe8bd1660df5b70ce1b", "wy": "722f774a601a61f2e8e364477b0ccea457b76977ab300139c4ee0e1fbb7fe8f9"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004cf259a52f769eecab5071c0b4676bc4cc474474b74675fe8bd1660df5b70ce1b722f774a601a61f2e8e364477b0ccea457b76977ab300139c4ee0e1fbb7fe8f9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEzyWaUvdp7sq1BxwLRna8TMR0R0t0Z1/o\nvRZg31twzhtyL3dKYBph8ujjZEd7DM6kV7dpd6swATnE7g4fu3/o+Q==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 371, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "3032020d1033e67e37b32b445580bf4efc022100906f906f906f906f906f906f906f906ed8e426f7b1968c35a204236a579723d2", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0467ba78328cffa6eb3f7119096adf17e3fca6b2da966c03bc66174c2984a1d5539abdde7989d6f5083187261393a6e162eb508ae62749e41caf55b2be14d9a960", "wx": "67ba78328cffa6eb3f7119096adf17e3fca6b2da966c03bc66174c2984a1d553", "wy": "009abdde7989d6f5083187261393a6e162eb508ae62749e41caf55b2be14d9a960"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000467ba78328cffa6eb3f7119096adf17e3fca6b2da966c03bc66174c2984a1d5539abdde7989d6f5083187261393a6e162eb508ae62749e41caf55b2be14d9a960", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEZ7p4Moz/pus/cRkJat8X4/ymstqWbAO8\nZhdMKYSh1VOavd55idb1CDGHJhOTpuFi61CK5idJ5ByvVbK+FNmpYA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 372, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "3026020201010220783266e90f43dafe5cd9b3b0be86de22f9de83677d0f50713a468ec72fcf5d57", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048f8c5fdf711aff7ddc807c4308a132b1099e3e95dabf803ca29283dd41b090558f60c71b4a813e7b4364b0a7260765042d3696a9580548f585be12633ac3b824", "wx": "008f8c5fdf711aff7ddc807c4308a132b1099e3e95dabf803ca29283dd41b09055", "wy": "008f60c71b4a813e7b4364b0a7260765042d3696a9580548f585be12633ac3b824"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200048f8c5fdf711aff7ddc807c4308a132b1099e3e95dabf803ca29283dd41b090558f60c71b4a813e7b4364b0a7260765042d3696a9580548f585be12633ac3b824", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEj4xf33Ea/33cgHxDCKEysQmePpXav4A8\nopKD3UGwkFWPYMcbSoE+e0NksKcmB2UELTaWqVgFSPWFvhJjOsO4JA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 373, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "3031020d062522bbd3ecbe7c39e93e7c260220783266e90f43dafe5cd9b3b0be86de22f9de83677d0f50713a468ec72fcf5d57", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04fb78c45f16de9b4e098ddc9ef6eb340b055a6a49c438b87bf7969dc24f7967dd2af9ebaba55b6713e06e9df9e42b79ea9364405ebab1199ea230ae38ec83b91a", "wx": "00fb78c45f16de9b4e098ddc9ef6eb340b055a6a49c438b87bf7969dc24f7967dd", "wy": "2af9ebaba55b6713e06e9df9e42b79ea9364405ebab1199ea230ae38ec83b91a"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004fb78c45f16de9b4e098ddc9ef6eb340b055a6a49c438b87bf7969dc24f7967dd2af9ebaba55b6713e06e9df9e42b79ea9364405ebab1199ea230ae38ec83b91a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE+3jEXxbem04Jjdye9us0CwVaaknEOLh7\n95adwk95Z90q+eurpVtnE+BunfnkK3nqk2RAXrqxGZ6iMK447IO5Gg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 374, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "3045022100fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03640c1022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b7b91538786272e70a603cfc80c52f87f057a369149848dbc865a8d2f445ec26bd90ba8844f58db6744b5f31a470e59ebfee1891be36ee65b18ba172e5eaf943", "wx": "00b7b91538786272e70a603cfc80c52f87f057a369149848dbc865a8d2f445ec26", "wy": "00bd90ba8844f58db6744b5f31a470e59ebfee1891be36ee65b18ba172e5eaf943"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004b7b91538786272e70a603cfc80c52f87f057a369149848dbc865a8d2f445ec26bd90ba8844f58db6744b5f31a470e59ebfee1891be36ee65b18ba172e5eaf943", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEt7kVOHhicucKYDz8gMUvh/BXo2kUmEjb\nyGWo0vRF7Ca9kLqIRPWNtnRLXzGkcOWev+4Ykb427mWxi6Fy5er5Qw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 375, "comment": "s == 1", "msg": "313233343030", "sig": "3025022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c1020101", "result": "valid", "flags": []}, {"tcId": 376, "comment": "s == 0", "msg": "313233343030", "sig": "3025022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c1020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "049c0c462658f6493295775ed99348db5895ae8471c819a1ed9ae1b5180397f08ff386d14d6e56758d1dafa50755af4fe079233c139436abf61a9208f8b7f893af", "wx": "009c0c462658f6493295775ed99348db5895ae8471c819a1ed9ae1b5180397f08f", "wy": "00f386d14d6e56758d1dafa50755af4fe079233c139436abf61a9208f8b7f893af"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200049c0c462658f6493295775ed99348db5895ae8471c819a1ed9ae1b5180397f08ff386d14d6e56758d1dafa50755af4fe079233c139436abf61a9208f8b7f893af", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEnAxGJlj2STKVd17Zk0jbWJWuhHHIGaHt\nmuG1GAOX8I/zhtFNblZ1jR2vpQdVr0/geSM8E5Q2q/Yakgj4t/iTrw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 377, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "304402207fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a0022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0489e63127c97dd4cb19fb802f22229decd0d852639b3d982b2589817a7e520049e1fd70b15e5e5d3ea4ab748903ca891ab3964ff4d7bf48b17c6007957a5e2021", "wx": "0089e63127c97dd4cb19fb802f22229decd0d852639b3d982b2589817a7e520049", "wy": "00e1fd70b15e5e5d3ea4ab748903ca891ab3964ff4d7bf48b17c6007957a5e2021"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000489e63127c97dd4cb19fb802f22229decd0d852639b3d982b2589817a7e520049e1fd70b15e5e5d3ea4ab748903ca891ab3964ff4d7bf48b17c6007957a5e2021", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEieYxJ8l91MsZ+4AvIiKd7NDYUmObPZgr\nJYmBen5SAEnh/XCxXl5dPqSrdIkDyokas5ZP9Ne/SLF8YAeVel4gIQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 378, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "304402207fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a002207fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a0", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04eb89c838542537c3f530b6e8bc62d1e6284ed4e9b8c6aea96e82970d8abdefff58cae0df61874d30c2afa05c8a703800ac80564397688b19a5149f65054b138f", "wx": "00eb89c838542537c3f530b6e8bc62d1e6284ed4e9b8c6aea96e82970d8abdefff", "wy": "58cae0df61874d30c2afa05c8a703800ac80564397688b19a5149f65054b138f"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004eb89c838542537c3f530b6e8bc62d1e6284ed4e9b8c6aea96e82970d8abdefff58cae0df61874d30c2afa05c8a703800ac80564397688b19a5149f65054b138f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE64nIOFQlN8P1MLbovGLR5ihO1Om4xq6p\nboKXDYq97/9YyuDfYYdNMMKvoFyKcDgArIBWQ5doixmlFJ9lBUsTjw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 379, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "304402207fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a002207fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0472bdb42d379cd807e8dcdd597e5c68c464ecb4211ee885f7210e55ff52e9368834231f3921839c8a3a2cc7ff5964f1f79c77f2c8813e2659684ee1d8bf7125c0", "wx": "72bdb42d379cd807e8dcdd597e5c68c464ecb4211ee885f7210e55ff52e93688", "wy": "34231f3921839c8a3a2cc7ff5964f1f79c77f2c8813e2659684ee1d8bf7125c0"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000472bdb42d379cd807e8dcdd597e5c68c464ecb4211ee885f7210e55ff52e9368834231f3921839c8a3a2cc7ff5964f1f79c77f2c8813e2659684ee1d8bf7125c0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEcr20LTec2Afo3N1ZflxoxGTstCEe6IX3\nIQ5V/1LpNog0Ix85IYOcijosx/9ZZPH3nHfyyIE+JlloTuHYv3ElwA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 380, "comment": "u1 == 1", "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b80220342dae751a63a3ca8189cf342b3b34eaaa2565e2c7e26121c1bfd5435447f1c3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0457cf2c69876d8a9822a6b796492aa889c39fa1371fc730c5a15532ac4aa197b38c936d0041821e1ca81df3f1fd0a495c0c8974a81fb41cec4622cc1bfcccf3d2", "wx": "57cf2c69876d8a9822a6b796492aa889c39fa1371fc730c5a15532ac4aa197b3", "wy": "008c936d0041821e1ca81df3f1fd0a495c0c8974a81fb41cec4622cc1bfcccf3d2"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000457cf2c69876d8a9822a6b796492aa889c39fa1371fc730c5a15532ac4aa197b38c936d0041821e1ca81df3f1fd0a495c0c8974a81fb41cec4622cc1bfcccf3d2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEV88saYdtipgipreWSSqoicOfoTcfxzDF\noVUyrEqhl7OMk20AQYIeHKgd8/H9CklcDIl0qB+0HOxGIswb/Mzz0g==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 381, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "3045022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8022100cbd2518ae59c5c357e7630cbd4c4cb1410897703e7663f19fe1289497bee4f7e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040a7413800a6571b03100d9f327c68a89aaaef2e7ff922b0a0aa95e39a082c4fb37466eb04ed38187bedfd767de7c45416577bca4bd961de3d8890bea3409f697", "wx": "0a7413800a6571b03100d9f327c68a89aaaef2e7ff922b0a0aa95e39a082c4fb", "wy": "37466eb04ed38187bedfd767de7c45416577bca4bd961de3d8890bea3409f697"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200040a7413800a6571b03100d9f327c68a89aaaef2e7ff922b0a0aa95e39a082c4fb37466eb04ed38187bedfd767de7c45416577bca4bd961de3d8890bea3409f697", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAECnQTgAplcbAxANnzJ8aKiaqu8uf/kisK\nCqleOaCCxPs3Rm6wTtOBh77f12fefEVBZXe8pL2WHePYiQvqNAn2lw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 382, "comment": "u2 == 1", "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "047e27893adb379d40a61668ad660edc256004bbfc12d55889fbd5121eac56a06e9a36f42598db7d643842a72562fe6d86ddc38623830e42a17d444d44a2472b5f", "wx": "7e27893adb379d40a61668ad660edc256004bbfc12d55889fbd5121eac56a06e", "wy": "009a36f42598db7d643842a72562fe6d86ddc38623830e42a17d444d44a2472b5f"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200047e27893adb379d40a61668ad660edc256004bbfc12d55889fbd5121eac56a06e9a36f42598db7d643842a72562fe6d86ddc38623830e42a17d444d44a2472b5f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEfieJOts3nUCmFmitZg7cJWAEu/wS1ViJ\n+9USHqxWoG6aNvQlmNt9ZDhCpyVi/m2G3cOGI4MOQqF9RE1EokcrXw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 383, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "3045022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8022100aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa9d1c9e899ca306ad27fe1945de0242b89", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043b65f40a248d91a7e6377bfb5989f47d562bdaaf364ea982830f61b71957bda5e42108c5d388f2e173210f867633167eb0f5cbc693aa7bb9223ae8f1aaa26983", "wx": "3b65f40a248d91a7e6377bfb5989f47d562bdaaf364ea982830f61b71957bda5", "wy": "00e42108c5d388f2e173210f867633167eb0f5cbc693aa7bb9223ae8f1aaa26983"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200043b65f40a248d91a7e6377bfb5989f47d562bdaaf364ea982830f61b71957bda5e42108c5d388f2e173210f867633167eb0f5cbc693aa7bb9223ae8f1aaa26983", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEO2X0CiSNkafmN3v7WYn0fVYr2q82TqmC\ngw9htxlXvaXkIQjF04jy4XMhD4Z2MxZ+sPXLxpOqe7kiOujxqqJpgw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 384, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100bc0f3a2708cbe1438083451163be66f80a810a900cd135ddc076db7451917c17", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040585249ff4a3acbb996eed6f17a70a7b83a6dfe96e80edc0cd4cc7594e806d59b7576508dbb4eab123e0ed688a9e6625d056c7ad8134776252728dcae375cd84", "wx": "0585249ff4a3acbb996eed6f17a70a7b83a6dfe96e80edc0cd4cc7594e806d59", "wy": "00b7576508dbb4eab123e0ed688a9e6625d056c7ad8134776252728dcae375cd84"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200040585249ff4a3acbb996eed6f17a70a7b83a6dfe96e80edc0cd4cc7594e806d59b7576508dbb4eab123e0ed688a9e6625d056c7ad8134776252728dcae375cd84", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEBYUkn/SjrLuZbu1vF6cKe4Om3+lugO3A\nzUzHWU6AbVm3V2UI27TqsSPg7WiKnmYl0FbHrYE0d2JSco3K43XNhA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 385, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022062bf43ba34e73cc3c8922f26d64e3bf882f12dcc06e0b30c8363efa6badcff55", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04dd6ba66855e37b7fc91ad160bc4a7c5089f8633ac0e12298a6aba34db680e16b798f5573bd93756e39dd635d9c5f8e876364445a1c9a43f2918beb9137ba3b92", "wx": "00dd6ba66855e37b7fc91ad160bc4a7c5089f8633ac0e12298a6aba34db680e16b", "wy": "798f5573bd93756e39dd635d9c5f8e876364445a1c9a43f2918beb9137ba3b92"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004dd6ba66855e37b7fc91ad160bc4a7c5089f8633ac0e12298a6aba34db680e16b798f5573bd93756e39dd635d9c5f8e876364445a1c9a43f2918beb9137ba3b92", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE3WumaFXje3/JGtFgvEp8UIn4YzrA4SKY\npqujTbaA4Wt5j1VzvZN1bjndY12cX46HY2REWhyaQ/KRi+uRN7o7kg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 386, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022039422623a4386033ccfa96ad4f8228fb88ac9364ae8b3cd0715ee188c467572c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04ce2d8a8432670515b2133689d96e7369decfe994c87e39a28e5636897a360f2aba43f7fa77feba76de9634b6adfde47fb16f70b790bc9a1a5065ef16f6fd2467", "wx": "00ce2d8a8432670515b2133689d96e7369decfe994c87e39a28e5636897a360f2a", "wy": "00ba43f7fa77feba76de9634b6adfde47fb16f70b790bc9a1a5065ef16f6fd2467"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004ce2d8a8432670515b2133689d96e7369decfe994c87e39a28e5636897a360f2aba43f7fa77feba76de9634b6adfde47fb16f70b790bc9a1a5065ef16f6fd2467", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEzi2KhDJnBRWyEzaJ2W5zad7P6ZTIfjmi\njlY2iXo2Dyq6Q/f6d/66dt6WNLat/eR/sW9wt5C8mhpQZe8W9v0kZw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 387, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02206233bcf8558bae02cbd2518ae59c5c3501ab620efcbd7b40d8dd1f7288ff5dac", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046cefd89c949069c4ef5fefd20512a6fde92e08a2dfc408694a05d2a974bd0284ae4769496c219a59383a7fd6dc1e0690c25506264b0088e0362897e0da59103a", "wx": "6cefd89c949069c4ef5fefd20512a6fde92e08a2dfc408694a05d2a974bd0284", "wy": "00ae4769496c219a59383a7fd6dc1e0690c25506264b0088e0362897e0da59103a"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046cefd89c949069c4ef5fefd20512a6fde92e08a2dfc408694a05d2a974bd0284ae4769496c219a59383a7fd6dc1e0690c25506264b0088e0362897e0da59103a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEbO/YnJSQacTvX+/SBRKm/ekuCKLfxAhp\nSgXSqXS9AoSuR2lJbCGaWTg6f9bcHgaQwlUGJksAiOA2KJfg2lkQOg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 388, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02203bcf8558bae02cbd2518ae59c5c357e7170b54262d04bee3a9fcee6e38e84e1d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0433a6ee1a121ccab25b00fbecc860be15641a5baa4b4beb35d9a6dad35a1691fa36ba2323e463d684219a1bd15c5eb304878d82d1da113c52c7663cfae3f5751a", "wx": "33a6ee1a121ccab25b00fbecc860be15641a5baa4b4beb35d9a6dad35a1691fa", "wy": "36ba2323e463d684219a1bd15c5eb304878d82d1da113c52c7663cfae3f5751a"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000433a6ee1a121ccab25b00fbecc860be15641a5baa4b4beb35d9a6dad35a1691fa36ba2323e463d684219a1bd15c5eb304878d82d1da113c52c7663cfae3f5751a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEM6buGhIcyrJbAPvsyGC+FWQaW6pLS+s1\n2aba01oWkfo2uiMj5GPWhCGaG9FcXrMEh42C0doRPFLHZjz64/V1Gg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 389, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0220779f0ab175c0597a4a315cb38b86afce2e16a84c5a097dc753f9dcdc71d09c3a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a2d2e9810bf8f988af6cdf111f2f15062900d2ce06bb72c9c1dd1ca90d69c58c36c24fbc1323359ffc3d7cfdd66451dd3e950ad97cc7f1ddedf30e3aa4425c0f", "wx": "00a2d2e9810bf8f988af6cdf111f2f15062900d2ce06bb72c9c1dd1ca90d69c58c", "wy": "36c24fbc1323359ffc3d7cfdd66451dd3e950ad97cc7f1ddedf30e3aa4425c0f"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004a2d2e9810bf8f988af6cdf111f2f15062900d2ce06bb72c9c1dd1ca90d69c58c36c24fbc1323359ffc3d7cfdd66451dd3e950ad97cc7f1ddedf30e3aa4425c0f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEotLpgQv4+YivbN8RHy8VBikA0s4Gu3LJ\nwd0cqQ1pxYw2wk+8EyM1n/w9fP3WZFHdPpUK2XzH8d3t8w46pEJcDw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 390, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0220558bae02cbd2518ae59c5c357e7630cb680f5a3ee98045977a021c9091920d08", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e93d658ec3a9418daed0ee219d18180d0684fd676ed24f693bcdeb7e358ec44d6914850bd227eeb22bf22a02c3bfd628c769b0f0e50040b50fd3aaa324a1d4ce", "wx": "00e93d658ec3a9418daed0ee219d18180d0684fd676ed24f693bcdeb7e358ec44d", "wy": "6914850bd227eeb22bf22a02c3bfd628c769b0f0e50040b50fd3aaa324a1d4ce"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004e93d658ec3a9418daed0ee219d18180d0684fd676ed24f693bcdeb7e358ec44d6914850bd227eeb22bf22a02c3bfd628c769b0f0e50040b50fd3aaa324a1d4ce", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE6T1ljsOpQY2u0O4hnRgYDQaE/Wdu0k9p\nO83rfjWOxE1pFIUL0ifusivyKgLDv9Yox2mw8OUAQLUP06qjJKHUzg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 391, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022019ffa32fd51fb796c6154167347b7773d9058ddb148fdaef8c287fef848f2f7b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04ebe7e1278636290cb09c7d4554c71c117337d2ed40c77789433c27eaf4d4bc3273025752ca492238b622884c9fe287ce3723ae04ebfaa53505e14b8e86c5dbac", "wx": "00ebe7e1278636290cb09c7d4554c71c117337d2ed40c77789433c27eaf4d4bc32", "wy": "73025752ca492238b622884c9fe287ce3723ae04ebfaa53505e14b8e86c5dbac"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004ebe7e1278636290cb09c7d4554c71c117337d2ed40c77789433c27eaf4d4bc3273025752ca492238b622884c9fe287ce3723ae04ebfaa53505e14b8e86c5dbac", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE6+fhJ4Y2KQywnH1FVMccEXM30u1Ax3eJ\nQzwn6vTUvDJzAldSykkiOLYiiEyf4ofONyOuBOv6pTUF4UuOhsXbrA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 392, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02203d180992c1a38ddfd49ecf3d1813b0b195c69b06bbd41cf101fe40dac9c9e6ba", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d1150530bac21e35524ff9e1b7d00731401072d591e696a17bb388b4d7e5ca19bcc66bce3fc176d2da4a2cb954c836bf9b81f1913230ba99ea6e5054073ddf6f", "wx": "00d1150530bac21e35524ff9e1b7d00731401072d591e696a17bb388b4d7e5ca19", "wy": "00bcc66bce3fc176d2da4a2cb954c836bf9b81f1913230ba99ea6e5054073ddf6f"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004d1150530bac21e35524ff9e1b7d00731401072d591e696a17bb388b4d7e5ca19bcc66bce3fc176d2da4a2cb954c836bf9b81f1913230ba99ea6e5054073ddf6f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE0RUFMLrCHjVST/nht9AHMUAQctWR5pah\ne7OItNflyhm8xmvOP8F20tpKLLlUyDa/m4HxkTIwupnqblBUBz3fbw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 393, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100d39813c8e58536460cbfac4b0fa028e60d5d45c13612d79f9964a58cb33be185", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "045233a1deddbbdc29c0994fd43ceda3b020b35c465e02d1c12fd29017306be87bc31db5c0e32fbd3f045664acf088014a1116eb3379f24886b3a13f009628df42", "wx": "5233a1deddbbdc29c0994fd43ceda3b020b35c465e02d1c12fd29017306be87b", "wy": "00c31db5c0e32fbd3f045664acf088014a1116eb3379f24886b3a13f009628df42"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200045233a1deddbbdc29c0994fd43ceda3b020b35c465e02d1c12fd29017306be87bc31db5c0e32fbd3f045664acf088014a1116eb3379f24886b3a13f009628df42", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEUjOh3t273CnAmU/UPO2jsCCzXEZeAtHB\nL9KQFzBr6HvDHbXA4y+9PwRWZKzwiAFKERbrM3nySIazoT8AlijfQg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 394, "comment": "edge case for u1", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100e9fcbcae7e1d744cf6bf4ca0d8573312a131fcb3b90eb26bed6c7f6ba908ab53", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a160ab41fd3fe7d088ee874c8b82d8ae97c8ed99467579d01b97bade23ec46a6ae709a088bcbc72342996efeed0e913f8a5dd8c8878b1caec5c9e057e35d5cfe", "wx": "00a160ab41fd3fe7d088ee874c8b82d8ae97c8ed99467579d01b97bade23ec46a6", "wy": "00ae709a088bcbc72342996efeed0e913f8a5dd8c8878b1caec5c9e057e35d5cfe"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004a160ab41fd3fe7d088ee874c8b82d8ae97c8ed99467579d01b97bade23ec46a6ae709a088bcbc72342996efeed0e913f8a5dd8c8878b1caec5c9e057e35d5cfe", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEoWCrQf0/59CI7odMi4LYrpfI7ZlGdXnQ\nG5e63iPsRqaucJoIi8vHI0KZbv7tDpE/il3YyIeLHK7FyeBX411c/g==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 395, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022021f862ec7b9a0f5e3fbe5d774e20cc835816e92b513bb52effadc18c3f526295", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041d915ecbbf4c25bbc0d2216db6d1a3da8f80058653a24885494aff88fe1599fc8816898d958fa5431bb557d17b6b1b520c3fbdab6bc5984109d1468b6cc141f0", "wx": "1d915ecbbf4c25bbc0d2216db6d1a3da8f80058653a24885494aff88fe1599fc", "wy": "008816898d958fa5431bb557d17b6b1b520c3fbdab6bc5984109d1468b6cc141f0"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200041d915ecbbf4c25bbc0d2216db6d1a3da8f80058653a24885494aff88fe1599fc8816898d958fa5431bb557d17b6b1b520c3fbdab6bc5984109d1468b6cc141f0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEHZFey79MJbvA0iFtttGj2o+ABYZTokiF\nSUr/iP4VmfyIFomNlY+lQxu1V9F7axtSDD+9q2vFmEEJ0UaLbMFB8A==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 396, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022043f0c5d8f7341ebc7f7cbaee9c419906b02dd256a2776a5dff5b83187ea4c52a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "049598101f8704e2dd0e889ecab9ffe7a5e7536f3ee60d5f05111ce6f5a4ca0405c4c39bbca34c6a687c46a6ddff65e81d0a9a78a8c104f91ea6636a7c8ea6819a", "wx": "009598101f8704e2dd0e889ecab9ffe7a5e7536f3ee60d5f05111ce6f5a4ca0405", "wy": "00c4c39bbca34c6a687c46a6ddff65e81d0a9a78a8c104f91ea6636a7c8ea6819a"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200049598101f8704e2dd0e889ecab9ffe7a5e7536f3ee60d5f05111ce6f5a4ca0405c4c39bbca34c6a687c46a6ddff65e81d0a9a78a8c104f91ea6636a7c8ea6819a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAElZgQH4cE4t0OiJ7Kuf/npedTbz7mDV8F\nERzm9aTKBAXEw5u8o0xqaHxGpt3/ZegdCpp4qMEE+R6mY2p8jqaBmg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 397, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022065e928c572ce2e1abf3b1865ea62658a0844bb81f3b31f8cff0944a4bdf727bf", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0459ee9e0a000baefbe3fb59ea61d7370df77c58dee9d829b6c5e89faae019951bbf61a756c7a30d049bd37010b7b1c25670d4ddb6ceb8f1d7c7d449e393465959", "wx": "59ee9e0a000baefbe3fb59ea61d7370df77c58dee9d829b6c5e89faae019951b", "wy": "00bf61a756c7a30d049bd37010b7b1c25670d4ddb6ceb8f1d7c7d449e393465959"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000459ee9e0a000baefbe3fb59ea61d7370df77c58dee9d829b6c5e89faae019951bbf61a756c7a30d049bd37010b7b1c25670d4ddb6ceb8f1d7c7d449e393465959", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEWe6eCgALrvvj+1nqYdc3Dfd8WN7p2Cm2\nxeifquAZlRu/YadWx6MNBJvTcBC3scJWcNTdts648dfH1Enjk0ZZWQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 398, "comment": "edge case for u1", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02201ca11311d21c3019e67d4b56a7c1147dc45649b257459e6838af70c46233ab96", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d1f1ddd12c710d7c35617a0ed2fc35f4d09888f17034a47fe0a78415858e66a25fcda6abedc3a58ffc55bc5d9f320c60eb6b4c9a22833e13511b2e140ef14057", "wx": "00d1f1ddd12c710d7c35617a0ed2fc35f4d09888f17034a47fe0a78415858e66a2", "wy": "5fcda6abedc3a58ffc55bc5d9f320c60eb6b4c9a22833e13511b2e140ef14057"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004d1f1ddd12c710d7c35617a0ed2fc35f4d09888f17034a47fe0a78415858e66a25fcda6abedc3a58ffc55bc5d9f320c60eb6b4c9a22833e13511b2e140ef14057", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE0fHd0SxxDXw1YXoO0vw19NCYiPFwNKR/\n4KeEFYWOZqJfzaar7cOlj/xVvF2fMgxg62tMmiKDPhNRGy4UDvFAVw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 399, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100d55555555555555555555555555555547c74934474db157d2a8c3f088aced62a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043c9a5007f19ec624a73ce75fb61ab3e16736d519ee36381497f24bfd3bb691b534b42b0134e17222eff05f3b5477323a3224310b108c4a8fc9b17833128cb822", "wx": "3c9a5007f19ec624a73ce75fb61ab3e16736d519ee36381497f24bfd3bb691b5", "wy": "34b42b0134e17222eff05f3b5477323a3224310b108c4a8fc9b17833128cb822"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200043c9a5007f19ec624a73ce75fb61ab3e16736d519ee36381497f24bfd3bb691b534b42b0134e17222eff05f3b5477323a3224310b108c4a8fc9b17833128cb822", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEPJpQB/GexiSnPOdfthqz4Wc21RnuNjgU\nl/JL/Tu2kbU0tCsBNOFyIu/wXztUdzI6MiQxCxCMSo/JsXgzEoy4Ig==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 400, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100c1777c8853938e536213c02464a936000ba1e21c0fc62075d46c624e23b52f31", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f7a2f1027241c8514b2be7097a3eb5b208e8ffd09a700e5d72fc3af6964b3bbf08318a9043d959a8fc8bafa5d403d3490e4e45d9b1e156ff3e2aee38ece66e88", "wx": "00f7a2f1027241c8514b2be7097a3eb5b208e8ffd09a700e5d72fc3af6964b3bbf", "wy": "08318a9043d959a8fc8bafa5d403d3490e4e45d9b1e156ff3e2aee38ece66e88"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004f7a2f1027241c8514b2be7097a3eb5b208e8ffd09a700e5d72fc3af6964b3bbf08318a9043d959a8fc8bafa5d403d3490e4e45d9b1e156ff3e2aee38ece66e88", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE96LxAnJByFFLK+cJej61sgjo/9CacA5d\ncvw69pZLO78IMYqQQ9lZqPyLr6XUA9NJDk5F2bHhVv8+Ku447OZuiA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 401, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022030bbb794db588363b40679f6c182a50d3ce9679acdd3ffbe36d7813dacbdc818", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e58d8aba787d54ffcbe530c5ba5955f54e286d31f1a7558dce8924000d7a1b96f5acbdf479b313380325edbbadbc6287e08e98cc86e2ba8339873724437ce813", "wx": "00e58d8aba787d54ffcbe530c5ba5955f54e286d31f1a7558dce8924000d7a1b96", "wy": "00f5acbdf479b313380325edbbadbc6287e08e98cc86e2ba8339873724437ce813"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004e58d8aba787d54ffcbe530c5ba5955f54e286d31f1a7558dce8924000d7a1b96f5acbdf479b313380325edbbadbc6287e08e98cc86e2ba8339873724437ce813", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE5Y2Kunh9VP/L5TDFullV9U4obTHxp1WN\nzokkAA16G5b1rL30ebMTOAMl7butvGKH4I6YzIbiuoM5hzckQ3zoEw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 402, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02202c37fd995622c4fb7fffffffffffffffc7cee745110cb45ab558ed7c90c15a2f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04167df009cec2efd44991a523dc2fd4a13e4de3e76390382d4c1088593f33da65838f62138f2ed73fbc7be316ba5b6a79a4768fd1f4ea07df9eb0eeeef988ab73", "wx": "167df009cec2efd44991a523dc2fd4a13e4de3e76390382d4c1088593f33da65", "wy": "00838f62138f2ed73fbc7be316ba5b6a79a4768fd1f4ea07df9eb0eeeef988ab73"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004167df009cec2efd44991a523dc2fd4a13e4de3e76390382d4c1088593f33da65838f62138f2ed73fbc7be316ba5b6a79a4768fd1f4ea07df9eb0eeeef988ab73", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEFn3wCc7C79RJkaUj3C/UoT5N4+djkDgt\nTBCIWT8z2mWDj2ITjy7XP7x74xa6W2p5pHaP0fTqB9+esO7u+Yircw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 403, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02207fd995622c4fb7ffffffffffffffffff5d883ffab5b32652ccdcaa290fccb97d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0401a3a8df8aee278aa306844d60d9c5113e596d66cc92a3566ab0797cd01638250062494573ddc9a21706030fd795708b3fe0d0f224ac01e5957ad0d11d6ee265", "wx": "01a3a8df8aee278aa306844d60d9c5113e596d66cc92a3566ab0797cd0163825", "wy": "62494573ddc9a21706030fd795708b3fe0d0f224ac01e5957ad0d11d6ee265"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000401a3a8df8aee278aa306844d60d9c5113e596d66cc92a3566ab0797cd01638250062494573ddc9a21706030fd795708b3fe0d0f224ac01e5957ad0d11d6ee265", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEAaOo34ruJ4qjBoRNYNnFET5ZbWbMkqNW\narB5fNAWOCUAYklFc93JohcGAw/XlXCLP+DQ8iSsAeWVetDRHW7iZQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 404, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100ffb32ac4589f6ffffffffffffffffffebb107ff56b664ca599b954521f9972fa", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d6d0f45dbfa12ab4ea5b29a848c71923d1ecb57b148ec1c969b43662a18d00f988c2728d21508a421af6b612a4433c4b7c97f55dc12b24db2cf6cb7fada43f15", "wx": "00d6d0f45dbfa12ab4ea5b29a848c71923d1ecb57b148ec1c969b43662a18d00f9", "wy": "0088c2728d21508a421af6b612a4433c4b7c97f55dc12b24db2cf6cb7fada43f15"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004d6d0f45dbfa12ab4ea5b29a848c71923d1ecb57b148ec1c969b43662a18d00f988c2728d21508a421af6b612a4433c4b7c97f55dc12b24db2cf6cb7fada43f15", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE1tD0Xb+hKrTqWymoSMcZI9HstXsUjsHJ\nabQ2YqGNAPmIwnKNIVCKQhr2thKkQzxLfJf1XcErJNss9st/raQ/FQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 405, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02205622c4fb7fffffffffffffffffffffff928a8f1c7ac7bec1808b9f61c01ec327", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0469214198388da2a0d1a0c9464c6eb3731ad44e27287c17cd24bf73c3ada67c2a48dfabbfa5d9127fec9fb7986fb386cb5c7ebe3f609d95e71a70ad7f83334584", "wx": "69214198388da2a0d1a0c9464c6eb3731ad44e27287c17cd24bf73c3ada67c2a", "wy": "48dfabbfa5d9127fec9fb7986fb386cb5c7ebe3f609d95e71a70ad7f83334584"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000469214198388da2a0d1a0c9464c6eb3731ad44e27287c17cd24bf73c3ada67c2a48dfabbfa5d9127fec9fb7986fb386cb5c7ebe3f609d95e71a70ad7f83334584", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEaSFBmDiNoqDRoMlGTG6zcxrUTicofBfN\nJL9zw62mfCpI36u/pdkSf+yft5hvs4bLXH6+P2CdlecacK1/gzNFhA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 406, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022044104104104104104104104104104103b87853fd3b7d3f8e175125b4382f25ed", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0484672e2de042df2668775733c9b0cc716edd7d7534eb859279316ec5186d7733badc81e933abf3d4ce75fae00d1a47b30d69de8754666a294b4c925807dc3ecc", "wx": "0084672e2de042df2668775733c9b0cc716edd7d7534eb859279316ec5186d7733", "wy": "00badc81e933abf3d4ce75fae00d1a47b30d69de8754666a294b4c925807dc3ecc"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000484672e2de042df2668775733c9b0cc716edd7d7534eb859279316ec5186d7733badc81e933abf3d4ce75fae00d1a47b30d69de8754666a294b4c925807dc3ecc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEhGcuLeBC3yZod1czybDMcW7dfXU064WS\neTFuxRhtdzO63IHpM6vz1M51+uANGkezDWneh1RmailLTJJYB9w+zA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 407, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02202739ce739ce739ce739ce739ce739ce705560298d1f2f08dc419ac273a5b54d9", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04c9be9c1906a73789a9af1a677f60dd4163c5fa06c7f45c0993a63051aa0c0f303205debee5dc413e4abb3e1f6af550ac64c41b97e425cc2efa2a833c2ee72221", "wx": "00c9be9c1906a73789a9af1a677f60dd4163c5fa06c7f45c0993a63051aa0c0f30", "wy": "3205debee5dc413e4abb3e1f6af550ac64c41b97e425cc2efa2a833c2ee72221"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004c9be9c1906a73789a9af1a677f60dd4163c5fa06c7f45c0993a63051aa0c0f303205debee5dc413e4abb3e1f6af550ac64c41b97e425cc2efa2a833c2ee72221", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEyb6cGQanN4mprxpnf2DdQWPF+gbH9FwJ\nk6YwUaoMDzAyBd6+5dxBPkq7Ph9q9VCsZMQbl+QlzC76KoM8LuciIQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 408, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100b777777777777777777777777777777688e6a1fe808a97a348671222ff16b863", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04026794f7b5a84849f41141c68d3248f9c90c4de7edad4fb8f446e3076ffb7962c98e7b67192296efe04379c6a40280b4f113876981b44b73bb676a881f398790", "wx": "026794f7b5a84849f41141c68d3248f9c90c4de7edad4fb8f446e3076ffb7962", "wy": "00c98e7b67192296efe04379c6a40280b4f113876981b44b73bb676a881f398790"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004026794f7b5a84849f41141c68d3248f9c90c4de7edad4fb8f446e3076ffb7962c98e7b67192296efe04379c6a40280b4f113876981b44b73bb676a881f398790", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEAmeU97WoSEn0EUHGjTJI+ckMTeftrU+4\n9EbjB2/7eWLJjntnGSKW7+BDecakAoC08ROHaYG0S3O7Z2qIHzmHkA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 409, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02206492492492492492492492492492492406dd3a19b8d5fb875235963c593bd2d3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0418bd65dd46f8c0e326553be55e5e234bb43188ac1fddb37003d12f091aa7a1b9e9c00a03e4d5452ba9a607951d4e4d8a7391a952109d96599266d1e2d9ab2199", "wx": "18bd65dd46f8c0e326553be55e5e234bb43188ac1fddb37003d12f091aa7a1b9", "wy": "00e9c00a03e4d5452ba9a607951d4e4d8a7391a952109d96599266d1e2d9ab2199"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000418bd65dd46f8c0e326553be55e5e234bb43188ac1fddb37003d12f091aa7a1b9e9c00a03e4d5452ba9a607951d4e4d8a7391a952109d96599266d1e2d9ab2199", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEGL1l3Ub4wOMmVTvlXl4jS7QxiKwf3bNw\nA9EvCRqnobnpwAoD5NVFK6mmB5UdTk2Kc5GpUhCdllmSZtHi2ashmQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 410, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100955555555555555555555555555555547c74934474db157d2a8c3f088aced62c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b86ae764f2f95eb9331af538fa516fd78435794ebb244c090c6d6b286750f94cf3712f767495a10f2e81350662af3ba09defa2e0e6f27eceea35513032dafb61", "wx": "00b86ae764f2f95eb9331af538fa516fd78435794ebb244c090c6d6b286750f94c", "wy": "00f3712f767495a10f2e81350662af3ba09defa2e0e6f27eceea35513032dafb61"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004b86ae764f2f95eb9331af538fa516fd78435794ebb244c090c6d6b286750f94cf3712f767495a10f2e81350662af3ba09defa2e0e6f27eceea35513032dafb61", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEuGrnZPL5XrkzGvU4+lFv14Q1eU67JEwJ\nDG1rKGdQ+UzzcS92dJWhDy6BNQZirzugne+i4Obyfs7qNVEwMtr7YQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 411, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc02202aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa3e3a49a23a6d8abe95461f8445676b17", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e045cc5a9414e45e63f1b08648e20e229a9950ab56ec304e1b907989e81af2bf21e52db489853dc470713aaecd6aadc7bfd8504a9c82d0243f6e774600b5ea0a", "wx": "00e045cc5a9414e45e63f1b08648e20e229a9950ab56ec304e1b907989e81af2bf", "wy": "21e52db489853dc470713aaecd6aadc7bfd8504a9c82d0243f6e774600b5ea0a"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004e045cc5a9414e45e63f1b08648e20e229a9950ab56ec304e1b907989e81af2bf21e52db489853dc470713aaecd6aadc7bfd8504a9c82d0243f6e774600b5ea0a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE4EXMWpQU5F5j8bCGSOIOIpqZUKtW7DBO\nG5B5iega8r8h5S20iYU9xHBxOq7Naq3Hv9hQSpyC0CQ/bndGALXqCg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 412, "comment": "edge case for u2", "msg": "313233343030", "sig": "304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc022100bffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364143", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0437e377faea8a867f494bb402032c70c12af6fd57feb3866bfc5a5fc1d0a909f008dcbc53fd41b67073a4e71a81f3fe578da4d5add0d698041a9b7f38a9a19bff", "wx": "37e377faea8a867f494bb402032c70c12af6fd57feb3866bfc5a5fc1d0a909f0", "wy": "08dcbc53fd41b67073a4e71a81f3fe578da4d5add0d698041a9b7f38a9a19bff"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000437e377faea8a867f494bb402032c70c12af6fd57feb3866bfc5a5fc1d0a909f008dcbc53fd41b67073a4e71a81f3fe578da4d5add0d698041a9b7f38a9a19bff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEN+N3+uqKhn9JS7QCAyxwwSr2/Vf+s4Zr\n/FpfwdCpCfAI3LxT/UG2cHOk5xqB8/5XjaTVrdDWmAQam384qaGb/w==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 413, "comment": "edge case for u2", "msg": "313233343030", "sig": "304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc0220185ddbca6dac41b1da033cfb60c152869e74b3cd66e9ffdf1b6bc09ed65ee40c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0409a33110676e4a4f8a977150a7be291e0a269cae9678710b1d87f8068b0fe961043a6f64e86278a656a39e4468b3472597afb2dcd930ccba1b1ea2c988c13450", "wx": "09a33110676e4a4f8a977150a7be291e0a269cae9678710b1d87f8068b0fe961", "wy": "043a6f64e86278a656a39e4468b3472597afb2dcd930ccba1b1ea2c988c13450"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000409a33110676e4a4f8a977150a7be291e0a269cae9678710b1d87f8068b0fe961043a6f64e86278a656a39e4468b3472597afb2dcd930ccba1b1ea2c988c13450", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAECaMxEGduSk+Kl3FQp74pHgomnK6WeHEL\nHYf4BosP6WEEOm9k6GJ4plajnkRos0cll6+y3NkwzLobHqLJiME0UA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 414, "comment": "point duplication during verification", "msg": "313233343030", "sig": "3045022032b0d10d8d0e04bc8d4d064d270699e87cffc9b49c5c20730e1c26f6105ddcda022100c55205f423611c7e96615bcc20141945fde24bbce956d49cd43e14ab4cef3659", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0409a33110676e4a4f8a977150a7be291e0a269cae9678710b1d87f8068b0fe961fbc5909b179d8759a95c61bb974cb8da68504d2326cf3345e4e15d35773ec7df", "wx": "09a33110676e4a4f8a977150a7be291e0a269cae9678710b1d87f8068b0fe961", "wy": "00fbc5909b179d8759a95c61bb974cb8da68504d2326cf3345e4e15d35773ec7df"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000409a33110676e4a4f8a977150a7be291e0a269cae9678710b1d87f8068b0fe961fbc5909b179d8759a95c61bb974cb8da68504d2326cf3345e4e15d35773ec7df", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAECaMxEGduSk+Kl3FQp74pHgomnK6WeHEL\nHYf4BosP6WH7xZCbF52HWalcYbuXTLjaaFBNIybPM0Xk4V01dz7H3w==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 415, "comment": "duplication bug", "msg": "313233343030", "sig": "3045022032b0d10d8d0e04bc8d4d064d270699e87cffc9b49c5c20730e1c26f6105ddcda022100c55205f423611c7e96615bcc20141945fde24bbce956d49cd43e14ab4cef3659", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b7021faebf4081a63094d8ea78ca2004d02a303bbf363470ea4a649b08c1995bde5efba25c9d2f490f181e16795d5c75f83b5c11c81d67ede2ea8df81d970cef", "wx": "00b7021faebf4081a63094d8ea78ca2004d02a303bbf363470ea4a649b08c1995b", "wy": "00de5efba25c9d2f490f181e16795d5c75f83b5c11c81d67ede2ea8df81d970cef"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004b7021faebf4081a63094d8ea78ca2004d02a303bbf363470ea4a649b08c1995bde5efba25c9d2f490f181e16795d5c75f83b5c11c81d67ede2ea8df81d970cef", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEtwIfrr9AgaYwlNjqeMogBNAqMDu/NjRw\n6kpkmwjBmVveXvuiXJ0vSQ8YHhZ5XVx1+DtcEcgdZ+3i6o34HZcM7w==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 416, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "3044022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0022033333333333333333333333333333332f222f8faefdb533f265d461c29a47373", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044de024ff2abe7af94fb5e9f53c87d4b4bf1c5447c7c39a9280839f12e52e38d83ce6941fc329978e794abf91a25e83463f8eabcf106d76d4dcd92c0ae05493f3", "wx": "4de024ff2abe7af94fb5e9f53c87d4b4bf1c5447c7c39a9280839f12e52e38d8", "wy": "3ce6941fc329978e794abf91a25e83463f8eabcf106d76d4dcd92c0ae05493f3"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200044de024ff2abe7af94fb5e9f53c87d4b4bf1c5447c7c39a9280839f12e52e38d83ce6941fc329978e794abf91a25e83463f8eabcf106d76d4dcd92c0ae05493f3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAETeAk/yq+evlPten1PIfUtL8cVEfHw5qS\ngIOfEuUuONg85pQfwymXjnlKv5GiXoNGP46rzxBtdtTc2SwK4FST8w==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 417, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "3045022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04425e137c5fe08e842c3bcc4efbb6c4bca89edda8d6beb130e14899de2f20b74bb1af66ef5baead32e7892160deddcb57f43503104dbc331fa20a8de376e5bd17", "wx": "425e137c5fe08e842c3bcc4efbb6c4bca89edda8d6beb130e14899de2f20b74b", "wy": "00b1af66ef5baead32e7892160deddcb57f43503104dbc331fa20a8de376e5bd17"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004425e137c5fe08e842c3bcc4efbb6c4bca89edda8d6beb130e14899de2f20b74bb1af66ef5baead32e7892160deddcb57f43503104dbc331fa20a8de376e5bd17", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEQl4TfF/gjoQsO8xO+7bEvKie3ajWvrEw\n4UiZ3i8gt0uxr2bvW66tMueJIWDe3ctX9DUDEE28Mx+iCo3jduW9Fw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 418, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3046022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5022100b6db6db6db6db6db6db6db6db6db6db5f30f30127d33e02aad96438927022e9c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046c63d82f22bbeb1bd1c8eba4c680ae17dc2b4d196a0da0e191dc79fefd85e367a883018fb8d160ca01d17234fa0b060a619215ccb9dfea629d6bf92cfd8ed34b", "wx": "6c63d82f22bbeb1bd1c8eba4c680ae17dc2b4d196a0da0e191dc79fefd85e367", "wy": "00a883018fb8d160ca01d17234fa0b060a619215ccb9dfea629d6bf92cfd8ed34b"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046c63d82f22bbeb1bd1c8eba4c680ae17dc2b4d196a0da0e191dc79fefd85e367a883018fb8d160ca01d17234fa0b060a619215ccb9dfea629d6bf92cfd8ed34b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEbGPYLyK76xvRyOukxoCuF9wrTRlqDaDh\nkdx5/v2F42eogwGPuNFgygHRcjT6CwYKYZIVzLnf6mKda/ks/Y7TSw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 419, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3046022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee502210099999999999999999999999999999998d668eaf0cf91f9bd7317d2547ced5a5a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04dbf77c566f7483d7407f0095b8b468efdf85b7476e614b8658bcf5e71e6fd588413b50407df0def01b8fcba5621028a6cb0972831c893e3d3c20065b75a8e8e6", "wx": "00dbf77c566f7483d7407f0095b8b468efdf85b7476e614b8658bcf5e71e6fd588", "wy": "413b50407df0def01b8fcba5621028a6cb0972831c893e3d3c20065b75a8e8e6"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004dbf77c566f7483d7407f0095b8b468efdf85b7476e614b8658bcf5e71e6fd588413b50407df0def01b8fcba5621028a6cb0972831c893e3d3c20065b75a8e8e6", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE2/d8Vm90g9dAfwCVuLRo79+Ft0duYUuG\nWLz15x5v1YhBO1BAffDe8BuPy6ViECimywlygxyJPj08IAZbdajo5g==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 420, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3045022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5022066666666666666666666666666666665e445f1f5dfb6a67e4cba8c385348e6e7", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a7967bfb54cfbd8d13492b9ac421d967d7c4b0a3b18efb6408a424914789c8ef90969628e6553898c978ba48eb852714f9e220e5e93cada91478ce1af8948fd8", "wx": "00a7967bfb54cfbd8d13492b9ac421d967d7c4b0a3b18efb6408a424914789c8ef", "wy": "0090969628e6553898c978ba48eb852714f9e220e5e93cada91478ce1af8948fd8"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004a7967bfb54cfbd8d13492b9ac421d967d7c4b0a3b18efb6408a424914789c8ef90969628e6553898c978ba48eb852714f9e220e5e93cada91478ce1af8948fd8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEp5Z7+1TPvY0TSSuaxCHZZ9fEsKOxjvtk\nCKQkkUeJyO+QlpYo5lU4mMl4ukjrhScU+eIg5ek8rakUeM4a+JSP2A==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 421, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3045022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5022049249249249249249249249249249248c79facd43214c011123c1b03a93412a5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04325fd7ab7bc8cd7d859687937a90083f1b46776c2b8fdf4ce2bd9e2e808c68b1bb0d689ca6f542c094c99c71918f5455c7608514149148470494e05aa4ff6110", "wx": "325fd7ab7bc8cd7d859687937a90083f1b46776c2b8fdf4ce2bd9e2e808c68b1", "wy": "00bb0d689ca6f542c094c99c71918f5455c7608514149148470494e05aa4ff6110"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004325fd7ab7bc8cd7d859687937a90083f1b46776c2b8fdf4ce2bd9e2e808c68b1bb0d689ca6f542c094c99c71918f5455c7608514149148470494e05aa4ff6110", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEMl/Xq3vIzX2FloeTepAIPxtGd2wrj99M\n4r2eLoCMaLG7DWicpvVCwJTJnHGRj1RVx2CFFBSRSEcElOBapP9hEA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 422, "comment": "extreme value for k", "msg": "313233343030", "sig": "3045022100c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee502200eb10e5ab95f2f275348d82ad2e4d7949c8193800d8c9c75df58e343f0ebba7b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d637dc3c63060a0b21b80d6dc8a97ab6a543c21c18cb5e5c63ad80c3b86050fb1d68bb9b9c36ade49ddd84c7fa3ae5c70f45549592ee03a23a490a891cc70ebb", "wx": "00d637dc3c63060a0b21b80d6dc8a97ab6a543c21c18cb5e5c63ad80c3b86050fb", "wy": "1d68bb9b9c36ade49ddd84c7fa3ae5c70f45549592ee03a23a490a891cc70ebb"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004d637dc3c63060a0b21b80d6dc8a97ab6a543c21c18cb5e5c63ad80c3b86050fb1d68bb9b9c36ade49ddd84c7fa3ae5c70f45549592ee03a23a490a891cc70ebb", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE1jfcPGMGCgshuA1tyKl6tqVDwhwYy15c\nY62Aw7hgUPsdaLubnDat5J3dhMf6OuXHD0VUlZLuA6I6SQqJHMcOuw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 423, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "3044022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798022055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046bc87ce6047a3164be15ad781ef32d12bad8caaef7707ac3e15a53ed75efc90c8eee286e2ac0c8f9f6f0350b8bba94b6c5bfade87ba211adc0cad5f3818091e0", "wx": "6bc87ce6047a3164be15ad781ef32d12bad8caaef7707ac3e15a53ed75efc90c", "wy": "008eee286e2ac0c8f9f6f0350b8bba94b6c5bfade87ba211adc0cad5f3818091e0"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046bc87ce6047a3164be15ad781ef32d12bad8caaef7707ac3e15a53ed75efc90c8eee286e2ac0c8f9f6f0350b8bba94b6c5bfade87ba211adc0cad5f3818091e0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEa8h85gR6MWS+Fa14HvMtErrYyq73cHrD\n4VpT7XXvyQyO7ihuKsDI+fbwNQuLupS2xb+t6HuiEa3AytXzgYCR4A==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 424, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3045022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798022100b6db6db6db6db6db6db6db6db6db6db5f30f30127d33e02aad96438927022e9c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04dac247040488bec28dc3ec9a81a990701f45c0ba4bb6e22573da400efaa65e2e7de375486e1757b6c7b4269bee423edb84c7f4b333c1557b5ddfba0dd983ccf3", "wx": "00dac247040488bec28dc3ec9a81a990701f45c0ba4bb6e22573da400efaa65e2e", "wy": "7de375486e1757b6c7b4269bee423edb84c7f4b333c1557b5ddfba0dd983ccf3"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004dac247040488bec28dc3ec9a81a990701f45c0ba4bb6e22573da400efaa65e2e7de375486e1757b6c7b4269bee423edb84c7f4b333c1557b5ddfba0dd983ccf3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE2sJHBASIvsKNw+yagamQcB9FwLpLtuIl\nc9pADvqmXi5943VIbhdXtse0JpvuQj7bhMf0szPBVXtd37oN2YPM8w==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 425, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3045022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f8179802210099999999999999999999999999999998d668eaf0cf91f9bd7317d2547ced5a5a", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041d4d073190b327ab4e4f5ace8d8c8b68e100fd2565a1a4c4610bca309fe6a9c3e274a19b41e496b0832e9e42f5229fc000706c966d2557f3441d323d8faca129", "wx": "1d4d073190b327ab4e4f5ace8d8c8b68e100fd2565a1a4c4610bca309fe6a9c3", "wy": "00e274a19b41e496b0832e9e42f5229fc000706c966d2557f3441d323d8faca129"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200041d4d073190b327ab4e4f5ace8d8c8b68e100fd2565a1a4c4610bca309fe6a9c3e274a19b41e496b0832e9e42f5229fc000706c966d2557f3441d323d8faca129", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEHU0HMZCzJ6tOT1rOjYyLaOEA/SVloaTE\nYQvKMJ/mqcPidKGbQeSWsIMunkL1Ip/AAHBslm0lV/NEHTI9j6yhKQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 426, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3044022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798022066666666666666666666666666666665e445f1f5dfb6a67e4cba8c385348e6e7", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04274ba8da21e4ed44e218320daa103f0d4227bb351b67d84ad2628629b82fa8274c90d1dcfe55fe7ee66571ff4526c755cac8c8ed16b01c4db830b7dd9deae749", "wx": "274ba8da21e4ed44e218320daa103f0d4227bb351b67d84ad2628629b82fa827", "wy": "4c90d1dcfe55fe7ee66571ff4526c755cac8c8ed16b01c4db830b7dd9deae749"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004274ba8da21e4ed44e218320daa103f0d4227bb351b67d84ad2628629b82fa8274c90d1dcfe55fe7ee66571ff4526c755cac8c8ed16b01c4db830b7dd9deae749", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEJ0uo2iHk7UTiGDINqhA/DUInuzUbZ9hK\n0mKGKbgvqCdMkNHc/lX+fuZlcf9FJsdVysjI7RawHE24MLfdnernSQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 427, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "3044022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798022049249249249249249249249249249248c79facd43214c011123c1b03a93412a5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b45406f951d31975e953ac11c25c238046a7975dd2fbb38d890913c1c8b451cbcae0be688e6e400a9265bd9a59ba1047e164306ef6cd358bc0ff00e9e027e957", "wx": "00b45406f951d31975e953ac11c25c238046a7975dd2fbb38d890913c1c8b451cb", "wy": "00cae0be688e6e400a9265bd9a59ba1047e164306ef6cd358bc0ff00e9e027e957"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004b45406f951d31975e953ac11c25c238046a7975dd2fbb38d890913c1c8b451cbcae0be688e6e400a9265bd9a59ba1047e164306ef6cd358bc0ff00e9e027e957", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEtFQG+VHTGXXpU6wRwlwjgEanl13S+7ON\niQkTwci0UcvK4L5ojm5ACpJlvZpZuhBH4WQwbvbNNYvA/wDp4CfpVw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 428, "comment": "extreme value for k", "msg": "313233343030", "sig": "3044022079be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f8179802200eb10e5ab95f2f275348d82ad2e4d7949c8193800d8c9c75df58e343f0ebba7b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8", "wx": "79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798", "wy": "483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEeb5mfvncu6xVoGKVzocLBwKb/NstzijZ\nWfKBWxb4F5hIOtp3JqPEZV2k+/wOEQio/Re0SKaFVBmcR9CP+xDUuA==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 429, "comment": "testing point duplication", "msg": "313233343030", "sig": "30440220342dae751a63a3ca8189cf342b3b34eaaa2565e2c7e26121c1bfd5435447f1c302202492492492492492492492492492492463cfd66a190a6008891e0d81d49a0952", "result": "invalid", "flags": []}, {"tcId": 430, "comment": "testing point duplication", "msg": "313233343030", "sig": "3045022100cbd2518ae59c5c357e7630cbd4c4cb1410897703e7663f19fe1289497bee4f7e02202492492492492492492492492492492463cfd66a190a6008891e0d81d49a0952", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798b7c52588d95c3b9aa25b0403f1eef75702e84bb7597aabe663b82f6f04ef2777", "wx": "79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798", "wy": "00b7c52588d95c3b9aa25b0403f1eef75702e84bb7597aabe663b82f6f04ef2777"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798b7c52588d95c3b9aa25b0403f1eef75702e84bb7597aabe663b82f6f04ef2777", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEeb5mfvncu6xVoGKVzocLBwKb/NstzijZ\nWfKBWxb4F5i3xSWI2Vw7mqJbBAPx7vdXAuhLt1l6q+ZjuC9vBO8ndw==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 431, "comment": "testing point duplication", "msg": "313233343030", "sig": "30440220342dae751a63a3ca8189cf342b3b34eaaa2565e2c7e26121c1bfd5435447f1c302202492492492492492492492492492492463cfd66a190a6008891e0d81d49a0952", "result": "invalid", "flags": []}, {"tcId": 432, "comment": "testing point duplication", "msg": "313233343030", "sig": "3045022100cbd2518ae59c5c357e7630cbd4c4cb1410897703e7663f19fe1289497bee4f7e02202492492492492492492492492492492463cfd66a190a6008891e0d81d49a0952", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04782c8ed17e3b2a783b5464f33b09652a71c678e05ec51e84e2bcfc663a3de963af9acb4280b8c7f7c42f4ef9aba6245ec1ec1712fd38a0fa96418d8cd6aa6152", "wx": "782c8ed17e3b2a783b5464f33b09652a71c678e05ec51e84e2bcfc663a3de963", "wy": "00af9acb4280b8c7f7c42f4ef9aba6245ec1ec1712fd38a0fa96418d8cd6aa6152"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004782c8ed17e3b2a783b5464f33b09652a71c678e05ec51e84e2bcfc663a3de963af9acb4280b8c7f7c42f4ef9aba6245ec1ec1712fd38a0fa96418d8cd6aa6152", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEeCyO0X47Kng7VGTzOwllKnHGeOBexR6E\n4rz8Zjo96WOvmstCgLjH98QvTvmrpiRewewXEv04oPqWQY2M1qphUg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 433, "comment": "pseudorandom signature", "msg": "", "sig": "3044022022bb0000e9648a0ee659f9b6a9ab6513dc90ab968ec49d3953f64c82bddc852002204aa0dfd047b0786e118231eff7e86311487ec9d1bc84aaef1f736f4178c288f9", "result": "valid", "flags": []}, {"tcId": 434, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "3046022100855b60549e0d84ed3959ab2800eee0cbc9a2cecae2e510ed51e9f27975cdcc4e022100c60dcc80a3dcce9911cb9cfba123b6a6f85d20ab695a9ee7d46e0bd9eeb337f9", "result": "valid", "flags": []}, {"tcId": 435, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "304502205cd73aa58188658ea513f8ecf0d9e2da9eb5d6bcc7cbadcd6a4a8e0cba5176de022100a9f090bb5d3fcf5b7fa7e16d287718773f5f4ba0973b329a3788cd45b4bd3765", "result": "valid", "flags": []}, {"tcId": 436, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "304502207f9eebad2a323f8346445d1e2fcde47aba4c96ad4686172bebcffaa604e8dbe3022100ec09d731e58e3a337ea03ab72612b1f801b88eac571bd3a031250ac6f2e34fda", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40caff00000001060492d5a5673e0f25d8d50fb7e58c49d86d46d4216955e0aa3d40e1", "wx": "6e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40caff", "wy": "01060492d5a5673e0f25d8d50fb7e58c49d86d46d4216955e0aa3d40e1"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40caff00000001060492d5a5673e0f25d8d50fb7e58c49d86d46d4216955e0aa3d40e1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEboI1VUUpFAmRgsaywdbwtdKNUMzQBa8s\n4bulQapAyv8AAAABBgSS1aVnPg8l2NUPt+WMSdhtRtQhaVXgqj1A4Q==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 437, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3046022100c28ad156fe809ed36dc80812ae2f32d84dbbfbb9400123305c332551c4f10d39022100fc5b95b0c7fbc2e7cc4ec1bf01020f8050260ce2ca45c3bf5b64a7b2aeeface9", "result": "valid", "flags": []}, {"tcId": 438, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3046022100a36bfde0f5e23f6e3b3d6cc80ede3d9e4ea1c2cb9337221388f70aa52dec5e53022100c27e1db10d29720a120c2625c7e0756790200b2d9bcceb170a1356e1d5477e3c", "result": "valid", "flags": []}, {"tcId": 439, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3045022100818dee9730f01b3f525daa9cc0d5423b0c4af0414c647b6e0bc88546db9c0d75022061c16a90de1dbb1ab1e3c7917e891632f557f493b4106f225517ef186abc0ff9", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40cafffffffffef9fb6d2a5a98c1f0da272af0481a73b62792b92bde96aa1e55c2bb4e", "wx": "6e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40caff", "wy": "00fffffffef9fb6d2a5a98c1f0da272af0481a73b62792b92bde96aa1e55c2bb4e"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40cafffffffffef9fb6d2a5a98c1f0da272af0481a73b62792b92bde96aa1e55c2bb4e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEboI1VUUpFAmRgsaywdbwtdKNUMzQBa8s\n4bulQapAyv/////++fttKlqYwfDaJyrwSBpztieSuSvelqoeVcK7Tg==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 440, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3044022053cc0edfc688e3d264ed4755f9cf006418e16e24dc978453a6ef14fbecff617f022024694c00d38c13259973aa6db88adf7cc49b5673e628b3c65e7fe06f2665db86", "result": "valid", "flags": []}, {"tcId": 441, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3045022100cacd14406211ed85d8de1b50e167f59dec688574524c6fc1762c9268214e3bba0220289cf8949f717626c25833a0a159d63d77d022b48e161007464f59f5072df8a7", "result": "valid", "flags": []}, {"tcId": 442, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "3045022100c1a4c9d1feef48045813d911f6abb188502e06d26b34194f2deaa356e578a76902204063d3367b2bab52bf9fbc4cd3f670667569ccb1cfb05a7c7c156622ba593d45", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04000000013fd22248d64d95f73c29b48ab48631850be503fd00f8468b5f0f70e0f6ee7aa43bc2c6fd25b1d8269241cbdd9dbb0dac96dc96231f430705f838717d", "wx": "013fd22248d64d95f73c29b48ab48631850be503fd00f8468b5f0f70e0", "wy": "00f6ee7aa43bc2c6fd25b1d8269241cbdd9dbb0dac96dc96231f430705f838717d"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004000000013fd22248d64d95f73c29b48ab48631850be503fd00f8468b5f0f70e0f6ee7aa43bc2c6fd25b1d8269241cbdd9dbb0dac96dc96231f430705f838717d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEAAAAAT/SIkjWTZX3PCm0irSGMYUL5QP9\nAPhGi18PcOD27nqkO8LG/SWx2CaSQcvdnbsNrJbcliMfQwcF+DhxfQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 443, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "304402205af5683467b4cb3d68bd168c5fe229a07b7eb1de2f92b8a9743fb46c3691872a02204cbe35cbe66805729e907462169c13b5c4feb497aab658774bec1ecd7bd8863c", "result": "valid", "flags": []}, {"tcId": 444, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "30440220051e5f825a5c29f92a108a3ddcdcbf7ffce37ab32915985978512e89a2a83b0c02206340ac187077a7fb4373537b4595a39299ad0ba351db23bcae9176125c61eded", "result": "valid", "flags": []}, {"tcId": 445, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "3045022034e28decbf74abd30f55155ee2ff96f621066001a853acef916cdb39a7d07b4002210087561a96167016fb7dd6f3c7259f8c563f2144332ebb9a48e93b9512d3bf5392", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0425afd689acabaed67c1f296de59406f8c550f57146a0b4ec2c97876dfffffffffa46a76e520322dfbc491ec4f0cc197420fc4ea5883d8f6dd53c354bc4f67c35", "wx": "25afd689acabaed67c1f296de59406f8c550f57146a0b4ec2c97876dffffffff", "wy": "00fa46a76e520322dfbc491ec4f0cc197420fc4ea5883d8f6dd53c354bc4f67c35"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000425afd689acabaed67c1f296de59406f8c550f57146a0b4ec2c97876dfffffffffa46a76e520322dfbc491ec4f0cc197420fc4ea5883d8f6dd53c354bc4f67c35", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMF<PERSON><PERSON>EAYHKoZIzj0CAQYFK4EEAAoDQgAEJa/WiayrrtZ8Hylt5ZQG+MVQ9XFGoLTs\nLJeHbf/////6RqduUgMi37xJHsTwzBl0IPxOpYg9j23VPDVLxPZ8NQ==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 446, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3046022100cafded1bc98a4bdfe76e1df9ab75342a7fed16b0c1688a2e744d871b9404be14022100a11fcb57d1212068afed86a37e7291aa02061e75b883e9b9a7af3a52e81f5033", "result": "valid", "flags": []}, {"tcId": 447, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "30440220254f9541aebd4fca9ce7136fa8e6ed6367778afedf36201779b0ea6a61a82f3b022038668103eebca5e786e05dfffd8b9f1d87d4a1558b1cdfc0eeb98a606ab654c6", "result": "valid", "flags": []}, {"tcId": 448, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3046022100f998e8322b2a1101bbdf9b8b80bc147e5225632cdb6b1a8c4c4c25c29cd3319d022100ee6b3e7b59621fd62b3253ef646ad7cd4a4d53dd11372038b0b314ced0e2e5a9", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d12e6c66b67734c3c84d2601cf5d35dc097e27637f0aca4a4fdb74b6aadd3bb93f5bdff88bd5736df898e699006ed750f11cf07c5866cd7ad70c7121ffffffff", "wx": "00d12e6c66b67734c3c84d2601cf5d35dc097e27637f0aca4a4fdb74b6aadd3bb9", "wy": "3f5bdff88bd5736df898e699006ed750f11cf07c5866cd7ad70c7121ffffffff"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004d12e6c66b67734c3c84d2601cf5d35dc097e27637f0aca4a4fdb74b6aadd3bb93f5bdff88bd5736df898e699006ed750f11cf07c5866cd7ad70c7121ffffffff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMF<PERSON><PERSON>EAYHKoZIzj0CAQYFK4EEAAoDQgAE0S5sZrZ3NMPITSYBz1013Al+J2N/CspK\nT9t0tqrdO7k/W9/4i9VzbfiY5pkAbtdQ8RzwfFhmzXrXDHEh/////w==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 449, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "304402200c426a3f25f5d0250928ac4e5ea03cf949d34444283ac18a49ec638a2a6ea4c5022009f0df2fe78f8ce301057c734cf3c2505d7219775fb778758461360b168e2c8e", "result": "valid", "flags": []}, {"tcId": 450, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "304402201309ba742999ad66aef104cc140246bc576bd14acc6bb0be728577e49f4f8ed5022031e29fedcab5999d66b27a4f4ffc950dcc8066fb7cdbad9a6362270c066a500a", "result": "valid", "flags": []}, {"tcId": 451, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3045022100cb565ebfd48044d17d2e241b7cd5fec6089e3d0bee83516b710f68d583ccd1c6022026d6d5a1f12ae063528e7e4b1a6a9c5c760af38f2828db9407c439484f2ae9c1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046d4a7f60d4774a4f0aa8bbdedb953c7eea7909407e3164755664bc2800000000e659d34e4df38d9e8c9eaadfba36612c769195be86c77aac3f36e78b538680fb", "wx": "6d4a7f60d4774a4f0aa8bbdedb953c7eea7909407e3164755664bc2800000000", "wy": "00e659d34e4df38d9e8c9eaadfba36612c769195be86c77aac3f36e78b538680fb"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046d4a7f60d4774a4f0aa8bbdedb953c7eea7909407e3164755664bc2800000000e659d34e4df38d9e8c9eaadfba36612c769195be86c77aac3f36e78b538680fb", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEbUp/YNR3Sk8KqLve25U8fup5CUB+MWR1\nVmS8KAAAAADmWdNOTfONnoyeqt+6NmEsdpGVvobHeqw/NueLU4aA+w==\n-----END PUBLIC KEY-----", "sha": "SHA3-512", "type": "EcdsaVerify", "tests": [{"tcId": 452, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3045022100a6c419f478007ce4eb07cd8deb248b8d9d11e16c02364e18391ab934c6f3e91d0220363ab461b8d40c864998a6dfa9c9c77419930b9336f0cd471b74786b09aba27b", "result": "valid", "flags": []}, {"tcId": 453, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3046022100e22ab46f883c6ea58de97f982ffd3ef581749fe5568f8121761566509145b0c8022100fc4a53daf4122aa10b98a4d18c2e4920b37744447f0d843ff9ed1d79d482d73c", "result": "valid", "flags": []}, {"tcId": 454, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3045022100921a16ea241e69c9d4f3bde6ba2cc7e10a27c9dfd8b92076d0a4a6d9f8ae0ab3022039b66ee2afd7db1099fee2cd8c69c9f1ea29047efacc1c6e8ee92e5a2244a40b", "result": "valid", "flags": []}]}]}