from dataclasses import dataclass
from typing import List, Callable, Optional, Any

@dataclass
class Switch:
    key: str
    text: str
    default: bool
    subtext: Optional[str] = ...
    icon: Optional[str] = ...
    on_change: Optional[Callable[[bool], None]] = ...
    type: str = ...

@dataclass
class Selector:
    key: str
    text: str
    default: int
    items: List[str]
    icon: Optional[str] = ...
    on_change: Optional[Callable[[int], None]] = ...
    type: str = ...

@dataclass
class Input:
    key: str
    text: str
    default: str = ...
    subtext: Optional[str] = ...
    icon: Optional[str] = ...
    on_change: Optional[Callable[[str], None]] = ...
    type: str = ...

@dataclass
class Text:
    text: str
    icon: Optional[str] = ...
    accent: bool = ...
    red: bool = ...
    on_click: Optional[Callable[[Any], None]] = ...
    create_sub_fragment: Callable[[], List[Any]] = ...
    type: str = ...

@dataclass
class Header:
    text: str
    type: str = ...

@dataclass
class Divider:
    text: Optional[str] = ...
    type: str = ...