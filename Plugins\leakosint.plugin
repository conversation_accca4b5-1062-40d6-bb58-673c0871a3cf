import requests
import json
import time
import threading
from typing import Any, Dict, Optional, List, Tuple
from android_utils import log, run_on_ui_thread
from base_plugin import BasePlugin, Hook<PERSON><PERSON><PERSON>, HookStrategy
from client_utils import run_on_queue, get_last_fragment, send_message
from markdown_utils import parse_markdown
from ui.settings import Header, Switch, Divider, Input, Selector, Text
from ui.alert import AlertDialogBuilder
from ui.bulletin import BulletinHelper

__id__ = "leakosint_search_by_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"
__name__ = "LeakOSINT Search"
__version__ = "1.0.0"
__description__ = "Поиск информации по базам данных утечек через LeakOSINT API [.leak]"
__author__ = "@mihai<PERSON><PERSON><PERSON><PERSON> & @mishabotov"
__min_version__ = "11.9.1"
__icon__ = "msg_search"

# API Configuration
API_BASE_URL = "https://leakosintapi.com/"
API_HEADERS = {
    "Content-Type": "application/json",
    "User-Agent": "LeakOSINT-exteraGram-Plugin/1.0.0-@mishabotov"
}

# Default settings
DEFAULT_LIMIT = 100
DEFAULT_LANGUAGE = "ru"
DEFAULT_COMMAND = ".leak"

class LeakOSINTAPI:
    """API client for LeakOSINT service"""
    
    def __init__(self, token: str, timeout: int = 30):
        self.token = token
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update(API_HEADERS)
    
    def search(self, query: str, limit: int = DEFAULT_LIMIT, 
               language: str = DEFAULT_LANGUAGE, 
               report_type: str = "json") -> Optional[Dict]:
        """
        Perform search request to LeakOSINT API
        
        Args:
            query: Search query (email, name, phone, etc.)
            limit: Search limit (100-10000)
            language: Language code (en, ru)
            report_type: Report type (json, short, html)
            
        Returns:
            API response as dict or None if error
        """
        try:
            data = {
                "token": self.token,
                "request": query,
                "limit": limit,
                "lang": language,
                "type": report_type
            }
            
            log(f"[LeakOSINTAPI] Sending request: {query[:50]}...")
            response = self.session.post(API_BASE_URL, json=data, timeout=self.timeout)
            response.raise_for_status()
            
            result = response.json()
            log(f"[LeakOSINTAPI] Response received successfully")
            return result
            
        except requests.exceptions.RequestException as e:
            log(f"[LeakOSINTAPI] Request error: {e}")
            return None
        except json.JSONDecodeError as e:
            log(f"[LeakOSINTAPI] JSON decode error: {e}")
            return None
        except Exception as e:
            log(f"[LeakOSINTAPI] Unexpected error: {e}")
            return None

class ResultFormatter:
    """Format search results for display"""
    
    @staticmethod
    def format_results(data: Dict, query: str) -> str:
        """Format API response into readable message"""
        try:
            if "Error code" in data:
                return f"❌ **Ошибка API**: {data['Error code']}"

            if "List" not in data:
                return f"❌ **Ошибка**: Некорректный ответ API"

            results = data["List"]
            if not results:
                return f"🔍 **Поиск**: `{query}`\n\n❌ **Результат**: Данные не найдены"

            # Check if only "No results found"
            if len(results) == 1 and "No results found" in results:
                return f"🔍 **Поиск**: `{query}`\n\n❌ **Результат**: Данные не найдены"

            # Count total databases and records
            total_databases = len([k for k in results.keys() if k != "No results found"])
            total_records = 0
            for db_name, db_data in results.items():
                if db_name != "No results found" and "Data" in db_data:
                    total_records += len(db_data["Data"])

            formatted_parts = [
                f"🔍 **Поиск**: `{query}`",
                f"📊 **Найдено**: {total_databases} баз данных, {total_records} записей\n"
            ]

            database_count = 0
            for database_name, database_data in results.items():
                if database_name == "No results found":
                    continue

                database_count += 1
                if database_count > 5:  # Limit to 5 databases to avoid too long messages
                    remaining_dbs = total_databases - 5
                    formatted_parts.append(f"... и ещё {remaining_dbs} баз данных")
                    break

                formatted_parts.append(f"📊 **База данных**: {database_name}")

                # Add info leak description if available
                if "InfoLeak" in database_data and database_data["InfoLeak"]:
                    info_leak = database_data["InfoLeak"].strip()
                    if info_leak and len(info_leak) < 200:  # Limit description length
                        formatted_parts.append(f"ℹ️ **Описание**: {info_leak}")

                # Add data entries
                if "Data" in database_data and database_data["Data"]:
                    data_entries = database_data["Data"]
                    formatted_parts.append(f"📋 **Записей в базе**: {len(data_entries)}")

                    # Show first few entries
                    max_entries = 2  # Reduced to save space
                    for i, entry in enumerate(data_entries[:max_entries]):
                        formatted_parts.append(f"\n**Запись {i+1}:**")

                        # Prioritize important fields
                        important_fields = ["email", "password", "username", "name", "phone", "ip"]
                        shown_fields = 0
                        max_fields = 5

                        # Show important fields first
                        for field_name in important_fields:
                            if shown_fields >= max_fields:
                                break
                            for key, value in entry.items():
                                if (field_name.lower() in key.lower() and
                                    value and str(value).strip() and len(str(value)) < 100):
                                    formatted_parts.append(f"• **{key}**: `{value}`")
                                    shown_fields += 1
                                    break

                        # Show remaining fields if space allows
                        for field_name, field_value in entry.items():
                            if shown_fields >= max_fields:
                                break
                            if (field_value and str(field_value).strip() and
                                len(str(field_value)) < 100 and
                                not any(imp.lower() in field_name.lower() for imp in important_fields)):
                                formatted_parts.append(f"• **{field_name}**: `{field_value}`")
                                shown_fields += 1

                        if len(entry) > max_fields:
                            formatted_parts.append(f"• ... и ещё {len(entry) - shown_fields} полей")

                    if len(data_entries) > max_entries:
                        formatted_parts.append(f"\n... и ещё {len(data_entries) - max_entries} записей")

                formatted_parts.append("")  # Empty line between databases

            result = "\n".join(formatted_parts)

            # Add warning if results are truncated
            if len(result) > 3500:
                result = result[:3500] + "\n\n⚠️ **Результаты обрезаны** из-за ограничений Telegram"

            return result

        except Exception as e:
            log(f"[ResultFormatter] Error formatting results: {e}")
            return f"❌ **Ошибка форматирования**: {str(e)}"

    @staticmethod
    def validate_query(query: str) -> Tuple[bool, str]:
        """Validate search query and return (is_valid, error_message)"""
        if not query or not query.strip():
            return False, "Пустой запрос"

        query = query.strip()

        if len(query) < 2:
            return False, "Запрос слишком короткий (минимум 2 символа)"

        if len(query) > 500:
            return False, "Запрос слишком длинный (максимум 500 символов)"

        # Check for suspicious patterns
        suspicious_patterns = ["'", '"', "<", ">", "script", "javascript", "eval"]
        for pattern in suspicious_patterns:
            if pattern in query.lower():
                return False, f"Недопустимые символы или команды в запросе"

        return True, ""

    @staticmethod
    def format_results_with_settings(data: Dict, query: str, compact: bool = False, show_db_info: bool = True) -> str:
        """Format API response with custom settings"""
        try:
            if "Error code" in data:
                return f"❌ **Ошибка API**: {data['Error code']}"

            if "List" not in data:
                return f"❌ **Ошибка**: Некорректный ответ API"

            results = data["List"]
            if not results:
                return f"🔍 **Поиск**: `{query}`\n\n❌ **Результат**: Данные не найдены"

            # Check if only "No results found"
            if len(results) == 1 and "No results found" in results:
                return f"🔍 **Поиск**: `{query}`\n\n❌ **Результат**: Данные не найдены"

            # Count total databases and records
            total_databases = len([k for k in results.keys() if k != "No results found"])
            total_records = 0
            for db_name, db_data in results.items():
                if db_name != "No results found" and "Data" in db_data:
                    total_records += len(db_data["Data"])

            if compact:
                # Compact format
                formatted_parts = [
                    f"🔍 `{query}` → {total_databases} БД, {total_records} записей\n"
                ]

                for database_name, database_data in results.items():
                    if database_name == "No results found":
                        continue

                    if "Data" in database_data and database_data["Data"]:
                        data_entries = database_data["Data"]
                        formatted_parts.append(f"📊 **{database_name}**: {len(data_entries)} записей")

                        # Show only first entry in compact mode
                        if data_entries:
                            entry = data_entries[0]
                            important_fields = ["email", "password", "username", "name", "phone"]
                            shown = []

                            for field_name in important_fields:
                                for key, value in entry.items():
                                    if (field_name.lower() in key.lower() and
                                        value and str(value).strip() and len(str(value)) < 50):
                                        shown.append(f"`{value}`")
                                        break
                                if len(shown) >= 3:  # Limit to 3 fields in compact mode
                                    break

                            if shown:
                                formatted_parts.append(f"  → {' | '.join(shown)}")

                            if len(data_entries) > 1:
                                formatted_parts.append(f"  → +{len(data_entries) - 1} записей")

                        formatted_parts.append("")

                return "\n".join(formatted_parts)

            else:
                # Full format (existing logic)
                formatted_parts = [
                    f"🔍 **Поиск**: `{query}`",
                    f"📊 **Найдено**: {total_databases} баз данных, {total_records} записей\n"
                ]

                database_count = 0
                max_databases = 4 if show_db_info else 6

                for database_name, database_data in results.items():
                    if database_name == "No results found":
                        continue

                    database_count += 1
                    if database_count > max_databases:
                        remaining_dbs = total_databases - max_databases
                        formatted_parts.append(f"... и ещё {remaining_dbs} баз данных")
                        break

                    formatted_parts.append(f"📊 **База данных**: {database_name}")

                    # Add info leak description if enabled and available
                    if show_db_info and "InfoLeak" in database_data and database_data["InfoLeak"]:
                        info_leak = database_data["InfoLeak"].strip()
                        if info_leak and len(info_leak) < 150:
                            formatted_parts.append(f"ℹ️ **Описание**: {info_leak}")

                    # Add data entries
                    if "Data" in database_data and database_data["Data"]:
                        data_entries = database_data["Data"]
                        formatted_parts.append(f"📋 **Записей в базе**: {len(data_entries)}")

                        # Show entries
                        max_entries = 1 if show_db_info else 2
                        for i, entry in enumerate(data_entries[:max_entries]):
                            formatted_parts.append(f"\n**Запись {i+1}:**")

                            # Show important fields
                            important_fields = ["email", "password", "username", "name", "phone", "ip"]
                            shown_fields = 0
                            max_fields = 4 if show_db_info else 6

                            for field_name in important_fields:
                                if shown_fields >= max_fields:
                                    break
                                for key, value in entry.items():
                                    if (field_name.lower() in key.lower() and
                                        value and str(value).strip() and len(str(value)) < 80):
                                        formatted_parts.append(f"• **{key}**: `{value}`")
                                        shown_fields += 1
                                        break

                            # Show remaining fields
                            for field_name, field_value in entry.items():
                                if shown_fields >= max_fields:
                                    break
                                if (field_value and str(field_value).strip() and
                                    len(str(field_value)) < 80 and
                                    not any(imp.lower() in field_name.lower() for imp in important_fields)):
                                    formatted_parts.append(f"• **{field_name}**: `{field_value}`")
                                    shown_fields += 1

                            if len(entry) > max_fields:
                                formatted_parts.append(f"• ... и ещё {len(entry) - shown_fields} полей")

                        if len(data_entries) > max_entries:
                            formatted_parts.append(f"\n... и ещё {len(data_entries) - max_entries} записей")

                    formatted_parts.append("")  # Empty line between databases

                return "\n".join(formatted_parts)

        except Exception as e:
            log(f"[ResultFormatter] Error formatting results with settings: {e}")
            return f"❌ **Ошибка форматирования**: {str(e)}"

class LeakOSINTPlugin(BasePlugin):
    """Main plugin class"""
    
    def __init__(self):
        super().__init__()
        self.progress_dialog = None
        self.api_client = None
    
    def on_plugin_load(self):
        """Initialize plugin when loaded"""
        log("[LeakOSINTPlugin] Plugin loaded")
        self.add_on_send_message_hook()
        self._initialize_api_client()
    
    def on_plugin_unload(self):
        """Cleanup when plugin unloaded"""
        log("[LeakOSINTPlugin] Plugin unloaded")
        if self.progress_dialog:
            run_on_ui_thread(lambda: self._dismiss_progress_dialog())
    
    def _initialize_api_client(self):
        """Initialize API client with current settings"""
        token = self.get_setting("api_token", "")
        if token:
            self.api_client = LeakOSINTAPI(token)
            log("[LeakOSINTPlugin] API client initialized")
        else:
            log("[LeakOSINTPlugin] No API token configured")
    
    def _dismiss_progress_dialog(self):
        """Dismiss progress dialog safely"""
        if self.progress_dialog:
            try:
                self.progress_dialog.dismiss()
            except Exception as e:
                log(f"[LeakOSINTPlugin] Error dismissing dialog: {e}")
            finally:
                self.progress_dialog = None
    
    def create_settings(self):
        """Create plugin settings interface"""
        def test_api_token(view):
            """Test API token functionality"""
            token = self.get_setting("api_token", "")
            if not token:
                BulletinHelper.show_error("Токен не указан")
                return

            # Test with a simple query
            test_client = LeakOSINTAPI(token, timeout=10)

            def _test_in_background():
                try:
                    result = test_client.search("<EMAIL>", 100, "en")
                    if result is None:
                        run_on_ui_thread(lambda: BulletinHelper.show_error("Ошибка соединения"))
                    elif "Error code" in result:
                        error = result["Error code"]
                        if "Invalid token" in error or "token" in error.lower():
                            run_on_ui_thread(lambda: BulletinHelper.show_error("Неверный токен"))
                        else:
                            run_on_ui_thread(lambda: BulletinHelper.show_error(f"Ошибка API: {error}"))
                    else:
                        run_on_ui_thread(lambda: BulletinHelper.show_success("Токен работает корректно"))
                        # Reinitialize API client with new token
                        self._initialize_api_client()
                except Exception as e:
                    run_on_ui_thread(lambda: BulletinHelper.show_error(f"Ошибка тестирования: {str(e)}"))

            run_on_queue(_test_in_background)

        return [
            Header(text="API Настройки"),
            Input(
                key="api_token",
                text="API Токен",
                default="",
                icon="msg_pin_code"
            ),
            Text(
                text="Тестировать токен",
                icon="msg_retry",
                on_click=test_api_token,
                accent=True
            ),
            Divider(text="Получите токен в боте @LeakOsintBot командой /api"),

            Header(text="Настройки поиска"),
            Input(
                key="search_limit",
                text="Лимит поиска",
                default=str(DEFAULT_LIMIT),
                icon="msg_numbers"
            ),
            Divider(text="Количество результатов (100-10000)"),
            Selector(
                key="language",
                text="Язык результатов",
                default=0,
                items=["Русский (ru)", "English (en)"],
                icon="msg_language"
            ),
            Selector(
                key="timeout",
                text="Таймаут запроса",
                default=1,
                items=["15 секунд", "30 секунд", "60 секунд", "120 секунд"],
                icon="msg_timer"
            ),

            Header(text="Отображение"),
            Switch(
                key="show_promo",
                text="Показывать промо",
                default=True,
                icon="msg_channel"
            ),
            Divider(text="Отображать ссылку на канал @mishabotov"),
            Switch(
                key="compact_results",
                text="Компактные результаты",
                default=False,
                icon="msg_mini"
            ),
            Divider(text="Показывать меньше деталей для экономии места"),
            Switch(
                key="show_database_info",
                text="Показывать описание баз",
                default=True,
                icon="msg_info"
            ),
            Divider(text="Отображать информацию о базах данных"),

            Header(text="Команды"),
            Divider(text=f"{DEFAULT_COMMAND} [запрос] - Поиск по базам утечек\n\nПримеры:\n• {DEFAULT_COMMAND} <EMAIL>\n• {DEFAULT_COMMAND} Иван Петров\n• {DEFAULT_COMMAND} +***********"),
        ]
    
    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        """Handle outgoing messages to process search commands"""
        if not isinstance(params.message, str) or not params.message.startswith(DEFAULT_COMMAND):
            return HookResult()

        try:
            # Parse command
            parts = params.message.strip().split(" ", 1)
            if len(parts) < 2:
                help_message = (
                    f"❌ **Использование**: `{DEFAULT_COMMAND} [запрос]`\n\n"
                    f"**Примеры поиска**:\n"
                    f"• `{DEFAULT_COMMAND} <EMAIL>` - поиск по email\n"
                    f"• `{DEFAULT_COMMAND} Иван Петров` - поиск по имени\n"
                    f"• `{DEFAULT_COMMAND} +***********` - поиск по телефону\n"
                    f"• `{DEFAULT_COMMAND} username123` - поиск по никнейму\n\n"
                    f"**Поддерживаемые типы данных**:\n"
                    f"• Email адреса\n"
                    f"• Имена и фамилии\n"
                    f"• Номера телефонов\n"
                    f"• Никнеймы и логины\n"
                    f"• IP адреса\n"
                    f"• Домены"
                )
                params.message = help_message
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

            query = parts[1].strip()

            # Validate query
            is_valid, error_msg = ResultFormatter.validate_query(query)
            if not is_valid:
                params.message = f"❌ **Ошибка запроса**: {error_msg}"
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

            # Check API token
            if not self.api_client:
                self._initialize_api_client()

            if not self.api_client:
                setup_message = (
                    f"❌ **API токен не настроен**\n\n"
                    f"**Настройка плагина**:\n"
                    f"1. Перейдите в настройки плагина LeakOSINT\n"
                    f"2. Получите токен в боте LeakOSINT командой `/api`\n"
                    f"3. Укажите полученный токен в поле 'API Токен'\n"
                    f"4. Сохраните настройки\n\n"
                    f"**Бот LeakOSINT**: @LeakOsintBot"
                )
                params.message = setup_message
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

            # Show progress dialog and start search
            self._show_progress_dialog()
            run_on_queue(lambda: self._perform_search(query, params.peer))

            return HookResult(strategy=HookStrategy.CANCEL)

        except Exception as e:
            log(f"[LeakOSINTPlugin] Error in message hook: {e}")
            params.message = f"❌ **Неожиданная ошибка**: {str(e)}"
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
    
    def _show_progress_dialog(self):
        """Show loading dialog"""
        try:
            last_fragment = get_last_fragment()
            if not last_fragment or not last_fragment.getParentActivity():
                log("[LeakOSINTPlugin] Cannot get context for progress dialog")
                return
            
            context = last_fragment.getParentActivity()
            self.progress_dialog = AlertDialogBuilder(context, AlertDialogBuilder.ALERT_TYPE_SPINNER)
            self.progress_dialog.set_title("LeakOSINT")
            self.progress_dialog.set_message("Поиск данных...")
            self.progress_dialog.set_cancelable(False)
            self.progress_dialog.show()
            log("[LeakOSINTPlugin] Progress dialog shown")
            
        except Exception as e:
            log(f"[LeakOSINTPlugin] Error showing progress dialog: {e}")
    
    def _perform_search(self, query: str, peer_id: Any):
        """Perform search in background thread"""
        try:
            # Get settings
            limit_str = self.get_setting("search_limit", str(DEFAULT_LIMIT))
            try:
                limit = int(limit_str)
            except (ValueError, TypeError):
                limit = DEFAULT_LIMIT

            language_index = self.get_setting("language", 0)
            language = "ru" if language_index == 0 else "en"

            timeout_index = self.get_setting("timeout", 1)
            timeout_values = [15, 30, 60, 120]
            timeout = timeout_values[min(timeout_index, len(timeout_values) - 1)]

            show_promo = self.get_setting("show_promo", True)
            compact_results = self.get_setting("compact_results", False)
            show_database_info = self.get_setting("show_database_info", True)

            # Update API client timeout
            if self.api_client:
                self.api_client.timeout = timeout

            # Validate limit
            if limit < 100:
                limit = 100
                log(f"[LeakOSINTPlugin] Limit too low, set to 100")
            elif limit > 10000:
                limit = 10000
                log(f"[LeakOSINTPlugin] Limit too high, set to 10000")

            # Validate query (already done in hook, but double-check)
            is_valid, error_msg = ResultFormatter.validate_query(query)
            if not is_valid:
                self._send_error_message(f"❌ **Ошибка запроса**: {error_msg}", peer_id)
                return

            # Perform search
            log(f"[LeakOSINTPlugin] Searching for: {query[:50]}... (limit: {limit}, lang: {language})")
            result = self.api_client.search(query, limit, language)

            if result is None:
                self._send_error_message(
                    "❌ **Ошибка сети**: Не удалось выполнить поиск.\n\n"
                    "**Возможные причины**:\n"
                    "• Нет подключения к интернету\n"
                    "• Сервер LeakOSINT недоступен\n"
                    "• Неверный API токен\n\n"
                    "Проверьте настройки и попробуйте позже.",
                    peer_id
                )
                return

            # Check for API errors
            if "Error code" in result:
                error_code = result["Error code"]
                if "Invalid token" in error_code or "token" in error_code.lower():
                    error_msg = (
                        "❌ **Ошибка токена**: Неверный или истёкший API токен\n\n"
                        "**Решение**:\n"
                        "1. Перейдите в настройки плагина\n"
                        "2. Получите новый токен командой `/api` в боте LeakOSINT\n"
                        "3. Укажите новый токен в настройках"
                    )
                elif "limit" in error_code.lower():
                    error_msg = f"❌ **Ошибка лимита**: {error_code}\n\nПопробуйте уменьшить лимит поиска в настройках"
                elif "rate" in error_code.lower() or "frequency" in error_code.lower():
                    error_msg = f"❌ **Превышен лимит запросов**: {error_code}\n\nПодождите немного перед следующим запросом"
                else:
                    error_msg = f"❌ **Ошибка API**: {error_code}"

                self._send_error_message(error_msg, peer_id)
                return

            # Format and send results
            message_content = ResultFormatter.format_results_with_settings(
                result, query, compact_results, show_database_info
            )

            # Add promo if enabled
            if show_promo:
                message_content += f"\n\n🔗 **Канал разработчика**: @mishabotov"

            # Check message length and truncate if needed
            if len(message_content) > 4000:
                message_content = message_content[:3900] + "\n\n⚠️ **Результаты обрезаны** из-за ограничений Telegram"

            self._send_success_message(message_content, peer_id)

        except ValueError as e:
            log(f"[LeakOSINTPlugin] Value error in search: {e}")
            self._send_error_message(f"❌ **Ошибка данных**: Некорректные параметры поиска", peer_id)
        except requests.exceptions.Timeout:
            log(f"[LeakOSINTPlugin] Timeout in search")
            self._send_error_message(
                "❌ **Таймаут**: Запрос занял слишком много времени\n\n"
                "Попробуйте:\n"
                "• Уменьшить лимит поиска\n"
                "• Повторить запрос позже",
                peer_id
            )
        except requests.exceptions.ConnectionError:
            log(f"[LeakOSINTPlugin] Connection error in search")
            self._send_error_message(
                "❌ **Ошибка соединения**: Не удалось подключиться к серверу\n\n"
                "Проверьте подключение к интернету и повторите попытку.",
                peer_id
            )
        except Exception as e:
            log(f"[LeakOSINTPlugin] Unexpected error in search: {e}")
            self._send_error_message(f"❌ **Неожиданная ошибка**: {str(e)}", peer_id)

    def _send_success_message(self, content: str, peer_id: Any):
        """Send successful search result"""
        message_params = {
            "message": content,
            "peer": peer_id
        }

        def _send_and_dismiss():
            run_on_ui_thread(lambda: self._dismiss_progress_dialog())
            send_message(message_params)

        run_on_ui_thread(_send_and_dismiss)

    def _send_error_message(self, error_content: str, peer_id: Any):
        """Send error message"""
        message_params = {
            "message": error_content,
            "peer": peer_id
        }

        def _send_error_and_dismiss():
            run_on_ui_thread(lambda: self._dismiss_progress_dialog())
            send_message(message_params)

        run_on_ui_thread(_send_error_and_dismiss)
