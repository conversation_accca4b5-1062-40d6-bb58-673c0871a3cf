import ast
import traceback
import plugins_manager
from typing import Callable, Any, Optional

from java import dynamic_proxy, cast
from java.util import ArrayList

from com.exteragram.messenger.plugins import PluginsController, Plugin
from org.telegram.messenger import Utilities, FileLoader, MessageObject, AndroidUtilities, R, ApplicationLoader

from android_utils import log
from base_plugin import BasePlugin, HookResult, HookStrategy
from client_utils import get_file_loader, run_on_ui_thread
from ui.bulletin import BulletinHelper

__id__ = "qmrrchh_plugins_manager"
__name__ = "Plugins Manager"
__author__ = "@qmrrchh"
__icon__ = "remusic/3"
__version__ = "1.2.1"
__min_version__ = "11.9.1"
__description__ = "Usage:\n   .plist - Shows your plugins\n   .pdl - Download and install replied plugin.\n   .pdle - Download, install and enable replied plugin.\n   .pdis {name/id} - Disables entered plugin.\n   .pel {name/id} - Enables entered plugin.\n   .pdel {name/id} - Deletes entered plugin."

# From zwyLib
class Callback1(dynamic_proxy(Utilities.Callback)):
    def __init__(self, fn: Callable[[Any], None]):
        super().__init__()
        self._fn = fn

    def run(self, arg):
        try:
            self._fn(arg)
        except Exception:
            log(f"Error in Callback1: {traceback.format_exc().rstrip()}")

pm = plugins_manager.PluginsManager

def show_with_copy(message, submsg):
    def copy():
        if AndroidUtilities.addToClipboard(submsg):
            BulletinHelper.show_copied_to_clipboard()

    BulletinHelper.show_with_button(message, R.raw.error, "Copy", lambda : copy())

# From zwyLib
def download_and_install_plugin(document, enable: bool):
    def plugin_install_callback(arg):
        if arg is None:
            BulletinHelper.show_two_line("Plugin installed.", "Now you can check him in settings!", R.raw.contact_check)
            return
        show_with_copy("An error has occurred", arg)

    file_loader = get_file_loader()
    plugins_controller = PluginsController.getInstance()
    path = file_loader.getPathToAttach(document, True)

    if not path.exists():
        log("Started loading the file...")
        file_loader.loadFile(document, "new_plugin", FileLoader.PRIORITY_NORMAL, 1)
        download_and_install_plugin(document, enable)
        return

    log("Installing...")
    plugins_controller.loadPluginFromFile(str(path), Callback1(plugin_install_callback))

    if enable:
        try:
            plugin_id = get_id_from_file(path.getAbsolutePath())
            plugins_controller.setPluginEnabled(plugin_id, True, Callback1(plugin_install_callback))
        except Exception as e:
            show_with_copy("An error has occurred", e)


def get_id_from_file(file_path):
    with open(file_path, "r", encoding="utf-8") as file:
        tree = ast.parse(file.read(), filename=file_path)

    for node in ast.walk(tree):
        if isinstance(node, ast.Assign):
            for target in node.targets:
                if isinstance(target, ast.Name) and target.id == "__id__":
                    return ast.literal_eval(node.value)

    return None


# from debug plugin (@exteraDev)
def _get_plugins_info():
    try:
        active_plugins = [p for p in plugins_manager.PluginsManager._plugins.values()]
        count = len(active_plugins)
        if not active_plugins:
            return "Plugins not found."
        max_plugins = 20
        shown = active_plugins[:max_plugins]
        more = count - max_plugins
        plugins_md = "\n".join(f"- {p.name} | {p.id}" for p in shown)
        if more > 0:
            plugins_md += f"\n- ...and {more} more plugins"
        return f"🧩 All your Plugins ({count})\n{plugins_md}"
    except Exception as e:
        return f"Error getting plugins: {str(e)}"


def restart():
    pm.shutdown()
    PluginsController.getInstance().shutdown()
    pm.init(pm._plugins_dir)
    PluginsController.getInstance().init()


def search_plugin(prompt: str) -> Optional[BasePlugin]:
    prompt = prompt.lower()
    plugins = [p for p in plugins_manager.PluginsManager._plugins.values()]
    pluginsNames = [p.name.lower() for p in plugins_manager.PluginsManager._plugins.values()]
    pluginsIds = [p.id.lower() for p in plugins_manager.PluginsManager._plugins.values()]

    if prompt in pluginsNames:
        plugin = [p for p in plugins if prompt == p.name.lower()]
        if len(plugin) == 1 and plugin[0]:
            return plugin[0]
        else:
            return None

    elif prompt in pluginsIds:
        plugin = [p for p in plugins if prompt ==  p.id.lower()]
        if len(plugin) == 1 and plugin[0]:
            return plugin[0]
        else:
            return None

    else:
        return None


def enable_plugin(pluginId: str, enabled: bool, params) -> HookResult:
    if len(pluginId) <= 0:
        params.message = "Plugin name/id not found."
        return HookResult(strategy=HookStrategy.MODIFY, params=params)

    plugin = search_plugin(pluginId)
    if plugin:
        enabled_text = "enable" if enabled else "disable"
        if pm.set_plugin_enabled(plugin.id, enabled):
            BulletinHelper.show_success(f"Plugin {pluginId} {enabled_text}d!")
            restart()
        else:
            BulletinHelper.show_error(f"Plugin {enabled_text} for {pluginId} failed.")
    else:
        BulletinHelper.show_error(f"Plugin {pluginId} not found!")

    return HookResult(strategy=HookStrategy.CANCEL)


class LoaderPlugin(BasePlugin):
    def __init__(self):
        super().__init__()


    def on_plugin_load(self):
        self.add_on_send_message_hook()


    def on_send_message_hook(self, account: int, params: Any):
        text: str = params.message
        try:

            if text == ".pdl" and params.replyToMsg:
                document = MessageObject.getDocument(params.replyToMsg)
                if document and str(document.file_name_fixed).endswith(".plugin"):
                    run_on_ui_thread(lambda: download_and_install_plugin(document, False))
                    return HookResult(strategy=HookStrategy.CANCEL)
                else:
                    return HookResult(strategy=HookStrategy.DEFAULT)

            elif text == ".pdle" and params.replyToMsg:
                document = MessageObject.getDocument(params.replyToMsg)
                if document and str(document.file_name_fixed).endswith(".plugin"):
                    run_on_ui_thread(lambda: download_and_install_plugin(document, True))
                    return HookResult(strategy=HookStrategy.CANCEL)
                else:
                    return HookResult(strategy=HookStrategy.DEFAULT)

            elif text == ".plist":
                params.message = _get_plugins_info()
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

            elif text.startswith(".pdis "):
                pluginPrompt = text.replace(".pdis ", "")
                return enable_plugin(pluginPrompt, False, params)

            elif text.startswith(".pen "):
                pluginPrompt = text.replace(".pen ", "")
                return enable_plugin(pluginPrompt, True, params)

            elif text.startswith(".pdel "):
                pluginPrompt = text.replace(".pdel ", "")
                if len(pluginPrompt) <= 0:
                    params.message = "Plugin name/id not found."
                    return HookResult(strategy=HookStrategy.MODIFY, params=params)

                plugin = search_plugin(pluginPrompt)
                if plugin:
                    if pm.delete_plugin(plugin.id):
                        BulletinHelper.show_success(f"Plugin {pluginPrompt} deleted!")
                        restart()
                    else:
                        BulletinHelper.show_error(f"Plugin delete for {pluginPrompt} failed.")
                else:
                    BulletinHelper.show_error(f"Plugin {pluginPrompt} not found!")

                return HookResult(strategy=HookStrategy.CANCEL)

            else:
                return HookResult(strategy=HookStrategy.DEFAULT)

        except Exception as e:
            log(str(e))
            return HookResult(strategy=HookStrategy.DEFAULT)

