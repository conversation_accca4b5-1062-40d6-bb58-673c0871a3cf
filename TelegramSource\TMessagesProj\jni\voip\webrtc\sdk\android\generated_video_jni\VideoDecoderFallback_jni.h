// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/VideoDecoderFallback

#ifndef org_webrtc_VideoDecoderFallback_JNI
#define org_webrtc_VideoDecoderFallback_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_VideoDecoderFallback[];
const char kClassPath_org_webrtc_VideoDecoderFallback[] = "org/webrtc/VideoDecoderFallback";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_VideoDecoderFallback_clazz(nullptr);
#ifndef org_webrtc_VideoDecoderFallback_clazz_defined
#define org_webrtc_VideoDecoderFallback_clazz_defined
inline jclass org_webrtc_VideoDecoderFallback_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_VideoDecoderFallback,
      &g_org_webrtc_VideoDecoderFallback_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {

static jlong JNI_VideoDecoderFallback_Create(JNIEnv* env, jlong webrtcEnvRef,
    const jni_zero::JavaParamRef<jobject>& fallback,
    const jni_zero::JavaParamRef<jobject>& primary);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_VideoDecoderFallback_nativeCreate(
    JNIEnv* env,
    jclass jcaller,
    jlong webrtcEnvRef,
    jobject fallback,
    jobject primary) {
  return JNI_VideoDecoderFallback_Create(env, webrtcEnvRef, jni_zero::JavaParamRef<jobject>(env,
      fallback), jni_zero::JavaParamRef<jobject>(env, primary));
}


}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_VideoDecoderFallback_JNI
