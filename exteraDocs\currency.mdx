---
title: Currency Converter
description: Example and explanation of a plugin that converts currencies using the .cr command.
icon: DollarSign
---

## Introduction

This page explains how to create a plugin that converts currencies using the `.cr` command. The plugin demonstrates how to use external APIs, handle user input, manage plugin settings, and format messages for Telegram.

## Basic Structure

The Currency Converter plugin follows the standard plugin structure:

- Metadata variables (`__id__`, `__name__`, etc.)
- Inherits from `BasePlugin`
- Implements methods for plugin lifecycle and message hooks

```python
__id__ = "currency"
__name__ = "Currency Converter"
__description__ = "Converts currencies using the command .cr [amount] [currency code]"
__author__ = "exteraDev"
__min_version__ = "11.9.0"
```

## How It Works

When a user sends a message starting with `.cr`, the plugin:

1. Parses the amount and currency code from the message.
2. Fetches the latest exchange rates from an external API.
3. Converts the amount to all supported currencies.
4. Formats the result and sends it as a message.

## Key Components

### Settings

The plugin provides a setting for the default currency code, supporting multiple languages.

```python
def create_settings(self):
    return [
        Header(text="Currency Converter Settings"),
        Input(
            key="default_currency",
            text="Default Currency",
            default="USD",
            subtext="Enter the default currency code (e.g., USD, EUR, GBP)"
        ),
        Divider(text="Usage: .cr [amount] [currency code]")
    ]
```

### Handling the .cr Command

The plugin intercepts outgoing messages using `on_send_message_hook`. If the message starts with `.cr`, it processes the command:

```python
def on_send_message_hook(self, account, params):
    if not hasattr(params, "message") or not isinstance(params.message, str):
        return HookResult()
    if not params.message.startswith(".cr "):
        return HookResult()
    # ...parse amount and currency, fetch rates, format result...
    return HookResult(strategy=HookStrategy.MODIFY, params=params)
```

### Fetching and Formatting Currency Data

- The plugin uses the [open.er-api.com](https://open.er-api.com/) API to fetch exchange rates.
- It supports a wide range of currencies and displays country flags for each.
- The result is formatted as a list of conversions.

## Example Usage

Send a message like:

```
.cr 100 USD
```

The plugin will reply with the converted value in all supported currencies.

## Complete Example

```python
import requests
from settings import Header, Input, Divider
from base_plugin import BasePlugin, HookResult, HookStrategy
from android_utils import log
from org.telegram.tgnet import TLRPC
from java.util import Locale

__id__ = "currency"
__name__ = "Currency Converter"
__description__ = "Converts currencies using the command .cr [amount] [currency code]"
__author__ = "exteraDev"
__min_version__ = "11.9.0"
__icon__ = "DMJDuckX2/45"

FLAGS = {
    "USD": "🇺🇸", "RUB": "🇷🇺", "EUR": "🇪🇺",
    "UAH": "🇺🇦", "UZS": "🇺🇿", "BRL": "🇧🇷",
    "KZT": "🇰🇿", "GBP": "🇬🇧", "JPY": "🇯🇵",
    "CAD": "🇨🇦", "AUD": "🇦🇺", "CHF": "🇨🇭",
    "CNY": "🇨🇳", "INR": "🇮🇳", "BYN": "🇧🇾",
    "MXN": "🇲🇽", "TRY": "🇹🇷", "ZAR": "🇿🇦",
    "PLN": "🇵🇱", "SEK": "🇸🇪", "NOK": "🇳🇴",
    "DKK": "🇩🇰", "CZK": "🇨🇿", "SGD": "🇸🇬",
    "HKD": "🇭🇰", "KRW": "🇰🇷", "THB": "🇹🇭",
    "IDR": "🇮🇩", "MYR": "🇲🇾", "PHP": "🇵🇭",
    "ILS": "🇮🇱", "SAR": "🇸🇦", "AED": "🇦🇪",
    "EGP": "🇪🇬", "ARS": "🇦🇷", "CLP": "🇨🇱",
    "COP": "🇨🇴", "VND": "🇻🇳", "TWD": "🇹🇼",
    "NZD": "🇳🇿", "HUF": "🇭🇺", "RON": "🇷🇴",
    "BGN": "🇧🇬", "HRK": "🇭🇷", "PKR": "🇵🇰",
    "LKR": "🇱🇰", "BDT": "🇧🇩", "NGN": "🇳🇬",
    "GHS": "🇬🇭", "MAD": "🇲🇦", "DZD": "🇩🇿",
    "KWD": "🇰🇼", "QAR": "🇶🇦", "JOD": "🇯🇴",
    "OMR": "🇴🇲", "BHD": "🇧🇭", "TND": "🇹🇳"
}

class CurrencyConverter(BasePlugin):
    def create_settings(self):
        lang = Locale.getDefault().getLanguage()
        if lang.startswith("ru"):
            header_text = "Настройки конвертера валют"
            input_text = "Валюта по умолчанию"
            input_subtext = "Введите код валюты по умолчанию (например, USD, EUR, RUB)"
            divider_text = "Использование: .cr [сумма] [код валюты]"
        elif lang.startswith("pt"):
            header_text = "Configurações do Conversor de Moedas"
            input_text = "Moeda padrão"
            input_subtext = "Digite o código da moeda padrão (ex: USD, EUR, BRL)"
            divider_text = "Uso: .cr [quantia] [código da moeda]"
        else:
            header_text = "Currency Converter Settings"
            input_text = "Default Currency"
            input_subtext = "Enter the default currency code (e.g., USD, EUR, GBP)"
            divider_text = "Usage: .cr [amount] [currency code]"
        return [
            Header(text=header_text),
            Input(
                key="default_currency",
                text=input_text,
                default="USD",
                subtext=input_subtext
            ),
            Divider(text=divider_text)
        ]

    def on_plugin_load(self):
        self.add_hook("on_send_message")
        log("Currency Converter plugin loaded")

    def on_plugin_unload(self):
        log("Currency Converter plugin unloaded")

    def on_send_message_hook(self, account, params):
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()

        if not params.message.startswith(".cr "):
            return HookResult()

        try:
            parts = params.message.strip().split()
            if len(parts) < 2:
                params.message = self._usage_message()
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

            amount = float(parts[1])
            currency = parts[2].upper() if len(parts) > 2 else self.get_setting("default_currency", "USD").upper()

            if currency not in FLAGS:
                params.message = self._unsupported_currency_message(currency)
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

            url = f"https://open.er-api.com/v6/latest/{currency}"
            response = requests.get(url, timeout=5)

            if response.status_code != 200:
                params.message = self._fetch_error_message(currency)
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

            data = response.json()
            rates = data.get("rates", {})
            if not rates:
                params.message = self._no_rates_message(currency)
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

            header = [
                f"💱 {self._format_number(amount)} {FLAGS.get(currency, '')} {currency}",
                "-----------------------------------",
                self._conversions_label()
            ]
            header_text = "\n".join(header)

            conversions_list = []
            for target in FLAGS.keys():
                if target != currency and target in rates:
                    converted = amount * rates[target]
                    flag = FLAGS.get(target, '')
                    conversions_list.append(f"- {self._format_number(converted)} {flag} {target}")

            conversions_text = "\n".join(conversions_list)

            params.message = header_text + "\n" + conversions_text

            if not hasattr(params, "entities") or params.entities is None:
                params.entities = []

            offset = len(header_text) + 4

            entity = TLRPC.TL_messageEntityBlockquote()
            entity.collapsed = True
            entity.offset = offset
            entity.length = int(len(conversions_text.encode(encoding='utf_16_le')) / 2)

            params.entities.add(entity)

            log(params.message)
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        except ValueError:
            params.message = self._invalid_amount_message()
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        except Exception as e:
            log(f"Currency Converter Error: {e}")
            params.message = self._unexpected_error_message()
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

    def _format_number(self, value):
        if value == int(value):
            return f"{int(value):,}"
        return f"{value:,.2f}"

    def _usage_message(self):
        lang = Locale.getDefault().getLanguage()
        if lang.startswith("ru"):
            return "Использование: .cr [сумма] [код валюты]"
        elif lang.startswith("pt"):
            return "Uso: .cr [quantia] [código da moeda]"
        else:
            return "Usage: .cr [amount] [currency code]"

    def _unsupported_currency_message(self, currency):
        lang = Locale.getDefault().getLanguage()
        if lang.startswith("ru"):
            return f"Ошибка: код валюты '{currency}' не поддерживается."
        elif lang.startswith("pt"):
            return f"Erro: código de moeda '{currency}' não suportado."
        else:
            return f"Error: Unsupported currency code '{currency}'."

    def _fetch_error_message(self, currency):
        lang = Locale.getDefault().getLanguage()
        if lang.startswith("ru"):
            return f"Ошибка: не удалось получить курсы валют для {currency}."
        elif lang.startswith("pt"):
            return f"Erro: não foi possível obter taxas de câmbio para {currency}."
        else:
            return f"Error: Unable to fetch exchange rates for {currency}."

    def _no_rates_message(self, currency):
        lang = Locale.getDefault().getLanguage()
        if lang.startswith("ru"):
            return f"Ошибка: нет доступных курсов валют для {currency}."
        elif lang.startswith("pt"):
            return f"Erro: não há taxas de câmbio disponíveis para {currency}."
        else:
            return f"Error: No exchange rates available for {currency}."

    def _invalid_amount_message(self):
        lang = Locale.getDefault().getLanguage()
        if lang.startswith("ru"):
            return "Ошибка: неверная сумма. Пожалуйста, введите корректное число."
        elif lang.startswith("pt"):
            return "Erro: quantia inválida. Por favor, forneça um número válido."
        else:
            return "Error: Invalid amount. Please provide a valid number."

    def _unexpected_error_message(self):
        lang = Locale.getDefault().getLanguage()
        if lang.startswith("ru"):
            return "Ошибка: произошла непредвиденная ошибка. Пожалуйста, попробуйте позже."
        elif lang.startswith("pt"):
            return "Erro: ocorreu um erro inesperado. Por favor, tente novamente mais tarde."
        else:
            return "Error: An unexpected error occurred. Please try again later."

    def _conversions_label(self):
        lang = Locale.getDefault().getLanguage()
        if lang.startswith("ru"):
            return "Конвертации:"
        elif lang.startswith("pt"):
            return "Conversões:"
        else:
            return "Conversions:"
```

## Tips

- The plugin supports localization for settings and error messages.
- It uses message entities to format the output as a blockquote.
- Handles invalid input and unsupported currencies gracefully.

## What’s Next?

Try sending `.cr 100 USD` or any other amount and currency code to see the plugin in action. Explore the code to learn about API integration, message formatting, and plugin settings.
