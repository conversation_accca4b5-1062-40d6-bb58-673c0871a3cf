from dataclasses import dataclass
from enum import Enum
from typing import Any, List, final, Optional, Protocol

class AppEvent(Enum):
    START: Any
    STOP: Any
    PAUSE: Any
    RESUME: Any

class HookStrategy(Enum):
    CANCEL: Any
    MODIFY: Any
    DEFAULT: Any
    MODIFY_FINAL: Any

@dataclass
class HookResult:
    strategy: HookStrategy = ...
    request: Any = ...
    response: Any = ...
    update: Any = ...
    updates: Any = ...
    error: Any = ...
    params: Any = ...

class XposedHook(Protocol):
    def __init__(self) -> None: ...
    def replace_hooked_method(self, param: Any) -> Any: ...
    def before_hooked_method(self, param: Any) -> None: ...
    def after_hooked_method(self, param: Any) -> None: ...

class BasePlugin:
    id: str
    name: str
    description: str
    author: str
    min_version: str
    version: str
    icon: Optional[str]
    error_message: Optional[str]
    enabled: bool
    initialized: bool

    def __init__(self) -> None: ...
    def on_plugin_load(self) -> None: ...
    def on_plugin_unload(self) -> None: ...
    def create_settings(self) -> List[Any]: ...
    def on_app_event(self, event_type: AppEvent) -> None: ...
    def pre_request_hook(self, request_name: str, account: int, request: Any) -> HookResult: ...
    def post_request_hook(self, request_name: str, account: int, response: Any, error: Any) -> HookResult: ...
    def on_update_hook(self, update_name: str, account: int, update: Any) -> HookResult: ...
    def on_updates_hook(self, container_name: str, account: int, updates: Any) -> HookResult: ...
    def on_send_message_hook(self, account: int, params: Any) -> HookResult: ...

    @final
    def add_hook(self, name: str, match_substring: bool = False, priority: int = 0) -> None: ...
    @final
    def add_on_send_message_hook(self, priority: int = 0): ...
    @final
    def remove_hook(self, name: str) -> None: ...
    @final
    def get_setting(self, key: str, default: Any = None) -> Any: ...
    @final
    def set_setting(self, key: str, value: Any) -> None: ...
    @final
    def hook_method(self, method_or_constructor: Any, xposed_hook: Optional[XposedHook], priority: Optional[int] = None) -> Optional[Any]: ...
    @final
    def unhook_method(self, unhook: Any) -> None: ...
    @final
    def log(self, message: str) -> None: ...