{"AED": {"code": "AED", "title": "United Arab Emirates Dirham", "symbol": "AED", "native": "د.إ.‏", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "367", "max_amount": "3673042"}, "AFN": {"code": "AFN", "title": "Afghan Afghani", "symbol": "AFN", "native": "؋", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "7032", "max_amount": "70320011"}, "ALL": {"code": "ALL", "title": "Albanian Lek", "symbol": "ALL", "native": "Lek", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": false, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "9170", "max_amount": "91699858"}, "AMD": {"code": "AMD", "title": "Armenian Dram", "symbol": "AMD", "native": "դր.", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "38438", "max_amount": "*********"}, "ARS": {"code": "ARS", "title": "Argentine Peso", "symbol": "ARS", "native": "$", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "93241", "max_amount": "*********"}, "AUD": {"code": "AUD", "title": "Australian Dollar", "symbol": "AU$", "native": "$", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "154", "max_amount": "1535627"}, "AZN": {"code": "AZN", "title": "Azerbaijani Manat", "symbol": "AZN", "native": "ман.", "thousands_sep": " ", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "170", "max_amount": "1703970"}, "BAM": {"code": "BAM", "title": "Bosnia & Herzegovina Convertible Mark", "symbol": "BAM", "native": "KM", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "179", "max_amount": "1790780"}, "BDT": {"code": "BDT", "title": "Bangladeshi Taka", "symbol": "BDT", "native": "৳", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "11644", "max_amount": "*********"}, "BGN": {"code": "BGN", "title": "Bulgarian Lev", "symbol": "BGN", "native": "лв.", "thousands_sep": " ", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "179", "max_amount": "1790871"}, "BHD": {"code": "BHD", "title": "<PERSON><PERSON> dinar", "symbol": "BHD", "native": "د.ب.‏", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 3, "min_amount": "377", "max_amount": "3765970"}, "BND": {"code": "BND", "title": "Brunei Dollar", "symbol": "BND", "native": "$", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "132", "max_amount": "1319416"}, "BOB": {"code": "BOB", "title": "Bolivian Boliviano", "symbol": "BOB", "native": "Bs", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "685", "max_amount": "6847960"}, "BRL": {"code": "BRL", "title": "Brazilian Real", "symbol": "R$", "native": "R$", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "570", "max_amount": "5700224"}, "BYN": {"code": "BYN", "title": "Belarusian ruble", "symbol": "BYN", "native": "BYN", "thousands_sep": " ", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "324", "max_amount": "3242595"}, "CAD": {"code": "CAD", "title": "Canadian Dollar", "symbol": "CA$", "native": "$", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "139", "max_amount": "1387250"}, "CHF": {"code": "CHF", "title": "Swiss Franc", "symbol": "CHF", "native": "CHF", "thousands_sep": "'", "decimal_sep": ".", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "86", "max_amount": "858080"}, "CLP": {"code": "CLP", "title": "Chilean Peso", "symbol": "CLP", "native": "$", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 0, "min_amount": "944", "max_amount": "9438172"}, "CNY": {"code": "CNY", "title": "Chinese Renminbi Yuan", "symbol": "CN¥", "native": "CN¥", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "716", "max_amount": "7159204"}, "COP": {"code": "COP", "title": "Colombian Peso", "symbol": "COP", "native": "$", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "405201", "max_amount": "4052007508"}, "CRC": {"code": "CRC", "title": "Costa Rican Colón", "symbol": "CRC", "native": "₡", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "51870", "max_amount": "518701644"}, "CZK": {"code": "CZK", "title": "Czech Koruna", "symbol": "CZK", "native": "Kč", "thousands_sep": " ", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "2323", "max_amount": "23232104"}, "DKK": {"code": "DKK", "title": "Danish Krone", "symbol": "DKK", "native": "kr", "thousands_sep": "", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "684", "max_amount": "6838604"}, "DOP": {"code": "DOP", "title": "Dominican Peso", "symbol": "DOP", "native": "$", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "5895", "max_amount": "58947947"}, "DZD": {"code": "DZD", "title": "Algerian Dinar", "symbol": "DZD", "native": "د.ج.‏", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "13411", "max_amount": "134106121"}, "EGP": {"code": "EGP", "title": "Egyptian Pound", "symbol": "EGP", "native": "ج.م.‏", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "4863", "max_amount": "48626104"}, "ETB": {"code": "ETB", "title": "Ethiopian Birr", "symbol": "ETB", "native": "ብር", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "8025", "max_amount": "80247402"}, "EUR": {"code": "EUR", "title": "Euro", "symbol": "€", "native": "€", "thousands_sep": " ", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "92", "max_amount": "915704"}, "GBP": {"code": "GBP", "title": "British Pound", "symbol": "£", "native": "£", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "78", "max_amount": "780853"}, "GEL": {"code": "GEL", "title": "Georgian Lari", "symbol": "GEL", "native": "GEL", "thousands_sep": " ", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "271", "max_amount": "2705040"}, "GHS": {"code": "GHS", "title": "Ghanaian cedi", "symbol": "GHS", "native": "GHS", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "1541", "max_amount": "15410887"}, "GTQ": {"code": "GTQ", "title": "Guatemalan <PERSON>", "symbol": "GTQ", "native": "Q", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "768", "max_amount": "7680264"}, "HKD": {"code": "HKD", "title": "Hong Kong Dollar", "symbol": "HK$", "native": "$", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "781", "max_amount": "7807950"}, "HNL": {"code": "HNL", "title": "<PERSON><PERSON><PERSON>", "symbol": "HNL", "native": "L", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "2453", "max_amount": "24532344"}, "HRK": {"code": "HRK", "title": "Croatian Kuna", "symbol": "HRK", "native": "kn", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "691", "max_amount": "6907950"}, "HUF": {"code": "HUF", "title": "Hungarian Forint", "symbol": "HUF", "native": "Ft", "thousands_sep": " ", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "36510", "max_amount": "365103831"}, "IDR": {"code": "IDR", "title": "Indonesian Rupiah", "symbol": "IDR", "native": "Rp", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "1617595", "max_amount": "16175950000"}, "ILS": {"code": "ILS", "title": "Israeli New <PERSON><PERSON>", "symbol": "₪", "native": "₪", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "380", "max_amount": "3799935"}, "INR": {"code": "INR", "title": "Indian Rupee", "symbol": "₹", "native": "₹", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "8377", "max_amount": "83767350"}, "IQD": {"code": "IQD", "title": "Iraqi dinar", "symbol": "IQD", "native": "د.ع.‏", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 3, "min_amount": "1298173", "max_amount": "12981733280"}, "IRR": {"code": "IRR", "title": "Iranian rial", "symbol": "IRR", "native": "ريال", "thousands_sep": ",", "decimal_sep": "/", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "4210500", "max_amount": "42105000352"}, "ISK": {"code": "ISK", "title": "Icelandic Króna", "symbol": "ISK", "native": "kr", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 0, "min_amount": "138", "max_amount": "1379504"}, "JMD": {"code": "JMD", "title": "Jamaican Dollar", "symbol": "JMD", "native": "$", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "15504", "max_amount": "155042805"}, "JOD": {"code": "JOD", "title": "Jordanian dinar", "symbol": "JOD", "native": "JOD", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 3, "min_amount": "709", "max_amount": "7087040"}, "JPY": {"code": "JPY", "title": "Japanese Yen", "symbol": "¥", "native": "￥", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 0, "min_amount": "147", "max_amount": "1465950"}, "KES": {"code": "KES", "title": "Kenyan Shilling", "symbol": "KES", "native": "Ksh", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "12883", "max_amount": "128828458"}, "KGS": {"code": "KGS", "title": "Kyrgyzstani Som", "symbol": "KGS", "native": "KGS", "thousands_sep": " ", "decimal_sep": "-", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "8403", "max_amount": "84030384"}, "KRW": {"code": "KRW", "title": "South Korean Won", "symbol": "₩", "native": "₩", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 0, "min_amount": "1358", "max_amount": "13581650"}, "KZT": {"code": "KZT", "title": "<PERSON><PERSON>", "symbol": "KZT", "native": "₸", "thousands_sep": " ", "decimal_sep": "-", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "47034", "max_amount": "470338323"}, "LBP": {"code": "LBP", "title": "Lebanese Pound", "symbol": "LBP", "native": "ل.ل.‏", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "8874340", "max_amount": "88743396054"}, "LKR": {"code": "LKR", "title": "Sri Lankan Rupee", "symbol": "LKR", "native": "රු.", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "29919", "max_amount": "299189672"}, "MAD": {"code": "MAD", "title": "Moroccan <PERSON><PERSON><PERSON>", "symbol": "MAD", "native": "د.م.‏", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "979", "max_amount": "9790963"}, "MDL": {"code": "MDL", "title": "Moldovan Leu", "symbol": "MDL", "native": "MDL", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "1762", "max_amount": "17620290"}, "MMK": {"code": "MMK", "title": "Myanmar kyat", "symbol": "MMK", "native": "MMK", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "324796", "max_amount": "3247960992"}, "MNT": {"code": "MNT", "title": "Mongolian Tögrög", "symbol": "MNT", "native": "MNT", "thousands_sep": " ", "decimal_sep": ",", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "345000", "max_amount": "3450000346"}, "MOP": {"code": "MOP", "title": "Macanese pataca", "symbol": "MOP", "native": "MOP", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "797", "max_amount": "7972623"}, "MUR": {"code": "MUR", "title": "Mauritian Rupee", "symbol": "MUR", "native": "MUR", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "4675", "max_amount": "46750378"}, "MVR": {"code": "MVR", "title": "Maldivian Rufiyaa", "symbol": "MVR", "native": "MVR", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "1536", "max_amount": "15360378"}, "MXN": {"code": "MXN", "title": "Mexican Peso", "symbol": "MX$", "native": "$", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "1918", "max_amount": "19178504"}, "MYR": {"code": "MYR", "title": "Malaysian Ringgit", "symbol": "MYR", "native": "RM", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "450", "max_amount": "4497504"}, "MZN": {"code": "MZN", "title": "Mozambican Metical", "symbol": "MZN", "native": "MTn", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "6390", "max_amount": "63899991"}, "NGN": {"code": "NGN", "title": "Nigerian Naira", "symbol": "NGN", "native": "₦", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "163595", "max_amount": "1635950377"}, "NIO": {"code": "NIO", "title": "Nicaraguan Córdoba", "symbol": "NIO", "native": "C$", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "3648", "max_amount": "36478506"}, "NOK": {"code": "NOK", "title": "Norwegian Krone", "symbol": "NOK", "native": "kr", "thousands_sep": " ", "decimal_sep": ",", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "1095", "max_amount": "10948150"}, "NPR": {"code": "NPR", "title": "Nepalese Rupee", "symbol": "NPR", "native": "नेरू", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "13277", "max_amount": "132765646"}, "NZD": {"code": "NZD", "title": "New Zealand Dollar", "symbol": "NZ$", "native": "$", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "168", "max_amount": "1678557"}, "PAB": {"code": "PAB", "title": "Panamanian Balboa", "symbol": "PAB", "native": "B/.", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "99", "max_amount": "990981"}, "PEN": {"code": "PEN", "title": "Peruvian Nuevo Sol", "symbol": "PEN", "native": "S/.", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "371", "max_amount": "3708923"}, "PHP": {"code": "PHP", "title": "Philippine Peso", "symbol": "PHP", "native": "₱", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "5780", "max_amount": "57795038"}, "PKR": {"code": "PKR", "title": "Pakistani Rupee", "symbol": "PKR", "native": "₨", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "27599", "max_amount": "275991393"}, "PLN": {"code": "PLN", "title": "Polish Złoty", "symbol": "PLN", "native": "zł", "thousands_sep": " ", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "393", "max_amount": "3929050"}, "PYG": {"code": "PYG", "title": "Paraguayan Guaraní", "symbol": "PYG", "native": "₲", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 0, "min_amount": "7505", "max_amount": "75053793"}, "QAR": {"code": "QAR", "title": "Qatari Riyal", "symbol": "QAR", "native": "ر.ق.‏", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "361", "max_amount": "3613606"}, "RON": {"code": "RON", "title": "Romanian Leu", "symbol": "RON", "native": "RON", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "456", "max_amount": "4560404"}, "RSD": {"code": "RSD", "title": "Serbian Dinar", "symbol": "RSD", "native": "дин.", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "10716", "max_amount": "107157442"}, "RUB": {"code": "RUB", "title": "Russian Ruble", "symbol": "RUB", "native": "₽", "thousands_sep": " ", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "8922", "max_amount": "89218514"}, "SAR": {"code": "SAR", "title": "Saudi Riyal", "symbol": "SAR", "native": "ر.س.‏", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "375", "max_amount": "3753930"}, "SEK": {"code": "SEK", "title": "Swedish Krona", "symbol": "SEK", "native": "kr", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "1055", "max_amount": "10554325"}, "SGD": {"code": "SGD", "title": "Singapore Dollar", "symbol": "SGD", "native": "$", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "133", "max_amount": "1327104"}, "SYP": {"code": "SYP", "title": "Syrian pound", "symbol": "SYP", "native": "ل.س.‏", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "251253", "max_amount": "2512530370"}, "THB": {"code": "THB", "title": "Thai Baht", "symbol": "฿", "native": "฿", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "3531", "max_amount": "35313038"}, "TJS": {"code": "TJS", "title": "<PERSON>i Somoni", "symbol": "TJS", "native": "TJS", "thousands_sep": " ", "decimal_sep": ";", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "1048", "max_amount": "10479788"}, "TRY": {"code": "TRY", "title": "Turkish Lira", "symbol": "TRY", "native": "TL", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "3310", "max_amount": "33102504"}, "TTD": {"code": "TTD", "title": "Trinidad and Tobago Dollar", "symbol": "TTD", "native": "$", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "671", "max_amount": "6710617"}, "TWD": {"code": "TWD", "title": "New Taiwan Dollar", "symbol": "NT$", "native": "NT$", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "3260", "max_amount": "32603904"}, "TZS": {"code": "TZS", "title": "Tanzanian <PERSON>", "symbol": "TZS", "native": "TSh", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "268068", "max_amount": "2680675731"}, "UAH": {"code": "UAH", "title": "Ukrainian Hryvnia", "symbol": "UAH", "native": "₴", "thousands_sep": " ", "decimal_sep": ",", "symbol_left": false, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "4089", "max_amount": "40888706"}, "UGX": {"code": "UGX", "title": "Ugandan <PERSON>", "symbol": "UGX", "native": "USh", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 0, "min_amount": "3692", "max_amount": "36918006"}, "USD": {"code": "USD", "title": "United States Dollar", "symbol": "$", "native": "$", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": false, "drop_zeros": false, "exp": 2, "min_amount": "100", "max_amount": 1000000}, "UYU": {"code": "UYU", "title": "Uruguayan Peso", "symbol": "UYU", "native": "$", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "3993", "max_amount": "39930412"}, "UZS": {"code": "UZS", "title": "Uzbekistani Som", "symbol": "UZS", "native": "UZS", "thousands_sep": " ", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "1246166", "max_amount": "12461658197"}, "VEF": {"code": "VEF", "title": "Venezuelan bolívar", "symbol": "VEF", "native": "Bs.F.", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "362255253", "max_amount": "999999999999"}, "VND": {"code": "VND", "title": "Vietnamese Đồng", "symbol": "₫", "native": "₫", "thousands_sep": ".", "decimal_sep": ",", "symbol_left": false, "space_between": true, "drop_zeros": false, "exp": 0, "min_amount": "25218", "max_amount": "252175000"}, "YER": {"code": "YER", "title": "Yemeni R<PERSON>", "symbol": "YER", "native": "ر.ي.‏", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "25035", "max_amount": "250350363"}, "ZAR": {"code": "ZAR", "title": "South African Rand", "symbol": "ZAR", "native": "R", "thousands_sep": ",", "decimal_sep": ".", "symbol_left": true, "space_between": true, "drop_zeros": false, "exp": 2, "min_amount": "1828", "max_amount": "18278037"}}