import ast
import traceback
import plugins_manager
from typing import Callable, Any, Optional, List

from java import dynamic_proxy, cast
from java.util import ArrayList

from com.exteragram.messenger.plugins import Plugins<PERSON>ontroller, Plugin
from org.telegram.messenger import Utilities, FileLoader, MessageObject, AndroidUtilities, R, ApplicationLoader
from org.telegram.ui.ActionBar import AlertDialog

from android_utils import log
from base_plugin import BasePlugin, HookResult, HookStrategy
from client_utils import get_file_loader, run_on_ui_thread, get_last_fragment
from ui.settings import Selector, Text
from ui.alert import AlertDialogBuilder
from ui.bulletin import BulletinHelper

__id__ = "qmrrchh_plugins_manager"
__name__ = "Plugins Manager"
__author__ = "@qmrrchh"
__icon__ = "remusic/3"
__version__ = "1.3.0"
__min_version__ = "11.9.1"
__description__ = "Manager of locally installed plugins.\nYou can find out all the commands by writing .phelp in any chat."

# From zwyLib
class Callback1(dynamic_proxy(Utilities.Callback)):
    def __init__(self, fn: Callable[[Any], None]):
        super().__init__()
        self._fn = fn

    def run(self, arg):
        try:
            self._fn(arg)
        except Exception:
            log(f"Error in Callback1: {traceback.format_exc().rstrip()}")

pm = plugins_manager.PluginsManager

def show_with_copy(message, submsg):
    def copy():
        if AndroidUtilities.addToClipboard(submsg):
            BulletinHelper.show_copied_to_clipboard()

    BulletinHelper.show_with_button(message, R.raw.error, "Copy", lambda : copy())

# From zwyLib
def download_and_install_plugin(document, enable: bool):
    def plugin_install_callback(arg):
        if arg is None:
            BulletinHelper.show_two_line("Plugin installed.", "Now you can check him in settings!", R.raw.contact_check)
            return
        show_with_copy("An error has occurred", arg)

    file_loader = get_file_loader()
    plugins_controller = PluginsController.getInstance()
    path = file_loader.getPathToAttach(document, True)

    if not path.exists():
        log("Started loading the file...")
        file_loader.loadFile(document, "new_plugin", FileLoader.PRIORITY_NORMAL, 1)
        download_and_install_plugin(document, enable)
        return

    log("Installing...")
    plugins_controller.loadPluginFromFile(str(path), Callback1(plugin_install_callback))

    if enable:
        try:
            plugin_id = get_id_from_file(path.getAbsolutePath())
            plugins_controller.setPluginEnabled(plugin_id, True, Callback1(plugin_install_callback))
        except Exception as e:
            show_with_copy("An error has occurred", e)


def get_id_from_file(file_path):
    with open(file_path, "r", encoding="utf-8") as file:
        tree = ast.parse(file.read(), filename=file_path)

    for node in ast.walk(tree):
        if isinstance(node, ast.Assign):
            for target in node.targets:
                if isinstance(target, ast.Name) and target.id == "__id__":
                    return ast.literal_eval(node.value)

    return None


# from debug plugin (@exteraDev)
def _get_plugins_info(max: int):
    try:
        active_plugins = [p for p in plugins_manager.PluginsManager._plugins.values()]
        count = len(active_plugins)
        if not active_plugins:
            return "Plugins not found."
        max_plugins = get_max_plugins(max)
        shown = active_plugins[:max_plugins]
        more = count - max_plugins
        plugins_md = "\n".join(f"- {p.name} | {p.id}" for p in shown)
        if more > 0:
            plugins_md += f"\n- ...and {more} more plugins"
        return f"🧩 All your Plugins ({count})\n{plugins_md}"
    except Exception as e:
        return f"Error getting plugins: {str(e)}"


def get_max_plugins(max: int):
    if max == 0:
        return 5
    elif max == 1:
        return 10
    elif max == 2:
        return 15
    elif max == 3:
        return 20
    elif max == 4:
        return 30
    elif max == 5:
        return 40
    elif max == 6:
        return 50
    else:
        return 20


def restart():
    pm.shutdown()
    PluginsController.getInstance().shutdown()
    pm.init(pm._plugins_dir)
    PluginsController.getInstance().init()


def search_plugin(prompt: str) -> Optional[BasePlugin]:
    prompt = prompt.lower()
    plugins = [p for p in plugins_manager.PluginsManager._plugins.values()]
    plugins_names = [p.name.lower() for p in plugins_manager.PluginsManager._plugins.values()]
    plugins_ids = [p.id.lower() for p in plugins_manager.PluginsManager._plugins.values()]

    if prompt in plugins_names:
        plugin = [p for p in plugins if prompt == p.name.lower()]
        if len(plugin) == 1 and plugin[0]:
            return plugin[0]
        else:
            return None

    elif prompt in plugins_ids:
        plugin = [p for p in plugins if prompt ==  p.id.lower()]
        if len(plugin) == 1 and plugin[0]:
            return plugin[0]
        else:
            return None

    else:
        return None


def enable_plugin(plugin_id: str, enabled: bool, params) -> HookResult:
    if len(plugin_id) <= 0:
        params.message = "Plugin name/id not found."
        return HookResult(strategy=HookStrategy.MODIFY, params=params)

    plugin = search_plugin(plugin_id)
    if plugin:
        enabled_text = "enable" if enabled else "disable"
        if pm.set_plugin_enabled(plugin.id, enabled):
            BulletinHelper.show_success(f"Plugin {plugin_id} {enabled_text}d!")
            restart()
        else:
            BulletinHelper.show_error(f"Plugin {enabled_text} for {plugin_id} failed.")
    else:
        BulletinHelper.show_error(f"Plugin {plugin_id} not found!")

    return HookResult(strategy=HookStrategy.CANCEL)


def delete_plugin(plugin_id: str, with_restart: bool, bulletins: bool = True) -> bool:
    plugin = search_plugin(plugin_id)
    if plugin:
        if pm.delete_plugin(plugin.id):
            if bulletins:
                BulletinHelper.show_success(f"Plugin {plugin_id} deleted!")
            if with_restart:
                restart()
            return True
        else:
            if bulletins:
                BulletinHelper.show_error(f"Plugin delete for {plugin_id} failed.")
            return False
    else:
        if bulletins:
            BulletinHelper.show_error(f"Plugin {plugin_id} not found!")
        return False


class LoaderPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.alert_builder_instance = None


    def on_plugin_load(self):
        self.add_on_send_message_hook()


    def create_settings(self):
        return [
            Selector(
                key="max_plugins_count",
                text="Max plugin count",
                items=["5", "10", "15", "20", "30", "40", "50"],
                default=3
            ),
            Text(
                text="Support development",
                icon="msg_ton",
                accent=True,
                on_click=lambda view: self.show_info_alert(
                    title="Support development",
                    message="Below you can copy the TON address of the Plugins Manager developers and support the development with your donation.",
                    neutral_button="Donate",
                    listener2=lambda dialog, i: self.copy("UQBVxjueXqAEpALX_b0yr-ytXN26LOTpSBn26b9VRHKrmm5F"))
            )
        ]


    def on_send_message_hook(self, account: int, params: Any):
        text: str = params.message
        try:

            if text == ".pdl" and params.replyToMsg:
                document = MessageObject.getDocument(params.replyToMsg)
                if document and str(document.file_name_fixed).endswith(".plugin"):
                    run_on_ui_thread(lambda: download_and_install_plugin(document, False))
                    return HookResult(strategy=HookStrategy.CANCEL)
                else:
                    return HookResult(strategy=HookStrategy.DEFAULT)

            elif text == ".pdle" and params.replyToMsg:
                document = MessageObject.getDocument(params.replyToMsg)
                if document and str(document.file_name_fixed).endswith(".plugin"):
                    run_on_ui_thread(lambda: download_and_install_plugin(document, True))
                    return HookResult(strategy=HookStrategy.CANCEL)
                else:
                    return HookResult(strategy=HookStrategy.DEFAULT)

            elif text.strip().lower() == ".plist":
                params.message = _get_plugins_info(self.get_setting("max_plugins_count", 3))
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

            elif text.startswith(".pdis "):
                plugin_prompt = text.replace(".pdis ", "")
                return enable_plugin(plugin_prompt, False, params)

            elif text.startswith(".pen "):
                plugin_prompt = text.replace(".pen ", "")
                return enable_plugin(plugin_prompt, True, params)

            elif text.startswith(".pdel "):
                plugin_prompt = text.replace(".pdel ", "")
                if len(plugin_prompt) <= 0:
                    params.message = "Plugin name/id not found."
                    return HookResult(strategy=HookStrategy.MODIFY, params=params)
                delete_plugin(plugin_prompt, True)
                return HookResult(strategy=HookStrategy.CANCEL)

            elif text.strip().lower() == ".pdela":
                run_on_ui_thread(lambda: self.show_info_alert(
                    "Are you sure?",
                    "Are you sure you want to delete ALL THE INSTALLED plugins?",
                    "Yes",
                    "No",
                    lambda dialog, i: self._dismiss_dialog(self.alert_builder_instance),
                    lambda dialog, i: self.delete_all_plugins()
                ))
                return HookResult(strategy=HookStrategy.CANCEL)

            elif text.strip().lower() == ".phelp":
                params.message = "Usage:\n   .plist - Shows your plugins\n   .pdl - Download and install replied plugin.\n   .pdle - Download, install and enable replied plugin.\n   .pdis {name/id} - Disables entered plugin.\n   .pel {name/id} - Enables entered plugin.\n   .pdel {name/id} - Deletes entered plugin.\n   .pdela - Deletes all installed plugin."
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

            else:
                return HookResult(strategy=HookStrategy.DEFAULT)

        except Exception as e:
            log(str(e))
            return HookResult(strategy=HookStrategy.DEFAULT)


    def delete_all_plugins(self):
        try:
            self.show_loading_alert("Deleting plugins...")
            deleted_count = 0

            for plugin in list(pm._plugins.values()):
                if plugin.id == __id__: continue
                if pm.delete_plugin(plugin.id):
                    deleted_count += 1
                    run_on_ui_thread(lambda: self.update_alert(f"Deleting \"{plugin.name}\"...", deleted_count))

            run_on_ui_thread(lambda: self.update_alert(f"Restarting plugin engine...", 99))
            restart()
            self._dismiss_dialog(self.alert_builder_instance)
            BulletinHelper.show_success("All installed plugins deleted!")
        except Exception as e:
            run_on_ui_thread(lambda: self._dismiss_dialog(self.alert_builder_instance))
            show_with_copy("Error deleting plugins.", str(e))


    # from reSpotify
    def show_info_alert(self, title="TITLE", message="MESSAGE", positive_button="OK", neutral_button=None,
                           listener2: Optional[Callable[['AlertDialogBuilder', int], None]] = None, listener: Optional[Callable[['AlertDialogBuilder', int], None]] = None):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext

        builder = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
        builder.set_title(title)
        builder.set_message(message)

        if listener:
            builder.set_positive_button(positive_button, listener)
        else:
            builder.set_positive_button(positive_button, self._dismiss_dialog(self.alert_builder_instance))

        if neutral_button:
            builder.set_neutral_button(neutral_button, lambda builder, which: listener2)

        self.alert_builder_instance = builder.show()


    # from reSpotify
    def show_loading_alert(self, title="Дайте денег пожалуйста"):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        builder = AlertDialog(ctx, AlertDialogBuilder.ALERT_TYPE_LOADING)
        builder.setTitle(title)
        builder.setCancelable(False)
        builder.setCanceledOnTouchOutside(False)
        self.alert_builder_instance = builder
        self.alert_builder_instance.show()
        self.alert_builder_instance.setProgress(0)


    def update_alert(self, title: str, progress: int):
        self.alert_builder_instance.setTitle(title)
        self._update_dialog_progress(self.alert_builder_instance, progress)


    # from reSpotify
    def _update_dialog_progress(self, builder_instance: AlertDialogBuilder, progress: int):
        if builder_instance and builder_instance.isShowing():
            builder_instance.setProgress(progress)


    # from reSpotify
    def _dismiss_dialog(self, builder_instance: AlertDialogBuilder):
        def action():
            if builder_instance is not None:
                try:
                    dlg = builder_instance.getDialog() if hasattr(builder_instance, 'getDialog') else builder_instance
                    if dlg and dlg.isShowing():
                        dlg.dismiss()
                except Exception:
                    pass
                finally:
                    self.alert_builder_instance = None

        run_on_ui_thread(action)


    def copy(self, value):
        if AndroidUtilities.addToClipboard(value):
            BulletinHelper.show_copied_to_clipboard()