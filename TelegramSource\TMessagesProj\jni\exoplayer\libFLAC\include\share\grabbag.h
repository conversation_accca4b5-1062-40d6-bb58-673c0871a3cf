/* grabbag - Convenience lib for various routines common to several tools
 * Copyright (C) 2002-2009  <PERSON>
 * Copyright (C) 2011-2016  Xiph.Org Foundation
 *
 * This library is free software; you can redistribute it and/or
 * modify it under the terms of the GNU Lesser General Public
 * License as published by the Free Software Foundation; either
 * version 2.1 of the License, or (at your option) any later version.
 *
 * This library is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
 * Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public
 * License along with this library; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301  USA
 */

#ifndef SHARE__GRABBAG_H
#define SHARE__GRABBAG_H

/* These can't be included by themselves, only from within grabbag.h */
#include "grabbag/cuesheet.h"
#include "grabbag/file.h"
#include "grabbag/picture.h"
#include "grabbag/replaygain.h"
#include "grabbag/seektable.h"

#endif
