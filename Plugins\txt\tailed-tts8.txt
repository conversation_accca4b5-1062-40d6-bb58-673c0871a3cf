from client_utils import get_account_instance, get_last_fragment
from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from org.telegram.messenger import SendMessagesHelper
from ui.settings import Header, Input, Divider, Switch, Selector
from ui.bulletin import BulletinHelper
from ui.alert import AlertDialogBuilder
from java.io import File
from java.util import Locale
import requests, urllib.parse, uuid, os, threading, traceback

__id__ = "tailed-tts"
__name__ = "text-to-speech"
__description__ = "uses voicerss api to voice your text with .tts [text] command"
__author__ = "@tailsjs"
__min_version__ = "11.9.0"
__icon__ = "pomogitechezahuynya/3"
__version__ = "1.3"

BASE_TTS_URI = "https://api.voicerss.org/"
key = "2c7f658aa1b643669c71d0bd8a29fe70"
save_dir = "/storage/emulated/0/Download/is_that_tts"

voices = (
    "<PERSON>",
    "<PERSON>",
    "<PERSON>"
)

audio_formats = (
    "8khz",
    "11khz",
    "12khz",
    "16khz",
    "22khz",
    "24khz",
    "32khz",
    "44khz",
    "48khz"
)

quality = (
    "8bit",
    "16bit"
)

class LocalizationManager:
    def __init__(self):
        self.language = Locale.getDefault().getLanguage()
        self.language = self.language if self.language in self._get_supported_languages() else "en"
        
    def get_string(self, string):
        return self.strings[self.language][string]

    def _get_supported_languages(self):
        return self.strings.keys()
    
    strings = {
        "ru": {
            "USAGE_EXAMPLE": "⚠️ пример: .tts [текст]",
            "API_ERROR": "⛔ произошла ошибка на стороне api!",
            "CODE_ERROR": "⛔ произошла ошибка в коде плагина!",
            "RESULT": "⭐ текст озвучен!",
            "SETTINGS_TTS_TITLE": "настройки api",
            "SETTINGS_TTS_VOICES": "голос",
            "SETTINGS_TTS_FORMAT": "формат аудио",
            "SETTINGS_TTS_USE_STEREO": "использовать стерео?",
            "SETTINGS_TTS_USE_STEREO_SUBTEXT": "в ином случае, будет использоваться моно",
            "SETTINGS_TTS_QUALITY": "качество звука",
            "SETTINGS_PLUGIN_TITLE": "настройки плагина",
            "SETTINGS_PLUGIN_ON_TEXT": "включить текст",
            "SETTINGS_PLUGIN_ON_TEXT_SUBTEXT": "если включено, то текст '⭐ текст озвучен!' будет отображаться под голосовым сообщением",
            "LOADING_DIALOG_TITLE": "секунду, озвучиваем текст...",
            "LOADING_DIALOG_MESSAGE": "это может занять некоторое время..."
        },
        "en": {
            "USAGE_EXAMPLE": "⚠️ usage: .tts [text]", 
            "API_ERROR": "⛔ an error occured on api side!",
            "CODE_ERROR": "⛔ an error occurred in plugin code!",
            "RESULT": "⭐ text is voiced now!",
            "SETTINGS_TTS_TITLE": "api settings",
            "SETTINGS_TTS_VOICES": "voice",
            "SETTINGS_TTS_FORMAT": "audio format",
            "SETTINGS_TTS_USE_STEREO": "use stereo?",
            "SETTINGS_TTS_USE_STEREO_SUBTEXT": "otherwise, mono will be used",
            "SETTINGS_TTS_QUALITY": "sound quality",
            "SETTINGS_PLUGIN_TITLE": "plugin settings",
            "SETTINGS_PLUGIN_ON_TEXT": "enable text",
            "SETTINGS_PLUGIN_ON_TEXT_SUBTEXT": "if enabled, the text '⭐ text is voiced now!' will be displayed below the voice message",
            "LOADING_DIALOG_TITLE": "hold on, voicing the text...",
            "LOADING_DIALOG_MESSAGE": "it can take some time..."
        }
    }

locali = LocalizationManager()

class IsThatTTSPlugin(BasePlugin):
    def on_plugin_load(self):
        self.add_on_send_message_hook()
        
    def create_settings(self):
        return [
            Header(locali.get_string("SETTINGS_PLUGIN_TITLE")),
            Switch(
                key="plugin_enable_text",
                text=locali.get_string("SETTINGS_PLUGIN_ON_TEXT"),
                default=True,
                subtext=locali.get_string("SETTINGS_PLUGIN_ON_TEXT_SUBTEXT")  
            ),
            Header(locali.get_string("SETTINGS_TTS_TITLE")),
            Switch(
                key="tts_should_use_stereo",
                text=locali.get_string("SETTINGS_TTS_USE_STEREO"),
                default=True,
                subtext=locali.get_string("SETTINGS_TTS_USE_STEREO_SUBTEXT")
            ),
            Selector(
                key="tts_voice",
                text=locali.get_string("SETTINGS_TTS_VOICES"),
                default=0,
                items=voices
            ),
            Selector(
                key="tts_format",
                text=locali.get_string("SETTINGS_TTS_FORMAT"),
                default=8,
                items=audio_formats
            ),
            Selector(
                key="tts_quality",
                text=locali.get_string("SETTINGS_TTS_QUALITY"),
                default=1,
                items=quality
            )
        ]
        
    def on_send_message_hook(self, account, params) -> HookStrategy:
        if not isinstance(params.message, str) or not params.message.startswith(".tts"):
            return HookResult()

        try:
            text = ' '.join(params.message.strip().split()[1:])
            
            if len(text) is 0:
                BulletinHelper.show_info(locali.get_string("USAGE_EXAMPLE"))
                return HookResult(strategy=HookStrategy.CANCEL, params=params)
            
            self.alert = LoadingDialogHelper()
            self.params = params
            self.text = text
            
            self.alert.show()
            
            threading.Thread(target=self.handle_tts, daemon=True).start()
            
            return HookResult(strategy=HookStrategy.CANCEL, params=params)
            
        except Exception as e:
            BulletinHelper.show_error(locali.get_string("CODE_ERROR"))
            return HookResult(strategy=HookStrategy.CANCEL, params=params)
            pass
        
    def handle_tts(self):
        try:
            tts_file, error = self.get_tts_result(self.text)
            
            if tts_file is not None:
                self._send_audio(tts_file, self.params.peer, self.params.replyToMsg, self.params.replyToTopMsg)
                return True
                
            if error is not None:
                BulletinHelper.show_error(locali.get_string("API_ERROR"))
                return False
            
            return False
        except Exception as e:
            BulletinHelper.show_error(locali.get_string("CODE_ERROR"))
        
        
    def _send_audio(self, file, dialog_id, replyToMsg=None, replyToTopMsg=None):
        account_instance = get_account_instance()
        
        should_enable_text = self.get_setting("plugin_enable_text", True)
        
        text = locali.get_string("RESULT") if should_enable_text else ""
        
        SendMessagesHelper.prepareSendingDocument( # thx reSpotify
            account_instance, file, file, None, text, "audio/ogg",
            dialog_id, replyToMsg, replyToTopMsg, None, None, None,
            True, 0, None, None, 0, False
        )
        
        self.alert.destruct()
        
    def get_format(self):
        audio_format_index = self.get_setting("tts_format", 8)
        should_use_stereo = self.get_setting("tts_should_use_stereo", True)
        quality_index = self.get_setting("tts_quality", 1)
        
        audio_format = audio_formats[audio_format_index]
        tts_quality = quality[quality_index]
        steremono = "stereo" if should_use_stereo else "mono"
        
        return f"{audio_format}_{tts_quality}_{steremono}"
        
    def get_tts_result(self, text):
        filename = f"tts_{uuid.uuid4()}.ogg"
        
        save_file = File(save_dir, filename).getAbsolutePath()
        
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
            
        sound_format = self.get_format()
        
        voice_index = self.get_setting("tts_voice", 0)
        voice = voices[voice_index]
        
        request_uri = BASE_TTS_URI + "?key=" + key + "&hl=ru-ru&c=OGG" + "&v=" + voice + "&f=" + sound_format +"&src=" + urllib.parse.quote(text)
        
        audio_response = requests.get(request_uri, timeout=10)
        
        if audio_response.status_code != 200:
            log("Failed to make TTS!")
            return None, audio_response.text 
        
        with open(save_file, 'wb') as file:
            for chunk in audio_response.iter_content(chunk_size=8192):
                if chunk:
                    file.write(chunk)
                    
        return save_file, None
    
class LoadingDialogHelper:
    def __init__(self):
        self.is_ready = False
        
        current_fragment = get_last_fragment()
        if not current_fragment:
            BulletinHelper.show_error(locali.get_string("CODE_ERROR"))
            return
        
        activity = current_fragment.getParentActivity()
        if not activity:
            BulletinHelper.show_error(locali.get_string("CODE_ERROR"))
            return
        
        self.is_ready = True
        self.alert = AlertDialogBuilder(activity, AlertDialogBuilder.ALERT_TYPE_SPINNER)
        self.alert.set_title(locali.get_string("LOADING_DIALOG_TITLE"))
        self.alert.set_message(locali.get_string("LOADING_DIALOG_MESSAGE"))
        self.alert.set_cancelable(False)
    
    def show(self):
        if self.is_ready is True:
            self.alert.show()
            
    def destruct(self):
        if self.is_ready is True:
            self.alert.dismiss()    