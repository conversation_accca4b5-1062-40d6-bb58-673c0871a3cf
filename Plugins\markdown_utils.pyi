# markdown_utils.pyi
import re
from enum import Enum
from dataclasses import dataclass
from typing import List, Tuple, Optional, Dict, Any

__all__: List[str]

class TLEntityType(Enum):
    CODE: str
    PRE: str
    STRIKETHROUGH: str
    TEXT_LINK: str
    BOLD: str
    ITALIC: str
    UNDERLINE: str
    SPOILER: str
    CUSTOM_EMOJI: str

@dataclass
class RawEntity:
    type: TLEntityType
    offset: int
    length: int
    language: Optional[str] = ...
    url: Optional[str] = ...
    document_id: Optional[int] = ...

    def to_tlrpc_object(self) -> Any: ...

@dataclass
class ParsedMessage:
    text: str
    entities: Tuple[RawEntity, ...]

def count_chars_until(string: str, stop_chars: str, start_index: int = 0) -> int: ...
def get_utf16_code_unit_offset(text: str, python_char_index: int) -> int: ...
def parse_markdown(markdown: str) -> ParsedMessage: ...

TLRPC_ENTITIES_MAP: Dict[TLEntityType, Any]