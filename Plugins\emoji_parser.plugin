from base_plugin import <PERSON>Plugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from client_utils import get_messages_controller, run_on_queue, get_user_config
from android_utils import log, run_on_ui_thread
from ui.bulletin import BulletinHelper
from ui.settings import Header, Switch, Divider
from org.telegram.tgnet import TLRPC
from org.telegram.messenger import DialogObject, MessageObject, ChatObject
from markdown_utils import parse_markdown
from client_utils import send_message

__id__ = "emoji_parser"
__name__ = "Emoji Parser"
__description__ = "Парсит document ID премиум эмодзи и отправляет в чат.\n\nКоманды: .emoji, .eid, .estatus, .emd, .esend [markdown]"
__author__ = "@mihailk<PERSON><PERSON>ski & @mishabotov"
__version__ = "1.0 [release]"
__icon__ = "DateRegBot_by_MoiStikiBot/7"
__min_version__ = "11.9.1"

class EmojiParserPlugin(BasePlugin):
    def on_plugin_load(self):
        self.add_on_send_message_hook()

    def _show_msg(self, msg, msg_type="error"):
        func = getattr(BulletinHelper, f"show_{msg_type}")
        run_on_ui_thread(lambda: func(msg))

    def create_settings(self):
        return [
            Header(text="Настройки Emoji Parser"),
            Switch(key="auto_copy", text="Автокопирование", default=False, icon="msg_copy_solar"),
            Switch(key="enable_send", text="Режим отправки", default=True, icon="msg_send_solar"),
            Divider(text="Команды:\n.emoji - полная информация\n.eid - только ID\n.estatus - статус пользователя\n.emd - markdown\n.esend - отправить из реплая\n.esend [😎](ID) - отправить напрямую")
        ]

    def on_send_message_hook(self, account, params):
        if not isinstance(params.message, str):
            return HookResult()

        msg = params.message.strip()
        cmd = msg.lower()

        commands = {".emoji": "full", ".eid": "id_only", ".estatus": "status_only", ".emd": "markdown"}
        if self.get_setting("enable_send", True):
            if cmd == ".esend":
                commands[".esend"] = "send_emoji"
            elif msg.lower().startswith(".esend "):
                emoji_markdown = msg[7:].strip()
                if emoji_markdown:
                    self._show_msg("🔍 Отправляю...", "info")
                    run_on_queue(lambda: self._send_markdown(emoji_markdown, params))
                    return HookResult(strategy=HookStrategy.CANCEL)

        if cmd not in commands:
            return HookResult()

        if not hasattr(params, 'replyToMsg') or not params.replyToMsg:
            self._show_msg("❌ Используйте команду в ответ на сообщение")
            return HookResult(strategy=HookStrategy.CANCEL)

        self._show_msg("🔍 Анализирую...", "info")
        run_on_queue(lambda: self._parse_emoji(params.replyToMsg, params.peer, commands[cmd], params))
        return HookResult(strategy=HookStrategy.CANCEL)

    def _parse_emoji(self, reply_msg, peer_id, mode="full", original_params=None):
        try:
            msg_obj = self._get_message_object(reply_msg)
            if not msg_obj:
                self._show_msg("❌ Не удалось получить сообщение")
                return

            emoji_info = []


            if mode != "status_only" and hasattr(msg_obj, 'entities') and msg_obj.entities:
                for i in range(msg_obj.entities.size()):
                    entity = msg_obj.entities.get(i)
                    if isinstance(entity, TLRPC.TL_messageEntityCustomEmoji):
                        emoji_text = self._extract_emoji_text(msg_obj.message, entity.offset, entity.length)
                        emoji_info.append({
                            'document_id': entity.document_id,
                            'text': emoji_text,
                            'type': 'message_emoji'
                        })


            if mode == "status_only":
                user_id = self._get_user_id(msg_obj)
                if user_id:
                    user = get_messages_controller().getUser(user_id)
                    if user and hasattr(user, 'emoji_status') and user.emoji_status:
                        doc_id = DialogObject.getEmojiStatusDocumentId(user.emoji_status)
                        if doc_id:
                            emoji_info.append({
                                'document_id': doc_id,
                                'text': '👤',
                                'type': 'user_status',
                                'user_name': f"{user.first_name or ''} {user.last_name or ''}".strip()
                            })

            if not emoji_info:
                error_msg = "❌ У пользователя нет эмодзи статуса" if mode == "status_only" else "❌ Премиум эмодзи не найдено"
                self._show_msg(error_msg)
                return


            if mode == "send_emoji":
                self._send_emoji(emoji_info, original_params or reply_msg)
            else:
                result = self._format_result(emoji_info, mode)
                self._send_message(result, original_params or reply_msg)


                if self.get_setting("auto_copy", False) and emoji_info:
                    self._copy_to_clipboard(emoji_info[0]['document_id'])

        except Exception as e:
            self._show_msg(f"❌ Ошибка: {str(e)}")

    def _get_message_object(self, reply_msg):
        try:
            if hasattr(reply_msg, 'messageOwner') and reply_msg.messageOwner:
                return reply_msg.messageOwner
            if hasattr(reply_msg, 'replyMessageObject') and reply_msg.replyMessageObject:
                if hasattr(reply_msg.replyMessageObject, 'messageOwner'):
                    return reply_msg.replyMessageObject.messageOwner
                return reply_msg.replyMessageObject
            if hasattr(reply_msg, 'entities') or hasattr(reply_msg, 'message'):
                return reply_msg
            return None
        except:
            return None

    def _get_topic_id(self, msg_obj, peer_id):
        try:
            if peer_id > 0:
                return 0

            chat_id = -peer_id if peer_id < 0 else peer_id
            chat = get_messages_controller().getChat(chat_id)
            if not chat or not ChatObject.isForum(chat):
                return 0


            if hasattr(msg_obj, 'reply_to') and msg_obj.reply_to:
                if hasattr(msg_obj.reply_to, 'reply_to_top_id') and msg_obj.reply_to.reply_to_top_id != 0:
                    return msg_obj.reply_to.reply_to_top_id
                elif hasattr(msg_obj.reply_to, 'reply_to_msg_id') and msg_obj.reply_to.reply_to_msg_id != 0:
                    return msg_obj.reply_to.reply_to_msg_id


            try:
                account = get_user_config().selectedAccount
                topic_id = MessageObject.getTopicId(account, msg_obj, True)
                return topic_id if topic_id != 0 else 1
            except:
                return 1
        except:
            return 0

    def _get_user_id(self, msg_obj):
        try:
            if hasattr(msg_obj, 'from_id') and msg_obj.from_id:
                if hasattr(msg_obj.from_id, 'user_id'):
                    return msg_obj.from_id.user_id
            if hasattr(msg_obj, 'user_id'):
                return msg_obj.user_id
        except:
            pass
        return None

    def _format_result(self, emoji_info, mode="full"):
        if mode == "id_only":
            return "\n".join([f"`{e['document_id']}`" for e in emoji_info])

        if mode == "markdown":
            markdown = " ".join([f"[{e['text']}]({e['document_id']})" for e in emoji_info if e['type'] != 'user_status'])
            return f"📝 *Markdown:*\n```\n{markdown}\n```" if markdown else "❌ Нет эмодзи для markdown"


        emoji_count = len(emoji_info)
        emoji_word = "эмодзи" if emoji_count == 1 else "эмодзи"
        result = f"✨ *Найдено {emoji_count} {emoji_word}:*\n"

        for i, e in enumerate(emoji_info, 1):
            if e['type'] == 'user_status':
                if emoji_count == 1:
                    result += f"🎭 *Статус {e.get('user_name', 'Unknown')}:* `{e['document_id']}`"
                else:
                    result += f"🎭 *#{i} Статус:* `{e['document_id']}`\n"
            else:
                if emoji_count == 1:
                    result += f"*{e['text']}:* `{e['document_id']}`"
                else:
                    result += f"*#{i}* {e['text']} → `{e['document_id']}`\n"

        return result.strip()

    def _copy_to_clipboard(self, document_id):
        try:
            from android.content import ClipboardManager, ClipData, Context
            from org.telegram.messenger import ApplicationLoader

            context = ApplicationLoader.applicationContext
            clipboard = context.getSystemService(Context.CLIPBOARD_SERVICE)
            clip = ClipData.newPlainText("Document ID", str(document_id))
            clipboard.setPrimaryClip(clip)
            self._show_msg(f"📋 Скопировано: {document_id}", "success")
        except:
            pass

    def _send_emoji(self, emoji_info, original_params):
        try:

            markdown_parts = [f"[{e['text']}]({e['document_id']})"
                            for e in emoji_info if e['type'] != 'user_status']

            if not markdown_parts:
                self._show_msg("❌ Нет эмодзи для отправки")
                return

            markdown_text = " ".join(markdown_parts)
            self._send_markdown(markdown_text, original_params)

        except Exception as e:
            self._show_msg(f"❌ Ошибка отправки: {str(e)}")

    def _send_markdown(self, markdown_text, original_params):
        try:
            parsed = parse_markdown(markdown_text)
            params = {
                "message": parsed.text,
                "peer": original_params.peer,
                "entities": [e.to_tlrpc_object() for e in parsed.entities]
            }


            if hasattr(original_params, 'replyToMsg'):
                params["replyToMsg"] = original_params.replyToMsg
            if hasattr(original_params, 'replyToTopMsg'):
                params["replyToTopMsg"] = original_params.replyToTopMsg

            run_on_ui_thread(lambda: send_message(params))
            self._show_msg("✅ Эмодзи отправлено", "success")

        except Exception as e:
            self._show_msg(f"❌ Ошибка отправки: {str(e)}")

    def _send_message(self, text, original_params):
        try:
            parsed = parse_markdown(text)
            params = {
                "message": parsed.text,
                "peer": original_params.peer,
                "entities": [e.to_tlrpc_object() for e in parsed.entities]
            }


            if hasattr(original_params, 'replyToMsg'):
                params["replyToMsg"] = original_params.replyToMsg
            if hasattr(original_params, 'replyToTopMsg'):
                params["replyToTopMsg"] = original_params.replyToTopMsg

            run_on_ui_thread(lambda: send_message(params))

        except Exception as e:

            clean_text = text.replace("*", "").replace("`", "")
            params = {
                "message": clean_text,
                "peer": original_params.peer
            }

            if hasattr(original_params, 'replyToMsg'):
                params["replyToMsg"] = original_params.replyToMsg
            if hasattr(original_params, 'replyToTopMsg'):
                params["replyToTopMsg"] = original_params.replyToTopMsg

            run_on_ui_thread(lambda: send_message(params))

    def _extract_emoji_text(self, text, offset, length):
        try:
            if not text:
                return "😀"

            start = self._utf16_to_python(text, offset)
            end = self._utf16_to_python(text, offset + length)

            if start is not None and end is not None and start < len(text):
                emoji_text = text[start:end]
                return emoji_text if emoji_text.strip() else "😀"

            return "😀"
        except:
            return "😀"

    def _utf16_to_python(self, text, utf16_index):
        try:
            utf16_pos = 0
            for i, char in enumerate(text):
                if utf16_pos == utf16_index:
                    return i
                utf16_pos += len(char.encode('utf-16le')) // 2
                if utf16_pos > utf16_index:
                    return i
            return len(text)
        except:
            return None
