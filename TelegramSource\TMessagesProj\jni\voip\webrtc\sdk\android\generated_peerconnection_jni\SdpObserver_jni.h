// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/SdpObserver

#ifndef org_webrtc_SdpObserver_JNI
#define org_webrtc_SdpObserver_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_SdpObserver[];
const char kClassPath_org_webrtc_SdpObserver[] = "org/webrtc/SdpObserver";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_SdpObserver_clazz(nullptr);
#ifndef org_webrtc_SdpObserver_clazz_defined
#define org_webrtc_SdpObserver_clazz_defined
inline jclass org_webrtc_SdpObserver_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_SdpObserver,
      &g_org_webrtc_SdpObserver_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_SdpObserver_onCreateFailure1(nullptr);
static void Java_SdpObserver_onCreateFailure(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jstring>& error) {
  jclass clazz = org_webrtc_SdpObserver_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_SdpObserver_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onCreateFailure",
          "(Ljava/lang/String;)V",
          &g_org_webrtc_SdpObserver_onCreateFailure1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, error.obj());
}

static std::atomic<jmethodID> g_org_webrtc_SdpObserver_onCreateSuccess1(nullptr);
static void Java_SdpObserver_onCreateSuccess(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& sdp) {
  jclass clazz = org_webrtc_SdpObserver_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_SdpObserver_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onCreateSuccess",
          "(Lorg/webrtc/SessionDescription;)V",
          &g_org_webrtc_SdpObserver_onCreateSuccess1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, sdp.obj());
}

static std::atomic<jmethodID> g_org_webrtc_SdpObserver_onSetFailure1(nullptr);
static void Java_SdpObserver_onSetFailure(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jstring>& error) {
  jclass clazz = org_webrtc_SdpObserver_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_SdpObserver_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onSetFailure",
          "(Ljava/lang/String;)V",
          &g_org_webrtc_SdpObserver_onSetFailure1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, error.obj());
}

static std::atomic<jmethodID> g_org_webrtc_SdpObserver_onSetSuccess0(nullptr);
static void Java_SdpObserver_onSetSuccess(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_SdpObserver_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_SdpObserver_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onSetSuccess",
          "()V",
          &g_org_webrtc_SdpObserver_onSetSuccess0);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_SdpObserver_JNI
