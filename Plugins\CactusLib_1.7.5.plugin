"""
>>==================================================================<<
||    ____           _             ____  _             _            ||
||   / ___|__ _  ___| |_ _   _ ___|  _ \| |_   _  __ _(_)_ __  ___  ||
||  | |   / _` |/ __| __| | | / __| |_) | | | | |/ _` | | '_ \/ __| ||
||  | |__| (_| | (__| |_| |_| \__ \  __/| | |_| | (_| | | | | \__ \ ||
||   \____\__,_|\___|\__|\__,_|___/_|   |_|\__,_|\__, |_|_| |_|___/ ||
||     ____           _ _   _     _              |___/              ||
||    / __ \__      _(_) |_| |__ | | _____   _____                  ||
||   / / _` \ \ /\ / / | __| '_ \| |/ _ \ \ / / _ \                 ||
||  | | (_| |\ V  V /| | |_| | | | | (_) \ V /  __/                 ||
||   \ \__,_| \_/\_/ |_|\__|_| |_|_|\___/ \_/ \___|                 ||
||                                                                  ||
||                   https://t.me/CactusPlugins                     ||
|| https://github.com/Den4ikSuperOstryyPer4ik/Extera-CactusLib-Docs ||
||                                                                  ||
||            ПОЖАЛУЙСТА НЕ КОПИРУЙТЕ КОД НЕ УВЕДОМИВ МЕНЯ.         ||
||          PLEASE DO NOT COPY THIS CODE WITHOUT NOTIFYING ME.      ||
>>==================================================================<<
"""
import ast
import base64
import datetime
import html
import inspect
import json
import os.path
import random
import re
import shlex
import threading
import time
import traceback
from uuid import uuid4
import zlib
from contextlib import contextmanager, suppress
from dataclasses import dataclass
from enum import Enum
from html.parser import HTMLParser
from struct import unpack
from typing import Any, Callable, Dict, List, Optional, Tuple, Union, final, overload
from urllib.parse import parse_qs, urlencode, urlparse

import requests

from android.os import Build  # type: ignore
from org.telegram.messenger.support.fingerprint import FingerprintManagerCompat  # type: ignore
from android.content import DialogInterface  # type: ignore
from android.util import TypedValue  # type: ignore
from android.view import Gravity, View  # type: ignore
from android.widget import LinearLayout  # type: ignore
from android.widget import FrameLayout, TextView  # type: ignore
from android_utils import OnClickListener
from android_utils import log as logcat
from android_utils import run_on_ui_thread
from java.lang.reflect import Field, Method, Modifier  # type: ignore
from base_plugin import (BasePlugin, HookResult, HookStrategy, MenuItemData,
                         MenuItemType, MethodHook, AppEvent)
from client_utils import (get_account_instance,
                          get_last_fragment,
                          get_media_data_controller, get_messages_controller,
                          get_send_messages_helper,
                          run_on_queue, send_message, send_request, get_user_config, get_file_loader)
from com.exteragram.messenger.plugins import PluginsController, PluginsConstants  # type: ignore
from com.exteragram.messenger.plugins.ui import PluginSettingsActivity, PluginsActivity  # type: ignore
from com.exteragram.messenger.utils import ChatUtils  # type: ignore
from com.exteragram.messenger.plugins.hooks import PluginsHooks  # type: ignore
from java import dynamic_proxy, jarray, jfloat, jint, jlong, jclass  # type: ignore
from java.io import (BufferedReader, File, InputStreamReader,  # type: ignore
                     IOException)  # type: ignore
from java.lang import (Double, Integer, InterruptedException, Runtime,  # type: ignore
                       String, Boolean, Math)  # type: ignore
from java.lang import System as JavaSystem  # type: ignore
from java.util import ArrayList, Locale, Comparator  # type: ignore
from java.util.concurrent import CopyOnWriteArrayList  # type: ignore
from java.util.function import ToIntFunction, BiFunction  # type: ignore
from com.google.android.exoplayer2.text.span import SpanUtil  # type: ignore
from org.telegram.messenger import (AndroidUtilities, R, FileLoader, # type: ignore
                                    ApplicationLoader, BotInlineKeyboard,  # type: ignore
                                    LocaleController, MessageObject,  # type: ignore
                                    SendMessagesHelper, Utilities,  # type: ignore
                                    SharedConfig, FingerprintController,  # type: ignore
                                    MessagesController, UserConfig, NotificationCenter)  # type: ignore
from org.telegram.tgnet import TLRPC, TLObject, RequestDelegate  # type: ignore
from org.telegram.ui import ChatActivity  # type: ignore
from org.telegram.ui.ActionBar import Theme, AlertDialog, BottomSheet  # type: ignore
from org.telegram.ui.Cells import CheckBoxCell, ChatMessageCell  # type: ignore
from org.telegram.ui.Components import (BackupImageView, AnimatedEmojiDrawable,  # type: ignore
                                        CheckBox2, LayoutHelper, ColoredImageSpan,  # type: ignore
                                        LineProgressView, AnimatedEmojiSpan, Text,  # type: ignore
                                        EditTextBoldCursor, EmojiPacksAlert, URLSpanReplacement)  # type: ignore
from androidx.biometric import BiometricPrompt, BiometricManager  # type: ignore
from org.telegram.ui.bots import BotBiometry  # type: ignore
from packaging.version import Version
from ui.alert import AlertDialogBuilder
from ui.bulletin import BulletinHelper
from ui.settings import Divider, Header, Input, Switch, Selector
from android.text import SpannableStringBuilder, Spanned, InputType, TextWatcher  # type: ignore

from hook_utils import find_class, set_private_field

__name__ = "CactusLib"
__description__ = "Cactus's library with full-fledged help, limitless features, plugins import/export and more than!\nhttps://cactus.den4iksop.org"
__icon__ = "CactusPlugins/0"
__id__ = "cactuslib"
__version__ = "1.7.5"
__author__ = "@CactusPlugins"
__min_version__ = "11.13.0"

LVLS = ["DEBUG", "INFO", "WARN", "ERROR"]


##############################   Markdown & HTML parsers   ##############################
# Original parts (modified) of code from https://github.com/KurimuzonAkuma/pyrogram/blob/31fa1e48b6258f246289c5561a391eba584d546d/pyrogram/parser/html.py
# Original parts (modified) of code from https://github.com/KurimuzonAkuma/pyrogram/blob/31fa1e48b6258f246289c5561a391eba584d546d/pyrogram/parser/markdown.py

def add_surrogates(text: str) -> str:
    return re.compile(r"[\U00010000-\U0010FFFF]").sub(
        lambda match:
        "".join(chr(i) for i in unpack("<HH", match.group().encode("utf-16le"))),
        text
    )


NOT_PREMIUM = 0
TELEGRAM_PREMIUM = 1
CACTUS_GLOBAL_PREMIUM = 2


def remove_surrogates(text: str) -> str:
    return text.encode("utf-16", "surrogatepass").decode("utf-16")


class TLEntityType(Enum):
    CODE = 'code'
    PRE = 'pre'
    STRIKETHROUGH = 'strikethrough'
    TEXT_LINK = 'text_link'
    BOLD = 'bold'
    ITALIC = 'italic'
    UNDERLINE = 'underline'
    SPOILER = 'spoiler'
    CUSTOM_EMOJI = 'custom_emoji'
    BLOCKQUOTE = 'blockquote'

    @classmethod
    def from_(cls, entity):
        return (
            cls.CODE
            if isinstance(entity, TLRPC.TL_messageEntityCode)
            else cls.PRE
            if isinstance(entity, TLRPC.TL_messageEntityPre)
            else cls.STRIKETHROUGH
            if isinstance(entity, TLRPC.TL_messageEntityStrike)
            else cls.TEXT_LINK
            if isinstance(entity, TLRPC.TL_messageEntityTextUrl)
            else cls.BOLD
            if isinstance(entity, TLRPC.TL_messageEntityBold)
            else cls.ITALIC
            if isinstance(entity, TLRPC.TL_messageEntityItalic)
            else cls.UNDERLINE
            if isinstance(entity, TLRPC.TL_messageEntityUnderline)
            else cls.SPOILER
            if isinstance(entity, TLRPC.TL_messageEntitySpoiler)
            else cls.CUSTOM_EMOJI
            if isinstance(entity, TLRPC.TL_messageEntityCustomEmoji)
            else cls.BLOCKQUOTE
            if isinstance(entity, TLRPC.TL_messageEntityBlockquote)
            else None
        )


@dataclass
class RawEntity:
    TLRPC_ENTITIES_MAP = {
        TLEntityType.CODE: TLRPC.TL_messageEntityCode,
        TLEntityType.PRE: TLRPC.TL_messageEntityPre,
        TLEntityType.STRIKETHROUGH: TLRPC.TL_messageEntityStrike,
        TLEntityType.TEXT_LINK: TLRPC.TL_messageEntityTextUrl,
        TLEntityType.BOLD: TLRPC.TL_messageEntityBold,
        TLEntityType.ITALIC: TLRPC.TL_messageEntityItalic,
        TLEntityType.UNDERLINE: TLRPC.TL_messageEntityUnderline,
        TLEntityType.SPOILER: TLRPC.TL_messageEntitySpoiler,
        TLEntityType.CUSTOM_EMOJI: TLRPC.TL_messageEntityCustomEmoji,
        TLEntityType.BLOCKQUOTE: TLRPC.TL_messageEntityBlockquote
    }

    type: TLEntityType
    offset: int
    length: int
    language: Optional[str] = None
    url: Optional[str] = None
    document_id: Optional[int] = None
    collapsed: Optional[bool] = None

    def to_tlrpc_object(self):
        entity = self.TLRPC_ENTITIES_MAP[self.type]()
        entity.offset = self.offset
        entity.length = self.length

        if self.language is not None:
            entity.language = self.language
        if self.url is not None:
            entity.url = self.url
        if self.document_id is not None:
            entity.document_id = self.document_id
        if self.collapsed is not None:
            entity.collapsed = self.collapsed

        return entity


class Parser(HTMLParser):
    def __init__(self):
        super().__init__()
        self.text = ""
        self.entities = []
        self.tag_entities = {}

    def handle_starttag(self, tag, attrs):
        attrs = dict(attrs)
        extra = {}

        if tag in ["b", "strong"]:
            _type = TLEntityType.BOLD
        elif tag in ["i", "em"]:
            _type = TLEntityType.ITALIC
        elif tag == "u":
            _type = TLEntityType.UNDERLINE
        elif tag in ["s", "del", "strike"]:
            _type = TLEntityType.STRIKETHROUGH
        elif tag == "blockquote":
            _type = TLEntityType.BLOCKQUOTE
            extra["collapsed"] = "expandable" in attrs or "collapsed" in attrs
        elif tag == "code":
            _type = TLEntityType.CODE
        elif tag == "pre":
            _type = TLEntityType.PRE
            extra["language"] = attrs.get("language", attrs.get("lang", ""))
        elif tag in ["spoiler", "tg-spoiler"]:
            _type = TLEntityType.SPOILER
        elif tag == "a":
            url = attrs.get("href", "")
            _type = TLEntityType.TEXT_LINK
            extra["url"] = url
        elif tag == "emoji":
            _type = TLEntityType.CUSTOM_EMOJI
            custom_emoji_id = int(attrs.get("id", attrs.get("document_id", "0")))
            if not custom_emoji_id:
                return
            extra["document_id"] = custom_emoji_id
        else:
            return

        if tag not in self.tag_entities:
            self.tag_entities[tag] = []

        self.tag_entities[tag].append(
            RawEntity(
                type=_type,
                offset=len(self.text),
                length=0,
                **extra
            ))

    def handle_data(self, data):
        data = html.unescape(data)

        for entities in self.tag_entities.values():
            for entity in entities:
                entity.length += len(data)

        self.text += data

    def handle_endtag(self, tag):
        try:
            self.entities.append(self.tag_entities[tag].pop())
        except (KeyError, IndexError):
            pass
        else:
            if not self.tag_entities[tag]:
                self.tag_entities.pop(tag)


class HTML:
    @staticmethod
    def parse(text: str, custom_emoji: int) -> dict:
        text = re.sub(r"^\s*(<[\w<>=\s\"]*>)\s*", r"\1", text)
        text = re.sub(r"\s*(</[\w</>]*>)\s*$", r"\1", text)

        EMOJI = r"<emoji\s+(?:document_id=|id=)(\d+)>([^<]+)</emoji>"
        if custom_emoji == NOT_PREMIUM:
            text = re.sub(EMOJI, lambda m: m.group(2), text)
        elif custom_emoji == CACTUS_GLOBAL_PREMIUM:
            text = re.sub(EMOJI, lambda m: f"<a href='tg://cactus/emoji/{m.group(1)}'>{m.group(2)}</a>", text)

        parser = Parser()
        parser.feed(add_surrogates(text))
        parser.close()

        if parser.tag_entities:
            unclosed_tags = []

            for tag, entities in parser.tag_entities.items():
                unclosed_tags.append(f"<{tag}> (x{len(entities)})")

        entities = []

        for entity in parser.entities:
            if entity.length == 0:
                continue

            entities.append(entity)

        entities = list(filter(lambda x: x.length > 0, entities))

        return {
            "message": remove_surrogates(parser.text),
            "entities": sorted(entities, key=lambda e: e.offset)
        }

    @staticmethod
    def unparse(text: str, entities: list) -> str:
        def parse_one(e):
            """
            Parses a single entity and returns (start_tag, start), (end_tag, end)
            """
            entity_type = TLEntityType.from_(e)
            start = e.offset
            end = start + e.length

            if entity_type in (
                    TLEntityType.BOLD,
                    TLEntityType.ITALIC,
                    TLEntityType.UNDERLINE,
                    TLEntityType.STRIKETHROUGH,
            ):
                name = entity_type.name[0].lower()
                start_tag = f"<{name}>"
                end_tag = f"</{name}>"
            elif entity_type == TLEntityType.PRE:
                name = entity_type.name.lower()
                language = getattr(e, "language", "") or ""
                start_tag = f'<{name} language="{language}">' if language else f"<{name}>"
                end_tag = f"</{name}>"
            elif entity_type == TLEntityType.BLOCKQUOTE:
                name = entity_type.name.lower()
                expandable = getattr(e, "expandable", False)
                start_tag = f'<{name}{" expandable" if expandable else ""}>'
                end_tag = f"</{name}>"
            elif entity_type in (
                    TLEntityType.CODE,
                    TLEntityType.SPOILER,
            ):
                name = entity_type.name.lower()
                start_tag = f"<{name}>"
                end_tag = f"</{name}>"
            elif entity_type == TLEntityType.TEXT_LINK:
                url = e.url
                start_tag = f'<a href="{url}">'
                end_tag = "</a>"
            elif entity_type == TLEntityType.CUSTOM_EMOJI:
                custom_emoji_id = e.document_id
                start_tag = f'<emoji id={custom_emoji_id}>'
                end_tag = "</emoji>"
            else:
                return

            return (start_tag, start), (end_tag, end)

        def recursive(entity_i: int) -> int:
            """
            Takes the index of the entity to start parsing from, returns the number of parsed entities inside it.
            Uses entities_offsets as a stack, pushing (start_tag, start) first, then parsing nested entities,
            and finally pushing (end_tag, end) to the stack.
            No need to sort at the end.
            """
            this = parse_one(entities[entity_i])
            if this is None:
                return 1
            (start_tag, start), (end_tag, end) = this
            entities_offsets.append((start_tag, start))
            internal_i = entity_i + 1
            # while the next entity is inside the current one, keep parsing
            while internal_i < len(entities) and entities[internal_i].offset < end:
                internal_i += recursive(internal_i)
            entities_offsets.append((end_tag, end))
            return internal_i - entity_i

        text = add_surrogates(text)

        entities_offsets = []

        # probably useless because entities are already sorted by telegram
        entities.sort(key=lambda e: (e.offset, -e.length))

        # main loop for first-level entities
        i = 0
        while i < len(entities):
            i += recursive(i)

        if entities_offsets:
            last_offset = entities_offsets[-1][1]
            # no need to sort, but still add entities starting from the end
            for entity, offset in reversed(entities_offsets):
                text = text[:offset] + entity + html.escape(text[offset:last_offset]) + text[last_offset:]
                last_offset = offset

        return remove_surrogates(text)


BOLD_DELIM = "*"
ITALIC_DELIM = "_"
UNDERLINE_DELIM = "__"
STRIKE_DELIM = "~"
SPOILER_DELIM = "||"
CODE_DELIM = "`"
PRE_DELIM = "```"
BLOCKQUOTE_DELIM = ">"
BLOCKQUOTE_EXPANDABLE_DELIM = "**>"
BLOCKQUOTE_EXPANDABLE_END_DELIM = "||"

MARKDOWN_RE = re.compile(r"({d})|(!?)\[(.+?)]\((.+?)\)".format(
    d="|".join(
        ["".join(i) for i in [
            [rf"\{j}" for j in i]
            for i in [
                PRE_DELIM,
                CODE_DELIM,
                STRIKE_DELIM,
                UNDERLINE_DELIM,
                ITALIC_DELIM,
                BOLD_DELIM,
                SPOILER_DELIM
            ]
        ]]
    )))

OPENING_TAG = "<{}>"
CLOSING_TAG = "</{}>"
URL_MARKUP = '<a href="{}">{}</a>'
EMOJI_MARKUP = "<emoji id={}>{}</emoji>"
FIXED_WIDTH_DELIMS = [CODE_DELIM, PRE_DELIM]


def replace_once(source: str, old: str, new: str, start: int):
    return source[:start] + source[start:].replace(old, new, 1)


class Markdown:
    @staticmethod
    def escape_and_create_quotes(text: str, strict: bool):
        text_lines: List[Union[str, None]] = text.splitlines()

        # Indexes of Already escaped lines
        html_escaped_list: List[int] = []

        # Temporary Queue to hold lines to be quoted
        to_quote_list: List[Tuple[int, str]] = []

        def create_blockquote(expandable: bool = False) -> None:
            """
            Merges all lines in quote_queue into first line of queue
            Encloses that line in html quote
            Replaces rest of the lines with None placeholders to preserve indexes
            """
            if len(to_quote_list) == 0:
                return

            joined_lines = "\n".join([i[1] for i in to_quote_list])

            first_line_index, _ = to_quote_list[0]
            text_lines[first_line_index] = (
                f"<blockquote{' expandable' if expandable else ''}>{joined_lines}</blockquote>"
            )

            for line_to_remove in to_quote_list[1:]:
                text_lines[line_to_remove[0]] = None

            to_quote_list.clear()

        # Handle Expandable Quote
        inside_blockquote = False
        for index, line in enumerate(text_lines):
            if line.startswith(BLOCKQUOTE_EXPANDABLE_DELIM) and not inside_blockquote:
                delim_stripped_line = line[len(BLOCKQUOTE_EXPANDABLE_DELIM) + (
                    1 if line.startswith(f"{BLOCKQUOTE_EXPANDABLE_DELIM} ") else 0):]
                parsed_line = (
                    html.escape(delim_stripped_line) if strict else delim_stripped_line
                )

                to_quote_list.append((index, parsed_line))
                html_escaped_list.append(index)

                inside_blockquote = True
                continue

            elif line.endswith(BLOCKQUOTE_EXPANDABLE_END_DELIM) and inside_blockquote:
                if line.startswith(BLOCKQUOTE_DELIM):
                    line = line[len(BLOCKQUOTE_DELIM) + (1 if line.startswith(f"{BLOCKQUOTE_DELIM} ") else 0):]

                delim_stripped_line = line[:-len(BLOCKQUOTE_EXPANDABLE_END_DELIM)]

                parsed_line = (
                    html.escape(delim_stripped_line) if strict else delim_stripped_line
                )

                to_quote_list.append((index, parsed_line))
                html_escaped_list.append(index)

                inside_blockquote = False

                create_blockquote(expandable=True)

            if inside_blockquote:
                parsed_line = line[len(BLOCKQUOTE_DELIM) + (1 if line.startswith(f"{BLOCKQUOTE_DELIM} ") else 0):]
                parsed_line = html.escape(parsed_line) if strict else parsed_line
                to_quote_list.append((index, parsed_line))
                html_escaped_list.append(index)

        # Handle Single line/Continued Quote
        for index, line in enumerate(text_lines):
            if line is None:
                continue

            if line.startswith(BLOCKQUOTE_DELIM):
                delim_stripped_line = line[
                                      len(BLOCKQUOTE_DELIM) + (1 if line.startswith(f"{BLOCKQUOTE_DELIM} ") else 0):]
                parsed_line = (
                    html.escape(delim_stripped_line) if strict else delim_stripped_line
                )

                to_quote_list.append((index, parsed_line))
                html_escaped_list.append(index)

            elif len(to_quote_list) > 0:
                create_blockquote()
        else:
            create_blockquote()

        if strict:
            for idx, line in enumerate(text_lines):
                if idx not in html_escaped_list:
                    text_lines[idx] = html.escape(line)

        return "\n".join(
            [valid_line for valid_line in text_lines if valid_line is not None]
        )

    def parse(self, text: str, strict: bool = False, custom_emoji: int = TELEGRAM_PREMIUM):
        text = self.escape_and_create_quotes(text, strict=strict)
        delims = set()
        is_fixed_width = False

        for i, match in enumerate(re.finditer(MARKDOWN_RE, text)):
            start, _ = match.span()
            delim, is_emoji, text_url, url = match.groups()
            full = match.group(0)

            if delim in FIXED_WIDTH_DELIMS:
                is_fixed_width = not is_fixed_width

            if is_fixed_width and delim not in FIXED_WIDTH_DELIMS:
                continue

            if not is_emoji and text_url:
                text = replace_once(text, full, URL_MARKUP.format(url, text_url), start)
                continue

            if is_emoji:
                emoji = text_url
                emoji_id = url.lstrip("tg://emoji?id=")
                text = replace_once(text, full, EMOJI_MARKUP.format(emoji_id, emoji), start)
                continue

            if delim == BOLD_DELIM:
                tag = "b"
            elif delim == ITALIC_DELIM:
                tag = "i"
            elif delim == UNDERLINE_DELIM:
                tag = "u"
            elif delim == STRIKE_DELIM:
                tag = "s"
            elif delim == CODE_DELIM:
                tag = "code"
            elif delim == PRE_DELIM:
                tag = "pre"
            elif delim == SPOILER_DELIM:
                tag = "spoiler"
            else:
                continue

            if delim not in delims:
                delims.add(delim)
                tag = OPENING_TAG.format(tag)
            else:
                delims.remove(delim)
                tag = CLOSING_TAG.format(tag)

            if delim == PRE_DELIM and delim in delims:
                delim_and_language = text[text.find(PRE_DELIM):].split("\n")[0]
                language = delim_and_language[len(PRE_DELIM):]
                text = replace_once(text, delim_and_language, f'<pre language="{language}">', start)
                continue

            text = replace_once(text, delim, tag, start)

        return HTML.parse(text, custom_emoji)

    @staticmethod
    def unparse(text: str, entities: list):
        text = add_surrogates(text)
        entities_offsets = []

        for entity in entities:
            entity_type = TLEntityType.from_(entity)
            start = entity.offset
            end = start + entity.length

            if entity_type == TLEntityType.BOLD:
                start_tag = end_tag = BOLD_DELIM
            elif entity_type == TLEntityType.ITALIC:
                start_tag = end_tag = ITALIC_DELIM
            elif entity_type == TLEntityType.UNDERLINE:
                start_tag = end_tag = UNDERLINE_DELIM
            elif entity_type == TLEntityType.STRIKETHROUGH:
                start_tag = end_tag = STRIKE_DELIM
            elif entity_type == TLEntityType.CODE:
                start_tag = end_tag = CODE_DELIM
            elif entity_type == TLEntityType.PRE:
                language = getattr(entity, "language", "") or ""
                start_tag = f"{PRE_DELIM}{language}\n"
                end_tag = f"\n{PRE_DELIM}"
            elif entity_type == TLEntityType.BLOCKQUOTE:
                start_tag = BLOCKQUOTE_DELIM + " "
                end_tag = ""
                blockquote_text = text[start:end]
                lines = blockquote_text.split("\n")
                last_length = 0
                for line in lines:
                    if len(line) == 0 and last_length == end:
                        continue
                    start_offset = start + last_length
                    last_length = last_length + len(line)
                    end_offset = start_offset + last_length
                    entities_offsets.append((start_tag, start_offset,))
                    entities_offsets.append((end_tag, end_offset,))
                    last_length = last_length + 1
                continue
            elif entity_type == TLEntityType.SPOILER:
                start_tag = end_tag = SPOILER_DELIM
            elif entity_type == TLEntityType.TEXT_LINK:
                url = entity.url
                start_tag = "["
                end_tag = f"]({url})"
            elif entity_type == TLEntityType.CUSTOM_EMOJI:
                emoji_id = entity.document_id
                start_tag = "!["
                end_tag = f"](tg://emoji?id={emoji_id})"
            else:
                continue

            entities_offsets.append((start_tag, start,))
            entities_offsets.append((end_tag, end,))

        entities_offsets = map(
            lambda x: x[1],
            sorted(
                enumerate(entities_offsets),
                key=lambda x: (x[1][1], x[0]),
                reverse=True
            )
        )

        for entity, offset in entities_offsets:
            text = text[:offset] + entity + text[offset:]

        return remove_surrogates(text)


##############################  END Markdown & HTML parsers   ##############################


def link(text, url):
    return URL_MARKUP.format(url, text)


JavaClass = type(jclass("java.lang.Object"))
JavaObject = jclass("java.lang.Object")


def get_private_field(obj: JavaObject, field_name: str) -> Optional[Any]:
    # From sdk/hook_utils.py
    if not isinstance(obj, JavaObject) or not isinstance(field_name, str):
        return None

    try:
        clazz = obj.getClass()
        field = None
        current_class = clazz
        while current_class is not None:
            try:
                field = current_class.getDeclaredField(field_name)
                break
            except Exception:
                current_class = current_class.getSuperclass()

        if field is None:
            return None

        field.setAccessible(True)
        value = field.get(obj)
        return value
    except Exception as e:
        CactusUtils.error(f"Error accessing field '{field_name}' on {obj}: {e}")
        return None


class Callback(dynamic_proxy(Utilities.Callback)):
    def __init__(self, fn: callable, *args, **kwargs):
        super().__init__()
        self._fn = fn
        self._args = args
        self._kwargs = kwargs

    def run(self, arg):
        try:
            self._fn(arg, *self._args, **self._kwargs)
        except:
            CactusUtils.error(traceback.format_exc())


class PluginInfo:
    is_cactuslib_plugin: bool

    def __init__(self, lib, plugin: BasePlugin, is_cactuslib_plugin: bool = False, is_zwylib_plugin: bool = False):
        self.lib = lib
        self.plugin = plugin
        self.is_cactuslib_plugin = is_cactuslib_plugin
        self.is_zwylib_plugin = is_zwylib_plugin

    @property
    def emoji(self):
        return self.lib.emoji(
            "cactus"
            if self.is_cactuslib_plugin
            else "zwylib"
            if self.is_zwylib_plugin
            else "base"
        )

    def format_in_list(self, offset: int) -> str:
        P = self.plugin
        L = self.lib
        commands = " • ".join([
            f"<code>{cmd}</code>" + (
                (f" (<code>" + '</code> - <code>'.join(aliases) + "</code>)")
                if (aliases := [a for a, c in L._aliases.items() if c == cmd])
                else ""
            )
            for cmd in L._commands2.get(P.id, [])
            if (
                    (X := L._commands.get(cmd, None))
                    and (
                            L.get("disabled", {}).get("commands", {}).get(cmd, False) is False
                    ) or (
                            getattr(X[1], "__enabled__", None) is None
                    ) or (
                            isinstance(X[1].__enabled__, bool)
                            and X[1].__enabled__
                    ) or (
                            isinstance(X[1].__enabled__, str)
                            and P.get_setting(X[1].__enabled__, True)
                    )
            )
        ]) if self.is_cactuslib_plugin else ""

        plugin_format = "{emoji} <a href='{toggle_uri}'>({toggle})</a> [{version}] <b>{name}</b> (<code>{id}</code>){commands}"
        return plugin_format.format(
            emoji=self.emoji if P.enabled else L.emoji("disabled_other"),
            toggle_uri=CactusUtils.MessageUri.create(L, "setPluginEnabled", id=P.id, isAllList="true", offset=offset),
            toggle=L.string("enable" if not P.enabled else "disable"),
            id=P.id,
            name=("<a href='" + CactusUtils.MessageUri.create(L, "openPluginHelp", id=P.id) + f"'>{P.name}</a>"),
            version=P.version or '-',
            commands=(" | " + commands) if commands else "",
        )

    def export_settings(self):
        # PLEASE DON'T USE THIS IN ANY PLUGIN
        all_settings = get_private_field(PluginsController.getInstance(), "preferences").getAll()
        keys = [
            m.group(1) for skey in list(all_settings.keySet().toArray())
            if (m := re.match(re.compile(f"plugin_setting_{self.plugin.id}_(.*)"), skey))
        ]

        return {
            key: all_settings.get(f"plugin_setting_{self.plugin.id}_{key}")
            for key in keys
        }
        # PLEASE DON'T USE THIS IN ANY PLUGIN

    def get_file_path(self):
        return PluginsController.getInstance().getPluginPath(self.plugin.id)

    def export(self, with_data: bool = True):
        with open(self.get_file_path(), "rb") as f:
            file_content = f.read()

        return {
            "file_content": CactusUtils.compress_and_encode(file_content),
            "settings": self.export_settings() if with_data else None,
            "data": self.export_data() if with_data else None,
            "plugin_meta": {
                "id": self.plugin.id,
                "name": self.plugin.name,
                "version": self.plugin.version,
                "enabled": self.plugin.enabled,
            }
        }

    def export_data(self):
        if not self.is_cactuslib_plugin:
            return None

        return getattr(self.plugin, "_export_data", lambda: None)()

    def gen_zwycmd_doc(self, cmd):
        REQUIRED = "<{}>"
        OPTIONAL = "[{}]"
        return " ".join([
            (REQUIRED if not arg.is_optional else OPTIONAL).format(arg.name)
            for arg in cmd.args[2:]
        ]) or " "

    def draw_subcmds(
            self, precmd,
            subcmds: Dict[str, Any],
            emoji, prefix: str,
            indents: int = 1,
            cmd_disabled=None,
            root_disabled: bool = False,
            is_edit_mode: bool = False,
            plugin=None,
    ):
        pre = "  " + ("|     " * (indents - 1))
        INDENT = pre + "<emoji id=5199844298845094933>▶</emoji>"
        self.lib.info(f"{precmd} | {emoji} | {prefix} | {indents} | {INDENT} | {plugin}")
        string = ""
        for name, cmd in subcmds.items():
            string += INDENT + self.lib.string(
                "plugin_command_format",
                emoji=emoji if not root_disabled and not cmd_disabled(name, zwy=cmd.func) else self.lib.emoji("disabled_other"),
                prefix=prefix,
                command=precmd + " " + name,
                doc=(
                        CactusUtils.escape_html(cmd.func.__doc__ or self.gen_zwycmd_doc(cmd)).splitlines()[0]
                        if not is_edit_mode else (
                            " " + self.lib.emoji("edit") + link(
                                self.lib.string("edit"),
                                CactusUtils.MessageUri.create(self.lib, "editPluginCommand", cur=precmd + " " + name, name=plugin.name, id=plugin.id, zwy="1")
                            ) + (
                                (
                                    " • " + self.lib.emoji("disable" if not cmd_disabled(precmd + " " + name, zwy=cmd.func) else "check") + link(
                                        self.lib.string("disable2" if not cmd_disabled(precmd + " " + name, zwy=cmd.func) else "enable2"),
                                        CactusUtils.MessageUri.create(
                                            self.lib, "setPluginCommandEnabled",
                                            id=plugin.id, c=precmd + " " + name, name=plugin.name, zwy="1"
                                        )
                                    )
                                ) if not (root_disabled or cmd_disabled(precmd + " " + name, True, zwy=cmd.func)) else ""
                            )
                        )
                    ),
            ) + "\n"
            string += self.draw_subcmds(
                precmd + " " + name, cmd.subcommands,
                emoji, prefix, indents + 1, cmd_disabled,
                root_disabled or cmd_disabled(name, zwy=cmd.func),
                is_edit_mode=is_edit_mode,
                plugin=plugin
            )

        return string

    def info(self, rnd, prefix, edit_mode: bool = False, zwycmds: Optional[Dict[str, Any]] = None):
        P: Union[CactusUtils.Plugin, BasePlugin] = self.plugin
        L: CactusLib = self.lib

        emoji = random.choice(L.emoji("item")).format(random.choice(list("💊💉🪚🔩🏷📍💈🧿")))

        menu_items_text = ""
        menu_items = L._menu_items.get(P.id, {})
        has_items = False
        _added = False
        for type, items_ids in menu_items.items():
            if items_ids:
                if not _added:
                    menu_items_text += L.emoji("pin") + L.string("plugin_menu_items") + "\n"
                    _added = True

                menu_items_text += L.string(type) + "\n"
                for item_id in items_ids:
                    item = L._menu_items_by_id.get(item_id, None)
                    if item is not None:
                        # noinspection PyUnresolvedReferences
                        if not has_items:
                            has_items = True
                        menu_items_text += (
                            (emoji if item.enabled else L.emoji("disabled_other"))
                            + item.item.text + (f" ({item.item.subtext})" if item.item.subtext else "")
                        ) + (
                            (
                                " • " + L.emoji("disable" if item.enabled else "check") + link(
                                    L.string("disable2" if item.enabled else "enable2"),
                                    CactusUtils.MessageUri.create(
                                        L, "setPluginMenuItemEnabled",
                                        id=P.id, item_id=item_id, name=P.name
                                    )
                                ) if edit_mode else ""
                            )
                        ) + "\n"

                menu_items_text += "\n"

        is_edit_mode = (self.is_cactuslib_plugin or self.is_zwylib_plugin or has_items) and edit_mode

        text, markup = "", CactusUtils.Inline.Markup().add_row(
            CactusUtils.Inline.Button(
                text=(L.emoji("check" if not P.enabled else "disable") + L.string(
                    "toggle_" + ("d" if P.enabled else "e"))),
                callback_data=CactusUtils.Inline.CallbackData(L.id, "setPluginEnabled", id=P.id, isAllList="false")
            ) if not is_edit_mode else None,
            CactusUtils.Inline.Button(
                text=L.emoji("reset") + L.string("reset_changes_btn"),
                callback_data=CactusUtils.Inline.CallbackData(L.id, "resetPluginChanges", id=P.id, name=P.name, zwy="1" if self.is_zwylib_plugin else "0")
            ) if is_edit_mode else None
        ).add_row(
            CactusUtils.Inline.Button(
                text=L.emoji("settings") + L.string("settings_btn"),
                callback_data=CactusUtils.Inline.CallbackData(L.id, "openPluginSettings", id=P.id)
            ) if not is_edit_mode and (P.enabled and P.create_settings()) else None,
            CactusUtils.Inline.Button(
                text=L.emoji("delete") + L.string("delete_plugin_btn"),
                callback_data=CactusUtils.Inline.CallbackData(L.id, "deletePluginAlert", id=P.id, name=P.name)
            ) if not is_edit_mode else None
        ).add_row(
            CactusUtils.Inline.Button(
                text=(
                        L.emoji("check" if not edit_mode else "disable")
                        + L.string("toggle_" + ("d" if edit_mode else "e"))
                        + L.string("edit_mode")
                ),
                callback_data=CactusUtils.Inline.CallbackData(L.id, "openPluginHelp", id=P.id, edit_mode=str(not edit_mode))
            ) if (self.is_cactuslib_plugin or self.is_zwylib_plugin or has_items) else None
        ).add_row(
            CactusUtils.Inline.Button(
                text=L.emoji("edit") + L.string("edit_dispatcher_prefix"),
                callback_data=CactusUtils.Inline.CallbackData(L.id, "editZwyPrefix", id=P.id, name=P.name, edit_mode=str(edit_mode))
            ) if self.is_zwylib_plugin and zwycmds else None
        ).add_row(
            CactusUtils.Inline.Button(
                text=L.emoji("update") + L.string("check_updates_btn"),
                callback_data=CactusUtils.Inline.CallbackData(L.id, "checkPluginUpdates", id=P.id)
            ) if False and self.is_cactuslib_plugin and not edit_mode and getattr(P, "_update_data", None) else None  # TODO: make all updates logic
        ).add_row(
            CactusUtils.Inline.Button(
                text=L.emoji("file") + L.string("get_file_btn"),
                callback_data=CactusUtils.Inline.CallbackData(L.id, "sendPluginFile", id=P.id, name=P.name,
                                                              version=P.version or '-')
            ) if not is_edit_mode else None
        ).add_row(
            CactusUtils.Inline.Button(
                text=L.emoji("base_title") + L.string("all_plugins_btn"),
                callback_data=CactusUtils.Inline.CallbackData(L.id, "openPluginHelp", id="_")
            ) if not is_edit_mode else None
        )

        cached = {}

        def cmd_disabled(cmd, T=None, zwy=None):
            if (cmd, T) in cached:
                return cached[(cmd, T)]

            if not zwy:
                cached[(cmd, T)] = not (
                    (T or L.get("disabled", {}).get("commands", {}).get(cmd, False) is False) and (
                        (F := L._commands.get(cmd, None))
                        and (
                            (
                                getattr(F[1], "__enabled__", None) is None
                            ) or (
                                isinstance(F[1].__enabled__, bool)
                                and F[1].__enabled__
                            ) or (
                                isinstance(F[1].__enabled__, str)
                                and P.get_setting(F[1].__enabled__, True) is True
                            )
                        )
                    )
                )
            else:
                cached[(cmd, T)] = not (
                    (T or L.get("zwy_disabled", {}).get("commands", {}).get(cmd, False) is False) and (
                        (
                            getattr(zwy, "__enabled__", None) is None
                        ) or (
                            isinstance(zwy.__enabled__, bool)
                            and zwy.__enabled__
                        )
                    )
                )

            return cached[(cmd, T)]

        zwy_cmds = ""
        if self.is_zwylib_plugin and zwycmds:
            import zwylib
            prefix = zwylib.command_manager.get_dispatcher(P.id).prefix
            for name, cmd in zwycmds.items():
                zwy_cmds += L.string(
                    "plugin_command_format",
                    emoji=emoji if not cmd_disabled(name, zwy=cmd.func) else L.emoji("disabled_other"),
                    prefix=prefix,
                    command=name,
                    doc=(
                        CactusUtils.escape_html(cmd.func.__doc__ or self.gen_zwycmd_doc(cmd)).splitlines()[0]
                        if not is_edit_mode else (
                            " " + L.emoji("edit") + link(
                                L.string("edit"),
                                CactusUtils.MessageUri.create(L, "editPluginCommand", cur=name, name=P.name, id=P.id, zwy="1")
                            ) + (
                                (
                                    " • " + L.emoji("disable" if not cmd_disabled(name, zwy=cmd.func) else "check") + link(
                                        L.string("disable2" if not cmd_disabled(name, zwy=cmd.func) else "enable2"),
                                        CactusUtils.MessageUri.create(
                                            L, "setPluginCommandEnabled",
                                            id=P.id, c=name, name=P.name, zwy="1"
                                        )
                                    )
                                ) if not cmd_disabled(name, True, zwy=cmd.func) else ""
                            )
                        )
                    )
                ) + "\n"

                subcmds = cmd.subcommands
                if subcmds:
                    zwy_cmds += self.draw_subcmds(
                        name, subcmds, emoji,
                        prefix, 1, cmd_disabled,
                        cmd_disabled(name, zwy=True),
                        is_edit_mode, P
                    )

            zwy_cmds = "\n".join(zwy_cmds.splitlines()[:-1] + [zwy_cmds.splitlines()[-1].replace("|", " ")]) + "\n\n"

        description = P.lstrings().get("__doc__", P.description) if self.is_cactuslib_plugin else P.description
        text = L.string(
            "plugin_info",
            emoji=self.emoji if P.enabled else L.emoji("disabled_other"),
            id=P.id, rnd=rnd, veremoji=L.emoji("veremoji"),
            name=P.name, version=P.version or '-', author=P.author,
            doc=CactusUtils.escape_html(description),
            commands=zwy_cmds if zwy_cmds else ("\n".join([
                L.string(
                    "plugin_command_format",
                    emoji=emoji if not cmd_disabled(cmd) else L.emoji("disabled_other"),
                    prefix=prefix,
                    command=cmd,
                    doc=(
                        CactusUtils.escape_html(
                            P.string(
                                L._commands[cmd][1].__cdoc__,
                                default=(L._commands[cmd][1].__cdoc__ or "...").strip()
                            ).splitlines()[0]
                        )
                    ) if not edit_mode else (
                        " " + L.emoji("edit") + link(
                            L.string("edit"),
                            CactusUtils.MessageUri.create(L, "editPluginCommand", cur=cmd, name=P.name, id=P.id)
                        ) + (
                            (
                                " • " + L.emoji("disable" if not cmd_disabled(cmd) else "check") + link(
                                    L.string("disable2" if not cmd_disabled(cmd) else "enable2"),
                                    CactusUtils.MessageUri.create(
                                        L, "setPluginCommandEnabled",
                                        id=P.id, c=cmd, name=P.name
                                    )
                                )
                            ) if not cmd_disabled(cmd, True) else ""
                        )
                    )
                ) + (
                    (
                        (" (" + " || ".join([
                            (emoji if not L.get("disabled", {}).get("commands", {}).get(a, False) else L.emoji("disabled_other"))
                            + f"<code>{prefix}{a}</code>"
                            for a in aliases
                        ]) + ")")
                        if not edit_mode else (
                            " (" + " || ".join([
                                L.emoji("edit") + link(
                                    " " + a,
                                    CactusUtils.MessageUri.create(
                                        L, "editPluginCommand",
                                        cur=a, name=P.name, id=P.id, is_alias="true"
                                    )
                                ) + " - " + L.emoji("disable" if not L.get("disabled", {}).get("commands", {}).get(a, False) else "check") + link(
                                    L.string("disable2" if not L.get("disabled", {}).get("commands", {}).get(a, False) else "enable2"),
                                    CactusUtils.MessageUri.create(
                                        L, "setPluginCommandEnabled",
                                        c=a, name=P.name, id=P.id, orig=cmd
                                    )
                                )
                                for a in aliases
                            ]) + ")"
                        )
                    )
                    if (aliases := [a for a, c in L._aliases.items() if c == cmd])
                    else ""
                )
                for cmd in L._commands2.get(P.id, [])
            ]) + "\n\n") if self.is_cactuslib_plugin else "",
        )
        if menu_items_text.replace("\n", "").strip():
            text += menu_items_text

        return text, markup


class CactusUtils:
    _get_setting = None
    _plugins = None
    _on_new_plugin = None
    _on_unload_plugin = None
    _lib = None

    Callback = Callback
    HTML = HTML
    Markdown = Markdown
    get_private_field = get_private_field

    @staticmethod
    def version_bigger_or_equal(a, b):
        return Version(a) >= Version(b)

    @classmethod
    def all_plugins(cls):
        return list(PluginsController.getInstance().pluginInstances.values().toArray())

    @classmethod
    def plugin(cls, plugin_id):
        return PluginsController.getInstance().pluginInstances.get(plugin_id)
    
    @classmethod
    def zwy_commands(cls, only_plugins: bool = False):
        try:
            import zwylib
            if not cls.version_bigger_or_equal(zwylib.__version__, "1.2.2"):
                raise Exception

            if only_plugins:
                return list(zwylib.command_manager.dispatchers.keys())

            plugins = {}
            for plugin_id, dispatcher in zwylib.command_manager.dispatchers.items():
                plugins[plugin_id] = {
                    name: cmd
                    for name, cmd in dispatcher.listeners.items()
                }

            return plugins
        except Exception:
            cls.error(traceback.format_exc())
            return {} if not only_plugins else []

    @classmethod
    def gen(cls, java_class, method_name, return_value: bool = False):
        def _run(instance, *java_args):
            try:
                value = instance._fn(*java_args, *instance._args, **instance._kwargs)
                if return_value:
                    return value
            except Exception:
                CactusUtils.error(traceback.format_exc())

        def __init__(self, fn, *args, **kwargs):
            self._fn = fn
            self._args = args
            self._kwargs = kwargs
            super(type(self), self).__init__()

        NewClass = type('GeneratedProxyClass_' + java_class.__name__, (dynamic_proxy(java_class),), {
            '__init__': __init__,
            method_name: _run,
        })

        return NewClass

    @classmethod
    def gen2(cls, java_class, return_value: bool = False, **methods):
        def _run(method: callable):
            def _fn(instance, *java_args):
                try:
                    value = method(*java_args, *instance._args, **instance._kwargs)
                    if return_value:
                        return value
                except Exception:
                    CactusUtils.error(traceback.format_exc())

            return _fn

        def __init__(self, *args, **kwargs):
            self._args = args
            self._kwargs = kwargs
            super(type(self), self).__init__()

        NewClass = type('GeneratedProxyClass_' + java_class.__name__, (dynamic_proxy(java_class),), {
            '__init__': __init__,
            **{
                method_name: _run(method)
                for method_name, method in methods.items()
            },
        })

        return NewClass

    class Callback2(dynamic_proxy(Utilities.Callback2)):
        def __init__(self, fn: callable, *args, **kwargs):
            super().__init__()
            self._fn = fn
            self._args = args
            self._kwargs = kwargs

        def run(self, *args):
            try:
                self._fn(*args, *self._args, **self._kwargs)
            except:
                CactusUtils.error(traceback.format_exc())

    class Callback5(dynamic_proxy(Utilities.Callback5)):
        def __init__(self, fn: callable, *args, **kwargs):
            super().__init__()
            self._fn = fn
            self._args = args
            self._kwargs = kwargs

        def run(self, *args):
            try:
                self._fn(*args, *self._args, **self._kwargs)
            except:
                CactusUtils.error(traceback.format_exc())

    class JsonDB(dict):
        def __init__(self, loc: str) -> None:
            super().__init__()
            self._loc = loc
            self.update(**self._load())

        def __str__(self) -> str:
            return self.__repr__()

        def __repr__(self) -> str:
            return f"<JsonFileDB: {self._loc}>"

        def _load(self) -> Dict[str, Any]:
            if not File(self._loc).exists():
                return {}

            with open(self._loc, "r", encoding="utf-8") as file:
                return json.load(file)

        def save(self) -> None:
            with open(self._loc, "w", encoding="utf-8") as file:
                json.dump(self, file, ensure_ascii=False)

        def set(self, key: str, value: Any) -> None:
            self[key] = value
            self.save()

        @overload
        def get(self, key: str):
            ...

        # noinspection PyMethodOverriding
        @overload
        def get(self, key: str, default):
            ...

        def get(self, key: str, default=None):
            return super().get(key, default)

        def pop(self, key: str) -> Any:
            x = super().pop(key)
            self.save()
            return x

        def reset(self) -> None:
            self.clear()
            self.save()

        def update_from(self, **kwargs):
            super().update(kwargs)
            self.save()

    @dataclass
    class Command:
        command: str
        args: List[str]
        raw_args: Optional[str]
        text: str
        account: int

        params: Any

        def html(self):
            return HTML().unparse(self.text, list(self.params.entities.toArray())) if self.params.entities else self.text

        def markdown(self):
            return Markdown().unparse(self.text, list(self.params.entities.toArray())) if self.params.entities else self.text

        def answer(self, text, **kwargs):
            if not kwargs.get("replyToTopMsg", None):
                kwargs["replyToTopMsg"] = self.params.replyToTopMsg
            if not kwargs.get("replyToMsg", None):
                kwargs["replyToMsg"] = self.params.replyToMsg
            return CactusUtils.send_message(self.params.peer, text, **kwargs)

    @dataclass
    class UriCallback:
        cell: ChatMessageCell
        message: MessageObject
        method: str
        raw_url: str
        long_press: bool = False

        def edit_message(self, text: str, **kwargs):
            fragment = kwargs.pop("fragment", get_last_fragment())
            CactusUtils.edit_message(self.message, text, fragment=fragment, **kwargs)
            if kwargs.get("markup", None) is None and self.message.messageOwner.reply_markup:
                self.edit_markup()

        edit = edit_message

        def edit_markup(self, markup=None):
            CactusUtils.edit_message_markup(self.cell, markup)

        def delete_message(self):
            dialog_id = self.message.getDialogId()
            chat = get_messages_controller().getChat(-dialog_id)
            if self.message.canDeleteMessage(
                    self.message.getChatMode() == 1,
                    chat
            ):
                topic_id = self.message.getTopicId()
                CactusUtils.Telegram.delete_messages(
                    self.message.getRealId(),
                    dialog_id,
                    topic_id if topic_id != dialog_id and chat else 0
                )

        delete = delete_message

    class ValidationError(Exception):
        pass

    class FileSystem:
        # noinspection PyUnboundLocalVariable
        File = File

        @classmethod
        def basedir(cls, *path: str):
            _dir = ApplicationLoader.getFilesDirFixed()
            if path:
                for p in path:
                    _dir = File(_dir, p)
                    if not _dir.exists():
                        _dir.mkdirs()

            return _dir

        @classmethod
        def cachedir(cls, *path: str):
            _dir = ApplicationLoader.applicationContext.getExternalCacheDir()
            if path:
                for p in path:
                    _dir = File(_dir, p)
                    if not _dir.exists():
                        _dir.mkdirs()

            return _dir

        @classmethod
        def tempdir(cls):
            _dir = File(cls.cachedir(), "cactuslib_temp_files")
            if not _dir.exists():
                _dir.mkdirs()
            return _dir

        @classmethod
        def get_file_content(cls, file_path, mode: str = "rb"):
            with open(file_path, mode) as f:
                return f.read()

        @classmethod
        def get_temp_file_content(
                cls, filename: str, mode: str = "rb", delete_after: int = 0
        ):
            file_path = File(cls.tempdir(), filename).getAbsolutePath()
            content = cls.get_file_content(file_path, mode)
            if delete_after > 0:
                threading.Timer(delete_after, lambda: os.remove(file_path)).start()
            return content

        @classmethod
        def write_file(cls, file_path, content, mode: str = "wb"):
            with open(file_path, mode) as file:
                file.write(content)

            return file_path

        @classmethod
        def write_temp_file(cls, filename: str, content, mode="wb", delete_after: int = 0):
            path = cls.write_file(File(cls.tempdir(), filename).getAbsolutePath(), content, mode)
            if delete_after > 0:
                threading.Timer(delete_after, lambda: os.remove(path)).start()

            return path

        @classmethod
        def delete_file_after(cls, file_path, seconds: int = 0):
            if os.path.exists(file_path):
                if seconds > 0:
                    threading.Timer(seconds, lambda: os.remove(file_path)).start()
                    return

                os.remove(file_path)

    @classmethod
    def compress_and_encode(cls, data: Union[bytes, str], level: int = 7) -> str:
        try:
            if not data:
                return ""

            if isinstance(data, str):
                data = data.encode('utf-8')

            compressed_data = zlib.compress(data, level=level)
            encoded_data = base64.b64encode(compressed_data).decode('utf-8')
            return encoded_data
        except:
            cls.error(f"An unexpected error occurred: {traceback.format_exc()}")
            return ""

    @classmethod
    def decode_and_decompress(cls, encoded_data: Union[bytes, str]):
        try:
            if not encoded_data:
                return b""
            decoded_data = base64.b64decode(encoded_data)
            decompressed_data = zlib.decompress(decoded_data)
            return decompressed_data
        except:
            cls.error(f"An unexpected error occurred: {traceback.format_exc()}")
            return b""

    @staticmethod
    def pluralization_string(number: int, words: List[str]):
        """
        Returns a pluralized string based on the given number.

        Args:
            number (int): The number to determine the plural form.
            words (list[str]): A list of words representing the singular, dual, and plural forms.

        Returns:
            str: The pluralized string based on the given number.

        Examples:
            num = 5
            pluralization_string(num, ["жизнь", "жизни", "жизней"]) # 1 жизнь, 2 жизни, 3 жизни, 4 жизни, 5 жизней
            pluralization_string(num, ["рубль", "рубля", "рублей"]) # 1 рубль, 2 рубля, 3 рубля, 4 рубля, 5 рублей
            pluralization_string(num, ["ручка", "ручки", "ручек"]) # 1 ручка, 2 ручки, 3 ручки, 4 ручки, 5 ручек
            pluralization_string(num, ["апельсин", "апельсина", "апельсинов"]) # 1 апельсин, 2 апельсина, 3 апельсина, 4 апельсина, 5 апельсинов
        """
        if number % 10 == 1 and number % 100 != 11:
            return f"{number} {words[0]}"
        elif 2 <= number % 10 <= 4 and (number % 100 < 10 or number % 100 >= 20):
            return f"{number} {words[1]}"
        else:
            return f"{number} {words[2]}"

    @classmethod
    def initialize_plugin(cls, plugin: "CactusUtils.Plugin"):
        if plugin in (cls._plugins or []):
            return

        cls._plugins = [plugin] if cls._plugins is None else cls._plugins + [plugin]

        if "validation" in plugin.id:
            return

        if getattr(cls, "_on_new_plugin", None) is not None:
            cls._on_new_plugin(plugin)

    @classmethod
    def unload_plugin(cls, plugin: BasePlugin):
        if cls._on_unload_plugin is not None:
            cls._on_unload_plugin(plugin)

        if plugin not in (cls._plugins or []):
            return

        cls._plugins.remove(plugin)

    @staticmethod
    def escape_html(text: str):
        return str(text).replace("&", "&amp;").replace("<", "&lt;").replace(">", "&gt;")

    @staticmethod
    def show_error(message: str):
        BulletinHelper.show_error(message)

    @staticmethod
    def show_info(message: str):
        BulletinHelper.show_info(message)

    @staticmethod
    def copy_to_clipboard(text: str):
        if AndroidUtilities.addToClipboard(text):
            BulletinHelper.show_copied_to_clipboard()

    @classmethod
    def prefix(cls):
        return cls._get_setting("prefix", ".") if cls._get_setting is not None else "."

    @classmethod
    def get_locale(cls):
        return (
                cls._get_setting("language", "")
                or Locale.getDefault().getLanguage()
        ) if cls._get_setting is not None else Locale.getDefault().getLanguage()

    @staticmethod
    def log(message: str, level: str = "INFO", __id__: Optional[str] = __id__):
        """
        Logs a message to logcat with the specified level.

        Args:
            message (str): The message to log.
            level (str): The log level (e.g., "DEBUG", "INFO", "WARN", "ERROR" or custom lvl).
            __id__ (str):
        """

        logcat(f"[{level}] [{__id__}] " + message.replace("\n", "<CNL>"))

    @staticmethod
    def debug(message: str, __id__: Optional[str] = __id__):
        """Logs a debug message to logcat."""
        CactusUtils.log(message, "DEBUG", __id__)

    @staticmethod
    def error(message: str, __id__: Optional[str] = __id__):
        """Logs an error message to logcat."""
        CactusUtils.log(message, "ERROR", __id__)

    @staticmethod
    def info(message: str, __id__: Optional[str] = __id__):
        """Logs an info message to logcat."""
        CactusUtils.log(message, "INFO", __id__)

    @staticmethod
    def warn(message: str, __id__: Optional[str] = __id__):
        """Logs a warning message to logcat."""
        CactusUtils.log(message, "WARN", __id__)

    @classmethod
    def get_cactus_plugins(cls):
        return [
            p
            for p in cls.all_plugins()
            if (
                    p.id in PluginsData.plugins
            )
        ]

    @staticmethod
    def is_cactus_plugin(plugin: BasePlugin):
        return (
                getattr(getattr(getattr(plugin, "utils", None), "__class__", None), "__name__", None) == "CactusUtils"
                or issubclass(plugin.__class__, CactusUtils.Plugin)
        )

    @staticmethod
    def search_commands(plugin) -> Dict[str, callable]:
        return {
            func.__cmd__: func
            for name, func in inspect.getmembers(plugin, predicate=inspect.ismethod)
            if getattr(func, "__is_command__", None) is True
        }

    @staticmethod
    def search_uri_handlers(plugin, is_message: bool = False) -> Dict[str, callable]:
        return {
            func.__uri__: func
            for name, func in inspect.getmembers(plugin, predicate=inspect.ismethod)
            if getattr(func, "__is_uri_handler__" if not is_message else "__is_uri_message_handler__", None) is True
        }

    @staticmethod
    def search_watchers(plugin) -> Dict[callable, Any]:
        return {
            func: func.__filters__
            for name, func in inspect.getmembers(plugin, predicate=inspect.ismethod)
            if getattr(func, "__is_watcher__", None) is True
        }

    @staticmethod
    def search_inline_handlers(plugin) -> Dict[str, Dict[str, callable]]:
        return {
            func.__data__: func
            for name, func in inspect.getmembers(plugin, predicate=inspect.ismethod)
            if getattr(func, "__is_inline_callback__", None) is True
        }

    @staticmethod
    def runtime_exec(cmd: List[str], return_list_lines: bool = False, raise_errors: bool = True) -> Union[List[str], str]:
        result = []
        process = None
        reader = None
        try:
            process = Runtime.getRuntime().exec(cmd)
            reader = BufferedReader(InputStreamReader(process.getInputStream()))

            line = reader.readLine()
            while line is not None:
                result.append(line.strip())
                line = reader.readLine()

            try:
                process.waitFor()
            except InterruptedException as ie:
                CactusUtils.error(f"Runtime exec process ({cmd}) waitFor interrupted: {ie}")
                # noinspection PyUnresolvedReferences
                threading.current_thread().interrupt()
        except IOException as e:
            CactusUtils.error(
                f"[IOException:RuntimeExec({cmd})] reading exec process: {e}\n{traceback.format_exc()}")
            if raise_errors:
                raise e
        except Exception as e:
            CactusUtils.error(f"[RuntimeExec({cmd})] General error fetching logcat: {e}\n{traceback.format_exc()}")
            if raise_errors:
                raise e
        finally:
            if reader:
                try:
                    reader.close()
                except IOException as e:
                    CactusUtils.error(f"[IOException:RuntimeExec({cmd})] Error closing runtime exec reader: {e}")
            if process:
                process.destroy()

        return result if return_list_lines else "\n".join(result)

    @staticmethod
    def get_logs(__id__: Optional[str] = None, times: Optional[int] = None, lvl: Optional[str] = None,
                 as_list: bool = False):
        """
        Returns a list of logs from logcat by __id__ and time (in seconds).

        Args:
            __id__ (Optional[str]): The id of the plugin
            times (Optional[int]): The time in seconds to get logs from
            lvl (Optional[str]): The log level to get logs from
            as_list (bool): Whether to return the logs as a list or a string
        """
        cmd = ["logcat", "-d", "-v", "time"] + (
            [
                "-T",
                String.format(
                    Locale.US,
                    "%.3f",
                    Double((JavaSystem.currentTimeMillis() - (times * 1000)) / 1000.0)
                )
            ] if times else []
        )

        result = [
            re.sub(r" D/\[PyObject]\(.+[0-9]\):", "", line.replace("<CNL>", "\n").replace(" [callAttrThrows]", ""))
            if __id__ or lvl
            else line.replace("<CNL>", "\n")
            for line in CactusUtils.runtime_exec(cmd, return_list_lines=True)
            if (
                       __id__ is None or f"[{__id__}]" in line
               ) and (
                       lvl is None or f"[{lvl}]" in line
               )
        ]

        CactusUtils.debug(f"Got logs with {__id__=}, {times=}s, {lvl=}")

        return result if as_list else "\n".join(result)

    @classmethod
    def parse_message(cls, text: str, parse_mode: str, allow_custom_emoji: bool = False) -> Dict[str, Any]:
        try:
            result = (Markdown() if parse_mode == "MARKDOWN" else HTML()).parse(
                text,
                TELEGRAM_PREMIUM
                if allow_custom_emoji
                else (
                    CACTUS_GLOBAL_PREMIUM
                    if cls._lib.get_setting("global_premium", True)
                    else NOT_PREMIUM
                )
            )

            return {
                "message": result["message"],
                "entities": [i.to_tlrpc_object() for i in result["entities"]]
            }
        except SyntaxError as e:
            cls.error(f"Markdown parsing error: {e}")
            raise e
        except Exception as e:
            cls.error(f"An unexpected error occurred: {e}")
            raise e

    @classmethod
    def array_list(cls, *args):
        _l = ArrayList()

        if len(args) == 1 and isinstance(args[0], list):
            for arg in args[0]:
                _l.add(arg)
        else:
            for arg in args:
                _l.add(arg)

        return _l

    @classmethod
    def open_plugin_settings(cls, plugin_id: str):
        def _open_settings():
            try:
                get_last_fragment().presentFragment(
                    PluginSettingsActivity(PluginsController.getInstance().plugins.get(plugin_id)))
            except:
                cls.error(f"Failed to open plugin settings. {traceback.format_exc()}")

        run_on_ui_thread(_open_settings)

    @dataclass
    class Uri:
        plugin_id: str
        command: str
        kwargs: Dict[str, str]

        @classmethod
        def create(cls, plugin, cmd: str, **kwargs):
            return cls(plugin.id, cmd, kwargs).string()

        def string(self):
            return f"tg://cactus/{self.plugin_id}/{self.command}?{urlencode(self.kwargs)}"

    class MessageUri(Uri):
        def string(self):
            return f"tg://cactusX/{self.plugin_id}/{self.command}?{urlencode(self.kwargs)}"

    @classmethod
    def edit_message(
            cls,
            message_object: MessageObject,
            text: str,
            parse_message: bool = True,
            parse_mode: str = "HTML",
            markup: Optional[Any] = None,
            **kwargs
    ):
        if markup:
            if isinstance(markup, (dict, list)):
                markup = cls.Inline.Markup.from_dict(markup)

            if isinstance(markup, cls.Inline.Markup):
                if markup.is_global:
                    text = text + "\n" + cls._lib.emoji("heart") + link(" <i>CactusLib with love</i>", markup.to_url_with_data())
                else:
                    markup = markup._markup

        if isinstance(markup, TLRPC.TL_replyInlineMarkup):
            _old = cls.Inline.msg_markups.get(message_object.getDialogId(), {})
            _old[message_object.getRealId()] = markup

            cls.Inline.msg_markups[message_object.getDialogId()] = _old
        
        entities = ArrayList()
        if parse_message:
            parsed = cls.parse_message(text, parse_mode, message_object.isSaved or get_user_config().isPremium())
            text = parsed["message"]
            if len(text) > 4096:
                raise ValueError(f"Message is too long! ({len(text)} symbols)")
            
            entities = cls.array_list(parsed["entities"])

        return get_send_messages_helper().editMessage(message_object, text, False, kwargs.get("fragment", None),
                                                      entities, kwargs.get("scheduleDate", 0))

    @classmethod
    def edit_message_markup(cls, cell: ChatMessageCell, markup: Optional[Any] = None):
        msg = cell.getMessageObject()
        if not markup:
            if msg.messageOwner.flags & (1 << 6) != 0:
                msg.messageOwner.flags &= ~(1 << 6)

            msg.messageOwner.reply_markup = None

            if cls.Inline.msg_markups.get(msg.getDialogId(), {}).get(msg.getRealId(), None):
                del cls.Inline.msg_markups[msg.getDialogId()][msg.getRealId()]

            msg.forceUpdate = True
            cell.setMessageObject(msg, cell.getCurrentMessagesGroup(), cell.isPinnedBottom(),
                                  cell.isPinnedTop(), cell.isFirstInChat())
            return

        if isinstance(markup, (dict, list)):
            markup = cls.Inline.Markup.from_dict(markup)

        if isinstance(markup, cls.Inline.Markup):
            markup = markup._markup

        if isinstance(markup, TLRPC.TL_replyInlineMarkup):
            if msg.messageOwner.flags & (1 << 6) == 0:
                msg.messageOwner.flags |= (1 << 6)

            if not cls.Inline.msg_markups.get(msg.getDialogId(), {}):
                cls.Inline.msg_markups[msg.getDialogId()] = {}

            cls.Inline.msg_markups[msg.getDialogId()][msg.getRealId()] = markup

            msg.messageOwner.reply_markup = markup
            msg.forceUpdate = True
            cell.setMessageObject(msg, cell.getCurrentMessagesGroup(), cell.isPinnedBottom(),
                                  cell.isPinnedTop(), cell.isFirstInChat())
            return

    @classmethod
    def send_message(
            cls,
            peer: int,
            text: str, *,
            parse_message: bool = True,
            parse_mode: str = "HTML",
            markup: Optional[Any] = None,
            on_sent: Optional[Callable] = None,
            **kwargs
    ):
        if markup is None and on_sent:
            markup = cls.Inline.Markup(on_sent=on_sent).add_row(cls.Inline.Button("...", data="..."))

        if markup:
            if isinstance(markup, (dict, list)):
                markup = cls.Inline.Markup.from_dict(markup, on_sent=on_sent)

            if isinstance(markup, cls.Inline.Markup):
                on_sent = markup.on_sent
                if markup.is_global:
                    text = text + "\n" + cls._lib.emoji("heart") + link(" <i>CactusLib with love</i>", markup.to_url_with_data())
                else:
                    markup = markup._markup

        user_config = get_user_config()
        X = cls.parse_message(
            text, parse_mode,
            peer == user_config.getClientUserId() or user_config.isPremium()
        ) if parse_message else {
            "message": text,
            "entities": kwargs.pop("entities", [])
        }
        X["message"] = X["message"].strip()
        if len(X["message"]) > 4096:
            raise ValueError(f"Message is too long! ({len(X['message'])} symbols)")

        if isinstance(markup, TLRPC.TL_replyInlineMarkup):
            _old = cls.Inline.need_markups.get(peer, [])
            _old.append({
                "uid": kwargs.pop("uid", None),
                "markup": markup,
                "message": X["message"],
                "entities": X["entities"],
                "parse_mode": parse_mode,
                "after": time.time()-2,
                "on_sent": on_sent
            })
            cls.Inline.need_markups[peer] = _old

        return send_message(
            {
                "peer": peer,
                **X,
                **kwargs
            }
        )

    class CactusModule(BasePlugin):
        """
        Base class for plugins with CactusUtils and other features
        """

        utils: "CactusUtils"
        __min_lib_version__: str = None
        UPDATE_DATA = []
        strings: Dict[str, Dict[str, str]] = {}

        def __init__(self):
            super().__init__()
            self.utils = CactusUtils()

            if getattr(self, "id", None):
                self.__db: CactusUtils.JsonDB = self.utils.JsonDB(
                    File(self.utils.FileSystem.basedir("datastores"), f"{self.id}_db.json").getAbsolutePath())
            else:
                self.__db: CactusUtils.JsonDB = None  # type: ignore

            self._uri_handlers = {}
            self._uri_message_handlers = {}
            self._inline_handlers = {}
            self._update_data = self.__validate_update_info()

        def export_data(self) -> Dict[str, Any]:
            return {}

        def import_data(self, data):
            pass

        @final
        def _export_data(self) -> Dict:
            data = {}
            __db = self.__db or self.utils.JsonDB(
                File(self.utils.FileSystem.basedir("datastores"), f"{self.id}_db.json").getAbsolutePath())
            if __db.keys():
                data["db"] = __db

            if x := self.export_data():
                data["other"] = x

            return data

        @final
        def get(self, key: str, default: Any = None) -> Any:
            return self.__db.get(key, default)

        @final
        def set(self, key: str, value: Any):
            self.__db.set(key, value)

        @final
        def pop(self, key: str):
            if key not in self.__db:
                return None

            return self.__db.pop(key)

        @final
        def clear_db(self):
            self.__db.reset()

        @final
        def _import_data(self, data: Dict[str, Any]):
            if "db" in data:
                if not self.__db:
                    self.__db = self.utils.JsonDB(
                        File(self.utils.FileSystem.basedir("datastores"), f"{self.id}_db.json").getAbsolutePath())

                self.__db.clear()
                self.__db.update_from(**data["db"])

            if "other" in data:
                self.import_data(data["other"])

        def run_on_queue(self, func, *args, delay: int = 0, **kwargs):
            self.utils.run_on_queue(func, *args, plugin_id=self.id, delay=delay, **kwargs)

        def on_plugin_load(self):
            self.utils = CactusUtils()
            self.debug("Initializing plugin...")
            if not self.__db:
                self.__db = self.utils.JsonDB(
                    File(self.utils.FileSystem.basedir("datastores"), f"{self.id}_db.json").getAbsolutePath())

            if self.__min_lib_version__ and tuple(map(int, self.__min_lib_version__.split("."))) > tuple(
                    map(int, __version__.split("."))):
                raise Exception(
                    f"Plugin requires cactuslib version {self.__min_lib_version__} or higher, but {__version__} is installed")

            self._update_data = self.__validate_update_info()
            self.utils.initialize_plugin(self)
            self.debug(f"Update data: {self._update_data}")

            self.debug("Finished zero-level initializing plugin.")

        def on_plugin_unload(self):
            self.utils = CactusUtils()
            self.info("Unloading plugin...")
            self.utils.unload_plugin(self)

        def open_plugin_settings(self):
            self.utils.open_plugin_settings(self.id)

        @final
        def __validate_update_info(self) -> Dict[str, Any]:
            if not self.UPDATE_DATA:
                return {}

            info = self.UPDATE_DATA
            if isinstance(info, list) and len(info) == 1:
                info = info[1]

            data = {}

            if isinstance(info, str):
                if m := re.match(r"https?://github\.com/([^/]+)/([^/]+)/blob/([a-f0-9]+)/(.+)", info):
                    username, repo, commit_hash, file_path = m.groups()
                    RAW_URL = f"https://raw.githubusercontent.com/{username}/{repo}/{commit_hash}/{file_path}"
                elif m := re.match(r"https?://github\.com/([^/]+)/([^/]+)/blob/([a-z0-9]+)/(.+)", info):
                    username, repo, branch, file_path = m.groups()
                    RAW_URL = f"https://raw.githubusercontent.com/{username}/{repo}/refs/heads/{branch}/{file_path}"
                elif info.startswith("https://raw.githubusercontent.com/"):
                    RAW_URL = info
                else:
                    raise CactusUtils.ValidationError(f"Invalid update data! {info}")

                if requests.get(RAW_URL).status_code != 200:
                    raise CactusUtils.ValidationError(f"Github file not found! {info}")

                data = {
                    "github": {
                        "raw_url": RAW_URL
                    }
                }
            elif isinstance(info, list):
                tgdata = {}
                for i in info:
                    if m := re.match(r"(?:@|(?:https?://)?t(?:elegram)?\.me/)(\w{4,})$", str(i)):
                        tgdata["username"] = m.group(1)
                    elif isinstance(i, int):
                        e = CactusUtils.ValidationError(f"Passed value ({i}) is not a valid telegram id")

                        try:
                            value = int(str(i).strip())
                        except Exception:
                            raise e

                        if str(value).startswith("-100"):
                            value = int(str(value)[4:])

                        if value > 2 ** 64 - 1 or value < 0:
                            raise e

                        tgdata["channel_id"] = value
                    elif isinstance(i, str):
                        if data.get("uid", None):
                            raise CactusUtils.ValidationError(f"Invalid data: passed second uid")

                        if not bool(re.compile(r'^[0-9a-f]{32}$', re.IGNORECASE).match(i)):
                            raise CactusUtils.ValidationError(f"Invalid data: passed non-uuid string")

                        tgdata["uid"] = i

                if not tgdata.get("username", None) and not tgdata.get("channel_id", None) and not tgdata.get("uid",
                                                                                                              None):
                    raise CactusUtils.ValidationError(f"Invalid update data!")

                data["telegram"] = tgdata

            return data

        @final
        def lstrings(self) -> Dict[str, str]:
            locale_dict: Dict[str, Any] = self.strings.get(self.utils.get_locale(), self.strings)
            if "en" in locale_dict:
                locale_dict = locale_dict["en"]

            return locale_dict

        @final
        def string(self, key: str, *args, default: Optional[str] = None, locale: str = None, **kwargs) -> str:
            if key is None:
                return default.format(*args, **kwargs)

            base_strings = self.strings.get("en", self.strings)

            locale_dict: Dict[str, Any] = self.strings.get(self.utils.get_locale() if not locale else locale,
                                                           self.strings)
            if "en" in locale_dict:
                locale_dict = locale_dict["en"]

            string = (locale_dict.get(key, base_strings.get(key, default)) or default)
            if args or kwargs:
                string = string.format(*args, **kwargs)

            return string

        @final
        def log(self, message: str, level: str = "INFO"):
            """
            Logs a message to logcat with the specified level.

            Args:
                message (str): The message to log.
                level (str): The log level (e.g., "DEBUG", "INFO", "WARN", "ERROR" or custom lvl).
            """
            CactusUtils.log(message, level, self.id)

        @final
        def debug(self, message: str):
            """Logs a debug message to logcat."""
            CactusUtils.debug(message, self.id)

        @final
        def error(self, message: str):
            """Logs an error message to logcat."""
            CactusUtils.error(message, self.id)

        @final
        def info(self, message: str):
            """Logs an info message to logcat."""
            CactusUtils.info(message, self.id)

        @final
        def warn(self, message: str):
            """Logs a warning message to logcat."""
            CactusUtils.warn(message, self.id)

        @final
        def answer(self, params, text: str, *, parse_message: bool = True, parse_mode: str = "HTML", markup=None,
                   **kwargs):
            """
            TODO: сделать разделение если больше 4096 символов
            """

            return self.utils.send_message(
                params.peer, text,
                parse_message=parse_message,
                parse_mode=parse_mode,
                replyToMsg=getattr(params, "replyToMsg", None),
                replyToTopMsg=getattr(params, "replyToTopMsg", None),
                markup=markup,
                **kwargs
            )

        @final
        def edit_message(self, params, text: str, parse_message: bool = True, parse_mode: str = "HTML", **kwargs):
            if parse_message:
                parsed = self.utils.parse_message(text, parse_mode)
                params.message = parsed["message"]
                params.entities = self.utils.array_list(parsed["entities"])
            else:
                params.message = text

            for k, v in kwargs.items():
                setattr(params, k, v)

            return HookResult(HookStrategy.MODIFY, params=params)

        @final
        def answer_file(self, peer: int, path: str, caption: Optional[str] = None, *, parse_message: bool = True,
                        parse_mode: str = "HTML", **kwargs):
            """
            Sends a file with path and caption

            Additional kwargs:
            - `mime` (`str`): MIME type of the file (default: `text/plain`)
            - `replyToMsg`: Message to reply to
            - `replyToTopMsg`: top message to reply to (thread id)
            - `storyItem` (`TL_stories.StoryItem`): Story item
            - `quote` (`ChatActivity.ReplyQuote`): Quote
            - `notify` (`bool`): Send notification
            - `editingMessageObject` (`MessageObject`): Message to edit/add file
            - `scheduleDate` (`int`): Schedule date
            - `quickReplyShortcut` (`str`): Quick reply shortcut
            - `quickReplyShortcutId` (`int`): Quick reply shortcut ID
            - `effect_id` (`int`): Effect ID
            - `invertMedia` (`bool`): Invert media
            - `payStars` (`int`): Pay stars
            - `monoForumPeerId` (`int`): Mono forum peer ID
            """
            if caption and parse_message:
                parsed = self.utils.parse_message(caption, parse_mode)
                caption = parsed["message"]
                kwargs["entities"] = self.utils.array_list(parsed["entities"])

            [method] = [m for m in SendMessagesHelper.getClass().getDeclaredMethods() if
                        m.getName() == "prepareSendingDocumentInternal"]
            method.setAccessible(True)

            return method.invoke(
                None,
                kwargs.get("accountInstance", None) or get_account_instance(), path, path, None, kwargs.get("mime", "text/plain"),
                peer, kwargs.get("replyToMsg", None), kwargs.get("replyToTopMsg", None),
                kwargs.get("storyItem", None), kwargs.get("quote", None), kwargs.get("entities", None),
                kwargs.get("editingMessageObject", None), kwargs.get("groupId", jarray(jlong)([0])), kwargs.get("isGroupFinal", True), caption, kwargs.get("notify", True),
                jint(kwargs.get("scheduleDate", 0)), kwargs.get("docType", jarray(Integer)([0])), True,
                kwargs.get("quickReplyShortcut", None), jint(kwargs.get("quickReplyShortcutId", 0)),
                kwargs.get("effect_id", 0), kwargs.get("invertMedia", False), kwargs.get("payStars", 0),
                kwargs.get("monoForumPeerId", 0), kwargs.get("suggestionParams", None)
            )

        @final
        def answer_files(self, peer: int, paths: List[str], caption: Optional[str] = None, *, parse_message: bool = True,
                        parse_mode: str = "HTML", **kwargs):
            try:
                media_count = 0
                group_id = jarray(jlong)([0])
                docType = jarray(Integer)([0])

                [finishGroup] = [m for m in SendMessagesHelper.getClass().getDeclaredMethods() if
                            m.getName() == "finishGroup"]
                finishGroup.setAccessible(True)
                send_messages_helper = get_send_messages_helper()
                account_instance = get_account_instance()

                first = True
                for i, path in enumerate(paths):
                    if len(paths) > 1 and media_count % 10 == 0:
                        if group_id[0] != 0:
                            finishGroup.invoke(send_messages_helper, account_instance, group_id, kwargs.get("scheduleDate", 0))

                        group_id[0] = Utilities.random.nextLong()
                        media_count = 0

                    media_count += 1

                    _kwargs = kwargs.copy()
                    _kwargs.update(
                        groupId=group_id,
                        account_instance=account_instance,
                        isGroupFinal=media_count == 10 or i == len(paths) - 1,
                        docType=docType
                    )

                    prev_group_id = group_id[0]
                    self.answer_file(
                        peer, path, caption if first else None,
                        parse_message=parse_message, parse_mode=parse_mode,
                        **_kwargs
                    )
                    first = False
                    if prev_group_id != group_id[0] or group_id[0] == -1:
                        media_count = 1
            except:
                self.error(f"Failed to answer files: {traceback.format_exc()}")

        @final
        def answer_photo(self, params, path: str, caption: Optional[str] = None, *, parse_message: bool = True,
                         parse_mode: str = "HTML", edit_params: bool = False, **kwargs):
            photo = get_send_messages_helper().generatePhotoSizes(path, None)
            if not photo:
                raise Exception("Failed to generate photo sizes")

            entities = ArrayList()
            if caption and parse_message:
                parsed = self.utils.parse_message(caption, parse_mode)
                caption = parsed["message"]
                entities = self.utils.array_list(parsed["entities"])

            if edit_params:
                params.photo = photo
                params.path = path

                params.caption = caption
                params.entities = entities

                for k, v in kwargs.items():
                    setattr(params, k, v)

                return HookResult(strategy=HookStrategy.MODIFY, params=params)
            else:
                send_message(
                    {
                        "peer": params.peer,
                        "replyToMsg": params.replyToMsg,
                        "replyToTopMsg": params.replyToTopMsg,
                        "caption": caption,
                        "entities": entities,
                        "path": path,
                        "photo": photo,
                        **kwargs
                    }
                )

    Plugin = CactusPlugin = CactusModule
    PluginInfo = PluginInfo

    class Telegram:
        class Result:
            def __init__(self):
                self.req_id: int = None  # type: ignore
                self.response: TLObject = None
                self.error: TLRPC.TL_error = None

                self._event = threading.Event()

        class RequestCallback(dynamic_proxy(RequestDelegate)):
            def __init__(self, fn: callable, *args, **kwargs) -> None:
                self._fn = fn
                self._args = args
                self._kwargs = kwargs

            def run(self, response: TLObject, error: TLRPC.TL_error):
                try:
                    self._fn(response, error, *self._args, **self._kwargs)
                except:
                    CactusUtils.error(f"Error in callback <{self._fn}>: " + traceback.format_exc(), "TLRPC")

        class TLRPCException(Exception):
            def __init__(self, req_id: int, error: TLRPC.TL_error):
                super().__init__(f"[{req_id}] TLRPCError {error.code}: {error.text}")
                self.error: TLRPC.TL_error = error
                self.code: int = error.code
                self.text: str = error.text
                self.req_id: int = req_id

        _res: Dict[Union[int, str], Result] = {}

        @staticmethod
        def tlrpc_object(request_class, **kwargs):
            for k, v in kwargs.items():
                setattr(request_class, k, v)

            return request_class

        @classmethod
        def _callback(cls, result: Optional[TLObject], error: Optional[TLRPC.TL_error], uid: str):
            try:
                if uid not in cls._res:
                    CactusUtils.error(f"Callback called for unknown request {uid}", "TLRPC")
                    return

                CactusUtils.debug(f"{cls._res[uid].req_id}: {result=} | {error=}", "TLRPC")
                cls._res[uid].response = result
                cls._res[uid].error = error
                cls._res[uid]._event.set()
            except:
                CactusUtils.error(traceback.format_exc(), "TLRPC")

        @classmethod
        def _callback2(cls, result: Optional[TLObject], error: Optional[TLRPC.TL_error], uid: str, fn: callable):
            try:
                CactusUtils.debug(f"{uid}: {result=} | {error=}", "TLRPC")

                fn(result, error)
            except:
                CactusUtils.error(traceback.format_exc(), "TLRPC")

        @classmethod
        def send(
                cls,
                req,
                callback: Optional[callable] = None, *,
                wait_response: bool = True,
                timeout: int = 10,
                raise_errors: bool = True
        ) -> Union[int, Result]:
            uid = uuid4().hex
            if wait_response:
                cls._res[uid] = cls.Result()

            req_id = send_request(
                req, (
                    cls.RequestCallback(cls._callback, uid)
                    if wait_response
                    else cls.RequestCallback(cls._callback2, uid, callback)
                    if callback is not None
                    else cls.RequestCallback(lambda *_: None)
                )
            )
            CactusUtils.debug(f"Request with id {req_id} | {uid} sent", "TLRPC")

            if not wait_response:
                return req_id

            cls._res[uid].req_id = req_id

            X = cls._res[uid]._event.wait(timeout)

            if not X:
                raise TimeoutError(f"Request with id {req_id} | {uid} timed out")

            CactusUtils.debug(f"Request with id {req_id} | {uid} received", "TLRPC")

            result = cls._res.pop(uid)
            if result.error and raise_errors:
                CactusUtils.debug(f"Request with id {req_id} | {uid} failed [{result.error.code}]: {result.error.text}",
                                  "TLRPC")
                raise cls.TLRPCException(result.req_id, result.error)

            return result

        send_request = send

        class SearchFilter(Enum):
            GIF = 'gif'
            MUSIC = 'music'
            CHAT_PHOTOS = 'chat_photos'
            PHOTOS = 'photos'
            URL = 'url'
            DOCUMENT = 'document'
            PHOTO_VIDEO = 'photo_video'
            PHOTO_VIDEO_DOCUMENT = 'photo_video_document'
            GEO = 'geo'
            PINNED = 'pinned'
            MY_MENTIONS = 'my_mentions'
            ROUND_VOICE = 'round_voice'
            CONTACTS = 'contacts'
            VOICE = 'voice'
            VIDEO = 'video'
            PHONE_CALLS = 'phone_calls'
            ROUND_VIDEO = 'round_video'
            EMPTY = 'empty'

            def to_TLRPC_object(self):
                camel_case = re.sub(r'_([a-z])', lambda match: match.group(1).upper(), self.value)
                camel_case = camel_case[0].upper() + camel_case[1:]
                return getattr(TLRPC, f"TL_inputMessagesFilter" + camel_case)()

        @classmethod
        def search_messages(
                cls,
                dialog_id: int,
                query: Optional[str] = None,
                from_id: Optional[int] = None,
                offset_id: int = 0,
                limit: int = 20,
                reply_message_id: Optional[int] = None,
                top_message_id: Optional[int] = None,
                filter: SearchFilter = SearchFilter.EMPTY,
                **kwargs
        ) -> Union[List[MessageObject], int]:
            req = cls.tlrpc_object(
                TLRPC.TL_messages_search(),
                peer=cls.input_peer(dialog_id),
                q=query,
                offset_id=offset_id,
                limit=limit,
                filter=filter.to_TLRPC_object(),
            )
            if from_id:
                req.from_id = cls.input_peer(from_id)
                req.flags |= 1
            if reply_message_id or top_message_id:
                req.top_msg_id = reply_message_id or top_message_id
                req.flags |= 2

            current_account = get_account_instance().getCurrentAccount()
            if not kwargs.get("callback", None):
                result = cls.send_request(req, **kwargs)

                return [
                    MessageObject(current_account, result.response.messages.get(i), False, False)
                    for i in range(result.response.messages.size())
                ]
            else:
                fn = kwargs.pop("callback")

                def _cb(res, err):
                    if err:
                        return

                    fn([
                        MessageObject(current_account, res.messages.get(i), False, False)
                        for i in range(res.messages.size())
                    ])

                return cls.send(req, callback=_cb, wait_response=False, **kwargs)

        @staticmethod
        def get_user(user_id: int):
            return get_messages_controller().getUser(user_id)

        @staticmethod
        def input_user(user_id: int):
            return get_messages_controller().getInputUser(user_id)

        @staticmethod
        def peer(peer_id: int):
            return get_messages_controller().getPeer(peer_id)

        @staticmethod
        def input_peer(peer_id: int):
            return get_messages_controller().getInputPeer(peer_id)

        @classmethod
        def get_sticker_set_by_short_name(cls, short_name: str, **kwargs):
            return cls.send(
                cls.tlrpc_object(
                    TLRPC.TL_messages_getStickerSet(),
                    stickerset=cls.tlrpc_object(
                        TLRPC.TL_inputStickerSetShortName(),
                        short_name=short_name
                    )
                ), **kwargs
            )

        @classmethod
        def get_chat(cls, chat_id: int, **kwargs):
            req = TLRPC.TL_messages_getChats()
            req.id.add(chat_id)
            return cls.send_request(req, **kwargs)

        @classmethod
        def get_channel(cls, channel_id: int, **kwargs) -> int:
            req = TLRPC.TL_channels_getChannels()
            req.id.add(channel_id)
            return cls.send_request(req, **kwargs)

        @classmethod
        def get_user_photos(cls, user_id: int, limit: int = 1, **kwargs):
            return cls.send_request(
                cls.tlrpc_object(
                    TLRPC.TL_photos_getUserPhotos(),
                    user_id=cls.input_user(user_id),
                    limit=limit
                ),
                **kwargs
            )

        @classmethod
        def get_channel_messages(cls, channel: Union[int, str, Any], ids: Union[List[int], int], **kwargs):
            # МЕТОД НИЧЕГО НЕ ДАЕТ В ОТВЕТ, ЕСЛИ ВЫ ЗНАЕТЕ, КАК ИСПРАВИТЬ - t.me/chillden4ik
            # МЕТОД НИЧЕГО НЕ ДАЕТ В ОТВЕТ, ЕСЛИ ВЫ ЗНАЕТЕ, КАК ИСПРАВИТЬ - t.me/chillden4ik
            if isinstance(channel, str):
                channel = -cls.resolve_channel(channel).id

            if isinstance(channel, int):
                channel = cls.input_peer(channel)

            if isinstance(channel, TLRPC.InputPeer):
                channel = get_messages_controller().getInputChannel(channel)

            if isinstance(channel, TLRPC.TL_inputChannel):
                req = cls.tlrpc_object(
                    TLRPC.TL_channels_getMessages(),
                    channel=channel
                )
                if isinstance(ids, list):
                    for _id in ids:
                        req.id.add(_id)
                else:
                    req.id.add(ids)

                result = cls.send_request(req, **kwargs)
                if kwargs.get("wait_response", None) is False:
                    return result
                else:
                    current_account = get_account_instance().getCurrentAccount()

                    return [
                        MessageObject(current_account, result.response.messages.get(i), False, False)
                        for i in range(result.response.messages.size())
                    ]
            else:
                return []

        @classmethod
        def delete_messages(cls, ids: Union[List[int], int], chat_id: int, topic_id: int = 0, for_all: bool = True):
            return get_messages_controller().deleteMessages(
                CactusUtils.array_list([jint(i) for i in ids] if isinstance(ids, list) else [jint(ids)]),
                None, None, chat_id, topic_id, for_all, 0
            )

        @classmethod
        def _channel_resolve_callback(cls, arg, uid):
            CactusUtils.debug(f"Request with id {uid}: received", "TLRPC")

            cls._res[uid].response = arg
            cls._res[uid]._event.set()

        @classmethod
        def resolve_channel(cls, username: str, *, callback: Optional[Callable[[Any], None]] = None):
            if callback:
                return ChatUtils.getInstance().resolveChannel(username, Callback(callback))

            uid = uuid4().hex
            cls._res[uid] = cls.Result()

            CactusUtils.debug(f"Request with id {uid}: @{username} sent", "TLRPC")
            ChatUtils.getInstance().resolveChannel(username, Callback(cls._channel_resolve_callback, uid))
            X = cls._res[uid]._event.wait(20)

            result = cls._res.pop(uid, None)
            if not X and not result.response:
                raise TimeoutError(f"Request with id {uid} timed out")

            CactusUtils.debug(f"Request with id {uid} received", "TLRPC")

            return result.response

    @classmethod
    def get_message_by_rnd_in_current_chat(cls, rnd: str, callback=None):
        try:
            fragment = get_last_fragment()
            if fragment.__class__.__name__ == "ChatActivity":
                if not callback:
                    try:
                        return cls.Telegram.search_messages(
                            dialog_id=get_private_field(fragment, "dialog_id"),
                            query=rnd,
                            limit=21,
                            top_message_id=get_private_field(fragment, "threadMessageId"),
                        )[0], fragment
                    except cls.Telegram.TLRPCException as e:
                        cls.error(e.text)
                        cls.show_error(e.text)
                        return None, None
                else:
                    def _cb(msgs):
                        if not msgs:
                            return

                        callback(msgs[0], fragment)

                    cls.Telegram.search_messages(
                        dialog_id=get_private_field(fragment, "dialog_id"),
                        query=rnd,
                        limit=21,
                        top_message_id=get_private_field(fragment, "threadMessageId"),
                        callback=_cb
                    )
            else:
                cls.show_error(f"[{rnd}] You are not in a chat!...")
                return None, None
        except IndexError:
            return None, None

    @contextmanager
    def SpinnerAlertDialog(self, text: Optional[str] = None):
        spinner_dialog_builder: Optional[AlertDialogBuilder] = None

        def _show_dialog():
            nonlocal spinner_dialog_builder
            act = get_last_fragment().getParentActivity()
            if not act:
                return

            spinner_dialog_builder = AlertDialogBuilder(act, AlertDialogBuilder.ALERT_TYPE_SPINNER)
            spinner_dialog_builder.set_cancelable(False)
            if text and hasattr(spinner_dialog_builder, "set_text"):
                try:
                    spinner_dialog_builder.set_text(text)
                except Exception:
                    pass
            spinner_dialog_builder.show()

        def hide_spinner():
            nonlocal spinner_dialog_builder
            if spinner_dialog_builder:
                try:
                    spinner_dialog_builder.dismiss()
                except Exception:
                    pass
                spinner_dialog_builder = None

        try:
            run_on_ui_thread(_show_dialog)
            yield spinner_dialog_builder
        except:
            run_on_ui_thread(hide_spinner())
            raise
        finally:
            run_on_ui_thread(hide_spinner())

    @classmethod
    def run_on_queue(cls, func, *args, delay: int = 0, plugin_id: str = "...", **kwargs):
        def _fn():
            try:
                func(*args, **kwargs)
            except:
                cls.error(f"Error on queue <{plugin_id}>: " + traceback.format_exc(), plugin_id)

        run_on_queue(_fn, delay=delay)

    @property
    def plugins(self):
        return self._plugins

    class Inline:
        need_markups: Dict[Any, List[Dict[str, Any]]] = {}
        msg_markups: Dict[Any, Dict[Any, Any]] = {}

        @staticmethod
        def CallbackData(plugin_id, method: str, **kwargs):
            return f"cactus://{plugin_id}/{method}?{urlencode(kwargs)}"

        @staticmethod
        def Button(
                text: str, *,
                url: Optional[str] = None,
                callback_data: Optional[str] = None,
                query: Optional[str] = None,
                requires_password: Optional[bool] = None,
                copy: Optional[str] = None,
                **kwargs
        ):
            if (
                    callback_data
                    and not callback_data.startswith("cactus://")
                    and (not kwargs or not kwargs.get("plugin_id", None))
            ):
                raise Exception(f"Invalid callback_data <{callback_data}>")

            if m := re.findall(r'(<emoji\s+(?:document_id=|id=)(\d+)>([^<]+)</emoji>)', text):
                for tag, id, emoji in m:
                    text = text.replace(tag, f'<emoji id={id}/>', 1)

            if url:
                return CactusUtils.Telegram.tlrpc_object(
                    TLRPC.TL_keyboardButtonUrl(),
                    text=text,
                    url=url
                )
            if kwargs.get("data", None):
                return CactusUtils.Telegram.tlrpc_object(
                    TLRPC.TL_keyboardButtonCallback(),
                    text=text,
                    data=kwargs.get("data").encode("utf-8"),
                    requires_password=requires_password if requires_password is not None else False
                )
            if callback_data:
                return CactusUtils.Telegram.tlrpc_object(
                    TLRPC.TL_keyboardButtonCallback(),
                    text=text,
                    data=(
                        callback_data
                        if not kwargs
                        else CactusUtils.Inline.CallbackData(
                            plugin_id=kwargs.get("plugin_id"),
                            method=kwargs.get("method"),
                            **kwargs
                        )
                    ).encode("utf-8"),
                    requires_password=requires_password if requires_password is not None else False
                )
            if query:
                return CactusUtils.Telegram.tlrpc_object(
                    TLRPC.TL_keyboardButtonCallback(),
                    text=text,
                    data=CactusUtils.Inline.CallbackData(
                        "cactuslib", "setQuery", query=query
                    ).encode("utf-8")
                )
            if copy or kwargs.get("copy_text"):
                return CactusUtils.Telegram.tlrpc_object(
                    TLRPC.TL_keyboardButtonCopy(),
                    text=text,
                    copy_text=copy or kwargs.get("copy_text", None)
                )

            raise Exception("Invalid button")

        @classmethod
        def to_json(cls, btn):
            return {
                "text": btn.text,
                **{
                    key: getattr(btn, key) if key != "data" else bytes(getattr(btn, key)).decode("utf-8")
                    for key in (
                        ["data", "requires_password"]
                        if isinstance(btn, TLRPC.TL_keyboardButtonCallback)
                        else ["url"]
                        if isinstance(btn, TLRPC.TL_keyboardButtonUrl)
                        else ["copy_text"]
                        if isinstance(btn, TLRPC.TL_keyboardButtonCopy)
                        else []
                    )
                }
            }

        class Markup:
            def __init__(self, is_global: bool = False, on_sent: Optional[Callable] = None, *args, **kwargs):
                self.is_global = is_global
                self.on_sent = (on_sent, args, kwargs)
                self._markup = TLRPC.TL_replyInlineMarkup()
                self._json = []

            def add_row(self, *btns):
                row = []
                for btn in btns:
                    if btn is None:
                        continue

                    if isinstance(btn, ArrayList):
                        btn = list(btn.toArray())

                    if isinstance(btn, list):
                        for b in btn:
                            if isinstance(b, dict):
                                b = CactusUtils.Inline.Button(**b)
                            row.append(b)
                    else:
                        if isinstance(btn, dict):
                            btn = CactusUtils.Inline.Button(**btn)

                        row.append(btn)

                if len(row) > 0:
                    tlrow = TLRPC.TL_keyboardButtonRow()
                    for item in row:
                        tlrow.buttons.add(item)

                    self._markup.rows.add(tlrow)
                    self._json.append([CactusUtils.Inline.to_json(item) for item in row])

                return self

            @classmethod
            def from_dict(cls, d, *args, **kwargs):
                if isinstance(d, (dict, ArrayList)):
                    return cls(*args, **kwargs).add_row(d)

                if isinstance(d, list):
                    if not any(isinstance(item, list) for item in d):
                        return cls(*args, **kwargs).add_row(d)

                    m = cls(*args, **kwargs)
                    for item in d:
                        if isinstance(item, (dict, list, ArrayList)):
                            m.add_row(item)
                    return m

            def to_url_with_data(self):
                return "tg://cactus/mdata/" + CactusUtils.compress_and_encode(json.dumps({
                    "markup": self._json
                }))

        @dataclass
        class CallbackParams:
            cell: ChatMessageCell
            message: MessageObject
            button: Optional[Any] = None
            is_long: bool = False

            def edit_message(self, text: str, **kwargs):
                fragment = kwargs.pop("fragment", get_last_fragment())
                CactusUtils.edit_message(self.message, text, fragment=fragment, **kwargs)
                if kwargs.get("markup", None) is None and self.message.messageOwner.reply_markup:
                    self.edit_markup()

            edit = edit_message

            def edit_markup(self, markup=None):
                CactusUtils.edit_message_markup(self.cell, markup)

            def delete_message(self):
                dialog_id = self.message.getDialogId()
                chat = get_messages_controller().getChat(-dialog_id)
                if self.message.canDeleteMessage(
                        self.message.getChatMode() == 1,
                        chat
                ):
                    topic_id = self.message.getTopicId()
                    CactusUtils.Telegram.delete_messages(
                        self.message.getRealId(),
                        dialog_id,
                        topic_id if topic_id != dialog_id and chat else 0
                    )

            delete = delete_message

        @classmethod
        def on_click(cls, method: str, support_long_click: bool = False):
            def decorator(func):
                func.__is_inline_callback__ = True
                func.__support_long__ = support_long_click
                func.__data__ = method
                return func

            return decorator


def command(
        command: Optional[str] = None, *,
        aliases: Optional[List[str]] = None,
        doc: Optional[str] = None,
        enabled: Optional[Union[str, bool]] = None
):
    """
    Decorator for commands

    Args:
        command (str): The command
        aliases (List[str]): A list of aliases for the command
        doc (str): String-key in `strings` for command description
        enabled (str/bool): Setting-key or boolean for enabling the command
    """

    def decorator(func):
        func.__is_command__ = True
        func.__aliases__ = aliases
        func.__cdoc__ = doc
        func.__enabled__ = enabled
        func.__cmd__ = command or func.__name__

        return func

    return decorator


def uri(uri: str):
    """
    Decorator for URIs

    Args:
        uri (str): The URI
    """

    def decorator(func):
        func.__is_uri_handler__ = True
        func.__uri__ = uri
        return func

    return decorator


def message_uri(uri: str, support_long_click: bool = False):
    """
    Decorator for URIs in messages

    Args:
        uri (str): The URI
        support_long_click (bool): if true, func will be called on long click too
    """

    def decorator(func):
        func.__is_uri_message_handler__ = True
        func.__uri__ = uri
        func.__support_long__ = support_long_click
        return func

    return decorator


class PluginsData:
    _current_instance = None
    plugins = {}

    @classmethod
    def parse(cls, plugin_path: str, plugin_id=None):
        strings, commands, description = get_plugin_strings_and_commands(plugin_path)
        if not strings:
            return

        cls.plugins[plugin_id or strings.get("__id__", os.path.basename(plugin_path))] = {
            "strings": strings,
            "commands": commands,
            "description": description
        }

    @classmethod
    def description(cls, plugin_id: str) -> str:
        if plugin_id not in cls.plugins:
            return "<unknown-plugin>"

        return cls.locale(plugin_id).get("__doc__", cls.plugins[plugin_id].get("description", ""))

    @classmethod
    def locale(cls, plugin_id: str) -> Dict[str, str]:
        locale_dict: Dict[str, Union[str, Dict[str, str]]] = cls.plugins[plugin_id]["strings"].get(
            CactusUtils.get_locale(),
            cls.plugins[plugin_id]["strings"]
        )
        if "en" in locale_dict:
            locale_dict = locale_dict["en"]

        return locale_dict

    @classmethod
    def commands(cls, plugin_id: str) -> Dict[str, str]:
        ...


class Fingerprint:
    change_texts = {}

    @staticmethod
    def has_fingerprint() -> bool:
        if Build.VERSION.SDK_INT >= 23 and SharedConfig.useFingerprintLock:
            try:
                fingerprintManager = getattr(FingerprintManagerCompat, "from")(ApplicationLoader.applicationContext)
                return fingerprintManager.isHardwareDetected() and fingerprintManager.hasEnrolledFingerprints() and FingerprintController.isKeyReady() and not FingerprintController.checkDeviceFingerprintsChanged()
            except:
                CactusUtils.error(f"Fingerprint error: {traceback.format_exc()}")
        return False

    @classmethod
    def check_fingerprint(cls, title: str, description: str, cb: CactusUtils.Callback):
        try:
            frag = get_last_fragment()
            if not frag:
                return

            activity = frag.getParentActivity()
            if not activity:
                return

            rnd_id = random.randint(0, **********)
            cls.change_texts[rnd_id] = title

            X = BotBiometry.get(activity, UserConfig.selectedAccount, rnd_id)
            X.updateToken(description, uuid4().hex, cb)
        except:
            CactusUtils.error(f"Fingerprint error: {traceback.format_exc()}")

    @classmethod
    def change_text(cls, param):
        if param.args[0] in cls.change_texts:
            user = TLRPC.TL_user()
            user.id = param.args[0]
            user.first_name = cls.change_texts.pop(param.args[0])
            param.setResult(user)


@dataclass
class MenuItemInfo:
    item: Any
    enabled: bool


class CactusLib(CactusUtils.CactusModule):
    strings = {
        "en": {
            "retry": " Retry", "code": "Code", "result": "Result",
            "check_updates_btn": " Check for updates",
            "settings_command_prefix_label": "Command prefix",
            "settings_info_header": "Info (Help)",
            "logs": "🌵 <b>[{lvl}] Logs</b> with PID <code>{id}</code>\n{contains}\n<pre lang=python>...\n{last_logs}\n</pre>",
            "contains": "ℹ️ Contains: `{}`",
            "eval_error": "❌ <b>Error</b>:\n<pre language=\"python\">{}</pre> <pre language=\"python\">{}</pre>",
            "eval_result": "✅ <b>Result</b>:\n<pre language=\"python\">{}</pre> <pre language=\"python\">{}</pre>",
            "eval_result_file": "🗞 <b>Result</b> in the txt-file\n<pre language=\"python\">{}</pre>",
            "eval_error_file": "❌ <b>Error</b> in the txt-file\n<pre language=\"python\">{}</pre>",
            "plf_doc": "<plugin-id or plugin-name> - send plugin file",
            "cexport_doc": "- message-version of export",
            "no_logs": "❌ No logs found",
            "cactus_plugins": "{} <b>Installed plugins with using CactusLib</b> ({})\n<blockquote>{}</blockquote>\n",
            "other_plugins": "{} <b>Other plugins</b> ({})\n<blockquote>{}</blockquote>\n",
            "next_page": "⏩ Next page",
            "prev_page": "⏪ Previous page",
            "plugin_info": "{emoji} <b>{name}</b> ( <code>{id}</code><a href='tg://cactus/{rnd}'> </a>)\n<blockquote>{doc}</blockquote>\n{veremoji} <b>{version}</b> ({author})\n\n{commands}",
            "toggle_uri": "<a href=\"{}\">{} {}</a>\n",
            "get_file_btn": " Get file",
            "settings_btn": " Settings",
            "delete_plugin_btn": " Delete plugin",
            "all_plugins_btn": " All plugins",
            "edit_mode": " Edit mode",
            "plugin_command_format": "{emoji} <code>{prefix}{command}</code> {doc}",
            "help_cmd": "[plugin-name / command / plugin-id] - Show plugin info or all plugins",
            "eval_cmd": "<python-code> - Run python code",
            "file_too_large": "❌ File too large",
            "logs_cmd": "[lvl] [plugin-id] [time(s)] [-c \"find bla\"] - Show logs by level, plugin-id, time and contains string",
            "no_args": "❌ Where are the arguments themselves?",
            "prefix_must_be_one_char": "❌ Prefix must be one character",
            "prefix_set": "✅ New command prefix set: {}",
            "set_prefix_doc": "<prefix> - set new command prefix",
            "settings_commands_header": "Commands",
            "command_logger": "Command logger",
            "command_logger_subtext": "Enables logging of commands in the specified chat",
            "command_logger_chat_label": "Logger chat ID",
            "command_logger_chat": "Can be set by using the set_logger_chat command in the chat",
            "invalid_peer_id": "❌ Invalid chat ID (should be an integer)",
            "logger_chat_set": "✅ Logger chat ID set: {}",
            "set_logger_chat_cmd_doc": "[chat-id] - set chat-id for logging commands (recommended to use in the chat)",
            "settings_menu_item_header": "CactusLib's settings",
            "enabled": "enabled!", "enable": "ON", "toggle_d": " Disable",
            "disabled": "disabled!", "disable": "OFF", "toggle_e": " Enable",
            "plugin_not_found": "❌ Plugin <{}> not found", "all_list": "💠 All plugins",
            "settings_language_label": "Language for plugins (en/ru/...)",
            "settings_language_subtext": "Set empty to use the system language",
            "plugin_file_sent": "📁 <b>{plugin.name}</b> {version}",
            "exporting_plugins": "📤 <b>Export plugins with settings</b>\n<blockquote>Click on the <b>plugin name</b> to select</blockquote> <b>{add_all}  |  {clear}</b>\n<blockquote>{plugins}</blockquote>\n{export}",
            "add_all": "<a href='{}'>⏬ Add all</a>",
            "clear": "<a href='{}'>🗑 Clear</a>",
            "export": "<a href='{}'>{} <b>Export</b></a>",

            "select_plugins": "Select plugins",
            "select_plugins2": "Select plugins",
            "cancel": "Cancel",
            "import": "Import",
            "plugins": "Plugins",
            "selected_plugins": "{} selected",
            "processing": "Processing...",

            "import_alert_title": "Import {}",
            "import_info": "The file contains plugins with saved data.",
            "import_warning": "Note: You may lose the current plugins settings and data.",
            "import_progress": "Processed {}",
            "import_done": "Successfully imported {}!",

            "export_title": "Export Plugins",
            "export2": "Export",
            "export_progress": "Loaded {}",
            "export_info": "The file contains plugins with fully-saved data.",
            "export_warning": "The settings may contain confidential data.",
            "export_done": "Exported successfully!",

            "include_data_and_settings": "Include data and settings",
            "commands": "Commands:",
            "not_description": "Description not found",
            "settings_show_cmds_description_label": "Show commands description",
            "settings_show_cmds_description_subtext": "When installing a plugin and in the menu",
            "settings_emoji_type_label": "Premium emoji type",
            "settings_global_premium_label": "Global Cactus-Premium",
            "settings_global_premium_subtext": "Premium emojis in all chats, unless you have Telegram Premium.",

            "sort": " <b>Sort</b>",
            "sort_by_name": "<a href='{}'>Name{}</a>",
            "sort_by_filesize": "<a href='{}'>File size{}</a>",
            "sort_by_updatedate": "<a href='{}'>Update date{}</a>",

            "delete_plugin": "Delete plugin",
            "delete": "Delete",
            "delete_plugin_confirm": "Are you sure you want to delete the plugin {}?",
            "plugin_deleted": "Plugin {} deleted",
            "edit": " EDIT", "disable2": " DISABLE",
            "enable2": " ENABLE",
            "command_disabled": "Command {} in plugin {} disabled",
            "command_enabled": "Command {} in plugin {} enabled",
            "alias_enabled": "Alias {} for command {} in plugin {} enabled",
            "alias_disabled": "Alias {} for command {} in plugin {} disabled",
            "edit_cmd_subtext": "Enter new command to replace the old one",
            "edit_alias_subtext": "Enter new alias to replace the old one",
            "enter_command": "Enter command",
            "done": "Done",
            "enter_alias": "Enter alias",
            "only_letters_and_numbers": "Only letters and numbers and _ are allowed",
            "command_exists": "Such a command already exists",
            "alias_exists": "Such an alias already exists",
            "command_edited": "Command <{}> ➜ <{}>",
            "alias_edited": "Alias <{}> ➜ <{}>",
            "reset_changes_btn": " Reset changes",
            "reset": "Reset",
            "reset_changes_alert": "Are you sure you want to reset the changes in the plugin {}?",
            "reset_done": "Changes reset",

            "plugin_menu_items": " <b>Menu items</b>",
            "message_context_menu": "<b>[ Message ]</b>",
            "drawer_menu": "<b>[ Drawer ]</b>",
            "chat_action_menu": "<b>[ Chat ]</b>",
            "profile_action_menu": "<b>[ Profile ]</b>",
            "menu_item_disabled": "Menu item «{}» in plugin {} disabled",
            "menu_item_enabled": "Menu item «{}» in plugin {} enabled",

            "edit_zwylib_prefix": "Edit prefix for this plugin commands",
            "enter_prefix": "Enter prefix",
            "edit_dispatcher_prefix": "Edit prefix",
            "prefix_edited": "Prefix edited: {} ➜ {}",
        },
        "ru": {
            "__doc__": "Библиотека Cactus с полноценным хелпом по командам и плагинам, безграничными возможностями для текстовых/uri команд, импорта/экспорта плагинов и многим другим!",
            "retry": " Повторить", "code": "Код", "result": "Результат",
            "check_updates_btn": " Проверить обновления",
            "settings_command_prefix_label": "Префикс команды",
            "logs": "🌵 <b>[{lvl}] Логи</b> плагина с ID <code>{id}</code>\n{contains}\n<pre language=\"python\">...\n{last_logs}</pre>",
            "contains": "ℹ️ Содержит: <code>{}</code>",
            "eval_error": "❌ <b>Ошибка</b>:\n<pre language=\"python\">{}</pre> <pre language=\"python\">{}</pre>",
            "eval_result": "✅ <b>Результат</b>:\n<pre language=\"python\">{}</pre> <pre language=\"python\">{}</pre>",
            "eval_result_file": "🗞 <b>Результат</b> находится в файле\n<pre language=\"python\">{}</pre>",
            "eval_error_file": "❌ <b>Ошибка</b> находится в файле\n<pre language=\"python\">{}</pre>",
            "plf_doc": "<айди-плагина / имя-плагина> - получить код плагина в файле",
            "cexport_doc": "- меню экспорта в виде сообщения",
            "no_logs": "❌ Нет логов по вашему запросу",
            "settings_info_header": "Информация (Помощь)",
            "settings_btn": " Настройки",
            "get_file_btn": " Выгрузить файл",
            "delete_plugin_btn": " Удалить плагин",
            "all_plugins_btn": " Все плагины",
            "edit_mode": " Редактирование",
            "cactus_plugins": "{} <b>Установленные плагины, использующие CactusLib</b> ({})\n<blockquote>{}</blockquote>\n",
            "other_plugins": "{} <b>Другие плагины</b> ({})\n<blockquote>{}</blockquote>\n",
            "next_page": "⏩ Следующая стр.",
            "prev_page": "⏪ Предыдущая стр.",
            "help_cmd": "[имя плагина / команда / id] - список плагинов или инфо о плагине и командах",
            "eval_cmd": "<python-код> - выполнить код и получить результат",
            "file_too_large": "❌ Файл слишком большой",
            "logs_cmd": "[lvl] [id плагина] [время(в секундах)] [-c \"find bla\"] - показать логи по уровню, id плагина и времени",
            "not_found": "❌ Ничего не найдено по запросу {}",
            "no_args": "❌ Где аргументы собственно?",
            "prefix_must_be_one_char": "❌ Префикс должен быть одним символом",
            "prefix_set": "✅ Новый префикс команд установлен: {}",
            "set_prefix_doc": "<префикс> - установить новый префикс для команд",
            "settings_commands_header": "Команды",
            "command_logger": "Логирование команд",
            "command_logger_subtext": "Включает логирование команд в указанный чат",
            "command_logger_chat_label": "ID Logger-чата",
            "command_logger_chat_subtext": "Можно установить командой set_logger_chat в нужном чате",
            "invalid_peer_id": "❌ Неверно указан ID чата (должно быть целым числом)",
            "logger_chat_set": "✅ ID Logger-чата установлен: {}",
            "set_logger_chat_cmd_doc": "[id чата] - установить id чата для логирования команд (рекомендуется использовать в самом чате)",
            "settings_menu_item_header": "Настройки CactusLib",
            "enabled": "включен!", "enable": "ВКЛ", "toggle_d": " Выключить",
            "disabled": "выключен!", "disable": "ВЫКЛ", "toggle_e": " Включить",
            "plugin_not_found": "❌ Плагин <{}> не найден", "all_list": "💠 Все плагины",
            "settings_language_label": "Язык для плагинов (локаль) (en/ru/...)",
            "settings_language_subtext": "Оставьте пустым, чтобы использовать язык системы",
            "exporting_plugins": "📤 <b>Экспорт плагинов с настройками</b>\n<blockquote>Нажмите на <b>имя плагина</b>, чтобы выбрать</blockquote><b>{add_all}  |  {clear}</b>\n<blockquote>{plugins}</blockquote>\n{export}",
            "add_all": "<a href='{}'>⏬ Добавить все</a>",
            "clear": "<a href='{}'>🗑 Очистить</a>",
            "export": "<a href='{}'>{} <b>Экспортировать</b></a>",

            "select_plugins": "Выбрать плагины",
            "select_plugins2": "Выберите плагины",
            "selected_plugins": "Выбрано {}",
            "cancel": "Отмена",
            "import": "Импорт",
            "plugins": "плагинов",
            "processing": "Загрузка...",

            "import_alert_title": "Импорт {}",
            "import_info": "В файле находятся плагины с полностью сохраненными данными.",
            "import_warning": "Вы можете потерять данные текущих плагинов.",
            "import_progress": "Импортировано {}",
            "import_done": "Успешно импортировано {}!",

            "export_title": "Экспорт плагинов",
            "export2": "Экспорт",
            "export_progress": "Загружено {}",
            "export_info": "Создаёт файл с текущими версиями плагинов и сохраненными данными.",
            "export_warning": "В настройках могут содержаться конфиденциальные данные.",
            "export_done": "Успешно!",

            "include_data_and_settings": "Включая данные и настройки",
            "commands": "Команды:",
            "not_description": "Описание не найдено",
            "settings_show_cmds_description_label": "Показывать описание команд",
            "settings_show_cmds_description_subtext": "При установке плагина и в меню",
            "settings_emoji_type_label": "Тип премиум-эмодзи",
            "settings_global_premium_label": "Глобальный Cactus-Premium",
            "settings_global_premium_subtext": "Премиум-эмодзи во всех чатах, если у вас нет Telegram Premium.",

            "sort": " <b>Сортировка</b>",
            "sort_by_name": "<a href='{}'>Имя{}</a>",
            "sort_by_filesize": "<a href='{}'>Размер{}</a>",
            "sort_by_updatedate": "<a href='{}'>Дата обновления{}</a>",

            "delete_plugin": "Удалить плагин",
            "delete": "Удалить",
            "delete_plugin_confirm": "Вы уверены, что хотите удалить плагин {}?",
            "plugin_deleted": "Плагин {} удалён",
            "edit": " ИЗМЕНИТЬ",
            "disable2": " ВЫКЛЮЧИТЬ",
            "enable2": " ВКЛЮЧИТЬ",
            "command_disabled": "Команда {} плагина {} отключена",
            "command_enabled": "Команда {} плагина {} включена",
            "alias_enabled": "Алиас {} команды {} плагина {} включен",
            "alias_disabled": "Алиас {} команды {} плагина {} отключен",
            "edit_cmd_subtext": "Введите новую команду, чтобы заменить старую",
            "edit_alias_subtext": "Введите новый алиас, чтобы заменить старый",
            "enter_command": "Введите команду", "done": "Готово",
            "enter_alias": "Введите алиас",
            "only_letters_and_numbers": "Используйте только буквы, цифры и _",
            "command_exists": "Такая команда уже существует",
            "alias_exists": "Такой алиас уже существует",
            "command_edited": "Команда изменена: {} ➜ {}",
            "alias_edited": "Алиас изменён: {} ➜ {}",
            "reset_changes_btn": " Сбросить изменения",
            "reset": "Сбросить",
            "reset_changes_alert": "Вы уверены, что хотите сбросить изменения плагина {}?",
            "reset_done": "Изменения сброшены",

            "plugin_menu_items": " <b>Пункты меню</b>",
            "message_context_menu": "<b>[ Сообщение ]</b>",
            "drawer_menu": "<b>[ Боковое меню ]</b>",
            "chat_action_menu": "<b>[ Чат ]</b>",
            "profile_action_menu": "<b>[ Профиль ]</b>",
            "menu_item_disabled": "Пункт меню «{}» плагина {} отключён",
            "menu_item_enabled": "Пункт меню «{}» плагина {} включён",

            "edit_zwylib_prefix": "Изменить префикс для команд этого плагина",
            "enter_prefix": "Введите префикс",
            "edit_dispatcher_prefix": "Изменить префикс",
            "prefix_edited": "Префикс изменён: {} ➜ {}",
        }
    }
    emojis = {
        0: {
            "cactus": "<emoji id=5427317234403930129>🌵</emoji>",
            "cactus_title": "<emoji id=5427317234403930129>🌵</emoji>",
            "base_title": "<emoji id=4958944784417817197>🎈</emoji>",
            "base": "<emoji id=5789790449594011537>🥏</emoji>",
            "item": [
                f"<emoji id={i}>" + "{}</emoji>"
                for i in [
                    5262508305485421903, 5262770354325055631, 5262476050281029318,
                    5359429357825696533, 5362010744839740726, 5100704006838158068,
                    5100862156123931478, 5100862156123931478, 5100652175172830068,
                    4974259868996207180, 4974608010455286340, 4974405043185779140,
                    4974629970623071075, 5416090563554320480, 5415597204955996883,
                    5415856681110217088, 5418123573438980585, 5292288951585876795,
                    5292015873270228926, 5291886225387433931, 5359742959157783224
                ]
            ],
            "enabled_other": "<emoji id=5359375614899920473>🥏</emoji>",
            "disabled_other": "<emoji id=5361729832503745930>🚫</emoji>",
            "sort": "<emoji id=5780527330417970854>🕹</emoji>",
            "veremoji": "<emoji id=5816598974030681170>📶</emoji>",
            "edit": "<emoji id=4956408168142864996>✏️</emoji>",
            "check": "<emoji id=5336985409220001678>✅</emoji>",
            "disable": "<emoji id=5240241223632954241>🚫</emoji>",
            "delete": "<emoji id=5343709640083400091>❌</emoji>",
            "settings": "<emoji id=5787237370709413702>⚙️</emoji>",
            "update": "<emoji id=5298820832338915986>🔄</emoji>",
            "file": "<emoji id=5818955300463447293>🗂</emoji>",
            "reset": "<emoji id=5845943483382110702>🔄</emoji>",
            "pin": "<emoji id=5355265554535949832>📌</emoji>",
            "heart": "<emoji id=5357579953497983889>❤️</emoji>",
            "output": "<emoji id=5467452106057195213>💭</emoji>",
            "zwylib": "<emoji id=5472033848319548594>⭐️</emoji>",
        },
        1: {
            "cactus": "<emoji id=5427150864550755845>🌵</emoji>",
            "cactus_title": "<emoji id=5427150864550755845>🌵</emoji>",
            "base": "<emoji id=5366526456274891907>🎈</emoji>",
            "base_title": "<emoji id=5235588635885054955>🎈</emoji>",
            "item": [
                f"<emoji id={i}>" + "{}</emoji>"
                for i in [
                    5778335621491723621
                ]
            ],
            "enabled_other": "<emoji id=5348409781709192562>🥏</emoji>",
            "disabled_other": "<emoji id=5303186997307785736>🚫</emoji>",
            "sort": "<emoji id=5235588635885054955>🕹</emoji>",
            "veremoji": "<emoji id=5886446115905082831>📍</emoji>",
            "edit": "<emoji id=5879841310902324730>✏️</emoji>",
            "check": "<emoji id=5985596818912712352>✅</emoji>",
            "disable": "<emoji id=5872829476143894491>🚫</emoji>",
            "delete": "<emoji id=5985346521103604145>❌</emoji>",
            "settings": "<emoji id=5877260593903177342>⚙️</emoji>",
            "update": "<emoji id=5877410604225924969>🔄</emoji>",
            "file": "<emoji id=5839323457015256759>📄</emoji>",
            "reset": "<emoji id=5845943483382110702>🔄</emoji>",
            "pin": "<emoji id=5875462364110787088>📌</emoji>",
            "heart": "<emoji id=5271926881002727242>❤️</emoji>",
            "output": "<emoji id=5467452106057195213>💭</emoji>",  # TODO CREATE ADAPTIVE EMOJI
            "zwylib": "<emoji id=5222211766968151773>⭐️</emoji>",
        }
    }

    def emoji(self, name):
        return self.emojis.get(self.get_setting("emoji_type", 1), {}).get(name,
                                                                          "<emoji id=5192835217160681324>❔</emoji>")

    def __init__(self):
        super().__init__()
        self._zwy_commands_disabled = None
        self.__hooks = None
        self._last_cell = None
        self._original_commands = None
        self._original_aliases = None
        CactusUtils._lib = self
        self._instant_buttons = None
        self._plugins_modified = None
        self._plugins_sizes = None
        self._handled = None
        self._plugins: Dict[str, CactusUtils.Plugin] = None  # type: ignore
        self._commands2: Dict[str, List[str]] = None  # type: ignore
        self._aliases: Dict[str, str] = None  # type: ignore
        self._commands: Dict[str, Tuple[str, callable]] = None  # type: ignore
        self._menu_items: Dict[str, Dict[str, List[Any]]] = {}
        self._menu_items_by_id: Dict[str, MenuItemInfo] = {}

    def _add_menu_items(self, lang: str = None):
        with suppress(Exception):
            self.remove_menu_item("cactuslib_settings")
            self.remove_menu_item("cactuslib_export")

        if self.get_setting("show_settings_menu_item", False):
            self.add_menu_item(MenuItemData(
                menu_type=MenuItemType.DRAWER_MENU,
                text=self.string("settings_menu_item_header") if not lang else self.string("settings_menu_item_header",
                                                                                           locale=lang),
                icon="msg_settings_14",
                item_id="cactuslib_settings",
                priority=2,
                on_click=lambda ctx: self.open_plugin_settings()
            ))

        self.add_menu_item(MenuItemData(
            menu_type=MenuItemType.CHAT_ACTION_MENU,
            text=self.string("export_title") if not lang else self.string("export_title", locale=lang),
            icon="msg_filled_data_sent",
            item_id="cactuslib_export",
            priority=100,
            on_click=lambda ctx: run_on_ui_thread(
                lambda: self._open_export_plugins_alert(ctx)
            )
        ))

    def on_plugin_load(self):
        super().on_plugin_load()
        self.debug("Initializing library...")

        self._commands = {}
        self._commands2 = {}
        self._aliases = {}
        self._plugins = {}
        self._plugins_sizes = {}
        self._plugins_modified = {}
        self._instant_buttons = {}
        self._handled = {}
        self._original_commands = {}
        self._original_aliases = {}
        self._zwy_commands_disabled = {}

        try:
            CactusUtils._lib = self
            CactusUtils._get_setting = self.get_setting
            CactusUtils._on_new_plugin = self._on_new_plugin
            CactusUtils._on_unload_plugin = self._on_unload_plugin
            self.__hooks = Hooks(self).init()

            self.add_on_send_message_hook(priority=Integer.MAX_VALUE)
            self._add_menu_items()

            self._on_new_plugin(self)
            self.__search_plugins()
            self.debug("Plugin loaded!")
        except Exception:
            self.error(traceback.format_exc())

    def get_plugin_filesize(self, plugin_id: str) -> int:
        if plugin_id not in self._plugins_sizes:
            self._plugins_sizes[plugin_id] = os.path.getsize(PluginsController.getInstance().getPluginPath(plugin_id))

        return self._plugins_sizes[plugin_id]

    def get_plugin_modified_date(self, plugin_id: str) -> int:
        if plugin_id not in self._plugins_modified:
            self._plugins_modified[plugin_id] = os.path.getmtime(
                PluginsController.getInstance().getPluginPath(plugin_id))

        return self._plugins_modified[plugin_id]

    def _on_new_plugin(self, plugin: CactusUtils.Plugin):
        self.debug("Loading plugin: " + plugin.id)
        try:
            if plugin.id not in PluginsData.plugins:
                PluginsData.parse(PluginsController.getInstance().getPluginPath(plugin.id), plugin.id)

            if issubclass(plugin.__class__, CactusUtils.Plugin) is False:
                self._plugins[plugin.id] = plugin
                return

            plugin._uri_handlers = self.utils.search_uri_handlers(plugin)
            plugin._uri_message_handlers = self.utils.search_uri_handlers(plugin, True)
            plugin._watchers = self.utils.search_watchers(plugin)
            plugin._inline_handlers = self.utils.search_inline_handlers(plugin)

            self._load_plugin_commands(plugin)

            self._plugins[plugin.id] = plugin
        except:
            self.utils.show_error("Error loading plugin: " + plugin.id + ". See logs")
            self.error(f"[on_new_plugin]: {traceback.format_exc()}")

    def _load_plugin_commands(self, plugin: Union[CactusUtils.Plugin, str]):
        if isinstance(plugin, str):
            plugin = self._plugins[plugin]

        override_cmds: Dict[str, str] = self.get("override", {}).get(plugin.id, {}).get("commands", {})
        override_aliases: Dict[str, str] = self.get("override", {}).get(plugin.id, {}).get("aliases", {})

        for cmd, func in self.utils.search_commands(plugin).items():
            _orig = cmd
            if cmd in override_cmds:
                cmd = override_cmds[cmd]
            else:
                i = 0
                while cmd in self._commands or cmd in self._aliases:
                    i += 1
                    cmd += str(i)

            if cmd != _orig:
                self._original_commands[cmd] = _orig

            self._commands[cmd] = (plugin.id, func)
            self._commands2[plugin.id] = self._commands2.get(plugin.id, []) + [cmd]

            aliases = func.__aliases__ or []
            for alias in aliases:
                _orig = alias
                if alias in override_aliases:
                    alias = override_aliases[alias]
                else:
                    i = 0
                    while alias in self._aliases:
                        i += 1
                        alias += str(i)

                self._aliases[alias] = cmd
                if alias != _orig:
                    self._original_aliases[alias] = _orig

    def _on_unload_plugin(self, plugin: CactusUtils.Plugin):
        self.debug("Unloading plugin: " + plugin.id)
        for cmd in self._commands2.get(plugin.id, []):
            if cmd in self._commands:
                del self._commands[cmd]

            for alias, _cmd in self._aliases.copy().items():
                if _cmd == cmd:
                    del self._aliases[alias]

        if plugin.id in self._commands2:
            del self._commands2[plugin.id]

        if plugin.id in self._plugins:
            del self._plugins[plugin.id]

    def _on_plugin_delete(self, plugin_id: str):
        if plugin_id in self._plugins:
            self._on_unload_plugin(self._plugins[plugin_id])

        if plugin_id in self._commands2:
            cmds = self._commands2.pop(plugin_id)
            for cmd in cmds:
                if cmd in self._commands:
                    del self._commands[cmd]

                for alias, _cmd in self._aliases.copy().items():
                    if _cmd == cmd:
                        del self._aliases[alias]

        if plugin_id in self._menu_items:
            del self._menu_items[plugin_id]

        if plugin_id in PluginsData.plugins:
            del PluginsData.plugins[plugin_id]

    def on_send_message_hook(self, account, params):
        prefix = self.utils.prefix()
        if (
                not params or not params.message
                or not isinstance(params.message, str)
                or not params.message.startswith(prefix)
        ):
            return HookResult()

        text = params.message.strip()

        CMD_MATCH = re.compile(r"^(.)([a-zA-Z0-9_]+)\s*([\s\S]*)$", re.S)

        if (
                not (m := CMD_MATCH.match(text))
                or m.group(1) != prefix
                or (m.group(2) not in self._commands and m.group(2) not in self._aliases)
        ):
            return HookResult()

        cmd = m.group(2)
        if cmd in self._aliases:
            cmd = self._aliases[cmd]

        plugin_id, func = self._commands.get(cmd, None) or (None, None)
        if not plugin_id:
            return HookResult()

        if (
                self.get("disabled", {}).get("commands", {}).get(m.group(2), False) is True
        ) or (
                isinstance(func.__enabled__, bool)
                and not func.__enabled__
        ) or (
                isinstance(func.__enabled__, str)
                and not func.__self__.get_setting(func.__enabled__, True)
        ):
            return HookResult()

        try:
            args = shlex.split(m.group(3)) if m.group(3) else []
        except ValueError:
            args = [m.group(3)]

        try:
            command_obj = self.utils.Command(
                command=m.group(2),
                args=args,
                raw_args=m.group(3),
                text=text,
                account=account,
                params=params
            )

            return func(command_obj)
        except Exception as e:
            formatted = traceback.format_exc()
            self._plugins[plugin_id].error(f"Message hook error: [{str(e)}] {formatted}")
            self.answer(params,
                        f"🚫 <b>Error</b> while executing <b>message hook <i>{func.__name__}</i></b>:\n<pre language=\"python\">\n{self.utils.escape_html(formatted[:2000])}</pre>🔰 <b>Plugin {self.name}</b> (<code>{prefix}{m.group(2)}</code>)")
            return HookResult()

    def on_uri_command_hook(self, plugin_id: str, cmd: str, kwargs: Dict[str, Any]):
        if plugin_id == "emoji" and cmd.isdigit():
            self.open_emoji_preview(int(cmd))
            return

        if plugin_id in self._plugins and cmd in self._plugins[plugin_id]._uri_handlers:
            plugin = self._plugins[plugin_id]
            func = self._plugins[plugin_id]._uri_handlers[cmd]
            try:
                return func(**kwargs)
            except Exception as e:
                plugin.error(f"URI hook {func.__name__} error: [{str(e)}] {traceback.format_exc()}")
                self.utils.show_error(f"[URI:{cmd}] Error, see log {plugin_id} for details.")

    def open_emoji_preview(self, doc_id: int):
        frag = get_last_fragment()
        if not frag:
            return

        activity = frag.getParentActivity()
        if not activity:
            return

        current_account = UserConfig.selectedAccount
        doc = AnimatedEmojiDrawable.findDocument(current_account, doc_id)
        if not doc:
            return

        input_sticker_set = MessageObject.getInputStickerSet(doc)
        if not input_sticker_set:
            return

        alert = EmojiPacksAlert(frag, activity, frag.getResourceProvider(), self.utils.array_list(input_sticker_set))
        alert.setPreviewEmoji(doc)
        frag.showDialog(alert)

    def on_click_url_in_message(self, param):
        url, long_press, message, cell = param.args[1], param.args[2], param.args[0].getMessageObject(), param.args[0]
        if not url or not hasattr(url, "getURL") or not (string := url.getURL().strip()):
            return

        if not string.startswith("tg://cactusX/"):
            return

        m = re.match("tg://cactusX/(.+)/(.+)", string)
        if not m:
            return

        plugin_id = m.group(1)
        parsed = urlparse(m.group(2))
        cmd = parsed.path
        kwargs = {} if not parsed.query else {
            k: v[0]
            for k, v in parse_qs(parsed.query).items()
        }

        if plugin_id in self._plugins and cmd in self._plugins[plugin_id]._uri_message_handlers:
            plugin = self._plugins[plugin_id]
            func = self._plugins[plugin_id]._uri_message_handlers[cmd]
            try:
                if long_press is True and getattr(func, "__support_long__", False) is False:
                    return

                param.setResult(None)
                func(
                    CactusUtils.UriCallback(
                        cell=cell,
                        message=message,
                        long_press=long_press,
                        method=cmd,
                        raw_url=string
                    ), **kwargs
                )
                if long_press:
                    cell.resetPressedLink(-1)
            except Exception as e:
                plugin.error(f"URI hook {func.__name__} error: [{str(e)}] {traceback.format_exc()}")
                self.utils.show_error(f"[URI:{cmd}] Error, see log <{plugin_id}> for details.")

    def on_app_event(self, event_type: AppEvent):
        if event_type == AppEvent.START:
            self.__search_plugins()

    @CactusUtils.Inline.on_click("setQuery")
    def set_query_btn(self, _, query: str):
        frag = get_last_fragment()
        if not frag:
            return

        with suppress(Exception):
            frag.getChatActivityEnterView().setFieldText(query)

    def on_message_cell_setup(self, cell, msg, before: bool = False):
        try:
            if not cell or not msg or getattr(msg.messageOwner.media, "document", None):
                return

            self._last_cell = cell
            if before:
                return

            mid = msg.getRealId()

            if msg.hasInlineBotButtons():
                if mid > 0:
                    dialog_id = msg.getDialogId()
                    if dialog_id in CactusUtils.Inline.need_markups:
                        for data in CactusUtils.Inline.need_markups[dialog_id]:
                            if data["markup"] == msg.messageOwner.reply_markup:
                                self.utils.Inline.need_markups[dialog_id].remove(data)
                                _old = self.utils.Inline.msg_markups.get(dialog_id, {})
                                _old[mid] = msg.messageOwner.reply_markup
                                self.utils.Inline.msg_markups[dialog_id] = _old

                                if data.get("on_sent", None) and data["on_sent"][0]:
                                    data["on_sent"][0](CactusUtils.Inline.CallbackParams(cell, msg, None, False), *data["on_sent"][1], **data["on_sent"][2])

                                break
        except Exception as e:
            self.error(f"[on_message_cell_setup]: {e}\n{traceback.format_exc()}")

    def on_inline_button_click(self, cell, btn, param, is_long: bool = False):
        data = bytes(btn.data or b"...").decode("utf-8")
        if data.startswith("cactus://"):
            param.setResult(None)

            m = re.match("cactus://(.+)/(.+)", data)
            if not m:
                return

            plugin_id = m.group(1)
            parsed = urlparse(m.group(2))
            cmd = parsed.path
            kwargs = {} if not parsed.query else {
                k: v[0]
                for k, v in parse_qs(parsed.query).items()
            }

            if plugin_id in self._plugins:
                plugin = self._plugins[plugin_id]
                if cmd in plugin._inline_handlers:
                    func = plugin._inline_handlers[cmd]
                    try:
                        if is_long and not getattr(func, "__support_long__", False):
                            return

                        func(
                            CactusUtils.Inline.CallbackParams(
                                cell=cell,
                                message=cell.getMessageObject(),
                                button=btn,
                                is_long=is_long
                            ), **kwargs
                        )
                    except:
                        plugin.error(f"Inline hook {func.__name__} error: {traceback.format_exc()}")
                        self.utils.show_info(f"Inline button callback error. See logs for more details.")

    def __search_plugins(self):
        pc = PluginsController.getInstance()
        menu_items_by_id = list(get_private_field(pc, "menuItemsById").values().toArray())
        for plugin in [
            p
            for p in CactusUtils.all_plugins()
        ]:
            try:
                path = pc.getPluginPath(plugin.id)
                if (
                    plugin.id in PluginsData.plugins or (
                            getattr(getattr(getattr(plugin, "utils", None), "__class__", None), "__name__", None) == "CactusUtils"
                            or issubclass(plugin.__class__, CactusUtils.Plugin)
                    ) or (
                            is_cactus_plugin(path)
                    )
                ):
                    if plugin.id not in self._plugins:
                        self.utils.initialize_plugin(plugin)

                    if plugin.id not in PluginsData.plugins:
                        PluginsData.parse(path, plugin.id)

                for item in menu_items_by_id:
                    if item.pluginId == plugin.id:
                        plugin_items = self._menu_items.get(plugin.id, {})
                        plugin_items[item.menuType] = list(set(plugin_items.get(item.menuType, []) + [item.itemId]))
                        self._menu_items[plugin.id] = plugin_items
                        self._menu_items_by_id[item.itemId] = MenuItemInfo(item, True)
            except Exception as e:
                self.error(f"__search_plugins: {e}\n{traceback.format_exc()}")

    def create_settings(self):
        return [
            Header(text=self.string("settings_commands_header")),
            Switch(
                key="show_settings_menu_item",
                text=self.string("show_settings_in_menu"),
                default=False,
                icon="msg_settings_14",
                on_change=lambda value: self._add_menu_items()
            ),
            Input(
                key="prefix",
                text=self.string("settings_command_prefix_label"),
                default=".",
                icon="msg_limit_stories",
            ),
            Input(
                key="language",
                text=self.string("settings_language_label"),
                default=None,
                icon="",
                on_change=lambda lang: self._add_menu_items(lang),
                subtext=self.string("settings_language_subtext")
            ),
            Switch(
                key="show_cmds_description",
                text=self.string("settings_show_cmds_description_label"),
                default=True,
                icon="msg_info",
                subtext=self.string("settings_show_cmds_description_subtext")
            ),
            Divider(),
            Switch(
                key="global_premium",
                text=self.string("settings_global_premium_label"),
                default=True,
                icon="menu_feature_premium",
                subtext=self.string("settings_global_premium_subtext")
            ),
            Selector(
                key="emoji_type",
                text=self.string("settings_emoji_type_label"),
                default=1,
                items=["Colored", "Adaptive"],
                icon="msg_premium_icons"
            ),
        ]

    @uri("hello")
    def hello(self):
        self.utils.show_info("Hello, bro!")

    @CactusUtils.Inline.on_click("openPluginSettings")
    def open_plugin_settings_btn(self, _, id: str):
        self.utils.open_plugin_settings(id)

    @message_uri("setPluginCommandEnabled")
    def set_plugin_command_enabled(
            self, params: CactusUtils.UriCallback,
            id: str, name: str, c: str, orig: Optional[str] = None,
            zwy: str = "0"
    ):
        if not params.message.isOutOwner():
            return

        disabled = self.get("disabled" if zwy != "1" else "zwy_disabled", {})
        cmds = disabled.get("commands", {})
        cmds[c] = not cmds.get(c, False)

        disabled["commands"] = {
            k: v
            for k, v in cmds.items()
            if v
        }
        self.set("disabled" if zwy != "1" else "zwy_disabled", disabled)

        if zwy == "1":
            import zwylib
            listeners = zwylib.command_manager.get_dispatcher(id).listeners
            if id not in self._zwy_commands_disabled:
                self._zwy_commands_disabled[id] = {}
            if " " in c:
                if not cmds[c]:
                    if c in self._zwy_commands_disabled[id]:
                        self.__hooks.orig_methods["zwylib._register_command"](*self._zwy_commands_disabled[id][c])
                        del self._zwy_commands_disabled[id][c]
                else:
                    _temp = listeners
                    pre_cmd = None
                    parts = c.split(" ")
                    for i, cc in enumerate(parts, 1):
                        if i == len(parts):
                            cmd = pre_cmd.subcommands.pop(cc)
                            self._zwy_commands_disabled[id][c] = (pre_cmd.subcommands, cmd, id, True)
                        elif i == len(parts) - 1:
                            pre_cmd = _temp[cc] if i == 1 else _temp.subcommands[cc]
                        else:
                            _temp = _temp[cc] if i == 1 else _temp.subcommands[cc]
            else:
                if not cmds[c]:
                    if c in self._zwy_commands_disabled[id]:
                        self.__hooks.orig_methods["zwylib._register_command"](*self._zwy_commands_disabled[id][c])
                        del self._zwy_commands_disabled[id][c]
                else:
                    cmd = listeners.pop(c)
                    self._zwy_commands_disabled[id][c] = (listeners, cmd, id, False)

        if orig:
            self.utils.show_info(self.string("alias_enabled" if not cmds[c] else "alias_disabled", c, orig, name))
        else:
            self.utils.show_info(self.string("command_enabled" if not cmds[c] else "command_disabled", c, name))

        text, markup, _ = self.help_query(id, edit_mode=True)
        params.edit(text, markup=markup)

    @message_uri("setPluginMenuItemEnabled")
    def set_plugin_menu_item_enabled(self, params: CactusUtils.UriCallback, id: str, name: str, item_id: str):
        if not params.message.isOutOwner() or not id or not name or not item_id:
            return

        item = self._menu_items_by_id.get(item_id)
        if not item:
            return

        pc = PluginsController.getInstance()
        menuItemsById = get_private_field(pc, "menuItemsById")
        menuItemsByMenuType = get_private_field(pc, "menuItemsByMenuType")

        item.enabled = not item.enabled
        if item.enabled:
            def _compute(currentCowList):
                try:
                    if currentCowList is None:
                        nl = ArrayList()
                    else:
                        nl = ArrayList(currentCowList)
                        nl.remove(item.item)

                    nl.add(item.item)
                    nl.sort(Comparator.comparingInt(CactusUtils.gen(ToIntFunction, "applyAsInt", return_value=True)(lambda x: x.priority)).reversed())
                    return CopyOnWriteArrayList(nl)
                except:
                    self.error(f"[setPluginMenuItemEnabled]: {traceback.format_exc()}")
                    return currentCowList

            menuItemsById.put(item_id, item.item)
            menuItemsByMenuType.compute(item.item.menuType, CactusUtils.gen(BiFunction, "apply", return_value=True)(lambda x, y: _compute(y)))
            run_on_ui_thread(lambda: NotificationCenter.getInstance(UserConfig.selectedAccount).postNotificationName(NotificationCenter.pluginMenuItemsUpdated))
        else:
            pc.removeMenuItem(id, item_id)

        self.utils.show_info(self.string("menu_item_enabled" if item.enabled else "menu_item_disabled", item.item.text, name))
        text, markup, _ = self.help_query(id, edit_mode=True)
        params.edit(text, markup=markup)

    @CactusUtils.Inline.on_click("resetPluginChanges")
    def reset_plugin_changes(self, params: CactusUtils.Inline.CallbackParams, id: str, name: str, zwy: str = "0"):
        if not id or not params.message.isOutOwner():
            return
        frag = get_last_fragment()
        if not frag:
            return

        activity = frag.getParentActivity()
        if not activity:
            return

        def _cb(*_):
            try:
                if zwy == "1":
                    import zwylib
                    dispatcher = zwylib.command_manager.get_dispatcher(id)

                    override = self.get("zwy-override", {})
                    commands = override.get("commands", {})
                    cmds = []

                    for orig, new in commands.copy().items():
                        if " " in new:
                            parts = new.split(" ")
                            if parts[0] in dispatcher.listeners:
                                cmd = None
                                for p1, p2 in zip(parts[:-1], orig.split(" ")[:-1]):
                                    if cmd:
                                        cmd = cmd.subcommands[p1] if p1 in cmd.subcommands else cmd.subcommands.get(p2, None)
                                    else:
                                        cmd = dispatcher.listeners[p1] if p1 in dispatcher.listeners else dispatcher.listeners.get(p2, None)

                                if cmd:
                                    cmd_ = cmd.subcommands.pop(parts[-1])
                                    cmd_.name = orig.split(" ")[-1]
                                    cmd.subcommands[orig.split(" ")[-1]] = cmd_
                                    cmds.append(new)
                                    del commands[orig]
                        else:
                            if new in dispatcher.listeners:
                                cmd = dispatcher.listeners.pop(new)
                                cmd.name = orig
                                dispatcher.listeners[orig] = cmd
                                del commands[orig]

                    override["commands"] = commands
                    self.set("zwy-override", override)

                    disabled = self.get("zwy_disabled", {})
                    _cmds = disabled.get("commands", {})
                    for c in cmds:
                        if c in _cmds:
                            del _cmds[c]
                            if c in self._zwy_commands_disabled.get(id, {}):
                                self.__hooks.orig_methods["zwylib._register_command"](*self._zwy_commands_disabled[id][c])
                                del self._zwy_commands_disabled[id][c]

                    for k, v in self._zwy_commands_disabled.get(id, {}).copy().items():
                        if v[2] == id:
                            self.__hooks.orig_methods["zwylib._register_command"](*v)
                            del self._zwy_commands_disabled[k]
                            if k in _cmds:
                                del _cmds[k]

                    disabled["commands"] = _cmds
                    self.set("zwy_disabled", disabled)
                else:
                    override = self.get("override", {})
                    if id in override:
                        del override[id]
                        self.set("override", override)

                    disabled = self.get("disabled", {})
                    _cmds = disabled.get("commands", {})

                    cmds = self._commands2.pop(id, [])
                    for c in cmds:
                        del self._commands[c]
                        if c in _cmds:
                            del _cmds[c]

                        for k, v in self._aliases.copy().items():
                            if v == c:
                                del self._aliases[k]

                        for k, v in self._original_commands.copy().items():
                            if v == c or k == c:
                                del self._original_commands[k]

                        for k, v in self._original_aliases.copy().items():
                            if v == c or k == c:
                                del self._original_aliases[k]

                    disabled["commands"] = _cmds
                    self.set("disabled", disabled)

                    self._load_plugin_commands(id)

                self.utils.show_info(self.string("reset_done"))
                text, markup, _ = self.help_query(id, edit_mode=True)
                params.edit(text, markup=markup, frag=frag)
            except:
                self.error(traceback.format_exc())

        builder = (
            AlertDialogBuilder(activity, resources_provider=frag.getResourceProvider())
            .set_title(self.string("reset_changes_btn").strip())
            .set_message(self.string("reset_changes_alert", name))
            .set_positive_button(self.string("reset"), _cb)
            .set_negative_button(self.string("cancel"), None)
            .make_button_red(AlertDialogBuilder.BUTTON_POSITIVE)
        )
        builder.show()

    @message_uri("editPluginCommand")
    def edit_plugin_command(self, params: CactusUtils.UriCallback, id: str, name: str, cur: str, is_alias: bool = False, zwy: str = "0"):
        if not params.message.isOutOwner():
            return

        frag = get_last_fragment()
        if not frag:
            return

        activity = frag.getParentActivity()
        if not activity:
            return

        ALLOWED = (
                "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_"
                + "абвгдеёжзийклмнопрстуфхцчшщъыьэюяАБВГДЕЁЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ"
        )

        resources_provider = frag.getResourceProvider()

        b = AlertDialog.Builder(activity, resources_provider)
        b.setTitle(name)

        container = LinearLayout(activity)
        container.setOrientation(LinearLayout.VERTICAL)
        red = Theme.getColor(Theme.key_text_RedRegular, resources_provider)

        textView = TextView(activity)
        textView.setText(self.string("edit_alias_subtext" if is_alias else "edit_cmd_subtext"))
        textView.setTextColor(Theme.getColor(Theme.key_dialogTextBlack, resources_provider))
        textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16)
        container.addView(textView, LayoutHelper.createLinear(-1, -2, 24, 5, 24, 12))

        is_error = False

        def handle_error(s: bool, text: Optional[str] = None):
            if s:
                editText.setLineColors(red, red, red)
                errorView.setText(text)
                errorView.setVisibility(View.VISIBLE)
            else:
                editText.setLineColors(
                    Theme.getColor(Theme.key_windowBackgroundWhiteInputField, resources_provider),
                    Theme.getColor(Theme.key_windowBackgroundWhiteInputFieldActivated, resources_provider),
                    red
                )
                errorView.setText("")
                errorView.setVisibility(View.GONE)

            nonlocal is_error
            is_error = s

        def after_text_changed(s):
            ss = str(s)
            if zwy == "1" and " " in cur:
                pre = " ".join(cur.split(" ")[:-1]) + " "
                if not ss.startswith(pre):
                    s.clear()
                    s.append(pre)

        editText = EditTextBoldCursor(activity)
        editText.lineYFix = True
        editText.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 18)
        current = cur
        editText.setText(current)
        editText.setTextColor(Theme.getColor(Theme.key_dialogTextBlack, resources_provider))
        editText.setHintColor(Theme.getColor(Theme.key_groupcreate_hintText, resources_provider))
        editText.setHintText(self.string("enter_command" if not is_alias else "enter_alias"))
        editText.setFocusable(True)
        editText.setInputType(InputType.TYPE_CLASS_TEXT)
        editText.setCursorColor(Theme.getColor(Theme.key_windowBackgroundWhiteInputFieldActivated, resources_provider))
        editText.setLineColors(0, 0, 0)
        editText.addTextChangedListener(
            CactusUtils.gen2(TextWatcher, **{
                "onTextChanged": lambda text, *_: handle_error(zwy != "1" and any([char not in ALLOWED for char in str(text)]), self.string("only_letters_and_numbers")),
                "beforeTextChanged": lambda *_: None,
                "afterTextChanged": lambda s: after_text_changed(s)
            })()
        )
        editText.setBackgroundDrawable(None)
        editText.setPadding(0, AndroidUtilities.dp(6), 0, AndroidUtilities.dp(6))
        container.addView(editText, LayoutHelper.createLinear(-1, -2, 24, 0, 24, 5))

        errorView = TextView(activity)
        errorView.setTextColor(red)
        errorView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14)
        container.addView(errorView, LayoutHelper.createLinear(-1, -2, 24, 5, 24, 10))

        handle_error(False)

        def on_positive(d, _):
            if is_error:
                return

            new: str = editText.getText().toString().strip()
            if not new:
                return

            if new != current:
                if zwy != "1" and any([char not in ALLOWED for char in new]):
                    handle_error(True, self.string("only_letters_and_numbers"))
                    return

                if zwy != "1":
                    if new in self._commands:
                        handle_error(True, self.string("command_exists"))
                        return

                    if new in self._aliases:
                        handle_error(True, self.string("alias_exists"))
                        return

                    orig = (self._original_commands if not is_alias else self._original_aliases).get(current, None)
                    if orig is None:
                        orig = current

                    overrided = self.get("override", {})
                    p = overrided.get(id, {})
                    lo = p.get("aliases" if is_alias else "commands", {})
                    _setted = False
                    for k, v in lo.copy().items():
                        if v == orig:
                            lo[k] = new
                            _setted = True
                            break

                    if not _setted:
                        lo[orig] = new

                    p["aliases" if is_alias else "commands"] = lo
                    overrided[id] = p
                    self.set("override", overrided)

                    if not is_alias:
                        self._aliases = {
                            a: new if c == current else c
                            for a, c in self._aliases.items()
                        }
                        self._commands[new] = self._commands.pop(current)
                        self._commands2[id].insert(self._commands2[id].index(current), new)
                        self._commands2[id].remove(current)
                    else:
                        self._aliases[new] = self._aliases.pop(current)

                    disabled = self.get("disabled", {})
                    cmds = disabled.get("commands", {})
                    if current in cmds:
                        cmds[new] = cmds.pop(current)
                        disabled["commands"] = cmds
                        self.set("disabled", disabled)

                    if current in self._original_commands:
                        self._original_commands[new] = self._original_commands.pop(current)
                    elif current in self._original_aliases:
                        self._original_aliases[new] = self._original_aliases.pop(current)
                else:
                    with suppress(Exception):
                        import zwylib

                        if " " not in current:
                            cmd = zwylib.command_manager.get_dispatcher(id).listeners.pop(current, None)
                            if not cmd:
                                d.dismiss()
                                return

                            cmd.name = new
                            zwylib.command_manager.get_dispatcher(id).listeners[new] = cmd
                            overrided = self.get("zwy-override", {})
                            if "commands" not in overrided:
                                overrided["commands"] = {}
                            [orig] = [i for i, j in overrided["commands"].items() if j == current] or [current]
                            overrided["commands"][orig] = new
                            self.set("zwy-override", overrided)

                            disabled = self.get("zwy_disabled", {})
                            cmds = disabled.get("commands", {})
                            if current in cmds:
                                cmds[new] = cmds.pop(current)
                                disabled["commands"] = cmds
                                self.set("zwy_disabled", disabled)
                        else:
                            dispatcher = zwylib.command_manager.get_dispatcher(id)
                            listeners = dispatcher.listeners
                            _temp = listeners
                            pre_cmd = None
                            parts = current.split(" ")
                            for i, cc in enumerate(parts, 1):
                                if i == len(parts):
                                    cmd = pre_cmd.subcommands.pop(cc)
                                    cmd.name = new.split(" ")[-1]
                                    pre_cmd.subcommands[new.split(" ")[-1]] = cmd

                                    overrided = self.get("zwy-override", {})
                                    if "commands" not in overrided:
                                        overrided["commands"] = {}
                                    [orig] = [i for i, j in overrided["commands"].items() if j == current] or [current]
                                    overrided["commands"][orig] = new
                                    self.set("zwy-override", overrided)

                                    disabled = self.get("zwy_disabled", {})
                                    cmds = disabled.get("commands", {})
                                    if current in cmds:
                                        cmds[new] = cmds.pop(current)
                                        disabled["commands"] = cmds
                                        self.set("zwy_disabled", disabled)
                                elif i == len(parts) - 1:
                                    pre_cmd = _temp[cc] if i == 1 else _temp.subcommands[cc]
                                else:
                                    _temp = _temp[cc] if i == 1 else _temp.subcommands[cc]

                text, markup, _ = self.help_query(id, edit_mode=True)
                params.edit(text, markup=markup)

            d.dismiss()

        b.makeCustomMaxHeight()
        b.setView(container)
        b.setWidth(AndroidUtilities.dp(292))

        Listener = CactusUtils.gen(AlertDialog.OnButtonClickListener, "onClick")
        b.setPositiveButton(self.string("done"), Listener(on_positive))
        b.setNegativeButton(self.string("cancel"), Listener(lambda d, _: d.dismiss()))

        dialog = b.create()
        dialog.setOnDismissListener(CactusUtils.gen(DialogInterface.OnDismissListener, "onDismiss")(lambda _: AndroidUtilities.hideKeyboard(editText)))

        def _on_show():
            editText.requestFocus()
            editText.setSelection(editText.length())
            AndroidUtilities.showKeyboard(editText)

        dialog.setOnShowListener(CactusUtils.gen(DialogInterface.OnShowListener, "onShow")(lambda _: _on_show()))
        dialog.setDismissDialogByButtons(False)
        frag.showDialog(dialog)

    @CactusUtils.Inline.on_click("editZwyPrefix")
    def edit_zwylib_prefix(self, params, id: str, name: str, edit_mode):
        with suppress(Exception):
            if not params.message.isOutOwner():
                return

            frag = get_last_fragment()
            if not frag:
                return

            activity = frag.getParentActivity()
            if not activity:
                return

            import zwylib

            resources_provider = frag.getResourceProvider()

            b = AlertDialog.Builder(activity, resources_provider)
            b.setTitle(name)

            container = LinearLayout(activity)
            container.setOrientation(LinearLayout.VERTICAL)
            red = Theme.getColor(Theme.key_text_RedRegular, resources_provider)

            textView = TextView(activity)
            textView.setText(self.string("edit_zwylib_prefix"))
            textView.setTextColor(Theme.getColor(Theme.key_dialogTextBlack, resources_provider))
            textView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16)
            container.addView(textView, LayoutHelper.createLinear(-1, -2, 24, 5, 24, 12))

            is_error = False

            def handle_error(s: bool, text: Optional[str] = None):
                if s:
                    editText.setLineColors(red, red, red)
                    errorView.setText(text)
                    errorView.setVisibility(View.VISIBLE)
                else:
                    editText.setLineColors(
                        Theme.getColor(Theme.key_windowBackgroundWhiteInputField, resources_provider),
                        Theme.getColor(Theme.key_windowBackgroundWhiteInputFieldActivated, resources_provider),
                        red
                    )
                    errorView.setText("")
                    errorView.setVisibility(View.GONE)

                nonlocal is_error
                is_error = s

            editText = EditTextBoldCursor(activity)
            editText.lineYFix = True
            editText.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 18)
            current = zwylib.command_manager.get_dispatcher(id).prefix
            editText.setText(current)
            editText.setTextColor(Theme.getColor(Theme.key_dialogTextBlack, resources_provider))
            editText.setHintColor(Theme.getColor(Theme.key_groupcreate_hintText, resources_provider))
            editText.setHintText(self.string("enter_prefix"))
            editText.setFocusable(True)
            editText.setInputType(InputType.TYPE_CLASS_TEXT)
            editText.setCursorColor(
                Theme.getColor(Theme.key_windowBackgroundWhiteInputFieldActivated, resources_provider))
            editText.setLineColors(0, 0, 0)
            editText.addTextChangedListener(
                CactusUtils.gen2(TextWatcher, **{
                    "onTextChanged": lambda text, *_: handle_error(" " in str(text),
                                                                   self.string("only_letters_and_numbers")),
                    "beforeTextChanged": lambda *_: None,
                    "afterTextChanged": lambda *_: None
                })()
            )
            editText.setBackgroundDrawable(None)
            editText.setPadding(0, AndroidUtilities.dp(6), 0, AndroidUtilities.dp(6))
            container.addView(editText, LayoutHelper.createLinear(-1, -2, 24, 0, 24, 5))

            errorView = TextView(activity)
            errorView.setTextColor(red)
            errorView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14)
            container.addView(errorView, LayoutHelper.createLinear(-1, -2, 24, 5, 24, 10))

            handle_error(False)

            def on_positive(d, _):
                if is_error:
                    return

                new: str = editText.getText().toString().strip()
                if not new:
                    return

                if new != current:
                    if " " in new:
                        handle_error(True, self.string("only_letters_and_numbers"))
                        return

                    zwylib.command_manager.get_dispatcher(id).set_prefix(new)

                    text, markup, _ = self.help_query(id, edit_mode=True if edit_mode == "True" else False)
                    params.edit(text, markup=markup)

                d.dismiss()

            b.makeCustomMaxHeight()
            b.setView(container)
            b.setWidth(AndroidUtilities.dp(292))

            Listener = CactusUtils.gen(AlertDialog.OnButtonClickListener, "onClick")
            b.setPositiveButton(self.string("done"), Listener(on_positive))
            b.setNegativeButton(self.string("cancel"), Listener(lambda d, _: d.dismiss()))

            dialog = b.create()
            dialog.setOnDismissListener(CactusUtils.gen(DialogInterface.OnDismissListener, "onDismiss")(
                lambda _: AndroidUtilities.hideKeyboard(editText)))

            def _on_show():
                editText.requestFocus()
                editText.setSelection(editText.length())
                AndroidUtilities.showKeyboard(editText)

            dialog.setOnShowListener(CactusUtils.gen(DialogInterface.OnShowListener, "onShow")(lambda _: _on_show()))
            dialog.setDismissDialogByButtons(False)
            frag.showDialog(dialog)

    @CactusUtils.Inline.on_click("checkPluginUpdates")
    def check_plugin_updates(self, params, id: str):
        ...

    @CactusUtils.Inline.on_click("setPluginEnabled")
    @message_uri("setPluginEnabled")
    def uri_set_plugin_enabled(self, params, id: str, offset: int = 0, isAllList: str = "true"):
        if not id or not params.message.isOutOwner():
            return

        def _fn2():
            text, markup, _ = self.help_query("" if isAllList == "true" else id, int(offset))
            run_on_ui_thread(lambda: params.edit(text, markup=markup))

        self._enable_plugin(id, _fn2)

    def _enable_plugin(self, plugin_id: str, on_enable) -> bool:
        plugin: Optional[BasePlugin] = ([
                                            p
                                            for p in CactusUtils.all_plugins()
                                            if p.id == plugin_id
                                        ] or [None])[0]
        if plugin:
            def _callback(error: str):
                if error:
                    self.error(f"Error enabling plugin {plugin_id}: " + error)
                    self.utils.show_error(error)
                else:
                    self.utils.show_info(f"{plugin.name} " + self.string('enabled' if plugin.enabled else 'disabled'))
                    on_enable()

            PluginsController.getInstance().setPluginEnabled(
                plugin.id,
                not plugin.enabled,
                self.utils.Callback(_callback)
            )
        else:
            self.utils.show_error(self.string("plugin_not_found", plugin_id))
            return False

    @CactusUtils.Inline.on_click("sendPluginFile")
    def send_plugin_file_btn(self, params, id: str, name: str, version: str = None):
        if not id or not params.message.isOutOwner():
            return

        with open(PluginsController.getInstance().getPluginPath(id), "rb") as f:
            path = self.utils.FileSystem.write_temp_file(f"{name}_{version or '-'}.plugin", f.read(), delete_after=35)

        self.answer_file(params.message.getDialogId(), path, None, editingMessageObject=params.message)

    @CactusUtils.Inline.on_click("deletePluginAlert")
    def delete_plugin_alert(self, params: CactusUtils.Inline.CallbackParams, id: str, name: str):
        if not id or not params.message.isOutOwner():
            return
        frag = get_last_fragment()
        if not frag:
            return

        activity = frag.getParentActivity()
        if not activity:
            return

        def _cb(*_):
            def _on_delete(error):
                if error:
                    return self.utils.show_error(error)

                self.utils.show_info(self.string("plugin_deleted", name))
                text, markup, _ = self.help_query("_", 0)
                params.edit(text, markup=markup, fragment=frag)

            PluginsController.getInstance().deletePlugin(id, CactusUtils.Callback(_on_delete))

        builder = (
            AlertDialogBuilder(activity, resources_provider=frag.getResourceProvider())
            .set_title(self.string("delete_plugin"))
            .set_message(self.string("delete_plugin_confirm", name))
            .set_positive_button(self.string("delete"), _cb)
            .set_negative_button(self.string("cancel"), None)
            .make_button_red(AlertDialogBuilder.BUTTON_POSITIVE)
        )
        builder.show()

    @message_uri("setHelpSortBy")
    def uri_set_help_sort_by(self, params: CactusUtils.UriCallback, by: str, **_):
        if not by or not params.message.isOutOwner():
            return

        self.set(
            "help_sort_up",
            self.get("help_sort_up")
            if self.get("help_sort_by", "name") != by
            else not self.get("help_sort_up", True)
        )
        self.set("help_sort_by", by)

        text, markup, _ = self.help_query("_", 0)
        params.edit(text, markup=markup)

    def help_query(self, query: Optional[str] = None, offset: int = 0, limit: int = 20, edit_mode: bool = False):
        try:
            cactus_list = self._plugins.copy()
            for p in self.utils.get_cactus_plugins():
                if p.id not in self._plugins:
                    cactus_list[p.id] = p

            text = None
            markup = None
            prefix = self.utils.prefix()
            rnd = uuid4().hex

            other_plugins: Dict[str, BasePlugin] = {
                p.id: p
                for p in CactusUtils.all_plugins()
                if p.id not in cactus_list
            }
            zwylib_commands = self.utils.zwy_commands()

            if not query or query == "_":
                sort_by, up = self.get("help_sort_by", "name"), self.get("help_sort_up", True)

                def key(_p):
                    return (
                        _p.name
                        if sort_by == "name"
                        else self.get_plugin_filesize(_p.id)
                        if sort_by == "filesize"
                        else self.get_plugin_modified_date(_p.id)
                        if sort_by == "updatedate"
                        else ""
                    )

                plugins_list = [
                                   PluginInfo(self, p, True)
                                   for p in sorted(cactus_list.values(), key=key, reverse=not up)
                               ] + [
                                   PluginInfo(self, p, False, p.id in zwylib_commands)
                                   for p in sorted(other_plugins.values(), key=key, reverse=not up)
                               ]
                current_plugins_list = plugins_list[offset:offset + limit]

                text = ""

                text += self.emoji("sort") + self.string("sort")
                text += "\n" + " | ".join([
                    ("<b>" if _sort_by == sort_by else "") + self.string(
                        "sort_by_" + _sort_by,
                        self.utils.MessageUri.create(self, "setHelpSortBy", by=_sort_by, rnd=rnd),
                        ("🔺" if up else "🔻") if _sort_by == sort_by else ""
                    ) + ("</b>" if _sort_by == sort_by else "")
                    for _sort_by in ["name", "filesize", "updatedate"]
                ]) + "\n\n"

                cactus = [p for p in current_plugins_list if p.is_cactuslib_plugin]
                other = [p for p in current_plugins_list if not p.is_cactuslib_plugin]
                if cactus:
                    text += self.string("cactus_plugins", self.emoji("cactus_title"), len(cactus),
                                        "\n".join([p.format_in_list(offset) for p in cactus]))
                if other:
                    text += self.string("other_plugins", self.emoji("base_title"), len(other),
                                        "\n".join([p.format_in_list(offset) for p in other]))

                row = []
                if offset > 0:
                    row.append(self.utils.Inline.Button(
                        self.string("prev_page"),
                        callback_data=self.utils.Inline.CallbackData(
                            self.id,
                            "openPluginHelp",
                            id="_",
                            offset=(offset - limit) if offset - limit >= 0 else 0
                        ),
                    ))

                if offset + limit < len(plugins_list):
                    row.append(self.utils.Inline.Button(
                        self.string("next_page"),
                        callback_data=self.utils.Inline.CallbackData(
                            self.id,
                            "openPluginHelp",
                            id="_",
                            offset=(offset + limit)
                        ),
                    ))

                if row:
                    markup = row
            else:
                if query in cactus_list:
                    text, markup = PluginInfo(self, cactus_list[query], True).info(rnd, prefix, edit_mode)
                elif query in other_plugins:
                    zwycmds = zwylib_commands.get(other_plugins[query].id, {})
                    zwycmds.update({
                        name: items[1]
                        for name, items in self._zwy_commands_disabled.get(other_plugins[query].id, {}).items()
                    })
                    text, markup = PluginInfo(
                        self, other_plugins[query],
                        False, bool(zwycmds)
                    ).info(rnd, prefix, edit_mode, zwycmds)
                else:
                    for plugin in cactus_list.values():
                        _cmds = self._commands2.get(plugin.id, [])
                        names = [
                                    plugin.name.lower(),
                                    plugin.id.lower(),
                                ] + list(map(lambda x: x.lower(), self._commands2.get(plugin.id, []))) + [
                                    a.lower()
                                    for a, t in self._aliases.items()
                                    if t in _cmds
                                ]

                        if query.lower() in names:
                            text, markup = PluginInfo(self, plugin, True).info(rnd, prefix, edit_mode)
                            break

                    if not text:
                        for plugin in other_plugins.values():
                            names = [
                                plugin.name.lower(),
                                plugin.id.lower(),
                            ]

                            if query.lower() in names:
                                zwycmds = zwylib_commands.get(plugin.id, {})
                                zwycmds.update({
                                    name: items[1]
                                    for name, items in self._zwy_commands_disabled.get(plugin.id, {}).items()
                                })
                                text, markup = PluginInfo(self, plugin, False, bool(zwycmds)).info(rnd, prefix, edit_mode, zwycmds)
                                break

            return text, markup, rnd
        except:
            self.error(traceback.format_exc())
            return None, None, None

    @message_uri("openPluginHelp")
    @CactusUtils.Inline.on_click("openPluginHelp")
    def uri_open_plugin_help(self, params: Union[CactusUtils.UriCallback, CactusUtils.Inline.CallbackParams], id: Optional[str] = None, offset: int = 0,
                             edit_mode: str = "False"):
        if id is None or not params.message.isOutOwner():
            return

        text, markup, _ = self.help_query(id, int(offset), 20, edit_mode == "True")
        if not text:
            return

        params.edit(text, markup=markup)

    @command(doc="help_cmd")
    def chelp(self, cmd: CactusUtils.Command):
        args = cmd.raw_args
        text, markup, rnd = self.help_query(args)

        if not text:
            self.utils.show_error(self.string("not_found", args))
        else:
            cmd.answer(text, markup=markup, uid=rnd)

        return HookResult(strategy=HookStrategy.CANCEL)

    @command(doc="logs_cmd")
    def logs(self, cmd: CactusUtils.Command):
        _time = None
        lvl = None
        plugin_id = None
        contains = (None, None)

        if cmd.args:
            for index, arg in enumerate(cmd.args):
                if arg in LVLS:
                    lvl = arg
                elif arg.isdigit():
                    _time = int(arg)
                elif arg == "-c":
                    contains = (index + 1, cmd.args[index + 1])
                elif index != contains[0]:
                    plugin_id = arg

        logs = self.utils.get_logs(plugin_id, _time, lvl, as_list=True)

        if contains[0]:
            logs = [log for log in logs if contains[1] in log]

        if not logs:
            BulletinHelper.show_error(self.string("no_logs"))
            return

        slogs = "\n".join(logs)

        lvl = lvl or "-"

        caption = self.string(
            "logs",
            lvl=lvl,
            id=plugin_id,
            contains=(
                self.string("contains", contains[1])
                if contains[1]
                else ""
            ),
            last_logs=("\n".join([self.utils.escape_html(i) for i in logs[-7:]]))[-700:]
        )

        file_path = self.utils.FileSystem.write_temp_file(
            (f"{plugin_id}_" if plugin_id else "")
            + (f"{lvl}_" if lvl != "-" else "")
            + f"logs.txt",
            slogs.encode("utf-8"),
            delete_after=30
        )

        self.answer_file(cmd.params.peer, file_path, caption, replyToMsg=cmd.params.replyToMsg,
                         replyToTopMsg=cmd.params.replyToTopMsg)

        return HookResult(strategy=HookStrategy.CANCEL)

    # noinspection PyUnresolvedReferences
    @staticmethod
    def _eval(code: str, globs, **kwargs):
        locs = {}
        globs = globs.copy()
        global_args = "_globs"
        while global_args in globs.keys():
            global_args = "_" + global_args
        kwargs[global_args] = {}
        for glob in ["__name__", "__package__"]:
            kwargs[global_args][glob] = globs[glob]

        root = ast.parse(code, "exec")
        code = root.body

        ret_name = "_ret"
        ok = False
        while True:
            if ret_name in globs.keys():
                ret_name = "_" + ret_name
                continue
            for node in ast.walk(root):
                if isinstance(node, ast.Name) and node.id == ret_name:
                    ret_name = "_" + ret_name
                    break
                ok = True
            if ok:
                break

        if not code:
            return None

        if not any(isinstance(node, ast.Return) for node in code):
            for i in range(len(code)):
                if isinstance(code[i], ast.Expr):
                    if i == len(code) - 1 or not isinstance(code[i].value, ast.Call):
                        code[i] = ast.copy_location(
                            ast.Expr(
                                ast.Call(
                                    func=ast.Attribute(
                                        value=ast.Name(id=ret_name, ctx=ast.Load()),
                                        attr="append",
                                        ctx=ast.Load()
                                    ),
                                    args=[code[i].value],
                                    keywords=[]
                                )
                            ), code[-1]  # type: ignore
                        )
        else:
            for node in code:
                if isinstance(node, ast.Return):
                    node.value = ast.List(elts=[node.value], ctx=ast.Load())

        code.append(ast.copy_location(ast.Return(value=ast.Name(id=ret_name, ctx=ast.Load())), code[-1]))  # type: ignore

        # globals().update(**<global_args>)
        glob_copy = ast.Expr(
            ast.Call(
                func=ast.Attribute(
                    value=ast.Call(
                        func=ast.Name(id="globals", ctx=ast.Load()),
                        args=[], keywords=[]
                    ),
                    attr="update",
                    ctx=ast.Load()),
                args=[],
                keywords=[ast.keyword(arg=None, value=ast.Name(id=global_args, ctx=ast.Load()))]
            )
        )
        ast.fix_missing_locations(glob_copy)
        code.insert(0, glob_copy)
        ret_decl = ast.Assign(targets=[ast.Name(id=ret_name, ctx=ast.Store())], value=ast.List(elts=[], ctx=ast.Load()))
        ast.fix_missing_locations(ret_decl)
        code.insert(1, ret_decl)
        args = []
        for a in list(map(lambda x: ast.arg(x, None), kwargs.keys())):
            ast.fix_missing_locations(a)
            args += [a]
        args = ast.arguments(args=[], vararg=None, kwonlyargs=args, kwarg=None, defaults=[],
                             kw_defaults=[None for _ in range(len(args))])
        args.posonlyargs = []
        fun = ast.FunctionDef(name="tmp", args=args, body=code, decorator_list=[], returns=None)
        ast.fix_missing_locations(fun)
        mod = ast.parse("")
        mod.body = [fun]
        comp = compile(mod, "<string>", "exec")

        exec(comp, {}, locs)

        r = locs["tmp"](**kwargs)
        i = 0
        while i < len(r) - 1:
            if r[i] is None:
                del r[i]
            else:
                i += 1
        if len(r) == 1:
            [r] = r
        elif not r:
            r = None
        return r

    def _eval_print(self, cmd, uuid: str):
        params: Optional[CactusUtils.Inline.CallbackParams] = None
        ll = []
        length = 0
        last_end = None
        thread = None
        edited_text = False
        result_text = ""
        editing = False
        last_text = None

        def text():
            return self.emoji("output") + " <b>Output</b>\n" + (
                    "<pre language='python'>" + "".join([self.utils.escape_html(i) for i in ll]) + "</pre>"
                    if length < 4000 and len(ll) < 50 else ""
            ) + (
                ("\n" + result_text)
                if result_text
                else ""
            )

        def loop():
            nonlocal editing, edited_text, length, ll, last_text
            while True:
                try:
                    if edited_text:
                        if editing:
                            while editing:
                                time.sleep(0.5)

                        editing = True
                        if params:
                            edited_text = False
                            txt = text()
                            if txt != last_text:
                                params.edit_message(txt)
                                last_text = txt

                            if length > 4000 or len(ll) >= 50:
                                path = self.utils.FileSystem.write_temp_file(f"output-{uuid}.txt", "".join(ll), mode="w", delete_after=60)
                                self.answer_file(params.message.getDialogId(), path, txt, editingMessageObject=params.message)
                            editing = False
                        else:
                            def on_sent(p):
                                nonlocal params, editing
                                params = p

                                p.edit_markup()
                                editing = False

                            edited_text = False
                            cmd.answer(text(), on_sent=on_sent)
                            if length > 4000 or len(ll) >= 50:
                                edited_text = True

                    time.sleep(2)
                except:
                    self.error(traceback.format_exc())

        def _print(*values, sep=' ', end='\n'):
            nonlocal last_end, thread, edited_text, length
            if not thread:
                thread = threading.Thread(target=loop)
                thread.daemon = True
                thread.start()

            string = sep.join([str(i) for i in values]) + end
            if last_end != "\n" and isinstance(last_end, str):
                ll.append(string)
                length += len(string)
            else:
                ll.append("[" + datetime.datetime.now().strftime("%H:%M:%S.%f") + "] " + string)
                length += len(ll[-1])

            last_end = end
            edited_text = True

        _print.get_params = lambda: (params, text(), length > 4000 or len(ll) >= 50)
        def set_result(result: str):
            nonlocal edited_text, result_text
            result_text = result
            edited_text = True

        _print.set_result = set_result

        return _print

    @command("eval", aliases=["e"], doc="eval_cmd")
    def eval_cmd(self, cmd: CactusUtils.Command):
        code = cmd.raw_args

        dont_send = False
        show_code = True

        if "-DS" in code:
            dont_send = True
            code = code.replace("-DS", "").strip()

        if not code:
            if r := cmd.params.replyToMsg:
                document = MessageObject.getDocument(r)
                if document:
                    if document.size > 1024 * 1024:
                        self.utils.show_error(self.string("file_too_large"))
                        return HookResult(strategy=HookStrategy.CANCEL)

                    file_loader = get_file_loader()
                    path = file_loader.getPathToAttach(document, True)

                    if not path or not path.exists():
                        file_loader.loadFile(document, "eval_file", FileLoader.PRIORITY_HIGH, 1)

                    k = 0
                    while k < 40 and not path.exists():
                        time.sleep(0.5)
                        k += 1

                    code = self.utils.FileSystem.get_file_content(path.getAbsolutePath(), "r") if path.exists() else "None"
                else:
                    code = r.messageOwner.message

                show_code = False

        def execute():
            try:
                orig_result = "..."
                print_func = self._eval_print(cmd, uuid4().hex)

                try:
                    orig_result = result = self._eval(code, globals(), **{
                        "self": self, "lib": self,
                        "params": cmd.params,
                        "r": cmd.params.replyToMsg,
                        "cmd": cmd,
                        "utils": self.utils,
                        "print": print_func,
                    })
                    if dont_send:
                        return

                    sresult = self.utils.escape_html(str(result))
                    if len(sresult) > 3072:
                        file_path = self.utils.FileSystem.write_temp_file(f"eval-result.txt", str(result).encode("utf-8"),
                                                                          delete_after=60)

                        params, _, _ = print_func.get_params()

                        run_on_ui_thread(lambda: self.answer_file(cmd.params.peer, file_path,
                                         self.string("eval_result_file", self.utils.escape_html(code) if show_code else "..."),
                                         replyToMsg=(cmd.params.replyToMsg if not params else params.message), replyToTopMsg=cmd.params.replyToTopMsg))
                        return

                    result = self.string("eval_result", self.utils.escape_html(str(sresult)),
                                         self.utils.escape_html(code) if show_code else "...")
                except Exception:
                    error = traceback.format_exc()
                    if len(error) > 3072:
                        file_path = self.utils.FileSystem.write_temp_file(f"eval-error.txt", error.encode("utf-8"),
                                                                          delete_after=60)
                        params, _, _ = print_func.get_params()
                        run_on_ui_thread(lambda: self.answer_file(
                            cmd.params.peer, file_path,
                            self.string("eval_error_file", self.utils.escape_html(code) if show_code else "..."),
                            replyToMsg=(cmd.params.replyToMsg if not params else params.message),
                            replyToTopMsg=cmd.params.replyToTopMsg
                        ))
                        return

                    result = self.string("eval_error", self.utils.escape_html(error),
                                         self.utils.escape_html(code) if show_code else "...")

                params, text, long = print_func.get_params()
                answer_func = cmd.answer
                if params:
                    print_func.set_result(result)
                    if not long:
                        result = text + "\n" + result

                    answer_func = params.edit_message

                run_on_ui_thread(lambda: answer_func(result, markup=[
                    [
                        {
                            "text": self.emoji("edit") + self.string("retry"),
                            "query": cmd.text
                        }
                    ],
                    [
                        {
                            "text": self.string("code"),
                            "copy": code or "None"
                        },
                        {
                            "text": self.string("result"),
                            "copy": str(orig_result)
                        }
                    ]
                ]))
            except:
                self.error(traceback.format_exc())

        self.run_on_queue(execute)
        return HookResult(strategy=HookStrategy.CANCEL)

    @command(doc="set_prefix_doc")
    def setprefix(self, cmd: CactusUtils.Command):
        if not cmd.raw_args:
            self.utils.show_error(self.string("no_args"))
            return HookResult(strategy=HookStrategy.CANCEL)

        if len(cmd.raw_args) != 1:
            self.utils.show_error(self.string("prefix_must_be_one_char"))
            return HookResult(strategy=HookStrategy.CANCEL)

        self.set_setting("prefix", cmd.raw_args)
        self.utils.show_info(self.string("prefix_set", cmd.raw_args))
        return HookResult(strategy=HookStrategy.CANCEL)

    @command("plf", doc="plf_doc")
    def send_plugin_file(self, cmd: CactusUtils.Command):
        if not cmd.raw_args:
            self.utils.show_error(self.string("no_args"))
            return HookResult(strategy=HookStrategy.CANCEL)

        _plugins = sorted([
            p
            for p in CactusUtils.all_plugins()
        ], key=lambda x: x.name.lower())

        query = cmd.raw_args.lower()

        for plugin in _plugins:
            if query in [plugin.name.lower(), plugin.id.lower()]:
                with open(PluginInfo(self, plugin).get_file_path(), "rb") as f:
                    path = self.utils.FileSystem.write_temp_file(f"{plugin.name}_{plugin.version or '-'}.plugin",
                                                                 f.read())

                self.answer_file(cmd.params.peer, path, self.string("plugin_file_sent", plugin=plugin,
                                                                    version=f"(v{plugin.version})" if plugin.version else ""),
                                 replyToMsg=cmd.params.replyToMsg, replyToTopMsg=cmd.params.replyToTopMsg)
                self.utils.FileSystem.delete_file_after(path, 30)
                return HookResult(strategy=HookStrategy.CANCEL)

        self.utils.show_error(self.string("plugin_not_found", query))
        return HookResult(strategy=HookStrategy.CANCEL)

    @command("cexport", doc="cexport_doc")
    def export_plugins(self, cmd: CactusUtils.Command):
        self._open_export_plugins_alert(ctx={
            "dialog_id": cmd.params.peer,
            "context": get_last_fragment().getParentActivity()
        })

        return HookResult(strategy=HookStrategy.CANCEL)

    def _open_export_plugins_alert(self, ctx):
        CactusIEAlert(self, ctx.get("context"), isExport=True, ctx=ctx).show_alert()

    def on_add_menu_item(self, plugin_id: str, item_data: dict, item_id: str):
        if not plugin_id or not item_data or not item_id or "validation_test" in plugin_id:
            return

        pc = PluginsController.getInstance()
        item = get_private_field(pc, "menuItemsById").get(item_id)
        if not item:
            return

        menu_type = item.menuType
        plugin_items = self._menu_items.get(plugin_id, {})
        plugin_items[menu_type] = list(set(plugin_items.get(menu_type, []) + [item_id]))
        self._menu_items[plugin_id] = plugin_items
        self._menu_items_by_id[item_id] = MenuItemInfo(item, True)

    def on_remove_menu_item(self, plugin_id: str, item_id: str, deleted: bool):
        if not plugin_id or not item_id or not deleted or "validation_test" in plugin_id:
            return

        if item_id in self._menu_items_by_id:
            self._menu_items_by_id[item_id].enabled = False


class Hooks:
    def __init__(self, lib: CactusLib):
        self.lib = lib
        self.orig_methods = {}

    def init(self) -> "Hooks":
        lib = self.lib
        PC_methods = {
            repr(i): i
            for i in PluginsController.getClass().getDeclaredMethods()
        }
        MsgDelegate = {
            repr(i): i
            for i in
            find_class("org.telegram.ui.ChatActivity$ChatMessageCellDelegate").getClass().getDeclaredMethods()
        }

        lib.hook_method(
            [
                i for i in (
                    find_class("org.telegram.messenger.browser.Browser")
                    .getClass()
                    .getDeclaredMethods()
                )
                if repr(i) == (
                    "<java.lang.reflect.Method 'public static void org.telegram.messenger.browser.Browser.openUrl"
                    "(android.content.Context,android.net.Uri,boolean,boolean,boolean,org.telegram.messenger.browser.Browser$Progress"
                    ",java.lang.String,boolean,boolean,boolean)'>"
                )
            ][0], BaseHook(lib, after=lambda param: self.browser_open_url(param)), 0
        )
        lib.hook_method(
            [
                i for i in (
                    find_class("org.telegram.messenger.AndroidUtilities")
                    .getClass()
                    .getDeclaredMethods()
                )
                if repr(i) == (
                    "<java.lang.reflect.Method 'public static boolean org.telegram.messenger.AndroidUtilities.openForView"
                    "(java.io.File,java.lang.String,java.lang.String,android.app.Activity,"
                    "org.telegram.ui.ActionBar.Theme$ResourcesProvider,boolean)'>"
                )
            ][0], BaseHook(lib, before=lambda param: self.open_for_view(param)), Integer.MAX_VALUE
        )
        lib.hook_method(
            PC_methods[
                "<java.lang.reflect.Method 'public com.exteragram.messenger.plugins."
                "PluginsController$PluginValidationResult com.exteragram.messenger.plugins"
                ".PluginsController.validatePluginFromFile(java.lang.String)'>"
            ], BaseHook(lib, after=lambda param: self.validate_plugin_from_file(param)), 0
        )
        lib.hook_method(
            [
                i for i in (
                    PluginsActivity
                    .getClass()
                    .getDeclaredMethods()
                )
                if repr(i) == (
                    "<java.lang.reflect.Method 'private org.telegram.ui.Components.UItem "
                    "com.exteragram.messenger.plugins.ui.PluginsActivity.createPluginItem"
                    "(com.exteragram.messenger.plugins.Plugin)'>"
                )
            ][0], BaseHook(lib, before=lambda param: self.create_plugin_item(param)), 0
        )
        lib.hook_method(
            PC_methods[
                "<java.lang.reflect.Method 'public void com.exteragram.messenger.plugins."
                "PluginsController.loadPluginFromFile(java.lang.String,"
                "com.exteragram.messenger.plugins.Plugin,org.telegram.messenger.Utilities$Callback)'>"
            ], BaseHook(lib, after=lambda param: self.on_plugin_install(param.args[0], param.args[1])), 0
        )
        lib.hook_method(
            PC_methods[
                "<java.lang.reflect.Method 'public void com.exteragram.messenger.plugins."
                "PluginsController.deletePlugin("
                "java.lang.String,org.telegram.messenger.Utilities$Callback)'>"
            ], BaseHook(lib, after=lambda param: lib._on_plugin_delete(param.args[0])), 0
        )
        lib.hook_method(
            ChatMessageCell.getClass().getDeclaredMethod(
                "setMessageObject",
                MessageObject, MessageObject.GroupedMessages, Boolean.TYPE, Boolean.TYPE, Boolean.TYPE
            ), BaseHook(
                lib,
                before=lambda param: lib.on_message_cell_setup(param.thisObject, param.args[0], before=True),
                after=lambda param: lib.on_message_cell_setup(param.thisObject, param.args[0])
            ), 0
        )
        # lib.hook_method(
        #     [
        #         i for i in MsgDelegate
        #         if repr(i) == (
        #             "<java.lang.reflect.Method 'public void org.telegram.ui.ChatActivity$"
        #             "ChatMessageCellDelegate.didPressInstantButton(org.telegram.ui.Cells.ChatMessageCell,int)'>"
        #         )
        #     ][0], BaseHook(before=lambda param: ...), 0
        # )
        lib.hook_method(
            MsgDelegate[
                "<java.lang.reflect.Method 'public void org.telegram.ui.ChatActivity$ChatMessageCellDelegate."
                "didPressBotButton(org.telegram.ui.Cells.ChatMessageCell,"
                "org.telegram.tgnet.TLRPC$KeyboardButton)'>"
            ], BaseHook(
                lib,
                before=lambda param: lib.on_inline_button_click(param.args[0], param.args[1], param)
            ), Integer.MAX_VALUE
        )
        lib.hook_method(
            MsgDelegate[
                "<java.lang.reflect.Method 'public void org.telegram.ui.ChatActivity$ChatMessageCellDelegate."
                "didLongPressBotButton(org.telegram.ui.Cells.ChatMessageCell,"
                "org.telegram.tgnet.TLRPC$KeyboardButton)'>"
            ], BaseHook(
                lib,
                before=lambda param: lib.on_inline_button_click(param.args[0], param.args[1], param, True)
            ), Integer.MAX_VALUE
        )
        lib.hook_method(
            MsgDelegate[
                "<java.lang.reflect.Method 'public void org.telegram.ui.ChatActivity$ChatMessageCellDelegate."
                "didPressUrl(org.telegram.ui.Cells.ChatMessageCell,android.text.style.CharacterStyle,boolean)'>"
            ], BaseHook(
                lib,
                before=lambda param: lib.on_click_url_in_message(param)
            ), Integer.MAX_VALUE
        )
        lib.hook_method(
            PC_methods[
                "<java.lang.reflect.Method 'public java.lang.String com.exteragram.messenger.plugins."
                "PluginsController.addMenuItem(java.lang.String,com.chaquo.python.PyObject)'>"
            ], BaseHook(
                lib,
                after=lambda param: lib.on_add_menu_item(param.args[0], param.args[1], param.getResult())
            ), 0
        )
        lib.hook_method(
            PC_methods[
                "<java.lang.reflect.Method 'public boolean com.exteragram.messenger.plugins."
                "PluginsController.removeMenuItem(java.lang.String,java.lang.String)'>"
            ], BaseHook(
                lib,
                after=lambda param: lib.on_remove_menu_item(param.args[0], param.args[1], param.getResult())
            ), 0
        )

        def _remove_plugin_items(pid):
            types = lib._menu_items.pop(pid, {})
            for ids in types.values():
                for id in ids:
                    lib._menu_items_by_id.pop(id, None)

        lib.hook_method(
            PC_methods[
                "<java.lang.reflect.Method 'public void com.exteragram.messenger.plugins."
                "PluginsController.removeMenuItemsByPluginId(java.lang.String)'>"
            ], BaseHook(lib, after=lambda param: _remove_plugin_items(param.args[0])), 0
        )
        # lib.hook_method(
        #     [
        #         i for i in MessagesController.getClass().getDeclaredMethods()
        #         if repr(i) == (
        #             "<java.lang.reflect.Method 'public org.telegram.tgnet.TLRPC$User org.telegram.messenger."
        #             "MessagesController.getUser(java.lang.Long)'>"
        #         )
        #     ][0], BaseHook(
        #         before=lambda param: Fingerprint.change_text(param)
        #     ), 0
        # )
        lib.hook_method(
            MessageObject.getClass().getDeclaredMethod("updateTranslation", Boolean.TYPE),
            BaseHook(lib, before=lambda param: self.update_message_translation(param))
        )
        lib.hook_method(
            MessageObject.getClass().getDeclaredMethod("getInlineBotButtons"),
            BaseHook(lib, after=lambda param: self.get_inline_bot_buttons(param))
        )
        lib.hook_method(
            [
                i
                for i in Text.getClass().getDeclaredMethods()
                if repr(i) == (
                    "<java.lang.reflect.Method 'public void org.telegram.ui.Components.Text.setText(java.lang.CharSequence)'>"
                )
            ][0], BaseHook(lib, before=lambda param: self.text_update_text(param))
        )
        self.hook_zwylib()

        return self

    def hook_zwylib(self):
        import zwylib

        self.orig_methods["zwylib._register_command"] = getattr(zwylib, "_original_register_command", zwylib._register_command)
        zwylib._register_command = self.zwylib_register_command
        if not hasattr(zwylib, "_original_register_command"):
            setattr(zwylib, "_original_register_command", self.orig_methods["zwylib._register_command"])

        overrided = self.lib.get("zwy-override", {}).get("commands", {})
        disabled = self.lib.get("zwy_disabled", {}).get("commands", {})
        self.lib._zwy_commands_disabled = {}

        for plugin_id, dispatcher in zwylib.command_manager.dispatchers.items():
            for name, cmd in dispatcher.listeners.copy().items():
                try:
                    full = f"{name}"

                    if name in overrided:
                        del dispatcher.listeners[name]
                        name = overrided[name]
                        cmd.name = overrided[name]
                        dispatcher.listeners[name] = cmd

                    if name in disabled:
                        dispatcher.listeners.pop(name)

                        if plugin_id not in self.lib._zwy_commands_disabled:
                            self.lib._zwy_commands_disabled[plugin_id] = {}

                        self.lib._zwy_commands_disabled[plugin_id][name] = (dispatcher.listeners, cmd, plugin_id, False)

                    if cmd.subcommands:
                        self.zwylib_check_subcmds(full, cmd.subcommands, plugin_id)
                except:
                    self.lib.error(f"An unexpected error occurred: {traceback.format_exc()}")

        self.lib.debug("zwylib hooked")

    def zwylib_check_subcmds(self, full: str, subcmds, plugin_id):
        for name, subcmd in subcmds.copy().items():
            full_name = f"{full} {name}"

            if full_name in self.lib.get("zwy-override", {}).get("commands", {}):
                name = self.lib.get("zwy-override", {}).get("commands", {})[full_name].split(" ")[-1]
                subcmd.name = name
                subcmds[name] = subcmd
                full_name = f"{full} {name}"

            if full_name in self.lib.get("zwy_disabled", {}).get("commands", {}):
                if plugin_id not in self.lib._zwy_commands_disabled:
                    self.lib._zwy_commands_disabled[plugin_id] = {}

                self.lib._zwy_commands_disabled[plugin_id][full_name] = (subcmds, subcmd, plugin_id, True)
                subcmds.pop(name)

            if subcmd.subcommands:
                self.zwylib_check_subcmds(full_name, subcmd.subcommands, plugin_id)

    def zwylib_register_command(
        self,
        target: Dict[str, Any],
        command: Any,
        plugin_id: str,
        is_subcommand: bool = False
    ):
        self.lib.debug(f"zwylib_register_command: {command.name} {plugin_id}")
        overrided = self.lib.get("zwy-override", {}).get("commands", {})
        if command.name in overrided:
            command.name = overrided[command.name]

        disabled = self.lib.get("zwy_disabled", {}).get("commands", {})
        if command.name in disabled:
            self.lib._zwy_commands_disabled[plugin_id][command.name] = (target, command, plugin_id, is_subcommand)
            return

        return self.orig_methods["zwylib._register_command"](target, command, plugin_id, is_subcommand)

    def browser_open_url(self, param):
        _uri = str(param.args[1].toString())
        m = re.match("tg://cactus/(.+)/(.+)", _uri)
        if not m:
            return

        plugin_id = m.group(1)
        parsed = urlparse(m.group(2))
        cmd = parsed.path
        kwargs = {} if not parsed.query else {
            k: v[0]
            for k, v in parse_qs(parsed.query).items()
        }

        self.lib.on_uri_command_hook(plugin_id, cmd, kwargs)

    def open_for_view(self, param):
        f = param.args[0]
        filename = param.args[1]
        if filename.split(".")[-1] == "cactusexport":
            with open(f.getAbsolutePath(), "rb") as f:
                content = f.read()

            if not content:
                return

            param.setResult(False)

            content = json.loads(CactusUtils.decode_and_decompress(content))
            CactusIEAlert(self.lib, param.args[3], plugins=content).show_alert()

    def validate_plugin_from_file(self, param):
        res = param.getResult()
        if res.error:
            return

        p = res.plugin

        strings, commands, description = get_plugin_strings_and_commands(param.args[0])
        if not strings:
            return

        locale_dict: Dict[str, Union[str, Dict[str, str]]] = strings.get(CactusUtils.get_locale(), strings)
        if "en" in locale_dict:
            locale_dict = locale_dict["en"]

        description = locale_dict.get("__doc__", description)

        commands_with_docs = self.lib.get_setting("show_cmds_description", True)

        if commands:
            description = description + "\n\n" + self.lib.string("commands") + "\n" + (
                (
                    " • ".join(list(commands.keys()))
                ) if not commands_with_docs else (
                    "\n".join(
                        [
                            (f"• {cmd} " + locale_dict.get(doc_key, strings.get("en", strings).get(doc_key, doc_key)))
                            if doc_key else f"• {cmd} - " + self.lib.string("not_description")
                            for cmd, doc_key in commands.items()
                        ]
                    )
                )
            )
        p.setDescription(description)

    def create_plugin_item(self, param):
        p = param.args[0]
        pid = p.getId()
        if pid not in PluginsData.plugins or not PluginsData.plugins[pid].get("strings", {}):
            return

        description = PluginsData.description(pid)

        commands_with_docs = self.lib.get_setting("show_cmds_description", True)
        locale_dict = PluginsData.locale(pid)
        strings = PluginsData.plugins[pid]["strings"]

        if commands := PluginsData.plugins[pid].get("commands", {}):
            description += "\n\n" + self.lib.string("commands") + "\n" + (
                (
                    " • ".join(list(commands.keys()))
                ) if not commands_with_docs else (
                    "\n".join(
                        [
                            (f"• {cmd} " + locale_dict.get(doc_key, strings.get("en", strings).get(doc_key, doc_key)))
                            if doc_key else f"• {cmd} - " + self.lib.string("not_description")
                            for cmd, doc_key in commands.items()
                        ]
                    )
                )
            )
        p.setDescription(description)

    def on_plugin_install(self, plugin_path: str, validation_result):
        if is_cactus_plugin(plugin_path):
            PluginsData.parse(plugin_path)

        if validation_result.getId() == "zwylib":
            self.hook_zwylib()

    def text_update_text(self, param):
        paint = get_private_field(param.thisObject, "paint")
        if paint != Theme.getThemePaint("paintChatBotButton") or not self.lib._last_cell:
            return

        orig = str(param.args[0])

        if not (matches := re.findall(r'(<(emoji|icon)\s+id=(\w+)/>)', orig)):
            return

        newText = SpannableStringBuilder("")
        current_index = 0
        if orig.startswith("c "):
            newText.append("c ")
            orig = orig.replace("c ", "", 1)
            span = ColoredImageSpan(R.drawable.menu_copy_s)
            span.setScale(.9, .9)
            newText.setSpan(span, 0, 1, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            current_index += 2

        lastIndex = 0
        paintId = paint.getFontMetricsInt()
        for full, type, id in matches:
            try:
                drawable = None
                if type == "emoji":
                    if not id.isdigit():
                        continue
                elif type == "icon":
                    if not (drawable := getattr(R.drawable, id, None)):
                        continue
                else:
                    continue

                index = orig.index(full, lastIndex)
                newText.append(orig[lastIndex:index])
                current_index += len(orig[lastIndex:index])
                newText.append("|")
                current_index += 1
                lastIndex = index + len(full)
                if type == "emoji":
                    span = AnimatedEmojiSpan(int(id), 1.2, paintId)
                    span.top = False
                elif type == "icon":
                    span = ColoredImageSpan(drawable)
                    span.setScale(.9, .9)
                else:
                    continue

                newText.setSpan(span, current_index - 1, current_index, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE)
            except:
                self.lib.error(f"[update_button_text]: {traceback.format_exc()}")
                continue

        newText.append(orig[lastIndex:])
        param.args[0] = newText
        param.thisObject.supportAnimatedEmojis(self.lib._last_cell)
        param.thisObject.setEmojiCacheType(26)

    def get_inline_bot_buttons(self, param):
        result = param.getResult()
        if not result:
            msg = param.thisObject
            mid = msg.getRealId()

            if not msg.messageOwner.entities.isEmpty():
                entities = msg.messageOwner.entities
                for i in range(entities.size()):
                    entity = entities.get(i)
                    if isinstance(entity, (TLRPC.TL_messageEntityUrl, TLRPC.TL_messageEntityTextUrl)):
                        url = msg.messageOwner.message[entity.offset:entity.offset + entity.length] if isinstance(
                            entity, TLRPC.TL_messageEntityUrl) else entity.url
                        if url.startswith("tg://cactus/mdata/"):
                            raw_data = url[len("tg://cactus/mdata/"):].strip()
                            data = CactusUtils.decode_and_decompress(raw_data).decode("utf-8")
                            if not data:
                                continue

                            try:
                                mdata = json.loads(data)

                                if buttons := mdata.get("markup", []):
                                    markup = CactusUtils.Inline.Markup.from_dict(buttons)

                                    if msg.messageOwner.flags & (1 << 6) == 0:
                                        msg.messageOwner.flags |= (1 << 6)

                                    msg.messageOwner.reply_markup = markup._markup
                                    msg.measureInlineBotButtons()

                                    param.setResult(get_private_field(msg, "inlineKeyboardSource"))
                                    return
                            except:
                                self.lib.error(f"Failed to parse mdata: {data} | {traceback.format_exc()}")
                                continue

            dialog_id = msg.getDialogId()

            if (
                    dialog_id and dialog_id in CactusUtils.Inline.msg_markups
                    and (markup := CactusUtils.Inline.msg_markups[dialog_id].get(mid, None))
            ):
                if msg.messageOwner.flags & (1 << 6) == 0:
                    msg.messageOwner.flags |= (1 << 6)

                msg.messageOwner.reply_markup = markup
                msg.measureInlineBotButtons()

                param.setResult(get_private_field(msg, "inlineKeyboardSource"))
                return

            if dialog_id and dialog_id in CactusUtils.Inline.need_markups:
                message_text_html = lambda: HTML().unparse(msg.messageOwner.message,
                                                           list(msg.messageOwner.entities.toArray()))
                message_text_md = lambda: Markdown().unparse(msg.messageOwner.message,
                                                             list(msg.messageOwner.entities.toArray()))
                _copy = CactusUtils.Inline.need_markups[dialog_id].copy()
                for data in reversed(_copy):
                    if data.get("after", None):
                        if msg.messageOwner.date < int(data["after"]):
                            continue
                    elif data.get("after_edit", None):
                        if msg.messageOwner.edit_date < int(data["after_edit"]):
                            continue
                    else:
                        continue

                    if data["uid"] and data["uid"] not in message_text_html():
                        continue

                    if not data["uid"]:
                        original_text = (
                            HTML() if data["parse_mode"] == "HTML" else Markdown()
                        ).unparse(data["message"], data["entities"])
                        if original_text != (message_text_html if data["parse_mode"] == "HTML" else message_text_md)():
                            continue

                    if msg.messageOwner.flags & (1 << 6) == 0:
                        msg.messageOwner.flags |= (1 << 6)

                    msg.messageOwner.reply_markup = data["markup"]
                    msg.measureInlineBotButtons()

                    param.setResult(get_private_field(msg, "inlineKeyboardSource"))
                    return

    def update_message_translation(self, param):
        msg = param.thisObject
        mid = msg.getRealId()

        if not msg.messageOwner.entities.isEmpty():
            global_premium = self.lib.get_setting("global_premium", True)
            entities = msg.messageOwner.entities
            pyentities = list(msg.messageOwner.entities.toArray())
            added = 0
            for i, entity in enumerate(pyentities):
                if isinstance(entity, TLRPC.TL_messageEntityTextUrl):
                    if global_premium and (m := re.match(r"tg://cactus/emoji/(\d+)", entity.url)):
                        (entities.set if mid > 0 else entities.add)(
                            i + added,
                            CactusUtils.Telegram.tlrpc_object(
                                TLRPC.TL_messageEntityCustomEmoji(),
                                document_id=int(m.group(1)),
                                offset=entity.offset,
                                length=entity.length
                            )
                        )
                        if mid < 0:
                            added += 1


class CactusIEAlert:
    def __init__(self, lib, activity, isExport: bool = False, plugins: Optional[Dict] = None, ctx=None):
        self.warningTextView = None
        self.infoTextView = None
        self.selectPluginsBtn = None
        self.loadingProgress = None
        self.doneBtn = None
        self.onProcess = None
        self.progressPercentage = None
        self.progressTextView = None
        self.bottomSheet = None
        self.loaded_plugins = None
        self.plugins_count = None
        self.preProcess = None
        self.titleTextView = None
        self.dismiss = None
        self._builder = None
        self.iconView = None

        self.current_plugins = {
            p.id: p
            for p in sorted([
                p
                for p in CactusUtils.all_plugins()
            ], key=lambda x: x.name.lower())
        }
        self.isExport = isExport
        self.with_data = True

        self.selected_plugins = {
            plugin_id: True
            for plugin_id in (plugins if not isExport else self.current_plugins)
        }
        self._new_selected_plugins = {}
        self.ctx = ctx

        self.plugins = plugins
        self.lib = lib
        self.activity = activity

    def string(self, key, *args, **kwargs):
        return self.lib.string(("import_" if not self.isExport else "export_") + key, *args, **kwargs)

    def _current_version(self, plugin_id):
        v = getattr(self.current_plugins.get(plugin_id, None), "version", None)
        return f"v{v} -> " if v else "", v

    def _select_plugins_dialog(self, *_):
        try:
            self._new_selected_plugins.clear()
            self._new_selected_plugins.update(self.selected_plugins)
            cells = {}
            _builder = AlertDialogBuilder(self.activity)
            _builder.set_title(
                self.lib.string("select_plugins") if self.isExport else self.string("alert_title", "")
            )

            container = LinearLayout(self.activity)
            container.setOrientation(LinearLayout.VERTICAL)
            container.setPadding(0, AndroidUtilities.dp(8), 0, AndroidUtilities.dp(8))

            def _on_click(_id):
                def _(_v=None):
                    self._new_selected_plugins[_id] = not self._new_selected_plugins.get(_id)
                    cells[_id].setChecked(self._new_selected_plugins[_id], True)
                    self._update_texts(True)

                return _

            if self.isExport:
                for plugin_id, plugin in self.current_plugins.items():
                    version = getattr(self.current_plugins.get(plugin_id, None), "version", None)

                    cell = CheckBoxCell(self.activity, 1, get_last_fragment().getResourceProvider())
                    cell.setBackgroundDrawable(Theme.getSelectorDrawable(False))
                    cell.setText(plugin.name, ("v" + version) if version else "", self._new_selected_plugins[plugin_id],
                                 False, False)

                    cell.setPadding(0, 0, 0, 0)
                    cell.setEnabled(True)
                    cells[plugin_id] = cell
                    container.addView(cell, LayoutHelper.createLinear(LinearLayout.LayoutParams.MATCH_PARENT, -2))

                    cell.setOnClickListener(OnClickListener(_on_click(plugin_id)))
            else:
                for plugin_id, plugin_info in self.plugins.items():
                    version = plugin_info["plugin_meta"].get("version", None) or "?"
                    merge, current_version = self._current_version(plugin_id)
                    version = "" if not current_version and not version else (
                        f"{merge}v{version}" if version != current_version else f"v{version}")

                    cell = CheckBoxCell(self.activity, 1, get_last_fragment().getResourceProvider())
                    cell.setBackgroundDrawable(Theme.getSelectorDrawable(False))
                    cell.setText(plugin_info["plugin_meta"]["name"], version.replace("v?", ""),
                                 self._new_selected_plugins[plugin_id], False, False)

                    cell.getValueTextView().setTextColor(
                        Theme.getColor(
                            Theme.key_text_RedRegular
                            if version == (current_version or "?")
                            else Theme.key_dialogTextBlue
                            if version != current_version and current_version
                            else Theme.key_color_green
                        )
                    )

                    cell.setPadding(0, 0, 0, 0)
                    cell.setEnabled(True)
                    cells[plugin_id] = cell
                    container.addView(cell, LayoutHelper.createLinear(LinearLayout.LayoutParams.MATCH_PARENT, -2))

                    cell.setOnClickListener(OnClickListener(_on_click(plugin_id)))

            _builder.set_view(container)

            def _ok(*_):
                try:
                    self.selected_plugins.clear()
                    self.selected_plugins.update(self._new_selected_plugins)
                    self._new_selected_plugins.clear()
                    self._update_texts()
                except:
                    self.lib.error(traceback.format_exc())

            def _cancel(b, _):
                b.dismiss()
                self._update_texts()

            _builder.set_positive_button("OK", _ok)
            _builder.set_negative_button(self.lib.string("cancel"), _cancel)
            _builder.show()
        except:
            self.lib.error(traceback.format_exc())

    def _update_texts(self, mode: bool = False):
        k = len([i for i, t in (self.selected_plugins if not mode else self._new_selected_plugins).items() if t])
        (self.titleTextView if not self.isExport else self.selectPluginsBtn).setText(
            (
                self.string("alert_title", self.plur(k))
                if not self.isExport
                else self.lib.string("selected_plugins", self.plur(k))
            ) + (" *" if mode else "")
        )

        self.doneBtn.setClickable(k > 0)

    def getIconView(self):
        self.iconView = imageView = BackupImageView(self.activity)
        imageView.setRoundRadius(AndroidUtilities.dp(12))
        imageView.getImageReceiver().setAutoRepeat(1)
        imageView.getImageReceiver().setAutoRepeatCount(1)

        return imageView

    def setSticker(self, path: str, imageView: Optional[Any] = None):
        get_media_data_controller().setPlaceholderImageByIndex(imageView if imageView else self.iconView,
                                                               path.split("/")[0], int(path.split("/")[1]), "200_200")

    def show_alert(self):
        try:
            builder = BottomSheet.Builder(self.activity)
            self._builder = builder
            self.dismiss = builder.getDismissRunnable()

            builder.setApplyTopPadding(False)
            builder.setApplyBottomPadding(False)
            linearLayout = LinearLayout(self.activity)
            builder.setCustomView(linearLayout)
            linearLayout.setOrientation(LinearLayout.VERTICAL)

            linearLayout.addView(self.getIconView(),
                                 LayoutHelper.createLinear(78, 78, Gravity.CENTER_HORIZONTAL, 0, 28, 0, 0))
            self.setSticker("CactusPlugins/" + ("3" if self.isExport else "1"))

            self.titleTextView = titleTextView = TextView(self.activity)
            titleTextView.setTextColor(Theme.getColor(Theme.key_dialogTextBlack))
            titleTextView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 22)
            titleTextView.setGravity(Gravity.CENTER)
            if self.isExport:
                titleTextView.setText(self.string("title"))
            linearLayout.addView(titleTextView, LayoutHelper.createFrame(-1, -2, Gravity.TOP, 0, 8, 0, 16))

            # lineView = View(self.activity)
            # lineView.setBackgroundColor(Theme.getColor(Theme.key_divider))
            # linearLayout.addView(lineView, LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, 1))

            self.preProcess = preProcess = LinearLayout(self.activity)
            preProcess.setOrientation(LinearLayout.VERTICAL)

            self.infoTextView = infoTextView = TextView(self.activity)
            infoTextView.setTextColor(Theme.getColor(Theme.key_dialogTextGray4))
            infoTextView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14)
            infoTextView.setGravity(Gravity.CENTER)
            infoTextView.setText(self.string("info"))
            preProcess.addView(infoTextView, LayoutHelper.createLinear(-1, -2, (
                Gravity.RIGHT if LocaleController.isRTL else Gravity.LEFT) | Gravity.TOP, 17, 8, 17, 8))

            self.warningTextView = warningTextView = TextView(self.activity)
            warningTextView.setTextColor(Theme.getColor(Theme.key_text_RedRegular))
            warningTextView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14)
            warningTextView.setGravity(Gravity.CENTER)
            warningTextView.setText(self.string("warning"))
            preProcess.addView(warningTextView, LayoutHelper.createLinear(-1, -2, (
                Gravity.RIGHT if LocaleController.isRTL else Gravity.LEFT) | Gravity.TOP, 50, 0, 50, 16))

            self.selectPluginsBtn = selectPluginsBtn = TextView(self.activity)
            selectPluginsBtn.setText(self.lib.string("select_plugins"))
            selectPluginsBtn.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16)
            selectPluginsBtn.setGravity(Gravity.CENTER)
            selectPluginsBtn.setClickable(True)
            selectPluginsBtn.setTextColor(Theme.getColor(Theme.key_featuredStickers_addButton))
            selectPluginsBtn.setOnClickListener(OnClickListener(self._select_plugins_dialog))
            preProcess.addView(selectPluginsBtn, LayoutHelper.createFrame(-1, 32, (
                Gravity.RIGHT if LocaleController.isRTL else Gravity.LEFT) | Gravity.TOP, 100, 8, 100, 8))

            self.doneBtn = doneBtn = TextView(self.activity)
            doneBtn.setText(self.lib.string("export2" if self.isExport else "import"))
            doneBtn.setTextColor(Theme.getColor(Theme.key_dialogTextBlack))
            doneBtn.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16)
            doneBtn.setGravity(Gravity.CENTER)
            doneBtn.setBackground(Theme.createSimpleSelectorRoundRectDrawable(
                AndroidUtilities.dp(6),
                Theme.getColor(Theme.key_featuredStickers_addButton),
                Theme.getColor(Theme.key_featuredStickers_addButtonPressed)
            ))
            doneBtn.setClickable(True)
            doneBtn.setTextColor(Theme.getColor(Theme.key_featuredStickers_buttonText))
            doneBtn.setOnClickListener(OnClickListener(self.start_process))
            preProcess.addView(doneBtn, LayoutHelper.createFrame(-1, 56, (
                Gravity.RIGHT if LocaleController.isRTL else Gravity.LEFT) | Gravity.TOP, 30, 8, 30, 8))

            includeDataCheckbox = CheckBox2(self.activity, 21, get_last_fragment().getResourceProvider())
            includeDataCheckbox.setColor(Theme.key_radioBackgroundChecked, Theme.key_checkboxDisabled,
                                         Theme.key_checkboxCheck)
            includeDataCheckbox.setDrawUnchecked(True)
            includeDataCheckbox.setChecked(self.with_data, False)
            includeDataCheckbox.setDrawBackgroundAsArc(10)

            includeDataTextView = TextView(self.activity)
            includeDataTextView.setTextColor(Theme.getColor(Theme.key_windowBackgroundWhiteBlackText))
            includeDataTextView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 14)
            includeDataTextView.setText(self.lib.string("include_data_and_settings"))
            includeDataCheckBoxContainer = FrameLayout(self.activity)
            includeDataCheckBoxContainer.addView(includeDataCheckbox,
                                                 LayoutHelper.createFrame(21, 21, Gravity.CENTER, 0, 0, 0, 0))

            includeDataCheckLayout = LinearLayout(self.activity)
            includeDataCheckLayout.setOrientation(LinearLayout.HORIZONTAL)
            includeDataCheckLayout.setPadding(AndroidUtilities.dp(8), AndroidUtilities.dp(6), AndroidUtilities.dp(10),
                                              AndroidUtilities.dp(6))
            includeDataCheckLayout.addView(includeDataCheckBoxContainer,
                                           LayoutHelper.createLinear(24, 24, Gravity.CENTER_VERTICAL, 0, 0, 6, 0))
            includeDataCheckLayout.addView(includeDataTextView,
                                           LayoutHelper.createLinear(-2, -2, Gravity.CENTER_VERTICAL))

            def _on_include_data_cell_click(*_):
                self.with_data = not includeDataCheckbox.isChecked()
                includeDataCheckbox.setChecked(self.with_data, True)

            includeDataCheckLayout.setOnClickListener(OnClickListener(_on_include_data_cell_click))

            includeDataCheckLayout.setBackground(
                Theme.createRadSelectorDrawable(Theme.getColor(Theme.key_listSelector), 8, 8))
            preProcess.addView(includeDataCheckLayout,
                               LayoutHelper.createLinear(-2, -2, Gravity.CENTER_HORIZONTAL, 0, 0, 0, 8))

            linearLayout.addView(preProcess, LayoutHelper.createLinear(-1, -2, Gravity.TOP, 0, 0, 0, 0))

            self.onProcess = onProcess = LinearLayout(self.activity)
            onProcess.setOrientation(LinearLayout.VERTICAL)
            onProcess.setPadding(0, 0, 0, AndroidUtilities.dp(16))

            onProcess.setVisibility(View.GONE)

            self.loadingProgress = loadingProgress = LineProgressView(self.activity)
            loadingProgress.setProgressColor(Theme.getColor(Theme.key_dialogLineProgress))
            loadingProgress.setProgress(0, True)
            onProcess.addView(loadingProgress, LayoutHelper.createFrame(-1, 4, Gravity.TOP, 26, 8, 26, 0))

            self.progressPercentage = progressPercentage = TextView(self.activity)
            progressPercentage.setTextColor(Theme.getColor(Theme.key_dialogTextBlack))
            progressPercentage.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16)
            progressPercentage.setText("0%")
            progressPercentage.setGravity(Gravity.LEFT)
            onProcess.addView(progressPercentage, LayoutHelper.createFrame(-1, -2, Gravity.TOP, 26, 0, 0, 8))

            self.progressTextView = progressTextView = TextView(self.activity)
            progressTextView.setTextColor(Theme.getColor(Theme.key_dialogTextBlack))
            progressTextView.setTextSize(TypedValue.COMPLEX_UNIT_DIP, 16)
            progressTextView.setText(self.lib.string("processing"))
            progressTextView.setGravity(Gravity.LEFT)
            onProcess.addView(progressTextView, LayoutHelper.createFrame(-1, -2, Gravity.TOP, 26, 0, 0, 0))

            linearLayout.addView(onProcess, LayoutHelper.createLinear(-1, -2, Gravity.TOP, 0, 0, 0, 0))

            self._update_texts()
            self.bottomSheet = builder.show()
        except:
            self.lib.error(traceback.format_exc())

    def process(self):
        try:
            plids = [i for i, t in self.selected_plugins.items() if t]
            if self.isExport:
                data = {}
                for index, plugin_id in enumerate(plids, 1):
                    data[plugin_id] = CactusUtils.PluginInfo(
                        self.lib,
                        self.current_plugins[plugin_id],
                        getattr(getattr(getattr(self.current_plugins[plugin_id], "utils", None), "__class__", None),
                                "__name__", None) == "CactusUtils"
                    ).export(with_data=self.with_data)
                    if index != len(plids):
                        self._update_progress(index, len(plids))

                path = CactusUtils.FileSystem.write_temp_file(f"plugins-export.cactusexport",
                                                              CactusUtils.compress_and_encode(
                                                                  json.dumps(data).encode("utf-8")).encode("utf-8"))
                self.lib.answer_file(self.ctx.get("dialog_id"), path, "export! :D")
                CactusUtils.FileSystem.delete_file_after(path, 30)

                self._update_progress(len(plids), len(plids))
            else:
                self.loaded_plugins = 0
                self.plugins_count = len(plids)
                for index, plugin_id in enumerate(plids, 1):
                    if plugin_id in self.current_plugins:
                        PluginsController.getInstance().deletePlugin(
                            plugin_id,
                            CactusUtils.Callback(self._load_plugin, self.plugins[plugin_id])
                        )
                    else:
                        self._load_plugin(None, self.plugins[plugin_id])

        except:
            self.lib.error(traceback.format_exc())

    def _load_data(self, plugin_data: dict, pc):
        if not self.with_data:
            self.loaded_plugins += 1
            self._update_progress(self.loaded_plugins, self.plugins_count)
            return

        plugin_id = plugin_data["plugin_meta"]["id"]

        if plugin_data.get("settings", {}):
            for key, value in plugin_data["settings"].items():
                getattr(
                    pc,
                    "setPluginSetting" + (
                        "Boolean" if isinstance(value, bool) else
                        "Int" if isinstance(value, int) else
                        "String"
                    ),
                    lambda _, __, ___: None
                )(plugin_id, key, value if isinstance(value, (str, bool, int)) or value is None else str(value))

        if plugin_data.get("data", {}):
            getattr(CactusUtils.plugin(plugin_id), "_import_data", lambda _: None)(
                plugin_data["data"])

        self.loaded_plugins += 1
        self._update_progress(self.loaded_plugins, self.plugins_count)

    def _load_plugin(self, error, plugin_data: dict):
        meta = plugin_data.get("plugin_meta", {})
        pid = meta.get("id")

        if error:
            self.lib.error(f"Failed to unload plugin {pid}: {error}")
            return

        pc = PluginsController.getInstance()

        def _enabled_cb(_error):
            if _error:
                self.lib.error(f"Failed to enable plugin {pid}: {_error}")
                return

        def _load_cb(_error):
            if _error:
                self.lib.error(f"Failed to load plugin {pid}: {_error}")
                return

            if meta.get("enabled", False):
                pc.setPluginEnabled(pid, True, CactusUtils.Callback(_enabled_cb))

            self._load_data(plugin_data, pc)

        path = CactusUtils.FileSystem.write_temp_file(f"{pid}.plugin",
                                                      CactusUtils.decode_and_decompress(plugin_data["file_content"]))
        self.lib.debug(f"Loading plugin {pid} from file: {path}")
        pc.loadPluginFromFile(str(path), pc.validatePluginFromFile(str(path)).plugin, CactusUtils.Callback(_load_cb))
        CactusUtils.FileSystem.delete_file_after(path, 30)

    @staticmethod
    def plur(k):
        return CactusUtils.pluralization_string(k, ["плагин", "плагина",
                                                    "плагинов"]) if CactusUtils.get_locale() == "ru" else (
                f"{k} plugin" + ("s" if k > 1 else ""))

    def _update_progress(self, index: int, count: int):
        try:
            if get_private_field(self.bottomSheet, "showing") is False:
                self.bottomSheet.show()

            self.loadingProgress.setProgress(
                jfloat((round((index / count) * 100) / 100) if index < count else 1),
                True
            )
            self.progressTextView.setText(self.string("progress", self.plur(index)) + "...")
            self.progressPercentage.setText(
                (str(round((index / count) * 100)) + "%")
                if index < count else "100%"
            )
            if index == count:
                self.setSticker("CactusPlugins/2")
                with suppress(Exception):
                    self.onProcess.setVisibility(View.GONE)

                self.titleTextView.setText(self.string("done", self.plur(index)))
                time.sleep(0.65)
                run_on_ui_thread(lambda: self.dismiss.run())
        except:
            self.lib.error(traceback.format_exc())

    def start_process(self):
        try:
            self.preProcess.setVisibility(View.GONE)
            self.onProcess.setVisibility(View.VISIBLE)
            self.bottomSheet.setCanDismissWithSwipe(False)

            run_on_queue(self.process)
        except:
            self.lib.error(traceback.format_exc())


class BaseHook(MethodHook):
    def __init__(self, plugin: Optional[CactusUtils.Plugin] = None, *, before: Optional[callable] = None, after: Optional[callable] = None):
        self.plugin = plugin
        self.before = before
        self.after = after

    def before_hooked_method(self, param):
        if self.before:
            try:
                self.before(param)
            except:
                (self.plugin if self.plugin else CactusUtils).error(f"[Hook {self.before.__name__} error] " + traceback.format_exc())

    def after_hooked_method(self, param):
        if self.after:
            try:
                self.after(param)
            except:
                (self.plugin if self.plugin else CactusUtils).error(f"[Hook {self.after.__name__} error] " + traceback.format_exc())


def get_plugin_strings_and_commands(
        filepath: Optional[str] = None,
        file_content: Optional[str] = None
) -> Tuple[Dict[str, Dict[str, str]], Dict[str, str], Optional[str]]:
    if file_content:
        tree = ast.parse(file_content, filename=filepath or "<unknown>")
    else:
        if not os.path.exists(filepath):
            return {}, {}, None

        with open(filepath, "r", encoding="utf-8") as f:
            tree = ast.parse(f.read(), filename=filepath)

    description, strings, commands, _id = "", {}, {}, None

    for node in ast.iter_child_nodes(tree):
        if isinstance(node, ast.Assign):
            for target in node.targets:
                if isinstance(target, ast.Name) and target.id == "__description__":
                    if isinstance(node.value, ast.Constant):
                        description = node.value.value
                        if _id:
                            break
                if isinstance(target, ast.Name) and target.id == "__id__":
                    if isinstance(node.value, ast.Constant):
                        _id = node.value.value
                        if description:
                            break

    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef):
            inherits_from_plugin = False
            for base in node.bases:
                if (isinstance(base, ast.Attribute) and
                        isinstance(base.value, ast.Name) and
                        base.value.id == "CactusUtils" and
                        base.attr in ["Plugin", "CactusPlugin", "CactusModule"]):
                    inherits_from_plugin = True
                    break

            if inherits_from_plugin:
                for item in node.body:
                    if isinstance(item, ast.Assign):
                        for target in item.targets:
                            if isinstance(target, ast.Name) and target.id == "strings":
                                strings = ast.literal_eval(item.value)  # type: ignore
                                break

                    elif isinstance(item, ast.FunctionDef):
                        for decorator in item.decorator_list:
                            is_command_decorator = False
                            decorator_args = {}

                            if isinstance(decorator, ast.Call):
                                if (isinstance(decorator.func, ast.Name) and decorator.func.id == "command") or \
                                        (isinstance(decorator.func,
                                                    ast.Attribute) and decorator.func.attr == "command"):
                                    is_command_decorator = True
                                    for keyword in decorator.keywords:
                                        if keyword.arg == "command":
                                            decorator_args['cmd'] = ast.literal_eval(keyword.value)  # type: ignore
                                        elif keyword.arg == "doc":
                                            decorator_args['doc'] = ast.literal_eval(keyword.value)  # type: ignore
                                    if decorator.args and len(decorator.args) > 0 and 'cmd' not in decorator_args:
                                        decorator_args['cmd'] = ast.literal_eval(decorator.args[0])  # type: ignore

                            elif isinstance(decorator, ast.Name) and decorator.id == "command":
                                is_command_decorator = True
                                decorator_args['cmd'] = item.name
                                decorator_args['doc'] = None

                            if is_command_decorator:
                                cmd_value = decorator_args.get('cmd', item.name)
                                doc_value = decorator_args.get('doc')

                                commands[cmd_value] = doc_value
                                break

                if strings is not None:
                    strings["__id__"] = _id
                    return strings, commands, description

    strings["__id__"] = _id
    return strings, commands, description


def is_cactus_plugin(filepath: str) -> bool:
    with open(filepath, "r", encoding="utf-8") as f:
        tree = ast.parse(f.read(), filename=filepath)

    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef):
            for base in node.bases:
                if (isinstance(base, ast.Attribute) and
                        isinstance(base.value, ast.Name) and
                        base.value.id == "CactusUtils" and
                        base.attr in ["Plugin", "CactusPlugin", "CactusModule"]):
                    return True

    return False
