// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/TimestampAligner

#ifndef org_webrtc_TimestampAligner_JNI
#define org_webrtc_TimestampAligner_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_TimestampAligner[];
const char kClassPath_org_webrtc_TimestampAligner[] = "org/webrtc/TimestampAligner";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_TimestampAligner_clazz(nullptr);
#ifndef org_webrtc_TimestampAligner_clazz_defined
#define org_webrtc_TimestampAligner_clazz_defined
inline jclass org_webrtc_TimestampAligner_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_TimestampAligner,
      &g_org_webrtc_TimestampAligner_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {

static jlong JNI_TimestampAligner_CreateTimestampAligner(JNIEnv* env);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_TimestampAligner_nativeCreateTimestampAligner(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_TimestampAligner_CreateTimestampAligner(env);
}

static void JNI_TimestampAligner_ReleaseTimestampAligner(JNIEnv* env, jlong timestampAligner);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_TimestampAligner_nativeReleaseTimestampAligner(
    JNIEnv* env,
    jclass jcaller,
    jlong timestampAligner) {
  return JNI_TimestampAligner_ReleaseTimestampAligner(env, timestampAligner);
}

static jlong JNI_TimestampAligner_RtcTimeNanos(JNIEnv* env);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_TimestampAligner_nativeRtcTimeNanos(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_TimestampAligner_RtcTimeNanos(env);
}

static jlong JNI_TimestampAligner_TranslateTimestamp(JNIEnv* env, jlong timestampAligner,
    jlong cameraTimeNs);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_TimestampAligner_nativeTranslateTimestamp(
    JNIEnv* env,
    jclass jcaller,
    jlong timestampAligner,
    jlong cameraTimeNs) {
  return JNI_TimestampAligner_TranslateTimestamp(env, timestampAligner, cameraTimeNs);
}


}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_TimestampAligner_JNI
