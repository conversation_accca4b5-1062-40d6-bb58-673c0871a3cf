from base_plugin import BasePlugin, HookResult, HookStrategy, MenuItemData, MenuItemType
from client_utils import (
    get_messages_controller, get_user_config, send_request, RequestCallback,
    run_on_queue, get_last_fragment, dynamic_proxy
)
from android_utils import log, run_on_ui_thread
from ui.bulletin import BulletinHelper
from ui.settings import Header, Switch, Divider
from org.telegram.tgnet import TLRPC
from org.telegram.tgnet.tl import TL_account
from org.telegram.messenger import (
    UserConfig, ImageLoader, FileLoader, ApplicationLoader,
    ImageLocation, NotificationCenter, MessagesController
)
from org.telegram.tgnet import ConnectionsManager
from java.util import ArrayList
from java.lang import Integer
from java.io import File
from android.graphics import BitmapFactory
import traceback
import time

__id__ = "copyprofile"
__name__ = "Copy Profile"
__description__ = "Копирует профиль пользовате<PERSON>я по команде .copy реплаем или через кнопку в меню профиля.\n\nКоманды: .backup - сохранить свой профиль, .restore - восстановить профиль\n\nКнопка 'Скопировать профиль' в меню действий профиля"
__author__ = "@mihailkotovski & @mishabotov"
__version__ = "3.0.0"
__icon__ = "DateRegBot_by_MoiStikiBot/6"
__min_version__ = "11.9.1"

MESSAGES = {
    "command_usage": ".copy должна использоваться в ответ на сообщение пользователя",
    "user_not_found": "Пользователь не найден",
    "copying_profile": "Копирование профиля: {} {}",
    "profile_copied": "✅ Профиль успешно скопирован!",
    "error": "❌ Ошибка: {}",
    "copy_name": "Копировать имя",
    "copy_bio": "Копировать биографию",
    "copy_avatar": "Обычная аватарка",
    "copy_profile_photo": "Основная аватарка",
    "copy_personal_photo": "Личная аватарка",
    "copy_fallback_photo": "Резервная аватарка",
    "copy_emoji_status": "Копировать эмодзи-статус",
    "copy_name_color": "Копировать цвет имени",
    "copy_profile_color": "Копировать цвет профиля",
    "settings_header": "Настройки копирования профиля",
    "avatar_settings_header": "Настройки аватарок",
    "backup_created": "✅ Бекап профиля создан!",
    "backup_restored": "✅ Профиль восстановлен из бекапа!",
    "no_backup": "❌ Бекап профиля не найден",
    "backup_header": "Настройки бекапа профиля",
    "enable_backup": "Включить функцию бекапа",
    "backup_name": "Сохранять имя",
    "backup_bio": "Сохранять биографию",
    "backup_avatar": "Сохранять обычную",
    "backup_profile_photo": "Сохранять основную",
    "backup_personal_photo": "Сохранять личную",
    "backup_fallback_photo": "Сохранять резервную",
    "backup_emoji_status": "Сохранять эмодзи-статус",
    "backup_name_color": "Сохранять цвет имени",
    "backup_profile_color": "Сохранять цвет профиля",
    "backup_info": "Используйте .backup для сохранения текущего профиля, .restore для восстановления",
    "additional_settings": "Дополнительные настройки",
    "show_detailed_logs": "Подробные логи",
    "show_detailed_logs_subtext": "Показывать детальную информацию в логах для отладки",
    "auto_save_avatars": "Автосохранение аватарок",
    "auto_save_avatars_subtext": "Автоматически сохранять скопированные аватарки в папку Download/exteraGram",

    "premium_bio_limit": "Премиум аккаунт: лимит биографии 140 символов",
    "regular_bio_limit": "Обычный аккаунт: лимит биографии 70 символов",
    "bio_truncated_premium": "Биография обрезана до 140 символов (премиум аккаунт)",
    "bio_truncated_regular": "Биография обрезана до 70 символов (обычный аккаунт)",
    "bio_copied_full": "Биография скопирована полностью",


    "avatar_copied": "✅ Обычная аватарка скопирована!",
    "profile_photo_copied": "✅ Основная аватарка скопирована!",
    "personal_photo_copied": "✅ Личная аватарка скопирована!",
    "fallback_photo_copied": "✅ Резервная аватарка скопирована!",
    "no_avatar_found": "❌ Аватарка не найдена",
    "no_profile_photo_found": "❌ Основная аватарка не найдена",
    "no_personal_photo_found": "❌ Личная аватарка не найдена",
    "no_fallback_photo_found": "❌ Резервная аватарка не найдена",
    "avatar_type_not_available": "Этот тип аватарки недоступен для данного пользователя"
}

class UploadNotificationDelegate(dynamic_proxy(NotificationCenter.NotificationCenterDelegate)):
    def __init__(self, plugin_instance):
        super().__init__()
        self.plugin_instance = plugin_instance

    def didReceivedNotification(self, notification_id, account, *args):
        try:
            self.plugin_instance._handle_notification(notification_id, account, *args)
        except Exception as e:
            self.plugin_instance._log(f"Error in UploadNotificationDelegate: {str(e)}")

class CopyProfilePlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.uploading_avatar_path = None
        self.big_photo_size = None
        self.small_photo_size = None
        self.notification_delegate = None
        self.menu_item_id = None

    def on_plugin_load(self):
        self.add_on_send_message_hook()

        self.menu_item_id = self.add_menu_item(
            MenuItemData(
                menu_type=MenuItemType.PROFILE_ACTION_MENU,
                item_id="copy_profile_button",
                text="Скопировать профиль",
                icon="msg_openprofile_solar",
                on_click=self._handle_profile_menu_click,
                condition="user != null"
            )
        )

        log("CopyProfile plugin loaded")

    def on_plugin_unload(self):
        if self.menu_item_id:
            self.remove_menu_item(self.menu_item_id)
        log("CopyProfile plugin unloaded")

    def _handle_profile_menu_click(self, context):
        try:
            user = context.get("user")
            if not user:
                self._show_error("Пользователь не найден")
                return

            self._log(f"Profile menu clicked for user: {user.first_name} (ID: {user.id})")

            run_on_queue(lambda: self._copy_profile_async(user.id))

        except Exception as e:
            self._log(f"Error in profile menu click handler: {str(e)}")
            self._show_error(f"Ошибка: {str(e)}")

    def _log(self, message):
        if self.get_setting("show_detailed_logs", False):
            log(f"[CopyProfile] {message}")
        else:
            log(message)

    def create_settings(self):
        backup_info = ""
        if self.get_setting("backup_exists", False):
            first_name = self.get_setting("backup_first_name", "")
            last_name = self.get_setting("backup_last_name", "")
            backup_info = f"Сохранен профиль: {first_name} {last_name}".strip()
        else:
            backup_info = "Бекап профиля не создан"

        def on_backup_toggle_change(enabled):
            self._log(f"Backup function toggled: {enabled}")
            if not enabled:
                self._show_success("Функция бекапа отключена")
            else:
                self._show_success("Функция бекапа включена")

        def on_detailed_logs_change(enabled):
            self._log(f"Detailed logs toggled: {enabled}")
            if enabled:
                self._show_success("Подробные логи включены")
            else:
                self._show_success("Подробные логи отключены")

        settings = [
            Header(text=MESSAGES["settings_header"]),
            Switch(key="copy_name", text=MESSAGES["copy_name"], default=True, icon="msg_payment_card_solar"),
            Switch(key="copy_bio", text=MESSAGES["copy_bio"], default=True, icon="msg_log_solar"),
            Switch(key="copy_emoji_status", text=MESSAGES["copy_emoji_status"], default=True, icon="input_smile_solar"),
            Switch(key="copy_name_color", text=MESSAGES["copy_name_color"], default=True, icon="menu_tag_rename_solar"),
            Switch(key="copy_profile_color", text=MESSAGES["copy_profile_color"], default=True, icon="menu_profile_colors_solar"),

            Header(text=MESSAGES["avatar_settings_header"]),
            Switch(key="copy_avatar", text=MESSAGES["copy_avatar"], default=True, icon="msg_openprofile_solar"),
            Switch(key="copy_profile_photo", text=MESSAGES["copy_profile_photo"], default=False, icon="msg_addphoto_solar"),
            Switch(key="copy_personal_photo", text=MESSAGES["copy_personal_photo"], default=False, icon="msg_photo_sticker"),
            Switch(key="copy_fallback_photo", text=MESSAGES["copy_fallback_photo"], default=False, icon="msg_saved_ny"),
            Divider(text="Используйте команду .copy в ответ на сообщение"),

            Header(text=MESSAGES["backup_header"]),
            Switch(key="enable_backup", text=MESSAGES["enable_backup"], default=True, icon="menu_clear_cache_solar",
                   on_change=on_backup_toggle_change),
        ]

        if self.get_setting("enable_backup", True):
            settings.extend([
                Switch(key="backup_name", text=MESSAGES["backup_name"], default=True, icon="msg_payment_card_solar"),
                Switch(key="backup_bio", text=MESSAGES["backup_bio"], default=True, icon="msg_log_solar"),
                Switch(key="backup_emoji_status", text=MESSAGES["backup_emoji_status"], default=True, icon="input_smile_solar"),
                Switch(key="backup_name_color", text=MESSAGES["backup_name_color"], default=True, icon="menu_tag_rename_solar"),
                Switch(key="backup_profile_color", text=MESSAGES["backup_profile_color"], default=True, icon="menu_profile_colors_solar"),
                Switch(key="backup_avatar", text=MESSAGES["backup_avatar"], default=True, icon="msg_openprofile_solar"),
                Switch(key="backup_profile_photo", text=MESSAGES["backup_profile_photo"], default=False, icon="msg_addphoto_solar"),
                Switch(key="backup_personal_photo", text=MESSAGES["backup_personal_photo"], default=False, icon="msg_photo_sticker"),
                Switch(key="backup_fallback_photo", text=MESSAGES["backup_fallback_photo"], default=False, icon="msg_saved_ny"),
                Divider(text=backup_info),
            ])

        settings.extend([
            Divider(text=MESSAGES["backup_info"]),
            Header(text=MESSAGES["additional_settings"]),
            Switch(key="show_detailed_logs", text=MESSAGES["show_detailed_logs"], default=False, icon="msg_folder_solar",
                   subtext=MESSAGES["show_detailed_logs_subtext"], on_change=on_detailed_logs_change),
            Switch(key="auto_save_avatars", text=MESSAGES["auto_save_avatars"], default=True, icon="qr_gallery_solar",
                   subtext=MESSAGES["auto_save_avatars_subtext"]),

        ])

        return settings

    def on_send_message_hook(self, account, params):
        try:
            if not isinstance(params.message, str):
                return HookResult()

            message = params.message.strip().lower()

            if message == ".copy":
                if not hasattr(params, 'replyToMsg') or params.replyToMsg is None:
                    self._show_error(MESSAGES["command_usage"])
                    return HookResult(strategy=HookStrategy.CANCEL)

                target_user_id = self._get_user_id_from_reply(params.replyToMsg)
                if target_user_id is None:
                    self._show_error(MESSAGES["user_not_found"])
                    return HookResult(strategy=HookStrategy.CANCEL)

                run_on_queue(lambda: self._copy_profile_async(target_user_id))
                return HookResult(strategy=HookStrategy.CANCEL)

            elif message == ".backup":
                if not self.get_setting("enable_backup", True):
                    self._show_error("Функция бекапа отключена в настройках")
                    return HookResult(strategy=HookStrategy.CANCEL)

                run_on_queue(lambda: self._backup_profile_async())
                return HookResult(strategy=HookStrategy.CANCEL)

            elif message == ".restore":
                if not self.get_setting("enable_backup", True):
                    self._show_error("Функция бекапа отключена в настройках")
                    return HookResult(strategy=HookStrategy.CANCEL)

                run_on_queue(lambda: self._restore_profile_async())
                return HookResult(strategy=HookStrategy.CANCEL)

            return HookResult()

        except Exception as e:
            self._log(f"CopyProfile error: {str(e)}")
            return HookResult()

    def _get_user_id_from_reply(self, reply_msg):
        try:
            if hasattr(reply_msg, 'messageOwner') and reply_msg.messageOwner:
                message_owner = reply_msg.messageOwner
                if hasattr(message_owner, 'from_id') and message_owner.from_id and hasattr(message_owner.from_id, 'user_id'):
                    user_id = message_owner.from_id.user_id
                    self._log(f"Found user ID from messageOwner: {user_id}")
                    return user_id

            if hasattr(reply_msg, 'replyMessageObject') and reply_msg.replyMessageObject:
                message_owner = reply_msg.replyMessageObject.messageOwner
                if hasattr(message_owner, 'from_id') and message_owner.from_id and hasattr(message_owner.from_id, 'user_id'):
                    user_id = message_owner.from_id.user_id
                    self._log(f"Found user ID from replyMessageObject: {user_id}")
                    return user_id

            self._log("No user ID found in reply message")
            return None
        except Exception as e:
            self._log(f"Error getting user ID: {str(e)}")
            return None

    def _copy_profile_async(self, target_user_id):
        try:
            messages_controller = get_messages_controller()
            target_user = messages_controller.getUser(target_user_id)

            if target_user is None:
                run_on_ui_thread(lambda: self._show_error(MESSAGES["user_not_found"]))
                return

            self._load_user_full_info(target_user_id, target_user)
        except Exception as e:
            self._log(f"Error copying profile: {str(e)}")
            run_on_ui_thread(lambda: self._show_error(MESSAGES["error"].format(str(e))))

    def _load_user_full_info(self, target_user_id, target_user):
        try:
            req = TLRPC.TL_users_getFullUser()
            req.id = get_messages_controller().getInputUser(target_user)

            def callback(response, error):
                if error:
                    run_on_ui_thread(lambda: self._show_error(MESSAGES["error"].format(error.text)))
                    return

                if response and isinstance(response, TLRPC.TL_users_userFull):
                    user_full = response.full_user
                    user = None

                    if response.users and response.users.size() > 0:
                        for i in range(response.users.size()):
                            u = response.users.get(i)
                            if u.id == target_user_id:
                                user = u
                                break

                    if user:
                        run_on_ui_thread(lambda: self._apply_profile_data(user, user_full))
                    else:
                        run_on_ui_thread(lambda: self._show_error(MESSAGES["user_not_found"]))
                else:
                    run_on_ui_thread(lambda: self._show_error(MESSAGES["error"].format("Invalid response")))

            send_request(req, RequestCallback(callback))
        except Exception as e:
            self._log(f"Error loading user info: {str(e)}")
            run_on_ui_thread(lambda: self._show_error(MESSAGES["error"].format(str(e))))

    def _apply_profile_data(self, target_user, target_user_full):
        try:
            current_user = get_user_config().getCurrentUser()
            if not current_user:
                self._show_error(MESSAGES["user_not_found"])
                return

            if not target_user:
                self._show_error("Целевой пользователь не найден")
                return

            copy_name = self.get_setting("copy_name", True)
            copy_bio = self.get_setting("copy_bio", True)
            copy_avatar = self.get_setting("copy_avatar", True)
            copy_profile_photo = self.get_setting("copy_profile_photo", False)
            copy_personal_photo = self.get_setting("copy_personal_photo", False)
            copy_fallback_photo = self.get_setting("copy_fallback_photo", False)
            copy_emoji_status = self.get_setting("copy_emoji_status", True)
            copy_name_color = self.get_setting("copy_name_color", True)
            copy_profile_color = self.get_setting("copy_profile_color", True)

            if copy_name:
                first_name = target_user.first_name or ""
                last_name = target_user.last_name or ""
                if len(first_name) > 64:
                    first_name = first_name[:64]
                    self._log("First name truncated to 64 characters")
                if len(last_name) > 64:
                    last_name = last_name[:64]
                    self._log("Last name truncated to 64 characters")
            else:
                first_name = current_user.first_name or ""
                last_name = current_user.last_name or ""

            bio = ""
            if copy_bio and target_user_full and target_user_full.about:
                bio = target_user_full.about
                is_premium = get_user_config().isPremium()
                bio_limit = 140 if is_premium else 70

                if len(bio) > bio_limit:
                    bio = bio[:bio_limit]
                    premium_status = "премиум" if is_premium else "обычный"
                    self._log(f"Bio truncated to {bio_limit} characters (user has {premium_status} account)")
                    self._show_success(f"ℹ️ Биография обрезана до {bio_limit} символов ({premium_status} аккаунт)")
                else:
                    premium_status = "премиум" if is_premium else "обычный"
                    self._log(f"Bio copied fully ({len(bio)} characters, {premium_status} account limit: {bio_limit})")

            self._show_success(MESSAGES["copying_profile"].format(first_name, last_name))


            self._log(f"Copy settings - Name: {copy_name}, Bio: {copy_bio}")
            self._log(f"Avatar settings - Regular: {copy_avatar}, Profile: {copy_profile_photo}, Personal: {copy_personal_photo}, Fallback: {copy_fallback_photo}")
            self._log(f"Other settings - Emoji: {copy_emoji_status}, Name color: {copy_name_color}, Profile color: {copy_profile_color}")

            self._update_profile(first_name, last_name, bio)


            if copy_avatar:
                if target_user.photo:
                    self._log("Starting regular avatar copy...")
                    self._copy_avatar(target_user)
                else:
                    self._log("Regular avatar copy skipped - no avatar found")
                    if self.get_setting("show_detailed_logs", False):
                        self._show_success("ℹ️ Обычная аватарка не найдена")

            if copy_profile_photo:
                if target_user_full and target_user_full.profile_photo:
                    self._log("Starting profile photo copy...")
                    self._copy_profile_photo(target_user_full.profile_photo)
                else:
                    self._log("Profile photo copy skipped - no profile photo found")
                    if self.get_setting("show_detailed_logs", False):
                        self._show_success("ℹ️ Основная аватарка не найдена")

            if copy_personal_photo:
                if target_user_full and target_user_full.personal_photo:
                    self._log("Starting personal photo copy...")
                    self._copy_personal_photo(target_user_full.personal_photo)
                else:
                    self._log("Personal photo copy skipped - no personal photo found")
                    if self.get_setting("show_detailed_logs", False):
                        self._show_success("ℹ️ Личная аватарка не найдена")

            if copy_fallback_photo:
                if target_user_full and target_user_full.fallback_photo:
                    self._log("Starting fallback photo copy...")
                    self._copy_fallback_photo(target_user_full.fallback_photo)
                else:
                    self._log("Fallback photo copy skipped - no fallback photo found")
                    if self.get_setting("show_detailed_logs", False):
                        self._show_success("ℹ️ Резервная аватарка не найдена")

            if copy_emoji_status and hasattr(target_user, 'emoji_status') and target_user.emoji_status:
                self._log("Starting emoji status copy...")
                self._copy_emoji_status(target_user.emoji_status)

            if copy_name_color and hasattr(target_user, 'color') and target_user.color:
                self._log("Starting name color copy...")
                self._copy_name_color(target_user.color)

            if copy_profile_color and hasattr(target_user, 'profile_color') and target_user.profile_color:
                self._log("Starting profile color copy...")
                self._copy_profile_color(target_user.profile_color)

        except Exception as e:
            self._log(f"Error applying profile data: {str(e)}")
            self._show_error(MESSAGES["error"].format(str(e)))

    def _update_profile(self, first_name, last_name, bio, is_restore=False):
        try:
            req = TL_account.updateProfile()
            req.flags = 7
            req.first_name = first_name
            req.last_name = last_name
            req.about = bio

            def callback(response, error):
                if error:
                    run_on_ui_thread(lambda: self._show_error(MESSAGES["error"].format(error.text)))
                    return

                if response:
                    current_user = get_user_config().getCurrentUser()
                    if current_user:
                        current_user.first_name = first_name
                        current_user.last_name = last_name

                    messages_controller = get_messages_controller()
                    user = messages_controller.getUser(get_user_config().getClientUserId())
                    if user:
                        user.first_name = first_name
                        user.last_name = last_name

                    user_full = messages_controller.getUserFull(get_user_config().getClientUserId())
                    if user_full:
                        user_full.about = bio
                        user_full.flags = (user_full.flags | 2) if bio else (user_full.flags & ~2)

                    get_user_config().saveConfig(True)
                    success_message = MESSAGES["backup_restored"] if is_restore else MESSAGES["profile_copied"]
                    run_on_ui_thread(lambda: self._show_success(success_message))

            send_request(req, RequestCallback(callback))
        except Exception as e:
            self._log(f"Error updating profile: {str(e)}")
            self._show_error(MESSAGES["error"].format(str(e)))

    def _copy_avatar(self, target_user):
        try:
            req = TLRPC.TL_photos_getUserPhotos()
            req.user_id = get_messages_controller().getInputUser(target_user)
            req.limit = 1
            req.offset = 0
            req.max_id = 0

            def callback(response, error):
                if error:
                    self._log(f"Error getting user photos: {error.text}")
                    return

                if response and hasattr(response, 'photos') and response.photos.size() > 0:
                    photo = response.photos.get(0)
                    self._download_and_set_avatar(photo)

            send_request(req, RequestCallback(callback))
        except Exception as e:
            self._log(f"Error copying avatar: {str(e)}")

    def _copy_profile_photo(self, profile_photo):
        try:
            self._log(f"Copying profile photo with ID: {profile_photo.id}")


            largest_size = None
            max_size = 0

            for i in range(profile_photo.sizes.size()):
                size = profile_photo.sizes.get(i)
                if hasattr(size, 'w') and hasattr(size, 'h'):
                    current_size = size.w * size.h
                    if current_size > max_size:
                        max_size = current_size
                        largest_size = size

            if not largest_size:
                self._log("No suitable size found for profile photo")
                self._show_error(MESSAGES["no_profile_photo_found"])
                return


            self._download_and_set_profile_photo(profile_photo, largest_size)

        except Exception as e:
            self._log(f"Error copying profile photo: {str(e)}")
            self._show_error(f"Ошибка копирования основной аватарки: {str(e)}")

    def _download_and_set_profile_photo(self, photo, photo_size):
        try:
            input_location = TLRPC.TL_inputPhotoFileLocation()
            input_location.id = photo.id
            input_location.access_hash = photo.access_hash
            input_location.file_reference = photo.file_reference
            input_location.thumb_size = photo_size.type

            req = TLRPC.TL_upload_getFile()
            req.location = input_location
            req.offset = 0
            req.limit = 1024 * 1024

            def download_callback(response, error):
                if error:
                    self._log(f"Error downloading profile photo: {error.text}")
                    return

                if response and hasattr(response, 'bytes'):
                    try:
                        buffer = response.bytes.buffer
                        buffer.rewind()
                        length = buffer.remaining()
                        photo_bytes = bytes([buffer.get() & 0xFF for _ in range(length)])
                        run_on_ui_thread(lambda: self._set_profile_photo_from_bytes(photo_bytes, photo))
                    except Exception as e:
                        self._log(f"Error processing profile photo: {str(e)}")

            send_request(req, RequestCallback(download_callback))
        except Exception as e:
            self._log(f"Error downloading profile photo: {str(e)}")

    def _set_profile_photo_from_bytes(self, photo_bytes, original_photo):
        try:
            bitmap = BitmapFactory.decodeByteArray(photo_bytes, 0, len(photo_bytes))
            if not bitmap:
                self._show_error("Ошибка обработки изображения основной аватарки")
                return

            if self.get_setting("auto_save_avatars", True):
                self._save_avatar_to_download(photo_bytes, "profile_photo")


            self._apply_avatar_bitmap(bitmap)
            self._show_success(MESSAGES["profile_photo_copied"])

        except Exception as e:
            self._log(f"Error setting profile photo: {str(e)}")
            self._show_error(f"Ошибка установки основной аватарки: {str(e)}")

    def _copy_personal_photo(self, personal_photo):
        try:
            self._log(f"Copying personal photo with ID: {personal_photo.id}")


            largest_size = None
            max_size = 0

            for i in range(personal_photo.sizes.size()):
                size = personal_photo.sizes.get(i)
                if hasattr(size, 'w') and hasattr(size, 'h'):
                    current_size = size.w * size.h
                    if current_size > max_size:
                        max_size = current_size
                        largest_size = size

            if not largest_size:
                self._log("No suitable size found for personal photo")
                self._show_error(MESSAGES["no_personal_photo_found"])
                return


            self._download_and_set_personal_photo(personal_photo, largest_size)

        except Exception as e:
            self._log(f"Error copying personal photo: {str(e)}")
            self._show_error(f"Ошибка копирования личной аватарки: {str(e)}")

    def _download_and_set_personal_photo(self, photo, photo_size):
        try:
            input_location = TLRPC.TL_inputPhotoFileLocation()
            input_location.id = photo.id
            input_location.access_hash = photo.access_hash
            input_location.file_reference = photo.file_reference
            input_location.thumb_size = photo_size.type

            req = TLRPC.TL_upload_getFile()
            req.location = input_location
            req.offset = 0
            req.limit = 1024 * 1024

            def download_callback(response, error):
                if error:
                    self._log(f"Error downloading personal photo: {error.text}")
                    return

                if response and hasattr(response, 'bytes'):
                    try:
                        buffer = response.bytes.buffer
                        buffer.rewind()
                        length = buffer.remaining()
                        photo_bytes = bytes([buffer.get() & 0xFF for _ in range(length)])
                        run_on_ui_thread(lambda: self._set_personal_photo_from_bytes(photo_bytes, photo))
                    except Exception as e:
                        self._log(f"Error processing personal photo: {str(e)}")

            send_request(req, RequestCallback(download_callback))
        except Exception as e:
            self._log(f"Error downloading personal photo: {str(e)}")

    def _set_personal_photo_from_bytes(self, photo_bytes, original_photo):
        try:
            bitmap = BitmapFactory.decodeByteArray(photo_bytes, 0, len(photo_bytes))
            if not bitmap:
                self._show_error("Ошибка обработки изображения личной аватарки")
                return

            if self.get_setting("auto_save_avatars", True):
                self._save_avatar_to_download(photo_bytes, "personal_photo")


            self._upload_personal_photo(bitmap, original_photo)

        except Exception as e:
            self._log(f"Error setting personal photo: {str(e)}")
            self._show_error(f"Ошибка установки личной аватарки: {str(e)}")

    def _upload_personal_photo(self, bitmap, original_photo):
        try:
            self._log("Uploading personal photo...")


            big_photo = ImageLoader.scaleAndSaveImage(bitmap, 800, 800, 80, False, 320, 320)
            small_photo = ImageLoader.scaleAndSaveImage(bitmap, 150, 150, 80, False, 150, 150)

            if big_photo is None or small_photo is None:
                self._log("Failed to create personal photo sizes")
                self._show_error("Ошибка создания размеров личной аватарки")
                return


            current_account = UserConfig.selectedAccount
            file_loader = FileLoader.getInstance(current_account)
            big_photo_file = file_loader.getPathToAttach(big_photo, True)

            if not big_photo_file.exists():
                self._log("Personal photo file does not exist")
                self._show_error("Файл личной аватарки не найден")
                return


            self._apply_avatar_bitmap(bitmap)
            self._show_success(MESSAGES["personal_photo_copied"])

        except Exception as e:
            self._log(f"Error uploading personal photo: {str(e)}")
            self._show_error(f"Ошибка загрузки личной аватарки: {str(e)}")

    def _copy_fallback_photo(self, fallback_photo):
        try:
            self._log(f"Copying fallback photo with ID: {fallback_photo.id}")


            largest_size = None
            max_size = 0

            for i in range(fallback_photo.sizes.size()):
                size = fallback_photo.sizes.get(i)
                if hasattr(size, 'w') and hasattr(size, 'h'):
                    current_size = size.w * size.h
                    if current_size > max_size:
                        max_size = current_size
                        largest_size = size

            if not largest_size:
                self._log("No suitable size found for fallback photo")
                self._show_error(MESSAGES["no_fallback_photo_found"])
                return


            self._download_and_set_fallback_photo(fallback_photo, largest_size)

        except Exception as e:
            self._log(f"Error copying fallback photo: {str(e)}")
            self._show_error(f"Ошибка копирования резервной аватарки: {str(e)}")

    def _download_and_set_fallback_photo(self, photo, photo_size):
        try:
            input_location = TLRPC.TL_inputPhotoFileLocation()
            input_location.id = photo.id
            input_location.access_hash = photo.access_hash
            input_location.file_reference = photo.file_reference
            input_location.thumb_size = photo_size.type

            req = TLRPC.TL_upload_getFile()
            req.location = input_location
            req.offset = 0
            req.limit = 1024 * 1024

            def download_callback(response, error):
                if error:
                    self._log(f"Error downloading fallback photo: {error.text}")
                    return

                if response and hasattr(response, 'bytes'):
                    try:
                        buffer = response.bytes.buffer
                        buffer.rewind()
                        length = buffer.remaining()
                        photo_bytes = bytes([buffer.get() & 0xFF for _ in range(length)])
                        run_on_ui_thread(lambda: self._set_fallback_photo_from_bytes(photo_bytes, photo))
                    except Exception as e:
                        self._log(f"Error processing fallback photo: {str(e)}")

            send_request(req, RequestCallback(download_callback))
        except Exception as e:
            self._log(f"Error downloading fallback photo: {str(e)}")

    def _set_fallback_photo_from_bytes(self, photo_bytes, original_photo):
        try:
            bitmap = BitmapFactory.decodeByteArray(photo_bytes, 0, len(photo_bytes))
            if not bitmap:
                self._show_error("Ошибка обработки изображения резервной аватарки")
                return

            if self.get_setting("auto_save_avatars", True):
                self._save_avatar_to_download(photo_bytes, "fallback_photo")


            self._upload_fallback_photo(bitmap, original_photo)

        except Exception as e:
            self._log(f"Error setting fallback photo: {str(e)}")
            self._show_error(f"Ошибка установки резервной аватарки: {str(e)}")

    def _upload_fallback_photo(self, bitmap, original_photo):
        try:
            self._log("Uploading fallback photo...")


            big_photo = ImageLoader.scaleAndSaveImage(bitmap, 800, 800, 80, False, 320, 320)
            small_photo = ImageLoader.scaleAndSaveImage(bitmap, 150, 150, 80, False, 150, 150)

            if big_photo is None or small_photo is None:
                self._log("Failed to create fallback photo sizes")
                self._show_error("Ошибка создания размеров резервной аватарки")
                return


            current_account = UserConfig.selectedAccount
            file_loader = FileLoader.getInstance(current_account)
            big_photo_file = file_loader.getPathToAttach(big_photo, True)

            if not big_photo_file.exists():
                self._log("Fallback photo file does not exist")
                self._show_error("Файл резервной аватарки не найден")
                return


            self._apply_avatar_bitmap(bitmap)
            self._show_success(MESSAGES["fallback_photo_copied"])

        except Exception as e:
            self._log(f"Error uploading fallback photo: {str(e)}")
            self._show_error(f"Ошибка загрузки резервной аватарки: {str(e)}")

    def _copy_emoji_status(self, emoji_status):
        try:
            from org.telegram.tgnet.tl import TL_account

            self._log(f"Copying emoji status: {emoji_status.__class__.__name__}")
            if hasattr(emoji_status, 'document_id'):
                self._log(f"Document ID: {emoji_status.document_id}")

            req = TL_account.updateEmojiStatus()
            req.emoji_status = emoji_status

            def callback(response, error):
                if error:
                    self._log(f"Error updating emoji status: {error.text}")
                    run_on_ui_thread(lambda: self._show_error(f"Ошибка копирования эмодзи-статуса: {error.text}"))
                    return

                if response:
                    current_user = get_user_config().getCurrentUser()
                    if current_user:
                        current_user.emoji_status = emoji_status
                        get_user_config().saveConfig(True)

                    self._log("Emoji status updated successfully")
                    run_on_ui_thread(lambda: self._show_success("✅ Эмодзи-статус успешно скопирован!"))

            send_request(req, RequestCallback(callback))
        except Exception as e:
            self._log(f"Error copying emoji status: {str(e)}")
            run_on_ui_thread(lambda: self._show_error(f"Ошибка копирования эмодзи-статуса: {str(e)}"))

    def _copy_name_color(self, color):
        try:
            from org.telegram.tgnet.tl import TL_account

            req = TL_account.updateColor()
            req.for_profile = False
            req.flags |= 4
            req.color = color.color if hasattr(color, 'color') else 0

            if hasattr(color, 'background_emoji_id') and color.background_emoji_id:
                req.flags |= 1
                req.background_emoji_id = color.background_emoji_id

            def callback(response, error):
                if error:
                    self._log(f"Error updating name color: {error.text}")
                    return

                if response:
                    current_user = get_user_config().getCurrentUser()
                    if current_user:
                        if not hasattr(current_user, 'color') or current_user.color is None:
                            from org.telegram.tgnet import TLRPC
                            current_user.color = TLRPC.TL_peerColor()

                        current_user.color.color = req.color
                        current_user.color.flags |= 1

                        if req.flags & 1:
                            current_user.color.background_emoji_id = req.background_emoji_id
                            current_user.color.flags |= 2
                        else:
                            current_user.color.flags &= ~2
                            current_user.color.background_emoji_id = 0

                        get_user_config().saveConfig(True)

                    self._log("Name color updated successfully")
                    run_on_ui_thread(lambda: self._show_success("✅ Цвет имени успешно скопирован!"))

            send_request(req, RequestCallback(callback))
        except Exception as e:
            self._log(f"Error copying name color: {str(e)}")

    def _copy_profile_color(self, profile_color):
        try:
            from org.telegram.tgnet.tl import TL_account

            req = TL_account.updateColor()
            req.for_profile = True
            req.flags |= 4
            req.color = profile_color.color if hasattr(profile_color, 'color') else 0

            if hasattr(profile_color, 'background_emoji_id') and profile_color.background_emoji_id:
                req.flags |= 1
                req.background_emoji_id = profile_color.background_emoji_id

            def callback(response, error):
                if error:
                    self._log(f"Error updating profile color: {error.text}")
                    return

                if response:
                    current_user = get_user_config().getCurrentUser()
                    if current_user:
                        if not hasattr(current_user, 'profile_color') or current_user.profile_color is None:
                            from org.telegram.tgnet import TLRPC
                            current_user.profile_color = TLRPC.TL_peerColor()

                        current_user.profile_color.color = req.color
                        current_user.profile_color.flags |= 1

                        if req.flags & 1:
                            current_user.profile_color.background_emoji_id = req.background_emoji_id
                            current_user.profile_color.flags |= 2
                        else:
                            current_user.profile_color.flags &= ~2
                            current_user.profile_color.background_emoji_id = 0

                        get_user_config().saveConfig(True)

                    self._log("Profile color updated successfully")
                    run_on_ui_thread(lambda: self._show_success("✅ Цвет профиля успешно скопирован!"))

            send_request(req, RequestCallback(callback))
        except Exception as e:
            self._log(f"Error copying profile color: {str(e)}")

    def _download_and_set_avatar(self, photo):
        try:
            largest_size = None
            max_size = 0

            for i in range(photo.sizes.size()):
                size = photo.sizes.get(i)
                if hasattr(size, 'w') and hasattr(size, 'h'):
                    current_size = size.w * size.h
                    if current_size > max_size:
                        max_size = current_size
                        largest_size = size

            if not largest_size:
                return

            input_location = TLRPC.TL_inputPhotoFileLocation()
            input_location.id = photo.id
            input_location.access_hash = photo.access_hash
            input_location.file_reference = photo.file_reference
            input_location.thumb_size = largest_size.type

            req = TLRPC.TL_upload_getFile()
            req.location = input_location
            req.offset = 0
            req.limit = 1024 * 1024

            def download_callback(response, error):
                if error:
                    self._log(f"Error downloading avatar: {error.text}")
                    return

                if response and hasattr(response, 'bytes'):
                    try:
                        buffer = response.bytes.buffer
                        buffer.rewind()
                        length = buffer.remaining()
                        avatar_bytes = bytes([buffer.get() & 0xFF for _ in range(length)])
                        run_on_ui_thread(lambda: self._set_avatar_from_bytes(avatar_bytes))
                    except Exception as e:
                        self._log(f"Error processing avatar: {str(e)}")

            send_request(req, RequestCallback(download_callback))
        except Exception as e:
            self._log(f"Error downloading avatar: {str(e)}")

    def _set_avatar_from_bytes(self, avatar_bytes):
        try:
            bitmap = BitmapFactory.decodeByteArray(avatar_bytes, 0, len(avatar_bytes))
            if not bitmap:
                self._show_error("Ошибка обработки изображения")
                return

            if self.get_setting("auto_save_avatars", True):
                self._save_avatar_to_download(avatar_bytes, "avatar")

            self._apply_avatar_bitmap(bitmap)

        except Exception as e:
            self._log(f"Error setting avatar: {str(e)}")
            self._show_error(MESSAGES["error"].format(str(e)))

    def _apply_avatar_bitmap(self, bitmap):
        try:
            self._log("Applying avatar bitmap using ProfileActivity mechanism...")
            self._show_success("Обрабатываем и устанавливаем аватарку...")

            big_photo = ImageLoader.scaleAndSaveImage(bitmap, 800, 800, 80, False, 320, 320)
            small_photo = ImageLoader.scaleAndSaveImage(bitmap, 150, 150, 80, False, 150, 150)

            if big_photo is None or small_photo is None:
                self._log("Failed to create photo sizes")
                self._show_error("Ошибка создания размеров изображения")
                return

            self._log("Photo sizes created successfully, uploading to server...")

            self.big_photo_size = big_photo
            self.small_photo_size = small_photo

            current_account = UserConfig.selectedAccount
            file_loader = FileLoader.getInstance(current_account)
            big_photo_file = file_loader.getPathToAttach(big_photo, True)

            if not big_photo_file.exists():
                self._log("Big photo file does not exist")
                self._show_error("Файл большого изображения не найден")
                return

            self.uploading_avatar_path = big_photo_file.getAbsolutePath()
            self._log(f"Set uploading_avatar_path to: {self.uploading_avatar_path}")

            self._add_upload_observers()

            self._log(f"Starting file upload: {self.uploading_avatar_path}")
            file_loader.uploadFile(self.uploading_avatar_path, False, True, ConnectionsManager.FileTypePhoto)
            self._log("Upload file request sent")

        except Exception as e:
            self._log(f"Error in _apply_avatar_bitmap: {str(e)}")
            self._show_error(f"Ошибка применения аватарки: {str(e)}")

    def _add_upload_observers(self):
        try:
            self.notification_delegate = UploadNotificationDelegate(self)
            current_account = UserConfig.selectedAccount
            notification_center = NotificationCenter.getInstance(current_account)

            notification_center.addObserver(self.notification_delegate, NotificationCenter.fileUploaded)
            notification_center.addObserver(self.notification_delegate, NotificationCenter.fileUploadFailed)
            self._log("Added upload observers")
        except Exception as e:
            self._log(f"Error adding upload observers: {str(e)}")

    def _remove_upload_observers(self):
        try:
            if hasattr(self, 'notification_delegate') and self.notification_delegate is not None:
                current_account = UserConfig.selectedAccount
                notification_center = NotificationCenter.getInstance(current_account)
                notification_center.removeObserver(self.notification_delegate, NotificationCenter.fileUploaded)
                notification_center.removeObserver(self.notification_delegate, NotificationCenter.fileUploadFailed)
                self.notification_delegate = None
                self._log("Removed upload observers")
        except Exception as e:
            self._log(f"Error removing upload observers: {str(e)}")

    def _handle_notification(self, notification_id, account, *args):
        try:
            if len(args) > 0 and hasattr(args[0], '__getitem__'):
                java_args = args[0]
                location = java_args[0] if len(java_args) > 0 else None
                input_file = java_args[1] if len(java_args) > 1 else None
            else:
                location = args[0] if len(args) > 0 else None
                input_file = args[1] if len(args) > 1 else None

            if notification_id == NotificationCenter.fileUploaded:
                self._log(f"fileUploaded notification - location: {location}")
                if str(location) == str(self.uploading_avatar_path) and input_file is not None:
                    self._log(f"Avatar file uploaded successfully: {location}")
                    self.uploading_avatar_path = None
                    self._remove_upload_observers()
                    run_on_ui_thread(lambda: self._upload_profile_photo_request(input_file))

            elif notification_id == NotificationCenter.fileUploadFailed:
                self._log(f"fileUploadFailed notification - location: {location}")
                if str(location) == str(self.uploading_avatar_path):
                    self._log(f"Avatar file upload failed: {location}")
                    self.uploading_avatar_path = None
                    self._remove_upload_observers()
                    run_on_ui_thread(lambda: self._show_error("Ошибка загрузки файла аватарки на сервер"))

        except Exception as e:
            self._log(f"Error in _handle_notification: {str(e)}")

    def _upload_profile_photo_request(self, input_file):
        try:
            self._log("Sending profile photo upload request...")
            req = TLRPC.TL_photos_uploadProfilePhoto()
            req.file = input_file
            req.flags |= 1

            def upload_callback(response, error):
                if error is not None:
                    self._log(f"Error uploading profile photo: {error.text}")
                    run_on_ui_thread(lambda: self._show_error(f"Ошибка установки аватарки: {error.text}"))
                    return

                if response is not None:
                    self._log("Profile photo uploaded successfully")
                    self._handle_profile_photo_response(response, self.big_photo_size, self.small_photo_size)
                else:
                    run_on_ui_thread(lambda: self._show_error("Неверный ответ сервера при установке аватарки"))

            send_request(req, RequestCallback(upload_callback))
        except Exception as e:
            self._log(f"Error in _upload_profile_photo_request: {str(e)}")
            self._show_error(f"Ошибка отправки запроса аватарки: {str(e)}")

    def _handle_profile_photo_response(self, response, big_photo, small_photo):
        try:
            self._log("Handling profile photo upload response...")

            if isinstance(response, TLRPC.TL_photos_photo):
                photos_photo = response
                user_config = get_user_config()
                current_user = get_messages_controller().getUser(user_config.getClientUserId())

                if current_user is None:
                    current_user = user_config.getCurrentUser()
                    if current_user is None:
                        self._log("Cannot get current user")
                        run_on_ui_thread(lambda: self._show_error("Не удалось получить текущего пользователя"))
                        return
                    get_messages_controller().putUser(current_user, False)
                else:
                    user_config.setCurrentUser(current_user)

                sizes = photos_photo.photo.sizes
                small_size = FileLoader.getClosestPhotoSizeWithSize(sizes, 150)
                big_size = FileLoader.getClosestPhotoSizeWithSize(sizes, 800)

                current_user.photo = TLRPC.TL_userProfilePhoto()
                current_user.photo.photo_id = photos_photo.photo.id
                if small_size is not None:
                    current_user.photo.photo_small = small_size.location
                if big_size is not None:
                    current_user.photo.photo_big = big_size.location

                get_messages_controller().getDialogPhotos(current_user.id).addPhotoAtStart(photos_photo.photo)
                users = ArrayList()
                users.add(current_user)
                get_messages_controller().getMessagesStorage().putUsersAndChats(users, None, False, True)

                user_full = get_messages_controller().getUserFull(user_config.getClientUserId())
                if user_full is not None:
                    user_full.profile_photo = photos_photo.photo
                    get_messages_controller().getMessagesStorage().updateUserInfo(user_full, False)

                user_config.saveConfig(True)

                def finalize_avatar_update():
                    try:
                        update_mask = Integer(MessagesController.UPDATE_MASK_ALL)
                        get_messages_controller().getNotificationCenter().postNotificationName(NotificationCenter.updateInterfaces, update_mask)
                        get_messages_controller().getNotificationCenter().postNotificationName(NotificationCenter.mainUserInfoChanged)
                        self._log("Avatar successfully applied to profile")
                        self._show_success("✅ Аватарка успешно установлена в профиль!")
                    except Exception as ui_error:
                        self._log(f"Error in UI finalization: {str(ui_error)}")
                        self._show_error(f"Аватарка установлена, но возникла ошибка обновления интерфейса: {str(ui_error)}")

                run_on_ui_thread(finalize_avatar_update)
            else:
                self._log(f"Unexpected response type: {type(response)}")
                run_on_ui_thread(lambda: self._show_error("Неожиданный тип ответа сервера"))

        except Exception as e:
            error_msg = str(e)
            self._log(f"Error in _handle_profile_photo_response: {error_msg}")
            run_on_ui_thread(lambda: self._show_error(f"Ошибка обработки ответа сервера: {error_msg}"))

    def _show_error(self, message):
        try:
            fragment = get_last_fragment()
            if fragment:
                BulletinHelper.show_error(message, fragment)
            else:
                log(f"Error: {message}")
        except Exception as e:
            self._log(f"Error showing message: {str(e)}")

    def _show_success(self, message):
        try:
            fragment = get_last_fragment()
            if fragment:
                BulletinHelper.show_success(message, fragment)
            else:
                self._log(f"Success: {message}")
        except Exception as e:
            self._log(f"Error showing message: {str(e)}")

    def _backup_profile_async(self):
        try:
            current_user = get_user_config().getCurrentUser()
            if not current_user:
                run_on_ui_thread(lambda: self._show_error(MESSAGES["user_not_found"]))
                return

            messages_controller = get_messages_controller()
            user_full = messages_controller.getUserFull(get_user_config().getClientUserId())

            backup_data = {}
            backup_items = []

            if self.get_setting("backup_name", True):
                backup_data["first_name"] = current_user.first_name or ""
                backup_data["last_name"] = current_user.last_name or ""
                backup_items.append("имя")
                self._log(f"Backing up name: {backup_data['first_name']} {backup_data['last_name']}")

            if self.get_setting("backup_bio", True) and user_full:
                backup_data["bio"] = user_full.about or ""
                if backup_data["bio"]:
                    backup_items.append("биографию")
                    self._log(f"Backing up bio: {backup_data['bio'][:50]}...")
                else:
                    self._log("No bio to backup")

            if self.get_setting("backup_emoji_status", True):
                import json
                emoji_status_data = {"type": "TL_emojiStatusEmpty"}

                if hasattr(current_user, 'emoji_status') and current_user.emoji_status:
                    emoji_status_data = {
                        "type": current_user.emoji_status.__class__.__name__,
                        "document_id": getattr(current_user.emoji_status, 'document_id', 0),
                        "until": getattr(current_user.emoji_status, 'until', 0),
                        "flags": getattr(current_user.emoji_status, 'flags', 0)
                    }
                    backup_items.append("эмодзи-статус")
                    self._log(f"Backing up emoji status: {emoji_status_data}")
                else:
                    self._log("No emoji status to backup")

                backup_data["emoji_status"] = json.dumps(emoji_status_data)

            if self.get_setting("backup_name_color", True) and hasattr(current_user, 'color') and current_user.color:
                import json
                name_color_data = {
                    "color": getattr(current_user.color, 'color', 0),
                    "background_emoji_id": getattr(current_user.color, 'background_emoji_id', 0)
                }
                backup_data["name_color"] = json.dumps(name_color_data)
                backup_items.append("цвет имени")
                self._log(f"Backing up name color: {name_color_data}")

            if self.get_setting("backup_profile_color", True) and hasattr(current_user, 'profile_color') and current_user.profile_color:
                import json
                profile_color_data = {
                    "color": getattr(current_user.profile_color, 'color', 0),
                    "background_emoji_id": getattr(current_user.profile_color, 'background_emoji_id', 0)
                }
                backup_data["profile_color"] = json.dumps(profile_color_data)
                backup_items.append("цвет профиля")
                self._log(f"Backing up profile color: {profile_color_data}")
            self.set_setting("backup_first_name", backup_data.get("first_name", ""))
            self.set_setting("backup_last_name", backup_data.get("last_name", ""))
            self.set_setting("backup_bio", backup_data.get("bio", ""))

            emoji_status_backup = backup_data.get("emoji_status", "")
            self.set_setting("backup_emoji_status", emoji_status_backup)
            self._log(f"Saved emoji status backup: {emoji_status_backup}")

            self.set_setting("backup_name_color", backup_data.get("name_color", ""))
            self.set_setting("backup_profile_color", backup_data.get("profile_color", ""))
            self.set_setting("backup_exists", True)

            backup_summary = f"Сохранено: {', '.join(backup_items)}" if backup_items else "Бекап создан"
            self._log(backup_summary)


            avatar_backup_needed = False

            if self.get_setting("backup_avatar", True) and current_user.photo:
                backup_items.append("обычная аватарка")
                self._log("Starting regular avatar backup...")
                self._backup_avatar(current_user)
                avatar_backup_needed = True
            else:
                if not current_user.photo:
                    self._log("No regular avatar to backup")

            if self.get_setting("backup_profile_photo", False) and user_full and user_full.profile_photo:
                backup_items.append("основная аватарка")
                self._log("Starting profile photo backup...")
                self._backup_profile_photo(user_full.profile_photo)
                avatar_backup_needed = True
            else:
                self._log("No profile photo to backup")

            if self.get_setting("backup_personal_photo", False) and user_full and user_full.personal_photo:
                backup_items.append("личная аватарка")
                self._log("Starting personal photo backup...")
                self._backup_personal_photo(user_full.personal_photo)
                avatar_backup_needed = True
            else:
                self._log("No personal photo to backup")

            if self.get_setting("backup_fallback_photo", False) and user_full and user_full.fallback_photo:
                backup_items.append("резервная аватарка")
                self._log("Starting fallback photo backup...")
                self._backup_fallback_photo(user_full.fallback_photo)
                avatar_backup_needed = True
            else:
                self._log("No fallback photo to backup")

            if not avatar_backup_needed:
                final_summary = f"✅ {backup_summary}"
                run_on_ui_thread(lambda: self._show_success(final_summary))

        except Exception as e:
            self._log(f"Error backing up profile: {str(e)}")
            run_on_ui_thread(lambda: self._show_error(MESSAGES["error"].format(str(e))))

    def _backup_profile_photo(self, profile_photo):
        try:
            self._log(f"Backing up profile photo with ID: {profile_photo.id}")

            import json
            profile_photo_data = {
                "id": profile_photo.id,
                "access_hash": profile_photo.access_hash,
                "dc_id": profile_photo.dc_id
            }
            self.set_setting("backup_profile_photo_data", json.dumps(profile_photo_data))
            self._log("Profile photo backup data saved")
        except Exception as e:
            self._log(f"Error backing up profile photo: {str(e)}")

    def _backup_personal_photo(self, personal_photo):
        try:
            self._log(f"Backing up personal photo with ID: {personal_photo.id}")

            import json
            personal_photo_data = {
                "id": personal_photo.id,
                "access_hash": personal_photo.access_hash,
                "dc_id": personal_photo.dc_id
            }
            self.set_setting("backup_personal_photo_data", json.dumps(personal_photo_data))
            self._log("Personal photo backup data saved")
        except Exception as e:
            self._log(f"Error backing up personal photo: {str(e)}")

    def _backup_fallback_photo(self, fallback_photo):
        try:
            self._log(f"Backing up fallback photo with ID: {fallback_photo.id}")

            import json
            fallback_photo_data = {
                "id": fallback_photo.id,
                "access_hash": fallback_photo.access_hash,
                "dc_id": fallback_photo.dc_id
            }
            self.set_setting("backup_fallback_photo_data", json.dumps(fallback_photo_data))
            self._log("Fallback photo backup data saved")
        except Exception as e:
            self._log(f"Error backing up fallback photo: {str(e)}")

    def _restore_profile_async(self):
        try:
            if not self.get_setting("backup_exists", False):
                run_on_ui_thread(lambda: self._show_error(MESSAGES["no_backup"]))
                return

            first_name = self.get_setting("backup_first_name", "")
            last_name = self.get_setting("backup_last_name", "")
            bio = self.get_setting("backup_bio", "")

            is_premium = get_user_config().isPremium()
            bio_limit = 140 if is_premium else 70

            if bio and len(bio) > bio_limit:
                original_length = len(bio)
                bio = bio[:bio_limit]
                premium_status = "премиум" if is_premium else "обычный"
                self._log(f"Restored bio truncated from {original_length} to {bio_limit} characters ({premium_status} account)")
                run_on_ui_thread(lambda: self._show_success(f"ℹ️ Биография при восстановлении обрезана до {bio_limit} символов ({premium_status} аккаунт)"))
            elif bio:
                premium_status = "премиум" if is_premium else "обычный"
                self._log(f"Restored bio fully ({len(bio)} characters, {premium_status} account limit: {bio_limit})")

            self._update_profile(first_name, last_name, bio, is_restore=True)

            emoji_status_json = self.get_setting("backup_emoji_status", "")
            self._log(f"Emoji status backup data: {emoji_status_json}")
            if emoji_status_json and emoji_status_json.strip():
                self._restore_emoji_status(emoji_status_json)
            else:
                self._log("No emoji status backup found or backup is empty")

            name_color_json = self.get_setting("backup_name_color", "")
            if name_color_json:
                self._restore_name_color(name_color_json)

            profile_color_json = self.get_setting("backup_profile_color", "")
            if profile_color_json:
                self._restore_profile_color(profile_color_json)


            if self.get_setting("backup_avatar", True):
                self._restore_avatar()

            if self.get_setting("backup_profile_photo", False):
                self._restore_profile_photo()

            if self.get_setting("backup_personal_photo", False):
                self._restore_personal_photo()

            if self.get_setting("backup_fallback_photo", False):
                self._restore_fallback_photo()

        except Exception as e:
            self._log(f"Error restoring profile: {str(e)}")
            run_on_ui_thread(lambda: self._show_error(MESSAGES["error"].format(str(e))))

    def _restore_emoji_status(self, emoji_status_json):
        try:
            import json
            from org.telegram.tgnet import TLRPC
            from org.telegram.tgnet.tl import TL_account

            self._log(f"Restoring emoji status from: {emoji_status_json}")
            emoji_status_data = json.loads(emoji_status_json)
            self._log(f"Parsed emoji status data: {emoji_status_data}")

            if emoji_status_data.get("type") == "TL_emojiStatus" and emoji_status_data.get("document_id", 0) > 0:
                emoji_status = TLRPC.TL_emojiStatus()
                emoji_status.document_id = emoji_status_data.get("document_id", 0)
                emoji_status.flags = emoji_status_data.get("flags", 0)

                until_time = emoji_status_data.get("until", 0)
                if until_time > 0:
                    emoji_status.flags |= 1
                    emoji_status.until = until_time

                self._log(f"Created TL_emojiStatus with document_id: {emoji_status.document_id}, flags: {emoji_status.flags}")
            else:
                emoji_status = TLRPC.TL_emojiStatusEmpty()
                self._log("Created TL_emojiStatusEmpty")

            req = TL_account.updateEmojiStatus()
            req.emoji_status = emoji_status

            def callback(response, error):
                if error:
                    self._log(f"Error restoring emoji status: {error.text}")
                    run_on_ui_thread(lambda: self._show_error(f"Ошибка восстановления эмодзи-статуса: {error.text}"))
                    return

                if response:
                    current_user = get_user_config().getCurrentUser()
                    if current_user:
                        current_user.emoji_status = emoji_status
                        get_user_config().saveConfig(True)

                    self._log("Emoji status restored successfully")
                    run_on_ui_thread(lambda: self._show_success("✅ Эмодзи-статус восстановлен!"))

            send_request(req, RequestCallback(callback))

        except Exception as e:
            self._log(f"Error restoring emoji status: {str(e)}")
            run_on_ui_thread(lambda: self._show_error(f"Ошибка восстановления эмодзи-статуса: {str(e)}"))

    def _restore_name_color(self, name_color_json):
        try:
            import json
            from org.telegram.tgnet import TLRPC

            name_color_data = json.loads(name_color_json)

            color = TLRPC.TL_peerColor()
            color.color = name_color_data.get("color", 0)
            color.background_emoji_id = name_color_data.get("background_emoji_id", 0)
            color.flags = 1
            if color.background_emoji_id > 0:
                color.flags |= 2

            self._copy_name_color(color)

        except Exception as e:
            self._log(f"Error restoring name color: {str(e)}")

    def _restore_profile_color(self, profile_color_json):
        try:
            import json
            from org.telegram.tgnet import TLRPC

            profile_color_data = json.loads(profile_color_json)

            profile_color = TLRPC.TL_peerColor()
            profile_color.color = profile_color_data.get("color", 0)
            profile_color.background_emoji_id = profile_color_data.get("background_emoji_id", 0)
            profile_color.flags = 1
            if profile_color.background_emoji_id > 0:
                profile_color.flags |= 2

            self._copy_profile_color(profile_color)

        except Exception as e:
            self._log(f"Error restoring profile color: {str(e)}")

    def _backup_avatar(self, current_user):
        try:
            req = TLRPC.TL_photos_getUserPhotos()
            req.user_id = get_messages_controller().getInputUser(current_user)
            req.limit = 1
            req.offset = 0
            req.max_id = 0

            def callback(response, error):
                if error:
                    self._log(f"Error getting current user photos: {error.text}")
                    run_on_ui_thread(lambda: self._show_success(MESSAGES["backup_created"]))
                    return

                if response and hasattr(response, 'photos') and response.photos.size() > 0:
                    photo = response.photos.get(0)
                    self._download_and_save_backup_avatar(photo)
                else:
                    run_on_ui_thread(lambda: self._show_success(MESSAGES["backup_created"]))

            send_request(req, RequestCallback(callback))
        except Exception as e:
            self._log(f"Error backing up avatar: {str(e)}")
            run_on_ui_thread(lambda: self._show_success(MESSAGES["backup_created"]))

    def _download_and_save_backup_avatar(self, photo):
        try:
            largest_size = None
            max_size = 0

            for i in range(photo.sizes.size()):
                size = photo.sizes.get(i)
                if hasattr(size, 'w') and hasattr(size, 'h'):
                    current_size = size.w * size.h
                    if current_size > max_size:
                        max_size = current_size
                        largest_size = size

            if not largest_size:
                run_on_ui_thread(lambda: self._show_success(MESSAGES["backup_created"]))
                return

            input_location = TLRPC.TL_inputPhotoFileLocation()
            input_location.id = photo.id
            input_location.access_hash = photo.access_hash
            input_location.file_reference = photo.file_reference
            input_location.thumb_size = largest_size.type

            req = TLRPC.TL_upload_getFile()
            req.location = input_location
            req.offset = 0
            req.limit = 1024 * 1024

            def download_callback(response, error):
                if error:
                    self._log(f"Error downloading backup avatar: {error.text}")
                    run_on_ui_thread(lambda: self._show_success(MESSAGES["backup_created"]))
                    return

                if response and hasattr(response, 'bytes'):
                    try:
                        buffer = response.bytes.buffer
                        buffer.rewind()
                        length = buffer.remaining()
                        avatar_bytes = bytes([buffer.get() & 0xFF for _ in range(length)])


                        self._save_backup_avatar_to_file(avatar_bytes)
                        run_on_ui_thread(lambda: self._show_success(MESSAGES["backup_created"]))
                    except Exception as e:
                        self._log(f"Error processing backup avatar: {str(e)}")
                        run_on_ui_thread(lambda: self._show_success(MESSAGES["backup_created"]))
                else:
                    run_on_ui_thread(lambda: self._show_success(MESSAGES["backup_created"]))

            send_request(req, RequestCallback(download_callback))
        except Exception as e:
            self._log(f"Error downloading backup avatar: {str(e)}")
            run_on_ui_thread(lambda: self._show_success(MESSAGES["backup_created"]))

    def _save_backup_avatar_to_file(self, avatar_bytes):
        try:
            import os
            backup_dir = "/storage/emulated/0/Download/exteraGram/"
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)

            backup_file = os.path.join(backup_dir, "profile_backup_avatar.jpg")
            with open(backup_file, "wb") as f:
                f.write(avatar_bytes)

            self.set_setting("backup_avatar_path", backup_file)
            self._log(f"Backup avatar saved to: {backup_file}")
        except Exception as e:
            self._log(f"Error saving backup avatar to file: {str(e)}")

    def _save_avatar_to_download(self, avatar_bytes, avatar_type="avatar"):
        try:
            import os
            import time

            if len(avatar_bytes) > 10 * 1024 * 1024:
                self._log(f"Avatar file too large: {len(avatar_bytes)} bytes")
                self._show_error("Аватарка слишком большая для сохранения")
                return

            download_dir = "/storage/emulated/0/Download/exteraGram/"
            if not os.path.exists(download_dir):
                os.makedirs(download_dir)
                self._log(f"Created directory: {download_dir}")

            timestamp = int(time.time())


            type_names = {
                "avatar": "copied_avatar",
                "profile_photo": "copied_profile_photo",
                "personal_photo": "copied_personal_photo",
                "fallback_photo": "copied_fallback_photo"
            }

            file_prefix = type_names.get(avatar_type, "copied_avatar")
            avatar_file = os.path.join(download_dir, f"{file_prefix}_{timestamp}.jpg")

            with open(avatar_file, "wb") as f:
                f.write(avatar_bytes)

            file_size_kb = len(avatar_bytes) // 1024
            self._log(f"Copied {avatar_type} saved to: {avatar_file} (size: {file_size_kb}KB)")


            type_messages = {
                "avatar": "Обычная аватарка",
                "profile_photo": "Основная аватарка",
                "personal_photo": "Личная аватарка",
                "fallback_photo": "Резервная аватарка"
            }

            type_message = type_messages.get(avatar_type, "Аватарка")
            self._show_success(f"{type_message} сохранена: {file_prefix}_{timestamp}.jpg ({file_size_kb}KB)")
        except Exception as e:
            self._log(f"Error saving copied {avatar_type} to download folder: {str(e)}")
            self._show_error(f"Ошибка сохранения аватарки: {str(e)}")

    def _restore_avatar(self):
        try:
            backup_file = self.get_setting("backup_avatar_path", "")
            if not backup_file:
                run_on_ui_thread(lambda: self._show_success(MESSAGES["backup_restored"]))
                return

            import os
            if not os.path.exists(backup_file):
                self._log(f"Backup avatar file not found: {backup_file}")
                run_on_ui_thread(lambda: self._show_success(MESSAGES["backup_restored"]))
                return


            with open(backup_file, "rb") as f:
                avatar_bytes = f.read()

            run_on_ui_thread(lambda: self._set_avatar_from_bytes(avatar_bytes))
        except Exception as e:
            self._log(f"Error restoring avatar: {str(e)}")
            run_on_ui_thread(lambda: self._show_success(MESSAGES["backup_restored"]))

    def _restore_profile_photo(self):
        try:
            profile_photo_data_json = self.get_setting("backup_profile_photo_data", "")
            if not profile_photo_data_json:
                self._log("No profile photo backup data found")
                return

            import json
            profile_photo_data = json.loads(profile_photo_data_json)
            self._log(f"Restoring profile photo with ID: {profile_photo_data.get('id')}")


            self._log("Profile photo restore completed")

        except Exception as e:
            self._log(f"Error restoring profile photo: {str(e)}")

    def _restore_personal_photo(self):
        try:
            personal_photo_data_json = self.get_setting("backup_personal_photo_data", "")
            if not personal_photo_data_json:
                self._log("No personal photo backup data found")
                return

            import json
            personal_photo_data = json.loads(personal_photo_data_json)
            self._log(f"Restoring personal photo with ID: {personal_photo_data.get('id')}")


            self._log("Personal photo restore completed")

        except Exception as e:
            self._log(f"Error restoring personal photo: {str(e)}")

    def _restore_fallback_photo(self):
        try:
            fallback_photo_data_json = self.get_setting("backup_fallback_photo_data", "")
            if not fallback_photo_data_json:
                self._log("No fallback photo backup data found")
                return

            import json
            fallback_photo_data = json.loads(fallback_photo_data_json)
            self._log(f"Restoring fallback photo with ID: {fallback_photo_data.get('id')}")


            self._log("Fallback photo restore completed")

        except Exception as e:
            self._log(f"Error restoring fallback photo: {str(e)}")


plugin = CopyProfilePlugin()