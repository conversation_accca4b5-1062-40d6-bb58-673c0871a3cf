// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/PeerConnectionFactory

#ifndef org_webrtc_PeerConnectionFactory_JNI
#define org_webrtc_PeerConnectionFactory_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_PeerConnectionFactory[];
const char kClassPath_org_webrtc_PeerConnectionFactory[] = "org/webrtc/PeerConnectionFactory";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_PeerConnectionFactory_00024Options[];
const char kClassPath_org_webrtc_PeerConnectionFactory_00024Options[] =
    "org/webrtc/PeerConnectionFactory$Options";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_PeerConnectionFactory_clazz(nullptr);
#ifndef org_webrtc_PeerConnectionFactory_clazz_defined
#define org_webrtc_PeerConnectionFactory_clazz_defined
inline jclass org_webrtc_PeerConnectionFactory_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_PeerConnectionFactory,
      &g_org_webrtc_PeerConnectionFactory_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_PeerConnectionFactory_00024Options_clazz(nullptr);
#ifndef org_webrtc_PeerConnectionFactory_00024Options_clazz_defined
#define org_webrtc_PeerConnectionFactory_00024Options_clazz_defined
inline jclass org_webrtc_PeerConnectionFactory_00024Options_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_PeerConnectionFactory_00024Options,
      &g_org_webrtc_PeerConnectionFactory_00024Options_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {

static jlong JNI_PeerConnectionFactory_CreateAudioSource(JNIEnv* env, jlong factory,
    const jni_zero::JavaParamRef<jobject>& constraints);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_PeerConnectionFactory_nativeCreateAudioSource(
    JNIEnv* env,
    jclass jcaller,
    jlong factory,
    jobject constraints) {
  return JNI_PeerConnectionFactory_CreateAudioSource(env, factory,
      jni_zero::JavaParamRef<jobject>(env, constraints));
}

static jlong JNI_PeerConnectionFactory_CreateAudioTrack(JNIEnv* env, jlong factory,
    const jni_zero::JavaParamRef<jstring>& id,
    jlong nativeSource);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_PeerConnectionFactory_nativeCreateAudioTrack(
    JNIEnv* env,
    jclass jcaller,
    jlong factory,
    jstring id,
    jlong nativeSource) {
  return JNI_PeerConnectionFactory_CreateAudioTrack(env, factory,
      jni_zero::JavaParamRef<jstring>(env, id), nativeSource);
}

static jlong JNI_PeerConnectionFactory_CreateLocalMediaStream(JNIEnv* env, jlong factory,
    const jni_zero::JavaParamRef<jstring>& label);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_PeerConnectionFactory_nativeCreateLocalMediaStream(
    JNIEnv* env,
    jclass jcaller,
    jlong factory,
    jstring label) {
  return JNI_PeerConnectionFactory_CreateLocalMediaStream(env, factory,
      jni_zero::JavaParamRef<jstring>(env, label));
}

static jlong JNI_PeerConnectionFactory_CreatePeerConnection(JNIEnv* env, jlong factory,
    const jni_zero::JavaParamRef<jobject>& rtcConfig,
    const jni_zero::JavaParamRef<jobject>& constraints,
    jlong nativeObserver,
    const jni_zero::JavaParamRef<jobject>& sslCertificateVerifier);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_PeerConnectionFactory_nativeCreatePeerConnection(
    JNIEnv* env,
    jclass jcaller,
    jlong factory,
    jobject rtcConfig,
    jobject constraints,
    jlong nativeObserver,
    jobject sslCertificateVerifier) {
  return JNI_PeerConnectionFactory_CreatePeerConnection(env, factory,
      jni_zero::JavaParamRef<jobject>(env, rtcConfig), jni_zero::JavaParamRef<jobject>(env,
      constraints), nativeObserver, jni_zero::JavaParamRef<jobject>(env, sslCertificateVerifier));
}

static jni_zero::ScopedJavaLocalRef<jobject>
    JNI_PeerConnectionFactory_CreatePeerConnectionFactory(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& context,
    const jni_zero::JavaParamRef<jobject>& options,
    jlong nativeAudioDeviceModule,
    jlong audioEncoderFactory,
    jlong audioDecoderFactory,
    const jni_zero::JavaParamRef<jobject>& encoderFactory,
    const jni_zero::JavaParamRef<jobject>& decoderFactory,
    jlong nativeAudioProcessor,
    jlong nativeFecControllerFactory,
    jlong nativeNetworkControllerFactory,
    jlong nativeNetworkStatePredictorFactory,
    jlong neteqFactory);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnectionFactory_nativeCreatePeerConnectionFactory(
    JNIEnv* env,
    jclass jcaller,
    jobject context,
    jobject options,
    jlong nativeAudioDeviceModule,
    jlong audioEncoderFactory,
    jlong audioDecoderFactory,
    jobject encoderFactory,
    jobject decoderFactory,
    jlong nativeAudioProcessor,
    jlong nativeFecControllerFactory,
    jlong nativeNetworkControllerFactory,
    jlong nativeNetworkStatePredictorFactory,
    jlong neteqFactory) {
  return JNI_PeerConnectionFactory_CreatePeerConnectionFactory(env,
      jni_zero::JavaParamRef<jobject>(env, context), jni_zero::JavaParamRef<jobject>(env, options),
      nativeAudioDeviceModule, audioEncoderFactory, audioDecoderFactory,
      jni_zero::JavaParamRef<jobject>(env, encoderFactory), jni_zero::JavaParamRef<jobject>(env,
      decoderFactory), nativeAudioProcessor, nativeFecControllerFactory,
      nativeNetworkControllerFactory, nativeNetworkStatePredictorFactory, neteqFactory).Release();
}

static jlong JNI_PeerConnectionFactory_CreateVideoSource(JNIEnv* env, jlong factory,
    jboolean is_screencast,
    jboolean alignTimestamps);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_PeerConnectionFactory_nativeCreateVideoSource(
    JNIEnv* env,
    jclass jcaller,
    jlong factory,
    jboolean is_screencast,
    jboolean alignTimestamps) {
  return JNI_PeerConnectionFactory_CreateVideoSource(env, factory, is_screencast, alignTimestamps);
}

static jlong JNI_PeerConnectionFactory_CreateVideoTrack(JNIEnv* env, jlong factory,
    const jni_zero::JavaParamRef<jstring>& id,
    jlong nativeVideoSource);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_PeerConnectionFactory_nativeCreateVideoTrack(
    JNIEnv* env,
    jclass jcaller,
    jlong factory,
    jstring id,
    jlong nativeVideoSource) {
  return JNI_PeerConnectionFactory_CreateVideoTrack(env, factory,
      jni_zero::JavaParamRef<jstring>(env, id), nativeVideoSource);
}

static void JNI_PeerConnectionFactory_DeleteLoggable(JNIEnv* env);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnectionFactory_nativeDeleteLoggable(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_PeerConnectionFactory_DeleteLoggable(env);
}

static jni_zero::ScopedJavaLocalRef<jstring>
    JNI_PeerConnectionFactory_FindFieldTrialsFullName(JNIEnv* env, const
    jni_zero::JavaParamRef<jstring>& name);

JNI_BOUNDARY_EXPORT jstring Java_org_webrtc_PeerConnectionFactory_nativeFindFieldTrialsFullName(
    JNIEnv* env,
    jclass jcaller,
    jstring name) {
  return JNI_PeerConnectionFactory_FindFieldTrialsFullName(env, jni_zero::JavaParamRef<jstring>(env,
      name)).Release();
}

static void JNI_PeerConnectionFactory_FreeFactory(JNIEnv* env, jlong factory);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnectionFactory_nativeFreeFactory(
    JNIEnv* env,
    jclass jcaller,
    jlong factory) {
  return JNI_PeerConnectionFactory_FreeFactory(env, factory);
}

static jlong JNI_PeerConnectionFactory_GetNativePeerConnectionFactory(JNIEnv* env, jlong factory);

JNI_BOUNDARY_EXPORT jlong
    Java_org_webrtc_PeerConnectionFactory_nativeGetNativePeerConnectionFactory(
    JNIEnv* env,
    jclass jcaller,
    jlong factory) {
  return JNI_PeerConnectionFactory_GetNativePeerConnectionFactory(env, factory);
}

static jni_zero::ScopedJavaLocalRef<jobject>
    JNI_PeerConnectionFactory_GetRtpReceiverCapabilities(JNIEnv* env, jlong factory,
    const jni_zero::JavaParamRef<jobject>& mediaType);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnectionFactory_nativeGetRtpReceiverCapabilities(
    JNIEnv* env,
    jclass jcaller,
    jlong factory,
    jobject mediaType) {
  return JNI_PeerConnectionFactory_GetRtpReceiverCapabilities(env, factory,
      jni_zero::JavaParamRef<jobject>(env, mediaType)).Release();
}

static jni_zero::ScopedJavaLocalRef<jobject>
    JNI_PeerConnectionFactory_GetRtpSenderCapabilities(JNIEnv* env, jlong factory,
    const jni_zero::JavaParamRef<jobject>& mediaType);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnectionFactory_nativeGetRtpSenderCapabilities(
    JNIEnv* env,
    jclass jcaller,
    jlong factory,
    jobject mediaType) {
  return JNI_PeerConnectionFactory_GetRtpSenderCapabilities(env, factory,
      jni_zero::JavaParamRef<jobject>(env, mediaType)).Release();
}

static void JNI_PeerConnectionFactory_InitializeAndroidGlobals(JNIEnv* env);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnectionFactory_nativeInitializeAndroidGlobals(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_PeerConnectionFactory_InitializeAndroidGlobals(env);
}

static void JNI_PeerConnectionFactory_InitializeFieldTrials(JNIEnv* env, const
    jni_zero::JavaParamRef<jstring>& fieldTrialsInitString);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnectionFactory_nativeInitializeFieldTrials(
    JNIEnv* env,
    jclass jcaller,
    jstring fieldTrialsInitString) {
  return JNI_PeerConnectionFactory_InitializeFieldTrials(env, jni_zero::JavaParamRef<jstring>(env,
      fieldTrialsInitString));
}

static void JNI_PeerConnectionFactory_InitializeInternalTracer(JNIEnv* env);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnectionFactory_nativeInitializeInternalTracer(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_PeerConnectionFactory_InitializeInternalTracer(env);
}

static void JNI_PeerConnectionFactory_InjectLoggable(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jniLogging,
    jint severity);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnectionFactory_nativeInjectLoggable(
    JNIEnv* env,
    jclass jcaller,
    jobject jniLogging,
    jint severity) {
  return JNI_PeerConnectionFactory_InjectLoggable(env, jni_zero::JavaParamRef<jobject>(env,
      jniLogging), severity);
}

static void JNI_PeerConnectionFactory_PrintStackTrace(JNIEnv* env, jint tid);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnectionFactory_nativePrintStackTrace(
    JNIEnv* env,
    jclass jcaller,
    jint tid) {
  return JNI_PeerConnectionFactory_PrintStackTrace(env, tid);
}

static void JNI_PeerConnectionFactory_ShutdownInternalTracer(JNIEnv* env);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnectionFactory_nativeShutdownInternalTracer(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_PeerConnectionFactory_ShutdownInternalTracer(env);
}

static jboolean JNI_PeerConnectionFactory_StartAecDump(JNIEnv* env, jlong factory,
    jint file_descriptor,
    jint filesize_limit_bytes);

JNI_BOUNDARY_EXPORT jboolean Java_org_webrtc_PeerConnectionFactory_nativeStartAecDump(
    JNIEnv* env,
    jclass jcaller,
    jlong factory,
    jint file_descriptor,
    jint filesize_limit_bytes) {
  return JNI_PeerConnectionFactory_StartAecDump(env, factory, file_descriptor,
      filesize_limit_bytes);
}

static jboolean JNI_PeerConnectionFactory_StartInternalTracingCapture(JNIEnv* env, const
    jni_zero::JavaParamRef<jstring>& tracingFilename);

JNI_BOUNDARY_EXPORT jboolean
    Java_org_webrtc_PeerConnectionFactory_nativeStartInternalTracingCapture(
    JNIEnv* env,
    jclass jcaller,
    jstring tracingFilename) {
  return JNI_PeerConnectionFactory_StartInternalTracingCapture(env,
      jni_zero::JavaParamRef<jstring>(env, tracingFilename));
}

static void JNI_PeerConnectionFactory_StopAecDump(JNIEnv* env, jlong factory);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnectionFactory_nativeStopAecDump(
    JNIEnv* env,
    jclass jcaller,
    jlong factory) {
  return JNI_PeerConnectionFactory_StopAecDump(env, factory);
}

static void JNI_PeerConnectionFactory_StopInternalTracingCapture(JNIEnv* env);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnectionFactory_nativeStopInternalTracingCapture(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_PeerConnectionFactory_StopInternalTracingCapture(env);
}


static std::atomic<jmethodID> g_org_webrtc_PeerConnectionFactory_Constructor1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_PeerConnectionFactory_Constructor(JNIEnv* env,
    jlong nativeFactory) {
  jclass clazz = org_webrtc_PeerConnectionFactory_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_PeerConnectionFactory_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(J)V",
          &g_org_webrtc_PeerConnectionFactory_Constructor1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, nativeFactory);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_PeerConnectionFactory_onNetworkThreadReady0(nullptr);
static void Java_PeerConnectionFactory_onNetworkThreadReady(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnectionFactory_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnectionFactory_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onNetworkThreadReady",
          "()V",
          &g_org_webrtc_PeerConnectionFactory_onNetworkThreadReady0);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID> g_org_webrtc_PeerConnectionFactory_onSignalingThreadReady0(nullptr);
static void Java_PeerConnectionFactory_onSignalingThreadReady(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnectionFactory_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnectionFactory_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onSignalingThreadReady",
          "()V",
          &g_org_webrtc_PeerConnectionFactory_onSignalingThreadReady0);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID> g_org_webrtc_PeerConnectionFactory_onWorkerThreadReady0(nullptr);
static void Java_PeerConnectionFactory_onWorkerThreadReady(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnectionFactory_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnectionFactory_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onWorkerThreadReady",
          "()V",
          &g_org_webrtc_PeerConnectionFactory_onWorkerThreadReady0);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnectionFactory_00024Options_getDisableEncryption0(nullptr);
static jboolean Java_Options_getDisableEncryption(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj) {
  jclass clazz = org_webrtc_PeerConnectionFactory_00024Options_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnectionFactory_00024Options_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getDisableEncryption",
          "()Z",
          &g_org_webrtc_PeerConnectionFactory_00024Options_getDisableEncryption0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnectionFactory_00024Options_getDisableNetworkMonitor0(nullptr);
static jboolean Java_Options_getDisableNetworkMonitor(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj) {
  jclass clazz = org_webrtc_PeerConnectionFactory_00024Options_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnectionFactory_00024Options_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getDisableNetworkMonitor",
          "()Z",
          &g_org_webrtc_PeerConnectionFactory_00024Options_getDisableNetworkMonitor0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnectionFactory_00024Options_getNetworkIgnoreMask0(nullptr);
static jint Java_Options_getNetworkIgnoreMask(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnectionFactory_00024Options_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnectionFactory_00024Options_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getNetworkIgnoreMask",
          "()I",
          &g_org_webrtc_PeerConnectionFactory_00024Options_getNetworkIgnoreMask0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_PeerConnectionFactory_JNI
