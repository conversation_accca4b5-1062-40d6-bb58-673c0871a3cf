from base_plugin import BasePlugin, HookResult, HookStrategy
from ui.settings import Header, Switch, Input
from android_utils import log
from java.util import Locale

__id__ = "simple_anonymous"
__name__ = "Простая анонимизация"
__description__ = "Максимально простой плагин для замены имен пользователей"
__author__ = "@exteraDev"
__version__ = "1.0.0"
__icon__ = "exteraPlugins/7"
__min_version__ = "11.9.0"

class SimpleAnonymousPlugin(BasePlugin):
    def on_plugin_load(self):
        self.log("🚀 Загрузка простого плагина анонимизации...")
        # Регистрируем только самые основные хуки
        self.add_hook("TL_updateUserName")
        self.add_hook("TL_users")
        self.log("✅ Плагин загружен")

    def create_settings(self):
        return [
            Header(text="Простая анонимизация"),
            Switch(
                key="enabled",
                text="Включить анонимизацию",
                default=True
            ),
            Input(
                key="text",
                text="Анонимный текст",
                default="Анонимный пользователь"
            )
        ]

    def post_request_hook(self, request_name, account, response, error):
        if not self.get_setting("enabled", True) or error:
            return HookResult()

        try:
            modified = False
            anonymous_text = self.get_setting("text", "Анонимный пользователь")

            # Обрабатываем пользователей в ответе
            if hasattr(response, 'users') and response.users:
                for i in range(response.users.size()):
                    user = response.users.get(i)
                    if hasattr(user, 'first_name'):
                        user.first_name = anonymous_text
                        modified = True
                    if hasattr(user, 'last_name'):
                        user.last_name = ""
                        modified = True

            if hasattr(response, 'user') and response.user:
                if hasattr(response.user, 'first_name'):
                    response.user.first_name = anonymous_text
                    modified = True
                if hasattr(response.user, 'last_name'):
                    response.user.last_name = ""
                    modified = True

            if modified:
                self.log(f"✅ Заменены имена на: {anonymous_text}")
                return HookResult(strategy=HookStrategy.MODIFY, response=response)

        except Exception as e:
            self.log(f"❌ Ошибка: {str(e)}")

        return HookResult()

    def on_update_hook(self, update_name, account, update):
        if not self.get_setting("enabled", True):
            return HookResult()

        try:
            anonymous_text = self.get_setting("text", "Анонимный пользователь")

            if update_name == "TL_updateUserName":
                if hasattr(update, 'first_name'):
                    update.first_name = anonymous_text
                if hasattr(update, 'last_name'):
                    update.last_name = ""
                self.log(f"✅ Обновление имени заменено на: {anonymous_text}")
                return HookResult(strategy=HookStrategy.MODIFY, update=update)

        except Exception as e:
            self.log(f"❌ Ошибка в обновлении: {str(e)}")

        return HookResult()

    def on_updates_hook(self, container_name, account, updates):
        if not self.get_setting("enabled", True):
            return HookResult()

        try:
            modified = False
            anonymous_text = self.get_setting("text", "Анонимный пользователь")

            if hasattr(updates, 'users') and updates.users:
                for i in range(updates.users.size()):
                    user = updates.users.get(i)
                    if hasattr(user, 'first_name'):
                        user.first_name = anonymous_text
                        modified = True
                    if hasattr(user, 'last_name'):
                        user.last_name = ""
                        modified = True

            if modified:
                self.log(f"✅ Контейнер обновлен, имена заменены на: {anonymous_text}")
                return HookResult(strategy=HookStrategy.MODIFY, updates=updates)

        except Exception as e:
            self.log(f"❌ Ошибка в контейнере: {str(e)}")

        return HookResult()