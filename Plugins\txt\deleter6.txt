import re
import traceback
import typing

from android_utils import log, run_on_ui_thread
from base_plugin import <PERSON><PERSON><PERSON>ult, HookStrategy, BasePlugin
from client_utils import (
    run_on_queue, get_last_fragment, send_message,
    get_messages_controller, send_request, get_user_config,
    get_account_instance
)
from ui.alert import AlertDialog<PERSON>uilder
from ui.bulletin import BulletinHelper
import ui.settings

from android.content import Intent
from android.net import Uri
import java
from java.util import ArrayList, Locale
from org.telegram.tgnet import TLRPC
from org.telegram.messenger import MessageObject, ApplicationLoader, AndroidUtilities
from org.telegram.ui.ActionBar import AlertDialog

__id__ = "deleter"
__name__ = "Deleter"
__description__ = "Plugin for fast-delete your messages"
__icon__ = "etgPlugins/1"
__version__ = "1.0.2 (********)"
__author__ = "@etgPlugins"
__min_version__ = "11.9.1"


# Thanks to reMusic (@reNightly, @itsv1eds, @qmrrchh, channel: @meeowPlugins)
class AlertManager:
    alert_builder_instance: AlertDialogBuilder = None

    def copy(self, value):
        if AndroidUtilities.addToClipboard(value):
            BulletinHelper.show_copied_to_clipboard()

    def _open_link(self, url):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        run_on_ui_thread(lambda: ctx.startActivity(intent))

    def _dismiss_dialog(self, builder_instance: AlertDialogBuilder):
        def action():
            if builder_instance is not None:
                try:
                    dlg = builder_instance.getDialog() if hasattr(builder_instance, 'getDialog') else builder_instance
                    if dlg and dlg.isShowing():
                        dlg.dismiss()
                except Exception:
                    pass
                finally:
                    self.alert_builder_instance = None

        run_on_ui_thread(action)

    def show_info_alert(
        self,
        title: str = "TITLE",
        message: str = "MESSAGE",
        positive_button: str = "OK",
        neutral_button: str = None,
        neutral_link: str = None,
        neutral_type: str = None
    ):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        builder = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
        self.alert_builder_instance = builder
        builder.set_title(title)
        builder.set_message(message)
        builder.set_positive_button(positive_button, self._dismiss_dialog(self.alert_builder_instance))
        if neutral_button:
            builder.set_neutral_button(
                neutral_button,
                lambda builder, which: self._open_link(
                    neutral_link
                ) if neutral_type == "link" else self.copy(neutral_link)
            )
        self.alert_builder_instance = builder.show()



class DeleterPlugin(BasePlugin):
    raw_strings: dict = {
        "en": {
            "min_msgs": "The count of messages must be greater than or equal to 1",
            "deleted": "{count} message(s) was deleted",
            "settings": {
                "title": f"{__name__} settings",
                "alias": {
                    "desc": "Alias for delete messages",
                    "full_desc": "The command alias that will be used to delete messages (default is «.delmsg»)",
                    "default": ".delmsg"
                },
                "usage": "Usage",
                "help": "{alias} <count> - delete <count> of your previous messages"
            }
        },
        "ru": {
            "min_msgs": "Количество сообщений должно быть больше или равно 1",
            "deleted": "Удалено {count} сообщений",
            "settings": {
                "title": f"Настройки плагина {__name__}",
                "alias": {
                    "desc": "Алиас для удаления сообщений",
                    "full_desc": "Алиас команды, используемый для удаления сообщений (по умолчанию «.дд»)",
                    "default": ".дд"
                },
                "usage": "Использование",
                "help": "{alias} <количество> — удалить <количество> твоих предыдущих сообщений"
            }
        }
    }

    def __init__(self):
        super().__init__()
        self.progress_dialog = None
        self.am: AlertManager = AlertManager()

    def on_plugin_load(self):
        self.user_config = get_user_config()
        self.add_on_send_message_hook(100)
        log(f"{__name__} plugin loaded successfully") 

    def on_plugin_unload(self):
        log(f"{__name__} plugin unloaded")

    def create_settings(self):
        self.strings: dict = self.raw_strings[
            'ru' if Locale.getDefault().getLanguage().startswith("ru") else 'en'
        ]

        return [
            ui.settings.Header(text=__name__),
            ui.settings.Text(
                text=self.strings['settings']['usage'],
                icon="msg_info",
                on_click=lambda view: self.am.show_info_alert(
                    title=self.strings['settings']['usage'],
                    message=self.strings['settings']['help'].format(
                        alias=self.get_setting("alias", self.strings['settings']['alias']['default'])
                    )
                )
            ),
            ui.settings.Input(
                key="alias", icon="msg_edit",
                text=str(self.strings['settings']['alias']['desc']),
                subtext=str(self.strings['settings']['alias']['full_desc']),
                default=str(self.strings['settings']['alias']['default'])
            )
        ]


    def delete_message(self, messages: typing.List[int], chat_id: int, topic_id: int = 0):
        msgs = ArrayList()
        for i in messages:
            msgs.add(java.jint(i))
        get_messages_controller().deleteMessages(msgs, None, None, chat_id, topic_id, True, 0)


    def on_send_message_hook(self, account, params) -> HookStrategy:
        alias_cmd = self.get_setting("alias", self.strings['settings']['alias']['default'])
        message: str = getattr(params, "message", "")
        if not message or not message.startswith(alias_cmd):
            return HookResult()

        pattern = rf"{re.escape(alias_cmd)}\s*(\d+)"
        matching = re.search(pattern, message)
        self.count = 1
        if matching:
            self.count = int(matching.group(1))
        if self.count < 1:
            BulletinHelper.show_error(self.strings['min_msgs'])
            return HookResult()

        try:
            self.progress_dialog = AlertDialog(get_last_fragment().getParentActivity(), 3)
            self.progress_dialog.show()
            run_on_queue(lambda: self.delmsg(params))
            return HookResult(strategy=HookStrategy.CANCEL)
        except Exception as e:
            log(f"Deleter plugin error:\n{traceback.format_exc()}")
            BulletinHelper.show_error(str(e))
            return HookResult(strategy=HookStrategy.CANCEL)


    def cb(self, resp, err, params = None):
        try:
            if resp:
                uid = self.user_config.getClientUserId()
                msgs_to_del = []
                messages = list(resp.messages.toArray())
                for msg in messages:
                    if msg.from_id.user_id == uid:
                        log(msg.message)
                        msgs_to_del.append(msg.id)
                    if len(msgs_to_del) == self.count:
                        break
                run_on_ui_thread(lambda: self.delete_message(
                    msgs_to_del, params.peer,
                    MessageObject.getTopicId(
                        get_account_instance, params.replyToTopMsg.messageOwner
                    ) if params.replyToMsg else 0
                ))
                return True
            if err:
                BulletinHelper.show_error(str(err))
                return False
        except Exception as e:
            log(f"Deleter plugin error:\n{traceback.format_exc()}")
            BulletinHelper.show_error(str(e))
            return False


    def delmsg(self, params):
        req = TLRPC.TL_messages_getHistory()
        req.peer = get_messages_controller().getInputPeer(params.peer)
        req.limit = 100+self.count
        req.offset_id = 0
        def process():
            self.progress_dialog.dismiss()
            send_request(req, lambda resp, err: self.cb(resp, err, params))
            BulletinHelper.show_info(self.strings['deleted'].format(count=self.count))
        run_on_ui_thread(process)