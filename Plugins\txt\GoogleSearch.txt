import requests
from base_plugin import <PERSON><PERSON>lug<PERSON>, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from client_utils import run_on_queue, send_message, get_send_messages_helper
import os
import uuid
import re
from ui.settings import Switch, Selector, Input, Text, Header, Divider

__id__ = "google_search"
__name__ = "Google Search"
__description__ = "Searching for information via Google [.g], searching pic's via Google [.gi]"
__author__ = "семятеч"
__min_version__ = "11.12.0"
__version__ = "1.0"
__icon__ = "googleemojikaif/0"

#api ключи
GOOGLE_API_KEY = "AIzaSyBIfmOC77W907oVKw9vMTLgdOgdM2kjQXE"
GOOGLE_CX = "65c56934c80f0453a"
API_BASE_URL = "https://www.googleapis.com/customsearch/v1"

class GoogleSearch:
    def __init__(self):
        self.history = []
        
    def search(self, query):
        try:
            params = {
                "key": GOOGLE_API_KEY,
                "cx": GOOGLE_CX,
                "q": query,
                "num": 5  
            }
            
            response = requests.get(API_BASE_URL, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            if "items" in data:
                results = []
                for item in data["items"]:
                    title = item.get("title", "Без названия")
                    snippet = item.get("snippet", "Нет описания")
                    link = item.get("link", "")
                    
                    results.append(f"📍 {title}\n📝 {snippet}\n🔗 {link}\n")
                
                result_text = "\n".join(results)
                self.history.append({"query": query, "results": result_text})
                return f"🔍 Результаты поиска:\n\n{result_text}"
            return "По вашему запросу ничего не найдено"
            
        except Exception as e:
            print(f"Ошибка Google API: {str(e)}")
            return None

    def search_images(self, query):
        try:
            params = {
                "key": GOOGLE_API_KEY,
                "cx": GOOGLE_CX,
                "q": query,
                "num": 5,
                "searchType": "image"
            }
            response = requests.get(API_BASE_URL, params=params)
            response.raise_for_status()
            data = response.json()
            if "items" in data:
                return data["items"]  # Возвращаем список картинок
            return []
        except Exception as e:
            print(f"Ошибка Google Image API: {str(e)}")
            return []

    def download_image(self, url, save_dir):
        try:
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)
            filename = f"google_img_{uuid.uuid4()}.jpg"
            save_path = os.path.join(save_dir, filename)
            response = requests.get(url, stream=True, timeout=15)
            if response.status_code != 200:
                error_msg = f"Ошибка загрузки изображения: код {response.status_code}"
                print(error_msg)
                return None, error_msg
            content_type = response.headers.get('Content-Type', '')
            if not content_type.startswith('image/'):
                error_msg = "URL не вернул изображение"
                print(error_msg)
                return None, error_msg
            with open(save_path, 'wb') as f:
                wrote = False
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        wrote = True
                if not wrote:
                    error_msg = "Не удалось сохранить файл изображения"
                    print(error_msg)
                    return None, error_msg
            if not os.path.exists(save_path) or os.path.getsize(save_path) == 0:
                error_msg = "Файл изображения не создан или пустой"
                print(error_msg)
                return None, error_msg
            return save_path, None
        except Exception as e:
            error_msg = f"Ошибка при скачивании изображения: {e}"
            print(error_msg)
            return None, error_msg

class GoogleSearchPlugin(BasePlugin):
    priority = 1 # или другое максимальное значение

    def __init__(self):
        super().__init__()
        self.searcher = GoogleSearch()

    @classmethod
    def settings(cls):
        return [
            Switch(
                key="safe_search",
                text="Безопасный поиск (SafeSearch)",
                default=True,
                subtext="Блокировать непристойные запросы"
            ),
            Selector(
                key="num_results",
                text="Количество результатов",
                default=5,
                items=[str(i) for i in range(1, 11)],
                icon=None
            ),
            Input(
                key="lang",
                text="Язык (lr)",
                default="lang_ru",
                subtext="Например: lang_ru, lang_en"
            ),
            Input(
                key="country",
                text="Страна (cr)",
                default="countryRU",
                subtext="Например: countryRU, countryUS"
            ),
            Selector(
                key="sort",
                text="Сортировка",
                default=0,
                items=["", "date"],
                icon=None
            ),
            Input(
                key="file_type",
                text="Тип файла (fileType)",
                default="",
                subtext="Например: pdf, docx, txt"
            ),
            Input(
                key="site_search",
                text="Поиск по сайту (siteSearch)",
                default="",
                subtext="Например: wikipedia.org"
            ),
            Input(
                key="exact_terms",
                text="Точное совпадение (exactTerms)",
                default="",
                subtext="Ключевое слово для точного совпадения"
            )
        ]

    @classmethod
    def create_settings(cls):
        return cls.settings()

    def get_search_params(self, query, search_type=None):
        params = {
            "key": GOOGLE_API_KEY,
            "cx": GOOGLE_CX,
            "q": query,
            "num": int(self.get_setting("num_results", 5)),
        }
        lang = self.get_setting("lang", "lang_ru")
        if lang:
            params["lr"] = lang
        country = self.get_setting("country", "countryRU")
        if country:
            params["cr"] = country
        sort = ["", "date"][self.get_setting("sort", 0)]
        if sort:
            params["sort"] = sort
        file_type = self.get_setting("file_type", "")
        if file_type:
            params["fileType"] = file_type
        site_search = self.get_setting("site_search", "")
        if site_search:
            params["siteSearch"] = site_search
        exact_terms = self.get_setting("exact_terms", "")
        if exact_terms:
            params["exactTerms"] = exact_terms
        if search_type == "image":
            params["searchType"] = "image"
        return params

    def is_obscene(self, query):
        obscene_words = [
            "порно", "sex", "секс", "эротика", "porn", "xxx", "гей", "лесби", "anal", "анальный", "oral", "оргазм", "эрекция", "эротический", "эротика", "жопа", "сиськи", "член", "пенис", "вагина", "vagina", "dick", "pussy", "cum", "orgasm", "masturb", "masturbation", "masturbate"
        ]
        q = query.lower()
        return any(word in q for word in obscene_words)

    def process_query(self, query, peer):
        params = self.get_search_params(query)
        num_results = int(self.get_setting("num_results", 5))
        try:
            response = requests.get(API_BASE_URL, params=params)
            response.raise_for_status()
            data = response.json()
            if "items" in data:
                results = []
                for item in data["items"][:num_results]:
                    title = item.get("title", "Без названия")
                    snippet = item.get("snippet", "Нет описания")
                    link = item.get("link", "")
                    results.append(f"📍 {title}\n📝 {snippet}\n🔗 {link}\n")
                result_text = "\n".join(results)
                self.searcher.history.append({"query": query, "results": result_text})
                message = f"🔍 Результаты поиска:\n\n{result_text}"
            else:
                message = "По вашему запросу ничего не найдено"
        except Exception as e:
            print(f"Ошибка Google API: {str(e)}")
            message = "Ошибка при выполнении поиска"
        params = {
            "message": message,
            "peer": peer
        }
        send_message(params)

    def process_image_query(self, query, peer):
        params = self.get_search_params(query, search_type="image")
        try:
            response = requests.get(API_BASE_URL, params=params)
            response.raise_for_status()
            data = response.json()
            items = data.get("items", [])
        except Exception as e:
            print(f"Ошибка Google Image API: {str(e)}")
            items = []
        if not items:
            message = "Не удалось получить результаты поиска картинок"
            params = {
                "message": message,
                "peer": peer
            }
            send_message(params)
            return
        send_helper = get_send_messages_helper()
        save_dir = "/storage/emulated/0/Download/google_images"
        item = items[0]
        image_url = item.get("link")
        if image_url:
            image_path, error = self.searcher.download_image(image_url, save_dir)
            if image_path:
                generated_photo = send_helper.generatePhotoSizes(image_path, None)
                if generated_photo:
                    message = f"Источник: {image_url}"
                    params = {
                        "photos": [generated_photo],
                        "peer": peer,
                        "message": message
                    }
                    send_message(params)
                    return
        message = "Не удалось найти и обработать картинку"
        params = {
            "message": message,
            "peer": peer
        }
        send_message(params)

    def on_send_message_hook(self, account, params) -> HookStrategy:
        if not isinstance(params.message, str):
            return HookResult()
        if params.message.startswith(".g ") or params.message.startswith(".gi "):
            try:
                parts = params.message.strip().split(" ", 1)
                if len(parts) < 2 or not parts[1].strip():
                    params.message = f"Использование: {parts[0]} [поисковый запрос]"
                    return HookResult(strategy=HookStrategy.MODIFY, params=params)
                query = parts[1].strip()
                safe_search = self.get_setting("safe_search", True)
                if safe_search and self.is_obscene(query):
                    params.message = "Заблокировано параметрами поиска"
                    return HookResult(strategy=HookStrategy.MODIFY, params=params)
                if params.message.startswith(".g "):
                    run_on_queue(lambda: self.process_query(query, params.peer))
                else:
                    run_on_queue(lambda: self.process_image_query(query, params.peer))
                return HookResult(strategy=HookStrategy.CANCEL)
            except Exception as e:
                print(f"Google Search plugin error: {str(e)}")
                params.message = f"Ошибка: {str(e)}"
                return HookResult(strategy=HookStrategy.MODIFY, params=params)
        return HookResult()

    def on_plugin_load(self):
        self.add_on_send_message_hook(priority=self.priority)