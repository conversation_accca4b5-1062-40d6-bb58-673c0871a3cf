import time

import json

import traceback

from base_plugin import BasePlugin, MenuItemData, MenuItemT<PERSON>, HookR<PERSON>ult, HookStrategy

from ui.alert import AlertD<PERSON>og<PERSON>uilder

from ui.bulletin import BulletinHelper

from client_utils import get_last_fragment, get_messages_controller

from ui.settings import Header, Text, Divider

from android_utils import run_on_ui_thread, log

from org.telegram.tgnet import TLRPC



__id__ = "user_spy_pro"

__name__ = "User Spy"

__description__ = "Позволяет отслеживать активность пользователя: онлайн/офлайн, смена имени и фото, с сохранением истории."

__author__ = "@imlovename"

__version__ = "1.1"

__min_version__ = "11.12.0"

__icon__ = "TgAndroidIcons/99"



TRACKED_USERS_KEY = "tracked_users_list_v2"

COLLECTED_DATA_KEY = "collected_spy_data_v2"



class UserSpyPlugin(BasePlugin):

    def __init__(self):

        super().__init__()

        self.tracked_users = set()

        self.collected_data = {}  # {user_id_str: [(timestamp, event_string), ...]}

        self.menu_item = None



    def on_plugin_load(self):

        self._load_data()

        self.add_hook("TL_updateUserStatus")

        self.add_hook("TL_updateUserName")

        self.add_hook("TL_updateUserPhoto")

        

        self.menu_item = self.add_menu_item(

            MenuItemData(

                menu_type=MenuItemType.PROFILE_ACTION_MENU,

                item_id="user_spy_toggle",

                text="Слежка за пользователем",

                icon="msg_view",

                on_click=self._handle_menu_click,

                condition="user != null"

            )

        )

        log("[UserSpy] Plugin loaded and hooks registered.")



    def on_plugin_unload(self):

        self._save_data()

        self.tracked_users.clear()

        self.collected_data.clear()

        if self.menu_item:

            self.remove_menu_item(self.menu_item.item_id)

        log("[UserSpy] Plugin unloaded.")



    def create_settings(self):

        settings_items = [Header(text="История отслеживания")]

        

        if not self.tracked_users:

            settings_items.append(Text(text="Вы пока ни за кем не следите."))

        else:

            settings_items.append(Divider(text="Нажмите на пользователя, чтобы посмотреть историю"))

            sorted_users = sorted(list(self.tracked_users))

            for user_id in sorted_users:

                user = get_messages_controller().getUser(user_id)

                user_name = user.first_name if user else f"Пользователь {user_id}"

                

                # ИСПРАВЛЕНО: on_click в компоненте Text принимает view в качестве первого аргумента

                on_click_handler = lambda view, uid=user_id: self._show_history_for_user(uid)

                

                settings_items.append(

                    Text(

                        text=user_name,

                        icon="msg_info",

                        on_click=on_click_handler

                    )

                )

        return settings_items



    def _show_history_for_user(self, user_id):

        user = get_messages_controller().getUser(user_id)

        user_name = user.first_name if user else f"Пользователь {user_id}"

        report = self._format_report(str(user_id))

        run_on_ui_thread(lambda: self._show_report_dialog(user_name, report))



    def _load_data(self):

        try:

            tracked_users_json = self.get_setting(TRACKED_USERS_KEY, "[]")

            self.tracked_users = set(json.loads(tracked_users_json))

            collected_data_json = self.get_setting(COLLECTED_DATA_KEY, "{}")

            self.collected_data = json.loads(collected_data_json)

            log(f"[UserSpy] Loaded data: {len(self.tracked_users)} users are being tracked.")

        except Exception as e:

            log(f"[UserSpy] Failed to load data: {e}")

            self.tracked_users = set()

            self.collected_data = {}



    def _save_data(self):

        try:

            self.set_setting(TRACKED_USERS_KEY, json.dumps(list(self.tracked_users)))

            self.set_setting(COLLECTED_DATA_KEY, json.dumps(self.collected_data))

            log(f"[UserSpy] Data saved.")

        except Exception as e:

            log(f"[UserSpy] Failed to save data: {e}")



    def _handle_menu_click(self, context):

        user = context.get("user")

        if not user: return



        user_id = user.id

        user_id_str = str(user_id)

        user_name = user.first_name



        if user_id in self.tracked_users:

            self.tracked_users.remove(user_id)

            report = self._format_report(user_id_str)

            

            run_on_ui_thread(lambda: self._show_report_dialog(user_name, report))



            if user_id_str in self.collected_data:

                del self.collected_data[user_id_str]

                

            BulletinHelper.show_success(f"Слежка за {user_name} остановлена.")

            log(f"[UserSpy] Stopped tracking user {user_id}")

        else:

            self.tracked_users.add(user_id)

            self.collected_data[user_id_str] = []

            

            self._add_event(user_id, "Начало слежки.")

            

            BulletinHelper.show_info(f"Начата слежка за {user_name}.")

            log(f"[UserSpy] Started tracking user {user_id}")

        

        self._save_data()



    def on_update_hook(self, update_name, account, update):

        user_id = getattr(update, 'user_id', None)

        if not user_id or user_id not in self.tracked_users:

            return HookResult()



        log(f"[UserSpy] Received update '{update_name}' for tracked user {user_id}")



        try:

            event_text = None

            if update_name == "TL_updateUserStatus":

                status = getattr(update, 'status', None)

                # ИСПРАВЛЕНО: Более детальная и корректная обработка статусов, чтобы избежать дубликатов

                if isinstance(status, TLRPC.TL_userStatusOnline):

                    event_text = "Пользователь стал онлайн."

                elif isinstance(status, TLRPC.TL_userStatusOffline):

                    event_text = "Пользователь стал офлайн."

                elif isinstance(status, TLRPC.TL_userStatusRecently):

                    event_text = "Статус изменился на 'недавно'."

                elif isinstance(status, TLRPC.TL_userStatusLastWeek):

                    event_text = "Статус изменился на 'на прошлой неделе'."

                elif isinstance(status, TLRPC.TL_userStatusLastMonth):

                    event_text = "Статус изменился на 'в прошлом месяце'."

                

            elif update_name == "TL_updateUserName":

                first_name = getattr(update, 'first_name', '')

                last_name = getattr(update, 'last_name', '')

                full_name = f"{first_name} {last_name}".strip()

                event_text = f"Имя изменено на: {full_name}"

            elif update_name == "TL_updateUserPhoto":

                event_text = "Фотография профиля обновлена."



            if event_text:

                if self._add_event(user_id, event_text):

                    self._save_data()



        except Exception as e:

            log(f"[UserSpy] Error in on_update_hook: {e}\n{traceback.format_exc()}")

            

        return HookResult()



    def _add_event(self, user_id, event_text):

        user_id_str = str(user_id)

        if user_id_str not in self.collected_data:

            self.collected_data[user_id_str] = []

        

        user_events = self.collected_data[user_id_str]

        # Предотвращаем запись абсолютно одинаковых событий подряд

        if user_events and user_events[-1][1] == event_text:

            log(f"[UserSpy] Duplicate event suppressed for user {user_id}: '{event_text}'")

            return False



        self.collected_data[user_id_str].append((time.time(), event_text))

        log(f"[UserSpy] Added event for user {user_id}: '{event_text}'")

        return True



    def _format_report(self, user_id_str):

        if user_id_str not in self.collected_data or not self.collected_data[user_id_str]:

            return "Нет данных для отчета."



        report_lines = ["Отчет о слежке:"]

        for timestamp, event in self.collected_data[user_id_str]:

            formatted_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(timestamp))

            report_lines.append(f"[{formatted_time}] {event}")

            

        return "\n".join(report_lines)



    def _show_report_dialog(self, user_name, report_text):

        fragment = get_last_fragment()

        activity = fragment.getParentActivity() if fragment else None

        if not activity:

            log("[UserSpy] Cannot show report dialog, no activity context.")

            return



        builder = AlertDialogBuilder(activity)

        builder.set_title(f"Отчет по {user_name}")

        builder.set_message(report_text)

        builder.set_positive_button("Закрыть", lambda d, w: d.dismiss())

        builder.set_cancelable(True)

        builder.show()
