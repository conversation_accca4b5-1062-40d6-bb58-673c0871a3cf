// Copyright 2018 The BoringSSL Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

//go:build ignore

package main

import (
	"crypto/elliptic"
	"fmt"
	"math/big"
)

const numPoints = 64

func printPadded(key string, n, max *big.Int) {
	padded := make([]byte, len(max.Bytes()))
	b := n.Bytes()
	copy(padded[len(padded)-len(b):], b)
	fmt.Printf("%s = %x\n", key, padded)
}

func printMultiples(name string, curve elliptic.Curve) {
	n := new(big.Int)
	for i := -numPoints; i <= numPoints; i++ {
		fmt.Printf("Curve = %s\n", name)
		n.SetInt64(int64(i))
		if i < 0 {
			n = n.Add(n, curve.Params().N)
		}
		fmt.Printf("# N = %d\n", i)
		printPadded("N", n, curve.Params().N)
		x, y := curve.ScalarBaseMult(n.Bytes())
		printPadded("X", x, curve.Params().P)
		printPadded("Y", y, curve.Params().P)
		fmt.Printf("\n")
	}
}

func main() {
	fmt.Printf(`# This file contains multiples of the base point for various curves. The point
# at infinity is represented as X = 0, Y = 0.
#
# This file is generated by make_ec_scalar_base_mult_tests.go

`)
	printMultiples("P-224", elliptic.P224())
	printMultiples("P-256", elliptic.P256())
	printMultiples("P-384", elliptic.P384())
	printMultiples("P-521", elliptic.P521())
}
