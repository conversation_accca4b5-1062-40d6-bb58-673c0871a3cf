// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/NetworkChangeDetector

#ifndef org_webrtc_NetworkChangeDetector_JNI
#define org_webrtc_NetworkChangeDetector_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_NetworkChangeDetector[];
const char kClassPath_org_webrtc_NetworkChangeDetector[] = "org/webrtc/NetworkChangeDetector";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_NetworkChangeDetector_00024IPAddress[];
const char kClassPath_org_webrtc_NetworkChangeDetector_00024IPAddress[] =
    "org/webrtc/NetworkChangeDetector$IPAddress";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_NetworkChangeDetector_00024NetworkInformation[];
const char kClassPath_org_webrtc_NetworkChangeDetector_00024NetworkInformation[] =
    "org/webrtc/NetworkChangeDetector$NetworkInformation";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_NetworkChangeDetector_clazz(nullptr);
#ifndef org_webrtc_NetworkChangeDetector_clazz_defined
#define org_webrtc_NetworkChangeDetector_clazz_defined
inline jclass org_webrtc_NetworkChangeDetector_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_NetworkChangeDetector,
      &g_org_webrtc_NetworkChangeDetector_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_NetworkChangeDetector_00024IPAddress_clazz(nullptr);
#ifndef org_webrtc_NetworkChangeDetector_00024IPAddress_clazz_defined
#define org_webrtc_NetworkChangeDetector_00024IPAddress_clazz_defined
inline jclass org_webrtc_NetworkChangeDetector_00024IPAddress_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_NetworkChangeDetector_00024IPAddress,
      &g_org_webrtc_NetworkChangeDetector_00024IPAddress_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_NetworkChangeDetector_00024NetworkInformation_clazz(nullptr);
#ifndef org_webrtc_NetworkChangeDetector_00024NetworkInformation_clazz_defined
#define org_webrtc_NetworkChangeDetector_00024NetworkInformation_clazz_defined
inline jclass org_webrtc_NetworkChangeDetector_00024NetworkInformation_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env,
      kClassPath_org_webrtc_NetworkChangeDetector_00024NetworkInformation,
      &g_org_webrtc_NetworkChangeDetector_00024NetworkInformation_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID>
    g_org_webrtc_NetworkChangeDetector_00024IPAddress_getAddress0(nullptr);
static jni_zero::ScopedJavaLocalRef<jbyteArray> Java_IPAddress_getAddress(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_NetworkChangeDetector_00024IPAddress_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_NetworkChangeDetector_00024IPAddress_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getAddress",
          "()[B",
          &g_org_webrtc_NetworkChangeDetector_00024IPAddress_getAddress0);

  jbyteArray ret =
      static_cast<jbyteArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jbyteArray>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_NetworkChangeDetector_00024NetworkInformation_getConnectionType0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_NetworkInformation_getConnectionType(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_NetworkChangeDetector_00024NetworkInformation_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_NetworkChangeDetector_00024NetworkInformation_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getConnectionType",
          "()Lorg/webrtc/NetworkChangeDetector$ConnectionType;",
          &g_org_webrtc_NetworkChangeDetector_00024NetworkInformation_getConnectionType0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_NetworkChangeDetector_00024NetworkInformation_getHandle0(nullptr);
static jlong Java_NetworkInformation_getHandle(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_NetworkChangeDetector_00024NetworkInformation_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_NetworkChangeDetector_00024NetworkInformation_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHandle",
          "()J",
          &g_org_webrtc_NetworkChangeDetector_00024NetworkInformation_getHandle0);

  jlong ret =
      env->CallLongMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_NetworkChangeDetector_00024NetworkInformation_getIpAddresses0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobjectArray> Java_NetworkInformation_getIpAddresses(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_NetworkChangeDetector_00024NetworkInformation_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_NetworkChangeDetector_00024NetworkInformation_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getIpAddresses",
          "()[Lorg/webrtc/NetworkChangeDetector$IPAddress;",
          &g_org_webrtc_NetworkChangeDetector_00024NetworkInformation_getIpAddresses0);

  jobjectArray ret =
      static_cast<jobjectArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jobjectArray>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_NetworkChangeDetector_00024NetworkInformation_getName0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_NetworkInformation_getName(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_NetworkChangeDetector_00024NetworkInformation_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_NetworkChangeDetector_00024NetworkInformation_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getName",
          "()Ljava/lang/String;",
          &g_org_webrtc_NetworkChangeDetector_00024NetworkInformation_getName0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_NetworkChangeDetector_00024NetworkInformation_getUnderlyingConnectionTypeForVpn0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject>
    Java_NetworkInformation_getUnderlyingConnectionTypeForVpn(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_NetworkChangeDetector_00024NetworkInformation_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_NetworkChangeDetector_00024NetworkInformation_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getUnderlyingConnectionTypeForVpn",
          "()Lorg/webrtc/NetworkChangeDetector$ConnectionType;",
&g_org_webrtc_NetworkChangeDetector_00024NetworkInformation_getUnderlyingConnectionTypeForVpn0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_NetworkChangeDetector_JNI
