import random
import traceback
from java.util import Locale
from base_plugin import BasePlugin, HookR<PERSON>ult, HookStrategy
from ui.settings import Header, Switch, Input, Text, Divider
from android_utils import log, run_on_ui_thread
from ui.alert import AlertDialogBuilder
from client_utils import get_last_fragment

__id__ = "random_text_sender"
__name__ = "Случайный текст"
__description__ = "Отправляет случайный текст из настроенного списка по команде. Обновленная версия."
__author__ = "123"
__version__ = "123"
__min_version__ = "11.9.0"
__icon__ = "NiggerDuck/1"

class LocalizationManager:
    strings = {
        "ru": {
            "settings_header": "Настройки случайного текста",
            "blacklist_title": "Черный список использованных текстов",
            "blacklist_subtitle": "Не повторять тексты, пока не будут использованы все в наборе",
            "binds_header": "Бинды (Команды)",
            "binds_subtitle": "Настройка команд для каждого набора текстов",
            "command_n_title": "Команда {n}",
            "command_n_subtitle": "Команда для вызова набора текстов №{n}",
            "text_sets_header": "Наборы текстов",
            "text_sets_subtitle": "Каждый новый вариант текста должен быть с новой строки",
            "set_n_title": "Набор {n} (Команда: {command})",
            "set_n_subtitle": "Тексты для команды {command}",
            "emoji_header": "Настройки смайликов",
            "emojis_title": "Смайлики",
            "emojis_subtitle": "Список смайликов, разделенных пробелом",
            "emoji_chance_title": "Шанс добавления смайликов",
            "emoji_chance_subtitle": "Вероятность в % (0-100)",
            "min_emojis_title": "Минимум смайликов",
            "min_emojis_subtitle": "Минимальное количество смайликов, если шанс сработал",
            "max_emojis_title": "Максимум смайликов",
            "max_emojis_subtitle": "Максимальное количество смайликов",
            "error_log": "Ошибка в плагине 'Случайный текст' для команды '{command}': {error}",
            "empty_list_error": "Список текстов {n} пуст. Добавьте варианты в настройках плагина.",
            "blacklist_reset_log": "Черный список для набора {n} очищен - начинаем новый цикл.",
            "text_used_log": "Набор {n}: использован текст, осталось {remaining} неиспользованных.",
            "alert_close_button": "Закрыть",
        },
        "en": {
            "settings_header": "Random Text Settings",
            "blacklist_title": "Used texts blacklist",
            "blacklist_subtitle": "Do not repeat texts until all from the set have been used",
            "binds_header": "Binds (Commands)",
            "binds_subtitle": "Configure commands for each text set",
            "command_n_title": "Command {n}",
            "command_n_subtitle": "Command to trigger text set #{n}",
            "text_sets_header": "Text Sets",
            "text_sets_subtitle": "Each new text variant must be on a new line",
            "set_n_title": "Set {n} (Command: {command})",
            "set_n_subtitle": "Texts for the {command} command",
            "emoji_header": "Emoji Settings",
            "emojis_title": "Emojis",
            "emojis_subtitle": "List of emojis separated by a space",
            "emoji_chance_title": "Emoji Chance",
            "emoji_chance_subtitle": "Probability in % (0-100)",
            "min_emojis_title": "Minimum Emojis",
            "min_emojis_subtitle": "Minimum number of emojis if the chance is triggered",
            "max_emojis_title": "Maximum Emojis",
            "max_emojis_subtitle": "Maximum number of emojis",
            "error_log": "Error in 'Random Text' plugin for command '{command}': {error}",
            "empty_list_error": "Text list {n} is empty. Please add variants in the plugin settings.",
            "blacklist_reset_log": "Blacklist for set {n} has been cleared - starting a new cycle.",
            "text_used_log": "Set {n}: used a text, {remaining} unused texts remaining.",
            "alert_close_button": "Close",
        }
    }

    def __init__(self):
        lang = Locale.getDefault().getLanguage()
        self.lang = "ru" if lang == "ru" else "en"

    def get_string(self, key, **kwargs):
        return self.strings[self.lang].get(key, key).format(**kwargs)

DEFAULT_TEXTS = {str(i): [f"Пример текста {i}.1", f"Пример текста {i}.2"] for i in range(1, 21)}
DEFAULT_COMMANDS = {str(i): f".cmd{i}" for i in range(1, 21)}
DEFAULT_EMOJIS = "😊 😂 ❤️ 👍 😎 🔥"
DEFAULT_EMOJI_CHANCE = "50"
DEFAULT_MIN_EMOJIS = "1"
DEFAULT_MAX_EMOJIS = "3"

class RandomTextSenderPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.locali = LocalizationManager()
        self.used_texts = {str(i): set() for i in range(1, 21)}

    def on_plugin_load(self):
        self.add_on_send_message_hook(priority=50)
        log("RandomTextSender (Fixed) loaded")

    def handle_blacklist_toggle(self, enabled):
        if not enabled:
            self.used_texts = {str(i): set() for i in range(1, 21)}

    def handle_text_change(self, set_number_str, _):
        if set_number_str in self.used_texts:
            self.used_texts[set_number_str].clear()

    def get_random_text(self, set_number_str):
        texts_str = self.get_setting(f"texts_{set_number_str}", "\n".join(DEFAULT_TEXTS[set_number_str]))
        active_texts = [t.strip() for t in texts_str.split('\n') if t.strip()]

        if not active_texts:
            return self.locali.get_string("empty_list_error", n=set_number_str)

        chosen_text = None

        if self.get_setting("blacklist_enabled", False):
            potential_texts = [t for t in active_texts if t not in self.used_texts.get(set_number_str, set())]
            
            if not potential_texts:
                self.used_texts[set_number_str].clear()
                log(self.locali.get_string("blacklist_reset_log", n=set_number_str))
                potential_texts = list(active_texts)
            
            if potential_texts:
                chosen_text = random.choice(potential_texts)
                self.used_texts[set_number_str].add(chosen_text)
                remaining = len(active_texts) - len(self.used_texts[set_number_str])
                log(self.locali.get_string("text_used_log", n=set_number_str, remaining=remaining))
        else:
            if active_texts:
                chosen_text = random.choice(active_texts)

        if chosen_text is None:
             return self.locali.get_string("empty_list_error", n=set_number_str)

        text = chosen_text

        try:
            emoji_chance = int(self.get_setting("emoji_chance", DEFAULT_EMOJI_CHANCE))
            if random.randint(1, 100) <= emoji_chance:
                emoji_list_str = self.get_setting("emojis", DEFAULT_EMOJIS)
                emoji_list = [e.strip() for e in emoji_list_str.split() if e.strip()]
                if emoji_list:
                    min_emojis = int(self.get_setting("min_emojis", DEFAULT_MIN_EMOJIS))
                    max_emojis = int(self.get_setting("max_emojis", DEFAULT_MAX_EMOJIS))
                    if min_emojis > max_emojis: min_emojis = max_emojis
                    emoji_count = random.randint(min_emojis, max_emojis)
                    emojis_to_add = " ".join(random.choices(emoji_list, k=emoji_count))
                    text = f"{text} {emojis_to_add}"
        except (ValueError, IndexError) as e:
            log(f"Emoji processing error: {e}")

        return text

    def on_send_message_hook(self, account, params):
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()

        msg = params.message.strip().lower()
        
        try:
            for i in range(1, 21):
                set_number_str = str(i)
                command = self.get_setting(f"command_{set_number_str}", DEFAULT_COMMANDS[set_number_str]).lower()
                if msg == command:
                    random_text = self.get_random_text(set_number_str)
                    params.message = random_text
                    return HookResult(strategy=HookStrategy.MODIFY_FINAL, params=params)
            
            return HookResult()

        except Exception as e:
            log(self.locali.get_string("error_log", command=msg, error=traceback.format_exc()))
            return HookResult()

    def _show_info_alert(self, title, message):
        """Отображает всплывающее окно с информацией."""
        fragment = get_last_fragment()
        if not fragment or not fragment.getParentActivity():
            log("Could not get context to show info alert.")
            return

        def show_dialog_on_ui():
            close_button_text = self.locali.get_string("alert_close_button")
            builder = AlertDialogBuilder(fragment.getParentActivity())
            builder.set_title(title)
            builder.set_message(message)
            builder.set_positive_button(close_button_text, None)
            builder.show()

        run_on_ui_thread(show_dialog_on_ui)

    def create_settings(self):
        settings = [
            Header(text=self.locali.get_string("settings_header")),
            Switch(
                key="blacklist_enabled",
                text=self.locali.get_string("blacklist_title"),
                subtext=self.locali.get_string("blacklist_subtitle"),
                icon="msg_stories_stealth",
                default=False,
                on_change=self.handle_blacklist_toggle
            ),
            Divider()
        ]
        
        settings.append(Header(text=self.locali.get_string("binds_header")))
        settings.append(Text(
            text=self.locali.get_string("binds_subtitle"),
            icon="msg_info",
            on_click=lambda view: self._show_info_alert(
                self.locali.get_string("binds_header"),
                self.locali.get_string("binds_subtitle")
            )
        ))
        for i in range(1, 21):
            settings.append(Input(
                key=f"command_{str(i)}",
                text=self.locali.get_string("command_n_title", n=i),
                default=DEFAULT_COMMANDS[str(i)],
                icon="msg_edit",
                subtext=self.locali.get_string("command_n_subtitle", n=i)
            ))
        settings.append(Divider())
        
        settings.append(Header(text=self.locali.get_string("text_sets_header")))
        settings.append(Text(
            text=self.locali.get_string("text_sets_subtitle"),
            icon="msg_info",
            on_click=lambda view: self._show_info_alert(
                self.locali.get_string("text_sets_header"),
                self.locali.get_string("text_sets_subtitle")
            )
        ))
        
        for i in range(1, 21):
            set_number_str = str(i)
            command = self.get_setting(f"command_{set_number_str}", DEFAULT_COMMANDS[set_number_str])
            
            settings.append(Input(
                key=f"texts_{set_number_str}",
                text=self.locali.get_string("set_n_title", n=i, command=command),
                default="\n".join(DEFAULT_TEXTS[set_number_str]),
                icon="msg_saved",
                subtext=self.locali.get_string("set_n_subtitle", command=command),
                on_change=lambda _, s=set_number_str: self.handle_text_change(s, _)
            ))
            
        settings.append(Divider())

        settings.append(Header(text=self.locali.get_string("emoji_header")))
        settings.extend([
            Input(
                key="emojis",
                text=self.locali.get_string("emojis_title"),
                default=DEFAULT_EMOJIS,
                icon="msg_sticker",
                subtext=self.locali.get_string("emojis_subtitle")
            ),
            Input(
                key="emoji_chance",
                text=self.locali.get_string("emoji_chance_title"),
                default=DEFAULT_EMOJI_CHANCE,
                icon="msg_stats",
                subtext=self.locali.get_string("emoji_chance_subtitle")
            ),
            Input(
                key="min_emojis",
                text=self.locali.get_string("min_emojis_title"),
                default=DEFAULT_MIN_EMOJIS,
                icon="msg_call_minimize",
                subtext=self.locali.get_string("min_emojis_subtitle")
            ),
            Input(
                key="max_emojis",
                text=self.locali.get_string("max_emojis_title"),
                default=DEFAULT_MAX_EMOJIS,
                icon="msg_maxvideo",
                subtext=self.locali.get_string("max_emojis_subtitle")
            )
        ])
        
        return settings