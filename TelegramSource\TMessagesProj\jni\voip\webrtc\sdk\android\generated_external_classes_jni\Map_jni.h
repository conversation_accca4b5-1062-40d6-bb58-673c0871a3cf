// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     java/util/Map

#ifndef java_util_Map_JNI
#define java_util_Map_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_java_util_Map[];
const char kClassPath_java_util_Map[] = "java/util/Map";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_java_util_Map_clazz(nullptr);
#ifndef java_util_Map_clazz_defined
#define java_util_Map_clazz_defined
inline jclass java_util_Map_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_java_util_Map, &g_java_util_Map_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace JNI_Map {


static std::atomic<jmethodID> g_java_util_Map_clear0(nullptr);
[[maybe_unused]] static void Java_Map_clear(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj);
static void Java_Map_clear(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "clear",
          "()V",
          &g_java_util_Map_clear0);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID> g_java_util_Map_compute2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_compute(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_compute(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "compute",
          "(Ljava/lang/Object;Ljava/util/function/BiFunction;)Ljava/lang/Object;",
          &g_java_util_Map_compute2);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj(), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_computeIfAbsent2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_computeIfAbsent(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_computeIfAbsent(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "computeIfAbsent",
          "(Ljava/lang/Object;Ljava/util/function/Function;)Ljava/lang/Object;",
          &g_java_util_Map_computeIfAbsent2);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj(), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_computeIfPresent2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_computeIfPresent(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_computeIfPresent(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "computeIfPresent",
          "(Ljava/lang/Object;Ljava/util/function/BiFunction;)Ljava/lang/Object;",
          &g_java_util_Map_computeIfPresent2);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj(), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_containsKey1(nullptr);
[[maybe_unused]] static jboolean Java_Map_containsKey(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_Map_containsKey(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "containsKey",
          "(Ljava/lang/Object;)Z",
          &g_java_util_Map_containsKey1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_Map_containsValue1(nullptr);
[[maybe_unused]] static jboolean Java_Map_containsValue(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_Map_containsValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "containsValue",
          "(Ljava/lang/Object;)Z",
          &g_java_util_Map_containsValue1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_Map_copyOf1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_copyOf(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_copyOf(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "copyOf",
          "(Ljava/util/Map;)Ljava/util/Map;",
          &g_java_util_Map_copyOf1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_entry2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_entry(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_entry(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "entry",
          "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map$Entry;",
          &g_java_util_Map_entry2);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_entrySet0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_entrySet(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_entrySet(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "entrySet",
          "()Ljava/util/Set;",
          &g_java_util_Map_entrySet0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_equals1(nullptr);
[[maybe_unused]] static jboolean Java_Map_equals(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_Map_equals(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "equals",
          "(Ljava/lang/Object;)Z",
          &g_java_util_Map_equals1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_Map_forEach1(nullptr);
[[maybe_unused]] static void Java_Map_forEach(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0);
static void Java_Map_forEach(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "forEach",
          "(Ljava/util/function/BiConsumer;)V",
          &g_java_util_Map_forEach1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
}

static std::atomic<jmethodID> g_java_util_Map_get1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_get(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_get(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "get",
          "(Ljava/lang/Object;)Ljava/lang/Object;",
          &g_java_util_Map_get1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_getOrDefault2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_getOrDefault(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_getOrDefault(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getOrDefault",
          "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;",
          &g_java_util_Map_getOrDefault2);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj(), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_hashCode0(nullptr);
[[maybe_unused]] static jint Java_Map_hashCode(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj);
static jint Java_Map_hashCode(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "hashCode",
          "()I",
          &g_java_util_Map_hashCode0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_util_Map_isEmpty0(nullptr);
[[maybe_unused]] static jboolean Java_Map_isEmpty(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jboolean Java_Map_isEmpty(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "isEmpty",
          "()Z",
          &g_java_util_Map_isEmpty0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_util_Map_keySet0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_keySet(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_keySet(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "keySet",
          "()Ljava/util/Set;",
          &g_java_util_Map_keySet0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_merge3(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_merge(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_merge(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "merge",
          "(Ljava/lang/Object;Ljava/lang/Object;Ljava/util/function/BiFunction;)Ljava/lang/Object;",
          &g_java_util_Map_merge3);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj(), p1.obj(), p2.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_of0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "of",
          "()Ljava/util/Map;",
          &g_java_util_Map_of0);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_of2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "of",
          "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;",
          &g_java_util_Map_of2);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_of4(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "of",
"(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;",
          &g_java_util_Map_of4);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), p1.obj(), p2.obj(), p3.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_of6(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "of",
"(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;",
          &g_java_util_Map_of6);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), p1.obj(), p2.obj(), p3.obj(), p4.obj(), p5.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_of8(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "of",
"(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;",
          &g_java_util_Map_of8);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), p1.obj(), p2.obj(), p3.obj(), p4.obj(), p5.obj(),
              p6.obj(), p7.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_of10(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7,
    const jni_zero::JavaRef<jobject>& p8,
    const jni_zero::JavaRef<jobject>& p9);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7,
    const jni_zero::JavaRef<jobject>& p8,
    const jni_zero::JavaRef<jobject>& p9) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "of",
"(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;",
          &g_java_util_Map_of10);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), p1.obj(), p2.obj(), p3.obj(), p4.obj(), p5.obj(),
              p6.obj(), p7.obj(), p8.obj(), p9.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_of12(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7,
    const jni_zero::JavaRef<jobject>& p8,
    const jni_zero::JavaRef<jobject>& p9,
    const jni_zero::JavaRef<jobject>& p10,
    const jni_zero::JavaRef<jobject>& p11);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7,
    const jni_zero::JavaRef<jobject>& p8,
    const jni_zero::JavaRef<jobject>& p9,
    const jni_zero::JavaRef<jobject>& p10,
    const jni_zero::JavaRef<jobject>& p11) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "of",
"(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;",
          &g_java_util_Map_of12);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), p1.obj(), p2.obj(), p3.obj(), p4.obj(), p5.obj(),
              p6.obj(), p7.obj(), p8.obj(), p9.obj(), p10.obj(), p11.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_of14(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7,
    const jni_zero::JavaRef<jobject>& p8,
    const jni_zero::JavaRef<jobject>& p9,
    const jni_zero::JavaRef<jobject>& p10,
    const jni_zero::JavaRef<jobject>& p11,
    const jni_zero::JavaRef<jobject>& p12,
    const jni_zero::JavaRef<jobject>& p13);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7,
    const jni_zero::JavaRef<jobject>& p8,
    const jni_zero::JavaRef<jobject>& p9,
    const jni_zero::JavaRef<jobject>& p10,
    const jni_zero::JavaRef<jobject>& p11,
    const jni_zero::JavaRef<jobject>& p12,
    const jni_zero::JavaRef<jobject>& p13) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "of",
"(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;",
          &g_java_util_Map_of14);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), p1.obj(), p2.obj(), p3.obj(), p4.obj(), p5.obj(),
              p6.obj(), p7.obj(), p8.obj(), p9.obj(), p10.obj(), p11.obj(), p12.obj(), p13.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_of16(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7,
    const jni_zero::JavaRef<jobject>& p8,
    const jni_zero::JavaRef<jobject>& p9,
    const jni_zero::JavaRef<jobject>& p10,
    const jni_zero::JavaRef<jobject>& p11,
    const jni_zero::JavaRef<jobject>& p12,
    const jni_zero::JavaRef<jobject>& p13,
    const jni_zero::JavaRef<jobject>& p14,
    const jni_zero::JavaRef<jobject>& p15);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7,
    const jni_zero::JavaRef<jobject>& p8,
    const jni_zero::JavaRef<jobject>& p9,
    const jni_zero::JavaRef<jobject>& p10,
    const jni_zero::JavaRef<jobject>& p11,
    const jni_zero::JavaRef<jobject>& p12,
    const jni_zero::JavaRef<jobject>& p13,
    const jni_zero::JavaRef<jobject>& p14,
    const jni_zero::JavaRef<jobject>& p15) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "of",
"(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;",
          &g_java_util_Map_of16);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), p1.obj(), p2.obj(), p3.obj(), p4.obj(), p5.obj(),
              p6.obj(), p7.obj(), p8.obj(), p9.obj(), p10.obj(), p11.obj(), p12.obj(), p13.obj(),
              p14.obj(), p15.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_of18(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7,
    const jni_zero::JavaRef<jobject>& p8,
    const jni_zero::JavaRef<jobject>& p9,
    const jni_zero::JavaRef<jobject>& p10,
    const jni_zero::JavaRef<jobject>& p11,
    const jni_zero::JavaRef<jobject>& p12,
    const jni_zero::JavaRef<jobject>& p13,
    const jni_zero::JavaRef<jobject>& p14,
    const jni_zero::JavaRef<jobject>& p15,
    const jni_zero::JavaRef<jobject>& p16,
    const jni_zero::JavaRef<jobject>& p17);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7,
    const jni_zero::JavaRef<jobject>& p8,
    const jni_zero::JavaRef<jobject>& p9,
    const jni_zero::JavaRef<jobject>& p10,
    const jni_zero::JavaRef<jobject>& p11,
    const jni_zero::JavaRef<jobject>& p12,
    const jni_zero::JavaRef<jobject>& p13,
    const jni_zero::JavaRef<jobject>& p14,
    const jni_zero::JavaRef<jobject>& p15,
    const jni_zero::JavaRef<jobject>& p16,
    const jni_zero::JavaRef<jobject>& p17) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "of",
"(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;",
          &g_java_util_Map_of18);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), p1.obj(), p2.obj(), p3.obj(), p4.obj(), p5.obj(),
              p6.obj(), p7.obj(), p8.obj(), p9.obj(), p10.obj(), p11.obj(), p12.obj(), p13.obj(),
              p14.obj(), p15.obj(), p16.obj(), p17.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_of20(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7,
    const jni_zero::JavaRef<jobject>& p8,
    const jni_zero::JavaRef<jobject>& p9,
    const jni_zero::JavaRef<jobject>& p10,
    const jni_zero::JavaRef<jobject>& p11,
    const jni_zero::JavaRef<jobject>& p12,
    const jni_zero::JavaRef<jobject>& p13,
    const jni_zero::JavaRef<jobject>& p14,
    const jni_zero::JavaRef<jobject>& p15,
    const jni_zero::JavaRef<jobject>& p16,
    const jni_zero::JavaRef<jobject>& p17,
    const jni_zero::JavaRef<jobject>& p18,
    const jni_zero::JavaRef<jobject>& p19);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_of(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7,
    const jni_zero::JavaRef<jobject>& p8,
    const jni_zero::JavaRef<jobject>& p9,
    const jni_zero::JavaRef<jobject>& p10,
    const jni_zero::JavaRef<jobject>& p11,
    const jni_zero::JavaRef<jobject>& p12,
    const jni_zero::JavaRef<jobject>& p13,
    const jni_zero::JavaRef<jobject>& p14,
    const jni_zero::JavaRef<jobject>& p15,
    const jni_zero::JavaRef<jobject>& p16,
    const jni_zero::JavaRef<jobject>& p17,
    const jni_zero::JavaRef<jobject>& p18,
    const jni_zero::JavaRef<jobject>& p19) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "of",
"(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;",
          &g_java_util_Map_of20);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), p1.obj(), p2.obj(), p3.obj(), p4.obj(), p5.obj(),
              p6.obj(), p7.obj(), p8.obj(), p9.obj(), p10.obj(), p11.obj(), p12.obj(), p13.obj(),
              p14.obj(), p15.obj(), p16.obj(), p17.obj(), p18.obj(), p19.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_ofEntries1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_ofEntries(JNIEnv* env, const
    jni_zero::JavaRef<jobjectArray>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_ofEntries(JNIEnv* env, const
    jni_zero::JavaRef<jobjectArray>& p0) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "ofEntries",
          "([Ljava/util/Map$Entry;)Ljava/util/Map;",
          &g_java_util_Map_ofEntries1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_put2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_put(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_put(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "put",
          "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;",
          &g_java_util_Map_put2);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj(), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_putAll1(nullptr);
[[maybe_unused]] static void Java_Map_putAll(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0);
static void Java_Map_putAll(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "putAll",
          "(Ljava/util/Map;)V",
          &g_java_util_Map_putAll1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
}

static std::atomic<jmethodID> g_java_util_Map_putIfAbsent2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_putIfAbsent(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_putIfAbsent(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "putIfAbsent",
          "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;",
          &g_java_util_Map_putIfAbsent2);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj(), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_remove2(nullptr);
[[maybe_unused]] static jboolean Java_Map_remove(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1);
static jboolean Java_Map_remove(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "remove",
          "(Ljava/lang/Object;Ljava/lang/Object;)Z",
          &g_java_util_Map_remove2);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj(), p1.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_Map_remove1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_remove(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_remove(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "remove",
          "(Ljava/lang/Object;)Ljava/lang/Object;",
          &g_java_util_Map_remove1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_replace2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_replace(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_replace(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "replace",
          "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;",
          &g_java_util_Map_replace2);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj(), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_Map_replace3(nullptr);
[[maybe_unused]] static jboolean Java_Map_replace(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2);
static jboolean Java_Map_replace(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "replace",
          "(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Z",
          &g_java_util_Map_replace3);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj(), p1.obj(), p2.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_Map_replaceAll1(nullptr);
[[maybe_unused]] static void Java_Map_replaceAll(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0);
static void Java_Map_replaceAll(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "replaceAll",
          "(Ljava/util/function/BiFunction;)V",
          &g_java_util_Map_replaceAll1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
}

static std::atomic<jmethodID> g_java_util_Map_size0(nullptr);
[[maybe_unused]] static jint Java_Map_size(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj);
static jint Java_Map_size(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "size",
          "()I",
          &g_java_util_Map_size0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_util_Map_values0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_values(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Map_values(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_Map_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_Map_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "values",
          "()Ljava/util/Collection;",
          &g_java_util_Map_values0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace JNI_Map

#endif  // java_util_Map_JNI
