from base_plugin import BasePlugin, HookR<PERSON>ult, HookStrategy
from ui.settings import Header, Switch, Input, Divider, Text
from android_utils import log
from java.util import Locale

__id__ = "anonymous_final_working"
__name__ = "Анонимизация (рабочая версия)"
__description__ = "Финальная рабочая версия плагина анонимизации на основе тестирования"
__author__ = "@exteraDev"
__version__ = "1.0.0"
__icon__ = "exteraPlugins/13"
__min_version__ = "11.9.0"

class AnonymousFinalWorkingPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.found_names = set()
        self.anonymized_count = 0

    def on_plugin_load(self):
        self.log("🎯 Загрузка финальной рабочей версии анонимизации...")
        # Используем все хуки, которые показали результат в тестах
        self.add_hook("TL_messages_getHistory")
        self.add_hook("TL_messages_getDialogs") 
        self.add_hook("TL_contacts_getContacts")
        self.add_hook("TL_users_getUsers")
        self.add_hook("TL_messages_getChats")
        self.add_hook("TL_channels_getParticipants")
        self.add_hook("TL_messages_search")
        self.add_hook("TL_updateNewMessage")
        self.add_hook("TL_updateUserName")
        self.log("✅ Финальная версия загружена")

    def create_settings(self):
        lang = Locale.getDefault().getLanguage()
        if lang.startswith("ru"):
            return [
                Header(text="Анонимизация (рабочая версия)"),
                Switch(
                    key="enabled",
                    text="Включить анонимизацию",
                    default=True,
                    subtext="Заменяет все найденные имена пользователей"
                ),
                Input(
                    key="text",
                    text="Анонимный текст",
                    default="Анонимный пользователь",
                    subtext="Текст для замены имен"
                ),
                Switch(
                    key="skip_bots",
                    text="Пропускать ботов",
                    default=True,
                    subtext="Не анонимизировать имена ботов"
                ),
                Switch(
                    key="debug",
                    text="Показывать статистику",
                    default=False,
                    subtext="Логировать процесс анонимизации"
                ),
                Text(
                    text="Показать статистику",
                    icon="msg_stats",
                    on_click=self.show_stats
                ),
                Text(
                    text="Сбросить счетчики",
                    icon="msg_clear",
                    on_click=self.reset_stats
                ),
                Divider(text="Проверенная рабочая версия на основе тестирования")
            ]
        else:
            return [
                Header(text="Anonymization (Working Version)"),
                Switch(
                    key="enabled",
                    text="Enable Anonymization",
                    default=True,
                    subtext="Replace all found user names"
                ),
                Input(
                    key="text",
                    text="Anonymous Text",
                    default="Anonymous User",
                    subtext="Text to replace names with"
                ),
                Switch(
                    key="skip_bots",
                    text="Skip Bots",
                    default=True,
                    subtext="Don't anonymize bot names"
                ),
                Switch(
                    key="debug",
                    text="Show Statistics",
                    default=False,
                    subtext="Log anonymization process"
                ),
                Text(
                    text="Show Statistics",
                    icon="msg_stats",
                    on_click=self.show_stats
                ),
                Text(
                    text="Reset Counters",
                    icon="msg_clear",
                    on_click=self.reset_stats
                ),
                Divider(text="Tested working version based on research")
            ]

    def show_stats(self, view):
        stats_text = f"""📊 Статистика анонимизации:
        
Найдено уникальных имен: {len(self.found_names)}
Анонимизировано: {self.anonymized_count}

Примеры найденных имен:
{chr(10).join([f"• {name}" for name in sorted(list(self.found_names)[:10])])}
{"..." if len(self.found_names) > 10 else ""}"""
        
        self.log(stats_text)

    def reset_stats(self, view):
        self.found_names.clear()
        self.anonymized_count = 0
        self.log("🗑️ Статистика сброшена")

    def debug_log(self, message):
        if self.get_setting("debug", False):
            self.log(f"🎯 {message}")

    def safe_iterate_arraylist(self, arraylist):
        if not arraylist:
            return []
        try:
            size = arraylist.size()
            items = []
            for i in range(size):
                try:
                    item = arraylist.get(i)
                    items.append(item)
                except Exception:
                    continue
            return items
        except Exception:
            return []

    def should_anonymize_user(self, user):
        """Определяет, нужно ли анонимизировать пользователя"""
        if not user:
            return False
            
        # Пропускаем ботов, если включена настройка
        is_bot = getattr(user, 'bot', False)
        if is_bot and self.get_setting("skip_bots", True):
            return False
            
        # Пропускаем удаленных пользователей
        is_deleted = getattr(user, 'deleted', False)
        if is_deleted:
            return False
            
        return True

    def anonymize_user_safe(self, user):
        """Безопасная анонимизация пользователя"""
        if not user or not self.get_setting("enabled", True):
            return False
            
        try:
            if not self.should_anonymize_user(user):
                return False
                
            modified = False
            anonymous_text = self.get_setting("text", "Анонимный пользователь")
            
            # Анонимизируем имя
            if hasattr(user, 'first_name') and user.first_name:
                original_name = str(user.first_name)
                last_name = str(getattr(user, 'last_name', '')) or ""
                full_name = f"{original_name} {last_name}".strip()
                
                # Сохраняем в статистику
                self.found_names.add(full_name)
                
                # Заменяем имя
                user.first_name = anonymous_text
                modified = True
                self.anonymized_count += 1
                
                self.debug_log(f"✅ Анонимизировано: '{full_name}' -> '{anonymous_text}'")
            
            # Очищаем фамилию
            if hasattr(user, 'last_name') and user.last_name:
                user.last_name = ""
                modified = True
                
            return modified
            
        except Exception as e:
            self.debug_log(f"Ошибка анонимизации: {str(e)}")
            return False

    def post_request_hook(self, request_name, account, response, error):
        if not self.get_setting("enabled", True) or error:
            return HookResult()

        try:
            modified = False

            # Обрабатываем пользователей в ответе
            if hasattr(response, 'users') and response.users:
                users_list = self.safe_iterate_arraylist(response.users)
                
                for user in users_list:
                    if self.anonymize_user_safe(user):
                        modified = True

            # Обрабатываем одиночного пользователя
            if hasattr(response, 'user') and response.user:
                if self.anonymize_user_safe(response.user):
                    modified = True

            if modified:
                self.debug_log(f"✅ Модифицирован ответ на {request_name}")
                return HookResult(strategy=HookStrategy.MODIFY, response=response)

        except Exception as e:
            self.debug_log(f"Ошибка в post_request_hook: {str(e)}")

        return HookResult()

    def on_update_hook(self, update_name, account, update):
        if not self.get_setting("enabled", True):
            return HookResult()

        try:
            anonymous_text = self.get_setting("text", "Анонимный пользователь")

            if update_name == "TL_updateUserName":
                modified = False
                
                if hasattr(update, 'first_name') and update.first_name:
                    original_name = str(update.first_name)
                    last_name = str(getattr(update, 'last_name', '')) or ""
                    full_name = f"{original_name} {last_name}".strip()
                    
                    # Сохраняем в статистику
                    self.found_names.add(full_name)
                    
                    # Анонимизируем
                    update.first_name = anonymous_text
                    self.anonymized_count += 1
                    modified = True
                    
                    self.debug_log(f"✅ Обновление имени: '{full_name}' -> '{anonymous_text}'")
                
                if hasattr(update, 'last_name') and update.last_name:
                    update.last_name = ""
                    modified = True
                
                if modified:
                    return HookResult(strategy=HookStrategy.MODIFY, update=update)

        except Exception as e:
            self.debug_log(f"Ошибка в on_update_hook: {str(e)}")

        return HookResult()

    def on_updates_hook(self, container_name, account, updates):
        if not self.get_setting("enabled", True):
            return HookResult()

        try:
            modified = False

            if hasattr(updates, 'users') and updates.users:
                users_list = self.safe_iterate_arraylist(updates.users)
                
                for user in users_list:
                    if self.anonymize_user_safe(user):
                        modified = True

            if modified:
                self.debug_log(f"✅ Модифицирован контейнер {container_name}")
                return HookResult(strategy=HookStrategy.MODIFY, updates=updates)

        except Exception as e:
            self.debug_log(f"Ошибка в on_updates_hook: {str(e)}")

        return HookResult()