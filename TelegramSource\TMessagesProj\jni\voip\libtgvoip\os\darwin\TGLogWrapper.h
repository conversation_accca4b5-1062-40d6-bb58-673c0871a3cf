//
// libtgvoip is free and unencumbered public domain software.
// For more information, see http://unlicense.org or the UNLICENSE file
// you should have received with this source code distribution.
//

#ifndef TGVOIP_TGLOGWRAPPER_H
#define TGVOIP_TGLOGWRAPPER_H

#if defined __cplusplus
extern "C" {
#endif

void __tgvoip_call_tglog(const char* format, ...);
	
#if defined __cplusplus
};
#endif

#endif //TGVOIP_TGLOGWRAPPER_H
