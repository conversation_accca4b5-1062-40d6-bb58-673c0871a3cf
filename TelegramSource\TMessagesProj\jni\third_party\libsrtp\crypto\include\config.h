/*
 * Copyright (c) 2016 The Chromium Authors. All rights reserved.
 * Use of this source code is governed by a BSD-style license that can be
 * found in the LICENSE file.
 */

/*
 * This space intentionally left blank.
 *
 * All of the #defines have been moved to BUILD.gn. The libsrtp headers
 * equate "building as a library" with HAS_CONFIG_H, so we need to leave that
 * defined and this file needs to be here.
 */

#define HAVE_CONFIG_H
#define OPENSSL
#define HAVE_STDLIB_H
#define HAVE_STRING_H
#define HAVE_STDINT_H
#define HAVE_INTTYPES_H
#define HAVE_INT16_T
#define HAVE_INT32_T
#define HAVE_INT8_T
#define HAVE_UINT16_T
#define HAVE_UINT32_T
#define HAVE_UINT64_T
#define HAVE_UINT8_T
#define HAVE_ARPA_INET_H
#define HAVE_SYS_TYPES_H
#define HAVE_UNISTD_H
