#!/usr/bin/env python3
# Copyright 2023 The Chromium Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Single certificate chain for a serverAuth which does not contain the
issuer."""

import sys
sys.path += ['../..']

import gencerts

# Self-signed root certificate.
root = gencerts.create_self_signed_root_certificate('Root')

# Target certificate.
target = gencerts.create_end_entity_certificate('Target', root)
target.get_extensions().set_property('extendedKeyUsage', 'serverAuth')

chain = [target]
gencerts.write_chain(__doc__, chain, 'chain.pem')
