#
# SSLeay example configuration file.
# This is mostly being used for generation of certificate requests.
#
# create RSA certs - CA

RANDFILE              = ./.rnd

####################################################################
[ req ]
distinguished_name    = req_distinguished_name
encrypt_key           = no

[ req_distinguished_name ]
countryName                   = Country Name (2 letter code)
countryName_default           = ES
countryName_value             = ES

organizationName              = Organization Name (eg, company)
organizationName_value                = Hermanos Locos

commonName                    = Common Name (eg, YOUR name)
commonName_value              = Hermanos Locos CA

