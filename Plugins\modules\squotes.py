import io
import json
import base64
import requests
import time
from typing import List, Union, Any, Dict, Optional
from time import gmtime

from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from client_utils import run_on_queue, send_message, get_last_fragment, get_messages_controller
from android_utils import run_on_ui_thread, log
from ui.settings import Header, Input, Divider, Switch, Selector, Text
from ui.alert import AlertDialogBuilder
from ui.bulletin import BulletinHelper

from org.telegram.tgnet import TLRPC
from org.telegram.messenger import MessageObject, FileLoader

# Метаданные плагина
__id__ = "squotes_plugin"
__name__ = "SQuotes"
__description__ = "Создание цитат из сообщений с поддержкой медиа и настройками"
__author__ = "@sh1tchannel"
__version__ = "2.0.0"
__min_version__ = "11.9.1"
__icon__ = "exteraPlugins/1"

def get_message_media(message):
    """Получение медиа из сообщения"""
    if not message or not hasattr(message, 'messageOwner'):
        return None

    msg = message.messageOwner
    if not msg or not hasattr(msg, 'media'):
        return None

    media = msg.media
    if not media:
        return None

    # Проверяем различные типы медиа
    if hasattr(media, 'photo') and media.photo:
        return media.photo
    elif hasattr(media, 'document') and media.document:
        return media.document
    elif hasattr(media, 'webpage') and media.webpage:
        return media.webpage

    return media


def get_entities(entities):
    """Преобразование entities в нужный формат"""
    r = []
    if entities:
        for entity in entities:
            if hasattr(entity, '__class__'):
                entity_type = entity.__class__.__name__.replace("TL_messageEntity", "").lower()
                entity_dict = {
                    "type": entity_type,
                    "offset": getattr(entity, 'offset', 0),
                    "length": getattr(entity, 'length', 0)
                }
                # Добавляем дополнительные поля если есть
                if hasattr(entity, 'url'):
                    entity_dict["url"] = entity.url
                if hasattr(entity, 'language'):
                    entity_dict["language"] = entity.language
                r.append(entity_dict)
    return r


def get_message_text(message, reply: bool = False):
    """Получение текстового описания сообщения"""
    if not message:
        return ""

    # Для MessageObject получаем messageOwner
    if hasattr(message, 'messageOwner'):
        msg = message.messageOwner
    else:
        msg = message

    if not msg:
        return ""

    # Проверяем тип сообщения
    if hasattr(msg, 'media') and msg.media:
        media = msg.media

        # Фото
        if hasattr(media, 'photo') and media.photo and reply:
            return "� Фото"

        # Документ (стикер, видео, файл и т.д.)
        if hasattr(media, 'document') and media.document:
            doc = media.document
            if hasattr(doc, 'attributes') and doc.attributes:
                for attr in doc.attributes:
                    attr_name = attr.__class__.__name__
                    if 'DocumentAttributeSticker' in attr_name and reply:
                        return "🎭 Стикер"
                    elif 'DocumentAttributeVideo' in attr_name:
                        if hasattr(attr, 'round_message') and attr.round_message and reply:
                            return "� Видеосообщение"
                        elif reply:
                            return "� Видео"
                    elif 'DocumentAttributeAudio' in attr_name:
                        if hasattr(attr, 'voice') and attr.voice and reply:
                            duration = getattr(attr, 'duration', 0)
                            return f"🎵 Голосовое сообщение: {strftime(duration)}"
                        elif reply:
                            duration = getattr(attr, 'duration', 0)
                            performer = getattr(attr, 'performer', '')
                            title = getattr(attr, 'title', '')
                            return f"🎧 Музыка: {strftime(duration)} | {performer} - {title}"
                    elif 'DocumentAttributeAnimated' in attr_name and reply:
                        return "🖼 GIF"

            if reply:
                return f"💾 Файл: {getattr(doc, 'file_name', 'Неизвестно')}"

        # Опрос
        if hasattr(media, 'poll') and media.poll:
            return "📊 Опрос"

        # Геолокация
        if hasattr(media, 'geo') and media.geo:
            return "📍 Местоположение"

        # Контакт
        if hasattr(media, 'phone_number'):
            return "👤 Контакт"

        # Игра (дайс)
        if hasattr(media, 'value'):
            emoticon = getattr(media, 'emoticon', '🎲')
            value = getattr(media, 'value', 0)
            return f"{emoticon} Дайс: {value}"

    # Служебное сообщение
    if hasattr(msg, 'action') and msg.action:
        return f"Service message: {msg.action.__class__.__name__}"

    return ""


def strftime(time: Union[int, float]):
    t = gmtime(time)
    return (
        (
            f"{t.tm_hour:02d}:"
            if t.tm_hour > 0
            else ""
        ) + f"{t.tm_min:02d}:{t.tm_sec:02d}"
    )
        


class SQuotesPlugin(BasePlugin):
    """
    Quotes by @sh1tchannel
    """

    def __init__(self):
        super().__init__()
        self.api_endpoint = "https://quotes.fl1yd.su/generate"
        self.progress_dialog = None

    def on_plugin_load(self):
        """Инициализация плагина"""
        self.add_on_send_message_hook()
        self.log("SQuotes плагин загружен")

    def on_plugin_unload(self):
        """Очистка ресурсов при выгрузке плагина"""
        if self.progress_dialog:
            run_on_ui_thread(lambda: self.progress_dialog.dismiss())
        self.log("SQuotes плагин выгружен")


    async def qcmd(self, message: types.Message):
        """
        Сокращение команды .sq
        """

        return await self.sqcmd(message)


    async def sqcmd(self, message: Message):
        """
        Использование:

        • .sq <кол-во сообщений> + <реплай> + <!file - скидывает файлом (по желанию)> + <цвет (по желанию)>
        >>> .sq
        >>> .sq 2 #2d2d2d
        >>> .sq red
        >>> .sq !file
        """

        args: List[str] = utils.get_args(message)
        if not await message.get_reply_message():
            return await utils.answer(
                message, self.strings["no_reply"])

        m = await utils.answer(
            message, self.strings["processing"])

        isFile = "!file" in args
        [count] = [int(arg) for arg in args if arg.isdigit() and int(arg) > 0] or [1]
        [bg_color] = [arg for arg in args if arg != "!file" and not arg.isdigit()] or [self.settings["bg_color"]]

        if count > self.settings["max_messages"]:
            return await utils.answer(
                m, self.strings["too_many_messages"].format(
                    self.settings["max_messages"])
            )

        payload = {
            "messages": await self.quote_parse_messages(message, count),
            "quote_color": bg_color,
            "text_color": self.settings["text_color"]
        }

        if self.settings["debug"]:
            file = open("SQuotesDebug.json", "w")
            json.dump(
                payload, file, indent = 4,
                ensure_ascii = False,
            )
            await message.respond(
                file = file.name)

        await utils.answer(
            m, self.strings["api_processing"])

        r = await self._api_request(payload)
        if r.status_code != 200:
            return await utils.answer(
                m, self.strings["api_error"])

        quote = io.BytesIO(r.content)
        quote.name = "SQuote" + (".png" if isFile else ".webp")

        await utils.answer(m, quote, force_document = isFile)
        return await m[-1].delete()


    async def quote_parse_messages(self, message: Message, count: int):
        payloads = []
        messages = [
            msg async for msg in self.client.iter_messages(
                message.chat_id, count, reverse = True, add_offset = 1,
                offset_id = (await message.get_reply_message()).id,
            )
        ]

        for message in messages:
            avatar = rank = reply_id = reply_name = reply_text = None
            entities = get_entities(message.entities)

            if message.fwd_from:
                if message.fwd_from.from_id:
                    if type(message.fwd_from.from_id) == types.PeerChannel:
                        user_id = message.fwd_from.from_id.channel_id
                    else:
                        user_id = message.fwd_from.from_id.user_id
                    try:
                        user = await self.client.get_entity(user_id)
                    except Exception:
                        name, avatar = await self.get_profile_data(message.sender)
                        return (
                            "Вот блин, произошла ошибка. Возможно на этом канале тебя забанили, и невозможно получить информацию.",
                            None, message.sender.id, name, avatar, "ошибка :(", None, None, None, None
                        )
                    name, avatar = await self.get_profile_data(user)
                    user_id = user.id

                elif name := message.fwd_from.from_name:
                    user_id = message.chat_id
            else:
                if reply := await message.get_reply_message():
                    reply_id = reply.sender.id
                    reply_name = telethon.utils.get_display_name(reply.sender)
                    reply_text = get_message_text(reply, True) + (
                        ". " + reply.raw_text
                        if reply.raw_text and get_message_text(reply, True)
                        else reply.raw_text or ""
                    )

                user = await self.client.get_entity(message.sender)
                name, avatar = await self.get_profile_data(user)
                user_id = user.id

                if message.is_group and message.is_channel:
                    admins = await self.client.get_participants(message.chat_id, filter = types.ChannelParticipantsAdmins)
                    if user in admins:
                        admin = admins[admins.index(user)].participant
                        rank = admin.rank or ("creator" if type(admin) == types.ChannelParticipantCreator else "admin")

            media = await self.client.download_media(get_message_media(message), bytes, thumb = -1)
            media = base64.b64encode(media).decode() if media else None

            via_bot = message.via_bot.username if message.via_bot else None
            text = (
                (message.raw_text or "") + (
                    (
                        "\n\n" + get_message_text(message)
                        if message.raw_text
                        else get_message_text(message)
                    )
                    if get_message_text(message)
                    else ""
                )
            )

            payloads.append(
                {
                    "text": text,
                    "media": media,
                    "entities": entities,
                    "author": {
                        "id": user_id,
                        "name": name,
                        "avatar": avatar,
                        "rank": rank or "",
                        "via_bot": via_bot
                    },
                    "reply": {
                        "id": reply_id,
                        "name": reply_name,
                        "text": reply_text
                    }
                }
            )

        return payloads


    async def fsqcmd(self, message: Message):
        """
        Использование:

        • .fsq <@ или ID> + <текст> - квота от юзера с @ или ID + указанный текст
        >>> .fsq @onetimeusername Вам пизда

        • .fsq <реплай> + <текст> - квота от юзера с реплая + указанный текст
        >>> .fsq Я лох

        • .fsq <@ или ID> + <текст> + -r + <@ или ID> + <текст> - квота с фейковым реплаем
        >>> .fsq @Fl1yd спасибо -r @onetimeusername Ты крутой

        • .fsq <@ или ID> + <текст> + -r + <@ или ID> + <текст>; <аргументы> - квота с фейковыми мульти сообщениями
        >>> .fsq @onetimeusername Пацаны из @sh1tchannel, ждите награду за ахуенный ботнет; @elonmuskplssuckmybigdick чево; @Fl1yd НАШ БОТНЕТ ЛУЧШИЙ -r @elonmuskplssuckmybigdick чево
        """

        args: str = utils.get_args_raw(message)
        reply = await message.get_reply_message()
        if not (args or reply):
            return await utils.answer(
                message, self.strings["no_args_or_reply"])

        m = await utils.answer(
            message, self.strings["processing"])
        try:
            payload = await self.fakequote_parse_messages(args, reply)
        except (IndexError, ValueError):
            return await utils.answer(
                m, self.strings["args_error"].format(
                    message.text)
            )

        if len(payload) > self.settings["max_messages"]:
            return await utils.answer(
                m, self.strings["too_many_messages"].format(
                    self.settings["max_messages"])
            ) 

        payload = {
            "messages": payload,
            "quote_color": self.settings["bg_color"],
            "text_color": self.settings["text_color"]
        }

        if self.settings["debug"]:
            file = open("SQuotesDebug.json", "w")
            json.dump(
                payload, file, indent = 4,
                ensure_ascii = False,
            )
            await message.respond(
                file = file.name)

        await utils.answer(
            m, self.strings["api_processing"])

        r = await self._api_request(payload)
        if r.status_code != 200:
            return await utils.answer(
                m, self.strings["api_error"])

        quote = io.BytesIO(r.content)
        quote.name = "SQuote.webp"

        await utils.answer(m, quote)
        return await m[-1].delete()


    async def fakequote_parse_messages(self, args: str, reply: Message):
        async def get_user(args: str):
            args_, text = args.split(), ""
            user = await self.client.get_entity(
                int(args_[0]) if args_[0].isdigit() else args_[0])

            if len(args_) < 2:
                user = await self.client.get_entity(
                    int(args) if args.isdigit() else args)
            else:
                text = args.split(maxsplit = 1)[1]
            return user, text

        if reply or reply and args:
            user = reply.sender
            name, avatar = await self.get_profile_data(user)
            text = args or ""

        else:
            messages = []
            for part in args.split("; "):
                user, text = await get_user(part)
                name, avatar = await self.get_profile_data(user)
                reply_id = reply_name = reply_text = None

                if " -r " in part:
                    user, text = await get_user(''.join(part.split(" -r ")[0]))
                    user2, text2 = await get_user(''.join(part.split(" -r ")[1]))

                    name, avatar = await self.get_profile_data(user)
                    name2, _ = await self.get_profile_data(user2)

                    reply_id = user2.id
                    reply_name = name2
                    reply_text = text2

                messages.append(
                    {
                        "text": text,
                        "media": None,
                        "entities": None,
                        "author": {
                            "id": user.id,
                            "name": name,
                            "avatar": avatar,
                            "rank": ""
                        },
                        "reply": {
                            "id": reply_id,
                            "name": reply_name,
                            "text": reply_text
                        }
                    }
                )
            return messages

        return [
            {
                "text": text,
                "media": None,
                "entities": None,
                "author": {
                    "id": user.id,
                    "name": name,
                    "avatar": avatar,
                    "rank": ""
                },
                "reply": {
                    "id": None,
                    "name": None,
                    "text": None
                }
            }
        ]


    async def get_profile_data(self, user: types.User):
        avatar = await self.client.download_profile_photo(user.id, bytes)
        return telethon.utils.get_display_name(user), \
            base64.b64encode(avatar).decode() if avatar else None


    async def sqsetcmd(self, message: Message):
        """
        Использование:

        • .sqset <bg_color/text_color/debug> (<цвет для bg_color/text_color> <True/False для debug>)
        >>> .sqset bg_color #2d2d2d
        >>> .sqset debug true
        """

        args: List[str] = utils.get_args_raw(message).split(maxsplit = 1)
        if not args:
            return await utils.answer(
                message,
                f"<b>[SQuotes]</b> Настройки:\n\n"
                f"Максимум сообщений (<code>max_messages</code>): {self.settings['max_messages']}\n"
                f"Цвет квоты (<code>bg_color</code>): {self.settings['bg_color']}\n"
                f"Цвет текста (<code>text_color</code>): {self.settings['text_color']}\n"
                f"Дебаг (<code>debug</code>): {self.settings['debug']}\n\n"
                f"Настроить можно с помощью <code>.sqset</code> <параметр> <значение> или <code>reset</code>"
            )

        if args[0] == "reset":
            self.get_settings(True)
            return await utils.answer(
                message, "<b>[SQuotes - Settings]</b> Настойки квот были сброшены")

        if len(args) < 2:
            return await utils.answer(
                message, "<b>[SQuotes - Settings]</b> Недостаточно аргументов")

        mods = ["max_messages", "bg_color", "text_color", "debug"]
        if args[0] not in mods:
            return await utils.answer(
                message, f"<b>[SQuotes - Settings]</b> Такого парамерта нет, есть {', '.join(mods)}")

        elif args[0] == "debug":
            if args[1].lower() not in ["true", "false"]:
                return await utils.answer(
                    message, "<b>[SQuotes - Settings]</b> Такого значения параметра нет, есть true/false")
            self.settings[args[0]] = args[1].lower() == "true"

        elif args[0] == "max_messages":
            if not args[1].isdigit():
                return await utils.answer(
                    message, "<b>[SQuotes - Settings]</b> Это не число")
            self.settings[args[0]] = int(args[1])

        else:
            self.settings[args[0]] = args[1]

        self.db.set("SQuotes", "settings", self.settings)
        return await utils.answer(
            message, f"<b>[SQuotes - Settings]</b> Значение параметра {args[0]} было выставлено на {args[1]}")


    def get_settings(self, force: bool = False):
        settings: dict = self.db.get(
            "SQuotes", "settings", {}
        )
        if not settings or force:
            settings.update(
                {
                    "max_messages": 15,
                    "bg_color": "#162330",
                    "text_color": "#fff",
                    "debug": False
                }
            )
            self.db.set("SQuotes", "settings", settings)

        return settings


    async def _api_request(self, data: dict):
        return await utils.run_sync(
            requests.post, self.api_endpoint, json = data)