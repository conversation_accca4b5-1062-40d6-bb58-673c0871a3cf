// Copyright 2024 The Abseil Authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <cassert>

#include "gtest/gtest.h"
#include "absl/base/nullability.h"

ABSL_POINTERS_DEFAULT_NONNULL

namespace {

void FuncWithDefaultNonnullArg(int* /*arg*/) {}
template <typename T>
void FuncWithDeducedDefaultNonnullArg(T* /*arg*/) {}

TEST(DefaultNonnullTest, NonnullArgument) {
  int var = 0;
  FuncWithDefaultNonnullArg(&var);
  FuncWithDeducedDefaultNonnullArg<int>(&var);
}

int* FuncWithDefaultNonnullReturn() {
  static int var = 0;
  return &var;
}

TEST(DefaultNonnullTest, NonnullReturn) {
  auto var = FuncWithDefaultNonnullReturn();
  (void)var;
}

}  // namespace
