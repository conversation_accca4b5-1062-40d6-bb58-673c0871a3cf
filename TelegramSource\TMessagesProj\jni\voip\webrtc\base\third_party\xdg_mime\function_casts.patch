diff --git a/base/third_party/xdg_mime/xdgmime.c b/base/third_party/xdg_mime/xdgmime.c
index 6dc58c253fa2..f340fcefabb4 100644
--- a/base/third_party/xdg_mime/xdgmime.c
+++ b/base/third_party/xdg_mime/xdgmime.c
@@ -136,7 +136,7 @@ xdg_dir_time_list_free (XdgDirTimeList *list)
 }
 
 static int
-xdg_mime_init_from_directory (const char *directory)
+xdg_mime_init_from_directory (const char *directory, void *user_data)
 {
   char *file_name;
   struct stat st;
@@ -340,8 +340,9 @@ xdg_check_file (const char *file_path,
 
 static int
 xdg_check_dir (const char *directory,
-	       int        *invalid_dir_list)
+	       void       *user_data)
 {
+  int *invalid_dir_list = user_data;
   int invalid, exists;
   char *file_name;
 
@@ -398,8 +399,7 @@ xdg_check_dirs (void)
   for (list = dir_time_list; list; list = list->next)
     list->checked = XDG_CHECKED_UNCHECKED;
 
-  xdg_run_command_on_dirs ((XdgDirectoryFunc) xdg_check_dir,
-			   &invalid_dir_list);
+  xdg_run_command_on_dirs (xdg_check_dir, &invalid_dir_list);
 
   if (invalid_dir_list)
     return TRUE;
@@ -455,8 +455,7 @@ xdg_mime_init (void)
       icon_list = _xdg_mime_icon_list_new ();
       generic_icon_list = _xdg_mime_icon_list_new ();
 
-      xdg_run_command_on_dirs ((XdgDirectoryFunc) xdg_mime_init_from_directory,
-			       NULL);
+      xdg_run_command_on_dirs (xdg_mime_init_from_directory, NULL);
 
       need_reread = FALSE;
     }
