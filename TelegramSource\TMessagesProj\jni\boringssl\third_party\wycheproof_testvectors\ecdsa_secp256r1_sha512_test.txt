# Imported from Wych<PERSON><PERSON><PERSON>'s ecdsa_secp256r1_sha512_test.json.
# This file is generated by convert_wycheproof.go. Do not edit by hand.
#
# Algorithm: ECDSA
# Generator version: 0.8r12

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 042927b10512bae3eddcfe467828128bad2903269919f7086069c8c4df6c732838c7787964eaac00e5921fb1498a60f4606766b3d9685001558d1a974e7341513e]
[key.wx = 2927b10512bae3eddcfe467828128bad2903269919f7086069c8c4df6c732838]
[key.wy = 00c7787964eaac00e5921fb1498a60f4606766b3d9685001558d1a974e7341513e]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200042927b10512bae3eddcfe467828128bad2903269919f7086069c8c4df6c732838c7787964eaac00e5921fb1498a60f4606766b3d9685001558d1a974e7341513e]
[sha = SHA-512]

# tcId = 1
# signature malleability
msg = 313233343030
result = valid
sig = 304402202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c002205f85a63a5be977ad714cea16b10035f07cadf7513ae8cca86f35b7692aafd69f

# tcId = 2
# Legacy:ASN encoding of s misses leading 0
msg = 313233343030
result = acceptable
sig = 304402202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00220a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2
flags = MissingZero

# tcId = 3
# valid
msg = 313233343030
result = valid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 4
# long form encoding of length of sequence
msg = 313233343030
result = invalid
sig = 30814502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2
flags = BER

# tcId = 5
# length of sequence contains leading 0
msg = 313233343030
result = invalid
sig = 3082004502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2
flags = BER

# tcId = 6
# wrong length of sequence
msg = 313233343030
result = invalid
sig = 304602202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 7
# wrong length of sequence
msg = 313233343030
result = invalid
sig = 304402202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 8
# uint32 overflow in length of sequence
msg = 313233343030
result = invalid
sig = 3085010000004502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 9
# uint64 overflow in length of sequence
msg = 313233343030
result = invalid
sig = 308901000000000000004502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 10
# length of sequence = 2**31 - 1
msg = 313233343030
result = invalid
sig = 30847fffffff02202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 11
# length of sequence = 2**32 - 1
msg = 313233343030
result = invalid
sig = 3084ffffffff02202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 12
# length of sequence = 2**40 - 1
msg = 313233343030
result = invalid
sig = 3085ffffffffff02202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 13
# length of sequence = 2**64 - 1
msg = 313233343030
result = invalid
sig = 3088ffffffffffffffff02202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 14
# incorrect length of sequence
msg = 313233343030
result = invalid
sig = 30ff02202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 15
# indefinite length without termination
msg = 313233343030
result = invalid
sig = 308002202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 16
# indefinite length without termination
msg = 313233343030
result = invalid
sig = 304502802478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 17
# indefinite length without termination
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0028000a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 18
# removing sequence
msg = 313233343030
result = invalid
sig = 

# tcId = 19
# lonely sequence tag
msg = 313233343030
result = invalid
sig = 30

# tcId = 20
# appending 0's to sequence
msg = 313233343030
result = invalid
sig = 304702202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb20000

# tcId = 21
# prepending 0's to sequence
msg = 313233343030
result = invalid
sig = 3047000002202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 22
# appending unused 0's to sequence
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb20000

# tcId = 23
# appending null value to sequence
msg = 313233343030
result = invalid
sig = 304702202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb20500

# tcId = 24
# including garbage
msg = 313233343030
result = invalid
sig = 304a498177304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 25
# including garbage
msg = 313233343030
result = invalid
sig = 30492500304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 26
# including garbage
msg = 313233343030
result = invalid
sig = 3047304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb20004deadbeef

# tcId = 27
# including garbage
msg = 313233343030
result = invalid
sig = 304a222549817702202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 28
# including garbage
msg = 313233343030
result = invalid
sig = 30492224250002202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 29
# including garbage
msg = 313233343030
result = invalid
sig = 304d222202202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00004deadbeef022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 30
# including garbage
msg = 313233343030
result = invalid
sig = 304a02202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c02226498177022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 31
# including garbage
msg = 313233343030
result = invalid
sig = 304902202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c022252500022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 32
# including garbage
msg = 313233343030
result = invalid
sig = 304d02202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c02223022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb20004deadbeef

# tcId = 33
# including undefined tags
msg = 313233343030
result = invalid
sig = 304daa00bb00cd00304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 34
# including undefined tags
msg = 313233343030
result = invalid
sig = 304baa02aabb304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 35
# including undefined tags
msg = 313233343030
result = invalid
sig = 304d2228aa00bb00cd0002202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 36
# including undefined tags
msg = 313233343030
result = invalid
sig = 304b2226aa02aabb02202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 37
# including undefined tags
msg = 313233343030
result = invalid
sig = 304d02202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c02229aa00bb00cd00022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 38
# including undefined tags
msg = 313233343030
result = invalid
sig = 304b02202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c02227aa02aabb022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 39
# truncated length of sequence
msg = 313233343030
result = invalid
sig = 3081

# tcId = 40
# using composition with indefinite length
msg = 313233343030
result = invalid
sig = 3080304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb20000

# tcId = 41
# using composition with indefinite length
msg = 313233343030
result = invalid
sig = 3049228002202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00000022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 42
# using composition with indefinite length
msg = 313233343030
result = invalid
sig = 304902202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c02280022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb20000

# tcId = 43
# using composition with wrong tag
msg = 313233343030
result = invalid
sig = 3080314502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb20000

# tcId = 44
# using composition with wrong tag
msg = 313233343030
result = invalid
sig = 3049228003202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00000022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 45
# using composition with wrong tag
msg = 313233343030
result = invalid
sig = 304902202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c02280032100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb20000

# tcId = 46
# Replacing sequence with NULL
msg = 313233343030
result = invalid
sig = 0500

# tcId = 47
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = 2e4502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 48
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = 2f4502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 49
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = 314502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 50
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = 324502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 51
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = ff4502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 52
# dropping value of sequence
msg = 313233343030
result = invalid
sig = 3000

# tcId = 53
# using composition for sequence
msg = 313233343030
result = invalid
sig = 30493001023044202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 54
# truncated sequence
msg = 313233343030
result = invalid
sig = 304402202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34e

# tcId = 55
# truncated sequence
msg = 313233343030
result = invalid
sig = 3044202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 56
# indefinite length
msg = 313233343030
result = invalid
sig = 308002202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb20000
flags = BER

# tcId = 57
# indefinite length with truncated delimiter
msg = 313233343030
result = invalid
sig = 308002202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb200

# tcId = 58
# indefinite length with additional element
msg = 313233343030
result = invalid
sig = 308002202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb205000000

# tcId = 59
# indefinite length with truncated element
msg = 313233343030
result = invalid
sig = 308002202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2060811220000

# tcId = 60
# indefinite length with garbage
msg = 313233343030
result = invalid
sig = 308002202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb20000fe02beef

# tcId = 61
# indefinite length with nonempty EOC
msg = 313233343030
result = invalid
sig = 308002202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb20002beef

# tcId = 62
# prepend empty sequence
msg = 313233343030
result = invalid
sig = 3047300002202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 63
# append empty sequence
msg = 313233343030
result = invalid
sig = 304702202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb23000

# tcId = 64
# append garbage with high tag number
msg = 313233343030
result = invalid
sig = 304802202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2bf7f00

# tcId = 65
# sequence of sequence
msg = 313233343030
result = invalid
sig = 3047304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 66
# truncated sequence: removed last 1 elements
msg = 313233343030
result = invalid
sig = 302202202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0

# tcId = 67
# repeating element in sequence
msg = 313233343030
result = invalid
sig = 306802202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 68
# long form encoding of length of integer
msg = 313233343030
result = invalid
sig = 30460281202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2
flags = BER

# tcId = 69
# long form encoding of length of integer
msg = 313233343030
result = invalid
sig = 304602202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c002812100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2
flags = BER

# tcId = 70
# length of integer contains leading 0
msg = 313233343030
result = invalid
sig = 3047028200202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2
flags = BER

# tcId = 71
# length of integer contains leading 0
msg = 313233343030
result = invalid
sig = 304702202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00282002100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2
flags = BER

# tcId = 72
# wrong length of integer
msg = 313233343030
result = invalid
sig = 304502212478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 73
# wrong length of integer
msg = 313233343030
result = invalid
sig = 3045021f2478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 74
# wrong length of integer
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022200a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 75
# wrong length of integer
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022000a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 76
# uint32 overflow in length of integer
msg = 313233343030
result = invalid
sig = 304a028501000000202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 77
# uint32 overflow in length of integer
msg = 313233343030
result = invalid
sig = 304a02202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00285010000002100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 78
# uint64 overflow in length of integer
msg = 313233343030
result = invalid
sig = 304e02890100000000000000202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 79
# uint64 overflow in length of integer
msg = 313233343030
result = invalid
sig = 304e02202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0028901000000000000002100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 80
# length of integer = 2**31 - 1
msg = 313233343030
result = invalid
sig = 304902847fffffff2478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 81
# length of integer = 2**31 - 1
msg = 313233343030
result = invalid
sig = 304902202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c002847fffffff00a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 82
# length of integer = 2**32 - 1
msg = 313233343030
result = invalid
sig = 30490284ffffffff2478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 83
# length of integer = 2**32 - 1
msg = 313233343030
result = invalid
sig = 304902202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00284ffffffff00a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 84
# length of integer = 2**40 - 1
msg = 313233343030
result = invalid
sig = 304a0285ffffffffff2478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 85
# length of integer = 2**40 - 1
msg = 313233343030
result = invalid
sig = 304a02202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00285ffffffffff00a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 86
# length of integer = 2**64 - 1
msg = 313233343030
result = invalid
sig = 304d0288ffffffffffffffff2478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 87
# length of integer = 2**64 - 1
msg = 313233343030
result = invalid
sig = 304d02202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00288ffffffffffffffff00a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 88
# incorrect length of integer
msg = 313233343030
result = invalid
sig = 304502ff2478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 89
# incorrect length of integer
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c002ff00a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 90
# removing integer
msg = 313233343030
result = invalid
sig = 3023022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 91
# lonely integer tag
msg = 313233343030
result = invalid
sig = 302402022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 92
# lonely integer tag
msg = 313233343030
result = invalid
sig = 302302202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c002

# tcId = 93
# appending 0's to integer
msg = 313233343030
result = invalid
sig = 304702222478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00000022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 94
# appending 0's to integer
msg = 313233343030
result = invalid
sig = 304702202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022300a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb20000

# tcId = 95
# prepending 0's to integer
msg = 313233343030
result = invalid
sig = 3047022200002478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2
flags = BER

# tcId = 96
# prepending 0's to integer
msg = 313233343030
result = invalid
sig = 304702202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00223000000a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2
flags = BER

# tcId = 97
# appending unused 0's to integer
msg = 313233343030
result = invalid
sig = 304702202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00000022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 98
# appending null value to integer
msg = 313233343030
result = invalid
sig = 304702222478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00500022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 99
# appending null value to integer
msg = 313233343030
result = invalid
sig = 304702202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022300a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb20500

# tcId = 100
# truncated length of integer
msg = 313233343030
result = invalid
sig = 30250281022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 101
# truncated length of integer
msg = 313233343030
result = invalid
sig = 302402202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00281

# tcId = 102
# Replacing integer with NULL
msg = 313233343030
result = invalid
sig = 30250500022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 103
# Replacing integer with NULL
msg = 313233343030
result = invalid
sig = 302402202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00500

# tcId = 104
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 304500202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 105
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 304501202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 106
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 304503202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 107
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 304504202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 108
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 3045ff202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 109
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0002100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 110
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0012100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 111
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0032100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 112
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0042100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 113
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0ff2100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 114
# dropping value of integer
msg = 313233343030
result = invalid
sig = 30250200022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 115
# dropping value of integer
msg = 313233343030
result = invalid
sig = 302402202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00200

# tcId = 116
# using composition for integer
msg = 313233343030
result = invalid
sig = 30492224020124021f78f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 117
# using composition for integer
msg = 313233343030
result = invalid
sig = 304902202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c022250201000220a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 118
# modify first byte of integer
msg = 313233343030
result = invalid
sig = 304502202678f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 119
# modify first byte of integer
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022102a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 120
# modify last byte of integer
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f98140022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 121
# modify last byte of integer
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34e32

# tcId = 122
# truncated integer
msg = 313233343030
result = invalid
sig = 3044021f2478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 123
# truncated integer
msg = 313233343030
result = invalid
sig = 3044021f78f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 124
# truncated integer
msg = 313233343030
result = invalid
sig = 304402202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022000a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34e

# tcId = 125
# leading ff in integer
msg = 313233343030
result = invalid
sig = 30460221ff2478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 126
# leading ff in integer
msg = 313233343030
result = invalid
sig = 304602202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00222ff00a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 127
# replaced integer by infinity
msg = 313233343030
result = invalid
sig = 3026090180022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 128
# replaced integer by infinity
msg = 313233343030
result = invalid
sig = 302502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0090180

# tcId = 129
# replacing integer with zero
msg = 313233343030
result = invalid
sig = 3026020100022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 130
# replacing integer with zero
msg = 313233343030
result = invalid
sig = 302502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0020100

# tcId = 131
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 30460221012478f1cf49f6d858ac900a7af177222661ac95e206d32ee63020beee955ca711022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 132
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 30460221ff2478f1d149f6d856ac900a7af1772226e7dea086b8a3f1dc48ad29689c965c6f022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 133
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 30450220db870e2fb60927a8536ff5850e88ddd95b3a64cba0446f9ec3990bd467067e40022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 134
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 3046022100db870e2eb60927a9536ff5850e88ddd918215f79475c0e23b752d6976369a391022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 135
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 30460221fedb870e30b60927a7536ff5850e88ddd99e536a1df92cd119cfdf41116aa358ef022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 136
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 30460221012478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 137
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 3046022100db870e2fb60927a8536ff5850e88ddd95b3a64cba0446f9ec3990bd467067e40022100a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 138
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022101a07a59c3a41688548eb315e94effca0efd1ffe0a13467061783dde1cce167403

# tcId = 139
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 304402202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00220a07a59c5a41688528eb315e94effca0f835208aec517335790ca4896d5502961

# tcId = 140
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00221ff5f85a63b5be977ac714cea16b10035f0bfc6fca393d12e237b7beca62e4cb14e

# tcId = 141
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c00221fe5f85a63c5be977ab714cea16b10035f102e001f5ecb98f9e87c221e331e98bfd

# tcId = 142
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 304502202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c0022101a07a59c4a41688538eb315e94effca0f4039035c6c2ed1dc84841359d1b34eb2

# tcId = 143
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 304402202478f1d049f6d857ac900a7af1772226a4c59b345fbb90613c66f42b98f981c002205f85a63b5be977ac714cea16b10035f0bfc6fca393d12e237b7beca62e4cb14e

# tcId = 144
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020100020100
flags = EdgeCase

# tcId = 145
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020100020101
flags = EdgeCase

# tcId = 146
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201000201ff
flags = EdgeCase

# tcId = 147
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026020100022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551
flags = EdgeCase

# tcId = 148
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026020100022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550
flags = EdgeCase

# tcId = 149
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026020100022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552
flags = EdgeCase

# tcId = 150
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026020100022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff
flags = EdgeCase

# tcId = 151
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026020100022100ffffffff00000001000000000000000000000001000000000000000000000000
flags = EdgeCase

# tcId = 152
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3008020100090380fe01
flags = EdgeCase

# tcId = 153
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020100090142
flags = EdgeCase

# tcId = 154
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020101020100
flags = EdgeCase

# tcId = 155
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020101020101
flags = EdgeCase

# tcId = 156
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201010201ff
flags = EdgeCase

# tcId = 157
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026020101022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551
flags = EdgeCase

# tcId = 158
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026020101022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550
flags = EdgeCase

# tcId = 159
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026020101022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552
flags = EdgeCase

# tcId = 160
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026020101022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff
flags = EdgeCase

# tcId = 161
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026020101022100ffffffff00000001000000000000000000000001000000000000000000000000
flags = EdgeCase

# tcId = 162
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3008020101090380fe01
flags = EdgeCase

# tcId = 163
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020101090142
flags = EdgeCase

# tcId = 164
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201ff020100
flags = EdgeCase

# tcId = 165
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201ff020101
flags = EdgeCase

# tcId = 166
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201ff0201ff
flags = EdgeCase

# tcId = 167
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30260201ff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551
flags = EdgeCase

# tcId = 168
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30260201ff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550
flags = EdgeCase

# tcId = 169
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30260201ff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552
flags = EdgeCase

# tcId = 170
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30260201ff022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff
flags = EdgeCase

# tcId = 171
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30260201ff022100ffffffff00000001000000000000000000000001000000000000000000000000
flags = EdgeCase

# tcId = 172
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30080201ff090380fe01
flags = EdgeCase

# tcId = 173
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201ff090142
flags = EdgeCase

# tcId = 174
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551020100
flags = EdgeCase

# tcId = 175
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551020101
flags = EdgeCase

# tcId = 176
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325510201ff
flags = EdgeCase

# tcId = 177
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551
flags = EdgeCase

# tcId = 178
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550
flags = EdgeCase

# tcId = 179
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552
flags = EdgeCase

# tcId = 180
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff
flags = EdgeCase

# tcId = 181
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551022100ffffffff00000001000000000000000000000001000000000000000000000000
flags = EdgeCase

# tcId = 182
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3028022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551090380fe01
flags = EdgeCase

# tcId = 183
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551090142
flags = EdgeCase

# tcId = 184
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550020100
flags = EdgeCase

# tcId = 185
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550020101
flags = EdgeCase

# tcId = 186
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325500201ff
flags = EdgeCase

# tcId = 187
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551
flags = EdgeCase

# tcId = 188
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550
flags = EdgeCase

# tcId = 189
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552
flags = EdgeCase

# tcId = 190
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff
flags = EdgeCase

# tcId = 191
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550022100ffffffff00000001000000000000000000000001000000000000000000000000
flags = EdgeCase

# tcId = 192
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3028022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550090380fe01
flags = EdgeCase

# tcId = 193
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550090142
flags = EdgeCase

# tcId = 194
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552020100
flags = EdgeCase

# tcId = 195
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552020101
flags = EdgeCase

# tcId = 196
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6325520201ff
flags = EdgeCase

# tcId = 197
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551
flags = EdgeCase

# tcId = 198
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550
flags = EdgeCase

# tcId = 199
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552
flags = EdgeCase

# tcId = 200
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff
flags = EdgeCase

# tcId = 201
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552022100ffffffff00000001000000000000000000000001000000000000000000000000
flags = EdgeCase

# tcId = 202
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3028022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552090380fe01
flags = EdgeCase

# tcId = 203
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552090142
flags = EdgeCase

# tcId = 204
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff020100
flags = EdgeCase

# tcId = 205
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff020101
flags = EdgeCase

# tcId = 206
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff0201ff
flags = EdgeCase

# tcId = 207
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551
flags = EdgeCase

# tcId = 208
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550
flags = EdgeCase

# tcId = 209
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552
flags = EdgeCase

# tcId = 210
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff
flags = EdgeCase

# tcId = 211
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff022100ffffffff00000001000000000000000000000001000000000000000000000000
flags = EdgeCase

# tcId = 212
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3028022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff090380fe01
flags = EdgeCase

# tcId = 213
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff090142
flags = EdgeCase

# tcId = 214
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000001000000000000000000000001000000000000000000000000020100
flags = EdgeCase

# tcId = 215
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000001000000000000000000000001000000000000000000000000020101
flags = EdgeCase

# tcId = 216
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff000000010000000000000000000000010000000000000000000000000201ff
flags = EdgeCase

# tcId = 217
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000001000000000000000000000001000000000000000000000000022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632551
flags = EdgeCase

# tcId = 218
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000001000000000000000000000001000000000000000000000000022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550
flags = EdgeCase

# tcId = 219
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000001000000000000000000000001000000000000000000000000022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632552
flags = EdgeCase

# tcId = 220
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000001000000000000000000000001000000000000000000000000022100ffffffff00000001000000000000000000000000ffffffffffffffffffffffff
flags = EdgeCase

# tcId = 221
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000001000000000000000000000001000000000000000000000000022100ffffffff00000001000000000000000000000001000000000000000000000000
flags = EdgeCase

# tcId = 222
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3028022100ffffffff00000001000000000000000000000001000000000000000000000000090380fe01
flags = EdgeCase

# tcId = 223
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000001000000000000000000000001000000000000000000000000090142
flags = EdgeCase

# tcId = 224
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 30060201010c0130

# tcId = 225
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 30050201010c00

# tcId = 226
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 30090c0225730c03732573

# tcId = 227
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 30080201013003020100

# tcId = 228
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 3003020101

# tcId = 229
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 3006020101010100

# tcId = 230
# Edge case for Shamir multiplication
msg = 3932323038
result = valid
sig = 3044022064a1aab5000d0e804f3e2fc02bdee9be8ff312334e2ba16d11547c97711c898e02203c623e7f7598376825fa8bc09e727c75794cbb4ee8716ae15c31cd1cbe9ca3ee

# tcId = 231
# special case hash
msg = 33393439313934313732
result = valid
sig = 304402203a4f61f7f8c4546e3580f7848411786fee1229a07a6ecf5fb84870869188215d022018c5ce44354e2274eadb8fea319f8d6f60944532dbaae86bfd8105f253041bcb

# tcId = 232
# special case hash
msg = 35333637363431383737
result = valid
sig = 304502203fa9975fb2b08b7b6e33f3843099da3f43f1dcfe9b171a60cafd5489ca9c5328022100985a86825a0cc728f5d9dac2a513b49127a06100f0fc4b8b1f200903e0df9ed2

# tcId = 233
# special case hash
msg = 35363731343831303935
result = valid
sig = 304402204d66e7ee5edd02ab96db25954050079ef8de1d0f02f34d4d75112eaf3f73124002206292d1563140013c589be40e599862bdd6bda2103809928928a119b43851a2ce

# tcId = 234
# special case hash
msg = 3131323037313732393039
result = valid
sig = 3046022100a9228305f7b486f568eb65d44e49ba007e3f14b8f23c689c952e4ced1e6cf91e022100b73c74d28bd1268002bed784a6b06c40a90ee5938ea6d08f272d027e0f96a72c

# tcId = 235
# special case hash
msg = 3131323938303334323336
result = valid
sig = 304402203fa39842bfab6c38afa7963c60beb09484d4579fc75ef09efff44e91bc62ca8302205612add1924f0285ace5b158828e2b32ab2b6e7f10ee68dca1cc54591fee1fec

# tcId = 236
# special case hash
msg = 39383736303239363833
result = valid
sig = 3045022006c04b02edfeecd8620f035ea4f449bd924593e86e5288a6f22d1923b0e2e8a9022100f666718e6fefb515bb9339d29cc0e58cfba89d605ca0066bca87f6a3f08ebcfa

# tcId = 237
# special case hash
msg = 3230323034323936353139
result = valid
sig = 304402201ddd953c32a5f84109cd4d9ec8c364dd318376ff5d228211a367483077d638800220563dba4845de762baf04910618d587e0dd0c97dd1c9785c24ffdf2f8a660abf2

# tcId = 238
# special case hash
msg = 31343531363639313830
result = valid
sig = 30460221009fe4ec4831ef4945f100d5d35a2e6312411ca5df6c900ca60690f2985d553482022100c674ad5e1bead2f767c9248e444452a4a8530dd47246cbbc968da865bdf212b6

# tcId = 239
# special case hash
msg = 31303933363835393531
result = valid
sig = 3046022100e8703d6b16a79fc2ab3653cece29d06f65dd6f2c230cb08ee30c5517407d75db0221008cfeb87b8e95ddacd638b37d315393c5005f3ab8bba0cc1cd1a050829b775bfb

# tcId = 240
# special case hash
msg = 36323139353630323031
result = valid
sig = 3046022100def608caf1f277d71403009f209c1d7eef11aaa7920397fbf429b8146181aece022100f3b8f2aa5b3df9a8b37313ea66ad5b74673f3e8614ff471b1eb6773217511fb0

# tcId = 241
# special case hash
msg = 35363832343734333033
result = valid
sig = 304402204f5d08e8d936ce831d02d6b23fb8fce0e0750101af3ab9c3b28636b95a5e24ad02206f034480553bcecac221f8be8288163c55492e2e56a88f4d0341b61436a0a6c0

# tcId = 242
# special case hash
msg = 33373336353331373836
result = valid
sig = 3045022100bdd822bfe3733d9f4b88764fe091db2e8f8af366e4c44d876bf82e62bd48c7ee02207fbf7750c5dc849a2c55dbdd067806f869652a7b3a57baa4733781d3128f02de

# tcId = 243
# special case hash
msg = 34373935393033373932
result = valid
sig = 304402201c4fc02961b7f4245566b410bf08f447502ea4f75b15690344681efa2edf7b4b02207d63eef119dc88bc4a1b2c43ac21cd53892443661f8c3a97d558bf888c29f769

# tcId = 244
# special case hash
msg = 39333939363131303037
result = valid
sig = 304402206406f2d249ab1264e175476ca3300efd049fcad569dff40b922082b41cc7b7ce0220461872b803383f785077714a9566c4d652e87b2cad90dd4f4cc84bc55004c530

# tcId = 245
# special case hash
msg = 31303837343931313835
result = valid
sig = 30450220415c924b9ba1902b340058117d90623602d48b8280583fb231dc93823b83a153022100f18be8cdc2063a26ab030504d3397dc6e9c6b6c56f4e3a59832c0e4643c0263c

# tcId = 246
# special case hash
msg = 33323336363738353030
result = valid
sig = 3045022100d12e96c7d2f177b7cf6d8a1ede060a2b174dc993d43f5fe60f75604824b64fef02200c97d87035fcca0a5f47fe6461bb30cbaf05b37e4211ec3fcd51fc71a12239ca

# tcId = 247
# special case hash
msg = 31343438393937373033
result = valid
sig = 304502207df72a64c7e982c88f83b3a22802690098147e0e42ef4371ef069910858c0646022100adbaa7b10c6a3f995ed5f83d7bda4ba626b355f34a72bf92ff788300b70e72d0

# tcId = 248
# special case hash
msg = 35373134363332383037
result = valid
sig = 30440220047c4306f8d30e425ae70e0bee9e0b94faa4ef18a9c6d7f2c95de0fe6e2a323702207a4d0d0a596bd9ea3fe9850e9c8c77322594344623c0b46ac2a8c95948aefd98

# tcId = 249
# special case hash
msg = 323236343837343932
result = valid
sig = 3044022057d603a367e23af39c95dd418c0176da8b211d50b1be82bf5ef621a2640204f702205dc3f285ad015c4d71157bd11e5b8df6a89e4b267393b08b5ad5013bdae544b1

# tcId = 250
# special case hash
msg = 35333533343439343739
result = valid
sig = 3044022011df6741021ec8cc567584aea16817c540859c4e5011551c00b097fcfc2337e50220668551919d43206ac0571fc5ad3ac0efb489bea599e7bf99fe4c7468d6c2c5e0

# tcId = 251
# special case hash
msg = 34373837333033383830
result = valid
sig = 304402207451ffede471bd370406533436fc42a89daa0af4903d087cbc062fe7e54dbf700220590895398f22b48ce72cbf7c3d3ee1dd7fb0ee645edb0b1b1de35f370e5bf5ee

# tcId = 252
# special case hash
msg = 32323332313935383233
result = valid
sig = 3045022100fc4c4d81da6f687a6426263193c1a680b67734a1b180647b8c76407cc4f0a9c6022056f775d372c9bee685374085be676c9cf31cf1f978a5e6ccb04e4a0761159cc7

# tcId = 253
# special case hash
msg = 3130373339333931393137
result = valid
sig = 3045022100feb978ca33c46ffba47eb63bb40de7833e43d5654575b54de1fea3d1de3c8ad50220108078ba997bfa064521baf342c97b0c64bd25240c8fd0fd7533ae2d03081b70

# tcId = 254
# special case hash
msg = 31383831303237333135
result = valid
sig = 3046022100cc61729698467ba53da199ff481fe7433f194fc96367907e8dc5e1d9f42b1e2102210083dd9ef156e7c1f9c09b3bf86a4f1c88e5dd20cd74d997858e600797dbe74ad2

# tcId = 255
# special case hash
msg = 36303631363933393037
result = valid
sig = 3045022100d47f616303ff0eb813eac32e760ba30ad445e0af7dc57e70756104823f6a895f0220047f2217b399c46a426b936a124980a6011f0896f51dbe07632828a72d7173f1

# tcId = 256
# special case hash
msg = 38383935323237303934
result = valid
sig = 3046022100cff73dfa2bac67ce1340b25c885abb3e7979ef7f840f15d5f19e86640cdd40a3022100c7d1210802796c4f251049ee08a2c29f5c71064033d17010c65bf2e94499381e

# tcId = 257
# special case hash
msg = 31353830323334303934
result = valid
sig = 3044022010acaf9c485ab1220355b95be269f124e12eb252f2224b0fc50785eb2ee3df45022032443b557efc6896347fa778e1fcf33cbb769c9a7da896b20d93fea7c2791ea4

# tcId = 258
# special case hash
msg = 33393635393931353132
result = valid
sig = 3046022100f919da0651abc2bff994a879d2778fa5195d57400e003e8dd6adb3fc7a0cc4cc0221009b945d06bd119665b278a59bd24fdd2350817d0be87997bee57b70c479d64a2d

# tcId = 259
# special case hash
msg = 32323838373332313938
result = valid
sig = 3045022100cc38e7a018f6d70b2d9b49120cc9b4a169f2f72238821a86b81f553b6225d24e0220276efd8bf06ccce07c7aae35eaac3bd1c374dcf0cf0588d5e0e4171936688636

# tcId = 260
# special case hash
msg = 32323330383837333139
result = valid
sig = 3045022100ff85ad66621991c318b85cef73c576cb2a8d43c568c1aafc85b40ef2a9a6b41c0220732a79e6837ebf8434fea6e7fefa948f506ae455c1a3eb36a030185a23037d96

# tcId = 261
# special case hash
msg = 313239303536393337
result = valid
sig = 3044022033f016e51eef9b1136380cb8b84c6b38b107e24c6731bd07cb1c7f4a29f33a83022036b177bb8be94c8be67ff3a41fcc4d22b5c9eb377da713eb014ae01c64ca6dd7

# tcId = 262
# special case hash
msg = 32373438363536343338
result = valid
sig = 3045022100929413ee91f27454d74e91370a10a86fc98ac7305c8ab4ca59752bda3a7bfc370220483b47a26a0d7d2e6bd37d351d9ee37c5ec2a4686d884d78b6beb7f6b08c50f9

# tcId = 263
# special case hash
msg = 37353833353032363034
result = valid
sig = 30450220578202c7d0abac93ca43dde3cb44414e5601c1eb557604cb9adb4bde0a12633b022100fb9a7412e307aee95ef4b53540571a21559414e5306794ab5182cfb229dab3e9

# tcId = 264
# special case hash
msg = 32333237373534323739
result = valid
sig = 3045022046d45ad0bb75b8639d0e91d8450fc31887c211328a5784fc83b4cb7f5b962c1b022100d6751d13ede2079b7aa1d822bdb32d7f3cf00273a1ff03df90c0ec7c62a47568

# tcId = 265
# special case hash
msg = 373735353038353834
result = valid
sig = 3046022100abe84c941783d5ced284fea56341ecc68d6bdd3196d318fbd074641f8c885bd5022100bdea3c44d48e01aa40935c1c9723ff733199563440f26b4ecf0b444b0418d9f5

# tcId = 266
# special case hash
msg = 3137393832363438333832
result = valid
sig = 3045022005277cdbf491e336fe81be24e393a161a4fb89112c9ffed1ee6649c406713408022100ab6934332e68e108bb0484d21c457dcf381a620c3a4712fdbfeb658a3fafd60c

# tcId = 267
# special case hash
msg = 32333936373737333635
result = valid
sig = 30450220293825737c8c14430ed10dbadd7da337275f9b61d1d26377f778ffaa00c139de022100cdddec267a8678c96829bf6c1d6f38322e119937cfd2fee01e9dc9525f43ed6b

# tcId = 268
# special case hash
msg = 35393938313035383031
result = valid
sig = 304402202041fdd6111c45dfd29e750e082dcdadc9a584a8a2be46580fb0ba3b3dc658620220421824fe987e4172a0f8bbcb7bcd9e1b073b7742ed9f9df98f2a1a37cd374ce3

# tcId = 269
# special case hash
msg = 3136363737383237303537
result = valid
sig = 30450220267941db660e046ab14e795669e002b852f7788447c53ebef46a2056978b5574022100d00183bcaf75bc11e37653f952f6a6537151c3aa0a1b9e4e41b004a29185395b

# tcId = 270
# special case hash
msg = 323036323134333632
result = valid
sig = 304402205dcd7f6814739d47f80a363b9414e6cbfb5f0846223888510abd5b3903d7ae09022043418f138bb3c857c0ad750ca8389ebcf3719cb389634ac54a91de9f18fd7238

# tcId = 271
# special case hash
msg = 36383432343936303435
result = valid
sig = 304502205e0e8cc0280409a0ce252da02b2424d2de3a52b406c3778932dbc60cb86c356702210093d25e929c5b00e950d89585ec6c01b6589ae0ec0af8a79c04df9e5b27b58bc5

# tcId = 272
# special case hash
msg = 33323639383937333231
result = valid
sig = 304502204fcf9c9d9ffbf4e0b98268c087071bffe0673bb8dcb32aa667f8a639c364ea47022100820db0730bee8227fc831643fcb8e2ef9c0f7059ce42da45cf74828effa8d772

# tcId = 273
# special case hash
msg = 31333837333234363932
result = valid
sig = 3046022100c60cd2e08248d58d1639b123633643c63f89aff611f998937ccb08c9113bcdca022100ac4bb470ce0164616dada7a173364ed3f9d16fd32c686136f904c99266fda17e

# tcId = 274
# special case hash
msg = 34313138383837353336
result = valid
sig = 304502207cfdaf6f22c1c7668d7b6f56f8a7be3fdeeb17a7863539555bbfa899dd70c5f1022100cee151adc71e68483b95a7857a862ae0c5a6eee478d93d40ccc7d40a31dcbd90

# tcId = 275
# special case hash
msg = 393838363036353435
result = valid
sig = 304402202270be7ee033a706b59746eab34816be7e15c8784061d5281060707a0abe0a7d022056a163341ee95e7e3c04294a57f5f7d24bf3c3c6f13ef2f161077c47bd27665d

# tcId = 276
# special case hash
msg = 32343739313135383435
result = valid
sig = 3044022016b5d2bfcaba21167a69f7433d0c476b21ded37d84dc74ca401a3ecddb2752a8022062852cf97d89adfb0ebbe6f398ee641bfea8a2271580aac8a3d8326d8c6e0ef9

# tcId = 277
# special case hash
msg = 35303736383837333637
result = valid
sig = 3046022100d907eefa664115848b90c3d5baa0236f08eafaf81c0d52bb9d0f8acb57490847022100fd91bc45a76e31cdc58c4bfb3df27f6470d20b19f0fba6a77b6c8846650ed8a6

# tcId = 278
# special case hash
msg = 393838353036393637
result = valid
sig = 30450220048337b34f427e8774b3bf7c8ff4b1ae65d132ac8af94829bb2d32944579bb31022100bd6f8eab82213ccf80764644204bb6bf16c668729cdd31dd8596286c15686e8e

# tcId = 279
# special case hash
msg = 32373231333036313331
result = valid
sig = 3046022100b2bc46b7c44293557ab7ebeb0264924277193f87a25d94c924df1518ba7c7260022100abf1f6238ff696aaafaf4f0cbbe152c3d771c5bfc43f36d7e5f5235819d02c1a

# tcId = 280
# special case hash
msg = 33323034313031363535
result = valid
sig = 3045022040d4b38a61232e654ffd08b91e18609851f4189f7bf8a425ad59d9cbb1b54c990221009e775a7bd0d934c3ed886037f5d3b356f60eda41191690566e99677d7aaf64f3

# tcId = 281
# special case hash
msg = 33313530363830393530
result = valid
sig = 3046022100ac8f64d7df8d9fea005744e3ac4af70aa3a38e5a0f3d069d85806a4f29710339022100c014e96decfef3857cc174f2c46ad0882bef0c4c8a17ce09441961e4ae8d2df3

# tcId = 282
# special case hash
msg = 31373237343630313033
result = valid
sig = 3044022041b3766f41a673a01e2c0cab5ceedbcec8d82530a393f884d72aa4e6685dea0a0220073a55dca2da577cafb40e12dd20bf8529a13a6acdf9a1c7d4b2048d60876cb3

# tcId = 283
# special case hash
msg = 3134353731343631323235
result = valid
sig = 304502201942755aa8128382cd8e35a4350c22cc45ba5704d99e8a240970df11956ad866022100f64cf1e0816cf7ac5044f73ba938e142ef3305cb09becb80a0a5b9ad7ba3eb07

# tcId = 284
# special case hash
msg = 34313739353136303930
result = valid
sig = 3045022051aba4ff1c7ddf17e0632ab71684d8de6dc700219ef346cb28ce9dafc3565b3b022100b6aaebe1af0ad01f07a68bf1cf57f9d6040b43c14b7eb8238542760e32ce3b0c

# tcId = 285
# special case hash
msg = 35383932373133303534
result = valid
sig = 304502210091efbfcc731650e9f004c38b71db146c17bf871c82c4e87716f7ff2f7f9e51d00220089ea631a7c5f05311c521d21ba798b5174881f0fd8095fb3a77515913efb6e0

# tcId = 286
# special case hash
msg = 33383936313832323937
result = valid
sig = 304502204a7e47bd281ea09b9e3a32934c7a969e1f788f978b41585989f4689e804663fb022100e65f6bd702403cbbed7f8ad0045f331d4a96fbf8c43f71f11615b7d1b9153b7f

# tcId = 287
# special case hash
msg = 38323833333436373332
result = valid
sig = 3046022100c795f5da86e10a604d4f94bf7cac381c73edad1461d66929e53aa57ca294e89f022100bae784ab6c7b58332ee05e7d54169edf55ce45f030e71ae8df63969fb327a10c

# tcId = 288
# special case hash
msg = 33333636393734383931
result = valid
sig = 3046022100ea68b24843b225f505e01c0e608b20b4d93e8faf6b9cf70cf8f9134a80e7b668022100a3abc044b4728f80fe414bdc66f032b262356720547bec7729fad94151c6adc7

# tcId = 289
# special case hash
msg = 32313939313533323239
result = valid
sig = 3046022100bfe7502140c57a24a77edc3d9b3c4bc11d21bdb0b196977b7f2b13ac973ad697022100947a01da9731849d72b67ef7bc40b012480fd389895aad1f6b1cdbeab3b93b8d

# tcId = 290
# special case hash
msg = 35363030333136383232
result = valid
sig = 304402203434ee1142740a0ab8623b97fc8dc2567eda45dadf6039b45c448819e840cf3002203c0fac0487841997202c29f3bf2df540b115b29dc619160d52203d4a1fd4b9f7

# tcId = 291
# special case hash
msg = 383639363531363935
result = valid
sig = 304502205338500e23ba96a0adc6ef84932e25fbad7435d9f70eb7f476c6912de12e33c8022100a002f5583ea8c0d7fb17136d0ee0415acf629879ce6b01ac52e3ecd7772a3704

# tcId = 292
# special case hash
msg = 36353833393236333732
result = valid
sig = 304402204ff2d4e31f4180de6901d2d20341d12387c9c55f4cf003a742f049b84af6fe0502200312f38771414555fa5ed2817dcc629a8c7cf69d306300e87bc167278ec3ef37

# tcId = 293
# special case hash
msg = 3133323035303135373235
result = valid
sig = 3044022051d665bad5f2d6306c6bbfe1f27555887670061d4df36ec9f4ce6cdfaf9ea7ac02202905e43f6207ee93df35a2e9fb9bc8098c448ae98a14e4ad1ebaea5d56b6e493

# tcId = 294
# special case hash
msg = 35303835333330373931
result = valid
sig = 3046022100b804e0235f135aba7b7531b6831f26cc9fb77d3f83854957431be20706b813690221009d317fd08e4e0467617db819cde1d7d4d74da489b2bce4db055ea01eccfafcf2

# tcId = 295
# special case hash
msg = 37383636383133313139
result = valid
sig = 30450221008ab50ef3660ccb6af34c78e795ded6b256ffca5c94f249f3d907fb65235ef680022049d5aaeae5a6d0c15b286e428b5e720cf37a822ede445baa143ffae69aba91b8

# tcId = 296
# special case hash
msg = 32303832353339343239
result = valid
sig = 30440220571b9c46a47c5cc53a574c196c3fb07f3510c0f4443b9f2fe781252c24d343de022068a9aebd50ff165c89b5b9cb6c1754191958f360b4d2851a481a3e1106ee7809

# tcId = 297
# special case hash
msg = 3130303635393536363937
result = valid
sig = 304502204cb7817b04dc73be60d3711803bc10687a6e3f4ab79c4c1a4e9d63a73174d4eb022100ce398d2d6602d2af58a64042f830bf774aee18209d6fb5c743b6a6e437826b98

# tcId = 298
# special case hash
msg = 33303234313831363034
result = valid
sig = 30450220684399c6cd6ebb1c5d5efb0d78dce40ebd48d9d944eb6548c9ce68d7fdc82229022100cf25c8e427fae359bfe60fa02964f4c9b8d6db54612e05c78c341f0a8c52d0b5

# tcId = 299
# special case hash
msg = 37373637383532383734
result = valid
sig = 3045022020b7b36d5bc76fa182ca27152a99a956e6a0880000694296e31af98a7312d04b022100eeeabc5521f9856e920eb7d29ed7e4042f178ff706dff8eeb24b429e3b63402a

# tcId = 300
# special case hash
msg = 353434313939393734
result = valid
sig = 304402206b65c95e8e121d2e6ee506cfd62cb88e0bfb3589da40876898ef66c43982aca9022009642c05ad619b4402fd297eb57e29cca5c2eb6823931ba82de32d7c652ba73e

# tcId = 301
# special case hash
msg = 35383433343830333931
result = valid
sig = 3044022067c74cbf5ea4b777bf521ace099f4f094d8f58900e15e67e1b4bd399056629ed02203d2884655c49b8b5f64e802a054e7bf09b0fc80ca18ebf927b82e58bb4a00400

# tcId = 302
# special case hash
msg = 373138383932363239
result = valid
sig = 3045022079a5e40da5cf34c4c39adf7dfc5d454995a250314ebd212b5c8e3f4e6f875feb022100b268920e403ba17828ff271938a6558a5b2dd000229f8edb4a9d9f9b6ac1b472

# tcId = 303
# special case hash
msg = 31373433323233343433
result = valid
sig = 3045022100c8b13006c3a51a322fff9321761b01de134f526be582b22e19693c443fc9fe46022034e7f60179c6162ab980fcd58f173b0e6c30b524d35c67921677522dcef843a1

# tcId = 304
# special case hash
msg = 32343036303035393336
result = valid
sig = 304502203513db745489a487c88a6cedf8795b640f8f71578397bdabd6cc586c25bd66ad02210099a72cd3f0ca6c799149283ca0af37f86b88200d0c905bd3c9f1b859e55b1659

# tcId = 305
# special case hash
msg = 31363134303336393838
result = valid
sig = 304402203a6386afb08f7ff8140b5a270f764e8706ef2830fb177446f7b4eeb8a25aac6402204b70854b38c29245b2b980eba10ea936c68a38c1da5255ce2386db23afc7c06a

# tcId = 306
# special case hash
msg = 32303935343235363835
result = valid
sig = 3046022100b8fc54a8a6be3c55e99c06f99ccdcce7af5c18a3c5829726a870cc1068458f64022100cc7237c39c8e6a4a1c8c62f5f88636549c7410798b89684c502c3adfe5fb7ad2

# tcId = 307
# special case hash
msg = 31303038303938393833
result = valid
sig = 3045022047b460851e5607f2021626635c565a63f78f558795e1b330d09115970dbbb8ab022100a6a9f4f213e08d3c736d3e1c44a35140cb107619f265a5b13608ed729fd6d894

# tcId = 308
# special case hash
msg = 31353734313437393237
result = valid
sig = 30450221008cfda4f7a65864ebbea3144863da9b075c07b5b42cb4569643ddfd70dd753b190220595784b1ab217874b82b9585521f8090b9f6322884ab7a620464f51cf846c5b7

# tcId = 309
# special case hash
msg = 32383636373731353232
result = valid
sig = 304402204cd6a45bd7c8bf0edbdf073dbf1f746234cbbca31ec20b526b077c9f480096e702207cf97ae0d33f50b73a5d7adf8aa4eeeb6ff10f89a8794efe1d874e23299c1b3d

# tcId = 310
# special case hash
msg = 31363934323830373837
result = valid
sig = 304402202e233f4df8ffebeaec64842b23cce161c80d303b016eca562429b227ae2b58ec022046b6b56adec82f82b54daa6a5fca286740a1704828052072a5f0bc8c7b884242

# tcId = 311
# special case hash
msg = 39393231363932353638
result = valid
sig = 30440220549f658d4a3f98233a2c93bd5b1a52d64af10815ae60becb4139cac822b579c3022027bdddf0dbcf374a2aec8accc47a8ac897f8d1823dda8eb2052590970b39ce2a

# tcId = 312
# special case hash
msg = 3131363039343339373938
result = valid
sig = 30450221009fabcc1e5fd965226902f594559e231369e584453974e74f49d7d762e134fb9d0220293cccc510793bac45ce5da2bb6c9e906437f59435ca206655f74b625df07c7c

# tcId = 313
# special case hash
msg = 37313836313632313030
result = valid
sig = 304502202e5c140fd6f5f823addc8088ffaae967e7f4897274316769561dfb31435825d9022100eda47327d7cfae1daa344ff5582a467bd18eb9f01caeab9c6da3c0cc89df6713

# tcId = 314
# special case hash
msg = 33323934333437313737
result = valid
sig = 304402204c11e3b7efbe3908ad2118e54d7d34d6c6eb4570bf7fdb11a7679fe93afa254c0220712e90f421836e542dac49d10bb39db4a98b2735b6336d8a3c392f3b90e60bbe

# tcId = 315
# special case hash
msg = 3138353134343535313230
result = valid
sig = 3045022100dfb4619303f4ff689563d2275069fac44d63ea3c3b18f4fb1ac805d7df3d12ec022068e37b846583901db256329f9cf64f40c416fba50dcb9be333a3e29c76ae32db

# tcId = 316
# special case hash
msg = 343736303433393330
result = valid
sig = 3045022100e70e8e17bd758ff0c48f91cb2c53d293f0f5ae82eb9dfe76ab98f9b064278635022021dde32cb0389cad7bdf676d9b9b7d25bb034ad25a55ea71ee7ee26a18359dd2

# tcId = 317
# special case hash
msg = 32353637333738373431
result = valid
sig = 30440220421397ecae30617a5a6081ad1badf6ce9d9d4cb2afdabf1f900e7fdb7fb0af5a022057ca89dc22801c75fdbefdaeca65c675625f94de7d635062b08ed308df5762cc

# tcId = 318
# special case hash
msg = 35373339393334393935
result = valid
sig = 304502200610c08076909bb722fba105c23eac8f66b4db1d58f66a882fc90d59acdec8e0022100af59e8d570761cac589d49f11c884007f7ac1eea1a44c6f3fdad1d542187d25e

# tcId = 319
# special case hash
msg = 33343738333636313339
result = valid
sig = 3045022059a1181cab0ee8ce94ab2b5ab4f4b13a422e38efe69f634bf947485a5b9ea49c0221009b3c913d98a4ab15f6a39f1802b8f2d28559aa1f8d03a3a88df00c89dc293a97

# tcId = 320
# special case hash
msg = 363439303532363032
result = valid
sig = 30460221008cae6c4dfbf901bd66ab82541011fa15c8e90e2c18c01bd881acaa2b63cb587b022100a86acf943f29cef91d1b66a7de5547df6cdfc45dd7bef816dcb8de9f5a425d2d

# tcId = 321
# special case hash
msg = 34373633383837343936
result = valid
sig = 30450221008b00c74b86474d782eac9974aea606d8f7ee78c79597e15687021f5991e86acd0220309dfe3686648eae104e87b3e9b5616a3ad479ca4f0b558ae4f1e5ab3115346a

# tcId = 322
# special case hash
msg = 353739303230303830
result = valid
sig = 30450220433a915504c977809634a36fcf4480e4c8069fc127d201d30dfdb1f423c95fd4022100bcb1b89aafd50a1766b09741fc6a9a96e744ae9826d839bf85ffb50a91981773

# tcId = 323
# special case hash
msg = 35333434373837383438
result = valid
sig = 304502204b69abd2b39840a545cdd4a72d384234580e2fd938b7091d0ecdb562780857db022100fdab9957119e0a4092af82f6cc29f3c8a692671ec86efb0a03c1112a0a1e0467

# tcId = 324
# special case hash
msg = 3139323636343130393230
result = valid
sig = 3045022100dab9d3686c28363ad017b4a2b36d35bf2eb80633613d44deb9501d42a3efbd3802201392a562d79f9ab19014e4f7e2f2668259f3720a76c120d4a3c3964e880f7679

# tcId = 325
# special case hash
msg = 33373033393135373035
result = valid
sig = 3045022023f94e47b440ce379b74c9311232b19a64e3e7c9b90da34b0c1c3f3d7af28105022100e1425903b1479c2ce18b108a6d1ec8b7a4f0f657dedb00de3a3ceea7fdeee9be

# tcId = 326
# special case hash
msg = 3831353435373730
result = valid
sig = 30450221009d706a8fa85d15bd0c3492c6672dfe529f4073b217b3947b5b2cfd61f87ccb7102206aaaaf369f82a0e542f72ded7d7eb90c8314ffa613a0ea81da1c8393dbae2bac

# tcId = 327
# special case hash
msg = 313935353330333737
result = valid
sig = 3046022100ac77918c4085c8a7ce5020b00c315629aee053a445cb4661eb50f6b62a47da29022100df2aea2b9c11a6ce39d3cd9e1faf4a53057e0b1b2e48a324be9e773203fe9fbb

# tcId = 328
# special case hash
msg = 31323637383130393033
result = valid
sig = 30460221009db2dbd2935f147fae7f6a95c8e2307bd8537c3d96eb732ad6d5ebdd89bc754e02210093a9ab99d2de9d08fe0a61e26c8fe1ebbf88726e4b69d551b57d15f0ae16df5a

# tcId = 329
# special case hash
msg = 3131313830373230383135
result = valid
sig = 30440220769f70093939afbd1fa15873decfa803ca523ace8040280ba78cf833497722bc0220369875aba5e1ced5a4ca8444ec9399a38038b00e153a0ae34d9b3c9781447eea

# tcId = 330
# special case hash
msg = 38333831383639323930
result = valid
sig = 3045022026e5182b9822550ad52f46ad80781d6bef3d110a204db5e58a0746f796982200022100a9418e76029ced0cf78a571a9e59ad04086e91f70e6813981bb33c1dee891165

# tcId = 331
# special case hash
msg = 33313331323837323737
result = valid
sig = 3046022100e7bd6aefcf7b27e1f3fadbe713f9adb3d23398e88200cd2e94989c9d12e921770221009583e0de3b76f8d4b1e634a81cbc34af54e2f8599f3684ce48d372760c8204c4

# tcId = 332
# special case hash
msg = 3134333331393236353338
result = valid
sig = 30450221008638ed7eaa83609a01a6af9c52ec9bfddda90442b1e6031d61cfa22e48b2e1e2022020c284d596f71c6c8df732f5a5a2006302301e1a792e2b39663d93a9760762d2

# tcId = 333
# special case hash
msg = 333434393038323336
result = valid
sig = 3044022061d924307a96180b06383608ba91674e15c3ea06ff2534412b93a587dde649c1022059b84aa2115b2547edac88088ca6313e9fbe1ca6a361c7e57938f9dde3f4349c

# tcId = 334
# special case hash
msg = 36383239383335393239
result = valid
sig = 30450220424fcfc3fd63d128c2eb125e88c7fe5d283b63470a786b82783edbb8a0b7a6d7022100b11548c2cd7fce9d44e795ca51af0b2f6a5180e9c9be0314007ed9e7f4bbe5e9

# tcId = 335
# special case hash
msg = 33343435313538303233
result = valid
sig = 3045022100a5f747ae6290fa9582c6ce8d5608621d495f061551bc4531bacba586a563b184022062faf8f92291e12812835b3f1d43c967bceb885b110bd06e5a68e2d74781ae2b

# tcId = 336
# special case hash
msg = 3132363937393837363434
result = valid
sig = 3045022100b731dc0d92c2cc7a605d78233f7814699bdf1cab2df297b6844eec4015af8ea0022039b1a0cc88eb85bcdc356b3620c51f1298c60aec5306b107e900ffdba049dd6f

# tcId = 337
# special case hash
msg = 333939323432353533
result = valid
sig = 3046022100ef73c4fa322da39fb6503bab6b66b64d241056afbcd6908f84b61ccbbe890433022100f1ef85413e5764aa58a3128ccfcf388324fe5340e5edf8d0135ae76786ce415b

# tcId = 338
# special case hash
msg = 31363031393737393737
result = valid
sig = 30450220694cd30e2ad0182579331474b271ee2d48723bc8415dc6513873586ce705b76b022100c5ac0c0ed5a4017d110cb45d63aa955dc7dc5ce23e7965c5397c3ff46a884636

# tcId = 339
# special case hash
msg = 3130383738373535313435
result = valid
sig = 3046022100f38b2236be3024e10b894ffb1cc68d0bb8d4cf0fcd2cfc1779f8883765d3cd96022100da69cd0b74c25566d60a486edd559fc39d569fb2751445a4798df8a36891802c

# tcId = 340
# special case hash
msg = 37303034323532393939
result = valid
sig = 3046022100a881732c205a0b4b95669c00756fd91973450109a46f17d5a9d971b5e92b9aa40221008acefdca4e06c16b47ccad1c57c05912637e107096ba230c92b97187db79e19e

# tcId = 341
# special case hash
msg = 31353635333235323833
result = valid
sig = 3044022004452f554bae819b42effb84ef44a9f1cb7e2d75b4ba9ff9b9cfffaddde3fd1b022061a3fbc5e73c350f2e3d85a7452cd231a3f3375fc11f5fe153b185f53b09c1d0

# tcId = 342
# special case hash
msg = 3233383236333432333530
result = valid
sig = 3045022005814f57f58efc7cb490119e584e635e6f0ad1c19fb5dc2edafda075bb55f98e0221009dd5c6e39009d67d965903ecffe08a851775cc1248cc19c0b77798282131b8f6

# tcId = 343
# special case hash
msg = 31343437383437303635
result = valid
sig = 3045022100dc1c4a46085e198843b1f01980cd5e4a1ff6f8e8ff7014397f0afd5b247fb0a0022038a13dc723ed90b30251d742b14733a03292ff26530a1ebcaf3d10862a6eff82

# tcId = 344
# special case hash
msg = 3134323630323035353434
result = valid
sig = 304502201067667bf525734ca7f2510e36348fd9c2c9bccf032dfd571de6d45abd49361a022100fa762568d3a19e5a1d8ea65e00202a5b16f9afae56733a01f86e35378c558da4

# tcId = 345
# special case hash
msg = 31393933383335323835
result = valid
sig = 3046022100e58d69dc56bc1031644847e3e046e2ea845a515d969d07ea1aa53aea5bd92fa1022100bfe50b80f7c512f5ab521fe7e1a131045fde78d4de826c91573baaba1e35ca97

# tcId = 346
# special case hash
msg = 34323932313533353233
result = valid
sig = 3046022100fe79c6b8c14d0f23d426e3d157f1b541f6bb91bf29957ef97c55949c9ba48a350221009da112c4a4cf4b1ff490c426f6c8ff122183964a0de56f7336ab382dc9d10285

# tcId = 347
# special case hash
msg = 34343539393031343936
result = valid
sig = 3045022045d4ed7e9edacb5a730944ab0037fba0a136ed9d0d26b2f4d4058554f148fa6f022100f136f15fd30cfe5e5548b3f4965c16a66a7c12904686abe12da777619212ae8c

# tcId = 348
# special case hash
msg = 31333933393731313731
result = valid
sig = 304402204fb7c1727e40bae272f6143a50001b54b536f90233157896dbf845e263f2486302206fea5c924dca17519f6e502ef67efa08d39eb5cc3381266f0216864d2bd00a62

# tcId = 349
# special case hash
msg = 32333930363936343935
result = valid
sig = 30450220779aac665dd988054b04f2e9d483ca79179b3372b58ca00fe43520f44fcb4c32022100b4eca1182cd51f0abd3ea2268dcda49a807ad4116a583102047498aa863653f5

# tcId = 350
# special case hash
msg = 3131343436303536323634
result = valid
sig = 3046022100db7ac6f65fb1c38d80064fd11861631237a09924b4eeca4e1569fa4b7d80ad24022100a38d178d37e13e1afa07a9d03da025d594461938a62a6c6744f5c8f7d7b7bb81

# tcId = 351
# special case hash
msg = 363835303034373530
result = valid
sig = 3046022100c90043b4aadf795d870ac223f33acdbd1948c31afff059054dc99528c6503fa6022100829f67b312bb134f6954a23c611a7f7b5b2a69efced9c48db589ac0b4d3da827

# tcId = 352
# special case hash
msg = 3232323035333630363139
result = valid
sig = 3045022100fa16c0125b6615b90e81f7499804308a90179bf3fcff6a4b2695271c68b23ded02200d6cda5ce041dc5a5f319ad9c0de4927d0cf5e89e37b79216194413d42976d54

# tcId = 353
# special case hash
msg = 36323135363635313234
result = valid
sig = 304502201a4b5bd0f806549f46a3e71bfe412d6d89206017640ded66f3d0b2d9b26bec45022100aac5f74e3130264e01428570ee82ee47e245d160ed812ae252dedffd82e1ec2c

# tcId = 354
# Signature generated without truncating the hash
msg = 313233343030
result = invalid
sig = 3045022100f8e272234b51475ec4c6f327562a6e5c9080a96225e88b2e5f72a8eecbd41ab40220516b91617fc39e3141b3bc769f6a3b2e468e687f50bdc29e19088af62d203f4b

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04b6e08b1bcc89e7fb0b84d7497e310553495be4877eccc4b3d6d79f7c68a0573431760fa1bcea4972759174ac1103bc6011985ccee251918d0573fbcb78969116]
[key.wx = 00b6e08b1bcc89e7fb0b84d7497e310553495be4877eccc4b3d6d79f7c68a05734]
[key.wy = 31760fa1bcea4972759174ac1103bc6011985ccee251918d0573fbcb78969116]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004b6e08b1bcc89e7fb0b84d7497e310553495be4877eccc4b3d6d79f7c68a0573431760fa1bcea4972759174ac1103bc6011985ccee251918d0573fbcb78969116]
[sha = SHA-512]

# tcId = 355
# k*G has a large x-coordinate
msg = 313233343030
result = valid
sig = 303502104319055358e8617b0c46353d039cdaab022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254e

# tcId = 356
# r too large
msg = 313233343030
result = invalid
sig = 3046022100ffffffff00000001000000000000000000000000fffffffffffffffffffffffc022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254e

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 043590c6a10353d669bc94d8e2ff9e14bbeed4a7f45b887255ab7e37b676387bb615fc6f97ce39a3874c2b34cc571889abfa0a706c2cfb0e5a4750cc25690696f8]
[key.wx = 3590c6a10353d669bc94d8e2ff9e14bbeed4a7f45b887255ab7e37b676387bb6]
[key.wy = 15fc6f97ce39a3874c2b34cc571889abfa0a706c2cfb0e5a4750cc25690696f8]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200043590c6a10353d669bc94d8e2ff9e14bbeed4a7f45b887255ab7e37b676387bb615fc6f97ce39a3874c2b34cc571889abfa0a706c2cfb0e5a4750cc25690696f8]
[sha = SHA-512]

# tcId = 357
# r,s are large
msg = 313233343030
result = valid
sig = 3046022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254f022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254e

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04369e96402f2cfd1a37b3acbdecfc562862dbca944a0f12d7aaacb8d325d7650aa723621922be2bdac9186290fdcdda028d94437966507d93f2fc1f5c887fdedb]
[key.wx = 369e96402f2cfd1a37b3acbdecfc562862dbca944a0f12d7aaacb8d325d7650a]
[key.wy = 00a723621922be2bdac9186290fdcdda028d94437966507d93f2fc1f5c887fdedb]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004369e96402f2cfd1a37b3acbdecfc562862dbca944a0f12d7aaacb8d325d7650aa723621922be2bdac9186290fdcdda028d94437966507d93f2fc1f5c887fdedb]
[sha = SHA-512]

# tcId = 358
# r and s^-1 have a large Hamming weight
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100909135bdb6799286170f5ead2de4f6511453fe50914f3df2de54a36383df8dd4

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 0427a0a80ea2e1aa798ea9bcc3aedbf01ab78e49c9ec2ad0e08a0429a0e1db4d0d32a8ee7bee9d0a40014e484f34a92bd6f33fe63624ea9579657441ac79666e7f]
[key.wx = 27a0a80ea2e1aa798ea9bcc3aedbf01ab78e49c9ec2ad0e08a0429a0e1db4d0d]
[key.wy = 32a8ee7bee9d0a40014e484f34a92bd6f33fe63624ea9579657441ac79666e7f]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d0301070342000427a0a80ea2e1aa798ea9bcc3aedbf01ab78e49c9ec2ad0e08a0429a0e1db4d0d32a8ee7bee9d0a40014e484f34a92bd6f33fe63624ea9579657441ac79666e7f]
[sha = SHA-512]

# tcId = 359
# r and s^-1 have a large Hamming weight
msg = 313233343030
result = valid
sig = 304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022027b4577ca009376f71303fd5dd227dcef5deb773ad5f5a84360644669ca249a5

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 049cff61712d4bc5b3638341e6e0a576a8098c9c6d3f198d389c4669f398dc0867f3b9e09f567f3dfd9c4d2c1163e82beadf16c76e8f9d7a64673800ea76fa1e59]
[key.wx = 009cff61712d4bc5b3638341e6e0a576a8098c9c6d3f198d389c4669f398dc0867]
[key.wy = 00f3b9e09f567f3dfd9c4d2c1163e82beadf16c76e8f9d7a64673800ea76fa1e59]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200049cff61712d4bc5b3638341e6e0a576a8098c9c6d3f198d389c4669f398dc0867f3b9e09f567f3dfd9c4d2c1163e82beadf16c76e8f9d7a64673800ea76fa1e59]
[sha = SHA-512]

# tcId = 360
# small r and s
msg = 313233343030
result = valid
sig = 3006020105020101

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04d9117cae81295e82682fa387991e668e1570e0e90100bf4e63964822460561bc19f96b1787ed15769929978ba3dd7f68c97adf5c16f671e756cd8f08c49456ca]
[key.wx = 00d9117cae81295e82682fa387991e668e1570e0e90100bf4e63964822460561bc]
[key.wy = 19f96b1787ed15769929978ba3dd7f68c97adf5c16f671e756cd8f08c49456ca]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004d9117cae81295e82682fa387991e668e1570e0e90100bf4e63964822460561bc19f96b1787ed15769929978ba3dd7f68c97adf5c16f671e756cd8f08c49456ca]
[sha = SHA-512]

# tcId = 361
# small r and s
msg = 313233343030
result = valid
sig = 3006020105020103

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 048cfcbad3524c22b992529f943e3ce0b2d126085501d6e3edd4f1dbf74bdca21eafb259b1ba179cac09e8e43a88c8a09e7339910a7c941932e44b8be56f1fccde]
[key.wx = 008cfcbad3524c22b992529f943e3ce0b2d126085501d6e3edd4f1dbf74bdca21e]
[key.wy = 00afb259b1ba179cac09e8e43a88c8a09e7339910a7c941932e44b8be56f1fccde]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200048cfcbad3524c22b992529f943e3ce0b2d126085501d6e3edd4f1dbf74bdca21eafb259b1ba179cac09e8e43a88c8a09e7339910a7c941932e44b8be56f1fccde]
[sha = SHA-512]

# tcId = 362
# small r and s
msg = 313233343030
result = valid
sig = 3006020105020105

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04fbb51127e1f1b6a38e9fe9a2544614edb8e43ad7cd8c56f14b3235dda3bc11179abd9753a9e647e9340c395fb2b91384d6d33fcb6456214350b6f3fa00f4364c]
[key.wx = 00fbb51127e1f1b6a38e9fe9a2544614edb8e43ad7cd8c56f14b3235dda3bc1117]
[key.wy = 009abd9753a9e647e9340c395fb2b91384d6d33fcb6456214350b6f3fa00f4364c]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004fbb51127e1f1b6a38e9fe9a2544614edb8e43ad7cd8c56f14b3235dda3bc11179abd9753a9e647e9340c395fb2b91384d6d33fcb6456214350b6f3fa00f4364c]
[sha = SHA-512]

# tcId = 363
# small r and s
msg = 313233343030
result = valid
sig = 3006020105020106

# tcId = 364
# r is larger than n
msg = 313233343030
result = invalid
sig = 3026022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632556020106

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04dc80905500d7d74ed47de5224d8734545f22b776ae086cabfffe6ce58d5ef994dc3067ce7d2cdfa9f4d5ace296b752814acc69c19a932d8b14077927901de3bf]
[key.wx = 00dc80905500d7d74ed47de5224d8734545f22b776ae086cabfffe6ce58d5ef994]
[key.wy = 00dc3067ce7d2cdfa9f4d5ace296b752814acc69c19a932d8b14077927901de3bf]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004dc80905500d7d74ed47de5224d8734545f22b776ae086cabfffe6ce58d5ef994dc3067ce7d2cdfa9f4d5ace296b752814acc69c19a932d8b14077927901de3bf]
[sha = SHA-512]

# tcId = 365
# s is larger than n
msg = 313233343030
result = invalid
sig = 3026020105022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc75fbd8

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 041b824a11eed94fbcd9b722d06613bbcf7eca00b9136f2652642178f37b1a920ee900de495d9ef56fa6d19f3dd1e0edb23d23835ac8c2d3d13c0227e852e503eb]
[key.wx = 1b824a11eed94fbcd9b722d06613bbcf7eca00b9136f2652642178f37b1a920e]
[key.wy = 00e900de495d9ef56fa6d19f3dd1e0edb23d23835ac8c2d3d13c0227e852e503eb]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200041b824a11eed94fbcd9b722d06613bbcf7eca00b9136f2652642178f37b1a920ee900de495d9ef56fa6d19f3dd1e0edb23d23835ac8c2d3d13c0227e852e503eb]
[sha = SHA-512]

# tcId = 366
# small r and s^-1
msg = 313233343030
result = valid
sig = 3027020201000221008f1e3c7862c58b16bb76eddbb76eddbb516af4f63f2d74d76e0d28c9bb75ea88

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 042914b30c4c784696ffc3dddcec05f36cb1488bc342b9f529d5387acb9e48cb8d3dbd30d0d5d6d6a39108863c2d6a6e8571cd3261fb9eb98ce46125bd8f139136]
[key.wx = 2914b30c4c784696ffc3dddcec05f36cb1488bc342b9f529d5387acb9e48cb8d]
[key.wy = 3dbd30d0d5d6d6a39108863c2d6a6e8571cd3261fb9eb98ce46125bd8f139136]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200042914b30c4c784696ffc3dddcec05f36cb1488bc342b9f529d5387acb9e48cb8d3dbd30d0d5d6d6a39108863c2d6a6e8571cd3261fb9eb98ce46125bd8f139136]
[sha = SHA-512]

# tcId = 367
# smallish r and s^-1
msg = 313233343030
result = valid
sig = 302c02072d9b4d347952d6022100ef3043e7329581dbb3974497710ab11505ee1c87ff907beebadd195a0ffe6d7a

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 042579f546fe2f2aeb5f822feb28f2f8371618d04815455a7e903c10024a17da415528e951147f76bee1314e65a49c6ec70686e62d38fbc23472f96e3d3b33fd1f]
[key.wx = 2579f546fe2f2aeb5f822feb28f2f8371618d04815455a7e903c10024a17da41]
[key.wy = 5528e951147f76bee1314e65a49c6ec70686e62d38fbc23472f96e3d3b33fd1f]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200042579f546fe2f2aeb5f822feb28f2f8371618d04815455a7e903c10024a17da415528e951147f76bee1314e65a49c6ec70686e62d38fbc23472f96e3d3b33fd1f]
[sha = SHA-512]

# tcId = 368
# 100-bit r and small s^-1
msg = 313233343030
result = valid
sig = 3032020d1033e67e37b32b445580bf4eff0221008b748b74000000008b748b748b748b7466e769ad4a16d3dcd87129b8e91d1b4d

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04b102196bf455ee5aafc6f895504d3c3b6b2d37c35f8669bd0f0b694795fbd992f777b6f829b9628ac35db0ef43f6a89f0a42812614e4c15924d8d47ebe45bae5]
[key.wx = 00b102196bf455ee5aafc6f895504d3c3b6b2d37c35f8669bd0f0b694795fbd992]
[key.wy = 00f777b6f829b9628ac35db0ef43f6a89f0a42812614e4c15924d8d47ebe45bae5]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004b102196bf455ee5aafc6f895504d3c3b6b2d37c35f8669bd0f0b694795fbd992f777b6f829b9628ac35db0ef43f6a89f0a42812614e4c15924d8d47ebe45bae5]
[sha = SHA-512]

# tcId = 369
# small r and 100 bit s^-1
msg = 313233343030
result = valid
sig = 302702020100022100ef9f6ba4d97c09d03178fa20b4aaad83be3cf9cb824a879fec3270fc4b81ef5b

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 044d056ab2ff57662fd6eebbe23930fef5cd08083e24146190cd01960b1fcd3749fe7ec5847651c857898be0f09efd6e0116a5dbe327f6f3080a65fc966bf64d91]
[key.wx = 4d056ab2ff57662fd6eebbe23930fef5cd08083e24146190cd01960b1fcd3749]
[key.wy = 00fe7ec5847651c857898be0f09efd6e0116a5dbe327f6f3080a65fc966bf64d91]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200044d056ab2ff57662fd6eebbe23930fef5cd08083e24146190cd01960b1fcd3749fe7ec5847651c857898be0f09efd6e0116a5dbe327f6f3080a65fc966bf64d91]
[sha = SHA-512]

# tcId = 370
# 100-bit r and s^-1
msg = 313233343030
result = valid
sig = 3032020d062522bbd3ecbe7c39e93e7c25022100ef9f6ba4d97c09d03178fa20b4aaad83be3cf9cb824a879fec3270fc4b81ef5b

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04361c4a62cd867613138dfe24ccebc4b7df1b55fc7410f4995ee2b6b9ab2220584f116c6c84e53d262fd13a5f5de6b57e7a1981de4ecdffdf3323b4e91d80649c]
[key.wx = 361c4a62cd867613138dfe24ccebc4b7df1b55fc7410f4995ee2b6b9ab222058]
[key.wy = 4f116c6c84e53d262fd13a5f5de6b57e7a1981de4ecdffdf3323b4e91d80649c]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004361c4a62cd867613138dfe24ccebc4b7df1b55fc7410f4995ee2b6b9ab2220584f116c6c84e53d262fd13a5f5de6b57e7a1981de4ecdffdf3323b4e91d80649c]
[sha = SHA-512]

# tcId = 371
# r and s^-1 are close to n
msg = 313233343030
result = valid
sig = 3045022100ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6324d50220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04db9d5c5113f00822a146c9cda2e75cb6634cd0dff54aff6e22875171f57a0dad1c424cdd83eb01c02f6f8d36f42c6dc7e39db74358da8ac9bc9dc5890d46f667]
[key.wx = 00db9d5c5113f00822a146c9cda2e75cb6634cd0dff54aff6e22875171f57a0dad]
[key.wy = 1c424cdd83eb01c02f6f8d36f42c6dc7e39db74358da8ac9bc9dc5890d46f667]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004db9d5c5113f00822a146c9cda2e75cb6634cd0dff54aff6e22875171f57a0dad1c424cdd83eb01c02f6f8d36f42c6dc7e39db74358da8ac9bc9dc5890d46f667]
[sha = SHA-512]

# tcId = 372
# s == 1
msg = 313233343030
result = valid
sig = 30250220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70020101

# tcId = 373
# s == 0
msg = 313233343030
result = invalid
sig = 30250220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70020100

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 0499f19f07b33e03caf4703e04b930d57d6d9baa44460c596a2d3064e0b63ea41286a74c4612a812ee348d2b43f80de627c11c75d81511e22a199c32119b792c6a]
[key.wx = 0099f19f07b33e03caf4703e04b930d57d6d9baa44460c596a2d3064e0b63ea412]
[key.wy = 0086a74c4612a812ee348d2b43f80de627c11c75d81511e22a199c32119b792c6a]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d0301070342000499f19f07b33e03caf4703e04b930d57d6d9baa44460c596a2d3064e0b63ea41286a74c4612a812ee348d2b43f80de627c11c75d81511e22a199c32119b792c6a]
[sha = SHA-512]

# tcId = 374
# point at infinity during verify
msg = 313233343030
result = invalid
sig = 304402207fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a80220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04313f3309b236484c6eb4ea381e007854467a617343a2e97d845801c01a632cfe33f231854bba89a8ca3f802a2764d3bf6c3233c811a31e5e8028a0b862cb1977]
[key.wx = 313f3309b236484c6eb4ea381e007854467a617343a2e97d845801c01a632cfe]
[key.wy = 33f231854bba89a8ca3f802a2764d3bf6c3233c811a31e5e8028a0b862cb1977]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004313f3309b236484c6eb4ea381e007854467a617343a2e97d845801c01a632cfe33f231854bba89a8ca3f802a2764d3bf6c3233c811a31e5e8028a0b862cb1977]
[sha = SHA-512]

# tcId = 375
# edge case for signature malleability
msg = 313233343030
result = valid
sig = 304402207fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a902207fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a8

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04d3aa01fe59bad92cffe3db59e1385391fafd7af4e4ce462e8aac157274cc8a05c7a7e603e18538aac15f89610beacc21e39898e6c5f7680a81c5bd7bd744a989]
[key.wx = 00d3aa01fe59bad92cffe3db59e1385391fafd7af4e4ce462e8aac157274cc8a05]
[key.wy = 00c7a7e603e18538aac15f89610beacc21e39898e6c5f7680a81c5bd7bd744a989]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004d3aa01fe59bad92cffe3db59e1385391fafd7af4e4ce462e8aac157274cc8a05c7a7e603e18538aac15f89610beacc21e39898e6c5f7680a81c5bd7bd744a989]
[sha = SHA-512]

# tcId = 376
# edge case for signature malleability
msg = 313233343030
result = valid
sig = 304402207fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a902207fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192a9

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 045e31eccd4704ebf7a4247ea57f9351abadff63679f2276e2a3b05009ebc1b8df648465a925010db823b2a5f3a6072343a6cc9961a9c482399d0d82051c2e3232]
[key.wx = 5e31eccd4704ebf7a4247ea57f9351abadff63679f2276e2a3b05009ebc1b8df]
[key.wy = 648465a925010db823b2a5f3a6072343a6cc9961a9c482399d0d82051c2e3232]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200045e31eccd4704ebf7a4247ea57f9351abadff63679f2276e2a3b05009ebc1b8df648465a925010db823b2a5f3a6072343a6cc9961a9c482399d0d82051c2e3232]
[sha = SHA-512]

# tcId = 377
# u1 == 1
msg = 313233343030
result = valid
sig = 30440220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70022043f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b023210281

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04ce0a47f881fd7315a733c4317848fa33c72e38de0b8fda36b61aa9a164f5808a85b05d25115ea4097ddf63f878c8e83657e66de136a8f9e62ed81a58bf117ff9]
[key.wx = 00ce0a47f881fd7315a733c4317848fa33c72e38de0b8fda36b61aa9a164f5808a]
[key.wy = 0085b05d25115ea4097ddf63f878c8e83657e66de136a8f9e62ed81a58bf117ff9]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004ce0a47f881fd7315a733c4317848fa33c72e38de0b8fda36b61aa9a164f5808a85b05d25115ea4097ddf63f878c8e83657e66de136a8f9e62ed81a58bf117ff9]
[sha = SHA-512]

# tcId = 378
# u1 == n - 1
msg = 313233343030
result = valid
sig = 30450220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70022100bc07ff031506dc74a75086a43252fb43731975a16dca6b025e867412d94222d0

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04cd6f487b47f36c0dea8f4b04c4e6ac637c76b725929c611f48addcf3d2f65941b50ea8f3a491190ee0b20cfb6efd113608e7c7c127577500e7f5c4a4e490fd60]
[key.wx = 00cd6f487b47f36c0dea8f4b04c4e6ac637c76b725929c611f48addcf3d2f65941]
[key.wy = 00b50ea8f3a491190ee0b20cfb6efd113608e7c7c127577500e7f5c4a4e490fd60]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004cd6f487b47f36c0dea8f4b04c4e6ac637c76b725929c611f48addcf3d2f65941b50ea8f3a491190ee0b20cfb6efd113608e7c7c127577500e7f5c4a4e490fd60]
[sha = SHA-512]

# tcId = 379
# u2 == 1
msg = 313233343030
result = valid
sig = 30440220555555550000000055555555555555553ef7a8e48d07df81a693439654210c700220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04456e5f8067d68a1b0a2e8fe2b28acad5755687154a0f167734ebabbdc059070d720dbe96659a66ef0cf27a73e7b3f3f145a60e0ad29f1e21dcc2bb42f0d82c1e]
[key.wx = 456e5f8067d68a1b0a2e8fe2b28acad5755687154a0f167734ebabbdc059070d]
[key.wy = 720dbe96659a66ef0cf27a73e7b3f3f145a60e0ad29f1e21dcc2bb42f0d82c1e]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004456e5f8067d68a1b0a2e8fe2b28acad5755687154a0f167734ebabbdc059070d720dbe96659a66ef0cf27a73e7b3f3f145a60e0ad29f1e21dcc2bb42f0d82c1e]
[sha = SHA-512]

# tcId = 380
# u2 == n - 1
msg = 313233343030
result = valid
sig = 30450220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70022100aaaaaaaa00000000aaaaaaaaaaaaaaaa7def51c91a0fbf034d26872ca84218e1

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 0442bf0c0ac1e3850baf5515748a878e34249f71035e20a9f54ed468ec273cb0fc5b3138500230055c71f12d53f5c7d0e3d8aa54a94c668cb311e20d195fc71abb]
[key.wx = 42bf0c0ac1e3850baf5515748a878e34249f71035e20a9f54ed468ec273cb0fc]
[key.wy = 5b3138500230055c71f12d53f5c7d0e3d8aa54a94c668cb311e20d195fc71abb]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d0301070342000442bf0c0ac1e3850baf5515748a878e34249f71035e20a9f54ed468ec273cb0fc5b3138500230055c71f12d53f5c7d0e3d8aa54a94c668cb311e20d195fc71abb]
[sha = SHA-512]

# tcId = 381
# edge case for u1
msg = 313233343030
result = valid
sig = 304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02206bfd55a8f8fdb68472e52873ef39ac3eace6d53df576f0ad2da4607bb52c0d46

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04ffdd48da63d3af67223f16c51eb7e95600eb0b0e8b964f4fcd8c534face3c2c2b4e009ab2a76829480e69c9e43b2f1fe076cfafb3fa8d27dd4d6bab4d6c3db54]
[key.wx = 00ffdd48da63d3af67223f16c51eb7e95600eb0b0e8b964f4fcd8c534face3c2c2]
[key.wy = 00b4e009ab2a76829480e69c9e43b2f1fe076cfafb3fa8d27dd4d6bab4d6c3db54]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004ffdd48da63d3af67223f16c51eb7e95600eb0b0e8b964f4fcd8c534face3c2c2b4e009ab2a76829480e69c9e43b2f1fe076cfafb3fa8d27dd4d6bab4d6c3db54]
[sha = SHA-512]

# tcId = 382
# edge case for u1
msg = 313233343030
result = valid
sig = 304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0220654937791db0686f712ff9b453eeadb0026c9b058bba49199ca3e8fac03c094f

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04793cbfce6f335dcfede7c6898ea1c537d7661ed6a8c9d308d64a2560d21c6e2c483d23a5ff05da00eaf9d52cf5362be9b53b95316c6a32e9ebe68d9ac35c2fd6]
[key.wx = 793cbfce6f335dcfede7c6898ea1c537d7661ed6a8c9d308d64a2560d21c6e2c]
[key.wy = 483d23a5ff05da00eaf9d52cf5362be9b53b95316c6a32e9ebe68d9ac35c2fd6]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004793cbfce6f335dcfede7c6898ea1c537d7661ed6a8c9d308d64a2560d21c6e2c483d23a5ff05da00eaf9d52cf5362be9b53b95316c6a32e9ebe68d9ac35c2fd6]
[sha = SHA-512]

# tcId = 383
# edge case for u1
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100c51bbee23a95437abe5c978f8fe596a31c858ac8d55be9786aa5d36a5ac74e97

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04a9f7023f559d4bb6c9f4bc3643e2824aff5451d929479ec3ea5eb30bad2c36ac6a7c77e8dd21f4ad49b103e67da9d3cda62b653dd194fad2ba8d1dd37bb0ea9b]
[key.wx = 00a9f7023f559d4bb6c9f4bc3643e2824aff5451d929479ec3ea5eb30bad2c36ac]
[key.wy = 6a7c77e8dd21f4ad49b103e67da9d3cda62b653dd194fad2ba8d1dd37bb0ea9b]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004a9f7023f559d4bb6c9f4bc3643e2824aff5451d929479ec3ea5eb30bad2c36ac6a7c77e8dd21f4ad49b103e67da9d3cda62b653dd194fad2ba8d1dd37bb0ea9b]
[sha = SHA-512]

# tcId = 384
# edge case for u1
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0221008ba4c3da7154ba564ab344ae12005aa482b6c1639ea191f8568afb6e47163c45

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04df79ee082b2fc77e9ce4633471f569bbcb5ce53856e3067774f37e8a64a2c7ffaa488a6c34d499df76f427de3609bfcfd9feae67ffe0b0de594463c453b0ab16]
[key.wx = 00df79ee082b2fc77e9ce4633471f569bbcb5ce53856e3067774f37e8a64a2c7ff]
[key.wy = 00aa488a6c34d499df76f427de3609bfcfd9feae67ffe0b0de594463c453b0ab16]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004df79ee082b2fc77e9ce4633471f569bbcb5ce53856e3067774f37e8a64a2c7ffaa488a6c34d499df76f427de3609bfcfd9feae67ffe0b0de594463c453b0ab16]
[sha = SHA-512]

# tcId = 385
# edge case for u1
msg = 313233343030
result = valid
sig = 304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02204c3dafcf4ba55bf1344ae12005aa4a74f46eaa85f5023131cc637ae2ea90ab26

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 044cc3bf65e32e00284adfca00f40df755415c485091ac0489ae9a337103a5f8f0123ab86dd433b933b4f2063c002144df3cfeba78dad0ed89c0377541532908c2]
[key.wx = 4cc3bf65e32e00284adfca00f40df755415c485091ac0489ae9a337103a5f8f0]
[key.wy = 123ab86dd433b933b4f2063c002144df3cfeba78dad0ed89c0377541532908c2]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200044cc3bf65e32e00284adfca00f40df755415c485091ac0489ae9a337103a5f8f0123ab86dd433b933b4f2063c002144df3cfeba78dad0ed89c0377541532908c2]
[sha = SHA-512]

# tcId = 386
# edge case for u1
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100987b5f9e974ab7e26895c2400b5494e9e8dd550bea04626398c6f5c5d521564c

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04264a7ad439a4828a9dc97ecf837155355f99ae0b65975f851b541ad3a0e032f067268b7298c73e581866fbcbd161689b16b81cf262e007ce68e25a28c83ef041]
[key.wx = 264a7ad439a4828a9dc97ecf837155355f99ae0b65975f851b541ad3a0e032f0]
[key.wy = 67268b7298c73e581866fbcbd161689b16b81cf262e007ce68e25a28c83ef041]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004264a7ad439a4828a9dc97ecf837155355f99ae0b65975f851b541ad3a0e032f067268b7298c73e581866fbcbd161689b16b81cf262e007ce68e25a28c83ef041]
[sha = SHA-512]

# tcId = 387
# edge case for u1
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100fcf97e2fbf0e80d412005aa4a75086a3f004f59d512cb47271798733ab418606

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 041d7ff4d3a41206c8143635f12876e0ea0875ea5e4a5a249250d0eda33daa211f56e89c0beaf910ac934ca12380455600d0fd85b56a7035cb171b3f1c72a15569]
[key.wx = 1d7ff4d3a41206c8143635f12876e0ea0875ea5e4a5a249250d0eda33daa211f]
[key.wy = 56e89c0beaf910ac934ca12380455600d0fd85b56a7035cb171b3f1c72a15569]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200041d7ff4d3a41206c8143635f12876e0ea0875ea5e4a5a249250d0eda33daa211f56e89c0beaf910ac934ca12380455600d0fd85b56a7035cb171b3f1c72a15569]
[sha = SHA-512]

# tcId = 388
# edge case for u1
msg = 313233343030
result = valid
sig = 304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022079d482b60864d6c5cb4fd5db9e7e28ccd9a5948c316c8740fb429c0f37169a02

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04b09685f338dceb421778a1458d52bed734c236242da2baa280d6f6b7b86e4f117fe6a34146b422d7aebd1a51b20948d7872a514c4cfd7686dc436b70733d6473]
[key.wx = 00b09685f338dceb421778a1458d52bed734c236242da2baa280d6f6b7b86e4f11]
[key.wy = 7fe6a34146b422d7aebd1a51b20948d7872a514c4cfd7686dc436b70733d6473]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004b09685f338dceb421778a1458d52bed734c236242da2baa280d6f6b7b86e4f117fe6a34146b422d7aebd1a51b20948d7872a514c4cfd7686dc436b70733d6473]
[sha = SHA-512]

# tcId = 389
# edge case for u1
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0221008ecd11081a4d0759c14f7bf46813d52cc6738115321be0a4da78a3356bb71510

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04dd811f2c0f5e9d4fbb2ef31818c1cd807247bc14fcd1170bef00e2c71dc037b443a15cdf8f3fbdc87e06250c0720d261d2b8d087fa7bf9548f6293f0ce5ae899]
[key.wx = 00dd811f2c0f5e9d4fbb2ef31818c1cd807247bc14fcd1170bef00e2c71dc037b4]
[key.wy = 43a15cdf8f3fbdc87e06250c0720d261d2b8d087fa7bf9548f6293f0ce5ae899]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004dd811f2c0f5e9d4fbb2ef31818c1cd807247bc14fcd1170bef00e2c71dc037b443a15cdf8f3fbdc87e06250c0720d261d2b8d087fa7bf9548f6293f0ce5ae899]
[sha = SHA-512]

# tcId = 390
# edge case for u1
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100e8dbffed13c9a2093085c079714f11f24eb583d73ba2b416b3169183e7d9b4c2

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 0469d60ae1f39e1da95809d408894707ad2134f4943a1db089bebf815a391f18db32b401d98bf894d3b6d59e6eb45573285642e358ad687b7d7bf9600b1987809e]
[key.wx = 69d60ae1f39e1da95809d408894707ad2134f4943a1db089bebf815a391f18db]
[key.wy = 32b401d98bf894d3b6d59e6eb45573285642e358ad687b7d7bf9600b1987809e]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d0301070342000469d60ae1f39e1da95809d408894707ad2134f4943a1db089bebf815a391f18db32b401d98bf894d3b6d59e6eb45573285642e358ad687b7d7bf9600b1987809e]
[sha = SHA-512]

# tcId = 391
# edge case for u1
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100ca01552a838124bec68d6bc6086329e06673900eac5c262e5ce79a8521cd1eae

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04a658553a0620c95e987b5c3163bcfea68c52065f53c9d553f2a924d8b3ed511f79f0dfec4536b65aa5fb31297e96f6b464aa669b9268b3156c43d4612978a577]
[key.wx = 00a658553a0620c95e987b5c3163bcfea68c52065f53c9d553f2a924d8b3ed511f]
[key.wy = 79f0dfec4536b65aa5fb31297e96f6b464aa669b9268b3156c43d4612978a577]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004a658553a0620c95e987b5c3163bcfea68c52065f53c9d553f2a924d8b3ed511f79f0dfec4536b65aa5fb31297e96f6b464aa669b9268b3156c43d4612978a577]
[sha = SHA-512]

# tcId = 392
# edge case for u1
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0221009402aa560702497c8d1ad78c10c653c11000256fb1a0add7c6156a474737180b

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04bc4d3354a6a973dd8088919cc181194e879ed7920db30d0d1278edf74413b7b92450d162b26dcb25fbbd53ea4044189981d737055925bd2e86bfb0374b09f3ca]
[key.wx = 00bc4d3354a6a973dd8088919cc181194e879ed7920db30d0d1278edf74413b7b9]
[key.wy = 2450d162b26dcb25fbbd53ea4044189981d737055925bd2e86bfb0374b09f3ca]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004bc4d3354a6a973dd8088919cc181194e879ed7920db30d0d1278edf74413b7b92450d162b26dcb25fbbd53ea4044189981d737055925bd2e86bfb0374b09f3ca]
[sha = SHA-512]

# tcId = 393
# edge case for u1
msg = 313233343030
result = valid
sig = 304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02205e03ff818a836e3a53a8435219297da1b98cbad0b6e535812f433a096ca11168

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 040eb628724fce764c687d874ade7b8e0aa4abf20ee6e3610fac9fe3e72f97ab5aed09f4843660eb1daf015d397a7c1073d7ae43bda0ba3e117008785abfffa00f]
[key.wx = 0eb628724fce764c687d874ade7b8e0aa4abf20ee6e3610fac9fe3e72f97ab5a]
[key.wy = 00ed09f4843660eb1daf015d397a7c1073d7ae43bda0ba3e117008785abfffa00f]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200040eb628724fce764c687d874ade7b8e0aa4abf20ee6e3610fac9fe3e72f97ab5aed09f4843660eb1daf015d397a7c1073d7ae43bda0ba3e117008785abfffa00f]
[sha = SHA-512]

# tcId = 394
# edge case for u1
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100e28ddf709d4aa1bddf2e4bc7c7f2cb516cb642bb3e39c3feaf2fcf16ab9539f4

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04e7ac5cc7f296912f703f59fe88e49b521da245e12e6eee161ee6b3b1127611a77b3bedd2a773cf58b0629b936dd85dad2d0c39676306ed63e1a9bcd0e08bccc2]
[key.wx = 00e7ac5cc7f296912f703f59fe88e49b521da245e12e6eee161ee6b3b1127611a7]
[key.wy = 7b3bedd2a773cf58b0629b936dd85dad2d0c39676306ed63e1a9bcd0e08bccc2]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004e7ac5cc7f296912f703f59fe88e49b521da245e12e6eee161ee6b3b1127611a77b3bedd2a773cf58b0629b936dd85dad2d0c39676306ed63e1a9bcd0e08bccc2]
[sha = SHA-512]

# tcId = 395
# edge case for u2
msg = 313233343030
result = valid
sig = 304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02207fffffffaaaaaaaaffffffffffffffffe9a2538f37b28a2c513dee40fecbb71a

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 042407b60abf3ee5edaf92ed505a11d0ddce0ea33eca58a031bb2f162c512f4062fb81bff36bf967e834e3d5d468730dcd70440022ab60061a62fac53350fe259f]
[key.wx = 2407b60abf3ee5edaf92ed505a11d0ddce0ea33eca58a031bb2f162c512f4062]
[key.wy = 00fb81bff36bf967e834e3d5d468730dcd70440022ab60061a62fac53350fe259f]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200042407b60abf3ee5edaf92ed505a11d0ddce0ea33eca58a031bb2f162c512f4062fb81bff36bf967e834e3d5d468730dcd70440022ab60061a62fac53350fe259f]
[sha = SHA-512]

# tcId = 396
# edge case for u2
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100b62f26b5f2a2b26f6de86d42ad8a13da3ab3cccd0459b201de009e526adf21f2

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 0447b2ad96dfc2f23fe5926809f38042b2c801962bd7394cefbf4aacb2554b7b0bdf2b937a16a7d96a2a0682cd164428890208597f2cdcc734fda73600b5cf6c59]
[key.wx = 47b2ad96dfc2f23fe5926809f38042b2c801962bd7394cefbf4aacb2554b7b0b]
[key.wy = 00df2b937a16a7d96a2a0682cd164428890208597f2cdcc734fda73600b5cf6c59]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d0301070342000447b2ad96dfc2f23fe5926809f38042b2c801962bd7394cefbf4aacb2554b7b0bdf2b937a16a7d96a2a0682cd164428890208597f2cdcc734fda73600b5cf6c59]
[sha = SHA-512]

# tcId = 397
# edge case for u2
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100bb1d9ac949dd748cd02bbbe749bd351cd57b38bb61403d700686aa7b4c90851e

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 0469a65b75f31ae7b4930292f90902461befcee5d1606939c28e01b652a7fbc498cf68619e5860128f56cecf53eba2ffe82889a9bb04a5fa4c8b722bc91d55978a]
[key.wx = 69a65b75f31ae7b4930292f90902461befcee5d1606939c28e01b652a7fbc498]
[key.wy = 00cf68619e5860128f56cecf53eba2ffe82889a9bb04a5fa4c8b722bc91d55978a]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d0301070342000469a65b75f31ae7b4930292f90902461befcee5d1606939c28e01b652a7fbc498cf68619e5860128f56cecf53eba2ffe82889a9bb04a5fa4c8b722bc91d55978a]
[sha = SHA-512]

# tcId = 398
# edge case for u2
msg = 313233343030
result = valid
sig = 304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022066755a00638cdaec1c732513ca0234ece52545dac11f816e818f725b4f60aaf2

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04b2037176c84db04a6c773e32f9ed1d6b25ef4c303c6725c6932ec2cc2788bcbb9361505e6b771691adb41598f292d6521722404bf183241b195738b77abd6cfe]
[key.wx = 00b2037176c84db04a6c773e32f9ed1d6b25ef4c303c6725c6932ec2cc2788bcbb]
[key.wy = 009361505e6b771691adb41598f292d6521722404bf183241b195738b77abd6cfe]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004b2037176c84db04a6c773e32f9ed1d6b25ef4c303c6725c6932ec2cc2788bcbb9361505e6b771691adb41598f292d6521722404bf183241b195738b77abd6cfe]
[sha = SHA-512]

# tcId = 399
# edge case for u2
msg = 313233343030
result = valid
sig = 304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022055a00c9fcdaebb6032513ca0234ecfffe98ebe492fdf02e48ca48e982beb3669

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 041eef95aef71f793afd50bb2604064d63e88bef7404a4d0e206446245ae2e7834c96e86dd040f9794b63712d90e719576b8b92c406ab0f288ad9b327bd124454f]
[key.wx = 1eef95aef71f793afd50bb2604064d63e88bef7404a4d0e206446245ae2e7834]
[key.wy = 00c96e86dd040f9794b63712d90e719576b8b92c406ab0f288ad9b327bd124454f]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200041eef95aef71f793afd50bb2604064d63e88bef7404a4d0e206446245ae2e7834c96e86dd040f9794b63712d90e719576b8b92c406ab0f288ad9b327bd124454f]
[sha = SHA-512]

# tcId = 400
# edge case for u2
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100ab40193f9b5d76c064a27940469d9fffd31d7c925fbe05c919491d3057d66cd2

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04a9734899c954e5b7adbca8f783428b5fbcbdfd3d2813f8d2f95b31a78ab107567667abf8c02ce4951bc59b2564130c27d7b64cdbc5cad95ca42d5bbb7cd4e793]
[key.wx = 00a9734899c954e5b7adbca8f783428b5fbcbdfd3d2813f8d2f95b31a78ab10756]
[key.wy = 7667abf8c02ce4951bc59b2564130c27d7b64cdbc5cad95ca42d5bbb7cd4e793]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004a9734899c954e5b7adbca8f783428b5fbcbdfd3d2813f8d2f95b31a78ab107567667abf8c02ce4951bc59b2564130c27d7b64cdbc5cad95ca42d5bbb7cd4e793]
[sha = SHA-512]

# tcId = 401
# edge case for u2
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100ca0234ebb5fdcb13ca0234ecffffffffcb0dadbbc7f549f8a26b4408d0dc8600

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 041ae51662331a1dbfab0751d30dfab2273a04a239e055a537b16ab595f9612396434f21c2bfe6555c9fc4a8e82dab1fa5631881b016e0831d9e1bbf5799fcf32e]
[key.wx = 1ae51662331a1dbfab0751d30dfab2273a04a239e055a537b16ab595f9612396]
[key.wy = 434f21c2bfe6555c9fc4a8e82dab1fa5631881b016e0831d9e1bbf5799fcf32e]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200041ae51662331a1dbfab0751d30dfab2273a04a239e055a537b16ab595f9612396434f21c2bfe6555c9fc4a8e82dab1fa5631881b016e0831d9e1bbf5799fcf32e]
[sha = SHA-512]

# tcId = 402
# edge case for u2
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100bfffffff3ea3677e082b9310572620ae19933a9e65b285598711c77298815ad3

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 0453c90cdd8b0dadd21c44ad557b327f4dbf57144aaf06597deb3f94125206a6c14603475bd79b30e36340cd09b0b59e6cd46ce90150e9ffe5c8a0172b2c9898e3]
[key.wx = 53c90cdd8b0dadd21c44ad557b327f4dbf57144aaf06597deb3f94125206a6c1]
[key.wy = 4603475bd79b30e36340cd09b0b59e6cd46ce90150e9ffe5c8a0172b2c9898e3]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d0301070342000453c90cdd8b0dadd21c44ad557b327f4dbf57144aaf06597deb3f94125206a6c14603475bd79b30e36340cd09b0b59e6cd46ce90150e9ffe5c8a0172b2c9898e3]
[sha = SHA-512]

# tcId = 403
# edge case for u2
msg = 313233343030
result = valid
sig = 304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd0220266666663bbbbbbbe6666666666666665b37902e023fab7c8f055d86e5cc41f4

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 0433797539515c51f429967b8e36930d9fdda1edb13aecec9771f7cde5f6f2e74eba51d0b6456bb902dba1f3ea436f96ad2355da454dc9b32c503c4bc6cfd6d410]
[key.wx = 33797539515c51f429967b8e36930d9fdda1edb13aecec9771f7cde5f6f2e74e]
[key.wy = 00ba51d0b6456bb902dba1f3ea436f96ad2355da454dc9b32c503c4bc6cfd6d410]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d0301070342000433797539515c51f429967b8e36930d9fdda1edb13aecec9771f7cde5f6f2e74eba51d0b6456bb902dba1f3ea436f96ad2355da454dc9b32c503c4bc6cfd6d410]
[sha = SHA-512]

# tcId = 404
# edge case for u2
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100bfffffff36db6db7a492492492492492146c573f4c6dfc8d08a443e258970b09

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 040a8f5f1d5bbd2783fa7f37c86879057fb2fcf25383aafb86d03d6bafb41a17b3eaf6da715fe950349fd5736117b08e15e32cf1d2fdc003e510009f1b4ba1e648]
[key.wx = 0a8f5f1d5bbd2783fa7f37c86879057fb2fcf25383aafb86d03d6bafb41a17b3]
[key.wy = 00eaf6da715fe950349fd5736117b08e15e32cf1d2fdc003e510009f1b4ba1e648]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200040a8f5f1d5bbd2783fa7f37c86879057fb2fcf25383aafb86d03d6bafb41a17b3eaf6da715fe950349fd5736117b08e15e32cf1d2fdc003e510009f1b4ba1e648]
[sha = SHA-512]

# tcId = 405
# edge case for u2
msg = 313233343030
result = valid
sig = 304502207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd022100bfffffff2aaaaaab7fffffffffffffffc815d0e60b3e596ecb1ad3a27cfd49c4

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 041dbc94e96c056b9d2cb6773bb24b69ed473851badf927a29955aff290ef3675a65e587561122aa8226facb95df08308cadf01c8351a1569176d917821113aa7c]
[key.wx = 1dbc94e96c056b9d2cb6773bb24b69ed473851badf927a29955aff290ef3675a]
[key.wy = 65e587561122aa8226facb95df08308cadf01c8351a1569176d917821113aa7c]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200041dbc94e96c056b9d2cb6773bb24b69ed473851badf927a29955aff290ef3675a65e587561122aa8226facb95df08308cadf01c8351a1569176d917821113aa7c]
[sha = SHA-512]

# tcId = 406
# edge case for u2
msg = 313233343030
result = valid
sig = 304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02207fffffff55555555ffffffffffffffffd344a71e6f651458a27bdc81fd976e37

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04084ab885dbff7f12e6cdadb59d456e500797779425c7518c259c83718289e6e991c345d3a093e86670605bbc2ff4c69d0ed694fd433ec6b6ba1bf7d56c3e6b51]
[key.wx = 084ab885dbff7f12e6cdadb59d456e500797779425c7518c259c83718289e6e9]
[key.wy = 0091c345d3a093e86670605bbc2ff4c69d0ed694fd433ec6b6ba1bf7d56c3e6b51]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004084ab885dbff7f12e6cdadb59d456e500797779425c7518c259c83718289e6e991c345d3a093e86670605bbc2ff4c69d0ed694fd433ec6b6ba1bf7d56c3e6b51]
[sha = SHA-512]

# tcId = 407
# edge case for u2
msg = 313233343030
result = valid
sig = 304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02203fffffff800000007fffffffffffffffde737d56d38bcf4279dce5617e3192aa

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04003adfa4c620a207096cd18ee8fd2a90e20106cf824a0c63d6dec727a9fe7f509430d26bdd5f71e819d12b70069901461ae083cc809122d4fb86b5c475244e5a]
[key.wx = 3adfa4c620a207096cd18ee8fd2a90e20106cf824a0c63d6dec727a9fe7f50]
[key.wy = 009430d26bdd5f71e819d12b70069901461ae083cc809122d4fb86b5c475244e5a]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004003adfa4c620a207096cd18ee8fd2a90e20106cf824a0c63d6dec727a9fe7f509430d26bdd5f71e819d12b70069901461ae083cc809122d4fb86b5c475244e5a]
[sha = SHA-512]

# tcId = 408
# edge case for u2
msg = 313233343030
result = valid
sig = 304402207ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd02205d8ecd64a4eeba466815ddf3a4de9a8e6abd9c5db0a01eb80343553da648428f

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 047c98b2d47eb433c0d18e533cfbc8909d66f7b79d5925ccb17eccec9d105c58848d5ca99b350bd7d10ab5ee6fcfe46623fdc03e9f828158f4d4cc08ad1ff83de4]
[key.wx = 7c98b2d47eb433c0d18e533cfbc8909d66f7b79d5925ccb17eccec9d105c5884]
[key.wy = 008d5ca99b350bd7d10ab5ee6fcfe46623fdc03e9f828158f4d4cc08ad1ff83de4]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200047c98b2d47eb433c0d18e533cfbc8909d66f7b79d5925ccb17eccec9d105c58848d5ca99b350bd7d10ab5ee6fcfe46623fdc03e9f828158f4d4cc08ad1ff83de4]
[sha = SHA-512]

# tcId = 409
# point duplication during verification
msg = 313233343030
result = valid
sig = 304502206f2347cab7dd76858fe0555ac3bc99048c4aacafdfb6bcbe05ea6c42c4934569022100b4cfa1996ec1d24cdbc8fa17fcabc3a5d4b2b36cf4b50a7b775ab78785710746
flags = PointDuplication

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 047c98b2d47eb433c0d18e533cfbc8909d66f7b79d5925ccb17eccec9d105c588472a35663caf4282ff54a1190301b99dc023fc1617d7ea70b2b33f752e007c21b]
[key.wx = 7c98b2d47eb433c0d18e533cfbc8909d66f7b79d5925ccb17eccec9d105c5884]
[key.wy = 72a35663caf4282ff54a1190301b99dc023fc1617d7ea70b2b33f752e007c21b]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200047c98b2d47eb433c0d18e533cfbc8909d66f7b79d5925ccb17eccec9d105c588472a35663caf4282ff54a1190301b99dc023fc1617d7ea70b2b33f752e007c21b]
[sha = SHA-512]

# tcId = 410
# duplication bug
msg = 313233343030
result = invalid
sig = 304502206f2347cab7dd76858fe0555ac3bc99048c4aacafdfb6bcbe05ea6c42c4934569022100b4cfa1996ec1d24cdbc8fa17fcabc3a5d4b2b36cf4b50a7b775ab78785710746
flags = PointDuplication

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04b7a90e21e7547d73267940033cea05042c50f7c9fa5eaeb471cd6260c685f2e38bb7309d0c3bab249faaf3e44179d6dd5302375c580fd0570a788c6be3680c67]
[key.wx = 00b7a90e21e7547d73267940033cea05042c50f7c9fa5eaeb471cd6260c685f2e3]
[key.wy = 008bb7309d0c3bab249faaf3e44179d6dd5302375c580fd0570a788c6be3680c67]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004b7a90e21e7547d73267940033cea05042c50f7c9fa5eaeb471cd6260c685f2e38bb7309d0c3bab249faaf3e44179d6dd5302375c580fd0570a788c6be3680c67]
[sha = SHA-512]

# tcId = 411
# point with x-coordinate 0
msg = 313233343030
result = invalid
sig = 30250201010220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 041550a173373b2d594374f0642cd73de06a045c09c7a4f388c731e8cd8971adfc9a3a9843583a86c0e1c62cbde67165f40a926b1028ba38aa3895e188ebbc7066]
[key.wx = 1550a173373b2d594374f0642cd73de06a045c09c7a4f388c731e8cd8971adfc]
[key.wy = 009a3a9843583a86c0e1c62cbde67165f40a926b1028ba38aa3895e188ebbc7066]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200041550a173373b2d594374f0642cd73de06a045c09c7a4f388c731e8cd8971adfc9a3a9843583a86c0e1c62cbde67165f40a926b1028ba38aa3895e188ebbc7066]
[sha = SHA-512]

# tcId = 412
# point with x-coordinate 0
msg = 313233343030
result = invalid
sig = 3045022101000000000000000000000000000000000000000000000000000000000000000002203333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aa9

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04313447778195daa1791a6530cd0697ae34bf9d8d225984394f72eef3505971110996a8fbdd1a70ecd64cb00b595afe1669bfef80d91756a62d84c1d83e0f22ab]
[key.wx = 313447778195daa1791a6530cd0697ae34bf9d8d225984394f72eef350597111]
[key.wy = 0996a8fbdd1a70ecd64cb00b595afe1669bfef80d91756a62d84c1d83e0f22ab]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004313447778195daa1791a6530cd0697ae34bf9d8d225984394f72eef3505971110996a8fbdd1a70ecd64cb00b595afe1669bfef80d91756a62d84c1d83e0f22ab]
[sha = SHA-512]

# tcId = 413
# comparison with point at infinity 
msg = 313233343030
result = invalid
sig = 30440220555555550000000055555555555555553ef7a8e48d07df81a693439654210c7002203333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aa9

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 044ada634941476ca63c2c5803eec2f33b2d17920f798a5be6275f5a54cd2e7639b1a04bead5c7314c427492db21b9544d81caa8159587e41aa023aa967f31aaa1]
[key.wx = 4ada634941476ca63c2c5803eec2f33b2d17920f798a5be6275f5a54cd2e7639]
[key.wy = 00b1a04bead5c7314c427492db21b9544d81caa8159587e41aa023aa967f31aaa1]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200044ada634941476ca63c2c5803eec2f33b2d17920f798a5be6275f5a54cd2e7639b1a04bead5c7314c427492db21b9544d81caa8159587e41aa023aa967f31aaa1]
[sha = SHA-512]

# tcId = 414
# extreme value for k and edgecase s
msg = 313233343030
result = valid
sig = 304402207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc476699780220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04aacce093270fa59ad412b5459a08e490743b97086c781ac3c8d54030b41a31193bece4956172d56befb7011d684e772905e48d2115444a75ac7a325a3f25f4b1]
[key.wx = 00aacce093270fa59ad412b5459a08e490743b97086c781ac3c8d54030b41a3119]
[key.wy = 3bece4956172d56befb7011d684e772905e48d2115444a75ac7a325a3f25f4b1]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004aacce093270fa59ad412b5459a08e490743b97086c781ac3c8d54030b41a31193bece4956172d56befb7011d684e772905e48d2115444a75ac7a325a3f25f4b1]
[sha = SHA-512]

# tcId = 415
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 304502207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978022100b6db6db6249249254924924924924924625bd7a09bec4ca81bcdd9f8fd6b63cc

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04f62b8d7feeff5a847ab79212269e55e62fa87ebe930821747b57a511a5ea99f0439ee057bb27898582a683c3fdb7f95404d41d42f276803751a316eb3aab7ebf]
[key.wx = 00f62b8d7feeff5a847ab79212269e55e62fa87ebe930821747b57a511a5ea99f0]
[key.wy = 439ee057bb27898582a683c3fdb7f95404d41d42f276803751a316eb3aab7ebf]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004f62b8d7feeff5a847ab79212269e55e62fa87ebe930821747b57a511a5ea99f0439ee057bb27898582a683c3fdb7f95404d41d42f276803751a316eb3aab7ebf]
[sha = SHA-512]

# tcId = 416
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 304502207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978022100cccccccc00000000cccccccccccccccc971f2ef152794b9d8fc7d568c9e8eaa7

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 044baa07ff6e7bb9aa223d1c61932005fe98fe78b787fdab4bd3619bc8833072a2bcacd63802c56af82607953e72a0f5d3c23bd265544e020951824ea485555d33]
[key.wx = 4baa07ff6e7bb9aa223d1c61932005fe98fe78b787fdab4bd3619bc8833072a2]
[key.wy = 00bcacd63802c56af82607953e72a0f5d3c23bd265544e020951824ea485555d33]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200044baa07ff6e7bb9aa223d1c61932005fe98fe78b787fdab4bd3619bc8833072a2bcacd63802c56af82607953e72a0f5d3c23bd265544e020951824ea485555d33]
[sha = SHA-512]

# tcId = 417
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 304402207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc4766997802203333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aaa

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 040c753ed1ba92f766800fdd0ae1c0d7f8f4cd8305fd803d8bca881397b5937e2db568509b1faf3cf251de6db9810e8b8caed235da10eeddbed62775c8e5c9460a]
[key.wx = 0c753ed1ba92f766800fdd0ae1c0d7f8f4cd8305fd803d8bca881397b5937e2d]
[key.wy = 00b568509b1faf3cf251de6db9810e8b8caed235da10eeddbed62775c8e5c9460a]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200040c753ed1ba92f766800fdd0ae1c0d7f8f4cd8305fd803d8bca881397b5937e2db568509b1faf3cf251de6db9810e8b8caed235da10eeddbed62775c8e5c9460a]
[sha = SHA-512]

# tcId = 418
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 304402207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978022049249248db6db6dbb6db6db6db6db6db5a8b230d0b2b51dcd7ebf0c9fef7c185

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04030fdcae6541f22c5bab254e4f1a285c507d1cefea03bf90cf19daf3cb62df695ff2c94d588f2c2b2b0a12bebc011bcee4fa1b54506ec07d0a29d24a0891193c]
[key.wx = 030fdcae6541f22c5bab254e4f1a285c507d1cefea03bf90cf19daf3cb62df69]
[key.wy = 5ff2c94d588f2c2b2b0a12bebc011bcee4fa1b54506ec07d0a29d24a0891193c]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004030fdcae6541f22c5bab254e4f1a285c507d1cefea03bf90cf19daf3cb62df695ff2c94d588f2c2b2b0a12bebc011bcee4fa1b54506ec07d0a29d24a0891193c]
[sha = SHA-512]

# tcId = 419
# extreme value for k
msg = 313233343030
result = valid
sig = 304402207cf27b188d034f7e8a52380304b51ac3c08969e277f21b35a60b48fc47669978022016a4502e2781e11ac82cbc9d1edd8c981584d13e18411e2f6e0478c34416e3bb

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 0403fc621eaf90c23d8f9fa125d2c59b8728ebccb30ca3e3db879a06ca90f20cdcae58d3f0c6aef0e805be10ea54e23cf6f0397f9addddc2b09088855316b0ef44]
[key.wx = 03fc621eaf90c23d8f9fa125d2c59b8728ebccb30ca3e3db879a06ca90f20cdc]
[key.wy = 00ae58d3f0c6aef0e805be10ea54e23cf6f0397f9addddc2b09088855316b0ef44]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d0301070342000403fc621eaf90c23d8f9fa125d2c59b8728ebccb30ca3e3db879a06ca90f20cdcae58d3f0c6aef0e805be10ea54e23cf6f0397f9addddc2b09088855316b0ef44]
[sha = SHA-512]

# tcId = 420
# extreme value for k and edgecase s
msg = 313233343030
result = valid
sig = 304402206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2960220555555550000000055555555555555553ef7a8e48d07df81a693439654210c70

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 0470f2ce24dc62923bb09cc92d74329bbd0d2e6b0e354c0be2383d24acdccb9e4cd42d1f973466f5e5462a939084a294ebfc7a45629c70ee5def46de9536ea7bf7]
[key.wx = 70f2ce24dc62923bb09cc92d74329bbd0d2e6b0e354c0be2383d24acdccb9e4c]
[key.wy = 00d42d1f973466f5e5462a939084a294ebfc7a45629c70ee5def46de9536ea7bf7]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d0301070342000470f2ce24dc62923bb09cc92d74329bbd0d2e6b0e354c0be2383d24acdccb9e4cd42d1f973466f5e5462a939084a294ebfc7a45629c70ee5def46de9536ea7bf7]
[sha = SHA-512]

# tcId = 421
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 304502206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296022100b6db6db6249249254924924924924924625bd7a09bec4ca81bcdd9f8fd6b63cc

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04732b8ac0c30fe44307431235271cb5d6e5f677a19ce3f058b939a7bf19349d3c858cc735af8577468275847cf5ec19972e6c20738276e2708b23c595bfc4433d]
[key.wx = 732b8ac0c30fe44307431235271cb5d6e5f677a19ce3f058b939a7bf19349d3c]
[key.wy = 00858cc735af8577468275847cf5ec19972e6c20738276e2708b23c595bfc4433d]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004732b8ac0c30fe44307431235271cb5d6e5f677a19ce3f058b939a7bf19349d3c858cc735af8577468275847cf5ec19972e6c20738276e2708b23c595bfc4433d]
[sha = SHA-512]

# tcId = 422
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 304502206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296022100cccccccc00000000cccccccccccccccc971f2ef152794b9d8fc7d568c9e8eaa7

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 0447aff9501825a166782bb58a5b459006eacdbce5e5323addad34ec1b6444cdce9199c31502ad4277c73ddd0c807b72634c45762404837d9814a5d4b5a7c3f398]
[key.wx = 47aff9501825a166782bb58a5b459006eacdbce5e5323addad34ec1b6444cdce]
[key.wy = 009199c31502ad4277c73ddd0c807b72634c45762404837d9814a5d4b5a7c3f398]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d0301070342000447aff9501825a166782bb58a5b459006eacdbce5e5323addad34ec1b6444cdce9199c31502ad4277c73ddd0c807b72634c45762404837d9814a5d4b5a7c3f398]
[sha = SHA-512]

# tcId = 423
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 304402206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c29602203333333300000000333333333333333325c7cbbc549e52e763f1f55a327a3aaa

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04aed8eeff77644bf83b9222f8f57173fa8217ec7e0763ee7d7171fb6092fba5c06486a86d94f48834ba5adbaf349687f9cee400389642b828e68207b147ca2c46]
[key.wx = 00aed8eeff77644bf83b9222f8f57173fa8217ec7e0763ee7d7171fb6092fba5c0]
[key.wy = 6486a86d94f48834ba5adbaf349687f9cee400389642b828e68207b147ca2c46]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004aed8eeff77644bf83b9222f8f57173fa8217ec7e0763ee7d7171fb6092fba5c06486a86d94f48834ba5adbaf349687f9cee400389642b828e68207b147ca2c46]
[sha = SHA-512]

# tcId = 424
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 304402206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296022049249248db6db6dbb6db6db6db6db6db5a8b230d0b2b51dcd7ebf0c9fef7c185

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04f7c54a585a904300d05b53ef3b854e71999a344b89adc0caaa28e254db9bc7c7c161a79f38ff446051303577e40638fb020329940a63c241bb32c2205eb57b7d]
[key.wx = 00f7c54a585a904300d05b53ef3b854e71999a344b89adc0caaa28e254db9bc7c7]
[key.wy = 00c161a79f38ff446051303577e40638fb020329940a63c241bb32c2205eb57b7d]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004f7c54a585a904300d05b53ef3b854e71999a344b89adc0caaa28e254db9bc7c7c161a79f38ff446051303577e40638fb020329940a63c241bb32c2205eb57b7d]
[sha = SHA-512]

# tcId = 425
# extreme value for k
msg = 313233343030
result = valid
sig = 304402206b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296022016a4502e2781e11ac82cbc9d1edd8c981584d13e18411e2f6e0478c34416e3bb

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2964fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5]
[key.wx = 6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296]
[key.wy = 4fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c2964fe342e2fe1a7f9b8ee7eb4a7c0f9e162bce33576b315ececbb6406837bf51f5]
[sha = SHA-512]

# tcId = 426
# testing point duplication
msg = 313233343030
result = invalid
sig = 3044022043f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b0232102810220249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2

# tcId = 427
# testing point duplication
msg = 313233343030
result = invalid
sig = 3045022100bc07ff031506dc74a75086a43252fb43731975a16dca6b025e867412d94222d00220249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296b01cbd1c01e58065711814b583f061e9d431cca994cea1313449bf97c840ae0a]
[key.wx = 6b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296]
[key.wy = 00b01cbd1c01e58065711814b583f061e9d431cca994cea1313449bf97c840ae0a]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200046b17d1f2e12c4247f8bce6e563a440f277037d812deb33a0f4a13945d898c296b01cbd1c01e58065711814b583f061e9d431cca994cea1313449bf97c840ae0a]
[sha = SHA-512]

# tcId = 428
# testing point duplication
msg = 313233343030
result = invalid
sig = 3044022043f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b0232102810220249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2

# tcId = 429
# testing point duplication
msg = 313233343030
result = invalid
sig = 3045022100bc07ff031506dc74a75086a43252fb43731975a16dca6b025e867412d94222d00220249249246db6db6ddb6db6db6db6db6dad4591868595a8ee6bf5f864ff7be0c2

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 0404aaec73635726f213fb8a9e64da3b8632e41495a944d0045b522eba7240fad587d9315798aaa3a5ba01775787ced05eaaf7b4e09fc81d6d1aa546e8365d525d]
[key.wx = 04aaec73635726f213fb8a9e64da3b8632e41495a944d0045b522eba7240fad5]
[key.wy = 0087d9315798aaa3a5ba01775787ced05eaaf7b4e09fc81d6d1aa546e8365d525d]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d0301070342000404aaec73635726f213fb8a9e64da3b8632e41495a944d0045b522eba7240fad587d9315798aaa3a5ba01775787ced05eaaf7b4e09fc81d6d1aa546e8365d525d]
[sha = SHA-512]

# tcId = 430
# pseudorandom signature
msg = 
result = valid
sig = 30440220093f3825c0cf820cced816a3a67446c85606a6d529e43857643fccc11e1f705f0220769782888c63058630f97a5891c8700e82979e4f233586bfc5042fa73cb70a4e

# tcId = 431
# pseudorandom signature
msg = 4d7367
result = valid
sig = 3046022100e8564e3e515a09f9f35258442b99e162d27e10975fcb7963d3c26319dc093f84022100c3af01ed0fd0148749ca323364846c862fc6f4beb682b7ead3b2d89b9da8bad4

# tcId = 432
# pseudorandom signature
msg = 313233343030
result = valid
sig = 304502201412254f8c1dd2742a00ddee5192e7baa288741026871f3057ad9f983b5ab114022100bcdf878fa156f37040922698ad6fb6928601ddc26c40448ea660e67c25eda090

# tcId = 433
# pseudorandom signature
msg = 0000000000000000000000000000000000000000
result = valid
sig = 30450221009e0676048381839bb0a4703a0ae38facfe1e2c61bd25950c896aa975cd6ec86902206ea0cedf96f11fff0e746941183492f4d17272c92449afd20e34041a6894ee82

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 044f337ccfd67726a805e4f1600ae2849df3807eca117380239fbd816900000000ed9dea124cc8c396416411e988c30f427eb504af43a3146cd5df7ea60666d685]
[key.wx = 4f337ccfd67726a805e4f1600ae2849df3807eca117380239fbd816900000000]
[key.wy = 00ed9dea124cc8c396416411e988c30f427eb504af43a3146cd5df7ea60666d685]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200044f337ccfd67726a805e4f1600ae2849df3807eca117380239fbd816900000000ed9dea124cc8c396416411e988c30f427eb504af43a3146cd5df7ea60666d685]
[sha = SHA-512]

# tcId = 434
# x-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 30440220554482404173a5582884b0d168a32ef8033d7eb780936c390e8eedf720c7f56402200a15413f9ed0d454b92ab901119e7251a4d444ba1421ba639fa57e0d8cf6b313

# tcId = 435
# x-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 304502200b1d838dd54a462745e2c8d5f32637f26fb16dde20a385e45f8a20a8a1f8370e022100ae855e0a10ef087075fda0ed84e2bc5786a681172ea9834e53351316df332bbd

# tcId = 436
# x-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 3045022100af89e4f2b03e5d1f0352e258ef71493040c17d70c36cfd044128302df2ed5e4a0220420f04148c3e6f06561bd448362d6c6fa3f9aeeb7e42843b4674e7ddfd0ba901

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f49726500493584fa174d791c72bf2ce3880a8960dd2a7c7a1338a82f85a9e59cdbde80000000]
[key.wx = 3cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f497265004935]
[key.wy = 0084fa174d791c72bf2ce3880a8960dd2a7c7a1338a82f85a9e59cdbde80000000]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f49726500493584fa174d791c72bf2ce3880a8960dd2a7c7a1338a82f85a9e59cdbde80000000]
[sha = SHA-512]

# tcId = 437
# y-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 304402206c1581f1485ccc4e657606fa1a38cf227e3870dc9f41e26b84e28483635e321b02201b3e3c22af23e919b30330f8710f6ef3760c0e2237a9a9f5cf30a1d9f5bbd464

# tcId = 438
# y-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 3045022100dc83bf97ca28db0e04104a16fe3de694311a6cd9f230a300504ae71d8ec755b1022064a83af0ab3e6037003a1f4240dffd8a342afdee50604ed1afa416fd009e4668

# tcId = 439
# y-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 30450220575b70b4375684291b95d81e3c820ed9bde9e5b7343036e4951f3c46894a6d9d022100f10d716efbfeba953701b603fc9ef6ff6e47edef38c9eeef2d55e6486bc4d6e6

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f4972650049357b05e8b186e38d41d31c77f5769f22d58385ecc857d07a561a6324217fffffff]
[key.wx = 3cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f497265004935]
[key.wy = 7b05e8b186e38d41d31c77f5769f22d58385ecc857d07a561a6324217fffffff]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200043cf03d614d8939cfd499a07873fac281618f06b8ff87e8015c3f4972650049357b05e8b186e38d41d31c77f5769f22d58385ecc857d07a561a6324217fffffff]
[sha = SHA-512]

# tcId = 440
# y-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 30450221008d4f113189dfd3d3239e331f76d3fca9cef86fcd5dc9b4ab2ca38aeba56c178b022078389c3cf11dcff6d6c7f5efd277d480060691144b568a6f090c8902557bfc61

# tcId = 441
# y-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 3046022100834d10ec2d2d50eeebfecd6328f03fafbb488fc043c362cbc67880ec0ebd04b302210094c026feaf6e68759146fe5b6fd52eaa3c3c5552d83719d2cb900615e2a634db

# tcId = 442
# y-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 304502206894de495e7bb5566807d475d96a0d414a94f4f02c3ab7c2edc2916deafc1e1f022100a603642c20fabc07182867fcc6923d35be23ad3f97a5f93c6ec5b9cce8239569

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 042829c31faa2e400e344ed94bca3fcd0545956ebcfe8ad0f6dfa5ff8effffffffa01aafaf000e52585855afa7676ade284113099052df57e7eb3bd37ebeb9222e]
[key.wx = 2829c31faa2e400e344ed94bca3fcd0545956ebcfe8ad0f6dfa5ff8effffffff]
[key.wy = 00a01aafaf000e52585855afa7676ade284113099052df57e7eb3bd37ebeb9222e]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d030107034200042829c31faa2e400e344ed94bca3fcd0545956ebcfe8ad0f6dfa5ff8effffffffa01aafaf000e52585855afa7676ade284113099052df57e7eb3bd37ebeb9222e]
[sha = SHA-512]

# tcId = 443
# x-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 3046022100e500c086fedd59e090ce7bfb615751ed9abe4c09b839ee8f05320245b9796f3e022100807b1d0638c86ef6113fff0d63497800e1b848b5a303a54c748e45ca8f35d7d7

# tcId = 444
# x-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 3046022100b922c1abe1a8309c0acf90e586c6de8c33e37057673390a97ff098f71680b32b022100f86d92b051b7923d82555c205e21b54eab869766c716209648c3e6cc2629057d

# tcId = 445
# x-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 3046022100823c37e46c74ec8497d89245fde3bf53ddb462c00d840e983dcb1b72bbf8bf27022100c4552f2425d14f0f0fa988778403d60a58962e7c548715af83b2edabbb24a49f

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04fffffff948081e6a0458dd8f9e738f2665ff9059ad6aac0708318c4ca9a7a4f55a8abcba2dda8474311ee54149b973cae0c0fb89557ad0bf78e6529a1663bd73]
[key.wx = 00fffffff948081e6a0458dd8f9e738f2665ff9059ad6aac0708318c4ca9a7a4f5]
[key.wy = 5a8abcba2dda8474311ee54149b973cae0c0fb89557ad0bf78e6529a1663bd73]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004fffffff948081e6a0458dd8f9e738f2665ff9059ad6aac0708318c4ca9a7a4f55a8abcba2dda8474311ee54149b973cae0c0fb89557ad0bf78e6529a1663bd73]
[sha = SHA-512]

# tcId = 446
# x-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 30450220577a08a95db6dcda9985109942d3786630f640190f920b95bd4d5d84e0f163ef022100d762286e92925973fd38b67ef944a99c0ec5b499b7175cbb4369e053c1fcbb10

# tcId = 447
# x-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 304402207ba458cfe952326922c7aa2854bdc673ce3daaf65d464dfb9f700701503056b102200df8821c92d20546fa741fb426bf56728a53182691964225c9b380b56b22ee6d

# tcId = 448
# x-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 304402205cd60c3b021b4be116f06f1d447f65e458329a8bbae1d9b5977d18cf5618486102204c635cd7aa9aebb5716d5ae09e57f8c481a741a029b40f71ec47344ef883e86e

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 0400000003fa15f963949d5f03a6f5c7f86f9e0015eeb23aebbff1173937ba748e1099872070e8e87c555fa13659cca5d7fadcfcb0023ea889548ca48af2ba7e71]
[key.wx = 03fa15f963949d5f03a6f5c7f86f9e0015eeb23aebbff1173937ba748e]
[key.wy = 1099872070e8e87c555fa13659cca5d7fadcfcb0023ea889548ca48af2ba7e71]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d0301070342000400000003fa15f963949d5f03a6f5c7f86f9e0015eeb23aebbff1173937ba748e1099872070e8e87c555fa13659cca5d7fadcfcb0023ea889548ca48af2ba7e71]
[sha = SHA-512]

# tcId = 449
# x-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 304402204b50e1e8cf830e04c17e7472caf60da8150ffa568e2c64498cc972a379e542e502202e3adaa5afab89cca91693609555f40543578852cde29c21cb037c0c0b78478e

# tcId = 450
# x-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 304402205aea930c7d8fffcd5c6df2c9430ef76f8b5ed58a8b9c95847288abf8f09a1ac202207ddfef7688a6053ce4eeeeefd6f1a9d71381b7548925f6682aa0a9d05cf5a3a3

# tcId = 451
# x-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 304602210098b092c2d14b5b14a23e9368e0ce1be744dfae9f9a5cdaba51e7872099df96f202210090d3e4f87bd7bc94589f8150b6b01045cd8759a00af78b24d7de771887610df5

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015000000001352bb4a0fa2ea4cceb9ab63dd684ade5a1127bcf300a698a7193bc2]
[key.wx = 00bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015]
[key.wy = 1352bb4a0fa2ea4cceb9ab63dd684ade5a1127bcf300a698a7193bc2]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015000000001352bb4a0fa2ea4cceb9ab63dd684ade5a1127bcf300a698a7193bc2]
[sha = SHA-512]

# tcId = 452
# y-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 30460221009e95f2856a9fff9a172b07817c8c60fe185cd3ce9582678f8cc4b02bc444621a022100c54ca51d8117d904f0d3773911cb2792348fae21c2da7dad25f990d122376e4c

# tcId = 453
# y-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 3046022100e77df8f9782696344c33de29ebdc9f8d3fcf463d950cdbe256fd4fc2fd44877e02210087028850c962cf2fb450ffe6b983981e499dc498fbd654fa454c9e07c8cb5ca8

# tcId = 454
# y-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 3045022100bd2dd6f5026d2b5ad7ead74bdf52b8cbcabc08facee0a1c8584658a85ed0c5dc02203e8543e819bdae47d872e29a85ba38addf3eaeaad8786d79c3fb027f6f1ff4bf

[key.curve = secp256r1]
[key.keySize = 256]
[key.type = EcPublicKey]
[key.uncompressed = 04bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015fffffffeecad44b6f05d15b33146549c2297b522a5eed8430cff596758e6c43d]
[key.wx = 00bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015]
[key.wy = 00fffffffeecad44b6f05d15b33146549c2297b522a5eed8430cff596758e6c43d]
[keyDer = 3059301306072a8648ce3d020106082a8648ce3d03010703420004bcbb2914c79f045eaa6ecbbc612816b3be5d2d6796707d8125e9f851c18af015fffffffeecad44b6f05d15b33146549c2297b522a5eed8430cff596758e6c43d]
[sha = SHA-512]

# tcId = 455
# y-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 3045022100bd5c0294acc28c15c5d1ebc7274c9ca21a081c8a67da430a34a7fff1a564fabb02207ec103a2385b4ff38b47d306434e9091de24dc9f1a25967ee06f8a0a53ac0181

# tcId = 456
# y-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 304402203c7dbfb43dd80379ee2c23ad5472873a22c8a0179ac8f381ad9e0f193231dc1f02207cf8e07530ade503b3d43a84b75a2a76fc40763daed4e9734e745c58c9ae72d3

# tcId = 457
# y-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 3045022100b38ca4dac6d949be5e5f969860269f0eedff2eb92f45bfc02470300cc96dd52602201c7b22992bb13749cc0c5bc25330a17446e40db734203f9035172725fc70f863

