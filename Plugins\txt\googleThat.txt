import traceback
from urllib.parse import quote

from ui.bulletin import BulletinHelper
from ui.settings import <PERSON><PERSON>, <PERSON><PERSON>, Divider
from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from android_utils import log as _log

from java.util import Locale

__name__ = "GoogleThat"
__description__ = "Creates search link [.gt]"
__icon__ = "zwyPluginsIcons/2"
__id__ = "googlethat"
__version__ = "1.0.2"
__author__ = "@zwylair"
__min_version__ = "11.9.1"


class Locales:
    default = {"zwylib_was_not_found": "ZwyLib plugin required for this plugin is not found!"}
    ru = {"zwylib_was_not_found": "Требуемый плагин ZwyLib не найден!"}
    uk = {"zwylib_was_not_found": "Не знайдено обов’язковий плагін ZwyLib!"}
    en = default


def localise(key: str) -> str:
    locale = Locale.getDefault().getLanguage()
    locale_dict = getattr(Locales, locale, Locales.default)
    return locale_dict.get(key, key)


def import_zwylib(show_import_error_bulletin = True):
    global zwylib

    try:
        import zwylib
    except ImportError:
        if show_import_error_bulletin:
            show_error_bulletin(localise("zwylib_was_not_found"))


def is_zwylib_present() -> bool:
    return zwylib is not None


def show_error_bulletin(message: str):
    BulletinHelper.show_error(f"{__name__}: " + message)


def log(obj):
    _log(f"{__name__}: " + str(obj))


AUTOUPDATE_CHANNEL_ID = 2521243181
AUTOUPDATE_CHANNEL_USERNAME = "zwyPlugins"
AUTOUPDATE_MESSAGE_ID = 33

SEARCH_ENGINES = {
    "Google": "https://www.google.com/search?q=",
    "Bing": "https://www.bing.com/search?q=",
    "Yahoo": "https://search.yahoo.com/search?p=",
    "DuckDuckGo": "https://duckduckgo.com/?q=",
    "Baidu": "https://www.baidu.com/s?wd=",
    "Yandex": "https://yandex.com/search/?text=",
    "Ecosia": "https://www.ecosia.org/search?q=",
    "Ask": "https://www.ask.com/web?q=",
    "LetMeGoogleThatForYou": "https://letmegooglethat.com/?q="
}
DEFAULT_SEARCH_ENGINE = 0


class GoogleThat(BasePlugin):
    def on_plugin_load(self):
        self.add_on_send_message_hook()

        import_zwylib()
        if is_zwylib_present():
            zwylib.add_autoupdater_task(__id__, AUTOUPDATE_CHANNEL_ID, AUTOUPDATE_CHANNEL_USERNAME, AUTOUPDATE_MESSAGE_ID)

        log("Loaded")

    def on_plugin_unload(self):
        if is_zwylib_present():
            zwylib.remove_autoupdater_task(__id__)
        log("Unloaded")

    def create_settings(self):
        return [
            Header(text="Usage"),
            Divider(text=".gt <query>"),
            Header(text="Settings"),
            Selector(
                key="search_engine",
                text="Search Engine",
                default=DEFAULT_SEARCH_ENGINE,
                icon="msg2_language",
                items=list(SEARCH_ENGINES.keys())
            ),
        ]

    def on_send_message_hook(self, account, params):
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()

        if not params.message.startswith(".gt "):
            return HookResult()

        try:
            search_engine_index = self.get_setting("search_engine", DEFAULT_SEARCH_ENGINE)
            search_engine = list(SEARCH_ENGINES.keys())[search_engine_index]
            search_text = params.message[4:]

            params.message = SEARCH_ENGINES[search_engine] + quote(search_text)
            return HookResult(strategy=HookStrategy.MODIFY_FINAL, params=params)
        except Exception:
            params.message = f"An exception occurred:\n\n{traceback.format_exc()}"
            return HookResult(strategy=HookStrategy.MODIFY_FINAL, params=params)
