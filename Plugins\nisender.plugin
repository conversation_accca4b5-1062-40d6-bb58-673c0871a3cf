from typing import List, Tuple, Optional
import time
from collections import deque

from android.text import TextUtils
from android_utils import log, run_on_ui_thread
from client_utils import (
    send_message, get_last_fragment,
    get_user_config, get_messages_controller, get_messages_storage
)
from java import jint
from java.util import ArrayList
from org.telegram.messenger import R, LocaleController, AndroidUtilities, MessageObject
from org.telegram.tgnet import TLRPC
from org.telegram.ui.ActionBar import AlertDialog
from base_plugin import BasePlugin, HookResult, HookStrategy
from ui.settings import Header, Input

__id__ = "nisender"
__name__ = "<PERSON><PERSON>'s 'Sender"
__description__ = "Send messages from **another devices** with **plugins**.\n**Usage**: .send [message] | .csend [message]\n**Note**: Enable **Keep-Alive Service** and **Background Connection** for better support."
__author__ = "@immat0x1, @nijon4rch"
__min_version__ = "11.9.1"
__icon__ = "nixter4jon/3"
__version__ = "1.0.4"

prefix = ".send "
cprefix = ".csend "

MAX_CACHE_AGE_SECONDS = 60
MAX_CACHE_SIZE = 200


class MessageCache:
    def __init__(self):
        self._deque = deque()
        self._set = set()

    def _cleanup(self):
        current_time = time.time()
        while self._deque and (current_time - self._deque[0][0] > MAX_CACHE_AGE_SECONDS):
            _, key = self._deque.popleft()
            if key in self._set:
                self._set.remove(key)
        while len(self._deque) > MAX_CACHE_SIZE:
            _, key = self._deque.popleft()
            if key in self._set:
                self._set.remove(key)

    def add_and_check(self, key: Tuple[int, int]) -> bool:
        self._cleanup()
        if key in self._set:
            return True
        self._deque.append((time.time(), key))
        self._set.add(key)
        return False


def delete_messages(msg_ids: List[int], chat_id: int, topic_id: int = 0):
    if not msg_ids:
        return
    msgs_list = ArrayList()
    for msg_id in msg_ids:
        msgs_list.add(jint(msg_id))

    messages_ctrl = get_messages_controller()
    if messages_ctrl:
        messages_ctrl.deleteMessages(msgs_list, None, None, chat_id, topic_id, False, 0)


def show_alert(text):
    try:
        AndroidUtilities.addToClipboard(text)
        fragment = get_last_fragment()
        if fragment is None:
            return

        activity = fragment.getParentActivity()
        resource_provider = fragment.getResourceProvider()
        if activity is None or resource_provider is None:
            return

        builder = AlertDialog.Builder(activity, resource_provider)
        builder.setTitle("Plugin Info")
        builder.setMessage(text)
        builder.setPositiveButton(LocaleController.getString("Cancel", R.string.Cancel), None)
        dialog = builder.create()
        fragment.showDialog(dialog)
    except Exception as e:
        log(f"Failed to show alert: {e}")


class ExteraUserbot(BasePlugin):
    def __init__(self):
        self.msg_cache = None
        self.error_message = None

    def on_plugin_load(self):
        self.add_on_send_message_hook(priority = 1)
        self.add_hook("NewMessage", match_substring=True)
        self.add_hook("NewChannelMessage", match_substring=True)
        self.add_hook("ShortMessage", match_substring=True)
        self.add_hook("ShortChatMessage", match_substring=True)
        self.msg_cache = MessageCache()
        log(f"{__name__}: Plugin loaded, cache initialized.")

    def create_settings(self):
        account_id = get_user_config().selectedAccount
        my_user_id = get_user_config().getClientUserId()
        return [
            Header(text="Settings"),
            Input(
                key=f"whitelist{account_id}",
                default="",
                text="Message whitelist",
                subtext="Enter user IDs, separated by commas. These users can send messages from your name."
            ),
            Input(
                key=f"command_whitelist{account_id}",
                default="",
                text="Commands whitelist",
                subtext="Enter user IDs, separated by commas. These users can execute commands from your device."
            ),
            Input(
                key="cmd_prefix",
                default="./",
                text="Command prefixes",
                subtext="Enter command prefixes without separators. Only users from your command whitelist will be able to send messages, starting with these."
            )
        ]

    def on_update_hook(self, update_name, account, update) -> HookResult:
        run_on_ui_thread(lambda: self._process_update(account, update, show_alert))
        return HookResult()

    def on_updates_hook(self, container_name, account, updates_container) -> HookResult:
        run_on_ui_thread(lambda: self._process_update(account, updates_container, show_alert))
        return HookResult()

    def _process_update(self, account: int, update_obj, alert_fn):
        try:
            msg_data = self._extract_message_data(update_obj)
            if not msg_data:
                return

            msg_text, msg_id, chat_id, sender_id, topic_id = msg_data
            
            msg_key = (chat_id, msg_id)
            if self.msg_cache.add_and_check(msg_key):
                log(f"{__name__}: Duplicate message {msg_key}, skipping.")
                return

            if not msg_text or not msg_id:
                return

            my_user_id = get_user_config().getClientUserId()
            
            is_csend = msg_text.startswith(cprefix)
            is_send = msg_text.startswith(prefix)

            if not is_send and not is_csend:
                return
            
            cmd_prefix = cprefix if is_csend else prefix
            cmd_text = msg_text[len(cmd_prefix):]
            
            if not cmd_text:
                return

            if is_csend and sender_id != my_user_id:
                return
            
            if is_send and sender_id != my_user_id:
                if not self._is_user_whitelisted(sender_id, cmd_text):
                    return
            
            reply_to, reply_to_top = self._get_reply_objects(update_obj, chat_id)

            self._send_command_message(cmd_text, chat_id, topic_id, reply_to, reply_to_top)

            if sender_id == my_user_id:
                run_on_ui_thread(lambda: delete_messages([msg_id], chat_id, topic_id), 150)

        except Exception as e:
            log(f"{__name__}: Error processing update: {e}")
            alert_fn(f"Error: {e}")

    def _extract_message_data(self, update_obj):
        topic_id = 0
        my_user_id = get_user_config().getClientUserId()

        is_tlrpc = isinstance(update_obj, TLRPC.Update)

        if is_tlrpc:
            if not hasattr(update_obj, 'message'):
                return None

            if isinstance(update_obj.message, str):
                msg_text = update_obj.message
                msg_id = update_obj.id

                if isinstance(update_obj, TLRPC.TL_updateShortMessage):
                    chat_id = update_obj.user_id
                    sender_id = my_user_id if update_obj.out else update_obj.user_id
                elif isinstance(update_obj, TLRPC.TL_updateShortChatMessage):
                    chat_id = -update_obj.chat_id
                    sender_id = update_obj.from_id
                else:
                    return None

            elif isinstance(update_obj.message, TLRPC.Message):
                msg = update_obj.message
                msg_text = msg.message
                msg_id = msg.id

                peer = msg.peer_id
                if isinstance(peer, TLRPC.TL_peerUser):
                    chat_id = peer.user_id
                elif isinstance(peer, TLRPC.TL_peerChat):
                    chat_id = -peer.chat_id
                elif isinstance(peer, TLRPC.TL_peerChannel):
                    chat_id = -peer.channel_id
                else:
                    return None

                if hasattr(msg, 'out') and msg.out:
                    sender_id = my_user_id
                else:
                    from_peer = msg.from_id
                    if from_peer:
                        if isinstance(from_peer, TLRPC.TL_peerUser):
                            sender_id = from_peer.user_id
                        elif isinstance(from_peer, TLRPC.TL_peerChannel):
                            sender_id = from_peer.channel_id
                        else:
                            if isinstance(peer, TLRPC.TL_peerChannel):
                                sender_id = peer.channel_id
                            else:
                                return None
                    elif msg.post:
                        if isinstance(peer, TLRPC.TL_peerChannel):
                            sender_id = peer.channel_id
                        else:
                            return None
                    else:
                        if isinstance(peer, TLRPC.TL_peerChannel):
                            sender_id = peer.channel_id
                        else:
                            return None

                if msg.reply_to and hasattr(msg.reply_to, 'reply_to_top_id') and msg.reply_to.reply_to_top_id != 0:
                    topic_id = msg.reply_to.reply_to_top_id
                elif hasattr(msg, 'top_id') and msg.top_id != 0:
                    topic_id = msg.top_id
            else:
                return None
        else:
            if not hasattr(update_obj, 'message') or not hasattr(update_obj, 'id'):
                return None

            msg_text = update_obj.message
            msg_id = update_obj.id

            if hasattr(update_obj, 'chat_id') and update_obj.chat_id != 0:
                chat_id = -update_obj.chat_id
                if hasattr(update_obj, 'out') and update_obj.out:
                    sender_id = my_user_id
                elif hasattr(update_obj, 'user_id') and update_obj.user_id != 0:
                    sender_id = update_obj.user_id
                elif hasattr(update_obj, 'from_id') and update_obj.from_id != 0:
                    sender_id = update_obj.from_id
                else:
                    sender_id = -update_obj.chat_id
            elif hasattr(update_obj, 'user_id') and update_obj.user_id != 0:
                chat_id = update_obj.user_id
                if hasattr(update_obj, 'out') and update_obj.out:
                    sender_id = my_user_id
                else:
                    sender_id = update_obj.user_id
            else:
                return None

            if hasattr(update_obj, 'reply_to') and update_obj.reply_to and hasattr(
                    update_obj.reply_to, 'reply_to_top_id') and update_obj.reply_to.reply_to_top_id != 0:
                topic_id = update_obj.reply_to.reply_to_top_id

        return msg_text, msg_id, chat_id, sender_id, topic_id

    def _is_user_whitelisted(self, user_id, command_text):
        account_id = get_user_config().selectedAccount
        message_whitelist_key = f"whitelist{account_id}"
        command_whitelist_key = f"command_whitelist{account_id}"
        my_user_id = get_user_config().getClientUserId()

        message_whitelist_str = self.get_setting(message_whitelist_key, str(my_user_id))
        command_whitelist_str = self.get_setting(command_whitelist_key, str(my_user_id))
        try:
            message_whitelist = {int(uid.strip()) for uid in message_whitelist_str.split(',') if uid.strip()}
            command_whitelist = {int(uid.strip()) for uid in command_whitelist_str.split(',') if uid.strip()}
            if command_text[0] in self.get_setting("cmd_prefix", "./"):
                return user_id in command_whitelist
            else:
                return user_id in message_whitelist
        except ValueError:
            log(f"{__name__}: Invalid whitelist format")
            return False

    def _get_reply_objects(self, update_obj, chat_id: int) -> Tuple[Optional[MessageObject], Optional[MessageObject]]:
        reply_to_msg = None
        reply_to_top_msg = None

        reply_header = None
        is_tlrpc = isinstance(update_obj, TLRPC.Update)

        if is_tlrpc and hasattr(update_obj, 'message') and isinstance(update_obj.message, TLRPC.Message):
            reply_header = update_obj.message.reply_to
        elif not is_tlrpc and hasattr(update_obj, 'reply_to'):
            reply_header = update_obj.reply_to

        if not reply_header:
            return None, None

        account_id = get_user_config().selectedAccount

        if hasattr(reply_header, 'reply_to_msg_id') and reply_header.reply_to_msg_id != 0:
            reply_msg_raw = get_messages_storage().getMessage(chat_id, reply_header.reply_to_msg_id)
            if reply_msg_raw:
                reply_to_msg = MessageObject(account_id, reply_msg_raw, False, False)

        if hasattr(reply_header, 'reply_to_top_id') and reply_header.reply_to_top_id != 0:
            topic_msg_raw = get_messages_storage().getMessage(chat_id, reply_header.reply_to_top_id)
            if topic_msg_raw:
                reply_to_top_msg = MessageObject(account_id, topic_msg_raw, False, False)

        return reply_to_msg, reply_to_top_msg

    def _send_command_message(self, cmd_text: str, chat_id: int, topic_id: int, reply_to: Optional[MessageObject], reply_to_top: Optional[MessageObject]):
        params = {
            "message": cmd_text,
            "peer": chat_id,
        }

        log(f"Preparing to send to topic_id: {topic_id}, with reply_to: {bool(reply_to)}, reply_to_top: {bool(reply_to_top)}")

        # При наличии topic_id всегда используем его напрямую
        if topic_id:
            params["topicId"] = topic_id
            # Если нам нужно ответить на конкретное сообщение внутри топика
            if reply_to:
                params["replyToMsg"] = reply_to
        # Если нет topic_id, но есть на что ответить
        elif reply_to:
            params["replyToMsg"] = reply_to
            # В обычных чатах reply_to_top может быть использован по умолчанию
            if reply_to_top:
                params["replyToTopMsg"] = reply_to_top

        send_message(params)