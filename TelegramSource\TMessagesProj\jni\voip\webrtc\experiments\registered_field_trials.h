// This file was automatically generated. Do not edit.

#ifndef GEN_REGISTERED_FIELD_TRIALS_H_
#define GEN_REGISTERED_FIELD_TRIALS_H_

#include "absl/strings/string_view.h"

namespace webrtc {

inline constexpr absl::string_view kRegisteredFieldTrials[] = {
    "UseTwccPlrForAna",
    "WebRTC-AV1-OverridePriorityBitrate",
    "WebRTC-AddNetworkCostToVpn",
    "WebRTC-AddPacingToCongestionWindowPushback",
    "WebRTC-AdjustOpusBandwidth",
    "WebRTC-Aec3AecStateFullResetKillSwitch",
    "WebRTC-Aec3AecStateSubtractorAnalyzerResetKillSwitch",
    "WebRTC-Aec3AntiHowlingMinimizationKillSwitch",
    "WebRTC-Aec3ClampInstQualityToOneKillSwitch",
    "WebRTC-Aec3ClampInstQualityToZeroKillSwitch",
    "WebRTC-Aec3CoarseFilterResetHangoverKillSwitch",
    "WebRTC-Aec3ConservativeTailFreqResponse",
    "WebRTC-Aec3DeactivateInitialStateResetKillSwitch",
    "WebRTC-Aec3DelayEstimateSmoothingDelayFoundOverride",
    "WebRTC-Aec3DelayEstimateSmoothingOverride",
    "WebRTC-Aec3DelayEstimatorDetectPreEcho",
    "WebRTC-Aec3EchoSaturationDetectionKillSwitch",
    "WebRTC-Aec3EnforceCaptureDelayEstimationDownmixing",
    "WebRTC-Aec3EnforceCaptureDelayEstimationLeftRightPrioritization",
    "WebRTC-Aec3EnforceConservativeHfSuppression",
    "WebRTC-Aec3EnforceLowActiveRenderLimit",
    "WebRTC-Aec3EnforceMoreTransparentNearendSuppressorHfTuning",
    "WebRTC-Aec3EnforceMoreTransparentNearendSuppressorTuning",
    "WebRTC-Aec3EnforceMoreTransparentNormalSuppressorHfTuning",
    "WebRTC-Aec3EnforceMoreTransparentNormalSuppressorTuning",
    "WebRTC-Aec3EnforceRapidlyAdjustingNearendSuppressorTunings",
    "WebRTC-Aec3EnforceRapidlyAdjustingNormalSuppressorTunings",
    "WebRTC-Aec3EnforceRenderDelayEstimationDownmixing",
    "WebRTC-Aec3EnforceSlowlyAdjustingNearendSuppressorTunings",
    "WebRTC-Aec3EnforceSlowlyAdjustingNormalSuppressorTunings",
    "WebRTC-Aec3EnforceStationarityProperties",
    "WebRTC-Aec3EnforceStationarityPropertiesAtInit",
    "WebRTC-Aec3EnforceVeryLowActiveRenderLimit",
    "WebRTC-Aec3HighPassFilterEchoReference",
    "WebRTC-Aec3MinErleDuringOnsetsKillSwitch",
    "WebRTC-Aec3NonlinearModeReverbKillSwitch",
    "WebRTC-Aec3OnsetDetectionKillSwitch",
    "WebRTC-Aec3PenalyzeHighDelaysInitialPhase",
    "WebRTC-Aec3PreEchoConfiguration",
    "WebRTC-Aec3RenderDelayEstimationLeftRightPrioritizationKillSwitch",
    "WebRTC-Aec3SensitiveDominantNearendActivation",
    "WebRTC-Aec3SetupSpecificDefaultConfigDefaultsKillSwitch",
    "WebRTC-Aec3ShortHeadroomKillSwitch",
    "WebRTC-Aec3StereoContentDetectionKillSwitch",
    "WebRTC-Aec3SuppressorAntiHowlingGainOverride",
    "WebRTC-Aec3SuppressorDominantNearendEnrExitThresholdOverride",
    "WebRTC-Aec3SuppressorDominantNearendEnrThresholdOverride",
    "WebRTC-Aec3SuppressorDominantNearendHoldDurationOverride",
    "WebRTC-Aec3SuppressorDominantNearendSnrThresholdOverride",
    "WebRTC-Aec3SuppressorDominantNearendTriggerThresholdOverride",
    "WebRTC-Aec3SuppressorNearendHfMaskSuppressOverride",
    "WebRTC-Aec3SuppressorNearendHfMaskTransparentOverride",
    "WebRTC-Aec3SuppressorNearendLfMaskSuppressOverride",
    "WebRTC-Aec3SuppressorNearendLfMaskTransparentOverride",
    "WebRTC-Aec3SuppressorNearendMaxDecFactorLfOverride",
    "WebRTC-Aec3SuppressorNearendMaxIncFactorOverride",
    "WebRTC-Aec3SuppressorNormalHfMaskSuppressOverride",
    "WebRTC-Aec3SuppressorNormalHfMaskTransparentOverride",
    "WebRTC-Aec3SuppressorNormalLfMaskSuppressOverride",
    "WebRTC-Aec3SuppressorNormalLfMaskTransparentOverride",
    "WebRTC-Aec3SuppressorNormalMaxDecFactorLfOverride",
    "WebRTC-Aec3SuppressorNormalMaxIncFactorOverride",
    "WebRTC-Aec3SuppressorTuningOverride",
    "WebRTC-Aec3TransparentAntiHowlingGain",
    "WebRTC-Aec3TransparentModeHmm",
    "WebRTC-Aec3TransparentModeKillSwitch",
    "WebRTC-Aec3Use1Dot2SecondsInitialStateDuration",
    "WebRTC-Aec3Use1Dot6SecondsInitialStateDuration",
    "WebRTC-Aec3Use2Dot0SecondsInitialStateDuration",
    "WebRTC-Aec3UseDot1SecondsInitialStateDuration",
    "WebRTC-Aec3UseDot2SecondsInitialStateDuration",
    "WebRTC-Aec3UseDot3SecondsInitialStateDuration",
    "WebRTC-Aec3UseDot6SecondsInitialStateDuration",
    "WebRTC-Aec3UseDot9SecondsInitialStateDuration",
    "WebRTC-Aec3UseErleOnsetCompensationInDominantNearend",
    "WebRTC-Aec3UseLowEarlyReflectionsDefaultGain",
    "WebRTC-Aec3UseLowLateReflectionsDefaultGain",
    "WebRTC-Aec3UseNearendReverbLen",
    "WebRTC-Aec3UseShortConfigChangeDuration",
    "WebRTC-Aec3UseZeroInitialStateDuration",
    "WebRTC-Aec3VerySensitiveDominantNearendActivation",
    "WebRTC-Agc2SimdAvx2KillSwitch",
    "WebRTC-Agc2SimdNeonKillSwitch",
    "WebRTC-Agc2SimdSse2KillSwitch",
    "WebRTC-AllowMACBasedIPv6",
    "WebRTC-AlrDetectorParameters",
    "WebRTC-AndroidNetworkMonitor-IsAdapterAvailable",
    "WebRTC-ApmExperimentalMultiChannelCaptureKillSwitch",
    "WebRTC-ApmExperimentalMultiChannelRenderKillSwitch",
    "WebRTC-Audio-2ndAgcMinMicLevelExperiment",
    "WebRTC-Audio-ABWENoTWCC",
    "WebRTC-Audio-AdaptivePtime",
    "WebRTC-Audio-Allocation",
    "WebRTC-Audio-AlrProbing",
    "WebRTC-Audio-FecAdaptation",
    "WebRTC-Audio-GainController2",
    "WebRTC-Audio-LegacyOverhead",
    "WebRTC-Audio-MinimizeResamplingOnMobile",
    "WebRTC-Audio-NetEqDecisionLogicConfig",
    "WebRTC-Audio-NetEqDelayManagerConfig",
    "WebRTC-Audio-NetEqFecDelayAdaptation",
    "WebRTC-Audio-NetEqNackTrackerConfig",
    "WebRTC-Audio-NetEqSmartFlushing",
    "WebRTC-Audio-OpusAvoidNoisePumpingDuringDtx",
    "WebRTC-Audio-OpusBitrateMultipliers",
    "WebRTC-Audio-OpusGeneratePlc",
    "WebRTC-Audio-OpusPlcUsePrevDecodedSamples",
    "WebRTC-Audio-OpusSetSignalVoiceWithDtx",
    "WebRTC-Audio-PriorityBitrate",
    "WebRTC-Audio-Red-For-Opus",
    "WebRTC-Audio-StableTargetAdaptation",
    "WebRTC-Audio-iOS-Holding",
    "WebRTC-AudioDevicePlayoutBufferSizeFactor",
    "WebRTC-AutomaticAnimationDetectionScreenshare",
    "WebRTC-Av1-GetEncoderInfoOverride",
    "WebRTC-Avx2SupportKillSwitch",
    "WebRTC-BindUsingInterfaceName",
    "WebRTC-BoostedScreenshareQp",
    "WebRTC-BurstyPacer",
    "WebRTC-Bwe-AllocationProbing",
    "WebRTC-Bwe-AlrProbing",
    "WebRTC-Bwe-EstimateBoundedIncrease",
    "WebRTC-Bwe-ExponentialProbing",
    "WebRTC-Bwe-IgnoreProbesLowerThanNetworkStateEstimate",
    "WebRTC-Bwe-InitialProbing",
    "WebRTC-Bwe-InjectedCongestionController",
    "WebRTC-Bwe-LimitProbesLowerThanThroughputEstimate",
    "WebRTC-Bwe-LinkCapacity",
    "WebRTC-Bwe-LossBasedBweV2",
    "WebRTC-Bwe-LossBasedControl",
    "WebRTC-Bwe-MaxRttLimit",
    "WebRTC-Bwe-MinAllocAsLowerBound",
    "WebRTC-Bwe-NetworkRouteConstraints",
    "WebRTC-Bwe-NoFeedbackReset",
    "WebRTC-Bwe-PaceAtMaxOfBweAndLowerLinkCapacity",
    "WebRTC-Bwe-ProbingBehavior",
    "WebRTC-Bwe-ProbingConfiguration",
    "WebRTC-Bwe-ReceiveTimeFix",
    "WebRTC-Bwe-ReceiverLimitCapsOnly",
    "WebRTC-Bwe-RobustThroughputEstimatorSettings",
    "WebRTC-Bwe-SafeResetOnRouteChange",
    "WebRTC-Bwe-SeparateAudioPackets",
    "WebRTC-Bwe-SubtractAdditionalBackoffTerm",
    "WebRTC-Bwe-TrendlineEstimatorSettings",
    "WebRTC-BweBackOffFactor",
    "WebRTC-BweLossExperiment",
    "WebRTC-BweRapidRecoveryExperiment",
    "WebRTC-BweThroughputWindowConfig",
    "WebRTC-BweWindowSizeInPackets",
    "WebRTC-CongestionWindow",
    "WebRTC-CpuLoadEstimator",
    "WebRTC-Debugging-RtpDump",
    "WebRTC-DecoderDataDumpDirectory",
    "WebRTC-DefaultBitrateLimitsKillSwitch",
    "WebRTC-DependencyDescriptorAdvertised",
    "WebRTC-DisableRtxRateLimiter",
    "WebRTC-DisableUlpFecExperiment",
    "WebRTC-DontIncreaseDelayBasedBweInAlr",
    "WebRTC-DscpFieldTrial",
    "WebRTC-EncoderDataDumpDirectory",
    "WebRTC-ExtraICEPing",
    "WebRTC-FakeNetworkReceiveConfig",
    "WebRTC-FakeNetworkSendConfig",
    "WebRTC-FilterAbsSendTimeExtension",
    "WebRTC-FindNetworkHandleWithoutIpv6TemporaryPart",
    "WebRTC-FlexFEC-03",
    "WebRTC-FlexFEC-03-Advertised",
    "WebRTC-ForcePlayoutDelay",
    "WebRTC-ForceSendPlayoutDelay",
    "WebRTC-ForceSimulatedOveruseIntervalMs",
    "WebRTC-FrameDropper",
    "WebRTC-FullBandHpfKillSwitch",
    "WebRTC-GenericCodecDependencyDescriptor",
    "WebRTC-GenericDescriptorAdvertised",
    "WebRTC-GenericDescriptorAuth",
    "WebRTC-GenericPictureId",
    "WebRTC-GetEncoderInfoOverride",
    "WebRTC-H264HighProfile",
    "WebRTC-IPv6Default",
    "WebRTC-IPv6NetworkResolutionFixes",
    "WebRTC-IceControllerFieldTrials",
    "WebRTC-IceFieldTrials",
    "WebRTC-IncomingTimestampOnMarkerBitOnly",
    "WebRTC-IncreaseIceCandidatePriorityHostSrflx",
    "WebRTC-JitterEstimatorConfig",
    "WebRTC-KeyframeInterval",
    "WebRTC-LegacyFrameIdJumpBehavior",
    "WebRTC-LegacySimulcastLayerLimit",
    "WebRTC-LegacyTlsProtocols",
    "WebRTC-LibaomAv1Encoder-DisableFrameDropping",
    "WebRTC-LibaomAv1Encoder-MaxConsecFrameDrop",
    "WebRTC-LibvpxVp9Encoder-SvcFrameDropConfig",
    "WebRTC-LowresSimulcastBitrateInterpolation",
    "WebRTC-MutedStateKillSwitch",
    "WebRTC-Network-UseNWPathMonitor",
    "WebRTC-NetworkMonitorAutoDetect",
    "WebRTC-NormalizeSimulcastResolution",
    "WebRTC-Pacer-BlockAudio",
    "WebRTC-Pacer-DrainQueue",
    "WebRTC-Pacer-FastRetransmissions",
    "WebRTC-Pacer-IgnoreTransportOverhead",
    "WebRTC-Pacer-KeyframeFlushing",
    "WebRTC-Pacer-PadInSilence",
    "WebRTC-PacketBufferMaxSize",
    "WebRTC-PaddingMode-RecentLargePacket",
    "WebRTC-PcFactoryDefaultBitrates",
    "WebRTC-PermuteTlsClientHello",
    "WebRTC-PiggybackIceCheckAcknowledgement",
    "WebRTC-PixelLimitResource",
    "WebRTC-PreventSsrcGroupsWithUnexpectedSize",
    "WebRTC-ProbingScreenshareBwe",
    "WebRTC-ProtectionOverheadRateThreshold",
    "WebRTC-QpParsingKillSwitch",
    "WebRTC-ReceiveBufferSize",
    "WebRTC-RtcEventLogEncodeDependencyDescriptor",
    "WebRTC-RtcEventLogEncodeNetEqSetMinimumDelayKillSwitch",
    "WebRTC-RtcEventLogKillSwitch",
    "WebRTC-RtcEventLogNewFormat",
    "WebRTC-RtcpLossNotification",
    "WebRTC-RttMult",
    "WebRTC-SCM-Timestamp",
    "WebRTC-SendBufferSizeBytes",
    "WebRTC-SendNackDelayMs",
    "WebRTC-SendPacketsOnWorkerThread",
    "WebRTC-SetSocketReceiveBuffer",
    "WebRTC-SignalNetworkPreferenceChange",
    "WebRTC-SimulcastEncoderAdapter-GetEncoderInfoOverride",
    "WebRTC-SimulcastLayerLimitRoundUp",
    "WebRTC-SpsPpsIdrIsH264Keyframe",
    "WebRTC-SrtpRemoveReceiveStream",
    "WebRTC-StableTargetRate",
    "WebRTC-StrictPacingAndProbing",
    "WebRTC-StunInterPacketDelay",
    "WebRTC-SurfaceCellularTypes",
    "WebRTC-SwitchEncoderOnInitializationFailures",
    "WebRTC-Target-Bitrate-Rtcp",
    "WebRTC-TaskQueue-ReplaceLibeventWithStdlib",
    "WebRTC-TransientSuppressorForcedOff",
    "WebRTC-UseBaseHeavyVP8TL3RateAllocation",
    "WebRTC-UseDifferentiatedCellularCosts",
    "WebRTC-UseShortVP8TL2Pattern",
    "WebRTC-UseShortVP8TL3Pattern",
    "WebRTC-UseStandardBytesStats",
    "WebRTC-UseTurnServerAsStunServer",
    "WebRTC-VP8-CpuSpeed-Arm",
    "WebRTC-VP8-ForcePartitionResilience",
    "WebRTC-VP8-Forced-Fallback-Encoder-v2",
    "WebRTC-VP8-GetEncoderInfoOverride",
    "WebRTC-VP8-MaxFrameInterval",
    "WebRTC-VP8-Postproc-Config",
    "WebRTC-VP8-Postproc-Config-Arm",
    "WebRTC-VP8ConferenceTemporalLayers",
    "WebRTC-VP8IosMaxNumberOfThread",
    "WebRTC-VP8VariableFramerateScreenshare",
    "WebRTC-VP9-GetEncoderInfoOverride",
    "WebRTC-VP9-LowTierOptimizations",
    "WebRTC-VP9-PerformanceFlags",
    "WebRTC-VP9QualityScaler",
    "WebRTC-VP9VariableFramerateScreenshare",
    "WebRTC-Video-BalancedDegradation",
    "WebRTC-Video-BalancedDegradationSettings",
    "WebRTC-Video-BandwidthQualityScalerSettings",
    "WebRTC-Video-DisableAutomaticResize",
    "WebRTC-Video-DiscardPacketsWithUnknownSsrc",
    "WebRTC-Video-EnableRetransmitAllLayers",
    "WebRTC-Video-EncoderFallbackSettings",
    "WebRTC-Video-ForcedSwDecoderFallback",
    "WebRTC-Video-InitialDecoderResolution",
    "WebRTC-Video-MinVideoBitrate",
    "WebRTC-Video-Pacing",
    "WebRTC-Video-PreferTemporalSupportOnBaseLayer",
    "WebRTC-Video-QualityRampupSettings",
    "WebRTC-Video-QualityScalerSettings",
    "WebRTC-Video-QualityScaling",
    "WebRTC-Video-RequestedResolutionOverrideOutputFormatRequest",
    "WebRTC-Video-UseFrameRateForOverhead",
    "WebRTC-Video-VariableStartScaleFactor",
    "WebRTC-VideoEncoderSettings",
    "WebRTC-VideoFrameTrackingIdAdvertised",
    "WebRTC-VideoLayersAllocationAdvertised",
    "WebRTC-VideoRateControl",
    "WebRTC-VoIPChannelRemixingAdjustmentKillSwitch",
    "WebRTC-Vp9ExternalRefCtrl",
    "WebRTC-Vp9InterLayerPred",
    "WebRTC-Vp9IssueKeyFrameOnLayerDeactivation",
    "WebRTC-ZeroHertzQueueOverload",
    "WebRTC-ZeroHertzScreenshare",
    "WebRTC-ZeroPlayoutDelay",
};

}  // namespace webrtc

#endif  // GEN_REGISTERED_FIELD_TRIALS_H_
