# LeakOSINT Search Plugin для exteraGram

## Описание
Плагин для поиска информации по базам данных утечек через API LeakOSINT. Позволяет искать данные по email, именам, телефонам, никнеймам и другой персональной информации.

## Возможности
- 🔍 Поиск по множественным базам данных утечек
- 🌐 Поддержка русского и английского языков
- ⚙️ Гибкие настройки поиска и отображения
- 🔒 Безопасная валидация запросов
- 📱 Асинхронная обработка с индикатором загрузки
- 🎨 Красивое форматирование результатов
- 🧪 Встроенное тестирование API токена

## Установка и настройка

### 1. Получение API токена
1. Найдите бота @LeakOsintBot в Telegram
2. Отправьте команду `/api`
3. Скопируйте полученный токен

### 2. Настройка плагина
1. Перейдите в настройки плагина LeakOSINT
2. Вставьте токен в поле "API Токен"
3. Нажмите "Тестировать токен" для проверки
4. Настройте другие параметры по желанию

## Использование

### Основная команда
```
.leak [запрос]
```

### Примеры поиска
```
.leak <EMAIL>          # Поиск по email
.leak Иван Петров               # Поиск по имени
.leak +79123456789              # Поиск по телефону
.leak username123               # Поиск по никнейму
.leak ***********               # Поиск по IP
.leak example.com               # Поиск по домену
```

## Настройки

### API Настройки
- **API Токен**: Токен для доступа к LeakOSINT API
- **Тестировать токен**: Проверка работоспособности токена

### Настройки поиска
- **Лимит поиска**: Количество результатов (100-10000)
- **Язык результатов**: Русский или английский
- **Таймаут запроса**: Время ожидания ответа (15-120 сек)

### Отображение
- **Показывать промо**: Ссылка на канал разработчика
- **Компактные результаты**: Сокращенный формат вывода
- **Показывать описание баз**: Информация о базах данных

## Поддерживаемые типы данных
- 📧 Email адреса
- 👤 Имена и фамилии
- 📱 Номера телефонов
- 🏷️ Никнеймы и логины
- 🌐 IP адреса
- 🔗 Домены и URL

## Безопасность
- Валидация всех входящих запросов
- Защита от инъекций и вредоносного кода
- Ограничение длины запросов
- Безопасная обработка ошибок

## Обработка ошибок
Плагин корректно обрабатывает:
- Неверные или истёкшие токены
- Сетевые ошибки и таймауты
- Превышение лимитов API
- Некорректные запросы
- Ошибки форматирования

## Технические детали

### Структура плагина
- `LeakOSINTAPI`: Клиент для работы с API
- `ResultFormatter`: Форматирование результатов
- `LeakOSINTPlugin`: Основной класс плагина

### API Endpoint
- URL: https://leakosintapi.com/
- Метод: POST
- Формат: JSON

### Зависимости
- requests: HTTP клиент
- json: Обработка JSON
- threading: Многопоточность
- typing: Типизация

## Примеры результатов

### Успешный поиск
```
🔍 Поиск: <EMAIL>
📊 Найдено: 3 базы данных, 15 записей

📊 База данных: Database_2019
ℹ️ Описание: Email leak from 2019
📋 Записей в базе: 5

Запись 1:
• email: <EMAIL>
• password: 123456
• username: user123
• name: John Doe
```

### Компактный формат
```
🔍 <EMAIL> → 3 БД, 15 записей

📊 Database_2019: 5 записей
  → <EMAIL> | 123456 | user123
  → +4 записей
```

## Лицензия и авторство
- **Автор**: @mihailkotovski & @mishabotov
- **Версия**: 1.0.0
- **Канал**: @mishabotov

## Поддержка
Для получения поддержки обращайтесь:
- Канал: @mishabotov
- Автор: @mihailkotovski

## Changelog

### v1.0.0
- Первый релиз
- Базовый функционал поиска
- Настройки и валидация
- Асинхронная обработка
- Форматирование результатов
- Обработка ошибок
