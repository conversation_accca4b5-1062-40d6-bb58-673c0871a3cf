from android_utils import log

from java.lang import Boolean
from org.telegram.ui.Components.voip import VoIP<PERSON><PERSON>per
from org.telegram.tgnet import TLR<PERSON>
from android.app import Activity
from org.telegram.messenger import AccountInstance

__name__ = "No Call Confirmation"
__description__ = "Убирает надоедливое окно подтверждение звонка \n\nRemoves annoying call confirmation popup."
__icon__ = "Patrick_Bateman/48"
__version__ = "1.0.0"
__id__ = "NoCallConfirmation"
__author__ = "@bleizix"
__min_version__ = "11.12.0"


class StartCallHook:
    def before_hooked_method(self, param):
        param.args[6] = True


class NoCallConfirmation(BasePlugin):
    def on_plugin_load(self):
        try:
            self._on_plugin_load()
        except Exception as e:
            log(str(e))

    def _on_plugin_load(self):
        self.hook_method(VoIPHelper.getClass().getDeclaredMethod("startCall", TLRPC.User, Boolean.TYPE, Boolean.TYPE, Activity, TLRPC.UserFull, AccountInstance, Boolean.TYPE), StartCallHook())
