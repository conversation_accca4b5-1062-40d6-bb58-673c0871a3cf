# Imported from Wych<PERSON><PERSON><PERSON>'s ecdsa_secp224r1_sha256_test.json.
# This file is generated by convert_wycheproof.go. Do not edit by hand.
#
# Algorithm: ECDSA
# Generator version: 0.8r12

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5]
[key.wx = 00eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7]
[key.wy = 00eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5]
[sha = SHA-256]

# tcId = 1
# signature malleability
msg = 313233343030
result = valid
sig = 303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021d009e82950ebe102f37ff3645cc7d3c1bab8864e5e03a5011eeba8150bc

# tcId = 2
# valid
msg = 313233343030
result = valid
sig = 303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 3
# long form encoding of length of sequence
msg = 313233343030
result = invalid
sig = 30813c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981
flags = BER

# tcId = 4
# length of sequence contains leading 0
msg = 313233343030
result = invalid
sig = 3082003c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981
flags = BER

# tcId = 5
# wrong length of sequence
msg = 313233343030
result = invalid
sig = 303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 6
# wrong length of sequence
msg = 313233343030
result = invalid
sig = 303b021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 7
# uint32 overflow in length of sequence
msg = 313233343030
result = invalid
sig = 3085010000003c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 8
# uint64 overflow in length of sequence
msg = 313233343030
result = invalid
sig = 308901000000000000003c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 9
# length of sequence = 2**31 - 1
msg = 313233343030
result = invalid
sig = 30847fffffff021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 10
# length of sequence = 2**32 - 1
msg = 313233343030
result = invalid
sig = 3084ffffffff021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 11
# length of sequence = 2**40 - 1
msg = 313233343030
result = invalid
sig = 3085ffffffffff021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 12
# length of sequence = 2**64 - 1
msg = 313233343030
result = invalid
sig = 3088ffffffffffffffff021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 13
# incorrect length of sequence
msg = 313233343030
result = invalid
sig = 30ff021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 14
# indefinite length without termination
msg = 313233343030
result = invalid
sig = 3080021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 15
# indefinite length without termination
msg = 313233343030
result = invalid
sig = 303c02803ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 16
# indefinite length without termination
msg = 313233343030
result = invalid
sig = 303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040280617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 17
# removing sequence
msg = 313233343030
result = invalid
sig = 

# tcId = 18
# lonely sequence tag
msg = 313233343030
result = invalid
sig = 30

# tcId = 19
# appending 0's to sequence
msg = 313233343030
result = invalid
sig = 303e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000

# tcId = 20
# prepending 0's to sequence
msg = 313233343030
result = invalid
sig = 303e0000021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 21
# appending unused 0's to sequence
msg = 313233343030
result = invalid
sig = 303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000

# tcId = 22
# appending null value to sequence
msg = 313233343030
result = invalid
sig = 303e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810500

# tcId = 23
# including garbage
msg = 313233343030
result = invalid
sig = 3041498177303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 24
# including garbage
msg = 313233343030
result = invalid
sig = 30402500303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 25
# including garbage
msg = 313233343030
result = invalid
sig = 303e303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810004deadbeef

# tcId = 26
# including garbage
msg = 313233343030
result = invalid
sig = 30412221498177021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 27
# including garbage
msg = 313233343030
result = invalid
sig = 304022202500021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 28
# including garbage
msg = 313233343030
result = invalid
sig = 3044221e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040004deadbeef021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 29
# including garbage
msg = 313233343030
result = invalid
sig = 3041021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a042221498177021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 30
# including garbage
msg = 313233343030
result = invalid
sig = 3040021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a0422202500021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 31
# including garbage
msg = 313233343030
result = invalid
sig = 3044021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04221e021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810004deadbeef

# tcId = 32
# including undefined tags
msg = 313233343030
result = invalid
sig = 3044aa00bb00cd00303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 33
# including undefined tags
msg = 313233343030
result = invalid
sig = 3042aa02aabb303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 34
# including undefined tags
msg = 313233343030
result = invalid
sig = 30442224aa00bb00cd00021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 35
# including undefined tags
msg = 313233343030
result = invalid
sig = 30422222aa02aabb021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 36
# including undefined tags
msg = 313233343030
result = invalid
sig = 3044021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a042224aa00bb00cd00021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 37
# including undefined tags
msg = 313233343030
result = invalid
sig = 3042021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a042222aa02aabb021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 38
# truncated length of sequence
msg = 313233343030
result = invalid
sig = 3081

# tcId = 39
# using composition with indefinite length
msg = 313233343030
result = invalid
sig = 3080303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000

# tcId = 40
# using composition with indefinite length
msg = 313233343030
result = invalid
sig = 30402280021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040000021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 41
# using composition with indefinite length
msg = 313233343030
result = invalid
sig = 3040021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a042280021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000

# tcId = 42
# using composition with wrong tag
msg = 313233343030
result = invalid
sig = 3080313c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000

# tcId = 43
# using composition with wrong tag
msg = 313233343030
result = invalid
sig = 30402280031c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040000021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 44
# using composition with wrong tag
msg = 313233343030
result = invalid
sig = 3040021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a042280031c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000

# tcId = 45
# Replacing sequence with NULL
msg = 313233343030
result = invalid
sig = 0500

# tcId = 46
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = 2e3c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 47
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = 2f3c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 48
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = 313c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 49
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = 323c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 50
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = ff3c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 51
# dropping value of sequence
msg = 313233343030
result = invalid
sig = 3000

# tcId = 52
# using composition for sequence
msg = 313233343030
result = invalid
sig = 3040300102303b1c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 53
# truncated sequence
msg = 313233343030
result = invalid
sig = 303b021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9

# tcId = 54
# truncated sequence
msg = 313233343030
result = invalid
sig = 303b1c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 55
# indefinite length
msg = 313233343030
result = invalid
sig = 3080021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000
flags = BER

# tcId = 56
# indefinite length with truncated delimiter
msg = 313233343030
result = invalid
sig = 3080021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad98100

# tcId = 57
# indefinite length with additional element
msg = 313233343030
result = invalid
sig = 3080021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad98105000000

# tcId = 58
# indefinite length with truncated element
msg = 313233343030
result = invalid
sig = 3080021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981060811220000

# tcId = 59
# indefinite length with garbage
msg = 313233343030
result = invalid
sig = 3080021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000fe02beef

# tcId = 60
# indefinite length with nonempty EOC
msg = 313233343030
result = invalid
sig = 3080021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810002beef

# tcId = 61
# prepend empty sequence
msg = 313233343030
result = invalid
sig = 303e3000021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 62
# append empty sequence
msg = 313233343030
result = invalid
sig = 303e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9813000

# tcId = 63
# append garbage with high tag number
msg = 313233343030
result = invalid
sig = 303f021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981bf7f00

# tcId = 64
# sequence of sequence
msg = 313233343030
result = invalid
sig = 303e303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 65
# truncated sequence: removed last 1 elements
msg = 313233343030
result = invalid
sig = 301e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04

# tcId = 66
# repeating element in sequence
msg = 313233343030
result = invalid
sig = 305a021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 67
# long form encoding of length of integer
msg = 313233343030
result = invalid
sig = 303d02811c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981
flags = BER

# tcId = 68
# long form encoding of length of integer
msg = 313233343030
result = invalid
sig = 303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a0402811c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981
flags = BER

# tcId = 69
# length of integer contains leading 0
msg = 313233343030
result = invalid
sig = 303e0282001c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981
flags = BER

# tcId = 70
# length of integer contains leading 0
msg = 313233343030
result = invalid
sig = 303e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040282001c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981
flags = BER

# tcId = 71
# wrong length of integer
msg = 313233343030
result = invalid
sig = 303c021d3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 72
# wrong length of integer
msg = 313233343030
result = invalid
sig = 303c021b3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 73
# wrong length of integer
msg = 313233343030
result = invalid
sig = 303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021d617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 74
# wrong length of integer
msg = 313233343030
result = invalid
sig = 303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021b617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 75
# uint32 overflow in length of integer
msg = 313233343030
result = invalid
sig = 30410285010000001c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 76
# uint32 overflow in length of integer
msg = 313233343030
result = invalid
sig = 3041021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040285010000001c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 77
# uint64 overflow in length of integer
msg = 313233343030
result = invalid
sig = 3045028901000000000000001c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 78
# uint64 overflow in length of integer
msg = 313233343030
result = invalid
sig = 3045021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04028901000000000000001c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 79
# length of integer = 2**31 - 1
msg = 313233343030
result = invalid
sig = 304002847fffffff3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 80
# length of integer = 2**31 - 1
msg = 313233343030
result = invalid
sig = 3040021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a0402847fffffff617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 81
# length of integer = 2**32 - 1
msg = 313233343030
result = invalid
sig = 30400284ffffffff3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 82
# length of integer = 2**32 - 1
msg = 313233343030
result = invalid
sig = 3040021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040284ffffffff617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 83
# length of integer = 2**40 - 1
msg = 313233343030
result = invalid
sig = 30410285ffffffffff3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 84
# length of integer = 2**40 - 1
msg = 313233343030
result = invalid
sig = 3041021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040285ffffffffff617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 85
# length of integer = 2**64 - 1
msg = 313233343030
result = invalid
sig = 30440288ffffffffffffffff3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 86
# length of integer = 2**64 - 1
msg = 313233343030
result = invalid
sig = 3044021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040288ffffffffffffffff617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 87
# incorrect length of integer
msg = 313233343030
result = invalid
sig = 303c02ff3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 88
# incorrect length of integer
msg = 313233343030
result = invalid
sig = 303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a0402ff617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 89
# removing integer
msg = 313233343030
result = invalid
sig = 301e021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 90
# lonely integer tag
msg = 313233343030
result = invalid
sig = 301f02021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 91
# lonely integer tag
msg = 313233343030
result = invalid
sig = 301f021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a0402

# tcId = 92
# appending 0's to integer
msg = 313233343030
result = invalid
sig = 303e021e3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040000021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 93
# appending 0's to integer
msg = 313233343030
result = invalid
sig = 303e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021e617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810000

# tcId = 94
# prepending 0's to integer
msg = 313233343030
result = invalid
sig = 303e021e00003ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981
flags = BER

# tcId = 95
# prepending 0's to integer
msg = 313233343030
result = invalid
sig = 303e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021e0000617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981
flags = BER

# tcId = 96
# appending unused 0's to integer
msg = 313233343030
result = invalid
sig = 303e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040000021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 97
# appending null value to integer
msg = 313233343030
result = invalid
sig = 303e021e3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040500021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 98
# appending null value to integer
msg = 313233343030
result = invalid
sig = 303e021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021e617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9810500

# tcId = 99
# truncated length of integer
msg = 313233343030
result = invalid
sig = 30200281021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 100
# truncated length of integer
msg = 313233343030
result = invalid
sig = 3020021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040281

# tcId = 101
# Replacing integer with NULL
msg = 313233343030
result = invalid
sig = 30200500021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 102
# Replacing integer with NULL
msg = 313233343030
result = invalid
sig = 3020021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040500

# tcId = 103
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303c001c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 104
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303c011c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 105
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303c031c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 106
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303c041c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 107
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303cff1c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 108
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04001c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 109
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04011c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 110
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04031c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 111
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04041c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 112
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04ff1c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 113
# dropping value of integer
msg = 313233343030
result = invalid
sig = 30200200021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 114
# dropping value of integer
msg = 313233343030
result = invalid
sig = 3020021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a040200

# tcId = 115
# using composition for integer
msg = 313233343030
result = invalid
sig = 3040222002013a021bde5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 116
# using composition for integer
msg = 313233343030
result = invalid
sig = 3040021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a042220020161021b7d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 117
# modify first byte of integer
msg = 313233343030
result = invalid
sig = 303c021c38de5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 118
# modify first byte of integer
msg = 313233343030
result = invalid
sig = 303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c637d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 119
# modify last byte of integer
msg = 313233343030
result = invalid
sig = 303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a84021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 120
# modify last byte of integer
msg = 313233343030
result = invalid
sig = 303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad901

# tcId = 121
# truncated integer
msg = 313233343030
result = invalid
sig = 303b021b3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 122
# truncated integer
msg = 313233343030
result = invalid
sig = 303b021bde5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 123
# truncated integer
msg = 313233343030
result = invalid
sig = 303b021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021b617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad9

# tcId = 124
# truncated integer
msg = 313233343030
result = invalid
sig = 303b021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021b7d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 125
# leading ff in integer
msg = 313233343030
result = invalid
sig = 303d021dff3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 126
# leading ff in integer
msg = 313233343030
result = invalid
sig = 303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021dff617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 127
# replaced integer by infinity
msg = 313233343030
result = invalid
sig = 3021090180021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 128
# replaced integer by infinity
msg = 313233343030
result = invalid
sig = 3021021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04090180

# tcId = 129
# replacing integer with zero
msg = 313233343030
result = invalid
sig = 3021020100021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 130
# replacing integer with zero
msg = 313233343030
result = invalid
sig = 3021021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04020100

# tcId = 131
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303d021d013ade5c0624a5677ed7b6450d941fd283098d8a004fc718e2e7e6b441021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 132
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303d021dff3ade5c0624a5677ed7b6450d9421a53d481ba984280cc6582f2e5fc7021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 133
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303c021cc521a3f9db5a98812849baf26bdf441fd72b663dc4161062747575fc021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 134
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303d021d00c521a3f9db5a98812849baf26bde5ac2b7e4567bd7f339a7d0d1a039021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 135
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303d021dfec521a3f9db5a98812849baf26be02d7cf67275ffb038e71d18194bbf021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 136
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303d021d013ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 137
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303d021d00c521a3f9db5a98812849baf26bdf441fd72b663dc4161062747575fc021c617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 138
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021d01617d6af141efd0c800c9ba3382c2119a390cfa9bed6a409bfe3703be

# tcId = 139
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021dff617d6af141efd0c800c9ba3382c3e454779b1a1fc5afee11457eaf44

# tcId = 140
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303c021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021c9e82950ebe102f37ff3645cc7d3d0508a7abf5a22672e8a95e25267f

# tcId = 141
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021dfe9e82950ebe102f37ff3645cc7d3dee65c6f305641295bf6401c8fc42

# tcId = 142
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021d01617d6af141efd0c800c9ba3382c2faf758540a5dd98d1756a1dad981

# tcId = 143
# Modified r or s, e.g. by adding or subtracting the order of the group
msg = 313233343030
result = invalid
sig = 303d021c3ade5c0624a5677ed7b6450d9420bbe028d499c23be9ef9d8b8a8a04021d009e82950ebe102f37ff3645cc7d3d0508a7abf5a22672e8a95e25267f

# tcId = 144
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020100020100
flags = EdgeCase

# tcId = 145
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020100020101
flags = EdgeCase

# tcId = 146
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201000201ff
flags = EdgeCase

# tcId = 147
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d
flags = EdgeCase

# tcId = 148
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c
flags = EdgeCase

# tcId = 149
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e
flags = EdgeCase

# tcId = 150
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020100021d00ffffffffffffffffffffffffffffffff000000000000000000000001
flags = EdgeCase

# tcId = 151
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020100021d00ffffffffffffffffffffffffffffffff000000000000000000000002
flags = EdgeCase

# tcId = 152
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3008020100090380fe01
flags = EdgeCase

# tcId = 153
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020100090142
flags = EdgeCase

# tcId = 154
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020101020100
flags = EdgeCase

# tcId = 155
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020101020101
flags = EdgeCase

# tcId = 156
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201010201ff
flags = EdgeCase

# tcId = 157
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d
flags = EdgeCase

# tcId = 158
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c
flags = EdgeCase

# tcId = 159
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e
flags = EdgeCase

# tcId = 160
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020101021d00ffffffffffffffffffffffffffffffff000000000000000000000001
flags = EdgeCase

# tcId = 161
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022020101021d00ffffffffffffffffffffffffffffffff000000000000000000000002
flags = EdgeCase

# tcId = 162
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3008020101090380fe01
flags = EdgeCase

# tcId = 163
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3006020101090142
flags = EdgeCase

# tcId = 164
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201ff020100
flags = EdgeCase

# tcId = 165
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201ff020101
flags = EdgeCase

# tcId = 166
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201ff0201ff
flags = EdgeCase

# tcId = 167
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d
flags = EdgeCase

# tcId = 168
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c
flags = EdgeCase

# tcId = 169
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e
flags = EdgeCase

# tcId = 170
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30220201ff021d00ffffffffffffffffffffffffffffffff000000000000000000000001
flags = EdgeCase

# tcId = 171
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30220201ff021d00ffffffffffffffffffffffffffffffff000000000000000000000002
flags = EdgeCase

# tcId = 172
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30080201ff090380fe01
flags = EdgeCase

# tcId = 173
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 30060201ff090142
flags = EdgeCase

# tcId = 174
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d020100
flags = EdgeCase

# tcId = 175
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d020101
flags = EdgeCase

# tcId = 176
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d0201ff
flags = EdgeCase

# tcId = 177
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d
flags = EdgeCase

# tcId = 178
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c
flags = EdgeCase

# tcId = 179
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e
flags = EdgeCase

# tcId = 180
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffffffff000000000000000000000001
flags = EdgeCase

# tcId = 181
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffffffff000000000000000000000002
flags = EdgeCase

# tcId = 182
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d090380fe01
flags = EdgeCase

# tcId = 183
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d090142
flags = EdgeCase

# tcId = 184
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c020100
flags = EdgeCase

# tcId = 185
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c020101
flags = EdgeCase

# tcId = 186
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c0201ff
flags = EdgeCase

# tcId = 187
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d
flags = EdgeCase

# tcId = 188
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c
flags = EdgeCase

# tcId = 189
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e
flags = EdgeCase

# tcId = 190
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffffffff000000000000000000000001
flags = EdgeCase

# tcId = 191
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffffffff000000000000000000000002
flags = EdgeCase

# tcId = 192
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c090380fe01
flags = EdgeCase

# tcId = 193
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c090142
flags = EdgeCase

# tcId = 194
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e020100
flags = EdgeCase

# tcId = 195
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e020101
flags = EdgeCase

# tcId = 196
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e0201ff
flags = EdgeCase

# tcId = 197
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d
flags = EdgeCase

# tcId = 198
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c
flags = EdgeCase

# tcId = 199
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e
flags = EdgeCase

# tcId = 200
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffffffff000000000000000000000001
flags = EdgeCase

# tcId = 201
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffffffff000000000000000000000002
flags = EdgeCase

# tcId = 202
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e090380fe01
flags = EdgeCase

# tcId = 203
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e090142
flags = EdgeCase

# tcId = 204
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001020100
flags = EdgeCase

# tcId = 205
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001020101
flags = EdgeCase

# tcId = 206
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffffffff0000000000000000000000010201ff
flags = EdgeCase

# tcId = 207
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d
flags = EdgeCase

# tcId = 208
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c
flags = EdgeCase

# tcId = 209
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e
flags = EdgeCase

# tcId = 210
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffffffff000000000000000000000001
flags = EdgeCase

# tcId = 211
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffffffff000000000000000000000002
flags = EdgeCase

# tcId = 212
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3024021d00ffffffffffffffffffffffffffffffff000000000000000000000001090380fe01
flags = EdgeCase

# tcId = 213
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001090142
flags = EdgeCase

# tcId = 214
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002020100
flags = EdgeCase

# tcId = 215
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002020101
flags = EdgeCase

# tcId = 216
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffffffff0000000000000000000000020201ff
flags = EdgeCase

# tcId = 217
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d
flags = EdgeCase

# tcId = 218
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c
flags = EdgeCase

# tcId = 219
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e
flags = EdgeCase

# tcId = 220
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffffffff000000000000000000000001
flags = EdgeCase

# tcId = 221
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffffffff000000000000000000000002
flags = EdgeCase

# tcId = 222
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3024021d00ffffffffffffffffffffffffffffffff000000000000000000000002090380fe01
flags = EdgeCase

# tcId = 223
# Signature with special case values for r and s
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002090142
flags = EdgeCase

# tcId = 224
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 30060201010c0130

# tcId = 225
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 30050201010c00

# tcId = 226
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 30090c0225730c03732573

# tcId = 227
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 30080201013003020100

# tcId = 228
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 3003020101

# tcId = 229
# Signature encoding contains wrong types.
msg = 313233343030
result = invalid
sig = 3006020101010100

# tcId = 230
# Edge case for Shamir multiplication
msg = 3839313737
result = valid
sig = 303d021c326bc06353f7f9c9f77b8f4b55464e8619944e7879402cca572e041a021d0096ad91f02a3bc40c118abd416ed5c6203ed7ced0330860d7b88c10ab

# tcId = 231
# special case hash
msg = 343236343739373234
result = valid
sig = 303d021d00bcca2365cebdcf7c6cda1ee7b27c7fe79e371537b01869c715eabb1e021c3ae76f9bbfe519d778816dc8fe10635ee7576b6b7916f0c21df320c0

# tcId = 232
# special case hash
msg = 37313338363834383931
result = valid
sig = 303c021c59a9f83289ef6995d5d5592e80ab4f6a81123f69d385d3cfb152faf2021c3a97d5be190d5819241067e2be56375ab84155baab8fc7aeb7f8cb3d

# tcId = 233
# special case hash
msg = 3130333539333331363638
result = valid
sig = 303e021d00b54bac9be2beaaa09456a3968a1faf27c9d96bd5f6738fec6066d31e021d00d72c22129344a96d52fda60b264cf5e6fae45fd2c1b1b78bcba30070

# tcId = 234
# special case hash
msg = 33393439343031323135
result = valid
sig = 303d021c323dbdecd40910c6fa7a5691846fa7769113d1f2ba64ef0dc97d2ddb021d00ca9e73a4587af042f8ba924bb61829c5e24046f9803eb76ab80ef327

# tcId = 235
# special case hash
msg = 31333434323933303739
result = valid
sig = 303d021d00a55dccc27d287f15960ed79908a3edb6bb31aff07c8caa0e65fc0785021c559cb51aa5f2b9066610199dd01291a47729a6189a622ae9e7af7621

# tcId = 236
# special case hash
msg = 33373036323131373132
result = valid
sig = 303d021c137ed6105148d6f5b84e87735d57955f81c5914a6e69f55347ade074021d00dfa5d56b1a12567efacb348a133b79d48da7aac78d78ee589c2ec027

# tcId = 237
# special case hash
msg = 333433363838373132
result = valid
sig = 303e021d00856ff63d779163e78fed8c48330b48f08bf953a95266b3857eee91aa021d00f4aa917cd37f556c6df9d0960c2f7daa7ea118e5c30cc40ca1eed418

# tcId = 238
# special case hash
msg = 31333531353330333730
result = valid
sig = 303d021d00a9d7716f04c5ce247f6b8c608b37db55f68e2ff94a5883863e867708021c61bc093faa6fb25cd240aea4b56fed728f7b3669b4dc84c449d38c5d

# tcId = 239
# special case hash
msg = 36353533323033313236
result = valid
sig = 303d021d00f6d088fd3b9c981ac491c62030643bbd82d4f4588e8517de5884e73d021c773eee477980763b1ea27ae998bda0244cb67b07aa6779a38cd2ba3f

# tcId = 240
# special case hash
msg = 31353634333436363033
result = valid
sig = 303e021d00eacb55588e446bbf3687089ba8ba3b05cfef7458bb81b4277f90a853021d008039e8944cc3df7f4ce5badc349975d471a81dea14e9bcae3065d410

# tcId = 241
# special case hash
msg = 34343239353339313137
result = valid
sig = 303c021c5984af8c89fb9d596a1f28fd3d41e46f7205fe12fa63437ac79e7e81021c33b16b742d45f18f88de2713078384e6150f06b8b99f36ab2ce3dd49

# tcId = 242
# special case hash
msg = 3130393533323631333531
result = valid
sig = 303d021c3cda62d84711c262f782d5c3a79b567485227b34afb821f5241b1961021d00b615cef399706ff758f072931852b717ec898e9a1e6339d0ee81b8da

# tcId = 243
# special case hash
msg = 35393837333530303431
result = valid
sig = 303d021d00e1db7304609191ea1ac91183ffb31df51b5b3fdc6b1a1129d85818d6021c441886d003ae80fbe7139e1d02845cd1bd959f0df1468f5836dd6ea5

# tcId = 244
# special case hash
msg = 33343633303036383738
result = valid
sig = 303d021c3545dc4a4ef84bbb3a526ff929c91ad234516a9e95455ac8db4012b1021d00af49926f693a7cf11f71e199f382a8d640c0c85e46d94ee26e384344

# tcId = 245
# special case hash
msg = 39383137333230323837
result = valid
sig = 303d021c0ccafdeae4582c9de6795b2d09a7fc3848c75904fa960989156cbbb9021d00af1f994da3e7d89cc8aaa44616cb77e3be7a83ccecc965775194e502

# tcId = 246
# special case hash
msg = 33323232303431303436
result = valid
sig = 303e021d00a3b2145d8c669027532501eea1913abb22a78a827fdd82fe9d6d3757021d009b2f1ae84f5606d68653065f74e9d089886694c739fbe3fd4a1b2b4a

# tcId = 247
# special case hash
msg = 36363636333037313034
result = valid
sig = 303e021d009aac3a7e3d142344991bf177b4f4dbfa074148ad9e20f27555b547d9021d00f830a3c7fdf251d79d41977d28e6d9a72a36df11b86e17c8dc3acae0

# tcId = 248
# special case hash
msg = 31303335393531383938
result = valid
sig = 303c021c4769fba554fd436051c285bdadfa33a443d4f7084dd598ce3b98b8fb021c0c014c87cb14113d75864f74905f75b34f9970ba58b5d0676021826d

# tcId = 249
# special case hash
msg = 31383436353937313935
result = valid
sig = 303d021d008b91fc5054a75c34a508624b85708b3d25fa74328c68741c3aeb92d9021c155e3e46b1209583135a9fef15abe325b25bd19285ee6b5b4549629f

# tcId = 250
# special case hash
msg = 33313336303436313839
result = valid
sig = 303d021d00a4a2a85fbb8bb26c4d845cfac191f89d65b00d3f1b9450d177f78890021c6605a460e60402685c7a5accd2615e9232e51937bd83dfa3065eabf7

# tcId = 251
# special case hash
msg = 32363633373834323534
result = valid
sig = 303d021d00a89d333ae34187855cf7fa435ff39be6b7bb39b2d0ce682133ad9646021c483dcc89a3b43be250f5c3f78f78418e7b8341a8bcfb93dfd58e46d8

# tcId = 252
# special case hash
msg = 31363532313030353234
result = valid
sig = 303d021c2d0f99c71933c82ded544ef4faac9d669e437dea13b57186f4c20a0e021d00d9682b9f3a05d7832947bc45eadbc742d96e7ab1124832ddb7a8c65b

# tcId = 253
# special case hash
msg = 35373438303831363936
result = valid
sig = 303d021d00840208f7c41b1fbadcc701fb3a1d0f98a3e2a75235e695bfd378f8b4021c44c8daad4efc03e1753803c362b409c3ca6e0f21e538fe3a364c0e53

# tcId = 254
# special case hash
msg = 36333433393133343638
result = valid
sig = 303e021d0087cc582cb10602110566fcb10a233aede993fae5fb3f81b0bbff94ca021d00c971c05bd51d9685825b2cfc0a2596c7f80d9f9dc68c28c159aa395a

# tcId = 255
# special case hash
msg = 31353431313033353938
result = valid
sig = 303d021c50d73d949b3adcd3e8fa94dafefaf9d263ebc702128d891afac47ea7021d00f8423c378f0190574925142eb5b97c612abfa048fa3ab5375ec795a1

# tcId = 256
# special case hash
msg = 3130343738353830313238
result = valid
sig = 303e021d00d608915dfcd5d3c63ed10d0d9b614f7a866f8858a6e59dc03eb0a8ee021d008e701aa0bab491430f6e4da92244b0bb174957ee6f495bc5d15fabb1

# tcId = 257
# special case hash
msg = 3130353336323835353638
result = valid
sig = 303e021d00c87b0ab842c4769ed94b910bd7719691f9991bc5a347889608f07034021d00d083111048d6e019771fc2669c55156a3d09615a6b2d9cae52ddabee

# tcId = 258
# special case hash
msg = 393533393034313035
result = valid
sig = 303c021c0a1c2c2478e244464226c660edf724db1213f4923eb725d611d976fd021c764e55186a76f734891d05fb57af2727fab8fbea684ca4321d5de540

# tcId = 259
# special case hash
msg = 393738383438303339
result = valid
sig = 303e021d008a2747c5dd9ef5298b8aeabd2fb3a2beb16158fb2cc62be9e51b2152021d00f96251bc048bcad832e6cbc09c9c2e585ab7543dc552eaa5125be0d3

# tcId = 260
# special case hash
msg = 33363130363732343432
result = valid
sig = 303e021d00d9eac32a734f3a3e5b5a2905bed8164ef4c6cd24d5c0fc54cc83f3cc021d00a784930d16c3b753bb3ed9151d583c50ff97bc976274bde482fb9644

# tcId = 261
# special case hash
msg = 31303534323430373035
result = valid
sig = 303d021c6c40c6b15ae573f77b677cd878cc5e4da8171cf50d79974fde374e00021d00c88c9828037bf7013a1415537ca074d6c8a553bdb4b26b14a7e88d93

# tcId = 262
# special case hash
msg = 35313734343438313937
result = valid
sig = 303d021d00dca0aaa0a395393142b323edced09372760350f2ab261ce3339b114d021c0983bf6e510ce7f0a7520f2b7c60cd68a4912b78162c7ac33789e0c6

# tcId = 263
# special case hash
msg = 31393637353631323531
result = valid
sig = 303d021d00a0526ed47e2607e6bae6dcf3b8f54f4e0638023673a38cad4569c3ba021c61516f55746b379d11cbaa02cef35311d7771a47d1e127cff46dcfd6

# tcId = 264
# special case hash
msg = 33343437323533333433
result = valid
sig = 303d021c5c00db60178c8361092bdfb47fc9a47b33363d7e0d76e32520f79657021d00e1baf7ae7d81045793c73173f49d60bdfc8779942795d9d082b3ca11

# tcId = 265
# special case hash
msg = 333638323634333138
result = valid
sig = 303d021c46f69b6a99717949eee74092a0c1438a290a2cd82fe1e10d8f37e88b021d0099a5f59f09bd980a066233523397846987a8a1bfdde355062d140a4b

# tcId = 266
# special case hash
msg = 33323631313938363038
result = valid
sig = 303e021d00e643d8085a22706fa0e6540f3d5e169ad8cc49b4bfe98e325321c705021d00f95bd423f9cafe0cedfec6fd97871536d71b2ac58dfb2f7ab8952d4b

# tcId = 267
# special case hash
msg = 39363738373831303934
result = valid
sig = 303e021d00e65fb9bcdd791f141ccff2b3cfbf45d84f8c6272021a68dde8c36bc8021d00df6e08c74b5e36b7772658f02515ae0ea813b64df24f3522ea15fb15

# tcId = 268
# special case hash
msg = 34393538383233383233
result = valid
sig = 303e021d00a476d468221ef55611e8a724c9b4cd79c34f6940d5f665e3335f6231021d00bfddc18e7a008bc206c8e1ca6c878363e4138508e0c3a84a27eabe35

# tcId = 269
# special case hash
msg = 383234363337383337
result = valid
sig = 303c021c1b393477941879271873a8c043a77caadb9957fcdd263a6ac978e4ba021c270060d5f356ebb6d185772baa78b878af6807378e0d5c532da0a4a7

# tcId = 270
# special case hash
msg = 3131303230383333373736
result = valid
sig = 303e021d00b2eda8c969d4b1bdd31867fd1f92d547b406840c257f2f80dfbdc4e3021d00e6297b059ce64ef04de9715a8f686a9f73980865066a94975b7f8117

# tcId = 271
# special case hash
msg = 313333383731363438
result = valid
sig = 303d021d00938189a18a4bff5712ac99c2b8e92c218af3e4d4e3a84b906b0f704e021c7bb3e538f0b70664dad462ab14b0ed416c86ac6e9060fe760dabb715

# tcId = 272
# special case hash
msg = 333232313434313632
result = valid
sig = 303e021d00bb7c1d8120d2aa7765b16eeac44282de605fb2a1665657dea4492935021d00e0a8adb3a143883f981ea1323fa6f1d347845be2b8dcc6cd5cc93ee5

# tcId = 273
# special case hash
msg = 3130363836363535353436
result = valid
sig = 303c021c74a4c51dd60c7118467be29652060f39af94f8c0eb7f15c64771010c021c6102ec0c9257e607af3f3ff7490b54e78111f422bec11ba01277171f

# tcId = 274
# special case hash
msg = 3632313535323436
result = valid
sig = 303d021c625da18d676f02fae9dbcb3092265909488fb95d662569d7746b9687021d00c4f1ec831e36604d604b630fd0b1999cd09960862294251d85e5873d

# tcId = 275
# special case hash
msg = 37303330383138373734
result = valid
sig = 303d021d008ee0d4a31fd1c4d854d75c14151926899dde1c7332fd4769443d213d021c4b8278b89ba4f8fbd7dcc6affe4c12156f7409909416989685dd5a39

# tcId = 276
# special case hash
msg = 35393234353233373434
result = valid
sig = 303e021d00bdde45fc9ebb3749c9fb2c25bf02e2a217ccc112f8e65499eeffb6a1021d00becd6b88ef2bee872ebc0e2b805a56066e19179fce9f0dc0df3f6378

# tcId = 277
# special case hash
msg = 31343935353836363231
result = valid
sig = 303d021c50186e023a1f5053fcb4d0473039b1b2cdeba569719a4ebabdd675c8021d00f8fb893c1b6b5b827b5f3f4bb5eab75b6212bb56a5a39bb35c127a1c

# tcId = 278
# special case hash
msg = 34303035333134343036
result = valid
sig = 303e021d00d3b454639b0fb3da93b20d55be8609e40902cb4a608f3b9064c0deb7021d00ec7aa9637fd71b543e5243faab4c7a2edc2c48e982c5ac017807f19a

# tcId = 279
# special case hash
msg = 33303936343537353132
result = valid
sig = 303d021d00c202abbd98e03809de842bdef268a1c616a7306da69a87abaf03169c021c7e7e04823af8ed6836fd2ac011e47de8e1bef91ed1da5144893fc259

# tcId = 280
# special case hash
msg = 32373834303235363230
result = valid
sig = 303d021c2e4b76638816cce057a4a27a49258dcb5437ae97739f27ebc0973c0b021d00e9f6c0b64e764ad39dd92b576e11c23e5994b02095cb2a4720c8662c

# tcId = 281
# special case hash
msg = 32363138373837343138
result = valid
sig = 303c021c7e0f48761089aa4c7ecd5a7ac5380836b1e5d381d3400174d15df98b021c0c3df50060e3a6714aa565a33d784e7b16ac87bebfb3c2255cfd832c

# tcId = 282
# special case hash
msg = 31363432363235323632
result = valid
sig = 303c021c4d6f7408508eb0814dcd48007f0efd9e2b91cdac4030540cc678de19021c1e74f8dc34d13613ef42462fe88981cbe2489be10e4cdae975a1b38e

# tcId = 283
# special case hash
msg = 36383234313839343336
result = valid
sig = 303d021d00967f2c5d304c7932eaaa1682197945e66cc912b703824776ef16ad7a021c73957001d9037c63d6471c809a2388383ad695137c622cd5f5584414

# tcId = 284
# special case hash
msg = 343834323435343235
result = valid
sig = 303d021c49260804bb2ceae4b9cee63b02ea60173ec3f4f90167627c0bb39888021d00c9eb022f96db3e90fe0ff617730a629f342e02fb208d6836cbbdc7d3

# tcId = 285
# Signature generated without truncating the hash
msg = 313233343030
result = invalid
sig = 303d021d00f3e712597a4b22632c5f8eb9f2845882bb03a139735f80af8826fc56021c62865bd91c0903511a481d607eb6b5fe28f6f6c89295681a3e8d55d8

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0493b4c28f032d00f80e77491edc158359909ee9e30a7327b74219e5e2482c19ae35cb28afc9b95ca1ed7ad91c812d5fcceb4beddbf1a16d92]
[key.wx = 0093b4c28f032d00f80e77491edc158359909ee9e30a7327b74219e5e2]
[key.wy = 482c19ae35cb28afc9b95ca1ed7ad91c812d5fcceb4beddbf1a16d92]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000493b4c28f032d00f80e77491edc158359909ee9e30a7327b74219e5e2482c19ae35cb28afc9b95ca1ed7ad91c812d5fcceb4beddbf1a16d92]
[sha = SHA-256]

# tcId = 286
# k*G has a large x-coordinate
msg = 313233343030
result = valid
sig = 3030020f00e95c1f470fc1ec22d6baa3a3d5c1021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a

# tcId = 287
# r too large
msg = 313233343030
result = invalid
sig = 303e021d00fffffffffffffffffffffffffffffffefffffffffffffffffffffffe021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04da927f4ba88b639bf5334221d2f54d8ef9ccc1a1125fad18c7bfb789ac51ae53de6d834a9db3947b8dd4c6ac2b084b85496bfa72d86b6948]
[key.wx = 00da927f4ba88b639bf5334221d2f54d8ef9ccc1a1125fad18c7bfb789]
[key.wy = 00ac51ae53de6d834a9db3947b8dd4c6ac2b084b85496bfa72d86b6948]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004da927f4ba88b639bf5334221d2f54d8ef9ccc1a1125fad18c7bfb789ac51ae53de6d834a9db3947b8dd4c6ac2b084b85496bfa72d86b6948]
[sha = SHA-256]

# tcId = 288
# r,s are large
msg = 313233343030
result = valid
sig = 303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3b

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0420888e1c0f5694c4c0363b36482beb6e1e6649b3d3b26f127febb6fcde00c2f3d8e4a7e8a0bafd417c96d3e81c975946a2f3686aa39d35f1]
[key.wx = 20888e1c0f5694c4c0363b36482beb6e1e6649b3d3b26f127febb6fc]
[key.wy = 00de00c2f3d8e4a7e8a0bafd417c96d3e81c975946a2f3686aa39d35f1]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000420888e1c0f5694c4c0363b36482beb6e1e6649b3d3b26f127febb6fcde00c2f3d8e4a7e8a0bafd417c96d3e81c975946a2f3686aa39d35f1]
[sha = SHA-256]

# tcId = 289
# r and s^-1 have a large Hamming weight
msg = 313233343030
result = valid
sig = 303c021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021c3d5052691b8dc89debad360466f2a39e82e8ae2aefb77c3c92ad7cd1

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 049545c86f032c5df255a4490bb0b83eca201181792ad74246874db229405264c283063327b70f4c2be5ab4d2e9407b866e121d6145d124c04]
[key.wx = 009545c86f032c5df255a4490bb0b83eca201181792ad74246874db229]
[key.wy = 405264c283063327b70f4c2be5ab4d2e9407b866e121d6145d124c04]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00049545c86f032c5df255a4490bb0b83eca201181792ad74246874db229405264c283063327b70f4c2be5ab4d2e9407b866e121d6145d124c04]
[sha = SHA-256]

# tcId = 290
# r and s^-1 have a large Hamming weight
msg = 313233343030
result = valid
sig = 303d021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021d00bf19ab4d3ebf5a1a49d765909308daa88c2b7be3969db552ea30562b

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04579d53f39d5109bd440e3e3e7efd603740963348ff9c72c03b0fe6b8df02f133ecd60b072a0812adc752708f2be9d8c9ad5953d8c7bf3965]
[key.wx = 579d53f39d5109bd440e3e3e7efd603740963348ff9c72c03b0fe6b8]
[key.wy = 00df02f133ecd60b072a0812adc752708f2be9d8c9ad5953d8c7bf3965]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004579d53f39d5109bd440e3e3e7efd603740963348ff9c72c03b0fe6b8df02f133ecd60b072a0812adc752708f2be9d8c9ad5953d8c7bf3965]
[sha = SHA-256]

# tcId = 291
# small r and s
msg = 313233343030
result = valid
sig = 3006020103020101

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04d2a14c8106d89f3536faebdafcd4680f65ab4bf2243164ca1464b628acaf2bee52e6231d3c980f52f8e189a41c3e3a05e591195ec864217a]
[key.wx = 00d2a14c8106d89f3536faebdafcd4680f65ab4bf2243164ca1464b628]
[key.wy = 00acaf2bee52e6231d3c980f52f8e189a41c3e3a05e591195ec864217a]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004d2a14c8106d89f3536faebdafcd4680f65ab4bf2243164ca1464b628acaf2bee52e6231d3c980f52f8e189a41c3e3a05e591195ec864217a]
[sha = SHA-256]

# tcId = 292
# small r and s
msg = 313233343030
result = valid
sig = 3006020103020103

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04e892479153ad13ea5ca45d4c323ebf1fc3cd0cdf787c34306a3f79a4326ca9645f2b517608dc1f08b7a84cfc61e6ff68d14f27d2043c7ef5]
[key.wx = 00e892479153ad13ea5ca45d4c323ebf1fc3cd0cdf787c34306a3f79a4]
[key.wy = 326ca9645f2b517608dc1f08b7a84cfc61e6ff68d14f27d2043c7ef5]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004e892479153ad13ea5ca45d4c323ebf1fc3cd0cdf787c34306a3f79a4326ca9645f2b517608dc1f08b7a84cfc61e6ff68d14f27d2043c7ef5]
[sha = SHA-256]

# tcId = 293
# small r and s
msg = 313233343030
result = valid
sig = 3006020103020104

# tcId = 294
# r is larger than n
msg = 313233343030
result = invalid
sig = 3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a40020104

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 042b0eac35c0b294f6d435dcaffa8633b0123005465c30080adbcc103ad465a63bfb71d4aee09328697fe1088753646d8369b8dc103217c219]
[key.wx = 2b0eac35c0b294f6d435dcaffa8633b0123005465c30080adbcc103a]
[key.wy = 00d465a63bfb71d4aee09328697fe1088753646d8369b8dc103217c219]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00042b0eac35c0b294f6d435dcaffa8633b0123005465c30080adbcc103ad465a63bfb71d4aee09328697fe1088753646d8369b8dc103217c219]
[sha = SHA-256]

# tcId = 295
# s is larger than n
msg = 313233343030
result = invalid
sig = 3022020103021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c6f00c4

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04d156e01e33becede8f4fb4ae9521d751e7f8eb795ca00857db2fd7afd73a450ec60e6a9218a8431870687e0968944f6dc5ffeb30e4693b7c]
[key.wx = 00d156e01e33becede8f4fb4ae9521d751e7f8eb795ca00857db2fd7af]
[key.wy = 00d73a450ec60e6a9218a8431870687e0968944f6dc5ffeb30e4693b7c]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004d156e01e33becede8f4fb4ae9521d751e7f8eb795ca00857db2fd7afd73a450ec60e6a9218a8431870687e0968944f6dc5ffeb30e4693b7c]
[sha = SHA-256]

# tcId = 296
# small r and s^-1
msg = 313233343030
result = valid
sig = 302302020100021d00c993264c993264c993264c99326411d2e55b3214a8d67528812a55ab

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04f293a8a2b4aff0bed95c663b364afe69778d38dd7e7a304f7d3c74e617dfd09e7803c4439a6c075cb579cde652d03f7559ff58846312fa4c]
[key.wx = 00f293a8a2b4aff0bed95c663b364afe69778d38dd7e7a304f7d3c74e6]
[key.wy = 17dfd09e7803c4439a6c075cb579cde652d03f7559ff58846312fa4c]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004f293a8a2b4aff0bed95c663b364afe69778d38dd7e7a304f7d3c74e617dfd09e7803c4439a6c075cb579cde652d03f7559ff58846312fa4c]
[sha = SHA-256]

# tcId = 297
# smallish r and s^-1
msg = 313233343030
result = valid
sig = 302702072d9b4d347952cc021c3e85d56474b5c55fbe86608442a84b2bf093b7d75f53a47250e1c70c

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04d4ddf003b298cbaa7d2edc584b28b474a76162ed4b5b0f6222c54317d4e4fe030f178fb4aa4a6d7f61265ecd7ef13c313606b8d341a8b954]
[key.wx = 00d4ddf003b298cbaa7d2edc584b28b474a76162ed4b5b0f6222c54317]
[key.wy = 00d4e4fe030f178fb4aa4a6d7f61265ecd7ef13c313606b8d341a8b954]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004d4ddf003b298cbaa7d2edc584b28b474a76162ed4b5b0f6222c54317d4e4fe030f178fb4aa4a6d7f61265ecd7ef13c313606b8d341a8b954]
[sha = SHA-256]

# tcId = 298
# 100-bit r and small s^-1
msg = 313233343030
result = valid
sig = 302d020d1033e67e37b32b445580bf4efb021c02fd02fd02fd02fd02fd02fd02fd0043a4fd2da317247308c74dc6b8

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 048a5bf0028f1e3eb6841dee7b8f873f68b0c560e592e3182074f51ce89668c32224b65b6849713d35e3acf1786862e65b5a664b47a098caa0]
[key.wx = 008a5bf0028f1e3eb6841dee7b8f873f68b0c560e592e3182074f51ce8]
[key.wy = 009668c32224b65b6849713d35e3acf1786862e65b5a664b47a098caa0]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00048a5bf0028f1e3eb6841dee7b8f873f68b0c560e592e3182074f51ce89668c32224b65b6849713d35e3acf1786862e65b5a664b47a098caa0]
[sha = SHA-256]

# tcId = 299
# small r and 100 bit s^-1
msg = 313233343030
result = valid
sig = 302302020100021d00d05434abacd859ed74185e75b751c6d9f60c7921dacfbb8e19cdba8e

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04b53e569b18e9361567e5713ee69ecbe7949911b0257546a24c3dd137f29a83334cff1c44d8c0c33b6dadb8568c024fa1fbb694cd9e705f5a]
[key.wx = 00b53e569b18e9361567e5713ee69ecbe7949911b0257546a24c3dd137]
[key.wy = 00f29a83334cff1c44d8c0c33b6dadb8568c024fa1fbb694cd9e705f5a]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004b53e569b18e9361567e5713ee69ecbe7949911b0257546a24c3dd137f29a83334cff1c44d8c0c33b6dadb8568c024fa1fbb694cd9e705f5a]
[sha = SHA-256]

# tcId = 300
# 100-bit r and s^-1
msg = 313233343030
result = valid
sig = 302e020d062522bbd3ecbe7c39e93e7c24021d00d05434abacd859ed74185e75b751c6d9f60c7921dacfbb8e19cdba8e

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0477f3ebf52725c809acbb19adf093126a2a3a065ca654c22099c978129f1948d23c5158ec2adff455eb2fedf1075d4ec22d660977424a10f7]
[key.wx = 77f3ebf52725c809acbb19adf093126a2a3a065ca654c22099c97812]
[key.wy = 009f1948d23c5158ec2adff455eb2fedf1075d4ec22d660977424a10f7]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000477f3ebf52725c809acbb19adf093126a2a3a065ca654c22099c978129f1948d23c5158ec2adff455eb2fedf1075d4ec22d660977424a10f7]
[sha = SHA-256]

# tcId = 301
# r and s^-1 are close to n
msg = 313233343030
result = valid
sig = 303d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c29bd021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04a7f7b99e5cdc6fec8928eff773ccdf3b68b19d43cdb41809e19c60f31736b7a0c12a9c2d706671912915142b3e05c89ef3ad497bd6c34699]
[key.wx = 00a7f7b99e5cdc6fec8928eff773ccdf3b68b19d43cdb41809e19c60f3]
[key.wy = 1736b7a0c12a9c2d706671912915142b3e05c89ef3ad497bd6c34699]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004a7f7b99e5cdc6fec8928eff773ccdf3b68b19d43cdb41809e19c60f31736b7a0c12a9c2d706671912915142b3e05c89ef3ad497bd6c34699]
[sha = SHA-256]

# tcId = 302
# s == 1
msg = 313233343030
result = valid
sig = 3021021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14020101

# tcId = 303
# s == 0
msg = 313233343030
result = invalid
sig = 3021021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14020100

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 049cf00010b4ad86636f6cc70fb58c3b995c0d12e46fc58e24b0d28f6921c8a8a320cc450ccb15ebd71617f4ed25db4d3413fbdf157d31dbb6]
[key.wx = 009cf00010b4ad86636f6cc70fb58c3b995c0d12e46fc58e24b0d28f69]
[key.wy = 21c8a8a320cc450ccb15ebd71617f4ed25db4d3413fbdf157d31dbb6]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00049cf00010b4ad86636f6cc70fb58c3b995c0d12e46fc58e24b0d28f6921c8a8a320cc450ccb15ebd71617f4ed25db4d3413fbdf157d31dbb6]
[sha = SHA-256]

# tcId = 304
# point at infinity during verify
msg = 313233343030
result = invalid
sig = 303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151e021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04ae9b3636b8547232df438559b5a109e0238a73a76afc25d070ea27427210a69de44ad645b1b03845040f46fce238e92c131a71e4b184c01f]
[key.wx = 00ae9b3636b8547232df438559b5a109e0238a73a76afc25d070ea2742]
[key.wy = 7210a69de44ad645b1b03845040f46fce238e92c131a71e4b184c01f]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004ae9b3636b8547232df438559b5a109e0238a73a76afc25d070ea27427210a69de44ad645b1b03845040f46fce238e92c131a71e4b184c01f]
[sha = SHA-256]

# tcId = 305
# edge case for signature malleability
msg = 313233343030
result = valid
sig = 303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151e

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 048d57d4fce62757791888c1938076fd766daeb2ec9f1bda8ad5df4809aade924d7ea3ae5abbd0719a7d4865759da654cf76cf7ec031277108]
[key.wx = 008d57d4fce62757791888c1938076fd766daeb2ec9f1bda8ad5df4809]
[key.wy = 00aade924d7ea3ae5abbd0719a7d4865759da654cf76cf7ec031277108]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00048d57d4fce62757791888c1938076fd766daeb2ec9f1bda8ad5df4809aade924d7ea3ae5abbd0719a7d4865759da654cf76cf7ec031277108]
[sha = SHA-256]

# tcId = 306
# edge case for signature malleability
msg = 313233343030
result = valid
sig = 303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0410518eb7a926b5f7b65be801ec9b2abf76adce25c6152e452a3512c83f322b9ab57ea8352ad29beb99ef356b713432fcc4aef31f903045d9]
[key.wx = 10518eb7a926b5f7b65be801ec9b2abf76adce25c6152e452a3512c8]
[key.wy = 3f322b9ab57ea8352ad29beb99ef356b713432fcc4aef31f903045d9]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000410518eb7a926b5f7b65be801ec9b2abf76adce25c6152e452a3512c83f322b9ab57ea8352ad29beb99ef356b713432fcc4aef31f903045d9]
[sha = SHA-256]

# tcId = 307
# u1 == 1
msg = 313233343030
result = valid
sig = 303d021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021d00bb5a52f42f9c9261ed4361f59422a1e30036e7c32b270c8807a419fe

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 048a5dfedc9dd1cb9a439c88b3dd472b2e66173f7866855db6bb6c12fd3badfbb8a4c6fd80e66510957927c78a2aa02ecef62816d0356b49c3]
[key.wx = 008a5dfedc9dd1cb9a439c88b3dd472b2e66173f7866855db6bb6c12fd]
[key.wy = 3badfbb8a4c6fd80e66510957927c78a2aa02ecef62816d0356b49c3]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00048a5dfedc9dd1cb9a439c88b3dd472b2e66173f7866855db6bb6c12fd3badfbb8a4c6fd80e66510957927c78a2aa02ecef62816d0356b49c3]
[sha = SHA-256]

# tcId = 308
# u1 == n - 1
msg = 313233343030
result = valid
sig = 303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c44a5ad0bd0636d9e12bc9e0a6bdc74bfe082087ae8b61cbd54b8103f

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0483a59fc3df295e84c290b32d0b550a06f99456fc2298e4a68c4f2bff1b34f483db30db3a51d8288732c107d8b1a858cd54c3936e1b5c11a4]
[key.wx = 0083a59fc3df295e84c290b32d0b550a06f99456fc2298e4a68c4f2bff]
[key.wy = 1b34f483db30db3a51d8288732c107d8b1a858cd54c3936e1b5c11a4]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000483a59fc3df295e84c290b32d0b550a06f99456fc2298e4a68c4f2bff1b34f483db30db3a51d8288732c107d8b1a858cd54c3936e1b5c11a4]
[sha = SHA-256]

# tcId = 309
# u2 == 1
msg = 313233343030
result = valid
sig = 303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0458bada578a205d6e170722c8ed6c7715011fe33d7eba869ed1d448a75be4730c1d2d2ef881e02f028a241b7d7d3b0d0b4a9c0565fcb49977]
[key.wx = 58bada578a205d6e170722c8ed6c7715011fe33d7eba869ed1d448a7]
[key.wy = 5be4730c1d2d2ef881e02f028a241b7d7d3b0d0b4a9c0565fcb49977]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000458bada578a205d6e170722c8ed6c7715011fe33d7eba869ed1d448a75be4730c1d2d2ef881e02f028a241b7d7d3b0d0b4a9c0565fcb49977]
[sha = SHA-256]

# tcId = 310
# u2 == n - 1
msg = 313233343030
result = valid
sig = 303d021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021d00aaaaaaaaaaaaaaaaaaaaaaaaaaaa0f17407b4ad40d3e1b8392e81c29

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 047fcc799b919fe9789ce01dd9202731cb7d815158bc6cb8468760247c0f9d2957e0dd5e4c40124bd5e0dd1be41c038fce2cd1dc814e0af37d]
[key.wx = 7fcc799b919fe9789ce01dd9202731cb7d815158bc6cb8468760247c]
[key.wy = 0f9d2957e0dd5e4c40124bd5e0dd1be41c038fce2cd1dc814e0af37d]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00047fcc799b919fe9789ce01dd9202731cb7d815158bc6cb8468760247c0f9d2957e0dd5e4c40124bd5e0dd1be41c038fce2cd1dc814e0af37d]
[sha = SHA-256]

# tcId = 311
# edge case for u1
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0093c8c651653430cb4f1675fc86b5e82ca04ff2ab1501674476aac169

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 043ddd68f69d0bfd47ad19370fa3dc72eb258268c2b5f3768852151674fbe0e155d94d2373a01a5e70f1a105259e7b8b1d2fdf4dba3cf4c780]
[key.wx = 3ddd68f69d0bfd47ad19370fa3dc72eb258268c2b5f3768852151674]
[key.wy = 00fbe0e155d94d2373a01a5e70f1a105259e7b8b1d2fdf4dba3cf4c780]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00043ddd68f69d0bfd47ad19370fa3dc72eb258268c2b5f3768852151674fbe0e155d94d2373a01a5e70f1a105259e7b8b1d2fdf4dba3cf4c780]
[sha = SHA-256]

# tcId = 312
# edge case for u1
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d009df50acc33b3625a2d5940dd13dbb97d1f7dd56afff8b7de7545127c

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 041cb1f564c29ebf60a342b3bc33c8945cb279c6c1a012255c874e1c37b75191ab3b2bb730914ebfa14080410970b71eaf4fe01e2d48be9891]
[key.wx = 1cb1f564c29ebf60a342b3bc33c8945cb279c6c1a012255c874e1c37]
[key.wy = 00b75191ab3b2bb730914ebfa14080410970b71eaf4fe01e2d48be9891]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00041cb1f564c29ebf60a342b3bc33c8945cb279c6c1a012255c874e1c37b75191ab3b2bb730914ebfa14080410970b71eaf4fe01e2d48be9891]
[sha = SHA-256]

# tcId = 313
# edge case for u1
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00dce8c223f235699d1f5d2dcde4809d013390b59129f783239525c08f

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0444e309eb686e7af7f1e2cc17fd56542b38910b3b7908ea54fb038d36477e829d4c8332e5b29f344ad27a21c18dab24a31ce7985b63a21304]
[key.wx = 44e309eb686e7af7f1e2cc17fd56542b38910b3b7908ea54fb038d36]
[key.wy = 477e829d4c8332e5b29f344ad27a21c18dab24a31ce7985b63a21304]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000444e309eb686e7af7f1e2cc17fd56542b38910b3b7908ea54fb038d36477e829d4c8332e5b29f344ad27a21c18dab24a31ce7985b63a21304]
[sha = SHA-256]

# tcId = 314
# edge case for u1
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c074aae944ee7a7d544a5ad0bd06366f872d2250ba3018a63d2a7f2e6

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04c728064542cb5142f5eefe638124dcd7a1ad0b3555842a47dd5108e110129dd878ebd47313276cec86f521ea9585cd105b3dc421141993b8]
[key.wx = 00c728064542cb5142f5eefe638124dcd7a1ad0b3555842a47dd5108e1]
[key.wy = 10129dd878ebd47313276cec86f521ea9585cd105b3dc421141993b8]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004c728064542cb5142f5eefe638124dcd7a1ad0b3555842a47dd5108e110129dd878ebd47313276cec86f521ea9585cd105b3dc421141993b8]
[sha = SHA-256]

# tcId = 315
# edge case for u1
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00aae944ee7a7d544a5ad0bd0636d9455f4e83de0f186f89bca56b3c5c

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04c46c1ad3d3d0df8e9c0f525c21ce8d81ef9d66297f442d63099667220cfa2253aa31a98d8966b85969bf9c819c019292ef6a53ac1db2a108]
[key.wx = 00c46c1ad3d3d0df8e9c0f525c21ce8d81ef9d66297f442d6309966722]
[key.wy = 0cfa2253aa31a98d8966b85969bf9c819c019292ef6a53ac1db2a108]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004c46c1ad3d3d0df8e9c0f525c21ce8d81ef9d66297f442d63099667220cfa2253aa31a98d8966b85969bf9c819c019292ef6a53ac1db2a108]
[sha = SHA-256]

# tcId = 316
# edge case for u1
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c55d289dcf4faa894b5a17a0c6db3741bbc4ecbe01d01ea33ee7a4e7b

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04b7b2e48c1e60e20925f4d9b6be600dd83786a936c9bfab00639c33caa967cbc65070739a3379da80d54843a18d9c11a29a32234a0b303c12]
[key.wx = 00b7b2e48c1e60e20925f4d9b6be600dd83786a936c9bfab00639c33ca]
[key.wy = 00a967cbc65070739a3379da80d54843a18d9c11a29a32234a0b303c12]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004b7b2e48c1e60e20925f4d9b6be600dd83786a936c9bfab00639c33caa967cbc65070739a3379da80d54843a18d9c11a29a32234a0b303c12]
[sha = SHA-256]

# tcId = 317
# edge case for u1
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c4ee7a7d544a5ad0bd0636d9e12bc561ce04faaf1312bba3a15601ebc

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04f4a3d4598875af7f2741bbd67b1733b6541bc5325b3bcb4d3267c27ec30bf322f58a45c6c2aa2ced55f175d1cbf72a7c5bfc464d74f666c0]
[key.wx = 00f4a3d4598875af7f2741bbd67b1733b6541bc5325b3bcb4d3267c27e]
[key.wy = 00c30bf322f58a45c6c2aa2ced55f175d1cbf72a7c5bfc464d74f666c0]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004f4a3d4598875af7f2741bbd67b1733b6541bc5325b3bcb4d3267c27ec30bf322f58a45c6c2aa2ced55f175d1cbf72a7c5bfc464d74f666c0]
[sha = SHA-256]

# tcId = 318
# edge case for u1
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c361b9cd74d65e79a5874c501bca4973b20347ec97f6de10072d8b46a

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0456d1e5c1d664f6ce2fc1fcb937a7ce231a29486abf36c73f77a2bd116cb282c9d7c6fc05f399c183e880ea362edf043cd28ffac9f94f2141]
[key.wx = 56d1e5c1d664f6ce2fc1fcb937a7ce231a29486abf36c73f77a2bd11]
[key.wy = 6cb282c9d7c6fc05f399c183e880ea362edf043cd28ffac9f94f2141]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000456d1e5c1d664f6ce2fc1fcb937a7ce231a29486abf36c73f77a2bd116cb282c9d7c6fc05f399c183e880ea362edf043cd28ffac9f94f2141]
[sha = SHA-256]

# tcId = 319
# edge case for u1
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c6c3739ae9acbcf34b0e98a0379492e764068fd92fedbc200e5b168d4

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0430bce8c6b7f1bbba040b8d121d85d55167ac99b2e2cf1cfac8b018b5f1c384c35be0ae309a5cb55aba982343d2125f2d4a559d8c545359cd]
[key.wx = 30bce8c6b7f1bbba040b8d121d85d55167ac99b2e2cf1cfac8b018b5]
[key.wy = 00f1c384c35be0ae309a5cb55aba982343d2125f2d4a559d8c545359cd]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000430bce8c6b7f1bbba040b8d121d85d55167ac99b2e2cf1cfac8b018b5f1c384c35be0ae309a5cb55aba982343d2125f2d4a559d8c545359cd]
[sha = SHA-256]

# tcId = 320
# edge case for u1
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00a252d685e831b6cf095e4f0535edc5b1609d7c5c7e49a301588a1d3e

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04e87e538a978cf187908beb27a4a247d496a8421dab1fe79f8744d2b5539b9f8fe8bddcf7c97c44c55a4fc22f4d78f6a961447a5b613b5c49]
[key.wx = 00e87e538a978cf187908beb27a4a247d496a8421dab1fe79f8744d2b5]
[key.wy = 539b9f8fe8bddcf7c97c44c55a4fc22f4d78f6a961447a5b613b5c49]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004e87e538a978cf187908beb27a4a247d496a8421dab1fe79f8744d2b5539b9f8fe8bddcf7c97c44c55a4fc22f4d78f6a961447a5b613b5c49]
[sha = SHA-256]

# tcId = 321
# edge case for u1
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00ee746111f91ab4ce8fae96e6f23fd9d20a24d2e79eea563478c0f566

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04113a2cc57c8ee7de11bc45e14546c72a29725b9a7218114ac31f02816c765b9a46b0215312a3292f5979c98d37b35883baa156281b1bae8c]
[key.wx = 113a2cc57c8ee7de11bc45e14546c72a29725b9a7218114ac31f0281]
[key.wy = 6c765b9a46b0215312a3292f5979c98d37b35883baa156281b1bae8c]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004113a2cc57c8ee7de11bc45e14546c72a29725b9a7218114ac31f02816c765b9a46b0215312a3292f5979c98d37b35883baa156281b1bae8c]
[sha = SHA-256]

# tcId = 322
# edge case for u2
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fffffffffffffffffffffffffffb2364ae85014b149b86c741eb8be

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0423dd9c3f1a4b478b01fa2c5e997d0482073b32918de44be583dcf74ad661a5ed579a2f09d2ff56d6b80f26568d93a237ca6444b0cadc7951]
[key.wx = 23dd9c3f1a4b478b01fa2c5e997d0482073b32918de44be583dcf74a]
[key.wy = 00d661a5ed579a2f09d2ff56d6b80f26568d93a237ca6444b0cadc7951]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000423dd9c3f1a4b478b01fa2c5e997d0482073b32918de44be583dcf74ad661a5ed579a2f09d2ff56d6b80f26568d93a237ca6444b0cadc7951]
[sha = SHA-256]

# tcId = 323
# edge case for u2
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00855f5b2dc8e46ec428a593f73219cf65dae793e8346e30cc3701309c

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04bbce4b17d45d24a1c80bc8eca98c359d5e1e458058a00b950643256dfe09e092318e39303dca03688e4ecf300300784312d617e5088c584c]
[key.wx = 00bbce4b17d45d24a1c80bc8eca98c359d5e1e458058a00b950643256d]
[key.wy = 00fe09e092318e39303dca03688e4ecf300300784312d617e5088c584c]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004bbce4b17d45d24a1c80bc8eca98c359d5e1e458058a00b950643256dfe09e092318e39303dca03688e4ecf300300784312d617e5088c584c]
[sha = SHA-256]

# tcId = 324
# edge case for u2
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c2db5f61aea817276af2064e104c7a30e32034cb526dd0aacfa56566f

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04035f58446c1bdbeaa56660a897ebf965f2d18820c7cd0630f04a495347bdfaea60091f405e09929cb2c0e2f6eed53e0871b7fe0cd5a15d85]
[key.wx = 035f58446c1bdbeaa56660a897ebf965f2d18820c7cd0630f04a4953]
[key.wy = 47bdfaea60091f405e09929cb2c0e2f6eed53e0871b7fe0cd5a15d85]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004035f58446c1bdbeaa56660a897ebf965f2d18820c7cd0630f04a495347bdfaea60091f405e09929cb2c0e2f6eed53e0871b7fe0cd5a15d85]
[sha = SHA-256]

# tcId = 325
# edge case for u2
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0084a6c7513e5f48c07fffffffffff8713f3cba1293e4f3e95597fe6bd

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04911c0033eac46332691cb7920c4950eed57354761e1081a1ea9f1279508ebf7cfd3eab5dabdee1be14ce8296b1fc20acfaac16f7824c6002]
[key.wx = 00911c0033eac46332691cb7920c4950eed57354761e1081a1ea9f1279]
[key.wy = 508ebf7cfd3eab5dabdee1be14ce8296b1fc20acfaac16f7824c6002]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004911c0033eac46332691cb7920c4950eed57354761e1081a1ea9f1279508ebf7cfd3eab5dabdee1be14ce8296b1fc20acfaac16f7824c6002]
[sha = SHA-256]

# tcId = 326
# edge case for u2
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c6c7513e5f48c07ffffffffffffff9d21fd1b31544cb13ca86a75b25e

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0462b2abb70bb9c7efdfb57181f433b64751f108130dce180d6992e7d3124b3aa8a53e5eedf72aa67e6edcc71f19e36e6ad1d099a59ffd9555]
[key.wx = 62b2abb70bb9c7efdfb57181f433b64751f108130dce180d6992e7d3]
[key.wy = 124b3aa8a53e5eedf72aa67e6edcc71f19e36e6ad1d099a59ffd9555]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000462b2abb70bb9c7efdfb57181f433b64751f108130dce180d6992e7d3124b3aa8a53e5eedf72aa67e6edcc71f19e36e6ad1d099a59ffd9555]
[sha = SHA-256]

# tcId = 327
# edge case for u2
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00d8ea27cbe9180fffffffffffffff3a43fa3662a899627950d4eb64bc

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 040f759330e7992752aae6a85f7bb0599784bea53e288ff7ee8d53d5e6defe617362380e92f9a23c4fdcc34e09713aab9cc44119418f6f2fd1]
[key.wx = 0f759330e7992752aae6a85f7bb0599784bea53e288ff7ee8d53d5e6]
[key.wy = 00defe617362380e92f9a23c4fdcc34e09713aab9cc44119418f6f2fd1]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00040f759330e7992752aae6a85f7bb0599784bea53e288ff7ee8d53d5e6defe617362380e92f9a23c4fdcc34e09713aab9cc44119418f6f2fd1]
[sha = SHA-256]

# tcId = 328
# edge case for u2
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3e5f48c07fffffffffffffffffffc724968c0ecf9ed783744a7337b3

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 048f2eda42742ab31f5d4cf666892d1d623efd3b26f7df9aa70296e80d3beaf235cfea41fadb98c533a8fdeb5841d69ee65f6e71914711f138]
[key.wx = 008f2eda42742ab31f5d4cf666892d1d623efd3b26f7df9aa70296e80d]
[key.wy = 3beaf235cfea41fadb98c533a8fdeb5841d69ee65f6e71914711f138]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00048f2eda42742ab31f5d4cf666892d1d623efd3b26f7df9aa70296e80d3beaf235cfea41fadb98c533a8fdeb5841d69ee65f6e71914711f138]
[sha = SHA-256]

# tcId = 329
# edge case for u2
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00bfffffffffffffffffffffffffff3d87bb44c833bb384d0f224ccdde

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 042bcf4371b319a691ed0e2e0c4a55a8a9b987dec86b863621e97b9c095b8660a74cc964a6af0311edc6b1cd980f9c7bf3a6c9b7f9132a0b2f]
[key.wx = 2bcf4371b319a691ed0e2e0c4a55a8a9b987dec86b863621e97b9c09]
[key.wy = 5b8660a74cc964a6af0311edc6b1cd980f9c7bf3a6c9b7f9132a0b2f]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00042bcf4371b319a691ed0e2e0c4a55a8a9b987dec86b863621e97b9c095b8660a74cc964a6af0311edc6b1cd980f9c7bf3a6c9b7f9132a0b2f]
[sha = SHA-256]

# tcId = 330
# edge case for u2
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fffffffffffffffffffffffffff646c95d0a029629370d8e83d717f

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04a6f252568f6fbd1ae045e602344359c0c216911723748f9a3e7fadec3b76efc75ba030bfe7de2ded686991e6183d40241a05b479693c7015]
[key.wx = 00a6f252568f6fbd1ae045e602344359c0c216911723748f9a3e7fadec]
[key.wy = 3b76efc75ba030bfe7de2ded686991e6183d40241a05b479693c7015]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004a6f252568f6fbd1ae045e602344359c0c216911723748f9a3e7fadec3b76efc75ba030bfe7de2ded686991e6183d40241a05b479693c7015]
[sha = SHA-256]

# tcId = 331
# edge case for u2
msg = 313233343030
result = valid
sig = 303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e1520

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04a74c1c3a31c7d493ab2c0af89cf5e688621ca9466d2ba1d8761c3fe82ba0d08f4c9f76856c2b7138c8f1e780b6959992b16ccdfd925f4b3a]
[key.wx = 00a74c1c3a31c7d493ab2c0af89cf5e688621ca9466d2ba1d8761c3fe8]
[key.wy = 2ba0d08f4c9f76856c2b7138c8f1e780b6959992b16ccdfd925f4b3a]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004a74c1c3a31c7d493ab2c0af89cf5e688621ca9466d2ba1d8761c3fe82ba0d08f4c9f76856c2b7138c8f1e780b6959992b16ccdfd925f4b3a]
[sha = SHA-256]

# tcId = 332
# edge case for u2
msg = 313233343030
result = valid
sig = 303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0096dafb0d7540b93b5790327082635cd8895e1e799d5d19f92b594056

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04034ea72798257f33f24f64c49438fc43e8f67ddc7170fd127e2c43f280562acc9b49f2d7fcc89421d2a5db2ea8dd0361fb48d897d4612627]
[key.wx = 034ea72798257f33f24f64c49438fc43e8f67ddc7170fd127e2c43f2]
[key.wy = 0080562acc9b49f2d7fcc89421d2a5db2ea8dd0361fb48d897d4612627]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004034ea72798257f33f24f64c49438fc43e8f67ddc7170fd127e2c43f280562acc9b49f2d7fcc89421d2a5db2ea8dd0361fb48d897d4612627]
[sha = SHA-256]

# tcId = 333
# point duplication during verification
msg = 313233343030
result = valid
sig = 303d021d00c44503dae85dd5210780f02928b3d927171c578f8603d16b240663c7021c3f552f1c2b01651edf5902650fe9ab046f71999ac928edc0087bdb13
flags = PointDuplication

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04034ea72798257f33f24f64c49438fc43e8f67ddc7170fd127e2c43f27fa9d53364b60d2803376bde2d5a24d05722fc9e04b727682b9ed9da]
[key.wx = 034ea72798257f33f24f64c49438fc43e8f67ddc7170fd127e2c43f2]
[key.wy = 7fa9d53364b60d2803376bde2d5a24d05722fc9e04b727682b9ed9da]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004034ea72798257f33f24f64c49438fc43e8f67ddc7170fd127e2c43f27fa9d53364b60d2803376bde2d5a24d05722fc9e04b727682b9ed9da]
[sha = SHA-256]

# tcId = 334
# duplication bug
msg = 313233343030
result = invalid
sig = 303d021d00c44503dae85dd5210780f02928b3d927171c578f8603d16b240663c7021c3f552f1c2b01651edf5902650fe9ab046f71999ac928edc0087bdb13
flags = PointDuplication

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 043672ba9718e60d00eab4295c819ea366a778dd6fd621fa9665259cb67ae5e847eeaea674beeb636379e968f79265502e414a1d444f04ae79]
[key.wx = 3672ba9718e60d00eab4295c819ea366a778dd6fd621fa9665259cb6]
[key.wy = 7ae5e847eeaea674beeb636379e968f79265502e414a1d444f04ae79]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00043672ba9718e60d00eab4295c819ea366a778dd6fd621fa9665259cb67ae5e847eeaea674beeb636379e968f79265502e414a1d444f04ae79]
[sha = SHA-256]

# tcId = 335
# comparison with point at infinity 
msg = 313233343030
result = invalid
sig = 303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0433eeefbfc77229136e56b575144863ed90b4c0f8a9e315816d6de648051749dd11480c141fb5a1946313163c0141265b68a26216bcb9936a]
[key.wx = 33eeefbfc77229136e56b575144863ed90b4c0f8a9e315816d6de648]
[key.wy = 051749dd11480c141fb5a1946313163c0141265b68a26216bcb9936a]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000433eeefbfc77229136e56b575144863ed90b4c0f8a9e315816d6de648051749dd11480c141fb5a1946313163c0141265b68a26216bcb9936a]
[sha = SHA-256]

# tcId = 336
# extreme value for k and edgecase s
msg = 313233343030
result = valid
sig = 303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04bda03b24b62243c61e288b6ea1e99a2886f700944eb1b8f0466cffd61c712a3aaace69331989b707e69e8de39d7cd1aeb65d97ad1800bf7f]
[key.wx = 00bda03b24b62243c61e288b6ea1e99a2886f700944eb1b8f0466cffd6]
[key.wy = 1c712a3aaace69331989b707e69e8de39d7cd1aeb65d97ad1800bf7f]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004bda03b24b62243c61e288b6ea1e99a2886f700944eb1b8f0466cffd61c712a3aaace69331989b707e69e8de39d7cd1aeb65d97ad1800bf7f]
[sha = SHA-256]

# tcId = 337
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 303d021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021d00db6db6db6db6db6db6db6db6db6ceed4c09e84c77ebd9116e17391eb

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 047abba0cbff134ddcf54d04846f954b882ca9faefdfe818898bfb378b792f10b57970ae57bb4fb01c08886848855aeb1984d3d6fcb2b412df]
[key.wx = 7abba0cbff134ddcf54d04846f954b882ca9faefdfe818898bfb378b]
[key.wy = 792f10b57970ae57bb4fb01c08886848855aeb1984d3d6fcb2b412df]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00047abba0cbff134ddcf54d04846f954b882ca9faefdfe818898bfb378b792f10b57970ae57bb4fb01c08886848855aeb1984d3d6fcb2b412df]
[sha = SHA-256]

# tcId = 338
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04f68d99e28653b9ba3e7cedb3b78165f5a54fbe90d4b9f88497977e16234da3eaa0178a51b5b0c208ef0818df6f6578793c1af1787026b8da]
[key.wx = 00f68d99e28653b9ba3e7cedb3b78165f5a54fbe90d4b9f88497977e16]
[key.wy = 234da3eaa0178a51b5b0c208ef0818df6f6578793c1af1787026b8da]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004f68d99e28653b9ba3e7cedb3b78165f5a54fbe90d4b9f88497977e16234da3eaa0178a51b5b0c208ef0818df6f6578793c1af1787026b8da]
[sha = SHA-256]

# tcId = 339
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 303d021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021d00cccccccccccccccccccccccccccc121be6fa59cb431754377d168831

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04065d9ef133ce81c2d6b66e928360f9527f8f36b5badd35b5f10934272004852755f77440a0b08b9f165489c0696e8b4981d6d04a285b0fd1]
[key.wx = 065d9ef133ce81c2d6b66e928360f9527f8f36b5badd35b5f1093427]
[key.wy = 2004852755f77440a0b08b9f165489c0696e8b4981d6d04a285b0fd1]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004065d9ef133ce81c2d6b66e928360f9527f8f36b5badd35b5f10934272004852755f77440a0b08b9f165489c0696e8b4981d6d04a285b0fd1]
[sha = SHA-256]

# tcId = 340
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c249249249249249249249249249227ce201a6b76951f982e7ae89852

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04d6cea09472ede574ce1e0546c9acd0e1cd8cba9b121df29e89d5092e83904ebfb902ea61c987dc0508e0c9a7e563e2609feaf79140ab91d6]
[key.wx = 00d6cea09472ede574ce1e0546c9acd0e1cd8cba9b121df29e89d5092e]
[key.wy = 0083904ebfb902ea61c987dc0508e0c9a7e563e2609feaf79140ab91d6]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004d6cea09472ede574ce1e0546c9acd0e1cd8cba9b121df29e89d5092e83904ebfb902ea61c987dc0508e0c9a7e563e2609feaf79140ab91d6]
[sha = SHA-256]

# tcId = 341
# extreme value for k
msg = 313233343030
result = valid
sig = 303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c0eb10e5ab95facded4061029d63a46f46f12947411f2ea561a592057

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04c520b18003b356094147ee2f9df1178572bed837bd89443b25ebceb80e2e93a998fbbabe82192ea4c85651cf09a95ab0dc2e3d975ee7be98]
[key.wx = 00c520b18003b356094147ee2f9df1178572bed837bd89443b25ebceb8]
[key.wy = 0e2e93a998fbbabe82192ea4c85651cf09a95ab0dc2e3d975ee7be98]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004c520b18003b356094147ee2f9df1178572bed837bd89443b25ebceb80e2e93a998fbbabe82192ea4c85651cf09a95ab0dc2e3d975ee7be98]
[sha = SHA-256]

# tcId = 342
# extreme value for k and edgecase s
msg = 313233343030
result = valid
sig = 303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 049dd0b99bb7a830bcc7d55abac42912d525b063c50cf377ca5771a26ca141fccf0793c2ba2469a946c2d4ed26344052c63a6d7e7797ce96c3]
[key.wx = 009dd0b99bb7a830bcc7d55abac42912d525b063c50cf377ca5771a26c]
[key.wy = 00a141fccf0793c2ba2469a946c2d4ed26344052c63a6d7e7797ce96c3]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00049dd0b99bb7a830bcc7d55abac42912d525b063c50cf377ca5771a26ca141fccf0793c2ba2469a946c2d4ed26344052c63a6d7e7797ce96c3]
[sha = SHA-256]

# tcId = 343
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 303e021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021d00db6db6db6db6db6db6db6db6db6ceed4c09e84c77ebd9116e17391eb

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 043dab9f1b19e715d174a7360920375d569a181f055e66f01391871b6f47a6d87c23a5b6a1e3d0a9721302cc02cce35f35dea08e22619be521]
[key.wx = 3dab9f1b19e715d174a7360920375d569a181f055e66f01391871b6f]
[key.wy = 47a6d87c23a5b6a1e3d0a9721302cc02cce35f35dea08e22619be521]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00043dab9f1b19e715d174a7360920375d569a181f055e66f01391871b6f47a6d87c23a5b6a1e3d0a9721302cc02cce35f35dea08e22619be521]
[sha = SHA-256]

# tcId = 344
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0456dde1ba58ea31053b2535c66623344c24c72d214af5be6982e89100e771084806143e86f2b31bdaf62280f5b311d0d2bdbb385b20fc6c87]
[key.wx = 56dde1ba58ea31053b2535c66623344c24c72d214af5be6982e89100]
[key.wy = 00e771084806143e86f2b31bdaf62280f5b311d0d2bdbb385b20fc6c87]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000456dde1ba58ea31053b2535c66623344c24c72d214af5be6982e89100e771084806143e86f2b31bdaf62280f5b311d0d2bdbb385b20fc6c87]
[sha = SHA-256]

# tcId = 345
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 303e021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021d00cccccccccccccccccccccccccccc121be6fa59cb431754377d168831

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0494efe1387fc0447d7dbcb53739a0e4e0ddec181d382caea645b1a6124414a6b1c78908d0fa206f8f2de950ad4a14d1ce94d9cddbe32e4601]
[key.wx = 0094efe1387fc0447d7dbcb53739a0e4e0ddec181d382caea645b1a612]
[key.wy = 4414a6b1c78908d0fa206f8f2de950ad4a14d1ce94d9cddbe32e4601]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000494efe1387fc0447d7dbcb53739a0e4e0ddec181d382caea645b1a6124414a6b1c78908d0fa206f8f2de950ad4a14d1ce94d9cddbe32e4601]
[sha = SHA-256]

# tcId = 346
# extreme value for k and s^-1
msg = 313233343030
result = valid
sig = 303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c249249249249249249249249249227ce201a6b76951f982e7ae89852

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 046286803b952976ee1897013695d3ef2cbb6f977142a042b236572577722a6ce9ad3e3fd28e451833496c63b8ab70538877215f204942bf59]
[key.wx = 6286803b952976ee1897013695d3ef2cbb6f977142a042b236572577]
[key.wy = 722a6ce9ad3e3fd28e451833496c63b8ab70538877215f204942bf59]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00046286803b952976ee1897013695d3ef2cbb6f977142a042b236572577722a6ce9ad3e3fd28e451833496c63b8ab70538877215f204942bf59]
[sha = SHA-256]

# tcId = 347
# extreme value for k
msg = 313233343030
result = valid
sig = 303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c0eb10e5ab95facded4061029d63a46f46f12947411f2ea561a592057

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34]
[key.wx = 00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21]
[key.wy = 00bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34]
[sha = SHA-256]

# tcId = 348
# testing point duplication
msg = 313233343030
result = invalid
sig = 303d021d00bb5a52f42f9c9261ed4361f59422a1e30036e7c32b270c8807a419fe021c249249249249249249249249249227ce201a6b76951f982e7ae89851

# tcId = 349
# testing point duplication
msg = 313233343030
result = invalid
sig = 303c021c44a5ad0bd0636d9e12bc9e0a6bdc74bfe082087ae8b61cbd54b8103f021c249249249249249249249249249227ce201a6b76951f982e7ae89851

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d2142c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd]
[key.wx = 00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21]
[key.wy = 42c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d2142c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd]
[sha = SHA-256]

# tcId = 350
# testing point duplication
msg = 313233343030
result = invalid
sig = 303d021d00bb5a52f42f9c9261ed4361f59422a1e30036e7c32b270c8807a419fe021c249249249249249249249249249227ce201a6b76951f982e7ae89851

# tcId = 351
# testing point duplication
msg = 313233343030
result = invalid
sig = 303c021c44a5ad0bd0636d9e12bc9e0a6bdc74bfe082087ae8b61cbd54b8103f021c249249249249249249249249249227ce201a6b76951f982e7ae89851

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 044c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176]
[key.wx = 4c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466]
[key.wy = 00ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a00044c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176]
[sha = SHA-256]

# tcId = 352
# pseudorandom signature
msg = 
result = valid
sig = 303d021c0364e7d96832614a80216e730c353534d4bffd2c26649c0b4b0e2628021d008f40064b412fe38c5ba9cf664e6172ed48e6e79f0fe5e31a54985dfc

# tcId = 353
# pseudorandom signature
msg = 4d7367
result = valid
sig = 303d021d00f4b68df62b9238363ccc1bbee00deb3fb2693f7894178e14eeac596a021c7f51c9451adacd2bcbc721f7df0643d7cd18a6b52064b507e1912f23

# tcId = 354
# pseudorandom signature
msg = 313233343030
result = valid
sig = 303d021d00b2970cdec29c70294a18bbc49985efa33acc0af509c326a3977a35e8021c0cea3ed8ebaaf6ee6aef6049a23cbc39f61fcf8fc6be4bab13385579

# tcId = 355
# pseudorandom signature
msg = 0000000000000000000000000000000000000000
result = valid
sig = 303d021c7e7b0eb7da8c68a7072b11404ee95a5c407fbfe3d69646802e28ae77021d00d409a2f6bbaae59bb60fc0a092b12fa4e67dc8d088cf19a833322fd6

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf8a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000]
[key.wx = 00aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf]
[key.wy = 008a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf8a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000]
[sha = SHA-256]

# tcId = 356
# y-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 303c021c519bf185ff4635271961fa491be257231deeea9c53a6ede3b4a89ed1021c486bdad484a6a3134e1471cf56a9df0fac50f773b3e37d6f327617d7

# tcId = 357
# y-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 303d021c09fd644898b7cb5d018b52234e7b4ef2b54789afd0ce9c434e9e5515021d00f19309532164ea2053cae55df7bdcbab536c83ea7bfe6fe10d60c1ab

# tcId = 358
# y-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 303d021d00ec919d4e283ccf1f71a9e3c0f781a36758d3f38b1b78a87a74288e80021c4c4663044a73c79bd88f0dc245ab1a32f89f06f40a704b31e9fabc51

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f173d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff]
[key.wx = 00bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f1]
[key.wy = 73d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f173d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff]
[sha = SHA-256]

# tcId = 359
# y-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 303e021d00c51760478447217597ecc6f4001bd45088d53c90f53103608bf88aea021d00a201253aa903f9781e8992101d7171d2dd3a5d48c44d8e1d544cd6d7

# tcId = 360
# y-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 303c021c76be0112674ec29128823e1af7512e6143872fef30a64e2f1799bd56021c187e503e1a48c27b549fe0a4ce5e581e242c8663fc9efb02d6f2b193

# tcId = 361
# y-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 303c021c36245ef126b5b51e459f84eaaad5a495061f0471dc8c23f1c5f16282021c39e31d72a06ba8e14fcf95778e07bc16a2628e39449da8857d506edc

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0426e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0]
[key.wx = 26e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000]
[key.wy = 00eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000426e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0]
[sha = SHA-256]

# tcId = 362
# x-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 303c021c258682975df8bca7f203f771ebeb478ef637360c860fc386cfb21745021c7663e70188047e41469a2a35c8c330dd900f2340ba82aafd22962a96

# tcId = 363
# x-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 303e021d0085c98614f36c0d66f8d87834cae978611b7b4eebf59a46bea1b89ae9021d00d1a18e378dda840e06b60f6279bf0a2231d9fa2d8d2c31e88bc1bdd7

# tcId = 364
# x-coordinate of the public key has many trailing 0's
msg = 4d657373616765
result = valid
sig = 303e021d00ca7b7432ba41ff2112e1116fffde89bbd68f5ce67fe5513d16c8e6f7021d00e421b7599e0180798acc2006451603cda2db1d582741116e6033ce5f

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd]
[key.wx = 00ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff]
[key.wy = 41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd]
[sha = SHA-256]

# tcId = 365
# x-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 303c021c19397fe5d3ecabf80fc624c1bf379564387517c185087dc97d605069021c33b5773e9aaf6c34cb612cfc81efd3bf9c22224e8c4fa1bfccf5c501

# tcId = 366
# x-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 303d021c70f24f5c164164bfbb8459aa12a981aa312dbcf00204326ebaaabdc8021d00f5cebee8caedae8662c43501665084b45d2f494fb70d603043543dc4

# tcId = 367
# x-coordinate of the public key has many trailing 1's
msg = 4d657373616765
result = valid
sig = 303d021c0bf2d86ecaa8b56aca5e8f8ebcb45081d078a14555b75f5be8e9b132021d009a55b3ce4734849966b5034ccd9b19f76407ee0241c3f58e7b8fc89a

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a00000000762d28f1fdc219184f81681fbff566d465b5f1f31e872df5]
[key.wx = 15016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a]
[key.wy = 762d28f1fdc219184f81681fbff566d465b5f1f31e872df5]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a00000000762d28f1fdc219184f81681fbff566d465b5f1f31e872df5]
[sha = SHA-256]

# tcId = 368
# y-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303e021d00bfc5dc4434cd09369610687d38d2d418b63fd475dea246a456b25a3a021d00b171dfa6cf722f20816370a868785da842b37bac31d7b78e6751fc50

# tcId = 369
# y-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303e021d008fdbe8da646c5642d767c7dbeb3872b1edab6e37365805f0e94ce0a9021d00bcf35ab81222883dd3526cb0cf93138f4687cd0b10c2b0a126385161

# tcId = 370
# y-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303d021d00e23a11275848fd4f8b6f4ac4fc305eae981d3b7dc453e5a980c46422021c1a875693f24a03ea1614c4c3bbd0dd7221429f22b337ea7d98348ca4

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555affffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c]
[key.wx = 15016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a]
[key.wy = 00ffffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555affffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c]
[sha = SHA-256]

# tcId = 371
# y-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303d021c76645164ff9af3a1a9205fda2eef326d2bffc795dcc4829547fe01dd021d00b65bba503719314b27734dd06b1395d540af8396029b78b84e0149eb

# tcId = 372
# y-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303c021c32fa0ca7e07f1f86ac350734994e1f31b6da9c82f93dced2b983c29c021c7b7891282206a45711bdfcb2a102b5d289df84ff5778548603574004

# tcId = 373
# y-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303d021c2d5492478ca64e5111dfd8521867b6477b7e78227849ad090b855694021d00a532f5a2fa3594af81cd5928b81b4057da717be5fb42a3a86c68190d

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 0400000000f7e4713d085112112c37cdf4601ff688da796016b71a727ade5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1]
[key.wx = 00f7e4713d085112112c37cdf4601ff688da796016b71a727a]
[key.wy = 00de5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a000400000000f7e4713d085112112c37cdf4601ff688da796016b71a727ade5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1]
[sha = SHA-256]

# tcId = 374
# x-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303d021c191eee5daf55cd499e8539cb2cff797cfec5d566d2027bf9f8d64693021d00dadfeae8131f64d96b94fd340197caa2bc04818554812feef3343070

# tcId = 375
# x-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303e021d00e0e2c08180b8a207ee9105a7d379fa112368e8370fa09dfde4a45c45021d00c717bc0860e016e7ce48f8fe6a299b36906a6055adad93b416ce8838

# tcId = 376
# x-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303d021c1b919ef93532292743bb2e1b7b4894fd847c6e5de52a08e1b0f2dcfb021d00c2d30d6b7594d8dbd261491ae1d58779505b075b64e5564dc97a418b

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f72586c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e]
[key.wx = 00ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f725]
[key.wy = 0086c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f72586c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e]
[sha = SHA-256]

# tcId = 377
# x-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303d021d00e75db49ed33ff2885ea6100cc95b8fe1b9242ea4248db07bcac2e020021c796c866142ae8eb75bb0499c668c6fe45497692fbcc66b37c2e4624f

# tcId = 378
# x-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303c021c1f81cd924362ec825890307b9b3936e0d8f728a7c84bdb43c5cf0433021c39d3e46a03040ad41ac026b18e0629f6145e3dc8d1e6bbe200c8482b

# tcId = 379
# x-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303c021c00fda613aa67ca42673ad4309f3f0f05b2569f3dee63f4aa9cc54cf3021c1e5a64b68a37e5b201c918303dc7a40439aaeacf019c5892a8f6d0ce

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1000000000e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc]
[key.wx = 00b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1]
[key.wy = 0e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1000000000e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc]
[sha = SHA-256]

# tcId = 380
# y-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303e021d00b932b3f7e6467e1ec7a561f31160248c7f224550a8508788634b53ce021d00a0c5312acf9e801aff6d6fc98550cfa712bbf65937165a36f2c32dc9

# tcId = 381
# y-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303d021d00e509593fb09245ee8226ce72786b0cc352be555a7486be628f4fd00c021c0b7abde0061b1e07bf13319150a4ff6a464abab636ab4e297b0d7633

# tcId = 382
# y-coordinate of the public key is small
msg = 4d657373616765
result = valid
sig = 303c021c6e54f941204d4639b863c98a65b7bee318d51ab1900a8f345eac6f07021c0da5054829214ecde5e10579b36a2fe6426c24b064ed77c38590f25c

[key.curve = secp224r1]
[key.keySize = 224]
[key.type = EcPublicKey]
[key.uncompressed = 04b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945]
[key.wx = 00b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1]
[key.wy = 00fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945]
[keyDer = 304e301006072a8648ce3d020106052b81040021033a0004b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945]
[sha = SHA-256]

# tcId = 383
# y-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303d021d0085ea4ab3ffdc992330c0ca8152faf991386bce82877dbb239ba654f6021c0806c6baf0ebea4c1aaa190e7d4325d46d1f7789d550632b70b5fc9b

# tcId = 384
# y-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303d021c44d53debb646b73485402eab2d099081b97b1243c025b624f0dd67ea021d00e5de789a7d4b77eac6d7bba41658e6e4dc347dabed2f9680c04a6f55

# tcId = 385
# y-coordinate of the public key is large
msg = 4d657373616765
result = valid
sig = 303c021c1526eb2f657ebea9af4ca184b975c02372c88e24e835f3f5774c0e12021c1f1ecce38ee52372cb201907794de17b6d6c1afa13c316c51cb07bc7

