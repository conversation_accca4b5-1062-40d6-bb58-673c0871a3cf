{"algorithm": "ECDH", "generatorVersion": "0.8r12", "numberOfTests": 216, "header": ["Test vectors of type EcdhWebTest are intended for", "testing an ECDH implementations where the public key", "is just an ASN encoded point."], "notes": {"AddSubChain": "The private key has a special value. Implementations using addition subtraction chains for the point multiplication may get the point at infinity as an intermediate result. See CVE_2017_10176", "CompressedPoint": "The point in the public key is compressed. Not every library supports points in compressed format."}, "schema": "ecdh_ecpoint_test_schema.json", "testGroups": [{"curve": "secp256r1", "encoding": "ecpoint", "type": "EcdhEcpointTest", "tests": [{"tcId": 1, "comment": "normal case", "public": "0462d5bd3372af75fe85a040715d0f502428e07046868b0bfdfa61d731afe44f26ac333a93a9e70a81cd5a95b5bf8d13990eb741c8c38872b4a07d275a014e30cf", "private": "0612465c89a023ab17855b0a6bcebfd3febb53aef84138647b5352e02c10c346", "shared": "53020d908b0219328b658b525f26780e3ae12bcd952bb25a93bc0895e1714285", "result": "valid", "flags": []}, {"tcId": 2, "comment": "compressed public key", "public": "0362d5bd3372af75fe85a040715d0f502428e07046868b0bfdfa61d731afe44f26", "private": "0612465c89a023ab17855b0a6bcebfd3febb53aef84138647b5352e02c10c346", "shared": "53020d908b0219328b658b525f26780e3ae12bcd952bb25a93bc0895e1714285", "result": "acceptable", "flags": ["CompressedPoint"]}, {"tcId": 3, "comment": "edge case for shared secret", "public": "0458fd4168a87795603e2b04390285bdca6e57de6027fe211dd9d25e2212d29e62080d36bd224d7405509295eed02a17150e03b314f96da37445b0d1d29377d12c", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "valid", "flags": []}, {"tcId": 4, "comment": "edge case for shared secret", "public": "040f6d20c04261ecc3e92846acad48dc8ec5ee35ae0883f0d2ea71216906ee1c47c042689a996dd12830ae459382e94aac56b717af2e2080215f9e41949b1f52be", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "00000000000000000000000000000000ffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 5, "comment": "edge case for shared secret", "public": "0400c7defeb1a16236738e9a1123ba621bc8e9a3f2485b3f8ffde7f9ce98f5a8a1cb338c3912b1792f60c2b06ec5231e2d84b0e596e9b76d419ce105ece3791dbc", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "0000000000000000ffffffffffffffff00000000000000010000000000000001", "result": "valid", "flags": []}, {"tcId": 6, "comment": "edge case for shared secret", "public": "04e9b98fb2c0ac045f8c76125ffd99eb8a5157be1d7db3e85d655ec1d8210288cf218df24fd2c2746be59df41262ef3a97d986744b2836748a7486230a319ffec0", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "00000000ffffffff00000000ffffffff00000000ffffffff0000000100000000", "result": "valid", "flags": []}, {"tcId": 7, "comment": "edge case for shared secret", "public": "04e9484e58f3331b66ffed6d90cb1c78065fa28cfba5c7dd4352013d3252ee4277bd7503b045a38b4b247b32c59593580f39e6abfa376c3dca20cf7f9cfb659e13", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff", "result": "valid", "flags": []}, {"tcId": 8, "comment": "edge case for shared secret", "public": "04767d7fbb84aa6a4db1079372644e42ecb2fec200c178822392cb8b950ffdd0c91c86853cafd09b52ba2f287f0ebaa26415a3cfabaf92c6a617a19988563d9dea", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff00010001", "result": "valid", "flags": []}, {"tcId": 9, "comment": "edge case for shared secret", "public": "04c74d546f2fcc6dd392f85e5be167e358de908756b0c0bb01cb69d864ca083e1c93f959eece6e10ee11bd3934207d65ae28af68b092585a1509260eceb39b92ef", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "085ec5a4af40176b63189069aeffcb229c96d3e046e0283ed2f9dac21b15ad3c", "result": "valid", "flags": []}, {"tcId": 10, "comment": "edge case for shared secret", "public": "0434fc9f1e7a094cd29598d1841fa9613dbe82313d633a51d63fb6eff074cc9b9a4ecfd9f258c5c4d4210b49751213a24c596982bd1d54e0445443f21ef15492a5", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "190c25f88ad9ae3a098e6cffe6fd0b1bea42114eb0cedd5868a45c5fe277dff3", "result": "valid", "flags": []}, {"tcId": 11, "comment": "edge case for shared secret", "public": "04d5c96efd1907fd48de2ad715acf82eae5c6690fe3efe16a78d61c68d3bfd10df03eac816b9e7b776192a3f5075887c0e225617505833ca997cda32fd0f673c5e", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "507442007322aa895340cba4abc2d730bfd0b16c2c79a46815f8780d2c55a2dd", "result": "valid", "flags": []}, {"tcId": 12, "comment": "edge case for shared secret", "public": "04f475f503a770df72c45aedfe42c008f59aa57e72b232f26600bdd0353957cb20bdb8f6405b4918050a3549f44c07a8eba820cdce4ece699888c638df66f54f7c", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "5f177bfe19baaaee597e68b6a87a519e805e9d28a70cb72fd40f0fe5a754ba45", "result": "valid", "flags": []}, {"tcId": 13, "comment": "edge case for shared secret", "public": "04f3cb6754b7e2a86d064dfb9f903185aaa4c92b481c2c1a1ff276303bbc4183e49c318599b0984c3563df339311fe143a7d921ee75b755a52c6f804f897b809f7", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "7fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff", "result": "valid", "flags": []}, {"tcId": 14, "comment": "edge case for shared secret", "public": "04cce13fbdc96a946dfb8c6d9ed762dbd1731630455689f57a437fee124dd54cecaef78026c653030cf2f314a67064236b0a354defebc5e90c94124e9bf5c4fc24", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "8000000000000000000000000000000000000000000000000000000000000004", "result": "valid", "flags": []}, {"tcId": 15, "comment": "edge case for shared secret", "public": "047633dfd0ad06765097bc11bd5022b200df31f28c4ff0625421221ac7eeb6e6f4cb9c67693609ddd6f92343a5a1c635408240f4f8e27120c12554c7ff8c76e2fe", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "8000003ffffff0000007fffffe000000ffffffc000001ffffff8000004000000", "result": "valid", "flags": []}, {"tcId": 16, "comment": "edge case for shared secret", "public": "04a386ace573f87558a68ead2a20088e3fe928bdae9e109446f93a078c15741f0421261e6db2bf12106e4c6bf85b9581b4c0302a526222f90abc5a549206b11011", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "ff00000001fffffffc00000007fffffff00000001fffffffc00000007fffffff", "result": "valid", "flags": []}, {"tcId": 17, "comment": "edge case for shared secret", "public": "048e7b50f7d8c44d5d3496c43141a502f4a43f153d03ad43eda8e39597f1d477b8647f3da67969b7f989ff4addc393515af40c82085ce1f2ee195412c6f583774f", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "ffff00000003fffffff00000003fffffff00000003fffffff00000003fffffff", "result": "valid", "flags": []}, {"tcId": 18, "comment": "edge case for shared secret", "public": "04c827fb930fd51d926086191b502af83abb5f717debc8de29897a3934b2571ca05990c0597b0b7a2e42febd56b13235d1d408d76ed2c93b3facf514d902f6910a", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "ffffffff00000000000000ffffffffffffff00000000000000ffffffffffffff", "result": "valid", "flags": []}, {"tcId": 19, "comment": "y-coordinate of the public key is small", "public": "043cbc1b31b43f17dc200dd70c2944c04c6cb1b082820c234a300b05b7763844c74fde0a4ef93887469793270eb2ff148287da9265b0334f9e2609aac16e8ad503", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "7fffffffffffffffffffffffeecf2230ffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 20, "comment": "y-coordinate of the public key is small", "public": "042830d96489ae24b79cad425056e82746f9e3f419ab9aa21ca1fbb11c7325e7d318abe66f575ee8a2f1c4a80e35260ae82ad7d6f661d15f06967930a585097ef7", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "000000000000000000000000111124f400000000000000000000000000000000", "result": "valid", "flags": []}, {"tcId": 21, "comment": "y-coordinate of the public key is small", "public": "04450b6b6e2097178e9d2850109518d28eb3b6ded2922a5452003bc2e4a4ec775c894e90f0df1b0e6cadb03b9de24f6a22d1bd0a4a58cd645c273cae1c619bfd61", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "000000000000000000000001ea77d449ffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 22, "comment": "y-coordinate of the public key is large", "public": "043cbc1b31b43f17dc200dd70c2944c04c6cb1b082820c234a300b05b7763844c7b021f5b006c778ba686cd8f14d00eb7d78256d9b4fccb061d9f6553e91752afc", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "7fffffffffffffffffffffffeecf2230ffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 23, "comment": "y-coordinate of the public key is large", "public": "042830d96489ae24b79cad425056e82746f9e3f419ab9aa21ca1fbb11c7325e7d3e754198fa8a1175e0e3b57f1cad9f517d528290a9e2ea0f96986cf5a7af68108", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "000000000000000000000000111124f400000000000000000000000000000000", "result": "valid", "flags": []}, {"tcId": 24, "comment": "y-coordinate of the public key is large", "public": "04450b6b6e2097178e9d2850109518d28eb3b6ded2922a5452003bc2e4a4ec775c76b16f0e20e4f194524fc4621db095dd2e42f5b6a7329ba3d8c351e39e64029e", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "000000000000000000000001ea77d449ffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 25, "comment": "y-coordinate of the public key has many trailing 1's", "public": "049a0f0e3dd31417bbd9e298bc068ab6d5c36733af26ed67676f410c804b8b2ca1b02c82f3a61a376db795626e9400557112273a36cddb08caaa43953965454730", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "7fffffffffffffffffffffffca089011ffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 26, "comment": "y-coordinate of the public key has many trailing 1's", "public": "048e5d22d5e53ec797c55ecd68a08a7c3361cd99ca7fad1a68ea802a6a4cb58a918ea7a07023ef67677024bd3841e187c64b30a30a3750eb2ee873fbe58fa1357b", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "0000000000000000000000001f6bd1e500000000000000000000000000000000", "result": "valid", "flags": []}, {"tcId": 27, "comment": "y-coordinate of the public key has many trailing 1's", "public": "04293aa349b934ab2c839cf54b8a737df2304ef9b20fa494e31ad62b315dd6a53c118182b85ef466eb9a8e87f9661f7d017984c15ea82043f536d1ee6a6d95b509", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "000000000000000000000002099f55d5ffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 28, "comment": "y-coordinate of the public key has many trailing 0's", "public": "049a0f0e3dd31417bbd9e298bc068ab6d5c36733af26ed67676f410c804b8b2ca14fd37d0b59e5c893486a9d916bffaa8eedd8c5ca3224f73555bc6ac69abab8cf", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "7fffffffffffffffffffffffca089011ffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 29, "comment": "y-coordinate of the public key has many trailing 0's", "public": "048e5d22d5e53ec797c55ecd68a08a7c3361cd99ca7fad1a68ea802a6a4cb58a9171585f8edc1098998fdb42c7be1e7839b4cf5cf6c8af14d1178c041a705eca84", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "0000000000000000000000001f6bd1e500000000000000000000000000000000", "result": "valid", "flags": []}, {"tcId": 30, "comment": "y-coordinate of the public key has many trailing 0's", "public": "04293aa349b934ab2c839cf54b8a737df2304ef9b20fa494e31ad62b315dd6a53cee7e7d46a10b99156571780699e082fe867b3ea257dfbc0ac92e1195926a4af6", "private": "0a0d622a47e48f6bc1038ace438c6f528aa00ad2bd1da5f13ee46bf5f633d71a", "shared": "000000000000000000000002099f55d5ffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 31, "comment": "edge cases for ephemeral key", "public": "04000000000000000000000000000000000000000000000000000000000000000066485c780e2f83d72433bd5d84a06bb6541c2af31dae871728bf856a174f93f4", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "cfe4077c8730b1c9384581d36bff5542bc417c9eff5c2afcb98cc8829b2ce848", "result": "valid", "flags": []}, {"tcId": 32, "comment": "edge cases for ephemeral key", "public": "0400000000000000000000000000000000ffffffffffffffffffffffffffffffff4f2b92b4c596a5a47f8b041d2dea6043021ac77b9a80b1343ac9d778f4f8f733", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "49ae50fe096a6cd26698b78356b2c8adf1f6a3490f14e364629f7a0639442509", "result": "valid", "flags": []}, {"tcId": 33, "comment": "edge cases for ephemeral key", "public": "040000000000000000ffffffffffffffff0000000000000001000000000000000138120be6ab31edfa34768c4387d2f84fb4b0be8a9a985864a1575f4436bb37b0", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "5a1334572b2a711ead8b4653eb310cd8d9fd114399379a8f6b872e3b8fdda2d9", "result": "valid", "flags": []}, {"tcId": 34, "comment": "edge cases for ephemeral key", "public": "0400000000ffffffff00000000ffffffff00000000ffffffff0000000100000000462c0466e41802238d6c925ecbefc747cfe505ea196af9a2d11b62850fce946e", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "c73755133b6b9b4b2a00631cbc7940ecbe6ec08f20448071422e3362f2556888", "result": "valid", "flags": []}, {"tcId": 35, "comment": "edge cases for ephemeral key", "public": "04000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff1582fa32e2d4a89dfcfb3d0b149f667dba3329490f4d64ee2ad586c0c9e8c508", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "06fa1059935e47a9fd667e13f469614eb257cc9a7e3fc599bfb92780d59b146d", "result": "valid", "flags": []}, {"tcId": 36, "comment": "edge cases for ephemeral key", "public": "040000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff00010001684c8a9586ed6f9cbe447058a7da2108bab1e5e0a60d1f73e4e2e713f0a3dfe0", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "f237df4c10bd3e357971bb2b16b293566b7e355bdc8141d6c92cabc682983c45", "result": "valid", "flags": []}, {"tcId": 37, "comment": "edge cases for ephemeral key", "public": "04085ec5a4af40176b63189069aeffcb229c96d3e046e0283ed2f9dac21b15ad3c7859f97cb6e203f46bf3438f61282325e94e681b60b5669788aeb0655bf19d38", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "d874b55678d0a04d216c31b02f3ad1f30c92caaf168f34e3a743356d9276e993", "result": "valid", "flags": []}, {"tcId": 38, "comment": "edge cases for ephemeral key", "public": "04190c25f88ad9ae3a098e6cffe6fd0b1bea42114eb0cedd5868a45c5fe277dff321b8342ef077bc6724112403eaee5a15b4c31a71589f02ded09cd99cc5db9c83", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "11a8582057463fc76fda3ab8087eb0a420b0d601bb3134165a369646931e52a6", "result": "valid", "flags": []}, {"tcId": 39, "comment": "edge cases for ephemeral key", "public": "04507442007322aa895340cba4abc2d730bfd0b16c2c79a46815f8780d2c55a2dd4619d69f9940f51663aa12381bc7cf678bd1a72a49fbc11b0b69cb22d1af9f2d", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "4e173a80907f361fe5a5d335ba7685d5eba93e9dfc8d8fcdb1dcd2d2bde27507", "result": "valid", "flags": []}, {"tcId": 40, "comment": "edge cases for ephemeral key", "public": "045f177bfe19baaaee597e68b6a87a519e805e9d28a70cb72fd40f0fe5a754ba4562ca1103f70a2006cd1f67f5f6a3580b29dc446abc90e0e910c1e05a9aa788cd", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "73220471ec8bad99a297db488a34a259f9bc891ffaf09922e6b5001f5df67018", "result": "valid", "flags": []}, {"tcId": 41, "comment": "edge cases for ephemeral key", "public": "047fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff2e2213caf03033e0fd0f7951154f6e6c3a9244a72faca65e9ce9eeb5c8e1cea9", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "55d0a203e22ffb523c8d2705060cee9d28308b51f184beefc518cff690bad346", "result": "valid", "flags": []}, {"tcId": 42, "comment": "edge cases for ephemeral key", "public": "0480000000000000000000000000000000000000000000000000000000000000042be8789db81bb4870a9e60c5c18c80c83de464277281f1af1e640843a1a3148e", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "2518d846e577d95e9e7bc766cde7997cb887fb266d3a6cb598a839fd54aa2f4f", "result": "valid", "flags": []}, {"tcId": 43, "comment": "edge cases for ephemeral key", "public": "048000003ffffff0000007fffffe000000ffffffc000001ffffff8000004000000722540f8a471c379083c600b58fde4d95c7dcad5095f4219fc5e9bdde3c5cd39", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "bdb49f4bdf42ac64504e9ce677b3ec5c0a03828c5b3efad726005692d35c0f26", "result": "valid", "flags": []}, {"tcId": 44, "comment": "edge cases for ephemeral key", "public": "04ff00000001fffffffc00000007fffffff00000001fffffffc00000007fffffff5df80fc6cae26b6c1952fbd00ed174ee1209d069335f5b48588e29e80b9191ad", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "f503ac65637e0f17cb4408961cb882c875e4c6ef7a548d2d52d8c2f681838c55", "result": "valid", "flags": []}, {"tcId": 45, "comment": "edge cases for ephemeral key", "public": "04ffff00000003fffffff00000003fffffff00000003fffffff00000003fffffff2c63650e6a5d332e2987dd09a79008e8faabbd37e49cb016bfb92c8cd0f5da77", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "e3c18e7d7377dc540bc45c08d389bdbe255fa80ca8faf1ef6b94d52049987d21", "result": "valid", "flags": []}, {"tcId": 46, "comment": "edge cases for ephemeral key", "public": "04ffffffff00000000000000ffffffffffffff00000000000000ffffffffffffff7a116c964a4cd60668bf89cffe157714a3ce21b93b3ca607c8a5b93ac54ffc0a", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "516d6d329b095a7c7e93b4023d4d05020c1445ef1ddcb3347b3a27d7d7f57265", "result": "valid", "flags": []}, {"tcId": 47, "comment": "edge cases for ephemeral key", "public": "047fffffffffffffffffffffffeecf2230ffffffffffffffffffffffffffffffff00000001c7c30643abed0af0a49fe352cb483ff9b97dccdf427c658e8793240d", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "6fd26661851a8de3c6d06f834ef3acb8f2a5f9c136a985ffe10d5eeb51edcfa3", "result": "valid", "flags": []}, {"tcId": 48, "comment": "edge cases for ephemeral key", "public": "047fffffffffffffffffffffffeecf2230fffffffffffffffffffffffffffffffffffffffd383cf9bd5412f50f5b601cad34b7c00746823320bd839a71786cdbf2", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "6fd26661851a8de3c6d06f834ef3acb8f2a5f9c136a985ffe10d5eeb51edcfa3", "result": "valid", "flags": []}, {"tcId": 49, "comment": "edge cases for ephemeral key", "public": "047fffffffffffffffffffffffca089011ffffffffffffffffffffffffffffffff267bfdf8a61148decd80283732dd4c1095e4bb40b9658408208dc1147fffffff", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "44236c8b9505a19d48774a3903c0292759b0f826e6ac092ff898d87e53d353fc", "result": "valid", "flags": []}, {"tcId": 50, "comment": "edge cases for ephemeral key", "public": "047fffffffffffffffffffffffca089011ffffffffffffffffffffffffffffffffd984020659eeb722327fd7c8cd22b3ef6a1b44c0469a7bf7df723eeb80000000", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "44236c8b9505a19d48774a3903c0292759b0f826e6ac092ff898d87e53d353fc", "result": "valid", "flags": []}, {"tcId": 51, "comment": "edge cases for ephemeral key", "public": "04000000000000000000000000111124f4000000000000000000000000000000000000000d12d381b0760b1c50be8acf859385052c7f53cde67ce13759de3123a0", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "f1f0e43b374feb7e7f96d4ffe7519fa8bb6c3cfd25f6f87dab2623d2a2d33851", "result": "valid", "flags": []}, {"tcId": 52, "comment": "edge cases for ephemeral key", "public": "04000000000000000000000000111124f400000000000000000000000000000000fffffff1ed2c7e5089f4e3af4175307a6c7afad480ac3219831ec8a621cedc5f", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "f1f0e43b374feb7e7f96d4ffe7519fa8bb6c3cfd25f6f87dab2623d2a2d33851", "result": "valid", "flags": []}, {"tcId": 53, "comment": "edge cases for ephemeral key", "public": "040000000000000000000000001f6bd1e5000000000000000000000000000000004096edd6871c320cb8a9f4531751105c97b4c257811bbc32963eaf39ffffffff", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "3ebbace1098a81949d5605dd94a7aa88dc396c2c23e01a9c8cca5bb07bfbb6a1", "result": "valid", "flags": []}, {"tcId": 54, "comment": "edge cases for ephemeral key", "public": "040000000000000000000000001f6bd1e500000000000000000000000000000000bf69122878e3cdf447560bace8aeefa3684b3da97ee443cd69c150c600000000", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "3ebbace1098a81949d5605dd94a7aa88dc396c2c23e01a9c8cca5bb07bfbb6a1", "result": "valid", "flags": []}, {"tcId": 55, "comment": "edge cases for ephemeral key", "public": "04000000000000000000000001ea77d449ffffffffffffffffffffffffffffffff000000007afbc0b325e820646dec622fb558a51c342aa257f4b6a8ec5ddf144f", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "1b085213a9c89d353e1111af078c38c502b7b4771efba51f589b5be243417bdc", "result": "valid", "flags": []}, {"tcId": 56, "comment": "edge cases for ephemeral key", "public": "04000000000000000000000001ea77d449fffffffffffffffffffffffffffffffffffffffe85043f4dda17df9b92139dd04aa75ae4cbd55da80b495713a220ebb0", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "1b085213a9c89d353e1111af078c38c502b7b4771efba51f589b5be243417bdc", "result": "valid", "flags": []}, {"tcId": 57, "comment": "edge cases for ephemeral key", "public": "04000000000000000000000002099f55d5ffffffffffffffffffffffffffffffff152c1a22d823a27855ed03f8e2ab5038bb1df4d87e43865f2daf6948ffffffff", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "67cb63566c7ceb12fdd85ce9d2f77c359242bbaa0ea1bf3cf510a4a26591d1f1", "result": "valid", "flags": []}, {"tcId": 58, "comment": "edge cases for ephemeral key", "public": "04000000000000000000000002099f55d5ffffffffffffffffffffffffffffffffead3e5dc27dc5d88aa12fc071d54afc744e20b2881bc79a0d25096b700000000", "private": "55d55f11bb8da1ea318bca7266f0376662441ea87270aa2077f1b770c4854a48", "shared": "67cb63566c7ceb12fdd85ce9d2f77c359242bbaa0ea1bf3cf510a4a26591d1f1", "result": "valid", "flags": []}, {"tcId": 59, "comment": "point with coordinate x = 0", "public": "04000000000000000000000000000000000000000000000000000000000000000066485c780e2f83d72433bd5d84a06bb6541c2af31dae871728bf856a174f93f4", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "d11c640b4382e60ec8d254ee76f09b8fac57651ab73b6dd3fdc935a61564a3e9", "result": "valid", "flags": []}, {"tcId": 60, "comment": "point with coordinate x = 0", "public": "04100121f1a09443851c9aa2ab6ee6440e2ac5e1be648274bd5d26c12fb3ba3f7f032a1c219fa1457cb20588297e0513cfd4901f9a95414f7e914f9179f38567a6", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "90e712e2afd14171c19467a2bfe7abf1c477d1f40f6675f00e622fd5604fa16a", "result": "valid", "flags": []}, {"tcId": 61, "comment": "point with coordinate x = 0", "public": "04cad02ab537c80831ccdd395129fc4bfe4a89ae0c866f6619a3e14146d3691694689d477065b40f140ed87b37ad041e28229b0f79a6b3c992689954c97f7336d0", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "159583103d83f63538bd4e203607d7348990bb7f847ffbc9e5e509c7e34d392c", "result": "valid", "flags": []}, {"tcId": 62, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "04abd12eed4d654baa7d968633770f4a582f173d6633906000ed8acf6233c6365f0912f30bb98e7cb525890d5ea1e217149d52a6c59f7802a9f307e80d2a9fee3a", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "546a2dfadb1d60140becac2dc2e62d20c789037755ad5a49e37e48f2ca1b7680", "result": "valid", "flags": []}, {"tcId": 63, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "04a562c1ad9a72217df00147c7d2ceafc65a1620a1469c947e14fe43003ac5371b7ad1d33c01f0eb92b779ed6e460d0334447075a3cf66b2ffbdae31b438df6d7b", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "e5859c7811c5c3aca6c236ab499ccad10301c7c5ee913ce91bb66428cde11e4d", "result": "valid", "flags": []}, {"tcId": 64, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "048cdbebe9d07d2ebc4e41b1d72a9bac2974cfc4cf738d8b6de71a40ede9920d88dc2439ee0003fbde7b0a3ae41710c64b17b08a8841e97a390e482c9768fe01ea", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "65754ab459a10471af00943f414f28de1bc37968b097ad2845fe111420855008", "result": "valid", "flags": []}, {"tcId": 65, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "04f0cd7cd8334678308cfeb785a68a1504a91418d4441c4d4c740c57488b9aafb079d8a8d29973eb502267eccf6eda326626fc6e025d532b85e9f711f8ce6971bb", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "8631fedee6ceb3386ac42edf322c188824893d267d6108f0cf5de6964b88331b", "result": "valid", "flags": []}, {"tcId": 66, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "048ad0af23b90e0341b4e2a5a963c8522fe011ace19b1b8610cbe7927a17a7249736b87ab9907289a23a0fb20ca4be42d421fe38d35af09d79cbe6e6a4e95a1a8b", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "68c58599c123be6d37d343bd41b11cecc5f84b2635661163656f76d7fb04b426", "result": "valid", "flags": []}, {"tcId": 67, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "0459c9cc2d7297ddb0be6304c94cebf42d813e970c50f45287753b8e9cb0c6db45f571d986990897851fc8e1db67c99759e8979c3d9ddfd02f633cf1ea5b6c48ab", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "b58d00525c4c4b4f46562852c15ce2e48dbe23a3be37541e048446eff5152ec6", "result": "valid", "flags": []}, {"tcId": 68, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "04e97080da7263a29c3072a65178b7b31587a5dffc19754c561e32fc53199234f04e0b9b70c97b60e940d5629f2266d1a8e242deb71eb7f0b2b2da2e3044738ab0", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "4baa01c211af8f94aca89548902a71f7b53f7814bbceb3d4bef31b376e34b476", "result": "valid", "flags": []}, {"tcId": 69, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "0444f600da7160b975a0232cb6a4a9e72803fd77caac84352039ce9f4a67a1da77626045599381e599eb9cd03f282e267b8cfd3ba98dabbb0f29ab1c0944270f3f", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "e19fe9d1294cca94a6388825249e6b37931a231eb917cfecb292792d0c18f1b8", "result": "valid", "flags": []}, {"tcId": 70, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "0471e3e9be0e0ee4449a19d2ef7919266814a0fafd04fb677edc32656e6a46e4d2bc5f404c5b54f03e294be22e8820a71b4d4ac04a708e13cd71fdb0041e7e9698", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "ddc1f4663b928add06b1e57c48db98ea08c4d33c3c2106371407f3848a9d53f7", "result": "valid", "flags": []}, {"tcId": 71, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "0427b693610154d5b7f08094e46ff2a2ac1c01d3cd826e3208e5254436ed279960f2364e3a604f3b592e19262a1b22b1a148e38cd82c9e54f108ef8f833683f8b4", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "91dfa95ed1eacbea419156471a8ddbb6cb93dd456433e18633d26817611b9c64", "result": "valid", "flags": []}, {"tcId": 72, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "04c32a52af6dac369b6a499a49d3e38e7c9534bb9139f57d4984b1d3c04ab8220653cdc2daefac83cf43c0d64604e5f9d85b55dde62b692cd36af99ebff4140c39", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "9f91a9633daa4c56465e9fbef4431e13041f68910fb5ba89f8da9381d68a0dfe", "result": "valid", "flags": []}, {"tcId": 73, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "046f4e2f72f32ae66f1f4610966004c436aa0d90b7df07ce9c4aca52b02d46b4d0c6a3ec76bf321b7fe5203cf3d66e2d52e3ee0495ec766d579a4511175e01bc4d", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "014ae81442f8cb6df58ff41e6db203db40ea951b91bebf86d42cda7be33fea64", "result": "valid", "flags": []}, {"tcId": 74, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "042e065975df642fcfdafe2fa5affc18b2c68371796f9d963d89c4f5ac5ccea28b990f31522fbb265c3f4d5c4bb82ebf5ddff5a8ea588db4d282acdca7a6ccf428", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "78e81e8573c3ae6089df7db1fb29d7be12dc11f15bb25bff2af802e15ddc136e", "result": "valid", "flags": []}, {"tcId": 75, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "04e1331eee03c50cc2b90944ddfc0d3a7dd8185e6c21c75fa92a0c14b0f1949ac9154d783f4547dcf5508bbd86c3dd8c3b17b61989f93db5490ec02a46a1005c2c", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "ed67195a272c63c50205abf27439291134ffa1e8ec597f3b302716d93632e98d", "result": "valid", "flags": []}, {"tcId": 76, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "04e0c56d486e9c01163ed6c3ff25de3cdf5744dbf9e0e00bdcf19965df4ba1f311bd5e44430665823d8c0b34ebec0a6aab5ea96cf239de214fd011e6f9ec501dd4", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "50774347848828eeb6230f497cd181f8c57fbd18ffbf8328cd008321a1c37c43", "result": "valid", "flags": []}, {"tcId": 77, "comment": "point with coordinate x = 0 in left to right addition chain", "public": "04885ead6c074f8d751a767e918c4e89210a587c4b19d42244ae07027e361831053e80772be57fbd744955a2e8523063cc6136f2bb37befbef7a681d3bbbc57788", "private": "00e461c5b5e63d75b4c8c123bf8b9cd45e712af08f7e2e494a8f255ac9d80e058b", "shared": "913da71044b8021a86c8fcaf4f634d0d625ff91ee1c8474d548bd10888964fb1", "result": "valid", "flags": []}, {"tcId": 78, "comment": "point with coordinate x = 0 in precomputation or right to left addition chain", "public": "0441e9d4cfa8efe80b895a8cbcce2568e251db7ecdfd20a7ad710d4a4bf2addc6b5ec36a8339168a03f15b8c80f2a2a828f151d38791584853ba2ff44a2a0460a1", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "b48e119d29eef7dbb76b64218e728ddbf6ec600505ec7ced6ab6fb8763308da5", "result": "valid", "flags": []}, {"tcId": 79, "comment": "point with coordinate x = 0 in precomputation or right to left addition chain", "public": "04776aef1acb82b628e132cc29440988f0a15d4cc2b4f328aecb063c9b86e5018e6e44dfc60444faa9c4e36bc217451f7ac2956cb3b2e9bbd655eba297163d1f34", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "28a88b6b258f233020ba6fa9c00d1d72831f4515b86966a9782f521315e18aa7", "result": "valid", "flags": []}, {"tcId": 80, "comment": "point with coordinate x = 0 in precomputation or right to left addition chain", "public": "049ec06b0b08662c0e1dd9111696a63a1601cc83cee20695778adf84d43064fc90156001f084cd3c1df1a087f626533b6572584889bd3d5c2c99f0e311e22b41e6", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "c4ff865ff3dc4953ea78d92a02f3345a53bdb6050cfd8f41baa4395ecb6acab8", "result": "valid", "flags": []}, {"tcId": 81, "comment": "point with coordinate x = 0 in precomputation or right to left addition chain", "public": "04fa51d128adc2000f09ff12c6fd8e25aa08556d708bf6b0ffff9e8eaad4783f0de22bf529e516e1f64b8e0d09f98fad4e501695a930a1b22076659da707e3ccd0", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "de1069f051637e10166559cef44688afc809341855261215c4f381d9d7da76ca", "result": "valid", "flags": []}, {"tcId": 82, "comment": "point with coordinate x = 0 in precomputation or right to left addition chain", "public": "04614dcfbea4789a3f3eb4a8e2f111c887f0248d9316b99d0864c927a045d6941753a073befe08491a8050a4d96d08ba4790ae18db3ef7f0eaccf59ce1095afc54", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "4207bf4159faa0e50ed238b9c0ff46194a539a1ba03a5a4c8d68f369aecd31a5", "result": "valid", "flags": []}, {"tcId": 83, "comment": "point with coordinate x = 0 in precomputation or right to left addition chain", "public": "04efe7754ed4c0b3c1dd301bc1ed69800aa2ff5d51fb85937715e60d2e7bcada8eb1581ab75fb3c797ef94a9dba3d82568c84617eaf3fa04f279fbfd898f704604", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "b5a0ec92aecc3010d27d2263d3da66e3d2f3395d23947024a3f4744454622027", "result": "valid", "flags": []}, {"tcId": 84, "comment": "point with coordinate x = 0 in right to left addition chain", "public": "04d8e13fbd017f1f9a26be35c611d7b2299f5d10de3c8a26362273fffb85238f3ed1426b748c1f87e3afa2c1e7a0224310c980655e07399590d1494d6d6bea0396", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "d2a5bc66498c6036aecdfaad041cef732a893de190a0a5b42ff71e13f09280e7", "result": "valid", "flags": []}, {"tcId": 85, "comment": "point with coordinate x = 0 in right to left addition chain", "public": "045a1027666a0e372481fec0b3901e058d60107c07b1115550ceb05789b55a6d35063d4c8ee66ed45ff3e1dfdcfd73ed96a9e83193884adbcaa574b2dd118a692b", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "1f812313ddcf36bc38071d0e51a74100d630c8e20cc414326eefa42ecb1b5f8e", "result": "valid", "flags": []}, {"tcId": 86, "comment": "point with coordinate x = 0 in right to left addition chain", "public": "047937b9c40986dd755a0656203089782583da7d8113a44190762ab474a20bcf60efcbc1525aed5b4ad8e687cb02c2ef8887095cadca56c765b41b4a9544ff2fe8", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "f284089bddd5e2e1be3f82640efa0658468fa1f10b281963a3ca190c3982fda6", "result": "valid", "flags": []}, {"tcId": 87, "comment": "point with coordinate x = 0 in right to left addition chain", "public": "049368066a0748867a7b870244f5c9f82ea8bd51552959dd550bb7394497159a5d40764add1ae24c8e3f432ee011be97d3130718fe0a6a90ed8b1011b2034d09a0", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "4529f4b631c9984ab216a6801281fc4fd8731a58b65ca8d07bff07811116371f", "result": "valid", "flags": []}, {"tcId": 88, "comment": "point with coordinate x = 0 in right to left addition chain", "public": "04981d7449bdf0013f5eeddbb7e42c442f7ccdd9427bd26d7b388755aa5e26f46a1292b88fa6bf5dffca054dd42ed3594277b593dcc402d80340fb7816e4dcab37", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "64bbc9fdd73643eb2954f4ab640381b938c5e601846a0c6b6954966e0dc73e6f", "result": "valid", "flags": []}, {"tcId": 89, "comment": "point with coordinate y = 1", "public": "0409e78d4ef60d05f750f6636209092bc43cbdd6b47e11a9de20a9feb2a50bb96c0000000000000000000000000000000000000000000000000000000000000001", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "28f67757acc28b1684ba76ffd534aed42d45b8b3f10b82a5699416eff7199a74", "result": "valid", "flags": []}, {"tcId": 90, "comment": "point with coordinate y = 1", "public": "045384d6c0def78960db967b8096d35477c5a5ce30ef0c6d8879a5568ca87e979401ee56c4581722610b43f3cbfcf3862c082a6e36baa36fd6f78403c0e399faa5", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "9ee653cda46db67612760ce35bac8450bbf48dbf74451ed93abb6db408a9fe10", "result": "valid", "flags": []}, {"tcId": 91, "comment": "point with coordinate y = 1", "public": "044eca7641a4afd5eab0b214657ff3bdcbfc66f1551a53bb59493bc38ed78ff39614a0cadff14c14736edbdcdab510cba07a8924ffd0490ee514aedfaadb648b01", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "9736ad6b2a2ef17ec3f8c8dc2e35715fb1c06f28d82e4e26876f0214588165f1", "result": "valid", "flags": []}, {"tcId": 92, "comment": "point with coordinate y = 1", "public": "048d0177ebab9c6e9e10db6dd095dbac0d6375e8a97b70f611875d877f0069d2c70000000000000000000000000000000000000000000000000000000000000001", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "748fa4f5a399320382dc920026938694c41a26fe2aaa318c5e710198dd71c793", "result": "valid", "flags": []}, {"tcId": 93, "comment": "point with coordinate y = 1", "public": "045fdb7f0cffb8b5b1142d24698a4bda76bf9827d63b1a6bd85a4e2f9b59c510cfbcb35ba9c987108b6d4337ad5393f9f910ec92410c230869d66528ed88c1b98a", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "7f97db83b4d86f04fe286041ee21e80ec3d59f3ce82cdeeaf362016fc87a3e02", "result": "valid", "flags": []}, {"tcId": 94, "comment": "point with coordinate y = 1", "public": "04530b2293e60c6b6f14c75c90b1ef8b9f9fa6b2151b8d9855792eb2b3dc69f07a0db42440e73fd7d6df04aed5022fbe21ceaec33c5fbade1bd6ad321ef2e10d0b", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "21794cf24f56273fa4463cc7ae4232fa34dbe0f18b73613b8ae9cbfb9c36abf0", "result": "valid", "flags": []}, {"tcId": 95, "comment": "point with coordinate y = 1", "public": "046916fac45e568b6b9e2e2ecd611b282e5fcc40a3067d601057f879ce5a8a73cc0000000000000000000000000000000000000000000000000000000000000001", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "915106d07816e879e7643f00abf6d79fb8f1cb78bf64a6a3827f91a7b0ef0f41", "result": "valid", "flags": []}, {"tcId": 96, "comment": "point with coordinate y = 1", "public": "04ed9568c85bc52a6b45733618c3602107c1fdacf23b1a38e486af95978a214e2efa0d71d5e737891c4276e247581ee6139011ca1460db9b1e20b364d9275683e2", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "2fcce552310819dd775ab7ba9ff0f96a1fcadd25a0c709703cef04bb6e1a7bd7", "result": "valid", "flags": []}, {"tcId": 97, "comment": "point with coordinate y = 1", "public": "049ff7731c00f2aa88b3fc174aba907ad17595e602e768a5f1e9462a6d4b89b2d23f178a70b9bb3edce289118338a33df30c432c347f12a3de0a2b03b353878d96", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "757d926a2693bc8a3d2d8c0554a13579ef9e559186578911f37edc88b2f5e61a", "result": "valid", "flags": []}, {"tcId": 98, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "048270f8179d57436b34dfc0bdf7d417a5c895116b90cb51aec718614f864a635d174804e0c0e06e3d68d3149e0b956621c6aa2bde83f4d17d03d28ef8aa389fff", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "3db29ec6f978d2269e92e9c7eb5c8b5a8e56c2228a4fb9e483feca50aa3e451f", "result": "valid", "flags": []}, {"tcId": 99, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04c61750e98abaf20225a881dbfd3510532cfc3df971bbbca4a2bd52f91acc9c59d0fe79342097f88ae78fc79a8032245fdd2c30cc64aceaaa9fd57b0825692531", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "72c57c2e10d77318b3a796097bbf768c6366142d80f98c90a93780a841075f32", "result": "valid", "flags": []}, {"tcId": 100, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "049c5d3bb54650d9550e1ee2efa3ea43c14ab99d18bb049f37b42a6dac48232f0bd3a2760d83d33afe4ce6f1d1245489c509bd26b0251f308f8c996e80f7a3f8eb", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "a96b07944e9eb2b22a9a36575eff1f4f6363b4aa3a53b100b8518a67ba5405dd", "result": "valid", "flags": []}, {"tcId": 101, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04f1724efd481ad45a55795f06126b1f5ed28e7d9bb4fee910af2ad8c1373b18ff77edbc34da6c787ec73430347f4da86810032d88f7475f6c42f15914079d179e", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "855883316b6d097ae5eab6c67e8411a1397349a09b9d7d8f096b2ba1bd03ea31", "result": "valid", "flags": []}, {"tcId": 102, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04fc3680af52fa89ffcd193ecc0b0714466fe5db277ee5872846c520bf4e3721d927260a0e225a3d377e6723ecb6bef8d4493c2da78a22a307fcca8f88f4527208", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "5a75bb7a0c96b8340d0842bcccf11974e1a5a2c8f4bc22b333433cce646b6a8a", "result": "valid", "flags": []}, {"tcId": 103, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04106b6f81e3482db18d74029291821ae448c38844ef783bf1d6999a404401f63f6a5753f0edc68a62cfd6a0b181bb2599e1f3bac5fa8824af160de79ed867c350", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "d96412e31cf4d26195920cac952fb79ea25f6c50abc79b5ed0ef8026a6e83319", "result": "valid", "flags": []}, {"tcId": 104, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04093cb5193a4f94cd18edaa20a973b87ff79b0c03684c79487ecfee347e5354eb04fcb5752539170777932be15cd84c97f03815ffee8b60b647c178eebb8e14d4", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "2b0eed9badc92a1068196dfec124fe8f9d3f451e294d322eb881cce02f286026", "result": "valid", "flags": []}, {"tcId": 105, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04d6c38f448b964e27b5b450cc38d3cf41ef9df83d8a959771eb9c21855cb36445df638aef46a2aeb13199281e1a26d12fe61b029ec7f68b90faa89f88c7a95942", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "ed0b1d8dfd27a61fce91dc6405bfc53b6d48a8c13ba541c96ef3dcf31d7cdb88", "result": "valid", "flags": []}, {"tcId": 106, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "048a748d61f59c3b6a29b733b0d554b2492e7f76fad7cae1c17f2ac3de9e4a65d2eedbe6c26b6fd22bfc03c1687555d2f0a38e02adee5570686171abfec6681917", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "a796dd144f21ba3318f9e10828ecefc9c0f6ef2c427ae31351c16c2fbfa3cfa6", "result": "valid", "flags": []}, {"tcId": 107, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04f1052699d87e5677c75e26b2abe719310648d820a96e5b381fff58b392401581b1bb16ae8b68cbb76a3256870bad1ee5a30ff9fd662fd4f8d1fe5b5f1f98ff46", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "1f3a9615b0745046a972bad5d59794a0b60b032b4ac94fe85f77dfb380d1f32b", "result": "valid", "flags": []}, {"tcId": 108, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "041219af5230064ee9778667225f0e009cdb961330e386edb34e4fa9fddd0e5be7e2a12554227f613aaaa78938ddbbc99b923f9d181b8192dc4b816577e8f3b7e9", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "caf9141d1fca4d0f10683b5e86d2b41af5602f017991fe7348d44e8d7014115c", "result": "valid", "flags": []}, {"tcId": 109, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0460def130f190e6dc44f5eb8a59e12e7efb27db968c7fa6cc6d31785f066b41b1f1bb556ac4cd77033e7aa6c5ba16f47ebafb14975a7fd72dd9b7fe23116bca55", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "6539ec1c98fa75197ba07c678b26300b3da1fe407dd4c68b89457ed669082e06", "result": "valid", "flags": []}, {"tcId": 110, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04f23f09bdb7d17289eb005975a757a39325b4df9b29e55ba2ca679b5ec0973ae918c881f3c7b6c12bed1ec54b837d08c5908e89bdcedd84b9177720378f789600", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "0b6619827cfa948d63f021e9eddb92f884fb5ce8a404bfe059e993fc23447a69", "result": "valid", "flags": []}, {"tcId": 111, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "045dbec098c1b7de3e3e2e73d0b62cd49c877e1a0130a1b39eb2fd4dbd4426aa4ccbeee217591a8d76cc8deaf14dde52e3f401e53b30cbb9c1807910d827d0041d", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "2a53a561acf5caec6eb0d8aa40727942881a75d136899dfbff91528236926c39", "result": "valid", "flags": []}, {"tcId": 112, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "041e70730dc4f39c8970182e1a29cc836b9e9d6cbd6fcaa8c0dc1062fed9a849693e7b9151f9c8a3345366f8221c8fb700e8c3a9aa7f0cc46a48864e1605592094", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "9b377716ff1d056dac8e392249eaec740d2f5aa62303f4baf6bb1b03b2a276c5", "result": "valid", "flags": []}, {"tcId": 113, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04f428c9ae3e23eaf9c2a5b9a7e41efd1cffbf35f881bfc35694d9c05d1e312b10ef6da9023cfd2dd0cb7b9e2a77d644affe62a63fb0f29d45291c6861aa063c5c", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "0c0c6867669743082547aa94451feb362fa29fbaf228dfb3eaf375f1a5ec2fb3", "result": "valid", "flags": []}, {"tcId": 114, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04b9a16d9a5b85a714e2bb2aa22b086a17404c7a3ff62452732347419c99e90bdad578b462f523994304b6afcf6944a9cc5d0ad1afad956475c8f2953c06b06b97", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "d11f9e32587fd3b6f4a2354812618b4b3b4a7539b8a223b388bb7437f8d138a5", "result": "valid", "flags": []}, {"tcId": 115, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "048f659a163a58e9f900c1e9b34fb1cd61ffc9890267be3417c8afe79d57214da05cd5cb68a2b93da0dbe56c1cfc0dce8b6c3260e0c48379c6d2091f16b39221c0", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "4babf6368e0359b78614060241ece46facca3f52f5bbc47ac0b46a075b5dd3a0", "result": "valid", "flags": []}, {"tcId": 116, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04d257f133f00a079f4e6778ea4a9bf42b9f231290431b5b93d7e8b0e35b48010650d6c6b46574d1efce03510b8db4a0981ce138c5bd8fe0e54c988c40c5fc9200", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "9627cc5c8d8b72278be89c32b52210173e6f4b8e2f48e460c6429f46f9f469ae", "result": "valid", "flags": []}, {"tcId": 117, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "045ef2ac57c4e93cf78d8f86c35d413b98dc1902dd245affde5c16034afc7ea45547b3e9f77fbc5075bad03c418094f1aec1d03edeafa167fa6af83526552f7034", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "d2b178bc9bb16b5a91a100bb72e15a9639e050c034346061413ec20c4fcc9bbc", "result": "valid", "flags": []}, {"tcId": 118, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04a7b513f96266414fa6ff439a35d8f09ab615db0bb6a3b1a120c217683f724b2342007a2c9feabcd6249a0d17acecd995e2a217fb5f07bec96938016e297efa52", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "6cdca0a731aff1ccfb1904a769cef79eba965fbab1cc64d2049d0df45dccd276", "result": "valid", "flags": []}, {"tcId": 119, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "047743ab7248dae5f1a59ac6b0a136e9f1e51aff8bd45795ace5f8187a13edf9adbd9642078378bab5c6d484f9e1ce39675b72170bf39abc9be7942fc01fc435d7", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "bd15e97a7f49aa33e57b54140a75fffce71b788ce0faa334cf8b45623dcc818a", "result": "valid", "flags": []}, {"tcId": 120, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "040e3aa971bacdace350dc0957fa5bde0946324eb139939d7fc1997c701effd04a4e6c3625d9564168d3a752961221a1de8cf5f3d603752a8c2e6277ac3a918c25", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "c8b5e8e7488857a2dde62c5fc21e4525ebaba0e06b5be83ec6e7dd771e15a01a", "result": "valid", "flags": []}, {"tcId": 121, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "040f563e21bf9b24015a7cdbb6f000a692784ac2e4bc2715c76f684264a899c8240cab0d76e6b01cabe4f327429d11be115ed6dc0ca74f02c1b987a082f5af43a8", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "1c63a457509b148272687e6e442bde51982d41b0080d8c0c5eb714257af971e7", "result": "valid", "flags": []}, {"tcId": 122, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "045da49f10249e4df3dbb4e31ece0b0ee9aa073f2588195aaae63e74f6567a774810b5dd61b6bf219e9eab30ef09c13fc184b3d09ff7a4e192bca8f5111c4163c7", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "73a1ac9ece354a930dfd9c77577b4f50acc0a78964ea0d7775631d64c709c4a2", "result": "valid", "flags": []}, {"tcId": 123, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "046f72e6e5c6300679d3f14f0f6e590665643576ae8bbcb7c05b2f4a83e75e6ac3e712cb056ff034da340543c5da6997e65a3ab4cd39e997892bb92ee2c22b8167", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "fcaa406329bb74f995862cea7cecc7425c6bd4148ef1a9f46b5d42da5994556a", "result": "valid", "flags": []}, {"tcId": 124, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "046b544df9168e7787db282e2ae01dd72306d9c9bc80f5ab38ce594766c3d929e967493ff601ca60862b47d3a0785c917e44584044e36023a54424015e58be5040", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "e49ff11d46b6c4b5dde528b04132d15c040e79f9b7151fbc650030988028cb87", "result": "valid", "flags": []}, {"tcId": 125, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "041c31385b9db9b374e92499939ab0fd7e7eda464561eba89fcd7b4769814a8638a4764cf8ce97b5d143bb8eeb9e1b27287f2b73942ecdbc6359aafb1ee7a152c2", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "fc8f64eac1c7e688c52c467185de21914e8b253056d9e4be010ed0128f92a889", "result": "valid", "flags": []}, {"tcId": 126, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04aabcf8b1443d6cbb1de129a0ffe09f60b23fd9d0a44b6bdf25bed7373fdbfd1db716bde7fe9f2f46de0b688e3025e029cff15244429ad4f83484f5dea4af8583", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "6b56d8a01a884319ab5fb9d890cacfc7aabd81ad938cb5eaae207c8c1aa06efb", "result": "valid", "flags": []}, {"tcId": 127, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04e7cd580bd957915d527056832e37793ab3b082ddfad9372412e1908e5c16bbb6208601a970d5844b780d9246e9583eb35918c42ed695c07d52244037f0e31db5", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "2f64b5c8046d41a4e1d631ff23846bff956a4925a47f8534490a20b4b1918b9c", "result": "valid", "flags": []}, {"tcId": 128, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "042a52db1fe246b71c79c0d0ac49a7d38de67b202995efbbd2a9cc525f6f36010368f494be27e0593e2d612f1fa10a9211437e6aa16e65d97735014072f0dcec94", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "63ac31e718b9a780a85f0670e1d3685bbe306e5f06fee282a8784700b503c124", "result": "valid", "flags": []}, {"tcId": 129, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "041c50dc49fef708c4cdd62e766f9b60f784d51afee17a8fe9f3701b2fae55b7a5d10f0d9639d83dce8f26a869705a6d6d38e6d328f5685581142aec0dcd1f90e7", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "555c1917b770cebe6a98337a008ae3d8d04f571565327c93debf61ef90ddddd8", "result": "valid", "flags": []}, {"tcId": 130, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "046d0aa1bc1cee6d07d045002c13290d0ca25ca3c8783343a525fac70472b92c62d6fba71174448b472cf172b0ca9e377f1a2603ba7ae1276d153b20c63e7d24bf", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "3a65a9200f8f96635912faa5e7859fa303a76a1c2a41ea97ef61aa39287700a9", "result": "valid", "flags": []}, {"tcId": 131, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04f07e3d8be2ba54c6084141e1fd2b29cfd00d4e6dd6ffb115ed839b10bd8a422f42992cb9a5243897d55408e9bb556043318d87349af35dcc0975ed805c8fa2c9", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "bb7bb52da570ba58e05fd322f82d556c2d65b365db30815879f67f233b089b51", "result": "valid", "flags": []}, {"tcId": 132, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0443a9b90274dbd5f36dd29046fc8390008dde74513ce4c3e8892b236efff80c9dc71547152a5897dbe16957bd15d1a87d770496f814fe2921c8f33df04393c7f8", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "e8cae9944233b867eedf5902fc49ecd07e4c81c46279531e89520b74ba5370b5", "result": "valid", "flags": []}, {"tcId": 133, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04e9af8e8c19da9d5c2f3b3c03b8e927c3cbe2d717f98f500972e56d82eb07c2b14e83fcaacadc26f8bb5e7b94741fe54f31275ebd6e1c969d7ec2fecead8a0dae", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "e72ad0cdb25f4307d1d834a5f792e9af64fd1b69a47041ec8fa46d526f419e4d", "result": "valid", "flags": []}, {"tcId": 134, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0433d9582b567aadbe59606fa6ffc11848e4947b5179597317776317b2b4ff65d0b4d8568dc843319cc04f4bf110496dee7c9229fc68cb0958f3cbd37ecca6990f", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "000197fbc260a84dbcbf88136aeaa79b03bb8949aefd2416bef63929ef789bf3", "result": "valid", "flags": []}, {"tcId": 135, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04e21c0282adb1b2055fda744644c68612cfb0c68a70b9812d007f21a78f1adc4849f3e7644bc6633e2773a2f3cc5214fa7208e30afb3de992f077ee321569dc48", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "cdb18bf62670a853488ca510d8f55bab2918991424925bd9b74a821d2c6e7e3c", "result": "valid", "flags": []}, {"tcId": 136, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04af27de0da6556e4e64588c9694afee9a84e1cbd0c388972df3a997f760bbcd903c5a02e161551f333d770559ab1af49bf8b68274896590939ce956d9913b676f", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "167303505d22cf9ef78c5b9687a5418fa9fb284f2b0ff68316288ecd7f2e2e09", "result": "valid", "flags": []}, {"tcId": 137, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "040da41b82550b358ff474915d83104d41a83a12ef70589b9d392f0f30dc32429edc76163c8fe07a3f709cbd92da0bbfc5045f3db82aa5344cf1fd5b27fcd2f7a6", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "85600ff23c3cde26009fea9b6539664bf045056883728ab0d4498ea0a8f4a453", "result": "valid", "flags": []}, {"tcId": 138, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0419c844b8c7209026a0996a782983e1bd0f0de9255b86739be9bef08ea5475cc669a779ddf57747cf7d9a22f00ed8efc6e818af5827b750d665fee6d6d58a22e8", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "a3250a2bfb145ce86e706ac3ab2bf503a66486ac0b2f7522601c124b0e0f9c5b", "result": "valid", "flags": []}, {"tcId": 139, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04bd07bd4326cdcabf42905efa4559a30e68cb215d40c9afb60ce02d4fda617579b927b5cba02d24fb9aafe1d429351e48bae9dd92d7bc7be15e5b8a30a86be13d", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "2d70cc8c8af01366051cc8359c2fc8f258757e2601fd8f3e08422a7b23bfeff5", "result": "valid", "flags": []}, {"tcId": 140, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "040089dee27a60d071dabbaf58f3e56614dad3b7f9a8030769fd0463b3e6e0f03a147b4d6e7e7fd939b9b54dab458fd556ad8fdaf4da6c3909588c4e050ca74a67", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "cbe0c571d1080ea34ee20ad1bfd21ea5ecc442ead733fb4eee3c0d7b0cce9935", "result": "valid", "flags": []}, {"tcId": 141, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0442ede106cf85aef46df7e5dba8a8b00459317d9e766a7b77c299aa0e17dea142b6e9a86f4fc3e945d4323ba8e459f6b7b14c563a698c757a2d5f7b0bc301ede2", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "33320fc7917fe4e19280bfbfe16f223c037f7c2dc30c0fda98310740f57fe289", "result": "valid", "flags": []}, {"tcId": 142, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04974b4316c5e7d1348b28dbc4fd61d8d3470de744c30f5be237f85f29969dea77b5f00b58b83cfc7bc51655465b4a28abe1ed3dbec20c6b4643aec85b95a5bec6", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "35c726ead66c39414fe0c24604df7838e5725d2fc1bd0853261e1de3338ecb4f", "result": "valid", "flags": []}, {"tcId": 143, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "0459873d7523936a121b629e9870f930419f253a5767b9d0dc49716f2c50e17bd0163b71f2bf4318fbde1ceaa585450080eec28474cd18bf7c21d2d1bfde4ff677", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "66ea42fe6fd8741b37599bbdada3ec0e6b08c0b52ea67c29a33172f72742583c", "result": "valid", "flags": []}, {"tcId": 144, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "04bd85a79f81c4f9613e64fa347886437856c7358d1b69cf1e923d7742d82f9b6767d26918eaa8acb113a1daadaedc709742457303ebc23cdda5572613dc827703", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "2f8a502e4f440133e84fb625292cbeabe2cb79da73987c76d4fed864d1b1b762", "result": "valid", "flags": []}, {"tcId": 145, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "043e6a4effc47c2f5926bb6b4acf2eac48b9524c47d511f816976796778600d6c5bfce593242a5985a977590f8d7485df3f953352957f3c17c13e94583d9c0e7b9", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "06436817d8928b77b73d16c5c3b35e243ad3ef2ab59ad047142c67a6d0923c84", "result": "valid", "flags": []}, {"tcId": 146, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "049a4487fcfce8396688e7449e095fe803caa253d4bd7c66dbc6261cc9d9f883a50e5251bae29c5a5cdfa31bc61105671a88a018467398158d35b88829237c0bff", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "7e83fd2c3d713bc85d6d85d9078b3a0842824d410e8abde04da0fd71c7d94705", "result": "valid", "flags": []}, {"tcId": 147, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "04fed6ce127290c1291ca5ce64acb4e0f2f8905654d1d25ba57c1f74ab52f21f42963d31671c06b802169929525c4a1fdeff5b1eafab919dc2df6c52be84dfaef3", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "0e3dfdab606ebdc6428282acd443f189c99b3b483aa101fd8d6bed38aec59e02", "result": "valid", "flags": []}, {"tcId": 148, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "04f7cee5b55f1869f137dd707c8f8fb8965a2be5840c3149fb759695a4661b9c0d23c78c4e9647b0d6cb2f2602be73ff25cf3d09c96d892b5745fe5eca814aec91", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "f489f2bd93f76b8e41fc6b9f211bc599d49db1f17a38e95bab1d31b2a2b55829", "result": "valid", "flags": []}, {"tcId": 149, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "042baaaec3b3e8d54a4e18f0960b947da2535e3cfcca2cfa8b7113aad8e3b6626f72f71e7c9e96042c1d39cc8f1139d5147c6f4fe62e23cf6df364b5f4d899f842", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "cc5738b49d30d5d02cf7e0c54a3de09b5b6f3c4dea91dd0679072a3562444c37", "result": "valid", "flags": []}, {"tcId": 150, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "04a51ab1238bc1bed25247e7d179c83a61ae2d4a9fe2288c363ae0eb7a77de432a3c6d35d82ba8017e6ca9041cc785a30703f7bc4427506e624ac5979d715421dd", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "89a11177d6907a81d47467093bf6a3cc8ba55dee05239b160a31a3000f5d807b", "result": "valid", "flags": []}, {"tcId": 151, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "048b5ae8a0e55f30f509061315abae79ac480f88b44655f7269a385c81526884be262974a31a0e2322126c2d77b26b108abd81f8b952c458ccc95d46fb4924c7c0", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "2cb03c30b20037a5cf4d5b33574f3abac895bfab37867eb2ebed260e0929058d", "result": "valid", "flags": []}, {"tcId": 152, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "045f60c77e474dd66c8135ee3dafc75ba644649824c72737542091ad469adbb685312c09c69b629d0436bf3bd6c6083ff2a87be484a73ef3a5d2c3e06b5d9b21b3", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "e54d487d0c4b12fe522af3e663ce316e632ba9d63a1f02a36fc5a82bf82731a4", "result": "valid", "flags": []}, {"tcId": 153, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "04e06eaa73f6feae45417d859bbad4bc404b2885bcd213ebace594e16f4970e0c411ed3323a3d7afc7076239884307f91849ed5f5e36b6171d309c81344c53e06d", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "ccea969d40fa42933f4fbdc4cabe2185f8a452996254c1f4e0dde5e14feeea8d", "result": "valid", "flags": []}, {"tcId": 154, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "040f1c1b89e9fc6fc0faefc9109fc4a1247d9f54c7497b6cc975e6a5455bef410836cb3818548ac9b41e2b8336c3eb8d97075ae47e1827fa1ff93d4341d43c0c1d", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "eaae0e188c9427bf3c8b3ded772122204c328d5941e389d808e2724638f9aff8", "result": "valid", "flags": []}, {"tcId": 155, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "04577069e8284a95f51dcab919b0536657058971dab76217f8d3ae722a64092e26e51f68a722cc0397f4801401771e9a3d1988d4af76f14f9e2f9c36e0773e29c2", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "fea0cce1358f1ff40ffeaaffbf91b2e8d426d4e31e9627731ace3a122eab6b0d", "result": "valid", "flags": []}, {"tcId": 156, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "042406a2759050b925dd4f814c5033e355548f42bbf1afb791c110f0031f29f68099d5f4b005de3927f165abeff196a28c7217fab1be2b5209c324e7d62d2dd687", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "837621ea4827bba0376aaa8aa66cfe144a2ff1e359dc619a06441d3e055f9771", "result": "valid", "flags": []}, {"tcId": 157, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "04ccaac61f35a27861183621642bc573af913356fb47cf582f0b5299099d6f6c6991f7272b83b738a7a5d30447c87f126a7d98ec72fa2609d0939d18db7ea7eb3a", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "63974ce6153762e5b364523cead93e8ce8bcc77dda56365d676136169fc4e39b", "result": "valid", "flags": []}, {"tcId": 158, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "0401415917272f1984e7217a36fb311fd2904d41a6b13973f92aae3b90e85e4d56d97c822eb7b21a84d0d1be4867404a80c34867f43139dadcc3619e10b222562b", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "0a0488144bc36d690b62148ac3076047d46d48f7adbb0f34fee9a636295fe737", "result": "valid", "flags": []}, {"tcId": 159, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "04b2575d100c6fa056bcd137ab111b5315a8908c29243b84f3dc996d0e45764b9166cabeb41885588ec08b47257df58bd58f7dcd9e012e2669fa2f52e25767fc4c", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "1232165538a44268aa7c199c54d6d207c4ef3f5aa790c10c926a20752ca645ce", "result": "valid", "flags": []}, {"tcId": 160, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "04c17355ed30ccd6427f9685709021b25c11ed176e9610c479bcc4cc7552a738e61f75114761dba0ec60cd264bbab763c5d5abcc75cd8fb5651d0645179988cc6d", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "dcab5e874e4fb76bc4312528e9d76dfae56145922533089734110bf5653f4d77", "result": "valid", "flags": []}, {"tcId": 161, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "04341592390ccce485de8880f3d727f664c381914a1becec383b35586751fc81c2add71852b87016e1019cae7a9080e75ce0b0b8aac175d692d5e7b4dad088f5cc", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "4ce2701b2be63a0083a4c53f7a0bf04cf871654f5edb6f625e3ea5e7d0bdcc90", "result": "valid", "flags": []}, {"tcId": 162, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "04fa764b6b76a86c3b762120825d353a24766208c1f5cc0fe3fe7998026a2ec5c43bb2f948fd94cdaa5869b1e0e73a4d97035cc49357fb7b74d7ed0a2c5b8d54eb", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "7abef9765cca721320fbf8edcbef6d2ba25d17b70ffa1776029bc38fe677a12c", "result": "valid", "flags": []}, {"tcId": 163, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "04a71fbb617199bd585b4b66212ca33ca9e09370e6bf15c8ea0acefd9c8e945d06840f058863078e743e220ff99f23bbc1daa36835d4b1269f0a7536e63f06d853", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "5f61404dbbbc2867dff95c1f37ed44f4cb8fabcd223b03739d888308d13bc412", "result": "valid", "flags": []}, {"tcId": 164, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "0413c8292d854d39451c0c63a802b8c03e4fcb875ef01239896295ba1c0f386975f82df197086fd86032cb36b69a27876dd75a8e9679f36ffc2210edb128d4be13", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "8d673a577e35bf9d5d00676c08b2c739617c46a052188403aa06dc714af6acc1", "result": "valid", "flags": []}, {"tcId": 165, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "040cd9df415acc0c32fd4e3d6924ce53075b0452bf919a2ab2ebe26597570f1ecd5985d8d2c5df78fc100f87efb6dfa9543757bdffecf083dfcd1ecb38de6c23f8", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "a7835ffee0f2a69dfcf70d4e798dbe3ed32ba03cfddae5ddd11d8c0ac3d74f9b", "result": "valid", "flags": []}, {"tcId": 166, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "04d2dbea4046b23fd2b233d1ce31dceddb89b25f26c0627a9d2db3c5605c9cc99535bdc8de7451c1e27e97aa91402cce3882c71269d9cbdcb5d7ac0ceb911b9b6d", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "e98ea22209cd397edb6c319648c1eb24bc4d39598ab11995571926684ce2ceca", "result": "valid", "flags": []}, {"tcId": 167, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "04888fb044fb2b6caa60366bfa662adba479b8365a6555a29887d580f587086ba8482f4ec24082a48d6402afa1622143f26e61d91b7e30d6a4b223630ee10f70fb", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "91b65733860b1bdb9541d9f55895a3dbb3f13c199251d33006b6dcf90ac349ed", "result": "valid", "flags": []}, {"tcId": 168, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "042e2bec134249379d57700301f3a58e4b395a4d28370d2a06e65e7ac89ed76ac697dc960bd795cdf4fbcfdd75149057b8e022331c7b5461f383ac589d764df333", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "1fdf7c5c48047a113e5e5d1b7ed593337e769231cca5c7110160e0c1b97f4256", "result": "valid", "flags": []}, {"tcId": 169, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "04c78cda7e3b9e1772ebed30b2b51dcf155a69a0fc504557836e25147cfb8127d2f8289cf38b033d3763c8f9f6c091787a3142fb83dff5719590282c6f852e0105", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "ba0abc3e71726cb51330489176357b81b8074d7690e4e82e9a3c00151e1fa318", "result": "valid", "flags": []}, {"tcId": 170, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "041e3df4dd7fb7718cb0aa0dd72f8a25c83c4e804e7cbd48c5e965651f9e23bf4ef0ff40dd9796e4a9a5eddd2c4ca4ebd10990d8fb8918d12d53c76001afa9de7f", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "16e632f9752d36602c95ec274b32ad594f39f6ac3bd4b0b20f8637392142cef4", "result": "valid", "flags": []}, {"tcId": 171, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "04e5c5dc3fd88d85668b3b709fd6b4232f1f80949cbccb5588363e6c217a2b3ed88dbd0d6e3cc97f3081d16602aa3d1b655ee0791c87fcb5abe6217d8c8513807e", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "9eed4b96569f604a4d3f5af97499807111fc9888c458ece2e3000e245c2c02b0", "result": "valid", "flags": []}, {"tcId": 172, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "04021c41eceec24e0fba894ad7415a9598cbcd14fa6ca46e25575268a1d8e5bbc63f846c6a185fa3f23bb92c14e7e2cba8c74047c09af766f55ef0c907c80d9451", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "21ac32013838812621dbb584965bded6fc851d3a029810679bc57b2381bb7a7d", "result": "valid", "flags": []}, {"tcId": 173, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "048e24192cd33335a114f5070266c014cb0d8c704d16d6042e89c17597bcd4e77ebdb4c5171704c2c09275c22a310e0c4fe092e4084856da99b94abbfa9f469f48", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "fc5978da01ca83e127dddf989a0358871b3c4ce0755bfb020633db467e21a53c", "result": "valid", "flags": []}, {"tcId": 174, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "0431c90ae47a93d09a2352b6f3677e7975ea62aadedb56c118eb8b9f771e2dd9f5f2601fb9cca2304e594423cf48064dbed17ae40452f18be6ae018321911e8cb3", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "9f417341261aa45d396b0ccf2a3dee7a466ca47e3ce86ecd2071d9c4db08820e", "result": "valid", "flags": []}, {"tcId": 175, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "04d2f211cfab84e01c8e5544036234debe35ae103bb878d7abcea6825f753e03a385f7f1870e64f1262af67a25ef9880419f45608e7f9da6dee83f5f46ceb53dcb", "private": "00809c461d8b39163537ff8f5ef5b977e4cdb980e70e38a7ee0b37cc876729e9ff", "shared": "f419febb32c254611adf569c2d583b17542b1538caa0001967f0a4bc34b8b789", "result": "valid", "flags": []}, {"tcId": 176, "comment": "edge case private key", "public": "0431028f3377fc8f2b1967edaab90213acad0da9f50897f08f57537f78f116744743a1930189363bbde2ac4cbd1649cdc6f451add71dd2f16a8a867f2b17caa16b", "private": "03", "shared": "85a0b58519b28e70a694ec5198f72c4bfdabaa30a70f7143b5b1cd7536f716ca", "result": "valid", "flags": []}, {"tcId": 177, "comment": "edge case private key", "public": "0431028f3377fc8f2b1967edaab90213acad0da9f50897f08f57537f78f116744743a1930189363bbde2ac4cbd1649cdc6f451add71dd2f16a8a867f2b17caa16b", "private": "00ffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "shared": "a329a7d80424ea2d6c904393808e510dfbb28155092f1bac284dceda1f13afe5", "result": "valid", "flags": []}, {"tcId": 178, "comment": "edge case private key", "public": "0431028f3377fc8f2b1967edaab90213acad0da9f50897f08f57537f78f116744743a1930189363bbde2ac4cbd1649cdc6f451add71dd2f16a8a867f2b17caa16b", "private": "0100000000000000000000000000000000000000000000000000000000000000", "shared": "bd26d0293e8851c51ebe0d426345683ae94026aca545282a4759faa85fde6687", "result": "valid", "flags": []}, {"tcId": 179, "comment": "edge case private key", "public": "0431028f3377fc8f2b1967edaab90213acad0da9f50897f08f57537f78f116744743a1930189363bbde2ac4cbd1649cdc6f451add71dd2f16a8a867f2b17caa16b", "private": "7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "shared": "ea9350b2490a2010c7abf43fb1a38be729a2de375ea7a6ac34ff58cc87e51b6c", "result": "valid", "flags": []}, {"tcId": 180, "comment": "edge case private key", "public": "0431028f3377fc8f2b1967edaab90213acad0da9f50897f08f57537f78f116744743a1930189363bbde2ac4cbd1649cdc6f451add71dd2f16a8a867f2b17caa16b", "private": "008000000000000000000000000000000000000000000000000000000000000000", "shared": "34eed3f6673d340b6f716913f6dfa36b5ac85fa667791e2d6a217b0c0b7ba807", "result": "valid", "flags": []}, {"tcId": 181, "comment": "edge case private key", "public": "0431028f3377fc8f2b1967edaab90213acad0da9f50897f08f57537f78f116744743a1930189363bbde2ac4cbd1649cdc6f451add71dd2f16a8a867f2b17caa16b", "private": "00ffffffff00000000ffffffffffffffffbce6faada7179e83f3b9cac2fc632551", "shared": "1354ce6692c9df7b6fc3119d47c56338afbedccb62faa546c0fe6ed4959e41c3", "result": "valid", "flags": []}, {"tcId": 182, "comment": "edge case private key", "public": "0431028f3377fc8f2b1967edaab90213acad0da9f50897f08f57537f78f116744743a1930189363bbde2ac4cbd1649cdc6f451add71dd2f16a8a867f2b17caa16b", "private": "00ffffffff00000000ffffffffffffffffbce6faada7179e84f3a9cac2fc632551", "shared": "fe7496c30d534995f0bf428b5471c21585aaafc81733916f0165597a55d12cb4", "result": "valid", "flags": []}, {"tcId": 183, "comment": "edge case private key", "public": "0431028f3377fc8f2b1967edaab90213acad0da9f50897f08f57537f78f116744743a1930189363bbde2ac4cbd1649cdc6f451add71dd2f16a8a867f2b17caa16b", "private": "00ffffffff00000000ffffffffffffffffbce6faada7179e84f3b1cac2fc632551", "shared": "348bf8042e4edf1d03c8b36ab815156e77c201b764ed4562cfe2ee90638ffef5", "result": "valid", "flags": []}, {"tcId": 184, "comment": "edge case private key", "public": "0431028f3377fc8f2b1967edaab90213acad0da9f50897f08f57537f78f116744743a1930189363bbde2ac4cbd1649cdc6f451add71dd2f16a8a867f2b17caa16b", "private": "00ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac1fc632551", "shared": "6e4ec5479a7c20a537501700484f6f433a8a8fe53c288f7a25c8e8c92d39e8dc", "result": "valid", "flags": []}, {"tcId": 185, "comment": "edge case private key", "public": "0431028f3377fc8f2b1967edaab90213acad0da9f50897f08f57537f78f116744743a1930189363bbde2ac4cbd1649cdc6f451add71dd2f16a8a867f2b17caa16b", "private": "00ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc6324f3", "shared": "f7407d61fdf581be4f564621d590ca9b7ba37f31396150f9922f1501da8c83ef", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 186, "comment": "edge case private key", "public": "0431028f3377fc8f2b1967edaab90213acad0da9f50897f08f57537f78f116744743a1930189363bbde2ac4cbd1649cdc6f451add71dd2f16a8a867f2b17caa16b", "private": "00ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632533", "shared": "82236fd272208693e0574555ca465c6cc512163486084fa57f5e1bd2e2ccc0b3", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 187, "comment": "edge case private key", "public": "0431028f3377fc8f2b1967edaab90213acad0da9f50897f08f57537f78f116744743a1930189363bbde2ac4cbd1649cdc6f451add71dd2f16a8a867f2b17caa16b", "private": "00ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632543", "shared": "06537149664dba1a9924654cb7f787ed224851b0df25ef53fcf54f8f26cd5f3f", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 188, "comment": "edge case private key", "public": "0431028f3377fc8f2b1967edaab90213acad0da9f50897f08f57537f78f116744743a1930189363bbde2ac4cbd1649cdc6f451add71dd2f16a8a867f2b17caa16b", "private": "00ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254b", "shared": "f2b38539bce995d443c7bfeeefadc9e42cc2c89c60bf4e86eac95d51987bd112", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 189, "comment": "edge case private key", "public": "0431028f3377fc8f2b1967edaab90213acad0da9f50897f08f57537f78f116744743a1930189363bbde2ac4cbd1649cdc6f451add71dd2f16a8a867f2b17caa16b", "private": "00ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254e", "shared": "85a0b58519b28e70a694ec5198f72c4bfdabaa30a70f7143b5b1cd7536f716ca", "result": "valid", "flags": []}, {"tcId": 190, "comment": "edge case private key", "public": "0431028f3377fc8f2b1967edaab90213acad0da9f50897f08f57537f78f116744743a1930189363bbde2ac4cbd1649cdc6f451add71dd2f16a8a867f2b17caa16b", "private": "00ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc63254f", "shared": "027b013a6f166db655d69d643c127ef8ace175311e667dff2520f5b5c75b7659", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 191, "comment": "CVE-2017-8932", "public": "04023819813ac969847059028ea88a1f30dfbcde03fc791d3a252c6b41211882eaf93e4ae433cc12cf2a43fc0ef26400c0e125508224cdb649380f25479148a4ad", "private": "2a265f8bcbdcaf94d58519141e578124cb40d64a501fba9c11847b28965bc737", "shared": "4d4de80f1534850d261075997e3049321a0864082d24a917863366c0724f5ae3", "result": "valid", "flags": []}, {"tcId": 192, "comment": "CVE-2017-8932", "public": "04cc11887b2d66cbae8f4d306627192522932146b42f01d3c6f92bd5c8ba739b06a2f08a029cd06b46183085bae9248b0ed15b70280c7ef13a457f5af382426031", "private": "313f72ff9fe811bf573176231b286a3bdb6f1b14e05c40146590727a71c3bccd", "shared": "831c3f6b5f762d2f461901577af41354ac5f228c2591f84f8a6e51e2e3f17991", "result": "valid", "flags": []}, {"tcId": 193, "comment": "point is not on curve", "public": "0400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 194, "comment": "point is not on curve", "public": "0400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 195, "comment": "point is not on curve", "public": "040000000000000000000000000000000000000000000000000000000000000000ffffffff00000001000000000000000000000000fffffffffffffffffffffffe", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 196, "comment": "point is not on curve", "public": "040000000000000000000000000000000000000000000000000000000000000000ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 197, "comment": "point is not on curve", "public": "0400000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 198, "comment": "point is not on curve", "public": "0400000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 199, "comment": "point is not on curve", "public": "040000000000000000000000000000000000000000000000000000000000000001ffffffff00000001000000000000000000000000fffffffffffffffffffffffe", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 200, "comment": "point is not on curve", "public": "040000000000000000000000000000000000000000000000000000000000000001ffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 201, "comment": "point is not on curve", "public": "04ffffffff00000001000000000000000000000000fffffffffffffffffffffffe0000000000000000000000000000000000000000000000000000000000000000", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 202, "comment": "point is not on curve", "public": "04ffffffff00000001000000000000000000000000fffffffffffffffffffffffe0000000000000000000000000000000000000000000000000000000000000001", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 203, "comment": "point is not on curve", "public": "04ffffffff00000001000000000000000000000000fffffffffffffffffffffffeffffffff00000001000000000000000000000000fffffffffffffffffffffffe", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 204, "comment": "point is not on curve", "public": "04ffffffff00000001000000000000000000000000fffffffffffffffffffffffeffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 205, "comment": "point is not on curve", "public": "04ffffffff00000001000000000000000000000000ffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000000000000000", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 206, "comment": "point is not on curve", "public": "04ffffffff00000001000000000000000000000000ffffffffffffffffffffffff0000000000000000000000000000000000000000000000000000000000000001", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 207, "comment": "point is not on curve", "public": "04ffffffff00000001000000000000000000000000ffffffffffffffffffffffffffffffff00000001000000000000000000000000fffffffffffffffffffffffe", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 208, "comment": "point is not on curve", "public": "04ffffffff00000001000000000000000000000000ffffffffffffffffffffffffffffffff00000001000000000000000000000000ffffffffffffffffffffffff", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 209, "comment": "", "public": "", "private": "7e4aa54f714bf01df85c50269bea3a86721f84afe74f7b41ea58abcf3474e88d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 210, "comment": "invalid public key", "public": "02fd4bf61763b46581fd9174d623516cf3c81edd40e29ffa2777fb6cb0ae3ce535", "private": "6f953faff3599e6c762d7f4cabfeed092de2add1df1bc5748c6cbb725cf35458", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 211, "comment": "public key is a low order point on twist", "public": "03efdde3b32872a9effcf3b94cbf73aa7b39f9683ece9121b9852167f4e3da609b", "private": "00d27edf0ff5b6b6b465753e7158370332c153b468a1be087ad0f490bdb99e5f02", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 212, "comment": "public key is a low order point on twist", "public": "02efdde3b32872a9effcf3b94cbf73aa7b39f9683ece9121b9852167f4e3da609b", "private": "00d27edf0ff5b6b6b465753e7158370332c153b468a1be087ad0f490bdb99e5f03", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 213, "comment": "public key is a low order point on twist", "public": "02c49524b2adfd8f5f972ef554652836e2efb2d306c6d3b0689234cec93ae73db5", "private": "0095ead84540c2d027aa3130ff1b47888cc1ed67e8dda46156e71ce0991791e835", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 214, "comment": "public key is a low order point on twist", "public": "0318f9bae7747cd844e98525b7ccd0daf6e1d20a818b2175a9a91e4eae5343bc98", "private": "00a8681ef67fb1f189647d95e8db00c52ceef6d41a85ba0a5bd74c44e8e62c8aa4", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 215, "comment": "public key is a low order point on twist", "public": "0218f9bae7747cd844e98525b7ccd0daf6e1d20a818b2175a9a91e4eae5343bc98", "private": "00a8681ef67fb1f189647d95e8db00c52ceef6d41a85ba0a5bd74c44e8e62c8aa5", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 216, "comment": "public key is a low order point on twist", "public": "03c49524b2adfd8f5f972ef554652836e2efb2d306c6d3b0689234cec93ae73db5", "private": "0095ead84540c2d027aa3130ff1b47888cc1ed67e8dda46156e71ce0991791e834", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}]}]}