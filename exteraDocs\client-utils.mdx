---
title: Client Utilities
description: This module provides utility functions and classes for asynchronous tasks, making API requests, sending messages, and displaying UI notifications like bulletins.
icon: PocketKnife
---

This module contains helpers for interacting with Telegram's core functionalities, managing background tasks, and providing user feedback.

## Queues (Background Threads)

For performing long-running or blocking operations (like network requests or heavy computations) without freezing the UI, you should run your functions on a background thread. `client_utils` provides `run_on_queue` for this.

```python
import time
from client_utils import run_on_queue
from android_utils import log

def my_long_task(parameter: str):
    log(f"Task started with: {parameter}")
    time.sleep(5) # Simulate a long operation
    log(f"Task finished for: {parameter}")
    # If you need to update U<PERSON> after this, use run_on_ui_thread here

# Run on the default PLUGINS_QUEUE
run_on_queue(lambda: my_long_task("some_data"))
```

You can specify which queue to use and add a delay (in milliseconds):

```python
from client_utils import GLOBAL_QUEUE

# Run on GLOBAL_QUEUE after a 2.5 second delay
run_on_queue(lambda: my_long_task("other_data"), GLOBAL_QUEUE, 2500)
```

**Available Queues (as string constants):**
These allow you to target specific Telegram dispatch queues.

```python
STAGE_QUEUE = "stageQueue"                # For critical, sequential operations
GLOBAL_QUEUE = "globalQueue"              # General purpose background tasks
CACHE_CLEAR_QUEUE = "cacheClearQueue"    # Cache management tasks
SEARCH_QUEUE = "searchQueue"              # Search operations
PHONE_BOOK_QUEUE = "phoneBookQueue"      # Phone book and contact sync
THEME_QUEUE = "themeQueue"                # Theme application and processing
EXTERNAL_NETWORK_QUEUE = "externalNetworkQueue" # Network requests not related to Telegram API
PLUGINS_QUEUE = "pluginsQueue"            # **Default queue for `run_on_queue` if not specified.** Recommended for most plugin background tasks.
```

To get a direct Java `org.telegram.messenger.DispatchQueue` instance:

```python
from client_utils import get_queue_by_name

plugins_dispatch_queue = get_queue_by_name(PLUGINS_QUEUE)
if plugins_dispatch_queue:
    # You can use methods of DispatchQueue directly, e.g., plugins_dispatch_queue.postRunnable(...)
    pass
```

## Utilities

### Sending Telegram API Requests

To send raw Telegram API requests (TLObjects), use `send_request`. This function handles sending the request via the current account's connection manager and invoking your callback upon response or error.

`RequestCallback` is a `dynamic_proxy` for `org.telegram.tgnet.RequestDelegate`, simplifying callback implementation in Python.

```python
from org.telegram.tgnet import TLRPC
from client_utils import send_request, RequestCallback, get_messages_controller
from android_utils import log
from java.lang import Integer

def handle_read_contents_response(response: TLRPC.TLObject, error: TLRPC.TL_error):
    if error:
        log(f"Error reading message contents: {error.text}")
        return
    if response and isinstance(response, TLRPC.TL_messages_affectedMessages): # Or other expected type
        log(f"Successfully read contents. PTS: {response.pts}, Count: {response.pts_count}")
    else:
        log(f"Unexpected response type for readMessageContents: {type(response)}")

# Create the request object
req = TLRPC.TL_messages_readMessageContents()
req.id.add(Integer(12345))

# Create the callback proxy
callback_proxy = RequestCallback(handle_read_contents_response)

# Send the request
connection_request_id = send_request(req, callback_proxy)
log(f"Sent TL_messages_readMessageContents, request ID: {connection_request_id}")
```

### Sending Messages

The `send_message` utility simplifies sending various types of messages. It takes a dictionary of parameters.
This function internally handles execution on the UI thread, so you don't need to wrap calls to `send_message` in `run_on_ui_thread`.

```python
from client_utils import send_message

# Basic text message
text_params = {
    "peer": 12345678,  # peer ID in Telegram API format (without "-100")
    "message": "Hello from my plugin!"
}

send_message(text_params)
```

A comprehensive list of supported parameters for the `params` dictionary can be inferred from the fields of `org.telegram.messenger.SendMessagesHelper.SendMessageParams`. You can find its definition [here (SendMessagesHelper.java#L10185)](https://github.com/DrKLO/Telegram/blob/17067dfc6a1f69618a006b14e1741b75c64b276a/TMessagesProj/src/main/java/org/telegram/messenger/SendMessagesHelper.java#L10185). Common parameters include `peer`, `message`, `replyToMsg`, `entities`, `scheduleDate`, etc.

<Callout type="info" title="Sending Files">
For sending files (photos, videos, documents not already uploaded), you'll typically need to use the Java methods of `SendMessagesHelper` directly, often involving preparing the file path, creating `TLRPC.InputMedia` objects, etc. The `send_message` Python utility is more for messages where media is already represented by an ID or for simpler text-based messages.
</Callout>

### Displaying Bulletins (Bottom Notifications)

Bulletins are small, non-intrusive notifications shown at the bottom of the screen. The `BulletinHelper` class provides an easy way to show them.

For detailed information and examples on how to use various types of bulletins, please refer to the [**Bulletin Helper documentation**](/docs/bulletin-helper).

```python
from ui.bulletin import BulletinHelper

# Example:
BulletinHelper.show_info("This is an informational message.")
```

### Accessing Controllers and Managers

`client_utils.py` provides convenient getter functions for accessing various core Telegram controllers, managers, and configurations for the currently selected account.

```python
from client_utils import (
    get_account_instance, get_messages_controller, get_contacts_controller,
    get_media_data_controller, get_connections_manager, get_location_controller,
    get_notifications_controller, get_messages_storage, get_send_messages_helper,
    get_file_loader, get_secret_chat_helper, get_download_controller,
    get_notifications_settings, get_notification_center, get_media_controller,
    get_user_config
)

# Examples:
account_instance = get_account_instance() # Current AccountInstance
messages_controller = get_messages_controller() # MessagesController
connections_manager = get_connections_manager() # ConnectionsManager
send_helper = get_send_messages_helper() # SendMessagesHelper
user_cfg = get_user_config() # UserConfig

# Use these instances to interact with Telegram's internal systems.
if user_cfg.getCurrentUser():
  user_name = user_cfg.getCurrentUser().first_name

messages_controller.loadDialogs(0, 50, True) # Example method call
```
These functions simplify access to key components of the Telegram client.
