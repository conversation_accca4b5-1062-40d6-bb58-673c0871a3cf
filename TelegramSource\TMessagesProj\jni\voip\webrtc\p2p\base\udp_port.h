/*
 *  Copyright 2004 The WebRTC Project Authors. All rights reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef P2P_BASE_UDP_PORT_H_
#define P2P_BASE_UDP_PORT_H_

// StunPort will be handling UDPPort functionality.
#include "p2p/base/stun_port.h"

#endif  // P2P_BASE_UDP_PORT_H_
