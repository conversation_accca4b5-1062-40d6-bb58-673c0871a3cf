# See //base/README.md to find qualification for being an owner.

<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>
<EMAIL>

# For Bind/Callback:
per-file bind*=<EMAIL>
per-file callback*=<EMAIL>

# For Android-specific changes:
per-file *android*=file://base/android/OWNERS
per-file BUILD.gn=file://base/android/OWNERS

# For Fuchsia-specific changes:
per-file *_fuchsia*=file://build/fuchsia/OWNERS

# For Windows-specific changes:
per-file *_win*=file://base/win/OWNERS

# For FeatureList API:
per-file feature_list*=<EMAIL>
per-file feature_list*=<EMAIL>

# Restricted since rand_util.h also backs the cryptographically secure RNG.
per-file rand_util*=set noparent
per-file rand_util*=file://ipc/SECURITY_OWNERS

# For TCMalloc tests:
per-file security_unittest.cc=<EMAIL>

# For Value:
per-file values*=<EMAIL>

# COMPONENT: Internals>Core
