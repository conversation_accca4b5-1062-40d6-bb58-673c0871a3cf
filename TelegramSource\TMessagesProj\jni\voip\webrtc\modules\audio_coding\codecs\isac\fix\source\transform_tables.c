/*
 *  Copyright (c) 2014 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

/*
 * This file contains trigonometric functions look-up tables used in
 * transform functions WebRtcIsacfix_Time2Spec and WebRtcIsacfix_Spec2Time.
 */

#include <stdint.h>

#include "modules/audio_coding/codecs/isac/fix/source/settings.h"

/* Cosine table 1 in Q14. */
const int16_t WebRtcIsacfix_kCosTab1[FRAMESAMPLES/2] = {
  16384,  16383,  16378,  16371,  16362,  16349,  16333,  16315,  16294,  16270,
  16244,  16214,  16182,  16147,  16110,  16069,  16026,  15980,  15931,  15880,
  15826,  15769,  15709,  15647,  15582,  15515,  15444,  15371,  15296,  15218,
  15137,  15053,  14968,  14879,  14788,  14694,  14598,  14500,  14399,  14295,
  14189,  14081,  13970,  13856,  13741,  13623,  13502,  13380,  13255,  13128,
  12998,  12867,  12733,  12597,  12458,  12318,  12176,  12031,  11885,  11736,
  11585,  11433,  11278,  11121,  10963,  10803,  10641,  10477,  10311,  10143,
  9974,   9803,   9630,   9456,   9280,   9102,   8923,   8743,   8561,   8377,
  8192,   8006,   7818,   7629,   7438,   7246,   7053,   6859,   6664,   6467,
  6270,   6071,   5872,   5671,   5469,   5266,   5063,   4859,   4653,   4447,
  4240,   4033,   3825,   3616,   3406,   3196,   2986,   2775,   2563,   2351,
  2139,   1926,   1713,   1499,   1285,   1072,    857,    643,    429,    214,
  0,   -214,   -429,   -643,   -857,  -1072,  -1285,  -1499,  -1713,  -1926,
  -2139,  -2351,  -2563,  -2775,  -2986,  -3196,  -3406,  -3616,  -3825,  -4033,
  -4240,  -4447,  -4653,  -4859,  -5063,  -5266,  -5469,  -5671,  -5872,  -6071,
  -6270,  -6467,  -6664,  -6859,  -7053,  -7246,  -7438,  -7629,  -7818,  -8006,
  -8192,  -8377,  -8561,  -8743,  -8923,  -9102,  -9280,  -9456,  -9630,  -9803,
  -9974, -10143, -10311, -10477, -10641, -10803, -10963, -11121, -11278, -11433,
  -11585, -11736, -11885, -12031, -12176, -12318, -12458, -12597, -12733,
  -12867, -12998, -13128, -13255, -13380, -13502, -13623, -13741, -13856,
  -13970, -14081, -14189, -14295, -14399, -14500, -14598, -14694, -14788,
  -14879, -14968, -15053, -15137, -15218, -15296, -15371, -15444, -15515,
  -15582, -15647, -15709, -15769, -15826, -15880, -15931, -15980, -16026,
  -16069, -16110, -16147, -16182, -16214, -16244, -16270, -16294, -16315,
  -16333, -16349, -16362, -16371, -16378, -16383
};

/* Sine table 1 in Q14. */
const int16_t WebRtcIsacfix_kSinTab1[FRAMESAMPLES/2] = {
  0,   214,   429,   643,   857,  1072,  1285,  1499,  1713,  1926,
  2139,  2351,  2563,  2775,  2986,  3196,  3406,  3616,  3825,  4033,
  4240,  4447,  4653,  4859,  5063,  5266,  5469,  5671,  5872,  6071,
  6270,  6467,  6664,  6859,  7053,  7246,  7438,  7629,  7818,  8006,
  8192,  8377,  8561,  8743,  8923,  9102,  9280,  9456,  9630,  9803,
  9974, 10143, 10311, 10477, 10641, 10803, 10963, 11121, 11278, 11433,
  11585, 11736, 11885, 12031, 12176, 12318, 12458, 12597, 12733, 12867,
  12998, 13128, 13255, 13380, 13502, 13623, 13741, 13856, 13970, 14081,
  14189, 14295, 14399, 14500, 14598, 14694, 14788, 14879, 14968, 15053,
  15137, 15218, 15296, 15371, 15444, 15515, 15582, 15647, 15709, 15769,
  15826, 15880, 15931, 15980, 16026, 16069, 16110, 16147, 16182, 16214,
  16244, 16270, 16294, 16315, 16333, 16349, 16362, 16371, 16378, 16383,
  16384, 16383, 16378, 16371, 16362, 16349, 16333, 16315, 16294, 16270,
  16244, 16214, 16182, 16147, 16110, 16069, 16026, 15980, 15931, 15880,
  15826, 15769, 15709, 15647, 15582, 15515, 15444, 15371, 15296, 15218,
  15137, 15053, 14968, 14879, 14788, 14694, 14598, 14500, 14399, 14295,
  14189, 14081, 13970, 13856, 13741, 13623, 13502, 13380, 13255, 13128,
  12998, 12867, 12733, 12597, 12458, 12318, 12176, 12031, 11885, 11736,
  11585, 11433, 11278, 11121, 10963, 10803, 10641, 10477, 10311, 10143,
  9974,  9803,  9630,  9456,  9280,  9102,  8923,  8743,  8561,  8377,
  8192,  8006,  7818,  7629,  7438,  7246,  7053,  6859,  6664,  6467,
  6270,  6071,  5872,  5671,  5469,  5266,  5063,  4859,  4653,  4447,
  4240,  4033,  3825,  3616,  3406,  3196,  2986,  2775,  2563,  2351,
  2139,  1926,  1713,  1499,  1285,  1072,   857,   643,   429,   214
};


/* Sine table 2 in Q14. */
const int16_t WebRtcIsacfix_kSinTab2[FRAMESAMPLES/4] = {
  16384, -16381, 16375, -16367, 16356, -16342, 16325, -16305, 16283, -16257,
  16229, -16199, 16165, -16129, 16090, -16048, 16003, -15956, 15906, -15853,
  15798, -15739, 15679, -15615, 15549, -15480, 15408, -15334, 15257, -15178,
  15095, -15011, 14924, -14834, 14741, -14647, 14549, -14449, 14347, -14242,
  14135, -14025, 13913, -13799, 13682, -13563, 13441, -13318, 13192, -13063,
  12933, -12800, 12665, -12528, 12389, -12247, 12104, -11958, 11810, -11661,
  11509, -11356, 11200, -11042, 10883, -10722, 10559, -10394, 10227, -10059,
  9889,  -9717,  9543,  -9368,  9191,  -9013,  8833,  -8652,  8469,  -8285,
  8099,  -7912,  7723,  -7534,  7342,  -7150,  6957,  -6762,  6566,  -6369,
  6171,  -5971,  5771,  -5570,  5368,  -5165,  4961,  -4756,  4550,  -4344,
  4137,  -3929,  3720,  -3511,  3301,  -3091,  2880,  -2669,  2457,  -2245,
  2032,  -1819,  1606,  -1392,  1179,   -965,   750,   -536,   322,   -107
};

#if defined(MIPS32_LE)
/* Cosine table 2 in Q14. Used only on MIPS platforms. */
const int16_t WebRtcIsacfix_kCosTab2[FRAMESAMPLES/4] = {
  107,   -322,   536,   -750,   965,  -1179,  1392,  -1606,  1819,  -2032,
  2245,  -2457,  2669,  -2880,  3091,  -3301,  3511,  -3720,  3929,  -4137,
  4344,  -4550,  4756,  -4961,  5165,  -5368,  5570,  -5771,  5971,  -6171,
  6369,  -6566,  6762,  -6957,  7150,  -7342,  7534,  -7723,  7912,  -8099,
  8285,  -8469,  8652,  -8833,  9013,  -9191,  9368,  -9543,  9717,  -9889,
  10059, -10227, 10394, -10559, 10722, -10883, 11042, -11200, 11356, -11509,
  11661, -11810, 11958, -12104, 12247, -12389, 12528, -12665, 12800, -12933,
  13063, -13192, 13318, -13441, 13563, -13682, 13799, -13913, 14025, -14135,
  14242, -14347, 14449, -14549, 14647, -14741, 14834, -14924, 15011, -15095,
  15178, -15257, 15334, -15408, 15480, -15549, 15615, -15679, 15739, -15798,
  15853, -15906, 15956, -16003, 16048, -16090, 16129, -16165, 16199, -16229,
  16257, -16283, 16305, -16325, 16342, -16356, 16367, -16375, 16381, -16384
};
#endif
