// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/SoftwareVideoEncoderFactory

#ifndef org_webrtc_SoftwareVideoEncoderFactory_JNI
#define org_webrtc_SoftwareVideoEncoderFactory_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_SoftwareVideoEncoderFactory[];
const char kClassPath_org_webrtc_SoftwareVideoEncoderFactory[] =
    "org/webrtc/SoftwareVideoEncoderFactory";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_SoftwareVideoEncoderFactory_clazz(nullptr);
#ifndef org_webrtc_SoftwareVideoEncoderFactory_clazz_defined
#define org_webrtc_SoftwareVideoEncoderFactory_clazz_defined
inline jclass org_webrtc_SoftwareVideoEncoderFactory_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_SoftwareVideoEncoderFactory,
      &g_org_webrtc_SoftwareVideoEncoderFactory_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {

static jlong JNI_SoftwareVideoEncoderFactory_CreateEncoder(JNIEnv* env, jlong factory,
    const jni_zero::JavaParamRef<jobject>& videoCodecInfo);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_SoftwareVideoEncoderFactory_nativeCreateEncoder(
    JNIEnv* env,
    jclass jcaller,
    jlong factory,
    jobject videoCodecInfo) {
  return JNI_SoftwareVideoEncoderFactory_CreateEncoder(env, factory,
      jni_zero::JavaParamRef<jobject>(env, videoCodecInfo));
}

static jlong JNI_SoftwareVideoEncoderFactory_CreateFactory(JNIEnv* env);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_SoftwareVideoEncoderFactory_nativeCreateFactory(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_SoftwareVideoEncoderFactory_CreateFactory(env);
}

static jni_zero::ScopedJavaLocalRef<jobject>
    JNI_SoftwareVideoEncoderFactory_GetSupportedCodecs(JNIEnv* env, jlong factory);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_SoftwareVideoEncoderFactory_nativeGetSupportedCodecs(
    JNIEnv* env,
    jclass jcaller,
    jlong factory) {
  return JNI_SoftwareVideoEncoderFactory_GetSupportedCodecs(env, factory).Release();
}


}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_SoftwareVideoEncoderFactory_JNI
