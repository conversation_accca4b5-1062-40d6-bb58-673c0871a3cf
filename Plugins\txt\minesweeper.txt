from base_plugin import BasePlugin, MenuItemData, MenuItemType
from ui.alert import AlertDialogBuilder
from ui.settings import <PERSON><PERSON>, Divider, Input
from client_utils import get_last_fragment
from android_utils import run_on_ui_thread
import random

from hook_utils import find_class
from java import jboolean, dynamic_proxy

TextView = find_class("android.widget.TextView")
EditText = find_class("android.widget.EditText")
GridLayout = find_class("android.widget.GridLayout")
LinearLayout = find_class("android.widget.LinearLayout")
FrameLayout = find_class("android.widget.FrameLayout")
R = find_class("org.telegram.messenger.R")
AndroidUtilities = find_class("org.telegram.messenger.AndroidUtilities")
Theme = find_class("org.telegram.ui.ActionBar.Theme")
Gravity = find_class("android.view.Gravity")
InputType = find_class("android.text.InputType")
OnClickListenerInterface = find_class("android.view.View$OnClickListener")
OnLongClickListenerInterface = find_class("android.view.View$OnLongClickListener")

__id__ = "minesweeper"
__name__ = "Minesweeper"
__description__ = "Плагин для игры в Cапёр. Использование: в чате Три Точки --> Плагины --> Minesweeper"
__author__ = "@i9_12900kf"
__version__ = "1.0.0"
__min_version__ = "11.12.0"
__icon__ = "uniposting/54"

KEY_MINES_COUNT = "minesweeper_mines_count"
DEFAULT_MINES_COUNT = "10"

class Cell:
    def __init__(self):
        self.is_mine = False
        self.value = 0
        self.is_visible = False
        self.is_flagged = False

class MineSweeperGame:
    def __init__(self, rows=8, cols=8, mines=10):
        self.rows, self.cols, self.mines = rows, cols, mines
        self.game_over, self.game_won, self.first_click = False, False, True
        self.flags_placed = 0
        self.dialog, self.title_view = None, None
        self.board = [[Cell() for _ in range(cols)] for _ in range(rows)]

    def generate_board(self, first_click_row, first_click_col):
        self.board = [[Cell() for _ in range(self.cols)] for _ in range(self.rows)]
        possible_mine_positions = [(r, c) for r in range(self.rows) for c in range(self.cols) if abs(r - first_click_row) > 1 or abs(c - first_click_col) > 1]
        num_mines = min(self.mines, len(possible_mine_positions))
        mine_positions = random.sample(possible_mine_positions, num_mines)
 
        for r, c in mine_positions:
            self.board[r][c].is_mine = True
            for i in range(r - 1, r + 2):
                for j in range(c - 1, c + 2):
                    if 0 <= i < self.rows and 0 <= j < self.cols and not self.board[i][j].is_mine:
                        self.board[i][j].value += 1

    def reveal_cell(self, r, c):
        if self.game_over or self.board[r][c].is_visible or self.board[r][c].is_flagged: return
        if self.first_click:
            self.generate_board(r, c)
            self.first_click = False

        cell = self.board[r][c]
        if cell.is_mine:
            self.game_over = True
            return

        cell.is_visible = True
        if cell.value == 0:
            for i in range(r - 1, r + 2):
                for j in range(c - 1, c + 2):
                    if 0 <= i < self.rows and 0 <= j < self.cols: self.reveal_cell(i, j)
        self.check_win_condition()

    def toggle_flag(self, r, c):
        if self.game_over or self.board[r][c].is_visible: return
        cell = self.board[r][c]
        cell.is_flagged = not cell.is_flagged
        self.flags_placed += 1 if cell.is_flagged else -1

    def check_win_condition(self):
        if sum(c.is_visible for r in self.board for c in r) == self.rows * self.cols - self.mines:
            self.game_won, self.game_over = True, True

class CellClickListener(dynamic_proxy(OnClickListenerInterface)):
    def __init__(self, plugin, chat_id, r, c):
        super().__init__()
        self.plugin, self.chat_id, self.r, self.c = plugin, chat_id, r, c
    def onClick(self, view): self.plugin.on_cell_click(self.chat_id, self.r, self.c)

class CellLongClickListener(dynamic_proxy(OnLongClickListenerInterface)):
    def __init__(self, plugin, chat_id, r, c):
        super().__init__()
        self.plugin, self.chat_id, self.r, self.c = plugin, chat_id, r, c
    def onLongClick(self, view) -> jboolean: return self.plugin.on_cell_long_click(self.chat_id, self.r, self.c)

class MinesweeperPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.active_games, self.cell_views = {}, {}

    def on_plugin_load(self):
        self.add_menu_item(MenuItemData(menu_type=MenuItemType.CHAT_ACTION_MENU, text="Minesweeper", icon="msg_mini_bomb", on_click=self.show_rules_dialog))

    def create_settings(self):
        return [
            Header(text="Настройки плагина Minesweeper"),
            Input(key=KEY_MINES_COUNT, text="Количество мин", default=DEFAULT_MINES_COUNT, subtext="От 1 до 60")
        ]

    def show_rules_dialog(self, context: dict):
        chat_id = context.get("dialog_id")
        fragment = get_last_fragment(); activity = fragment and fragment.getParentActivity()
        if not activity: return

        builder = AlertDialogBuilder(activity)
        builder.set_title("Minesweeper")

        container = LinearLayout(activity)
        container.setOrientation(LinearLayout.VERTICAL)
        padding = AndroidUtilities.dp(24); container.setPadding(padding, AndroidUtilities.dp(8), padding, AndroidUtilities.dp(8))

        rules_text = "Плагин для игры в Сапёр.\n\nУправление:\nНажать на клетку - открыть клетку\nЗажать клетку - поставить флажок"
        rules_view = TextView(activity)
        rules_view.setText(rules_text); rules_view.setTextSize(16)
        rules_view.setTextColor(Theme.getColor(Theme.key_dialogTextBlack))
        rules_view.setPadding(0, 0, 0, AndroidUtilities.dp(16))
        container.addView(rules_view)

        mines_label = TextView(activity)
        mines_label.setText("Количество мин:"); mines_label.setTextSize(14)
        mines_label.setTextColor(Theme.getColor(Theme.key_dialogTextGray))
        container.addView(mines_label)

        mines_edit_text = EditText(activity)
        mines_edit_text.setText(self.get_setting(KEY_MINES_COUNT, DEFAULT_MINES_COUNT))
        mines_edit_text.setInputType(InputType.TYPE_CLASS_NUMBER)
        mines_edit_text.setTextColor(Theme.getColor(Theme.key_dialogTextBlack))
        container.addView(mines_edit_text)
        builder.set_view(container)

        def on_start(bld, which):
            try:
                mines_str = mines_edit_text.getText().toString()
                mines = int(mines_str)
                if not (1 <= mines <= 60): mines = 10
            except:
                mines = 10

            self.set_setting(KEY_MINES_COUNT, str(mines))
            bld.dismiss()
            run_on_ui_thread(lambda: self.start_new_game(chat_id, mines), 50)

        builder.set_positive_button("Начать игру", on_start)
        builder.set_negative_button("Закрыть", lambda b, w: b.dismiss())
        builder.show()

    def cleanup_game(self, chat_id: int):
        game = self.active_games.pop(chat_id, None)
        if game and game.dialog:
            try: game.dialog.dismiss()
            except: pass
        self.cell_views.pop(chat_id, None)

    def start_new_game(self, chat_id: int, mines: int):
        self.cleanup_game(chat_id)
        game = MineSweeperGame(mines=mines)
        self.active_games[chat_id], self.cell_views[chat_id] = game, {}
        self.show_board_dialog(chat_id, game)

    def show_board_dialog(self, chat_id: int, game: MineSweeperGame):
        fragment = get_last_fragment(); activity = fragment and fragment.getParentActivity()
        if not activity: return

        builder = AlertDialogBuilder(activity)

        main_container = LinearLayout(activity)
        main_container.setOrientation(LinearLayout.VERTICAL)

        title_container = LinearLayout(activity)
        title_container.setOrientation(LinearLayout.HORIZONTAL)
        padding_h = AndroidUtilities.dp(24); padding_v = AndroidUtilities.dp(12)
        title_container.setPadding(padding_h, padding_v, padding_h, padding_v)

        title_main = TextView(activity); title_main.setText("Minesweeper"); title_main.setTextSize(20)
        title_main.setTypeface(AndroidUtilities.getTypeface("fonts/rmedium.ttf"))
        title_main.setTextColor(Theme.getColor(Theme.key_dialogTextBlack))

        title_mines = TextView(activity); title_mines.setTextSize(16)
        title_mines.setTextColor(Theme.getColor(Theme.key_dialogTextGray))
        game.title_view = title_mines
        self.update_dialog_title(game)

        params_main = LinearLayout.LayoutParams(-2, -2); params_main.weight = 1.0
        title_container.addView(title_main, params_main)
        title_container.addView(title_mines)
        main_container.addView(title_container)

        grid_wrapper = FrameLayout(activity)

        grid = GridLayout(activity)
        grid.setColumnCount(game.cols); grid.setRowCount(game.rows)
        grid.setAlignmentMode(GridLayout.ALIGN_BOUNDS)

        wrapper_params = FrameLayout.LayoutParams(-2, -2)
        wrapper_params.gravity = Gravity.CENTER_HORIZONTAL
        grid.setLayoutParams(wrapper_params)

        views = self.cell_views[chat_id]
        size = AndroidUtilities.dp(36); margin = AndroidUtilities.dp(1)
        bg_color = Theme.getColor(Theme.key_chat_attachPhotoBackground)

        for r in range(game.rows):
            for c in range(game.cols):
                cell_view = TextView(activity)
                params = GridLayout.LayoutParams(GridLayout.spec(r), GridLayout.spec(c))
                params.width = params.height = size; params.setMargins(margin, margin, margin, margin)
                cell_view.setLayoutParams(params)
                cell_view.setTextSize(18); cell_view.setGravity(Gravity.CENTER)

                text, color_key, background_key = self.get_cell_style(game.board[r][c])
                cell_view.setText(text); cell_view.setTextColor(Theme.getColor(color_key))
                cell_view.setBackgroundColor(Theme.getColor(background_key))

                cell_view.setOnClickListener(CellClickListener(self, chat_id, r, c))
                cell_view.setOnLongClickListener(CellLongClickListener(self, chat_id, r, c))

                grid.addView(cell_view); views[(r, c)] = cell_view

        grid_wrapper.addView(grid)
        main_container.addView(grid_wrapper)
        builder.set_view(main_container)

        def on_close(bld, w):
            self.cleanup_game(chat_id)
        builder.set_positive_button("Закрыть", on_close)
        game.dialog = builder.show()

    def on_cell_click(self, chat_id: int, r: int, c: int):
        game = self.active_games.get(chat_id)
        if not game or game.game_over: return
        game.reveal_cell(r, c)
        self.redraw_board(chat_id)
        if game.game_over:
            self.show_game_over_dialog(chat_id, game.game_won)

    def on_cell_long_click(self, chat_id: int, r: int, c: int) -> jboolean:
        game = self.active_games.get(chat_id)
        if not game or game.game_over: return True
        game.toggle_flag(r, c)
        self.redraw_board(chat_id)
        self.update_dialog_title(game)
        return True

    def redraw_board(self, chat_id: int):
        game = self.active_games.get(chat_id); views = self.cell_views.get(chat_id)
        if not game or not views: return
        if game.game_over and not game.game_won:
            for r_ in range(game.rows):
                for c_ in range(game.cols):
                    if game.board[r_][c_].is_mine:
                        game.board[r_][c_].is_visible = True

        for (r, c), view in views.items():
            text, color_key, background_key = self.get_cell_style(game.board[r][c])
            view.setText(text); view.setTextColor(Theme.getColor(color_key))
            view.setBackgroundColor(Theme.getColor(background_key))

    def update_dialog_title(self, game: MineSweeperGame):
        if game and game.title_view:
            game.title_view.setText(f"Мины: {game.mines - game.flags_placed}")

    def get_cell_style(self, cell: Cell) -> (str, str, str):
        if not cell.is_visible:
            text = "🚩" if cell.is_flagged else ""
            color = Theme.key_text_RedBold if cell.is_flagged else Theme.key_dialogTextBlack
            background = Theme.key_chat_attachPhotoBackground 
            return text, color, background
        background = Theme.key_windowBackgroundGray 
        if cell.is_mine:
            return "💣", Theme.key_dialogTextBlack, background
        colors = { 1: Theme.key_chat_attachAudioText, 2: Theme.key_chat_attachFileText, 3: Theme.key_text_RedBold, 4: Theme.key_chat_attachLocationText, 5: Theme.key_chat_attachPollText, 6: Theme.key_chat_messageLinkIn, 7: Theme.key_dialogTextBlack, 8: Theme.key_dialogTextGray }
        text = str(cell.value) if cell.value > 0 else ""
        color = colors.get(cell.value, Theme.key_dialogTextBlack)
        return text, color, background

    def show_game_over_dialog(self, chat_id: int, won: bool):
        fragment = get_last_fragment(); activity = fragment and fragment.getParentActivity()
        if not activity: return
        builder = AlertDialogBuilder(activity)
        builder.set_title("Ты победил!" if won else "Ты проиграл!")
        builder.set_message("Поздравляем!" if won else "Ты попал на мину.")

        def start_new_action(bld, w):
            bld.dismiss()
            self.cleanup_game(chat_id)
            self.show_rules_dialog({"dialog_id": chat_id})

        def close_all_action(bld, w):
            bld.dismiss()
            self.cleanup_game(chat_id)

        builder.set_positive_button("Новая игра", start_new_action)
        builder.set_negative_button("Закрыть", close_all_action)
        builder.set_cancelable(False)
        builder.show()
