Цель: Сгенерировать Python-код плагина для exteraGram, который будет соответствовать заданной функциональности, используя архитектуру, основанную на Chaquopy v15 и <PERSON><PERSON><PERSON> hook (Xposed).

Контекст:
exteraGram - это модифицированный клиент Telegram, поддерживающий плагины, написанные на Python. Система плагинов использует:
Chaquopy v15: Для взаимодействия между Python-кодом плагина и Java-кодом приложения Android.
Aliucord hook (Xposed): Для низкоуровневого перехвата и модификации поведения приложения Telegram (методов, конструкторов, сетевых запросов, событий UI).

Общие требования к плагину:
1.  Структура файла: Плагин должен быть одним `.plugin` файлом.
2.  Метаданные: В начале файла должны быть определены следующие мета-переменные как простые строковые литералы (без конкатенации или форматирования):
    *   `__id__`: Уникальный идентификатор плагина (например, `"my_awesome_plugin"`).
    *   `__name__`: Человекочитаемое название плагина (например, `"Мой Отличный Плагин"`).
    *   `__description__`: Краткое описание функциональности плагина. Поддерживает базовый Markdown.
    *   `__author__`: Имя автора или Telegram username/channel link (например, `"@YourUsername"`).
    *   `__version__`: Версия плагина (например, `"1.0.0"`). Если не указано, по умолчанию `1.0`.
    *   `__icon__`: (Опционально) Ссылка на стикерпак и индекс стикера (например, `"https://t.me/addstickers/MyPackName/0"`).
    *   `__min_version__`: (Опционально) Минимальная версия exteraGram, необходимая для работы плагина.
3.  Базовый класс: Должен быть один класс, наследующийся от `BasePlugin`. `BasePlugin` импортируется неявно.

Технические детали и лучшие практики:
1.  **Жизненный цикл плагина:**
    *   Использовать `on_plugin_load()` для инициализации и регистрации хуков/меню.
    *   Использовать `on_plugin_unload()` для очистки ресурсов (если необходимо).
2.  **Работа с UI/потоками:**
    *   Все операции, изменяющие UI, должны выполняться в UI-потоке с помощью `android_utils.run_on_ui_thread(func, delay=0)`.
    *   Длительные или блокирующие операции (например, сетевые запросы) должны выполняться в фоновом потоке с помощью `client_utils.run_on_queue(func, queue_name="background", delay=0)`.
3.  **Перехват методов (Xposed Hooking):**
    *   Для перехвата методов использовать `self.hook_method(method_reference, hook_handler_instance)`.
    *   Обработчики хуков должны наследоваться от `MethodHook` (для `before_hooked_method` и `after_hooked_method`) или `MethodReplacement` (для `replace_hooked_method`).
    *   Использовать `param.thisObject`, `param.args`, `param.getResult()`, `param.setResult()` для взаимодействия с перехваченным методом.
4.  **Взаимодействие с Telegram API/клиентом:**
    *   **Отправка сообщений:** Использовать `client_utils.send_message(params)` для отправки сообщений. Эта функция уже работает в UI-потоке.
    *   **Сетевые запросы:** Для отправки raw Telegram API запросов (TLObjects) использовать `client_utils.send_request(request_object, callback)`.
    *   **Хуки запросов/обновлений:** Регистрировать с помощью `self.add_hook("TL_SCHEMA_NAME")` или `self.add_on_send_message_hook()`.
    *   **Меню:** Добавлять пункты меню с помощью `self.add_menu_item(MenuItemData(...))`.
    *   **Настройки:** Реализовать настройки плагина через `create_settings()` и использовать `self.get_setting()` / `self.set_setting()`.
5.  **Парсинг Markdown:** Использовать `markdown_utils.parse_markdown(text)` для обработки Markdown V2 и получения `ParsedMessage` с `text` и `entities`.
6.  **Логирование:** Использовать `self.log(data)` для вывода информации в Logcat.

Пример структуры кода:

```python
# Метаданные плагина
__id__ = "[УНИКАЛЬНЫЙ_ИДЕНТИФИКАТОР]"
__name__ = "[НАЗВАНИЕ_ПЛАГИНА]"
__description__ = "[ОПИСАНИЕ_ПЛАГИНА]"
__author__ = "[АВТОР]"
__version__ = "[ВЕРСИЯ]"

# Неявный импорт BasePlugin
# from base_plugin import BasePlugin
# from android_utils import run_on_ui_thread, log
# from client_utils import run_on_queue, send_message
# from markdown_utils import parse_markdown

class MyPlugin(BasePlugin):
    def on_plugin_load(self):
        # Инициализация и регистрация хуков/меню
        self.log("Плагин загружен!")
        # Пример: self.add_on_send_message_hook(priority=0)
        # Пример: self.add_menu_item(MenuItemData(...))

    def on_plugin_unload(self):
        # Очистка ресурсов
        self.log("Плагин выгружен!")

    # Реализация функциональности плагина (методы-обработчики хуков, вспомогательные функции)
    # Пример: def on_send_message_hook(self, params: Dict[str, Any]) -> HookResult:
    #             # Логика обработки сообщения
    #             return HookResult(strategy=HookStrategy.DEFAULT)

    # Пример: def create_settings(self) -> List[SettingControl]:
    #             # Определение настроек UI
    #             return []

    # Пример: def _my_background_task(self, data):
    #             # Длительная операция
    #             self.log(f"Выполняется фоновая задача с данными: {data}")
    #             run_on_ui_thread(lambda: self.log("Фоновая задача завершена, UI обновлен"))

```

Твоя задача:
На основе вышеизложенной информации, сгенерируй полный и рабочий Python-код плагина для exteraGram. Убедись, что код соответствует всем указанным требованиям, использует необходимые утилиты и следует лучшим практикам по работе с потоками и UI.
Цель плагина:

