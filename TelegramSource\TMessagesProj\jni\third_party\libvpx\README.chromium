Name: libvpx
URL: http://www.webmproject.org
Version: v1.8.2
CPEPrefix: cpe:/a:john_koleszar:libvpx:1.8.2
License: BSD
License File: source/libvpx/LICENSE
Security Critical: yes

Date: Tuesday March 31 2020
Branch: master
Commit: 667138e1f0581772de2b990e144bcd6c49a6adb8

Description:
Contains the sources used to compile libvpx binaries used by Google Chrome and
Chromium.

The libvpx source is from webmproject.org:
  http://www.webmproject.org/code/
  https://chromium.googlesource.com/webm/libvpx

Please follow these steps to update libvpx source code:

1. Update the code:
     roll_dep.py \
       -r <libvpx OWNER> \
       --log-limit 20 \
       --roll-to <libvpx hash> \
       src/third_party/libvpx/source/libvpx

   Use the generated commit message for the roll.

2. Generate .gni and config files.

   cd third_party/libvpx
   ./generate_gni.sh

3. Update 'Branch' in README.chromium if necessary.

Tools needed to build libvpx:

- generate_gni.sh

Generate config and .gni files that contain the source list for each platform.
Configuration for the build is taken from vpx_config.h for each platform.

- lint_config.sh

A tool to verify vpx_config.h and vpx_config.asm are matched. This also
prints the final configuration after checking.
