# **EXPERIMENTAL**
#
# See http://sebs.googlecode.com
#
# This is an experimental build definition file using the SEBS build system.
# I (<PERSON><PERSON>, maintainer of Protocol Buffers) happen to be the author of
# SEBS, though SEBS is not a Google project.  I'm sticking this file in
# protobuf's SVN because that's the easiest place for me to put it, and it
# shouldn't harm anyone.  This file is not included in the distribution.
#
# Currently, to use this file, you must generate config.h and put it at the
# top level of the source tree.

_cpp = sebs.import_("//sebs/cpp.sebs")

# ====================================================================
# Public targets

protobuf_lite = _cpp.Library(
  name = "protobuf-lite",
  srcs = [ "stubs/common.cc",
           "stubs/once.cc",
           "stubs/hash.cc",
           "stubs/hash.h",
           "stubs/map-util.h",
           "stubs/stl_util-inl.h",
           "extension_set.cc",
           "generated_message_util.cc",
           "message_lite.cc",
           "repeated_field.cc",
           "wire_format_lite.cc",
           "io/coded_stream.cc",
           "io/zero_copy_stream.cc",
           "io/zero_copy_stream_impl_lite.cc" ],
  deps = [ _cpp.SystemLibrary(name = "pthread") ])

protobuf = _cpp.Library(
  name = "protobuf",
  srcs = [ "stubs/strutil.cc",
           "stubs/strutil.h",
           "stubs/substitute.cc",
           "stubs/substitute.h",
           "stubs/structurally_valid.cc",
           "descriptor.cc",
           "descriptor.pb.cc",
           "descriptor_database.cc",
           "dynamic_message.cc",
           "extension_set_heavy.cc",
           "generated_message_reflection.cc",
           "message.cc",
           "reflection_ops.cc",
           "service.cc",
           "text_format.cc",
           "unknown_field_set.cc",
           "wire_format.cc",
           "io/gzip_stream.cc",
           "io/printer.cc",
           "io/tokenizer.cc",
           "io/zero_copy_stream_impl.cc",
           "compiler/importer.cc",
           "compiler/parser.cc" ],
  deps = [ protobuf_lite,
           _cpp.SystemLibrary(name = "z") ])

libprotoc = _cpp.Library(
  name = "protoc",
  srcs = [ "compiler/code_generator.cc",
           "compiler/command_line_interface.cc",
           "compiler/cpp/cpp_enum.cc",
           "compiler/cpp/cpp_enum.h",
           "compiler/cpp/cpp_enum_field.cc",
           "compiler/cpp/cpp_enum_field.h",
           "compiler/cpp/cpp_extension.cc",
           "compiler/cpp/cpp_extension.h",
           "compiler/cpp/cpp_field.cc",
           "compiler/cpp/cpp_field.h",
           "compiler/cpp/cpp_file.cc",
           "compiler/cpp/cpp_file.h",
           "compiler/cpp/cpp_generator.cc",
           "compiler/cpp/cpp_helpers.cc",
           "compiler/cpp/cpp_helpers.h",
           "compiler/cpp/cpp_message.cc",
           "compiler/cpp/cpp_message.h",
           "compiler/cpp/cpp_message_field.cc",
           "compiler/cpp/cpp_message_field.h",
           "compiler/cpp/cpp_primitive_field.cc",
           "compiler/cpp/cpp_primitive_field.h",
           "compiler/cpp/cpp_service.cc",
           "compiler/cpp/cpp_service.h",
           "compiler/cpp/cpp_string_field.cc",
           "compiler/cpp/cpp_string_field.h",
           "compiler/java/java_enum.cc",
           "compiler/java/java_enum.h",
           "compiler/java/java_enum_field.cc",
           "compiler/java/java_enum_field.h",
           "compiler/java/java_extension.cc",
           "compiler/java/java_extension.h",
           "compiler/java/java_field.cc",
           "compiler/java/java_field.h",
           "compiler/java/java_file.cc",
           "compiler/java/java_file.h",
           "compiler/java/java_generator.cc",
           "compiler/java/java_helpers.cc",
           "compiler/java/java_helpers.h",
           "compiler/java/java_message.cc",
           "compiler/java/java_message.h",
           "compiler/java/java_message_field.cc",
           "compiler/java/java_message_field.h",
           "compiler/java/java_primitive_field.cc",
           "compiler/java/java_primitive_field.h",
           "compiler/java/java_service.cc",
           "compiler/java/java_service.h",
           "compiler/python/python_generator.cc" ],
  deps = [ protobuf ])

protoc = _cpp.Binary(
  name = "protoc",
  srcs = [ "compiler/main.cc" ],
  deps = [ libprotoc ])

# ====================================================================
# ProtobufLibrary rule class

class ProtobufLibrary(sebs.Rule):
  argument_spec = sebs.ArgumentSpec(srcs = [sebs.Artifact],
                                    deps = ([sebs.Rule], []),
                                    lite = (bool, False))

  def _expand(self, args):
    for dep in args.deps:
      if not isinstance(dep, ProtobufLibrary):
        raise sebs.DefinitionError(
          "Dependency of ProtobufLibrary is not a ProtobufLibrary: %s" % dep)

    protoc.expand_once()

    # We must build protoc for the host configuration to allow cross-compiling.
    host_protoc = self.context.configured_artifact(protoc.binary, "host")

    protoc_action = self.context.action(self, "protobuf")
    protoc_args = [host_protoc, "-Isrc", "-Itmp", "-Iinclude","--cpp_out=tmp"]

    cpp_srcs = []
    for src in args.srcs:
      protoc_args.append(src)

      # We cannot build .proto files from other packages because the .pb.cc
      # and .pb.h files would be written to that package, and we aren't allowed
      # to write to other packages.
      if self.context.local_filename(src) is None:
        raise sebs.DefinitionError(
          "Source file is not in this package: %s" % src)

      cc_artifact = self.context.derived_artifact(src, ".pb.cc", protoc_action)
      header_artifact = self.context.derived_artifact(
          src, ".pb.h", protoc_action)

      cpp_srcs.append(cc_artifact)
      cpp_srcs.append(header_artifact)

    protoc_action.set_command(
      sebs.SubprocessCommand(protoc_action, protoc_args, implicit = cpp_srcs))

    deps = list(args.deps)
    if args.lite:
      deps.append(protobuf_lite)
    else:
      deps.append(protobuf)

    self.__cpp_library = _cpp.Library(srcs = cpp_srcs, deps = deps,
                                      context = self.context)
    self.__cpp_library.label = self.label
    self.outputs = []

  def as_cpp_library(self):
    self.expand_once()
    return self.__cpp_library

# ====================================================================
# Tests

_lite_test_protos = ProtobufLibrary(
  srcs = [ "unittest_lite.proto",
           "unittest_import_lite.proto" ],
  lite = True)
_test_protos = ProtobufLibrary(
  srcs = [ "unittest.proto",
           "unittest_empty.proto",
           "unittest_import.proto",
           "unittest_mset.proto",
           "unittest_optimize_for.proto",
           "unittest_embed_optimize_for.proto",
           "unittest_custom_options.proto",
           "unittest_lite_imports_nonlite.proto",
           "compiler/cpp/cpp_test_bad_identifiers.proto" ],
  deps = [ _lite_test_protos ])

_test_util = _cpp.Library(
  name = "test_util",
  srcs = [ "test_util.cc",
           "test_util.h",
           "testing/googletest.cc",
           "testing/googletest.h",
           "testing/file.cc",
           "testing/file.h" ],
  deps = [ protobuf, _test_protos, _cpp.SystemLibrary(name = "gtest")] )

protobuf_lite_test = _cpp.Test(
  srcs = [ "lite_unittest.cc",
           "test_util_lite.cc",
           "test_util_lite.h" ],
  deps = [ _lite_test_protos ])

protobuf_test = _cpp.Test(
  srcs = [ "stubs/common_unittest.cc",
           "stubs/once_unittest.cc",
           "stubs/strutil_unittest.cc",
           "stubs/structurally_valid_unittest.cc",
           "descriptor_database_unittest.cc",
           "descriptor_unittest.cc",
           "dynamic_message_unittest.cc",
           "extension_set_unittest.cc",
           "generated_message_reflection_unittest.cc",
           "message_unittest.cc",
           "reflection_ops_unittest.cc",
           "repeated_field_unittest.cc",
           "text_format_unittest.cc",
           "unknown_field_set_unittest.cc",
           "wire_format_unittest.cc",
           "io/coded_stream_unittest.cc",
           "io/printer_unittest.cc",
           "io/tokenizer_unittest.cc",
           "io/zero_copy_stream_unittest.cc",
           "compiler/command_line_interface_unittest.cc",
           "compiler/importer_unittest.cc",
           "compiler/parser_unittest.cc",
           "compiler/cpp/cpp_bootstrap_unittest.cc",
           "compiler/cpp/cpp_unittest.cc" ],
  deps = [ protobuf, libprotoc, _test_util,
           _cpp.SystemLibrary(name = "gtest_main") ])
