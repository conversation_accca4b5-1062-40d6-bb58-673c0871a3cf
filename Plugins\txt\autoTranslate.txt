import requests
from ui.settings import <PERSON><PERSON>, <PERSON><PERSON>, Selector
from base_plugin import <PERSON><PERSON>lug<PERSON>, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from java.util import Locale
import json
import time
from android_utils import log
import re

__id__ = "autoTranslate"
__name__ = "Auto Translate"
__description__ = "Automatically translates outgoing messages using Google Translate. Toggle in settings."
__author__ = "@luvztroy"
__min_version__ = "11.9.0"
__version__ = "1.0.2"
__icon__ = "luvztroy/0"
__category__ = "utility"
__priority__ = 100

TRANSLATE_API_URL = "https://translate.googleapis.com/translate_a/single"

class LocalizationManager:
    def __init__(self):
        self.language = Locale.getDefault().getLanguage()
        self.language = self.language if self.language in self._get_supported_languages() else "en"

    def get_string(self, key):
        return self.strings[self.language].get(key, key)

    def _get_supported_languages(self):
        return self.strings.keys()

    strings = {
        "en": {
            "SETTINGS_TITLE": "Auto Translate Settings",
            "ENABLE_TRANSLATOR": "Enable Outgoing Auto Translator",
            "ENABLE_TRANSLATOR_SUB": "Automatically translate outgoing messages to the selected language.",
            "TARGET_LANGUAGE": "Target Language",
            "BYPASS_COMMANDS": "Bypass Commands",
            "BYPASS_COMMANDS_SUB": "Skip translation for messages starting with '.' or '/'",
            "TRANSLATION_ERROR": "Translation failed! Message not sent."
        },
        "ru": {
            "SETTINGS_TITLE": "Настройки автоперевода",
            "ENABLE_TRANSLATOR": "Включить автопереводчик исходящих",
            "ENABLE_TRANSLATOR_SUB": "Автоматически переводить исходящие сообщения на выбранный язык.",
            "TARGET_LANGUAGE": "Язык перевода",
            "BYPASS_COMMANDS": "Пропускать команды",
            "BYPASS_COMMANDS_SUB": "Пропускать перевод для сообщений, начинающихся с '.' или '/'",
            "TRANSLATION_ERROR": "Ошибка перевода! Сообщение не отправлено."
        }
    }

LANG_CODES = [
    "af", "am", "ar", "as", "ay", "az", "be", "bg", "bn", "bs", "ca", "ceb", "co", "cs", "cy", "da", "de", "el", "en", "eo", "es", "et", "eu", "fa", "fi", "fil", "fr", "fy", "ga", "gd", "gl", "gu", "ha", "haw", "he", "hi", "hi-hinglish", "hmn", "hr", "ht", "hu", "hy", "id", "ig", "is", "it", "iw", "ja", "jw", "ka", "kk", "km", "kn", "ko", "ku", "ky", "la", "lb", "lo", "lt", "lv", "mg", "mi", "mk", "ml", "mn", "mr", "ms", "mt", "my", "ne", "nl", "no", "ny", "or", "pa", "pl", "ps", "pt", "ro", "ru", "si", "sk", "sl", "sm", "sn", "so", "sq", "sr", "st", "su", "sv", "sw", "ta", "te", "tg", "th", "tk", "tl", "tr", "tt", "ug", "uk", "ur", "uz", "vi", "xh", "yi", "yo", "zh", "zu"
]
LANG_NAMES = [
    "Afrikaans", "Amharic", "Arabic", "Assamese", "Aymara", "Azerbaijani", "Belarusian", "Bulgarian", "Bengali", "Bosnian", "Catalan", "Cebuano", "Corsican", "Czech", "Welsh", "Danish", "German", "Greek", "English", "Esperanto", "Spanish", "Estonian", "Basque", "Persian", "Finnish", "Filipino", "French", "Frisian", "Irish", "Scots Gaelic", "Galician", "Gujarati", "Hausa", "Hawaiian", "Hebrew", "Hindi", "Hinglish", "Hmong", "Croatian", "Haitian Creole", "Hungarian", "Armenian", "Indonesian", "Igbo", "Icelandic", "Italian", "Hebrew", "Japanese", "Javanese", "Georgian", "Kazakh", "Khmer", "Kannada", "Korean", "Kurdish", "Kyrgyz", "Latin", "Luxembourgish", "Lao", "Lithuanian", "Latvian", "Malagasy", "Maori", "Macedonian", "Malayalam", "Mongolian", "Marathi", "Malay", "Maltese", "Myanmar", "Nepali", "Dutch", "Norwegian", "Nyanja", "Odia", "Punjabi", "Polish", "Pashto", "Portuguese", "Romanian", "Russian", "Sinhala", "Slovak", "Slovenian", "Samoan", "Shona", "Somali", "Albanian", "Serbian", "Sesotho", "Sundanese", "Swedish", "Swahili", "Tamil", "Telugu", "Tajik", "Thai", "Turkmen", "Tagalog", "Turkish", "Tatar", "Uyghur", "Ukrainian", "Urdu", "Uzbek", "Vietnamese", "Xhosa", "Yiddish", "Yoruba", "Chinese", "Zulu"
]
DEFAULT_TARGET_LANG = "en"

locali = LocalizationManager()

class AutoTranslatePlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.translation_cache = {}
        self.cache_timeout = 300
        self.last_cache_cleanup = time.time()

    def on_plugin_load(self):
        print("AutoTranslatePlugin loaded")
        self.add_on_send_message_hook()

    def cleanup_cache(self):
        current_time = time.time()
        if current_time - self.last_cache_cleanup > 3600:
            expired_keys = []
            for key, (translation, timestamp) in self.translation_cache.items():
                if current_time - timestamp > self.cache_timeout:
                    expired_keys.append(key)
            for key in expired_keys:
                del self.translation_cache[key]
            self.last_cache_cleanup = current_time

    def get_cached_translation(self, text, target_lang):
        cache_key = f"{text}:{target_lang}"
        if cache_key in self.translation_cache:
            translation, timestamp = self.translation_cache[cache_key]
            if time.time() - timestamp < self.cache_timeout:
                return translation
        return None

    def cache_translation(self, text, target_lang, translation):
        cache_key = f"{text}:{target_lang}"
        self.translation_cache[cache_key] = (translation, time.time())
        self.cleanup_cache()

    def extract_emojis(self, text):
        emojis = []
        standard_emoji_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"
            "\U0001F300-\U0001F5FF"
            "\U0001F680-\U0001F6FF"
            "\U0001F700-\U0001F77F"
            "\U0001F780-\U0001F7FF"
            "\U0001F800-\U0001F8FF"
            "\U0001F900-\U0001F9FF"
            "\U0001FA00-\U0001FA6F"
            "\U0001FA70-\U0001FAFF"
            "\U00002702-\U000027B0"
            "\U000024C2-\U0001F251" 
            "]+"
        )
        
        for match in standard_emoji_pattern.finditer(text):
            emojis.append((match.group(), match.start()))
        
        premium_emoji_pattern = re.compile(r'<emoji>(\d+)</emoji>')
        for match in premium_emoji_pattern.finditer(text):
            emojis.append((match.group(), match.start()))
        
        emojis.sort(key=lambda x: x[1])
        
        text_without_emojis = text
        for emoji, pos in reversed(emojis):
            text_without_emojis = text_without_emojis[:pos] + text_without_emojis[pos + len(emoji):]
        
        return text_without_emojis, emojis

    def restore_emojis(self, text, emojis):
        if not emojis:
            return text
        
        emojis.sort(key=lambda x: x[1])
        
        result = text
        offset = 0
        for emoji, pos in emojis:
            adjusted_pos = pos + offset
            result = result[:adjusted_pos] + emoji + result[adjusted_pos:]
            offset += len(emoji)
        
        return result

    def on_send_message_hook(self, account, params):
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()

        original_text = params.message.strip()
        if not original_text:
            return HookResult()

        bypass_commands = self.get_setting("bypass_commands", True)
        if bypass_commands and (original_text.startswith(".") or original_text.startswith("/")):
            params.message = original_text[1:].strip()
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        lang_index = self.get_setting("target_language", 0)
        if not 0 <= lang_index < len(LANG_CODES):
            lang_index = 0
            self.set_setting("target_language", 0)

        target_lang = LANG_CODES[lang_index]

        exclude_lang_index = self.get_setting("exclude_language", 0)
        if not 0 <= exclude_lang_index < len(LANG_CODES) + 1:
            exclude_lang_index = 0
            self.set_setting("exclude_language", 0)

        if exclude_lang_index > 0:
            exclude_lang = LANG_CODES[exclude_lang_index - 1]
            provider_index = self.get_setting("provider", 1)
            providers = ["google", "yandex"]
            provider = providers[provider_index] if provider_index in range(len(providers)) else "yandex"

            if provider == "yandex":
                try:
                    test_translation = self.translate_with_yandex(original_text, exclude_lang)
                    if test_translation and test_translation.lower() == original_text.lower():
                        return HookResult()
                except Exception as e:
                    log(f"Error checking excluded language: {str(e)}")

        translated = self.translate_text(original_text, target_lang)
        if not translated:
            return HookResult()

        params.message = translated
        return HookResult(strategy=HookStrategy.MODIFY, params=params)

    def translate_text(self, text, target_lang):
        text_without_emojis, emojis = self.extract_emojis(text)
        
        if not text_without_emojis.strip():
            return text

        cached_translation = self.get_cached_translation(text_without_emojis, target_lang)
        if cached_translation:
            return self.restore_emojis(cached_translation, emojis)

        provider_index = self.get_setting("provider", 1)
        providers = ["google", "yandex"]
        provider = providers[provider_index] if provider_index in range(len(providers)) else "yandex"
        log(f"Using provider: {provider}")
        
        if target_lang == "hi-hinglish":
            try:
                hindi_translation = None
                if provider == "yandex":
                    hindi_translation = self.translate_with_yandex(text_without_emojis, "hi")
                else:
                    params = {
                        'client': 'gtx',
                        'sl': 'auto',
                        'tl': 'hi',
                        'dt': 't',
                        'q': text_without_emojis
                    }
                    response = requests.get(TRANSLATE_API_URL, params=params, timeout=5)
                    if response.status_code == 200:
                        result = response.json()
                        if result and isinstance(result, list) and result[0] and isinstance(result[0], list):
                            hindi_translation = result[0][0][0]

                if hindi_translation:
                    hinglish = hindi_translation
                    translit_map = {
                        'अ': 'a', 'आ': 'aa', 'इ': 'i', 'ई': 'ee', 'उ': 'u', 'ऊ': 'oo',
                        'ऋ': 'ri', 'ॠ': 'rī', 'ऌ': 'li', 'ॡ': 'lī', 'ए': 'e', 'ऐ': 'ai',
                        'ओ': 'o', 'औ': 'au', 'अं': 'an', 'अः': 'ah',
                        
                        'ा': 'aa', 'ि': 'i', 'ी': 'ee', 'ु': 'u', 'ू': 'oo',
                        'ृ': 'ri', 'ॄ': 'rī', 'ॢ': 'li', 'ॣ': 'lī',
                        'े': 'e', 'ै': 'ai', 'ो': 'o', 'ौ': 'au',
                        'ं': 'n', 'ः': 'h', '़': '', '्': '',
                        
                        'क': 'k', 'ख': 'kh', 'ग': 'g', 'घ': 'gh', 'ङ': 'ng',
                        'च': 'ch', 'छ': 'chh', 'ज': 'j', 'झ': 'jh', 'ञ': 'ny',
                        'ट': 'ṭ', 'ठ': 'ṭh', 'ड': 'ḍ', 'ढ': 'ḍh', 'ण': 'ṇ',
                        'त': 't', 'थ': 'th', 'द': 'd', 'ध': 'dh', 'न': 'n',
                        'प': 'p', 'फ': 'ph', 'ब': 'b', 'भ': 'bh', 'म': 'm',
                        'य': 'y', 'र': 'r', 'ल': 'l', 'व': 'v',
                        'श': 'sh', 'ष': 'ṣh', 'स': 's', 'ह': 'h',
                        'ळ': 'ḷ', 'क्ष': 'kṣ', 'ज्ञ': 'gy',
                        
                        '।': '.', '?': '?', '!': '!', ',': ',', ';': ';', ':': ':'
                    }
                    
                    for hindi, roman in translit_map.items():
                        hinglish = hinglish.replace(hindi, roman)
                    
                    hinglish = ''.join(char for char in hinglish if ord(char) < 128)
                    hinglish = ' '.join(hinglish.split())
                    
                    self.cache_translation(text_without_emojis, target_lang, hinglish)
                    return self.restore_emojis(hinglish, emojis)
            except Exception as e:
                log(f"Error in Hinglish translation: {str(e)}")
                return text
        
        if provider == "yandex":
            translation = self.translate_with_yandex(text_without_emojis, target_lang)
            if translation:
                self.cache_translation(text_without_emojis, target_lang, translation)
                return self.restore_emojis(translation, emojis)
            return text
        else:
            try:
                clients = ['gtx', 'dict-chrome-ex', 'translate_a_s']
                translation = None
                
                for client in clients:
                    try:
                        params = {
                            'client': client,
                            'sl': 'auto',
                            'tl': target_lang,
                            'dt': 't',
                            'q': text_without_emojis
                        }
                        
                        response = requests.get(TRANSLATE_API_URL, params=params, timeout=5)
                        if response.status_code == 200:
                            result = response.json()
                            
                            if client == 'translate_a_s':
                                if result and isinstance(result, list) and len(result) > 0:
                                    translation = result[0][0][0]
                            else:
                                if result and isinstance(result, list) and result[0] and isinstance(result[0], list):
                                    translation = result[0][0][0]
                            
                            if translation:
                                self.cache_translation(text_without_emojis, target_lang, translation)
                                return self.restore_emojis(translation, emojis)
                    except Exception as e:
                        continue
                
                if not translation:
                    yandex_translation = self.translate_with_yandex(text_without_emojis, target_lang)
                    if yandex_translation:
                        self.cache_translation(text_without_emojis, target_lang, yandex_translation)
                        return self.restore_emojis(yandex_translation, emojis)
                    return text
                    
            except Exception as e:
                yandex_translation = self.translate_with_yandex(text_without_emojis, target_lang)
                if yandex_translation:
                    self.cache_translation(text_without_emojis, target_lang, yandex_translation)
                    return self.restore_emojis(yandex_translation, emojis)
                return text

    def translate_with_yandex(self, text, target_lang):
        import uuid
        import urllib.parse
        YA_URL = "https://translate.yandex.net/api/v1/tr.json/translate"
        session_uuid = str(uuid.uuid4()).replace("-", "")
        to_lang = target_lang.lower()
        data = f"lang={to_lang}&text={urllib.parse.quote(text)}"
        headers = {
            "User-Agent": "ru.yandex.translate/21.15.4.21402814 (Xiaomi Redmi K20 Pro; Android 11)",
            "Content-Type": "application/x-www-form-urlencoded"
        }
        url = f"{YA_URL}?id={session_uuid}-0-0&srv=android"
        try:
            response = requests.post(url, data=data, headers=headers, timeout=5)
            if response.status_code == 200:
                obj = response.json()
                if "text" in obj:
                    return "".join(obj["text"])
            return None
        except Exception as e:
            log(f"Yandex translation failed: {str(e)}")
            return None

    def create_settings(self):
        return [
            Header(locali.get_string("SETTINGS_TITLE")),
            Switch(
                key="enable_translator",
                text=locali.get_string("ENABLE_TRANSLATOR"),
                default=False,
                subtext=locali.get_string("ENABLE_TRANSLATOR_SUB"),
                icon="ai_chat_solar"
            ),
            Selector(
                key="provider",
                text="Translation Provider",
                default=1,
                items=["Google (Experimental)", "Yandex"],
                icon="msg_translate_solar"
            ),
            Selector(
                key="target_language",
                text=locali.get_string("TARGET_LANGUAGE"),
                default=0,
                items=LANG_NAMES,
                icon="menu_premium_chatbot"
            ),
            Selector(
                key="exclude_language",
                text="Exclude Language",
                default=0,
                items=["No selection"] + LANG_NAMES,
                icon="msg_viewreplies_solar"
            ),
            Switch(
                key="bypass_commands",
                text=locali.get_string("BYPASS_COMMANDS"),
                default=True,
                subtext=locali.get_string("BYPASS_COMMANDS_SUB"),
                icon="msg_photo_curve_solar"
            ),
        ]
