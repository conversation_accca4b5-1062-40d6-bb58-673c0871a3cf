// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/audio/JavaAudioDeviceModule

#ifndef org_webrtc_audio_JavaAudioDeviceModule_JNI
#define org_webrtc_audio_JavaAudioDeviceModule_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_audio_JavaAudioDeviceModule[];
const char kClassPath_org_webrtc_audio_JavaAudioDeviceModule[] =
    "org/webrtc/audio/JavaAudioDeviceModule";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_audio_JavaAudioDeviceModule_clazz(nullptr);
#ifndef org_webrtc_audio_JavaAudioDeviceModule_clazz_defined
#define org_webrtc_audio_JavaAudioDeviceModule_clazz_defined
inline jclass org_webrtc_audio_JavaAudioDeviceModule_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_audio_JavaAudioDeviceModule,
      &g_org_webrtc_audio_JavaAudioDeviceModule_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {

static jlong JNI_JavaAudioDeviceModule_CreateAudioDeviceModule(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& context,
    const jni_zero::JavaParamRef<jobject>& audioManager,
    const jni_zero::JavaParamRef<jobject>& audioInput,
    const jni_zero::JavaParamRef<jobject>& audioOutput,
    jint inputSampleRate,
    jint outputSampleRate,
    jboolean useStereoInput,
    jboolean useStereoOutput);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_audio_JavaAudioDeviceModule_nativeCreateAudioDeviceModule(
    JNIEnv* env,
    jclass jcaller,
    jobject context,
    jobject audioManager,
    jobject audioInput,
    jobject audioOutput,
    jint inputSampleRate,
    jint outputSampleRate,
    jboolean useStereoInput,
    jboolean useStereoOutput) {
  return JNI_JavaAudioDeviceModule_CreateAudioDeviceModule(env, jni_zero::JavaParamRef<jobject>(env,
      context), jni_zero::JavaParamRef<jobject>(env, audioManager),
      jni_zero::JavaParamRef<jobject>(env, audioInput), jni_zero::JavaParamRef<jobject>(env,
      audioOutput), inputSampleRate, outputSampleRate, useStereoInput, useStereoOutput);
}


}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_audio_JavaAudioDeviceModule_JNI
