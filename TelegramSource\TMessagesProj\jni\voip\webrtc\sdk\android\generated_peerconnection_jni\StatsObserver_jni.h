// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/StatsObserver

#ifndef org_webrtc_StatsObserver_JNI
#define org_webrtc_StatsObserver_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_StatsObserver[];
const char kClassPath_org_webrtc_StatsObserver[] = "org/webrtc/StatsObserver";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_StatsObserver_clazz(nullptr);
#ifndef org_webrtc_StatsObserver_clazz_defined
#define org_webrtc_StatsObserver_clazz_defined
inline jclass org_webrtc_StatsObserver_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_StatsObserver,
      &g_org_webrtc_StatsObserver_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_StatsObserver_onComplete1(nullptr);
static void Java_StatsObserver_onComplete(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobjectArray>& reports) {
  jclass clazz = org_webrtc_StatsObserver_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_StatsObserver_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onComplete",
          "([Lorg/webrtc/StatsReport;)V",
          &g_org_webrtc_StatsObserver_onComplete1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, reports.obj());
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_StatsObserver_JNI
