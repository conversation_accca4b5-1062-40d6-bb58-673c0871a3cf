# Copyright 2019 The BoringSSL Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

package main

type Statement Peg {}

Statement <- WS? (Assignment / Action / Expression) WS? !.
Assignment <- Variable WS? '=' WS? Expression
Variable <- [a-zA-Z_][a-zA-Z0-9_]*
Expression <- (StringLiteral / Indexing / Search / Variable)
StringLiteral <- '"' QuotedText '"'
QuotedText <- (EscapedChar / [^\\"])*
EscapedChar <- '\\' [\\n"]
Indexing <- Variable ('[' Index ']')+
Index <- [0-9a-z]+
Search <- Variable '[' WS? 'where' WS Query ']'
Action <- Expression '.' Command
Command <- Function '(' Args? ')'
Function <- [a-zA-Z]+
Args <- StringLiteral (WS? ',' WS? Args)
Query <- Conjunctions (WS? '||' WS? Conjunctions)?
Conjunctions <- Conjunction (WS? '&&' WS? Conjunctions)?
Conjunction <- Field WS? Relation WS? StringLiteral
Field <- [a-z][a-zA-Z0-9]*
Relation <- ('==' / '!=' / 'contains' / 'startsWith' / 'endsWith')

WS <- [ \t]+
