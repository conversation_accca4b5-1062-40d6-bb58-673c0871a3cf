// Copyright 2017 The BoringSSL Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <openssl/crypto.h>

#include <stdio.h>
#include <stdlib.h>

#include <openssl/aead.h>
#include <openssl/aes.h>
#include <openssl/bn.h>
#include <openssl/ctrdrbg.h>
#include <openssl/dh.h>
#include <openssl/digest.h>
#include <openssl/ec.h>
#include <openssl/ec_key.h>
#include <openssl/ecdsa.h>
#include <openssl/hkdf.h>
#include <openssl/hmac.h>
#include <openssl/nid.h>
#include <openssl/rsa.h>
#include <openssl/sha.h>

#include "../../bcm_support.h"
#include "../../internal.h"
#include "../delocate.h"
#include "../dh/internal.h"
#include "../ec/internal.h"
#include "../ecdsa/internal.h"
#include "../rand/internal.h"
#include "../rsa/internal.h"
#include "../service_indicator/internal.h"
#include "../tls/internal.h"


static void hexdump(FILE *out, const void *in, size_t len) {
  const uint8_t *in8 = reinterpret_cast<const uint8_t *>(in);
  for (size_t i = 0; i < len; i++) {
    fprintf(out, "%02x", in8[i]);
  }
}

int BORINGSSL_check_test(const void *expected, const void *actual,
                         size_t expected_len, const char *name) {
  if (OPENSSL_memcmp(actual, expected, expected_len) != 0) {
    FILE *err = CRYPTO_get_stderr();
    fprintf(err, "%s failed.\nExpected:   ", name);
    hexdump(err, expected, expected_len);
    fprintf(err, "\nCalculated: ");
    hexdump(err, actual, expected_len);
    fprintf(err, "\n");
    fflush(err);
    return 0;
  }
  return 1;
}

// MSVC wants to put a NUL byte at the end of non-char arrays and so cannot
// compile the real logic.
#if defined(_MSC_VER)

int BORINGSSL_self_test(void) { return 0; }

#else

static int set_bignum(BIGNUM **out, const uint8_t *in, size_t len) {
  *out = BN_bin2bn(in, len, NULL);
  return *out != NULL;
}

static RSA *self_test_rsa_key(void) {
  static const uint8_t kN[] = {
      0xd3, 0x3a, 0x62, 0x9f, 0x07, 0x77, 0xb0, 0x18, 0xf3, 0xff, 0xfe, 0xcc,
      0xc9, 0xa2, 0xc2, 0x3a, 0xa6, 0x1d, 0xd8, 0xf0, 0x26, 0x5b, 0x38, 0x90,
      0x17, 0x48, 0x15, 0xce, 0x21, 0xcd, 0xd6, 0x62, 0x99, 0xe2, 0xd7, 0xda,
      0x40, 0x80, 0x3c, 0xad, 0x18, 0xb7, 0x26, 0xe9, 0x30, 0x8a, 0x23, 0x3f,
      0x68, 0x9a, 0x9c, 0x31, 0x34, 0x91, 0x99, 0x06, 0x11, 0x36, 0xb2, 0x9e,
      0x3a, 0xd0, 0xbc, 0xb9, 0x93, 0x4e, 0xb8, 0x72, 0xa1, 0x9f, 0xb6, 0x8c,
      0xd5, 0x17, 0x1f, 0x7e, 0xaa, 0x75, 0xbb, 0xdf, 0xa1, 0x70, 0x48, 0xc4,
      0xec, 0x9a, 0x51, 0xed, 0x41, 0xc9, 0x74, 0xc0, 0x3e, 0x1e, 0x85, 0x2f,
      0xbe, 0x34, 0xc7, 0x65, 0x34, 0x8b, 0x4d, 0x55, 0x4b, 0xe1, 0x45, 0x54,
      0x0d, 0x75, 0x7e, 0x89, 0x4d, 0x0c, 0xf6, 0x33, 0xe5, 0xfc, 0xfb, 0x56,
      0x1b, 0xf2, 0x39, 0x9d, 0xe0, 0xff, 0x55, 0xcf, 0x02, 0x05, 0xb9, 0x74,
      0xd2, 0x91, 0xfc, 0x87, 0xe1, 0xbb, 0x97, 0x2a, 0xe4, 0xdd, 0x20, 0xc0,
      0x38, 0x47, 0xc0, 0x76, 0x3f, 0xa1, 0x9b, 0x5c, 0x20, 0xff, 0xff, 0xc7,
      0x49, 0x3b, 0x4c, 0xaf, 0x99, 0xa6, 0x3e, 0x82, 0x5c, 0x58, 0x27, 0xce,
      0x01, 0x03, 0xc3, 0x16, 0x35, 0x20, 0xe9, 0xf0, 0x15, 0x7a, 0x41, 0xd5,
      0x1f, 0x52, 0xea, 0xdf, 0xad, 0x4c, 0xbb, 0x0d, 0xcb, 0x04, 0x91, 0xb0,
      0x95, 0xa8, 0xce, 0x25, 0xfd, 0xd2, 0x62, 0x47, 0x77, 0xee, 0x13, 0xf1,
      0x48, 0x72, 0x9e, 0xd9, 0x2d, 0xe6, 0x5f, 0xa4, 0xc6, 0x9e, 0x5a, 0xb2,
      0xc6, 0xa2, 0xf7, 0x0a, 0x16, 0x17, 0xae, 0x6b, 0x1c, 0x30, 0x7c, 0x63,
      0x08, 0x83, 0xe7, 0x43, 0xec, 0x54, 0x5e, 0x2c, 0x08, 0x0b, 0x5e, 0x46,
      0xa7, 0x10, 0x93, 0x43, 0x53, 0x4e, 0xe3, 0x16, 0x73, 0x55, 0xce, 0xf2,
      0x94, 0xc0, 0xbe, 0xb3,
  };
  static const uint8_t kE[] = {0x01, 0x00, 0x01};  // 65537
  static const uint8_t kD[] = {
      0x2f, 0x2c, 0x1e, 0xd2, 0x3d, 0x2c, 0xb1, 0x9b, 0x21, 0x02, 0xce, 0xb8,
      0x95, 0x5f, 0x4f, 0xd9, 0x21, 0x38, 0x11, 0x36, 0xb0, 0x9a, 0x36, 0xab,
      0x97, 0x47, 0x75, 0xf7, 0x2e, 0xfd, 0x75, 0x1f, 0x58, 0x16, 0x9c, 0xf6,
      0x14, 0xe9, 0x8e, 0xa3, 0x69, 0x9d, 0x9d, 0x86, 0xfe, 0x5c, 0x1b, 0x3b,
      0x11, 0xf5, 0x55, 0x64, 0x77, 0xc4, 0xfc, 0x53, 0xaa, 0x8c, 0x78, 0x9f,
      0x75, 0xab, 0x20, 0x3a, 0xa1, 0x77, 0x37, 0x22, 0x02, 0x8e, 0x54, 0x8a,
      0x67, 0x1c, 0x5e, 0xe0, 0x3e, 0xd9, 0x44, 0x37, 0xd1, 0x29, 0xee, 0x56,
      0x6c, 0x30, 0x9a, 0x93, 0x4d, 0xd9, 0xdb, 0xc5, 0x03, 0x1a, 0x75, 0xcc,
      0x0f, 0xc2, 0x61, 0xb5, 0x6c, 0x62, 0x9f, 0xc6, 0xa8, 0xc7, 0x8a, 0x60,
      0x17, 0x11, 0x62, 0x4c, 0xef, 0x74, 0x31, 0x97, 0xad, 0x89, 0x2d, 0xe8,
      0x31, 0x1d, 0x8b, 0x58, 0x82, 0xe3, 0x03, 0x1a, 0x6b, 0xdf, 0x3f, 0x3e,
      0xa4, 0x27, 0x19, 0xef, 0x46, 0x7a, 0x90, 0xdf, 0xa7, 0xe7, 0xc9, 0x66,
      0xab, 0x41, 0x1d, 0x65, 0x78, 0x1c, 0x18, 0x40, 0x5c, 0xd6, 0x87, 0xb5,
      0xea, 0x29, 0x44, 0xb3, 0xf5, 0xb3, 0xd2, 0x4f, 0xce, 0x88, 0x78, 0x49,
      0x27, 0x4e, 0x0b, 0x30, 0x85, 0xfb, 0x73, 0xfd, 0x8b, 0x32, 0x15, 0xee,
      0x1f, 0xc9, 0x0e, 0x89, 0xb9, 0x43, 0x2f, 0xe9, 0x60, 0x8d, 0xda, 0xae,
      0x2b, 0x30, 0x99, 0xee, 0x88, 0x81, 0x20, 0x7b, 0x4a, 0xc3, 0x18, 0xf2,
      0x94, 0x02, 0x79, 0x94, 0xaa, 0x65, 0xd9, 0x1b, 0x45, 0x2a, 0xac, 0x6e,
      0x30, 0x48, 0x57, 0xea, 0xbe, 0x79, 0x7d, 0xfc, 0x67, 0xaa, 0x47, 0xc0,
      0xf7, 0x52, 0xfd, 0x0b, 0x63, 0x4e, 0x3d, 0x2e, 0xcc, 0x36, 0xa0, 0xdb,
      0x92, 0x0b, 0xa9, 0x1b, 0xeb, 0xc2, 0xd5, 0x08, 0xd3, 0x85, 0x87, 0xf8,
      0x5d, 0x1a, 0xf6, 0xc1,
  };
  static const uint8_t kP[] = {
      0xf7, 0x06, 0xa3, 0x98, 0x8a, 0x52, 0xf8, 0x63, 0x68, 0x27, 0x4f, 0x68,
      0x7f, 0x34, 0xec, 0x8e, 0x5d, 0xf8, 0x30, 0x92, 0xb3, 0x62, 0x4c, 0xeb,
      0xdb, 0x19, 0x6b, 0x09, 0xc5, 0xa3, 0xf0, 0xbb, 0xff, 0x0f, 0xc2, 0xd4,
      0x9b, 0xc9, 0x54, 0x4f, 0xb9, 0xf9, 0xe1, 0x4c, 0xf0, 0xe3, 0x4c, 0x90,
      0xda, 0x7a, 0x01, 0xc2, 0x9f, 0xc4, 0xc8, 0x8e, 0xb1, 0x1e, 0x93, 0x75,
      0x75, 0xc6, 0x13, 0x25, 0xc3, 0xee, 0x3b, 0xcc, 0xb8, 0x72, 0x6c, 0x49,
      0xb0, 0x09, 0xfb, 0xab, 0x44, 0xeb, 0x4d, 0x40, 0xf0, 0x61, 0x6b, 0xe5,
      0xe6, 0xfe, 0x3e, 0x0a, 0x77, 0x26, 0x39, 0x76, 0x3d, 0x4c, 0x3e, 0x9b,
      0x5b, 0xc0, 0xaf, 0xa2, 0x58, 0x76, 0xb0, 0xe9, 0xda, 0x7f, 0x0e, 0x78,
      0xc9, 0x76, 0x49, 0x5c, 0xfa, 0xb3, 0xb0, 0x15, 0x4b, 0x41, 0xc7, 0x27,
      0xa4, 0x75, 0x28, 0x5c, 0x30, 0x69, 0x50, 0x29,
  };
  static const uint8_t kQ[] = {
      0xda, 0xe6, 0xd2, 0xbb, 0x44, 0xff, 0x4f, 0xdf, 0x57, 0xc1, 0x11, 0xa3,
      0x51, 0xba, 0x17, 0x89, 0x4c, 0x01, 0xc0, 0x0c, 0x97, 0x34, 0x50, 0xcf,
      0x32, 0x1e, 0xc0, 0xbd, 0x7b, 0x35, 0xb5, 0x6a, 0x26, 0xcc, 0xea, 0x4c,
      0x8e, 0x87, 0x4a, 0x67, 0x8b, 0xd3, 0xe5, 0x4f, 0x3a, 0x60, 0x48, 0x59,
      0x04, 0x93, 0x39, 0xd7, 0x7c, 0xfb, 0x19, 0x1a, 0x34, 0xd5, 0xe8, 0xaf,
      0xe7, 0x22, 0x2c, 0x0d, 0xc2, 0x91, 0x69, 0xb6, 0xe9, 0x2a, 0xe9, 0x1c,
      0x4c, 0x6e, 0x8f, 0x40, 0xf5, 0xa8, 0x3e, 0x82, 0x69, 0x69, 0xbe, 0x9f,
      0x7d, 0x5c, 0x7f, 0x92, 0x78, 0x17, 0xa3, 0x6d, 0x41, 0x2d, 0x72, 0xed,
      0x3f, 0x71, 0xfa, 0x97, 0xb4, 0x63, 0xe4, 0x4f, 0xd9, 0x46, 0x03, 0xfb,
      0x00, 0xeb, 0x30, 0x70, 0xb9, 0x51, 0xd9, 0x0a, 0xd2, 0xf8, 0x50, 0xd4,
      0xfb, 0x43, 0x84, 0xf8, 0xac, 0x58, 0xc3, 0x7b,
  };
  static const uint8_t kDModPMinusOne[] = {
      0xf5, 0x50, 0x8f, 0x88, 0x7d, 0xdd, 0xb5, 0xb4, 0x2a, 0x8b, 0xd7, 0x4d,
      0x23, 0xfe, 0xaf, 0xe9, 0x16, 0x22, 0xd2, 0x41, 0xed, 0x88, 0xf2, 0x70,
      0xcb, 0x4d, 0xeb, 0xc1, 0x71, 0x97, 0xc4, 0x0b, 0x3e, 0x5a, 0x2d, 0x96,
      0xab, 0xfa, 0xfd, 0x12, 0x8b, 0xd3, 0x3e, 0x4e, 0x05, 0x6f, 0x04, 0xeb,
      0x59, 0x3c, 0x0e, 0xa1, 0x73, 0xbe, 0x9d, 0x99, 0x2f, 0x05, 0xf9, 0x54,
      0x8d, 0x98, 0x1e, 0x0d, 0xc4, 0x0c, 0xc3, 0x30, 0x23, 0xff, 0xe5, 0xd0,
      0x2b, 0xd5, 0x4e, 0x2b, 0xa0, 0xae, 0xb8, 0x32, 0x84, 0x45, 0x8b, 0x3c,
      0x6d, 0xf0, 0x10, 0x36, 0x9e, 0x6a, 0xc4, 0x67, 0xca, 0xa9, 0xfc, 0x06,
      0x96, 0xd0, 0xbc, 0xda, 0xd1, 0x55, 0x55, 0x8d, 0x77, 0x21, 0xf4, 0x82,
      0x39, 0x37, 0x91, 0xd5, 0x97, 0x56, 0x78, 0xc8, 0x3c, 0xcb, 0x5e, 0xf6,
      0xdc, 0x58, 0x48, 0xb3, 0x7c, 0x94, 0x29, 0x39,
  };
  static const uint8_t kDModQMinusOne[] = {
      0x64, 0x65, 0xbd, 0x7d, 0x1a, 0x96, 0x26, 0xa1, 0xfe, 0xf3, 0x94, 0x0d,
      0x5d, 0xec, 0x85, 0xe2, 0xf8, 0xb3, 0x4c, 0xcb, 0xf9, 0x85, 0x8b, 0x12,
      0x9c, 0xa0, 0x32, 0x32, 0x35, 0x92, 0x5a, 0x94, 0x47, 0x1b, 0x70, 0xd2,
      0x90, 0x04, 0x49, 0x01, 0xd8, 0xc5, 0xe4, 0xc4, 0x43, 0xb7, 0xe9, 0x36,
      0xba, 0xbc, 0x73, 0xa8, 0xfb, 0xaf, 0x86, 0xc1, 0xd8, 0x3d, 0xcb, 0xac,
      0xf1, 0xcb, 0x60, 0x7d, 0x27, 0x21, 0xde, 0x64, 0x7f, 0xe8, 0xa8, 0x65,
      0xcc, 0x40, 0x60, 0xff, 0xa0, 0x2b, 0xfc, 0x0f, 0x80, 0x1d, 0x79, 0xca,
      0x58, 0x8a, 0xd6, 0x0f, 0xed, 0x78, 0x9a, 0x02, 0x00, 0x04, 0xc2, 0x53,
      0x41, 0xe8, 0x1a, 0xd0, 0xfd, 0x71, 0x5b, 0x43, 0xac, 0x19, 0x4a, 0xb6,
      0x12, 0xa3, 0xcb, 0xe1, 0xc7, 0x7d, 0x5c, 0x98, 0x74, 0x4e, 0x63, 0x74,
      0x6b, 0x91, 0x7a, 0x29, 0x3b, 0x92, 0xb2, 0x85,
  };
  static const uint8_t kQInverseModP[] = {
      0xd0, 0xde, 0x19, 0xda, 0x1e, 0xa2, 0xd8, 0x8f, 0x1c, 0x92, 0x73, 0xb0,
      0xc9, 0x90, 0xc7, 0xf5, 0xec, 0xc5, 0x89, 0x01, 0x05, 0x78, 0x11, 0x2d,
      0x74, 0x34, 0x44, 0xad, 0xd5, 0xf7, 0xa4, 0xfe, 0x9f, 0x25, 0x4d, 0x0b,
      0x92, 0xe3, 0xb8, 0x7d, 0xd3, 0xfd, 0xa5, 0xca, 0x95, 0x60, 0xa3, 0xf9,
      0x55, 0x42, 0x14, 0xb2, 0x45, 0x51, 0x9f, 0x73, 0x88, 0x43, 0x8a, 0xd1,
      0x65, 0x9e, 0xd1, 0xf7, 0x82, 0x2a, 0x2a, 0x8d, 0x70, 0x56, 0xe3, 0xef,
      0xc9, 0x0e, 0x2a, 0x2c, 0x15, 0xaf, 0x7f, 0x97, 0x81, 0x66, 0xf3, 0xb5,
      0x00, 0xa9, 0x26, 0xcc, 0x1e, 0xc2, 0x98, 0xdd, 0xd3, 0x37, 0x06, 0x79,
      0xb3, 0x60, 0x58, 0x79, 0x99, 0x3f, 0xa3, 0x15, 0x1f, 0x31, 0xe3, 0x11,
      0x88, 0x4c, 0x35, 0x57, 0xfa, 0x79, 0xd7, 0xd8, 0x72, 0xee, 0x73, 0x95,
      0x89, 0x29, 0xc7, 0x05, 0x27, 0x68, 0x90, 0x15,
  };

  RSA *rsa = RSA_new();
  if (rsa == NULL ||  //
      !set_bignum(&rsa->n, kN, sizeof(kN)) ||
      !set_bignum(&rsa->e, kE, sizeof(kE)) ||
      !set_bignum(&rsa->d, kD, sizeof(kD)) ||
      !set_bignum(&rsa->p, kP, sizeof(kP)) ||
      !set_bignum(&rsa->q, kQ, sizeof(kQ)) ||
      !set_bignum(&rsa->dmp1, kDModPMinusOne, sizeof(kDModPMinusOne)) ||
      !set_bignum(&rsa->dmq1, kDModQMinusOne, sizeof(kDModQMinusOne)) ||
      !set_bignum(&rsa->iqmp, kQInverseModP, sizeof(kQInverseModP))) {
    RSA_free(rsa);
    return NULL;
  }

  return rsa;
}

static EC_KEY *self_test_ecdsa_key(void) {
  static const uint8_t kQx[] = {
      0xc8, 0x15, 0x61, 0xec, 0xf2, 0xe5, 0x4e, 0xde, 0xfe, 0x66, 0x17,
      0xdb, 0x1c, 0x7a, 0x34, 0xa7, 0x07, 0x44, 0xdd, 0xb2, 0x61, 0xf2,
      0x69, 0xb8, 0x3d, 0xac, 0xfc, 0xd2, 0xad, 0xe5, 0xa6, 0x81,
  };
  static const uint8_t kQy[] = {
      0xe0, 0xe2, 0xaf, 0xa3, 0xf9, 0xb6, 0xab, 0xe4, 0xc6, 0x98, 0xef,
      0x64, 0x95, 0xf1, 0xbe, 0x49, 0xa3, 0x19, 0x6c, 0x50, 0x56, 0xac,
      0xb3, 0x76, 0x3f, 0xe4, 0x50, 0x7e, 0xec, 0x59, 0x6e, 0x88,
  };
  static const uint8_t kD[] = {
      0xc6, 0xc1, 0xaa, 0xda, 0x15, 0xb0, 0x76, 0x61, 0xf8, 0x14, 0x2c,
      0x6c, 0xaf, 0x0f, 0xdb, 0x24, 0x1a, 0xff, 0x2e, 0xfe, 0x46, 0xc0,
      0x93, 0x8b, 0x74, 0xf2, 0xbc, 0xc5, 0x30, 0x52, 0xb0, 0x77,
  };

  EC_KEY *ec_key = EC_KEY_new();
  BIGNUM *qx = BN_bin2bn(kQx, sizeof(kQx), NULL);
  BIGNUM *qy = BN_bin2bn(kQy, sizeof(kQy), NULL);
  BIGNUM *d = BN_bin2bn(kD, sizeof(kD), NULL);
  if (ec_key == NULL || qx == NULL || qy == NULL || d == NULL ||
      !EC_KEY_set_group(ec_key, EC_group_p256()) ||
      !EC_KEY_set_public_key_affine_coordinates(ec_key, qx, qy) ||
      !EC_KEY_set_private_key(ec_key, d)) {
    EC_KEY_free(ec_key);
    ec_key = NULL;
  }

  BN_free(qx);
  BN_free(qy);
  BN_free(d);
  return ec_key;
}

static DH *self_test_dh(void) {
  DH *dh = DH_get_rfc7919_2048();
  if (!dh) {
    return NULL;
  }

  BIGNUM *priv = BN_new();
  if (!priv) {
    goto err;
  }

  // kFFDHE2048PrivateKeyData is a 225-bit value. (225 because that's the
  // minimum private key size in
  // https://tools.ietf.org/html/rfc7919#appendix-A.1.)
  static const BN_ULONG kFFDHE2048PrivateKeyData[] = {
      TOBN(0x187be36b, 0xd38a4fa1),
      TOBN(0x0a152f39, 0x6458f3b8),
      TOBN(0x0570187e, 0xc422eeb7),
      TOBN(0x00000001, 0x91173f2a),
  };

  bn_set_static_words(priv, kFFDHE2048PrivateKeyData,
                      OPENSSL_ARRAY_SIZE(kFFDHE2048PrivateKeyData));

  if (!DH_set0_key(dh, NULL, priv)) {
    goto err;
  }
  return dh;

err:
  BN_free(priv);
  DH_free(dh);
  return NULL;
}


// Lazy self-tests
//
// Self tests that are slow are deferred until the corresponding algorithm is
// actually exercised, in FIPS mode. (In non-FIPS mode these tests are only run
// when requested by |BORINGSSL_self_test|.)

static int boringssl_self_test_rsa(void) {
  int ret = 0;
  uint8_t output[256];

  RSA *const rsa_key = self_test_rsa_key();
  if (rsa_key == NULL) {
    fprintf(CRYPTO_get_stderr(), "RSA key construction failed\n");
    goto err;
  }
  // Disable blinding for the power-on tests because it's not needed and
  // triggers an entropy draw.
  rsa_key->flags |= RSA_FLAG_NO_BLINDING;

  // RSA Sign KAT

  static const uint8_t kRSASignDigest[32] = {
      0xd2, 0xb5, 0x6e, 0x53, 0x30, 0x6f, 0x72, 0x0d, 0x79, 0x29, 0xd8,
      0x70, 0x8b, 0xf4, 0x6f, 0x1c, 0x22, 0x30, 0x03, 0x05, 0x58, 0x2b,
      0x11, 0x5b, 0xed, 0xca, 0xc7, 0x22, 0xd8, 0xaa, 0x5a, 0xb2,
  };
  static const uint8_t kRSASignSignature[256] = {
      0x64, 0xce, 0xdd, 0x91, 0x27, 0xb0, 0x4f, 0xb9, 0x14, 0xea, 0xc0, 0xb4,
      0xa2, 0x06, 0xc5, 0xd8, 0x40, 0x0f, 0x6c, 0x54, 0xac, 0xf7, 0x02, 0xde,
      0x26, 0xbb, 0xfd, 0x33, 0xe5, 0x2f, 0x4d, 0xb1, 0x53, 0xc4, 0xff, 0xd0,
      0x5f, 0xea, 0x15, 0x89, 0x83, 0x4c, 0xe3, 0x80, 0x0b, 0xe9, 0x13, 0x82,
      0x1d, 0x71, 0x92, 0x1a, 0x03, 0x60, 0x2c, 0xaf, 0xe2, 0x16, 0xc7, 0x43,
      0x3f, 0xde, 0x6b, 0x94, 0xfd, 0x6e, 0x08, 0x7b, 0x11, 0xf1, 0x34, 0x52,
      0xe5, 0xc0, 0x97, 0x66, 0x4a, 0xe0, 0x91, 0x45, 0xc8, 0xb1, 0x3d, 0x6a,
      0x54, 0xc1, 0x32, 0x0f, 0x32, 0xad, 0x25, 0x11, 0x3e, 0x49, 0xad, 0x41,
      0xce, 0x7b, 0xca, 0x95, 0x6b, 0x54, 0x5e, 0x86, 0x1b, 0xce, 0xfa, 0x2a,
      0x60, 0xe8, 0xfa, 0xbb, 0x23, 0xb2, 0x41, 0xbc, 0x7c, 0x98, 0xec, 0x73,
      0x20, 0xed, 0xb3, 0xcf, 0xab, 0x07, 0x24, 0x85, 0x6a, 0x2a, 0x61, 0x76,
      0x28, 0xf8, 0x00, 0x80, 0xeb, 0xd9, 0x3a, 0x63, 0xe2, 0x01, 0xb1, 0xee,
      0x6d, 0xe9, 0x73, 0xe9, 0xb6, 0x75, 0x2e, 0xf9, 0x81, 0xd9, 0xa8, 0x79,
      0xf6, 0x8f, 0xe3, 0x02, 0x7d, 0xf6, 0xea, 0xdc, 0x35, 0xe4, 0x62, 0x0d,
      0x91, 0xba, 0x3e, 0x7d, 0x8b, 0x82, 0xbf, 0x15, 0x74, 0x6a, 0x4e, 0x29,
      0xf8, 0x9b, 0x2c, 0x94, 0x8d, 0xa7, 0x00, 0x4d, 0x7b, 0xbf, 0x35, 0x07,
      0xeb, 0xdd, 0x10, 0xef, 0xd5, 0x2f, 0xe6, 0x98, 0x4b, 0x7e, 0x24, 0x80,
      0xe2, 0x01, 0xf2, 0x66, 0xb7, 0xd3, 0x93, 0xfe, 0x2a, 0xb3, 0x74, 0xed,
      0xec, 0x4b, 0xb1, 0x5f, 0x5f, 0xee, 0x85, 0x44, 0xa7, 0x26, 0xdf, 0xc1,
      0x2e, 0x7a, 0xf3, 0xa5, 0x8f, 0xf8, 0x64, 0xda, 0x65, 0xad, 0x91, 0xe2,
      0x90, 0x94, 0x20, 0x16, 0xb8, 0x61, 0xa5, 0x0a, 0x7d, 0xb4, 0xbf, 0xc0,
      0x10, 0xaf, 0x72, 0x67,
  };

  unsigned sig_len;
  if (!rsa_sign_no_self_test(NID_sha256, kRSASignDigest, sizeof(kRSASignDigest),
                             output, &sig_len, rsa_key) ||
      !BORINGSSL_check_test(kRSASignSignature, output,
                            sizeof(kRSASignSignature), "RSA-sign KAT")) {
    fprintf(CRYPTO_get_stderr(), "RSA signing test failed.\n");
    goto err;
  }

  // RSA Verify KAT

  static const uint8_t kRSAVerifyDigest[32] = {
      0x09, 0x65, 0x2f, 0xd8, 0xed, 0x9d, 0xc2, 0x6d, 0xbc, 0xbf, 0xf2,
      0xa7, 0xa5, 0xed, 0xe1, 0x37, 0x13, 0x78, 0x21, 0x36, 0xcf, 0x8d,
      0x22, 0x3d, 0xab, 0x93, 0xb4, 0x12, 0xa8, 0xb5, 0x15, 0x53,
  };
  static const uint8_t kRSAVerifySignature[256] = {
      0xab, 0xe2, 0xcb, 0xc1, 0x3d, 0x6b, 0xd3, 0x9d, 0x48, 0xdb, 0x53, 0x34,
      0xdd, 0xbf, 0x8d, 0x07, 0x0a, 0x93, 0xbd, 0xcb, 0x10, 0x4e, 0x2c, 0xc5,
      0xd0, 0xee, 0x48, 0x6e, 0xe2, 0x95, 0xf6, 0xb3, 0x1b, 0xda, 0x12, 0x6c,
      0x41, 0x89, 0x0b, 0x98, 0xb7, 0x3e, 0x70, 0xe6, 0xb6, 0x5d, 0x82, 0xf9,
      0x5c, 0x66, 0x31, 0x21, 0x75, 0x5a, 0x90, 0x74, 0x4c, 0x8d, 0x1c, 0x21,
      0x14, 0x8a, 0x19, 0x60, 0xbe, 0x0e, 0xca, 0x44, 0x6e, 0x9f, 0xf4, 0x97,
      0xf1, 0x34, 0x5c, 0x53, 0x7e, 0xf8, 0x11, 0x9b, 0x9a, 0x43, 0x98, 0xe9,
      0x5c, 0x5c, 0x6d, 0xe2, 0xb1, 0xc9, 0x55, 0x90, 0x5c, 0x52, 0x99, 0xd8,
      0xce, 0x7a, 0x3b, 0x6a, 0xb7, 0x63, 0x80, 0xd9, 0xba, 0xbd, 0xd1, 0x5f,
      0x61, 0x02, 0x37, 0xe1, 0xf3, 0xf2, 0xaa, 0x1c, 0x1f, 0x1e, 0x77, 0x0b,
      0x62, 0xfb, 0xb5, 0x96, 0x38, 0x1b, 0x2e, 0xbd, 0xd7, 0x7e, 0xce, 0xf9,
      0xc9, 0x0d, 0x4c, 0x92, 0xf7, 0xb6, 0xb0, 0x5f, 0xed, 0x29, 0x36, 0x28,
      0x5f, 0xa9, 0x48, 0x26, 0xe6, 0x20, 0x55, 0x32, 0x2a, 0x33, 0xb6, 0xf0,
      0x4c, 0x74, 0xce, 0x69, 0xe5, 0xd8, 0xd7, 0x37, 0xfb, 0x83, 0x8b, 0x79,
      0xd2, 0xd4, 0x8e, 0x3d, 0xaf, 0x71, 0x38, 0x75, 0x31, 0x88, 0x25, 0x31,
      0xa9, 0x5a, 0xc9, 0x64, 0xd0, 0x2e, 0xa4, 0x13, 0xbf, 0x85, 0x95, 0x29,
      0x82, 0xbb, 0xc0, 0x89, 0x52, 0x7d, 0xaf, 0xf5, 0xb8, 0x45, 0xc9, 0xa0,
      0xf4, 0xd1, 0x4e, 0xf1, 0x95, 0x6d, 0x9c, 0x3a, 0xca, 0xe8, 0x82, 0xd1,
      0x2d, 0xa6, 0x6d, 0xa0, 0xf3, 0x57, 0x94, 0xf5, 0xee, 0x32, 0x23, 0x23,
      0x33, 0x51, 0x7d, 0xb9, 0x31, 0x52, 0x32, 0xa1, 0x83, 0xb9, 0x91, 0x65,
      0x4d, 0xbe, 0xa4, 0x16, 0x15, 0x34, 0x5c, 0x88, 0x53, 0x25, 0x92, 0x67,
      0x44, 0xa5, 0x39, 0x15,
  };
  if (!rsa_verify_no_self_test(NID_sha256, kRSAVerifyDigest,
                               sizeof(kRSAVerifyDigest), kRSAVerifySignature,
                               sizeof(kRSAVerifySignature), rsa_key)) {
    fprintf(CRYPTO_get_stderr(), "RSA-verify KAT failed.\n");
    goto err;
  }

  ret = 1;

err:
  RSA_free(rsa_key);

  return ret;
}

static int boringssl_self_test_ecc(void) {
  int ret = 0;
  EC_KEY *ec_key = NULL;
  EC_POINT *ec_point_in = NULL;
  EC_POINT *ec_point_out = NULL;
  BIGNUM *ec_scalar = NULL;
  const EC_GROUP *ec_group = NULL;

  // The 'k' value for ECDSA is fixed to avoid an entropy draw.
  uint8_t ecdsa_k[32] = {0};
  ecdsa_k[31] = 42;

  ec_key = self_test_ecdsa_key();
  if (ec_key == NULL) {
    fprintf(CRYPTO_get_stderr(), "ECDSA KeyGen failed\n");
    goto err;
  }

  // ECDSA Sign/Verify KAT

  static const uint8_t kECDSASignDigest[32] = {
      0x1e, 0x35, 0x93, 0x0b, 0xe8, 0x60, 0xd0, 0x94, 0x2c, 0xa7, 0xbb,
      0xd6, 0xf6, 0xde, 0xd8, 0x7f, 0x15, 0x7e, 0x4d, 0xe2, 0x4f, 0x81,
      0xed, 0x4b, 0x87, 0x5c, 0x0e, 0x01, 0x8e, 0x89, 0xa8, 0x1f,
  };
  static const uint8_t kECDSASignSig[64] = {
      0x67, 0x80, 0xc5, 0xfc, 0x70, 0x27, 0x5e, 0x2c, 0x70, 0x61, 0xa0,
      0xe7, 0x87, 0x7b, 0xb1, 0x74, 0xde, 0xad, 0xeb, 0x98, 0x87, 0x02,
      0x7f, 0x3f, 0xa8, 0x36, 0x54, 0x15, 0x8b, 0xa7, 0xf5, 0x0c, 0x68,
      0x04, 0x73, 0x40, 0x94, 0xb2, 0xd1, 0x90, 0xac, 0x2d, 0x0c, 0xd7,
      0xa5, 0x7f, 0x2f, 0x2e, 0xb2, 0x62, 0xb0, 0x09, 0x16, 0xe1, 0xa6,
      0x70, 0xb5, 0xbb, 0x0d, 0xfd, 0x8e, 0x0c, 0x02, 0x3f,
  };

  uint8_t ecdsa_sign_output[64];
  size_t ecdsa_sign_output_len;
  if (!ecdsa_sign_fixed_with_nonce_for_known_answer_test(
          kECDSASignDigest, sizeof(kECDSASignDigest), ecdsa_sign_output,
          &ecdsa_sign_output_len, sizeof(ecdsa_sign_output), ec_key, ecdsa_k,
          sizeof(ecdsa_k)) ||
      !BORINGSSL_check_test(kECDSASignSig, ecdsa_sign_output,
                            sizeof(ecdsa_sign_output),
                            "ECDSA-sign signature")) {
    fprintf(CRYPTO_get_stderr(), "ECDSA-sign KAT failed.\n");
    goto err;
  }

  static const uint8_t kECDSAVerifyDigest[32] = {
      0x78, 0x7c, 0x50, 0x5c, 0x60, 0xc9, 0xe4, 0x13, 0x6c, 0xe4, 0x48,
      0xba, 0x93, 0xff, 0x71, 0xfa, 0x9c, 0x18, 0xf4, 0x17, 0x09, 0x4f,
      0xdf, 0x5a, 0xe2, 0x75, 0xc0, 0xcc, 0xd2, 0x67, 0x97, 0xad,
  };
  static const uint8_t kECDSAVerifySig[64] = {
      0x67, 0x80, 0xc5, 0xfc, 0x70, 0x27, 0x5e, 0x2c, 0x70, 0x61, 0xa0,
      0xe7, 0x87, 0x7b, 0xb1, 0x74, 0xde, 0xad, 0xeb, 0x98, 0x87, 0x02,
      0x7f, 0x3f, 0xa8, 0x36, 0x54, 0x15, 0x8b, 0xa7, 0xf5, 0x0c, 0x2d,
      0x36, 0xe5, 0x79, 0x97, 0x90, 0xbf, 0xbe, 0x21, 0x83, 0xd3, 0x3e,
      0x96, 0xf3, 0xc5, 0x1f, 0x6a, 0x23, 0x2f, 0x2a, 0x24, 0x48, 0x8c,
      0x8e, 0x5f, 0x64, 0xc3, 0x7e, 0xa2, 0xcf, 0x05, 0x29,
  };

  if (!ecdsa_verify_fixed_no_self_test(
          kECDSAVerifyDigest, sizeof(kECDSAVerifyDigest), kECDSAVerifySig,
          sizeof(kECDSAVerifySig), ec_key)) {
    fprintf(CRYPTO_get_stderr(), "ECDSA-verify KAT failed.\n");
    goto err;
  }

  // Primitive Z Computation KAT (IG 9.6).

  // kP256Point is SHA256("Primitive Z Computation KAT")×G within P-256.
  static const uint8_t kP256Point[65] = {
      0x04, 0x4e, 0xc1, 0x94, 0x8c, 0x5c, 0xf4, 0x37, 0x35, 0x0d, 0xa3,
      0xf9, 0x55, 0xf9, 0x8b, 0x26, 0x23, 0x5c, 0x43, 0xe0, 0x83, 0x51,
      0x2b, 0x0d, 0x4b, 0x56, 0x24, 0xc3, 0xe4, 0xa5, 0xa8, 0xe2, 0xe9,
      0x95, 0xf2, 0xc4, 0xb9, 0xb7, 0x48, 0x7d, 0x2a, 0xae, 0xc5, 0xc0,
      0x0a, 0xcc, 0x1b, 0xd0, 0xec, 0xb8, 0xdc, 0xbe, 0x0c, 0xbe, 0x52,
      0x79, 0x93, 0x7c, 0x0b, 0x92, 0x2b, 0x7f, 0x17, 0xa5, 0x80,
  };
  // kP256Scalar is SHA256("Primitive Z Computation KAT scalar").
  static const uint8_t kP256Scalar[32] = {
      0xe7, 0x60, 0x44, 0x91, 0x26, 0x9a, 0xfb, 0x5b, 0x10, 0x2d, 0x6e,
      0xa5, 0x2c, 0xb5, 0x9f, 0xeb, 0x70, 0xae, 0xde, 0x6c, 0xe3, 0xbf,
      0xb3, 0xe0, 0x10, 0x54, 0x85, 0xab, 0xd8, 0x61, 0xd7, 0x7b,
  };
  // kP256PointResult is |kP256Scalar|×|kP256Point|.
  static const uint8_t kP256PointResult[65] = {
      0x04, 0xf1, 0x63, 0x00, 0x88, 0xc5, 0xd5, 0xe9, 0x05, 0x52, 0xac,
      0xb6, 0xec, 0x68, 0x76, 0xb8, 0x73, 0x7f, 0x0f, 0x72, 0x34, 0xe6,
      0xbb, 0x30, 0x32, 0x22, 0x37, 0xb6, 0x2a, 0x80, 0xe8, 0x9e, 0x6e,
      0x6f, 0x36, 0x02, 0xe7, 0x21, 0xd2, 0x31, 0xdb, 0x94, 0x63, 0xb7,
      0xd8, 0x19, 0x0e, 0xc2, 0xc0, 0xa7, 0x2f, 0x15, 0x49, 0x1a, 0xa2,
      0x7c, 0x41, 0x8f, 0xaf, 0x9c, 0x40, 0xaf, 0x2e, 0x4a, 0x0c,
  };

  ec_group = EC_group_p256();
  ec_point_in = EC_POINT_new(ec_group);
  ec_point_out = EC_POINT_new(ec_group);
  ec_scalar = BN_new();
  uint8_t z_comp_result[65];
  if (ec_point_in == NULL || ec_point_out == NULL || ec_scalar == NULL ||
      !EC_POINT_oct2point(ec_group, ec_point_in, kP256Point, sizeof(kP256Point),
                          NULL) ||
      !BN_bin2bn(kP256Scalar, sizeof(kP256Scalar), ec_scalar) ||
      !ec_point_mul_no_self_test(ec_group, ec_point_out, NULL, ec_point_in,
                                 ec_scalar, NULL) ||
      !EC_POINT_point2oct(ec_group, ec_point_out, POINT_CONVERSION_UNCOMPRESSED,
                          z_comp_result, sizeof(z_comp_result), NULL) ||
      !BORINGSSL_check_test(kP256PointResult, z_comp_result,
                            sizeof(z_comp_result), "Z Computation Result")) {
    fprintf(CRYPTO_get_stderr(), "Z-computation KAT failed.\n");
    goto err;
  }

  ret = 1;

err:
  EC_KEY_free(ec_key);
  EC_POINT_free(ec_point_in);
  EC_POINT_free(ec_point_out);
  BN_free(ec_scalar);

  return ret;
}

static int boringssl_self_test_ffdh(void) {
  int ret = 0;
  DH *dh = NULL;
  BIGNUM *ffdhe2048_value = NULL;

  // FFC Diffie-Hellman KAT

  // kFFDHE2048PublicValueData is an arbitrary public value, mod
  // kFFDHE2048Data. (The private key happens to be 4096.)
  static const BN_ULONG kFFDHE2048PublicValueData[] = {
      TOBN(0x187be36b, 0xd38a4fa1), TOBN(0x0a152f39, 0x6458f3b8),
      TOBN(0x0570187e, 0xc422eeb7), TOBN(0x18af7482, 0x91173f2a),
      TOBN(0xe9fdac6a, 0xcff4eaaa), TOBN(0xf6afebb7, 0x6e589d6c),
      TOBN(0xf92f8e9a, 0xb7e33fb0), TOBN(0x70acf2aa, 0x4cf36ddd),
      TOBN(0x561ab426, 0xd07137fd), TOBN(0x5f57d037, 0x430ee91e),
      TOBN(0xe3e768c8, 0x60d10b8a), TOBN(0xb14884d8, 0xa18af8ce),
      TOBN(0xf8a98014, 0xa12b74e4), TOBN(0x748d407c, 0x3437b7a8),
      TOBN(0x627588c4, 0x9875d5a7), TOBN(0xdd24a127, 0x53c8f09d),
      TOBN(0x85a997d5, 0x0cd51aec), TOBN(0x44f0c619, 0xce348458),
      TOBN(0x9b894b24, 0x5f6b69a1), TOBN(0xae1302f2, 0xf6d4777e),
      TOBN(0xe6678eeb, 0x375db18e), TOBN(0x2674e1d6, 0x4fbcbdc8),
      TOBN(0xb297a823, 0x6fa93d28), TOBN(0x6a12fb70, 0x7c8c0510),
      TOBN(0x5c6d1aeb, 0xdb06f65b), TOBN(0xe8c2954e, 0x4c1804ca),
      TOBN(0x06bdeac1, 0xf5500fa7), TOBN(0x6a315604, 0x189cd76b),
      TOBN(0xbae7b0b3, 0x6e362dc0), TOBN(0xa57c73bd, 0xdc70fb82),
      TOBN(0xfaff50d2, 0x9d573457), TOBN(0x352bd399, 0xbe84058e),
  };
  static const uint8_t kDHOutput[2048 / 8] = {
      0x2a, 0xe6, 0xd3, 0xa6, 0x13, 0x58, 0x8e, 0xce, 0x53, 0xaa, 0xf6, 0x5d,
      0x9a, 0xae, 0x02, 0x12, 0xf5, 0x80, 0x3d, 0x06, 0x09, 0x76, 0xac, 0x57,
      0x37, 0x9e, 0xab, 0x38, 0x62, 0x25, 0x05, 0x1d, 0xf3, 0xa9, 0x39, 0x60,
      0xf6, 0xae, 0x90, 0xed, 0x1e, 0xad, 0x6e, 0xe9, 0xe3, 0xba, 0x27, 0xf6,
      0xdb, 0x54, 0xdf, 0xe2, 0xbd, 0xbb, 0x7f, 0xf1, 0x81, 0xac, 0x1a, 0xfa,
      0xdb, 0x87, 0x07, 0x98, 0x76, 0x90, 0x21, 0xf2, 0xae, 0xda, 0x0d, 0x84,
      0x97, 0x64, 0x0b, 0xbf, 0xb8, 0x8d, 0x10, 0x46, 0xe2, 0xd5, 0xca, 0x1b,
      0xbb, 0xe5, 0x37, 0xb2, 0x3b, 0x35, 0xd3, 0x1b, 0x65, 0xea, 0xae, 0xf2,
      0x03, 0xe2, 0xb6, 0xde, 0x22, 0xb7, 0x86, 0x49, 0x79, 0xfe, 0xd7, 0x16,
      0xf7, 0xdc, 0x9c, 0x59, 0xf5, 0xb7, 0x70, 0xc0, 0x53, 0x42, 0x6f, 0xb1,
      0xd2, 0x4e, 0x00, 0x25, 0x4b, 0x2d, 0x5a, 0x9b, 0xd0, 0xe9, 0x27, 0x43,
      0xcc, 0x00, 0x66, 0xea, 0x94, 0x7a, 0x0b, 0xb9, 0x89, 0x0c, 0x5e, 0x94,
      0xb8, 0x3a, 0x78, 0x9c, 0x4d, 0x84, 0xe6, 0x32, 0x2c, 0x38, 0x7c, 0xf7,
      0x43, 0x9c, 0xd8, 0xb8, 0x1c, 0xce, 0x24, 0x91, 0x20, 0x67, 0x7a, 0x54,
      0x1f, 0x7e, 0x86, 0x7f, 0xa1, 0xc1, 0x03, 0x4e, 0x2c, 0x26, 0x71, 0xb2,
      0x06, 0x30, 0xb3, 0x6c, 0x15, 0xcc, 0xac, 0x25, 0xe5, 0x37, 0x3f, 0x24,
      0x8f, 0x2a, 0x89, 0x5e, 0x3d, 0x43, 0x94, 0xc9, 0x36, 0xae, 0x40, 0x00,
      0x6a, 0x0d, 0xb0, 0x6e, 0x8b, 0x2e, 0x70, 0x57, 0xe1, 0x88, 0x53, 0xd6,
      0x06, 0x80, 0x2a, 0x4e, 0x5a, 0xf0, 0x1e, 0xaa, 0xcb, 0xab, 0x06, 0x0e,
      0x27, 0x0f, 0xd9, 0x88, 0xd9, 0x01, 0xe3, 0x07, 0xeb, 0xdf, 0xc3, 0x12,
      0xe3, 0x40, 0x88, 0x7b, 0x5f, 0x59, 0x78, 0x6e, 0x26, 0x20, 0xc3, 0xdf,
      0xc8, 0xe4, 0x5e, 0xb8,
  };

  ffdhe2048_value = BN_new();
  if (ffdhe2048_value) {
    bn_set_static_words(ffdhe2048_value, kFFDHE2048PublicValueData,
                        OPENSSL_ARRAY_SIZE(kFFDHE2048PublicValueData));
  }

  dh = self_test_dh();
  uint8_t dh_out[sizeof(kDHOutput)];
  if (dh == NULL || ffdhe2048_value == NULL || sizeof(dh_out) != DH_size(dh) ||
      dh_compute_key_padded_no_self_test(dh_out, ffdhe2048_value, dh) !=
          sizeof(dh_out) ||
      !BORINGSSL_check_test(kDHOutput, dh_out, sizeof(dh_out), "FFC DH")) {
    fprintf(CRYPTO_get_stderr(), "FFDH failed.\n");
    goto err;
  }

  ret = 1;

err:
  DH_free(dh);
  BN_free(ffdhe2048_value);

  return ret;
}

#if defined(BORINGSSL_FIPS)

static void run_self_test_rsa(void) {
  FIPS_service_indicator_lock_state();
  if (!boringssl_self_test_rsa()) {
    BORINGSSL_FIPS_abort();
  }
  FIPS_service_indicator_unlock_state();
}

DEFINE_STATIC_ONCE(g_self_test_once_rsa)

void boringssl_ensure_rsa_self_test(void) {
  CRYPTO_once(g_self_test_once_rsa_bss_get(), run_self_test_rsa);
}

static void run_self_test_ecc(void) {
  FIPS_service_indicator_lock_state();
  if (!boringssl_self_test_ecc()) {
    BORINGSSL_FIPS_abort();
  }
  FIPS_service_indicator_unlock_state();
}

DEFINE_STATIC_ONCE(g_self_test_once_ecc)

void boringssl_ensure_ecc_self_test(void) {
  CRYPTO_once(g_self_test_once_ecc_bss_get(), run_self_test_ecc);
}

static void run_self_test_ffdh(void) {
  FIPS_service_indicator_lock_state();
  if (!boringssl_self_test_ffdh()) {
    BORINGSSL_FIPS_abort();
  }
  FIPS_service_indicator_unlock_state();
}

DEFINE_STATIC_ONCE(g_self_test_once_ffdh)

void boringssl_ensure_ffdh_self_test(void) {
  CRYPTO_once(g_self_test_once_ffdh_bss_get(), run_self_test_ffdh);
}

#endif  // BORINGSSL_FIPS


// Startup self tests.
//
// These tests are run at process start when in FIPS mode.

int boringssl_self_test_sha256(void) {
  static const uint8_t kInput[16] = {
      0xff, 0x3b, 0x85, 0x7d, 0xa7, 0x23, 0x6a, 0x2b,
      0xaa, 0x0f, 0x39, 0x6b, 0x51, 0x52, 0x22, 0x17,
  };
  static const uint8_t kPlaintextSHA256[32] = {
      0x7f, 0xe4, 0xd5, 0xf1, 0xa1, 0xe3, 0x82, 0x87, 0xd9, 0x58, 0xf5,
      0x11, 0xc7, 0x1d, 0x5e, 0x27, 0x5e, 0xcc, 0xd2, 0x66, 0xcf, 0xb9,
      0xc8, 0xc6, 0x60, 0xd8, 0x92, 0x1e, 0x57, 0xfd, 0x46, 0x75,
  };
  uint8_t output[SHA256_DIGEST_LENGTH];

  // SHA-256 KAT
  SHA256(kInput, sizeof(kInput), output);
  return BORINGSSL_check_test(kPlaintextSHA256, output,
                              sizeof(kPlaintextSHA256), "SHA-256 KAT");
}

int boringssl_self_test_sha512(void) {
  static const uint8_t kInput[16] = {
      0x21, 0x25, 0x12, 0xf8, 0xd2, 0xad, 0x83, 0x22,
      0x78, 0x1c, 0x6c, 0x4d, 0x69, 0xa9, 0xda, 0xa1,
  };
  static const uint8_t kPlaintextSHA512[64] = {
      0x29, 0x3c, 0x94, 0x35, 0x4e, 0x98, 0x83, 0xe5, 0xc2, 0x78, 0x36,
      0x7a, 0xe5, 0x18, 0x90, 0xbf, 0x35, 0x41, 0x01, 0x64, 0x19, 0x8d,
      0x26, 0xeb, 0xe1, 0xf8, 0x2f, 0x04, 0x8e, 0xfa, 0x8b, 0x2b, 0xc6,
      0xb2, 0x9d, 0x5d, 0x46, 0x76, 0x5a, 0xc8, 0xb5, 0x25, 0xa3, 0xea,
      0x52, 0x84, 0x47, 0x6d, 0x6d, 0xf4, 0xc9, 0x71, 0xf3, 0x3d, 0x89,
      0x4c, 0x3b, 0x20, 0x8c, 0x5b, 0x75, 0xe8, 0xf8, 0x7c,
  };
  uint8_t output[SHA512_DIGEST_LENGTH];

  // SHA-512 KAT
  SHA512(kInput, sizeof(kInput), output);
  return BORINGSSL_check_test(kPlaintextSHA512, output,
                              sizeof(kPlaintextSHA512), "SHA-512 KAT");
}

int boringssl_self_test_hmac_sha256(void) {
  static const uint8_t kInput[16] = {
      0xda, 0xd9, 0x12, 0x93, 0xdf, 0xcf, 0x2a, 0x7c,
      0x8e, 0xcd, 0x13, 0xfe, 0x35, 0x3f, 0xa7, 0x5b,
  };
  static const uint8_t kPlaintextHMACSHA256[32] = {
      0x36, 0x5f, 0x5b, 0xd5, 0xf5, 0xeb, 0xfd, 0xc7, 0x6e, 0x53, 0xa5,
      0x73, 0x6d, 0x73, 0x20, 0x13, 0xaa, 0xd3, 0xbc, 0x86, 0x4b, 0xb8,
      0x84, 0x94, 0x16, 0x46, 0x88, 0x9c, 0x48, 0xee, 0xa9, 0x0e,
  };
  uint8_t output[EVP_MAX_MD_SIZE];

  unsigned output_len;
  HMAC(EVP_sha256(), kInput, sizeof(kInput), kInput, sizeof(kInput), output,
       &output_len);
  return output_len == sizeof(kPlaintextHMACSHA256) &&
         BORINGSSL_check_test(kPlaintextHMACSHA256, output,
                              sizeof(kPlaintextHMACSHA256), "HMAC-SHA-256 KAT");
}

static int boringssl_self_test_fast(void) {
  static const uint8_t kAESKey[16] = {
      'B', 'o', 'r', 'i', 'n', 'g', 'C', 'r',
      'y', 'p', 't', 'o', ' ', 'K', 'e', 'y',
  };
  static const uint8_t kAESIV[16] = {0};

  EVP_AEAD_CTX aead_ctx;
  EVP_AEAD_CTX_zero(&aead_ctx);
  int ret = 0;

  AES_KEY aes_key;
  uint8_t aes_iv[16];
  uint8_t output[256];

  // AES-CBC Encryption KAT
  static const uint8_t kAESCBCEncPlaintext[32] = {
      0x07, 0x86, 0x09, 0xa6, 0xc5, 0xac, 0x25, 0x44, 0x69, 0x9a, 0xdf,
      0x68, 0x2f, 0xa3, 0x77, 0xf9, 0xbe, 0x8a, 0xb6, 0xae, 0xf5, 0x63,
      0xe8, 0xc5, 0x6a, 0x36, 0xb8, 0x4f, 0x55, 0x7f, 0xad, 0xd3,
  };
  static const uint8_t kAESCBCEncCiphertext[sizeof(kAESCBCEncPlaintext)] = {
      0x56, 0x46, 0xc1, 0x41, 0xf4, 0x13, 0xd6, 0xff, 0x62, 0x92, 0x41,
      0x7a, 0x26, 0xc6, 0x86, 0xbd, 0x30, 0x5f, 0xb6, 0x57, 0xa7, 0xd2,
      0x50, 0x3a, 0xc5, 0x5e, 0x8e, 0x93, 0x40, 0xf2, 0x10, 0xd8,
  };
  memcpy(aes_iv, kAESIV, sizeof(kAESIV));
  if (!bcm_success(
          BCM_aes_set_encrypt_key(kAESKey, 8 * sizeof(kAESKey), &aes_key))) {
    fprintf(CRYPTO_get_stderr(), "BCM_aes_set_encrypt_key failed.\n");
    goto err;
  }
  AES_cbc_encrypt(kAESCBCEncPlaintext, output, sizeof(kAESCBCEncPlaintext),
                  &aes_key, aes_iv, AES_ENCRYPT);
  if (!BORINGSSL_check_test(kAESCBCEncCiphertext, output,
                            sizeof(kAESCBCEncCiphertext),
                            "AES-CBC-encrypt KAT")) {
    goto err;
  }

  // AES-CBC Decryption KAT
  static const uint8_t kAESCBCDecCiphertext[32] = {
      0x34, 0x7a, 0xa5, 0xa0, 0x24, 0xb2, 0x82, 0x57, 0xb3, 0x65, 0x10,
      0xbe, 0x58, 0x3d, 0x4f, 0x47, 0xad, 0xb7, 0xbb, 0xee, 0xdc, 0x60,
      0x05, 0xbb, 0xbd, 0x0d, 0x0a, 0x9f, 0x06, 0xbb, 0x7b, 0x10,
  };
  static const uint8_t kAESCBCDecPlaintext[sizeof(kAESCBCDecCiphertext)] = {
      0x51, 0xa7, 0xa0, 0x1f, 0x6b, 0x79, 0x6c, 0xcd, 0x48, 0x03, 0xa1,
      0x41, 0xdc, 0x56, 0xa6, 0xc2, 0x16, 0xb5, 0xd1, 0xd3, 0xb7, 0x06,
      0xb2, 0x25, 0x6f, 0xa6, 0xd0, 0xd2, 0x0e, 0x6f, 0x19, 0xb5,
  };
  memcpy(aes_iv, kAESIV, sizeof(kAESIV));
  if (!bcm_success(
          BCM_aes_set_decrypt_key(kAESKey, 8 * sizeof(kAESKey), &aes_key))) {
    fprintf(CRYPTO_get_stderr(), "BCM_aes_set_decrypt_key failed.\n");
    goto err;
  }
  AES_cbc_encrypt(kAESCBCDecCiphertext, output, sizeof(kAESCBCDecCiphertext),
                  &aes_key, aes_iv, AES_DECRYPT);
  if (!BORINGSSL_check_test(kAESCBCDecPlaintext, output,
                            sizeof(kAESCBCDecPlaintext),
                            "AES-CBC-decrypt KAT")) {
    goto err;
  }

  size_t out_len;
  uint8_t nonce[EVP_AEAD_MAX_NONCE_LENGTH];
  OPENSSL_memset(nonce, 0, sizeof(nonce));
  if (!EVP_AEAD_CTX_init(&aead_ctx, EVP_aead_aes_128_gcm(), kAESKey,
                         sizeof(kAESKey), 0, NULL)) {
    fprintf(CRYPTO_get_stderr(), "EVP_AEAD_CTX_init for AES-128-GCM failed.\n");
    goto err;
  }

  // AES-GCM Encryption KAT
  static const uint8_t kAESGCMEncPlaintext[32] = {
      0x8f, 0xcc, 0x40, 0x99, 0x80, 0x8e, 0x75, 0xca, 0xaf, 0xf5, 0x82,
      0x89, 0x88, 0x48, 0xa8, 0x8d, 0x80, 0x8b, 0x55, 0xab, 0x4e, 0x93,
      0x70, 0x79, 0x7d, 0x94, 0x0b, 0xe8, 0xcc, 0x1d, 0x78, 0x84,
  };
  static const uint8_t kAESGCMCiphertext[sizeof(kAESGCMEncPlaintext) + 16] = {
      0x87, 0x7b, 0xd5, 0x8d, 0x96, 0x3e, 0x4b, 0xe6, 0x64, 0x94, 0x40, 0x2f,
      0x61, 0x9b, 0x7e, 0x56, 0x52, 0x7d, 0xa4, 0x5a, 0xf9, 0xa6, 0xe2, 0xdb,
      0x1c, 0x63, 0x2e, 0x97, 0x93, 0x0f, 0xfb, 0xed, 0xb5, 0x9e, 0x1c, 0x20,
      0xb2, 0xb0, 0x58, 0xda, 0x48, 0x07, 0x2d, 0xbd, 0x96, 0x0d, 0x34, 0xc6,
  };
  if (!EVP_AEAD_CTX_seal(&aead_ctx, output, &out_len, sizeof(output), nonce,
                         EVP_AEAD_nonce_length(EVP_aead_aes_128_gcm()),
                         kAESGCMEncPlaintext, sizeof(kAESGCMEncPlaintext), NULL,
                         0) ||
      !BORINGSSL_check_test(kAESGCMCiphertext, output,
                            sizeof(kAESGCMCiphertext), "AES-GCM-encrypt KAT")) {
    fprintf(CRYPTO_get_stderr(), "EVP_AEAD_CTX_seal for AES-128-GCM failed.\n");
    goto err;
  }

  // AES-GCM Decryption KAT
  static const uint8_t kAESGCMDecCiphertext[48] = {
      0x35, 0xf3, 0x05, 0x8f, 0x87, 0x57, 0x60, 0xff, 0x09, 0xd3, 0x12, 0x0f,
      0x70, 0xc4, 0xbc, 0x9e, 0xd7, 0xa8, 0x68, 0x72, 0xe1, 0x34, 0x52, 0x20,
      0x21, 0x76, 0xf7, 0x37, 0x1a, 0xe0, 0x4f, 0xaa, 0xe1, 0xdd, 0x39, 0x19,
      0x20, 0xf5, 0xd1, 0x39, 0x53, 0xd8, 0x96, 0x78, 0x59, 0x94, 0x82, 0x3c,
  };
  static const uint8_t kAESGCMDecPlaintext[sizeof(kAESGCMDecCiphertext) - 16] =
      {
          0x3d, 0x44, 0x90, 0x9b, 0x91, 0xe7, 0x5e, 0xd3, 0xc2, 0xb2, 0xd0,
          0xa9, 0x99, 0x17, 0x6a, 0x45, 0x05, 0x5e, 0x99, 0x83, 0x56, 0x01,
          0xc0, 0x82, 0x40, 0x81, 0xd2, 0x48, 0x45, 0xf2, 0xcc, 0xc3,
      };
  if (!EVP_AEAD_CTX_open(&aead_ctx, output, &out_len, sizeof(output), nonce,
                         EVP_AEAD_nonce_length(EVP_aead_aes_128_gcm()),
                         kAESGCMDecCiphertext, sizeof(kAESGCMDecCiphertext),
                         NULL, 0) ||
      !BORINGSSL_check_test(kAESGCMDecPlaintext, output,
                            sizeof(kAESGCMDecPlaintext),
                            "AES-GCM-decrypt KAT")) {
    fprintf(CRYPTO_get_stderr(),
            "AES-GCM-decrypt KAT failed because EVP_AEAD_CTX_open failed.\n");
    goto err;
  }

  // SHA-1 KAT
  static const uint8_t kSHA1Input[16] = {
      0x13, 0x2f, 0xd9, 0xba, 0xd5, 0xc1, 0x82, 0x62,
      0x63, 0xba, 0xfb, 0xb6, 0x99, 0xf7, 0x07, 0xa5,
  };
  static const uint8_t kSHA1Digest[20] = {
      0x94, 0x19, 0x55, 0x93, 0x0a, 0x58, 0x29, 0x38, 0xeb, 0xf5,
      0x09, 0x11, 0x6d, 0x1a, 0xfd, 0x0f, 0x1e, 0x11, 0xe3, 0xcb,
  };
  SHA1(kSHA1Input, sizeof(kSHA1Input), output);
  if (!BORINGSSL_check_test(kSHA1Digest, output, sizeof(kSHA1Digest),
                            "SHA-1 KAT")) {
    goto err;
  }

  if (!boringssl_self_test_sha256() || !boringssl_self_test_sha512() ||
      !boringssl_self_test_hmac_sha256()) {
    goto err;
  }

  // DBRG KAT
  static const uint8_t kDRBGEntropy[48] = {
      0xc4, 0xda, 0x07, 0x40, 0xd5, 0x05, 0xf1, 0xee, 0x28, 0x0b, 0x95, 0xe5,
      0x8c, 0x49, 0x31, 0xac, 0x6d, 0xe8, 0x46, 0xa0, 0x15, 0x2f, 0xbb, 0x4a,
      0x3f, 0x17, 0x4c, 0xf4, 0x78, 0x7a, 0x4f, 0x1a, 0x40, 0xc2, 0xb5, 0x0b,
      0xab, 0xe1, 0x4a, 0xae, 0x53, 0x0b, 0xe5, 0x88, 0x6d, 0x91, 0x0a, 0x27,
  };
  static const uint8_t kDRBGPersonalization[18] = {
      'B', 'C', 'M', 'P', 'e', 'r', 's', 'o', 'n',
      'a', 'l', 'i', 'z', 'a', 't', 'i', 'o', 'n'};
  static const uint8_t kDRBGAD[16] = {'B', 'C', 'M', ' ', 'D', 'R', 'B', 'G',
                                      ' ', 'K', 'A', 'T', ' ', 'A', 'D', ' '};
  static const uint8_t kDRBGOutput[64] = {
      0x19, 0x1f, 0x2b, 0x49, 0x76, 0x85, 0xfd, 0x51, 0xb6, 0x56, 0xbc,
      0x1c, 0x7d, 0xd5, 0xdd, 0x44, 0x76, 0xa3, 0x5e, 0x17, 0x9b, 0x8e,
      0xb8, 0x98, 0x65, 0x12, 0xca, 0x35, 0x6c, 0xa0, 0x6f, 0xa0, 0x22,
      0xe4, 0xf6, 0xd8, 0x43, 0xed, 0x4e, 0x2d, 0x97, 0x39, 0x43, 0x3b,
      0x57, 0xfc, 0x23, 0x3f, 0x71, 0x0a, 0xe0, 0xed, 0xfe, 0xd5, 0xb8,
      0x67, 0x7a, 0x00, 0x39, 0xb2, 0x6e, 0xa9, 0x25, 0x97,
  };
  static const uint8_t kDRBGEntropy2[48] = {
      0xc7, 0x16, 0x1c, 0xa3, 0x6c, 0x23, 0x09, 0xb7, 0x16, 0xe9, 0x85, 0x9b,
      0xb9, 0x6c, 0x6d, 0x49, 0xbd, 0xc8, 0x35, 0x21, 0x03, 0xa1, 0x8c, 0xd2,
      0x4e, 0xf4, 0x2e, 0xc9, 0x7e, 0xf4, 0x6b, 0xf4, 0x46, 0xeb, 0x1a, 0x45,
      0x76, 0xc1, 0x86, 0xe9, 0x35, 0x18, 0x03, 0x76, 0x3a, 0x79, 0x12, 0xfe,
  };
  static const uint8_t kDRBGReseedOutput[64] = {
      0x00, 0xf2, 0x05, 0xaa, 0xfd, 0x11, 0x6c, 0x77, 0xbc, 0x81, 0x86,
      0x99, 0xca, 0x51, 0xcf, 0x80, 0x15, 0x9f, 0x02, 0x9e, 0x0b, 0xcd,
      0x26, 0xc8, 0x4b, 0x87, 0x8a, 0x15, 0x1a, 0xdd, 0xf2, 0xf3, 0xeb,
      0x94, 0x0b, 0x08, 0xc8, 0xc9, 0x57, 0xa4, 0x0b, 0x4b, 0x0f, 0x13,
      0xde, 0x7c, 0x0c, 0x6a, 0xac, 0x34, 0x4a, 0x9a, 0xf2, 0xd0, 0x83,
      0x02, 0x05, 0x17, 0xc9, 0x81, 0x8f, 0x2a, 0x81, 0x92,
  };
  CTR_DRBG_STATE drbg;
  if (!CTR_DRBG_init(&drbg, kDRBGEntropy, kDRBGPersonalization,
                     sizeof(kDRBGPersonalization)) ||
      !CTR_DRBG_generate(&drbg, output, sizeof(kDRBGOutput), kDRBGAD,
                         sizeof(kDRBGAD)) ||
      !BORINGSSL_check_test(kDRBGOutput, output, sizeof(kDRBGOutput),
                            "DRBG Generate KAT") ||
      !CTR_DRBG_reseed(&drbg, kDRBGEntropy2, kDRBGAD, sizeof(kDRBGAD)) ||
      !CTR_DRBG_generate(&drbg, output, sizeof(kDRBGReseedOutput), kDRBGAD,
                         sizeof(kDRBGAD)) ||
      !BORINGSSL_check_test(kDRBGReseedOutput, output,
                            sizeof(kDRBGReseedOutput), "DRBG-reseed KAT")) {
    fprintf(CRYPTO_get_stderr(), "CTR-DRBG failed.\n");
    goto err;
  }
  CTR_DRBG_clear(&drbg);

  CTR_DRBG_STATE kZeroDRBG;
  memset(&kZeroDRBG, 0, sizeof(kZeroDRBG));
  if (!BORINGSSL_check_test(&kZeroDRBG, &drbg, sizeof(drbg),
                            "DRBG Clear KAT")) {
    goto err;
  }

  // TLS KDF KAT
  static const char kTLSLabel[] = "FIPS self test";
  static const uint8_t kTLSSeed1[16] = {
      0x8f, 0x0d, 0xe8, 0xb6, 0x90, 0x8f, 0xb1, 0xd2,
      0x6d, 0x51, 0xf4, 0x79, 0x18, 0x63, 0x51, 0x65,
  };
  static const uint8_t kTLSSeed2[16] = {
      0x7d, 0x24, 0x1a, 0x9d, 0x3c, 0x59, 0xbf, 0x3c,
      0x31, 0x1e, 0x2b, 0x21, 0x41, 0x8d, 0x32, 0x81,
  };

  static const uint8_t kTLS10Secret[32] = {
      0xab, 0xc3, 0x65, 0x7b, 0x09, 0x4c, 0x76, 0x28, 0xa0, 0xb2, 0x82,
      0x99, 0x6f, 0xe7, 0x5a, 0x75, 0xf4, 0x98, 0x4f, 0xd9, 0x4d, 0x4e,
      0xcc, 0x2f, 0xcf, 0x53, 0xa2, 0xc4, 0x69, 0xa3, 0xf7, 0x31,
  };
  static const uint8_t kTLS10Output[32] = {
      0x69, 0x7c, 0x4e, 0x2c, 0xee, 0x82, 0xb1, 0xd2, 0x8b, 0xac, 0x90,
      0x7a, 0xa1, 0x8a, 0x81, 0xfe, 0xc5, 0x58, 0x45, 0x57, 0x61, 0x2f,
      0x7a, 0x8d, 0x80, 0xfb, 0x44, 0xd8, 0x81, 0x60, 0xe5, 0xf8,
  };
  uint8_t tls10_output[sizeof(kTLS10Output)];
  if (!CRYPTO_tls1_prf(EVP_md5_sha1(), tls10_output, sizeof(tls10_output),
                       kTLS10Secret, sizeof(kTLS10Secret), kTLSLabel,
                       sizeof(kTLSLabel), kTLSSeed1, sizeof(kTLSSeed1),
                       kTLSSeed2, sizeof(kTLSSeed2)) ||
      !BORINGSSL_check_test(kTLS10Output, tls10_output, sizeof(kTLS10Output),
                            "TLS10-KDF KAT")) {
    fprintf(CRYPTO_get_stderr(), "TLS KDF failed.\n");
    goto err;
  }

  static const uint8_t kTLS12Secret[32] = {
      0xc5, 0x43, 0x8e, 0xe2, 0x6f, 0xd4, 0xac, 0xbd, 0x25, 0x9f, 0xc9,
      0x18, 0x55, 0xdc, 0x69, 0xbf, 0x88, 0x4e, 0xe2, 0x93, 0x22, 0xfc,
      0xbf, 0xd2, 0x96, 0x6a, 0x46, 0x23, 0xd4, 0x2e, 0xc7, 0x81,
  };
  static const uint8_t kTLS12Output[32] = {
      0xee, 0x4a, 0xcd, 0x3f, 0xa3, 0xd3, 0x55, 0x89, 0x9e, 0x6f, 0xf1,
      0x38, 0x46, 0x9d, 0x2b, 0x33, 0xaa, 0x7f, 0xc4, 0x7f, 0x51, 0x85,
      0x8a, 0xf3, 0x13, 0x84, 0xbf, 0x53, 0x6a, 0x65, 0x37, 0x51,
  };
  uint8_t tls12_output[sizeof(kTLS12Output)];
  if (!CRYPTO_tls1_prf(EVP_sha256(), tls12_output, sizeof(tls12_output),
                       kTLS12Secret, sizeof(kTLS12Secret), kTLSLabel,
                       sizeof(kTLSLabel), kTLSSeed1, sizeof(kTLSSeed1),
                       kTLSSeed2, sizeof(kTLSSeed2)) ||
      !BORINGSSL_check_test(kTLS12Output, tls12_output, sizeof(kTLS12Output),
                            "TLS12-KDF KAT")) {
    fprintf(CRYPTO_get_stderr(), "TLS KDF failed.\n");
    goto err;
  }

  // TLS v1.3: derives a dummy client-early-traffic secret.
  static const uint8_t kTLS13Secret[32] = {
      0x02, 0x4a, 0x0d, 0x80, 0xf3, 0x57, 0xf2, 0x49, 0x9a, 0x12, 0x44,
      0xda, 0xc2, 0x6d, 0xab, 0x66, 0xfc, 0x13, 0xed, 0x85, 0xfc, 0xa7,
      0x1d, 0xac, 0xe1, 0x46, 0x21, 0x11, 0x19, 0x52, 0x58, 0x74,
  };
  static const uint8_t kTLS13Salt[16] = {
      0x54, 0x61, 0x11, 0x36, 0x75, 0x91, 0xf0, 0xf8,
      0x92, 0xec, 0x70, 0xbd, 0x78, 0x2a, 0xef, 0x61,
  };
  static const uint8_t kTLS13Label[] = "c e traffic";
  static const uint8_t kTLS13ClientHelloHash[32] = {
      0x1d, 0xe8, 0x67, 0xed, 0x93, 0x6a, 0x73, 0x65, 0x9b, 0x05, 0xcf,
      0x8a, 0x22, 0x77, 0xb7, 0x37, 0x29, 0xf2, 0x44, 0x94, 0x81, 0x6a,
      0x83, 0x33, 0x7f, 0x09, 0xbb, 0x6c, 0xc2, 0x6f, 0x48, 0x9c,
  };
  static const uint8_t kTLS13ExpandLabelOutput[32] = {
      0x62, 0x91, 0x52, 0x90, 0x2e, 0xc9, 0xcf, 0x9c, 0x5f, 0x1e, 0x0a,
      0xb7, 0x00, 0x33, 0x42, 0x24, 0xc4, 0xe3, 0xba, 0x01, 0x40, 0x32,
      0x06, 0xab, 0x09, 0x23, 0x8a, 0xdd, 0x01, 0xa4, 0x05, 0xcd,
  };
  uint8_t tls13_extract_output[32];
  size_t tls13_extract_output_len;
  uint8_t tls13_expand_label_output[32];
  if (!HKDF_extract(tls13_extract_output, &tls13_extract_output_len,
                    EVP_sha256(), kTLS13Secret, sizeof(kTLS13Secret),
                    kTLS13Salt, sizeof(kTLS13Salt)) ||
      tls13_extract_output_len != sizeof(tls13_extract_output) ||
      !CRYPTO_tls13_hkdf_expand_label(
          tls13_expand_label_output, sizeof(tls13_expand_label_output),
          EVP_sha256(), tls13_extract_output, sizeof(tls13_extract_output),
          kTLS13Label, sizeof(kTLS13Label) - 1, kTLS13ClientHelloHash,
          sizeof(kTLS13ClientHelloHash)) ||
      !BORINGSSL_check_test(kTLS13ExpandLabelOutput, tls13_expand_label_output,
                            sizeof(kTLS13ExpandLabelOutput),
                            "CRYPTO_tls13_hkdf_expand_label")) {
    fprintf(CRYPTO_get_stderr(), "TLS13-KDF failed.\n");
    goto err;
  }

  // HKDF
  static const uint8_t kHKDFSecret[32] = {
      0x68, 0x67, 0x85, 0x04, 0xb9, 0xb3, 0xad, 0xd1, 0x7d, 0x59, 0x67,
      0xa1, 0xa7, 0xbd, 0x37, 0x99, 0x3f, 0xd8, 0xa3, 0x3c, 0xe7, 0x30,
      0x30, 0x71, 0xf3, 0x9c, 0x09, 0x6d, 0x16, 0x35, 0xb3, 0xc9,
  };
  static const uint8_t kHKDFSalt[32] = {
      0x8a, 0xab, 0x18, 0xb4, 0x9b, 0x0a, 0x17, 0xf9, 0xe8, 0xe6, 0x97,
      0x1a, 0x3d, 0xff, 0xda, 0x9b, 0x26, 0x8b, 0x3d, 0x17, 0x78, 0x0a,
      0xb3, 0xea, 0x65, 0xdb, 0x2a, 0xc0, 0x29, 0x9c, 0xfa, 0x72,
  };
  static const uint8_t kHKDFInfo[32] = {
      0xe5, 0x6f, 0xf9, 0xe1, 0x18, 0x5e, 0x64, 0x8c, 0x6c, 0x8f, 0xee,
      0xc6, 0x93, 0x5a, 0xc5, 0x14, 0x8c, 0xf3, 0xd9, 0x78, 0xd2, 0x3a,
      0x86, 0xdd, 0x01, 0xdf, 0xb9, 0xe9, 0x5e, 0xe5, 0x1a, 0x56,
  };
  static const uint8_t kHKDFOutput[32] = {
      0xa6, 0x29, 0xb4, 0xd7, 0xf4, 0xc1, 0x16, 0x64, 0x71, 0x5e, 0xa4,
      0xa8, 0xe6, 0x60, 0x8c, 0xf3, 0xc1, 0xa5, 0x03, 0xe2, 0x22, 0xf9,
      0x89, 0xe2, 0x12, 0x18, 0xbe, 0xef, 0x16, 0x86, 0xe0, 0xec,
  };
  uint8_t hkdf_output[sizeof(kHKDFOutput)];
  if (!HKDF(hkdf_output, sizeof(hkdf_output), EVP_sha256(), kHKDFSecret,
            sizeof(kHKDFSecret), kHKDFSalt, sizeof(kHKDFSalt), kHKDFInfo,
            sizeof(kHKDFInfo)) ||
      !BORINGSSL_check_test(kHKDFOutput, hkdf_output, sizeof(kHKDFOutput),
                            "HKDF")) {
    fprintf(CRYPTO_get_stderr(), "HKDF failed.\n");
    goto err;
  }

  ret = 1;

err:
  EVP_AEAD_CTX_cleanup(&aead_ctx);

  return ret;
}

int BORINGSSL_self_test(void) {
  if (!boringssl_self_test_fast() ||
      // When requested to run self tests, also run the lazy tests.
      !boringssl_self_test_rsa() ||    //
      !boringssl_self_test_ecc() ||    //
      !boringssl_self_test_ffdh() ||   //
      !boringssl_self_test_mlkem() ||  //
      !boringssl_self_test_mldsa() ||  //
      !boringssl_self_test_slhdsa()) {
    return 0;
  }

  return 1;
}

#if defined(BORINGSSL_FIPS)
int boringssl_self_test_startup(void) { return boringssl_self_test_fast(); }
#endif

#endif  // !_MSC_VER
