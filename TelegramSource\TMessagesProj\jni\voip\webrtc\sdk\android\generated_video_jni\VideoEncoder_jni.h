// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/VideoEncoder

#ifndef org_webrtc_VideoEncoder_JNI
#define org_webrtc_VideoEncoder_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_VideoEncoder[];
const char kClassPath_org_webrtc_VideoEncoder[] = "org/webrtc/VideoEncoder";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_VideoEncoder_00024BitrateAllocation[];
const char kClassPath_org_webrtc_VideoEncoder_00024BitrateAllocation[] =
    "org/webrtc/VideoEncoder$BitrateAllocation";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_VideoEncoder_00024Capabilities[];
const char kClassPath_org_webrtc_VideoEncoder_00024Capabilities[] =
    "org/webrtc/VideoEncoder$Capabilities";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_VideoEncoder_00024EncodeInfo[];
const char kClassPath_org_webrtc_VideoEncoder_00024EncodeInfo[] =
    "org/webrtc/VideoEncoder$EncodeInfo";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_VideoEncoder_00024EncoderInfo[];
const char kClassPath_org_webrtc_VideoEncoder_00024EncoderInfo[] =
    "org/webrtc/VideoEncoder$EncoderInfo";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_VideoEncoder_00024RateControlParameters[];
const char kClassPath_org_webrtc_VideoEncoder_00024RateControlParameters[] =
    "org/webrtc/VideoEncoder$RateControlParameters";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_VideoEncoder_00024ResolutionBitrateLimits[];
const char kClassPath_org_webrtc_VideoEncoder_00024ResolutionBitrateLimits[] =
    "org/webrtc/VideoEncoder$ResolutionBitrateLimits";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_VideoEncoder_00024Settings[];
const char kClassPath_org_webrtc_VideoEncoder_00024Settings[] = "org/webrtc/VideoEncoder$Settings";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_VideoEncoder_clazz(nullptr);
#ifndef org_webrtc_VideoEncoder_clazz_defined
#define org_webrtc_VideoEncoder_clazz_defined
inline jclass org_webrtc_VideoEncoder_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_VideoEncoder,
      &g_org_webrtc_VideoEncoder_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_VideoEncoder_00024BitrateAllocation_clazz(nullptr);
#ifndef org_webrtc_VideoEncoder_00024BitrateAllocation_clazz_defined
#define org_webrtc_VideoEncoder_00024BitrateAllocation_clazz_defined
inline jclass org_webrtc_VideoEncoder_00024BitrateAllocation_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_VideoEncoder_00024BitrateAllocation,
      &g_org_webrtc_VideoEncoder_00024BitrateAllocation_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_VideoEncoder_00024Capabilities_clazz(nullptr);
#ifndef org_webrtc_VideoEncoder_00024Capabilities_clazz_defined
#define org_webrtc_VideoEncoder_00024Capabilities_clazz_defined
inline jclass org_webrtc_VideoEncoder_00024Capabilities_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_VideoEncoder_00024Capabilities,
      &g_org_webrtc_VideoEncoder_00024Capabilities_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_VideoEncoder_00024EncodeInfo_clazz(nullptr);
#ifndef org_webrtc_VideoEncoder_00024EncodeInfo_clazz_defined
#define org_webrtc_VideoEncoder_00024EncodeInfo_clazz_defined
inline jclass org_webrtc_VideoEncoder_00024EncodeInfo_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_VideoEncoder_00024EncodeInfo,
      &g_org_webrtc_VideoEncoder_00024EncodeInfo_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_VideoEncoder_00024EncoderInfo_clazz(nullptr);
#ifndef org_webrtc_VideoEncoder_00024EncoderInfo_clazz_defined
#define org_webrtc_VideoEncoder_00024EncoderInfo_clazz_defined
inline jclass org_webrtc_VideoEncoder_00024EncoderInfo_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_VideoEncoder_00024EncoderInfo,
      &g_org_webrtc_VideoEncoder_00024EncoderInfo_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_VideoEncoder_00024RateControlParameters_clazz(nullptr);
#ifndef org_webrtc_VideoEncoder_00024RateControlParameters_clazz_defined
#define org_webrtc_VideoEncoder_00024RateControlParameters_clazz_defined
inline jclass org_webrtc_VideoEncoder_00024RateControlParameters_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_VideoEncoder_00024RateControlParameters,
      &g_org_webrtc_VideoEncoder_00024RateControlParameters_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_clazz(nullptr);
#ifndef org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_clazz_defined
#define org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_clazz_defined
inline jclass org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env,
      kClassPath_org_webrtc_VideoEncoder_00024ResolutionBitrateLimits,
      &g_org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_VideoEncoder_00024Settings_clazz(nullptr);
#ifndef org_webrtc_VideoEncoder_00024Settings_clazz_defined
#define org_webrtc_VideoEncoder_00024Settings_clazz_defined
inline jclass org_webrtc_VideoEncoder_00024Settings_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_VideoEncoder_00024Settings,
      &g_org_webrtc_VideoEncoder_00024Settings_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_VideoEncoder_createNativeVideoEncoder0(nullptr);
static jlong Java_VideoEncoder_createNativeVideoEncoder(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_VideoEncoder_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoder_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "createNativeVideoEncoder",
          "()J",
          &g_org_webrtc_VideoEncoder_createNativeVideoEncoder0);

  jlong ret =
      env->CallLongMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoder_encode2(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoEncoder_encode(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& frame,
    const jni_zero::JavaRef<jobject>& info) {
  jclass clazz = org_webrtc_VideoEncoder_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoder_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "encode",
"(Lorg/webrtc/VideoFrame;Lorg/webrtc/VideoEncoder$EncodeInfo;)Lorg/webrtc/VideoCodecStatus;",
          &g_org_webrtc_VideoEncoder_encode2);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, frame.obj(), info.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoder_getEncoderInfo0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoEncoder_getEncoderInfo(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_VideoEncoder_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoder_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getEncoderInfo",
          "()Lorg/webrtc/VideoEncoder$EncoderInfo;",
          &g_org_webrtc_VideoEncoder_getEncoderInfo0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoder_getImplementationName0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_VideoEncoder_getImplementationName(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_VideoEncoder_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoder_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getImplementationName",
          "()Ljava/lang/String;",
          &g_org_webrtc_VideoEncoder_getImplementationName0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoder_getResolutionBitrateLimits0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobjectArray>
    Java_VideoEncoder_getResolutionBitrateLimits(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj)
    {
  jclass clazz = org_webrtc_VideoEncoder_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoder_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getResolutionBitrateLimits",
          "()[Lorg/webrtc/VideoEncoder$ResolutionBitrateLimits;",
          &g_org_webrtc_VideoEncoder_getResolutionBitrateLimits0);

  jobjectArray ret =
      static_cast<jobjectArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jobjectArray>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoder_getScalingSettings0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoEncoder_getScalingSettings(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_VideoEncoder_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoder_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getScalingSettings",
          "()Lorg/webrtc/VideoEncoder$ScalingSettings;",
          &g_org_webrtc_VideoEncoder_getScalingSettings0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoder_initEncode2(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoEncoder_initEncode(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& settings,
    const jni_zero::JavaRef<jobject>& encodeCallback) {
  jclass clazz = org_webrtc_VideoEncoder_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoder_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "initEncode",
"(Lorg/webrtc/VideoEncoder$Settings;Lorg/webrtc/VideoEncoder$Callback;)Lorg/webrtc/VideoCodecStatus;",
          &g_org_webrtc_VideoEncoder_initEncode2);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, settings.obj(), encodeCallback.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoder_isHardwareEncoder0(nullptr);
static jboolean Java_VideoEncoder_isHardwareEncoder(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj) {
  jclass clazz = org_webrtc_VideoEncoder_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoder_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "isHardwareEncoder",
          "()Z",
          &g_org_webrtc_VideoEncoder_isHardwareEncoder0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoder_release0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoEncoder_release(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_VideoEncoder_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoder_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "release",
          "()Lorg/webrtc/VideoCodecStatus;",
          &g_org_webrtc_VideoEncoder_release0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoder_setRates1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoEncoder_setRates(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& rcParameters) {
  jclass clazz = org_webrtc_VideoEncoder_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoder_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "setRates",
          "(Lorg/webrtc/VideoEncoder$RateControlParameters;)Lorg/webrtc/VideoCodecStatus;",
          &g_org_webrtc_VideoEncoder_setRates1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, rcParameters.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_VideoEncoder_00024BitrateAllocation_Constructor1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_BitrateAllocation_Constructor(JNIEnv* env, const
    jni_zero::JavaRef<jobjectArray>& bitratesBbs) {
  jclass clazz = org_webrtc_VideoEncoder_00024BitrateAllocation_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_VideoEncoder_00024BitrateAllocation_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "([[I)V",
          &g_org_webrtc_VideoEncoder_00024BitrateAllocation_Constructor1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, bitratesBbs.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoder_00024Capabilities_Constructor1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Capabilities_Constructor(JNIEnv* env, jboolean
    lossNotification) {
  jclass clazz = org_webrtc_VideoEncoder_00024Capabilities_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_VideoEncoder_00024Capabilities_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Z)V",
          &g_org_webrtc_VideoEncoder_00024Capabilities_Constructor1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, lossNotification);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoder_00024EncodeInfo_Constructor1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_EncodeInfo_Constructor(JNIEnv* env, const
    jni_zero::JavaRef<jobjectArray>& frameTypes) {
  jclass clazz = org_webrtc_VideoEncoder_00024EncodeInfo_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_VideoEncoder_00024EncodeInfo_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "([Lorg/webrtc/EncodedImage$FrameType;)V",
          &g_org_webrtc_VideoEncoder_00024EncodeInfo_Constructor1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, frameTypes.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_VideoEncoder_00024EncoderInfo_getApplyAlignmentToAllSimulcastLayers0(nullptr);
static jboolean Java_EncoderInfo_getApplyAlignmentToAllSimulcastLayers(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_VideoEncoder_00024EncoderInfo_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoder_00024EncoderInfo_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getApplyAlignmentToAllSimulcastLayers",
          "()Z",
          &g_org_webrtc_VideoEncoder_00024EncoderInfo_getApplyAlignmentToAllSimulcastLayers0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_VideoEncoder_00024EncoderInfo_getRequestedResolutionAlignment0(nullptr);
static jint Java_EncoderInfo_getRequestedResolutionAlignment(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_VideoEncoder_00024EncoderInfo_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoder_00024EncoderInfo_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getRequestedResolutionAlignment",
          "()I",
          &g_org_webrtc_VideoEncoder_00024EncoderInfo_getRequestedResolutionAlignment0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_VideoEncoder_00024RateControlParameters_Constructor2(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RateControlParameters_Constructor(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& bitrate,
    jdouble framerateFps) {
  jclass clazz = org_webrtc_VideoEncoder_00024RateControlParameters_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_VideoEncoder_00024RateControlParameters_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Lorg/webrtc/VideoEncoder$BitrateAllocation;D)V",
          &g_org_webrtc_VideoEncoder_00024RateControlParameters_Constructor2);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, bitrate.obj(), framerateFps);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_getFrameSizePixels0(nullptr);
static jint Java_ResolutionBitrateLimits_getFrameSizePixels(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getFrameSizePixels",
          "()I",
          &g_org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_getFrameSizePixels0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_getMaxBitrateBps0(nullptr);
static jint Java_ResolutionBitrateLimits_getMaxBitrateBps(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getMaxBitrateBps",
          "()I",
          &g_org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_getMaxBitrateBps0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_getMinBitrateBps0(nullptr);
static jint Java_ResolutionBitrateLimits_getMinBitrateBps(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getMinBitrateBps",
          "()I",
          &g_org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_getMinBitrateBps0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_getMinStartBitrateBps0(nullptr);
static jint Java_ResolutionBitrateLimits_getMinStartBitrateBps(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getMinStartBitrateBps",
          "()I",
          &g_org_webrtc_VideoEncoder_00024ResolutionBitrateLimits_getMinStartBitrateBps0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoder_00024Settings_Constructor8(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Settings_Constructor(JNIEnv* env, JniIntWrapper
    numberOfCores,
    JniIntWrapper width,
    JniIntWrapper height,
    JniIntWrapper startBitrate,
    JniIntWrapper maxFramerate,
    JniIntWrapper numberOfSimulcastStreams,
    jboolean automaticResizeOn,
    const jni_zero::JavaRef<jobject>& capabilities) {
  jclass clazz = org_webrtc_VideoEncoder_00024Settings_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_VideoEncoder_00024Settings_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(IIIIIIZLorg/webrtc/VideoEncoder$Capabilities;)V",
          &g_org_webrtc_VideoEncoder_00024Settings_Constructor8);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, as_jint(numberOfCores), as_jint(width), as_jint(height),
              as_jint(startBitrate), as_jint(maxFramerate), as_jint(numberOfSimulcastStreams),
              automaticResizeOn, capabilities.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_VideoEncoder_JNI
