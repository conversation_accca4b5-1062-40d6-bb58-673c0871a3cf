// Copyright 2017 The BoringSSL Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "../ssl/test/fuzzer.h"


static TLSFuzzer g_fuzzer(TLSFuzzer::kDTLS, TLSFuzzer::kClient,
                          TLSFuzzer::kFuzzerModeOn);

extern "C" int LLVMFuzzerTestOneInput(const uint8_t *buf, size_t len) {
  return g_fuzzer.TestOneInput(buf, len);
}
