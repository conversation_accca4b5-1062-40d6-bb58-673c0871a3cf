// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     java/util/ArrayList

#ifndef java_util_ArrayList_JNI
#define java_util_ArrayList_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_java_util_ArrayList[];
const char kClassPath_java_util_ArrayList[] = "java/util/ArrayList";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_java_util_ArrayList_clazz(nullptr);
#ifndef java_util_ArrayList_clazz_defined
#define java_util_ArrayList_clazz_defined
inline jclass java_util_ArrayList_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_java_util_ArrayList, &g_java_util_ArrayList_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace JNI_ArrayList {


static std::atomic<jmethodID> g_java_util_ArrayList_Constructor0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_Constructor(JNIEnv*
    env);
static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_Constructor(JNIEnv* env) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_ArrayList_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "()V",
          &g_java_util_ArrayList_Constructor0);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_ArrayList_Constructor__Collection1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_ArrayList_Constructor__Collection(JNIEnv* env, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_Constructor__Collection(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_ArrayList_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Ljava/util/Collection;)V",
          &g_java_util_ArrayList_Constructor__Collection1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_ArrayList_Constructor__int1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_ArrayList_Constructor__int(JNIEnv* env, JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_Constructor__int(JNIEnv* env,
    JniIntWrapper p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_ArrayList_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(I)V",
          &g_java_util_ArrayList_Constructor__int1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, as_jint(p0));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_ArrayList_add1(nullptr);
[[maybe_unused]] static jboolean Java_ArrayList_add(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_ArrayList_add(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "add",
          "(Ljava/lang/Object;)Z",
          &g_java_util_ArrayList_add1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_ArrayList_add2(nullptr);
[[maybe_unused]] static void Java_ArrayList_add(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    JniIntWrapper p0,
    const jni_zero::JavaRef<jobject>& p1);
static void Java_ArrayList_add(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "add",
          "(ILjava/lang/Object;)V",
          &g_java_util_ArrayList_add2);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), p1.obj());
}

static std::atomic<jmethodID> g_java_util_ArrayList_addAll2(nullptr);
[[maybe_unused]] static jboolean Java_ArrayList_addAll(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0,
    const jni_zero::JavaRef<jobject>& p1);
static jboolean Java_ArrayList_addAll(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    JniIntWrapper p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "addAll",
          "(ILjava/util/Collection;)Z",
          &g_java_util_ArrayList_addAll2);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), p1.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_ArrayList_addAll1(nullptr);
[[maybe_unused]] static jboolean Java_ArrayList_addAll(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_ArrayList_addAll(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "addAll",
          "(Ljava/util/Collection;)Z",
          &g_java_util_ArrayList_addAll1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_ArrayList_clear0(nullptr);
[[maybe_unused]] static void Java_ArrayList_clear(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static void Java_ArrayList_clear(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "clear",
          "()V",
          &g_java_util_ArrayList_clear0);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID> g_java_util_ArrayList_clone0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_clone(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_clone(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "clone",
          "()Ljava/lang/Object;",
          &g_java_util_ArrayList_clone0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_ArrayList_contains1(nullptr);
[[maybe_unused]] static jboolean Java_ArrayList_contains(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_ArrayList_contains(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "contains",
          "(Ljava/lang/Object;)Z",
          &g_java_util_ArrayList_contains1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_ArrayList_ensureCapacity1(nullptr);
[[maybe_unused]] static void Java_ArrayList_ensureCapacity(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0);
static void Java_ArrayList_ensureCapacity(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    JniIntWrapper p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "ensureCapacity",
          "(I)V",
          &g_java_util_ArrayList_ensureCapacity1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
}

static std::atomic<jmethodID> g_java_util_ArrayList_equals1(nullptr);
[[maybe_unused]] static jboolean Java_ArrayList_equals(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_ArrayList_equals(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "equals",
          "(Ljava/lang/Object;)Z",
          &g_java_util_ArrayList_equals1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_ArrayList_forEach1(nullptr);
[[maybe_unused]] static void Java_ArrayList_forEach(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj, const jni_zero::JavaRef<jobject>& p0);
static void Java_ArrayList_forEach(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "forEach",
          "(Ljava/util/function/Consumer;)V",
          &g_java_util_ArrayList_forEach1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
}

static std::atomic<jmethodID> g_java_util_ArrayList_get1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_get(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_get(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "get",
          "(I)Ljava/lang/Object;",
          &g_java_util_ArrayList_get1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_ArrayList_hashCode0(nullptr);
[[maybe_unused]] static jint Java_ArrayList_hashCode(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jint Java_ArrayList_hashCode(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "hashCode",
          "()I",
          &g_java_util_ArrayList_hashCode0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_util_ArrayList_indexOf1(nullptr);
[[maybe_unused]] static jint Java_ArrayList_indexOf(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj, const jni_zero::JavaRef<jobject>& p0);
static jint Java_ArrayList_indexOf(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "indexOf",
          "(Ljava/lang/Object;)I",
          &g_java_util_ArrayList_indexOf1);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_ArrayList_isEmpty0(nullptr);
[[maybe_unused]] static jboolean Java_ArrayList_isEmpty(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jboolean Java_ArrayList_isEmpty(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "isEmpty",
          "()Z",
          &g_java_util_ArrayList_isEmpty0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_util_ArrayList_iterator0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_iterator(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_iterator(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "iterator",
          "()Ljava/util/Iterator;",
          &g_java_util_ArrayList_iterator0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_ArrayList_lastIndexOf1(nullptr);
[[maybe_unused]] static jint Java_ArrayList_lastIndexOf(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jint Java_ArrayList_lastIndexOf(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "lastIndexOf",
          "(Ljava/lang/Object;)I",
          &g_java_util_ArrayList_lastIndexOf1);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_ArrayList_listIterator0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_listIterator(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_listIterator(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "listIterator",
          "()Ljava/util/ListIterator;",
          &g_java_util_ArrayList_listIterator0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_ArrayList_listIterator1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_listIterator(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_listIterator(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "listIterator",
          "(I)Ljava/util/ListIterator;",
          &g_java_util_ArrayList_listIterator1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_ArrayList_remove__Object1(nullptr);
[[maybe_unused]] static jboolean Java_ArrayList_remove__Object(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_ArrayList_remove__Object(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "remove",
          "(Ljava/lang/Object;)Z",
          &g_java_util_ArrayList_remove__Object1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_ArrayList_remove__int1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_remove__int(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_remove__int(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "remove",
          "(I)Ljava/lang/Object;",
          &g_java_util_ArrayList_remove__int1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_ArrayList_removeAll1(nullptr);
[[maybe_unused]] static jboolean Java_ArrayList_removeAll(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_ArrayList_removeAll(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "removeAll",
          "(Ljava/util/Collection;)Z",
          &g_java_util_ArrayList_removeAll1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_ArrayList_removeIf1(nullptr);
[[maybe_unused]] static jboolean Java_ArrayList_removeIf(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_ArrayList_removeIf(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "removeIf",
          "(Ljava/util/function/Predicate;)Z",
          &g_java_util_ArrayList_removeIf1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_ArrayList_removeRange2(nullptr);
[[maybe_unused]] static void Java_ArrayList_removeRange(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1);
static void Java_ArrayList_removeRange(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "removeRange",
          "(II)V",
          &g_java_util_ArrayList_removeRange2);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), as_jint(p1));
}

static std::atomic<jmethodID> g_java_util_ArrayList_replaceAll1(nullptr);
[[maybe_unused]] static void Java_ArrayList_replaceAll(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static void Java_ArrayList_replaceAll(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "replaceAll",
          "(Ljava/util/function/UnaryOperator;)V",
          &g_java_util_ArrayList_replaceAll1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
}

static std::atomic<jmethodID> g_java_util_ArrayList_retainAll1(nullptr);
[[maybe_unused]] static jboolean Java_ArrayList_retainAll(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_ArrayList_retainAll(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "retainAll",
          "(Ljava/util/Collection;)Z",
          &g_java_util_ArrayList_retainAll1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_ArrayList_set2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_set(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0,
    const jni_zero::JavaRef<jobject>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_set(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "set",
          "(ILjava/lang/Object;)Ljava/lang/Object;",
          &g_java_util_ArrayList_set2);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_ArrayList_size0(nullptr);
[[maybe_unused]] static jint Java_ArrayList_size(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jint Java_ArrayList_size(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "size",
          "()I",
          &g_java_util_ArrayList_size0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_util_ArrayList_sort1(nullptr);
[[maybe_unused]] static void Java_ArrayList_sort(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0);
static void Java_ArrayList_sort(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "sort",
          "(Ljava/util/Comparator;)V",
          &g_java_util_ArrayList_sort1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
}

static std::atomic<jmethodID> g_java_util_ArrayList_spliterator0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_spliterator(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_spliterator(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "spliterator",
          "()Ljava/util/Spliterator;",
          &g_java_util_ArrayList_spliterator0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_ArrayList_subList2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_subList(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_ArrayList_subList(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "subList",
          "(II)Ljava/util/List;",
          &g_java_util_ArrayList_subList2);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_ArrayList_toArray0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobjectArray> Java_ArrayList_toArray(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobjectArray> Java_ArrayList_toArray(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "toArray",
          "()[Ljava/lang/Object;",
          &g_java_util_ArrayList_toArray0);

  jobjectArray ret =
      static_cast<jobjectArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jobjectArray>(env, ret);
}

static std::atomic<jmethodID> g_java_util_ArrayList_toArray1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobjectArray> Java_ArrayList_toArray(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobjectArray>& p0);
static jni_zero::ScopedJavaLocalRef<jobjectArray> Java_ArrayList_toArray(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobjectArray>& p0) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "toArray",
          "([Ljava/lang/Object;)[Ljava/lang/Object;",
          &g_java_util_ArrayList_toArray1);

  jobjectArray ret =
      static_cast<jobjectArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj()));
  return jni_zero::ScopedJavaLocalRef<jobjectArray>(env, ret);
}

static std::atomic<jmethodID> g_java_util_ArrayList_trimToSize0(nullptr);
[[maybe_unused]] static void Java_ArrayList_trimToSize(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static void Java_ArrayList_trimToSize(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_ArrayList_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_ArrayList_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "trimToSize",
          "()V",
          &g_java_util_ArrayList_trimToSize0);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

}  // namespace JNI_ArrayList

#endif  // java_util_ArrayList_JNI
