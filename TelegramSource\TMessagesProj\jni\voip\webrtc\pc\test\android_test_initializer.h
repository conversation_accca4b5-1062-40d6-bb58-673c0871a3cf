/*
 *  Copyright 2015 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef PC_TEST_ANDROID_TEST_INITIALIZER_H_
#define PC_TEST_ANDROID_TEST_INITIALIZER_H_

namespace webrtc {

void InitializeAndroidObjects();

}  // namespace webrtc

#endif  // PC_TEST_ANDROID_TEST_INITIALIZER_H_
