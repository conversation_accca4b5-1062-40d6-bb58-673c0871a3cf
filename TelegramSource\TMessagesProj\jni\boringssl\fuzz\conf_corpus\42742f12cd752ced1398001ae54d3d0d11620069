# [ req ]
distinguished_name    = req_distinguished_name
encrypt_rsa_key               = no

#
# SSLeay example configuration file.
# This is mostly being used for generation of certificate requests.
#
# hacked by iang to do DSA certs - Server

RANDFILE              = ./.rnd

####################################################################
[ req ]
distinguished_name    = req_distinguished_name
encrypt_rsa_key               = no

[ req_distinguished_name ]
countryName                   = Country Name (2 letter code)
countryName_default           = ES
countryName_value             = ES

organizationName                = Organization Name (eg, company)
organizationName_value          = Tortilleras S.A.

0.commonName                  = Common Name (eg, YOUR name)
0.commonName_value            = Torti

1.commonName                  = Common Name (eg, YOUR name)
1.commonName_value            = Gordita

