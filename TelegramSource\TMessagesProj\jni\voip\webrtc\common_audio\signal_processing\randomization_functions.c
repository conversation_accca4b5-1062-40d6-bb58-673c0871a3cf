/*
 *  Copyright (c) 2011 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */


/*
 * This file contains implementations of the randomization functions
 * WebRtcSpl_RandU()
 * WebRtcSpl_RandN()
 * WebRtcSpl_RandUArray()
 *
 * The description header can be found in signal_processing_library.h
 *
 */

#include "common_audio/signal_processing/include/signal_processing_library.h"

static const uint32_t kMaxSeedUsed = 0x80000000;

static const int16_t kRandNTable[] = {
    9178,    -7260,       40,    10189,     4894,    -3531,   -13779,    14764,
   -4008,    -8884,    -8990,     1008,     7368,     5184,     3251,    -5817,
   -9786,     5963,     1770,     8066,    -7135,    10772,    -2298,     1361,
    6484,     2241,    -8633,      792,      199,    -3344,     6553,   -10079,
  -15040,       95,    11608,   -12469,    14161,    -4176,     2476,     6403,
   13685,   -16005,     6646,     2239,    10916,    -3004,     -602,    -3141,
    2142,    14144,    -5829,     5305,     8209,     4713,     2697,    -5112,
   16092,    -1210,    -2891,    -6631,    -5360,   -11878,    -6781,    -2739,
   -6392,      536,    10923,    10872,     5059,    -4748,    -7770,     5477,
      38,    -1025,    -2892,     1638,     6304,    14375,   -11028,     1553,
   -1565,    10762,     -393,     4040,     5257,    12310,     6554,    -4799,
    4899,    -6354,     1603,    -1048,    -2220,     8247,     -186,    -8944,
  -12004,     2332,     4801,    -4933,     6371,      131,     8614,    -5927,
   -8287,   -22760,     4033,   -15162,     3385,     3246,     3153,    -5250,
    3766,      784,     6494,      -62,     3531,    -1582,    15572,      662,
   -3952,     -330,    -3196,      669,     7236,    -2678,    -6569,    23319,
   -8645,     -741,    14830,   -15976,     4903,      315,   -11342,    10311,
    1858,    -7777,     2145,     5436,     5677,     -113,   -10033,      826,
   -1353,    17210,     7768,      986,    -1471,     8291,    -4982,     8207,
  -14911,    -6255,    -2449,   -11881,    -7059,   -11703,    -4338,     8025,
    7538,    -2823,   -12490,     9470,    -1613,    -2529,   -10092,    -7807,
    9480,     6970,   -12844,     5123,     3532,     4816,     4803,    -8455,
   -5045,    14032,    -4378,    -1643,     5756,   -11041,    -2732,   -16618,
   -6430,   -18375,    -3320,     6098,     5131,    -4269,    -8840,     2482,
   -7048,     1547,   -21890,    -6505,    -7414,     -424,   -11722,     7955,
    1653,   -17299,     1823,      473,    -9232,     3337,     1111,      873,
    4018,    -8982,     9889,     3531,   -11763,    -3799,     7373,    -4539,
    3231,     7054,    -8537,     7616,     6244,    16635,      447,    -2915,
   13967,      705,    -2669,    -1520,    -1771,   -16188,     5956,     5117,
    6371,    -9936,    -1448,     2480,     5128,     7550,    -8130,     5236,
    8213,    -6443,     7707,    -1950,   -13811,     7218,     7031,    -3883,
      67,     5731,    -2874,    13480,    -3743,     9298,    -3280,     3552,
   -4425,      -18,    -3785,    -9988,    -5357,     5477,   -11794,     2117,
    1416,    -9935,     3376,      802,    -5079,    -8243,    12652,       66,
    3653,    -2368,     6781,   -21895,    -7227,     2487,     7839,     -385,
    6646,    -7016,    -4658,     5531,    -1705,      834,      129,     3694,
   -1343,     2238,   -22640,    -6417,   -11139,    11301,    -2945,    -3494,
   -5626,      185,    -3615,    -2041,    -7972,    -3106,      -60,   -23497,
   -1566,    17064,     3519,     2518,      304,    -6805,   -10269,     2105,
    1936,     -426,     -736,    -8122,    -1467,     4238,    -6939,   -13309,
     360,     7402,    -7970,    12576,     3287,    12194,    -6289,   -16006,
    9171,     4042,    -9193,     9123,    -2512,     6388,    -4734,    -8739,
    1028,    -5406,    -1696,     5889,     -666,    -4736,     4971,     3565,
    9362,    -6292,     3876,    -3652,   -19666,     7523,    -4061,      391,
  -11773,     7502,    -3763,     4929,    -9478,    13278,     2805,     4496,
    7814,    16419,    12455,   -14773,     2127,    -2746,     3763,     4847,
    3698,     6978,     4751,    -6957,    -3581,      -45,     6252,     1513,
   -4797,    -7925,    11270,    16188,    -2359,    -5269,     9376,   -10777,
    7262,    20031,    -6515,    -2208,    -5353,     8085,    -1341,    -1303,
    7333,     5576,     3625,     5763,    -7931,     9833,    -3371,   -10305,
    6534,   -13539,    -9971,      997,     8464,    -4064,    -1495,     1857,
   13624,     5458,     9490,   -11086,    -4524,    12022,     -550,     -198,
     408,    -8455,    -7068,    10289,     9712,    -3366,     9028,    -7621,
   -5243,     2362,     6909,     4672,    -4933,    -1799,     4709,    -4563,
     -62,     -566,     1624,    -7010,    14730,   -17791,    -3697,    -2344,
   -1741,     7099,    -9509,    -6855,    -1989,     3495,    -2289,     2031,
   12784,      891,    14189,    -3963,    -5683,      421,   -12575,     1724,
  -12682,    -5970,    -8169,     3143,    -1824,    -5488,    -5130,     8536,
   12799,      794,     5738,     3459,   -11689,     -258,    -3738,    -3775,
   -8742,     2333,     8312,    -9383,    10331,    13119,     8398,    10644,
  -19433,    -6446,   -16277,   -11793,    16284,     9345,    15222,    15834,
    2009,    -7349,      130,   -14547,      338,    -5998,     3337,    21492,
    2406,     7703,     -951,    11196,     -564,     3406,     2217,     4806,
    2374,    -5797,    11839,     8940,   -11874,    18213,     2855,    10492
};

static uint32_t IncreaseSeed(uint32_t* seed) {
  seed[0] = (seed[0] * ((int32_t)69069) + 1) & (kMaxSeedUsed - 1);
  return seed[0];
}

int16_t WebRtcSpl_RandU(uint32_t* seed) {
  return (int16_t)(IncreaseSeed(seed) >> 16);
}

int16_t WebRtcSpl_RandN(uint32_t* seed) {
  return kRandNTable[IncreaseSeed(seed) >> 23];
}

// Creates an array of uniformly distributed variables.
int16_t WebRtcSpl_RandUArray(int16_t* vector,
                             int16_t vector_length,
                             uint32_t* seed) {
  int i;
  for (i = 0; i < vector_length; i++) {
    vector[i] = WebRtcSpl_RandU(seed);
  }
  return vector_length;
}
