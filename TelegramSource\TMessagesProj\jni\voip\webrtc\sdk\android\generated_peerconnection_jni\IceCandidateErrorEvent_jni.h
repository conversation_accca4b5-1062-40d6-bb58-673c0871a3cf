// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/IceCandidateErrorEvent

#ifndef org_webrtc_IceCandidateErrorEvent_JNI
#define org_webrtc_IceCandidateErrorEvent_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_IceCandidateErrorEvent[];
const char kClassPath_org_webrtc_IceCandidateErrorEvent[] = "org/webrtc/IceCandidateErrorEvent";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_IceCandidateErrorEvent_clazz(nullptr);
#ifndef org_webrtc_IceCandidateErrorEvent_clazz_defined
#define org_webrtc_IceCandidateErrorEvent_clazz_defined
inline jclass org_webrtc_IceCandidateErrorEvent_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_IceCandidateErrorEvent,
      &g_org_webrtc_IceCandidateErrorEvent_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_IceCandidateErrorEvent_Constructor5(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_IceCandidateErrorEvent_Constructor(JNIEnv* env,
    const jni_zero::JavaRef<jstring>& address,
    JniIntWrapper port,
    const jni_zero::JavaRef<jstring>& url,
    JniIntWrapper errorCode,
    const jni_zero::JavaRef<jstring>& errorText) {
  jclass clazz = org_webrtc_IceCandidateErrorEvent_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_IceCandidateErrorEvent_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Ljava/lang/String;ILjava/lang/String;ILjava/lang/String;)V",
          &g_org_webrtc_IceCandidateErrorEvent_Constructor5);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, address.obj(), as_jint(port), url.obj(), as_jint(errorCode),
              errorText.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_IceCandidateErrorEvent_JNI
