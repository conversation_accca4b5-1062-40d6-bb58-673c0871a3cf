/*
 *  Copyright (c) 2013 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef MODULES_DESKTOP_CAPTURE_WIN_CURSOR_UNITTEST_RESOURCES_H_
#define MODULES_DESKTOP_CAPTURE_WIN_CURSOR_UNITTEST_RESOURCES_H_

#define IDD_CURSOR1_24BPP 101
#define IDD_CURSOR1_32BPP 102
#define IDD_CURSOR1_8BPP 103

#define IDD_CURSOR2_1BPP 104
#define IDD_CURSOR2_32BPP 105

#define IDD_CURSOR3_4BPP 106
#define IDD_CURSOR3_32BPP 107

#endif  // MODULES_DESKTOP_CAPTURE_WIN_CURSOR_UNITTEST_RESOURCES_H_
