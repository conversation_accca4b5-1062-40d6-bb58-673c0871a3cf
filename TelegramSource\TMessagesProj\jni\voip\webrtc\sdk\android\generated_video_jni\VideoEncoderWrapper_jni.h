// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/VideoEncoderWrapper

#ifndef org_webrtc_VideoEncoderWrapper_JNI
#define org_webrtc_VideoEncoderWrapper_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_VideoEncoderWrapper[];
const char kClassPath_org_webrtc_VideoEncoderWrapper[] = "org/webrtc/VideoEncoderWrapper";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_VideoEncoderWrapper_clazz(nullptr);
#ifndef org_webrtc_VideoEncoderWrapper_clazz_defined
#define org_webrtc_VideoEncoderWrapper_clazz_defined
inline jclass org_webrtc_VideoEncoderWrapper_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_VideoEncoderWrapper,
      &g_org_webrtc_VideoEncoderWrapper_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {

JNI_BOUNDARY_EXPORT void Java_org_webrtc_VideoEncoderWrapper_nativeOnEncodedFrame(
    JNIEnv* env,
    jclass jcaller,
    jlong nativeVideoEncoderWrapper,
    jobject frame) {
  VideoEncoderWrapper* native = reinterpret_cast<VideoEncoderWrapper*>(nativeVideoEncoderWrapper);
  CHECK_NATIVE_PTR(env, jcaller, native, "OnEncodedFrame");
  return native->OnEncodedFrame(env, jni_zero::JavaParamRef<jobject>(env, frame));
}


static std::atomic<jmethodID> g_org_webrtc_VideoEncoderWrapper_createEncoderCallback1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoEncoderWrapper_createEncoderCallback(JNIEnv*
    env, jlong nativeEncoder) {
  jclass clazz = org_webrtc_VideoEncoderWrapper_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_VideoEncoderWrapper_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "createEncoderCallback",
          "(J)Lorg/webrtc/VideoEncoder$Callback;",
          &g_org_webrtc_VideoEncoderWrapper_createEncoderCallback1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, nativeEncoder);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoderWrapper_getScalingSettingsHigh1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoEncoderWrapper_getScalingSettingsHigh(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& scalingSettings) {
  jclass clazz = org_webrtc_VideoEncoderWrapper_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_VideoEncoderWrapper_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getScalingSettingsHigh",
          "(Lorg/webrtc/VideoEncoder$ScalingSettings;)Ljava/lang/Integer;",
          &g_org_webrtc_VideoEncoderWrapper_getScalingSettingsHigh1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, scalingSettings.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoderWrapper_getScalingSettingsLow1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoEncoderWrapper_getScalingSettingsLow(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& scalingSettings) {
  jclass clazz = org_webrtc_VideoEncoderWrapper_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_VideoEncoderWrapper_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getScalingSettingsLow",
          "(Lorg/webrtc/VideoEncoder$ScalingSettings;)Ljava/lang/Integer;",
          &g_org_webrtc_VideoEncoderWrapper_getScalingSettingsLow1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, scalingSettings.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoderWrapper_getScalingSettingsOn1(nullptr);
static jboolean Java_VideoEncoderWrapper_getScalingSettingsOn(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& scalingSettings) {
  jclass clazz = org_webrtc_VideoEncoderWrapper_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_VideoEncoderWrapper_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getScalingSettingsOn",
          "(Lorg/webrtc/VideoEncoder$ScalingSettings;)Z",
          &g_org_webrtc_VideoEncoderWrapper_getScalingSettingsOn1);

  jboolean ret =
      env->CallStaticBooleanMethod(clazz,
          call_context.base.method_id, scalingSettings.obj());
  return ret;
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_VideoEncoderWrapper_JNI
