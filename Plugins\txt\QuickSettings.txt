import traceback
import threading
import plugins_manager
import os
import re
from typing import Optional, List

from base_plugin import BasePlugin, MenuItemData, MenuItemType, XposedHook
from android_utils import log, run_on_ui_thread
from client_utils import get_last_fragment
from com.exteragram.messenger.plugins import PluginsController
from com.exteragram.messenger.plugins.ui import PluginSettingsActivity, PluginsActivity
from ui.alert import AlertDialogBuilder
from ui.bulletin import BulletinHelper
from ui.settings import Header, Selector

from java.util import Locale
from java.lang import String, Boolean
from org.telegram.messenger import Utilities

__id__ = "quick_settings_menu"
__name__ = "QuickSettings"
__description__ = "Добавляет меню для быстрого доступа к настройкам других плагинов."
__author__ = "@bokudzhava"
__version__ = "1.2.0"
__min_version__ = "11.12.0"
__icon__ = "AnimatedEmojies/409"

class Locales:
    default = {
        "HEADER_MENU_LOCATION": "Menu Location",
        "SELECTOR_SHOW_MENU_IN": "Show menu in:",
        "SELECTOR_ITEMS_MENU_LOCATION": ["Chat menu", "Drawer menu", "Everywhere"],
        "TITLE_PLUGIN_SETTINGS": "Plugin Settings",
        "DIALOG_CHOOSE_PLUGIN": "Choose a plugin",
        "INFO_NO_PLUGINS_WITH_SETTINGS": "No active plugins with settings found",
        "HEADER_PLUGINS_ACTIVITY_LOCATION": "'Plugins' Button Location",
        "SELECTOR_SHOW_PLUGINS_ACTIVITY_IN": "Show 'exteraGram > Plugins' in:",
        "SELECTOR_ITEMS_PLUGINS_ACTIVITY_LOCATION": ["Disabled", "Chat menu", "Drawer menu", "Everywhere"]
    }
    ru = {
        "HEADER_MENU_LOCATION": "Расположение меню",
        "SELECTOR_SHOW_MENU_IN": "Показывать меню в:",
        "SELECTOR_ITEMS_MENU_LOCATION": ["Меню чата", "Боковое меню", "Везде"],
        "TITLE_PLUGIN_SETTINGS": "Настройки плагинов",
        "DIALOG_CHOOSE_PLUGIN": "Выберите плагин",
        "INFO_NO_PLUGINS_WITH_SETTINGS": "Нет активных плагинов с настройками",
        "HEADER_PLUGINS_ACTIVITY_LOCATION": "Расположение кнопки 'Plugins'",
        "SELECTOR_SHOW_PLUGINS_ACTIVITY_IN": "Показывать кнопку 'Plugins' в:",
        "SELECTOR_ITEMS_PLUGINS_ACTIVITY_LOCATION": ["Отключено", "Меню чата", "Боковое меню", "Везде"]
    }
    uk = {
        "HEADER_MENU_LOCATION": "Розташування меню",
        "SELECTOR_SHOW_MENU_IN": "Показувати меню в:",
        "SELECTOR_ITEMS_MENU_LOCATION": ["Меню чату", "Бічне меню", "Скрізь"],
        "TITLE_PLUGIN_SETTINGS": "Налаштування плагінів",
        "DIALOG_CHOOSE_PLUGIN": "Виберіть плагін",
        "INFO_NO_PLUGINS_WITH_SETTINGS": "Немає активних плагінів з налаштуваннями",
        "HEADER_PLUGINS_ACTIVITY_LOCATION": "Розташування кнопки 'Plugins'",
        "SELECTOR_SHOW_PLUGINS_ACTIVITY_IN": "Показувати кнопку 'Plugins' в:",
        "SELECTOR_ITEMS_PLUGINS_ACTIVITY_LOCATION": ["Вимкнено", "Меню чату", "Бічне меню", "Скрізь"]
    }
    en = default

def localise(key: str) -> str:
    locale = Locale.getDefault().getLanguage()
    locale_dict = getattr(Locales, locale, Locales.default)
    return locale_dict.get(key, Locales.default.get(key, key))

MENU_LOCATION_CHAT = 0
MENU_LOCATION_DRAWER = 1
MENU_LOCATION_BOTH = 2

PLUGINS_ACTIVITY_LOCATION_DISABLED = 0
PLUGINS_ACTIVITY_LOCATION_CHAT = 1
PLUGINS_ACTIVITY_LOCATION_DRAWER = 2
PLUGINS_ACTIVITY_LOCATION_BOTH = 3

class PluginStateChangeHook(XposedHook):
    def __init__(self, plugin_instance):
        super().__init__()
        self.plugin = plugin_instance

    def after_hooked_method(self, param):
        method_name = param.method.getName()
        log(f"[{self.plugin.id}] Plugin state change detected via '{method_name}'. Scheduling menu update.")
        self.plugin._schedule_menu_update()

class QuickSettingsPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.chat_menu_items: List[MenuItemData] = []
        self.drawer_menu_item: Optional[MenuItemData] = None
        self.hooks: List[object] = []
        self.update_timer: Optional[threading.Timer] = None
        self.drawer_plugins_activity_item: Optional[MenuItemData] = None
        self.chat_plugins_activity_item: Optional[MenuItemData] = None

    def create_settings(self):
        return [
            Header(text=localise("HEADER_MENU_LOCATION")),
            Selector(
                key="menu_location",
                text=localise("SELECTOR_SHOW_MENU_IN"),
                items=localise("SELECTOR_ITEMS_MENU_LOCATION"),
                default=MENU_LOCATION_BOTH,
                on_change=lambda _: self._schedule_menu_update(delay=0)
            ),
            Header(text=localise("HEADER_PLUGINS_ACTIVITY_LOCATION")),
            Selector(
                key="plugins_activity_location",
                text=localise("SELECTOR_SHOW_PLUGINS_ACTIVITY_IN"),
                items=localise("SELECTOR_ITEMS_PLUGINS_ACTIVITY_LOCATION"),
                default=PLUGINS_ACTIVITY_LOCATION_BOTH,
                on_change=lambda _: self._schedule_menu_update(delay=0)
            )
        ]

    def on_plugin_load(self):
        self._update_menu_items()
        self._apply_hooks()
        log(f"[{__id__}] Plugin loaded.")

    def on_plugin_unload(self):
        self._remove_menu_items()
        self._remove_hooks()
        if self.update_timer:
            self.update_timer.cancel()
        log(f"[{__id__}] Plugin unloaded.")

    def _apply_hooks(self):
        if self.hooks: return
        try:
            controller_class = PluginsController.getClass()
            hook_handler = PluginStateChangeHook(self)
            
            methods_to_hook = [
                ("loadPluginFromFile", String, Utilities.Callback),
                ("setPluginEnabled", String, Boolean.TYPE, Utilities.Callback)
            ]

            for method_info in methods_to_hook:
                method_name, *arg_types = method_info
                target_method = controller_class.getDeclaredMethod(method_name, *arg_types)
                hook = self.hook_method(target_method, hook_handler)
                self.hooks.append(hook)
                log(f"[{__id__}] Successfully hooked PluginsController.{method_name}.")

        except Exception as e:
            log(f"[{__id__}] Failed to apply hooks: {e}\n{traceback.format_exc()}")

    def _remove_hooks(self):
        for hook in self.hooks:
            try: self.unhook_method(hook)
            except Exception as e: log(f"[{__id__}] Error unhooking: {e}")
        self.hooks.clear()
        log(f"[{__id__}] All hooks removed.")

    def _schedule_menu_update(self, delay: float = 0.5):
        if self.update_timer:
            self.update_timer.cancel()
        
        self.update_timer = threading.Timer(delay, self._update_menu_items)
        self.update_timer.start()
        log(f"[{__id__}] Scheduled menu update in {delay} seconds.")

    def _get_id_from_file_content(self, content: str) -> Optional[str]:
        match = re.search(r"__id__\s*=\s*['\"]([^'\"]+)['\"]", content)
        return match.group(1) if match else None

    def _get_active_plugins_with_settings(self) -> List[any]:
        plugins_to_show = []
        try:
            loaded_plugins_map = {p.id: p for p in plugins_manager.PluginsManager._plugins.values()}
            plugins_dir = plugins_manager.PluginsManager._plugins_dir

            if not plugins_dir:
                log(f"[{__id__}] Plugins directory not found.")
                return []

            for filename in os.listdir(plugins_dir):
                if not filename.endswith(('.py', '.plugin')):
                    continue

                file_path = os.path.join(plugins_dir, filename)
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        content = f.read()

                    if "def create_settings(self)" not in content:
                        continue

                    plugin_id = self._get_id_from_file_content(content)
                    if not plugin_id or plugin_id not in loaded_plugins_map:
                        continue
                    
                    plugin_object = loaded_plugins_map[plugin_id]

                    if plugin_object.id != __id__ and plugin_object.enabled:
                        plugins_to_show.append(plugin_object)

                except Exception as e:
                    log(f"[{__id__}] Error processing file {filename}: {e}")
                    continue
        
        except Exception as e:
            log(f"[{__id__}] General error getting active plugins: {e}\n{traceback.format_exc()}")

        return sorted(plugins_to_show, key=lambda p: p.name)

    def _update_menu_items(self):
        def action():
            self._remove_menu_items()
            
            location = self.get_setting("menu_location", MENU_LOCATION_BOTH)
            active_plugins = self._get_active_plugins_with_settings()

            if location in [MENU_LOCATION_CHAT, MENU_LOCATION_BOTH]:
                if active_plugins:
                    for plugin in active_plugins:
                        item = self.add_menu_item(MenuItemData(
                            menu_type=MenuItemType.CHAT_ACTION_MENU,
                            text=plugin.name,
                            icon="msg_settings_14",
                            priority=90,
                            on_click=lambda ctx, p_id=plugin.id: self._open_specific_settings(p_id)
                        ))
                        self.chat_menu_items.append(item)
                    log(f"[{__id__}] Added {len(self.chat_menu_items)} individual buttons to chat menu.")

            if location in [MENU_LOCATION_DRAWER, MENU_LOCATION_BOTH]:
                self.drawer_menu_item = self.add_menu_item(MenuItemData(
                    menu_type=MenuItemType.DRAWER_MENU,
                    text=localise("TITLE_PLUGIN_SETTINGS"),
                    icon="msg_reorder",
                    priority=90,
                    on_click=lambda ctx: self._show_plugin_list_dialog()
                ))
                log(f"[{__id__}] Drawer dialog button created.")

            plugins_activity_location = self.get_setting("plugins_activity_location", PLUGINS_ACTIVITY_LOCATION_BOTH)

            if plugins_activity_location in [PLUGINS_ACTIVITY_LOCATION_DRAWER, PLUGINS_ACTIVITY_LOCATION_BOTH]:
                self.drawer_plugins_activity_item = self.add_menu_item(MenuItemData(
                    menu_type=MenuItemType.DRAWER_MENU,
                    text="exteraGram > Plugins",
                    icon="msg_link",
                    priority=91,
                    on_click=lambda ctx: self._open_plugins_activity()
                ))
                log(f"[{__id__}] Added 'exteraGram > Plugins' to drawer menu.")

            if plugins_activity_location in [PLUGINS_ACTIVITY_LOCATION_CHAT, PLUGINS_ACTIVITY_LOCATION_BOTH]:
                self.chat_plugins_activity_item = self.add_menu_item(MenuItemData(
                    menu_type=MenuItemType.CHAT_ACTION_MENU,
                    text="exteraGram > Plugins",
                    icon="msg_link",
                    priority=91,
                    on_click=lambda ctx: self._open_plugins_activity()
                ))
                log(f"[{__id__}] Added 'exteraGram > Plugins' to chat menu.")

        run_on_ui_thread(action)

    def _remove_menu_items(self):
        for item in self.chat_menu_items:
            self.remove_menu_item(item)
        self.chat_menu_items.clear()

        if self.drawer_menu_item:
            self.remove_menu_item(self.drawer_menu_item)
            self.drawer_menu_item = None
        
        if self.drawer_plugins_activity_item:
            self.remove_menu_item(self.drawer_plugins_activity_item)
            self.drawer_plugins_activity_item = None
        
        if self.chat_plugins_activity_item:
            self.remove_menu_item(self.chat_plugins_activity_item)
            self.chat_plugins_activity_item = None

    def _show_plugin_list_dialog(self):
        def action():
            try:
                active_plugins = self._get_active_plugins_with_settings()
                
                if not active_plugins:
                    BulletinHelper.show_info(localise("INFO_NO_PLUGINS_WITH_SETTINGS"))
                    return

                plugin_names = [p.name for p in active_plugins]

                def on_item_click(dialog, which):
                    selected_plugin = active_plugins[which]
                    self._open_specific_settings(selected_plugin.id)
                    dialog.dismiss()

                builder = AlertDialogBuilder(get_last_fragment().getContext())
                builder.set_title(localise("DIALOG_CHOOSE_PLUGIN"))
                builder.set_items(plugin_names, on_item_click)
                builder.show()

            except Exception as e:
                log(f"[{__id__}] Error showing plugin list dialog: {e}\n{traceback.format_exc()}")
                BulletinHelper.show_error("QuickSettings: Error building list")

        run_on_ui_thread(action)

    def _open_specific_settings(self, plugin_id: str):
        def action():
            try:
                java_plugin = PluginsController.getInstance().plugins.get(plugin_id)
                if java_plugin:
                    get_last_fragment().presentFragment(PluginSettingsActivity(java_plugin))
                else:
                    log(f"[{__id__}] Could not find Java plugin object for ID: {plugin_id}")
            except Exception as e:
                log(f"[{__id__}] Error opening specific plugin settings for {plugin_id}: {e}\n{traceback.format_exc()}")
        
        run_on_ui_thread(action)

    def _open_plugins_activity(self):
        def action():
            try:
                get_last_fragment().presentFragment(PluginsActivity())
            except Exception as e:
                log(f"[{__id__}] Error opening PluginsActivity: {e}\n{traceback.format_exc()}")
        run_on_ui_thread(action)