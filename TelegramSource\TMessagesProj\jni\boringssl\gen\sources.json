{"bcm": {"srcs": ["crypto/fipsmodule/bcm.cc"], "internal_hdrs": ["crypto/fipsmodule/aes/aes.cc.inc", "crypto/fipsmodule/aes/aes_nohw.cc.inc", "crypto/fipsmodule/aes/cbc.cc.inc", "crypto/fipsmodule/aes/cfb.cc.inc", "crypto/fipsmodule/aes/ctr.cc.inc", "crypto/fipsmodule/aes/gcm.cc.inc", "crypto/fipsmodule/aes/gcm_nohw.cc.inc", "crypto/fipsmodule/aes/key_wrap.cc.inc", "crypto/fipsmodule/aes/mode_wrappers.cc.inc", "crypto/fipsmodule/aes/ofb.cc.inc", "crypto/fipsmodule/bn/add.cc.inc", "crypto/fipsmodule/bn/asm/x86_64-gcc.cc.inc", "crypto/fipsmodule/bn/bn.cc.inc", "crypto/fipsmodule/bn/bytes.cc.inc", "crypto/fipsmodule/bn/cmp.cc.inc", "crypto/fipsmodule/bn/ctx.cc.inc", "crypto/fipsmodule/bn/div.cc.inc", "crypto/fipsmodule/bn/div_extra.cc.inc", "crypto/fipsmodule/bn/exponentiation.cc.inc", "crypto/fipsmodule/bn/gcd.cc.inc", "crypto/fipsmodule/bn/gcd_extra.cc.inc", "crypto/fipsmodule/bn/generic.cc.inc", "crypto/fipsmodule/bn/jacobi.cc.inc", "crypto/fipsmodule/bn/montgomery.cc.inc", "crypto/fipsmodule/bn/montgomery_inv.cc.inc", "crypto/fipsmodule/bn/mul.cc.inc", "crypto/fipsmodule/bn/prime.cc.inc", "crypto/fipsmodule/bn/random.cc.inc", "crypto/fipsmodule/bn/rsaz_exp.cc.inc", "crypto/fipsmodule/bn/shift.cc.inc", "crypto/fipsmodule/bn/sqrt.cc.inc", "crypto/fipsmodule/cipher/aead.cc.inc", "crypto/fipsmodule/cipher/cipher.cc.inc", "crypto/fipsmodule/cipher/e_aes.cc.inc", "crypto/fipsmodule/cipher/e_aesccm.cc.inc", "crypto/fipsmodule/cmac/cmac.cc.inc", "crypto/fipsmodule/dh/check.cc.inc", "crypto/fipsmodule/dh/dh.cc.inc", "crypto/fipsmodule/digest/digest.cc.inc", "crypto/fipsmodule/digest/digests.cc.inc", "crypto/fipsmodule/digestsign/digestsign.cc.inc", "crypto/fipsmodule/ec/ec.cc.inc", "crypto/fipsmodule/ec/ec_key.cc.inc", "crypto/fipsmodule/ec/ec_montgomery.cc.inc", "crypto/fipsmodule/ec/felem.cc.inc", "crypto/fipsmodule/ec/oct.cc.inc", "crypto/fipsmodule/ec/p224-64.cc.inc", "crypto/fipsmodule/ec/p256-nistz.cc.inc", "crypto/fipsmodule/ec/p256.cc.inc", "crypto/fipsmodule/ec/scalar.cc.inc", "crypto/fipsmodule/ec/simple.cc.inc", "crypto/fipsmodule/ec/simple_mul.cc.inc", "crypto/fipsmodule/ec/util.cc.inc", "crypto/fipsmodule/ec/wnaf.cc.inc", "crypto/fipsmodule/ecdh/ecdh.cc.inc", "crypto/fipsmodule/ecdsa/ecdsa.cc.inc", "crypto/fipsmodule/hkdf/hkdf.cc.inc", "crypto/fipsmodule/hmac/hmac.cc.inc", "crypto/fipsmodule/keccak/keccak.cc.inc", "crypto/fipsmodule/mldsa/fips_known_values.inc", "crypto/fipsmodule/mldsa/mldsa.cc.inc", "crypto/fipsmodule/mlkem/fips_known_values.inc", "crypto/fipsmodule/mlkem/mlkem.cc.inc", "crypto/fipsmodule/rand/ctrdrbg.cc.inc", "crypto/fipsmodule/rand/rand.cc.inc", "crypto/fipsmodule/rsa/blinding.cc.inc", "crypto/fipsmodule/rsa/padding.cc.inc", "crypto/fipsmodule/rsa/rsa.cc.inc", "crypto/fipsmodule/rsa/rsa_impl.cc.inc", "crypto/fipsmodule/self_check/fips.cc.inc", "crypto/fipsmodule/self_check/self_check.cc.inc", "crypto/fipsmodule/service_indicator/service_indicator.cc.inc", "crypto/fipsmodule/sha/sha1.cc.inc", "crypto/fipsmodule/sha/sha256.cc.inc", "crypto/fipsmodule/sha/sha512.cc.inc", "crypto/fipsmodule/slhdsa/fips_known_values.inc", "crypto/fipsmodule/slhdsa/fors.cc.inc", "crypto/fipsmodule/slhdsa/merkle.cc.inc", "crypto/fipsmodule/slhdsa/slhdsa.cc.inc", "crypto/fipsmodule/slhdsa/thash.cc.inc", "crypto/fipsmodule/slhdsa/wots.cc.inc", "crypto/fipsmodule/tls/kdf.cc.inc"], "asm": ["gen/bcm/aes-gcm-avx2-x86_64-apple.S", "gen/bcm/aes-gcm-avx2-x86_64-linux.S", "gen/bcm/aes-gcm-avx512-x86_64-apple.S", "gen/bcm/aes-gcm-avx512-x86_64-linux.S", "gen/bcm/aesni-gcm-x86_64-apple.S", "gen/bcm/aesni-gcm-x86_64-linux.S", "gen/bcm/aesni-x86-apple.S", "gen/bcm/aesni-x86-linux.S", "gen/bcm/aesni-x86_64-apple.S", "gen/bcm/aesni-x86_64-linux.S", "gen/bcm/aesv8-armv7-linux.S", "gen/bcm/aesv8-armv8-apple.S", "gen/bcm/aesv8-armv8-linux.S", "gen/bcm/aesv8-armv8-win.S", "gen/bcm/aesv8-gcm-armv8-apple.S", "gen/bcm/aesv8-gcm-armv8-linux.S", "gen/bcm/aesv8-gcm-armv8-win.S", "gen/bcm/armv4-mont-linux.S", "gen/bcm/armv8-mont-apple.S", "gen/bcm/armv8-mont-linux.S", "gen/bcm/armv8-mont-win.S", "gen/bcm/bn-586-apple.S", "gen/bcm/bn-586-linux.S", "gen/bcm/bn-armv8-apple.S", "gen/bcm/bn-armv8-linux.S", "gen/bcm/bn-armv8-win.S", "gen/bcm/bsaes-armv7-linux.S", "gen/bcm/co-586-apple.S", "gen/bcm/co-586-linux.S", "gen/bcm/ghash-armv4-linux.S", "gen/bcm/ghash-neon-armv8-apple.S", "gen/bcm/ghash-neon-armv8-linux.S", "gen/bcm/ghash-neon-armv8-win.S", "gen/bcm/ghash-ssse3-x86-apple.S", "gen/bcm/ghash-ssse3-x86-linux.S", "gen/bcm/ghash-ssse3-x86_64-apple.S", "gen/bcm/ghash-ssse3-x86_64-linux.S", "gen/bcm/ghash-x86-apple.S", "gen/bcm/ghash-x86-linux.S", "gen/bcm/ghash-x86_64-apple.S", "gen/bcm/ghash-x86_64-linux.S", "gen/bcm/ghashv8-armv7-linux.S", "gen/bcm/ghashv8-armv8-apple.S", "gen/bcm/ghashv8-armv8-linux.S", "gen/bcm/ghashv8-armv8-win.S", "gen/bcm/p256-armv8-asm-apple.S", "gen/bcm/p256-armv8-asm-linux.S", "gen/bcm/p256-armv8-asm-win.S", "gen/bcm/p256-x86_64-asm-apple.S", "gen/bcm/p256-x86_64-asm-linux.S", "gen/bcm/p256_beeu-armv8-asm-apple.S", "gen/bcm/p256_beeu-armv8-asm-linux.S", "gen/bcm/p256_beeu-armv8-asm-win.S", "gen/bcm/p256_beeu-x86_64-asm-apple.S", "gen/bcm/p256_beeu-x86_64-asm-linux.S", "gen/bcm/rdrand-x86_64-apple.S", "gen/bcm/rdrand-x86_64-linux.S", "gen/bcm/rsaz-avx2-apple.S", "gen/bcm/rsaz-avx2-linux.S", "gen/bcm/sha1-586-apple.S", "gen/bcm/sha1-586-linux.S", "gen/bcm/sha1-armv4-large-linux.S", "gen/bcm/sha1-armv8-apple.S", "gen/bcm/sha1-armv8-linux.S", "gen/bcm/sha1-armv8-win.S", "gen/bcm/sha1-x86_64-apple.S", "gen/bcm/sha1-x86_64-linux.S", "gen/bcm/sha256-586-apple.S", "gen/bcm/sha256-586-linux.S", "gen/bcm/sha256-armv4-linux.S", "gen/bcm/sha256-armv8-apple.S", "gen/bcm/sha256-armv8-linux.S", "gen/bcm/sha256-armv8-win.S", "gen/bcm/sha256-x86_64-apple.S", "gen/bcm/sha256-x86_64-linux.S", "gen/bcm/sha512-586-apple.S", "gen/bcm/sha512-586-linux.S", "gen/bcm/sha512-armv4-linux.S", "gen/bcm/sha512-armv8-apple.S", "gen/bcm/sha512-armv8-linux.S", "gen/bcm/sha512-armv8-win.S", "gen/bcm/sha512-x86_64-apple.S", "gen/bcm/sha512-x86_64-linux.S", "gen/bcm/vpaes-armv7-linux.S", "gen/bcm/vpaes-armv8-apple.S", "gen/bcm/vpaes-armv8-linux.S", "gen/bcm/vpaes-armv8-win.S", "gen/bcm/vpaes-x86-apple.S", "gen/bcm/vpaes-x86-linux.S", "gen/bcm/vpaes-x86_64-apple.S", "gen/bcm/vpaes-x86_64-linux.S", "gen/bcm/x86-mont-apple.S", "gen/bcm/x86-mont-linux.S", "gen/bcm/x86_64-mont-apple.S", "gen/bcm/x86_64-mont-linux.S", "gen/bcm/x86_64-mont5-apple.S", "gen/bcm/x86_64-mont5-linux.S", "third_party/fiat/asm/fiat_p256_adx_mul.S", "third_party/fiat/asm/fiat_p256_adx_sqr.S"], "nasm": ["gen/bcm/aes-gcm-avx2-x86_64-win.asm", "gen/bcm/aes-gcm-avx512-x86_64-win.asm", "gen/bcm/aesni-gcm-x86_64-win.asm", "gen/bcm/aesni-x86-win.asm", "gen/bcm/aesni-x86_64-win.asm", "gen/bcm/bn-586-win.asm", "gen/bcm/co-586-win.asm", "gen/bcm/ghash-ssse3-x86-win.asm", "gen/bcm/ghash-ssse3-x86_64-win.asm", "gen/bcm/ghash-x86-win.asm", "gen/bcm/ghash-x86_64-win.asm", "gen/bcm/p256-x86_64-asm-win.asm", "gen/bcm/p256_beeu-x86_64-asm-win.asm", "gen/bcm/rdrand-x86_64-win.asm", "gen/bcm/rsaz-avx2-win.asm", "gen/bcm/sha1-586-win.asm", "gen/bcm/sha1-x86_64-win.asm", "gen/bcm/sha256-586-win.asm", "gen/bcm/sha256-x86_64-win.asm", "gen/bcm/sha512-586-win.asm", "gen/bcm/sha512-x86_64-win.asm", "gen/bcm/vpaes-x86-win.asm", "gen/bcm/vpaes-x86_64-win.asm", "gen/bcm/x86-mont-win.asm", "gen/bcm/x86_64-mont-win.asm", "gen/bcm/x86_64-mont5-win.asm"]}, "bssl": {"srcs": ["tool/args.cc", "tool/ciphers.cc", "tool/client.cc", "tool/const.cc", "tool/digest.cc", "tool/fd.cc", "tool/file.cc", "tool/generate_ech.cc", "tool/generate_ed25519.cc", "tool/genrsa.cc", "tool/pkcs12.cc", "tool/rand.cc", "tool/server.cc", "tool/sign.cc", "tool/speed.cc", "tool/tool.cc", "tool/transport_common.cc"], "internal_hdrs": ["tool/internal.h", "tool/transport_common.h"]}, "crypto": {"srcs": ["crypto/aes/aes.cc", "crypto/asn1/a_bitstr.cc", "crypto/asn1/a_bool.cc", "crypto/asn1/a_d2i_fp.cc", "crypto/asn1/a_dup.cc", "crypto/asn1/a_gentm.cc", "crypto/asn1/a_i2d_fp.cc", "crypto/asn1/a_int.cc", "crypto/asn1/a_mbstr.cc", "crypto/asn1/a_object.cc", "crypto/asn1/a_octet.cc", "crypto/asn1/a_strex.cc", "crypto/asn1/a_strnid.cc", "crypto/asn1/a_time.cc", "crypto/asn1/a_type.cc", "crypto/asn1/a_utctm.cc", "crypto/asn1/asn1_lib.cc", "crypto/asn1/asn1_par.cc", "crypto/asn1/asn_pack.cc", "crypto/asn1/f_int.cc", "crypto/asn1/f_string.cc", "crypto/asn1/posix_time.cc", "crypto/asn1/tasn_dec.cc", "crypto/asn1/tasn_enc.cc", "crypto/asn1/tasn_fre.cc", "crypto/asn1/tasn_new.cc", "crypto/asn1/tasn_typ.cc", "crypto/asn1/tasn_utl.cc", "crypto/base64/base64.cc", "crypto/bio/bio.cc", "crypto/bio/bio_mem.cc", "crypto/bio/connect.cc", "crypto/bio/errno.cc", "crypto/bio/fd.cc", "crypto/bio/file.cc", "crypto/bio/hexdump.cc", "crypto/bio/pair.cc", "crypto/bio/printf.cc", "crypto/bio/socket.cc", "crypto/bio/socket_helper.cc", "crypto/blake2/blake2.cc", "crypto/bn/bn_asn1.cc", "crypto/bn/convert.cc", "crypto/bn/exponentiation.cc", "crypto/buf/buf.cc", "crypto/bytestring/asn1_compat.cc", "crypto/bytestring/ber.cc", "crypto/bytestring/cbb.cc", "crypto/bytestring/cbs.cc", "crypto/bytestring/unicode.cc", "crypto/chacha/chacha.cc", "crypto/cipher/derive_key.cc", "crypto/cipher/e_aesctrhmac.cc", "crypto/cipher/e_aeseax.cc", "crypto/cipher/e_aesgcmsiv.cc", "crypto/cipher/e_chacha20poly1305.cc", "crypto/cipher/e_des.cc", "crypto/cipher/e_null.cc", "crypto/cipher/e_rc2.cc", "crypto/cipher/e_rc4.cc", "crypto/cipher/e_tls.cc", "crypto/cipher/get_cipher.cc", "crypto/cipher/tls_cbc.cc", "crypto/conf/conf.cc", "crypto/cpu_aarch64_apple.cc", "crypto/cpu_aarch64_fuchsia.cc", "crypto/cpu_aarch64_linux.cc", "crypto/cpu_aarch64_openbsd.cc", "crypto/cpu_aarch64_sysreg.cc", "crypto/cpu_aarch64_win.cc", "crypto/cpu_arm_freebsd.cc", "crypto/cpu_arm_linux.cc", "crypto/cpu_intel.cc", "crypto/crypto.cc", "crypto/curve25519/curve25519.cc", "crypto/curve25519/curve25519_64_adx.cc", "crypto/curve25519/spake25519.cc", "crypto/des/des.cc", "crypto/dh/dh_asn1.cc", "crypto/dh/params.cc", "crypto/digest/digest_extra.cc", "crypto/dsa/dsa.cc", "crypto/dsa/dsa_asn1.cc", "crypto/ec/ec_asn1.cc", "crypto/ec/ec_derive.cc", "crypto/ec/hash_to_curve.cc", "crypto/ecdh/ecdh.cc", "crypto/ecdsa/ecdsa_asn1.cc", "crypto/engine/engine.cc", "crypto/err/err.cc", "crypto/evp/evp.cc", "crypto/evp/evp_asn1.cc", "crypto/evp/evp_ctx.cc", "crypto/evp/p_dh.cc", "crypto/evp/p_dh_asn1.cc", "crypto/evp/p_dsa_asn1.cc", "crypto/evp/p_ec.cc", "crypto/evp/p_ec_asn1.cc", "crypto/evp/p_ed25519.cc", "crypto/evp/p_ed25519_asn1.cc", "crypto/evp/p_hkdf.cc", "crypto/evp/p_rsa.cc", "crypto/evp/p_rsa_asn1.cc", "crypto/evp/p_x25519.cc", "crypto/evp/p_x25519_asn1.cc", "crypto/evp/pbkdf.cc", "crypto/evp/print.cc", "crypto/evp/scrypt.cc", "crypto/evp/sign.cc", "crypto/ex_data.cc", "crypto/fipsmodule/fips_shared_support.cc", "crypto/fuzzer_mode.cc", "crypto/hpke/hpke.cc", "crypto/hrss/hrss.cc", "crypto/kyber/kyber.cc", "crypto/lhash/lhash.cc", "crypto/md4/md4.cc", "crypto/md5/md5.cc", "crypto/mem.cc", "crypto/mldsa/mldsa.cc", "crypto/mlkem/mlkem.cc", "crypto/obj/obj.cc", "crypto/obj/obj_xref.cc", "crypto/pem/pem_all.cc", "crypto/pem/pem_info.cc", "crypto/pem/pem_lib.cc", "crypto/pem/pem_oth.cc", "crypto/pem/pem_pk8.cc", "crypto/pem/pem_pkey.cc", "crypto/pem/pem_x509.cc", "crypto/pem/pem_xaux.cc", "crypto/pkcs7/pkcs7.cc", "crypto/pkcs7/pkcs7_x509.cc", "crypto/pkcs8/p5_pbev2.cc", "crypto/pkcs8/pkcs8.cc", "crypto/pkcs8/pkcs8_x509.cc", "crypto/poly1305/poly1305.cc", "crypto/poly1305/poly1305_arm.cc", "crypto/poly1305/poly1305_vec.cc", "crypto/pool/pool.cc", "crypto/rand/deterministic.cc", "crypto/rand/fork_detect.cc", "crypto/rand/forkunsafe.cc", "crypto/rand/getentropy.cc", "crypto/rand/ios.cc", "crypto/rand/passive.cc", "crypto/rand/rand.cc", "crypto/rand/trusty.cc", "crypto/rand/urandom.cc", "crypto/rand/windows.cc", "crypto/rc4/rc4.cc", "crypto/refcount.cc", "crypto/rsa/rsa_asn1.cc", "crypto/rsa/rsa_crypt.cc", "crypto/rsa/rsa_extra.cc", "crypto/rsa/rsa_print.cc", "crypto/sha/sha1.cc", "crypto/sha/sha256.cc", "crypto/sha/sha512.cc", "crypto/siphash/siphash.cc", "crypto/slhdsa/slhdsa.cc", "crypto/spake2plus/spake2plus.cc", "crypto/stack/stack.cc", "crypto/thread.cc", "crypto/thread_none.cc", "crypto/thread_pthread.cc", "crypto/thread_win.cc", "crypto/trust_token/pmbtoken.cc", "crypto/trust_token/trust_token.cc", "crypto/trust_token/voprf.cc", "crypto/x509/a_digest.cc", "crypto/x509/a_sign.cc", "crypto/x509/a_verify.cc", "crypto/x509/algorithm.cc", "crypto/x509/asn1_gen.cc", "crypto/x509/by_dir.cc", "crypto/x509/by_file.cc", "crypto/x509/i2d_pr.cc", "crypto/x509/name_print.cc", "crypto/x509/policy.cc", "crypto/x509/rsa_pss.cc", "crypto/x509/t_crl.cc", "crypto/x509/t_req.cc", "crypto/x509/t_x509.cc", "crypto/x509/t_x509a.cc", "crypto/x509/v3_akey.cc", "crypto/x509/v3_akeya.cc", "crypto/x509/v3_alt.cc", "crypto/x509/v3_bcons.cc", "crypto/x509/v3_bitst.cc", "crypto/x509/v3_conf.cc", "crypto/x509/v3_cpols.cc", "crypto/x509/v3_crld.cc", "crypto/x509/v3_enum.cc", "crypto/x509/v3_extku.cc", "crypto/x509/v3_genn.cc", "crypto/x509/v3_ia5.cc", "crypto/x509/v3_info.cc", "crypto/x509/v3_int.cc", "crypto/x509/v3_lib.cc", "crypto/x509/v3_ncons.cc", "crypto/x509/v3_ocsp.cc", "crypto/x509/v3_pcons.cc", "crypto/x509/v3_pmaps.cc", "crypto/x509/v3_prn.cc", "crypto/x509/v3_purp.cc", "crypto/x509/v3_skey.cc", "crypto/x509/v3_utl.cc", "crypto/x509/x509.cc", "crypto/x509/x509_att.cc", "crypto/x509/x509_cmp.cc", "crypto/x509/x509_d2.cc", "crypto/x509/x509_def.cc", "crypto/x509/x509_ext.cc", "crypto/x509/x509_lu.cc", "crypto/x509/x509_obj.cc", "crypto/x509/x509_req.cc", "crypto/x509/x509_set.cc", "crypto/x509/x509_trs.cc", "crypto/x509/x509_txt.cc", "crypto/x509/x509_v3.cc", "crypto/x509/x509_vfy.cc", "crypto/x509/x509_vpm.cc", "crypto/x509/x509cset.cc", "crypto/x509/x509name.cc", "crypto/x509/x509rset.cc", "crypto/x509/x509spki.cc", "crypto/x509/x_algor.cc", "crypto/x509/x_all.cc", "crypto/x509/x_attrib.cc", "crypto/x509/x_crl.cc", "crypto/x509/x_exten.cc", "crypto/x509/x_name.cc", "crypto/x509/x_pubkey.cc", "crypto/x509/x_req.cc", "crypto/x509/x_sig.cc", "crypto/x509/x_spki.cc", "crypto/x509/x_val.cc", "crypto/x509/x_x509.cc", "crypto/x509/x_x509a.cc", "gen/crypto/err_data.cc"], "hdrs": ["include/openssl/aead.h", "include/openssl/aes.h", "include/openssl/arm_arch.h", "include/openssl/asm_base.h", "include/openssl/asn1.h", "include/openssl/asn1_mac.h", "include/openssl/asn1t.h", "include/openssl/base.h", "include/openssl/base64.h", "include/openssl/bcm_public.h", "include/openssl/bio.h", "include/openssl/blake2.h", "include/openssl/blowfish.h", "include/openssl/bn.h", "include/openssl/buf.h", "include/openssl/buffer.h", "include/openssl/bytestring.h", "include/openssl/cast.h", "include/openssl/chacha.h", "include/openssl/cipher.h", "include/openssl/cmac.h", "include/openssl/conf.h", "include/openssl/cpu.h", "include/openssl/crypto.h", "include/openssl/ctrdrbg.h", "include/openssl/curve25519.h", "include/openssl/des.h", "include/openssl/dh.h", "include/openssl/digest.h", "include/openssl/dsa.h", "include/openssl/e_os2.h", "include/openssl/ec.h", "include/openssl/ec_key.h", "include/openssl/ecdh.h", "include/openssl/ecdsa.h", "include/openssl/engine.h", "include/openssl/err.h", "include/openssl/evp.h", "include/openssl/evp_errors.h", "include/openssl/ex_data.h", "include/openssl/experimental/kyber.h", "include/openssl/hkdf.h", "include/openssl/hmac.h", "include/openssl/hpke.h", "include/openssl/hrss.h", "include/openssl/is_boringssl.h", "include/openssl/kdf.h", "include/openssl/lhash.h", "include/openssl/md4.h", "include/openssl/md5.h", "include/openssl/mem.h", "include/openssl/mldsa.h", "include/openssl/mlkem.h", "include/openssl/nid.h", "include/openssl/obj.h", "include/openssl/obj_mac.h", "include/openssl/objects.h", "include/openssl/opensslconf.h", "include/openssl/opensslv.h", "include/openssl/ossl_typ.h", "include/openssl/pem.h", "include/openssl/pkcs12.h", "include/openssl/pkcs7.h", "include/openssl/pkcs8.h", "include/openssl/poly1305.h", "include/openssl/pool.h", "include/openssl/posix_time.h", "include/openssl/rand.h", "include/openssl/rc4.h", "include/openssl/ripemd.h", "include/openssl/rsa.h", "include/openssl/safestack.h", "include/openssl/service_indicator.h", "include/openssl/sha.h", "include/openssl/siphash.h", "include/openssl/slhdsa.h", "include/openssl/span.h", "include/openssl/stack.h", "include/openssl/target.h", "include/openssl/thread.h", "include/openssl/time.h", "include/openssl/trust_token.h", "include/openssl/type_check.h", "include/openssl/x509.h", "include/openssl/x509_vfy.h", "include/openssl/x509v3.h", "include/openssl/x509v3_errors.h"], "internal_hdrs": ["crypto/asn1/internal.h", "crypto/bcm_support.h", "crypto/bio/internal.h", "crypto/bytestring/internal.h", "crypto/chacha/internal.h", "crypto/cipher/internal.h", "crypto/conf/internal.h", "crypto/cpu_arm_linux.h", "crypto/curve25519/curve25519_tables.h", "crypto/curve25519/internal.h", "crypto/des/internal.h", "crypto/dsa/internal.h", "crypto/ec/internal.h", "crypto/err/internal.h", "crypto/evp/internal.h", "crypto/fipsmodule/aes/internal.h", "crypto/fipsmodule/bcm_interface.h", "crypto/fipsmodule/bn/internal.h", "crypto/fipsmodule/bn/rsaz_exp.h", "crypto/fipsmodule/cipher/internal.h", "crypto/fipsmodule/delocate.h", "crypto/fipsmodule/dh/internal.h", "crypto/fipsmodule/digest/internal.h", "crypto/fipsmodule/digest/md32_common.h", "crypto/fipsmodule/ec/builtin_curves.h", "crypto/fipsmodule/ec/internal.h", "crypto/fipsmodule/ec/p256-nistz-table.h", "crypto/fipsmodule/ec/p256-nistz.h", "crypto/fipsmodule/ec/p256_table.h", "crypto/fipsmodule/ecdsa/internal.h", "crypto/fipsmodule/keccak/internal.h", "crypto/fipsmodule/rand/internal.h", "crypto/fipsmodule/rsa/internal.h", "crypto/fipsmodule/service_indicator/internal.h", "crypto/fipsmodule/sha/internal.h", "crypto/fipsmodule/slhdsa/address.h", "crypto/fipsmodule/slhdsa/fors.h", "crypto/fipsmodule/slhdsa/merkle.h", "crypto/fipsmodule/slhdsa/params.h", "crypto/fipsmodule/slhdsa/thash.h", "crypto/fipsmodule/slhdsa/wots.h", "crypto/fipsmodule/tls/internal.h", "crypto/hrss/internal.h", "crypto/internal.h", "crypto/kyber/internal.h", "crypto/lhash/internal.h", "crypto/md5/internal.h", "crypto/obj/obj_dat.h", "crypto/pem/internal.h", "crypto/pkcs7/internal.h", "crypto/pkcs8/internal.h", "crypto/poly1305/internal.h", "crypto/pool/internal.h", "crypto/rand/getrandom_fillin.h", "crypto/rand/internal.h", "crypto/rsa/internal.h", "crypto/spake2plus/internal.h", "crypto/trust_token/internal.h", "crypto/x509/ext_dat.h", "crypto/x509/internal.h", "third_party/fiat/curve25519_32.h", "third_party/fiat/curve25519_64.h", "third_party/fiat/curve25519_64_adx.h", "third_party/fiat/curve25519_64_msvc.h", "third_party/fiat/p256_32.h", "third_party/fiat/p256_64.h", "third_party/fiat/p256_64_msvc.h"], "asm": ["crypto/curve25519/asm/x25519-asm-arm.S", "crypto/hrss/asm/poly_rq_mul.S", "crypto/poly1305/poly1305_arm_asm.S", "gen/crypto/aes128gcmsiv-x86_64-apple.S", "gen/crypto/aes128gcmsiv-x86_64-linux.S", "gen/crypto/chacha-armv4-linux.S", "gen/crypto/chacha-armv8-apple.S", "gen/crypto/chacha-armv8-linux.S", "gen/crypto/chacha-armv8-win.S", "gen/crypto/chacha-x86-apple.S", "gen/crypto/chacha-x86-linux.S", "gen/crypto/chacha-x86_64-apple.S", "gen/crypto/chacha-x86_64-linux.S", "gen/crypto/chacha20_poly1305_armv8-apple.S", "gen/crypto/chacha20_poly1305_armv8-linux.S", "gen/crypto/chacha20_poly1305_armv8-win.S", "gen/crypto/chacha20_poly1305_x86_64-apple.S", "gen/crypto/chacha20_poly1305_x86_64-linux.S", "gen/crypto/md5-586-apple.S", "gen/crypto/md5-586-linux.S", "gen/crypto/md5-x86_64-apple.S", "gen/crypto/md5-x86_64-linux.S", "third_party/fiat/asm/fiat_curve25519_adx_mul.S", "third_party/fiat/asm/fiat_curve25519_adx_square.S"], "nasm": ["gen/crypto/aes128gcmsiv-x86_64-win.asm", "gen/crypto/chacha-x86-win.asm", "gen/crypto/chacha-x86_64-win.asm", "gen/crypto/chacha20_poly1305_x86_64-win.asm", "gen/crypto/md5-586-win.asm", "gen/crypto/md5-x86_64-win.asm"]}, "crypto_test": {"srcs": ["crypto/abi_self_test.cc", "crypto/asn1/asn1_test.cc", "crypto/base64/base64_test.cc", "crypto/bio/bio_test.cc", "crypto/blake2/blake2_test.cc", "crypto/buf/buf_test.cc", "crypto/bytestring/bytestring_test.cc", "crypto/chacha/chacha_test.cc", "crypto/cipher/aead_test.cc", "crypto/cipher/cipher_test.cc", "crypto/compiler_test.cc", "crypto/conf/conf_test.cc", "crypto/constant_time_test.cc", "crypto/cpu_arm_linux_test.cc", "crypto/crypto_test.cc", "crypto/curve25519/ed25519_test.cc", "crypto/curve25519/spake25519_test.cc", "crypto/curve25519/x25519_test.cc", "crypto/dh/dh_test.cc", "crypto/digest/digest_test.cc", "crypto/dsa/dsa_test.cc", "crypto/ecdh/ecdh_test.cc", "crypto/err/err_test.cc", "crypto/evp/evp_extra_test.cc", "crypto/evp/evp_test.cc", "crypto/evp/pbkdf_test.cc", "crypto/evp/scrypt_test.cc", "crypto/fipsmodule/aes/aes_test.cc", "crypto/fipsmodule/aes/gcm_test.cc", "crypto/fipsmodule/bn/bn_test.cc", "crypto/fipsmodule/cmac/cmac_test.cc", "crypto/fipsmodule/ec/ec_test.cc", "crypto/fipsmodule/ec/p256-nistz_test.cc", "crypto/fipsmodule/ec/p256_test.cc", "crypto/fipsmodule/ecdsa/ecdsa_test.cc", "crypto/fipsmodule/hkdf/hkdf_test.cc", "crypto/fipsmodule/keccak/keccak_test.cc", "crypto/fipsmodule/rand/ctrdrbg_test.cc", "crypto/fipsmodule/service_indicator/service_indicator_test.cc", "crypto/fipsmodule/sha/sha_test.cc", "crypto/hmac/hmac_test.cc", "crypto/hpke/hpke_test.cc", "crypto/hrss/hrss_test.cc", "crypto/impl_dispatch_test.cc", "crypto/kyber/kyber_test.cc", "crypto/lhash/lhash_test.cc", "crypto/md5/md5_test.cc", "crypto/mldsa/mldsa_test.cc", "crypto/mlkem/mlkem_test.cc", "crypto/obj/obj_test.cc", "crypto/pem/pem_test.cc", "crypto/pkcs7/pkcs7_test.cc", "crypto/pkcs8/pkcs12_test.cc", "crypto/pkcs8/pkcs8_test.cc", "crypto/poly1305/poly1305_test.cc", "crypto/pool/pool_test.cc", "crypto/rand/fork_detect_test.cc", "crypto/rand/getentropy_test.cc", "crypto/rand/rand_test.cc", "crypto/refcount_test.cc", "crypto/rsa/rsa_test.cc", "crypto/self_test.cc", "crypto/siphash/siphash_test.cc", "crypto/slhdsa/slhdsa_test.cc", "crypto/spake2plus/spake2plus_test.cc", "crypto/stack/stack_test.cc", "crypto/test/gtest_main.cc", "crypto/thread_test.cc", "crypto/trust_token/trust_token_test.cc", "crypto/x509/tab_test.cc", "crypto/x509/x509_test.cc", "crypto/x509/x509_time_test.cc"], "data": ["crypto/blake2/blake2b256_tests.txt", "crypto/cipher/test/aes_128_cbc_sha1_tls_implicit_iv_tests.txt", "crypto/cipher/test/aes_128_cbc_sha1_tls_tests.txt", "crypto/cipher/test/aes_128_ccm_bluetooth_8_tests.txt", "crypto/cipher/test/aes_128_ccm_bluetooth_tests.txt", "crypto/cipher/test/aes_128_ccm_matter_tests.txt", "crypto/cipher/test/aes_128_ctr_hmac_sha256.txt", "crypto/cipher/test/aes_128_eax_test.txt", "crypto/cipher/test/aes_128_gcm_randnonce_tests.txt", "crypto/cipher/test/aes_128_gcm_siv_tests.txt", "crypto/cipher/test/aes_128_gcm_tests.txt", "crypto/cipher/test/aes_192_gcm_tests.txt", "crypto/cipher/test/aes_256_cbc_sha1_tls_implicit_iv_tests.txt", "crypto/cipher/test/aes_256_cbc_sha1_tls_tests.txt", "crypto/cipher/test/aes_256_ctr_hmac_sha256.txt", "crypto/cipher/test/aes_256_eax_test.txt", "crypto/cipher/test/aes_256_gcm_randnonce_tests.txt", "crypto/cipher/test/aes_256_gcm_siv_tests.txt", "crypto/cipher/test/aes_256_gcm_tests.txt", "crypto/cipher/test/chacha20_poly1305_tests.txt", "crypto/cipher/test/cipher_tests.txt", "crypto/cipher/test/des_ede3_cbc_sha1_tls_implicit_iv_tests.txt", "crypto/cipher/test/des_ede3_cbc_sha1_tls_tests.txt", "crypto/cipher/test/nist_cavp/aes_128_cbc.txt", "crypto/cipher/test/nist_cavp/aes_128_ctr.txt", "crypto/cipher/test/nist_cavp/aes_128_gcm.txt", "crypto/cipher/test/nist_cavp/aes_192_cbc.txt", "crypto/cipher/test/nist_cavp/aes_192_ctr.txt", "crypto/cipher/test/nist_cavp/aes_256_cbc.txt", "crypto/cipher/test/nist_cavp/aes_256_ctr.txt", "crypto/cipher/test/nist_cavp/aes_256_gcm.txt", "crypto/cipher/test/nist_cavp/tdes_cbc.txt", "crypto/cipher/test/nist_cavp/tdes_ecb.txt", "crypto/cipher/test/xchacha20_poly1305_tests.txt", "crypto/curve25519/ed25519_tests.txt", "crypto/ecdh/ecdh_tests.txt", "crypto/evp/evp_tests.txt", "crypto/evp/scrypt_tests.txt", "crypto/fipsmodule/aes/aes_tests.txt", "crypto/fipsmodule/bn/test/exp_tests.txt", "crypto/fipsmodule/bn/test/gcd_tests.txt", "crypto/fipsmodule/bn/test/miller_rabin_tests.txt", "crypto/fipsmodule/bn/test/mod_exp_tests.txt", "crypto/fipsmodule/bn/test/mod_inv_tests.txt", "crypto/fipsmodule/bn/test/mod_mul_tests.txt", "crypto/fipsmodule/bn/test/mod_sqrt_tests.txt", "crypto/fipsmodule/bn/test/product_tests.txt", "crypto/fipsmodule/bn/test/quotient_tests.txt", "crypto/fipsmodule/bn/test/shift_tests.txt", "crypto/fipsmodule/bn/test/sum_tests.txt", "crypto/fipsmodule/cmac/cavp_3des_cmac_tests.txt", "crypto/fipsmodule/cmac/cavp_aes128_cmac_tests.txt", "crypto/fipsmodule/cmac/cavp_aes192_cmac_tests.txt", "crypto/fipsmodule/cmac/cavp_aes256_cmac_tests.txt", "crypto/fipsmodule/ec/ec_scalar_base_mult_tests.txt", "crypto/fipsmodule/ec/p256-nistz_tests.txt", "crypto/fipsmodule/ecdsa/ecdsa_sign_tests.txt", "crypto/fipsmodule/ecdsa/ecdsa_verify_tests.txt", "crypto/fipsmodule/keccak/keccak_tests.txt", "crypto/fipsmodule/rand/ctrdrbg_vectors.txt", "crypto/hmac/hmac_tests.txt", "crypto/hpke/hpke_test_vectors.txt", "crypto/kyber/kyber_tests.txt", "crypto/mldsa/mldsa_nist_keygen_65_tests.txt", "crypto/mldsa/mldsa_nist_keygen_87_tests.txt", "crypto/mldsa/mldsa_nist_siggen_65_tests.txt", "crypto/mldsa/mldsa_nist_siggen_87_tests.txt", "crypto/mlkem/mlkem1024_decap_tests.txt", "crypto/mlkem/mlkem1024_encap_tests.txt", "crypto/mlkem/mlkem1024_keygen_tests.txt", "crypto/mlkem/mlkem1024_nist_decap_tests.txt", "crypto/mlkem/mlkem1024_nist_keygen_tests.txt", "crypto/mlkem/mlkem768_decap_tests.txt", "crypto/mlkem/mlkem768_encap_tests.txt", "crypto/mlkem/mlkem768_keygen_tests.txt", "crypto/mlkem/mlkem768_nist_decap_tests.txt", "crypto/mlkem/mlkem768_nist_keygen_tests.txt", "crypto/pkcs8/test/bad1.p12", "crypto/pkcs8/test/bad2.p12", "crypto/pkcs8/test/bad3.p12", "crypto/pkcs8/test/empty_password.p12", "crypto/pkcs8/test/empty_password_ber.p12", "crypto/pkcs8/test/empty_password_ber_nested.p12", "crypto/pkcs8/test/no_encryption.p12", "crypto/pkcs8/test/nss.p12", "crypto/pkcs8/test/null_password.p12", "crypto/pkcs8/test/openssl.p12", "crypto/pkcs8/test/pbes2_sha1.p12", "crypto/pkcs8/test/pbes2_sha256.p12", "crypto/pkcs8/test/unicode_password.p12", "crypto/pkcs8/test/windows.p12", "crypto/poly1305/poly1305_tests.txt", "crypto/siphash/siphash_tests.txt", "crypto/slhdsa/slhdsa_keygen.txt", "crypto/slhdsa/slhdsa_prehash.txt", "crypto/slhdsa/slhdsa_siggen.txt", "crypto/slhdsa/slhdsa_sigver.txt", "crypto/x509/test/basic_constraints_ca.pem", "crypto/x509/test/basic_constraints_ca_pathlen_0.pem", "crypto/x509/test/basic_constraints_ca_pathlen_1.pem", "crypto/x509/test/basic_constraints_ca_pathlen_10.pem", "crypto/x509/test/basic_constraints_leaf.pem", "crypto/x509/test/basic_constraints_none.pem", "crypto/x509/test/invalid_extension_intermediate.pem", "crypto/x509/test/invalid_extension_intermediate_authority_key_identifier.pem", "crypto/x509/test/invalid_extension_intermediate_basic_constraints.pem", "crypto/x509/test/invalid_extension_intermediate_ext_key_usage.pem", "crypto/x509/test/invalid_extension_intermediate_key_usage.pem", "crypto/x509/test/invalid_extension_intermediate_name_constraints.pem", "crypto/x509/test/invalid_extension_intermediate_subject_alt_name.pem", "crypto/x509/test/invalid_extension_intermediate_subject_key_identifier.pem", "crypto/x509/test/invalid_extension_leaf.pem", "crypto/x509/test/invalid_extension_leaf_authority_key_identifier.pem", "crypto/x509/test/invalid_extension_leaf_basic_constraints.pem", "crypto/x509/test/invalid_extension_leaf_ext_key_usage.pem", "crypto/x509/test/invalid_extension_leaf_key_usage.pem", "crypto/x509/test/invalid_extension_leaf_name_constraints.pem", "crypto/x509/test/invalid_extension_leaf_subject_alt_name.pem", "crypto/x509/test/invalid_extension_leaf_subject_key_identifier.pem", "crypto/x509/test/invalid_extension_root.pem", "crypto/x509/test/invalid_extension_root_authority_key_identifier.pem", "crypto/x509/test/invalid_extension_root_basic_constraints.pem", "crypto/x509/test/invalid_extension_root_ext_key_usage.pem", "crypto/x509/test/invalid_extension_root_key_usage.pem", "crypto/x509/test/invalid_extension_root_name_constraints.pem", "crypto/x509/test/invalid_extension_root_subject_alt_name.pem", "crypto/x509/test/invalid_extension_root_subject_key_identifier.pem", "crypto/x509/test/many_constraints.pem", "crypto/x509/test/many_names1.pem", "crypto/x509/test/many_names2.pem", "crypto/x509/test/many_names3.pem", "crypto/x509/test/policy_intermediate.pem", "crypto/x509/test/policy_intermediate_any.pem", "crypto/x509/test/policy_intermediate_duplicate.pem", "crypto/x509/test/policy_intermediate_invalid.pem", "crypto/x509/test/policy_intermediate_mapped.pem", "crypto/x509/test/policy_intermediate_mapped_any.pem", "crypto/x509/test/policy_intermediate_mapped_oid3.pem", "crypto/x509/test/policy_intermediate_require.pem", "crypto/x509/test/policy_intermediate_require1.pem", "crypto/x509/test/policy_intermediate_require2.pem", "crypto/x509/test/policy_intermediate_require_duplicate.pem", "crypto/x509/test/policy_intermediate_require_no_policies.pem", "crypto/x509/test/policy_leaf.pem", "crypto/x509/test/policy_leaf_any.pem", "crypto/x509/test/policy_leaf_duplicate.pem", "crypto/x509/test/policy_leaf_invalid.pem", "crypto/x509/test/policy_leaf_none.pem", "crypto/x509/test/policy_leaf_oid1.pem", "crypto/x509/test/policy_leaf_oid2.pem", "crypto/x509/test/policy_leaf_oid3.pem", "crypto/x509/test/policy_leaf_oid4.pem", "crypto/x509/test/policy_leaf_oid5.pem", "crypto/x509/test/policy_leaf_require.pem", "crypto/x509/test/policy_leaf_require1.pem", "crypto/x509/test/policy_root.pem", "crypto/x509/test/policy_root2.pem", "crypto/x509/test/policy_root_cross_inhibit_mapping.pem", "crypto/x509/test/pss_sha1.pem", "crypto/x509/test/pss_sha1_explicit.pem", "crypto/x509/test/pss_sha1_mgf1_syntax_error.pem", "crypto/x509/test/pss_sha224.pem", "crypto/x509/test/pss_sha256.pem", "crypto/x509/test/pss_sha256_explicit_trailer.pem", "crypto/x509/test/pss_sha256_mgf1_sha384.pem", "crypto/x509/test/pss_sha256_mgf1_syntax_error.pem", "crypto/x509/test/pss_sha256_omit_nulls.pem", "crypto/x509/test/pss_sha256_salt31.pem", "crypto/x509/test/pss_sha256_salt_overflow.pem", "crypto/x509/test/pss_sha256_unknown_mgf.pem", "crypto/x509/test/pss_sha256_wrong_trailer.pem", "crypto/x509/test/pss_sha384.pem", "crypto/x509/test/pss_sha512.pem", "crypto/x509/test/some_names1.pem", "crypto/x509/test/some_names2.pem", "crypto/x509/test/some_names3.pem", "crypto/x509/test/trailing_data_leaf_authority_key_identifier.pem", "crypto/x509/test/trailing_data_leaf_basic_constraints.pem", "crypto/x509/test/trailing_data_leaf_ext_key_usage.pem", "crypto/x509/test/trailing_data_leaf_key_usage.pem", "crypto/x509/test/trailing_data_leaf_name_constraints.pem", "crypto/x509/test/trailing_data_leaf_subject_alt_name.pem", "crypto/x509/test/trailing_data_leaf_subject_key_identifier.pem", "third_party/wycheproof_testvectors/aes_cbc_pkcs5_test.txt", "third_party/wycheproof_testvectors/aes_cmac_test.txt", "third_party/wycheproof_testvectors/aes_eax_test.txt", "third_party/wycheproof_testvectors/aes_gcm_siv_test.txt", "third_party/wycheproof_testvectors/aes_gcm_test.txt", "third_party/wycheproof_testvectors/chacha20_poly1305_test.txt", "third_party/wycheproof_testvectors/dsa_test.txt", "third_party/wycheproof_testvectors/ecdh_secp224r1_test.txt", "third_party/wycheproof_testvectors/ecdh_secp256r1_test.txt", "third_party/wycheproof_testvectors/ecdh_secp384r1_test.txt", "third_party/wycheproof_testvectors/ecdh_secp521r1_test.txt", "third_party/wycheproof_testvectors/ecdsa_secp224r1_sha224_test.txt", "third_party/wycheproof_testvectors/ecdsa_secp224r1_sha256_test.txt", "third_party/wycheproof_testvectors/ecdsa_secp224r1_sha512_test.txt", "third_party/wycheproof_testvectors/ecdsa_secp256r1_sha256_test.txt", "third_party/wycheproof_testvectors/ecdsa_secp256r1_sha512_test.txt", "third_party/wycheproof_testvectors/ecdsa_secp384r1_sha384_test.txt", "third_party/wycheproof_testvectors/ecdsa_secp384r1_sha512_test.txt", "third_party/wycheproof_testvectors/ecdsa_secp521r1_sha512_test.txt", "third_party/wycheproof_testvectors/eddsa_test.txt", "third_party/wycheproof_testvectors/hkdf_sha1_test.txt", "third_party/wycheproof_testvectors/hkdf_sha256_test.txt", "third_party/wycheproof_testvectors/hkdf_sha384_test.txt", "third_party/wycheproof_testvectors/hkdf_sha512_test.txt", "third_party/wycheproof_testvectors/hmac_sha1_test.txt", "third_party/wycheproof_testvectors/hmac_sha224_test.txt", "third_party/wycheproof_testvectors/hmac_sha256_test.txt", "third_party/wycheproof_testvectors/hmac_sha384_test.txt", "third_party/wycheproof_testvectors/hmac_sha512_test.txt", "third_party/wycheproof_testvectors/kw_test.txt", "third_party/wycheproof_testvectors/kwp_test.txt", "third_party/wycheproof_testvectors/mldsa_65_standard_sign_test.txt", "third_party/wycheproof_testvectors/mldsa_65_standard_verify_test.txt", "third_party/wycheproof_testvectors/mldsa_87_standard_sign_test.txt", "third_party/wycheproof_testvectors/mldsa_87_standard_verify_test.txt", "third_party/wycheproof_testvectors/primality_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_2048_sha1_mgf1sha1_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_2048_sha224_mgf1sha1_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_2048_sha224_mgf1sha224_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_2048_sha256_mgf1sha1_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_2048_sha256_mgf1sha256_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_2048_sha384_mgf1sha1_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_2048_sha384_mgf1sha384_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_2048_sha512_mgf1sha1_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_2048_sha512_mgf1sha512_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_3072_sha256_mgf1sha1_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_3072_sha256_mgf1sha256_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_3072_sha512_mgf1sha1_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_3072_sha512_mgf1sha512_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_4096_sha256_mgf1sha1_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_4096_sha256_mgf1sha256_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_4096_sha512_mgf1sha1_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_4096_sha512_mgf1sha512_test.txt", "third_party/wycheproof_testvectors/rsa_oaep_misc_test.txt", "third_party/wycheproof_testvectors/rsa_pkcs1_2048_test.txt", "third_party/wycheproof_testvectors/rsa_pkcs1_3072_test.txt", "third_party/wycheproof_testvectors/rsa_pkcs1_4096_test.txt", "third_party/wycheproof_testvectors/rsa_pss_2048_sha1_mgf1_20_test.txt", "third_party/wycheproof_testvectors/rsa_pss_2048_sha256_mgf1_0_test.txt", "third_party/wycheproof_testvectors/rsa_pss_2048_sha256_mgf1_32_test.txt", "third_party/wycheproof_testvectors/rsa_pss_3072_sha256_mgf1_32_test.txt", "third_party/wycheproof_testvectors/rsa_pss_4096_sha256_mgf1_32_test.txt", "third_party/wycheproof_testvectors/rsa_pss_4096_sha512_mgf1_32_test.txt", "third_party/wycheproof_testvectors/rsa_pss_misc_test.txt", "third_party/wycheproof_testvectors/rsa_sig_gen_misc_test.txt", "third_party/wycheproof_testvectors/rsa_signature_2048_sha224_test.txt", "third_party/wycheproof_testvectors/rsa_signature_2048_sha256_test.txt", "third_party/wycheproof_testvectors/rsa_signature_2048_sha384_test.txt", "third_party/wycheproof_testvectors/rsa_signature_2048_sha512_test.txt", "third_party/wycheproof_testvectors/rsa_signature_3072_sha256_test.txt", "third_party/wycheproof_testvectors/rsa_signature_3072_sha384_test.txt", "third_party/wycheproof_testvectors/rsa_signature_3072_sha512_test.txt", "third_party/wycheproof_testvectors/rsa_signature_4096_sha384_test.txt", "third_party/wycheproof_testvectors/rsa_signature_4096_sha512_test.txt", "third_party/wycheproof_testvectors/rsa_signature_test.txt", "third_party/wycheproof_testvectors/x25519_test.txt", "third_party/wycheproof_testvectors/xchacha20_poly1305_test.txt"]}, "decrepit": {"srcs": ["decrepit/bio/base64_bio.cc", "decrepit/blowfish/blowfish.cc", "decrepit/cast/cast.cc", "decrepit/cast/cast_tables.cc", "decrepit/cfb/cfb.cc", "decrepit/des/cfb64ede.cc", "decrepit/dh/dh_decrepit.cc", "decrepit/dsa/dsa_decrepit.cc", "decrepit/evp/dss1.cc", "decrepit/evp/evp_do_all.cc", "decrepit/obj/obj_decrepit.cc", "decrepit/rc4/rc4_decrepit.cc", "decrepit/ripemd/ripemd.cc", "decrepit/rsa/rsa_decrepit.cc", "decrepit/ssl/ssl_decrepit.cc", "decrepit/x509/x509_decrepit.cc", "decrepit/xts/xts.cc"], "internal_hdrs": ["decrepit/cast/internal.h", "decrepit/macros.h"]}, "decrepit_test": {"srcs": ["crypto/test/gtest_main.cc", "decrepit/blowfish/blowfish_test.cc", "decrepit/cast/cast_test.cc", "decrepit/cfb/cfb_test.cc", "decrepit/des/des_test.cc", "decrepit/evp/evp_test.cc", "decrepit/ripemd/ripemd_test.cc", "decrepit/xts/xts_test.cc"]}, "fuzz": {"srcs": ["fuzz/arm_cpuinfo.cc", "fuzz/bn_div.cc", "fuzz/bn_mod_exp.cc", "fuzz/cert.cc", "fuzz/client.cc", "fuzz/client_no_fuzzer_mode.cc", "fuzz/conf.cc", "fuzz/crl_getcrlstatusforcert_fuzzer.cc", "fuzz/crl_parse_crl_certificatelist_fuzzer.cc", "fuzz/crl_parse_crl_tbscertlist_fuzzer.cc", "fuzz/crl_parse_issuing_distribution_point_fuzzer.cc", "fuzz/decode_client_hello_inner.cc", "fuzz/der_roundtrip.cc", "fuzz/dtls_client.cc", "fuzz/dtls_server.cc", "fuzz/ocsp_parse_ocsp_cert_id_fuzzer.cc", "fuzz/ocsp_parse_ocsp_response_data_fuzzer.cc", "fuzz/ocsp_parse_ocsp_response_fuzzer.cc", "fuzz/ocsp_parse_ocsp_single_response_fuzzer.cc", "fuzz/parse_authority_key_identifier_fuzzer.cc", "fuzz/parse_certificate_fuzzer.cc", "fuzz/parse_crldp_fuzzer.cc", "fuzz/pkcs12.cc", "fuzz/pkcs8.cc", "fuzz/privkey.cc", "fuzz/read_pem.cc", "fuzz/server.cc", "fuzz/server_no_fuzzer_mode.cc", "fuzz/session.cc", "fuzz/spki.cc", "fuzz/ssl_ctx_api.cc", "fuzz/verify_name_match_fuzzer.cc", "fuzz/verify_name_match_normalizename_fuzzer.cc", "fuzz/verify_name_match_verifynameinsubtree_fuzzer.cc"]}, "pki": {"srcs": ["pki/cert_error_id.cc", "pki/cert_error_params.cc", "pki/cert_errors.cc", "pki/cert_issuer_source_static.cc", "pki/certificate.cc", "pki/certificate_policies.cc", "pki/common_cert_errors.cc", "pki/crl.cc", "pki/encode_values.cc", "pki/extended_key_usage.cc", "pki/general_names.cc", "pki/input.cc", "pki/ip_util.cc", "pki/name_constraints.cc", "pki/ocsp.cc", "pki/parse_certificate.cc", "pki/parse_name.cc", "pki/parse_values.cc", "pki/parsed_certificate.cc", "pki/parser.cc", "pki/path_builder.cc", "pki/pem.cc", "pki/revocation_util.cc", "pki/signature_algorithm.cc", "pki/simple_path_builder_delegate.cc", "pki/string_util.cc", "pki/trust_store.cc", "pki/trust_store_collection.cc", "pki/trust_store_in_memory.cc", "pki/verify.cc", "pki/verify_certificate_chain.cc", "pki/verify_error.cc", "pki/verify_name_match.cc", "pki/verify_signed_data.cc"], "hdrs": ["include/openssl/pki/certificate.h", "include/openssl/pki/ocsp.h", "include/openssl/pki/signature_verify_cache.h", "include/openssl/pki/verify.h", "include/openssl/pki/verify_error.h"], "internal_hdrs": ["pki/cert_error_id.h", "pki/cert_error_params.h", "pki/cert_errors.h", "pki/cert_issuer_source.h", "pki/cert_issuer_source_static.h", "pki/cert_issuer_source_sync_unittest.h", "pki/certificate_policies.h", "pki/common_cert_errors.h", "pki/crl.h", "pki/encode_values.h", "pki/extended_key_usage.h", "pki/general_names.h", "pki/input.h", "pki/ip_util.h", "pki/mock_signature_verify_cache.h", "pki/name_constraints.h", "pki/nist_pkits_unittest.h", "pki/ocsp.h", "pki/parse_certificate.h", "pki/parse_name.h", "pki/parse_values.h", "pki/parsed_certificate.h", "pki/parser.h", "pki/path_builder.h", "pki/pem.h", "pki/revocation_util.h", "pki/signature_algorithm.h", "pki/simple_path_builder_delegate.h", "pki/string_util.h", "pki/test_helpers.h", "pki/testdata/nist-pkits/pkits_testcases-inl.h", "pki/trust_store.h", "pki/trust_store_collection.h", "pki/trust_store_in_memory.h", "pki/verify_certificate_chain.h", "pki/verify_certificate_chain_typed_unittest.h", "pki/verify_name_match.h", "pki/verify_signed_data.h"]}, "pki_test": {"srcs": ["crypto/test/gtest_main.cc", "pki/cert_issuer_source_static_unittest.cc", "pki/certificate_policies_unittest.cc", "pki/certificate_unittest.cc", "pki/crl_unittest.cc", "pki/encode_values_unittest.cc", "pki/extended_key_usage_unittest.cc", "pki/general_names_unittest.cc", "pki/input_unittest.cc", "pki/ip_util_unittest.cc", "pki/mock_signature_verify_cache.cc", "pki/name_constraints_unittest.cc", "pki/nist_pkits_unittest.cc", "pki/ocsp_unittest.cc", "pki/parse_certificate_unittest.cc", "pki/parse_name_unittest.cc", "pki/parse_values_unittest.cc", "pki/parsed_certificate_unittest.cc", "pki/parser_unittest.cc", "pki/path_builder_pkits_unittest.cc", "pki/path_builder_unittest.cc", "pki/path_builder_verify_certificate_chain_unittest.cc", "pki/pem_unittest.cc", "pki/signature_algorithm_unittest.cc", "pki/simple_path_builder_delegate_unittest.cc", "pki/string_util_unittest.cc", "pki/test_helpers.cc", "pki/trust_store_collection_unittest.cc", "pki/trust_store_in_memory_unittest.cc", "pki/verify_certificate_chain_pkits_unittest.cc", "pki/verify_certificate_chain_unittest.cc", "pki/verify_name_match_unittest.cc", "pki/verify_signed_data_unittest.cc", "pki/verify_unittest.cc"], "data": ["pki/testdata/cert_issuer_source_static_unittest/c1.pem", "pki/testdata/cert_issuer_source_static_unittest/c2.pem", "pki/testdata/cert_issuer_source_static_unittest/d.pem", "pki/testdata/cert_issuer_source_static_unittest/e1.pem", "pki/testdata/cert_issuer_source_static_unittest/e2.pem", "pki/testdata/cert_issuer_source_static_unittest/i1_1.pem", "pki/testdata/cert_issuer_source_static_unittest/i1_2.pem", "pki/testdata/cert_issuer_source_static_unittest/i2.pem", "pki/testdata/cert_issuer_source_static_unittest/i3_1.pem", "pki/testdata/cert_issuer_source_static_unittest/i3_2.pem", "pki/testdata/cert_issuer_source_static_unittest/root.pem", "pki/testdata/certificate_policies_unittest/anypolicy.pem", "pki/testdata/certificate_policies_unittest/anypolicy_with_qualifier.pem", "pki/testdata/certificate_policies_unittest/invalid-anypolicy_with_custom_qualifier.pem", "pki/testdata/certificate_policies_unittest/invalid-empty.pem", "pki/testdata/certificate_policies_unittest/invalid-policy_1_2_3_dupe.pem", "pki/testdata/certificate_policies_unittest/invalid-policy_1_2_3_policyinformation_unconsumed_data.pem", "pki/testdata/certificate_policies_unittest/invalid-policy_1_2_3_policyqualifierinfo_unconsumed_data.pem", "pki/testdata/certificate_policies_unittest/invalid-policy_1_2_3_with_empty_qualifiers_sequence.pem", "pki/testdata/certificate_policies_unittest/invalid-policy_identifier_not_oid.pem", "pki/testdata/certificate_policies_unittest/policy_1_2_3.pem", "pki/testdata/certificate_policies_unittest/policy_1_2_3_and_1_2_4.pem", "pki/testdata/certificate_policies_unittest/policy_1_2_3_and_1_2_4_with_qualifiers.pem", "pki/testdata/certificate_policies_unittest/policy_1_2_3_with_custom_qualifier.pem", "pki/testdata/certificate_policies_unittest/policy_1_2_3_with_qualifier.pem", "pki/testdata/crl_unittest/bad_crldp_has_crlissuer.pem", "pki/testdata/crl_unittest/bad_fake_critical_crlentryextension.pem", "pki/testdata/crl_unittest/bad_fake_critical_extension.pem", "pki/testdata/crl_unittest/bad_idp_contains_wrong_uri.pem", "pki/testdata/crl_unittest/bad_idp_indirectcrl.pem", "pki/testdata/crl_unittest/bad_idp_onlycontainscacerts.pem", "pki/testdata/crl_unittest/bad_idp_onlycontainscacerts_no_basic_constraints.pem", "pki/testdata/crl_unittest/bad_idp_onlycontainsusercerts.pem", "pki/testdata/crl_unittest/bad_idp_uri_and_onlycontainscacerts.pem", "pki/testdata/crl_unittest/bad_idp_uri_and_onlycontainsusercerts.pem", "pki/testdata/crl_unittest/bad_key_rollover_signature.pem", "pki/testdata/crl_unittest/bad_nextupdate_too_old.pem", "pki/testdata/crl_unittest/bad_signature.pem", "pki/testdata/crl_unittest/bad_thisupdate_in_future.pem", "pki/testdata/crl_unittest/bad_thisupdate_too_old.pem", "pki/testdata/crl_unittest/bad_wrong_issuer.pem", "pki/testdata/crl_unittest/good.pem", "pki/testdata/crl_unittest/good_fake_extension.pem", "pki/testdata/crl_unittest/good_fake_extension_no_nextupdate.pem", "pki/testdata/crl_unittest/good_generalizedtime.pem", "pki/testdata/crl_unittest/good_idp_contains_uri.pem", "pki/testdata/crl_unittest/good_idp_onlycontainscacerts.pem", "pki/testdata/crl_unittest/good_idp_onlycontainsusercerts.pem", "pki/testdata/crl_unittest/good_idp_onlycontainsusercerts_no_basic_constraints.pem", "pki/testdata/crl_unittest/good_idp_uri_and_onlycontainscacerts.pem", "pki/testdata/crl_unittest/good_idp_uri_and_onlycontainsusercerts.pem", "pki/testdata/crl_unittest/good_issuer_name_normalization.pem", "pki/testdata/crl_unittest/good_issuer_no_keyusage.pem", "pki/testdata/crl_unittest/good_key_rollover.pem", "pki/testdata/crl_unittest/good_no_crldp.pem", "pki/testdata/crl_unittest/good_no_nextupdate.pem", "pki/testdata/crl_unittest/good_no_version.pem", "pki/testdata/crl_unittest/invalid_garbage_after_crlentryextensions.pem", "pki/testdata/crl_unittest/invalid_garbage_after_extensions.pem", "pki/testdata/crl_unittest/invalid_garbage_after_nextupdate.pem", "pki/testdata/crl_unittest/invalid_garbage_after_revocationdate.pem", "pki/testdata/crl_unittest/invalid_garbage_after_revokedcerts.pem", "pki/testdata/crl_unittest/invalid_garbage_after_signaturevalue.pem", "pki/testdata/crl_unittest/invalid_garbage_after_thisupdate.pem", "pki/testdata/crl_unittest/invalid_garbage_crlentry.pem", "pki/testdata/crl_unittest/invalid_garbage_issuer_name.pem", "pki/testdata/crl_unittest/invalid_garbage_revocationdate.pem", "pki/testdata/crl_unittest/invalid_garbage_revoked_serial_number.pem", "pki/testdata/crl_unittest/invalid_garbage_signaturealgorithm.pem", "pki/testdata/crl_unittest/invalid_garbage_signaturevalue.pem", "pki/testdata/crl_unittest/invalid_garbage_tbs_signature_algorithm.pem", "pki/testdata/crl_unittest/invalid_garbage_tbscertlist.pem", "pki/testdata/crl_unittest/invalid_garbage_thisupdate.pem", "pki/testdata/crl_unittest/invalid_garbage_version.pem", "pki/testdata/crl_unittest/invalid_idp_dpname_choice_extra_data.pem", "pki/testdata/crl_unittest/invalid_idp_empty_sequence.pem", "pki/testdata/crl_unittest/invalid_idp_onlycontains_user_and_ca_certs.pem", "pki/testdata/crl_unittest/invalid_idp_onlycontainsusercerts_v1_leaf.pem", "pki/testdata/crl_unittest/invalid_issuer_keyusage_no_crlsign.pem", "pki/testdata/crl_unittest/invalid_key_rollover_issuer_keyusage_no_crlsign.pem", "pki/testdata/crl_unittest/invalid_mismatched_signature_algorithm.pem", "pki/testdata/crl_unittest/invalid_revoked_empty_sequence.pem", "pki/testdata/crl_unittest/invalid_v1_explicit.pem", "pki/testdata/crl_unittest/invalid_v1_with_crlentryextension.pem", "pki/testdata/crl_unittest/invalid_v1_with_extension.pem", "pki/testdata/crl_unittest/invalid_v3.pem", "pki/testdata/crl_unittest/revoked.pem", "pki/testdata/crl_unittest/revoked_fake_crlentryextension.pem", "pki/testdata/crl_unittest/revoked_generalized_revocationdate.pem", "pki/testdata/crl_unittest/revoked_key_rollover.pem", "pki/testdata/crl_unittest/revoked_no_nextupdate.pem", "pki/testdata/name_constraints_unittest/directoryname-excludeall.pem", "pki/testdata/name_constraints_unittest/directoryname-excluded.pem", "pki/testdata/name_constraints_unittest/directoryname.pem", "pki/testdata/name_constraints_unittest/directoryname_and_dnsname.pem", "pki/testdata/name_constraints_unittest/directoryname_and_dnsname_and_ipaddress.pem", "pki/testdata/name_constraints_unittest/dnsname-exclude_dot.pem", "pki/testdata/name_constraints_unittest/dnsname-excludeall.pem", "pki/testdata/name_constraints_unittest/dnsname-excluded.pem", "pki/testdata/name_constraints_unittest/dnsname-excluded_with_leading_dot.pem", "pki/testdata/name_constraints_unittest/dnsname-permitted_two_dot.pem", "pki/testdata/name_constraints_unittest/dnsname-permitted_with_leading_dot.pem", "pki/testdata/name_constraints_unittest/dnsname-with_max.pem", "pki/testdata/name_constraints_unittest/dnsname-with_min_0.pem", "pki/testdata/name_constraints_unittest/dnsname-with_min_0_and_max.pem", "pki/testdata/name_constraints_unittest/dnsname-with_min_1.pem", "pki/testdata/name_constraints_unittest/dnsname-with_min_1_and_max.pem", "pki/testdata/name_constraints_unittest/dnsname.pem", "pki/testdata/name_constraints_unittest/dnsname2.pem", "pki/testdata/name_constraints_unittest/edipartyname-excluded.pem", "pki/testdata/name_constraints_unittest/edipartyname-permitted.pem", "pki/testdata/name_constraints_unittest/invalid-empty_excluded_subtree.pem", "pki/testdata/name_constraints_unittest/invalid-empty_permitted_subtree.pem", "pki/testdata/name_constraints_unittest/invalid-no_subtrees.pem", "pki/testdata/name_constraints_unittest/ipaddress-excludeall.pem", "pki/testdata/name_constraints_unittest/ipaddress-excluded.pem", "pki/testdata/name_constraints_unittest/ipaddress-invalid_addr.pem", "pki/testdata/name_constraints_unittest/ipaddress-invalid_mask_not_contiguous_1.pem", "pki/testdata/name_constraints_unittest/ipaddress-invalid_mask_not_contiguous_2.pem", "pki/testdata/name_constraints_unittest/ipaddress-invalid_mask_not_contiguous_3.pem", "pki/testdata/name_constraints_unittest/ipaddress-invalid_mask_not_contiguous_4.pem", "pki/testdata/name_constraints_unittest/ipaddress-mapped_addrs.pem", "pki/testdata/name_constraints_unittest/ipaddress-permit_all.pem", "pki/testdata/name_constraints_unittest/ipaddress-permit_prefix1.pem", "pki/testdata/name_constraints_unittest/ipaddress-permit_prefix31.pem", "pki/testdata/name_constraints_unittest/ipaddress-permit_singlehost.pem", "pki/testdata/name_constraints_unittest/ipaddress.pem", "pki/testdata/name_constraints_unittest/name-ca.pem", "pki/testdata/name_constraints_unittest/name-de.pem", "pki/testdata/name_constraints_unittest/name-empty.pem", "pki/testdata/name_constraints_unittest/name-jp-tokyo.pem", "pki/testdata/name_constraints_unittest/name-jp.pem", "pki/testdata/name_constraints_unittest/name-us-arizona-*******.pem", "pki/testdata/name_constraints_unittest/name-us-arizona-***********.pem", "pki/testdata/name_constraints_unittest/name-us-arizona-email-invalidstring.pem", "pki/testdata/name_constraints_unittest/name-us-arizona-email-localpartcase.pem", "pki/testdata/name_constraints_unittest/name-us-arizona-email-multiple.pem", "pki/testdata/name_constraints_unittest/name-us-arizona-email.pem", "pki/testdata/name_constraints_unittest/name-us-arizona-foo.com.pem", "pki/testdata/name_constraints_unittest/name-us-arizona-ipv6.pem", "pki/testdata/name_constraints_unittest/name-us-arizona-permitted.example.com.pem", "pki/testdata/name_constraints_unittest/name-us-arizona.pem", "pki/testdata/name_constraints_unittest/name-us-california-***********.pem", "pki/testdata/name_constraints_unittest/name-us-california-mountain_view.pem", "pki/testdata/name_constraints_unittest/name-us-california-permitted.example.com.pem", "pki/testdata/name_constraints_unittest/name-us-california.pem", "pki/testdata/name_constraints_unittest/name-us.pem", "pki/testdata/name_constraints_unittest/othername-excluded.pem", "pki/testdata/name_constraints_unittest/othername-permitted.pem", "pki/testdata/name_constraints_unittest/registeredid-excluded.pem", "pki/testdata/name_constraints_unittest/registeredid-permitted.pem", "pki/testdata/name_constraints_unittest/rfc822name-excluded-empty.pem", "pki/testdata/name_constraints_unittest/rfc822name-excluded-hostname.pem", "pki/testdata/name_constraints_unittest/rfc822name-excluded-hostnamewithat.pem", "pki/testdata/name_constraints_unittest/rfc822name-excluded-ipv4.pem", "pki/testdata/name_constraints_unittest/rfc822name-excluded-quoted.pem", "pki/testdata/name_constraints_unittest/rfc822name-excluded-subdomains.pem", "pki/testdata/name_constraints_unittest/rfc822name-excluded.pem", "pki/testdata/name_constraints_unittest/rfc822name-permitted-empty.pem", "pki/testdata/name_constraints_unittest/rfc822name-permitted-hostname.pem", "pki/testdata/name_constraints_unittest/rfc822name-permitted-hostnamewithat.pem", "pki/testdata/name_constraints_unittest/rfc822name-permitted-ipv4.pem", "pki/testdata/name_constraints_unittest/rfc822name-permitted-quoted.pem", "pki/testdata/name_constraints_unittest/rfc822name-permitted-subdomains.pem", "pki/testdata/name_constraints_unittest/rfc822name-permitted.pem", "pki/testdata/name_constraints_unittest/san-directoryname.pem", "pki/testdata/name_constraints_unittest/san-dnsname.pem", "pki/testdata/name_constraints_unittest/san-edipartyname.pem", "pki/testdata/name_constraints_unittest/san-excluded-directoryname.pem", "pki/testdata/name_constraints_unittest/san-excluded-dnsname.pem", "pki/testdata/name_constraints_unittest/san-excluded-ipaddress.pem", "pki/testdata/name_constraints_unittest/san-invalid-empty.pem", "pki/testdata/name_constraints_unittest/san-invalid-ipaddress.pem", "pki/testdata/name_constraints_unittest/san-ipaddress4.pem", "pki/testdata/name_constraints_unittest/san-ipaddress6.pem", "pki/testdata/name_constraints_unittest/san-othername.pem", "pki/testdata/name_constraints_unittest/san-permitted.pem", "pki/testdata/name_constraints_unittest/san-registeredid.pem", "pki/testdata/name_constraints_unittest/san-rfc822name-domaincase.pem", "pki/testdata/name_constraints_unittest/san-rfc822name-empty-localpart.pem", "pki/testdata/name_constraints_unittest/san-rfc822name-empty.pem", "pki/testdata/name_constraints_unittest/san-rfc822name-ipv4.pem", "pki/testdata/name_constraints_unittest/san-rfc822name-localpartcase.pem", "pki/testdata/name_constraints_unittest/san-rfc822name-multiple.pem", "pki/testdata/name_constraints_unittest/san-rfc822name-no-at.pem", "pki/testdata/name_constraints_unittest/san-rfc822name-quoted.pem", "pki/testdata/name_constraints_unittest/san-rfc822name-subdomain-no-at.pem", "pki/testdata/name_constraints_unittest/san-rfc822name-subdomain-two-ats.pem", "pki/testdata/name_constraints_unittest/san-rfc822name-subdomain.pem", "pki/testdata/name_constraints_unittest/san-rfc822name-subdomaincase.pem", "pki/testdata/name_constraints_unittest/san-rfc822name-two-ats.pem", "pki/testdata/name_constraints_unittest/san-rfc822name.pem", "pki/testdata/name_constraints_unittest/san-uri.pem", "pki/testdata/name_constraints_unittest/san-x400address.pem", "pki/testdata/name_constraints_unittest/uri-excluded.pem", "pki/testdata/name_constraints_unittest/uri-permitted.pem", "pki/testdata/name_constraints_unittest/x400address-excluded.pem", "pki/testdata/name_constraints_unittest/x400address-permitted.pem", "pki/testdata/nist-pkits/certs/AllCertificatesNoPoliciesTest2EE.crt", "pki/testdata/nist-pkits/certs/AllCertificatesSamePoliciesTest10EE.crt", "pki/testdata/nist-pkits/certs/AllCertificatesSamePoliciesTest13EE.crt", "pki/testdata/nist-pkits/certs/AllCertificatesanyPolicyTest11EE.crt", "pki/testdata/nist-pkits/certs/AnyPolicyTest14EE.crt", "pki/testdata/nist-pkits/certs/BadCRLIssuerNameCACert.crt", "pki/testdata/nist-pkits/certs/BadCRLSignatureCACert.crt", "pki/testdata/nist-pkits/certs/BadSignedCACert.crt", "pki/testdata/nist-pkits/certs/BadnotAfterDateCACert.crt", "pki/testdata/nist-pkits/certs/BadnotBeforeDateCACert.crt", "pki/testdata/nist-pkits/certs/BasicSelfIssuedCRLSigningKeyCACert.crt", "pki/testdata/nist-pkits/certs/BasicSelfIssuedCRLSigningKeyCRLCert.crt", "pki/testdata/nist-pkits/certs/BasicSelfIssuedNewKeyCACert.crt", "pki/testdata/nist-pkits/certs/BasicSelfIssuedNewKeyOldWithNewCACert.crt", "pki/testdata/nist-pkits/certs/BasicSelfIssuedOldKeyCACert.crt", "pki/testdata/nist-pkits/certs/BasicSelfIssuedOldKeyNewWithOldCACert.crt", "pki/testdata/nist-pkits/certs/CPSPointerQualifierTest20EE.crt", "pki/testdata/nist-pkits/certs/DSACACert.crt", "pki/testdata/nist-pkits/certs/DSAParametersInheritedCACert.crt", "pki/testdata/nist-pkits/certs/DifferentPoliciesTest12EE.crt", "pki/testdata/nist-pkits/certs/DifferentPoliciesTest3EE.crt", "pki/testdata/nist-pkits/certs/DifferentPoliciesTest4EE.crt", "pki/testdata/nist-pkits/certs/DifferentPoliciesTest5EE.crt", "pki/testdata/nist-pkits/certs/DifferentPoliciesTest7EE.crt", "pki/testdata/nist-pkits/certs/DifferentPoliciesTest8EE.crt", "pki/testdata/nist-pkits/certs/DifferentPoliciesTest9EE.crt", "pki/testdata/nist-pkits/certs/GeneralizedTimeCRLnextUpdateCACert.crt", "pki/testdata/nist-pkits/certs/GoodCACert.crt", "pki/testdata/nist-pkits/certs/GoodsubCACert.crt", "pki/testdata/nist-pkits/certs/GoodsubCAPanyPolicyMapping1to2CACert.crt", "pki/testdata/nist-pkits/certs/InvalidBadCRLIssuerNameTest5EE.crt", "pki/testdata/nist-pkits/certs/InvalidBadCRLSignatureTest4EE.crt", "pki/testdata/nist-pkits/certs/InvalidBasicSelfIssuedCRLSigningKeyTest7EE.crt", "pki/testdata/nist-pkits/certs/InvalidBasicSelfIssuedCRLSigningKeyTest8EE.crt", "pki/testdata/nist-pkits/certs/InvalidBasicSelfIssuedNewWithOldTest5EE.crt", "pki/testdata/nist-pkits/certs/InvalidBasicSelfIssuedOldWithNewTest2EE.crt", "pki/testdata/nist-pkits/certs/InvalidCASignatureTest2EE.crt", "pki/testdata/nist-pkits/certs/InvalidCAnotAfterDateTest5EE.crt", "pki/testdata/nist-pkits/certs/InvalidCAnotBeforeDateTest1EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNSnameConstraintsTest31EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNSnameConstraintsTest33EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNSnameConstraintsTest38EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNandRFC822nameConstraintsTest28EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNandRFC822nameConstraintsTest29EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNnameConstraintsTest10EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNnameConstraintsTest12EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNnameConstraintsTest13EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNnameConstraintsTest15EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNnameConstraintsTest16EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNnameConstraintsTest17EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNnameConstraintsTest20EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNnameConstraintsTest2EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNnameConstraintsTest3EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNnameConstraintsTest7EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNnameConstraintsTest8EE.crt", "pki/testdata/nist-pkits/certs/InvalidDNnameConstraintsTest9EE.crt", "pki/testdata/nist-pkits/certs/InvalidDSASignatureTest6EE.crt", "pki/testdata/nist-pkits/certs/InvalidEESignatureTest3EE.crt", "pki/testdata/nist-pkits/certs/InvalidEEnotAfterDateTest6EE.crt", "pki/testdata/nist-pkits/certs/InvalidEEnotBeforeDateTest2EE.crt", "pki/testdata/nist-pkits/certs/InvalidIDPwithindirectCRLTest23EE.crt", "pki/testdata/nist-pkits/certs/InvalidIDPwithindirectCRLTest26EE.crt", "pki/testdata/nist-pkits/certs/InvalidLongSerialNumberTest18EE.crt", "pki/testdata/nist-pkits/certs/InvalidMappingFromanyPolicyTest7EE.crt", "pki/testdata/nist-pkits/certs/InvalidMappingToanyPolicyTest8EE.crt", "pki/testdata/nist-pkits/certs/InvalidMissingCRLTest1EE.crt", "pki/testdata/nist-pkits/certs/InvalidMissingbasicConstraintsTest1EE.crt", "pki/testdata/nist-pkits/certs/InvalidNameChainingOrderTest2EE.crt", "pki/testdata/nist-pkits/certs/InvalidNameChainingTest1EE.crt", "pki/testdata/nist-pkits/certs/InvalidNegativeSerialNumberTest15EE.crt", "pki/testdata/nist-pkits/certs/InvalidOldCRLnextUpdateTest11EE.crt", "pki/testdata/nist-pkits/certs/InvalidPolicyMappingTest10EE.crt", "pki/testdata/nist-pkits/certs/InvalidPolicyMappingTest2EE.crt", "pki/testdata/nist-pkits/certs/InvalidPolicyMappingTest4EE.crt", "pki/testdata/nist-pkits/certs/InvalidRFC822nameConstraintsTest22EE.crt", "pki/testdata/nist-pkits/certs/InvalidRFC822nameConstraintsTest24EE.crt", "pki/testdata/nist-pkits/certs/InvalidRFC822nameConstraintsTest26EE.crt", "pki/testdata/nist-pkits/certs/InvalidRevokedCATest2EE.crt", "pki/testdata/nist-pkits/certs/InvalidRevokedEETest3EE.crt", "pki/testdata/nist-pkits/certs/InvalidSelfIssuedinhibitAnyPolicyTest10EE.crt", "pki/testdata/nist-pkits/certs/InvalidSelfIssuedinhibitAnyPolicyTest8EE.crt", "pki/testdata/nist-pkits/certs/InvalidSelfIssuedinhibitPolicyMappingTest10EE.crt", "pki/testdata/nist-pkits/certs/InvalidSelfIssuedinhibitPolicyMappingTest11EE.crt", "pki/testdata/nist-pkits/certs/InvalidSelfIssuedinhibitPolicyMappingTest8EE.crt", "pki/testdata/nist-pkits/certs/InvalidSelfIssuedinhibitPolicyMappingTest9EE.crt", "pki/testdata/nist-pkits/certs/InvalidSelfIssuedpathLenConstraintTest16EE.crt", "pki/testdata/nist-pkits/certs/InvalidSelfIssuedrequireExplicitPolicyTest7EE.crt", "pki/testdata/nist-pkits/certs/InvalidSelfIssuedrequireExplicitPolicyTest8EE.crt", "pki/testdata/nist-pkits/certs/InvalidSeparateCertificateandCRLKeysTest20EE.crt", "pki/testdata/nist-pkits/certs/InvalidSeparateCertificateandCRLKeysTest21EE.crt", "pki/testdata/nist-pkits/certs/InvalidURInameConstraintsTest35EE.crt", "pki/testdata/nist-pkits/certs/InvalidURInameConstraintsTest37EE.crt", "pki/testdata/nist-pkits/certs/InvalidUnknownCRLEntryExtensionTest8EE.crt", "pki/testdata/nist-pkits/certs/InvalidUnknownCRLExtensionTest10EE.crt", "pki/testdata/nist-pkits/certs/InvalidUnknownCRLExtensionTest9EE.crt", "pki/testdata/nist-pkits/certs/InvalidUnknownCriticalCertificateExtensionTest2EE.crt", "pki/testdata/nist-pkits/certs/InvalidWrongCRLTest6EE.crt", "pki/testdata/nist-pkits/certs/InvalidcAFalseTest2EE.crt", "pki/testdata/nist-pkits/certs/InvalidcAFalseTest3EE.crt", "pki/testdata/nist-pkits/certs/InvalidcRLIssuerTest27EE.crt", "pki/testdata/nist-pkits/certs/InvalidcRLIssuerTest31EE.crt", "pki/testdata/nist-pkits/certs/InvalidcRLIssuerTest32EE.crt", "pki/testdata/nist-pkits/certs/InvalidcRLIssuerTest34EE.crt", "pki/testdata/nist-pkits/certs/InvalidcRLIssuerTest35EE.crt", "pki/testdata/nist-pkits/certs/InvaliddeltaCRLIndicatorNoBaseTest1EE.crt", "pki/testdata/nist-pkits/certs/InvaliddeltaCRLTest10EE.crt", "pki/testdata/nist-pkits/certs/InvaliddeltaCRLTest3EE.crt", "pki/testdata/nist-pkits/certs/InvaliddeltaCRLTest4EE.crt", "pki/testdata/nist-pkits/certs/InvaliddeltaCRLTest6EE.crt", "pki/testdata/nist-pkits/certs/InvaliddeltaCRLTest9EE.crt", "pki/testdata/nist-pkits/certs/InvaliddistributionPointTest2EE.crt", "pki/testdata/nist-pkits/certs/InvaliddistributionPointTest3EE.crt", "pki/testdata/nist-pkits/certs/InvaliddistributionPointTest6EE.crt", "pki/testdata/nist-pkits/certs/InvaliddistributionPointTest8EE.crt", "pki/testdata/nist-pkits/certs/InvaliddistributionPointTest9EE.crt", "pki/testdata/nist-pkits/certs/InvalidinhibitAnyPolicyTest1EE.crt", "pki/testdata/nist-pkits/certs/InvalidinhibitAnyPolicyTest4EE.crt", "pki/testdata/nist-pkits/certs/InvalidinhibitAnyPolicyTest5EE.crt", "pki/testdata/nist-pkits/certs/InvalidinhibitAnyPolicyTest6EE.crt", "pki/testdata/nist-pkits/certs/InvalidinhibitPolicyMappingTest1EE.crt", "pki/testdata/nist-pkits/certs/InvalidinhibitPolicyMappingTest3EE.crt", "pki/testdata/nist-pkits/certs/InvalidinhibitPolicyMappingTest5EE.crt", "pki/testdata/nist-pkits/certs/InvalidinhibitPolicyMappingTest6EE.crt", "pki/testdata/nist-pkits/certs/InvalidkeyUsageCriticalcRLSignFalseTest4EE.crt", "pki/testdata/nist-pkits/certs/InvalidkeyUsageCriticalkeyCertSignFalseTest1EE.crt", "pki/testdata/nist-pkits/certs/InvalidkeyUsageNotCriticalcRLSignFalseTest5EE.crt", "pki/testdata/nist-pkits/certs/InvalidkeyUsageNotCriticalkeyCertSignFalseTest2EE.crt", "pki/testdata/nist-pkits/certs/InvalidonlyContainsAttributeCertsTest14EE.crt", "pki/testdata/nist-pkits/certs/InvalidonlyContainsCACertsTest12EE.crt", "pki/testdata/nist-pkits/certs/InvalidonlyContainsUserCertsTest11EE.crt", "pki/testdata/nist-pkits/certs/InvalidonlySomeReasonsTest15EE.crt", "pki/testdata/nist-pkits/certs/InvalidonlySomeReasonsTest16EE.crt", "pki/testdata/nist-pkits/certs/InvalidonlySomeReasonsTest17EE.crt", "pki/testdata/nist-pkits/certs/InvalidonlySomeReasonsTest20EE.crt", "pki/testdata/nist-pkits/certs/InvalidonlySomeReasonsTest21EE.crt", "pki/testdata/nist-pkits/certs/InvalidpathLenConstraintTest10EE.crt", "pki/testdata/nist-pkits/certs/InvalidpathLenConstraintTest11EE.crt", "pki/testdata/nist-pkits/certs/InvalidpathLenConstraintTest12EE.crt", "pki/testdata/nist-pkits/certs/InvalidpathLenConstraintTest5EE.crt", "pki/testdata/nist-pkits/certs/InvalidpathLenConstraintTest6EE.crt", "pki/testdata/nist-pkits/certs/InvalidpathLenConstraintTest9EE.crt", "pki/testdata/nist-pkits/certs/Invalidpre2000CRLnextUpdateTest12EE.crt", "pki/testdata/nist-pkits/certs/Invalidpre2000UTCEEnotAfterDateTest7EE.crt", "pki/testdata/nist-pkits/certs/InvalidrequireExplicitPolicyTest3EE.crt", "pki/testdata/nist-pkits/certs/InvalidrequireExplicitPolicyTest5EE.crt", "pki/testdata/nist-pkits/certs/LongSerialNumberCACert.crt", "pki/testdata/nist-pkits/certs/Mapping1to2CACert.crt", "pki/testdata/nist-pkits/certs/MappingFromanyPolicyCACert.crt", "pki/testdata/nist-pkits/certs/MappingToanyPolicyCACert.crt", "pki/testdata/nist-pkits/certs/MissingbasicConstraintsCACert.crt", "pki/testdata/nist-pkits/certs/NameOrderingCACert.crt", "pki/testdata/nist-pkits/certs/NegativeSerialNumberCACert.crt", "pki/testdata/nist-pkits/certs/NoCRLCACert.crt", "pki/testdata/nist-pkits/certs/NoPoliciesCACert.crt", "pki/testdata/nist-pkits/certs/NoissuingDistributionPointCACert.crt", "pki/testdata/nist-pkits/certs/OldCRLnextUpdateCACert.crt", "pki/testdata/nist-pkits/certs/OverlappingPoliciesTest6EE.crt", "pki/testdata/nist-pkits/certs/P12Mapping1to3CACert.crt", "pki/testdata/nist-pkits/certs/P12Mapping1to3subCACert.crt", "pki/testdata/nist-pkits/certs/P12Mapping1to3subsubCACert.crt", "pki/testdata/nist-pkits/certs/P1Mapping1to234CACert.crt", "pki/testdata/nist-pkits/certs/P1Mapping1to234subCACert.crt", "pki/testdata/nist-pkits/certs/P1anyPolicyMapping1to2CACert.crt", "pki/testdata/nist-pkits/certs/PanyPolicyMapping1to2CACert.crt", "pki/testdata/nist-pkits/certs/PoliciesP1234CACert.crt", "pki/testdata/nist-pkits/certs/PoliciesP1234subCAP123Cert.crt", "pki/testdata/nist-pkits/certs/PoliciesP1234subsubCAP123P12Cert.crt", "pki/testdata/nist-pkits/certs/PoliciesP123CACert.crt", "pki/testdata/nist-pkits/certs/PoliciesP123subCAP12Cert.crt", "pki/testdata/nist-pkits/certs/PoliciesP123subsubCAP12P1Cert.crt", "pki/testdata/nist-pkits/certs/PoliciesP123subsubCAP12P2Cert.crt", "pki/testdata/nist-pkits/certs/PoliciesP123subsubsubCAP12P2P1Cert.crt", "pki/testdata/nist-pkits/certs/PoliciesP12CACert.crt", "pki/testdata/nist-pkits/certs/PoliciesP12subCAP1Cert.crt", "pki/testdata/nist-pkits/certs/PoliciesP12subsubCAP1P2Cert.crt", "pki/testdata/nist-pkits/certs/PoliciesP2subCA2Cert.crt", "pki/testdata/nist-pkits/certs/PoliciesP2subCACert.crt", "pki/testdata/nist-pkits/certs/PoliciesP3CACert.crt", "pki/testdata/nist-pkits/certs/RFC3280MandatoryAttributeTypesCACert.crt", "pki/testdata/nist-pkits/certs/RFC3280OptionalAttributeTypesCACert.crt", "pki/testdata/nist-pkits/certs/RevokedsubCACert.crt", "pki/testdata/nist-pkits/certs/RolloverfromPrintableStringtoUTF8StringCACert.crt", "pki/testdata/nist-pkits/certs/SeparateCertificateandCRLKeysCA2CRLSigningCert.crt", "pki/testdata/nist-pkits/certs/SeparateCertificateandCRLKeysCA2CertificateSigningCACert.crt", "pki/testdata/nist-pkits/certs/SeparateCertificateandCRLKeysCRLSigningCert.crt", "pki/testdata/nist-pkits/certs/SeparateCertificateandCRLKeysCertificateSigningCACert.crt", "pki/testdata/nist-pkits/certs/TrustAnchorRootCertificate.crt", "pki/testdata/nist-pkits/certs/TwoCRLsCACert.crt", "pki/testdata/nist-pkits/certs/UIDCACert.crt", "pki/testdata/nist-pkits/certs/UTF8StringCaseInsensitiveMatchCACert.crt", "pki/testdata/nist-pkits/certs/UTF8StringEncodedNamesCACert.crt", "pki/testdata/nist-pkits/certs/UnknownCRLEntryExtensionCACert.crt", "pki/testdata/nist-pkits/certs/UnknownCRLExtensionCACert.crt", "pki/testdata/nist-pkits/certs/UserNoticeQualifierTest15EE.crt", "pki/testdata/nist-pkits/certs/UserNoticeQualifierTest16EE.crt", "pki/testdata/nist-pkits/certs/UserNoticeQualifierTest17EE.crt", "pki/testdata/nist-pkits/certs/UserNoticeQualifierTest18EE.crt", "pki/testdata/nist-pkits/certs/UserNoticeQualifierTest19EE.crt", "pki/testdata/nist-pkits/certs/ValidBasicSelfIssuedCRLSigningKeyTest6EE.crt", "pki/testdata/nist-pkits/certs/ValidBasicSelfIssuedNewWithOldTest3EE.crt", "pki/testdata/nist-pkits/certs/ValidBasicSelfIssuedNewWithOldTest4EE.crt", "pki/testdata/nist-pkits/certs/ValidBasicSelfIssuedOldWithNewTest1EE.crt", "pki/testdata/nist-pkits/certs/ValidCertificatePathTest1EE.crt", "pki/testdata/nist-pkits/certs/ValidDNSnameConstraintsTest30EE.crt", "pki/testdata/nist-pkits/certs/ValidDNSnameConstraintsTest32EE.crt", "pki/testdata/nist-pkits/certs/ValidDNandRFC822nameConstraintsTest27EE.crt", "pki/testdata/nist-pkits/certs/ValidDNnameConstraintsTest11EE.crt", "pki/testdata/nist-pkits/certs/ValidDNnameConstraintsTest14EE.crt", "pki/testdata/nist-pkits/certs/ValidDNnameConstraintsTest18EE.crt", "pki/testdata/nist-pkits/certs/ValidDNnameConstraintsTest19EE.crt", "pki/testdata/nist-pkits/certs/ValidDNnameConstraintsTest1EE.crt", "pki/testdata/nist-pkits/certs/ValidDNnameConstraintsTest4EE.crt", "pki/testdata/nist-pkits/certs/ValidDNnameConstraintsTest5EE.crt", "pki/testdata/nist-pkits/certs/ValidDNnameConstraintsTest6EE.crt", "pki/testdata/nist-pkits/certs/ValidDSAParameterInheritanceTest5EE.crt", "pki/testdata/nist-pkits/certs/ValidDSASignaturesTest4EE.crt", "pki/testdata/nist-pkits/certs/ValidGeneralizedTimeCRLnextUpdateTest13EE.crt", "pki/testdata/nist-pkits/certs/ValidGeneralizedTimenotAfterDateTest8EE.crt", "pki/testdata/nist-pkits/certs/ValidGeneralizedTimenotBeforeDateTest4EE.crt", "pki/testdata/nist-pkits/certs/ValidIDPwithindirectCRLTest22EE.crt", "pki/testdata/nist-pkits/certs/ValidIDPwithindirectCRLTest24EE.crt", "pki/testdata/nist-pkits/certs/ValidIDPwithindirectCRLTest25EE.crt", "pki/testdata/nist-pkits/certs/ValidLongSerialNumberTest16EE.crt", "pki/testdata/nist-pkits/certs/ValidLongSerialNumberTest17EE.crt", "pki/testdata/nist-pkits/certs/ValidNameChainingCapitalizationTest5EE.crt", "pki/testdata/nist-pkits/certs/ValidNameChainingWhitespaceTest3EE.crt", "pki/testdata/nist-pkits/certs/ValidNameChainingWhitespaceTest4EE.crt", "pki/testdata/nist-pkits/certs/ValidNameUIDsTest6EE.crt", "pki/testdata/nist-pkits/certs/ValidNegativeSerialNumberTest14EE.crt", "pki/testdata/nist-pkits/certs/ValidNoissuingDistributionPointTest10EE.crt", "pki/testdata/nist-pkits/certs/ValidPolicyMappingTest11EE.crt", "pki/testdata/nist-pkits/certs/ValidPolicyMappingTest12EE.crt", "pki/testdata/nist-pkits/certs/ValidPolicyMappingTest13EE.crt", "pki/testdata/nist-pkits/certs/ValidPolicyMappingTest14EE.crt", "pki/testdata/nist-pkits/certs/ValidPolicyMappingTest1EE.crt", "pki/testdata/nist-pkits/certs/ValidPolicyMappingTest3EE.crt", "pki/testdata/nist-pkits/certs/ValidPolicyMappingTest5EE.crt", "pki/testdata/nist-pkits/certs/ValidPolicyMappingTest6EE.crt", "pki/testdata/nist-pkits/certs/ValidPolicyMappingTest9EE.crt", "pki/testdata/nist-pkits/certs/ValidRFC3280MandatoryAttributeTypesTest7EE.crt", "pki/testdata/nist-pkits/certs/ValidRFC3280OptionalAttributeTypesTest8EE.crt", "pki/testdata/nist-pkits/certs/ValidRFC822nameConstraintsTest21EE.crt", "pki/testdata/nist-pkits/certs/ValidRFC822nameConstraintsTest23EE.crt", "pki/testdata/nist-pkits/certs/ValidRFC822nameConstraintsTest25EE.crt", "pki/testdata/nist-pkits/certs/ValidRolloverfromPrintableStringtoUTF8StringTest10EE.crt", "pki/testdata/nist-pkits/certs/ValidSelfIssuedinhibitAnyPolicyTest7EE.crt", "pki/testdata/nist-pkits/certs/ValidSelfIssuedinhibitAnyPolicyTest9EE.crt", "pki/testdata/nist-pkits/certs/ValidSelfIssuedinhibitPolicyMappingTest7EE.crt", "pki/testdata/nist-pkits/certs/ValidSelfIssuedpathLenConstraintTest15EE.crt", "pki/testdata/nist-pkits/certs/ValidSelfIssuedpathLenConstraintTest17EE.crt", "pki/testdata/nist-pkits/certs/ValidSelfIssuedrequireExplicitPolicyTest6EE.crt", "pki/testdata/nist-pkits/certs/ValidSeparateCertificateandCRLKeysTest19EE.crt", "pki/testdata/nist-pkits/certs/ValidTwoCRLsTest7EE.crt", "pki/testdata/nist-pkits/certs/ValidURInameConstraintsTest34EE.crt", "pki/testdata/nist-pkits/certs/ValidURInameConstraintsTest36EE.crt", "pki/testdata/nist-pkits/certs/ValidUTF8StringCaseInsensitiveMatchTest11EE.crt", "pki/testdata/nist-pkits/certs/ValidUTF8StringEncodedNamesTest9EE.crt", "pki/testdata/nist-pkits/certs/ValidUnknownNotCriticalCertificateExtensionTest1EE.crt", "pki/testdata/nist-pkits/certs/ValidbasicConstraintsNotCriticalTest4EE.crt", "pki/testdata/nist-pkits/certs/ValidcRLIssuerTest28EE.crt", "pki/testdata/nist-pkits/certs/ValidcRLIssuerTest29EE.crt", "pki/testdata/nist-pkits/certs/ValidcRLIssuerTest30EE.crt", "pki/testdata/nist-pkits/certs/ValidcRLIssuerTest33EE.crt", "pki/testdata/nist-pkits/certs/ValiddeltaCRLTest2EE.crt", "pki/testdata/nist-pkits/certs/ValiddeltaCRLTest5EE.crt", "pki/testdata/nist-pkits/certs/ValiddeltaCRLTest7EE.crt", "pki/testdata/nist-pkits/certs/ValiddeltaCRLTest8EE.crt", "pki/testdata/nist-pkits/certs/ValiddistributionPointTest1EE.crt", "pki/testdata/nist-pkits/certs/ValiddistributionPointTest4EE.crt", "pki/testdata/nist-pkits/certs/ValiddistributionPointTest5EE.crt", "pki/testdata/nist-pkits/certs/ValiddistributionPointTest7EE.crt", "pki/testdata/nist-pkits/certs/ValidinhibitAnyPolicyTest2EE.crt", "pki/testdata/nist-pkits/certs/ValidinhibitPolicyMappingTest2EE.crt", "pki/testdata/nist-pkits/certs/ValidinhibitPolicyMappingTest4EE.crt", "pki/testdata/nist-pkits/certs/ValidkeyUsageNotCriticalTest3EE.crt", "pki/testdata/nist-pkits/certs/ValidonlyContainsCACertsTest13EE.crt", "pki/testdata/nist-pkits/certs/ValidonlySomeReasonsTest18EE.crt", "pki/testdata/nist-pkits/certs/ValidonlySomeReasonsTest19EE.crt", "pki/testdata/nist-pkits/certs/ValidpathLenConstraintTest13EE.crt", "pki/testdata/nist-pkits/certs/ValidpathLenConstraintTest14EE.crt", "pki/testdata/nist-pkits/certs/ValidpathLenConstraintTest7EE.crt", "pki/testdata/nist-pkits/certs/ValidpathLenConstraintTest8EE.crt", "pki/testdata/nist-pkits/certs/Validpre2000UTCnotBeforeDateTest3EE.crt", "pki/testdata/nist-pkits/certs/ValidrequireExplicitPolicyTest1EE.crt", "pki/testdata/nist-pkits/certs/ValidrequireExplicitPolicyTest2EE.crt", "pki/testdata/nist-pkits/certs/ValidrequireExplicitPolicyTest4EE.crt", "pki/testdata/nist-pkits/certs/WrongCRLCACert.crt", "pki/testdata/nist-pkits/certs/anyPolicyCACert.crt", "pki/testdata/nist-pkits/certs/basicConstraintsCriticalcAFalseCACert.crt", "pki/testdata/nist-pkits/certs/basicConstraintsNotCriticalCACert.crt", "pki/testdata/nist-pkits/certs/basicConstraintsNotCriticalcAFalseCACert.crt", "pki/testdata/nist-pkits/certs/deltaCRLCA1Cert.crt", "pki/testdata/nist-pkits/certs/deltaCRLCA2Cert.crt", "pki/testdata/nist-pkits/certs/deltaCRLCA3Cert.crt", "pki/testdata/nist-pkits/certs/deltaCRLIndicatorNoBaseCACert.crt", "pki/testdata/nist-pkits/certs/distributionPoint1CACert.crt", "pki/testdata/nist-pkits/certs/distributionPoint2CACert.crt", "pki/testdata/nist-pkits/certs/indirectCRLCA1Cert.crt", "pki/testdata/nist-pkits/certs/indirectCRLCA2Cert.crt", "pki/testdata/nist-pkits/certs/indirectCRLCA3Cert.crt", "pki/testdata/nist-pkits/certs/indirectCRLCA3cRLIssuerCert.crt", "pki/testdata/nist-pkits/certs/indirectCRLCA4Cert.crt", "pki/testdata/nist-pkits/certs/indirectCRLCA4cRLIssuerCert.crt", "pki/testdata/nist-pkits/certs/indirectCRLCA5Cert.crt", "pki/testdata/nist-pkits/certs/indirectCRLCA6Cert.crt", "pki/testdata/nist-pkits/certs/inhibitAnyPolicy0CACert.crt", "pki/testdata/nist-pkits/certs/inhibitAnyPolicy1CACert.crt", "pki/testdata/nist-pkits/certs/inhibitAnyPolicy1SelfIssuedCACert.crt", "pki/testdata/nist-pkits/certs/inhibitAnyPolicy1SelfIssuedsubCA2Cert.crt", "pki/testdata/nist-pkits/certs/inhibitAnyPolicy1subCA1Cert.crt", "pki/testdata/nist-pkits/certs/inhibitAnyPolicy1subCA2Cert.crt", "pki/testdata/nist-pkits/certs/inhibitAnyPolicy1subCAIAP5Cert.crt", "pki/testdata/nist-pkits/certs/inhibitAnyPolicy1subsubCA2Cert.crt", "pki/testdata/nist-pkits/certs/inhibitAnyPolicy5CACert.crt", "pki/testdata/nist-pkits/certs/inhibitAnyPolicy5subCACert.crt", "pki/testdata/nist-pkits/certs/inhibitAnyPolicy5subsubCACert.crt", "pki/testdata/nist-pkits/certs/inhibitAnyPolicyTest3EE.crt", "pki/testdata/nist-pkits/certs/inhibitPolicyMapping0CACert.crt", "pki/testdata/nist-pkits/certs/inhibitPolicyMapping0subCACert.crt", "pki/testdata/nist-pkits/certs/inhibitPolicyMapping1P12CACert.crt", "pki/testdata/nist-pkits/certs/inhibitPolicyMapping1P12subCACert.crt", "pki/testdata/nist-pkits/certs/inhibitPolicyMapping1P12subCAIPM5Cert.crt", "pki/testdata/nist-pkits/certs/inhibitPolicyMapping1P12subsubCACert.crt", "pki/testdata/nist-pkits/certs/inhibitPolicyMapping1P12subsubCAIPM5Cert.crt", "pki/testdata/nist-pkits/certs/inhibitPolicyMapping1P1CACert.crt", "pki/testdata/nist-pkits/certs/inhibitPolicyMapping1P1SelfIssuedCACert.crt", "pki/testdata/nist-pkits/certs/inhibitPolicyMapping1P1SelfIssuedsubCACert.crt", "pki/testdata/nist-pkits/certs/inhibitPolicyMapping1P1subCACert.crt", "pki/testdata/nist-pkits/certs/inhibitPolicyMapping1P1subsubCACert.crt", "pki/testdata/nist-pkits/certs/inhibitPolicyMapping5CACert.crt", "pki/testdata/nist-pkits/certs/inhibitPolicyMapping5subCACert.crt", "pki/testdata/nist-pkits/certs/inhibitPolicyMapping5subsubCACert.crt", "pki/testdata/nist-pkits/certs/inhibitPolicyMapping5subsubsubCACert.crt", "pki/testdata/nist-pkits/certs/keyUsageCriticalcRLSignFalseCACert.crt", "pki/testdata/nist-pkits/certs/keyUsageCriticalkeyCertSignFalseCACert.crt", "pki/testdata/nist-pkits/certs/keyUsageNotCriticalCACert.crt", "pki/testdata/nist-pkits/certs/keyUsageNotCriticalcRLSignFalseCACert.crt", "pki/testdata/nist-pkits/certs/keyUsageNotCriticalkeyCertSignFalseCACert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsDN1CACert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsDN1SelfIssuedCACert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsDN1subCA1Cert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsDN1subCA2Cert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsDN1subCA3Cert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsDN2CACert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsDN3CACert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsDN3subCA1Cert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsDN3subCA2Cert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsDN4CACert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsDN5CACert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsDNS1CACert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsDNS2CACert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsRFC822CA1Cert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsRFC822CA2Cert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsRFC822CA3Cert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsURI1CACert.crt", "pki/testdata/nist-pkits/certs/nameConstraintsURI2CACert.crt", "pki/testdata/nist-pkits/certs/onlyContainsAttributeCertsCACert.crt", "pki/testdata/nist-pkits/certs/onlyContainsCACertsCACert.crt", "pki/testdata/nist-pkits/certs/onlyContainsUserCertsCACert.crt", "pki/testdata/nist-pkits/certs/onlySomeReasonsCA1Cert.crt", "pki/testdata/nist-pkits/certs/onlySomeReasonsCA2Cert.crt", "pki/testdata/nist-pkits/certs/onlySomeReasonsCA3Cert.crt", "pki/testdata/nist-pkits/certs/onlySomeReasonsCA4Cert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint0CACert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint0SelfIssuedCACert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint0subCA2Cert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint0subCACert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint1CACert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint1SelfIssuedCACert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint1SelfIssuedsubCACert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint1subCACert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint6CACert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint6subCA0Cert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint6subCA1Cert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint6subCA4Cert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint6subsubCA00Cert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint6subsubCA11Cert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint6subsubCA41Cert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint6subsubsubCA11XCert.crt", "pki/testdata/nist-pkits/certs/pathLenConstraint6subsubsubCA41XCert.crt", "pki/testdata/nist-pkits/certs/pre2000CRLnextUpdateCACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy0CACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy0subCACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy0subsubCACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy0subsubsubCACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy10CACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy10subCACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy10subsubCACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy10subsubsubCACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy2CACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy2SelfIssuedCACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy2SelfIssuedsubCACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy2subCACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy4CACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy4subCACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy4subsubCACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy4subsubsubCACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy5CACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy5subCACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy5subsubCACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy5subsubsubCACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy7CACert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy7subCARE2Cert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy7subsubCARE2RE4Cert.crt", "pki/testdata/nist-pkits/certs/requireExplicitPolicy7subsubsubCARE2RE4Cert.crt", "pki/testdata/nist-pkits/crls/BadCRLIssuerNameCACRL.crl", "pki/testdata/nist-pkits/crls/BadCRLSignatureCACRL.crl", "pki/testdata/nist-pkits/crls/BadSignedCACRL.crl", "pki/testdata/nist-pkits/crls/BadnotAfterDateCACRL.crl", "pki/testdata/nist-pkits/crls/BadnotBeforeDateCACRL.crl", "pki/testdata/nist-pkits/crls/BasicSelfIssuedCRLSigningKeyCACRL.crl", "pki/testdata/nist-pkits/crls/BasicSelfIssuedCRLSigningKeyCRLCertCRL.crl", "pki/testdata/nist-pkits/crls/BasicSelfIssuedNewKeyCACRL.crl", "pki/testdata/nist-pkits/crls/BasicSelfIssuedOldKeyCACRL.crl", "pki/testdata/nist-pkits/crls/BasicSelfIssuedOldKeySelfIssuedCertCRL.crl", "pki/testdata/nist-pkits/crls/DSACACRL.crl", "pki/testdata/nist-pkits/crls/DSAParametersInheritedCACRL.crl", "pki/testdata/nist-pkits/crls/GeneralizedTimeCRLnextUpdateCACRL.crl", "pki/testdata/nist-pkits/crls/GoodCACRL.crl", "pki/testdata/nist-pkits/crls/GoodsubCACRL.crl", "pki/testdata/nist-pkits/crls/GoodsubCAPanyPolicyMapping1to2CACRL.crl", "pki/testdata/nist-pkits/crls/LongSerialNumberCACRL.crl", "pki/testdata/nist-pkits/crls/Mapping1to2CACRL.crl", "pki/testdata/nist-pkits/crls/MappingFromanyPolicyCACRL.crl", "pki/testdata/nist-pkits/crls/MappingToanyPolicyCACRL.crl", "pki/testdata/nist-pkits/crls/MissingbasicConstraintsCACRL.crl", "pki/testdata/nist-pkits/crls/NameOrderCACRL.crl", "pki/testdata/nist-pkits/crls/NegativeSerialNumberCACRL.crl", "pki/testdata/nist-pkits/crls/NoPoliciesCACRL.crl", "pki/testdata/nist-pkits/crls/NoissuingDistributionPointCACRL.crl", "pki/testdata/nist-pkits/crls/OldCRLnextUpdateCACRL.crl", "pki/testdata/nist-pkits/crls/P12Mapping1to3CACRL.crl", "pki/testdata/nist-pkits/crls/P12Mapping1to3subCACRL.crl", "pki/testdata/nist-pkits/crls/P12Mapping1to3subsubCACRL.crl", "pki/testdata/nist-pkits/crls/P1Mapping1to234CACRL.crl", "pki/testdata/nist-pkits/crls/P1Mapping1to234subCACRL.crl", "pki/testdata/nist-pkits/crls/P1anyPolicyMapping1to2CACRL.crl", "pki/testdata/nist-pkits/crls/PanyPolicyMapping1to2CACRL.crl", "pki/testdata/nist-pkits/crls/PoliciesP1234CACRL.crl", "pki/testdata/nist-pkits/crls/PoliciesP1234subCAP123CRL.crl", "pki/testdata/nist-pkits/crls/PoliciesP1234subsubCAP123P12CRL.crl", "pki/testdata/nist-pkits/crls/PoliciesP123CACRL.crl", "pki/testdata/nist-pkits/crls/PoliciesP123subCAP12CRL.crl", "pki/testdata/nist-pkits/crls/PoliciesP123subsubCAP12P1CRL.crl", "pki/testdata/nist-pkits/crls/PoliciesP123subsubCAP2P2CRL.crl", "pki/testdata/nist-pkits/crls/PoliciesP123subsubsubCAP12P2P1CRL.crl", "pki/testdata/nist-pkits/crls/PoliciesP12CACRL.crl", "pki/testdata/nist-pkits/crls/PoliciesP12subCAP1CRL.crl", "pki/testdata/nist-pkits/crls/PoliciesP12subsubCAP1P2CRL.crl", "pki/testdata/nist-pkits/crls/PoliciesP2subCA2CRL.crl", "pki/testdata/nist-pkits/crls/PoliciesP2subCACRL.crl", "pki/testdata/nist-pkits/crls/PoliciesP3CACRL.crl", "pki/testdata/nist-pkits/crls/RFC3280MandatoryAttributeTypesCACRL.crl", "pki/testdata/nist-pkits/crls/RFC3280OptionalAttributeTypesCACRL.crl", "pki/testdata/nist-pkits/crls/RevokedsubCACRL.crl", "pki/testdata/nist-pkits/crls/RolloverfromPrintableStringtoUTF8StringCACRL.crl", "pki/testdata/nist-pkits/crls/SeparateCertificateandCRLKeysCA2CRL.crl", "pki/testdata/nist-pkits/crls/SeparateCertificateandCRLKeysCRL.crl", "pki/testdata/nist-pkits/crls/TrustAnchorRootCRL.crl", "pki/testdata/nist-pkits/crls/TwoCRLsCABadCRL.crl", "pki/testdata/nist-pkits/crls/TwoCRLsCAGoodCRL.crl", "pki/testdata/nist-pkits/crls/UIDCACRL.crl", "pki/testdata/nist-pkits/crls/UTF8StringCaseInsensitiveMatchCACRL.crl", "pki/testdata/nist-pkits/crls/UTF8StringEncodedNamesCACRL.crl", "pki/testdata/nist-pkits/crls/UnknownCRLEntryExtensionCACRL.crl", "pki/testdata/nist-pkits/crls/UnknownCRLExtensionCACRL.crl", "pki/testdata/nist-pkits/crls/WrongCRLCACRL.crl", "pki/testdata/nist-pkits/crls/anyPolicyCACRL.crl", "pki/testdata/nist-pkits/crls/basicConstraintsCriticalcAFalseCACRL.crl", "pki/testdata/nist-pkits/crls/basicConstraintsNotCriticalCACRL.crl", "pki/testdata/nist-pkits/crls/basicConstraintsNotCriticalcAFalseCACRL.crl", "pki/testdata/nist-pkits/crls/deltaCRLCA1CRL.crl", "pki/testdata/nist-pkits/crls/deltaCRLCA1deltaCRL.crl", "pki/testdata/nist-pkits/crls/deltaCRLCA2CRL.crl", "pki/testdata/nist-pkits/crls/deltaCRLCA2deltaCRL.crl", "pki/testdata/nist-pkits/crls/deltaCRLCA3CRL.crl", "pki/testdata/nist-pkits/crls/deltaCRLCA3deltaCRL.crl", "pki/testdata/nist-pkits/crls/deltaCRLIndicatorNoBaseCACRL.crl", "pki/testdata/nist-pkits/crls/distributionPoint1CACRL.crl", "pki/testdata/nist-pkits/crls/distributionPoint2CACRL.crl", "pki/testdata/nist-pkits/crls/indirectCRLCA1CRL.crl", "pki/testdata/nist-pkits/crls/indirectCRLCA3CRL.crl", "pki/testdata/nist-pkits/crls/indirectCRLCA3cRLIssuerCRL.crl", "pki/testdata/nist-pkits/crls/indirectCRLCA4cRLIssuerCRL.crl", "pki/testdata/nist-pkits/crls/indirectCRLCA5CRL.crl", "pki/testdata/nist-pkits/crls/inhibitAnyPolicy0CACRL.crl", "pki/testdata/nist-pkits/crls/inhibitAnyPolicy1CACRL.crl", "pki/testdata/nist-pkits/crls/inhibitAnyPolicy1subCA1CRL.crl", "pki/testdata/nist-pkits/crls/inhibitAnyPolicy1subCA2CRL.crl", "pki/testdata/nist-pkits/crls/inhibitAnyPolicy1subCAIAP5CRL.crl", "pki/testdata/nist-pkits/crls/inhibitAnyPolicy1subsubCA2CRL.crl", "pki/testdata/nist-pkits/crls/inhibitAnyPolicy5CACRL.crl", "pki/testdata/nist-pkits/crls/inhibitAnyPolicy5subCACRL.crl", "pki/testdata/nist-pkits/crls/inhibitAnyPolicy5subsubCACRL.crl", "pki/testdata/nist-pkits/crls/inhibitPolicyMapping0CACRL.crl", "pki/testdata/nist-pkits/crls/inhibitPolicyMapping0subCACRL.crl", "pki/testdata/nist-pkits/crls/inhibitPolicyMapping1P12CACRL.crl", "pki/testdata/nist-pkits/crls/inhibitPolicyMapping1P12subCACRL.crl", "pki/testdata/nist-pkits/crls/inhibitPolicyMapping1P12subCAIPM5CRL.crl", "pki/testdata/nist-pkits/crls/inhibitPolicyMapping1P12subsubCACRL.crl", "pki/testdata/nist-pkits/crls/inhibitPolicyMapping1P12subsubCAIPM5CRL.crl", "pki/testdata/nist-pkits/crls/inhibitPolicyMapping1P1CACRL.crl", "pki/testdata/nist-pkits/crls/inhibitPolicyMapping1P1subCACRL.crl", "pki/testdata/nist-pkits/crls/inhibitPolicyMapping1P1subsubCACRL.crl", "pki/testdata/nist-pkits/crls/inhibitPolicyMapping5CACRL.crl", "pki/testdata/nist-pkits/crls/inhibitPolicyMapping5subCACRL.crl", "pki/testdata/nist-pkits/crls/inhibitPolicyMapping5subsubCACRL.crl", "pki/testdata/nist-pkits/crls/inhibitPolicyMapping5subsubsubCACRL.crl", "pki/testdata/nist-pkits/crls/keyUsageCriticalcRLSignFalseCACRL.crl", "pki/testdata/nist-pkits/crls/keyUsageCriticalkeyCertSignFalseCACRL.crl", "pki/testdata/nist-pkits/crls/keyUsageNotCriticalCACRL.crl", "pki/testdata/nist-pkits/crls/keyUsageNotCriticalcRLSignFalseCACRL.crl", "pki/testdata/nist-pkits/crls/keyUsageNotCriticalkeyCertSignFalseCACRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsDN1CACRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsDN1subCA1CRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsDN1subCA2CRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsDN1subCA3CRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsDN2CACRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsDN3CACRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsDN3subCA1CRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsDN3subCA2CRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsDN4CACRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsDN5CACRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsDNS1CACRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsDNS2CACRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsRFC822CA1CRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsRFC822CA2CRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsRFC822CA3CRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsURI1CACRL.crl", "pki/testdata/nist-pkits/crls/nameConstraintsURI2CACRL.crl", "pki/testdata/nist-pkits/crls/onlyContainsAttributeCertsCACRL.crl", "pki/testdata/nist-pkits/crls/onlyContainsCACertsCACRL.crl", "pki/testdata/nist-pkits/crls/onlyContainsUserCertsCACRL.crl", "pki/testdata/nist-pkits/crls/onlySomeReasonsCA1compromiseCRL.crl", "pki/testdata/nist-pkits/crls/onlySomeReasonsCA1otherreasonsCRL.crl", "pki/testdata/nist-pkits/crls/onlySomeReasonsCA2CRL1.crl", "pki/testdata/nist-pkits/crls/onlySomeReasonsCA2CRL2.crl", "pki/testdata/nist-pkits/crls/onlySomeReasonsCA3compromiseCRL.crl", "pki/testdata/nist-pkits/crls/onlySomeReasonsCA3otherreasonsCRL.crl", "pki/testdata/nist-pkits/crls/onlySomeReasonsCA4compromiseCRL.crl", "pki/testdata/nist-pkits/crls/onlySomeReasonsCA4otherreasonsCRL.crl", "pki/testdata/nist-pkits/crls/pathLenConstraint0CACRL.crl", "pki/testdata/nist-pkits/crls/pathLenConstraint0subCA2CRL.crl", "pki/testdata/nist-pkits/crls/pathLenConstraint0subCACRL.crl", "pki/testdata/nist-pkits/crls/pathLenConstraint1CACRL.crl", "pki/testdata/nist-pkits/crls/pathLenConstraint1subCACRL.crl", "pki/testdata/nist-pkits/crls/pathLenConstraint6CACRL.crl", "pki/testdata/nist-pkits/crls/pathLenConstraint6subCA0CRL.crl", "pki/testdata/nist-pkits/crls/pathLenConstraint6subCA1CRL.crl", "pki/testdata/nist-pkits/crls/pathLenConstraint6subCA4CRL.crl", "pki/testdata/nist-pkits/crls/pathLenConstraint6subsubCA00CRL.crl", "pki/testdata/nist-pkits/crls/pathLenConstraint6subsubCA11CRL.crl", "pki/testdata/nist-pkits/crls/pathLenConstraint6subsubCA41CRL.crl", "pki/testdata/nist-pkits/crls/pathLenConstraint6subsubsubCA11XCRL.crl", "pki/testdata/nist-pkits/crls/pathLenConstraint6subsubsubCA41XCRL.crl", "pki/testdata/nist-pkits/crls/pre2000CRLnextUpdateCACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy0CACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy0subCACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy0subsubCACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy0subsubsubCACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy10CACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy10subCACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy10subsubCACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy10subsubsubCACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy2CACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy2subCACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy4CACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy4subCACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy4subsubCACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy4subsubsubCACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy5CACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy5subCACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy5subsubCACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy5subsubsubCACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy7CACRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy7subCARE2CRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy7subsubCARE2RE4CRL.crl", "pki/testdata/nist-pkits/crls/requireExplicitPolicy7subsubsubCARE2RE4CRL.crl", "pki/testdata/ocsp_unittest/bad_ocsp_type.pem", "pki/testdata/ocsp_unittest/bad_signature.pem", "pki/testdata/ocsp_unittest/bad_status.pem", "pki/testdata/ocsp_unittest/good_response.pem", "pki/testdata/ocsp_unittest/good_response_next_update.pem", "pki/testdata/ocsp_unittest/good_response_sha256.pem", "pki/testdata/ocsp_unittest/has_critical_ct_extension.pem", "pki/testdata/ocsp_unittest/has_critical_response_extension.pem", "pki/testdata/ocsp_unittest/has_critical_single_extension.pem", "pki/testdata/ocsp_unittest/has_extension.pem", "pki/testdata/ocsp_unittest/has_single_extension.pem", "pki/testdata/ocsp_unittest/has_version.pem", "pki/testdata/ocsp_unittest/malformed_request.pem", "pki/testdata/ocsp_unittest/missing_response.pem", "pki/testdata/ocsp_unittest/multiple_response.pem", "pki/testdata/ocsp_unittest/no_response.pem", "pki/testdata/ocsp_unittest/ocsp_extra_certs.pem", "pki/testdata/ocsp_unittest/ocsp_sign_bad_indirect.pem", "pki/testdata/ocsp_unittest/ocsp_sign_direct.pem", "pki/testdata/ocsp_unittest/ocsp_sign_indirect.pem", "pki/testdata/ocsp_unittest/ocsp_sign_indirect_missing.pem", "pki/testdata/ocsp_unittest/other_response.pem", "pki/testdata/ocsp_unittest/responder_id.pem", "pki/testdata/ocsp_unittest/responder_name.pem", "pki/testdata/ocsp_unittest/revoke_response.pem", "pki/testdata/ocsp_unittest/revoke_response_reason.pem", "pki/testdata/ocsp_unittest/unknown_response.pem", "pki/testdata/parse_certificate_unittest/authority_key_identifier/empty_sequence.pem", "pki/testdata/parse_certificate_unittest/authority_key_identifier/extra_contents_after_extension_sequence.pem", "pki/testdata/parse_certificate_unittest/authority_key_identifier/extra_contents_after_issuer_and_serial.pem", "pki/testdata/parse_certificate_unittest/authority_key_identifier/invalid_contents.pem", "pki/testdata/parse_certificate_unittest/authority_key_identifier/invalid_issuer.pem", "pki/testdata/parse_certificate_unittest/authority_key_identifier/invalid_key_identifier.pem", "pki/testdata/parse_certificate_unittest/authority_key_identifier/invalid_serial.pem", "pki/testdata/parse_certificate_unittest/authority_key_identifier/issuer_and_serial.pem", "pki/testdata/parse_certificate_unittest/authority_key_identifier/issuer_only.pem", "pki/testdata/parse_certificate_unittest/authority_key_identifier/key_identifier.pem", "pki/testdata/parse_certificate_unittest/authority_key_identifier/key_identifier_and_issuer_and_serial.pem", "pki/testdata/parse_certificate_unittest/authority_key_identifier/serial_only.pem", "pki/testdata/parse_certificate_unittest/authority_key_identifier/url_issuer_and_serial.pem", "pki/testdata/parse_certificate_unittest/authority_key_identifier_not_sequence.pem", "pki/testdata/parse_certificate_unittest/bad_key_usage.pem", "pki/testdata/parse_certificate_unittest/bad_policy_qualifiers.pem", "pki/testdata/parse_certificate_unittest/bad_signature_algorithm_oid.pem", "pki/testdata/parse_certificate_unittest/bad_validity.pem", "pki/testdata/parse_certificate_unittest/basic_constraints_ca_false.pem", "pki/testdata/parse_certificate_unittest/basic_constraints_ca_no_path.pem", "pki/testdata/parse_certificate_unittest/basic_constraints_ca_path_9.pem", "pki/testdata/parse_certificate_unittest/basic_constraints_negative_path.pem", "pki/testdata/parse_certificate_unittest/basic_constraints_not_ca.pem", "pki/testdata/parse_certificate_unittest/basic_constraints_path_too_large.pem", "pki/testdata/parse_certificate_unittest/basic_constraints_pathlen_255.pem", "pki/testdata/parse_certificate_unittest/basic_constraints_pathlen_256.pem", "pki/testdata/parse_certificate_unittest/basic_constraints_pathlen_not_ca.pem", "pki/testdata/parse_certificate_unittest/basic_constraints_unconsumed_data.pem", "pki/testdata/parse_certificate_unittest/cert_algorithm_not_sequence.pem", "pki/testdata/parse_certificate_unittest/cert_data_after_signature.pem", "pki/testdata/parse_certificate_unittest/cert_empty_sequence.pem", "pki/testdata/parse_certificate_unittest/cert_missing_signature.pem", "pki/testdata/parse_certificate_unittest/cert_not_sequence.pem", "pki/testdata/parse_certificate_unittest/cert_signature_not_bit_string.pem", "pki/testdata/parse_certificate_unittest/cert_skeleton.pem", "pki/testdata/parse_certificate_unittest/cert_version3.pem", "pki/testdata/parse_certificate_unittest/crldp_1uri_noissuer.pem", "pki/testdata/parse_certificate_unittest/crldp_3uri_noissuer.pem", "pki/testdata/parse_certificate_unittest/crldp_full_name_as_dirname.pem", "pki/testdata/parse_certificate_unittest/crldp_issuer_as_dirname.pem", "pki/testdata/parse_certificate_unittest/extended_key_usage.pem", "pki/testdata/parse_certificate_unittest/extension_critical.pem", "pki/testdata/parse_certificate_unittest/extension_critical_0.pem", "pki/testdata/parse_certificate_unittest/extension_critical_3.pem", "pki/testdata/parse_certificate_unittest/extension_not_critical.pem", "pki/testdata/parse_certificate_unittest/extensions_data_after_sequence.pem", "pki/testdata/parse_certificate_unittest/extensions_duplicate_key_usage.pem", "pki/testdata/parse_certificate_unittest/extensions_empty_sequence.pem", "pki/testdata/parse_certificate_unittest/extensions_not_sequence.pem", "pki/testdata/parse_certificate_unittest/extensions_real.pem", "pki/testdata/parse_certificate_unittest/failed_signature_algorithm.pem", "pki/testdata/parse_certificate_unittest/inhibit_any_policy.pem", "pki/testdata/parse_certificate_unittest/issuer_bad_printable_string.pem", "pki/testdata/parse_certificate_unittest/key_usage.pem", "pki/testdata/parse_certificate_unittest/name_constraints_bad_ip.pem", "pki/testdata/parse_certificate_unittest/policies.pem", "pki/testdata/parse_certificate_unittest/policy_constraints_empty.pem", "pki/testdata/parse_certificate_unittest/policy_constraints_inhibit.pem", "pki/testdata/parse_certificate_unittest/policy_constraints_inhibit_require.pem", "pki/testdata/parse_certificate_unittest/policy_constraints_require.pem", "pki/testdata/parse_certificate_unittest/policy_qualifiers_empty_sequence.pem", "pki/testdata/parse_certificate_unittest/serial_37_bytes.pem", "pki/testdata/parse_certificate_unittest/serial_negative.pem", "pki/testdata/parse_certificate_unittest/serial_not_minimal.pem", "pki/testdata/parse_certificate_unittest/serial_not_number.pem", "pki/testdata/parse_certificate_unittest/serial_zero.pem", "pki/testdata/parse_certificate_unittest/serial_zero_padded.pem", "pki/testdata/parse_certificate_unittest/serial_zero_padded_21_bytes.pem", "pki/testdata/parse_certificate_unittest/signature_algorithm_null.pem", "pki/testdata/parse_certificate_unittest/subject_alt_name.pem", "pki/testdata/parse_certificate_unittest/subject_blank_subjectaltname_not_critical.pem", "pki/testdata/parse_certificate_unittest/subject_key_identifier_not_octet_string.pem", "pki/testdata/parse_certificate_unittest/subject_not_ascii.pem", "pki/testdata/parse_certificate_unittest/subject_not_printable_string.pem", "pki/testdata/parse_certificate_unittest/subject_printable_string_containing_utf8_client_cert.pem", "pki/testdata/parse_certificate_unittest/subject_t61string.pem", "pki/testdata/parse_certificate_unittest/subject_t61string_1-32.pem", "pki/testdata/parse_certificate_unittest/subject_t61string_126-160.pem", "pki/testdata/parse_certificate_unittest/subject_t61string_actual.pem", "pki/testdata/parse_certificate_unittest/subjectaltname_bad_ip.pem", "pki/testdata/parse_certificate_unittest/subjectaltname_dns_not_ascii.pem", "pki/testdata/parse_certificate_unittest/subjectaltname_general_names_empty_sequence.pem", "pki/testdata/parse_certificate_unittest/subjectaltname_trailing_data.pem", "pki/testdata/parse_certificate_unittest/tbs_explicit_v1.pem", "pki/testdata/parse_certificate_unittest/tbs_v1.pem", "pki/testdata/parse_certificate_unittest/tbs_v1_extensions.pem", "pki/testdata/parse_certificate_unittest/tbs_v2_extensions.pem", "pki/testdata/parse_certificate_unittest/tbs_v2_issuer_and_subject_unique_id.pem", "pki/testdata/parse_certificate_unittest/tbs_v2_issuer_unique_id.pem", "pki/testdata/parse_certificate_unittest/tbs_v2_no_optionals.pem", "pki/testdata/parse_certificate_unittest/tbs_v3_all_optionals.pem", "pki/testdata/parse_certificate_unittest/tbs_v3_data_after_extensions.pem", "pki/testdata/parse_certificate_unittest/tbs_v3_extensions.pem", "pki/testdata/parse_certificate_unittest/tbs_v3_extensions_not_sequence.pem", "pki/testdata/parse_certificate_unittest/tbs_v3_no_optionals.pem", "pki/testdata/parse_certificate_unittest/tbs_v3_real.pem", "pki/testdata/parse_certificate_unittest/tbs_v4.pem", "pki/testdata/parse_certificate_unittest/tbs_validity_both_generalized_time.pem", "pki/testdata/parse_certificate_unittest/tbs_validity_both_utc_time.pem", "pki/testdata/parse_certificate_unittest/tbs_validity_generalized_time_and_utc_time.pem", "pki/testdata/parse_certificate_unittest/tbs_validity_relaxed.pem", "pki/testdata/parse_certificate_unittest/tbs_validity_utc_time_and_generalized_time.pem", "pki/testdata/parse_certificate_unittest/v1_explicit_version.pem", "pki/testdata/path_builder_unittest/key_id_name_and_serial_prioritization/int_match_name_only.pem", "pki/testdata/path_builder_unittest/key_id_name_and_serial_prioritization/int_matching.pem", "pki/testdata/path_builder_unittest/key_id_name_and_serial_prioritization/int_mismatch.pem", "pki/testdata/path_builder_unittest/key_id_name_and_serial_prioritization/root.pem", "pki/testdata/path_builder_unittest/key_id_name_and_serial_prioritization/root2.pem", "pki/testdata/path_builder_unittest/key_id_name_and_serial_prioritization/target.pem", "pki/testdata/path_builder_unittest/key_id_prioritization/int_different_ski_a.pem", "pki/testdata/path_builder_unittest/key_id_prioritization/int_different_ski_b.pem", "pki/testdata/path_builder_unittest/key_id_prioritization/int_different_ski_c.pem", "pki/testdata/path_builder_unittest/key_id_prioritization/int_matching_ski_a.pem", "pki/testdata/path_builder_unittest/key_id_prioritization/int_matching_ski_b.pem", "pki/testdata/path_builder_unittest/key_id_prioritization/int_matching_ski_c.pem", "pki/testdata/path_builder_unittest/key_id_prioritization/int_no_ski_a.pem", "pki/testdata/path_builder_unittest/key_id_prioritization/int_no_ski_b.pem", "pki/testdata/path_builder_unittest/key_id_prioritization/int_no_ski_c.pem", "pki/testdata/path_builder_unittest/key_id_prioritization/root.pem", "pki/testdata/path_builder_unittest/key_id_prioritization/target.pem", "pki/testdata/path_builder_unittest/multi-root-A-by-B.pem", "pki/testdata/path_builder_unittest/multi-root-B-by-C.pem", "pki/testdata/path_builder_unittest/multi-root-B-by-F.pem", "pki/testdata/path_builder_unittest/multi-root-C-by-D.pem", "pki/testdata/path_builder_unittest/multi-root-C-by-E.pem", "pki/testdata/path_builder_unittest/multi-root-D-by-D.pem", "pki/testdata/path_builder_unittest/multi-root-E-by-E.pem", "pki/testdata/path_builder_unittest/multi-root-F-by-E.pem", "pki/testdata/path_builder_unittest/precertificate/precertificate.pem", "pki/testdata/path_builder_unittest/precertificate/root.pem", "pki/testdata/path_builder_unittest/self_issued_prioritization/root1.pem", "pki/testdata/path_builder_unittest/self_issued_prioritization/root1_cross.pem", "pki/testdata/path_builder_unittest/self_issued_prioritization/root2.pem", "pki/testdata/path_builder_unittest/self_issued_prioritization/target.pem", "pki/testdata/path_builder_unittest/validity_date_prioritization/int_ac.pem", "pki/testdata/path_builder_unittest/validity_date_prioritization/int_ad.pem", "pki/testdata/path_builder_unittest/validity_date_prioritization/int_bc.pem", "pki/testdata/path_builder_unittest/validity_date_prioritization/int_bd.pem", "pki/testdata/path_builder_unittest/validity_date_prioritization/root.pem", "pki/testdata/path_builder_unittest/validity_date_prioritization/target.pem", "pki/testdata/verify_certificate_chain_unittest/basic-constraints-pathlen-0-self-issued/chain.pem", "pki/testdata/verify_certificate_chain_unittest/basic-constraints-pathlen-0-self-issued/main.test", "pki/testdata/verify_certificate_chain_unittest/expired-intermediate/chain.pem", "pki/testdata/verify_certificate_chain_unittest/expired-intermediate/not-after.test", "pki/testdata/verify_certificate_chain_unittest/expired-intermediate/not-before.test", "pki/testdata/verify_certificate_chain_unittest/expired-root/chain.pem", "pki/testdata/verify_certificate_chain_unittest/expired-root/not-after-ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/expired-root/not-after-ta-with-expiration-and-constraints.test", "pki/testdata/verify_certificate_chain_unittest/expired-root/not-after-ta-with-expiration.test", "pki/testdata/verify_certificate_chain_unittest/expired-root/not-after.test", "pki/testdata/verify_certificate_chain_unittest/expired-root/not-before-ta-with-expiration.test", "pki/testdata/verify_certificate_chain_unittest/expired-root/not-before.test", "pki/testdata/verify_certificate_chain_unittest/expired-target/chain.pem", "pki/testdata/verify_certificate_chain_unittest/expired-target/not-after.test", "pki/testdata/verify_certificate_chain_unittest/expired-target/not-before.test", "pki/testdata/verify_certificate_chain_unittest/incorrect-trust-anchor/chain.pem", "pki/testdata/verify_certificate_chain_unittest/incorrect-trust-anchor/main.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-and-target-wrong-signature/chain.pem", "pki/testdata/verify_certificate_chain_unittest/intermediate-and-target-wrong-signature/main.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-basic-constraints-ca-false/chain.pem", "pki/testdata/verify_certificate_chain_unittest/intermediate-basic-constraints-ca-false/main.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-basic-constraints-not-critical/chain.pem", "pki/testdata/verify_certificate_chain_unittest/intermediate-basic-constraints-not-critical/main.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-any-and-clientauth/any.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-any-and-clientauth/chain.pem", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-any-and-clientauth/clientauth-strict-leaf.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-any-and-clientauth/clientauth-strict.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-any-and-clientauth/clientauth.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-any-and-clientauth/serverauth-strict-leaf.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-any-and-clientauth/serverauth-strict.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-any-and-clientauth/serverauth.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-clientauth/any.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-clientauth/chain.pem", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-clientauth/clientauth-strict-leaf.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-clientauth/clientauth-strict.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-clientauth/clientauth.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-clientauth/serverauth-strict-leaf.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-clientauth/serverauth-strict.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-clientauth/serverauth.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-mlsclientauth-extra/any.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-mlsclientauth-extra/chain.pem", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-mlsclientauth-extra/mlsclientauth.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-mlsclientauth/any.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-mlsclientauth/chain.pem", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-mlsclientauth/clientauth.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-mlsclientauth/mlsclientauth.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-mlsclientauth/serverauth.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-server-gated-crypto/sha1-chain.pem", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-server-gated-crypto/sha1-eku-any.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-server-gated-crypto/sha1-eku-clientAuth-strict.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-server-gated-crypto/sha1-eku-clientAuth.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-server-gated-crypto/sha1-eku-serverAuth-strict.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-server-gated-crypto/sha1-eku-serverAuth.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-server-gated-crypto/sha256-chain.pem", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-server-gated-crypto/sha256-eku-any.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-server-gated-crypto/sha256-eku-clientAuth-strict.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-server-gated-crypto/sha256-eku-clientAuth.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-server-gated-crypto/sha256-eku-serverAuth-strict.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-eku-server-gated-crypto/sha256-eku-serverAuth.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-invalid-spki/chain.pem", "pki/testdata/verify_certificate_chain_unittest/intermediate-invalid-spki/main.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-lacks-basic-constraints/chain.pem", "pki/testdata/verify_certificate_chain_unittest/intermediate-lacks-basic-constraints/main.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-lacks-signing-key-usage/chain.pem", "pki/testdata/verify_certificate_chain_unittest/intermediate-lacks-signing-key-usage/main.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-signed-with-sha1/chain.pem", "pki/testdata/verify_certificate_chain_unittest/intermediate-signed-with-sha1/main.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-unknown-critical-extension/chain.pem", "pki/testdata/verify_certificate_chain_unittest/intermediate-unknown-critical-extension/main.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-unknown-non-critical-extension/chain.pem", "pki/testdata/verify_certificate_chain_unittest/intermediate-unknown-non-critical-extension/main.test", "pki/testdata/verify_certificate_chain_unittest/intermediate-wrong-signature-no-authority-key-identifier/chain.pem", "pki/testdata/verify_certificate_chain_unittest/intermediate-wrong-signature-no-authority-key-identifier/main.test", "pki/testdata/verify_certificate_chain_unittest/issuer-and-subject-not-byte-for-byte-equal/anchor.pem", "pki/testdata/verify_certificate_chain_unittest/issuer-and-subject-not-byte-for-byte-equal/anchor.test", "pki/testdata/verify_certificate_chain_unittest/issuer-and-subject-not-byte-for-byte-equal/target.pem", "pki/testdata/verify_certificate_chain_unittest/issuer-and-subject-not-byte-for-byte-equal/target.test", "pki/testdata/verify_certificate_chain_unittest/key-rollover/longrolloverchain.pem", "pki/testdata/verify_certificate_chain_unittest/key-rollover/longrolloverchain.test", "pki/testdata/verify_certificate_chain_unittest/key-rollover/newchain.pem", "pki/testdata/verify_certificate_chain_unittest/key-rollover/newchain.test", "pki/testdata/verify_certificate_chain_unittest/key-rollover/oldchain.pem", "pki/testdata/verify_certificate_chain_unittest/key-rollover/oldchain.test", "pki/testdata/verify_certificate_chain_unittest/key-rollover/rolloverchain.pem", "pki/testdata/verify_certificate_chain_unittest/key-rollover/rolloverchain.test", "pki/testdata/verify_certificate_chain_unittest/many-names/ok-all-types.pem", "pki/testdata/verify_certificate_chain_unittest/many-names/ok-all-types.test", "pki/testdata/verify_certificate_chain_unittest/many-names/toomany-all-types.pem", "pki/testdata/verify_certificate_chain_unittest/many-names/toomany-all-types.test", "pki/testdata/verify_certificate_chain_unittest/many-names/toomany-dirnames-excluded.pem", "pki/testdata/verify_certificate_chain_unittest/many-names/toomany-dirnames-excluded.test", "pki/testdata/verify_certificate_chain_unittest/many-names/toomany-dirnames-permitted.pem", "pki/testdata/verify_certificate_chain_unittest/many-names/toomany-dirnames-permitted.test", "pki/testdata/verify_certificate_chain_unittest/many-names/toomany-dns-excluded.pem", "pki/testdata/verify_certificate_chain_unittest/many-names/toomany-dns-excluded.test", "pki/testdata/verify_certificate_chain_unittest/many-names/toomany-dns-permitted.pem", "pki/testdata/verify_certificate_chain_unittest/many-names/toomany-dns-permitted.test", "pki/testdata/verify_certificate_chain_unittest/many-names/toomany-ips-excluded.pem", "pki/testdata/verify_certificate_chain_unittest/many-names/toomany-ips-excluded.test", "pki/testdata/verify_certificate_chain_unittest/many-names/toomany-ips-permitted.pem", "pki/testdata/verify_certificate_chain_unittest/many-names/toomany-ips-permitted.test", "pki/testdata/verify_certificate_chain_unittest/non-self-signed-root/chain.pem", "pki/testdata/verify_certificate_chain_unittest/non-self-signed-root/main.test", "pki/testdata/verify_certificate_chain_unittest/non-self-signed-root/ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.1.2.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.1.3.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.1.4.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.1.5.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.1.6.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/********.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/********.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.10.10.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.10.13.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.10.2.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.10.3.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.10.4.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.10.5.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.10.6.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.10.7.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.10.8.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.11.1.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.11.10.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.11.11.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.11.3.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.11.5.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.11.6.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.11.8.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.11.9.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.12.1.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.12.10.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.12.3.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.12.4.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.12.5.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.12.6.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.12.8.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.10.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.12.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.13.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.15.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.16.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.17.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.2.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.20.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.21.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.22.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.23.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.24.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.25.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.26.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.27.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.28.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.29.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.3.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.31.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.33.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.34.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.35.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.36.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.37.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.38.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.7.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.8.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.13.9.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.16.2.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.2.1.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.2.2.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.2.5.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.2.6.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.2.7.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.3.1.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.3.2.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.6.1.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.6.10.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.6.11.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.6.12.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.6.16.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.6.2.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.6.3.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.6.5.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.6.6.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.6.9.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.7.1.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.7.2.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.8.1.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.8.12.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.8.14.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.8.2.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.8.3.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.8.4.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.8.5.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.8.6.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.8.7.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.8.8.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.8.9.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.9.3.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.9.5.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.9.7.txt", "pki/testdata/verify_certificate_chain_unittest/pkits_errors/4.9.8.txt", "pki/testdata/verify_certificate_chain_unittest/policies-inhibit-anypolicy-by-root-fail/chain.pem", "pki/testdata/verify_certificate_chain_unittest/policies-inhibit-anypolicy-by-root-fail/main.test", "pki/testdata/verify_certificate_chain_unittest/policies-inhibit-anypolicy-by-root-fail/ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/policies-inhibit-anypolicy-by-root-ok/chain.pem", "pki/testdata/verify_certificate_chain_unittest/policies-inhibit-anypolicy-by-root-ok/main.test", "pki/testdata/verify_certificate_chain_unittest/policies-inhibit-anypolicy-by-root-ok/ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/policies-inhibit-mapping-by-root-fail/chain.pem", "pki/testdata/verify_certificate_chain_unittest/policies-inhibit-mapping-by-root-fail/main.test", "pki/testdata/verify_certificate_chain_unittest/policies-inhibit-mapping-by-root-fail/ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/policies-inhibit-mapping-by-root-ok/chain.pem", "pki/testdata/verify_certificate_chain_unittest/policies-inhibit-mapping-by-root-ok/main.test", "pki/testdata/verify_certificate_chain_unittest/policies-inhibit-mapping-by-root-ok/ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/policies-ok/chain.pem", "pki/testdata/verify_certificate_chain_unittest/policies-ok/main.test", "pki/testdata/verify_certificate_chain_unittest/policies-ok/ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/policies-on-root-ok/chain.pem", "pki/testdata/verify_certificate_chain_unittest/policies-on-root-ok/main.test", "pki/testdata/verify_certificate_chain_unittest/policies-on-root-ok/ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/policies-on-root-wrong/chain.pem", "pki/testdata/verify_certificate_chain_unittest/policies-on-root-wrong/main.test", "pki/testdata/verify_certificate_chain_unittest/policies-on-root-wrong/ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/policies-required-by-root-fail/chain.pem", "pki/testdata/verify_certificate_chain_unittest/policies-required-by-root-fail/main.test", "pki/testdata/verify_certificate_chain_unittest/policies-required-by-root-fail/ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/policies-required-by-root-ok/chain.pem", "pki/testdata/verify_certificate_chain_unittest/policies-required-by-root-ok/main.test", "pki/testdata/verify_certificate_chain_unittest/policies-required-by-root-ok/ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/policy-mappings-on-root-fail/chain.pem", "pki/testdata/verify_certificate_chain_unittest/policy-mappings-on-root-fail/main.test", "pki/testdata/verify_certificate_chain_unittest/policy-mappings-on-root-fail/ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/policy-mappings-on-root-ok/chain.pem", "pki/testdata/verify_certificate_chain_unittest/policy-mappings-on-root-ok/main.test", "pki/testdata/verify_certificate_chain_unittest/policy-mappings-on-root-ok/ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/root-basic-constraints-ca-false/chain.pem", "pki/testdata/verify_certificate_chain_unittest/root-basic-constraints-ca-false/main.test", "pki/testdata/verify_certificate_chain_unittest/root-basic-constraints-ca-false/ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/root-eku-clientauth/chain.pem", "pki/testdata/verify_certificate_chain_unittest/root-eku-clientauth/serverauth-strict.test", "pki/testdata/verify_certificate_chain_unittest/root-eku-clientauth/serverauth-ta-with-constraints-strict.test", "pki/testdata/verify_certificate_chain_unittest/root-eku-clientauth/serverauth-ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/root-eku-clientauth/serverauth-ta-with-expiration-and-constraints.test", "pki/testdata/verify_certificate_chain_unittest/root-eku-clientauth/serverauth-ta-with-expiration.test", "pki/testdata/verify_certificate_chain_unittest/root-eku-clientauth/serverauth.test", "pki/testdata/verify_certificate_chain_unittest/root-lacks-basic-constraints/chain.pem", "pki/testdata/verify_certificate_chain_unittest/root-lacks-basic-constraints/main.test", "pki/testdata/verify_certificate_chain_unittest/root-lacks-basic-constraints/ta-with-constraints-require-basic-constraints.test", "pki/testdata/verify_certificate_chain_unittest/root-lacks-basic-constraints/ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/root-lacks-basic-constraints/ta-with-require-basic-constraints.test", "pki/testdata/verify_certificate_chain_unittest/root-lacks-keycertsign-key-usage/chain.pem", "pki/testdata/verify_certificate_chain_unittest/root-lacks-keycertsign-key-usage/main.test", "pki/testdata/verify_certificate_chain_unittest/root-lacks-keycertsign-key-usage/ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/target-and-intermediate/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-and-intermediate/distrusted-root-expired.test", "pki/testdata/verify_certificate_chain_unittest/target-and-intermediate/distrusted-root.test", "pki/testdata/verify_certificate_chain_unittest/target-and-intermediate/main.test", "pki/testdata/verify_certificate_chain_unittest/target-and-intermediate/ta-with-constraints.test", "pki/testdata/verify_certificate_chain_unittest/target-and-intermediate/ta-with-expiration.test", "pki/testdata/verify_certificate_chain_unittest/target-and-intermediate/trusted_leaf-and-trust_anchor.test", "pki/testdata/verify_certificate_chain_unittest/target-and-intermediate/trusted_leaf-root.test", "pki/testdata/verify_certificate_chain_unittest/target-and-intermediate/unspecified-trust-root.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-any/any.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-any/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-eku-any/clientauth-strict-leaf.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-any/clientauth-strict.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-any/clientauth.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-any/mlsclientauth.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-any/serverauth-strict-leaf.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-any/serverauth-strict.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-any/serverauth.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-clientauth/any.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-clientauth/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-eku-clientauth/clientauth-strict.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-clientauth/clientauth.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-clientauth/serverauth-strict.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-clientauth/serverauth.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-many/any.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-many/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-eku-many/clientauth-strict-leaf.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-many/clientauth-strict.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-many/clientauth.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-many/mlsclientauth.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-many/serverauth-strict-leaf.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-many/serverauth-strict.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-many/serverauth.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-none/any.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-none/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-eku-none/clientauth-strict-leaf.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-none/clientauth-strict.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-none/clientauth.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-none/mlsclientauth.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-none/serverauth-strict.test", "pki/testdata/verify_certificate_chain_unittest/target-eku-none/serverauth.test", "pki/testdata/verify_certificate_chain_unittest/target-has-512bit-rsa-key/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-has-512bit-rsa-key/main.test", "pki/testdata/verify_certificate_chain_unittest/target-has-ca-basic-constraints/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-has-ca-basic-constraints/main.test", "pki/testdata/verify_certificate_chain_unittest/target-has-ca-basic-constraints/strict.test", "pki/testdata/verify_certificate_chain_unittest/target-has-ca-basic-constraints/target_only-trusted_leaf-strict.test", "pki/testdata/verify_certificate_chain_unittest/target-has-ca-basic-constraints/target_only-trusted_leaf.test", "pki/testdata/verify_certificate_chain_unittest/target-has-ca-basic-constraints/target_only.pem", "pki/testdata/verify_certificate_chain_unittest/target-has-keycertsign-but-not-ca/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-has-keycertsign-but-not-ca/main.test", "pki/testdata/verify_certificate_chain_unittest/target-has-pathlen-but-not-ca/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-has-pathlen-but-not-ca/main.test", "pki/testdata/verify_certificate_chain_unittest/target-msapplicationpolicies-and-eku/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-msapplicationpolicies-and-eku/main.test", "pki/testdata/verify_certificate_chain_unittest/target-msapplicationpolicies-no-eku/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-msapplicationpolicies-no-eku/main.test", "pki/testdata/verify_certificate_chain_unittest/target-not-end-entity/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-not-end-entity/main.test", "pki/testdata/verify_certificate_chain_unittest/target-not-end-entity/strict-leaf.test", "pki/testdata/verify_certificate_chain_unittest/target-not-end-entity/strict.test", "pki/testdata/verify_certificate_chain_unittest/target-only/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-only/trusted_anchor.test", "pki/testdata/verify_certificate_chain_unittest/target-only/trusted_leaf-and-trust_anchor.test", "pki/testdata/verify_certificate_chain_unittest/target-only/trusted_leaf-not_after.test", "pki/testdata/verify_certificate_chain_unittest/target-only/trusted_leaf-wrong_eku.test", "pki/testdata/verify_certificate_chain_unittest/target-only/trusted_leaf.test", "pki/testdata/verify_certificate_chain_unittest/target-only/trusted_leaf_require_self_signed.test", "pki/testdata/verify_certificate_chain_unittest/target-selfissued/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-selfissued/trusted_anchor.test", "pki/testdata/verify_certificate_chain_unittest/target-selfissued/trusted_leaf-and-trust_anchor.test", "pki/testdata/verify_certificate_chain_unittest/target-selfissued/trusted_leaf.test", "pki/testdata/verify_certificate_chain_unittest/target-selfissued/trusted_leaf_require_self_signed.test", "pki/testdata/verify_certificate_chain_unittest/target-selfsigned/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-selfsigned/trusted_leaf-and-trust_anchor.test", "pki/testdata/verify_certificate_chain_unittest/target-selfsigned/trusted_leaf-not_after.test", "pki/testdata/verify_certificate_chain_unittest/target-selfsigned/trusted_leaf-wrong_eku.test", "pki/testdata/verify_certificate_chain_unittest/target-selfsigned/trusted_leaf.test", "pki/testdata/verify_certificate_chain_unittest/target-selfsigned/trusted_leaf_require_self_signed.test", "pki/testdata/verify_certificate_chain_unittest/target-serverauth-various-keyusages/ec-decipherOnly.pem", "pki/testdata/verify_certificate_chain_unittest/target-serverauth-various-keyusages/ec-decipherOnly.test", "pki/testdata/verify_certificate_chain_unittest/target-serverauth-various-keyusages/ec-digitalSignature.pem", "pki/testdata/verify_certificate_chain_unittest/target-serverauth-various-keyusages/ec-digitalSignature.test", "pki/testdata/verify_certificate_chain_unittest/target-serverauth-various-keyusages/ec-keyAgreement.pem", "pki/testdata/verify_certificate_chain_unittest/target-serverauth-various-keyusages/ec-keyAgreement.test", "pki/testdata/verify_certificate_chain_unittest/target-serverauth-various-keyusages/ec-keyEncipherment.pem", "pki/testdata/verify_certificate_chain_unittest/target-serverauth-various-keyusages/ec-keyEncipherment.test", "pki/testdata/verify_certificate_chain_unittest/target-serverauth-various-keyusages/rsa-decipherOnly.pem", "pki/testdata/verify_certificate_chain_unittest/target-serverauth-various-keyusages/rsa-decipherOnly.test", "pki/testdata/verify_certificate_chain_unittest/target-serverauth-various-keyusages/rsa-digitalSignature.pem", "pki/testdata/verify_certificate_chain_unittest/target-serverauth-various-keyusages/rsa-digitalSignature.test", "pki/testdata/verify_certificate_chain_unittest/target-serverauth-various-keyusages/rsa-keyAgreement.pem", "pki/testdata/verify_certificate_chain_unittest/target-serverauth-various-keyusages/rsa-keyAgreement.test", "pki/testdata/verify_certificate_chain_unittest/target-serverauth-various-keyusages/rsa-keyEncipherment.pem", "pki/testdata/verify_certificate_chain_unittest/target-serverauth-various-keyusages/rsa-keyEncipherment.test", "pki/testdata/verify_certificate_chain_unittest/target-signed-by-512bit-rsa/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-signed-by-512bit-rsa/main.test", "pki/testdata/verify_certificate_chain_unittest/target-signed-using-ecdsa/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-signed-using-ecdsa/main.test", "pki/testdata/verify_certificate_chain_unittest/target-signed-with-sha1/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-signed-with-sha1/main.test", "pki/testdata/verify_certificate_chain_unittest/target-unknown-critical-extension/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-unknown-critical-extension/main.test", "pki/testdata/verify_certificate_chain_unittest/target-unknown-critical-extension/target_only-trusted_leaf.test", "pki/testdata/verify_certificate_chain_unittest/target-unknown-critical-extension/target_only.pem", "pki/testdata/verify_certificate_chain_unittest/target-wrong-signature-no-authority-key-identifier/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-wrong-signature-no-authority-key-identifier/main.test", "pki/testdata/verify_certificate_chain_unittest/target-wrong-signature/chain.pem", "pki/testdata/verify_certificate_chain_unittest/target-wrong-signature/main.test", "pki/testdata/verify_certificate_chain_unittest/unknown-critical-policy-qualifier/chain.pem", "pki/testdata/verify_certificate_chain_unittest/unknown-critical-policy-qualifier/main.test", "pki/testdata/verify_certificate_chain_unittest/unknown-non-critical-policy-qualifier/chain.pem", "pki/testdata/verify_certificate_chain_unittest/unknown-non-critical-policy-qualifier/main.test", "pki/testdata/verify_certificate_chain_unittest/violates-basic-constraints-pathlen-0/chain.pem", "pki/testdata/verify_certificate_chain_unittest/violates-basic-constraints-pathlen-0/main.test", "pki/testdata/verify_certificate_chain_unittest/violates-pathlen-1-from-root/chain.pem", "pki/testdata/verify_certificate_chain_unittest/violates-pathlen-1-from-root/main.test", "pki/testdata/verify_certificate_chain_unittest/violates-pathlen-1-from-root/ta-with-constraints.test", "pki/testdata/verify_name_match_unittest/names/ascii-BMPSTRING-case_swap-dupe_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-BMPSTRING-case_swap-extra_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-BMPSTRING-case_swap-extra_rdn.pem", "pki/testdata/verify_name_match_unittest/names/ascii-BMPSTRING-case_swap.pem", "pki/testdata/verify_name_match_unittest/names/ascii-BMPSTRING-extra_whitespace-dupe_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-BMPSTRING-extra_whitespace-extra_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-BMPSTRING-extra_whitespace-extra_rdn.pem", "pki/testdata/verify_name_match_unittest/names/ascii-BMPSTRING-extra_whitespace.pem", "pki/testdata/verify_name_match_unittest/names/ascii-BMPSTRING-unmangled-dupe_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-BMPSTRING-unmangled-extra_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-BMPSTRING-unmangled-extra_rdn.pem", "pki/testdata/verify_name_match_unittest/names/ascii-BMPSTRING-unmangled.pem", "pki/testdata/verify_name_match_unittest/names/ascii-PRINTABLESTRING-case_swap-dupe_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-PRINTABLESTRING-case_swap-extra_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-PRINTABLESTRING-case_swap-extra_rdn.pem", "pki/testdata/verify_name_match_unittest/names/ascii-PRINTABLESTRING-case_swap.pem", "pki/testdata/verify_name_match_unittest/names/ascii-PRINTABLESTRING-extra_whitespace-dupe_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-PRINTABLESTRING-extra_whitespace-extra_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-PRINTABLESTRING-extra_whitespace-extra_rdn.pem", "pki/testdata/verify_name_match_unittest/names/ascii-PRINTABLESTRING-extra_whitespace.pem", "pki/testdata/verify_name_match_unittest/names/ascii-PRINTABLESTRING-rdn_sorting_1.pem", "pki/testdata/verify_name_match_unittest/names/ascii-PRINTABLESTRING-rdn_sorting_2.pem", "pki/testdata/verify_name_match_unittest/names/ascii-PRINTABLESTRING-unmangled-dupe_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-PRINTABLESTRING-unmangled-extra_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-PRINTABLESTRING-unmangled-extra_rdn.pem", "pki/testdata/verify_name_match_unittest/names/ascii-PRINTABLESTRING-unmangled.pem", "pki/testdata/verify_name_match_unittest/names/ascii-T61STRING-case_swap-dupe_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-T61STRING-case_swap-extra_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-T61STRING-case_swap-extra_rdn.pem", "pki/testdata/verify_name_match_unittest/names/ascii-T61STRING-case_swap.pem", "pki/testdata/verify_name_match_unittest/names/ascii-T61STRING-extra_whitespace-dupe_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-T61STRING-extra_whitespace-extra_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-T61STRING-extra_whitespace-extra_rdn.pem", "pki/testdata/verify_name_match_unittest/names/ascii-T61STRING-extra_whitespace.pem", "pki/testdata/verify_name_match_unittest/names/ascii-T61STRING-unmangled-dupe_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-T61STRING-unmangled-extra_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-T61STRING-unmangled-extra_rdn.pem", "pki/testdata/verify_name_match_unittest/names/ascii-T61STRING-unmangled.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UNIVERSALSTRING-case_swap-dupe_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UNIVERSALSTRING-case_swap-extra_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UNIVERSALSTRING-case_swap-extra_rdn.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UNIVERSALSTRING-case_swap.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UNIVERSALSTRING-extra_whitespace-dupe_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UNIVERSALSTRING-extra_whitespace-extra_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UNIVERSALSTRING-extra_whitespace-extra_rdn.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UNIVERSALSTRING-extra_whitespace.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UNIVERSALSTRING-unmangled-dupe_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UNIVERSALSTRING-unmangled-extra_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UNIVERSALSTRING-unmangled-extra_rdn.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UNIVERSALSTRING-unmangled.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UTF8-case_swap-dupe_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UTF8-case_swap-extra_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UTF8-case_swap-extra_rdn.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UTF8-case_swap.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UTF8-extra_whitespace-dupe_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UTF8-extra_whitespace-extra_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UTF8-extra_whitespace-extra_rdn.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UTF8-extra_whitespace.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UTF8-unmangled-dupe_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UTF8-unmangled-extra_attr.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UTF8-unmangled-extra_rdn.pem", "pki/testdata/verify_name_match_unittest/names/ascii-UTF8-unmangled.pem", "pki/testdata/verify_name_match_unittest/names/ascii-mixed-rdn_dupetype_sorting_1.pem", "pki/testdata/verify_name_match_unittest/names/ascii-mixed-rdn_dupetype_sorting_2.pem", "pki/testdata/verify_name_match_unittest/names/custom-custom-normalized.pem", "pki/testdata/verify_name_match_unittest/names/invalid-AttributeTypeAndValue-badAttributeType.pem", "pki/testdata/verify_name_match_unittest/names/invalid-AttributeTypeAndValue-empty.pem", "pki/testdata/verify_name_match_unittest/names/invalid-AttributeTypeAndValue-extradata.pem", "pki/testdata/verify_name_match_unittest/names/invalid-AttributeTypeAndValue-onlyOneElement.pem", "pki/testdata/verify_name_match_unittest/names/invalid-AttributeTypeAndValue-setNotSequence.pem", "pki/testdata/verify_name_match_unittest/names/invalid-Name-setInsteadOfSequence.pem", "pki/testdata/verify_name_match_unittest/names/invalid-RDN-empty.pem", "pki/testdata/verify_name_match_unittest/names/invalid-RDN-sequenceInsteadOfSet.pem", "pki/testdata/verify_name_match_unittest/names/unicode-mixed-normalized.pem", "pki/testdata/verify_name_match_unittest/names/unicode-mixed-unnormalized.pem", "pki/testdata/verify_name_match_unittest/names/unicode_bmp-BMPSTRING-unmangled.pem", "pki/testdata/verify_name_match_unittest/names/unicode_bmp-UNIVERSALSTRING-unmangled.pem", "pki/testdata/verify_name_match_unittest/names/unicode_bmp-UTF8-unmangled.pem", "pki/testdata/verify_name_match_unittest/names/unicode_supplementary-UNIVERSALSTRING-unmangled.pem", "pki/testdata/verify_name_match_unittest/names/unicode_supplementary-UTF8-unmangled.pem", "pki/testdata/verify_name_match_unittest/names/valid-Name-empty.pem", "pki/testdata/verify_name_match_unittest/names/valid-minimal.pem", "pki/testdata/verify_signed_data_unittest/ecdsa-prime256v1-sha512-spki-params-null.pem", "pki/testdata/verify_signed_data_unittest/ecdsa-prime256v1-sha512-unused-bits-signature.pem", "pki/testdata/verify_signed_data_unittest/ecdsa-prime256v1-sha512-using-ecdh-key.pem", "pki/testdata/verify_signed_data_unittest/ecdsa-prime256v1-sha512-using-ecmqv-key.pem", "pki/testdata/verify_signed_data_unittest/ecdsa-prime256v1-sha512-using-rsa-algorithm.pem", "pki/testdata/verify_signed_data_unittest/ecdsa-prime256v1-sha512-wrong-signature-format.pem", "pki/testdata/verify_signed_data_unittest/ecdsa-prime256v1-sha512.pem", "pki/testdata/verify_signed_data_unittest/ecdsa-secp384r1-sha256-corrupted-data.pem", "pki/testdata/verify_signed_data_unittest/ecdsa-secp384r1-sha256.pem", "pki/testdata/verify_signed_data_unittest/ecdsa-using-rsa-key.pem", "pki/testdata/verify_signed_data_unittest/rsa-pkcs1-sha1-bad-key-der-length.pem", "pki/testdata/verify_signed_data_unittest/rsa-pkcs1-sha1-bad-key-der-null.pem", "pki/testdata/verify_signed_data_unittest/rsa-pkcs1-sha1-key-params-absent.pem", "pki/testdata/verify_signed_data_unittest/rsa-pkcs1-sha1-using-pss-key-no-params.pem", "pki/testdata/verify_signed_data_unittest/rsa-pkcs1-sha1-wrong-algorithm.pem", "pki/testdata/verify_signed_data_unittest/rsa-pkcs1-sha1.pem", "pki/testdata/verify_signed_data_unittest/rsa-pkcs1-sha256-key-encoded-ber.pem", "pki/testdata/verify_signed_data_unittest/rsa-pkcs1-sha256-spki-non-null-params.pem", "pki/testdata/verify_signed_data_unittest/rsa-pkcs1-sha256-using-ecdsa-algorithm.pem", "pki/testdata/verify_signed_data_unittest/rsa-pkcs1-sha256-using-id-ea-rsa.pem", "pki/testdata/verify_signed_data_unittest/rsa-pkcs1-sha256.pem", "pki/testdata/verify_signed_data_unittest/rsa-pss-sha256-using-pss-key-with-params.pem", "pki/testdata/verify_signed_data_unittest/rsa-pss-sha256-wrong-salt.pem", "pki/testdata/verify_signed_data_unittest/rsa-pss-sha256.pem", "pki/testdata/verify_signed_data_unittest/rsa-using-ec-key.pem", "pki/testdata/verify_signed_data_unittest/rsa2048-pkcs1-sha512.pem", "pki/testdata/verify_unittest/google-intermediate1.der", "pki/testdata/verify_unittest/google-intermediate2.der", "pki/testdata/verify_unittest/google-leaf.der", "pki/testdata/verify_unittest/lencr-intermediate-r3.der", "pki/testdata/verify_unittest/lencr-leaf.der", "pki/testdata/verify_unittest/lencr-root-dst-x3.der", "pki/testdata/verify_unittest/lencr-root-x1-cross-signed.der", "pki/testdata/verify_unittest/lencr-root-x1.der", "pki/testdata/verify_unittest/mozilla_roots.der", "pki/testdata/verify_unittest/self-issued.pem"]}, "rust_bssl_crypto": {"srcs": ["rust/bssl-crypto/src/aead.rs", "rust/bssl-crypto/src/aes.rs", "rust/bssl-crypto/src/cipher/aes_cbc.rs", "rust/bssl-crypto/src/cipher/aes_ctr.rs", "rust/bssl-crypto/src/cipher/mod.rs", "rust/bssl-crypto/src/digest.rs", "rust/bssl-crypto/src/ec.rs", "rust/bssl-crypto/src/ecdh.rs", "rust/bssl-crypto/src/ecdsa.rs", "rust/bssl-crypto/src/ed25519.rs", "rust/bssl-crypto/src/hkdf.rs", "rust/bssl-crypto/src/hmac.rs", "rust/bssl-crypto/src/hpke.rs", "rust/bssl-crypto/src/lib.rs", "rust/bssl-crypto/src/macros.rs", "rust/bssl-crypto/src/mem.rs", "rust/bssl-crypto/src/mldsa.rs", "rust/bssl-crypto/src/mlkem.rs", "rust/bssl-crypto/src/rand.rs", "rust/bssl-crypto/src/rsa.rs", "rust/bssl-crypto/src/scoped.rs", "rust/bssl-crypto/src/slhdsa.rs", "rust/bssl-crypto/src/test_helpers.rs", "rust/bssl-crypto/src/x25519.rs"]}, "rust_bssl_sys": {"srcs": ["rust/bssl-sys/src/lib.rs"]}, "ssl": {"srcs": ["ssl/bio_ssl.cc", "ssl/d1_both.cc", "ssl/d1_lib.cc", "ssl/d1_pkt.cc", "ssl/d1_srtp.cc", "ssl/dtls_method.cc", "ssl/dtls_record.cc", "ssl/encrypted_client_hello.cc", "ssl/extensions.cc", "ssl/handoff.cc", "ssl/handshake.cc", "ssl/handshake_client.cc", "ssl/handshake_server.cc", "ssl/s3_both.cc", "ssl/s3_lib.cc", "ssl/s3_pkt.cc", "ssl/ssl_aead_ctx.cc", "ssl/ssl_asn1.cc", "ssl/ssl_buffer.cc", "ssl/ssl_cert.cc", "ssl/ssl_cipher.cc", "ssl/ssl_credential.cc", "ssl/ssl_file.cc", "ssl/ssl_key_share.cc", "ssl/ssl_lib.cc", "ssl/ssl_privkey.cc", "ssl/ssl_session.cc", "ssl/ssl_stat.cc", "ssl/ssl_transcript.cc", "ssl/ssl_versions.cc", "ssl/ssl_x509.cc", "ssl/t1_enc.cc", "ssl/tls13_both.cc", "ssl/tls13_client.cc", "ssl/tls13_enc.cc", "ssl/tls13_server.cc", "ssl/tls_method.cc", "ssl/tls_record.cc"], "hdrs": ["include/openssl/dtls1.h", "include/openssl/srtp.h", "include/openssl/ssl.h", "include/openssl/ssl3.h", "include/openssl/tls1.h"], "internal_hdrs": ["ssl/internal.h"]}, "ssl_test": {"srcs": ["crypto/test/gtest_main.cc", "ssl/span_test.cc", "ssl/ssl_c_test.c", "ssl/ssl_internal_test.cc", "ssl/ssl_test.cc"]}, "test_support": {"srcs": ["crypto/test/abi_test.cc", "crypto/test/file_test.cc", "crypto/test/file_test_gtest.cc", "crypto/test/file_util.cc", "crypto/test/test_data.cc", "crypto/test/test_util.cc", "crypto/test/wycheproof_util.cc"], "internal_hdrs": ["crypto/test/abi_test.h", "crypto/test/file_test.h", "crypto/test/file_util.h", "crypto/test/gtest_main.h", "crypto/test/test_data.h", "crypto/test/test_util.h", "crypto/test/wycheproof_util.h", "ssl/test/async_bio.h", "ssl/test/fuzzer.h", "ssl/test/fuzzer_tags.h", "ssl/test/handshake_util.h", "ssl/test/mock_quic_transport.h", "ssl/test/packeted_bio.h", "ssl/test/settings_writer.h", "ssl/test/test_config.h", "ssl/test/test_state.h"], "asm": ["gen/test_support/trampoline-armv4-linux.S", "gen/test_support/trampoline-armv8-apple.S", "gen/test_support/trampoline-armv8-linux.S", "gen/test_support/trampoline-armv8-win.S", "gen/test_support/trampoline-x86-apple.S", "gen/test_support/trampoline-x86-linux.S", "gen/test_support/trampoline-x86_64-apple.S", "gen/test_support/trampoline-x86_64-linux.S"], "nasm": ["gen/test_support/trampoline-x86-win.asm", "gen/test_support/trampoline-x86_64-win.asm"]}, "urandom_test": {"srcs": ["crypto/rand/urandom_test.cc"]}}