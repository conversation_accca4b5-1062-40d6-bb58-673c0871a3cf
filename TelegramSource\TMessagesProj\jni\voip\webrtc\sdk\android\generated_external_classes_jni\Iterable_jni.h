// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     java/lang/Iterable

#ifndef java_lang_Iterable_JNI
#define java_lang_Iterable_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_java_lang_Iterable[];
const char kClassPath_java_lang_Iterable[] = "java/lang/Iterable";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_java_lang_Iterable_clazz(nullptr);
#ifndef java_lang_Iterable_clazz_defined
#define java_lang_Iterable_clazz_defined
inline jclass java_lang_Iterable_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_java_lang_Iterable, &g_java_lang_Iterable_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace JNI_Iterable {


static std::atomic<jmethodID> g_java_lang_Iterable_forEach1(nullptr);
[[maybe_unused]] static void Java_Iterable_forEach(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj, const jni_zero::JavaRef<jobject>& p0);
static void Java_Iterable_forEach(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_lang_Iterable_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Iterable_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "forEach",
          "(Ljava/util/function/Consumer;)V",
          &g_java_lang_Iterable_forEach1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
}

static std::atomic<jmethodID> g_java_lang_Iterable_iterator0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Iterable_iterator(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Iterable_iterator(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Iterable_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Iterable_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "iterator",
          "()Ljava/util/Iterator;",
          &g_java_lang_Iterable_iterator0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Iterable_spliterator0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Iterable_spliterator(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Iterable_spliterator(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Iterable_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Iterable_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "spliterator",
          "()Ljava/util/Spliterator;",
          &g_java_lang_Iterable_spliterator0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace JNI_Iterable

#endif  // java_lang_Iterable_JNI
