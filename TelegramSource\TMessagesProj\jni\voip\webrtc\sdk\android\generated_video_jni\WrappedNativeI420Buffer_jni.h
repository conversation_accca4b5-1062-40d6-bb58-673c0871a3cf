// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/WrappedNativeI420Buffer

#ifndef org_webrtc_WrappedNativeI420Buffer_JNI
#define org_webrtc_WrappedNativeI420Buffer_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_WrappedNativeI420Buffer[];
const char kClassPath_org_webrtc_WrappedNativeI420Buffer[] = "org/webrtc/WrappedNativeI420Buffer";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_WrappedNativeI420Buffer_clazz(nullptr);
#ifndef org_webrtc_WrappedNativeI420Buffer_clazz_defined
#define org_webrtc_WrappedNativeI420Buffer_clazz_defined
inline jclass org_webrtc_WrappedNativeI420Buffer_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_WrappedNativeI420Buffer,
      &g_org_webrtc_WrappedNativeI420Buffer_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_WrappedNativeI420Buffer_Constructor9(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_WrappedNativeI420Buffer_Constructor(JNIEnv* env,
    JniIntWrapper width,
    JniIntWrapper height,
    const jni_zero::JavaRef<jobject>& dataY,
    JniIntWrapper strideY,
    const jni_zero::JavaRef<jobject>& dataU,
    JniIntWrapper strideU,
    const jni_zero::JavaRef<jobject>& dataV,
    JniIntWrapper strideV,
    jlong nativeBuffer) {
  jclass clazz = org_webrtc_WrappedNativeI420Buffer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_WrappedNativeI420Buffer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(IILjava/nio/ByteBuffer;ILjava/nio/ByteBuffer;ILjava/nio/ByteBuffer;IJ)V",
          &g_org_webrtc_WrappedNativeI420Buffer_Constructor9);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, as_jint(width), as_jint(height), dataY.obj(),
              as_jint(strideY), dataU.obj(), as_jint(strideU), dataV.obj(), as_jint(strideV),
              nativeBuffer);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_WrappedNativeI420Buffer_JNI
