Name: dynamic annotations
URL: http://code.google.com/p/data-race-test/wiki/DynamicAnnotations
Version: 4384
License: BSD

ATTENTION: please avoid using these annotations in Chromium code.
They were mainly intended to instruct the Valgrind-based version of
ThreadSanitizer to handle atomic operations. The new version of ThreadSanitizer
based on compiler instrumentation understands atomic operations out of the box,
so normally you don't need the annotations.
If you still think you do, please consider writing a comment at http://crbug.com/349861

One header and one source file (dynamic_annotations.h and dynamic_annotations.c)
in this directory define runtime macros useful for annotating synchronization
utilities and benign data races so data race detectors can handle Chromium code
with better precision.

These files were taken from
http://code.google.com/p/data-race-test/source/browse/?#svn/trunk/dynamic_annotations
The files are covered under BSD license as described within the files.

Local modifications:
* made lineno an unsigned short (for -Wconstant-conversion warning fixes)
* remove two superfluous semicolons for -Wextra-semi
