// Copyright 2017 The BoringSSL Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

//go:build ignore

// make_many_constraints.go generates test certificates many_constraints.pem,
// many_names*.pem, and some_names*.pem for x509_test.cc
package main

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"crypto/x509/pkix"
	"encoding/asn1"
	"encoding/pem"
	"fmt"
	"math/big"
	"os"
	"time"
)

const privateKeyPEM = `***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

var privateKey *rsa.PrivateKey

func init() {
	in := []byte(privateKeyPEM)
	keyBlock, in := pem.Decode(in)
	if keyBlock == nil || keyBlock.Type != "PRIVATE KEY" {
		panic("could not decode private key")
	}
	key, err := x509.ParsePKCS8PrivateKey(keyBlock.Bytes)
	if err != nil {
		panic(err)
	}
	privateKey = key.(*rsa.PrivateKey)
}

func randOrDie(out []byte) {
	if _, err := rand.Reader.Read(out); err != nil {
		panic(err)
	}
}

func writePEM(path string, in []byte) {
	file, err := os.Create(path)
	if err != nil {
		panic(err)
	}
	defer file.Close()
	err = pem.Encode(file, &pem.Block{Type: "CERTIFICATE", Bytes: in})
	if err != nil {
		panic(err)
	}
}

func main() {
	notBefore, err := time.Parse(time.RFC3339, "2000-01-01T00:00:00Z")
	if err != nil {
		panic(err)
	}
	notAfter, err := time.Parse(time.RFC3339, "2100-01-01T00:00:00Z")
	if err != nil {
		panic(err)
	}

	caTemplate := x509.Certificate{
		SerialNumber:          new(big.Int).SetInt64(1),
		Subject:               pkix.Name{CommonName: "CA"},
		NotBefore:             notBefore,
		NotAfter:              notAfter,
		BasicConstraintsValid: true,
		IsCA:                  true,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
		KeyUsage:              x509.KeyUsageCertSign,
		SignatureAlgorithm:    x509.SHA256WithRSA,
	}
	for i := 0; i < 513; i++ {
		caTemplate.ExcludedDNSDomains = append(caTemplate.ExcludedDNSDomains, fmt.Sprintf("x%d.test", i))
	}
	for i := 0; i < 513; i++ {
		caTemplate.PermittedDNSDomains = append(caTemplate.PermittedDNSDomains, fmt.Sprintf("t%d.test", i))
	}
	caTemplate.PermittedDNSDomains = append(caTemplate.PermittedDNSDomains, ".test")
	caBytes, err := x509.CreateCertificate(rand.Reader, &caTemplate, &caTemplate, &privateKey.PublicKey, privateKey)
	if err != nil {
		panic(err)
	}
	writePEM("many_constraints.pem", caBytes)

	ca, err := x509.ParseCertificate(caBytes)
	if err != nil {
		panic(err)
	}

	leaves := []struct {
		path   string
		names  int
		emails int
	}{
		{"many_names1.pem", 513, 513},
		{"many_names2.pem", 1025, 0},
		{"many_names3.pem", 1, 1025},
		{"some_names1.pem", 256, 256},
		{"some_names2.pem", 513, 0},
		{"some_names3.pem", 1, 513},
	}
	for i, leaf := range leaves {
		leafTemplate := x509.Certificate{
			SerialNumber:          new(big.Int).SetInt64(int64(i + 2)),
			Subject:               pkix.Name{CommonName: "t0.test"},
			NotBefore:             notBefore,
			NotAfter:              notAfter,
			BasicConstraintsValid: true,
			IsCA:                  false,
			ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth},
			KeyUsage:              x509.KeyUsageDigitalSignature | x509.KeyUsageKeyEncipherment,
			SignatureAlgorithm:    x509.SHA256WithRSA,
		}
		for i := 0; i < leaf.names; i++ {
			leafTemplate.DNSNames = append(leafTemplate.DNSNames, fmt.Sprintf("t%d.test", i))
		}
		for i := 0; i < leaf.emails; i++ {
			leafTemplate.Subject.ExtraNames = append(leafTemplate.Subject.ExtraNames, pkix.AttributeTypeAndValue{
				Type: []int{1, 2, 840, 113549, 1, 9, 1},
				Value: asn1.RawValue{
					Class:      asn1.ClassUniversal,
					Tag:        asn1.TagIA5String,
					IsCompound: false,
					Bytes:      []byte(fmt.Sprintf("t%d@test", i)),
				},
			})
		}
		leafBytes, err := x509.CreateCertificate(rand.Reader, &leafTemplate, ca, &privateKey.PublicKey, privateKey)
		if err != nil {
			panic(err)
		}

		writePEM(leaf.path, leafBytes)
	}
}
