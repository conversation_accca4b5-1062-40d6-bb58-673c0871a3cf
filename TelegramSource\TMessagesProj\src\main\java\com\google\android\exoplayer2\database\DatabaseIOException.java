/*
 * Copyright (C) 2019 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.google.android.exoplayer2.database;

import android.database.SQLException;
import java.io.IOException;

/** An {@link IOException} whose cause is an {@link SQLException}. */
public final class DatabaseIOException extends IOException {

  public DatabaseIOException(SQLException cause) {
    super(cause);
  }

  public DatabaseIOException(SQLException cause, String message) {
    super(message, cause);
  }
}
