from base_plugin import <PERSON><PERSON><PERSON><PERSON>, HookStrategy, BasePlugin
from java.util import Locale
import requests
import json

__id__ = "tailed-shrt"
__name__ = "short it!"
__description__ = "shortens url with .shrt [url] command"
__author__ = "@tailsjs"
__min_version__ = "11.9.0"
__icon__ = "dailymix5/101"
__version__ = "1.0"

BASE_URI = "https://spoo.me"

def short_link(url):
    payload = {
        "url": url
    }
    
    headers = {
        "Accept": "application/json"
    }
    
    response = requests.post(BASE_URI, data=payload, headers=headers)
    
    result = response.json()
    
    if response.status_code == 200:
        return result["short_url"], None
    else:
        log(f"Error while trying to short url: {result.text} (STATUS CODE: {response.status_code})")
        return None, f"Error while trying to short url: {result.text} (STATUS CODE: {response.status_code})"
        
class LocalizationManager:
    def __init__(self):
        self.language = Locale.getDefault().getLanguage()
        self.language = self.language if self.language in self._get_supported_languages() else "en"

        
    def get_string(self, string):
        return self.strings[self.language][string]

    def _get_supported_languages(self):
        return self.strings.keys()
    
    strings = {
        "ru": {
            "USAGE_EXAMPLE": "⚠️ пример: .shrt [ссылка]",
            "API_ERROR": "⛔ произошла ошибка на стороне api!\n\n{0}",
            "RESULT": "⭐ ссылка сокращена: {0}",
            "CODE_ERROR": "⛔ произошла ошибка в коде плагина!\n\n{0}"
        },
        "en": {
            "USAGE_EXAMPLE": "⚠️ usage: .shrt [url]",
            "API_ERROR": "⛔ an error occured on api side!\n\n{0}",
            "RESULT": "⭐ link shortened: {0}",
            "CODE_ERROR": "⛔ an error occurred in plugin code!\n\n{0}"
        }
    }
    
locali = LocalizationManager()

class ShortenPlugin(BasePlugin):
    def on_plugin_load(self):
        self.add_on_send_message_hook()
    
    def on_send_message_hook(self, account, params) -> HookStrategy:
        if not isinstance(params.message, str) or not params.message.startswith(".shrt"):
            return HookResult()
 
        try:
            parts = params.message.strip().split(" ", 1)
            
            uri = parts[1].strip() if len(parts) > 1 else "https://example.com/"
            if not uri:
                params.message = locali.get_string("USAGE_EXAMPLE")
                return HookResult(strategy=HookStrategy.MODIFY, params=params)
            
            if not uri.startswith(("http://", "https://")): # {"UrlError":"Invalid URL, URL must have a valid protocol and must follow rfc_1034 & rfc_2728 patterns"}
                uri = "https://" + uri
            
            short_uri, error = short_link(uri)
            
            if short_uri is None and error is not None:
                params.message = locali.get_string("API_ERROR").format(error)
                return HookResult(strategy=HookStrategy.MODIFY, params=params)
            
            params.message = locali.get_string("RESULT").format(short_uri)
            
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
            
        except Exception as e:
            log(f"Error! {str(e)}")
            params.message = locali.get_string("CODE_ERROR").format(str(e))
            
            return HookResult(strategy=HookStrategy.MODIFY, params=params)