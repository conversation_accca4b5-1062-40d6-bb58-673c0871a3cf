from base_plugin import BasePlugin, MethodHook
from hook_utils import find_class
from android_utils import log as _log
from ui.settings import Header, Switch, Selector, Text, Divider, Input
from ui.bulletin import BulletinHelper
from org.telegram.messenger import AndroidUtilities
from java import jclass
from java.lang import Integer

__name__ = "Fullscreen Messages Enhanced"
__description__ = "🔧 Expands ALL message bubbles to use full screen width for better readability. Enhanced version with comprehensive hooks for all message types (text, media, stickers, voice, documents, grouped messages), smart filtering, and detailed statistics. Now includes advanced hooks for getWidth(), getWidthForButtons(), getAdditionalWidthForPosition(), onMeasure(), computeHeight(), computeScroll(), and setParentBounds() methods."
__version__ = "3.0.0"
__id__ = "fullscreen_messages_enhanced"
__author__ = "@exteraDev"
__icon__ = "exteraPlugins/1"
__min_version__ = "11.12.0"

class FullscreenMessagesPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.hooked_methods = []

    def on_plugin_load(self):
        """Called when plugin is loaded"""
        self.log("🚀 Fullscreen Messages Enhanced plugin loading...")

        # Check if plugin is enabled
        if not self.get_setting("enabled", True):
            self.log("⏸️ Plugin is disabled in settings")
            return

        # Check compatibility
        try:
            screen_width = AndroidUtilities.displaySize.x
            self.log(f"📱 Screen width detected: {screen_width}px")

            if screen_width < 720:
                self.log("⚠️ Warning: Screen width is quite small, plugin may not be very effective")

        except Exception as e:
            self.log(f"⚠️ Warning: Could not detect screen size: {e}")

        try:
            self.setup_hooks()
            self.log("✅ Fullscreen Messages Enhanced plugin loaded successfully")
            BulletinHelper.show_success("Fullscreen Messages Enhanced: Plugin activated")
        except Exception as e:
            self.log(f"❌ Failed to load plugin: {e}")
            BulletinHelper.show_error(f"Fullscreen Messages Enhanced: Failed to load - {e}")

    def on_plugin_unload(self):
        """Called when plugin is unloaded"""
        self.log("Fullscreen Messages plugin unloaded")
        # Hooks are automatically removed by the system

    def setup_hooks(self):
        """Setup method hooks for ChatMessageCell and AndroidUtilities"""
        hooks_successful = 0
        total_hooks = 0

        try:
            # Find ChatMessageCell class using find_class
            chat_message_cell_class = find_class("org.telegram.ui.Cells.ChatMessageCell")
            if not chat_message_cell_class:
                raise Exception("ChatMessageCell class not found")

            self.log(f"Successfully found class: {chat_message_cell_class}")

            # Hook 1: getParentWidth method
            total_hooks += 1
            if self.hook_get_parent_width(chat_message_cell_class):
                hooks_successful += 1

            # Hook 2: calcBackgroundWidth method
            total_hooks += 1
            if self.hook_calc_background_width(chat_message_cell_class):
                hooks_successful += 1

            # Hook 3: AndroidUtilities.getMinTabletSide for tablets
            total_hooks += 1
            if self.hook_min_tablet_side():
                hooks_successful += 1

            # Hook 4: computeWidth method for additional width calculations
            total_hooks += 1
            if self.hook_compute_width(chat_message_cell_class):
                hooks_successful += 1

            # Hook 5: getGroupPhotosWidth method for grouped messages
            total_hooks += 1
            if self.hook_group_photos_width(chat_message_cell_class):
                hooks_successful += 1

            # Hook 6: getMaxNameWidth method for name width calculations
            total_hooks += 1
            if self.hook_max_name_width(chat_message_cell_class):
                hooks_successful += 1

            # Hook 7: AndroidUtilities.displaySize for global width calculations
            total_hooks += 1
            if self.hook_display_size():
                hooks_successful += 1

            # NEW HOOKS - Additional methods for comprehensive fullscreen support

            # Hook 8: getWidth method (Android View method)
            total_hooks += 1
            if self.hook_get_width(chat_message_cell_class):
                hooks_successful += 1

            # Hook 9: getWidthForButtons method
            total_hooks += 1
            if self.hook_get_width_for_buttons(chat_message_cell_class):
                hooks_successful += 1

            # Hook 10: getAdditionalWidthForPosition method
            total_hooks += 1
            if self.hook_get_additional_width_for_position(chat_message_cell_class):
                hooks_successful += 1

            # Hook 11: onMeasure method
            total_hooks += 1
            if self.hook_on_measure(chat_message_cell_class):
                hooks_successful += 1

            # Hook 12: computeHeight method
            total_hooks += 1
            if self.hook_compute_height(chat_message_cell_class):
                hooks_successful += 1

            # Hook 13: computeScroll method
            total_hooks += 1
            if self.hook_compute_scroll(chat_message_cell_class):
                hooks_successful += 1

            # Hook 14: setParentBounds method
            total_hooks += 1
            if self.hook_set_parent_bounds(chat_message_cell_class):
                hooks_successful += 1

            self.log(f"✅ Successfully hooked {hooks_successful}/{total_hooks} methods")

            if hooks_successful == 0:
                raise Exception("No hooks were successful")

        except Exception as e:
            self.log(f"❌ Error setting up hooks: {e}")
            # Don't raise the exception, just log it
            self.log("Plugin will continue with limited functionality")

    def hook_get_parent_width(self, chat_message_cell_class):
        """Hook getParentWidth method"""
        try:
            # Try to hook getParentWidth method using proper Java reflection
            try:
                # Use getClass() to get the Java Class object, then getDeclaredMethod
                java_class = chat_message_cell_class.getClass()
                get_parent_width_method = java_class.getDeclaredMethod("getParentWidth")
                get_parent_width_method.setAccessible(True)

                # Create hook instance and apply hook
                hook_instance = ParentWidthHook(self)
                hook_obj = self.hook_method(get_parent_width_method, hook_instance)

                if hook_obj:
                    self.hooked_methods.append(hook_obj)
                    self.log("✅ Successfully hooked getParentWidth method")
                    return True
                else:
                    self.log("❌ Failed to hook getParentWidth method")
                    return False

            except Exception as e:
                self.log(f"❌ getParentWidth method not found with getClass(): {e}")
                # Try alternative approach using jclass
                try:
                    # Alternative: Use jclass to get Java class directly
                    java_class = jclass("org.telegram.ui.Cells.ChatMessageCell")
                    get_parent_width_method = java_class.getDeclaredMethod("getParentWidth")
                    get_parent_width_method.setAccessible(True)

                    hook_instance = ParentWidthHook(self)
                    hook_obj = self.hook_method(get_parent_width_method, hook_instance)

                    if hook_obj:
                        self.hooked_methods.append(hook_obj)
                        self.log("✅ Successfully hooked getParentWidth method (jclass approach)")
                        return True
                    else:
                        self.log("❌ Failed to hook getParentWidth method (jclass approach)")
                        return False

                except Exception as e2:
                    self.log(f"❌ jclass approach failed: {e2}")
                    # Final fallback: try to find method by scanning all methods
                    try:
                        java_class = chat_message_cell_class.getClass()
                        methods = java_class.getDeclaredMethods()
                        target_method = None

                        for method in methods:
                            if method.getName() == "getParentWidth" and method.getParameterCount() == 0:
                                target_method = method
                                break

                        if target_method:
                            target_method.setAccessible(True)
                            hook_instance = ParentWidthHook(self)
                            hook_obj = self.hook_method(target_method, hook_instance)
                            if hook_obj:
                                self.hooked_methods.append(hook_obj)
                                self.log("✅ Successfully hooked getParentWidth method (method scan approach)")
                                return True
                            else:
                                self.log("❌ Failed to hook getParentWidth method (method scan approach)")
                                return False
                        else:
                            self.log("❌ getParentWidth method not found in class methods")
                            return False

                    except Exception as e3:
                        self.log(f"❌ Method scan approach failed: {e3}")
                        return False

        except Exception as e:
            self.log(f"❌ Error hooking getParentWidth: {e}")
            return False

    def hook_calc_background_width(self, chat_message_cell_class):
        """Hook calcBackgroundWidth method"""
        try:
            # Try to find the method using reflection
            try:
                java_class = chat_message_cell_class.getClass()
                methods = java_class.getDeclaredMethods()
                target_method = None

                for method in methods:
                    if (method.getName() == "calcBackgroundWidth" and
                        method.getParameterCount() == 3):
                        target_method = method
                        break

                if target_method:
                    target_method.setAccessible(True)
                    hook_instance = CalcBackgroundWidthHook(self)
                    hook_obj = self.hook_method(target_method, hook_instance)

                    if hook_obj:
                        self.hooked_methods.append(hook_obj)
                        self.log("✅ Successfully hooked calcBackgroundWidth method")
                        return True
                    else:
                        self.log("❌ Failed to hook calcBackgroundWidth method")
                        return False
                else:
                    self.log("❌ calcBackgroundWidth method not found")
                    return False

            except Exception as e:
                self.log(f"❌ Error finding calcBackgroundWidth method: {e}")
                return False

        except Exception as e:
            self.log(f"❌ Error hooking calcBackgroundWidth: {e}")
            return False

    def hook_min_tablet_side(self):
        """Hook AndroidUtilities.getMinTabletSide method for tablets"""
        try:
            android_utils_class = find_class("org.telegram.messenger.AndroidUtilities")
            if not android_utils_class:
                self.log("❌ AndroidUtilities class not found")
                return False

            # Try to find the method using reflection
            try:
                java_class = android_utils_class.getClass()
                methods = java_class.getDeclaredMethods()
                target_method = None

                for method in methods:
                    if (method.getName() == "getMinTabletSide" and
                        method.getParameterCount() == 0):
                        target_method = method
                        break

                if target_method:
                    target_method.setAccessible(True)
                    hook_instance = MinTabletSideHook(self)
                    hook_obj = self.hook_method(target_method, hook_instance)

                    if hook_obj:
                        self.hooked_methods.append(hook_obj)
                        self.log("✅ Successfully hooked getMinTabletSide method")
                        return True
                    else:
                        self.log("❌ Failed to hook getMinTabletSide method")
                        return False
                else:
                    self.log("❌ getMinTabletSide method not found")
                    return False

            except Exception as e:
                self.log(f"❌ Error finding getMinTabletSide method: {e}")
                return False

        except Exception as e:
            self.log(f"❌ Error hooking getMinTabletSide: {e}")
            return False

    def hook_compute_width(self, chat_message_cell_class):
        """Hook computeWidth method for additional width calculations"""
        try:
            # Try to find the computeWidth method using reflection
            try:
                java_class = chat_message_cell_class.getClass()
                methods = java_class.getDeclaredMethods()
                target_method = None

                for method in methods:
                    if (method.getName() == "computeWidth" and
                        method.getParameterCount() == 2):
                        target_method = method
                        break

                if target_method:
                    target_method.setAccessible(True)
                    hook_instance = ComputeWidthHook(self)
                    hook_obj = self.hook_method(target_method, hook_instance)

                    if hook_obj:
                        self.hooked_methods.append(hook_obj)
                        self.log("✅ Successfully hooked computeWidth method")
                        return True
                    else:
                        self.log("❌ Failed to hook computeWidth method")
                        return False
                else:
                    self.log("❌ computeWidth method not found")
                    return False

            except Exception as e:
                self.log(f"❌ Error finding computeWidth method: {e}")
                return False

        except Exception as e:
            self.log(f"❌ Error hooking computeWidth: {e}")
            return False

    def hook_group_photos_width(self, chat_message_cell_class):
        """Hook getGroupPhotosWidth method for grouped messages"""
        try:
            # Try to find the getGroupPhotosWidth method using reflection
            try:
                java_class = chat_message_cell_class.getClass()
                methods = java_class.getDeclaredMethods()
                target_method = None

                for method in methods:
                    if (method.getName() == "getGroupPhotosWidth" and
                        method.getParameterCount() == 0):
                        target_method = method
                        break

                if target_method:
                    target_method.setAccessible(True)
                    hook_instance = GroupPhotosWidthHook(self)
                    hook_obj = self.hook_method(target_method, hook_instance)

                    if hook_obj:
                        self.hooked_methods.append(hook_obj)
                        self.log("✅ Successfully hooked getGroupPhotosWidth method")
                        return True
                    else:
                        self.log("❌ Failed to hook getGroupPhotosWidth method")
                        return False
                else:
                    self.log("❌ getGroupPhotosWidth method not found")
                    return False

            except Exception as e:
                self.log(f"❌ Error finding getGroupPhotosWidth method: {e}")
                return False

        except Exception as e:
            self.log(f"❌ Error hooking getGroupPhotosWidth: {e}")
            return False

    def hook_max_name_width(self, chat_message_cell_class):
        """Hook getMaxNameWidth method for name width calculations"""
        try:
            # Try to find the getMaxNameWidth method using reflection
            try:
                java_class = chat_message_cell_class.getClass()
                methods = java_class.getDeclaredMethods()
                target_method = None

                for method in methods:
                    if (method.getName() == "getMaxNameWidth" and
                        method.getParameterCount() == 0):
                        target_method = method
                        break

                if target_method:
                    target_method.setAccessible(True)
                    hook_instance = MaxNameWidthHook(self)
                    hook_obj = self.hook_method(target_method, hook_instance)

                    if hook_obj:
                        self.hooked_methods.append(hook_obj)
                        self.log("✅ Successfully hooked getMaxNameWidth method")
                        return True
                    else:
                        self.log("❌ Failed to hook getMaxNameWidth method")
                        return False
                else:
                    self.log("❌ getMaxNameWidth method not found")
                    return False

            except Exception as e:
                self.log(f"❌ Error finding getMaxNameWidth method: {e}")
                return False

        except Exception as e:
            self.log(f"❌ Error hooking getMaxNameWidth: {e}")
            return False

    def hook_display_size(self):
        """Hook AndroidUtilities.displaySize for global width calculations"""
        try:
            android_utils_class = find_class("org.telegram.messenger.AndroidUtilities")
            if not android_utils_class:
                self.log("❌ AndroidUtilities class not found for displaySize hook")
                return False

            # Try to hook the displaySize field access
            # This is more complex as it's a field, not a method
            # We'll try to hook methods that use displaySize.x
            self.log("ℹ️ displaySize hook: This is a field, not a method - will be handled by other hooks")
            return True

        except Exception as e:
            self.log(f"❌ Error hooking displaySize: {e}")
            return False

    def hook_get_width(self, chat_message_cell_class):
        """Hook getWidth method (Android View method)"""
        try:
            # getWidth is inherited from Android View class
            # We need to hook it on the ChatMessageCell instance
            try:
                # Try to find getWidth method using reflection
                java_class = chat_message_cell_class.getClass()
                get_width_method = java_class.getMethod("getWidth")

                if get_width_method:
                    hook_instance = GetWidthHook(self)
                    hook_obj = self.hook_method(get_width_method, hook_instance)

                    if hook_obj:
                        self.hooked_methods.append(hook_obj)
                        self.log("✅ Successfully hooked getWidth method")
                        return True
                    else:
                        self.log("❌ Failed to hook getWidth method")
                        return False
                else:
                    self.log("❌ getWidth method not found")
                    return False

            except Exception as e:
                self.log(f"❌ Error finding getWidth method: {e}")
                return False

        except Exception as e:
            self.log(f"❌ Error hooking getWidth: {e}")
            return False

    def hook_get_width_for_buttons(self, chat_message_cell_class):
        """Hook getWidthForButtons method"""
        try:
            # Try to find the getWidthForButtons method using reflection
            try:
                java_class = chat_message_cell_class.getClass()
                methods = java_class.getDeclaredMethods()
                target_method = None

                for method in methods:
                    if (method.getName() == "getWidthForButtons" and
                        method.getParameterCount() == 0):
                        target_method = method
                        break

                if target_method:
                    target_method.setAccessible(True)
                    hook_instance = GetWidthForButtonsHook(self)
                    hook_obj = self.hook_method(target_method, hook_instance)

                    if hook_obj:
                        self.hooked_methods.append(hook_obj)
                        self.log("✅ Successfully hooked getWidthForButtons method")
                        return True
                    else:
                        self.log("❌ Failed to hook getWidthForButtons method")
                        return False
                else:
                    self.log("❌ getWidthForButtons method not found")
                    return False

            except Exception as e:
                self.log(f"❌ Error finding getWidthForButtons method: {e}")
                return False

        except Exception as e:
            self.log(f"❌ Error hooking getWidthForButtons: {e}")
            return False

    def hook_get_additional_width_for_position(self, chat_message_cell_class):
        """Hook getAdditionalWidthForPosition method"""
        try:
            # Try to find the getAdditionalWidthForPosition method using reflection
            try:
                java_class = chat_message_cell_class.getClass()
                methods = java_class.getDeclaredMethods()
                target_method = None

                for method in methods:
                    if (method.getName() == "getAdditionalWidthForPosition" and
                        method.getParameterCount() == 1):
                        target_method = method
                        break

                if target_method:
                    target_method.setAccessible(True)
                    hook_instance = GetAdditionalWidthForPositionHook(self)
                    hook_obj = self.hook_method(target_method, hook_instance)

                    if hook_obj:
                        self.hooked_methods.append(hook_obj)
                        self.log("✅ Successfully hooked getAdditionalWidthForPosition method")
                        return True
                    else:
                        self.log("❌ Failed to hook getAdditionalWidthForPosition method")
                        return False
                else:
                    self.log("❌ getAdditionalWidthForPosition method not found")
                    return False

            except Exception as e:
                self.log(f"❌ Error finding getAdditionalWidthForPosition method: {e}")
                return False

        except Exception as e:
            self.log(f"❌ Error hooking getAdditionalWidthForPosition: {e}")
            return False

    def hook_on_measure(self, chat_message_cell_class):
        """Hook onMeasure method"""
        try:
            # Try to find the onMeasure method using reflection
            try:
                java_class = chat_message_cell_class.getClass()
                methods = java_class.getDeclaredMethods()
                target_method = None

                for method in methods:
                    if (method.getName() == "onMeasure" and
                        method.getParameterCount() == 2):
                        target_method = method
                        break

                if target_method:
                    target_method.setAccessible(True)
                    hook_instance = OnMeasureHook(self)
                    hook_obj = self.hook_method(target_method, hook_instance)

                    if hook_obj:
                        self.hooked_methods.append(hook_obj)
                        self.log("✅ Successfully hooked onMeasure method")
                        return True
                    else:
                        self.log("❌ Failed to hook onMeasure method")
                        return False
                else:
                    self.log("❌ onMeasure method not found")
                    return False

            except Exception as e:
                self.log(f"❌ Error finding onMeasure method: {e}")
                return False

        except Exception as e:
            self.log(f"❌ Error hooking onMeasure: {e}")
            return False

    def hook_compute_height(self, chat_message_cell_class):
        """Hook computeHeight method"""
        try:
            # Try to find the computeHeight method using reflection
            try:
                java_class = chat_message_cell_class.getClass()
                methods = java_class.getDeclaredMethods()
                target_method = None

                for method in methods:
                    if (method.getName() == "computeHeight" and
                        method.getParameterCount() == 3):
                        target_method = method
                        break

                if target_method:
                    target_method.setAccessible(True)
                    hook_instance = ComputeHeightHook(self)
                    hook_obj = self.hook_method(target_method, hook_instance)

                    if hook_obj:
                        self.hooked_methods.append(hook_obj)
                        self.log("✅ Successfully hooked computeHeight method")
                        return True
                    else:
                        self.log("❌ Failed to hook computeHeight method")
                        return False
                else:
                    self.log("❌ computeHeight method not found")
                    return False

            except Exception as e:
                self.log(f"❌ Error finding computeHeight method: {e}")
                return False

        except Exception as e:
            self.log(f"❌ Error hooking computeHeight: {e}")
            return False

    def hook_compute_scroll(self, chat_message_cell_class):
        """Hook computeScroll method"""
        try:
            # Try to find the computeScroll method using reflection
            try:
                java_class = chat_message_cell_class.getClass()
                methods = java_class.getDeclaredMethods()
                target_method = None

                for method in methods:
                    if (method.getName() == "computeScroll" and
                        method.getParameterCount() == 0):
                        target_method = method
                        break

                if target_method:
                    target_method.setAccessible(True)
                    hook_instance = ComputeScrollHook(self)
                    hook_obj = self.hook_method(target_method, hook_instance)

                    if hook_obj:
                        self.hooked_methods.append(hook_obj)
                        self.log("✅ Successfully hooked computeScroll method")
                        return True
                    else:
                        self.log("❌ Failed to hook computeScroll method")
                        return False
                else:
                    self.log("❌ computeScroll method not found")
                    return False

            except Exception as e:
                self.log(f"❌ Error finding computeScroll method: {e}")
                return False

        except Exception as e:
            self.log(f"❌ Error hooking computeScroll: {e}")
            return False

    def hook_set_parent_bounds(self, chat_message_cell_class):
        """Hook setParentBounds method"""
        try:
            # Try to find the setParentBounds method using reflection
            try:
                java_class = chat_message_cell_class.getClass()
                methods = java_class.getDeclaredMethods()
                target_method = None

                for method in methods:
                    if (method.getName() == "setParentBounds" and
                        method.getParameterCount() == 2):
                        # Check parameter types: float and int
                        param_types = method.getParameterTypes()
                        if (len(param_types) == 2 and
                            param_types[0].getName() == "float" and
                            param_types[1].getName() == "int"):
                            target_method = method
                            break

                if target_method:
                    target_method.setAccessible(True)
                    hook_instance = SetParentBoundsHook(self)
                    hook_obj = self.hook_method(target_method, hook_instance)

                    if hook_obj:
                        self.hooked_methods.append(hook_obj)
                        self.log("✅ Successfully hooked setParentBounds method")
                        return True
                    else:
                        self.log("❌ Failed to hook setParentBounds method")
                        return False
                else:
                    self.log("❌ setParentBounds method not found")
                    return False

            except Exception as e:
                self.log(f"❌ Error finding setParentBounds method: {e}")
                return False

        except Exception as e:
            self.log(f"❌ Error hooking setParentBounds: {e}")
            return False

    def log(self, message):
        """Log with plugin prefix"""
        _log(f"[FullscreenMessagesEnhanced] {message}")

    def create_settings(self):
        """Create plugin settings UI"""
        return [
            Header(text="Fullscreen Messages Enhanced Settings"),
            Switch(
                key="enabled",
                text="Enable Fullscreen Messages Enhanced",
                default=True,
                subtext="Expand ALL message bubbles to use full screen width",
                on_change=self.on_enabled_change
            ),
            Divider(),
            Selector(
                key="expansion_mode",
                text="Expansion Mode",
                default=0,
                items=["Full Width", "95% Width", "90% Width", "85% Width"],
                icon="msg_photo_settings"
            ),
            Text(
                text="ℹ️ How much of the screen width to use for message bubbles",
                icon="msg_info"
            ),
            Input(
                key="extra_margin",
                text="Extra Margin (px)",
                default="200",
                subtext="Additional pixels to compensate for system margins",
                icon="msg_photo_settings"
            ),
            Switch(
                key="hook_calc_background_width",
                text="Hook calcBackgroundWidth",
                default=True,
                subtext="Enable advanced background width calculation hook"
            ),
            Switch(
                key="hook_tablet_side",
                text="Hook Tablet Side Width",
                default=True,
                subtext="Enable tablet-specific width expansion"
            ),
            Switch(
                key="hook_group_photos_width",
                text="Hook Group Photos Width",
                default=True,
                subtext="Enable width expansion for grouped messages"
            ),
            Switch(
                key="hook_max_name_width",
                text="Hook Max Name Width",
                default=True,
                subtext="Enable width expansion for message names"
            ),
            Divider(),
            Text(
                text="🆕 Advanced Hook Settings:",
                accent=True,
                icon="msg_settings"
            ),
            Switch(
                key="hook_get_width",
                text="Hook getWidth Method",
                default=True,
                subtext="Hook Android View getWidth method for comprehensive width control"
            ),
            Switch(
                key="hook_get_width_for_buttons",
                text="Hook getWidthForButtons Method",
                default=True,
                subtext="Hook button width calculations for better button layout"
            ),
            Switch(
                key="hook_get_additional_width_for_position",
                text="Hook getAdditionalWidthForPosition Method",
                default=True,
                subtext="Hook additional width calculations for grouped messages"
            ),
            Switch(
                key="hook_on_measure",
                text="Hook onMeasure Method",
                default=True,
                subtext="Hook measurement process for enhanced width control"
            ),
            Switch(
                key="hook_compute_height",
                text="Hook computeHeight Method",
                default=False,
                subtext="Hook height computation (mainly for logging)"
            ),
            Switch(
                key="hook_compute_scroll",
                text="Hook computeScroll Method",
                default=False,
                subtext="Hook scroll computation (mainly for logging)"
            ),
            Switch(
                key="hook_set_parent_bounds",
                text="Hook setParentBounds Method",
                default=True,
                subtext="Hook parent bounds setting for better layout"
            ),
            Divider(),
            Text(
                text="📱 Message Filtering:",
                accent=True,
                icon="msg_filter"
            ),
            Switch(
                key="text_messages_only",
                text="Text Messages Only",
                default=False,
                subtext="Only expand text messages, not media messages"
            ),
            Switch(
                key="exclude_short_messages",
                text="Exclude Short Messages",
                default=False,
                subtext="Don't expand messages shorter than 2 lines"
            ),
            Switch(
                key="exclude_media_messages",
                text="Exclude Media Messages",
                default=False,
                subtext="Don't expand messages with photos, videos, or stickers"
            ),
            Switch(
                key="expand_outgoing_only",
                text="Outgoing Messages Only",
                default=False,
                subtext="Only expand messages you sent"
            ),
            Switch(
                key="aggressive_expansion",
                text="Aggressive Expansion",
                default=False,
                subtext="Force expansion even for messages that normally shouldn't be expanded"
            ),
            Divider(),
            Text(
                text="ℹ️ Enhanced Features:",
                accent=True,
                icon="msg_info"
            ),
            Text(
                text="• Multiple hook points for comprehensive coverage\n• Original hooks: getParentWidth(), calcBackgroundWidth(), getMinTabletSide()\n• Original hooks: getGroupPhotosWidth(), getMaxNameWidth(), computeWidth()\n• NEW hooks: getWidth(), getWidthForButtons(), getAdditionalWidthForPosition()\n• NEW hooks: onMeasure(), computeHeight(), computeScroll(), setParentBounds()\n• Enhanced tablet support and better handling of all message types\n• Improved margin compensation and advanced width control"
            ),
            Text(
                text="⚠️ Note: Changes require app restart to take effect",
                accent=True
            ),
            Text(
                text="🔄 Restart App",
                red=True,
                on_click=self.restart_app
            ),
            Divider(),
            Text(
                text="📊 Plugin Statistics",
                accent=True,
                on_click=self.show_stats
            ),
            Text(
                text="🗑️ Reset Statistics",
                red=True,
                on_click=self.reset_stats
            )
        ]

    def on_enabled_change(self, new_value):
        """Called when enabled setting changes"""
        self.log(f"Plugin enabled changed to: {new_value}")
        if new_value:
            BulletinHelper.show_info("Fullscreen Messages Enhanced: Enabled (restart required)")
        else:
            BulletinHelper.show_info("Fullscreen Messages Enhanced: Disabled (restart required)")

    def restart_app(self, view):
        """Restart the app"""
        try:
            # Use AndroidUtilities to restart the app
            from org.telegram.messenger import ApplicationLoader
            import android.os.Process as Process
            import java.lang.System as System

            # Kill the current process to force restart
            Process.killProcess(Process.myPid())
            System.exit(0)
        except Exception as e:
            self.log(f"Failed to restart app: {e}")
            BulletinHelper.show_error("Please restart the app manually")

    def show_stats(self, view):
        """Show plugin statistics"""
        try:
            expanded_count = self.get_setting("expanded_count", 0)
            skipped_count = self.get_setting("skipped_count", 0)

            # Original hooks
            parent_width_hooks = self.get_setting("parent_width_hooks", 0)
            calc_bg_width_hooks = self.get_setting("calc_bg_width_hooks", 0)
            tablet_side_hooks = self.get_setting("tablet_side_hooks", 0)
            group_photos_width_hooks = self.get_setting("group_photos_width_hooks", 0)
            max_name_width_hooks = self.get_setting("max_name_width_hooks", 0)
            compute_width_hooks = self.get_setting("compute_width_hooks", 0)

            # New hooks
            get_width_hooks = self.get_setting("get_width_hooks", 0)
            get_width_for_buttons_hooks = self.get_setting("get_width_for_buttons_hooks", 0)
            get_additional_width_hooks = self.get_setting("get_additional_width_for_position_hooks", 0)
            on_measure_hooks = self.get_setting("on_measure_hooks", 0)
            compute_height_hooks = self.get_setting("compute_height_hooks", 0)
            compute_scroll_hooks = self.get_setting("compute_scroll_hooks", 0)
            set_parent_bounds_hooks = self.get_setting("set_parent_bounds_hooks", 0)

            total_count = expanded_count + skipped_count

            if total_count == 0:
                stats_text = "📊 Enhanced Plugin Statistics\n\nNo messages processed yet.\nThe plugin will start working after you restart the app."
            else:
                expansion_rate = (expanded_count / total_count * 100) if total_count > 0 else 0
                stats_text = f"📊 Enhanced Plugin Statistics\n\n✅ Messages expanded: {expanded_count}\n⏭️ Messages skipped: {skipped_count}\n📈 Expansion rate: {expansion_rate:.1f}%\n\n🔧 Original Hook Statistics:\n• getParentWidth: {parent_width_hooks}\n• calcBackgroundWidth: {calc_bg_width_hooks}\n• getMinTabletSide: {tablet_side_hooks}\n• getGroupPhotosWidth: {group_photos_width_hooks}\n• getMaxNameWidth: {max_name_width_hooks}\n• computeWidth: {compute_width_hooks}\n\n🆕 New Hook Statistics:\n• getWidth: {get_width_hooks}\n• getWidthForButtons: {get_width_for_buttons_hooks}\n• getAdditionalWidthForPosition: {get_additional_width_hooks}\n• onMeasure: {on_measure_hooks}\n• computeHeight: {compute_height_hooks}\n• computeScroll: {compute_scroll_hooks}\n• setParentBounds: {set_parent_bounds_hooks}\n\nTotal processed: {total_count}"

            from ui.alert import AlertDialog
            AlertDialog.show_info(stats_text)

        except Exception as e:
            self.log(f"Error showing stats: {e}")
            BulletinHelper.show_error("Failed to load statistics")

    def reset_stats(self, view):
        """Reset plugin statistics"""
        try:
            self.set_setting("expanded_count", 0)
            self.set_setting("skipped_count", 0)

            # Reset original hook statistics
            self.set_setting("parent_width_hooks", 0)
            self.set_setting("calc_bg_width_hooks", 0)
            self.set_setting("tablet_side_hooks", 0)
            self.set_setting("group_photos_width_hooks", 0)
            self.set_setting("max_name_width_hooks", 0)
            self.set_setting("compute_width_hooks", 0)

            # Reset new hook statistics
            self.set_setting("get_width_hooks", 0)
            self.set_setting("get_width_for_buttons_hooks", 0)
            self.set_setting("get_additional_width_for_position_hooks", 0)
            self.set_setting("on_measure_hooks", 0)
            self.set_setting("compute_height_hooks", 0)
            self.set_setting("compute_scroll_hooks", 0)
            self.set_setting("set_parent_bounds_hooks", 0)

            self.log("📊 All statistics reset")
            BulletinHelper.show_success("Enhanced statistics have been reset")
        except Exception as e:
            self.log(f"Error resetting stats: {e}")
            BulletinHelper.show_error("Failed to reset statistics")


class ParentWidthHook(MethodHook):
    """Hook for getParentWidth method to return expanded width"""

    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin

    def before_hooked_method(self, param):
        """Called before getParentWidth method"""
        try:
            if not self.plugin.get_setting("enabled", True):
                return

            # Update hook statistics
            parent_width_hooks = self.plugin.get_setting("parent_width_hooks", 0)
            self.plugin.set_setting("parent_width_hooks", parent_width_hooks + 1)

            # Get expansion mode setting
            expansion_mode = self.plugin.get_setting("expansion_mode", 0)

            # Get current screen width
            screen_width = AndroidUtilities.displaySize.x

            # Calculate new width based on mode
            # We need to add extra width to compensate for margins that will be subtracted
            # Common subtractions: dp(50), dp(80), dp(122), etc.
            try:
                extra_margin = int(self.plugin.get_setting("extra_margin", "200"))
            except (ValueError, TypeError):
                extra_margin = 200  # Fallback to default

            if expansion_mode == 0:  # Full Width
                new_width = int(screen_width + extra_margin)
            elif expansion_mode == 1:  # 95% Width
                new_width = int(screen_width * 0.95 + extra_margin)
            elif expansion_mode == 2:  # 90% Width
                new_width = int(screen_width * 0.90 + extra_margin)
            else:  # 85% Width
                new_width = int(screen_width * 0.85 + extra_margin)

            # Check filter settings (unless aggressive expansion is enabled)
            if not self.plugin.get_setting("aggressive_expansion", False) and self.should_skip_expansion(param):
                return

            # Override the return value using setResult (Xposed way)
            # Ensure we return a Java Integer, not Long
            from java.lang import Integer
            param.setResult(Integer(new_width))

            # Update statistics
            expanded_count = self.plugin.get_setting("expanded_count", 0)
            self.plugin.set_setting("expanded_count", expanded_count + 1)

            self.plugin.log(f"🔧 getParentWidth: {screen_width}px → {new_width}px (mode: {expansion_mode}, margin: +{extra_margin}px)")

        except Exception as e:
            self.plugin.log(f"❌ Error in ParentWidthHook: {e}")
            # Don't interfere with normal operation if there's an error

    def after_hooked_method(self, param):
        """Called after getParentWidth method"""
        # This won't be called if setResult was used in before_hooked_method
        pass

    def should_skip_expansion(self, param):
        """Check if we should skip expansion based on filter settings"""
        try:
            # Try to get message context from ChatMessageCell instance
            chat_message_cell = param.thisObject
            if chat_message_cell is None:
                return False

            # Try to get current message object using Java reflection
            try:
                # Access the currentMessageObject field using Java reflection
                java_class = chat_message_cell.getClass()
                current_message_field = java_class.getDeclaredField("currentMessageObject")
                current_message_field.setAccessible(True)
                current_message_object = current_message_field.get(chat_message_cell)

                if current_message_object is None:
                    # Skip filtering if we can't access the message object
                    return False

                # Check filter settings
                if self.plugin.get_setting("expand_outgoing_only", False):
                    try:
                        if not current_message_object.isOutOwner():
                            skipped_count = self.plugin.get_setting("skipped_count", 0)
                            self.plugin.set_setting("skipped_count", skipped_count + 1)
                            return True
                    except:
                        pass  # If method doesn't exist, continue

                if self.plugin.get_setting("text_messages_only", False):
                    try:
                        # Check if it's not a text message
                        if hasattr(current_message_object, 'type') and current_message_object.type != 0:  # MessageObject.TYPE_TEXT = 0
                            skipped_count = self.plugin.get_setting("skipped_count", 0)
                            self.plugin.set_setting("skipped_count", skipped_count + 1)
                            return True
                    except:
                        pass

                if self.plugin.get_setting("exclude_media_messages", False):
                    try:
                        # Check if it's a media message
                        if hasattr(current_message_object, 'type') and current_message_object.type in [1, 3, 8, 9, 13, 14]:  # Photo, Video, GIF, Sticker, etc.
                            skipped_count = self.plugin.get_setting("skipped_count", 0)
                            self.plugin.set_setting("skipped_count", skipped_count + 1)
                            return True
                    except:
                        pass

                if self.plugin.get_setting("exclude_short_messages", False):
                    try:
                        # Check if message is short (less than 2 lines)
                        if hasattr(current_message_object, 'messageText') and current_message_object.messageText:
                            text_length = len(str(current_message_object.messageText))
                            if text_length < 100:  # Approximate threshold for 2 lines
                                skipped_count = self.plugin.get_setting("skipped_count", 0)
                                self.plugin.set_setting("skipped_count", skipped_count + 1)
                                return True
                    except:
                        pass  # If we can't determine text length, don't skip

            except Exception as e:
                # If we can't access message object, don't skip but log the issue
                self.plugin.log(f"⚠️ Could not access message object for filtering: {e}")
                return False

            return False
        except Exception as e:
            self.plugin.log(f"❌ Error in should_skip_expansion: {e}")
            return False


class CalcBackgroundWidthHook(MethodHook):
    """Hook for calcBackgroundWidth method to control background width calculation"""

    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin

    def before_hooked_method(self, param):
        """Called before calcBackgroundWidth method"""
        try:
            if not self.plugin.get_setting("enabled", True):
                return

            if not self.plugin.get_setting("hook_calc_background_width", True):
                return

            # Update hook statistics
            calc_bg_width_hooks = self.plugin.get_setting("calc_bg_width_hooks", 0)
            self.plugin.set_setting("calc_bg_width_hooks", calc_bg_width_hooks + 1)

            # Check if we should skip expansion based on message context (unless aggressive expansion is enabled)
            if not self.plugin.get_setting("aggressive_expansion", False) and self.should_skip_expansion_for_calc(param):
                return

            # Get parameters: maxWidth, timeMore, maxChildWidth
            max_width = int(param.args[0])  # Ensure it's int, not Long
            time_more = int(param.args[1])  # Ensure it's int, not Long
            max_child_width = int(param.args[2])  # Ensure it's int, not Long

            # Get expansion mode setting
            expansion_mode = self.plugin.get_setting("expansion_mode", 0)
            screen_width = AndroidUtilities.displaySize.x

            try:
                extra_margin = int(self.plugin.get_setting("extra_margin", "200"))
            except (ValueError, TypeError):
                extra_margin = 200

            # Calculate enhanced max width
            if expansion_mode == 0:  # Full Width
                enhanced_max_width = int(screen_width + extra_margin)
            elif expansion_mode == 1:  # 95% Width
                enhanced_max_width = int(screen_width * 0.95 + extra_margin)
            elif expansion_mode == 2:  # 90% Width
                enhanced_max_width = int(screen_width * 0.90 + extra_margin)
            else:  # 85% Width
                enhanced_max_width = int(screen_width * 0.85 + extra_margin)

            # Override the maxWidth parameter with proper Java Integer type
            from java.lang import Integer
            param.args[0] = Integer(enhanced_max_width)
            # Keep other parameters as proper Java Integers too
            param.args[1] = Integer(time_more)
            param.args[2] = Integer(max_child_width)

            self.plugin.log(f"🔧 calcBackgroundWidth: maxWidth {max_width}px → {enhanced_max_width}px")

        except Exception as e:
            self.plugin.log(f"❌ Error in CalcBackgroundWidthHook: {e}")

    def should_skip_expansion_for_calc(self, param):
        """Check if we should skip expansion for calcBackgroundWidth"""
        try:
            # Try to get ChatMessageCell instance from the method call context
            chat_message_cell = param.thisObject
            if chat_message_cell is None:
                return False

            # Try to get current message object using Java reflection
            try:
                # Access the currentMessageObject field using Java reflection
                java_class = chat_message_cell.getClass()
                current_message_field = java_class.getDeclaredField("currentMessageObject")
                current_message_field.setAccessible(True)
                current_message_object = current_message_field.get(chat_message_cell)

                if current_message_object is None:
                    return False

                # Apply the same filtering logic as in ParentWidthHook
                if self.plugin.get_setting("expand_outgoing_only", False):
                    try:
                        if not current_message_object.isOutOwner():
                            return True
                    except:
                        pass

                if self.plugin.get_setting("text_messages_only", False):
                    try:
                        if hasattr(current_message_object, 'type') and current_message_object.type != 0:  # Not a text message
                            return True
                    except:
                        pass

                if self.plugin.get_setting("exclude_media_messages", False):
                    try:
                        if hasattr(current_message_object, 'type') and current_message_object.type in [1, 3, 8, 9, 13, 14]:  # Media messages
                            return True
                    except:
                        pass

            except Exception as e:
                # If we can't access message object, don't skip
                pass

            return False
        except Exception as e:
            self.plugin.log(f"❌ Error in should_skip_expansion_for_calc: {e}")
            return False


class MinTabletSideHook(MethodHook):
    """Hook for AndroidUtilities.getMinTabletSide method to expand tablet messages"""

    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin

    def before_hooked_method(self, param):
        """Called before getMinTabletSide method"""
        try:
            if not self.plugin.get_setting("enabled", True):
                return

            if not self.plugin.get_setting("hook_tablet_side", True):
                return

            # Update hook statistics
            tablet_side_hooks = self.plugin.get_setting("tablet_side_hooks", 0)
            self.plugin.set_setting("tablet_side_hooks", tablet_side_hooks + 1)

            # Get expansion mode setting
            expansion_mode = self.plugin.get_setting("expansion_mode", 0)
            screen_width = AndroidUtilities.displaySize.x

            try:
                extra_margin = int(self.plugin.get_setting("extra_margin", "200"))
            except (ValueError, TypeError):
                extra_margin = 200

            # Calculate enhanced tablet side width
            if expansion_mode == 0:  # Full Width
                enhanced_width = int(screen_width + extra_margin)
            elif expansion_mode == 1:  # 95% Width
                enhanced_width = int(screen_width * 0.95 + extra_margin)
            elif expansion_mode == 2:  # 90% Width
                enhanced_width = int(screen_width * 0.90 + extra_margin)
            else:  # 85% Width
                enhanced_width = int(screen_width * 0.85 + extra_margin)

            # Override the return value with proper Java Integer type
            from java.lang import Integer
            param.setResult(Integer(enhanced_width))

            self.plugin.log(f"🔧 getMinTabletSide: → {enhanced_width}px (tablet mode)")

        except Exception as e:
            self.plugin.log(f"❌ Error in MinTabletSideHook: {e}")


class ComputeWidthHook(MethodHook):
    """Hook for ChatMessageCell.computeWidth method"""

    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin

    def after_hooked_method(self, param):
        """Called after computeWidth method"""
        try:
            if not self.plugin.get_setting("enabled", True):
                return

            # Update hook statistics
            compute_width_hooks = self.plugin.get_setting("compute_width_hooks", 0)
            self.plugin.set_setting("compute_width_hooks", compute_width_hooks + 1)

            # Get the original computed width
            original_width = param.getResult()
            if original_width is None:
                return

            # Get expansion mode setting
            expansion_mode = self.plugin.get_setting("expansion_mode", 0)
            screen_width = AndroidUtilities.displaySize.x

            try:
                extra_margin = int(self.plugin.get_setting("extra_margin", "200"))
            except (ValueError, TypeError):
                extra_margin = 200

            # Calculate enhanced width
            if expansion_mode == 0:  # Full Width
                enhanced_width = int(screen_width + extra_margin)
            elif expansion_mode == 1:  # 95% Width
                enhanced_width = int(screen_width * 0.95 + extra_margin)
            elif expansion_mode == 2:  # 90% Width
                enhanced_width = int(screen_width * 0.90 + extra_margin)
            else:  # 85% Width
                enhanced_width = int(screen_width * 0.85 + extra_margin)

            # Only enhance if the original width is smaller
            if int(original_width) < enhanced_width:
                from java.lang import Integer
                param.setResult(Integer(enhanced_width))
                self.plugin.log(f"🔧 computeWidth: {original_width}px → {enhanced_width}px")

        except Exception as e:
            self.plugin.log(f"❌ Error in ComputeWidthHook: {e}")


class GroupPhotosWidthHook(MethodHook):
    """Hook for ChatMessageCell.getGroupPhotosWidth method"""

    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin

    def before_hooked_method(self, param):
        """Called before getGroupPhotosWidth method"""
        try:
            if not self.plugin.get_setting("enabled", True):
                return

            if not self.plugin.get_setting("hook_group_photos_width", True):
                return

            # Update hook statistics
            group_photos_width_hooks = self.plugin.get_setting("group_photos_width_hooks", 0)
            self.plugin.set_setting("group_photos_width_hooks", group_photos_width_hooks + 1)

            # Get expansion mode setting
            expansion_mode = self.plugin.get_setting("expansion_mode", 0)
            screen_width = AndroidUtilities.displaySize.x

            try:
                extra_margin = int(self.plugin.get_setting("extra_margin", "200"))
            except (ValueError, TypeError):
                extra_margin = 200

            # Calculate enhanced width
            if expansion_mode == 0:  # Full Width
                enhanced_width = int(screen_width + extra_margin)
            elif expansion_mode == 1:  # 95% Width
                enhanced_width = int(screen_width * 0.95 + extra_margin)
            elif expansion_mode == 2:  # 90% Width
                enhanced_width = int(screen_width * 0.90 + extra_margin)
            else:  # 85% Width
                enhanced_width = int(screen_width * 0.85 + extra_margin)

            # Override the return value with proper Java Integer type
            from java.lang import Integer
            param.setResult(Integer(enhanced_width))

            self.plugin.log(f"🔧 getGroupPhotosWidth: → {enhanced_width}px (group photos mode)")

        except Exception as e:
            self.plugin.log(f"❌ Error in GroupPhotosWidthHook: {e}")


class MaxNameWidthHook(MethodHook):
    """Hook for ChatMessageCell.getMaxNameWidth method"""

    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin

    def before_hooked_method(self, param):
        """Called before getMaxNameWidth method"""
        try:
            if not self.plugin.get_setting("enabled", True):
                return

            if not self.plugin.get_setting("hook_max_name_width", True):
                return

            # Update hook statistics
            max_name_width_hooks = self.plugin.get_setting("max_name_width_hooks", 0)
            self.plugin.set_setting("max_name_width_hooks", max_name_width_hooks + 1)

            # Get expansion mode setting
            expansion_mode = self.plugin.get_setting("expansion_mode", 0)
            screen_width = AndroidUtilities.displaySize.x

            try:
                extra_margin = int(self.plugin.get_setting("extra_margin", "200"))
            except (ValueError, TypeError):
                extra_margin = 200

            # Calculate enhanced width
            if expansion_mode == 0:  # Full Width
                enhanced_width = int(screen_width + extra_margin)
            elif expansion_mode == 1:  # 95% Width
                enhanced_width = int(screen_width * 0.95 + extra_margin)
            elif expansion_mode == 2:  # 90% Width
                enhanced_width = int(screen_width * 0.90 + extra_margin)
            else:  # 85% Width
                enhanced_width = int(screen_width * 0.85 + extra_margin)

            # Override the return value with proper Java Integer type
            from java.lang import Integer
            param.setResult(Integer(enhanced_width))

            self.plugin.log(f"🔧 getMaxNameWidth: → {enhanced_width}px (max name width mode)")

        except Exception as e:
            self.plugin.log(f"❌ Error in MaxNameWidthHook: {e}")


# NEW HOOK CLASSES FOR ADDITIONAL METHODS

class GetWidthHook(MethodHook):
    """Hook for Android View.getWidth method"""

    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin

    def after_hooked_method(self, param):
        """Called after getWidth method"""
        try:
            if not self.plugin.get_setting("enabled", True):
                return

            if not self.plugin.get_setting("hook_get_width", True):
                return

            # Update hook statistics
            get_width_hooks = self.plugin.get_setting("get_width_hooks", 0)
            self.plugin.set_setting("get_width_hooks", get_width_hooks + 1)

            # Get the original width
            original_width = param.getResult()
            if original_width is None:
                return

            # Get expansion mode setting
            expansion_mode = self.plugin.get_setting("expansion_mode", 0)
            screen_width = AndroidUtilities.displaySize.x

            try:
                extra_margin = int(self.plugin.get_setting("extra_margin", "200"))
            except (ValueError, TypeError):
                extra_margin = 200

            # Calculate enhanced width
            if expansion_mode == 0:  # Full Width
                enhanced_width = int(screen_width + extra_margin)
            elif expansion_mode == 1:  # 95% Width
                enhanced_width = int(screen_width * 0.95 + extra_margin)
            elif expansion_mode == 2:  # 90% Width
                enhanced_width = int(screen_width * 0.90 + extra_margin)
            else:  # 85% Width
                enhanced_width = int(screen_width * 0.85 + extra_margin)

            # Only enhance if the original width is smaller
            if int(original_width) < enhanced_width:
                from java.lang import Integer
                param.setResult(Integer(enhanced_width))
                self.plugin.log(f"🔧 getWidth: {original_width}px → {enhanced_width}px")

        except Exception as e:
            self.plugin.log(f"❌ Error in GetWidthHook: {e}")


class GetWidthForButtonsHook(MethodHook):
    """Hook for ChatMessageCell.getWidthForButtons method"""

    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin

    def after_hooked_method(self, param):
        """Called after getWidthForButtons method"""
        try:
            if not self.plugin.get_setting("enabled", True):
                return

            if not self.plugin.get_setting("hook_get_width_for_buttons", True):
                return

            # Update hook statistics
            get_width_for_buttons_hooks = self.plugin.get_setting("get_width_for_buttons_hooks", 0)
            self.plugin.set_setting("get_width_for_buttons_hooks", get_width_for_buttons_hooks + 1)

            # Get the original width
            original_width = param.getResult()
            if original_width is None:
                return

            # Get expansion mode setting
            expansion_mode = self.plugin.get_setting("expansion_mode", 0)
            screen_width = AndroidUtilities.displaySize.x

            try:
                extra_margin = int(self.plugin.get_setting("extra_margin", "200"))
            except (ValueError, TypeError):
                extra_margin = 200

            # Calculate enhanced width
            if expansion_mode == 0:  # Full Width
                enhanced_width = int(screen_width + extra_margin)
            elif expansion_mode == 1:  # 95% Width
                enhanced_width = int(screen_width * 0.95 + extra_margin)
            elif expansion_mode == 2:  # 90% Width
                enhanced_width = int(screen_width * 0.90 + extra_margin)
            else:  # 85% Width
                enhanced_width = int(screen_width * 0.85 + extra_margin)

            # Only enhance if the original width is smaller
            if int(original_width) < enhanced_width:
                from java.lang import Integer
                param.setResult(Integer(enhanced_width))
                self.plugin.log(f"🔧 getWidthForButtons: {original_width}px → {enhanced_width}px")

        except Exception as e:
            self.plugin.log(f"❌ Error in GetWidthForButtonsHook: {e}")


class GetAdditionalWidthForPositionHook(MethodHook):
    """Hook for ChatMessageCell.getAdditionalWidthForPosition method"""

    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin

    def after_hooked_method(self, param):
        """Called after getAdditionalWidthForPosition method"""
        try:
            if not self.plugin.get_setting("enabled", True):
                return

            if not self.plugin.get_setting("hook_get_additional_width_for_position", True):
                return

            # Update hook statistics
            get_additional_width_hooks = self.plugin.get_setting("get_additional_width_for_position_hooks", 0)
            self.plugin.set_setting("get_additional_width_for_position_hooks", get_additional_width_hooks + 1)

            # Get the original width
            original_width = param.getResult()
            if original_width is None:
                return

            # Get expansion mode setting
            expansion_mode = self.plugin.get_setting("expansion_mode", 0)

            # Add extra width based on expansion mode
            extra_width = 0
            if expansion_mode == 0:  # Full Width
                extra_width = 8  # Add more spacing for full width
            elif expansion_mode == 1:  # 95% Width
                extra_width = 6
            elif expansion_mode == 2:  # 90% Width
                extra_width = 4
            else:  # 85% Width
                extra_width = 2

            enhanced_width = int(original_width) + extra_width

            from java.lang import Integer
            param.setResult(Integer(enhanced_width))
            self.plugin.log(f"🔧 getAdditionalWidthForPosition: {original_width}px → {enhanced_width}px")

        except Exception as e:
            self.plugin.log(f"❌ Error in GetAdditionalWidthForPositionHook: {e}")


class OnMeasureHook(MethodHook):
    """Hook for ChatMessageCell.onMeasure method"""

    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin

    def before_hooked_method(self, param):
        """Called before onMeasure method"""
        try:
            if not self.plugin.get_setting("enabled", True):
                return

            if not self.plugin.get_setting("hook_on_measure", True):
                return

            # Update hook statistics
            on_measure_hooks = self.plugin.get_setting("on_measure_hooks", 0)
            self.plugin.set_setting("on_measure_hooks", on_measure_hooks + 1)

            # Get the width and height measure specs
            width_measure_spec = param.args[0]
            height_measure_spec = param.args[1]

            # Get expansion mode setting
            expansion_mode = self.plugin.get_setting("expansion_mode", 0)
            screen_width = AndroidUtilities.displaySize.x

            try:
                extra_margin = int(self.plugin.get_setting("extra_margin", "200"))
            except (ValueError, TypeError):
                extra_margin = 200

            # Calculate enhanced width
            if expansion_mode == 0:  # Full Width
                enhanced_width = int(screen_width + extra_margin)
            elif expansion_mode == 1:  # 95% Width
                enhanced_width = int(screen_width * 0.95 + extra_margin)
            elif expansion_mode == 2:  # 90% Width
                enhanced_width = int(screen_width * 0.90 + extra_margin)
            else:  # 85% Width
                enhanced_width = int(screen_width * 0.85 + extra_margin)

            # Create new width measure spec with enhanced width
            from android.view import View
            new_width_spec = View.MeasureSpec.makeMeasureSpec(enhanced_width, View.MeasureSpec.EXACTLY)

            # Update the parameter
            from java.lang import Integer
            param.args[0] = Integer(new_width_spec)

            self.plugin.log(f"🔧 onMeasure: Enhanced width spec to {enhanced_width}px")

        except Exception as e:
            self.plugin.log(f"❌ Error in OnMeasureHook: {e}")


class ComputeHeightHook(MethodHook):
    """Hook for ChatMessageCell.computeHeight method"""

    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin

    def after_hooked_method(self, param):
        """Called after computeHeight method"""
        try:
            if not self.plugin.get_setting("enabled", True):
                return

            if not self.plugin.get_setting("hook_compute_height", True):
                return

            # Update hook statistics
            compute_height_hooks = self.plugin.get_setting("compute_height_hooks", 0)
            self.plugin.set_setting("compute_height_hooks", compute_height_hooks + 1)

            # Get the original height
            original_height = param.getResult()
            if original_height is None:
                return

            # For height, we don't need to modify much, just log the activity
            self.plugin.log(f"🔧 computeHeight: {original_height}px (height calculation)")

        except Exception as e:
            self.plugin.log(f"❌ Error in ComputeHeightHook: {e}")


class ComputeScrollHook(MethodHook):
    """Hook for ChatMessageCell.computeScroll method"""

    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin

    def before_hooked_method(self, param):
        """Called before computeScroll method"""
        try:
            if not self.plugin.get_setting("enabled", True):
                return

            if not self.plugin.get_setting("hook_compute_scroll", True):
                return

            # Update hook statistics
            compute_scroll_hooks = self.plugin.get_setting("compute_scroll_hooks", 0)
            self.plugin.set_setting("compute_scroll_hooks", compute_scroll_hooks + 1)

            # For scroll computation, we mainly just log the activity
            self.plugin.log(f"🔧 computeScroll: Scroll computation called")

        except Exception as e:
            self.plugin.log(f"❌ Error in ComputeScrollHook: {e}")


class SetParentBoundsHook(MethodHook):
    """Hook for ChatMessageCell.setParentBounds method"""

    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin

    def before_hooked_method(self, param):
        """Called before setParentBounds method"""
        try:
            if not self.plugin.get_setting("enabled", True):
                return

            if not self.plugin.get_setting("hook_set_parent_bounds", True):
                return

            # Update hook statistics
            set_parent_bounds_hooks = self.plugin.get_setting("set_parent_bounds_hooks", 0)
            self.plugin.set_setting("set_parent_bounds_hooks", set_parent_bounds_hooks + 1)

            # Get the parameters: float chatListViewPaddingTop, int blurredViewBottomOffset
            chat_list_view_padding_top = param.args[0]  # float
            blurred_view_bottom_offset = param.args[1]  # int

            # Get expansion mode setting
            expansion_mode = self.plugin.get_setting("expansion_mode", 0)
            screen_width = AndroidUtilities.displaySize.x

            try:
                extra_margin = int(self.plugin.get_setting("extra_margin", "200"))
            except (ValueError, TypeError):
                extra_margin = 200

            # Calculate enhanced width for bounds
            if expansion_mode == 0:  # Full Width
                enhanced_width = int(screen_width + extra_margin)
            elif expansion_mode == 1:  # 95% Width
                enhanced_width = int(screen_width * 0.95 + extra_margin)
            elif expansion_mode == 2:  # 90% Width
                enhanced_width = int(screen_width * 0.90 + extra_margin)
            else:  # 85% Width
                enhanced_width = int(screen_width * 0.85 + extra_margin)

            # We can modify the blurred_view_bottom_offset to account for wider messages
            # Add some extra offset based on the width enhancement
            width_diff = enhanced_width - screen_width
            enhanced_offset = int(blurred_view_bottom_offset) + max(0, width_diff // 8)

            from java.lang import Integer, Float
            # Keep the original padding top as float, enhance the bottom offset as int
            param.args[1] = Integer(enhanced_offset)

            self.plugin.log(f"🔧 setParentBounds: Enhanced bounds offset {blurred_view_bottom_offset} → {enhanced_offset}")

        except Exception as e:
            self.plugin.log(f"❌ Error in SetParentBoundsHook: {e}")