diff --git a/base/third_party/cityhash_v103/src/city_v103.cc b/base/third_party/cityhash_v103/src/city_v103.cc
index bd6bc1aa2c8c..e02d20fbbaf7 100644
--- a/base/third_party/cityhash_v103/src/city_v103.cc
+++ b/base/third_party/cityhash_v103/src/city_v103.cc
@@ -27,8 +27,7 @@
 // possible hash functions, by using SIMD instructions, or by
 // compromising on hash quality.
 
-#include "config.h"
-#include <city.h>
+#include "base/third_party/cityhash_v103/src/city_v103.h"
 
 #include <algorithm>
 #include <string.h>  // for memcpy and memset
