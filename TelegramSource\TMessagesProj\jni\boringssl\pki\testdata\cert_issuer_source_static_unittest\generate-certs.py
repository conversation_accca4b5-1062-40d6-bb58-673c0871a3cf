#!/usr/bin/env python
# Copyright 2016 The Chromium Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Certificates for testing issuer lookup.

                Root
             /|   |   |\\
            / |   |   | \\
           /  |   |   |  \\
          /   |   |   |   \\
         /    |   |   |    \\
        v     v   v   v     v
      I1_1  i1_2  I2  I3_1 I3_2
        |    |    |   |    |
        |    |    |   |    |
        |    |    |   |    |
        |    |    |   |    |
        v    v    v   |    |
       C1    C2   D   E1   E2

I1 (i1_1.pem) and i1 (i1_2.pem) have subjects that are equal after
normalization.

I3_1 and I3_2 have subjects that are exactly equal.

C1 and C2 should (attempt to) chain up through both I1 and i1, since I1 and i1
have the same the name (after normalization).

E1 and E3 should (attempt to) chain up through both I3 intermediates.
"""

import os
import sys
sys.path += ['..']

import gencerts


def write_cert_to_file(cert, filename):
  gencerts.write_string_to_file(
      "Generated by %s.\n"
      "Refer to generator script docstring for details.\n%s" % (
          sys.argv[0], cert.get_cert_pem()),
      filename)


# Self-signed root certificate
root = gencerts.create_self_signed_root_certificate('Root')
write_cert_to_file(root, 'root.pem')


# Intermediate certificates
i1_1 = gencerts.create_intermediate_certificate('I1', root)
write_cert_to_file(i1_1, 'i1_1.pem')

# same name (after normalization), different key
i1_2 = gencerts.create_intermediate_certificate('i1', root)
write_cert_to_file(i1_2, 'i1_2.pem')

# different name
i2 = gencerts.create_intermediate_certificate('I2', root)
write_cert_to_file(i2, 'i2.pem')

# Two intermediates with exactly the same name.
i3_1 = gencerts.create_intermediate_certificate('I3', root)
write_cert_to_file(i3_1, 'i3_1.pem')
i3_2 = gencerts.create_intermediate_certificate('I3', root)
write_cert_to_file(i3_2, 'i3_2.pem')

# target certs

c1 = gencerts.create_end_entity_certificate('C1', i1_1)
write_cert_to_file(c1, 'c1.pem')

c2 = gencerts.create_end_entity_certificate('C2', i1_2)
write_cert_to_file(c2, 'c2.pem')

d = gencerts.create_end_entity_certificate('D', i2)
write_cert_to_file(d, 'd.pem')

e1 = gencerts.create_end_entity_certificate('E1', i3_1)
write_cert_to_file(e1, 'e1.pem')

e2 = gencerts.create_end_entity_certificate('E2', i3_2)
write_cert_to_file(e2, 'e2.pem')

