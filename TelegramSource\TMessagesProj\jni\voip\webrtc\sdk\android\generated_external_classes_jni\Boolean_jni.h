// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     java/lang/Boolean

#ifndef java_lang_Boolean_JNI
#define java_lang_Boolean_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_java_lang_Boolean[];
const char kClassPath_java_lang_Boolean[] = "java/lang/Boolean";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_java_lang_Boolean_clazz(nullptr);
#ifndef java_lang_Boolean_clazz_defined
#define java_lang_Boolean_clazz_defined
inline jclass java_lang_Boolean_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_java_lang_Boolean, &g_java_lang_Boolean_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace JNI_Boolean {


static std::atomic<jmethodID> g_java_lang_Boolean_Constructor__String1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_Boolean_Constructor__String(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Boolean_Constructor__String(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Boolean_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Ljava/lang/String;)V",
          &g_java_lang_Boolean_Constructor__String1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Boolean_Constructor__boolean1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_Boolean_Constructor__boolean(JNIEnv* env, jboolean p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Boolean_Constructor__boolean(JNIEnv* env, jboolean
    p0) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Boolean_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Z)V",
          &g_java_lang_Boolean_Constructor__boolean1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, p0);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Boolean_booleanValue0(nullptr);
[[maybe_unused]] static jboolean Java_Boolean_booleanValue(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jboolean Java_Boolean_booleanValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Boolean_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "booleanValue",
          "()Z",
          &g_java_lang_Boolean_booleanValue0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Boolean_compare2(nullptr);
[[maybe_unused]] static jint Java_Boolean_compare(JNIEnv* env, jboolean p0,
    jboolean p1);
static jint Java_Boolean_compare(JNIEnv* env, jboolean p0,
    jboolean p1) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Boolean_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "compare",
          "(ZZ)I",
          &g_java_lang_Boolean_compare2);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0, p1);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Boolean_compareTo__Boolean1(nullptr);
[[maybe_unused]] static jint Java_Boolean_compareTo__Boolean(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jint Java_Boolean_compareTo__Boolean(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Boolean_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "compareTo",
          "(Ljava/lang/Boolean;)I",
          &g_java_lang_Boolean_compareTo__Boolean1);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Boolean_compareTo__Object1(nullptr);
[[maybe_unused]] static jint Java_Boolean_compareTo__Object(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jint Java_Boolean_compareTo__Object(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Boolean_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "compareTo",
          "(Ljava/lang/Object;)I",
          &g_java_lang_Boolean_compareTo__Object1);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Boolean_equals1(nullptr);
[[maybe_unused]] static jboolean Java_Boolean_equals(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_Boolean_equals(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Boolean_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "equals",
          "(Ljava/lang/Object;)Z",
          &g_java_lang_Boolean_equals1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Boolean_getBoolean1(nullptr);
[[maybe_unused]] static jboolean Java_Boolean_getBoolean(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0);
static jboolean Java_Boolean_getBoolean(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Boolean_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getBoolean",
          "(Ljava/lang/String;)Z",
          &g_java_lang_Boolean_getBoolean1);

  jboolean ret =
      env->CallStaticBooleanMethod(clazz,
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Boolean_hashCode0(nullptr);
[[maybe_unused]] static jint Java_Boolean_hashCode(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jint Java_Boolean_hashCode(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Boolean_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "hashCode",
          "()I",
          &g_java_lang_Boolean_hashCode0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Boolean_hashCode1(nullptr);
[[maybe_unused]] static jint Java_Boolean_hashCode(JNIEnv* env, jboolean p0);
static jint Java_Boolean_hashCode(JNIEnv* env, jboolean p0) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Boolean_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "hashCode",
          "(Z)I",
          &g_java_lang_Boolean_hashCode1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Boolean_logicalAnd2(nullptr);
[[maybe_unused]] static jboolean Java_Boolean_logicalAnd(JNIEnv* env, jboolean p0,
    jboolean p1);
static jboolean Java_Boolean_logicalAnd(JNIEnv* env, jboolean p0,
    jboolean p1) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Boolean_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "logicalAnd",
          "(ZZ)Z",
          &g_java_lang_Boolean_logicalAnd2);

  jboolean ret =
      env->CallStaticBooleanMethod(clazz,
          call_context.base.method_id, p0, p1);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Boolean_logicalOr2(nullptr);
[[maybe_unused]] static jboolean Java_Boolean_logicalOr(JNIEnv* env, jboolean p0,
    jboolean p1);
static jboolean Java_Boolean_logicalOr(JNIEnv* env, jboolean p0,
    jboolean p1) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Boolean_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "logicalOr",
          "(ZZ)Z",
          &g_java_lang_Boolean_logicalOr2);

  jboolean ret =
      env->CallStaticBooleanMethod(clazz,
          call_context.base.method_id, p0, p1);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Boolean_logicalXor2(nullptr);
[[maybe_unused]] static jboolean Java_Boolean_logicalXor(JNIEnv* env, jboolean p0,
    jboolean p1);
static jboolean Java_Boolean_logicalXor(JNIEnv* env, jboolean p0,
    jboolean p1) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Boolean_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "logicalXor",
          "(ZZ)Z",
          &g_java_lang_Boolean_logicalXor2);

  jboolean ret =
      env->CallStaticBooleanMethod(clazz,
          call_context.base.method_id, p0, p1);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Boolean_parseBoolean1(nullptr);
[[maybe_unused]] static jboolean Java_Boolean_parseBoolean(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0);
static jboolean Java_Boolean_parseBoolean(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Boolean_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "parseBoolean",
          "(Ljava/lang/String;)Z",
          &g_java_lang_Boolean_parseBoolean1);

  jboolean ret =
      env->CallStaticBooleanMethod(clazz,
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Boolean_toString0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Boolean_toString(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Boolean_toString(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Boolean_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "toString",
          "()Ljava/lang/String;",
          &g_java_lang_Boolean_toString0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Boolean_toString1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Boolean_toString(JNIEnv* env,
    jboolean p0);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Boolean_toString(JNIEnv* env, jboolean p0) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Boolean_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toString",
          "(Z)Ljava/lang/String;",
          &g_java_lang_Boolean_toString1);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Boolean_valueOf__String1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Boolean_valueOf__String(JNIEnv*
    env, const jni_zero::JavaRef<jstring>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Boolean_valueOf__String(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Boolean_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "valueOf",
          "(Ljava/lang/String;)Ljava/lang/Boolean;",
          &g_java_lang_Boolean_valueOf__String1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Boolean_valueOf__boolean1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Boolean_valueOf__boolean(JNIEnv*
    env, jboolean p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Boolean_valueOf__boolean(JNIEnv* env, jboolean p0)
    {
  jclass clazz = java_lang_Boolean_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Boolean_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "valueOf",
          "(Z)Ljava/lang/Boolean;",
          &g_java_lang_Boolean_valueOf__boolean1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace JNI_Boolean

#endif  // java_lang_Boolean_JNI
