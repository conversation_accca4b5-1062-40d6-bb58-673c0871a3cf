Name: ICU
URL: http://site.icu-project.org/
Version: 60
License: Unicode
License File: NOT_SHIPPED

This file has the relevant components from ICU copied to handle basic UTF8/16/32
conversions. Components are copied from umachine.h, utf.h, utf8.h, and utf16.h
into icu_utf.h, and from utf_impl.cpp into icu_utf.cc.

The main change is that U_/U8_/U16_ prefixes have been replaced with
CBU_/CBU8_/CBU16_ (for "Chrome Base") to avoid confusion with the "real" ICU
macros should ICU be in use on the system. For the same reason, the functions
and types have been put in the "base_icu" namespace.

Note that this license file is marked as NOT_SHIPPED, since a more complete
ICU license is included from //third_party/icu/README.chromium
