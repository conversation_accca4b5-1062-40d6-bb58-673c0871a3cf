# Copyright (c) 2017 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../../webrtc.gni")
if (is_android) {
  import("//build/config/android/config.gni")
  import("//build/config/android/rules.gni")
}

if (rtc_include_tests) {
  rtc_library("audio_codecs_api_unittests") {
    testonly = true
    sources = [
      "audio_decoder_factory_template_unittest.cc",
      "audio_encoder_factory_template_unittest.cc",
    ]
    deps = [
      "..:audio_codecs_api",
      "../../../test:audio_codec_mocks",
      "../../../test:scoped_key_value_config",
      "../../../test:test_support",
      "../L16:audio_decoder_L16",
      "../L16:audio_encoder_L16",
      "../g711:audio_decoder_g711",
      "../g711:audio_encoder_g711",
      "../g722:audio_decoder_g722",
      "../g722:audio_encoder_g722",
      "../ilbc:audio_decoder_ilbc",
      "../ilbc:audio_encoder_ilbc",
      "../opus:audio_decoder_opus",
      "../opus:audio_encoder_opus",
    ]
  }
}
