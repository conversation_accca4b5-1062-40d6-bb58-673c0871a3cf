// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/VideoEncoderFactory

#ifndef org_webrtc_VideoEncoderFactory_JNI
#define org_webrtc_VideoEncoderFactory_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_VideoEncoderFactory[];
const char kClassPath_org_webrtc_VideoEncoderFactory[] = "org/webrtc/VideoEncoderFactory";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector[];
const char kClassPath_org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector[] =
    "org/webrtc/VideoEncoderFactory$VideoEncoderSelector";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_VideoEncoderFactory_clazz(nullptr);
#ifndef org_webrtc_VideoEncoderFactory_clazz_defined
#define org_webrtc_VideoEncoderFactory_clazz_defined
inline jclass org_webrtc_VideoEncoderFactory_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_VideoEncoderFactory,
      &g_org_webrtc_VideoEncoderFactory_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_clazz(nullptr);
#ifndef org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_clazz_defined
#define org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_clazz_defined
inline jclass org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env,
      kClassPath_org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector,
      &g_org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_VideoEncoderFactory_createEncoder1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoEncoderFactory_createEncoder(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& info) {
  jclass clazz = org_webrtc_VideoEncoderFactory_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoderFactory_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "createEncoder",
          "(Lorg/webrtc/VideoCodecInfo;)Lorg/webrtc/VideoEncoder;",
          &g_org_webrtc_VideoEncoderFactory_createEncoder1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, info.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoderFactory_getEncoderSelector0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoEncoderFactory_getEncoderSelector(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_VideoEncoderFactory_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoderFactory_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getEncoderSelector",
          "()Lorg/webrtc/VideoEncoderFactory$VideoEncoderSelector;",
          &g_org_webrtc_VideoEncoderFactory_getEncoderSelector0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoderFactory_getImplementations0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobjectArray>
    Java_VideoEncoderFactory_getImplementations(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj)
    {
  jclass clazz = org_webrtc_VideoEncoderFactory_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoderFactory_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getImplementations",
          "()[Lorg/webrtc/VideoCodecInfo;",
          &g_org_webrtc_VideoEncoderFactory_getImplementations0);

  jobjectArray ret =
      static_cast<jobjectArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jobjectArray>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_VideoEncoderFactory_getSupportedCodecs0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobjectArray>
    Java_VideoEncoderFactory_getSupportedCodecs(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj)
    {
  jclass clazz = org_webrtc_VideoEncoderFactory_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoderFactory_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getSupportedCodecs",
          "()[Lorg/webrtc/VideoCodecInfo;",
          &g_org_webrtc_VideoEncoderFactory_getSupportedCodecs0);

  jobjectArray ret =
      static_cast<jobjectArray>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jobjectArray>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_onAvailableBitrate1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoEncoderSelector_onAvailableBitrate(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj, JniIntWrapper kbps) {
  jclass clazz = org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onAvailableBitrate",
          "(I)Lorg/webrtc/VideoCodecInfo;",
          &g_org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_onAvailableBitrate1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(kbps));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_onCurrentEncoder1(nullptr);
static void Java_VideoEncoderSelector_onCurrentEncoder(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& info) {
  jclass clazz = org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onCurrentEncoder",
          "(Lorg/webrtc/VideoCodecInfo;)V",
          &g_org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_onCurrentEncoder1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, info.obj());
}

static std::atomic<jmethodID>
    g_org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_onEncoderBroken0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoEncoderSelector_onEncoderBroken(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onEncoderBroken",
          "()Lorg/webrtc/VideoCodecInfo;",
          &g_org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_onEncoderBroken0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_onResolutionChange2(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_VideoEncoderSelector_onResolutionChange(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj, JniIntWrapper widht,
    JniIntWrapper height) {
  jclass clazz = org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onResolutionChange",
          "(II)Lorg/webrtc/VideoCodecInfo;",
          &g_org_webrtc_VideoEncoderFactory_00024VideoEncoderSelector_onResolutionChange2);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, as_jint(widht), as_jint(height));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_VideoEncoderFactory_JNI
