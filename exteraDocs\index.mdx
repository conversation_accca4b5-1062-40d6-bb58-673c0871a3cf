---
title: Introduction
description: Plugin development in all Telegram developers familiar language.
icon: PackagePlus
---

# exteraGram Plugins

Our plugins system is powered by [Chaquopy v15](https://chaquo.com/chaquopy/doc/15.0/) and [<PERSON><PERSON><PERSON> hook](https://github.com/Aliucord/hook).

Developers may write plugins in **Python** and use **Xposed** method hooking to change app behaviour.

## Chaquopy

[Chaquopy](https://github.com/chaquo/chaquopy) is a Java library that provides interop between Java and Python, allowing you to write plugins in Python.

## Aliucord hook

[Aliucord](https://github.com/<PERSON>ucord) itself is a modification for the Discord Android app. We use their [hook](https://github.com/Aliucord/hook) to provide **Xposed** functionality for plugins.