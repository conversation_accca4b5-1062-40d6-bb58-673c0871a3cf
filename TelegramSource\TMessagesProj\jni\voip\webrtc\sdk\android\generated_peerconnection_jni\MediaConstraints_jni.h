// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/MediaConstraints

#ifndef org_webrtc_MediaConstraints_JNI
#define org_webrtc_MediaConstraints_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_MediaConstraints[];
const char kClassPath_org_webrtc_MediaConstraints[] = "org/webrtc/MediaConstraints";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_MediaConstraints_00024KeyValuePair[];
const char kClassPath_org_webrtc_MediaConstraints_00024KeyValuePair[] =
    "org/webrtc/MediaConstraints$KeyValuePair";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_MediaConstraints_clazz(nullptr);
#ifndef org_webrtc_MediaConstraints_clazz_defined
#define org_webrtc_MediaConstraints_clazz_defined
inline jclass org_webrtc_MediaConstraints_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_MediaConstraints,
      &g_org_webrtc_MediaConstraints_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_MediaConstraints_00024KeyValuePair_clazz(nullptr);
#ifndef org_webrtc_MediaConstraints_00024KeyValuePair_clazz_defined
#define org_webrtc_MediaConstraints_00024KeyValuePair_clazz_defined
inline jclass org_webrtc_MediaConstraints_00024KeyValuePair_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_MediaConstraints_00024KeyValuePair,
      &g_org_webrtc_MediaConstraints_00024KeyValuePair_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_MediaConstraints_getMandatory0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_MediaConstraints_getMandatory(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_MediaConstraints_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_MediaConstraints_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getMandatory",
          "()Ljava/util/List;",
          &g_org_webrtc_MediaConstraints_getMandatory0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_MediaConstraints_getOptional0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_MediaConstraints_getOptional(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_MediaConstraints_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_MediaConstraints_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getOptional",
          "()Ljava/util/List;",
          &g_org_webrtc_MediaConstraints_getOptional0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_MediaConstraints_00024KeyValuePair_getKey0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_KeyValuePair_getKey(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_MediaConstraints_00024KeyValuePair_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_MediaConstraints_00024KeyValuePair_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getKey",
          "()Ljava/lang/String;",
          &g_org_webrtc_MediaConstraints_00024KeyValuePair_getKey0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_MediaConstraints_00024KeyValuePair_getValue0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_KeyValuePair_getValue(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_MediaConstraints_00024KeyValuePair_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_MediaConstraints_00024KeyValuePair_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getValue",
          "()Ljava/lang/String;",
          &g_org_webrtc_MediaConstraints_00024KeyValuePair_getValue0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_MediaConstraints_JNI
