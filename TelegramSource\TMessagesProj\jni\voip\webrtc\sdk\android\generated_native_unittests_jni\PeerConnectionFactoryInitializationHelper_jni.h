// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/PeerConnectionFactoryInitializationHelper

#ifndef org_webrtc_PeerConnectionFactoryInitializationHelper_JNI
#define org_webrtc_PeerConnectionFactoryInitializationHelper_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_PeerConnectionFactoryInitializationHelper[];
const char kClassPath_org_webrtc_PeerConnectionFactoryInitializationHelper[] =
    "org/webrtc/PeerConnectionFactoryInitializationHelper";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_PeerConnectionFactoryInitializationHelper_clazz(nullptr);
#ifndef org_webrtc_PeerConnectionFactoryInitializationHelper_clazz_defined
#define org_webrtc_PeerConnectionFactoryInitializationHelper_clazz_defined
inline jclass org_webrtc_PeerConnectionFactoryInitializationHelper_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env,
      kClassPath_org_webrtc_PeerConnectionFactoryInitializationHelper,
      &g_org_webrtc_PeerConnectionFactoryInitializationHelper_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID>
    g_org_webrtc_PeerConnectionFactoryInitializationHelper_initializeFactoryForTests0(nullptr);
static void Java_PeerConnectionFactoryInitializationHelper_initializeFactoryForTests(JNIEnv* env) {
  jclass clazz = org_webrtc_PeerConnectionFactoryInitializationHelper_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_PeerConnectionFactoryInitializationHelper_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "initializeFactoryForTests",
          "()V",
          &g_org_webrtc_PeerConnectionFactoryInitializationHelper_initializeFactoryForTests0);

     env->CallStaticVoidMethod(clazz,
          call_context.base.method_id);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_PeerConnectionFactoryInitializationHelper_JNI
