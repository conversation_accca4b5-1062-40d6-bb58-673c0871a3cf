#include <jni.h>

#ifndef _Included_org_telegram_messenger_voip_Instance
#define _Included_org_telegram_messenger_voip_Instance
#ifdef __cplusplus

extern "C" {
#endif
#undef org_telegram_messenger_voip_Instance_NET_TYPE_UNKNOWN
#define org_telegram_messenger_voip_Instance_NET_TYPE_UNKNOWN 0L
#undef org_telegram_messenger_voip_Instance_NET_TYPE_GPRS
#define org_telegram_messenger_voip_Instance_NET_TYPE_GPRS 1L
#undef org_telegram_messenger_voip_Instance_NET_TYPE_EDGE
#define org_telegram_messenger_voip_Instance_NET_TYPE_EDGE 2L
#undef org_telegram_messenger_voip_Instance_NET_TYPE_3G
#define org_telegram_messenger_voip_Instance_NET_TYPE_3G 3L
#undef org_telegram_messenger_voip_Instance_NET_TYPE_HSPA
#define org_telegram_messenger_voip_Instance_NET_TYPE_HSPA 4L
#undef org_telegram_messenger_voip_Instance_NET_TYPE_LTE
#define org_telegram_messenger_voip_Instance_NET_TYPE_LTE 5L
#undef org_telegram_messenger_voip_Instance_NET_TYPE_WIFI
#define org_telegram_messenger_voip_Instance_NET_TYPE_WIFI 6L
#undef org_telegram_messenger_voip_Instance_NET_TYPE_ETHERNET
#define org_telegram_messenger_voip_Instance_NET_TYPE_ETHERNET 7L
#undef org_telegram_messenger_voip_Instance_NET_TYPE_OTHER_HIGH_SPEED
#define org_telegram_messenger_voip_Instance_NET_TYPE_OTHER_HIGH_SPEED 8L
#undef org_telegram_messenger_voip_Instance_NET_TYPE_OTHER_LOW_SPEED
#define org_telegram_messenger_voip_Instance_NET_TYPE_OTHER_LOW_SPEED 9L
#undef org_telegram_messenger_voip_Instance_NET_TYPE_DIALUP
#define org_telegram_messenger_voip_Instance_NET_TYPE_DIALUP 10L
#undef org_telegram_messenger_voip_Instance_NET_TYPE_OTHER_MOBILE
#define org_telegram_messenger_voip_Instance_NET_TYPE_OTHER_MOBILE 11L
#undef org_telegram_messenger_voip_Instance_ENDPOINT_TYPE_INET
#define org_telegram_messenger_voip_Instance_ENDPOINT_TYPE_INET 0L
#undef org_telegram_messenger_voip_Instance_ENDPOINT_TYPE_LAN
#define org_telegram_messenger_voip_Instance_ENDPOINT_TYPE_LAN 1L
#undef org_telegram_messenger_voip_Instance_ENDPOINT_TYPE_UDP_RELAY
#define org_telegram_messenger_voip_Instance_ENDPOINT_TYPE_UDP_RELAY 2L
#undef org_telegram_messenger_voip_Instance_ENDPOINT_TYPE_TCP_RELAY
#define org_telegram_messenger_voip_Instance_ENDPOINT_TYPE_TCP_RELAY 3L
#undef org_telegram_messenger_voip_Instance_STATE_WAIT_INIT
#define org_telegram_messenger_voip_Instance_STATE_WAIT_INIT 1L
#undef org_telegram_messenger_voip_Instance_STATE_WAIT_INIT_ACK
#define org_telegram_messenger_voip_Instance_STATE_WAIT_INIT_ACK 2L
#undef org_telegram_messenger_voip_Instance_STATE_ESTABLISHED
#define org_telegram_messenger_voip_Instance_STATE_ESTABLISHED 3L
#undef org_telegram_messenger_voip_Instance_STATE_FAILED
#define org_telegram_messenger_voip_Instance_STATE_FAILED 4L
#undef org_telegram_messenger_voip_Instance_STATE_RECONNECTING
#define org_telegram_messenger_voip_Instance_STATE_RECONNECTING 5L
#undef org_telegram_messenger_voip_Instance_DATA_SAVING_NEVER
#define org_telegram_messenger_voip_Instance_DATA_SAVING_NEVER 0L
#undef org_telegram_messenger_voip_Instance_DATA_SAVING_MOBILE
#define org_telegram_messenger_voip_Instance_DATA_SAVING_MOBILE 1L
#undef org_telegram_messenger_voip_Instance_DATA_SAVING_ALWAYS
#define org_telegram_messenger_voip_Instance_DATA_SAVING_ALWAYS 2L
#undef org_telegram_messenger_voip_Instance_DATA_SAVING_ROAMING
#define org_telegram_messenger_voip_Instance_DATA_SAVING_ROAMING 3L
#undef org_telegram_messenger_voip_Instance_PEER_CAP_GROUP_CALLS
#define org_telegram_messenger_voip_Instance_PEER_CAP_GROUP_CALLS 1L
#ifdef __cplusplus
}
#endif
#endif
