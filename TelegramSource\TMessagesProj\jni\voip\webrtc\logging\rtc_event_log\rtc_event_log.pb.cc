// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: rtc_event_log.proto

#include "rtc_event_log.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/io/zero_copy_stream_impl_lite.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace webrtc {
namespace rtclog {
PROTOBUF_CONSTEXPR EventStream::EventStream(
    ::_pbi::ConstantInitialized)
  : stream_(){}
struct EventStreamDefaultTypeInternal {
  PROTOBUF_CONSTEXPR EventStreamDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~EventStreamDefaultTypeInternal() {}
  union {
    EventStream _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 EventStreamDefaultTypeInternal _EventStream_default_instance_;
PROTOBUF_CONSTEXPR Event::Event(
    ::_pbi::ConstantInitialized)
  : timestamp_us_(int64_t{0})
  , type_(0)

  , _oneof_case_{}{}
struct EventDefaultTypeInternal {
  PROTOBUF_CONSTEXPR EventDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~EventDefaultTypeInternal() {}
  union {
    Event _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 EventDefaultTypeInternal _Event_default_instance_;
PROTOBUF_CONSTEXPR RtpPacket::RtpPacket(
    ::_pbi::ConstantInitialized)
  : header_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , incoming_(false)
  , type_(0)

  , packet_length_(0u)
  , probe_cluster_id_(0){}
struct RtpPacketDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RtpPacketDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RtpPacketDefaultTypeInternal() {}
  union {
    RtpPacket _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RtpPacketDefaultTypeInternal _RtpPacket_default_instance_;
PROTOBUF_CONSTEXPR RtcpPacket::RtcpPacket(
    ::_pbi::ConstantInitialized)
  : packet_data_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , incoming_(false)
  , type_(0)
{}
struct RtcpPacketDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RtcpPacketDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RtcpPacketDefaultTypeInternal() {}
  union {
    RtcpPacket _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RtcpPacketDefaultTypeInternal _RtcpPacket_default_instance_;
PROTOBUF_CONSTEXPR AudioPlayoutEvent::AudioPlayoutEvent(
    ::_pbi::ConstantInitialized)
  : local_ssrc_(0u){}
struct AudioPlayoutEventDefaultTypeInternal {
  PROTOBUF_CONSTEXPR AudioPlayoutEventDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~AudioPlayoutEventDefaultTypeInternal() {}
  union {
    AudioPlayoutEvent _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 AudioPlayoutEventDefaultTypeInternal _AudioPlayoutEvent_default_instance_;
PROTOBUF_CONSTEXPR LossBasedBweUpdate::LossBasedBweUpdate(
    ::_pbi::ConstantInitialized)
  : bitrate_bps_(0)
  , fraction_loss_(0u)
  , total_packets_(0){}
struct LossBasedBweUpdateDefaultTypeInternal {
  PROTOBUF_CONSTEXPR LossBasedBweUpdateDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~LossBasedBweUpdateDefaultTypeInternal() {}
  union {
    LossBasedBweUpdate _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 LossBasedBweUpdateDefaultTypeInternal _LossBasedBweUpdate_default_instance_;
PROTOBUF_CONSTEXPR DelayBasedBweUpdate::DelayBasedBweUpdate(
    ::_pbi::ConstantInitialized)
  : bitrate_bps_(0)
  , detector_state_(0)
{}
struct DelayBasedBweUpdateDefaultTypeInternal {
  PROTOBUF_CONSTEXPR DelayBasedBweUpdateDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~DelayBasedBweUpdateDefaultTypeInternal() {}
  union {
    DelayBasedBweUpdate _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 DelayBasedBweUpdateDefaultTypeInternal _DelayBasedBweUpdate_default_instance_;
PROTOBUF_CONSTEXPR VideoReceiveConfig::VideoReceiveConfig(
    ::_pbi::ConstantInitialized)
  : rtx_map_()
  , header_extensions_()
  , decoders_()
  , remote_ssrc_(0u)
  , local_ssrc_(0u)
  , remb_(false)
  , rtcp_mode_(1)
{}
struct VideoReceiveConfigDefaultTypeInternal {
  PROTOBUF_CONSTEXPR VideoReceiveConfigDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~VideoReceiveConfigDefaultTypeInternal() {}
  union {
    VideoReceiveConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 VideoReceiveConfigDefaultTypeInternal _VideoReceiveConfig_default_instance_;
PROTOBUF_CONSTEXPR DecoderConfig::DecoderConfig(
    ::_pbi::ConstantInitialized)
  : name_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , payload_type_(0){}
struct DecoderConfigDefaultTypeInternal {
  PROTOBUF_CONSTEXPR DecoderConfigDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~DecoderConfigDefaultTypeInternal() {}
  union {
    DecoderConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 DecoderConfigDefaultTypeInternal _DecoderConfig_default_instance_;
PROTOBUF_CONSTEXPR RtpHeaderExtension::RtpHeaderExtension(
    ::_pbi::ConstantInitialized)
  : name_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , id_(0){}
struct RtpHeaderExtensionDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RtpHeaderExtensionDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RtpHeaderExtensionDefaultTypeInternal() {}
  union {
    RtpHeaderExtension _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RtpHeaderExtensionDefaultTypeInternal _RtpHeaderExtension_default_instance_;
PROTOBUF_CONSTEXPR RtxConfig::RtxConfig(
    ::_pbi::ConstantInitialized)
  : rtx_ssrc_(0u)
  , rtx_payload_type_(0){}
struct RtxConfigDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RtxConfigDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RtxConfigDefaultTypeInternal() {}
  union {
    RtxConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RtxConfigDefaultTypeInternal _RtxConfig_default_instance_;
PROTOBUF_CONSTEXPR RtxMap::RtxMap(
    ::_pbi::ConstantInitialized)
  : config_(nullptr)
  , payload_type_(0){}
struct RtxMapDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RtxMapDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RtxMapDefaultTypeInternal() {}
  union {
    RtxMap _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RtxMapDefaultTypeInternal _RtxMap_default_instance_;
PROTOBUF_CONSTEXPR VideoSendConfig::VideoSendConfig(
    ::_pbi::ConstantInitialized)
  : ssrcs_()
  , header_extensions_()
  , rtx_ssrcs_()
  , encoder_(nullptr)
  , rtx_payload_type_(0){}
struct VideoSendConfigDefaultTypeInternal {
  PROTOBUF_CONSTEXPR VideoSendConfigDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~VideoSendConfigDefaultTypeInternal() {}
  union {
    VideoSendConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 VideoSendConfigDefaultTypeInternal _VideoSendConfig_default_instance_;
PROTOBUF_CONSTEXPR EncoderConfig::EncoderConfig(
    ::_pbi::ConstantInitialized)
  : name_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , payload_type_(0){}
struct EncoderConfigDefaultTypeInternal {
  PROTOBUF_CONSTEXPR EncoderConfigDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~EncoderConfigDefaultTypeInternal() {}
  union {
    EncoderConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 EncoderConfigDefaultTypeInternal _EncoderConfig_default_instance_;
PROTOBUF_CONSTEXPR AudioReceiveConfig::AudioReceiveConfig(
    ::_pbi::ConstantInitialized)
  : header_extensions_()
  , remote_ssrc_(0u)
  , local_ssrc_(0u){}
struct AudioReceiveConfigDefaultTypeInternal {
  PROTOBUF_CONSTEXPR AudioReceiveConfigDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~AudioReceiveConfigDefaultTypeInternal() {}
  union {
    AudioReceiveConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 AudioReceiveConfigDefaultTypeInternal _AudioReceiveConfig_default_instance_;
PROTOBUF_CONSTEXPR AudioSendConfig::AudioSendConfig(
    ::_pbi::ConstantInitialized)
  : header_extensions_()
  , ssrc_(0u){}
struct AudioSendConfigDefaultTypeInternal {
  PROTOBUF_CONSTEXPR AudioSendConfigDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~AudioSendConfigDefaultTypeInternal() {}
  union {
    AudioSendConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 AudioSendConfigDefaultTypeInternal _AudioSendConfig_default_instance_;
PROTOBUF_CONSTEXPR AudioNetworkAdaptation::AudioNetworkAdaptation(
    ::_pbi::ConstantInitialized)
  : bitrate_bps_(0)
  , frame_length_ms_(0)
  , uplink_packet_loss_fraction_(0)
  , enable_fec_(false)
  , enable_dtx_(false)
  , num_channels_(0u){}
struct AudioNetworkAdaptationDefaultTypeInternal {
  PROTOBUF_CONSTEXPR AudioNetworkAdaptationDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~AudioNetworkAdaptationDefaultTypeInternal() {}
  union {
    AudioNetworkAdaptation _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 AudioNetworkAdaptationDefaultTypeInternal _AudioNetworkAdaptation_default_instance_;
PROTOBUF_CONSTEXPR BweProbeCluster::BweProbeCluster(
    ::_pbi::ConstantInitialized)
  : id_(0)
  , bitrate_bps_(0)
  , min_packets_(0u)
  , min_bytes_(0u){}
struct BweProbeClusterDefaultTypeInternal {
  PROTOBUF_CONSTEXPR BweProbeClusterDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~BweProbeClusterDefaultTypeInternal() {}
  union {
    BweProbeCluster _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 BweProbeClusterDefaultTypeInternal _BweProbeCluster_default_instance_;
PROTOBUF_CONSTEXPR BweProbeResult::BweProbeResult(
    ::_pbi::ConstantInitialized)
  : id_(0)
  , result_(0)

  , bitrate_bps_(0){}
struct BweProbeResultDefaultTypeInternal {
  PROTOBUF_CONSTEXPR BweProbeResultDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~BweProbeResultDefaultTypeInternal() {}
  union {
    BweProbeResult _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 BweProbeResultDefaultTypeInternal _BweProbeResult_default_instance_;
PROTOBUF_CONSTEXPR RemoteEstimate::RemoteEstimate(
    ::_pbi::ConstantInitialized)
  : link_capacity_lower_kbps_(0u)
  , link_capacity_upper_kbps_(0u){}
struct RemoteEstimateDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RemoteEstimateDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RemoteEstimateDefaultTypeInternal() {}
  union {
    RemoteEstimate _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RemoteEstimateDefaultTypeInternal _RemoteEstimate_default_instance_;
PROTOBUF_CONSTEXPR AlrState::AlrState(
    ::_pbi::ConstantInitialized)
  : in_alr_(false){}
struct AlrStateDefaultTypeInternal {
  PROTOBUF_CONSTEXPR AlrStateDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~AlrStateDefaultTypeInternal() {}
  union {
    AlrState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 AlrStateDefaultTypeInternal _AlrState_default_instance_;
PROTOBUF_CONSTEXPR IceCandidatePairConfig::IceCandidatePairConfig(
    ::_pbi::ConstantInitialized)
  : config_type_(0)

  , candidate_pair_id_(0u)
  , local_candidate_type_(0)

  , local_relay_protocol_(0)

  , local_network_type_(0)

  , local_address_family_(0)

  , remote_candidate_type_(0)

  , remote_address_family_(0)

  , candidate_pair_protocol_(0)
{}
struct IceCandidatePairConfigDefaultTypeInternal {
  PROTOBUF_CONSTEXPR IceCandidatePairConfigDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~IceCandidatePairConfigDefaultTypeInternal() {}
  union {
    IceCandidatePairConfig _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 IceCandidatePairConfigDefaultTypeInternal _IceCandidatePairConfig_default_instance_;
PROTOBUF_CONSTEXPR IceCandidatePairEvent::IceCandidatePairEvent(
    ::_pbi::ConstantInitialized)
  : event_type_(0)

  , candidate_pair_id_(0u){}
struct IceCandidatePairEventDefaultTypeInternal {
  PROTOBUF_CONSTEXPR IceCandidatePairEventDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~IceCandidatePairEventDefaultTypeInternal() {}
  union {
    IceCandidatePairEvent _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 IceCandidatePairEventDefaultTypeInternal _IceCandidatePairEvent_default_instance_;
}  // namespace rtclog
}  // namespace webrtc
namespace webrtc {
namespace rtclog {
bool Event_EventType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 16:
    case 17:
    case 18:
    case 19:
    case 20:
    case 21:
    case 22:
      return true;
    default:
      return false;
  }
}

static ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<std::string> Event_EventType_strings[19] = {};

static const char Event_EventType_names[] =
  "ALR_STATE_EVENT"
  "AUDIO_NETWORK_ADAPTATION_EVENT"
  "AUDIO_PLAYOUT_EVENT"
  "AUDIO_RECEIVER_CONFIG_EVENT"
  "AUDIO_SENDER_CONFIG_EVENT"
  "BWE_PROBE_CLUSTER_CREATED_EVENT"
  "BWE_PROBE_RESULT_EVENT"
  "DELAY_BASED_BWE_UPDATE"
  "ICE_CANDIDATE_PAIR_CONFIG"
  "ICE_CANDIDATE_PAIR_EVENT"
  "LOG_END"
  "LOG_START"
  "LOSS_BASED_BWE_UPDATE"
  "REMOTE_ESTIMATE"
  "RTCP_EVENT"
  "RTP_EVENT"
  "UNKNOWN_EVENT"
  "VIDEO_RECEIVER_CONFIG_EVENT"
  "VIDEO_SENDER_CONFIG_EVENT";

static const ::PROTOBUF_NAMESPACE_ID::internal::EnumEntry Event_EventType_entries[] = {
  { {Event_EventType_names + 0, 15}, 19 },
  { {Event_EventType_names + 15, 30}, 16 },
  { {Event_EventType_names + 45, 19}, 5 },
  { {Event_EventType_names + 64, 27}, 10 },
  { {Event_EventType_names + 91, 25}, 11 },
  { {Event_EventType_names + 116, 31}, 17 },
  { {Event_EventType_names + 147, 22}, 18 },
  { {Event_EventType_names + 169, 22}, 7 },
  { {Event_EventType_names + 191, 25}, 20 },
  { {Event_EventType_names + 216, 24}, 21 },
  { {Event_EventType_names + 240, 7}, 2 },
  { {Event_EventType_names + 247, 9}, 1 },
  { {Event_EventType_names + 256, 21}, 6 },
  { {Event_EventType_names + 277, 15}, 22 },
  { {Event_EventType_names + 292, 10}, 4 },
  { {Event_EventType_names + 302, 9}, 3 },
  { {Event_EventType_names + 311, 13}, 0 },
  { {Event_EventType_names + 324, 27}, 8 },
  { {Event_EventType_names + 351, 25}, 9 },
};

static const int Event_EventType_entries_by_number[] = {
  16, // 0 -> UNKNOWN_EVENT
  11, // 1 -> LOG_START
  10, // 2 -> LOG_END
  15, // 3 -> RTP_EVENT
  14, // 4 -> RTCP_EVENT
  2, // 5 -> AUDIO_PLAYOUT_EVENT
  12, // 6 -> LOSS_BASED_BWE_UPDATE
  7, // 7 -> DELAY_BASED_BWE_UPDATE
  17, // 8 -> VIDEO_RECEIVER_CONFIG_EVENT
  18, // 9 -> VIDEO_SENDER_CONFIG_EVENT
  3, // 10 -> AUDIO_RECEIVER_CONFIG_EVENT
  4, // 11 -> AUDIO_SENDER_CONFIG_EVENT
  1, // 16 -> AUDIO_NETWORK_ADAPTATION_EVENT
  5, // 17 -> BWE_PROBE_CLUSTER_CREATED_EVENT
  6, // 18 -> BWE_PROBE_RESULT_EVENT
  0, // 19 -> ALR_STATE_EVENT
  8, // 20 -> ICE_CANDIDATE_PAIR_CONFIG
  9, // 21 -> ICE_CANDIDATE_PAIR_EVENT
  13, // 22 -> REMOTE_ESTIMATE
};

const std::string& Event_EventType_Name(
    Event_EventType value) {
  static const bool dummy =
      ::PROTOBUF_NAMESPACE_ID::internal::InitializeEnumStrings(
          Event_EventType_entries,
          Event_EventType_entries_by_number,
          19, Event_EventType_strings);
  (void) dummy;
  int idx = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumName(
      Event_EventType_entries,
      Event_EventType_entries_by_number,
      19, value);
  return idx == -1 ? ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString() :
                     Event_EventType_strings[idx].get();
}
bool Event_EventType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Event_EventType* value) {
  int int_value;
  bool success = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumValue(
      Event_EventType_entries, 19, name, &int_value);
  if (success) {
    *value = static_cast<Event_EventType>(int_value);
  }
  return success;
}
#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr Event_EventType Event::UNKNOWN_EVENT;
constexpr Event_EventType Event::LOG_START;
constexpr Event_EventType Event::LOG_END;
constexpr Event_EventType Event::RTP_EVENT;
constexpr Event_EventType Event::RTCP_EVENT;
constexpr Event_EventType Event::AUDIO_PLAYOUT_EVENT;
constexpr Event_EventType Event::LOSS_BASED_BWE_UPDATE;
constexpr Event_EventType Event::DELAY_BASED_BWE_UPDATE;
constexpr Event_EventType Event::VIDEO_RECEIVER_CONFIG_EVENT;
constexpr Event_EventType Event::VIDEO_SENDER_CONFIG_EVENT;
constexpr Event_EventType Event::AUDIO_RECEIVER_CONFIG_EVENT;
constexpr Event_EventType Event::AUDIO_SENDER_CONFIG_EVENT;
constexpr Event_EventType Event::AUDIO_NETWORK_ADAPTATION_EVENT;
constexpr Event_EventType Event::BWE_PROBE_CLUSTER_CREATED_EVENT;
constexpr Event_EventType Event::BWE_PROBE_RESULT_EVENT;
constexpr Event_EventType Event::ALR_STATE_EVENT;
constexpr Event_EventType Event::ICE_CANDIDATE_PAIR_CONFIG;
constexpr Event_EventType Event::ICE_CANDIDATE_PAIR_EVENT;
constexpr Event_EventType Event::REMOTE_ESTIMATE;
constexpr Event_EventType Event::EventType_MIN;
constexpr Event_EventType Event::EventType_MAX;
constexpr int Event::EventType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
bool DelayBasedBweUpdate_DetectorState_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

static ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<std::string> DelayBasedBweUpdate_DetectorState_strings[3] = {};

static const char DelayBasedBweUpdate_DetectorState_names[] =
  "BWE_NORMAL"
  "BWE_OVERUSING"
  "BWE_UNDERUSING";

static const ::PROTOBUF_NAMESPACE_ID::internal::EnumEntry DelayBasedBweUpdate_DetectorState_entries[] = {
  { {DelayBasedBweUpdate_DetectorState_names + 0, 10}, 0 },
  { {DelayBasedBweUpdate_DetectorState_names + 10, 13}, 2 },
  { {DelayBasedBweUpdate_DetectorState_names + 23, 14}, 1 },
};

static const int DelayBasedBweUpdate_DetectorState_entries_by_number[] = {
  0, // 0 -> BWE_NORMAL
  2, // 1 -> BWE_UNDERUSING
  1, // 2 -> BWE_OVERUSING
};

const std::string& DelayBasedBweUpdate_DetectorState_Name(
    DelayBasedBweUpdate_DetectorState value) {
  static const bool dummy =
      ::PROTOBUF_NAMESPACE_ID::internal::InitializeEnumStrings(
          DelayBasedBweUpdate_DetectorState_entries,
          DelayBasedBweUpdate_DetectorState_entries_by_number,
          3, DelayBasedBweUpdate_DetectorState_strings);
  (void) dummy;
  int idx = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumName(
      DelayBasedBweUpdate_DetectorState_entries,
      DelayBasedBweUpdate_DetectorState_entries_by_number,
      3, value);
  return idx == -1 ? ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString() :
                     DelayBasedBweUpdate_DetectorState_strings[idx].get();
}
bool DelayBasedBweUpdate_DetectorState_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DelayBasedBweUpdate_DetectorState* value) {
  int int_value;
  bool success = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumValue(
      DelayBasedBweUpdate_DetectorState_entries, 3, name, &int_value);
  if (success) {
    *value = static_cast<DelayBasedBweUpdate_DetectorState>(int_value);
  }
  return success;
}
#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr DelayBasedBweUpdate_DetectorState DelayBasedBweUpdate::BWE_NORMAL;
constexpr DelayBasedBweUpdate_DetectorState DelayBasedBweUpdate::BWE_UNDERUSING;
constexpr DelayBasedBweUpdate_DetectorState DelayBasedBweUpdate::BWE_OVERUSING;
constexpr DelayBasedBweUpdate_DetectorState DelayBasedBweUpdate::DetectorState_MIN;
constexpr DelayBasedBweUpdate_DetectorState DelayBasedBweUpdate::DetectorState_MAX;
constexpr int DelayBasedBweUpdate::DetectorState_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
bool VideoReceiveConfig_RtcpMode_IsValid(int value) {
  switch (value) {
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

static ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<std::string> VideoReceiveConfig_RtcpMode_strings[2] = {};

static const char VideoReceiveConfig_RtcpMode_names[] =
  "RTCP_COMPOUND"
  "RTCP_REDUCEDSIZE";

static const ::PROTOBUF_NAMESPACE_ID::internal::EnumEntry VideoReceiveConfig_RtcpMode_entries[] = {
  { {VideoReceiveConfig_RtcpMode_names + 0, 13}, 1 },
  { {VideoReceiveConfig_RtcpMode_names + 13, 16}, 2 },
};

static const int VideoReceiveConfig_RtcpMode_entries_by_number[] = {
  0, // 1 -> RTCP_COMPOUND
  1, // 2 -> RTCP_REDUCEDSIZE
};

const std::string& VideoReceiveConfig_RtcpMode_Name(
    VideoReceiveConfig_RtcpMode value) {
  static const bool dummy =
      ::PROTOBUF_NAMESPACE_ID::internal::InitializeEnumStrings(
          VideoReceiveConfig_RtcpMode_entries,
          VideoReceiveConfig_RtcpMode_entries_by_number,
          2, VideoReceiveConfig_RtcpMode_strings);
  (void) dummy;
  int idx = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumName(
      VideoReceiveConfig_RtcpMode_entries,
      VideoReceiveConfig_RtcpMode_entries_by_number,
      2, value);
  return idx == -1 ? ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString() :
                     VideoReceiveConfig_RtcpMode_strings[idx].get();
}
bool VideoReceiveConfig_RtcpMode_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, VideoReceiveConfig_RtcpMode* value) {
  int int_value;
  bool success = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumValue(
      VideoReceiveConfig_RtcpMode_entries, 2, name, &int_value);
  if (success) {
    *value = static_cast<VideoReceiveConfig_RtcpMode>(int_value);
  }
  return success;
}
#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr VideoReceiveConfig_RtcpMode VideoReceiveConfig::RTCP_COMPOUND;
constexpr VideoReceiveConfig_RtcpMode VideoReceiveConfig::RTCP_REDUCEDSIZE;
constexpr VideoReceiveConfig_RtcpMode VideoReceiveConfig::RtcpMode_MIN;
constexpr VideoReceiveConfig_RtcpMode VideoReceiveConfig::RtcpMode_MAX;
constexpr int VideoReceiveConfig::RtcpMode_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
bool BweProbeResult_ResultType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

static ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<std::string> BweProbeResult_ResultType_strings[4] = {};

static const char BweProbeResult_ResultType_names[] =
  "INVALID_SEND_RECEIVE_INTERVAL"
  "INVALID_SEND_RECEIVE_RATIO"
  "SUCCESS"
  "TIMEOUT";

static const ::PROTOBUF_NAMESPACE_ID::internal::EnumEntry BweProbeResult_ResultType_entries[] = {
  { {BweProbeResult_ResultType_names + 0, 29}, 1 },
  { {BweProbeResult_ResultType_names + 29, 26}, 2 },
  { {BweProbeResult_ResultType_names + 55, 7}, 0 },
  { {BweProbeResult_ResultType_names + 62, 7}, 3 },
};

static const int BweProbeResult_ResultType_entries_by_number[] = {
  2, // 0 -> SUCCESS
  0, // 1 -> INVALID_SEND_RECEIVE_INTERVAL
  1, // 2 -> INVALID_SEND_RECEIVE_RATIO
  3, // 3 -> TIMEOUT
};

const std::string& BweProbeResult_ResultType_Name(
    BweProbeResult_ResultType value) {
  static const bool dummy =
      ::PROTOBUF_NAMESPACE_ID::internal::InitializeEnumStrings(
          BweProbeResult_ResultType_entries,
          BweProbeResult_ResultType_entries_by_number,
          4, BweProbeResult_ResultType_strings);
  (void) dummy;
  int idx = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumName(
      BweProbeResult_ResultType_entries,
      BweProbeResult_ResultType_entries_by_number,
      4, value);
  return idx == -1 ? ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString() :
                     BweProbeResult_ResultType_strings[idx].get();
}
bool BweProbeResult_ResultType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, BweProbeResult_ResultType* value) {
  int int_value;
  bool success = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumValue(
      BweProbeResult_ResultType_entries, 4, name, &int_value);
  if (success) {
    *value = static_cast<BweProbeResult_ResultType>(int_value);
  }
  return success;
}
#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr BweProbeResult_ResultType BweProbeResult::SUCCESS;
constexpr BweProbeResult_ResultType BweProbeResult::INVALID_SEND_RECEIVE_INTERVAL;
constexpr BweProbeResult_ResultType BweProbeResult::INVALID_SEND_RECEIVE_RATIO;
constexpr BweProbeResult_ResultType BweProbeResult::TIMEOUT;
constexpr BweProbeResult_ResultType BweProbeResult::ResultType_MIN;
constexpr BweProbeResult_ResultType BweProbeResult::ResultType_MAX;
constexpr int BweProbeResult::ResultType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
bool IceCandidatePairConfig_IceCandidatePairConfigType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

static ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<std::string> IceCandidatePairConfig_IceCandidatePairConfigType_strings[4] = {};

static const char IceCandidatePairConfig_IceCandidatePairConfigType_names[] =
  "ADDED"
  "DESTROYED"
  "SELECTED"
  "UPDATED";

static const ::PROTOBUF_NAMESPACE_ID::internal::EnumEntry IceCandidatePairConfig_IceCandidatePairConfigType_entries[] = {
  { {IceCandidatePairConfig_IceCandidatePairConfigType_names + 0, 5}, 0 },
  { {IceCandidatePairConfig_IceCandidatePairConfigType_names + 5, 9}, 2 },
  { {IceCandidatePairConfig_IceCandidatePairConfigType_names + 14, 8}, 3 },
  { {IceCandidatePairConfig_IceCandidatePairConfigType_names + 22, 7}, 1 },
};

static const int IceCandidatePairConfig_IceCandidatePairConfigType_entries_by_number[] = {
  0, // 0 -> ADDED
  3, // 1 -> UPDATED
  1, // 2 -> DESTROYED
  2, // 3 -> SELECTED
};

const std::string& IceCandidatePairConfig_IceCandidatePairConfigType_Name(
    IceCandidatePairConfig_IceCandidatePairConfigType value) {
  static const bool dummy =
      ::PROTOBUF_NAMESPACE_ID::internal::InitializeEnumStrings(
          IceCandidatePairConfig_IceCandidatePairConfigType_entries,
          IceCandidatePairConfig_IceCandidatePairConfigType_entries_by_number,
          4, IceCandidatePairConfig_IceCandidatePairConfigType_strings);
  (void) dummy;
  int idx = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumName(
      IceCandidatePairConfig_IceCandidatePairConfigType_entries,
      IceCandidatePairConfig_IceCandidatePairConfigType_entries_by_number,
      4, value);
  return idx == -1 ? ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString() :
                     IceCandidatePairConfig_IceCandidatePairConfigType_strings[idx].get();
}
bool IceCandidatePairConfig_IceCandidatePairConfigType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, IceCandidatePairConfig_IceCandidatePairConfigType* value) {
  int int_value;
  bool success = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumValue(
      IceCandidatePairConfig_IceCandidatePairConfigType_entries, 4, name, &int_value);
  if (success) {
    *value = static_cast<IceCandidatePairConfig_IceCandidatePairConfigType>(int_value);
  }
  return success;
}
#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr IceCandidatePairConfig_IceCandidatePairConfigType IceCandidatePairConfig::ADDED;
constexpr IceCandidatePairConfig_IceCandidatePairConfigType IceCandidatePairConfig::UPDATED;
constexpr IceCandidatePairConfig_IceCandidatePairConfigType IceCandidatePairConfig::DESTROYED;
constexpr IceCandidatePairConfig_IceCandidatePairConfigType IceCandidatePairConfig::SELECTED;
constexpr IceCandidatePairConfig_IceCandidatePairConfigType IceCandidatePairConfig::IceCandidatePairConfigType_MIN;
constexpr IceCandidatePairConfig_IceCandidatePairConfigType IceCandidatePairConfig::IceCandidatePairConfigType_MAX;
constexpr int IceCandidatePairConfig::IceCandidatePairConfigType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
bool IceCandidatePairConfig_IceCandidateType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}

static ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<std::string> IceCandidatePairConfig_IceCandidateType_strings[5] = {};

static const char IceCandidatePairConfig_IceCandidateType_names[] =
  "LOCAL"
  "PRFLX"
  "RELAY"
  "STUN"
  "UNKNOWN_CANDIDATE_TYPE";

static const ::PROTOBUF_NAMESPACE_ID::internal::EnumEntry IceCandidatePairConfig_IceCandidateType_entries[] = {
  { {IceCandidatePairConfig_IceCandidateType_names + 0, 5}, 0 },
  { {IceCandidatePairConfig_IceCandidateType_names + 5, 5}, 2 },
  { {IceCandidatePairConfig_IceCandidateType_names + 10, 5}, 3 },
  { {IceCandidatePairConfig_IceCandidateType_names + 15, 4}, 1 },
  { {IceCandidatePairConfig_IceCandidateType_names + 19, 22}, 4 },
};

static const int IceCandidatePairConfig_IceCandidateType_entries_by_number[] = {
  0, // 0 -> LOCAL
  3, // 1 -> STUN
  1, // 2 -> PRFLX
  2, // 3 -> RELAY
  4, // 4 -> UNKNOWN_CANDIDATE_TYPE
};

const std::string& IceCandidatePairConfig_IceCandidateType_Name(
    IceCandidatePairConfig_IceCandidateType value) {
  static const bool dummy =
      ::PROTOBUF_NAMESPACE_ID::internal::InitializeEnumStrings(
          IceCandidatePairConfig_IceCandidateType_entries,
          IceCandidatePairConfig_IceCandidateType_entries_by_number,
          5, IceCandidatePairConfig_IceCandidateType_strings);
  (void) dummy;
  int idx = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumName(
      IceCandidatePairConfig_IceCandidateType_entries,
      IceCandidatePairConfig_IceCandidateType_entries_by_number,
      5, value);
  return idx == -1 ? ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString() :
                     IceCandidatePairConfig_IceCandidateType_strings[idx].get();
}
bool IceCandidatePairConfig_IceCandidateType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, IceCandidatePairConfig_IceCandidateType* value) {
  int int_value;
  bool success = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumValue(
      IceCandidatePairConfig_IceCandidateType_entries, 5, name, &int_value);
  if (success) {
    *value = static_cast<IceCandidatePairConfig_IceCandidateType>(int_value);
  }
  return success;
}
#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr IceCandidatePairConfig_IceCandidateType IceCandidatePairConfig::LOCAL;
constexpr IceCandidatePairConfig_IceCandidateType IceCandidatePairConfig::STUN;
constexpr IceCandidatePairConfig_IceCandidateType IceCandidatePairConfig::PRFLX;
constexpr IceCandidatePairConfig_IceCandidateType IceCandidatePairConfig::RELAY;
constexpr IceCandidatePairConfig_IceCandidateType IceCandidatePairConfig::UNKNOWN_CANDIDATE_TYPE;
constexpr IceCandidatePairConfig_IceCandidateType IceCandidatePairConfig::IceCandidateType_MIN;
constexpr IceCandidatePairConfig_IceCandidateType IceCandidatePairConfig::IceCandidateType_MAX;
constexpr int IceCandidatePairConfig::IceCandidateType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
bool IceCandidatePairConfig_Protocol_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}

static ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<std::string> IceCandidatePairConfig_Protocol_strings[5] = {};

static const char IceCandidatePairConfig_Protocol_names[] =
  "SSLTCP"
  "TCP"
  "TLS"
  "UDP"
  "UNKNOWN_PROTOCOL";

static const ::PROTOBUF_NAMESPACE_ID::internal::EnumEntry IceCandidatePairConfig_Protocol_entries[] = {
  { {IceCandidatePairConfig_Protocol_names + 0, 6}, 2 },
  { {IceCandidatePairConfig_Protocol_names + 6, 3}, 1 },
  { {IceCandidatePairConfig_Protocol_names + 9, 3}, 3 },
  { {IceCandidatePairConfig_Protocol_names + 12, 3}, 0 },
  { {IceCandidatePairConfig_Protocol_names + 15, 16}, 4 },
};

static const int IceCandidatePairConfig_Protocol_entries_by_number[] = {
  3, // 0 -> UDP
  1, // 1 -> TCP
  0, // 2 -> SSLTCP
  2, // 3 -> TLS
  4, // 4 -> UNKNOWN_PROTOCOL
};

const std::string& IceCandidatePairConfig_Protocol_Name(
    IceCandidatePairConfig_Protocol value) {
  static const bool dummy =
      ::PROTOBUF_NAMESPACE_ID::internal::InitializeEnumStrings(
          IceCandidatePairConfig_Protocol_entries,
          IceCandidatePairConfig_Protocol_entries_by_number,
          5, IceCandidatePairConfig_Protocol_strings);
  (void) dummy;
  int idx = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumName(
      IceCandidatePairConfig_Protocol_entries,
      IceCandidatePairConfig_Protocol_entries_by_number,
      5, value);
  return idx == -1 ? ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString() :
                     IceCandidatePairConfig_Protocol_strings[idx].get();
}
bool IceCandidatePairConfig_Protocol_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, IceCandidatePairConfig_Protocol* value) {
  int int_value;
  bool success = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumValue(
      IceCandidatePairConfig_Protocol_entries, 5, name, &int_value);
  if (success) {
    *value = static_cast<IceCandidatePairConfig_Protocol>(int_value);
  }
  return success;
}
#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr IceCandidatePairConfig_Protocol IceCandidatePairConfig::UDP;
constexpr IceCandidatePairConfig_Protocol IceCandidatePairConfig::TCP;
constexpr IceCandidatePairConfig_Protocol IceCandidatePairConfig::SSLTCP;
constexpr IceCandidatePairConfig_Protocol IceCandidatePairConfig::TLS;
constexpr IceCandidatePairConfig_Protocol IceCandidatePairConfig::UNKNOWN_PROTOCOL;
constexpr IceCandidatePairConfig_Protocol IceCandidatePairConfig::Protocol_MIN;
constexpr IceCandidatePairConfig_Protocol IceCandidatePairConfig::Protocol_MAX;
constexpr int IceCandidatePairConfig::Protocol_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
bool IceCandidatePairConfig_AddressFamily_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

static ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<std::string> IceCandidatePairConfig_AddressFamily_strings[3] = {};

static const char IceCandidatePairConfig_AddressFamily_names[] =
  "IPV4"
  "IPV6"
  "UNKNOWN_ADDRESS_FAMILY";

static const ::PROTOBUF_NAMESPACE_ID::internal::EnumEntry IceCandidatePairConfig_AddressFamily_entries[] = {
  { {IceCandidatePairConfig_AddressFamily_names + 0, 4}, 0 },
  { {IceCandidatePairConfig_AddressFamily_names + 4, 4}, 1 },
  { {IceCandidatePairConfig_AddressFamily_names + 8, 22}, 2 },
};

static const int IceCandidatePairConfig_AddressFamily_entries_by_number[] = {
  0, // 0 -> IPV4
  1, // 1 -> IPV6
  2, // 2 -> UNKNOWN_ADDRESS_FAMILY
};

const std::string& IceCandidatePairConfig_AddressFamily_Name(
    IceCandidatePairConfig_AddressFamily value) {
  static const bool dummy =
      ::PROTOBUF_NAMESPACE_ID::internal::InitializeEnumStrings(
          IceCandidatePairConfig_AddressFamily_entries,
          IceCandidatePairConfig_AddressFamily_entries_by_number,
          3, IceCandidatePairConfig_AddressFamily_strings);
  (void) dummy;
  int idx = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumName(
      IceCandidatePairConfig_AddressFamily_entries,
      IceCandidatePairConfig_AddressFamily_entries_by_number,
      3, value);
  return idx == -1 ? ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString() :
                     IceCandidatePairConfig_AddressFamily_strings[idx].get();
}
bool IceCandidatePairConfig_AddressFamily_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, IceCandidatePairConfig_AddressFamily* value) {
  int int_value;
  bool success = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumValue(
      IceCandidatePairConfig_AddressFamily_entries, 3, name, &int_value);
  if (success) {
    *value = static_cast<IceCandidatePairConfig_AddressFamily>(int_value);
  }
  return success;
}
#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr IceCandidatePairConfig_AddressFamily IceCandidatePairConfig::IPV4;
constexpr IceCandidatePairConfig_AddressFamily IceCandidatePairConfig::IPV6;
constexpr IceCandidatePairConfig_AddressFamily IceCandidatePairConfig::UNKNOWN_ADDRESS_FAMILY;
constexpr IceCandidatePairConfig_AddressFamily IceCandidatePairConfig::AddressFamily_MIN;
constexpr IceCandidatePairConfig_AddressFamily IceCandidatePairConfig::AddressFamily_MAX;
constexpr int IceCandidatePairConfig::AddressFamily_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
bool IceCandidatePairConfig_NetworkType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}

static ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<std::string> IceCandidatePairConfig_NetworkType_strings[6] = {};

static const char IceCandidatePairConfig_NetworkType_names[] =
  "CELLULAR"
  "ETHERNET"
  "LOOPBACK"
  "UNKNOWN_NETWORK_TYPE"
  "VPN"
  "WIFI";

static const ::PROTOBUF_NAMESPACE_ID::internal::EnumEntry IceCandidatePairConfig_NetworkType_entries[] = {
  { {IceCandidatePairConfig_NetworkType_names + 0, 8}, 4 },
  { {IceCandidatePairConfig_NetworkType_names + 8, 8}, 0 },
  { {IceCandidatePairConfig_NetworkType_names + 16, 8}, 1 },
  { {IceCandidatePairConfig_NetworkType_names + 24, 20}, 5 },
  { {IceCandidatePairConfig_NetworkType_names + 44, 3}, 3 },
  { {IceCandidatePairConfig_NetworkType_names + 47, 4}, 2 },
};

static const int IceCandidatePairConfig_NetworkType_entries_by_number[] = {
  1, // 0 -> ETHERNET
  2, // 1 -> LOOPBACK
  5, // 2 -> WIFI
  4, // 3 -> VPN
  0, // 4 -> CELLULAR
  3, // 5 -> UNKNOWN_NETWORK_TYPE
};

const std::string& IceCandidatePairConfig_NetworkType_Name(
    IceCandidatePairConfig_NetworkType value) {
  static const bool dummy =
      ::PROTOBUF_NAMESPACE_ID::internal::InitializeEnumStrings(
          IceCandidatePairConfig_NetworkType_entries,
          IceCandidatePairConfig_NetworkType_entries_by_number,
          6, IceCandidatePairConfig_NetworkType_strings);
  (void) dummy;
  int idx = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumName(
      IceCandidatePairConfig_NetworkType_entries,
      IceCandidatePairConfig_NetworkType_entries_by_number,
      6, value);
  return idx == -1 ? ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString() :
                     IceCandidatePairConfig_NetworkType_strings[idx].get();
}
bool IceCandidatePairConfig_NetworkType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, IceCandidatePairConfig_NetworkType* value) {
  int int_value;
  bool success = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumValue(
      IceCandidatePairConfig_NetworkType_entries, 6, name, &int_value);
  if (success) {
    *value = static_cast<IceCandidatePairConfig_NetworkType>(int_value);
  }
  return success;
}
#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr IceCandidatePairConfig_NetworkType IceCandidatePairConfig::ETHERNET;
constexpr IceCandidatePairConfig_NetworkType IceCandidatePairConfig::LOOPBACK;
constexpr IceCandidatePairConfig_NetworkType IceCandidatePairConfig::WIFI;
constexpr IceCandidatePairConfig_NetworkType IceCandidatePairConfig::VPN;
constexpr IceCandidatePairConfig_NetworkType IceCandidatePairConfig::CELLULAR;
constexpr IceCandidatePairConfig_NetworkType IceCandidatePairConfig::UNKNOWN_NETWORK_TYPE;
constexpr IceCandidatePairConfig_NetworkType IceCandidatePairConfig::NetworkType_MIN;
constexpr IceCandidatePairConfig_NetworkType IceCandidatePairConfig::NetworkType_MAX;
constexpr int IceCandidatePairConfig::NetworkType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
bool IceCandidatePairEvent_IceCandidatePairEventType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

static ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<std::string> IceCandidatePairEvent_IceCandidatePairEventType_strings[4] = {};

static const char IceCandidatePairEvent_IceCandidatePairEventType_names[] =
  "CHECK_RECEIVED"
  "CHECK_RESPONSE_RECEIVED"
  "CHECK_RESPONSE_SENT"
  "CHECK_SENT";

static const ::PROTOBUF_NAMESPACE_ID::internal::EnumEntry IceCandidatePairEvent_IceCandidatePairEventType_entries[] = {
  { {IceCandidatePairEvent_IceCandidatePairEventType_names + 0, 14}, 1 },
  { {IceCandidatePairEvent_IceCandidatePairEventType_names + 14, 23}, 3 },
  { {IceCandidatePairEvent_IceCandidatePairEventType_names + 37, 19}, 2 },
  { {IceCandidatePairEvent_IceCandidatePairEventType_names + 56, 10}, 0 },
};

static const int IceCandidatePairEvent_IceCandidatePairEventType_entries_by_number[] = {
  3, // 0 -> CHECK_SENT
  0, // 1 -> CHECK_RECEIVED
  2, // 2 -> CHECK_RESPONSE_SENT
  1, // 3 -> CHECK_RESPONSE_RECEIVED
};

const std::string& IceCandidatePairEvent_IceCandidatePairEventType_Name(
    IceCandidatePairEvent_IceCandidatePairEventType value) {
  static const bool dummy =
      ::PROTOBUF_NAMESPACE_ID::internal::InitializeEnumStrings(
          IceCandidatePairEvent_IceCandidatePairEventType_entries,
          IceCandidatePairEvent_IceCandidatePairEventType_entries_by_number,
          4, IceCandidatePairEvent_IceCandidatePairEventType_strings);
  (void) dummy;
  int idx = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumName(
      IceCandidatePairEvent_IceCandidatePairEventType_entries,
      IceCandidatePairEvent_IceCandidatePairEventType_entries_by_number,
      4, value);
  return idx == -1 ? ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString() :
                     IceCandidatePairEvent_IceCandidatePairEventType_strings[idx].get();
}
bool IceCandidatePairEvent_IceCandidatePairEventType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, IceCandidatePairEvent_IceCandidatePairEventType* value) {
  int int_value;
  bool success = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumValue(
      IceCandidatePairEvent_IceCandidatePairEventType_entries, 4, name, &int_value);
  if (success) {
    *value = static_cast<IceCandidatePairEvent_IceCandidatePairEventType>(int_value);
  }
  return success;
}
#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr IceCandidatePairEvent_IceCandidatePairEventType IceCandidatePairEvent::CHECK_SENT;
constexpr IceCandidatePairEvent_IceCandidatePairEventType IceCandidatePairEvent::CHECK_RECEIVED;
constexpr IceCandidatePairEvent_IceCandidatePairEventType IceCandidatePairEvent::CHECK_RESPONSE_SENT;
constexpr IceCandidatePairEvent_IceCandidatePairEventType IceCandidatePairEvent::CHECK_RESPONSE_RECEIVED;
constexpr IceCandidatePairEvent_IceCandidatePairEventType IceCandidatePairEvent::IceCandidatePairEventType_MIN;
constexpr IceCandidatePairEvent_IceCandidatePairEventType IceCandidatePairEvent::IceCandidatePairEventType_MAX;
constexpr int IceCandidatePairEvent::IceCandidatePairEventType_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
bool MediaType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

static ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<std::string> MediaType_strings[4] = {};

static const char MediaType_names[] =
  "ANY"
  "AUDIO"
  "DATA"
  "VIDEO";

static const ::PROTOBUF_NAMESPACE_ID::internal::EnumEntry MediaType_entries[] = {
  { {MediaType_names + 0, 3}, 0 },
  { {MediaType_names + 3, 5}, 1 },
  { {MediaType_names + 8, 4}, 3 },
  { {MediaType_names + 12, 5}, 2 },
};

static const int MediaType_entries_by_number[] = {
  0, // 0 -> ANY
  1, // 1 -> AUDIO
  3, // 2 -> VIDEO
  2, // 3 -> DATA
};

const std::string& MediaType_Name(
    MediaType value) {
  static const bool dummy =
      ::PROTOBUF_NAMESPACE_ID::internal::InitializeEnumStrings(
          MediaType_entries,
          MediaType_entries_by_number,
          4, MediaType_strings);
  (void) dummy;
  int idx = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumName(
      MediaType_entries,
      MediaType_entries_by_number,
      4, value);
  return idx == -1 ? ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString() :
                     MediaType_strings[idx].get();
}
bool MediaType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, MediaType* value) {
  int int_value;
  bool success = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumValue(
      MediaType_entries, 4, name, &int_value);
  if (success) {
    *value = static_cast<MediaType>(int_value);
  }
  return success;
}

// ===================================================================

class EventStream::_Internal {
 public:
};

EventStream::EventStream(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned),
  stream_(arena) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.EventStream)
}
EventStream::EventStream(const EventStream& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      stream_(from.stream_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.EventStream)
}

inline void EventStream::SharedCtor() {
}

EventStream::~EventStream() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.EventStream)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void EventStream::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EventStream::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EventStream::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.EventStream)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  stream_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* EventStream::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .webrtc.rtclog.Event stream = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_stream(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* EventStream::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.EventStream)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .webrtc.rtclog.Event stream = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_stream_size()); i < n; i++) {
    const auto& repfield = this->_internal_stream(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.EventStream)
  return target;
}

size_t EventStream::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.EventStream)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .webrtc.rtclog.Event stream = 1;
  total_size += 1UL * this->_internal_stream_size();
  for (const auto& msg : this->stream_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void EventStream::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const EventStream*>(
      &from));
}

void EventStream::MergeFrom(const EventStream& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.EventStream)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  stream_.MergeFrom(from.stream_);
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void EventStream::CopyFrom(const EventStream& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.EventStream)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EventStream::IsInitialized() const {
  return true;
}

void EventStream::InternalSwap(EventStream* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  stream_.InternalSwap(&other->stream_);
}

std::string EventStream::GetTypeName() const {
  return "webrtc.rtclog.EventStream";
}


// ===================================================================

class Event::_Internal {
 public:
  using HasBits = decltype(std::declval<Event>()._has_bits_);
  static void set_has_timestamp_us(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::webrtc::rtclog::RtpPacket& rtp_packet(const Event* msg);
  static const ::webrtc::rtclog::RtcpPacket& rtcp_packet(const Event* msg);
  static const ::webrtc::rtclog::AudioPlayoutEvent& audio_playout_event(const Event* msg);
  static const ::webrtc::rtclog::LossBasedBweUpdate& loss_based_bwe_update(const Event* msg);
  static const ::webrtc::rtclog::DelayBasedBweUpdate& delay_based_bwe_update(const Event* msg);
  static const ::webrtc::rtclog::VideoReceiveConfig& video_receiver_config(const Event* msg);
  static const ::webrtc::rtclog::VideoSendConfig& video_sender_config(const Event* msg);
  static const ::webrtc::rtclog::AudioReceiveConfig& audio_receiver_config(const Event* msg);
  static const ::webrtc::rtclog::AudioSendConfig& audio_sender_config(const Event* msg);
  static const ::webrtc::rtclog::AudioNetworkAdaptation& audio_network_adaptation(const Event* msg);
  static const ::webrtc::rtclog::BweProbeCluster& probe_cluster(const Event* msg);
  static const ::webrtc::rtclog::BweProbeResult& probe_result(const Event* msg);
  static const ::webrtc::rtclog::AlrState& alr_state(const Event* msg);
  static const ::webrtc::rtclog::IceCandidatePairConfig& ice_candidate_pair_config(const Event* msg);
  static const ::webrtc::rtclog::IceCandidatePairEvent& ice_candidate_pair_event(const Event* msg);
  static const ::webrtc::rtclog::RemoteEstimate& remote_estimate(const Event* msg);
};

const ::webrtc::rtclog::RtpPacket&
Event::_Internal::rtp_packet(const Event* msg) {
  return *msg->subtype_.rtp_packet_;
}
const ::webrtc::rtclog::RtcpPacket&
Event::_Internal::rtcp_packet(const Event* msg) {
  return *msg->subtype_.rtcp_packet_;
}
const ::webrtc::rtclog::AudioPlayoutEvent&
Event::_Internal::audio_playout_event(const Event* msg) {
  return *msg->subtype_.audio_playout_event_;
}
const ::webrtc::rtclog::LossBasedBweUpdate&
Event::_Internal::loss_based_bwe_update(const Event* msg) {
  return *msg->subtype_.loss_based_bwe_update_;
}
const ::webrtc::rtclog::DelayBasedBweUpdate&
Event::_Internal::delay_based_bwe_update(const Event* msg) {
  return *msg->subtype_.delay_based_bwe_update_;
}
const ::webrtc::rtclog::VideoReceiveConfig&
Event::_Internal::video_receiver_config(const Event* msg) {
  return *msg->subtype_.video_receiver_config_;
}
const ::webrtc::rtclog::VideoSendConfig&
Event::_Internal::video_sender_config(const Event* msg) {
  return *msg->subtype_.video_sender_config_;
}
const ::webrtc::rtclog::AudioReceiveConfig&
Event::_Internal::audio_receiver_config(const Event* msg) {
  return *msg->subtype_.audio_receiver_config_;
}
const ::webrtc::rtclog::AudioSendConfig&
Event::_Internal::audio_sender_config(const Event* msg) {
  return *msg->subtype_.audio_sender_config_;
}
const ::webrtc::rtclog::AudioNetworkAdaptation&
Event::_Internal::audio_network_adaptation(const Event* msg) {
  return *msg->subtype_.audio_network_adaptation_;
}
const ::webrtc::rtclog::BweProbeCluster&
Event::_Internal::probe_cluster(const Event* msg) {
  return *msg->subtype_.probe_cluster_;
}
const ::webrtc::rtclog::BweProbeResult&
Event::_Internal::probe_result(const Event* msg) {
  return *msg->subtype_.probe_result_;
}
const ::webrtc::rtclog::AlrState&
Event::_Internal::alr_state(const Event* msg) {
  return *msg->subtype_.alr_state_;
}
const ::webrtc::rtclog::IceCandidatePairConfig&
Event::_Internal::ice_candidate_pair_config(const Event* msg) {
  return *msg->subtype_.ice_candidate_pair_config_;
}
const ::webrtc::rtclog::IceCandidatePairEvent&
Event::_Internal::ice_candidate_pair_event(const Event* msg) {
  return *msg->subtype_.ice_candidate_pair_event_;
}
const ::webrtc::rtclog::RemoteEstimate&
Event::_Internal::remote_estimate(const Event* msg) {
  return *msg->subtype_.remote_estimate_;
}
void Event::set_allocated_rtp_packet(::webrtc::rtclog::RtpPacket* rtp_packet) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_subtype();
  if (rtp_packet) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(rtp_packet);
    if (message_arena != submessage_arena) {
      rtp_packet = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rtp_packet, submessage_arena);
    }
    set_has_rtp_packet();
    subtype_.rtp_packet_ = rtp_packet;
  }
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.Event.rtp_packet)
}
void Event::set_allocated_rtcp_packet(::webrtc::rtclog::RtcpPacket* rtcp_packet) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_subtype();
  if (rtcp_packet) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(rtcp_packet);
    if (message_arena != submessage_arena) {
      rtcp_packet = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rtcp_packet, submessage_arena);
    }
    set_has_rtcp_packet();
    subtype_.rtcp_packet_ = rtcp_packet;
  }
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.Event.rtcp_packet)
}
void Event::set_allocated_audio_playout_event(::webrtc::rtclog::AudioPlayoutEvent* audio_playout_event) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_subtype();
  if (audio_playout_event) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(audio_playout_event);
    if (message_arena != submessage_arena) {
      audio_playout_event = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, audio_playout_event, submessage_arena);
    }
    set_has_audio_playout_event();
    subtype_.audio_playout_event_ = audio_playout_event;
  }
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.Event.audio_playout_event)
}
void Event::set_allocated_loss_based_bwe_update(::webrtc::rtclog::LossBasedBweUpdate* loss_based_bwe_update) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_subtype();
  if (loss_based_bwe_update) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(loss_based_bwe_update);
    if (message_arena != submessage_arena) {
      loss_based_bwe_update = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, loss_based_bwe_update, submessage_arena);
    }
    set_has_loss_based_bwe_update();
    subtype_.loss_based_bwe_update_ = loss_based_bwe_update;
  }
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.Event.loss_based_bwe_update)
}
void Event::set_allocated_delay_based_bwe_update(::webrtc::rtclog::DelayBasedBweUpdate* delay_based_bwe_update) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_subtype();
  if (delay_based_bwe_update) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(delay_based_bwe_update);
    if (message_arena != submessage_arena) {
      delay_based_bwe_update = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, delay_based_bwe_update, submessage_arena);
    }
    set_has_delay_based_bwe_update();
    subtype_.delay_based_bwe_update_ = delay_based_bwe_update;
  }
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.Event.delay_based_bwe_update)
}
void Event::set_allocated_video_receiver_config(::webrtc::rtclog::VideoReceiveConfig* video_receiver_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_subtype();
  if (video_receiver_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(video_receiver_config);
    if (message_arena != submessage_arena) {
      video_receiver_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, video_receiver_config, submessage_arena);
    }
    set_has_video_receiver_config();
    subtype_.video_receiver_config_ = video_receiver_config;
  }
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.Event.video_receiver_config)
}
void Event::set_allocated_video_sender_config(::webrtc::rtclog::VideoSendConfig* video_sender_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_subtype();
  if (video_sender_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(video_sender_config);
    if (message_arena != submessage_arena) {
      video_sender_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, video_sender_config, submessage_arena);
    }
    set_has_video_sender_config();
    subtype_.video_sender_config_ = video_sender_config;
  }
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.Event.video_sender_config)
}
void Event::set_allocated_audio_receiver_config(::webrtc::rtclog::AudioReceiveConfig* audio_receiver_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_subtype();
  if (audio_receiver_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(audio_receiver_config);
    if (message_arena != submessage_arena) {
      audio_receiver_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, audio_receiver_config, submessage_arena);
    }
    set_has_audio_receiver_config();
    subtype_.audio_receiver_config_ = audio_receiver_config;
  }
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.Event.audio_receiver_config)
}
void Event::set_allocated_audio_sender_config(::webrtc::rtclog::AudioSendConfig* audio_sender_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_subtype();
  if (audio_sender_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(audio_sender_config);
    if (message_arena != submessage_arena) {
      audio_sender_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, audio_sender_config, submessage_arena);
    }
    set_has_audio_sender_config();
    subtype_.audio_sender_config_ = audio_sender_config;
  }
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.Event.audio_sender_config)
}
void Event::set_allocated_audio_network_adaptation(::webrtc::rtclog::AudioNetworkAdaptation* audio_network_adaptation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_subtype();
  if (audio_network_adaptation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(audio_network_adaptation);
    if (message_arena != submessage_arena) {
      audio_network_adaptation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, audio_network_adaptation, submessage_arena);
    }
    set_has_audio_network_adaptation();
    subtype_.audio_network_adaptation_ = audio_network_adaptation;
  }
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.Event.audio_network_adaptation)
}
void Event::set_allocated_probe_cluster(::webrtc::rtclog::BweProbeCluster* probe_cluster) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_subtype();
  if (probe_cluster) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(probe_cluster);
    if (message_arena != submessage_arena) {
      probe_cluster = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, probe_cluster, submessage_arena);
    }
    set_has_probe_cluster();
    subtype_.probe_cluster_ = probe_cluster;
  }
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.Event.probe_cluster)
}
void Event::set_allocated_probe_result(::webrtc::rtclog::BweProbeResult* probe_result) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_subtype();
  if (probe_result) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(probe_result);
    if (message_arena != submessage_arena) {
      probe_result = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, probe_result, submessage_arena);
    }
    set_has_probe_result();
    subtype_.probe_result_ = probe_result;
  }
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.Event.probe_result)
}
void Event::set_allocated_alr_state(::webrtc::rtclog::AlrState* alr_state) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_subtype();
  if (alr_state) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(alr_state);
    if (message_arena != submessage_arena) {
      alr_state = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, alr_state, submessage_arena);
    }
    set_has_alr_state();
    subtype_.alr_state_ = alr_state;
  }
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.Event.alr_state)
}
void Event::set_allocated_ice_candidate_pair_config(::webrtc::rtclog::IceCandidatePairConfig* ice_candidate_pair_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_subtype();
  if (ice_candidate_pair_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(ice_candidate_pair_config);
    if (message_arena != submessage_arena) {
      ice_candidate_pair_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ice_candidate_pair_config, submessage_arena);
    }
    set_has_ice_candidate_pair_config();
    subtype_.ice_candidate_pair_config_ = ice_candidate_pair_config;
  }
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.Event.ice_candidate_pair_config)
}
void Event::set_allocated_ice_candidate_pair_event(::webrtc::rtclog::IceCandidatePairEvent* ice_candidate_pair_event) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_subtype();
  if (ice_candidate_pair_event) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(ice_candidate_pair_event);
    if (message_arena != submessage_arena) {
      ice_candidate_pair_event = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ice_candidate_pair_event, submessage_arena);
    }
    set_has_ice_candidate_pair_event();
    subtype_.ice_candidate_pair_event_ = ice_candidate_pair_event;
  }
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.Event.ice_candidate_pair_event)
}
void Event::set_allocated_remote_estimate(::webrtc::rtclog::RemoteEstimate* remote_estimate) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_subtype();
  if (remote_estimate) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(remote_estimate);
    if (message_arena != submessage_arena) {
      remote_estimate = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, remote_estimate, submessage_arena);
    }
    set_has_remote_estimate();
    subtype_.remote_estimate_ = remote_estimate;
  }
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.Event.remote_estimate)
}
Event::Event(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.Event)
}
Event::Event(const Event& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ::memcpy(&timestamp_us_, &from.timestamp_us_,
    static_cast<size_t>(reinterpret_cast<char*>(&type_) -
    reinterpret_cast<char*>(&timestamp_us_)) + sizeof(type_));
  clear_has_subtype();
  switch (from.subtype_case()) {
    case kRtpPacket: {
      _internal_mutable_rtp_packet()->::webrtc::rtclog::RtpPacket::MergeFrom(from._internal_rtp_packet());
      break;
    }
    case kRtcpPacket: {
      _internal_mutable_rtcp_packet()->::webrtc::rtclog::RtcpPacket::MergeFrom(from._internal_rtcp_packet());
      break;
    }
    case kAudioPlayoutEvent: {
      _internal_mutable_audio_playout_event()->::webrtc::rtclog::AudioPlayoutEvent::MergeFrom(from._internal_audio_playout_event());
      break;
    }
    case kLossBasedBweUpdate: {
      _internal_mutable_loss_based_bwe_update()->::webrtc::rtclog::LossBasedBweUpdate::MergeFrom(from._internal_loss_based_bwe_update());
      break;
    }
    case kDelayBasedBweUpdate: {
      _internal_mutable_delay_based_bwe_update()->::webrtc::rtclog::DelayBasedBweUpdate::MergeFrom(from._internal_delay_based_bwe_update());
      break;
    }
    case kVideoReceiverConfig: {
      _internal_mutable_video_receiver_config()->::webrtc::rtclog::VideoReceiveConfig::MergeFrom(from._internal_video_receiver_config());
      break;
    }
    case kVideoSenderConfig: {
      _internal_mutable_video_sender_config()->::webrtc::rtclog::VideoSendConfig::MergeFrom(from._internal_video_sender_config());
      break;
    }
    case kAudioReceiverConfig: {
      _internal_mutable_audio_receiver_config()->::webrtc::rtclog::AudioReceiveConfig::MergeFrom(from._internal_audio_receiver_config());
      break;
    }
    case kAudioSenderConfig: {
      _internal_mutable_audio_sender_config()->::webrtc::rtclog::AudioSendConfig::MergeFrom(from._internal_audio_sender_config());
      break;
    }
    case kAudioNetworkAdaptation: {
      _internal_mutable_audio_network_adaptation()->::webrtc::rtclog::AudioNetworkAdaptation::MergeFrom(from._internal_audio_network_adaptation());
      break;
    }
    case kProbeCluster: {
      _internal_mutable_probe_cluster()->::webrtc::rtclog::BweProbeCluster::MergeFrom(from._internal_probe_cluster());
      break;
    }
    case kProbeResult: {
      _internal_mutable_probe_result()->::webrtc::rtclog::BweProbeResult::MergeFrom(from._internal_probe_result());
      break;
    }
    case kAlrState: {
      _internal_mutable_alr_state()->::webrtc::rtclog::AlrState::MergeFrom(from._internal_alr_state());
      break;
    }
    case kIceCandidatePairConfig: {
      _internal_mutable_ice_candidate_pair_config()->::webrtc::rtclog::IceCandidatePairConfig::MergeFrom(from._internal_ice_candidate_pair_config());
      break;
    }
    case kIceCandidatePairEvent: {
      _internal_mutable_ice_candidate_pair_event()->::webrtc::rtclog::IceCandidatePairEvent::MergeFrom(from._internal_ice_candidate_pair_event());
      break;
    }
    case kRemoteEstimate: {
      _internal_mutable_remote_estimate()->::webrtc::rtclog::RemoteEstimate::MergeFrom(from._internal_remote_estimate());
      break;
    }
    case SUBTYPE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.Event)
}

inline void Event::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&timestamp_us_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&type_) -
    reinterpret_cast<char*>(&timestamp_us_)) + sizeof(type_));
clear_has_subtype();
}

Event::~Event() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.Event)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Event::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_subtype()) {
    clear_subtype();
  }
}

void Event::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Event::clear_subtype() {
// @@protoc_insertion_point(one_of_clear_start:webrtc.rtclog.Event)
  switch (subtype_case()) {
    case kRtpPacket: {
      if (GetArenaForAllocation() == nullptr) {
        delete subtype_.rtp_packet_;
      }
      break;
    }
    case kRtcpPacket: {
      if (GetArenaForAllocation() == nullptr) {
        delete subtype_.rtcp_packet_;
      }
      break;
    }
    case kAudioPlayoutEvent: {
      if (GetArenaForAllocation() == nullptr) {
        delete subtype_.audio_playout_event_;
      }
      break;
    }
    case kLossBasedBweUpdate: {
      if (GetArenaForAllocation() == nullptr) {
        delete subtype_.loss_based_bwe_update_;
      }
      break;
    }
    case kDelayBasedBweUpdate: {
      if (GetArenaForAllocation() == nullptr) {
        delete subtype_.delay_based_bwe_update_;
      }
      break;
    }
    case kVideoReceiverConfig: {
      if (GetArenaForAllocation() == nullptr) {
        delete subtype_.video_receiver_config_;
      }
      break;
    }
    case kVideoSenderConfig: {
      if (GetArenaForAllocation() == nullptr) {
        delete subtype_.video_sender_config_;
      }
      break;
    }
    case kAudioReceiverConfig: {
      if (GetArenaForAllocation() == nullptr) {
        delete subtype_.audio_receiver_config_;
      }
      break;
    }
    case kAudioSenderConfig: {
      if (GetArenaForAllocation() == nullptr) {
        delete subtype_.audio_sender_config_;
      }
      break;
    }
    case kAudioNetworkAdaptation: {
      if (GetArenaForAllocation() == nullptr) {
        delete subtype_.audio_network_adaptation_;
      }
      break;
    }
    case kProbeCluster: {
      if (GetArenaForAllocation() == nullptr) {
        delete subtype_.probe_cluster_;
      }
      break;
    }
    case kProbeResult: {
      if (GetArenaForAllocation() == nullptr) {
        delete subtype_.probe_result_;
      }
      break;
    }
    case kAlrState: {
      if (GetArenaForAllocation() == nullptr) {
        delete subtype_.alr_state_;
      }
      break;
    }
    case kIceCandidatePairConfig: {
      if (GetArenaForAllocation() == nullptr) {
        delete subtype_.ice_candidate_pair_config_;
      }
      break;
    }
    case kIceCandidatePairEvent: {
      if (GetArenaForAllocation() == nullptr) {
        delete subtype_.ice_candidate_pair_event_;
      }
      break;
    }
    case kRemoteEstimate: {
      if (GetArenaForAllocation() == nullptr) {
        delete subtype_.remote_estimate_;
      }
      break;
    }
    case SUBTYPE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = SUBTYPE_NOT_SET;
}


void Event::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.Event)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    ::memset(&timestamp_us_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&type_) -
        reinterpret_cast<char*>(&timestamp_us_)) + sizeof(type_));
  }
  clear_subtype();
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* Event::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional int64 timestamp_us = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_timestamp_us(&has_bits);
          timestamp_us_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.rtclog.Event.EventType type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::webrtc::rtclog::Event_EventType_IsValid(val))) {
            _internal_set_type(static_cast<::webrtc::rtclog::Event_EventType>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(2, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // .webrtc.rtclog.RtpPacket rtp_packet = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_rtp_packet(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .webrtc.rtclog.RtcpPacket rtcp_packet = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_rtcp_packet(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .webrtc.rtclog.AudioPlayoutEvent audio_playout_event = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_audio_playout_event(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .webrtc.rtclog.LossBasedBweUpdate loss_based_bwe_update = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_loss_based_bwe_update(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .webrtc.rtclog.DelayBasedBweUpdate delay_based_bwe_update = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_delay_based_bwe_update(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .webrtc.rtclog.VideoReceiveConfig video_receiver_config = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_video_receiver_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .webrtc.rtclog.VideoSendConfig video_sender_config = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_video_sender_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .webrtc.rtclog.AudioReceiveConfig audio_receiver_config = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          ptr = ctx->ParseMessage(_internal_mutable_audio_receiver_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .webrtc.rtclog.AudioSendConfig audio_sender_config = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr = ctx->ParseMessage(_internal_mutable_audio_sender_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .webrtc.rtclog.AudioNetworkAdaptation audio_network_adaptation = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 130)) {
          ptr = ctx->ParseMessage(_internal_mutable_audio_network_adaptation(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .webrtc.rtclog.BweProbeCluster probe_cluster = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 138)) {
          ptr = ctx->ParseMessage(_internal_mutable_probe_cluster(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .webrtc.rtclog.BweProbeResult probe_result = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 146)) {
          ptr = ctx->ParseMessage(_internal_mutable_probe_result(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .webrtc.rtclog.AlrState alr_state = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 154)) {
          ptr = ctx->ParseMessage(_internal_mutable_alr_state(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .webrtc.rtclog.IceCandidatePairConfig ice_candidate_pair_config = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 162)) {
          ptr = ctx->ParseMessage(_internal_mutable_ice_candidate_pair_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .webrtc.rtclog.IceCandidatePairEvent ice_candidate_pair_event = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 170)) {
          ptr = ctx->ParseMessage(_internal_mutable_ice_candidate_pair_event(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .webrtc.rtclog.RemoteEstimate remote_estimate = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 178)) {
          ptr = ctx->ParseMessage(_internal_mutable_remote_estimate(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Event::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.Event)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int64 timestamp_us = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(1, this->_internal_timestamp_us(), target);
  }

  // optional .webrtc.rtclog.Event.EventType type = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      2, this->_internal_type(), target);
  }

  switch (subtype_case()) {
    case kRtpPacket: {
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(3, _Internal::rtp_packet(this),
          _Internal::rtp_packet(this).GetCachedSize(), target, stream);
      break;
    }
    case kRtcpPacket: {
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(4, _Internal::rtcp_packet(this),
          _Internal::rtcp_packet(this).GetCachedSize(), target, stream);
      break;
    }
    case kAudioPlayoutEvent: {
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(5, _Internal::audio_playout_event(this),
          _Internal::audio_playout_event(this).GetCachedSize(), target, stream);
      break;
    }
    case kLossBasedBweUpdate: {
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(6, _Internal::loss_based_bwe_update(this),
          _Internal::loss_based_bwe_update(this).GetCachedSize(), target, stream);
      break;
    }
    case kDelayBasedBweUpdate: {
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(7, _Internal::delay_based_bwe_update(this),
          _Internal::delay_based_bwe_update(this).GetCachedSize(), target, stream);
      break;
    }
    case kVideoReceiverConfig: {
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(8, _Internal::video_receiver_config(this),
          _Internal::video_receiver_config(this).GetCachedSize(), target, stream);
      break;
    }
    case kVideoSenderConfig: {
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(9, _Internal::video_sender_config(this),
          _Internal::video_sender_config(this).GetCachedSize(), target, stream);
      break;
    }
    case kAudioReceiverConfig: {
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(10, _Internal::audio_receiver_config(this),
          _Internal::audio_receiver_config(this).GetCachedSize(), target, stream);
      break;
    }
    case kAudioSenderConfig: {
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(11, _Internal::audio_sender_config(this),
          _Internal::audio_sender_config(this).GetCachedSize(), target, stream);
      break;
    }
    case kAudioNetworkAdaptation: {
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(16, _Internal::audio_network_adaptation(this),
          _Internal::audio_network_adaptation(this).GetCachedSize(), target, stream);
      break;
    }
    case kProbeCluster: {
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(17, _Internal::probe_cluster(this),
          _Internal::probe_cluster(this).GetCachedSize(), target, stream);
      break;
    }
    case kProbeResult: {
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(18, _Internal::probe_result(this),
          _Internal::probe_result(this).GetCachedSize(), target, stream);
      break;
    }
    case kAlrState: {
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(19, _Internal::alr_state(this),
          _Internal::alr_state(this).GetCachedSize(), target, stream);
      break;
    }
    case kIceCandidatePairConfig: {
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(20, _Internal::ice_candidate_pair_config(this),
          _Internal::ice_candidate_pair_config(this).GetCachedSize(), target, stream);
      break;
    }
    case kIceCandidatePairEvent: {
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(21, _Internal::ice_candidate_pair_event(this),
          _Internal::ice_candidate_pair_event(this).GetCachedSize(), target, stream);
      break;
    }
    case kRemoteEstimate: {
      target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(22, _Internal::remote_estimate(this),
          _Internal::remote_estimate(this).GetCachedSize(), target, stream);
      break;
    }
    default: ;
  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.Event)
  return target;
}

size_t Event::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.Event)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional int64 timestamp_us = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_us());
    }

    // optional .webrtc.rtclog.Event.EventType type = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::_pbi::WireFormatLite::EnumSize(this->_internal_type());
    }

  }
  switch (subtype_case()) {
    // .webrtc.rtclog.RtpPacket rtp_packet = 3;
    case kRtpPacket: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *subtype_.rtp_packet_);
      break;
    }
    // .webrtc.rtclog.RtcpPacket rtcp_packet = 4;
    case kRtcpPacket: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *subtype_.rtcp_packet_);
      break;
    }
    // .webrtc.rtclog.AudioPlayoutEvent audio_playout_event = 5;
    case kAudioPlayoutEvent: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *subtype_.audio_playout_event_);
      break;
    }
    // .webrtc.rtclog.LossBasedBweUpdate loss_based_bwe_update = 6;
    case kLossBasedBweUpdate: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *subtype_.loss_based_bwe_update_);
      break;
    }
    // .webrtc.rtclog.DelayBasedBweUpdate delay_based_bwe_update = 7;
    case kDelayBasedBweUpdate: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *subtype_.delay_based_bwe_update_);
      break;
    }
    // .webrtc.rtclog.VideoReceiveConfig video_receiver_config = 8;
    case kVideoReceiverConfig: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *subtype_.video_receiver_config_);
      break;
    }
    // .webrtc.rtclog.VideoSendConfig video_sender_config = 9;
    case kVideoSenderConfig: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *subtype_.video_sender_config_);
      break;
    }
    // .webrtc.rtclog.AudioReceiveConfig audio_receiver_config = 10;
    case kAudioReceiverConfig: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *subtype_.audio_receiver_config_);
      break;
    }
    // .webrtc.rtclog.AudioSendConfig audio_sender_config = 11;
    case kAudioSenderConfig: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *subtype_.audio_sender_config_);
      break;
    }
    // .webrtc.rtclog.AudioNetworkAdaptation audio_network_adaptation = 16;
    case kAudioNetworkAdaptation: {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *subtype_.audio_network_adaptation_);
      break;
    }
    // .webrtc.rtclog.BweProbeCluster probe_cluster = 17;
    case kProbeCluster: {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *subtype_.probe_cluster_);
      break;
    }
    // .webrtc.rtclog.BweProbeResult probe_result = 18;
    case kProbeResult: {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *subtype_.probe_result_);
      break;
    }
    // .webrtc.rtclog.AlrState alr_state = 19;
    case kAlrState: {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *subtype_.alr_state_);
      break;
    }
    // .webrtc.rtclog.IceCandidatePairConfig ice_candidate_pair_config = 20;
    case kIceCandidatePairConfig: {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *subtype_.ice_candidate_pair_config_);
      break;
    }
    // .webrtc.rtclog.IceCandidatePairEvent ice_candidate_pair_event = 21;
    case kIceCandidatePairEvent: {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *subtype_.ice_candidate_pair_event_);
      break;
    }
    // .webrtc.rtclog.RemoteEstimate remote_estimate = 22;
    case kRemoteEstimate: {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *subtype_.remote_estimate_);
      break;
    }
    case SUBTYPE_NOT_SET: {
      break;
    }
  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Event::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const Event*>(
      &from));
}

void Event::MergeFrom(const Event& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.Event)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      timestamp_us_ = from.timestamp_us_;
    }
    if (cached_has_bits & 0x00000002u) {
      type_ = from.type_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  switch (from.subtype_case()) {
    case kRtpPacket: {
      _internal_mutable_rtp_packet()->::webrtc::rtclog::RtpPacket::MergeFrom(from._internal_rtp_packet());
      break;
    }
    case kRtcpPacket: {
      _internal_mutable_rtcp_packet()->::webrtc::rtclog::RtcpPacket::MergeFrom(from._internal_rtcp_packet());
      break;
    }
    case kAudioPlayoutEvent: {
      _internal_mutable_audio_playout_event()->::webrtc::rtclog::AudioPlayoutEvent::MergeFrom(from._internal_audio_playout_event());
      break;
    }
    case kLossBasedBweUpdate: {
      _internal_mutable_loss_based_bwe_update()->::webrtc::rtclog::LossBasedBweUpdate::MergeFrom(from._internal_loss_based_bwe_update());
      break;
    }
    case kDelayBasedBweUpdate: {
      _internal_mutable_delay_based_bwe_update()->::webrtc::rtclog::DelayBasedBweUpdate::MergeFrom(from._internal_delay_based_bwe_update());
      break;
    }
    case kVideoReceiverConfig: {
      _internal_mutable_video_receiver_config()->::webrtc::rtclog::VideoReceiveConfig::MergeFrom(from._internal_video_receiver_config());
      break;
    }
    case kVideoSenderConfig: {
      _internal_mutable_video_sender_config()->::webrtc::rtclog::VideoSendConfig::MergeFrom(from._internal_video_sender_config());
      break;
    }
    case kAudioReceiverConfig: {
      _internal_mutable_audio_receiver_config()->::webrtc::rtclog::AudioReceiveConfig::MergeFrom(from._internal_audio_receiver_config());
      break;
    }
    case kAudioSenderConfig: {
      _internal_mutable_audio_sender_config()->::webrtc::rtclog::AudioSendConfig::MergeFrom(from._internal_audio_sender_config());
      break;
    }
    case kAudioNetworkAdaptation: {
      _internal_mutable_audio_network_adaptation()->::webrtc::rtclog::AudioNetworkAdaptation::MergeFrom(from._internal_audio_network_adaptation());
      break;
    }
    case kProbeCluster: {
      _internal_mutable_probe_cluster()->::webrtc::rtclog::BweProbeCluster::MergeFrom(from._internal_probe_cluster());
      break;
    }
    case kProbeResult: {
      _internal_mutable_probe_result()->::webrtc::rtclog::BweProbeResult::MergeFrom(from._internal_probe_result());
      break;
    }
    case kAlrState: {
      _internal_mutable_alr_state()->::webrtc::rtclog::AlrState::MergeFrom(from._internal_alr_state());
      break;
    }
    case kIceCandidatePairConfig: {
      _internal_mutable_ice_candidate_pair_config()->::webrtc::rtclog::IceCandidatePairConfig::MergeFrom(from._internal_ice_candidate_pair_config());
      break;
    }
    case kIceCandidatePairEvent: {
      _internal_mutable_ice_candidate_pair_event()->::webrtc::rtclog::IceCandidatePairEvent::MergeFrom(from._internal_ice_candidate_pair_event());
      break;
    }
    case kRemoteEstimate: {
      _internal_mutable_remote_estimate()->::webrtc::rtclog::RemoteEstimate::MergeFrom(from._internal_remote_estimate());
      break;
    }
    case SUBTYPE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void Event::CopyFrom(const Event& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.Event)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Event::IsInitialized() const {
  return true;
}

void Event::InternalSwap(Event* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Event, type_)
      + sizeof(Event::type_)
      - PROTOBUF_FIELD_OFFSET(Event, timestamp_us_)>(
          reinterpret_cast<char*>(&timestamp_us_),
          reinterpret_cast<char*>(&other->timestamp_us_));
  swap(subtype_, other->subtype_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

std::string Event::GetTypeName() const {
  return "webrtc.rtclog.Event";
}


// ===================================================================

class RtpPacket::_Internal {
 public:
  using HasBits = decltype(std::declval<RtpPacket>()._has_bits_);
  static void set_has_incoming(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_type(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_packet_length(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_header(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_probe_cluster_id(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
};

RtpPacket::RtpPacket(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.RtpPacket)
}
RtpPacket::RtpPacket(const RtpPacket& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  header_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    header_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_header()) {
    header_.Set(from._internal_header(), 
      GetArenaForAllocation());
  }
  ::memcpy(&incoming_, &from.incoming_,
    static_cast<size_t>(reinterpret_cast<char*>(&probe_cluster_id_) -
    reinterpret_cast<char*>(&incoming_)) + sizeof(probe_cluster_id_));
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.RtpPacket)
}

inline void RtpPacket::SharedCtor() {
header_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  header_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&incoming_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&probe_cluster_id_) -
    reinterpret_cast<char*>(&incoming_)) + sizeof(probe_cluster_id_));
}

RtpPacket::~RtpPacket() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.RtpPacket)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RtpPacket::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  header_.Destroy();
}

void RtpPacket::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RtpPacket::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.RtpPacket)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    header_.ClearNonDefaultToEmpty();
  }
  if (cached_has_bits & 0x0000001eu) {
    ::memset(&incoming_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&probe_cluster_id_) -
        reinterpret_cast<char*>(&incoming_)) + sizeof(probe_cluster_id_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* RtpPacket::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional bool incoming = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_incoming(&has_bits);
          incoming_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.rtclog.MediaType type = 2 [deprecated = true];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::webrtc::rtclog::MediaType_IsValid(val))) {
            _internal_set_type(static_cast<::webrtc::rtclog::MediaType>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(2, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional uint32 packet_length = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _Internal::set_has_packet_length(&has_bits);
          packet_length_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bytes header = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_header();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 probe_cluster_id = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _Internal::set_has_probe_cluster_id(&has_bits);
          probe_cluster_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RtpPacket::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.RtpPacket)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bool incoming = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(1, this->_internal_incoming(), target);
  }

  // optional .webrtc.rtclog.MediaType type = 2 [deprecated = true];
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      2, this->_internal_type(), target);
  }

  // optional uint32 packet_length = 3;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(3, this->_internal_packet_length(), target);
  }

  // optional bytes header = 4;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteBytesMaybeAliased(
        4, this->_internal_header(), target);
  }

  // optional int32 probe_cluster_id = 5;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(5, this->_internal_probe_cluster_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.RtpPacket)
  return target;
}

size_t RtpPacket::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.RtpPacket)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    // optional bytes header = 4;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
          this->_internal_header());
    }

    // optional bool incoming = 1;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 + 1;
    }

    // optional .webrtc.rtclog.MediaType type = 2 [deprecated = true];
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::_pbi::WireFormatLite::EnumSize(this->_internal_type());
    }

    // optional uint32 packet_length = 3;
    if (cached_has_bits & 0x00000008u) {
      total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_packet_length());
    }

    // optional int32 probe_cluster_id = 5;
    if (cached_has_bits & 0x00000010u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_probe_cluster_id());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RtpPacket::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const RtpPacket*>(
      &from));
}

void RtpPacket::MergeFrom(const RtpPacket& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.RtpPacket)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_header(from._internal_header());
    }
    if (cached_has_bits & 0x00000002u) {
      incoming_ = from.incoming_;
    }
    if (cached_has_bits & 0x00000004u) {
      type_ = from.type_;
    }
    if (cached_has_bits & 0x00000008u) {
      packet_length_ = from.packet_length_;
    }
    if (cached_has_bits & 0x00000010u) {
      probe_cluster_id_ = from.probe_cluster_id_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void RtpPacket::CopyFrom(const RtpPacket& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.RtpPacket)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RtpPacket::IsInitialized() const {
  return true;
}

void RtpPacket::InternalSwap(RtpPacket* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &header_, lhs_arena,
      &other->header_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RtpPacket, probe_cluster_id_)
      + sizeof(RtpPacket::probe_cluster_id_)
      - PROTOBUF_FIELD_OFFSET(RtpPacket, incoming_)>(
          reinterpret_cast<char*>(&incoming_),
          reinterpret_cast<char*>(&other->incoming_));
}

std::string RtpPacket::GetTypeName() const {
  return "webrtc.rtclog.RtpPacket";
}


// ===================================================================

class RtcpPacket::_Internal {
 public:
  using HasBits = decltype(std::declval<RtcpPacket>()._has_bits_);
  static void set_has_incoming(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_type(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_packet_data(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

RtcpPacket::RtcpPacket(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.RtcpPacket)
}
RtcpPacket::RtcpPacket(const RtcpPacket& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  packet_data_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    packet_data_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_packet_data()) {
    packet_data_.Set(from._internal_packet_data(), 
      GetArenaForAllocation());
  }
  ::memcpy(&incoming_, &from.incoming_,
    static_cast<size_t>(reinterpret_cast<char*>(&type_) -
    reinterpret_cast<char*>(&incoming_)) + sizeof(type_));
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.RtcpPacket)
}

inline void RtcpPacket::SharedCtor() {
packet_data_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  packet_data_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&incoming_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&type_) -
    reinterpret_cast<char*>(&incoming_)) + sizeof(type_));
}

RtcpPacket::~RtcpPacket() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.RtcpPacket)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RtcpPacket::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  packet_data_.Destroy();
}

void RtcpPacket::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RtcpPacket::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.RtcpPacket)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    packet_data_.ClearNonDefaultToEmpty();
  }
  if (cached_has_bits & 0x00000006u) {
    ::memset(&incoming_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&type_) -
        reinterpret_cast<char*>(&incoming_)) + sizeof(type_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* RtcpPacket::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional bool incoming = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_incoming(&has_bits);
          incoming_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.rtclog.MediaType type = 2 [deprecated = true];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::webrtc::rtclog::MediaType_IsValid(val))) {
            _internal_set_type(static_cast<::webrtc::rtclog::MediaType>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(2, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional bytes packet_data = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_packet_data();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RtcpPacket::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.RtcpPacket)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bool incoming = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(1, this->_internal_incoming(), target);
  }

  // optional .webrtc.rtclog.MediaType type = 2 [deprecated = true];
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      2, this->_internal_type(), target);
  }

  // optional bytes packet_data = 3;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteBytesMaybeAliased(
        3, this->_internal_packet_data(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.RtcpPacket)
  return target;
}

size_t RtcpPacket::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.RtcpPacket)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    // optional bytes packet_data = 3;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
          this->_internal_packet_data());
    }

    // optional bool incoming = 1;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 + 1;
    }

    // optional .webrtc.rtclog.MediaType type = 2 [deprecated = true];
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::_pbi::WireFormatLite::EnumSize(this->_internal_type());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RtcpPacket::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const RtcpPacket*>(
      &from));
}

void RtcpPacket::MergeFrom(const RtcpPacket& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.RtcpPacket)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_packet_data(from._internal_packet_data());
    }
    if (cached_has_bits & 0x00000002u) {
      incoming_ = from.incoming_;
    }
    if (cached_has_bits & 0x00000004u) {
      type_ = from.type_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void RtcpPacket::CopyFrom(const RtcpPacket& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.RtcpPacket)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RtcpPacket::IsInitialized() const {
  return true;
}

void RtcpPacket::InternalSwap(RtcpPacket* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &packet_data_, lhs_arena,
      &other->packet_data_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RtcpPacket, type_)
      + sizeof(RtcpPacket::type_)
      - PROTOBUF_FIELD_OFFSET(RtcpPacket, incoming_)>(
          reinterpret_cast<char*>(&incoming_),
          reinterpret_cast<char*>(&other->incoming_));
}

std::string RtcpPacket::GetTypeName() const {
  return "webrtc.rtclog.RtcpPacket";
}


// ===================================================================

class AudioPlayoutEvent::_Internal {
 public:
  using HasBits = decltype(std::declval<AudioPlayoutEvent>()._has_bits_);
  static void set_has_local_ssrc(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

AudioPlayoutEvent::AudioPlayoutEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.AudioPlayoutEvent)
}
AudioPlayoutEvent::AudioPlayoutEvent(const AudioPlayoutEvent& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  local_ssrc_ = from.local_ssrc_;
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.AudioPlayoutEvent)
}

inline void AudioPlayoutEvent::SharedCtor() {
local_ssrc_ = 0u;
}

AudioPlayoutEvent::~AudioPlayoutEvent() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.AudioPlayoutEvent)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void AudioPlayoutEvent::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void AudioPlayoutEvent::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AudioPlayoutEvent::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.AudioPlayoutEvent)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  local_ssrc_ = 0u;
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* AudioPlayoutEvent::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional uint32 local_ssrc = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_local_ssrc(&has_bits);
          local_ssrc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AudioPlayoutEvent::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.AudioPlayoutEvent)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional uint32 local_ssrc = 2;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(2, this->_internal_local_ssrc(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.AudioPlayoutEvent)
  return target;
}

size_t AudioPlayoutEvent::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.AudioPlayoutEvent)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional uint32 local_ssrc = 2;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_local_ssrc());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AudioPlayoutEvent::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const AudioPlayoutEvent*>(
      &from));
}

void AudioPlayoutEvent::MergeFrom(const AudioPlayoutEvent& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.AudioPlayoutEvent)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_local_ssrc()) {
    _internal_set_local_ssrc(from._internal_local_ssrc());
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void AudioPlayoutEvent::CopyFrom(const AudioPlayoutEvent& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.AudioPlayoutEvent)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AudioPlayoutEvent::IsInitialized() const {
  return true;
}

void AudioPlayoutEvent::InternalSwap(AudioPlayoutEvent* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(local_ssrc_, other->local_ssrc_);
}

std::string AudioPlayoutEvent::GetTypeName() const {
  return "webrtc.rtclog.AudioPlayoutEvent";
}


// ===================================================================

class LossBasedBweUpdate::_Internal {
 public:
  using HasBits = decltype(std::declval<LossBasedBweUpdate>()._has_bits_);
  static void set_has_bitrate_bps(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_fraction_loss(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_total_packets(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
};

LossBasedBweUpdate::LossBasedBweUpdate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.LossBasedBweUpdate)
}
LossBasedBweUpdate::LossBasedBweUpdate(const LossBasedBweUpdate& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ::memcpy(&bitrate_bps_, &from.bitrate_bps_,
    static_cast<size_t>(reinterpret_cast<char*>(&total_packets_) -
    reinterpret_cast<char*>(&bitrate_bps_)) + sizeof(total_packets_));
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.LossBasedBweUpdate)
}

inline void LossBasedBweUpdate::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bitrate_bps_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&total_packets_) -
    reinterpret_cast<char*>(&bitrate_bps_)) + sizeof(total_packets_));
}

LossBasedBweUpdate::~LossBasedBweUpdate() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.LossBasedBweUpdate)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void LossBasedBweUpdate::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void LossBasedBweUpdate::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LossBasedBweUpdate::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.LossBasedBweUpdate)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    ::memset(&bitrate_bps_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&total_packets_) -
        reinterpret_cast<char*>(&bitrate_bps_)) + sizeof(total_packets_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* LossBasedBweUpdate::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional int32 bitrate_bps = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_bitrate_bps(&has_bits);
          bitrate_bps_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional uint32 fraction_loss = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_fraction_loss(&has_bits);
          fraction_loss_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 total_packets = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _Internal::set_has_total_packets(&has_bits);
          total_packets_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LossBasedBweUpdate::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.LossBasedBweUpdate)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 bitrate_bps = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_bitrate_bps(), target);
  }

  // optional uint32 fraction_loss = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(2, this->_internal_fraction_loss(), target);
  }

  // optional int32 total_packets = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_total_packets(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.LossBasedBweUpdate)
  return target;
}

size_t LossBasedBweUpdate::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.LossBasedBweUpdate)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    // optional int32 bitrate_bps = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_bitrate_bps());
    }

    // optional uint32 fraction_loss = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_fraction_loss());
    }

    // optional int32 total_packets = 3;
    if (cached_has_bits & 0x00000004u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_total_packets());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void LossBasedBweUpdate::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const LossBasedBweUpdate*>(
      &from));
}

void LossBasedBweUpdate::MergeFrom(const LossBasedBweUpdate& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.LossBasedBweUpdate)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      bitrate_bps_ = from.bitrate_bps_;
    }
    if (cached_has_bits & 0x00000002u) {
      fraction_loss_ = from.fraction_loss_;
    }
    if (cached_has_bits & 0x00000004u) {
      total_packets_ = from.total_packets_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void LossBasedBweUpdate::CopyFrom(const LossBasedBweUpdate& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.LossBasedBweUpdate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LossBasedBweUpdate::IsInitialized() const {
  return true;
}

void LossBasedBweUpdate::InternalSwap(LossBasedBweUpdate* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LossBasedBweUpdate, total_packets_)
      + sizeof(LossBasedBweUpdate::total_packets_)
      - PROTOBUF_FIELD_OFFSET(LossBasedBweUpdate, bitrate_bps_)>(
          reinterpret_cast<char*>(&bitrate_bps_),
          reinterpret_cast<char*>(&other->bitrate_bps_));
}

std::string LossBasedBweUpdate::GetTypeName() const {
  return "webrtc.rtclog.LossBasedBweUpdate";
}


// ===================================================================

class DelayBasedBweUpdate::_Internal {
 public:
  using HasBits = decltype(std::declval<DelayBasedBweUpdate>()._has_bits_);
  static void set_has_bitrate_bps(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_detector_state(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

DelayBasedBweUpdate::DelayBasedBweUpdate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.DelayBasedBweUpdate)
}
DelayBasedBweUpdate::DelayBasedBweUpdate(const DelayBasedBweUpdate& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ::memcpy(&bitrate_bps_, &from.bitrate_bps_,
    static_cast<size_t>(reinterpret_cast<char*>(&detector_state_) -
    reinterpret_cast<char*>(&bitrate_bps_)) + sizeof(detector_state_));
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.DelayBasedBweUpdate)
}

inline void DelayBasedBweUpdate::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bitrate_bps_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&detector_state_) -
    reinterpret_cast<char*>(&bitrate_bps_)) + sizeof(detector_state_));
}

DelayBasedBweUpdate::~DelayBasedBweUpdate() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.DelayBasedBweUpdate)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void DelayBasedBweUpdate::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void DelayBasedBweUpdate::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DelayBasedBweUpdate::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.DelayBasedBweUpdate)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    ::memset(&bitrate_bps_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&detector_state_) -
        reinterpret_cast<char*>(&bitrate_bps_)) + sizeof(detector_state_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* DelayBasedBweUpdate::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional int32 bitrate_bps = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_bitrate_bps(&has_bits);
          bitrate_bps_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.rtclog.DelayBasedBweUpdate.DetectorState detector_state = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::webrtc::rtclog::DelayBasedBweUpdate_DetectorState_IsValid(val))) {
            _internal_set_detector_state(static_cast<::webrtc::rtclog::DelayBasedBweUpdate_DetectorState>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(2, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DelayBasedBweUpdate::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.DelayBasedBweUpdate)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 bitrate_bps = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_bitrate_bps(), target);
  }

  // optional .webrtc.rtclog.DelayBasedBweUpdate.DetectorState detector_state = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      2, this->_internal_detector_state(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.DelayBasedBweUpdate)
  return target;
}

size_t DelayBasedBweUpdate::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.DelayBasedBweUpdate)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional int32 bitrate_bps = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_bitrate_bps());
    }

    // optional .webrtc.rtclog.DelayBasedBweUpdate.DetectorState detector_state = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::_pbi::WireFormatLite::EnumSize(this->_internal_detector_state());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DelayBasedBweUpdate::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const DelayBasedBweUpdate*>(
      &from));
}

void DelayBasedBweUpdate::MergeFrom(const DelayBasedBweUpdate& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.DelayBasedBweUpdate)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      bitrate_bps_ = from.bitrate_bps_;
    }
    if (cached_has_bits & 0x00000002u) {
      detector_state_ = from.detector_state_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void DelayBasedBweUpdate::CopyFrom(const DelayBasedBweUpdate& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.DelayBasedBweUpdate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DelayBasedBweUpdate::IsInitialized() const {
  return true;
}

void DelayBasedBweUpdate::InternalSwap(DelayBasedBweUpdate* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DelayBasedBweUpdate, detector_state_)
      + sizeof(DelayBasedBweUpdate::detector_state_)
      - PROTOBUF_FIELD_OFFSET(DelayBasedBweUpdate, bitrate_bps_)>(
          reinterpret_cast<char*>(&bitrate_bps_),
          reinterpret_cast<char*>(&other->bitrate_bps_));
}

std::string DelayBasedBweUpdate::GetTypeName() const {
  return "webrtc.rtclog.DelayBasedBweUpdate";
}


// ===================================================================

class VideoReceiveConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<VideoReceiveConfig>()._has_bits_);
  static void set_has_remote_ssrc(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_local_ssrc(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_rtcp_mode(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_remb(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
};

VideoReceiveConfig::VideoReceiveConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned),
  rtx_map_(arena),
  header_extensions_(arena),
  decoders_(arena) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.VideoReceiveConfig)
}
VideoReceiveConfig::VideoReceiveConfig(const VideoReceiveConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_),
      rtx_map_(from.rtx_map_),
      header_extensions_(from.header_extensions_),
      decoders_(from.decoders_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ::memcpy(&remote_ssrc_, &from.remote_ssrc_,
    static_cast<size_t>(reinterpret_cast<char*>(&rtcp_mode_) -
    reinterpret_cast<char*>(&remote_ssrc_)) + sizeof(rtcp_mode_));
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.VideoReceiveConfig)
}

inline void VideoReceiveConfig::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&remote_ssrc_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&remb_) -
    reinterpret_cast<char*>(&remote_ssrc_)) + sizeof(remb_));
rtcp_mode_ = 1;
}

VideoReceiveConfig::~VideoReceiveConfig() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.VideoReceiveConfig)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void VideoReceiveConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void VideoReceiveConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void VideoReceiveConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.VideoReceiveConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  rtx_map_.Clear();
  header_extensions_.Clear();
  decoders_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    ::memset(&remote_ssrc_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&remb_) -
        reinterpret_cast<char*>(&remote_ssrc_)) + sizeof(remb_));
    rtcp_mode_ = 1;
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* VideoReceiveConfig::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional uint32 remote_ssrc = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_remote_ssrc(&has_bits);
          remote_ssrc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional uint32 local_ssrc = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_local_ssrc(&has_bits);
          local_ssrc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.rtclog.VideoReceiveConfig.RtcpMode rtcp_mode = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::webrtc::rtclog::VideoReceiveConfig_RtcpMode_IsValid(val))) {
            _internal_set_rtcp_mode(static_cast<::webrtc::rtclog::VideoReceiveConfig_RtcpMode>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(3, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional bool remb = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _Internal::set_has_remb(&has_bits);
          remb_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .webrtc.rtclog.RtxMap rtx_map = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_rtx_map(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_header_extensions(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .webrtc.rtclog.DecoderConfig decoders = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_decoders(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* VideoReceiveConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.VideoReceiveConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional uint32 remote_ssrc = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(1, this->_internal_remote_ssrc(), target);
  }

  // optional uint32 local_ssrc = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(2, this->_internal_local_ssrc(), target);
  }

  // optional .webrtc.rtclog.VideoReceiveConfig.RtcpMode rtcp_mode = 3;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      3, this->_internal_rtcp_mode(), target);
  }

  // optional bool remb = 4;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(4, this->_internal_remb(), target);
  }

  // repeated .webrtc.rtclog.RtxMap rtx_map = 5;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_rtx_map_size()); i < n; i++) {
    const auto& repfield = this->_internal_rtx_map(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(5, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 6;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_header_extensions_size()); i < n; i++) {
    const auto& repfield = this->_internal_header_extensions(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(6, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated .webrtc.rtclog.DecoderConfig decoders = 7;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_decoders_size()); i < n; i++) {
    const auto& repfield = this->_internal_decoders(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(7, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.VideoReceiveConfig)
  return target;
}

size_t VideoReceiveConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.VideoReceiveConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .webrtc.rtclog.RtxMap rtx_map = 5;
  total_size += 1UL * this->_internal_rtx_map_size();
  for (const auto& msg : this->rtx_map_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 6;
  total_size += 1UL * this->_internal_header_extensions_size();
  for (const auto& msg : this->header_extensions_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .webrtc.rtclog.DecoderConfig decoders = 7;
  total_size += 1UL * this->_internal_decoders_size();
  for (const auto& msg : this->decoders_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    // optional uint32 remote_ssrc = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_remote_ssrc());
    }

    // optional uint32 local_ssrc = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_local_ssrc());
    }

    // optional bool remb = 4;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 + 1;
    }

    // optional .webrtc.rtclog.VideoReceiveConfig.RtcpMode rtcp_mode = 3;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::_pbi::WireFormatLite::EnumSize(this->_internal_rtcp_mode());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void VideoReceiveConfig::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const VideoReceiveConfig*>(
      &from));
}

void VideoReceiveConfig::MergeFrom(const VideoReceiveConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.VideoReceiveConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  rtx_map_.MergeFrom(from.rtx_map_);
  header_extensions_.MergeFrom(from.header_extensions_);
  decoders_.MergeFrom(from.decoders_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      remote_ssrc_ = from.remote_ssrc_;
    }
    if (cached_has_bits & 0x00000002u) {
      local_ssrc_ = from.local_ssrc_;
    }
    if (cached_has_bits & 0x00000004u) {
      remb_ = from.remb_;
    }
    if (cached_has_bits & 0x00000008u) {
      rtcp_mode_ = from.rtcp_mode_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void VideoReceiveConfig::CopyFrom(const VideoReceiveConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.VideoReceiveConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VideoReceiveConfig::IsInitialized() const {
  return true;
}

void VideoReceiveConfig::InternalSwap(VideoReceiveConfig* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  rtx_map_.InternalSwap(&other->rtx_map_);
  header_extensions_.InternalSwap(&other->header_extensions_);
  decoders_.InternalSwap(&other->decoders_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(VideoReceiveConfig, remb_)
      + sizeof(VideoReceiveConfig::remb_)
      - PROTOBUF_FIELD_OFFSET(VideoReceiveConfig, remote_ssrc_)>(
          reinterpret_cast<char*>(&remote_ssrc_),
          reinterpret_cast<char*>(&other->remote_ssrc_));
  swap(rtcp_mode_, other->rtcp_mode_);
}

std::string VideoReceiveConfig::GetTypeName() const {
  return "webrtc.rtclog.VideoReceiveConfig";
}


// ===================================================================

class DecoderConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<DecoderConfig>()._has_bits_);
  static void set_has_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_payload_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

DecoderConfig::DecoderConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.DecoderConfig)
}
DecoderConfig::DecoderConfig(const DecoderConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_name()) {
    name_.Set(from._internal_name(), 
      GetArenaForAllocation());
  }
  payload_type_ = from.payload_type_;
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.DecoderConfig)
}

inline void DecoderConfig::SharedCtor() {
name_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
payload_type_ = 0;
}

DecoderConfig::~DecoderConfig() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.DecoderConfig)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void DecoderConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.Destroy();
}

void DecoderConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DecoderConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.DecoderConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    name_.ClearNonDefaultToEmpty();
  }
  payload_type_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* DecoderConfig::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 payload_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_payload_type(&has_bits);
          payload_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DecoderConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.DecoderConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string name = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // optional int32 payload_type = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_payload_type(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.DecoderConfig)
  return target;
}

size_t DecoderConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.DecoderConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string name = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_name());
    }

    // optional int32 payload_type = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_payload_type());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void DecoderConfig::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const DecoderConfig*>(
      &from));
}

void DecoderConfig::MergeFrom(const DecoderConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.DecoderConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_name(from._internal_name());
    }
    if (cached_has_bits & 0x00000002u) {
      payload_type_ = from.payload_type_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void DecoderConfig::CopyFrom(const DecoderConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.DecoderConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DecoderConfig::IsInitialized() const {
  return true;
}

void DecoderConfig::InternalSwap(DecoderConfig* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  swap(payload_type_, other->payload_type_);
}

std::string DecoderConfig::GetTypeName() const {
  return "webrtc.rtclog.DecoderConfig";
}


// ===================================================================

class RtpHeaderExtension::_Internal {
 public:
  using HasBits = decltype(std::declval<RtpHeaderExtension>()._has_bits_);
  static void set_has_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_id(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

RtpHeaderExtension::RtpHeaderExtension(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.RtpHeaderExtension)
}
RtpHeaderExtension::RtpHeaderExtension(const RtpHeaderExtension& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_name()) {
    name_.Set(from._internal_name(), 
      GetArenaForAllocation());
  }
  id_ = from.id_;
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.RtpHeaderExtension)
}

inline void RtpHeaderExtension::SharedCtor() {
name_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
id_ = 0;
}

RtpHeaderExtension::~RtpHeaderExtension() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.RtpHeaderExtension)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RtpHeaderExtension::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.Destroy();
}

void RtpHeaderExtension::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RtpHeaderExtension::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.RtpHeaderExtension)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    name_.ClearNonDefaultToEmpty();
  }
  id_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* RtpHeaderExtension::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_id(&has_bits);
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RtpHeaderExtension::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.RtpHeaderExtension)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string name = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // optional int32 id = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.RtpHeaderExtension)
  return target;
}

size_t RtpHeaderExtension::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.RtpHeaderExtension)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string name = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_name());
    }

    // optional int32 id = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_id());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RtpHeaderExtension::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const RtpHeaderExtension*>(
      &from));
}

void RtpHeaderExtension::MergeFrom(const RtpHeaderExtension& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.RtpHeaderExtension)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_name(from._internal_name());
    }
    if (cached_has_bits & 0x00000002u) {
      id_ = from.id_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void RtpHeaderExtension::CopyFrom(const RtpHeaderExtension& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.RtpHeaderExtension)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RtpHeaderExtension::IsInitialized() const {
  return true;
}

void RtpHeaderExtension::InternalSwap(RtpHeaderExtension* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  swap(id_, other->id_);
}

std::string RtpHeaderExtension::GetTypeName() const {
  return "webrtc.rtclog.RtpHeaderExtension";
}


// ===================================================================

class RtxConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<RtxConfig>()._has_bits_);
  static void set_has_rtx_ssrc(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_rtx_payload_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

RtxConfig::RtxConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.RtxConfig)
}
RtxConfig::RtxConfig(const RtxConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ::memcpy(&rtx_ssrc_, &from.rtx_ssrc_,
    static_cast<size_t>(reinterpret_cast<char*>(&rtx_payload_type_) -
    reinterpret_cast<char*>(&rtx_ssrc_)) + sizeof(rtx_payload_type_));
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.RtxConfig)
}

inline void RtxConfig::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&rtx_ssrc_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&rtx_payload_type_) -
    reinterpret_cast<char*>(&rtx_ssrc_)) + sizeof(rtx_payload_type_));
}

RtxConfig::~RtxConfig() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.RtxConfig)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RtxConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RtxConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RtxConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.RtxConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    ::memset(&rtx_ssrc_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&rtx_payload_type_) -
        reinterpret_cast<char*>(&rtx_ssrc_)) + sizeof(rtx_payload_type_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* RtxConfig::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional uint32 rtx_ssrc = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_rtx_ssrc(&has_bits);
          rtx_ssrc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 rtx_payload_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_rtx_payload_type(&has_bits);
          rtx_payload_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RtxConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.RtxConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional uint32 rtx_ssrc = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(1, this->_internal_rtx_ssrc(), target);
  }

  // optional int32 rtx_payload_type = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_rtx_payload_type(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.RtxConfig)
  return target;
}

size_t RtxConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.RtxConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional uint32 rtx_ssrc = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_rtx_ssrc());
    }

    // optional int32 rtx_payload_type = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_rtx_payload_type());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RtxConfig::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const RtxConfig*>(
      &from));
}

void RtxConfig::MergeFrom(const RtxConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.RtxConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      rtx_ssrc_ = from.rtx_ssrc_;
    }
    if (cached_has_bits & 0x00000002u) {
      rtx_payload_type_ = from.rtx_payload_type_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void RtxConfig::CopyFrom(const RtxConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.RtxConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RtxConfig::IsInitialized() const {
  return true;
}

void RtxConfig::InternalSwap(RtxConfig* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RtxConfig, rtx_payload_type_)
      + sizeof(RtxConfig::rtx_payload_type_)
      - PROTOBUF_FIELD_OFFSET(RtxConfig, rtx_ssrc_)>(
          reinterpret_cast<char*>(&rtx_ssrc_),
          reinterpret_cast<char*>(&other->rtx_ssrc_));
}

std::string RtxConfig::GetTypeName() const {
  return "webrtc.rtclog.RtxConfig";
}


// ===================================================================

class RtxMap::_Internal {
 public:
  using HasBits = decltype(std::declval<RtxMap>()._has_bits_);
  static void set_has_payload_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::webrtc::rtclog::RtxConfig& config(const RtxMap* msg);
  static void set_has_config(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

const ::webrtc::rtclog::RtxConfig&
RtxMap::_Internal::config(const RtxMap* msg) {
  return *msg->config_;
}
RtxMap::RtxMap(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.RtxMap)
}
RtxMap::RtxMap(const RtxMap& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  if (from._internal_has_config()) {
    config_ = new ::webrtc::rtclog::RtxConfig(*from.config_);
  } else {
    config_ = nullptr;
  }
  payload_type_ = from.payload_type_;
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.RtxMap)
}

inline void RtxMap::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&config_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&payload_type_) -
    reinterpret_cast<char*>(&config_)) + sizeof(payload_type_));
}

RtxMap::~RtxMap() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.RtxMap)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RtxMap::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete config_;
}

void RtxMap::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RtxMap::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.RtxMap)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(config_ != nullptr);
    config_->Clear();
  }
  payload_type_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* RtxMap::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional int32 payload_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_payload_type(&has_bits);
          payload_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.rtclog.RtxConfig config = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RtxMap::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.RtxMap)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 payload_type = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_payload_type(), target);
  }

  // optional .webrtc.rtclog.RtxConfig config = 2;
  if (cached_has_bits & 0x00000001u) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::config(this),
        _Internal::config(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.RtxMap)
  return target;
}

size_t RtxMap::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.RtxMap)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional .webrtc.rtclog.RtxConfig config = 2;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *config_);
    }

    // optional int32 payload_type = 1;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_payload_type());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RtxMap::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const RtxMap*>(
      &from));
}

void RtxMap::MergeFrom(const RtxMap& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.RtxMap)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_config()->::webrtc::rtclog::RtxConfig::MergeFrom(from._internal_config());
    }
    if (cached_has_bits & 0x00000002u) {
      payload_type_ = from.payload_type_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void RtxMap::CopyFrom(const RtxMap& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.RtxMap)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RtxMap::IsInitialized() const {
  return true;
}

void RtxMap::InternalSwap(RtxMap* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RtxMap, payload_type_)
      + sizeof(RtxMap::payload_type_)
      - PROTOBUF_FIELD_OFFSET(RtxMap, config_)>(
          reinterpret_cast<char*>(&config_),
          reinterpret_cast<char*>(&other->config_));
}

std::string RtxMap::GetTypeName() const {
  return "webrtc.rtclog.RtxMap";
}


// ===================================================================

class VideoSendConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<VideoSendConfig>()._has_bits_);
  static void set_has_rtx_payload_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::webrtc::rtclog::EncoderConfig& encoder(const VideoSendConfig* msg);
  static void set_has_encoder(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

const ::webrtc::rtclog::EncoderConfig&
VideoSendConfig::_Internal::encoder(const VideoSendConfig* msg) {
  return *msg->encoder_;
}
VideoSendConfig::VideoSendConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned),
  ssrcs_(arena),
  header_extensions_(arena),
  rtx_ssrcs_(arena) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.VideoSendConfig)
}
VideoSendConfig::VideoSendConfig(const VideoSendConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_),
      ssrcs_(from.ssrcs_),
      header_extensions_(from.header_extensions_),
      rtx_ssrcs_(from.rtx_ssrcs_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  if (from._internal_has_encoder()) {
    encoder_ = new ::webrtc::rtclog::EncoderConfig(*from.encoder_);
  } else {
    encoder_ = nullptr;
  }
  rtx_payload_type_ = from.rtx_payload_type_;
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.VideoSendConfig)
}

inline void VideoSendConfig::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&encoder_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&rtx_payload_type_) -
    reinterpret_cast<char*>(&encoder_)) + sizeof(rtx_payload_type_));
}

VideoSendConfig::~VideoSendConfig() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.VideoSendConfig)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void VideoSendConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete encoder_;
}

void VideoSendConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void VideoSendConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.VideoSendConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ssrcs_.Clear();
  header_extensions_.Clear();
  rtx_ssrcs_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(encoder_ != nullptr);
    encoder_->Clear();
  }
  rtx_payload_type_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* VideoSendConfig::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated uint32 ssrcs = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          ptr -= 1;
          do {
            ptr += 1;
            _internal_add_ssrcs(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<8>(ptr));
        } else if (static_cast<uint8_t>(tag) == 10) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedUInt32Parser(_internal_mutable_ssrcs(), ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_header_extensions(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated uint32 rtx_ssrcs = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          ptr -= 1;
          do {
            ptr += 1;
            _internal_add_rtx_ssrcs(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<24>(ptr));
        } else if (static_cast<uint8_t>(tag) == 26) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedUInt32Parser(_internal_mutable_rtx_ssrcs(), ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 rtx_payload_type = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _Internal::set_has_rtx_payload_type(&has_bits);
          rtx_payload_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.rtclog.EncoderConfig encoder = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_encoder(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* VideoSendConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.VideoSendConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated uint32 ssrcs = 1;
  for (int i = 0, n = this->_internal_ssrcs_size(); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(1, this->_internal_ssrcs(i), target);
  }

  // repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 2;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_header_extensions_size()); i < n; i++) {
    const auto& repfield = this->_internal_header_extensions(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(2, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated uint32 rtx_ssrcs = 3;
  for (int i = 0, n = this->_internal_rtx_ssrcs_size(); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(3, this->_internal_rtx_ssrcs(i), target);
  }

  cached_has_bits = _has_bits_[0];
  // optional int32 rtx_payload_type = 4;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(4, this->_internal_rtx_payload_type(), target);
  }

  // optional .webrtc.rtclog.EncoderConfig encoder = 5;
  if (cached_has_bits & 0x00000001u) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, _Internal::encoder(this),
        _Internal::encoder(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.VideoSendConfig)
  return target;
}

size_t VideoSendConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.VideoSendConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated uint32 ssrcs = 1;
  {
    size_t data_size = ::_pbi::WireFormatLite::
      UInt32Size(this->ssrcs_);
    total_size += 1 *
                  ::_pbi::FromIntSize(this->_internal_ssrcs_size());
    total_size += data_size;
  }

  // repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 2;
  total_size += 1UL * this->_internal_header_extensions_size();
  for (const auto& msg : this->header_extensions_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated uint32 rtx_ssrcs = 3;
  {
    size_t data_size = ::_pbi::WireFormatLite::
      UInt32Size(this->rtx_ssrcs_);
    total_size += 1 *
                  ::_pbi::FromIntSize(this->_internal_rtx_ssrcs_size());
    total_size += data_size;
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional .webrtc.rtclog.EncoderConfig encoder = 5;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *encoder_);
    }

    // optional int32 rtx_payload_type = 4;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_rtx_payload_type());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void VideoSendConfig::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const VideoSendConfig*>(
      &from));
}

void VideoSendConfig::MergeFrom(const VideoSendConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.VideoSendConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  ssrcs_.MergeFrom(from.ssrcs_);
  header_extensions_.MergeFrom(from.header_extensions_);
  rtx_ssrcs_.MergeFrom(from.rtx_ssrcs_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_encoder()->::webrtc::rtclog::EncoderConfig::MergeFrom(from._internal_encoder());
    }
    if (cached_has_bits & 0x00000002u) {
      rtx_payload_type_ = from.rtx_payload_type_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void VideoSendConfig::CopyFrom(const VideoSendConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.VideoSendConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VideoSendConfig::IsInitialized() const {
  return true;
}

void VideoSendConfig::InternalSwap(VideoSendConfig* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ssrcs_.InternalSwap(&other->ssrcs_);
  header_extensions_.InternalSwap(&other->header_extensions_);
  rtx_ssrcs_.InternalSwap(&other->rtx_ssrcs_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(VideoSendConfig, rtx_payload_type_)
      + sizeof(VideoSendConfig::rtx_payload_type_)
      - PROTOBUF_FIELD_OFFSET(VideoSendConfig, encoder_)>(
          reinterpret_cast<char*>(&encoder_),
          reinterpret_cast<char*>(&other->encoder_));
}

std::string VideoSendConfig::GetTypeName() const {
  return "webrtc.rtclog.VideoSendConfig";
}


// ===================================================================

class EncoderConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<EncoderConfig>()._has_bits_);
  static void set_has_name(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_payload_type(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

EncoderConfig::EncoderConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.EncoderConfig)
}
EncoderConfig::EncoderConfig(const EncoderConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_name()) {
    name_.Set(from._internal_name(), 
      GetArenaForAllocation());
  }
  payload_type_ = from.payload_type_;
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.EncoderConfig)
}

inline void EncoderConfig::SharedCtor() {
name_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
payload_type_ = 0;
}

EncoderConfig::~EncoderConfig() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.EncoderConfig)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void EncoderConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.Destroy();
}

void EncoderConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EncoderConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.EncoderConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    name_.ClearNonDefaultToEmpty();
  }
  payload_type_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* EncoderConfig::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 payload_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_payload_type(&has_bits);
          payload_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* EncoderConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.EncoderConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional string name = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // optional int32 payload_type = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_payload_type(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.EncoderConfig)
  return target;
}

size_t EncoderConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.EncoderConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional string name = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_name());
    }

    // optional int32 payload_type = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_payload_type());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void EncoderConfig::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const EncoderConfig*>(
      &from));
}

void EncoderConfig::MergeFrom(const EncoderConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.EncoderConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_name(from._internal_name());
    }
    if (cached_has_bits & 0x00000002u) {
      payload_type_ = from.payload_type_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void EncoderConfig::CopyFrom(const EncoderConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.EncoderConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EncoderConfig::IsInitialized() const {
  return true;
}

void EncoderConfig::InternalSwap(EncoderConfig* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  swap(payload_type_, other->payload_type_);
}

std::string EncoderConfig::GetTypeName() const {
  return "webrtc.rtclog.EncoderConfig";
}


// ===================================================================

class AudioReceiveConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<AudioReceiveConfig>()._has_bits_);
  static void set_has_remote_ssrc(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_local_ssrc(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

AudioReceiveConfig::AudioReceiveConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned),
  header_extensions_(arena) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.AudioReceiveConfig)
}
AudioReceiveConfig::AudioReceiveConfig(const AudioReceiveConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_),
      header_extensions_(from.header_extensions_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ::memcpy(&remote_ssrc_, &from.remote_ssrc_,
    static_cast<size_t>(reinterpret_cast<char*>(&local_ssrc_) -
    reinterpret_cast<char*>(&remote_ssrc_)) + sizeof(local_ssrc_));
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.AudioReceiveConfig)
}

inline void AudioReceiveConfig::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&remote_ssrc_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&local_ssrc_) -
    reinterpret_cast<char*>(&remote_ssrc_)) + sizeof(local_ssrc_));
}

AudioReceiveConfig::~AudioReceiveConfig() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.AudioReceiveConfig)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void AudioReceiveConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void AudioReceiveConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AudioReceiveConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.AudioReceiveConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  header_extensions_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    ::memset(&remote_ssrc_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&local_ssrc_) -
        reinterpret_cast<char*>(&remote_ssrc_)) + sizeof(local_ssrc_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* AudioReceiveConfig::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional uint32 remote_ssrc = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_remote_ssrc(&has_bits);
          remote_ssrc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional uint32 local_ssrc = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_local_ssrc(&has_bits);
          local_ssrc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_header_extensions(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AudioReceiveConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.AudioReceiveConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional uint32 remote_ssrc = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(1, this->_internal_remote_ssrc(), target);
  }

  // optional uint32 local_ssrc = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(2, this->_internal_local_ssrc(), target);
  }

  // repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 3;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_header_extensions_size()); i < n; i++) {
    const auto& repfield = this->_internal_header_extensions(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(3, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.AudioReceiveConfig)
  return target;
}

size_t AudioReceiveConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.AudioReceiveConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 3;
  total_size += 1UL * this->_internal_header_extensions_size();
  for (const auto& msg : this->header_extensions_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional uint32 remote_ssrc = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_remote_ssrc());
    }

    // optional uint32 local_ssrc = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_local_ssrc());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AudioReceiveConfig::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const AudioReceiveConfig*>(
      &from));
}

void AudioReceiveConfig::MergeFrom(const AudioReceiveConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.AudioReceiveConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  header_extensions_.MergeFrom(from.header_extensions_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      remote_ssrc_ = from.remote_ssrc_;
    }
    if (cached_has_bits & 0x00000002u) {
      local_ssrc_ = from.local_ssrc_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void AudioReceiveConfig::CopyFrom(const AudioReceiveConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.AudioReceiveConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AudioReceiveConfig::IsInitialized() const {
  return true;
}

void AudioReceiveConfig::InternalSwap(AudioReceiveConfig* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  header_extensions_.InternalSwap(&other->header_extensions_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(AudioReceiveConfig, local_ssrc_)
      + sizeof(AudioReceiveConfig::local_ssrc_)
      - PROTOBUF_FIELD_OFFSET(AudioReceiveConfig, remote_ssrc_)>(
          reinterpret_cast<char*>(&remote_ssrc_),
          reinterpret_cast<char*>(&other->remote_ssrc_));
}

std::string AudioReceiveConfig::GetTypeName() const {
  return "webrtc.rtclog.AudioReceiveConfig";
}


// ===================================================================

class AudioSendConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<AudioSendConfig>()._has_bits_);
  static void set_has_ssrc(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

AudioSendConfig::AudioSendConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned),
  header_extensions_(arena) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.AudioSendConfig)
}
AudioSendConfig::AudioSendConfig(const AudioSendConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_),
      header_extensions_(from.header_extensions_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ssrc_ = from.ssrc_;
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.AudioSendConfig)
}

inline void AudioSendConfig::SharedCtor() {
ssrc_ = 0u;
}

AudioSendConfig::~AudioSendConfig() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.AudioSendConfig)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void AudioSendConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void AudioSendConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AudioSendConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.AudioSendConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  header_extensions_.Clear();
  ssrc_ = 0u;
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* AudioSendConfig::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional uint32 ssrc = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_ssrc(&has_bits);
          ssrc_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_header_extensions(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AudioSendConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.AudioSendConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional uint32 ssrc = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(1, this->_internal_ssrc(), target);
  }

  // repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 2;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_header_extensions_size()); i < n; i++) {
    const auto& repfield = this->_internal_header_extensions(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(2, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.AudioSendConfig)
  return target;
}

size_t AudioSendConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.AudioSendConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 2;
  total_size += 1UL * this->_internal_header_extensions_size();
  for (const auto& msg : this->header_extensions_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // optional uint32 ssrc = 1;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_ssrc());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AudioSendConfig::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const AudioSendConfig*>(
      &from));
}

void AudioSendConfig::MergeFrom(const AudioSendConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.AudioSendConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  header_extensions_.MergeFrom(from.header_extensions_);
  if (from._internal_has_ssrc()) {
    _internal_set_ssrc(from._internal_ssrc());
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void AudioSendConfig::CopyFrom(const AudioSendConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.AudioSendConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AudioSendConfig::IsInitialized() const {
  return true;
}

void AudioSendConfig::InternalSwap(AudioSendConfig* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  header_extensions_.InternalSwap(&other->header_extensions_);
  swap(ssrc_, other->ssrc_);
}

std::string AudioSendConfig::GetTypeName() const {
  return "webrtc.rtclog.AudioSendConfig";
}


// ===================================================================

class AudioNetworkAdaptation::_Internal {
 public:
  using HasBits = decltype(std::declval<AudioNetworkAdaptation>()._has_bits_);
  static void set_has_bitrate_bps(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_frame_length_ms(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_uplink_packet_loss_fraction(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_enable_fec(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_enable_dtx(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_num_channels(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
};

AudioNetworkAdaptation::AudioNetworkAdaptation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.AudioNetworkAdaptation)
}
AudioNetworkAdaptation::AudioNetworkAdaptation(const AudioNetworkAdaptation& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ::memcpy(&bitrate_bps_, &from.bitrate_bps_,
    static_cast<size_t>(reinterpret_cast<char*>(&num_channels_) -
    reinterpret_cast<char*>(&bitrate_bps_)) + sizeof(num_channels_));
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.AudioNetworkAdaptation)
}

inline void AudioNetworkAdaptation::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bitrate_bps_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&num_channels_) -
    reinterpret_cast<char*>(&bitrate_bps_)) + sizeof(num_channels_));
}

AudioNetworkAdaptation::~AudioNetworkAdaptation() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.AudioNetworkAdaptation)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void AudioNetworkAdaptation::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void AudioNetworkAdaptation::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AudioNetworkAdaptation::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.AudioNetworkAdaptation)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    ::memset(&bitrate_bps_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&num_channels_) -
        reinterpret_cast<char*>(&bitrate_bps_)) + sizeof(num_channels_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* AudioNetworkAdaptation::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional int32 bitrate_bps = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_bitrate_bps(&has_bits);
          bitrate_bps_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 frame_length_ms = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_frame_length_ms(&has_bits);
          frame_length_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional float uplink_packet_loss_fraction = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _Internal::set_has_uplink_packet_loss_fraction(&has_bits);
          uplink_packet_loss_fraction_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // optional bool enable_fec = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _Internal::set_has_enable_fec(&has_bits);
          enable_fec_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool enable_dtx = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _Internal::set_has_enable_dtx(&has_bits);
          enable_dtx_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional uint32 num_channels = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _Internal::set_has_num_channels(&has_bits);
          num_channels_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AudioNetworkAdaptation::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.AudioNetworkAdaptation)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 bitrate_bps = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_bitrate_bps(), target);
  }

  // optional int32 frame_length_ms = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_frame_length_ms(), target);
  }

  // optional float uplink_packet_loss_fraction = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_uplink_packet_loss_fraction(), target);
  }

  // optional bool enable_fec = 4;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(4, this->_internal_enable_fec(), target);
  }

  // optional bool enable_dtx = 5;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(5, this->_internal_enable_dtx(), target);
  }

  // optional uint32 num_channels = 6;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(6, this->_internal_num_channels(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.AudioNetworkAdaptation)
  return target;
}

size_t AudioNetworkAdaptation::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.AudioNetworkAdaptation)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    // optional int32 bitrate_bps = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_bitrate_bps());
    }

    // optional int32 frame_length_ms = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_frame_length_ms());
    }

    // optional float uplink_packet_loss_fraction = 3;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 + 4;
    }

    // optional bool enable_fec = 4;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 + 1;
    }

    // optional bool enable_dtx = 5;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 + 1;
    }

    // optional uint32 num_channels = 6;
    if (cached_has_bits & 0x00000020u) {
      total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_num_channels());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AudioNetworkAdaptation::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const AudioNetworkAdaptation*>(
      &from));
}

void AudioNetworkAdaptation::MergeFrom(const AudioNetworkAdaptation& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.AudioNetworkAdaptation)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    if (cached_has_bits & 0x00000001u) {
      bitrate_bps_ = from.bitrate_bps_;
    }
    if (cached_has_bits & 0x00000002u) {
      frame_length_ms_ = from.frame_length_ms_;
    }
    if (cached_has_bits & 0x00000004u) {
      uplink_packet_loss_fraction_ = from.uplink_packet_loss_fraction_;
    }
    if (cached_has_bits & 0x00000008u) {
      enable_fec_ = from.enable_fec_;
    }
    if (cached_has_bits & 0x00000010u) {
      enable_dtx_ = from.enable_dtx_;
    }
    if (cached_has_bits & 0x00000020u) {
      num_channels_ = from.num_channels_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void AudioNetworkAdaptation::CopyFrom(const AudioNetworkAdaptation& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.AudioNetworkAdaptation)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AudioNetworkAdaptation::IsInitialized() const {
  return true;
}

void AudioNetworkAdaptation::InternalSwap(AudioNetworkAdaptation* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(AudioNetworkAdaptation, num_channels_)
      + sizeof(AudioNetworkAdaptation::num_channels_)
      - PROTOBUF_FIELD_OFFSET(AudioNetworkAdaptation, bitrate_bps_)>(
          reinterpret_cast<char*>(&bitrate_bps_),
          reinterpret_cast<char*>(&other->bitrate_bps_));
}

std::string AudioNetworkAdaptation::GetTypeName() const {
  return "webrtc.rtclog.AudioNetworkAdaptation";
}


// ===================================================================

class BweProbeCluster::_Internal {
 public:
  using HasBits = decltype(std::declval<BweProbeCluster>()._has_bits_);
  static void set_has_id(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_bitrate_bps(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_min_packets(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_min_bytes(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
};

BweProbeCluster::BweProbeCluster(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.BweProbeCluster)
}
BweProbeCluster::BweProbeCluster(const BweProbeCluster& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&min_bytes_) -
    reinterpret_cast<char*>(&id_)) + sizeof(min_bytes_));
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.BweProbeCluster)
}

inline void BweProbeCluster::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&min_bytes_) -
    reinterpret_cast<char*>(&id_)) + sizeof(min_bytes_));
}

BweProbeCluster::~BweProbeCluster() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.BweProbeCluster)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void BweProbeCluster::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void BweProbeCluster::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BweProbeCluster::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.BweProbeCluster)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    ::memset(&id_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&min_bytes_) -
        reinterpret_cast<char*>(&id_)) + sizeof(min_bytes_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* BweProbeCluster::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_id(&has_bits);
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 bitrate_bps = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_bitrate_bps(&has_bits);
          bitrate_bps_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional uint32 min_packets = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _Internal::set_has_min_packets(&has_bits);
          min_packets_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional uint32 min_bytes = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _Internal::set_has_min_bytes(&has_bits);
          min_bytes_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* BweProbeCluster::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.BweProbeCluster)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 id = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // optional int32 bitrate_bps = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_bitrate_bps(), target);
  }

  // optional uint32 min_packets = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(3, this->_internal_min_packets(), target);
  }

  // optional uint32 min_bytes = 4;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(4, this->_internal_min_bytes(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.BweProbeCluster)
  return target;
}

size_t BweProbeCluster::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.BweProbeCluster)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    // optional int32 id = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_id());
    }

    // optional int32 bitrate_bps = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_bitrate_bps());
    }

    // optional uint32 min_packets = 3;
    if (cached_has_bits & 0x00000004u) {
      total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_min_packets());
    }

    // optional uint32 min_bytes = 4;
    if (cached_has_bits & 0x00000008u) {
      total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_min_bytes());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void BweProbeCluster::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const BweProbeCluster*>(
      &from));
}

void BweProbeCluster::MergeFrom(const BweProbeCluster& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.BweProbeCluster)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000000fu) {
    if (cached_has_bits & 0x00000001u) {
      id_ = from.id_;
    }
    if (cached_has_bits & 0x00000002u) {
      bitrate_bps_ = from.bitrate_bps_;
    }
    if (cached_has_bits & 0x00000004u) {
      min_packets_ = from.min_packets_;
    }
    if (cached_has_bits & 0x00000008u) {
      min_bytes_ = from.min_bytes_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void BweProbeCluster::CopyFrom(const BweProbeCluster& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.BweProbeCluster)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BweProbeCluster::IsInitialized() const {
  return true;
}

void BweProbeCluster::InternalSwap(BweProbeCluster* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BweProbeCluster, min_bytes_)
      + sizeof(BweProbeCluster::min_bytes_)
      - PROTOBUF_FIELD_OFFSET(BweProbeCluster, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

std::string BweProbeCluster::GetTypeName() const {
  return "webrtc.rtclog.BweProbeCluster";
}


// ===================================================================

class BweProbeResult::_Internal {
 public:
  using HasBits = decltype(std::declval<BweProbeResult>()._has_bits_);
  static void set_has_id(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_result(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_bitrate_bps(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
};

BweProbeResult::BweProbeResult(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.BweProbeResult)
}
BweProbeResult::BweProbeResult(const BweProbeResult& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&bitrate_bps_) -
    reinterpret_cast<char*>(&id_)) + sizeof(bitrate_bps_));
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.BweProbeResult)
}

inline void BweProbeResult::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&bitrate_bps_) -
    reinterpret_cast<char*>(&id_)) + sizeof(bitrate_bps_));
}

BweProbeResult::~BweProbeResult() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.BweProbeResult)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void BweProbeResult::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void BweProbeResult::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BweProbeResult::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.BweProbeResult)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    ::memset(&id_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&bitrate_bps_) -
        reinterpret_cast<char*>(&id_)) + sizeof(bitrate_bps_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* BweProbeResult::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_id(&has_bits);
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.rtclog.BweProbeResult.ResultType result = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::webrtc::rtclog::BweProbeResult_ResultType_IsValid(val))) {
            _internal_set_result(static_cast<::webrtc::rtclog::BweProbeResult_ResultType>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(2, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional int32 bitrate_bps = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _Internal::set_has_bitrate_bps(&has_bits);
          bitrate_bps_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* BweProbeResult::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.BweProbeResult)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 id = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // optional .webrtc.rtclog.BweProbeResult.ResultType result = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      2, this->_internal_result(), target);
  }

  // optional int32 bitrate_bps = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_bitrate_bps(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.BweProbeResult)
  return target;
}

size_t BweProbeResult::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.BweProbeResult)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    // optional int32 id = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_id());
    }

    // optional .webrtc.rtclog.BweProbeResult.ResultType result = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::_pbi::WireFormatLite::EnumSize(this->_internal_result());
    }

    // optional int32 bitrate_bps = 3;
    if (cached_has_bits & 0x00000004u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_bitrate_bps());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void BweProbeResult::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const BweProbeResult*>(
      &from));
}

void BweProbeResult::MergeFrom(const BweProbeResult& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.BweProbeResult)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000007u) {
    if (cached_has_bits & 0x00000001u) {
      id_ = from.id_;
    }
    if (cached_has_bits & 0x00000002u) {
      result_ = from.result_;
    }
    if (cached_has_bits & 0x00000004u) {
      bitrate_bps_ = from.bitrate_bps_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void BweProbeResult::CopyFrom(const BweProbeResult& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.BweProbeResult)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BweProbeResult::IsInitialized() const {
  return true;
}

void BweProbeResult::InternalSwap(BweProbeResult* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BweProbeResult, bitrate_bps_)
      + sizeof(BweProbeResult::bitrate_bps_)
      - PROTOBUF_FIELD_OFFSET(BweProbeResult, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

std::string BweProbeResult::GetTypeName() const {
  return "webrtc.rtclog.BweProbeResult";
}


// ===================================================================

class RemoteEstimate::_Internal {
 public:
  using HasBits = decltype(std::declval<RemoteEstimate>()._has_bits_);
  static void set_has_link_capacity_lower_kbps(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_link_capacity_upper_kbps(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

RemoteEstimate::RemoteEstimate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.RemoteEstimate)
}
RemoteEstimate::RemoteEstimate(const RemoteEstimate& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ::memcpy(&link_capacity_lower_kbps_, &from.link_capacity_lower_kbps_,
    static_cast<size_t>(reinterpret_cast<char*>(&link_capacity_upper_kbps_) -
    reinterpret_cast<char*>(&link_capacity_lower_kbps_)) + sizeof(link_capacity_upper_kbps_));
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.RemoteEstimate)
}

inline void RemoteEstimate::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&link_capacity_lower_kbps_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&link_capacity_upper_kbps_) -
    reinterpret_cast<char*>(&link_capacity_lower_kbps_)) + sizeof(link_capacity_upper_kbps_));
}

RemoteEstimate::~RemoteEstimate() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.RemoteEstimate)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RemoteEstimate::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RemoteEstimate::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RemoteEstimate::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.RemoteEstimate)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    ::memset(&link_capacity_lower_kbps_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&link_capacity_upper_kbps_) -
        reinterpret_cast<char*>(&link_capacity_lower_kbps_)) + sizeof(link_capacity_upper_kbps_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* RemoteEstimate::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional uint32 link_capacity_lower_kbps = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_link_capacity_lower_kbps(&has_bits);
          link_capacity_lower_kbps_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional uint32 link_capacity_upper_kbps = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_link_capacity_upper_kbps(&has_bits);
          link_capacity_upper_kbps_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RemoteEstimate::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.RemoteEstimate)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional uint32 link_capacity_lower_kbps = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(1, this->_internal_link_capacity_lower_kbps(), target);
  }

  // optional uint32 link_capacity_upper_kbps = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(2, this->_internal_link_capacity_upper_kbps(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.RemoteEstimate)
  return target;
}

size_t RemoteEstimate::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.RemoteEstimate)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional uint32 link_capacity_lower_kbps = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_link_capacity_lower_kbps());
    }

    // optional uint32 link_capacity_upper_kbps = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_link_capacity_upper_kbps());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RemoteEstimate::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const RemoteEstimate*>(
      &from));
}

void RemoteEstimate::MergeFrom(const RemoteEstimate& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.RemoteEstimate)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      link_capacity_lower_kbps_ = from.link_capacity_lower_kbps_;
    }
    if (cached_has_bits & 0x00000002u) {
      link_capacity_upper_kbps_ = from.link_capacity_upper_kbps_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void RemoteEstimate::CopyFrom(const RemoteEstimate& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.RemoteEstimate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RemoteEstimate::IsInitialized() const {
  return true;
}

void RemoteEstimate::InternalSwap(RemoteEstimate* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RemoteEstimate, link_capacity_upper_kbps_)
      + sizeof(RemoteEstimate::link_capacity_upper_kbps_)
      - PROTOBUF_FIELD_OFFSET(RemoteEstimate, link_capacity_lower_kbps_)>(
          reinterpret_cast<char*>(&link_capacity_lower_kbps_),
          reinterpret_cast<char*>(&other->link_capacity_lower_kbps_));
}

std::string RemoteEstimate::GetTypeName() const {
  return "webrtc.rtclog.RemoteEstimate";
}


// ===================================================================

class AlrState::_Internal {
 public:
  using HasBits = decltype(std::declval<AlrState>()._has_bits_);
  static void set_has_in_alr(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

AlrState::AlrState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.AlrState)
}
AlrState::AlrState(const AlrState& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  in_alr_ = from.in_alr_;
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.AlrState)
}

inline void AlrState::SharedCtor() {
in_alr_ = false;
}

AlrState::~AlrState() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.AlrState)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void AlrState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void AlrState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AlrState::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.AlrState)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  in_alr_ = false;
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* AlrState::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional bool in_alr = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_in_alr(&has_bits);
          in_alr_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AlrState::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.AlrState)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bool in_alr = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(1, this->_internal_in_alr(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.AlrState)
  return target;
}

size_t AlrState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.AlrState)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional bool in_alr = 1;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 + 1;
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void AlrState::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const AlrState*>(
      &from));
}

void AlrState::MergeFrom(const AlrState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.AlrState)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_in_alr()) {
    _internal_set_in_alr(from._internal_in_alr());
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void AlrState::CopyFrom(const AlrState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.AlrState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AlrState::IsInitialized() const {
  return true;
}

void AlrState::InternalSwap(AlrState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(in_alr_, other->in_alr_);
}

std::string AlrState::GetTypeName() const {
  return "webrtc.rtclog.AlrState";
}


// ===================================================================

class IceCandidatePairConfig::_Internal {
 public:
  using HasBits = decltype(std::declval<IceCandidatePairConfig>()._has_bits_);
  static void set_has_config_type(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_candidate_pair_id(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_local_candidate_type(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_local_relay_protocol(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_local_network_type(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_local_address_family(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_remote_candidate_type(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static void set_has_remote_address_family(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static void set_has_candidate_pair_protocol(HasBits* has_bits) {
    (*has_bits)[0] |= 256u;
  }
};

IceCandidatePairConfig::IceCandidatePairConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.IceCandidatePairConfig)
}
IceCandidatePairConfig::IceCandidatePairConfig(const IceCandidatePairConfig& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ::memcpy(&config_type_, &from.config_type_,
    static_cast<size_t>(reinterpret_cast<char*>(&candidate_pair_protocol_) -
    reinterpret_cast<char*>(&config_type_)) + sizeof(candidate_pair_protocol_));
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.IceCandidatePairConfig)
}

inline void IceCandidatePairConfig::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&config_type_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&candidate_pair_protocol_) -
    reinterpret_cast<char*>(&config_type_)) + sizeof(candidate_pair_protocol_));
}

IceCandidatePairConfig::~IceCandidatePairConfig() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.IceCandidatePairConfig)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void IceCandidatePairConfig::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void IceCandidatePairConfig::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void IceCandidatePairConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.IceCandidatePairConfig)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    ::memset(&config_type_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&remote_address_family_) -
        reinterpret_cast<char*>(&config_type_)) + sizeof(remote_address_family_));
  }
  candidate_pair_protocol_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* IceCandidatePairConfig::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional .webrtc.rtclog.IceCandidatePairConfig.IceCandidatePairConfigType config_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::webrtc::rtclog::IceCandidatePairConfig_IceCandidatePairConfigType_IsValid(val))) {
            _internal_set_config_type(static_cast<::webrtc::rtclog::IceCandidatePairConfig_IceCandidatePairConfigType>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional uint32 candidate_pair_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_candidate_pair_id(&has_bits);
          candidate_pair_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.rtclog.IceCandidatePairConfig.IceCandidateType local_candidate_type = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType_IsValid(val))) {
            _internal_set_local_candidate_type(static_cast<::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(3, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.rtclog.IceCandidatePairConfig.Protocol local_relay_protocol = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::webrtc::rtclog::IceCandidatePairConfig_Protocol_IsValid(val))) {
            _internal_set_local_relay_protocol(static_cast<::webrtc::rtclog::IceCandidatePairConfig_Protocol>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(4, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.rtclog.IceCandidatePairConfig.NetworkType local_network_type = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::webrtc::rtclog::IceCandidatePairConfig_NetworkType_IsValid(val))) {
            _internal_set_local_network_type(static_cast<::webrtc::rtclog::IceCandidatePairConfig_NetworkType>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(5, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.rtclog.IceCandidatePairConfig.AddressFamily local_address_family = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::webrtc::rtclog::IceCandidatePairConfig_AddressFamily_IsValid(val))) {
            _internal_set_local_address_family(static_cast<::webrtc::rtclog::IceCandidatePairConfig_AddressFamily>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(6, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.rtclog.IceCandidatePairConfig.IceCandidateType remote_candidate_type = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType_IsValid(val))) {
            _internal_set_remote_candidate_type(static_cast<::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(7, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.rtclog.IceCandidatePairConfig.AddressFamily remote_address_family = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::webrtc::rtclog::IceCandidatePairConfig_AddressFamily_IsValid(val))) {
            _internal_set_remote_address_family(static_cast<::webrtc::rtclog::IceCandidatePairConfig_AddressFamily>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(8, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.rtclog.IceCandidatePairConfig.Protocol candidate_pair_protocol = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::webrtc::rtclog::IceCandidatePairConfig_Protocol_IsValid(val))) {
            _internal_set_candidate_pair_protocol(static_cast<::webrtc::rtclog::IceCandidatePairConfig_Protocol>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(9, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* IceCandidatePairConfig::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.IceCandidatePairConfig)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .webrtc.rtclog.IceCandidatePairConfig.IceCandidatePairConfigType config_type = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      1, this->_internal_config_type(), target);
  }

  // optional uint32 candidate_pair_id = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(2, this->_internal_candidate_pair_id(), target);
  }

  // optional .webrtc.rtclog.IceCandidatePairConfig.IceCandidateType local_candidate_type = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      3, this->_internal_local_candidate_type(), target);
  }

  // optional .webrtc.rtclog.IceCandidatePairConfig.Protocol local_relay_protocol = 4;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      4, this->_internal_local_relay_protocol(), target);
  }

  // optional .webrtc.rtclog.IceCandidatePairConfig.NetworkType local_network_type = 5;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      5, this->_internal_local_network_type(), target);
  }

  // optional .webrtc.rtclog.IceCandidatePairConfig.AddressFamily local_address_family = 6;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      6, this->_internal_local_address_family(), target);
  }

  // optional .webrtc.rtclog.IceCandidatePairConfig.IceCandidateType remote_candidate_type = 7;
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      7, this->_internal_remote_candidate_type(), target);
  }

  // optional .webrtc.rtclog.IceCandidatePairConfig.AddressFamily remote_address_family = 8;
  if (cached_has_bits & 0x00000080u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      8, this->_internal_remote_address_family(), target);
  }

  // optional .webrtc.rtclog.IceCandidatePairConfig.Protocol candidate_pair_protocol = 9;
  if (cached_has_bits & 0x00000100u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      9, this->_internal_candidate_pair_protocol(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.IceCandidatePairConfig)
  return target;
}

size_t IceCandidatePairConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.IceCandidatePairConfig)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    // optional .webrtc.rtclog.IceCandidatePairConfig.IceCandidatePairConfigType config_type = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::_pbi::WireFormatLite::EnumSize(this->_internal_config_type());
    }

    // optional uint32 candidate_pair_id = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_candidate_pair_id());
    }

    // optional .webrtc.rtclog.IceCandidatePairConfig.IceCandidateType local_candidate_type = 3;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::_pbi::WireFormatLite::EnumSize(this->_internal_local_candidate_type());
    }

    // optional .webrtc.rtclog.IceCandidatePairConfig.Protocol local_relay_protocol = 4;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::_pbi::WireFormatLite::EnumSize(this->_internal_local_relay_protocol());
    }

    // optional .webrtc.rtclog.IceCandidatePairConfig.NetworkType local_network_type = 5;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::_pbi::WireFormatLite::EnumSize(this->_internal_local_network_type());
    }

    // optional .webrtc.rtclog.IceCandidatePairConfig.AddressFamily local_address_family = 6;
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 +
        ::_pbi::WireFormatLite::EnumSize(this->_internal_local_address_family());
    }

    // optional .webrtc.rtclog.IceCandidatePairConfig.IceCandidateType remote_candidate_type = 7;
    if (cached_has_bits & 0x00000040u) {
      total_size += 1 +
        ::_pbi::WireFormatLite::EnumSize(this->_internal_remote_candidate_type());
    }

    // optional .webrtc.rtclog.IceCandidatePairConfig.AddressFamily remote_address_family = 8;
    if (cached_has_bits & 0x00000080u) {
      total_size += 1 +
        ::_pbi::WireFormatLite::EnumSize(this->_internal_remote_address_family());
    }

  }
  // optional .webrtc.rtclog.IceCandidatePairConfig.Protocol candidate_pair_protocol = 9;
  if (cached_has_bits & 0x00000100u) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_candidate_pair_protocol());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void IceCandidatePairConfig::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const IceCandidatePairConfig*>(
      &from));
}

void IceCandidatePairConfig::MergeFrom(const IceCandidatePairConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.IceCandidatePairConfig)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      config_type_ = from.config_type_;
    }
    if (cached_has_bits & 0x00000002u) {
      candidate_pair_id_ = from.candidate_pair_id_;
    }
    if (cached_has_bits & 0x00000004u) {
      local_candidate_type_ = from.local_candidate_type_;
    }
    if (cached_has_bits & 0x00000008u) {
      local_relay_protocol_ = from.local_relay_protocol_;
    }
    if (cached_has_bits & 0x00000010u) {
      local_network_type_ = from.local_network_type_;
    }
    if (cached_has_bits & 0x00000020u) {
      local_address_family_ = from.local_address_family_;
    }
    if (cached_has_bits & 0x00000040u) {
      remote_candidate_type_ = from.remote_candidate_type_;
    }
    if (cached_has_bits & 0x00000080u) {
      remote_address_family_ = from.remote_address_family_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 0x00000100u) {
    _internal_set_candidate_pair_protocol(from._internal_candidate_pair_protocol());
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void IceCandidatePairConfig::CopyFrom(const IceCandidatePairConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.IceCandidatePairConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool IceCandidatePairConfig::IsInitialized() const {
  return true;
}

void IceCandidatePairConfig::InternalSwap(IceCandidatePairConfig* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(IceCandidatePairConfig, candidate_pair_protocol_)
      + sizeof(IceCandidatePairConfig::candidate_pair_protocol_)
      - PROTOBUF_FIELD_OFFSET(IceCandidatePairConfig, config_type_)>(
          reinterpret_cast<char*>(&config_type_),
          reinterpret_cast<char*>(&other->config_type_));
}

std::string IceCandidatePairConfig::GetTypeName() const {
  return "webrtc.rtclog.IceCandidatePairConfig";
}


// ===================================================================

class IceCandidatePairEvent::_Internal {
 public:
  using HasBits = decltype(std::declval<IceCandidatePairEvent>()._has_bits_);
  static void set_has_event_type(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_candidate_pair_id(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

IceCandidatePairEvent::IceCandidatePairEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.rtclog.IceCandidatePairEvent)
}
IceCandidatePairEvent::IceCandidatePairEvent(const IceCandidatePairEvent& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ::memcpy(&event_type_, &from.event_type_,
    static_cast<size_t>(reinterpret_cast<char*>(&candidate_pair_id_) -
    reinterpret_cast<char*>(&event_type_)) + sizeof(candidate_pair_id_));
  // @@protoc_insertion_point(copy_constructor:webrtc.rtclog.IceCandidatePairEvent)
}

inline void IceCandidatePairEvent::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&event_type_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&candidate_pair_id_) -
    reinterpret_cast<char*>(&event_type_)) + sizeof(candidate_pair_id_));
}

IceCandidatePairEvent::~IceCandidatePairEvent() {
  // @@protoc_insertion_point(destructor:webrtc.rtclog.IceCandidatePairEvent)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void IceCandidatePairEvent::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void IceCandidatePairEvent::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void IceCandidatePairEvent::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.rtclog.IceCandidatePairEvent)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    ::memset(&event_type_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&candidate_pair_id_) -
        reinterpret_cast<char*>(&event_type_)) + sizeof(candidate_pair_id_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* IceCandidatePairEvent::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional .webrtc.rtclog.IceCandidatePairEvent.IceCandidatePairEventType event_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::webrtc::rtclog::IceCandidatePairEvent_IceCandidatePairEventType_IsValid(val))) {
            _internal_set_event_type(static_cast<::webrtc::rtclog::IceCandidatePairEvent_IceCandidatePairEventType>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional uint32 candidate_pair_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_candidate_pair_id(&has_bits);
          candidate_pair_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* IceCandidatePairEvent::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.rtclog.IceCandidatePairEvent)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional .webrtc.rtclog.IceCandidatePairEvent.IceCandidatePairEventType event_type = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      1, this->_internal_event_type(), target);
  }

  // optional uint32 candidate_pair_id = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(2, this->_internal_candidate_pair_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.rtclog.IceCandidatePairEvent)
  return target;
}

size_t IceCandidatePairEvent::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.rtclog.IceCandidatePairEvent)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional .webrtc.rtclog.IceCandidatePairEvent.IceCandidatePairEventType event_type = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::_pbi::WireFormatLite::EnumSize(this->_internal_event_type());
    }

    // optional uint32 candidate_pair_id = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_candidate_pair_id());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void IceCandidatePairEvent::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const IceCandidatePairEvent*>(
      &from));
}

void IceCandidatePairEvent::MergeFrom(const IceCandidatePairEvent& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.rtclog.IceCandidatePairEvent)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      event_type_ = from.event_type_;
    }
    if (cached_has_bits & 0x00000002u) {
      candidate_pair_id_ = from.candidate_pair_id_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void IceCandidatePairEvent::CopyFrom(const IceCandidatePairEvent& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.rtclog.IceCandidatePairEvent)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool IceCandidatePairEvent::IsInitialized() const {
  return true;
}

void IceCandidatePairEvent::InternalSwap(IceCandidatePairEvent* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(IceCandidatePairEvent, candidate_pair_id_)
      + sizeof(IceCandidatePairEvent::candidate_pair_id_)
      - PROTOBUF_FIELD_OFFSET(IceCandidatePairEvent, event_type_)>(
          reinterpret_cast<char*>(&event_type_),
          reinterpret_cast<char*>(&other->event_type_));
}

std::string IceCandidatePairEvent::GetTypeName() const {
  return "webrtc.rtclog.IceCandidatePairEvent";
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace rtclog
}  // namespace webrtc
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::EventStream*
Arena::CreateMaybeMessage< ::webrtc::rtclog::EventStream >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::EventStream >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::Event*
Arena::CreateMaybeMessage< ::webrtc::rtclog::Event >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::Event >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::RtpPacket*
Arena::CreateMaybeMessage< ::webrtc::rtclog::RtpPacket >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::RtpPacket >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::RtcpPacket*
Arena::CreateMaybeMessage< ::webrtc::rtclog::RtcpPacket >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::RtcpPacket >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::AudioPlayoutEvent*
Arena::CreateMaybeMessage< ::webrtc::rtclog::AudioPlayoutEvent >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::AudioPlayoutEvent >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::LossBasedBweUpdate*
Arena::CreateMaybeMessage< ::webrtc::rtclog::LossBasedBweUpdate >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::LossBasedBweUpdate >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::DelayBasedBweUpdate*
Arena::CreateMaybeMessage< ::webrtc::rtclog::DelayBasedBweUpdate >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::DelayBasedBweUpdate >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::VideoReceiveConfig*
Arena::CreateMaybeMessage< ::webrtc::rtclog::VideoReceiveConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::VideoReceiveConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::DecoderConfig*
Arena::CreateMaybeMessage< ::webrtc::rtclog::DecoderConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::DecoderConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::RtpHeaderExtension*
Arena::CreateMaybeMessage< ::webrtc::rtclog::RtpHeaderExtension >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::RtpHeaderExtension >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::RtxConfig*
Arena::CreateMaybeMessage< ::webrtc::rtclog::RtxConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::RtxConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::RtxMap*
Arena::CreateMaybeMessage< ::webrtc::rtclog::RtxMap >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::RtxMap >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::VideoSendConfig*
Arena::CreateMaybeMessage< ::webrtc::rtclog::VideoSendConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::VideoSendConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::EncoderConfig*
Arena::CreateMaybeMessage< ::webrtc::rtclog::EncoderConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::EncoderConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::AudioReceiveConfig*
Arena::CreateMaybeMessage< ::webrtc::rtclog::AudioReceiveConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::AudioReceiveConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::AudioSendConfig*
Arena::CreateMaybeMessage< ::webrtc::rtclog::AudioSendConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::AudioSendConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::AudioNetworkAdaptation*
Arena::CreateMaybeMessage< ::webrtc::rtclog::AudioNetworkAdaptation >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::AudioNetworkAdaptation >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::BweProbeCluster*
Arena::CreateMaybeMessage< ::webrtc::rtclog::BweProbeCluster >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::BweProbeCluster >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::BweProbeResult*
Arena::CreateMaybeMessage< ::webrtc::rtclog::BweProbeResult >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::BweProbeResult >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::RemoteEstimate*
Arena::CreateMaybeMessage< ::webrtc::rtclog::RemoteEstimate >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::RemoteEstimate >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::AlrState*
Arena::CreateMaybeMessage< ::webrtc::rtclog::AlrState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::AlrState >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::IceCandidatePairConfig*
Arena::CreateMaybeMessage< ::webrtc::rtclog::IceCandidatePairConfig >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::IceCandidatePairConfig >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::rtclog::IceCandidatePairEvent*
Arena::CreateMaybeMessage< ::webrtc::rtclog::IceCandidatePairEvent >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::rtclog::IceCandidatePairEvent >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
