// Copyright 1995-2016 The OpenSSL Project Authors. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <openssl/bio.h>

#include <errno.h>

#include "internal.h"


int bio_errno_should_retry(int return_value) {
  if (return_value != -1) {
    return 0;
  }

  return
#ifdef EWOULDBLOCK
      errno == EWOULDBLOCK ||
#endif
#ifdef ENOTCONN
      errno == ENOTCONN ||
#endif
#ifdef EINTR
      errno == EINTR ||
#endif
#ifdef EAGAIN
      errno == EAGAIN ||
#endif
#ifdef EPROTO
      errno == EPROTO ||
#endif
#ifdef EINPROGRESS
      errno == EINPROGRESS ||
#endif
#ifdef EALREADY
      errno == EALREADY ||
#endif
      0;
}
