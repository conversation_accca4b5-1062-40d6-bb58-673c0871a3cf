{"algorithm": "ECDH", "generatorVersion": "0.8r12", "numberOfTests": 427, "header": ["Test vectors of type EcdhTest are intended for", "testing an ECDH implementations using X509 encoded", "public keys and integers for private keys.", "Test vectors of this format are useful for testing", "Java providers."], "notes": {"AddSubChain": "The private key has a special value. Implementations using addition subtraction chains for the point multiplication may get the point at infinity as an intermediate result. See CVE_2017_10176", "CompressedPoint": "The point in the public key is compressed. Not every library supports points in compressed format.", "InvalidAsn": "The public key in this test uses an invalid ASN encoding. Some cases where the ASN parser is not strictly checking the ASN format are benign as long as the ECDH computation still returns the correct shared value.", "InvalidPublic": "The public key has been modified and is invalid. An implementation should always check whether the public key is valid and on the same curve as the private key. The test vector includes the shared secret computed with the original public key if the public point is on the curve of the private key. Generating a shared secret other than the one with the original key likely indicates that the bug is exploitable.", "ModifiedPrime": "The modulus of the public key has been modified. The public point of the public key has been chosen so that it is both a point on both the curve of the modified public key and the private key.", "UnnamedCurve": "The public key does not use a named curve. RFC 3279 allows to encode such curves by explicitly encoding, the parameters of the curve equation, modulus, generator, order and cofactor. However, many crypto libraries only support named curves. Modifying some of the EC parameters and encoding the corresponding public key as an unnamed curve is a potential attack vector.", "UnusedParam": "A parameter that is typically not used for ECDH has been modified. Sometimes libraries ignore small differences between public and private key. For example, a library might ignore an incorrect cofactor in the public key. We consider ignoring such changes as acceptable as long as these differences do not change the outcome of the ECDH computation, i.e. as long as the computation is done on the curve from the private key.", "WeakPublicKey": "The vector contains a weak public key. The curve is not a named curve, the public key point has order 3 and has been chosen to be on the same curve as the private key. This test vector is used to check ECC implementations for missing steps in the verification of the public key.", "WrongOrder": "The order of the public key has been modified. If this order is used in a cryptographic primitive instead of the correct order then private keys may leak. E.g. ECDHC in BC 1.52 suffered from this."}, "schema": "ecdh_test_schema.json", "testGroups": [{"curve": "brainpoolP320r1", "encoding": "asn", "type": "EcdhTest", "tests": [{"tcId": 1, "comment": "normal case", "public": "306a301406072a8648ce3d020106092b240303020801010903520004b7fc5720d4b325c0805cc213c1b9ccbd9e4f380a121fbc00784c821bcd7f0d618b70d39fbdceae553d9de4f90683eb9f000463c7bd1d32db8cfad2b40862ccac05030e93fc6694c071e8e1427b305006", "private": "12d06c51deda1595d8c43e7c146f898f7141c0836522931049afe61333ae2c03e350b61aeba540bf", "shared": "94982d50b29faedaa7ed0988fd57b90303b032ce398ec70af6d194bcfd7f9008d14e09425502b7e7", "result": "valid", "flags": []}, {"tcId": 2, "comment": "compressed public key", "public": "3042301406072a8648ce3d020106092b2403030208010109032a0002b7fc5720d4b325c0805cc213c1b9ccbd9e4f380a121fbc00784c821bcd7f0d618b70d39fbdceae55", "private": "12d06c51deda1595d8c43e7c146f898f7141c0836522931049afe61333ae2c03e350b61aeba540bf", "shared": "94982d50b29faedaa7ed0988fd57b90303b032ce398ec70af6d194bcfd7f9008d14e09425502b7e7", "result": "acceptable", "flags": ["CompressedPoint"]}, {"tcId": 3, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b240303020801010903520004430dd30b26350bc66c41c71769dd61b740d71008b59e455c1795ebbdc6bb1f0c8afaab415850142f1557feb7bef5fd3937f088ccb4d3ff8dc7431eecc9dce6f57c45035ca97dd0d7de9991742a39ad20", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "00000000000000000000000000000000000000000000000000000000000000000000000000000001", "result": "valid", "flags": []}, {"tcId": 4, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b2403030208010109035200047bad53f50a369371b9742952b37f6a179ffb0f7f46894321f1b3485453b7bb58b9e61e1ad06913247287976110d22bbaf17dad584cd104bca6601daa0b6633ba889793f931ef61f889a491bfc3776256", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "00000000000000000000000000000000000000000000000000000000000000000000000000000002", "result": "valid", "flags": []}, {"tcId": 5, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b24030302080101090352000439a0050ad1000627ffeb7bd1c4fa9fc336507c4d1c2a55f4e3672c15dabc8dc9567e326e740c54c799c1b05bcd6fc5b43c693569c0583a19e5a0aff1b4135df461e626e81fb098be2eefbf9ec1075a53", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "00000000000000000000000000000000000000000000000000000000000000000000000000000003", "result": "valid", "flags": []}, {"tcId": 6, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b240303020801010903520004887c70eaddf1e67b2e05e0613154b61cac592568641c5332a218a87cebba63b2518e6c1cc1ca4036a4a62bbe99f7cab95eb1fd6b0acc85d495c9b82ed1ce047db89ab3c08f953c13ab35128638921b09", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "0000000000ffffffffffffff00000000000000ffffffffffffff00000000000000ffffffffffffff", "result": "valid", "flags": []}, {"tcId": 7, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b24030302080101090352000404d114977ae64cbca724fc517d6b7bafaf7c9eec36d682c1fd02e9f9bca302b149793582b301c8a95acab775a1f9c63d1eab8f1e67421c7d8de16f0128167a2f7ef8ae55fda1b83e97503b8323343a92", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "00000000ffffffff00000000ffffffff00000000ffffffff00000000ffffffff0000000100000006", "result": "valid", "flags": []}, {"tcId": 8, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b2403030208010109035200042e78935c77d5fc195928849d98b81c79c8dc407bf113bd1e3f49967d288d7bf8c1cd853a9a5fd80788e3e8ebdf07f70c44075b8ad217123e2ef44ae53f4306d0f1958f1311d0509e1c7754a68afdf7eb", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff", "result": "valid", "flags": []}, {"tcId": 9, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b2403030208010109035200048f93a6519729320805291a17925e6d42ba23dd25acb3eea29c18f1e550c7dfb41182a9fd75747df77191f150afa979031f546222bbdc1afc448c6613d219a109a018ad341b6bb5fff61939a242b42968", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "07fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff", "result": "valid", "flags": []}, {"tcId": 10, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b24030302080101090352000473a8101f2569337b61237b94105f02af62af56fe0364a8b06cfd0c65ad26898034828dbda03b90e7394b1a51f0c32effa86c58c28c5ae2efce2a147eb69580519f31a57a697d9c069b97ef9c02f4bbf4", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "2e5544fd7d843f5df87bad4422ee2f10139748eb004974c3858f8a7a689df792ae0af08dc43414d9", "result": "valid", "flags": []}, {"tcId": 11, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b240303020801010903520004a83df8e6d9326d0f6e399db9094f81ab8af846091f5d50f220d2dc94a268c6a8d11ade13e2484b253a77027983c3d8b2d0a4cb9ee9008cbd8d34c50fc65a5aad5c21f332788f719897c44447e0f11370", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "2e62b007e0e833a4fe3a6b527a4fe88f6bbd0ce20e6a374fb6474d56956b6a0f807f68652c8b0ded", "result": "valid", "flags": []}, {"tcId": 12, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b240303020801010903520004bef54b0fe97b66cbfc62731645fe3c6fc1d582dfc18fe419fefb8cbdb0c5fa17a363f352ed7b6bfa37e24f93ec8da50062937823baab3c138cf1335e07bb50c8a05ed6a9bb9f82631c95f500aa9f3645", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "5caa89fafb087ebbf0f75a8845dc5e20272e91d60092e9870b1f14f4d13bef255c15e11b886829b2", "result": "valid", "flags": []}, {"tcId": 13, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b240303020801010903520004ab49dd4a73e9f8e0595bd8ca1655e2bca61fa2e1a3cc87635ae7a61fdb7c9bd5a8e4e34aa2b928c930732ccb23d3f512aeda1496a84e7e75303b0ee662d61107da8c67fe7193de7c29251423501df209", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "6166f3de8da8f6e286176276a03ac6dbb4a9f8b0154a1504caa5ba2bb12dde1be6eae89f3384123b", "result": "valid", "flags": []}, {"tcId": 14, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b2403030208010109035200048f5659116b4af2d03dd2b3e72c0d78d77677fefb4e277bee2a4966bba8354f85d83845b33a431a6907921555c1ac471e51e26a0cc67fb4146da0502fcee38f5ef502fcd4f9a380b086af5c403396c4f7", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "6946ec9bdddac30dc5060f5bdb41d2140d57066758e3b73f0c50b5590ac45e9c3a483016c97d409d", "result": "valid", "flags": []}, {"tcId": 15, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b240303020801010903520004243bd450e757a6a5308cb366f87c359b953cb42c6c61de923a24247217974d6ccb3f8869e832227c64035c2066f24423fee0041ab5ef361d1ca4bd19f34f9802f1fbcd2cc132087d8f4f3d5b32e1b046", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "7f0000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff", "result": "valid", "flags": []}, {"tcId": 16, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b24030302080101090352000465677dff00f5899c5100c5ceaeef383de49012e005e8f73656caff3e95dd1254bbbffa840e206e962935d733930dbe0130714761a1a26bf9a59cd37db1311bae7ab2f9ae3f0827aa0ae240b34cefb97c", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "7fc00000007fffffff00000001fffffffc00000007fffffff00000001fffffffc000000080000001", "result": "valid", "flags": []}, {"tcId": 17, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b2403030208010109035200046394defc20111cd8cddc2b8545b9200f080736bf6438f21c6fe0bc246e4bb0bdce1f1107d6dc55b536d025461efef8ec5dfa7f088fc03070efe56158050ac3042d1285aa9ea40215dfe5542e6d5590f4", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "7ffc000001ffffff8000003ffffff0000007fffffe000000ffffffc000001ffffff8000004000000", "result": "valid", "flags": []}, {"tcId": 18, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b2403030208010109035200040c1142edacae66ffa6993bca134cd4450b5adf0d5f5c32e21479f2419a9000afe9226749f27bcad113bee319eac4e6c6927deef999d25017890e95a615bd222720ecbb22ebe2ed9aafb9610bfa7e77f9", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "7ffff00000003fffffff00000003fffffff00000003fffffff00000003fffffff000000040000001", "result": "valid", "flags": []}, {"tcId": 19, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b240303020801010903520004770298807fbfc4f084210a361403c6dd3c3f2c31bbf20ec851e10144fc8340ae7a77cead742b5d1dc4a9a855df56a21362614044ae945739c707c0c89b65a43fb091069bd4f695b553c583f538206c35", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "7fffffffffffffff00000000000000000000000000000000ffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 20, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b240303020801010903520004a719ed2e9ff2debac0713bc4806181736885736f183be24175ced514ebb76a0e49b20f7a47bec04477405570758bb4a1835f71338d394ac799d389a9a2b0aab561717a0642c668dd7e88521fc0884db9", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "7fffffffffffffff0000000000000000ffffffffffffffff00000000000000010000000000000000", "result": "valid", "flags": []}, {"tcId": 21, "comment": "edge case for shared secret", "public": "306a301406072a8648ce3d020106092b24030302080101090352000423c02591b8ee778c5780ed4c783974ddd5aa52117d68cdda1c375cb77dc36f5fbea9709e44612898c1f5fcb67bd6b2bcc6ff0b01003be7559c1cd0827847de5943d5adde9b8519a55675970051659a03", "private": "5a21b0e16c6eb888381f19009dae7a91274e8f18e16674ee6c2bf782e24c2f0cf994daa6fe60ed10", "shared": "7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 22, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200040000000000000000000000000000000000000000000000000000000000000000000000000000000129110253d52cf3c5fc3382fca93d18adf7b97999028767b9722381db68fe3a41793b7d9952c6177f", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "2bab467efa3adf782324563a4d8f19018ca44458f697318a11f3e3c2269275c7b1aec44ce83cce8a", "result": "valid", "flags": []}, {"tcId": 23, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b240303020801010903520004000000000000000000000000000000000000000000000000000000000000000000000000000000020d1a18c0b25d0d32d9c4249a523cfcc12a20c2ead596607d73260895676315a70ad098e8b51d25a8", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "829a5f7c664dce621ba1373c0758d3d2b2984d85c384cf0077930ae4909f6072731572ecc3e4a1aa", "result": "valid", "flags": []}, {"tcId": 24, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b24030302080101090352000400000000000000000000000000000000000000000000000000000000000000000000000000000003638b53e2a2f41dc6d6dc7b3611a6dde54f7e8a7bb2681afa2060100074e51289d13d79251d168da2", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "8131f95388bd0c74d3fedc3077d750382e90f765c820e608292fe923c4f3c76ac77d6c2c05862c7a", "result": "valid", "flags": []}, {"tcId": 25, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200040000000000ffffffffffffff00000000000000ffffffffffffff00000000000000ffffffffffffff13b6599cb2e990a4fee490dff267362978a0e76f57c53b4ee85fc39c790f4c6969978fc462e5a847", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "203ab48d6e3a11afcb44140f0b6596cc85dfe39e589d64b699ddb5470ee2ac5cf61d6ec8534aacfe", "result": "valid", "flags": []}, {"tcId": 26, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b24030302080101090352000400000000ffffffff00000000ffffffff00000000ffffffff00000000ffffffff00000001000000063a91ee30c63eb15b1c0f2102c6cf3438dd75ca71636238f891e367c105f0b781d02de648399712a0", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "0f5a4a6607bfd93aa3d1ed5a4aa5ed3d973547cc978265649cc47dae0cd32bfeeef058e4e2ed4288", "result": "valid", "flags": []}, {"tcId": 27, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200040000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff308f36ab8f37e97723b0aadd7ee4dd585b9e68dc00db4242f6c3cf7b0ec1497a26e629b24a613b3a", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "a6b0761c5fe60e1e4c63704ecb8a0da1a9b89cd502b2091eb37a414181af99c4b421f107865a3989", "result": "valid", "flags": []}, {"tcId": 28, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b24030302080101090352000407fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0007fff231f9aef9b1a7c143485f601980bfa4f7bc7b312b01400bd1d15669197e07f2edf39cd08c905e280", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "8ccfba752052d15c8cca181abd9fded4cb0c176dbadccafe44cf1cc1907fc9ba356c7e7c3289e19e", "result": "valid", "flags": []}, {"tcId": 29, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200042e5544fd7d843f5df87bad4422ee2f10139748eb004974c3858f8a7a689df792ae0af08dc43414d92c64eca76e2196d01899acac1824effa5a9b4b3167b2131477386006fc3a0f624d9684c900df7b78", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "5e7e6ba602aa2208b1607f879635d6e3c28568a7f754dae448cfc793a80600809ac13381bc800a8d", "result": "valid", "flags": []}, {"tcId": 30, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200042e62b007e0e833a4fe3a6b527a4fe88f6bbd0ce20e6a374fb6474d56956b6a0f807f68652c8b0ded5b0554bd701c2b9e6687e1b0135dff715d53dae858fa77df8e7e566ed638f9726ade9ea2db601079", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "9f6059cbada97ab9f0826b6a1bedfe79b9c75da51fc2fe2620436bff79b15ac416b9ed1a02513640", "result": "valid", "flags": []}, {"tcId": 31, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200045caa89fafb087ebbf0f75a8845dc5e20272e91d60092e9870b1f14f4d13bef255c15e11b886829b213eff0c83008731468789c7865e5c17d5de2da065c8f039a7475cd7588e03e9a68ca0841f6f8429f", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "1a05058f316d94efa0829e12c65c32a01202b24a9cf97b9f64990a39f643456dfb5a89649a8f6832", "result": "valid", "flags": []}, {"tcId": 32, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200046166f3de8da8f6e286176276a03ac6dbb4a9f8b0154a1504caa5ba2bb12dde1be6eae89f3384123b0e75f218e122dac1d46f0c09e49b92c167798a6b458e6660c8c10fb3858d89a5f546a301406d456f", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "1f9ae24d0082d8a103842086fae91da616c1b0d26007db2f755a8fb0dd1720205895a1ead88b2a30", "result": "valid", "flags": []}, {"tcId": 33, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200046946ec9bdddac30dc5060f5bdb41d2140d57066758e3b73f0c50b5590ac45e9c3a483016c97d409d0ece7c2cf1c5a46c15c6ff814b1252593cb532621fe14c7f557762911a166e46b9b7a3d4b9aa9f7b", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "01980373c0a3a00be8b30b1afb8123b032a7054b16b51016be94f5d9f5dd8a5a5f19e0807f6c27cd", "result": "valid", "flags": []}, {"tcId": 34, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200047f0000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff0000003ffffff025368d44d7706408e18118e8338b5a8f65dcb4f7ac1884d97a44d235b8c89e5c240576680e6def6", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "8c14fa61be79ee096dcf6a2cd31aaab422b98161476c02e71a18168eaf3185ac515f6e451af5e27c", "result": "valid", "flags": []}, {"tcId": 35, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200047fc00000007fffffff00000001fffffffc00000007fffffff00000001fffffffc0000000800000011a2b8d3c67305de21501cd7c43ad4cd9a57459c42e6fdac1e2cb37952703ffdccd18fcb326a2e0c7", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "61482d667be2a2d7bfcbd75b9e8140ea37813ede1fd44eb046f436a4d672784643d0d84b1c82f55f", "result": "valid", "flags": []}, {"tcId": 36, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200047ffc000001ffffff8000003ffffff0000007fffffe000000ffffffc000001ffffff800000400000025a0dc389bcdc85d8434b2c615d1092b546d7808978f2d0cd3b3f17f57a2774c883fc5fad79ed32d", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "c586152d7416ca85eb55486ec2914a78a3d99a240d7d2657d54a2c8c1540b349380a151c9669a107", "result": "valid", "flags": []}, {"tcId": 37, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200047ffff00000003fffffff00000003fffffff00000003fffffff00000003fffffff000000040000001030432044ddf1b1586c51deec0306d02d88e54bc2a2dc6c7e65895891633f866addb9de1ad32a8bc", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "c11b2dca6672802c8528af2c84b1a04e59c9a22bb271bd6d7568092babd2f23efc063ab248a2717d", "result": "valid", "flags": []}, {"tcId": 38, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200047fffffffffffffff00000000000000000000000000000000ffffffffffffffffffffffffffffffff65f89731c0263d13aaa8ca7b3dadea10c46d7243883d7cab726d463b254df6ef728b52d8bbfb06ee", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "aa2e1ba6b508026b967ab355b99318bc394fc0d3cfabbb246804ddb5f913e6ef6bc0a228311bedcc", "result": "valid", "flags": []}, {"tcId": 39, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200047fffffffffffffff0000000000000000ffffffffffffffff00000000000000010000000000000000138c04477f42d165e4d474248d091ad74220de2021d0ca8f9f7295c7cc19c787f94ec33a2e4d51ee", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "5397c3d5663f5fcc4705eceba8255e89cea1747ad0d08d40b7a1aee01193b5df9593f8013c5dfb96", "result": "valid", "flags": []}, {"tcId": 40, "comment": "edge cases for ephemeral key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200047fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff04f2455cdb035b0cd4422a3ca06bb19bf018d1a5cb84eb12446d47f7f7a16c035c70951b4b6bad7b", "private": "00a0fd4aaf14d5c4f13a2b5afb677d44e2087856cd75bb7bd90c628e4761defe1485de4fbb97ae3aca", "shared": "20c790cf41723c9d5757b1eec1ff88ef16cbb632879cdce47fcb583262a7a6775c4486df301df5f5", "result": "valid", "flags": []}, {"tcId": 41, "comment": "edge case for Jacobian and projective coordinates", "public": "306a301406072a8648ce3d020106092b240303020801010903520004c3c0a0cb3e5a3e6ac794a0d7326cd98c565c565536a68d3578b369a99848d7487548c648c7ce43a762b9c83b6aadea1e64e3f69df761f20c617454a40a57b750b0f6604aed468363805e541cde79ca8a", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "ad83781e759ccc606da18ba87713f90e9120e66aa663c39394cd40f72567bef9b8e7a2475db7e116", "result": "valid", "flags": []}, {"tcId": 42, "comment": "edge case for Jacobian and projective coordinates", "public": "306a301406072a8648ce3d020106092b240303020801010903520004c4c6b508121df7c4ffb947fed30ba4d17cb78f556b2373cf48957432a8ead5284ae978f79e7a30c855af67cd8d37b054195e8ac6db76a587c28067d73f9ff951771f11d788c097ccfb184917e15c7874", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "75062491becce01b25f58085e0330fcd22ec1afc61df92c8584ebe9c84cd308a3619a9a00cd6f097", "result": "valid", "flags": []}, {"tcId": 43, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000417309937c50a7b080901dadfad0fef03e6ecd2532003a4651dab6c3c9e0b9d50d7b1feec3a342c425afcd8d979d9af01ac08d8ad86826ee095e647ac406f39f05bc34e6aca159b5fca94ba98829e505e", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "76e9c86c1c5f0aab629c26b6559d7644777a39d467c3cf0ded0eecb2c0e211ced104ffb994e019c8", "result": "valid", "flags": []}, {"tcId": 44, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000461696b1d263a267ee0cdf8ff896484cb8e4ee9991ec73161a965967fcc3bb1d4c8c16fe2e975d9638d45a239b309750fd2d84f5b55a49090fe7e760a66d0d085f18da32133c163b3629ea97debba4999", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "10caefa60cbe1d9f15e7219f90654c32c063227541f3d1ba0cde2c157e823d0ae40aa1f57bef6632", "result": "valid", "flags": []}, {"tcId": 45, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200043ace8748bf11b1439ee76a361469c80cc6d999726409d5597eea7984bf8bdada5d62973848f021f44a6479923539366b5a481caf094078e9e51c56e68b4912ae072c6531e5e1d0d9249b6bafc415a70c", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "0e1379e1f968719571c4d277f36c83c5fe4c6ceadfa9975dc77d7ac49d8a4f0cc3d4c4736de02564", "result": "valid", "flags": []}, {"tcId": 46, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200043367b79ca553efbea23f5447b2e287fe908362e2de5ae12f6351a5851c7abb2799c5e7811a3dfcda9d6f2928e92131030932d1d53dacd6065cc1fb74144215b140f1ca4f7e6397e744a9532932350daa", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "94f4251139acf4d8ea845e08f66c66024683a872fe3db323dba43c3a0a4dd2b479f3a3f52bdad146", "result": "valid", "flags": []}, {"tcId": 47, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200040ab3e041dce78e43a0d73122f05cce75fac56df829685fec71daa497a1f808c1a8ee893e560103b30f4be33b0a5dd319acc8e6e0a461b2305a312e76e2e8491d57e2c9d158640097ed62f00b325ff36b", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "22ab7ba4799d1d5d5716146751ae368ce787385382b62df4bac7777ced19b5839109bb89622ed698", "result": "valid", "flags": []}, {"tcId": 48, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000490ddd0f5580fe5f87e8f21c99447d54c41271655b971c4409380848ee7e331efa0639adfcd1e48c1ad3bee32cbdf0ad513f357b5490c39ba2c1701b590b44c9a84ade128fbb3c2015b48480739c035b2", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "9b895ad99ac4fe1a3eeb5337b035bb64f581c0f3ca271ccadddfd88a9d0892660bdb4f9ad1908b74", "result": "valid", "flags": []}, {"tcId": 49, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000404c0c943bbdfcdc12e8b4bce80d258cf5bcc9a0dd72a7e83e200f8d6376c100fd41dfb691e65f3834e74759c856998e7e55b8b57a3855bff6ca9ea636cb76aad77a9a0f4f7f9ddf04003c7c02f36f604", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "8b2188cba52410b3d1e1afcda1c1f718b2e82b904eb9c0d00653eb121dceaaf84296ca736ba44718", "result": "valid", "flags": []}, {"tcId": 50, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000423749d53f0a73119b7bcc084a1da7d2298907253282a4a94a82cd598734f8f2fab7b3270f0a9c5c26abeb46f96f90744a355acf982a224fdfcf56a98eaf0f6e1497fd93eab65037b59415b63ed610f16", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "42c211fa061565fe17a85e716f5eae222b960b60de85657fdc6e264884b8df7902a53e9026e419fc", "result": "valid", "flags": []}, {"tcId": 51, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200047d871df217eb2fb0733ebf222ee4764553f783eba58721225fed5fbb1bcc654235d290f62a61f02fb2b3c241e22192ab7b705c44e0ed33e9b58930114b4e4342ff865f2b39fde6c2f86fd2b4c1f30c8e", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "2152a857bf30e11894a8dcde942e0b4c4f22649cc67c624cc12df96002c02d0432f729c132117216", "result": "valid", "flags": []}, {"tcId": 52, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004ca34b09218d138b468dd6e7142296b61bd68d0815e905604cc0b5e842d4e9c486381a8bcae353eb898aa04f80ef9cc89ec4c123c0900bcf13beb655804bcb895f2d7009378d5978820250f7beb874ef1", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "553f622097c53e895a8b7ebebd3602d1c8390ea85b4240878f233e10ae9564c0f52d31f57f902a1b", "result": "valid", "flags": []}, {"tcId": 53, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000416e19b737730c34e15b38735afa33f867d3d2c71cdf455c70aa400034ac65d8ffa7f81e7987df3809304ec728d3ce6f361646f1857376aced7c3d7d6e81081becb0886d11baab50a4f392f5f4d93fe0f", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "5d583d61cdcf9517da03851869ce31403e651341b388c0826cd763b1b75213965336730f40206bca", "result": "valid", "flags": []}, {"tcId": 54, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004587cabfc69b1bc3e7d29e4ee25b645d357dee7f245fd5f8a9a08ba0a032560a23e3ea88ae340589bcda575f3c66f2cf74ad0ac25da2829a68101bc2695353bece6a33747833d01ea9963600b0f70befc", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "19b578afe0103596b169fa47984d3a6a10da053e564b467dc6fcca1fa83b6140ac07f47c93b4d473", "result": "valid", "flags": []}, {"tcId": 55, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000405e853797439172aa272a8f43ab9dab9924b51f67f587b89e303ef9131c13ee64aad1c0745bfb3cf6f1f04775d3c2554417e13f88362e3138b53a8f8bed6f9629c1378c92b5a73ffb20b2f6198c745f8", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "1400cc446af39e0977c5e2d45293fa995538633ab6bede31608fddf03462e034e2878e6a094fba9d", "result": "valid", "flags": []}, {"tcId": 56, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004d0ebc85cbecc65e564f18731b6b4ac1603ac519fa5716be3b65cf2bde07ca1f6de0c85329d9b7454c8b7e54c5da59a779c00a50dddb7a06c9e0e1b79dffd3b73925030888cac08616346081813cad49d", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "0edd79e8b9d67a0ba475bff46141fbbcb257ec77bd2dd14940b66ad4aa14fd7c9c1f8efcf68dd189", "result": "valid", "flags": []}, {"tcId": 57, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200046cfa92d0823ab62f32d32dd626c3ec999654ebb44717f8ac66ea30c71aa8393692bc4fa8e03472835c7dee562e4529209b47a3ce7b598acc0bb94683704f12d05fa262cd037cbf5d5c3015e823412138", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "5986742cb7ab5fa059c3ce98eed88895e2c8a54b4e8abb4d6c6117b52d6d4d1efb51f7165da61846", "result": "valid", "flags": []}, {"tcId": 58, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004530249b8b2b1c090df7d5465fd064a725cdcddc3a1bd23fa60d0a900808a30fc44bb4f8680662500baf7c8bbc5417a87fdde94fc399f0a9df34cad24c30e0f62c73021d027f64d460c9d25d63dc7b487", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "a4dc0fc37a2526f1e8acd129ba2e4b1c8e510038360507b8acb6bb832818ae75f04b0da2106bc14a", "result": "valid", "flags": []}, {"tcId": 59, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000441e88de6857d88601f52c96f6d14d9e57ef5604e2ef5cafcbd4c052823bca140b9eb5046d82483d29856af4100323df935634689e87f8f065407c5b97f8ff04bea2fd2d51edee9d7370ef51f9f3398ff", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "909267c8a37fa72fb4abbb960ebbab97a8bd8e0dd5d7218d2a2eeab281616e34b467a6c14f5bb1c4", "result": "valid", "flags": []}, {"tcId": 60, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200041240074427dba7cca8f9e39f02a21b091a990bf747f7ae62617e3595cc7cd313e83cce1f4726766096b0969cbd3316176a5b9f3e4e86f016640a62d849b1eee842e1c5c34cab790af9fb528ee7c0e7da", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "89269246e1af793819521dcd86e64fd1c658d3d70cb36874eac01b4de3086565daa8e9224cff7c3a", "result": "valid", "flags": []}, {"tcId": 61, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004adda307c9e3cb7973733c81828d14ce82eb61919e6b0a6a7d1d528676cda2b97b5bbd30218fc08eb84e08efdd28bb5887b3916279f3fbdd68e086b6213f94ab651a982f46db8a171209bc7ae0bad05e0", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "25a35ac09fd851c099727feda63eaccac12096c3a6965be3aed1642c1735283cb37075a8a90a7430", "result": "valid", "flags": []}, {"tcId": 62, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004596246460a651dcdfb754e9dc729c102a7dca0ea918cba8363152902eac266661211bc2dea394837ad2033ca247fcf870688527f0e6c785073475d6cb3a54d6e7ae90c38732e61e0b63543497801c21a", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "b67e4ec557a10801b9c7f2dc18f5b6c8cbc669918640c5e9ab13202f67d27f2c3fb1612d66528e71", "result": "valid", "flags": []}, {"tcId": 63, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000488eb56163b0612a2b4a6cf7d3ba46fd46d81e961ec27716852a19c6dac844c5cc3b754376c147f87027113079815c7a22406013c54a5cfb68be466f48332135bbeee592ffd649136322549d421671c93", "private": "6ec319890c097559246101be7cf09f4dd89b6b3e58c1799f58bb183118809c1ad733b6e8a6329a17", "shared": "80e19c5468786f33da3ca6b7c4c02a4b28fde857a407622ff51bde6f750714fdab01b3dc8d2f3245", "result": "valid", "flags": []}, {"tcId": 64, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004842bb99fff913e764f2dbb37708a3d6eb464318f862b9f07c05190e4a68874e9decfe0faeadf2281714b1f0de69e5f7a58c5daa3074f475377ba10843949b4e5e21419fdcd4c8c0d2955e9e84070ac51", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "157f71f1ea01efe08b61cbdefa2b84df5a3b241f428da099dade6d3f6b0995e0cfbfc68da301576a", "result": "valid", "flags": []}, {"tcId": 65, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200045dc733657a02174b19d3ac4f514d1b6a8cb60a08eca98289b25f344fd667b1a7d21e429a50b237882c8c16dd426cffa8fefee7e7b63e4b58cf2aa76f41503ab1b6431f599eb8162f01a679981d3b1f8c", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "bdaed3e4f7f20e5ba8170f738a8c2ab4a57384e13b43e753e452b67d40548d4d4304ca85fa474c2d", "result": "valid", "flags": []}, {"tcId": 66, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004b47bdd0c25e56d99914fec4282530f61e5db1b81007ea1141a2ae09c171ec5cea4ceb145bceb6eeccb8ad5792d9c068fb6ca30fedd3bf5b33531c1200f6e1a7c29fa75a5cbc5de94993a8540df48e542", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "cca5dac5b7b1e75ae095da7380dd00ad4eb11eb3aeebc6b6cd3977ff37a670a52826b30e7a659a23", "result": "valid", "flags": []}, {"tcId": 67, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200049594853b1d27b67e21a807307476e597cc62846a524a6cefe33d05ee09d024a7ed6d78f7377577890295a86af95c63afd71e7c0c09c554b95d8743dbde997ea7980bc84022f47a009ff65ea146f1d6a6", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "78051eaae92ced6c02a381431bda6414e6fd746a8c8224330aae110ab77d2d8580cfc89588597cf4", "result": "valid", "flags": []}, {"tcId": 68, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004a174c7be59d59f2041b574d8e7a4cc40b3cbba314a81444c54b98e04fe27783bc32373a0959828657492bdd1eb73e6a5a9c87f6e8b09fa86f142ab6bde7dec8242fe41be25a409de916a99d2e466e698", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "900842fa0e7b34b3579de3059ac21067d9f980dce877c024941489d68db56eab88b360c038ac05ff", "result": "valid", "flags": []}, {"tcId": 69, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004c95d5ff18d3a64ff86d5e2934d912f494389924b4600e32c3665eabc80f21e6fcac30edd82ba1baa4fc918efa5999def4c65a744213a08348e6a035bc0eecf58b83e7fda4da041d04a78339b4d1f1269", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "3b0dce554d3f5ad623abcab719123cce23bbe0ab0bc5371878d9103639668501eafb1cb2f7f083f7", "result": "valid", "flags": []}, {"tcId": 70, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000424588969a00e897419596d7ba8f44f28688b20d8025fe66c9e18c443f0bcbaf411224a250fcd8895bbe4030ec0f740f62fb37eda0f8be039f9842916cb8157321d4051f2b92f28e6cf6b45f4961e376b", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "551c468b85758bab5d0e0cc8410ba2b712662c8606e48501a0fba3fe7627f032c27398b877acdfbd", "result": "valid", "flags": []}, {"tcId": 71, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200042a475bb4c50916af011b310ba54486831c6cdfaed629b768b58a45eac803a135c4d46cbcf8d2b95d244e74edd70c33eaf454b55d85bab97ff362663cf807d1fe0e4ed1fbe5e512ce4ffb01c63332af00", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "7794d2fc01d457421aba85920d2963e9f42450f9db394a1aa7eba6216473fa0099c2a19b3fa68c96", "result": "valid", "flags": []}, {"tcId": 72, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000478c05a9df4edd66f78d04f06117ae317afbab1a21915e35b69545060d868fcc0a713e9cea2bb856b03b8ccc6382544ac8905585bce980fdb58f5139ed03a13fd1ed3343a0b383c4e02defd14313fa8a5", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "33a95574cd53abbc11faf08c8dd66343ec41eec793942907c7c0f3c43252872b70a2552723457d36", "result": "valid", "flags": []}, {"tcId": 73, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200048c38209253fa06ac4739e51158f5eb1beb8465e10b05ec2ec91cd2cf55db23c45373c3d826b3dfd0a0594e483f0dcd20ad88fde90a88dff026be0af9fe10353e9e95a58f86fe19df3081bc576785af84", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "9f44ca6bc2b73a654de20c4215904342ee7f0776031b5c725dc0d2fc7dba6b1ce3b2f80a531ca19a", "result": "valid", "flags": []}, {"tcId": 74, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004001492c138ae8fcd30c6ffe93f1ea83fa6b8e59988f414a77d4e04841e9f4f60f4804bd95b51d26127208547ba9c9ce82890144a1ae17303e8397a654a26bee0324435efd7895d55195e439a53536917", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "20f2b10b9f2c3ab01390c653a0d4eeec3aea5269360c6b221b4bc192c012ab2befe4b5c2442d3888", "result": "valid", "flags": []}, {"tcId": 75, "comment": "point with coordinate x = 1", "public": "306a301406072a8648ce3d020106092b2403030208010109035200040000000000000000000000000000000000000000000000000000000000000000000000000000000129110253d52cf3c5fc3382fca93d18adf7b97999028767b9722381db68fe3a41793b7d9952c6177f", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "143ce0d0a4a2e5c5be7ba1a1b50dd0646ad1fd8996be20eb5a3a6c7cec52dc748564d17314f7e83d", "result": "valid", "flags": []}, {"tcId": 76, "comment": "point with coordinate x = 1", "public": "306a301406072a8648ce3d020106092b24030302080101090352000459f0f2c74cfef6e085687c74187cd839c9e1d84ca45c93e06613bc9e0dfe64192e5aef12825834a8563a39d9eafa5fa44b01d4a1ffa855a79480dd4a68cd5293a402d94824cc2203f5870a73f53cb74f", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "383742873a1edb45d57844ddeffceb5aaa00867a29c58fd63a1c2171d571d6c5a2f1b91b9cfe6168", "result": "valid", "flags": []}, {"tcId": 77, "comment": "point with coordinate x = 1", "public": "306a301406072a8648ce3d020106092b24030302080101090352000427941f0a311c29b4995709533381544193f5cba66f5796d94df95f82b5ab60e2e7c12b1488bbc625c1af47f7d50a319406816eddcd7e66729bc96b5806ea4bf1e74338120f5b777dc71670ade149286d", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "c0b34d5f214bfc5fbb6a24ca42570e11a048ed81fefe9bedaed07fcda2ceb97ae96997b10ecbd856", "result": "valid", "flags": []}, {"tcId": 78, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200049a756d9131f1a6c75a1131c770a8c22c8339018dfeeb1af1aca63e38683c87891e47d8d473cb1ccb89c484b3a90db1e6aa9453adf96764203ca6e4e2772146e495a3ea9bbf3877395bd2342716010337", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "1317faa0f61eba0fac665dee99e2c7c2f26cffc4e6435482bb64bf1e809496e17eb6313e8416e208", "result": "valid", "flags": []}, {"tcId": 79, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004367b07e1d93ad48fe6c5e5b437668137bd83265d74da9a840320e717c538aa7efeb1e8e043b6f66b590abff4d0ec83aaffa3bbeaa46bd105818f4e3cc80cbbf8dd06c56526d5b7c652627ce1066b0e6d", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "5380549de6db72e975dfd73e99a113f2e6a3c5da548c2facc0e8f149f03222ac19187a727fe12c5a", "result": "valid", "flags": []}, {"tcId": 80, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200045d69dc51a29685ecff8a165d0042f33b2b7713005871d5f4ba0cdb79df7d53fb2baa9c089212c6aa455fd5b7ef29bc8928b055bf2cfca786dc847b31c6231a6939bfa498db93e90ee892d37ac1ebcd6c", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "873a0b764890b49361fd6527b4df81856dcdcb619a5c55ae7b05ab2efaae9e75c3660c66f0c4c4b2", "result": "valid", "flags": []}, {"tcId": 81, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004bdf1c05879c2461061a351b305269f6309968e57535a2a7574a61267eef4979fd001209bce02c41465b0e8c40f92f015d3c03de0f64a63ebb525fa5a1975910c7446e1256595422108fe0e671509c17a", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "865de8381541909f5dd4283ade9e28c2b6f155b3f76881fff03953999bce3a45d4fc76e265df888a", "result": "valid", "flags": []}, {"tcId": 82, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004897cb2399eacebcff97f305ddd7dfb6313d444db2f3a831a5a3d0bc9bd825e36057f1afed7bb1298c9fe5a3cf0cdd2abfd9951a5cfb20bdc4192d4da4c69393e1a76731005acb9ad8197bb27dd8bd91f", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "63b125d27eaad2c7b4d7e7f890db355ee1d9d91154cdbbd727b49fdd45ec5892dc2424330a28bbcd", "result": "valid", "flags": []}, {"tcId": 83, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004b4d6eabb91a7d543377933ec9271670973378391b689d9526c2345a483f239ee305e2c10f337adf27dd03a14ba16be16dc89fcaf23a3dfbcc78580b706da817c3b10b1fe8b6d275c90acebcdd2adc33a", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "2af9a7c4794aadc2364669d6885a0c4d60af8d1af8fcada0d273bdc52c650fcdb392ab5b75e442da", "result": "valid", "flags": []}, {"tcId": 84, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044332e6d2bf3286e9bb1bc5c3a124988b9736506da3c4017511f58368e7ce15d0878c26b18da7d68a44ef6cdec0a1ff81b5c5bda3b773aec65b95d7ed94d895d2bf969bc24d2062e7a5a3c8f4b81c07b7", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "5f1ea38fbdd4e1af0d4104a749b372cbc96d0615bf075fa05ea45d1f90148ae9982c4aedc00b59be", "result": "valid", "flags": []}, {"tcId": 85, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200046bb27b19b70ae514e8984ffb264c119bc37b6162c46f9c24efe8f5c1d9c685b4adf6d23fe699e6338a2a21d9c026b84a3a8bc08c504790f4f49fe843c565329b797732e642ae9ba5f2e22ac87ed9ee97", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "1aaf0fdf250b2c4d58140e5f26401159877fb6f4c7817668850ec41681a60c3973de2622bda9d644", "result": "valid", "flags": []}, {"tcId": 86, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004b0e15af4e849e102a6937cde7d1e3da511d4dc268c4a035f57b8a4088b1725f233dfaaa3527fc8f244a5416d65ccdeb7808c5cfe26b597540149f68246a36f3f6129b94251b7db499709e636c1b23946", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "82ec7dc34b0a2584d32e583d2a60c4c5385a5e659f8c60844d38c1fad53708cdf69b8f2c385d1d81", "result": "valid", "flags": []}, {"tcId": 87, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000491d2433eab888fc74905c6fed75d3379c870b1a63ce9aa030a441e3f62c7c17b207b112221d2dd3c7652da1323f535ecc03105c0562bc86d71641a8eb49a174f23cda28f1ac8c6bbd8625a9e70f3eb84", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "84c4044c006096a73b720d614e1c713058c23edd86f2e820bc7cec1e28a0862a221fceee2c9b7934", "result": "valid", "flags": []}, {"tcId": 88, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004c64862bd8256e36a5f1a9b3a3964b9de6e04d6fd3daf9acca76f731f59cae72d0b67119c97a20109b59addb37d1619416a48425232232a35eda5e59057f0afe0305942621be76fccdc701878604cb0ca", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "73ae02438e60788c51b4cea1e97532f1ee0a1f93d26cb29b7a8aa6e45532dedff0af6146a17726bf", "result": "valid", "flags": []}, {"tcId": 89, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200040bac6afcaca38ff3d345aa77711edf00ea72ef7d73c6e43adc5b6ebeb579797a110296d19fc1a110464bde0d4d00fed905eb7fa4c0b055d48616a6608b91109727f1cbe243cfaa0e5f14ebcffc7340c7", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "366c1879479f24c8b3d7b49d088e9bfddfeb0e59728e7b0984d4072275843e7e02ba010d3af87b16", "result": "valid", "flags": []}, {"tcId": 90, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044bb4f76ca89b9234bc94eddaf1959e01f3d284e53f43d7bdb17aa007bba3b1fabfbd2739b2698dbe2a7cb6e461d398dcbee30e57af79c11943eda42c9a1ba8a6e573640e163776d80a9ccb7428867014", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "b20992884d78f347b51580a3013e947e8224bfce90158902782a2aef6d69e67b99703959da295ab1", "result": "valid", "flags": []}, {"tcId": 91, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004c830d7da94ab1578996d590fa859f1e44f85c0cf6dd144386be2965cf87a5c3e58d6b4848e7b6eed86bea65901549127973c94c989d8cdbfec2698fe26cf66315bf7efaec98d2870b4813908213bf762", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "55f3c85697692d344b63a1425af11f948953a65b7ded497a6188b983c4e6d6391f14ae7631d050b4", "result": "valid", "flags": []}, {"tcId": 92, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200046045442348eb3e83acf27dacf9bc84f0be844ebb3d374bbe771b72e4cc79b5dc857b1d1316eccd5b48a451aefaaee81260402e2685f2f92c743651f600424c55aa870f58770bbdf4bfd57405beb87c5c", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "b17605a70c142548fef6265d2c0eb0235158fc3428c824db37d02ccc69bf332dab71428fd690bb52", "result": "valid", "flags": []}, {"tcId": 93, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000472077fe371ec0665019cfd1d7235475bc0ccaad6422220e7554f64ed9cd1df5c4a71df5a21659276b61372f907d6b7afdac6c96e6416baa9c6b06bf6c46b919abdafb1a4d828979cf282cc96233d609b", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "220c92cd3b7ff1a8feb218f2c5670731fd4561cce152fc1f5a9bcbdf50980c9d23e6e915c8501028", "result": "valid", "flags": []}, {"tcId": 94, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000490bbc7f7442d4a6540123bd3205039be572c3d05c74413eb4f8ee8c9e1bca0f8b387fecb4c75be28bd387962a550c7acb1c390054b89aa381dbbfedcdbaf48035727b23773a778fef7546797a684d928", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "3fe2913d84ae9e33a208af3a9d2cfb5cf6518f8b146705995cee087eaddb41000b1a18fc89408d70", "result": "valid", "flags": []}, {"tcId": 95, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200047782c1ea46a62b8d2f6000702578254baf1e4d5ffc61f418a6ef7aa916ca0f6e15f25da5a489b19638cfd2d26fb25ff96fde59ff3eb3c78e0878fada9ff0a18cd913e693389aaf45ef1e6a30d0eb1202", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "437553ab5230fc921a804849d9fd614da307b94235e68fb4c56cb033adb50cbe0529967b45c9d189", "result": "valid", "flags": []}, {"tcId": 96, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000428e412eb78d9714ab767a798736ef8732a9efed04532d48d31a34f7375bfa94b672b661cedaca42f280e69ef2a216b49c9d9344aef11926538116e91f84c9e54cd0983d719040daa9a9b1aaa7783ed9f", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "4a6e09fcd87377c3ccf209187a2c798657dd782576f43f5cb36e281d6b147cbf249032a02176e136", "result": "valid", "flags": []}, {"tcId": 97, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004b7c59b18e1a2973db0d19e1a99cdbb65a005e9cf91a2c343e4f85b47c8d352f0b8f5bc63ebdbf842d0c530329b6a9c466bc244698e34a763bc7b022cfe671e3e13ca64d5e073e8d142c7df000e3d3a4e", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "c4e0ac86ed19425993b3370091d680fae072a87da5e36e0955c97b0a5cd765cba32e411995c3e65d", "result": "valid", "flags": []}, {"tcId": 98, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000479099f049d51b29d3e19acd5486d3d15316a66ab59e915bbac2c9eb84b67dd4f65f201428ba3ce2f4b22ec67c9d9c95acb0aecdce220bbe26cc36b0910af61ac9208aae5ef7196bc8b3dbf3cca45673d", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "06816dbae72dfb90ff66d8eb721520200cf84dae6aa7bda57f0c7a848430e74a6bb7deecb268715c", "result": "valid", "flags": []}, {"tcId": 99, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004b4ec81bf865903ff5075a3c007f82624cf6b41f7e9880c27c0e157bf4cc40d019a7804369783d3cdd2e3d0ca81e69818e883913b314f5a7cc2b7d0dc429f3fd7f7370cb85009326689334539016b10f4", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "1dfadf5791168956fd92bc5b340933547653d11a70281ca9d16eb3217ff9f058879c2fc6f46e4293", "result": "valid", "flags": []}, {"tcId": 100, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200045ca4858a04e6f189c8e79fba973c604d3c0ecb10126f04c1e1332454fe1d9242ad182559e2dbcc1021ee097346ca8e7cef9f23577743fb0500cb55c9820c5923dc7aab9b47fcdb9fb93525017fc1ac1d", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "291d8a69235e626a978ed43c3fbe2ffd70b16b40260cdbc9b7ffa467a55cd0abcd7e7451088bdba5", "result": "valid", "flags": []}, {"tcId": 101, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004823e375aae805dc04bda02e9c50f418082febf280cbecf003fb6c5088aa127679ae5fd1ccbb64c82cf01eb94525b203385f03ae01c1c4bc81eee215cd7e4c2f917f4cd0b435cafc51825b6c8757475bd", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "94a6883ac5eca70458bdeb513e471e67c56e9a547d2f1e78867a05e1d02b9fb3cc5b2065fee4524a", "result": "valid", "flags": []}, {"tcId": 102, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200041d7194d70ddf8fb79b1ee1223e4fd704ed37daeb4d682588ccff21ef001478cd84acf8c9c36c6c3eba8e73469d654a379036a1ea8bf621f3c39e2a41d58c5b95e9882494a4912415d1a9a31c20531b2b", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "79e8200270930ca220dce4a297d54617faa4b258dcb13595949624c6705b83aec114981456303296", "result": "valid", "flags": []}, {"tcId": 103, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004b0ad13db129c0c914f6071f7874ae39e69dd89871854e97b1891a4a2ab579027d9bd37510fc83f2102fa3dd8d8d333344885bce06d577eba0b9cc608d28f1d3d682844ab20d94d0b77d048737ad27eeb", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "62f6192fee3ae1a84afeb47f3838a9fcad9ba937391a9ba24674029ebc7b0a00126013b5aab63a4f", "result": "valid", "flags": []}, {"tcId": 104, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044fc4baaacc521eb5d11810d83390ab54fdeafc568f51266ba3366efb511f4c4c859fdd72a977a99014d7743d1acaa052ef3fa770fa167075f82eeac360851850b0f228f61a997cf63f14240efb9b7c11", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "08fac3fbacde7983e040871e7e3eaa7de316f8482d78add49b954fe027cda7843948b3a878927da7", "result": "valid", "flags": []}, {"tcId": 105, "comment": "point with coordinate x = 1 in right to left addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004726a133f9bd2ba99d269d5e22178fec40ba23cc54ffce3a6ad9d0abff0122164d7de68bf01ae423b7ab15e125bd1bd032066f40e8ae1804f16c03b752a7d2afab93416592b9297a05941fa5e210b9241", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "485692c5c5d7df7d6a5588fddc461a3c85050bc56d2275751f80732e52244ef41b0d636b48c5890a", "result": "valid", "flags": []}, {"tcId": 106, "comment": "point with coordinate x = 1 in right to left addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000465c10cd16129be18fc8f5b6272fe349df18c45aeb3b4917a2694ac17bd5a903718ef230811b58c627b0c054cac526f1bcf607c2a3aca419c24b0851c5aa66a38929b83ad28d8e3845f1641fbaae79dd6", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "84361f6aa6f1355211632f867d657284d681f2dedaf972917673ab9294db2f7da104b5c832e3f445", "result": "valid", "flags": []}, {"tcId": 107, "comment": "point with coordinate x = 1 in right to left addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200045510ba0731143c2ca2608a4af2d35707e8fe67777ce593acfddd0bbd0bf634d01b0d2d132cab5f256b55209c4bf824aa887cfb70ec0e00260df55f5db7633be61647ed0739063045ef6dc1a36852cd79", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "59dbac04960d464f4df51add3c33645cb4c9abe492719556717521d16a0913f4ca63a59c58c3e101", "result": "valid", "flags": []}, {"tcId": 108, "comment": "point with coordinate x = 1 in right to left addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000482b4b282778bfb219f1ff993f9294871731dabfcb4ff41fa7025f2fd11656b5f192855b1a7c17897bf74b6171607ae31e41af7e7d6838d208203ed465ba855580614be1647e6b5abf78a70454bb61663", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "0ce0bf73c614949339d7d1c414d18a3a0a9d23f085a3cb5c37195c6e62c0dfd0415eee2b2e46cd50", "result": "valid", "flags": []}, {"tcId": 109, "comment": "point with coordinate x = 1 in right to left addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200048aaeff5637ccf7dbecb7c5c9fadbf7f3d5ef6bbca3a643bfd499f380159366874a5293b24b930c7107e68a6c7b654b1b60888f325a364e33bc7496bc79298468fe64e58b360b5690afa8eb44288c45b4", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "81e31473f80d6cc1751932ff8c48cef7eff5d7f5c7bd08724c95b822cafaa3a93baf6a0d152a100c", "result": "valid", "flags": []}, {"tcId": 110, "comment": "point with coordinate y = 1", "public": "306a301406072a8648ce3d020106092b2403030208010109035200046543633f3e4e1d9097b8827c8ee4d582484218c9383f0cccdb63c9f17e8a111d43780163fadcc1cb00000000000000000000000000000000000000000000000000000000000000000000000000000001", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "1f538c5e7f1c9623fb6ff0013626a8bf399efc9aba65fdd3b711dfd6e14da08b52c90c2fb3f84883", "result": "valid", "flags": []}, {"tcId": 111, "comment": "point with coordinate y = 1", "public": "306a301406072a8648ce3d020106092b2403030208010109035200048f65ace89845bcf5e074b77ea50f2da1343dfa1fe27046fc60d15eac32bcdf5131f83df8c9927acf464c15621ef320c3d17121a603539bb4ab8cf5beff24c59e7f1446d5ed00032cef5955560b2f3f58", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "652f2204c3d1c28e597876d86883bc4463193604e66ba530e4b03e5e0d2513528aa2065c17744337", "result": "valid", "flags": []}, {"tcId": 112, "comment": "point with coordinate y = 1", "public": "306a301406072a8648ce3d020106092b24030302080101090352000428d623fedb49a45dd275ef5d064e7ea92cd9e7443f9a00057036e12c1d8daf621cadb96d37fa3292bb13f2515219e4351d8af861e297b22878623139f3cedb8a823d7c76b49e8c8da1e062f05bd8e844", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "858368ca052fd7354f6a94ed359830ee7bd6ebb0c8cc5b381a80ad6121422973bdc07c2ce311d2f0", "result": "valid", "flags": []}, {"tcId": 113, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004acce56aa39bc7617bff19cbc4ec8abd2b46204d7dccbcb18326325493f43302a46f4c131895b8a26845877217ff36093461de220fed218827121982d48d97bfdf5fa0b59f7b23795a66c03ab177ad351", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "6552c8dd125c169be611961666e6ab80358698e42416095aa5c6a0ae2058b162f1674546f10f9c55", "result": "valid", "flags": []}, {"tcId": 114, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000498e8f85bef0040a57eebf9d297de460d334374b51ba3c6a772f002d69b5792aad8e37635e72cea5b393556fcad835bacc9d8eb1c4e977510fb5b59440e1f144366d1d52547d4b75aae18584bbffa2ab8", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "3d5f8da8b5bd68751628409a56baf166c1dbcea890a9414db7d3b836dd45cedb34ec7ef195bf0bc7", "result": "valid", "flags": []}, {"tcId": 115, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004bd25ab212e2eab51ec8379a168aa1e3fee7bb769024f7022d439de6a425c91a8ff2e2c36e5ef52b403c60f9f99cfc4396642e64400e99e623ca305a6b6a6d3d96f90371bf16dbb73efb8d4c853a81369", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "1f9c6fde54b194ee8d730798449f2a150e42f2400b9a27f8e0b7daaabba3583e21b7769dac0fe1c9", "result": "valid", "flags": []}, {"tcId": 116, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004aa2cff40a631387c0184d0898e3d38cb330c4009177a172c480f4a3a569a3c9173dd73f42e8562a401448bbb276b8f88cb37ec61d62ab40e60b4f30ee3d1c8c455421981c7b3d7ef067eac426a96a687", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "c4b074539025066adb03e2be9c705077823f4c4688e553a1e07a56fd2d0b0d575bac86dbda5e5c19", "result": "valid", "flags": []}, {"tcId": 117, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000459229784aeba81180aa70b39a453e69571c16e7474656b6b59b664f0b59c1be0aa9d6fa39fb6db739ddd45e842645028f986ecfd2d8dbf19a2a2524caf5aa578500841032da0ba3d21c19f3051856ffc", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "b3f74ed216c4334b037e94a1f8de09b615ece4d6aba0217267cac86698f604db53632bc529b9d528", "result": "valid", "flags": []}, {"tcId": 118, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004a70bf7e4c2281f8e0a5d53485dbca488708b532165b7c02cca2943c8b69609f8752f11005691b48b254d72d57272e8d1e78cc3e32bc3a73b5004c903a65e231ee0e14d8e76fe2d27d46a30c396bed6fd", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "05dac67f72ea4bf4dce6f6ef1971e84c471188f779b15d5a0456bba829c7568169210d583e4bd1eb", "result": "valid", "flags": []}, {"tcId": 119, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200040949957689f8f75219516aec73b0b904169d42efbee6b03b1b03b464afcc51a6bb37c53c7d6546a45e5229981f053fea58d2517b8fb6e99d18b4b3f6944b5e503c94746be9bd0dda39f4c917605d2e1d", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "246a61f292dd51b3f168acd50d44002ae6317f9a1a21e2b32f39333edead71092d5bc5450d105dfd", "result": "valid", "flags": []}, {"tcId": 120, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004b093c4867d6f9ad714504b0e4366b7934aa4be0202c556d62bd726a0f37d89f5663d1d4bbc827315bbb8293b9370cb662427476c5fb439998f90e7eead78dbfdace102113d7dabaf3bcac8cc56b890b3", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "32c3ed9454d78ae462147c010c88aff691c479d6dec55b19cff856680017a164e45c57d90d5aef0c", "result": "valid", "flags": []}, {"tcId": 121, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200042eff93a730bd79bb0acda8ffbc7c927b584e11acd5b5af726c965e496701ae8f4fd5a49e5467fcd089d16be5eafe7ecb7dcb7b2fa40269c389084b2dc068f039e6497158fea7bf53b913c1ac66e83921", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "ce5dbd12186cbd2025a71e8588c38f8a77841a249dad66168523e7ac2fb9a221c1b5bd446282b1f1", "result": "valid", "flags": []}, {"tcId": 122, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200049d27cb211ffe7b55302e922547dae1bd22e7891f6afe0a639031c93a5192c92c24d2a567df5ad11425b3b1b25c408a818ed2d81914922993bbf1057cee48b9340211bb557431c037f840242a1825d6a8", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "03177bee627f3b12c2220289ba539c670d4e44b88dc6b227debe69697cdbb621ecc35fa238478c10", "result": "valid", "flags": []}, {"tcId": 123, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000428c2760e4643954983df25c1669e7f60075a35c2a0a6b51edc66aa9b5cde311127ebecde55391df838dfee3ee6446745715918ba6df23eef3f7183b7dee1a1f11f11eb2099f514823c2c8917ceb76b53", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "a98d9962cb3e5318048d4dcb4cdf0c7df45a697a34eaf4dc8a740958c011a9736bd92cbd6b3b5705", "result": "valid", "flags": []}, {"tcId": 124, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004a9a7779e78acc309c0b36362def7b7541d6e4727c5b1f2427dd2226fe56f3a1faff7ea737464fdc030051f5d43f9cd4987e1f4949b2502390c440a61c4e907b5af70cf54eead280ff6cae5140dae921d", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "42b706597fcf1c64f6107d4242d236e7fe761722faf05f1bf3f4c3662aa2f39764401d12c7685859", "result": "valid", "flags": []}, {"tcId": 125, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200045af83d0971a0f73a0ed0283d8c46ab2f980fa40dbabb575b27f776298944f383aeff4c375ac45a7c32ae966aee37304edeaa854c1949ba0b6594f96238a848598ce606b7ee125f121da507cc1c3b6384", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "52d0ccf90333ce0cad89a3a5c10e1c83594d481dcf16fb115e40cfd1c671b7fdac827b54982a42c3", "result": "valid", "flags": []}, {"tcId": 126, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200042b752cd47982c0567869f085fa32591f91d1535dd246714a9a4443cd4a65cd127e6584da982fbf445ad3599767f9a6f0193cd61672521e4cec02615b84a89254a171dd6e1b3c06243cd7e8e54f930cca", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "777204f220bc76d57ac4e8bc15c65d275ecde4b1cb8faf2279476d4b793ced8f4d9783d4b0483d1d", "result": "valid", "flags": []}, {"tcId": 127, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200046c9b8942afbfe59687168e8e52023e18cbebdc7fb468287788c98bc6a65eeb6aa0fb49209267a0b9cf33a32770ab0fc7dd3070ffc54b1cb1ca4261a18103bad2aef403332e76f38ca3a4ae4437eff33c", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "5e11f8858854948e620144385e6807169ca59fd9728177f981325026887cdd803e7ae2768f4365d5", "result": "valid", "flags": []}, {"tcId": 128, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000488010d2b02559eb9d56810e0f48bf00e5f19d306a182d36ad4e62d3da9dc3838e3c18b0a0c4fee2c2808bbe033a165e8ce26a65555997b7e616785e8e3a91e198e389ff935a3291d5566a9d91ac69c7b", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "974e8a572ce6611e810c3d1dc2bf6bc7651a2892f961fa386460e6c93ded7111d061259d9a0d2285", "result": "valid", "flags": []}, {"tcId": 129, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044f3e53c8c4ba007a2f12505fe87a24ef96504b36455d9e815f24600035b1fae55f329c39bfdd2e412b9984f1d235db690c3948b03582b0c9570afda9a93051d0e80bba76888e8f3211d62607e67a6744", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "038150bc168d71b4c99d9e6b08aa882a64415dd04e6951c609db3555b7cae2202b9190e8019c21ba", "result": "valid", "flags": []}, {"tcId": 130, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000471d38f28bd5b003e6bf72419aaef283639602e2408b781b23ed319380213ffd9d0727e63ced74e12c21874724bd0b0969e12ca093abf43704fdf3f53987a6f675849cf2fa1975a77c143197ee21bc482", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "9f092811ffeb10fbb33d6abfc00eeb07e181b0900dfe9777818a422585d55099c1d4e213e6bdc554", "result": "valid", "flags": []}, {"tcId": 131, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004c10139c8c3f046844cac36d7dc7050fa250004190028fdd01c04f2cef13f91a678c58ed89e3f54967e60117e9ea2f394c59326d620a9a68bb0a62745cfe7018626bf500127fb0db22a01e13bd7e69946", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "1a67f784f64adbc3cfd0bc19e272bc3bac59d8035b1e5152550d772562e52867cba8523f8b3648b5", "result": "valid", "flags": []}, {"tcId": 132, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200043b81b65ebd78fefbf03906088d3d702657b137481e55cead9be8b8edf3e7408f23f03383137071035d315364d33b4d4bf070e9aad652f3dc36ff82517b04d449dd5072c996d95e4441df6f8ade68bbd4", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "3e0ffdfe181c1b80e0694900a5f5eb801c362a5298b1bef5b4bbb0c94058a8c5b5043706c70e38b6", "result": "valid", "flags": []}, {"tcId": 133, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000489682b24fd24781022179e629e0a6d1aba8286bc6628ad4e2eede39c1ea8233fbe731e81fec8df42002eebe95af215eb4dad23da303c206637da3551890fae98d03e0686177a2d5b2b093a4827e8d18a", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "858e55fc4a59e73b57676401bfb4e111747a79cda9da06cd8bad8d170e1a43e5fd2d5de7dbbfd42a", "result": "valid", "flags": []}, {"tcId": 134, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004a23a49094f11973ae2320557cb05a33ddbb497049569f23eaff124b34e41ba5f3c0a3d19b36a31a54438cfcdeeab3fe3fc365371a715ef59a215899af2e7dbe2c18faaef86caf648b64022a8ffaaf179", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "00a89377a81524b30980bf4634ad670f219a29098ab5cb291c42d84a20fc8bd76922edea9aa622f6", "result": "valid", "flags": []}, {"tcId": 135, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004914cb46a0544ee31ff1ac51ad5928018d71998b4d988cdfdee1b8f024d8fa9dfcd802ecdd693b3245e5a1056b9bc4104ae1374c59ba44ab888bad4ddbd42dae81b229c7504f797e249e5e661ac06f3aa", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "80597cc645e946d61208a80547558a08bb189365dacb2d909141e70d37c31ade6fd889a90ba7f372", "result": "valid", "flags": []}, {"tcId": 136, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200048eae8e5e0353087af15c711e77f81004664ddf993e855f644c6b471720ca39925f63ecf64a8314b0abc12c6c75fcf598ffb0039dadbbf319cfd73c88f2af2b907d9d0b294143566a55d85d2aa76769ff", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "2c7a43362aad6af4f7047f10e0740319377081f1d8f13d0780f5942474399bd309fae8c56676189f", "result": "valid", "flags": []}, {"tcId": 137, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000438635cddd7892c35b469efe33b9e928261c3dfb3764495aed90b668c7318fa721a4ebe27b531ded64debf104c2a50b098bff462cb73f63744079a8138470abd04c9cf9ca4436398bac5a6665f8784fa4", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "53a1370fd4c15cd26c65ca1e8e36a09608f12a6c1f9c67d9e58630064a2cf4412f8a17c8ff455247", "result": "valid", "flags": []}, {"tcId": 138, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004110d1375adbde14dbfd1c4a9e3a046e44c06bfc13453940ccf6613000ec7d41e9dc41a32252fd2dc89bc34a85eff53b35bc5f91c00ad29ee8e2895551ccd3d9aea2b65903295b52ca9fefe634864bcbc", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "b46d2bd52c81639b915df091351292233e270a918f7468b4a1ec62bfe504c86045398ea0dc9f68b5", "result": "valid", "flags": []}, {"tcId": 139, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200042d10753a978047cb3de80466cc0db57b2ecfd21dcefbba5b1cf4872c10841e64792f2f99d6f1a901b57a8435a238993afc6550893c7bf54e25d0499713a247e673b056d4b1d1212f74fa52fd8a686842", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "200ed19b0879a65d46516a0cde86ebfe6be4f90cc45adf7af384aa8adf12670394f8455df540df3b", "result": "valid", "flags": []}, {"tcId": 140, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044ee8e187e9ea408807f5fbfc11909f9b3c442a1e7d8ea98474f8ada337078017801d5fe9293bbc50c7bfd24b220f48d7b51daa7fbbd637c115e1b4d536641cecd5293b441453e2f4a138ac195574d38b", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "c70c537368f231ac41eb589c4956d5f30affc04e833a7b6cde3fee89921e9a289dedc78994b37458", "result": "valid", "flags": []}, {"tcId": 141, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "306a301406072a8648ce3d020106092b24030302080101090352000496a4043124851cac3e290630a07b6944f9f0509895a8fee4920227db5c60a11f9693f26f3c94c1a9260bb96d7520cd0d15cf9dc40de0b72bd3400500936b9a773172d33cb8bba031e608e72c60d5e704", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "65f00b9cf9a0062a16a08ed992edb21a64f7c41e733547de4ad419d167f54c0fe16065ba2e1086ca", "result": "valid", "flags": []}, {"tcId": 142, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200041ac27bfc8d070217483700d187ee89d50be1625579db9c6525569499e9d1666b0999f826babac57e0b93595b26ca72284a4a4598aaa55675ccfa4b58bab287f56918f3af3e6e83c959d8e78698ecbc48", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "3e21fb8c5de6bb3fa096e5bf73a270bad16447b55dcdca39e3d9923f3fd04bf64ee705247d047296", "result": "valid", "flags": []}, {"tcId": 143, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "306a301406072a8648ce3d020106092b2403030208010109035200047dd14080bea4fb4cb7112ec8c4c303242536f69d28d3a586ce65c4be96167ad52a1b9633e15c597b8c0597f27271ccb34091c01e7b7cf36757f5739f5763111e94ba8ada9d6e1a095c66b5b45e562aac", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "d2fe1275bb639f4465cb95ed3b6ac433a98cb1e19b516e854451f9da98ece32e2a673c0ab30bdad6", "result": "valid", "flags": []}, {"tcId": 144, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "306a301406072a8648ce3d020106092b240303020801010903520004c9dce0102a722b5e0f9d4f41a3659c63cac01537f52a374aa63e8f47e4a9a7d9224118e620521d1e071e20c43697e082831d1671713b54402a676ea587cecaee750089f596172aecadbdc3aac9297e83", "private": "009bfe2480d1e03e73d296430807ec16b808f0a200e5af357287dc2859bfdc9a89e06bf77c9bb7fedb", "shared": "80d784eb18ec8fa5b599bdf00ad2f799b37960ae1443d8f89464f09ba51dea846c548d86a15e9484", "result": "valid", "flags": []}, {"tcId": 145, "comment": "edge case private key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044e73d59ec474e679414d0922de22e06d0dad990ba4746c3d026bdea52e7bbeaac928d0ddaab29dfed230dde60fd57d4ef8e935b23cb7d4216b278b17a3f02d70454fa0e45da2054b91b0c4b663ab243d", "private": "03", "shared": "a3e86777a4263b4656733a65bf322d397199cda903588fc3754cb0e27901f5e29f115b511b94c831", "result": "valid", "flags": []}, {"tcId": 146, "comment": "edge case private key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044e73d59ec474e679414d0922de22e06d0dad990ba4746c3d026bdea52e7bbeaac928d0ddaab29dfed230dde60fd57d4ef8e935b23cb7d4216b278b17a3f02d70454fa0e45da2054b91b0c4b663ab243d", "private": "00ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "shared": "93cd465d332e25bbec7b195b37f7e3a126f1d67cdb946cb9b2c45a36a5c4d7e9171f3d3fded20ef7", "result": "valid", "flags": []}, {"tcId": 147, "comment": "edge case private key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044e73d59ec474e679414d0922de22e06d0dad990ba4746c3d026bdea52e7bbeaac928d0ddaab29dfed230dde60fd57d4ef8e935b23cb7d4216b278b17a3f02d70454fa0e45da2054b91b0c4b663ab243d", "private": "01000000000000000000000000000000000000000000000000000000000000000000000000000000", "shared": "6cc6520a3d34ec73bdc0648390a8be6736c104a6651c7a35face310fd0bf69b151aecf457ef584da", "result": "valid", "flags": []}, {"tcId": 148, "comment": "edge case private key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044e73d59ec474e679414d0922de22e06d0dad990ba4746c3d026bdea52e7bbeaac928d0ddaab29dfed230dde60fd57d4ef8e935b23cb7d4216b278b17a3f02d70454fa0e45da2054b91b0c4b663ab243d", "private": "7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "shared": "78b7d04861460cbf141f6d41648742adf459b5f3695ba2449ce76210494ce540069e1536c03100ff", "result": "valid", "flags": []}, {"tcId": 149, "comment": "edge case private key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044e73d59ec474e679414d0922de22e06d0dad990ba4746c3d026bdea52e7bbeaac928d0ddaab29dfed230dde60fd57d4ef8e935b23cb7d4216b278b17a3f02d70454fa0e45da2054b91b0c4b663ab243d", "private": "0080000000000000000000000000000000000000000000000000000000000000000000000000000000", "shared": "8f21e698a1e1bc66acc08adb5b923e4b2a012dfe8145637e702351f161688f0b572c57d4d1b6b4f7", "result": "valid", "flags": []}, {"tcId": 150, "comment": "edge case private key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044e73d59ec474e679414d0922de22e06d0dad990ba4746c3d026bdea52e7bbeaac928d0ddaab29dfed230dde60fd57d4ef8e935b23cb7d4216b278b17a3f02d70454fa0e45da2054b91b0c4b663ab243d", "private": "00d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e88691555b44c59311", "shared": "151b2e82d009a18b4b7e46df2ad0e30d151d321bd64ef2f1ff6a36781ae448b99ef443ac7d1b09cc", "result": "valid", "flags": []}, {"tcId": 151, "comment": "edge case private key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044e73d59ec474e679414d0922de22e06d0dad990ba4746c3d026bdea52e7bbeaac928d0ddaab29dfed230dde60fd57d4ef8e935b23cb7d4216b278b17a3f02d70454fa0e45da2054b91b0c4b663ab243d", "private": "00d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98681555b44c59311", "shared": "d04a5e44828163fcb66f9e601d933ba2f431cada91256861e36765f6098e234005f48bb039a288ef", "result": "valid", "flags": []}, {"tcId": 152, "comment": "edge case private key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044e73d59ec474e679414d0922de22e06d0dad990ba4746c3d026bdea52e7bbeaac928d0ddaab29dfed230dde60fd57d4ef8e935b23cb7d4216b278b17a3f02d70454fa0e45da2054b91b0c4b663ab243d", "private": "00d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98689555b44c59311", "shared": "94023419382fbdcacb99fc0c37b73be771bbcb8b2efdc6dd324f0328e16b755bb27a2ab440ed6c87", "result": "valid", "flags": []}, {"tcId": 153, "comment": "edge case private key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044e73d59ec474e679414d0922de22e06d0dad990ba4746c3d026bdea52e7bbeaac928d0ddaab29dfed230dde60fd57d4ef8e935b23cb7d4216b278b17a3f02d70454fa0e45da2054b91b0c4b663ab243d", "private": "00d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555a44c59311", "shared": "aa464b779d451beb21ba7f63b65db0442c52b9ac32f0a2addcd0869c7ebddff479d47461b83225f8", "result": "valid", "flags": []}, {"tcId": 154, "comment": "edge case private key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044e73d59ec474e679414d0922de22e06d0dad990ba4746c3d026bdea52e7bbeaac928d0ddaab29dfed230dde60fd57d4ef8e935b23cb7d4216b278b17a3f02d70454fa0e45da2054b91b0c4b663ab243d", "private": "00d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59233", "shared": "c0038da858441f559a864dcd6c4558437f9ad091a67c3fda69a9e0cb6f446a8b47ae95edc2f4eade", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 155, "comment": "edge case private key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044e73d59ec474e679414d0922de22e06d0dad990ba4746c3d026bdea52e7bbeaac928d0ddaab29dfed230dde60fd57d4ef8e935b23cb7d4216b278b17a3f02d70454fa0e45da2054b91b0c4b663ab243d", "private": "00d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c592b3", "shared": "8258131a80bc9f2b8ba532ef1253ef39dce25e6deb85227c670273521c311dbb9bf1a56dd29107b3", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 156, "comment": "edge case private key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044e73d59ec474e679414d0922de22e06d0dad990ba4746c3d026bdea52e7bbeaac928d0ddaab29dfed230dde60fd57d4ef8e935b23cb7d4216b278b17a3f02d70454fa0e45da2054b91b0c4b663ab243d", "private": "00d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c592f3", "shared": "ae752e75684a9adfc6198e6c1ce9249d26743104e8b0bd0417998c62982622ea2fdf6917413d547c", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 157, "comment": "edge case private key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044e73d59ec474e679414d0922de22e06d0dad990ba4746c3d026bdea52e7bbeaac928d0ddaab29dfed230dde60fd57d4ef8e935b23cb7d4216b278b17a3f02d70454fa0e45da2054b91b0c4b663ab243d", "private": "00d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59303", "shared": "37bfbb637fce27ee80b3af326546303e0bd8af01b72f591830a548609055bda489d9a4e6b5e3f43e", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 158, "comment": "edge case private key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044e73d59ec474e679414d0922de22e06d0dad990ba4746c3d026bdea52e7bbeaac928d0ddaab29dfed230dde60fd57d4ef8e935b23cb7d4216b278b17a3f02d70454fa0e45da2054b91b0c4b663ab243d", "private": "00d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c5930b", "shared": "854ce9516e73c6cc8d0d6ce3cdf933541a719578712440f02a86829af1398fcab2bb0949c1d63106", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 159, "comment": "edge case private key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044e73d59ec474e679414d0922de22e06d0dad990ba4746c3d026bdea52e7bbeaac928d0ddaab29dfed230dde60fd57d4ef8e935b23cb7d4216b278b17a3f02d70454fa0e45da2054b91b0c4b663ab243d", "private": "00d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c5930e", "shared": "a3e86777a4263b4656733a65bf322d397199cda903588fc3754cb0e27901f5e29f115b511b94c831", "result": "valid", "flags": []}, {"tcId": 160, "comment": "edge case private key", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044e73d59ec474e679414d0922de22e06d0dad990ba4746c3d026bdea52e7bbeaac928d0ddaab29dfed230dde60fd57d4ef8e935b23cb7d4216b278b17a3f02d70454fa0e45da2054b91b0c4b663ab243d", "private": "00d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c5930f", "shared": "7dfd07c1cfe70db4772cf9f6bb6b58a10bbc9509e5ce86651d5c395f3544f62d6d8f8109edba441a", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 161, "comment": "point is not on curve", "public": "306a301406072a8648ce3d020106092b2403030208010109035200040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 162, "comment": "point is not on curve", "public": "306a301406072a8648ce3d020106092b2403030208010109035200040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 163, "comment": "point is not on curve", "public": "306a301406072a8648ce3d020106092b24030302080101090352000400000000000000000000000000000000000000000000000000000000000000000000000000000000d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e26", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 164, "comment": "point is not on curve", "public": "306a301406072a8648ce3d020106092b24030302080101090352000400000000000000000000000000000000000000000000000000000000000000000000000000000000d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 165, "comment": "point is not on curve", "public": "306a301406072a8648ce3d020106092b2403030208010109035200040000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 166, "comment": "point is not on curve", "public": "306a301406072a8648ce3d020106092b2403030208010109035200040000000000000000000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000001", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 167, "comment": "point is not on curve", "public": "306a301406072a8648ce3d020106092b24030302080101090352000400000000000000000000000000000000000000000000000000000000000000000000000000000001d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e26", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 168, "comment": "point is not on curve", "public": "306a301406072a8648ce3d020106092b24030302080101090352000400000000000000000000000000000000000000000000000000000000000000000000000000000001d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 169, "comment": "point is not on curve", "public": "306a301406072a8648ce3d020106092b240303020801010903520004d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e2600000000000000000000000000000000000000000000000000000000000000000000000000000000", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 170, "comment": "point is not on curve", "public": "306a301406072a8648ce3d020106092b240303020801010903520004d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e2600000000000000000000000000000000000000000000000000000000000000000000000000000001", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 171, "comment": "point is not on curve", "public": "306a301406072a8648ce3d020106092b240303020801010903520004d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e26d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e26", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 172, "comment": "point is not on curve", "public": "306a301406072a8648ce3d020106092b240303020801010903520004d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e26d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 173, "comment": "point is not on curve", "public": "306a301406072a8648ce3d020106092b240303020801010903520004d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e2700000000000000000000000000000000000000000000000000000000000000000000000000000000", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 174, "comment": "point is not on curve", "public": "306a301406072a8648ce3d020106092b240303020801010903520004d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e2700000000000000000000000000000000000000000000000000000000000000000000000000000001", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 175, "comment": "point is not on curve", "public": "306a301406072a8648ce3d020106092b240303020801010903520004d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e26", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 176, "comment": "point is not on curve", "public": "306a301406072a8648ce3d020106092b240303020801010903520004d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 177, "comment": "", "public": "3019301406072a8648ce3d020106092b2403030208010109030100", "private": "0657260acc897adc69b0aa87116379842a395b661abc1befad4787cf96a2d702220f2da2b7f762aa", "shared": "", "result": "invalid", "flags": []}, {"tcId": 178, "comment": "public point not on curve", "public": "306a301406072a8648ce3d020106092b2403030208010109035200044c666c637a771b09bc9c83edc96a6e78840b0a43fbb42b1b363bfd28d2a22a11889dd1b9db24386a22e44d181b8587700a405e083e4fbee3d441a5496a7a481f572d062d29fd2d4665adc843a1ebb28b", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "", "result": "invalid", "flags": ["InvalidPublic"]}, {"tcId": 179, "comment": "public point = (0,0)", "public": "306a301406072a8648ce3d020106092b2403030208010109035200040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "", "result": "invalid", "flags": ["InvalidPublic"]}, {"tcId": 180, "comment": "order = -1763593322239166354161909842446019520889512772717686063760686124016784784845843468355685258203921", "public": "308201753082011d06072a8648ce3d020130820110020101303406072a8648ce3d0101022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27305404283ee30b568fbab0f883ccebd46d3f3bb8a2a73513f5eb79da66190eb085ffa9f492f375a97d860eb40428520883949dfdbc42d3ad198640688a6fe13f41349554b49acc31dccd884539816f5eb4ac8fb1f1a604510443bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061114fdd05545ec1cc8ab4093247f77275e0743ffed117182eaa9c77877aaac6ac7d35245d1692e8ee10229ff2ca1b8dfc943b0481ec387a12dfe1f9a0670305a4970ed5cd2b7d1381179a716796eaaa4bb3a6cef020101035200044c666c637a771b09bc9c83edc96a6e78840b0a43fbb42b1b363bfd28d2a22a11889dd1b9db24386a22e44d181b8587700a405e083e4fbee3d441a5496a7a481f572d062d29fd2d4665adc843a1ebb289", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "4f29ff49359bd65f86dde2fdcdee484544b989c6b85c8489076cbf187c57145effbe48e30c1cd762", "result": "invalid", "flags": ["WrongOrder", "InvalidPublic", "UnnamedCurve"]}, {"tcId": 181, "comment": "order = 0", "public": "3082014b3081f406072a8648ce3d02013081e8020101303406072a8648ce3d0101022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27305404283ee30b568fbab0f883ccebd46d3f3bb8a2a73513f5eb79da66190eb085ffa9f492f375a97d860eb40428520883949dfdbc42d3ad198640688a6fe13f41349554b49acc31dccd884539816f5eb4ac8fb1f1a604510443bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061114fdd05545ec1cc8ab4093247f77275e0743ffed117182eaa9c77877aaac6ac7d35245d1692e8ee1020100020101035200044c666c637a771b09bc9c83edc96a6e78840b0a43fbb42b1b363bfd28d2a22a11889dd1b9db24386a22e44d181b8587700a405e083e4fbee3d441a5496a7a481f572d062d29fd2d4665adc843a1ebb289", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "4f29ff49359bd65f86dde2fdcdee484544b989c6b85c8489076cbf187c57145effbe48e30c1cd762", "result": "invalid", "flags": ["WrongOrder", "InvalidPublic", "UnnamedCurve"]}, {"tcId": 182, "comment": "order = 1", "public": "3082014b3081f406072a8648ce3d02013081e8020101303406072a8648ce3d0101022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27305404283ee30b568fbab0f883ccebd46d3f3bb8a2a73513f5eb79da66190eb085ffa9f492f375a97d860eb40428520883949dfdbc42d3ad198640688a6fe13f41349554b49acc31dccd884539816f5eb4ac8fb1f1a604510443bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061114fdd05545ec1cc8ab4093247f77275e0743ffed117182eaa9c77877aaac6ac7d35245d1692e8ee1020101020101035200044c666c637a771b09bc9c83edc96a6e78840b0a43fbb42b1b363bfd28d2a22a11889dd1b9db24386a22e44d181b8587700a405e083e4fbee3d441a5496a7a481f572d062d29fd2d4665adc843a1ebb289", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "4f29ff49359bd65f86dde2fdcdee484544b989c6b85c8489076cbf187c57145effbe48e30c1cd762", "result": "acceptable", "flags": ["WrongOrder", "UnusedParam", "UnnamedCurve"]}, {"tcId": 183, "comment": "order = 410618568360611413177547474029944166748205379750040840301822434183392856467013123528027", "public": "308201713082011906072a8648ce3d02013082010c020101303406072a8648ce3d0101022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27305404283ee30b568fbab0f883ccebd46d3f3bb8a2a73513f5eb79da66190eb085ffa9f492f375a97d860eb40428520883949dfdbc42d3ad198640688a6fe13f41349554b49acc31dccd884539816f5eb4ac8fb1f1a604510443bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061114fdd05545ec1cc8ab4093247f77275e0743ffed117182eaa9c77877aaac6ac7d35245d1692e8ee1022500d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b020101035200044c666c637a771b09bc9c83edc96a6e78840b0a43fbb42b1b363bfd28d2a22a11889dd1b9db24386a22e44d181b8587700a405e083e4fbee3d441a5496a7a481f572d062d29fd2d4665adc843a1ebb289", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "4f29ff49359bd65f86dde2fdcdee484544b989c6b85c8489076cbf187c57145effbe48e30c1cd762", "result": "acceptable", "flags": ["WrongOrder", "UnusedParam", "UnnamedCurve"]}, {"tcId": 184, "comment": "generator = (0,0)", "public": "308201753082011d06072a8648ce3d020130820110020101303406072a8648ce3d0101022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27305404283ee30b568fbab0f883ccebd46d3f3bb8a2a73513f5eb79da66190eb085ffa9f492f375a97d860eb40428520883949dfdbc42d3ad198640688a6fe13f41349554b49acc31dccd884539816f5eb4ac8fb1f1a60451040000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311020101035200044c666c637a771b09bc9c83edc96a6e78840b0a43fbb42b1b363bfd28d2a22a11889dd1b9db24386a22e44d181b8587700a405e083e4fbee3d441a5496a7a481f572d062d29fd2d4665adc843a1ebb289", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "4f29ff49359bd65f86dde2fdcdee484544b989c6b85c8489076cbf187c57145effbe48e30c1cd762", "result": "acceptable", "flags": ["UnusedParam", "UnnamedCurve"]}, {"tcId": 185, "comment": "generator not on curve", "public": "308201753082011d06072a8648ce3d020130820110020101303406072a8648ce3d0101022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27305404283ee30b568fbab0f883ccebd46d3f3bb8a2a73513f5eb79da66190eb085ffa9f492f375a97d860eb40428520883949dfdbc42d3ad198640688a6fe13f41349554b49acc31dccd884539816f5eb4ac8fb1f1a604510443bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061114fdd05545ec1cc8ab4093247f77275e0743ffed117182eaa9c77877aaac6ac7d35245d1692e8ee3022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311020101035200044c666c637a771b09bc9c83edc96a6e78840b0a43fbb42b1b363bfd28d2a22a11889dd1b9db24386a22e44d181b8587700a405e083e4fbee3d441a5496a7a481f572d062d29fd2d4665adc843a1ebb289", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "4f29ff49359bd65f86dde2fdcdee484544b989c6b85c8489076cbf187c57145effbe48e30c1cd762", "result": "acceptable", "flags": ["UnusedParam", "UnnamedCurve"]}, {"tcId": 186, "comment": "cofactor = -1", "public": "308201753082011d06072a8648ce3d020130820110020101303406072a8648ce3d0101022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27305404283ee30b568fbab0f883ccebd46d3f3bb8a2a73513f5eb79da66190eb085ffa9f492f375a97d860eb40428520883949dfdbc42d3ad198640688a6fe13f41349554b49acc31dccd884539816f5eb4ac8fb1f1a604510443bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061114fdd05545ec1cc8ab4093247f77275e0743ffed117182eaa9c77877aaac6ac7d35245d1692e8ee1022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c593110201ff035200044c666c637a771b09bc9c83edc96a6e78840b0a43fbb42b1b363bfd28d2a22a11889dd1b9db24386a22e44d181b8587700a405e083e4fbee3d441a5496a7a481f572d062d29fd2d4665adc843a1ebb289", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "4f29ff49359bd65f86dde2fdcdee484544b989c6b85c8489076cbf187c57145effbe48e30c1cd762", "result": "invalid", "flags": ["InvalidPublic", "UnnamedCurve"]}, {"tcId": 187, "comment": "cofactor = 0", "public": "308201753082011d06072a8648ce3d020130820110020101303406072a8648ce3d0101022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27305404283ee30b568fbab0f883ccebd46d3f3bb8a2a73513f5eb79da66190eb085ffa9f492f375a97d860eb40428520883949dfdbc42d3ad198640688a6fe13f41349554b49acc31dccd884539816f5eb4ac8fb1f1a604510443bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061114fdd05545ec1cc8ab4093247f77275e0743ffed117182eaa9c77877aaac6ac7d35245d1692e8ee1022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311020100035200044c666c637a771b09bc9c83edc96a6e78840b0a43fbb42b1b363bfd28d2a22a11889dd1b9db24386a22e44d181b8587700a405e083e4fbee3d441a5496a7a481f572d062d29fd2d4665adc843a1ebb289", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "4f29ff49359bd65f86dde2fdcdee484544b989c6b85c8489076cbf187c57145effbe48e30c1cd762", "result": "invalid", "flags": ["InvalidPublic", "UnnamedCurve"]}, {"tcId": 188, "comment": "cofactor = 2", "public": "308201753082011d06072a8648ce3d020130820110020101303406072a8648ce3d0101022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27305404283ee30b568fbab0f883ccebd46d3f3bb8a2a73513f5eb79da66190eb085ffa9f492f375a97d860eb40428520883949dfdbc42d3ad198640688a6fe13f41349554b49acc31dccd884539816f5eb4ac8fb1f1a604510443bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061114fdd05545ec1cc8ab4093247f77275e0743ffed117182eaa9c77877aaac6ac7d35245d1692e8ee1022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311020102035200044c666c637a771b09bc9c83edc96a6e78840b0a43fbb42b1b363bfd28d2a22a11889dd1b9db24386a22e44d181b8587700a405e083e4fbee3d441a5496a7a481f572d062d29fd2d4665adc843a1ebb289", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "4f29ff49359bd65f86dde2fdcdee484544b989c6b85c8489076cbf187c57145effbe48e30c1cd762", "result": "acceptable", "flags": ["UnusedParam", "UnnamedCurve"]}, {"tcId": 189, "comment": "cofactor = 1763593322239166354161909842446019520889512772717686063760686124016784784845843468355685258203921", "public": "3082019d3082014506072a8648ce3d020130820138020101303406072a8648ce3d0101022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27305404283ee30b568fbab0f883ccebd46d3f3bb8a2a73513f5eb79da66190eb085ffa9f492f375a97d860eb40428520883949dfdbc42d3ad198640688a6fe13f41349554b49acc31dccd884539816f5eb4ac8fb1f1a604510443bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061114fdd05545ec1cc8ab4093247f77275e0743ffed117182eaa9c77877aaac6ac7d35245d1692e8ee1022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311035200044c666c637a771b09bc9c83edc96a6e78840b0a43fbb42b1b363bfd28d2a22a11889dd1b9db24386a22e44d181b8587700a405e083e4fbee3d441a5496a7a481f572d062d29fd2d4665adc843a1ebb289", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "4f29ff49359bd65f86dde2fdcdee484544b989c6b85c8489076cbf187c57145effbe48e30c1cd762", "result": "invalid", "flags": ["InvalidPublic", "UnnamedCurve"]}, {"tcId": 190, "comment": "cofactor = None", "public": "308201723082011a06072a8648ce3d02013082010d020101303406072a8648ce3d0101022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27305404283ee30b568fbab0f883ccebd46d3f3bb8a2a73513f5eb79da66190eb085ffa9f492f375a97d860eb40428520883949dfdbc42d3ad198640688a6fe13f41349554b49acc31dccd884539816f5eb4ac8fb1f1a604510443bd7e9afb53d8b85289bcc48ee5bfe6f20137d10a087eb6e7871e2a10a599c710af8d0d39e2061114fdd05545ec1cc8ab4093247f77275e0743ffed117182eaa9c77877aaac6ac7d35245d1692e8ee1022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311035200044c666c637a771b09bc9c83edc96a6e78840b0a43fbb42b1b363bfd28d2a22a11889dd1b9db24386a22e44d181b8587700a405e083e4fbee3d441a5496a7a481f572d062d29fd2d4665adc843a1ebb289", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "4f29ff49359bd65f86dde2fdcdee484544b989c6b85c8489076cbf187c57145effbe48e30c1cd762", "result": "acceptable", "flags": ["UnusedParam", "UnnamedCurve"]}, {"tcId": 191, "comment": "modified prime", "public": "308201753082011d06072a8648ce3d020130820110020101303406072a8648ce3d0101022900bfd4cef07c157c860286264eb02a03e4bd57db899dfc714f55bcc71c32b829e7af670ba56e9c05d1305404283ee30b568fbab0f883ccebd46d3f3bb8a2a73513f5eb79da66190eb085ffa9f492f375a97d860eb40428520883949dfdbc42d3ad198640688a6fe13f41349554b49acc31dccd884539816f5eb4ac8fb1f1a604510400000000000000000000000000237bc7178cc16000000000000000000000000000000000000001d54042dd00b5c5ebcbab5d16bfe9013cc20d177a3491ba0fd60ab4d1eafcb56a97a71827d8589de6c0022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c593110201010352000400000000000000000000000000237bc7178cc16000000000000000000000000000000000000001d54042dd00b5c5ebcbab5d16bfe9013cc20d177a3491ba0fd60ab4d1eafcb56a97a71827d8589de6c0", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "98a5ab0e3aa9396e44e9a6cf836db0e622639dcead75c2b337487a303c1a100d6e9e8dc71c9c2162", "result": "invalid", "flags": ["ModifiedPrime", "InvalidPublic", "UnnamedCurve"]}, {"tcId": 192, "comment": "using secp224r1", "public": "304e301006072a8648ce3d020106052b81040021033a0004074f56dc2ea648ef89c3b72e23bbd2da36f60243e4d2067b70604af1c2165cec2f86603d60c8a611d5b84ba3d91dfe1a480825bcc4af3bcf", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "", "result": "invalid", "flags": ["InvalidPublic"]}, {"tcId": 193, "comment": "using secp256r1", "public": "3059301306072a8648ce3d020106082a8648ce3d03010703420004cbf6606595a3ee50f9fceaa2798c2740c82540516b4e5a7d361ff24e9dd15364e5408b2e679f9d5310d1f6893b36ce16b4a507509175fcb52aea53b781556b39", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "", "result": "invalid", "flags": ["InvalidPublic"]}, {"tcId": 194, "comment": "using secp256k1", "public": "3056301006072a8648ce3d020106052b8104000a03420004a1263e75b87ae0937060ff1472f330ee55cdf8f4329d6284a9ebfbcc856c11684225e72cbebff41e54fb6f00e11afe53a17937bedbf2df787f8ef9584f775838", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "", "result": "invalid", "flags": ["InvalidPublic"]}, {"tcId": 195, "comment": "a = 0", "public": "3082014c3081f506072a8648ce3d02013081e9020101303406072a8648ce3d0101022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27302d04010004281c77c2abfaacfed15441047a89c6d600bb9a3c8aa864067cd4281901a411872412960f59881b9f010451044c666c637a771b09bc9c83edc96a6e78840b0a43fbb42b1b363bfd28d2a22a11889dd1b9db24386a22e44d181b8587700a405e083e4fbee3d441a5496a7a481f572d062d29fd2d4665adc843a1ebb289022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c59311020101035200044c666c637a771b09bc9c83edc96a6e78840b0a43fbb42b1b363bfd28d2a22a11889dd1b9db24386a22e44d181b8587700a405e083e4fbee3d441a5496a7a481f572d062d29fd2d4665adc843a1ebb289", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "4f29ff49359bd65f86dde2fdcdee484544b989c6b85c8489076cbf187c57145effbe48e30c1cd762", "result": "acceptable", "flags": ["UnusedParam", "UnnamedCurve"]}, {"tcId": 196, "comment": "public key of order 3", "public": "308201753082011d06072a8648ce3d020130820110020101303406072a8648ce3d0101022900d35e472036bc4fb7e13c785ed201e065f98fcfa6f6f40def4f92b9ec7893ec28fcd412b1f1b32e27305404287a39bc52ee94382586a91217565362dc5f93e05be97ac18f3032cec297fe447ecd69ab0a8793f9ab0428189b72fdf225e5a7ce620412b377d0b3ec43459854b801056165d30c32948fc1b39876f4176a490904510407cf9683882fe44816cdd41750ce0f6d58738abb8eea8829f574521dbae749e67bfa099050931723174437e6f4e9c5554895e504ac0ee11e6f883d439147ac1207b6863fd040883b53f03cf8e28386fd022900d35e472036bc4fb7e13c785ed201e065f98fcfa5b68f12a32d482ec7ee8658e98691555b44c593110201010352000407cf9683882fe44816cdd41750ce0f6d58738abb8eea8829f574521dbae749e67bfa099050931723bc1a0f3941d28a6298a6935a25f2ff478a07926365ac61dd47dc33aca85363eda8e3d5b90f2fa72a", "private": "63c73ba272722eb5c4ec1f8c0e8b98e3c0d6ae3547a13a1d7614d44ef1d6d14d8822b3f7dd6109c4", "shared": "", "result": "invalid", "flags": ["WeakPublicKey", "InvalidPublic", "UnnamedCurve"]}, {"tcId": 197, "comment": "Public key uses wrong curve: secp224r1", "public": "304e301006072a8648ce3d020106052b81040021033a0004cbbced7d0d8fb5d17ed00a047fb60933d1716b5f120ca68abd92edc2eb4b22a1f8d0410966791ba8fc7a7558a3b220038d84088abbe19728", "private": "70d5b65d90e6ab58a91b5a75073a347d0863cfc8a96e7db78e098dfb50c4262f401af3c36c43c719", "shared": "", "result": "invalid", "flags": []}, {"tcId": 198, "comment": "Public key uses wrong curve: secp256r1", "public": "3059301306072a8648ce3d020106082a8648ce3d03010703420004b479d6c21efe1412dd59f04404ea05a8cd7fd74c41c4d2c1535bded83afb32b92c6105ac9ff9786bb20dce846922e69218ea79a89d73573add154272385ffe2b", "private": "70d5b65d90e6ab58a91b5a75073a347d0863cfc8a96e7db78e098dfb50c4262f401af3c36c43c719", "shared": "", "result": "invalid", "flags": []}, {"tcId": 199, "comment": "Public key uses wrong curve: secp384r1", "public": "3076301006072a8648ce3d020106052b8104002203620004788d64af48129627a1b87b999bc87c3a633321f6047f071587395dd7690fdd0b85c535c2d5ae157bf1a213b1ba452b38af8087cd933f3effa32c546948a7b79c9801399f5a5c7aeaedd6a8ba1355bb12152f01f060a9cfd32da747edd316417b", "private": "70d5b65d90e6ab58a91b5a75073a347d0863cfc8a96e7db78e098dfb50c4262f401af3c36c43c719", "shared": "", "result": "invalid", "flags": []}, {"tcId": 200, "comment": "Public key uses wrong curve: secp521r1", "public": "30819b301006072a8648ce3d020106052b81040023038186000400f3f8effa60bcf759f795e0fe77c6e5aa1014b78e92957f9de9ba5b4e7cb99c163c80db96cb28472d721d8f6f39aa738ffe55c7bbb78c54a8e465fc7c548353968700e2b278cb0688cbdae017de7bd98a73d04434058978a5bad558f20120cc230080762b717f5a9d17c1bb11ec6e80fec81f4775784ec9d17aa7e5b119dbec02233e92", "private": "70d5b65d90e6ab58a91b5a75073a347d0863cfc8a96e7db78e098dfb50c4262f401af3c36c43c719", "shared": "", "result": "invalid", "flags": []}, {"tcId": 201, "comment": "Public key uses wrong curve: secp256k1", "public": "3056301006072a8648ce3d020106052b8104000a0342000486b290e29307ff43b3c81a48720c0e89a478b06d8c811b10c7ff3654aa33105244c37378866ef4920ef6099b596e95449788df3a2a8452ba922ba99e69346f54", "private": "70d5b65d90e6ab58a91b5a75073a347d0863cfc8a96e7db78e098dfb50c4262f401af3c36c43c719", "shared": "", "result": "invalid", "flags": []}, {"tcId": 202, "comment": "Public key uses wrong curve: secp224k1", "public": "304e301006072a8648ce3d020106052b81040020033a00047b727412cd8f24d539d4efe4796b95a1353967f6c58b7b807bf59070319c4d8387513180c54ef2275a33043fc7e43c328e5d08b048740604", "private": "70d5b65d90e6ab58a91b5a75073a347d0863cfc8a96e7db78e098dfb50c4262f401af3c36c43c719", "shared": "", "result": "invalid", "flags": []}, {"tcId": 203, "comment": "Public key uses wrong curve: brainpoolP224r1", "public": "3052301406072a8648ce3d020106092b2403030208010105033a000459b150d0645424cf928844d1bbd532d8a8195960332da50cf9aa186480ffb0b58b64c4bd198525183852d127aa3742fe96df49bec7585aa4", "private": "70d5b65d90e6ab58a91b5a75073a347d0863cfc8a96e7db78e098dfb50c4262f401af3c36c43c719", "shared": "", "result": "invalid", "flags": []}, {"tcId": 204, "comment": "Public key uses wrong curve: brainpoolP256r1", "public": "305a301406072a8648ce3d020106092b24030302080101070342000489021ce82845fcefd942146ce35cad027ade8f821ddfad0ea831d1f6fe6980e60be917f848a4e7dbd3dea6035bbfa41d115fba98797862cbebf8087b7647fe89", "private": "70d5b65d90e6ab58a91b5a75073a347d0863cfc8a96e7db78e098dfb50c4262f401af3c36c43c719", "shared": "", "result": "invalid", "flags": []}, {"tcId": 205, "comment": "Public key uses wrong curve: brainpoolP384r1", "public": "307a301406072a8648ce3d020106092b240303020801010b0362000437f59347ed0c8de4e888171eed280944742e5cd4246017c657a4daf94436f38f10a3076eac990493ea5ff93234662d9c3f7747a2fb2e14fb72bdc74d5d588cbacc0241cd7ac19fd8eead50526974e6ad56ac471cf7c21667b92ded8d714c79f6", "private": "70d5b65d90e6ab58a91b5a75073a347d0863cfc8a96e7db78e098dfb50c4262f401af3c36c43c719", "shared": "", "result": "invalid", "flags": []}, {"tcId": 206, "comment": "Public key uses wrong curve: brainpoolP512r1", "public": "30819b301406072a8648ce3d020106092b240303020801010d0381820004a59f99993f8e3706eb6e104b638e8511c5b08e82344a6538528df723eb38ffe58c70197c8e88371f1d0c550e3bbbfe125c1123d5d0900c8164f9b19dc66fb34f45869a8aac9294587c6bfa261f401883a1844e71b83268e916ed6397c7df9542991437b705ac561cdcd0886409919b69cb12d36399df71e8e2414f1ff17111fb", "private": "70d5b65d90e6ab58a91b5a75073a347d0863cfc8a96e7db78e098dfb50c4262f401af3c36c43c719", "shared": "", "result": "invalid", "flags": []}, {"tcId": 207, "comment": "Public key uses wrong curve: brainpoolP224t1", "public": "3052301406072a8648ce3d020106092b2403030208010106033a000471f4543326fb3cb36fe6471712c30b1e81e5d717bd1f2bf7136793728cba811d4bfdfa99dd5731911ee78d7010f6bc9e71a423d8d6823ec9", "private": "70d5b65d90e6ab58a91b5a75073a347d0863cfc8a96e7db78e098dfb50c4262f401af3c36c43c719", "shared": "", "result": "invalid", "flags": []}, {"tcId": 208, "comment": "Public key uses wrong curve: brainpoolP256t1", "public": "305a301406072a8648ce3d020106092b2403030208010108034200040b2ad117bdc9c839820852ff829d60cb51a6a10f0a72061ca27eead92113b1d98b98919105efdd2376669e8e8361571d3db8736777690a263f04dd2605a65225", "private": "70d5b65d90e6ab58a91b5a75073a347d0863cfc8a96e7db78e098dfb50c4262f401af3c36c43c719", "shared": "", "result": "invalid", "flags": []}, {"tcId": 209, "comment": "Public key uses wrong curve: brainpoolP320t1", "public": "306a301406072a8648ce3d020106092b240303020801010a035200042ddfda6a53e3aa3133a04c2ed9a3c36d3f390710ae781e2975c83701ea352f007b4f7a976ba322419d20d75300cdcf190a58942f9aab229a3dbfee185c3adb6ce9a4907b90cac0fa649c22cc2e17d48f", "private": "70d5b65d90e6ab58a91b5a75073a347d0863cfc8a96e7db78e098dfb50c4262f401af3c36c43c719", "shared": "", "result": "invalid", "flags": []}, {"tcId": 210, "comment": "Public key uses wrong curve: brainpoolP384t1", "public": "307a301406072a8648ce3d020106092b240303020801010c036200041768d368f882df7083b3d53987ba94d8720cbbc443e12700df5eb044d2f5621e8223a3ee3bd71b34f32e3afb5404e85a51cc7eaa1569ae4c555200a1b2df9c4adcdd6ec3d3de9abadf022da337b6f8873bb415e959e57b029850cdbbb38ab417", "private": "70d5b65d90e6ab58a91b5a75073a347d0863cfc8a96e7db78e098dfb50c4262f401af3c36c43c719", "shared": "", "result": "invalid", "flags": []}, {"tcId": 211, "comment": "Public key uses wrong curve: brainpoolP512t1", "public": "30819b301406072a8648ce3d020106092b240303020801010e03818200045ab89b3df599c3c1f1fbe4c15c0aac1ea95dd83f60623c2aaec3b9c31ba68d1f5a637758a7b8631d720f71d756a72c141e41afd508bf7ebcf9b6d1d4764768a66ffd7dd1d605daea2ba841d0a9106a22f1a630f272f906116c27fe3c3ff729d8516aaf0c3555e352ab69dc64f36e2d6320696b366bf0d07aac51ff9db3a480ec", "private": "70d5b65d90e6ab58a91b5a75073a347d0863cfc8a96e7db78e098dfb50c4262f401af3c36c43c719", "shared": "", "result": "invalid", "flags": []}, {"tcId": 212, "comment": "invalid public key", "public": "3042301406072a8648ce3d020106092b2403030208010109032a000227f2f8f1cbc7ee440c167d52782f509b980e1da034c259f079fff653301ae28a88511e501836a7d6", "private": "00cdeaa01757954bf813fafec8356923518e410cc8778d88eac1a07f90b7fb4d20db28670e178b941c", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 213, "comment": "public key is a low order point on twist", "public": "3042301406072a8648ce3d020106092b2403030208010109032a0002cd873725ad5a0cb428ee170bbea7609f8e156a73c4ab18fd32bab9e41af3d36eaa5313a81f74e845", "private": "00b58eeb16a7f22f4562161ed8ea68ac86322d3db6e5d4fec50aae16d01129bf8c4d7530ff4d94a137", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 214, "comment": "public key is a low order point on twist", "public": "3042301406072a8648ce3d020106092b2403030208010109032a00033fef766ef618b774a78e4d81e16e5a9afea06500eb0e366377d839bffceeed1c74e6caecdc9fbb21", "private": "00bdbee13a7eac096b33fe43b8adc4c0e5bab438faa6333570ffdeb05d2b3e95961f9d591f36882027", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 215, "comment": "public key is a low order point on twist", "public": "3042301406072a8648ce3d020106092b2403030208010109032a00023fef766ef618b774a78e4d81e16e5a9afea06500eb0e366377d839bffceeed1c74e6caecdc9fbb21", "private": "00bdbee13a7eac096b33fe43b8adc4c0e5bab438faa6333570ffdeb05d2b3e95961f9d591f36882028", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 216, "comment": "public key is a low order point on twist", "public": "3042301406072a8648ce3d020106092b2403030208010109032a0003cd873725ad5a0cb428ee170bbea7609f8e156a73c4ab18fd32bab9e41af3d36eaa5313a81f74e845", "private": "00b58eeb16a7f22f4562161ed8ea68ac86322d3db6e5d4fec50aae16d01129bf8c4d7530ff4d94a136", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 217, "comment": "long form encoding of length of sequence", "public": "30816a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 218, "comment": "long form encoding of length of sequence", "public": "306b30811406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 219, "comment": "length of sequence contains leading 0", "public": "3082006a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 220, "comment": "length of sequence contains leading 0", "public": "306c3082001406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 221, "comment": "wrong length of sequence", "public": "306b301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 222, "comment": "wrong length of sequence", "public": "3069301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 223, "comment": "wrong length of sequence", "public": "306a301506072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 224, "comment": "wrong length of sequence", "public": "306a301306072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 225, "comment": "uint32 overflow in length of sequence", "public": "3085010000006a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 226, "comment": "uint32 overflow in length of sequence", "public": "306f3085010000001406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 227, "comment": "uint64 overflow in length of sequence", "public": "308901000000000000006a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 228, "comment": "uint64 overflow in length of sequence", "public": "3073308901000000000000001406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 229, "comment": "length of sequence = 2**31 - 1", "public": "30847fffffff301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 230, "comment": "length of sequence = 2**31 - 1", "public": "306e30847fffffff06072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 231, "comment": "length of sequence = 2**32 - 1", "public": "3084ffffffff301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 232, "comment": "length of sequence = 2**32 - 1", "public": "306e3084ffffffff06072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 233, "comment": "length of sequence = 2**40 - 1", "public": "3085ffffffffff301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 234, "comment": "length of sequence = 2**40 - 1", "public": "306f3085ffffffffff06072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 235, "comment": "length of sequence = 2**64 - 1", "public": "3088ffffffffffffffff301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 236, "comment": "length of sequence = 2**64 - 1", "public": "30723088ffffffffffffffff06072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 237, "comment": "incorrect length of sequence", "public": "30ff301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 238, "comment": "incorrect length of sequence", "public": "306a30ff06072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 239, "comment": "indefinite length without termination", "public": "3080301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 240, "comment": "indefinite length without termination", "public": "306a308006072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 241, "comment": "indefinite length without termination", "public": "306a301406802a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 242, "comment": "indefinite length without termination", "public": "306a301406072a8648ce3d020106802b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 243, "comment": "indefinite length without termination", "public": "306a301406072a8648ce3d020106092b240303020801010903800004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 244, "comment": "removing sequence", "public": "", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 245, "comment": "removing sequence", "public": "305403520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 246, "comment": "lonely sequence tag", "public": "30", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 247, "comment": "lonely sequence tag", "public": "30553003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 248, "comment": "appending 0's to sequence", "public": "306c301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc90000", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 249, "comment": "appending 0's to sequence", "public": "306c301606072a8648ce3d020106092b2403030208010109000003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 250, "comment": "prepending 0's to sequence", "public": "306c0000301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 251, "comment": "prepending 0's to sequence", "public": "306c3016000006072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 252, "comment": "appending unused 0's to sequence", "public": "306a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc90000", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 253, "comment": "appending unused 0's to sequence", "public": "306c301406072a8648ce3d020106092b2403030208010109000003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 254, "comment": "appending null value to sequence", "public": "306c301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc90500", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 255, "comment": "appending null value to sequence", "public": "306c301606072a8648ce3d020106092b2403030208010109050003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 256, "comment": "including garbage", "public": "306f498177306a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 257, "comment": "including garbage", "public": "306e2500306a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 258, "comment": "including garbage", "public": "306c306a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc90004deadbeef", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 259, "comment": "including garbage", "public": "306f3019498177301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 260, "comment": "including garbage", "public": "306e30182500301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 261, "comment": "including garbage", "public": "30723016301406072a8648ce3d020106092b24030302080101090004deadbeef03520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 262, "comment": "including garbage", "public": "306f3019260c49817706072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 263, "comment": "including garbage", "public": "306e3018260b250006072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 264, "comment": "including garbage", "public": "3072301c260906072a8648ce3d02010004deadbeef06092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 265, "comment": "including garbage", "public": "306f301906072a8648ce3d0201260e49817706092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 266, "comment": "including garbage", "public": "306e301806072a8648ce3d0201260d250006092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 267, "comment": "including garbage", "public": "3072301c06072a8648ce3d0201260b06092b24030302080101090004deadbeef03520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 268, "comment": "including garbage", "public": "306f301406072a8648ce3d020106092b2403030208010109235749817703520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 269, "comment": "including garbage", "public": "306e301406072a8648ce3d020106092b24030302080101092356250003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 270, "comment": "including garbage", "public": "3072301406072a8648ce3d020106092b2403030208010109235403520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc90004deadbeef", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 271, "comment": "including undefined tags", "public": "3072aa00bb00cd00306a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 272, "comment": "including undefined tags", "public": "3070aa02aabb306a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 273, "comment": "including undefined tags", "public": "3072301caa00bb00cd00301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 274, "comment": "including undefined tags", "public": "3070301aaa02aabb301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 275, "comment": "including undefined tags", "public": "3072301c260faa00bb00cd0006072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 276, "comment": "including undefined tags", "public": "3070301a260daa02aabb06072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 277, "comment": "including undefined tags", "public": "3072301c06072a8648ce3d02012611aa00bb00cd0006092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 278, "comment": "including undefined tags", "public": "3070301a06072a8648ce3d0201260faa02aabb06092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 279, "comment": "including undefined tags", "public": "3072301406072a8648ce3d020106092b2403030208010109235aaa00bb00cd0003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 280, "comment": "including undefined tags", "public": "3070301406072a8648ce3d020106092b24030302080101092358aa02aabb03520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 281, "comment": "truncated length of sequence", "public": "3081", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 282, "comment": "truncated length of sequence", "public": "3056308103520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 283, "comment": "Replacing sequence with NULL", "public": "0500", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 284, "comment": "Replacing sequence with NULL", "public": "3056050003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 285, "comment": "changing tag value of sequence", "public": "2e6a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 286, "comment": "changing tag value of sequence", "public": "2f6a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 287, "comment": "changing tag value of sequence", "public": "316a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 288, "comment": "changing tag value of sequence", "public": "326a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 289, "comment": "changing tag value of sequence", "public": "ff6a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 290, "comment": "changing tag value of sequence", "public": "306a2e1406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 291, "comment": "changing tag value of sequence", "public": "306a2f1406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 292, "comment": "changing tag value of sequence", "public": "306a311406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 293, "comment": "changing tag value of sequence", "public": "306a321406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 294, "comment": "changing tag value of sequence", "public": "306aff1406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 295, "comment": "dropping value of sequence", "public": "3000", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 296, "comment": "dropping value of sequence", "public": "3056300003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 297, "comment": "truncated sequence", "public": "3069301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadb", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 298, "comment": "truncated sequence", "public": "30691406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 299, "comment": "truncated sequence", "public": "3069301306072a8648ce3d020106092b2403030208010103520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 300, "comment": "truncated sequence", "public": "30693013072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 301, "comment": "indefinite length", "public": "3080301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc90000", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 302, "comment": "indefinite length", "public": "306c308006072a8648ce3d020106092b2403030208010109000003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 303, "comment": "indefinite length with truncated delimiter", "public": "3080301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc900", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 304, "comment": "indefinite length with truncated delimiter", "public": "306b308006072a8648ce3d020106092b24030302080101090003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 305, "comment": "indefinite length with additional element", "public": "3080301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc905000000", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 306, "comment": "indefinite length with additional element", "public": "306e308006072a8648ce3d020106092b24030302080101090500000003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 307, "comment": "indefinite length with truncated element", "public": "3080301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9060811220000", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 308, "comment": "indefinite length with truncated element", "public": "3070308006072a8648ce3d020106092b240303020801010906081122000003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 309, "comment": "indefinite length with garbage", "public": "3080301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc90000fe02beef", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 310, "comment": "indefinite length with garbage", "public": "3070308006072a8648ce3d020106092b24030302080101090000fe02beef03520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 311, "comment": "indefinite length with nonempty EOC", "public": "3080301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc90002beef", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 312, "comment": "indefinite length with nonempty EOC", "public": "306e308006072a8648ce3d020106092b24030302080101090002beef03520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 313, "comment": "prepend empty sequence", "public": "306c3000301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 314, "comment": "prepend empty sequence", "public": "306c3016300006072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 315, "comment": "append empty sequence", "public": "306c301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc93000", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 316, "comment": "append empty sequence", "public": "306c301606072a8648ce3d020106092b2403030208010109300003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 317, "comment": "append garbage with high tag number", "public": "306d301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9bf7f00", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 318, "comment": "append garbage with high tag number", "public": "306d301706072a8648ce3d020106092b2403030208010109bf7f0003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 319, "comment": "sequence of sequence", "public": "306c306a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 320, "comment": "sequence of sequence", "public": "306c3016301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 321, "comment": "truncated sequence: removed last 1 elements", "public": "3016301406072a8648ce3d020106092b2403030208010109", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 322, "comment": "truncated sequence: removed last 1 elements", "public": "305f300906072a8648ce3d020103520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 323, "comment": "repeating element in sequence", "public": "3081be301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 324, "comment": "repeating element in sequence", "public": "3075301f06072a8648ce3d020106092b240303020801010906092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 325, "comment": "long form encoding of length of oid", "public": "306b30150681072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 326, "comment": "long form encoding of length of oid", "public": "306b301506072a8648ce3d02010681092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 327, "comment": "length of oid contains leading 0", "public": "306c3016068200072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 328, "comment": "length of oid contains leading 0", "public": "306c301606072a8648ce3d0201068200092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 329, "comment": "wrong length of oid", "public": "306a301406082a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 330, "comment": "wrong length of oid", "public": "306a301406062a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 331, "comment": "wrong length of oid", "public": "306a301406072a8648ce3d0201060a2b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 332, "comment": "wrong length of oid", "public": "306a301406072a8648ce3d020106082b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 333, "comment": "uint32 overflow in length of oid", "public": "306f3019068501000000072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 334, "comment": "uint32 overflow in length of oid", "public": "306f301906072a8648ce3d0201068501000000092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 335, "comment": "uint64 overflow in length of oid", "public": "3073301d06890100000000000000072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 336, "comment": "uint64 overflow in length of oid", "public": "3073301d06072a8648ce3d020106890100000000000000092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 337, "comment": "length of oid = 2**31 - 1", "public": "306e301806847fffffff2a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 338, "comment": "length of oid = 2**31 - 1", "public": "306e301806072a8648ce3d020106847fffffff2b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 339, "comment": "length of oid = 2**32 - 1", "public": "306e30180684ffffffff2a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 340, "comment": "length of oid = 2**32 - 1", "public": "306e301806072a8648ce3d02010684ffffffff2b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 341, "comment": "length of oid = 2**40 - 1", "public": "306f30190685ffffffffff2a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 342, "comment": "length of oid = 2**40 - 1", "public": "306f301906072a8648ce3d02010685ffffffffff2b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 343, "comment": "length of oid = 2**64 - 1", "public": "3072301c0688ffffffffffffffff2a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 344, "comment": "length of oid = 2**64 - 1", "public": "3072301c06072a8648ce3d02010688ffffffffffffffff2b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 345, "comment": "incorrect length of oid", "public": "306a301406ff2a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 346, "comment": "incorrect length of oid", "public": "306a301406072a8648ce3d020106ff2b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 347, "comment": "removing oid", "public": "3061300b06092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 348, "comment": "lonely oid tag", "public": "3062300c0606092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 349, "comment": "lonely oid tag", "public": "3060300a06072a8648ce3d02010603520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 350, "comment": "appending 0's to oid", "public": "306c301606092a8648ce3d0201000006092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 351, "comment": "appending 0's to oid", "public": "306c301606072a8648ce3d0201060b2b2403030208010109000003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 352, "comment": "prepending 0's to oid", "public": "306c3016060900002a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 353, "comment": "prepending 0's to oid", "public": "306c301606072a8648ce3d0201060b00002b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 354, "comment": "appending unused 0's to oid", "public": "306c301606072a8648ce3d0201000006092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 355, "comment": "appending null value to oid", "public": "306c301606092a8648ce3d0201050006092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 356, "comment": "appending null value to oid", "public": "306c301606072a8648ce3d0201060b2b2403030208010109050003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 357, "comment": "truncated length of oid", "public": "3063300d068106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 358, "comment": "truncated length of oid", "public": "3061300b06072a8648ce3d0201068103520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 359, "comment": "Replacing oid with NULL", "public": "3063300d050006092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 360, "comment": "Replacing oid with NULL", "public": "3061300b06072a8648ce3d0201050003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 361, "comment": "changing tag value of oid", "public": "306a301404072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 362, "comment": "changing tag value of oid", "public": "306a301405072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 363, "comment": "changing tag value of oid", "public": "306a301407072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 364, "comment": "changing tag value of oid", "public": "306a301408072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 365, "comment": "changing tag value of oid", "public": "306a3014ff072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 366, "comment": "changing tag value of oid", "public": "306a301406072a8648ce3d020104092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 367, "comment": "changing tag value of oid", "public": "306a301406072a8648ce3d020105092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 368, "comment": "changing tag value of oid", "public": "306a301406072a8648ce3d020107092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 369, "comment": "changing tag value of oid", "public": "306a301406072a8648ce3d020108092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 370, "comment": "changing tag value of oid", "public": "306a301406072a8648ce3d0201ff092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 371, "comment": "dropping value of oid", "public": "3063300d060006092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 372, "comment": "dropping value of oid", "public": "3061300b06072a8648ce3d0201060003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 373, "comment": "modify first byte of oid", "public": "306a30140607288648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 374, "comment": "modify first byte of oid", "public": "306a301406072a8648ce3d0201060929240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 375, "comment": "modify last byte of oid", "public": "306a301406072a8648ce3d028106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 376, "comment": "modify last byte of oid", "public": "306a301406072a8648ce3d020106092b240303020801018903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 377, "comment": "truncated oid", "public": "3069301306062a8648ce3d0206092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 378, "comment": "truncated oid", "public": "3069301306068648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 379, "comment": "truncated oid", "public": "3069301306072a8648ce3d020106082b2403030208010103520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 380, "comment": "truncated oid", "public": "3069301306072a8648ce3d02010608240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 381, "comment": "wrong oid", "public": "3068301206052b0e03021a06092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 382, "comment": "wrong oid", "public": "306c3016060960864801650304020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 383, "comment": "wrong oid", "public": "3066301006072a8648ce3d020106052b0e03021a03520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 384, "comment": "wrong oid", "public": "306a301406072a8648ce3d0201060960864801650304020103520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 385, "comment": "longer oid", "public": "306b301506082a8648ce3d02010106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 386, "comment": "longer oid", "public": "306b301506072a8648ce3d0201060a2b24030302080101090103520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 387, "comment": "oid with modified node", "public": "306a301406072a8648ce3d021106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 388, "comment": "oid with modified node", "public": "306e3018060b2a8648ce3d02888080800106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 389, "comment": "oid with modified node", "public": "306a301406072a8648ce3d020106092b240303020801011903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 390, "comment": "oid with modified node", "public": "306e301806072a8648ce3d0201060d2b24030302080101888080800903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 391, "comment": "large integer in oid", "public": "3073301d06102a8648ce3d028280808080808080800106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 392, "comment": "large integer in oid", "public": "3073301d06072a8648ce3d020106122b240303020801018280808080808080800903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 393, "comment": "oid with invalid node", "public": "306b301506082a8648ce3d0201e006092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 394, "comment": "oid with invalid node", "public": "306b301506082a808648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 395, "comment": "oid with invalid node", "public": "306b301506072a8648ce3d0201060a2b2403030208010109e003520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 396, "comment": "oid with invalid node", "public": "306b301506072a8648ce3d0201060a2b80240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 397, "comment": "long form encoding of length of bit string", "public": "306b301406072a8648ce3d020106092b24030302080101090381520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 398, "comment": "length of bit string contains leading 0", "public": "306c301406072a8648ce3d020106092b2403030208010109038200520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 399, "comment": "wrong length of bit string", "public": "306a301406072a8648ce3d020106092b240303020801010903530004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 400, "comment": "wrong length of bit string", "public": "306a301406072a8648ce3d020106092b240303020801010903510004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 401, "comment": "uint32 overflow in length of bit string", "public": "306f301406072a8648ce3d020106092b2403030208010109038501000000520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 402, "comment": "uint64 overflow in length of bit string", "public": "3073301406072a8648ce3d020106092b240303020801010903890100000000000000520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 403, "comment": "length of bit string = 2**31 - 1", "public": "306e301406072a8648ce3d020106092b240303020801010903847fffffff0004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 404, "comment": "length of bit string = 2**32 - 1", "public": "306e301406072a8648ce3d020106092b24030302080101090384ffffffff0004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 405, "comment": "length of bit string = 2**40 - 1", "public": "306f301406072a8648ce3d020106092b24030302080101090385ffffffffff0004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 406, "comment": "length of bit string = 2**64 - 1", "public": "3072301406072a8648ce3d020106092b24030302080101090388ffffffffffffffff0004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 407, "comment": "incorrect length of bit string", "public": "306a301406072a8648ce3d020106092b240303020801010903ff0004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 408, "comment": "lonely bit string tag", "public": "3017301406072a8648ce3d020106092b240303020801010903", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 409, "comment": "appending 0's to bit string", "public": "306c301406072a8648ce3d020106092b240303020801010903540004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc90000", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 410, "comment": "prepending 0's to bit string", "public": "306c301406072a8648ce3d020106092b2403030208010109035400000004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 411, "comment": "appending null value to bit string", "public": "306c301406072a8648ce3d020106092b240303020801010903540004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc90500", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 412, "comment": "truncated length of bit string", "public": "3018301406072a8648ce3d020106092b24030302080101090381", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 413, "comment": "Replacing bit string with NULL", "public": "3018301406072a8648ce3d020106092b24030302080101090500", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 414, "comment": "changing tag value of bit string", "public": "306a301406072a8648ce3d020106092b240303020801010901520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 415, "comment": "changing tag value of bit string", "public": "306a301406072a8648ce3d020106092b240303020801010902520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 416, "comment": "changing tag value of bit string", "public": "306a301406072a8648ce3d020106092b240303020801010904520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 417, "comment": "changing tag value of bit string", "public": "306a301406072a8648ce3d020106092b240303020801010905520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 418, "comment": "changing tag value of bit string", "public": "306a301406072a8648ce3d020106092b2403030208010109ff520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 419, "comment": "dropping value of bit string", "public": "3018301406072a8648ce3d020106092b24030302080101090300", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 420, "comment": "modify first byte of bit string", "public": "306a301406072a8648ce3d020106092b240303020801010903520204ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 421, "comment": "modify last byte of bit string", "public": "306a301406072a8648ce3d020106092b240303020801010903520004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadb49", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 422, "comment": "truncated bit string", "public": "3069301406072a8648ce3d020106092b240303020801010903510004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadb", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 423, "comment": "truncated bit string", "public": "3069301406072a8648ce3d020106092b2403030208010109035104ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 424, "comment": "declaring bits as unused in bit string", "public": "306a301406072a8648ce3d020106092b240303020801010903520104ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 425, "comment": "unused bits in bit string", "public": "306e301406072a8648ce3d020106092b240303020801010903562004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc901020304", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 426, "comment": "unused bits in empty bit-string", "public": "3019301406072a8648ce3d020106092b2403030208010109030103", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 427, "comment": "128 unused bits", "public": "306a301406072a8648ce3d020106092b240303020801010903528004ac311df3fe43c502239aad07f7db6a5ec622612069c225a626ed934f3e2b8be01b1153f2b30093b699fcbf3fd40498d79e3d43662c4971e5ffd7a5dae20393aead656246f11447920dcbdb8350fadbc9", "private": "0f1ba6ba3785be0466f050c17d4c935acd4694ed1d283c8df7a2cfb00f2e92d159213a5d5e5e25d2", "shared": "50e75ce38235c8052e159fb31421a486c9435207e36ac68da9e0ebf0df0b94aafdd57b25c6ee2fdf", "result": "acceptable", "flags": ["InvalidAsn"]}]}]}