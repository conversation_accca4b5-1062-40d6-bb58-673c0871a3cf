// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/SoftwareVideoDecoderFactory

#ifndef org_webrtc_SoftwareVideoDecoderFactory_JNI
#define org_webrtc_SoftwareVideoDecoderFactory_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_SoftwareVideoDecoderFactory[];
const char kClassPath_org_webrtc_SoftwareVideoDecoderFactory[] =
    "org/webrtc/SoftwareVideoDecoderFactory";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_SoftwareVideoDecoderFactory_clazz(nullptr);
#ifndef org_webrtc_SoftwareVideoDecoderFactory_clazz_defined
#define org_webrtc_SoftwareVideoDecoderFactory_clazz_defined
inline jclass org_webrtc_SoftwareVideoDecoderFactory_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_SoftwareVideoDecoderFactory,
      &g_org_webrtc_SoftwareVideoDecoderFactory_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {

static jlong JNI_SoftwareVideoDecoderFactory_Create(JNIEnv* env, jlong factory,
    jlong webrtcEnvRef,
    const jni_zero::JavaParamRef<jobject>& info);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_SoftwareVideoDecoderFactory_nativeCreate(
    JNIEnv* env,
    jclass jcaller,
    jlong factory,
    jlong webrtcEnvRef,
    jobject info) {
  return JNI_SoftwareVideoDecoderFactory_Create(env, factory, webrtcEnvRef,
      jni_zero::JavaParamRef<jobject>(env, info));
}

static jlong JNI_SoftwareVideoDecoderFactory_CreateFactory(JNIEnv* env);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_SoftwareVideoDecoderFactory_nativeCreateFactory(
    JNIEnv* env,
    jclass jcaller) {
  return JNI_SoftwareVideoDecoderFactory_CreateFactory(env);
}

static jni_zero::ScopedJavaLocalRef<jobject>
    JNI_SoftwareVideoDecoderFactory_GetSupportedCodecs(JNIEnv* env, jlong factory);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_SoftwareVideoDecoderFactory_nativeGetSupportedCodecs(
    JNIEnv* env,
    jclass jcaller,
    jlong factory) {
  return JNI_SoftwareVideoDecoderFactory_GetSupportedCodecs(env, factory).Release();
}

static jboolean JNI_SoftwareVideoDecoderFactory_IsSupported(JNIEnv* env, jlong factory,
    const jni_zero::JavaParamRef<jobject>& info);

JNI_BOUNDARY_EXPORT jboolean Java_org_webrtc_SoftwareVideoDecoderFactory_nativeIsSupported(
    JNIEnv* env,
    jclass jcaller,
    jlong factory,
    jobject info) {
  return JNI_SoftwareVideoDecoderFactory_IsSupported(env, factory,
      jni_zero::JavaParamRef<jobject>(env, info));
}


}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_SoftwareVideoDecoderFactory_JNI
