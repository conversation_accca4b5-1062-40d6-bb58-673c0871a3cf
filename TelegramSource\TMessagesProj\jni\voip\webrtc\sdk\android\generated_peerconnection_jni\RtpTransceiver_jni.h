// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/RtpTransceiver

#ifndef org_webrtc_RtpTransceiver_JNI
#define org_webrtc_RtpTransceiver_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_RtpTransceiver[];
const char kClassPath_org_webrtc_RtpTransceiver[] = "org/webrtc/RtpTransceiver";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_RtpTransceiver_00024RtpTransceiverDirection[];
const char kClassPath_org_webrtc_RtpTransceiver_00024RtpTransceiverDirection[] =
    "org/webrtc/RtpTransceiver$RtpTransceiverDirection";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_RtpTransceiver_00024RtpTransceiverInit[];
const char kClassPath_org_webrtc_RtpTransceiver_00024RtpTransceiverInit[] =
    "org/webrtc/RtpTransceiver$RtpTransceiverInit";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_RtpTransceiver_clazz(nullptr);
#ifndef org_webrtc_RtpTransceiver_clazz_defined
#define org_webrtc_RtpTransceiver_clazz_defined
inline jclass org_webrtc_RtpTransceiver_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_RtpTransceiver,
      &g_org_webrtc_RtpTransceiver_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_RtpTransceiver_00024RtpTransceiverDirection_clazz(nullptr);
#ifndef org_webrtc_RtpTransceiver_00024RtpTransceiverDirection_clazz_defined
#define org_webrtc_RtpTransceiver_00024RtpTransceiverDirection_clazz_defined
inline jclass org_webrtc_RtpTransceiver_00024RtpTransceiverDirection_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env,
      kClassPath_org_webrtc_RtpTransceiver_00024RtpTransceiverDirection,
      &g_org_webrtc_RtpTransceiver_00024RtpTransceiverDirection_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_RtpTransceiver_00024RtpTransceiverInit_clazz(nullptr);
#ifndef org_webrtc_RtpTransceiver_00024RtpTransceiverInit_clazz_defined
#define org_webrtc_RtpTransceiver_00024RtpTransceiverInit_clazz_defined
inline jclass org_webrtc_RtpTransceiver_00024RtpTransceiverInit_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_RtpTransceiver_00024RtpTransceiverInit,
      &g_org_webrtc_RtpTransceiver_00024RtpTransceiverInit_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {

static jni_zero::ScopedJavaLocalRef<jobject> JNI_RtpTransceiver_CurrentDirection(JNIEnv* env, jlong
    rtpTransceiver);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_RtpTransceiver_nativeCurrentDirection(
    JNIEnv* env,
    jclass jcaller,
    jlong rtpTransceiver) {
  return JNI_RtpTransceiver_CurrentDirection(env, rtpTransceiver).Release();
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_RtpTransceiver_Direction(JNIEnv* env, jlong
    rtpTransceiver);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_RtpTransceiver_nativeDirection(
    JNIEnv* env,
    jclass jcaller,
    jlong rtpTransceiver) {
  return JNI_RtpTransceiver_Direction(env, rtpTransceiver).Release();
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_RtpTransceiver_GetMediaType(JNIEnv* env, jlong
    rtpTransceiver);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_RtpTransceiver_nativeGetMediaType(
    JNIEnv* env,
    jclass jcaller,
    jlong rtpTransceiver) {
  return JNI_RtpTransceiver_GetMediaType(env, rtpTransceiver).Release();
}

static jni_zero::ScopedJavaLocalRef<jstring> JNI_RtpTransceiver_GetMid(JNIEnv* env, jlong
    rtpTransceiver);

JNI_BOUNDARY_EXPORT jstring Java_org_webrtc_RtpTransceiver_nativeGetMid(
    JNIEnv* env,
    jclass jcaller,
    jlong rtpTransceiver) {
  return JNI_RtpTransceiver_GetMid(env, rtpTransceiver).Release();
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_RtpTransceiver_GetReceiver(JNIEnv* env, jlong
    rtpTransceiver);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_RtpTransceiver_nativeGetReceiver(
    JNIEnv* env,
    jclass jcaller,
    jlong rtpTransceiver) {
  return JNI_RtpTransceiver_GetReceiver(env, rtpTransceiver).Release();
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_RtpTransceiver_GetSender(JNIEnv* env, jlong
    rtpTransceiver);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_RtpTransceiver_nativeGetSender(
    JNIEnv* env,
    jclass jcaller,
    jlong rtpTransceiver) {
  return JNI_RtpTransceiver_GetSender(env, rtpTransceiver).Release();
}

static void JNI_RtpTransceiver_SetCodecPreferences(JNIEnv* env, jlong rtpTransceiver,
    const jni_zero::JavaParamRef<jobject>& codecs);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_RtpTransceiver_nativeSetCodecPreferences(
    JNIEnv* env,
    jclass jcaller,
    jlong rtpTransceiver,
    jobject codecs) {
  return JNI_RtpTransceiver_SetCodecPreferences(env, rtpTransceiver,
      jni_zero::JavaParamRef<jobject>(env, codecs));
}

static jboolean JNI_RtpTransceiver_SetDirection(JNIEnv* env, jlong rtpTransceiver,
    const jni_zero::JavaParamRef<jobject>& rtpTransceiverDirection);

JNI_BOUNDARY_EXPORT jboolean Java_org_webrtc_RtpTransceiver_nativeSetDirection(
    JNIEnv* env,
    jclass jcaller,
    jlong rtpTransceiver,
    jobject rtpTransceiverDirection) {
  return JNI_RtpTransceiver_SetDirection(env, rtpTransceiver, jni_zero::JavaParamRef<jobject>(env,
      rtpTransceiverDirection));
}

static void JNI_RtpTransceiver_StopInternal(JNIEnv* env, jlong rtpTransceiver);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_RtpTransceiver_nativeStopInternal(
    JNIEnv* env,
    jclass jcaller,
    jlong rtpTransceiver) {
  return JNI_RtpTransceiver_StopInternal(env, rtpTransceiver);
}

static void JNI_RtpTransceiver_StopStandard(JNIEnv* env, jlong rtpTransceiver);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_RtpTransceiver_nativeStopStandard(
    JNIEnv* env,
    jclass jcaller,
    jlong rtpTransceiver) {
  return JNI_RtpTransceiver_StopStandard(env, rtpTransceiver);
}

static jboolean JNI_RtpTransceiver_Stopped(JNIEnv* env, jlong rtpTransceiver);

JNI_BOUNDARY_EXPORT jboolean Java_org_webrtc_RtpTransceiver_nativeStopped(
    JNIEnv* env,
    jclass jcaller,
    jlong rtpTransceiver) {
  return JNI_RtpTransceiver_Stopped(env, rtpTransceiver);
}


static std::atomic<jmethodID> g_org_webrtc_RtpTransceiver_Constructor1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RtpTransceiver_Constructor(JNIEnv* env, jlong
    nativeRtpTransceiver) {
  jclass clazz = org_webrtc_RtpTransceiver_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_RtpTransceiver_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(J)V",
          &g_org_webrtc_RtpTransceiver_Constructor1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, nativeRtpTransceiver);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpTransceiver_dispose0(nullptr);
static void Java_RtpTransceiver_dispose(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpTransceiver_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpTransceiver_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "dispose",
          "()V",
          &g_org_webrtc_RtpTransceiver_dispose0);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpTransceiver_00024RtpTransceiverDirection_fromNativeIndex1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RtpTransceiverDirection_fromNativeIndex(JNIEnv*
    env, JniIntWrapper nativeIndex) {
  jclass clazz = org_webrtc_RtpTransceiver_00024RtpTransceiverDirection_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_RtpTransceiver_00024RtpTransceiverDirection_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "fromNativeIndex",
          "(I)Lorg/webrtc/RtpTransceiver$RtpTransceiverDirection;",
          &g_org_webrtc_RtpTransceiver_00024RtpTransceiverDirection_fromNativeIndex1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(nativeIndex));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpTransceiver_00024RtpTransceiverDirection_getNativeIndex0(nullptr);
static jint Java_RtpTransceiverDirection_getNativeIndex(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpTransceiver_00024RtpTransceiverDirection_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpTransceiver_00024RtpTransceiverDirection_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getNativeIndex",
          "()I",
          &g_org_webrtc_RtpTransceiver_00024RtpTransceiverDirection_getNativeIndex0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpTransceiver_00024RtpTransceiverInit_getDirectionNativeIndex0(nullptr);
static jint Java_RtpTransceiverInit_getDirectionNativeIndex(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpTransceiver_00024RtpTransceiverInit_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpTransceiver_00024RtpTransceiverInit_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getDirectionNativeIndex",
          "()I",
          &g_org_webrtc_RtpTransceiver_00024RtpTransceiverInit_getDirectionNativeIndex0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpTransceiver_00024RtpTransceiverInit_getSendEncodings0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RtpTransceiverInit_getSendEncodings(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpTransceiver_00024RtpTransceiverInit_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpTransceiver_00024RtpTransceiverInit_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getSendEncodings",
          "()Ljava/util/List;",
          &g_org_webrtc_RtpTransceiver_00024RtpTransceiverInit_getSendEncodings0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpTransceiver_00024RtpTransceiverInit_getStreamIds0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RtpTransceiverInit_getStreamIds(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpTransceiver_00024RtpTransceiverInit_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpTransceiver_00024RtpTransceiverInit_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getStreamIds",
          "()Ljava/util/List;",
          &g_org_webrtc_RtpTransceiver_00024RtpTransceiverInit_getStreamIds0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_RtpTransceiver_JNI
