import requests
import time
import threading
from typing import Any, Dict, Optional, List, Tuple
from collections import deque
from android_utils import log, run_on_ui_thread
from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from client_utils import run_on_queue, get_last_fragment, send_message
from markdown_utils import parse_markdown
from ui.settings import Header, Switch, Divider, Input, Selector, Text
from ui.alert import AlertDialogBuilder
from org.telegram.ui.ActionBar import AlertDialog

__id__ = "checkhost_network_by_mi<PERSON><PERSON><PERSON><PERSON><PERSON>"
__name__ = "CheckHost Network"
__version__ = "2.0.0"
__description__ = "Проверка IP, сайтов, DNS, TCP, UDP, HTTP соединений через check-host.net API"
__author__ = "@mihailk<PERSON><PERSON>ski & @mishabotov"
__min_version__ = "11.9.1"
__icon__ = "Developer/19"

API_BASE_URL = "https://check-host.net"
API_HEADERS = {
    "Accept": "application/json",
    "User-Agent": "CheckHost-exteraGram-Plugin/2.0.0-@mishabotov"
}

progress_dialog = None

class InfoAlertPopup:
    def __init__(self, title: str, text: str):
        self.text = text
        self.title = title
        self._alert_builder_instance = None

    def show_alert(self):
        last_fragment = get_last_fragment()
        if not last_fragment or not last_fragment.getParentActivity():
            log("[InfoAlertPopup] Не удалось получить контекст для показа диалога")
            return

        if (self._alert_builder_instance
            and self._alert_builder_instance.get_dialog()
            and self._alert_builder_instance.get_dialog().isShowing()):
            log("[InfoAlertPopup] Диалог уже отображается")
            return

        try:
            log("[InfoAlertPopup] Создание информационного диалога...")
            context = last_fragment.getParentActivity()
            self._alert_builder_instance = AlertDialogBuilder(context, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
            self._alert_builder_instance.set_title(self.title)
            self._alert_builder_instance.set_message(self.text)
            self._alert_builder_instance.create()
            self._alert_builder_instance.set_cancelable(True)
            self._alert_builder_instance.set_canceled_on_touch_outside(True)
            self._alert_builder_instance.set_positive_button("Закрыть")
            self._alert_builder_instance.show()
            log("[InfoAlertPopup] Диалог показан")
        except Exception as e:
            log(f"[InfoAlertPopup] Ошибка при создании диалога: {e}")

    def dismiss_dialog(self):
        if (self._alert_builder_instance
            and self._alert_builder_instance.get_dialog()
            and self._alert_builder_instance.get_dialog().isShowing()):
            self._alert_builder_instance.dismiss()
            self._alert_builder_instance = None
            log("[InfoAlertPopup] Диалог закрыт")

class CheckHostAPI:
    def __init__(self, max_nodes: int = 3, timeout: int = 10):
        self.max_nodes = max_nodes
        self.timeout = timeout
        self.session = requests.Session()
        self.session.headers.update(API_HEADERS)

    def _make_request(self, url: str, params: Dict = None) -> Optional[Dict]:
        try:
            response = self.session.get(url, params=params, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            log(f"[CheckHostAPI] Ошибка запроса: {e}")
            return None
        except Exception as e:
            log(f"[CheckHostAPI] Неожиданная ошибка: {e}")
            return None

    def start_check(self, check_type: str, host: str, nodes: List[str] = None) -> Optional[Dict]:
        url = f"{API_BASE_URL}/check-{check_type}"
        params = {
            "host": host,
            "max_nodes": self.max_nodes
        }

        if nodes:
            params_list = [(k, v) for k, v in params.items()]
            for node in nodes:
                params_list.append(("node", node))
            return self._make_request_with_params_list(url, params_list)

        return self._make_request(url, params)

    def _make_request_with_params_list(self, url: str, params_list: List[Tuple] = None) -> Optional[Dict]:
        try:
            response = self.session.get(url, params=params_list, timeout=self.timeout)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            log(f"[CheckHostAPI] Ошибка запроса: {e}")
            return None
        except Exception as e:
            log(f"[CheckHostAPI] Неожиданная ошибка: {e}")
            return None

    def get_result(self, request_id: str, extended: bool = False) -> Optional[Dict]:
        endpoint = "check-result-extended" if extended else "check-result"
        url = f"{API_BASE_URL}/{endpoint}/{request_id}"
        return self._make_request(url)

    def get_nodes(self) -> Optional[Dict]:
        url = f"{API_BASE_URL}/nodes/hosts"
        return self._make_request(url)

class CheckHostPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.api = None
        self.cache = {}
        self.cache_duration = 300
        self.request_queue = deque()
        self.is_processing = False
        self.info_alert_popup = None

    def on_plugin_load(self):
        self.load_config()
        self.add_on_send_message_hook()
        log("[CheckHostPlugin] Плагин загружен! Powered by @mishabotov")

    def load_config(self):
        max_nodes = int(self.get_setting("max_nodes", 3))
        timeout = int(self.get_setting("timeout", 10))
        self.api = CheckHostAPI(max_nodes=max_nodes, timeout=timeout)
        self.cache_duration = int(self.get_setting("cache_duration", 300))

    def _on_max_nodes_change(self, new_value: str):
        try:
            max_nodes = int(new_value)
            if 1 <= max_nodes <= 20:
                self.api.max_nodes = max_nodes
                log(f"[CheckHostPlugin] Максимальное количество узлов изменено на: {max_nodes}")
            else:
                log(f"[CheckHostPlugin] Неверное значение max_nodes: {max_nodes}. Должно быть от 1 до 20")
        except ValueError:
            log(f"[CheckHostPlugin] Неверный формат max_nodes: {new_value}")

    def _on_timeout_change(self, new_value: str):
        try:
            timeout = int(new_value)
            if timeout > 0:
                self.api.timeout = timeout
                log(f"[CheckHostPlugin] Таймаут изменен на: {timeout}")
            else:
                log(f"[CheckHostPlugin] Неверное значение timeout: {timeout}. Должно быть больше 0")
        except ValueError:
            log(f"[CheckHostPlugin] Неверный формат timeout: {new_value}")

    def _on_cache_duration_change(self, new_value: str):
        try:
            cache_duration = int(new_value)
            if cache_duration >= 0:
                self.cache_duration = cache_duration
                log(f"[CheckHostPlugin] Время кэширования изменено на: {cache_duration}")
            else:
                log(f"[CheckHostPlugin] Неверное значение cache_duration: {cache_duration}. Должно быть >= 0")
        except ValueError:
            log(f"[CheckHostPlugin] Неверный формат cache_duration: {new_value}")

    def _handle_show_commands_info(self, view):
        commands_text = """🌐 CheckHost Network Commands

• .ping <host> - Ping проверка
  Пример: .ping google.com

• .http <url> - HTTP проверка
  Пример: .http https://google.com

• .tcp <host:port> - TCP проверка
  Пример: .tcp google.com:80

• .dns <domain> - DNS проверка
  Пример: .dns google.com

• .udp <host:port> - UDP проверка
  Пример: .udp *******:53

• .nodes - Список доступных узлов
  Показывает все узлы check-host.net

📝 Примечания:
- Для TCP/UDP проверок обязательно указывайте порт
- HTTP URL может быть без протокола (добавится http://)
- Результаты кэшируются согласно настройкам

💡 Плагин создан при поддержке канала @mishabotov
🔧 Используется API check-host.net для мониторинга"""

        self.info_alert_popup = InfoAlertPopup("Справка по командам", commands_text)
        self.info_alert_popup.show_alert()

    def create_settings(self):
        return [
            Header(text="🌐 CheckHost Network Settings"),
            Input(
                key="max_nodes",
                text="Количество узлов",
                icon="filled_location_forever",
                default="3",
                subtext="Количество узлов для проверки (1-20)",
                on_change=self._on_max_nodes_change
            ),
            Input(
                key="timeout",
                text="Таймаут запроса (сек)",
                icon="filled_premium_away",
                default="10",
                subtext="Время ожидания ответа от API",
                on_change=self._on_timeout_change
            ),
            Input(
                key="cache_duration",
                text="Время кэширования (сек)",
                icon="msg_delete_auto",
                default="120",
                subtext="Время хранения результатов в кэше",
                on_change=self._on_cache_duration_change
            ),
            Switch(
                key="show_extended_info",
                text="Расширенная инфо-ция",
                icon="msg_filled_datausage",
                default=True,
                subtext="Отображать дополнительные детали проверок"
            ),
            Switch(
                key="show_node_locations",
                text="Местоположение узлов",
                icon="msg_location",
                default=True,
                subtext="Отображать страну и город узлов"
            ),
            Switch(
                key="show_promo_messages",
                text="Реклама?",
                icon="menu_feature_cover",
                default=True,
                subtext="Отображать упоминания канала @mishabotov в результатах"
            ),
            Divider(text="📚 Справка и информация"),
            Text(
                text="📋 Показать доступные команды",
                icon="msg_info",
                on_click=self._handle_show_commands_info
            ),
            Text(
                text="💡 Канал разработчика",
                icon="msg_channel",
                on_click=lambda v: self._show_channel_info()
            ),
        ]

    def _show_channel_info(self):
        channel_info = """🚀 Канал разработчика @mishabotov

📢 Здесь вы найдете:
• Новые плагины для exteraGram
• Обновления и исправления
• Полезные советы
• Техническую поддержку

🔗 Присоединяйтесь: @mishabotov

💡 CheckHost Network Plugin v2.0.0
Создан с использованием check-host.net API
Поддержка: ping, http, tcp, dns, udp проверки"""

        popup = InfoAlertPopup("О разработчике", channel_info)
        popup.show_alert()

    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        if not hasattr(params, 'message') or not isinstance(params.message, str):
            return HookResult()

        msg = params.message.strip()

        commands = {
            '.ping': self._handle_ping_command,
            '.http': self._handle_http_command,
            '.tcp': self._handle_tcp_command,
            '.dns': self._handle_dns_command,
            '.udp': self._handle_udp_command,
            '.nodes': self._handle_nodes_command
        }

        for command, handler in commands.items():
            if msg.startswith(command):
                return handler(msg, params)

        return HookResult()

    def _parse_command(self, msg: str, command: str) -> Tuple[str, List[str]]:
        parts = msg[len(command):].strip().split()
        if not parts:
            return "", []

        host = parts[0]
        args = parts[1:] if len(parts) > 1 else []
        return host, args

    def _validate_host(self, host: str, check_type: str) -> Tuple[bool, str]:
        if not host:
            return False, f"Укажите хост для {check_type} проверки"

        if check_type in ['tcp', 'udp']:
            if ':' not in host:
                return False, f"Для {check_type} проверки укажите порт (например: google.com:80)"

            try:
                host_part, port_part = host.rsplit(':', 1)
                port = int(port_part)
                if not (1 <= port <= 65535):
                    return False, "Порт должен быть в диапазоне 1-65535"
            except ValueError:
                return False, "Неверный формат порта"

        elif check_type == 'http':
            if not (host.startswith('http://') or host.startswith('https://')):
                host = 'http://' + host

        return True, host

    def _handle_ping_command(self, msg: str, params: Any) -> HookResult:
        host, args = self._parse_command(msg, '.ping')
        valid, result = self._validate_host(host, 'ping')

        if not valid:
            params.message = f"❌ {result}\nИспользование: .ping <host>"
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        return self._execute_check('ping', result, params, args)

    def _handle_http_command(self, msg: str, params: Any) -> HookResult:
        host, args = self._parse_command(msg, '.http')
        valid, result = self._validate_host(host, 'http')

        if not valid:
            params.message = f"❌ {result}\nИспользование: .http <url>"
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        return self._execute_check('http', result, params, args)

    def _handle_tcp_command(self, msg: str, params: Any) -> HookResult:
        host, args = self._parse_command(msg, '.tcp')
        valid, result = self._validate_host(host, 'tcp')

        if not valid:
            params.message = f"❌ {result}\nИспользование: .tcp <host:port>"
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        return self._execute_check('tcp', result, params, args)

    def _handle_dns_command(self, msg: str, params: Any) -> HookResult:
        host, args = self._parse_command(msg, '.dns')
        valid, result = self._validate_host(host, 'dns')

        if not valid:
            params.message = f"❌ {result}\nИспользование: .dns <domain>"
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        return self._execute_check('dns', result, params, args)

    def _handle_udp_command(self, msg: str, params: Any) -> HookResult:
        host, args = self._parse_command(msg, '.udp')
        valid, result = self._validate_host(host, 'udp')

        if not valid:
            params.message = f"❌ {result}\nИспользование: .udp <host:port>"
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        return self._execute_check('udp', result, params, args)

    def _handle_nodes_command(self, msg: str, params: Any) -> HookResult:
        return self._execute_nodes_request(params)

    def _execute_check(self, check_type: str, host: str, params: Any, args: List[str]) -> HookResult:
        cache_key = f"{check_type}:{host}"
        if cache_key in self.cache:
            cached_data = self.cache[cache_key]
            if time.time() - cached_data['timestamp'] < self.cache_duration:
                cached_message = f"📋 (Из кэша)\n{cached_data['result']}"
                self._send_result_message(params, cached_message)
                return HookResult(strategy=HookStrategy.CANCEL)

        self._show_progress_dialog(f"Выполняется {check_type.upper()} проверка...")

        def execute_async():
            self._perform_check_async(check_type, host, params, args)

        threading.Thread(target=execute_async, daemon=True).start()
        return HookResult(strategy=HookStrategy.CANCEL)

    def _execute_nodes_request(self, params: Any) -> HookResult:
        self._show_progress_dialog("Получение списка узлов...")

        def execute_async():
            self._get_nodes_async(params)

        threading.Thread(target=execute_async, daemon=True).start()
        return HookResult(strategy=HookStrategy.CANCEL)

    def _show_progress_dialog(self, message: str):
        global progress_dialog
        try:
            fragment = get_last_fragment()
            if fragment and fragment.getParentActivity():
                progress_dialog = AlertDialog(fragment.getParentActivity(), 3)
                progress_dialog.show()
        except Exception as e:
            log(f"[CheckHostPlugin] Ошибка создания диалога: {e}")

    def _dismiss_progress_dialog(self):
        global progress_dialog
        try:
            if progress_dialog is not None and progress_dialog.isShowing():
                progress_dialog.dismiss()
        except Exception:
            pass
        finally:
            progress_dialog = None

    def _perform_check_async(self, check_type: str, host: str, params: Any, args: List[str]):
        try:
            result = self.api.start_check(check_type, host)
            if not result or not result.get('ok'):
                error_msg = "❌ Не удалось запустить проверку"
                if result and 'error' in result:
                    error_msg += f": {result['error']}"
                self._send_result_message(params, error_msg)
                return

            request_id = result.get('request_id')
            if not request_id:
                self._send_result_message(params, "❌ Не получен ID запроса")
                return

            max_attempts = 30
            attempt = 0

            while attempt < max_attempts:
                time.sleep(2)
                attempt += 1

                check_result = self.api.get_result(request_id, extended=True)
                if not check_result:
                    continue

                if self._is_check_complete(check_result):
                    formatted_result = self._format_result(check_type, host, check_result, result)

                    cache_key = f"{check_type}:{host}"
                    self.cache[cache_key] = {
                        'result': formatted_result,
                        'timestamp': time.time()
                    }

                    self._send_result_message(params, formatted_result)
                    return

            self._send_result_message(params, "⏰ Время ожидания результата истекло")

        except Exception as e:
            log(f"[CheckHostPlugin] Ошибка при выполнении проверки: {e}")
            self._send_result_message(params, f"❌ Ошибка: {str(e)}")

    def _get_nodes_async(self, params: Any):
        try:
            nodes_data = self.api.get_nodes()
            if not nodes_data or 'nodes' not in nodes_data:
                self._send_result_message(params, "❌ Не удалось получить список узлов")
                return

            formatted_nodes = self._format_nodes(nodes_data['nodes'])
            self._send_result_message(params, formatted_nodes)

        except Exception as e:
            log(f"[CheckHostPlugin] Ошибка при получении узлов: {e}")
            self._send_result_message(params, f"❌ Ошибка: {str(e)}")

    def _send_result_message(self, params: Any, message: str):
        def send_and_dismiss():
            self._dismiss_progress_dialog()

            try:
                parsed = parse_markdown(message)
                message_payload = {
                    "peer": getattr(params, "peer", None),
                    "message": parsed.text,
                    "entities": [entity.to_tlrpc_object() for entity in parsed.entities] if parsed.entities else None,
                    "replyToMsg": getattr(params, "replyToMsg", None),
                    "replyToTopMsg": getattr(params, "replyToTopMsg", None)
                }
                send_message(message_payload)
            except Exception as e:
                log(f"[CheckHostPlugin] Ошибка парсинга markdown: {e}")
                fallback_message = message.replace('**', '').replace('*', '').replace('`', '')
                send_message({
                    "peer": getattr(params, "peer", None),
                    "message": fallback_message,
                    "replyToMsg": getattr(params, "replyToMsg", None),
                    "replyToTopMsg": getattr(params, "replyToTopMsg", None)
                })

        run_on_ui_thread(send_and_dismiss)

    def _is_check_complete(self, result: Dict) -> bool:
        if not result or 'results' not in result:
            return False

        results = result['results']
        if not results:
            return False

        for node_result in results.values():
            if node_result is None:
                return False

        return True

    def _format_result(self, check_type: str, host: str, result: Dict, initial_data: Dict) -> str:
        try:
            if check_type == 'ping':
                return self._format_ping_result(host, result, initial_data)
            elif check_type == 'http':
                return self._format_http_result(host, result, initial_data)
            elif check_type == 'tcp':
                return self._format_tcp_result(host, result, initial_data)
            elif check_type == 'dns':
                return self._format_dns_result(host, result, initial_data)
            elif check_type == 'udp':
                return self._format_udp_result(host, result, initial_data)
            else:
                return self._format_generic_result(check_type, host, result, initial_data)
        except Exception as e:
            log(f"[CheckHostPlugin] Ошибка форматирования: {e}")
            return f"❌ Ошибка форматирования результата: {str(e)}"

    def _format_ping_result(self, host: str, result: Dict, initial_data: Dict) -> str:
        output = [f"🏓 *Ping проверка:* `{host}`\n"]

        results = result.get('results', {})
        nodes_info = initial_data.get('nodes', {})

        successful_nodes = 0
        total_nodes = len(results)

        for node_id, node_result in results.items():
            node_info = nodes_info.get(node_id, [])
            location = self._format_node_location(node_info) if self.get_setting("show_node_locations", True) else ""

            if not node_result or not isinstance(node_result, list) or not node_result[0]:
                output.append(f"❌ `{node_id}`{location}: Нет ответа")
                continue

            pings = node_result[0]
            if not pings:
                output.append(f"❌ `{node_id}`{location}: Нет данных")
                continue

            successful_pings = []
            failed_pings = []

            for ping in pings:
                if isinstance(ping, list) and len(ping) >= 2:
                    status = ping[0]
                    time_ms = ping[1] if len(ping) > 1 else 0

                    if status == "OK":
                        successful_pings.append(time_ms)
                    else:
                        failed_pings.append(status)

            if successful_pings:
                successful_nodes += 1
                avg_time = sum(successful_pings) / len(successful_pings)
                min_time = min(successful_pings)
                max_time = max(successful_pings)

                status_icon = "✅" if avg_time < 100 else "⚠️" if avg_time < 300 else "🔴"
                output.append(f"{status_icon} `{node_id}`{location}:")
                output.append(f"   • Успешно: {len(successful_pings)}/{len(pings)}")
                output.append(f"   • Время: {avg_time:.1f}ms (мин: {min_time:.1f}, макс: {max_time:.1f})")

                if failed_pings:
                    output.append(f"   • Ошибки: {', '.join(set(failed_pings))}")
            else:
                output.append(f"❌ `{node_id}`{location}: Все пинги неуспешны")
                if failed_pings:
                    output.append(f"   • Ошибки: {', '.join(set(failed_pings))}")

        success_rate = (successful_nodes / total_nodes * 100) if total_nodes > 0 else 0
        output.append(f"\n📊 *Сводка:* {successful_nodes}/{total_nodes} узлов ({success_rate:.1f}%)")

        if self.get_setting("show_promo_messages", True):
            if success_rate == 100:
                output.append(f"\n💡 Отличный результат! Подписывайтесь на @mishabotov")
            elif success_rate >= 80:
                output.append(f"\n🎯 Хороший результат от @mishabotov")

        return "\n".join(output)

    def _format_http_result(self, host: str, result: Dict, initial_data: Dict) -> str:
        output = [f"🌐 *HTTP проверка:* `{host}`\n"]

        results = result.get('results', {})
        nodes_info = initial_data.get('nodes', {})

        successful_nodes = 0
        total_nodes = len(results)

        for node_id, node_result in results.items():
            node_info = nodes_info.get(node_id, [])
            location = self._format_node_location(node_info) if self.get_setting("show_node_locations", True) else ""

            if not node_result or not isinstance(node_result, list) or not node_result[0]:
                output.append(f"❌ `{node_id}`{location}: Нет ответа")
                continue

            http_data = node_result[0]
            if not isinstance(http_data, list) or len(http_data) < 4:
                output.append(f"❌ `{node_id}`{location}: Неверный формат данных")
                continue

            success = http_data[0]
            response_time = http_data[1]
            status_text = http_data[2]
            status_code = http_data[3]
            server_ip = http_data[4] if len(http_data) > 4 else "N/A"

            if success:
                successful_nodes += 1
                status_icon = "✅" if status_code in [200, 301, 302] else "⚠️"
                output.append(f"{status_icon} `{node_id}`{location}:")
                output.append(f"   • Статус: {status_code} {status_text}")
                output.append(f"   • Время ответа: {response_time:.3f}s")
                if server_ip != "N/A":
                    output.append(f"   • IP сервера: `{server_ip}`")
            else:
                output.append(f"❌ `{node_id}`{location}:")
                output.append(f"   • Ошибка: {status_text}")
                if response_time:
                    output.append(f"   • Время: {response_time:.3f}s")

        success_rate = (successful_nodes / total_nodes * 100) if total_nodes > 0 else 0
        output.append(f"\n📊 *Сводка:* {successful_nodes}/{total_nodes} узлов ({success_rate:.1f}%)")

        if self.get_setting("show_promo_messages", True):
            if success_rate == 100:
                output.append(f"\n🚀 Все узлы доступны! Плагин от @mishabotov")
            elif success_rate == 0:
                output.append(f"\n😔 Сайт недоступен. Разработчик @mishabotov")

        return "\n".join(output)

    def _format_tcp_result(self, host: str, result: Dict, initial_data: Dict) -> str:
        output = [f"🔌 *TCP проверка:* `{host}`\n"]

        results = result.get('results', {})
        nodes_info = initial_data.get('nodes', {})

        successful_nodes = 0
        total_nodes = len(results)

        for node_id, node_result in results.items():
            node_info = nodes_info.get(node_id, [])
            location = self._format_node_location(node_info) if self.get_setting("show_node_locations", True) else ""

            if not node_result or not isinstance(node_result, list) or not node_result[0]:
                output.append(f"❌ `{node_id}`{location}: Нет ответа")
                continue

            tcp_data = node_result[0]
            if isinstance(tcp_data, dict):
                if 'error' in tcp_data:
                    output.append(f"❌ `{node_id}`{location}: {tcp_data['error']}")
                elif 'time' in tcp_data and 'address' in tcp_data:
                    successful_nodes += 1
                    connect_time = tcp_data['time']
                    server_ip = tcp_data['address']

                    status_icon = "✅" if connect_time < 0.1 else "⚠️" if connect_time < 0.5 else "🔴"
                    output.append(f"{status_icon} `{node_id}`{location}:")
                    output.append(f"   • Соединение установлено")
                    output.append(f"   • Время подключения: {connect_time:.3f}s")
                    output.append(f"   • IP сервера: `{server_ip}`")
                else:
                    output.append(f"❓ `{node_id}`{location}: Неизвестный формат ответа")
            else:
                output.append(f"❌ `{node_id}`{location}: Неверный формат данных")

        success_rate = (successful_nodes / total_nodes * 100) if total_nodes > 0 else 0
        output.append(f"\n📊 *Сводка:* {successful_nodes}/{total_nodes} узлов ({success_rate:.1f}%)")

        if self.get_setting("show_promo_messages", True):
            if success_rate >= 75:
                output.append(f"\n🔗 Порт доступен! Мониторинг от @mishabotov")

        return "\n".join(output)

    def _format_dns_result(self, host: str, result: Dict, initial_data: Dict) -> str:
        output = [f"🔍 *DNS проверка:* `{host}`\n"]

        results = result.get('results', {})
        nodes_info = initial_data.get('nodes', {})

        successful_nodes = 0
        total_nodes = len(results)
        total_a_records = 0
        total_aaaa_records = 0

        for node_id, node_result in results.items():
            node_info = nodes_info.get(node_id, [])
            location = self._format_node_location(node_info) if self.get_setting("show_node_locations", True) else ""

            if not node_result or not isinstance(node_result, list) or not node_result[0]:
                output.append(f"❌ `{node_id}`{location}: Нет ответа")
                continue

            dns_data = node_result[0]
            if not isinstance(dns_data, dict):
                output.append(f"❌ `{node_id}`{location}: Неверный формат данных")
                continue

            a_records = dns_data.get('A', [])
            aaaa_records = dns_data.get('AAAA', [])
            ttl = dns_data.get('TTL')

            total_a_records += len(a_records)
            total_aaaa_records += len(aaaa_records)

            if a_records or aaaa_records:
                successful_nodes += 1
                output.append(f"✅ `{node_id}`{location}:")

                if a_records:
                    output.append(f"   • IPv4 (A): `{', '.join(a_records)}`")
                if aaaa_records:
                    output.append(f"   • IPv6 (AAAA): `{', '.join(aaaa_records)}`")
                if ttl:
                    output.append(f"   • TTL: {ttl}s")
            else:
                output.append(f"❌ `{node_id}`{location}: DNS записи не найдены")

        success_rate = (successful_nodes / total_nodes * 100) if total_nodes > 0 else 0
        output.append(f"\n📊 *Сводка:* {successful_nodes}/{total_nodes} узлов ({success_rate:.1f}%)")

        if self.get_setting("show_promo_messages", True):
            if total_a_records > 3 or total_aaaa_records > 1:
                output.append(f"\n🌐 Много IP адресов! DNS анализ от @mishabotov")

        return "\n".join(output)

    def _format_udp_result(self, host: str, result: Dict, initial_data: Dict) -> str:
        output = [f"📡 *UDP проверка:* `{host}`\n"]

        results = result.get('results', {})
        nodes_info = initial_data.get('nodes', {})

        successful_nodes = 0
        total_nodes = len(results)

        for node_id, node_result in results.items():
            node_info = nodes_info.get(node_id, [])
            location = self._format_node_location(node_info) if self.get_setting("show_node_locations", True) else ""

            if not node_result or not isinstance(node_result, list) or not node_result[0]:
                output.append(f"❌ `{node_id}`{location}: Нет ответа")
                continue

            udp_data = node_result[0]
            if isinstance(udp_data, dict):
                if 'error' in udp_data:
                    output.append(f"❌ `{node_id}`{location}: {udp_data['error']}")
                elif 'time' in udp_data:
                    successful_nodes += 1
                    response_time = udp_data['time']

                    status_icon = "✅" if response_time < 0.1 else "⚠️" if response_time < 0.5 else "🔴"
                    output.append(f"{status_icon} `{node_id}`{location}:")
                    output.append(f"   • UDP порт доступен")
                    output.append(f"   • Время ответа: {response_time:.3f}s")
                else:
                    output.append(f"❓ `{node_id}`{location}: Неизвестный формат ответа")
            else:
                output.append(f"❌ `{node_id}`{location}: Неверный формат данных")

        success_rate = (successful_nodes / total_nodes * 100) if total_nodes > 0 else 0
        output.append(f"\n📊 *Сводка:* {successful_nodes}/{total_nodes} узлов ({success_rate:.1f}%)")

        if self.get_setting("show_promo_messages", True):
            if success_rate > 50:
                output.append(f"\n📡 UDP сервис работает! Проверка от @mishabotov")

        return "\n".join(output)

    def _format_generic_result(self, check_type: str, host: str, result: Dict, initial_data: Dict) -> str:
        output = [f"🔧 *{check_type.upper()} проверка:* `{host}`\n"]

        results = result.get('results', {})
        nodes_info = initial_data.get('nodes', {})

        for node_id, node_result in results.items():
            node_info = nodes_info.get(node_id, [])
            location = self._format_node_location(node_info) if self.get_setting("show_node_locations", True) else ""

            if node_result is None:
                output.append(f"⏳ `{node_id}`{location}: Ожидание результата...")
            elif not node_result:
                output.append(f"❌ `{node_id}`{location}: Нет данных")
            else:
                output.append(f"📋 `{node_id}`{location}: {str(node_result)}")

        if self.get_setting("show_promo_messages", True):
            output.append(f"\n🛠️ Расширенная проверка от @mishabotov")
        return "\n".join(output)

    def _format_nodes(self, nodes: Dict) -> str:
        output = ["🌍 *Доступные узлы check-host.net:*\n"]

        countries = {}
        for node_id, node_info in nodes.items():
            location = node_info.get('location', [])
            if len(location) >= 2:
                country_code = location[0].upper()
                country_name = location[1]
                city = location[2] if len(location) > 2 else "N/A"

                if country_name not in countries:
                    countries[country_name] = []

                countries[country_name].append({
                    'id': node_id,
                    'city': city,
                    'ip': node_info.get('ip', 'N/A'),
                    'asn': node_info.get('asn', 'N/A')
                })

        for country in sorted(countries.keys()):
            output.append(f"🏳️ *{country}:*")

            for node in sorted(countries[country], key=lambda x: x['city']):
                output.append(f"   • `{node['id']}`")
                output.append(f"     📍 `{node['city']}`")
                output.append(f"     🌐 `{node['ip']}` ({node['asn']})")

            output.append("")

        output.append(f"📊 *Всего узлов:* {len(nodes)}")
        output.append("\n💡 *Использование:* Добавьте название узла после команды")
        output.append("Пример: `.ping google.com us1.node.check-host.net`")

        if self.get_setting("show_promo_messages", True):
            output.append(f"\n🔧 Плагин создан каналом @mishabotov")
            output.append("📡 Используется API check-host.net")

        return "\n".join(output)

    def _format_node_location(self, node_info: List) -> str:
        if not node_info or len(node_info) < 2:
            return ""

        country = node_info[1] if len(node_info) > 1 else ""
        city = node_info[2] if len(node_info) > 2 else ""

        if city and country:
            return f" `({city}, {country})`"
        elif country:
            return f" `({country})`"
        else:
            return ""