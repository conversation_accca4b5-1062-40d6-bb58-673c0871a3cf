---
title: Setup
description: How to start developing plugins.
icon: FileSliders
---

<Steps>
<Step>
  ### Download exteraGram

  Make sure you're using the latest version of exteraGram or derivative client.

  You may download latest version from the [beta channel](https://t.me/exteraGramCI).
</Step>
<Step>
  ### Enable plugins engine

  After logging into your account, go to the `exteraGram Preferences` > `Plugins` and enable plugins engine. Long-tap on header to enable developer mode.
</Step>
<Step>
    ### Bootstrap project

    Create a folder on your PC and create a Python file, e.g. `first_plugin.py`.

    Also make sure to download [dev client](https://t.me/c/**********/806) for hot-reload and debugging support in VS Code.
</Step>
[//]: # (<Step>)

[//]: # (  ### Download support extension)

[//]: # ()
[//]: # (  In order to get comfortable development experience, you should download extension for your code editor.)

[//]: # ()
[//]: # (</Step>)

[//]: # (<Step>)

[//]: # (  ### Initialize project folder)

[//]: # ()
[//]: # (  If you're using PyCharm, click `File` > `Project` > `New` and select `exteraGram Plugin` template here.)

[//]: # ()
[//]: # (  For VS Code users, you have to setup project manually.)

[//]: # (</Step>)
<Step>
  ### Connecting to the phone

  Connect your phone to your PC using cable. Also make sure [ADB](https://developer.android.com/tools/adb) is on your `PATH`.

    ```sh
    # replace first_plugin.py with your actual filename
    python dev_client.py first_plugin.py

    # for debugging support
    python dev_client.py first_plugin.py --debug
    ```

    VS Code remote debugging example:

```json
{
    "version": "0.2.0",
    "configurations": [
        {
        "name": "Python Debugger: Remote Attach exteraGram",
        "type": "debugpy",
        "request": "attach",
        "connect": {
            "host": "localhost",
            "port": 5678
        },
        "pathMappings": [
                {
                    "localRoot": "/Users/<USER>/Projects/extera-plugins/first_plugin.py",
                    "remoteRoot": "/data/user/0/com.exteragram.messenger/files/plugins/first_plugin.py"
                }
            ]
        }
    ]
}
```

    Note that `remoteRoot` should end with `PLUGIN_ID.py`.

  [//]: # (Normally, our extension should detect your device automatically. If it doesn't happen, then you should manually specify internal IP address of the phone.)

[//]: # ()
  [//]: # (If none of the methods worked for you, then you should use [ADB]&#40;https://developer.android.com/tools/adb&#41; to forward ports and then use `localhost` as device IP:)

[//]: # ()
  [//]: # (```sh)

[//]: # (adb forward tcp:42690 tcp:42690)

[//]: # (```)
</Step>
</Steps>