/*
 * Copyright 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.google.android.exoplayer2.util;

import android.view.SurfaceView;
import androidx.annotation.Nullable;

/** Provider for views to show diagnostic information during a transformation, for debugging. */
public interface DebugViewProvider {

  /** Debug view provider that doesn't show any debug info. */
  DebugViewProvider NONE = (int width, int height) -> null;

  /**
   * Returns a new surface view to show a preview of transformer output with the given width/height
   * in pixels, or {@code null} if no debug information should be shown.
   *
   * <p>This method may be called on an arbitrary thread.
   */
  @Nullable
  SurfaceView getDebugPreviewSurfaceView(int width, int height);
}
