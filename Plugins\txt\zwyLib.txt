import copy
import time
import json
import os.path
import threading
import traceback
from dataclasses import dataclass, asdict
from typing import List, Callable, Optional, Any

from ui.bulletin import BulletinHelper
from ui.settings import Divider, Header, Switch, Input
from base_plugin import BasePlugin
from android_utils import log as logcat, run_on_ui_thread
from client_utils import get_messages_controller, send_request, get_file_loader

from java import dynamic_proxy, cast
from org.telegram.tgnet import TLRPC
from org.telegram.messenger import Utilities, FileLoader
from com.exteragram.messenger.utils import ChatUtils
from com.exteragram.messenger.plugins import PluginsController

__name__ = "ZwyLib"
__description__ = "<PERSON><PERSON>'s library with highly used things"
__icon__ = "zwyPluginsIcons/0"
__id__ = "zwylib"
__version__ = "1.0.3"
__author__ = "@zwylair"
__min_version__ = "11.9.1"
__all__ = ("add_autoupdater_task", "remove_autoupdater_task", "JsonCacheFile")


class Callback1(dynamic_proxy(Utilities.Callback)):
    def __init__(self, fn: Callable[[Any], None]):
        super().__init__()
        self._fn = fn

    def run(self, arg):
        try:
            self._fn(arg)
        except Exception:
            log(f"Error in Callback1: {traceback.format_exc().rstrip()}")


class JsonCacheFile:
    cache_dir_name = os.path.join(os.path.dirname(os.path.realpath(__file__)), "cache")

    def __init__(self, filename: str, default: Any, read_on_init = True):
        self.filename = filename
        self.path = os.path.join(JsonCacheFile.cache_dir_name, filename)
        self.content = copy.copy(default)
        self.default = copy.copy(default)

        os.makedirs(JsonCacheFile.cache_dir_name, exist_ok=True)

        if read_on_init:
            self.read()

    def read(self) -> Any:
        try:
            with open(self.path) as file:
                self.content = json.load(file)
        except (json.JSONDecodeError, FileNotFoundError):
            self.wipe()
            return self.default
        else:
            return self.content

    # noinspection PyTypeChecker
    def write(self):
        try:
            with open(self.path, "w") as file:
                json.dump(self.content, file)
        except PermissionError:
            log(f"Permission denied when writing to {self.path}: {traceback.format_exc().rstrip()}")

    def wipe(self):
        self.content = copy.copy(self.default)
        self.write()


@dataclass
class UpdaterTask:
    plugin_id: str
    channel_id: int
    channel_username: str
    message_id: int


class AutoUpdater:
    def __init__(self):
        self.thread: Optional[threading.Thread] = None
        self.forced_stop = False
        self.tasks: List[UpdaterTask] = []
        self.msg_edited_ts_cache = JsonCacheFile("zwylib_au__msg_edited_ts", {})  # plugin_id: edit_timestamp_of_message_with_file

    def run(self):
        self.forced_stop = False

        if self.thread is None:
            self.thread = threading.Thread(target=self.cycle)
            self.thread.daemon = True

        if self.thread.is_alive():
            self.log(f"AutoUpdater (id: {id(self)}) has not been stopped")
            return

        self.thread.start()
        self.log(f"AutoUpdater (id: {id(self)}) has been started")

    def force_stop(self):
        if self.thread is None:
            self.log("Ignoring force_stop(): AutoUpdater thread has not been started")
            return
        self.forced_stop = True

    def cycle(self):
        while not self.forced_stop:
            try:
                self.check_for_updates()

                timer = 0
                while timer < self.get_timeout_time():
                    if self.forced_stop:
                        break

                    time.sleep(1)
                    timer += 1
            except Exception:
                self.log(f"Exception in cycle (id: {id(self)}): {traceback.format_exc().rstrip()}")

        self.thread = None
        self.log(f"Force stopped (id: {id(self)})")

    def check_for_updates(self):
        def get_messages_callback(response, error):
            if error is not None:
                log(f"Auto update task of plugin (id: {task.plugin_id}) has invalid message. Removing task")
                self.remove_task(task)
                return

            msg = get_message(response.messages, task.message_id)
            if not is_valid_plugin_message(msg):
                log(f"Auto update task of plugin (id: {task.plugin_id}) has invalid message (invalid/no document attached). Removing task")
                self.remove_task(task)
                return

            disable_ts_check = setting_getter("disable_ts_check", DEFAULT_EDIT_TIMESTAMP_CHECK)
            if not disable_ts_check:
                msg_edited_ts = self.msg_edited_ts_cache.content.get(task.plugin_id)
                if msg.edit_date == msg_edited_ts:
                    self.log((
                        "The message with plugin attachment has not been updated since the "
                        f"last parse. Skipping plugin (id: {task.plugin_id}) update"
                    ))
                    return

            log(f"Executing auto update of plugin (id: {task.plugin_id})")

            file_loader = get_file_loader()
            document = msg.media.getDocument()
            path = file_loader.getPathToAttach(document, True)

            if path.exists():
                self.msg_edited_ts_cache.content[task.plugin_id] = msg.edit_date
                self.msg_edited_ts_cache.write()

            run_on_ui_thread(lambda: download_and_install_plugin(msg))

        for task in list(self.tasks):  # iterate copy of self.tasks to prevent RuntimeError
            get_messages(task.channel_id, task.channel_username, get_messages_callback)

    def is_task_already_present(self, task: UpdaterTask):
        for i in list(self.tasks):
            if i.plugin_id == task.plugin_id:
                return True
        return False

    def add_task(self, task: UpdaterTask):
        if self.is_task_already_present(task):
            self.log(f"Task (id: {task.plugin_id}) is already present")
            return

        self.tasks.append(task)
        self.log(f"Added task (id: {task.plugin_id}) to autoupdater")

    def remove_task(self, task: UpdaterTask):
        if task in self.tasks:
            self.tasks.remove(task)
            self.log(f"Removed task (id: {task.plugin_id}) from autoupdater")
        else:
            self.log(f"Task (id: {task.plugin_id}) is not present in list")

    def remove_task_by_id(self, plugin_id: str):
        for task in self.tasks:
            if plugin_id == task.plugin_id:
                self.tasks.remove(task)
                break
        else:
            self.log(f"Task (id: {plugin_id}) is not present in list")
            return
        self.log(f"Removed task (id: {plugin_id}) from autoupdater")

    @staticmethod
    def log(text):
        log("[PyObject] [AutoUpdater] " + text)

    def get_timeout_time(self) -> int:
        try:
            return int(setting_getter("autoupdate_timeout", DEFAULT_AUTOUPDATE_TIMEOUT))
        except (ValueError, TypeError):
            self.log(f"Exception in cycle (id: {id(self)}): {traceback.format_exc().rstrip()}")
            return int(DEFAULT_AUTOUPDATE_TIMEOUT)


def is_valid_plugin_message(msg):
    if msg is None or msg.media is None:
        return False

    document = msg.media.getDocument()
    if document.attributes.isEmpty() or not document.attributes.get(0).file_name.endswith(".plugin"):
        return False
    return True


def download_and_install_plugin(msg):
    def plugin_install_error(arg):
        if arg is None:
            return
        BulletinHelper.show_error(arg)

    file_loader = get_file_loader()
    plugins_controller = PluginsController.getInstance()
    document = msg.media.getDocument()
    path = file_loader.getPathToAttach(document, True)

    if path.exists():
        log("Installing...")
        plugins_controller.loadPluginFromFile(str(path), Callback1(plugin_install_error))
    else:
        log("Started loading the file...")
        file_loader.loadFile(document, "plugin_update", FileLoader.PRIORITY_NORMAL, 1)


def get_messages(channel_id: int, channel_username: str, callback: Callable):
    def get_message_callback(response, error):
        if error or not response:
            callback(None, error)
            return

        messages = cast(TLRPC.messages_Messages, response)
        callback(messages, None)

    def send():
        send_request(req, get_message_callback)

    def channel_resolve_callback(arg):
        if arg is not None and arg.id == channel_id:
            req.peer = TLRPC.TL_inputPeerChannel()
            req.peer.channel_id = arg.id
            req.peer.access_hash = arg.access_hash
            run_on_ui_thread(send)
            return
        log(f"Failed to resolve a channel (id: {channel_id}; username: {channel_username}). Make sure you have entered the correct data")

    req = TLRPC.TL_messages_getHistory()
    req.peer = get_messages_controller().getInputPeer(channel_id)
    req.offset_id = 0
    req.limit = 50

    if req.peer.access_hash == 0:
        ChatUtils.getInstance().resolveChannel(channel_username, Callback1(channel_resolve_callback))
    else:
        run_on_ui_thread(send)


def get_message(messages, message_id: int) -> Optional[TLRPC.Message]:
    for i in range(messages.size()):
        if messages.get(i).id == message_id:
            return messages.get(i)
    return None


def log(string: str):
    logcat(f"{__name__}: " + string)


# noinspection PyTypeChecker
def add_autoupdater_task(plugin_id: str, channel_id: int, channel_username: str, message_id: int):
    task = UpdaterTask(plugin_id, channel_id, channel_username, message_id)

    if autoupdater is None:
        logcat("AutoUpdater is not initialized yet, saving task to pending list")
        pending_tasks_cache.content.append(asdict(task))
        pending_tasks_cache.write()
        return

    autoupdater.add_task(task)


# noinspection PyTypeChecker
def remove_autoupdater_task(plugin_id: str):
    if autoupdater is None:
        logcat("AutoUpdater is not initialized yet")
        return

    autoupdater.remove_task_by_id(plugin_id)


# noinspection PyTypeChecker
def cache_all_autoupdater_tasks(wipe: bool):
    if wipe:
        pending_tasks_cache.wipe()

    for task in autoupdater.tasks:
        pending_tasks_cache.content.append(asdict(task))
    pending_tasks_cache.write()


def load_cached_autoupdater_tasks(wipe: bool):
    for task in pending_tasks_cache.content:
        try:
            autoupdater.add_task(UpdaterTask(**task))
        except TypeError:
            pass

    if wipe:
        pending_tasks_cache.wipe()


AUTOUPDATE_CHANNEL_ID = 2521243181
AUTOUPDATE_CHANNEL_USERNAME = "zwyPlugins"
AUTOUPDATE_MESSAGE_ID = 48

DEFAULT_EDIT_TIMESTAMP_CHECK = False
DEFAULT_DISABLE_AUTOUPDATER = False
DEFAULT_AUTOUPDATE_TIMEOUT = str(10 * 60)

setting_getter: Optional[Callable] = None
autoupdater: Optional[AutoUpdater] = None
pending_tasks_cache = JsonCacheFile("zwylib_au__pending_tasks_list", [])


class ZwyLib(BasePlugin):
    def create_settings(self):
        try:
            return [
                Header(text="AutoUpdater"),
                Input(
                    key="autoupdate_timeout",
                    text="Auto update timeout",
                    subtext="Time in seconds between update checks",
                    default=DEFAULT_AUTOUPDATE_TIMEOUT,
                    icon="msg2_autodelete",
                ),
                Switch(
                    key="disable_autoupdate",
                    text="Disable autoupdates",
                    subtext="Auto updater will be disabled",
                    default=DEFAULT_DISABLE_AUTOUPDATER,
                    icon="msg_photo_switch2",
                    on_change=lambda enabled: autoupdater.force_stop() if enabled else run_on_ui_thread(autoupdater.run)
                ),
                Switch(
                    key="disable_ts_check",
                    text="Disable message edit check",
                    subtext="Plugin will be updated even if the file has not been modified",
                    default=DEFAULT_EDIT_TIMESTAMP_CHECK,
                    icon="msg_recent",
                ),
            ]
        except Exception:
            text = (
                f"An exception occurred on {self.__class__.__name__}.create_settings():\n"
                f"{traceback.format_exc().rstrip()}"
            )
            log(text)
            return [Divider(text=text)]

    def on_plugin_load(self):
        global autoupdater, setting_getter
        setting_getter = self.get_setting

        log("Initialising AutoUpdater...")
        autoupdater = AutoUpdater()
        add_autoupdater_task(__id__, AUTOUPDATE_CHANNEL_ID, AUTOUPDATE_CHANNEL_USERNAME, AUTOUPDATE_MESSAGE_ID)

        if not self.get_setting("disable_autoupdate", DEFAULT_DISABLE_AUTOUPDATER):
            log("Adding tasks from cache...")
            load_cached_autoupdater_tasks(wipe=True)
            autoupdater.run()

        log("Loaded")

    def on_plugin_unload(self):
        log("Caching all tasks to add them again after reloading the plugin")
        cache_all_autoupdater_tasks(wipe=True)

        log("Force-stopping the AutoUpdater")
        autoupdater.force_stop()

        log("Unloaded")
