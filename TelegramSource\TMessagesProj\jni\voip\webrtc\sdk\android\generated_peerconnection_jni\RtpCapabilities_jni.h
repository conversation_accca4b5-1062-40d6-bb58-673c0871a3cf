// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/RtpCapabilities

#ifndef org_webrtc_RtpCapabilities_JNI
#define org_webrtc_RtpCapabilities_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_RtpCapabilities[];
const char kClassPath_org_webrtc_RtpCapabilities[] = "org/webrtc/RtpCapabilities";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_RtpCapabilities_00024CodecCapability[];
const char kClassPath_org_webrtc_RtpCapabilities_00024CodecCapability[] =
    "org/webrtc/RtpCapabilities$CodecCapability";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_RtpCapabilities_00024HeaderExtensionCapability[];
const char kClassPath_org_webrtc_RtpCapabilities_00024HeaderExtensionCapability[] =
    "org/webrtc/RtpCapabilities$HeaderExtensionCapability";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_RtpCapabilities_clazz(nullptr);
#ifndef org_webrtc_RtpCapabilities_clazz_defined
#define org_webrtc_RtpCapabilities_clazz_defined
inline jclass org_webrtc_RtpCapabilities_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_RtpCapabilities,
      &g_org_webrtc_RtpCapabilities_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_RtpCapabilities_00024CodecCapability_clazz(nullptr);
#ifndef org_webrtc_RtpCapabilities_00024CodecCapability_clazz_defined
#define org_webrtc_RtpCapabilities_00024CodecCapability_clazz_defined
inline jclass org_webrtc_RtpCapabilities_00024CodecCapability_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_RtpCapabilities_00024CodecCapability,
      &g_org_webrtc_RtpCapabilities_00024CodecCapability_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_clazz(nullptr);
#ifndef org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_clazz_defined
#define org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_clazz_defined
inline jclass org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env,
      kClassPath_org_webrtc_RtpCapabilities_00024HeaderExtensionCapability,
      &g_org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_RtpCapabilities_Constructor2(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RtpCapabilities_Constructor(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& codecs,
    const jni_zero::JavaRef<jobject>& headerExtensions) {
  jclass clazz = org_webrtc_RtpCapabilities_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_RtpCapabilities_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Ljava/util/List;Ljava/util/List;)V",
          &g_org_webrtc_RtpCapabilities_Constructor2);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, codecs.obj(), headerExtensions.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpCapabilities_getCodecs0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RtpCapabilities_getCodecs(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpCapabilities_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpCapabilities_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getCodecs",
          "()Ljava/util/List;",
          &g_org_webrtc_RtpCapabilities_getCodecs0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpCapabilities_getHeaderExtensions0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RtpCapabilities_getHeaderExtensions(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpCapabilities_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpCapabilities_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHeaderExtensions",
          "()Ljava/util/List;",
          &g_org_webrtc_RtpCapabilities_getHeaderExtensions0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpCapabilities_00024CodecCapability_Constructor7(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_CodecCapability_Constructor(JNIEnv* env,
    JniIntWrapper preferredPayloadType,
    const jni_zero::JavaRef<jstring>& name,
    const jni_zero::JavaRef<jobject>& kind,
    const jni_zero::JavaRef<jobject>& clockRate,
    const jni_zero::JavaRef<jobject>& numChannels,
    const jni_zero::JavaRef<jstring>& mimeType,
    const jni_zero::JavaRef<jobject>& parameters) {
  jclass clazz = org_webrtc_RtpCapabilities_00024CodecCapability_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_RtpCapabilities_00024CodecCapability_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
"(ILjava/lang/String;Lorg/webrtc/MediaStreamTrack$MediaType;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/String;Ljava/util/Map;)V",
          &g_org_webrtc_RtpCapabilities_00024CodecCapability_Constructor7);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, as_jint(preferredPayloadType), name.obj(), kind.obj(),
              clockRate.obj(), numChannels.obj(), mimeType.obj(), parameters.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpCapabilities_00024CodecCapability_getClockRate0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_CodecCapability_getClockRate(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpCapabilities_00024CodecCapability_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpCapabilities_00024CodecCapability_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getClockRate",
          "()Ljava/lang/Integer;",
          &g_org_webrtc_RtpCapabilities_00024CodecCapability_getClockRate0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpCapabilities_00024CodecCapability_getKind0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_CodecCapability_getKind(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpCapabilities_00024CodecCapability_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpCapabilities_00024CodecCapability_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getKind",
          "()Lorg/webrtc/MediaStreamTrack$MediaType;",
          &g_org_webrtc_RtpCapabilities_00024CodecCapability_getKind0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpCapabilities_00024CodecCapability_getName0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_CodecCapability_getName(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpCapabilities_00024CodecCapability_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpCapabilities_00024CodecCapability_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getName",
          "()Ljava/lang/String;",
          &g_org_webrtc_RtpCapabilities_00024CodecCapability_getName0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpCapabilities_00024CodecCapability_getNumChannels0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_CodecCapability_getNumChannels(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpCapabilities_00024CodecCapability_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpCapabilities_00024CodecCapability_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getNumChannels",
          "()Ljava/lang/Integer;",
          &g_org_webrtc_RtpCapabilities_00024CodecCapability_getNumChannels0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpCapabilities_00024CodecCapability_getParameters0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_CodecCapability_getParameters(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpCapabilities_00024CodecCapability_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpCapabilities_00024CodecCapability_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getParameters",
          "()Ljava/util/Map;",
          &g_org_webrtc_RtpCapabilities_00024CodecCapability_getParameters0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpCapabilities_00024CodecCapability_getPreferredPayloadType0(nullptr);
static jint Java_CodecCapability_getPreferredPayloadType(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpCapabilities_00024CodecCapability_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpCapabilities_00024CodecCapability_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getPreferredPayloadType",
          "()I",
          &g_org_webrtc_RtpCapabilities_00024CodecCapability_getPreferredPayloadType0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_Constructor3(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_HeaderExtensionCapability_Constructor(JNIEnv* env,
    const jni_zero::JavaRef<jstring>& uri,
    JniIntWrapper preferredId,
    jboolean preferredEncrypted) {
  jclass clazz = org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Ljava/lang/String;IZ)V",
          &g_org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_Constructor3);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, uri.obj(), as_jint(preferredId), preferredEncrypted);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_getPreferredEncrypted0(nullptr);
static jboolean Java_HeaderExtensionCapability_getPreferredEncrypted(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getPreferredEncrypted",
          "()Z",
          &g_org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_getPreferredEncrypted0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_getPreferredId0(nullptr);
static jint Java_HeaderExtensionCapability_getPreferredId(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getPreferredId",
          "()I",
          &g_org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_getPreferredId0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_getUri0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_HeaderExtensionCapability_getUri(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getUri",
          "()Ljava/lang/String;",
          &g_org_webrtc_RtpCapabilities_00024HeaderExtensionCapability_getUri0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_RtpCapabilities_JNI
