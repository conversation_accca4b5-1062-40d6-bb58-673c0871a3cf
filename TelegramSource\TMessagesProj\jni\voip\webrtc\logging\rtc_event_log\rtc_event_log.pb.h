// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: rtc_event_log.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_rtc_5fevent_5flog_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_rtc_5fevent_5flog_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3020000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3020003 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/message_lite.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_util.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_rtc_5fevent_5flog_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_rtc_5fevent_5flog_2eproto {
  static const uint32_t offsets[];
};
namespace webrtc {
namespace rtclog {
class AlrState;
struct AlrStateDefaultTypeInternal;
extern AlrStateDefaultTypeInternal _AlrState_default_instance_;
class AudioNetworkAdaptation;
struct AudioNetworkAdaptationDefaultTypeInternal;
extern AudioNetworkAdaptationDefaultTypeInternal _AudioNetworkAdaptation_default_instance_;
class AudioPlayoutEvent;
struct AudioPlayoutEventDefaultTypeInternal;
extern AudioPlayoutEventDefaultTypeInternal _AudioPlayoutEvent_default_instance_;
class AudioReceiveConfig;
struct AudioReceiveConfigDefaultTypeInternal;
extern AudioReceiveConfigDefaultTypeInternal _AudioReceiveConfig_default_instance_;
class AudioSendConfig;
struct AudioSendConfigDefaultTypeInternal;
extern AudioSendConfigDefaultTypeInternal _AudioSendConfig_default_instance_;
class BweProbeCluster;
struct BweProbeClusterDefaultTypeInternal;
extern BweProbeClusterDefaultTypeInternal _BweProbeCluster_default_instance_;
class BweProbeResult;
struct BweProbeResultDefaultTypeInternal;
extern BweProbeResultDefaultTypeInternal _BweProbeResult_default_instance_;
class DecoderConfig;
struct DecoderConfigDefaultTypeInternal;
extern DecoderConfigDefaultTypeInternal _DecoderConfig_default_instance_;
class DelayBasedBweUpdate;
struct DelayBasedBweUpdateDefaultTypeInternal;
extern DelayBasedBweUpdateDefaultTypeInternal _DelayBasedBweUpdate_default_instance_;
class EncoderConfig;
struct EncoderConfigDefaultTypeInternal;
extern EncoderConfigDefaultTypeInternal _EncoderConfig_default_instance_;
class Event;
struct EventDefaultTypeInternal;
extern EventDefaultTypeInternal _Event_default_instance_;
class EventStream;
struct EventStreamDefaultTypeInternal;
extern EventStreamDefaultTypeInternal _EventStream_default_instance_;
class IceCandidatePairConfig;
struct IceCandidatePairConfigDefaultTypeInternal;
extern IceCandidatePairConfigDefaultTypeInternal _IceCandidatePairConfig_default_instance_;
class IceCandidatePairEvent;
struct IceCandidatePairEventDefaultTypeInternal;
extern IceCandidatePairEventDefaultTypeInternal _IceCandidatePairEvent_default_instance_;
class LossBasedBweUpdate;
struct LossBasedBweUpdateDefaultTypeInternal;
extern LossBasedBweUpdateDefaultTypeInternal _LossBasedBweUpdate_default_instance_;
class RemoteEstimate;
struct RemoteEstimateDefaultTypeInternal;
extern RemoteEstimateDefaultTypeInternal _RemoteEstimate_default_instance_;
class RtcpPacket;
struct RtcpPacketDefaultTypeInternal;
extern RtcpPacketDefaultTypeInternal _RtcpPacket_default_instance_;
class RtpHeaderExtension;
struct RtpHeaderExtensionDefaultTypeInternal;
extern RtpHeaderExtensionDefaultTypeInternal _RtpHeaderExtension_default_instance_;
class RtpPacket;
struct RtpPacketDefaultTypeInternal;
extern RtpPacketDefaultTypeInternal _RtpPacket_default_instance_;
class RtxConfig;
struct RtxConfigDefaultTypeInternal;
extern RtxConfigDefaultTypeInternal _RtxConfig_default_instance_;
class RtxMap;
struct RtxMapDefaultTypeInternal;
extern RtxMapDefaultTypeInternal _RtxMap_default_instance_;
class VideoReceiveConfig;
struct VideoReceiveConfigDefaultTypeInternal;
extern VideoReceiveConfigDefaultTypeInternal _VideoReceiveConfig_default_instance_;
class VideoSendConfig;
struct VideoSendConfigDefaultTypeInternal;
extern VideoSendConfigDefaultTypeInternal _VideoSendConfig_default_instance_;
}  // namespace rtclog
}  // namespace webrtc
PROTOBUF_NAMESPACE_OPEN
template<> ::webrtc::rtclog::AlrState* Arena::CreateMaybeMessage<::webrtc::rtclog::AlrState>(Arena*);
template<> ::webrtc::rtclog::AudioNetworkAdaptation* Arena::CreateMaybeMessage<::webrtc::rtclog::AudioNetworkAdaptation>(Arena*);
template<> ::webrtc::rtclog::AudioPlayoutEvent* Arena::CreateMaybeMessage<::webrtc::rtclog::AudioPlayoutEvent>(Arena*);
template<> ::webrtc::rtclog::AudioReceiveConfig* Arena::CreateMaybeMessage<::webrtc::rtclog::AudioReceiveConfig>(Arena*);
template<> ::webrtc::rtclog::AudioSendConfig* Arena::CreateMaybeMessage<::webrtc::rtclog::AudioSendConfig>(Arena*);
template<> ::webrtc::rtclog::BweProbeCluster* Arena::CreateMaybeMessage<::webrtc::rtclog::BweProbeCluster>(Arena*);
template<> ::webrtc::rtclog::BweProbeResult* Arena::CreateMaybeMessage<::webrtc::rtclog::BweProbeResult>(Arena*);
template<> ::webrtc::rtclog::DecoderConfig* Arena::CreateMaybeMessage<::webrtc::rtclog::DecoderConfig>(Arena*);
template<> ::webrtc::rtclog::DelayBasedBweUpdate* Arena::CreateMaybeMessage<::webrtc::rtclog::DelayBasedBweUpdate>(Arena*);
template<> ::webrtc::rtclog::EncoderConfig* Arena::CreateMaybeMessage<::webrtc::rtclog::EncoderConfig>(Arena*);
template<> ::webrtc::rtclog::Event* Arena::CreateMaybeMessage<::webrtc::rtclog::Event>(Arena*);
template<> ::webrtc::rtclog::EventStream* Arena::CreateMaybeMessage<::webrtc::rtclog::EventStream>(Arena*);
template<> ::webrtc::rtclog::IceCandidatePairConfig* Arena::CreateMaybeMessage<::webrtc::rtclog::IceCandidatePairConfig>(Arena*);
template<> ::webrtc::rtclog::IceCandidatePairEvent* Arena::CreateMaybeMessage<::webrtc::rtclog::IceCandidatePairEvent>(Arena*);
template<> ::webrtc::rtclog::LossBasedBweUpdate* Arena::CreateMaybeMessage<::webrtc::rtclog::LossBasedBweUpdate>(Arena*);
template<> ::webrtc::rtclog::RemoteEstimate* Arena::CreateMaybeMessage<::webrtc::rtclog::RemoteEstimate>(Arena*);
template<> ::webrtc::rtclog::RtcpPacket* Arena::CreateMaybeMessage<::webrtc::rtclog::RtcpPacket>(Arena*);
template<> ::webrtc::rtclog::RtpHeaderExtension* Arena::CreateMaybeMessage<::webrtc::rtclog::RtpHeaderExtension>(Arena*);
template<> ::webrtc::rtclog::RtpPacket* Arena::CreateMaybeMessage<::webrtc::rtclog::RtpPacket>(Arena*);
template<> ::webrtc::rtclog::RtxConfig* Arena::CreateMaybeMessage<::webrtc::rtclog::RtxConfig>(Arena*);
template<> ::webrtc::rtclog::RtxMap* Arena::CreateMaybeMessage<::webrtc::rtclog::RtxMap>(Arena*);
template<> ::webrtc::rtclog::VideoReceiveConfig* Arena::CreateMaybeMessage<::webrtc::rtclog::VideoReceiveConfig>(Arena*);
template<> ::webrtc::rtclog::VideoSendConfig* Arena::CreateMaybeMessage<::webrtc::rtclog::VideoSendConfig>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace webrtc {
namespace rtclog {

enum Event_EventType : int {
  Event_EventType_UNKNOWN_EVENT = 0,
  Event_EventType_LOG_START = 1,
  Event_EventType_LOG_END = 2,
  Event_EventType_RTP_EVENT = 3,
  Event_EventType_RTCP_EVENT = 4,
  Event_EventType_AUDIO_PLAYOUT_EVENT = 5,
  Event_EventType_LOSS_BASED_BWE_UPDATE = 6,
  Event_EventType_DELAY_BASED_BWE_UPDATE = 7,
  Event_EventType_VIDEO_RECEIVER_CONFIG_EVENT = 8,
  Event_EventType_VIDEO_SENDER_CONFIG_EVENT = 9,
  Event_EventType_AUDIO_RECEIVER_CONFIG_EVENT = 10,
  Event_EventType_AUDIO_SENDER_CONFIG_EVENT = 11,
  Event_EventType_AUDIO_NETWORK_ADAPTATION_EVENT = 16,
  Event_EventType_BWE_PROBE_CLUSTER_CREATED_EVENT = 17,
  Event_EventType_BWE_PROBE_RESULT_EVENT = 18,
  Event_EventType_ALR_STATE_EVENT = 19,
  Event_EventType_ICE_CANDIDATE_PAIR_CONFIG = 20,
  Event_EventType_ICE_CANDIDATE_PAIR_EVENT = 21,
  Event_EventType_REMOTE_ESTIMATE = 22
};
bool Event_EventType_IsValid(int value);
constexpr Event_EventType Event_EventType_EventType_MIN = Event_EventType_UNKNOWN_EVENT;
constexpr Event_EventType Event_EventType_EventType_MAX = Event_EventType_REMOTE_ESTIMATE;
constexpr int Event_EventType_EventType_ARRAYSIZE = Event_EventType_EventType_MAX + 1;

const std::string& Event_EventType_Name(Event_EventType value);
template<typename T>
inline const std::string& Event_EventType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Event_EventType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Event_EventType_Name.");
  return Event_EventType_Name(static_cast<Event_EventType>(enum_t_value));
}
bool Event_EventType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Event_EventType* value);
enum DelayBasedBweUpdate_DetectorState : int {
  DelayBasedBweUpdate_DetectorState_BWE_NORMAL = 0,
  DelayBasedBweUpdate_DetectorState_BWE_UNDERUSING = 1,
  DelayBasedBweUpdate_DetectorState_BWE_OVERUSING = 2
};
bool DelayBasedBweUpdate_DetectorState_IsValid(int value);
constexpr DelayBasedBweUpdate_DetectorState DelayBasedBweUpdate_DetectorState_DetectorState_MIN = DelayBasedBweUpdate_DetectorState_BWE_NORMAL;
constexpr DelayBasedBweUpdate_DetectorState DelayBasedBweUpdate_DetectorState_DetectorState_MAX = DelayBasedBweUpdate_DetectorState_BWE_OVERUSING;
constexpr int DelayBasedBweUpdate_DetectorState_DetectorState_ARRAYSIZE = DelayBasedBweUpdate_DetectorState_DetectorState_MAX + 1;

const std::string& DelayBasedBweUpdate_DetectorState_Name(DelayBasedBweUpdate_DetectorState value);
template<typename T>
inline const std::string& DelayBasedBweUpdate_DetectorState_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DelayBasedBweUpdate_DetectorState>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DelayBasedBweUpdate_DetectorState_Name.");
  return DelayBasedBweUpdate_DetectorState_Name(static_cast<DelayBasedBweUpdate_DetectorState>(enum_t_value));
}
bool DelayBasedBweUpdate_DetectorState_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DelayBasedBweUpdate_DetectorState* value);
enum VideoReceiveConfig_RtcpMode : int {
  VideoReceiveConfig_RtcpMode_RTCP_COMPOUND = 1,
  VideoReceiveConfig_RtcpMode_RTCP_REDUCEDSIZE = 2
};
bool VideoReceiveConfig_RtcpMode_IsValid(int value);
constexpr VideoReceiveConfig_RtcpMode VideoReceiveConfig_RtcpMode_RtcpMode_MIN = VideoReceiveConfig_RtcpMode_RTCP_COMPOUND;
constexpr VideoReceiveConfig_RtcpMode VideoReceiveConfig_RtcpMode_RtcpMode_MAX = VideoReceiveConfig_RtcpMode_RTCP_REDUCEDSIZE;
constexpr int VideoReceiveConfig_RtcpMode_RtcpMode_ARRAYSIZE = VideoReceiveConfig_RtcpMode_RtcpMode_MAX + 1;

const std::string& VideoReceiveConfig_RtcpMode_Name(VideoReceiveConfig_RtcpMode value);
template<typename T>
inline const std::string& VideoReceiveConfig_RtcpMode_Name(T enum_t_value) {
  static_assert(::std::is_same<T, VideoReceiveConfig_RtcpMode>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function VideoReceiveConfig_RtcpMode_Name.");
  return VideoReceiveConfig_RtcpMode_Name(static_cast<VideoReceiveConfig_RtcpMode>(enum_t_value));
}
bool VideoReceiveConfig_RtcpMode_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, VideoReceiveConfig_RtcpMode* value);
enum BweProbeResult_ResultType : int {
  BweProbeResult_ResultType_SUCCESS = 0,
  BweProbeResult_ResultType_INVALID_SEND_RECEIVE_INTERVAL = 1,
  BweProbeResult_ResultType_INVALID_SEND_RECEIVE_RATIO = 2,
  BweProbeResult_ResultType_TIMEOUT = 3
};
bool BweProbeResult_ResultType_IsValid(int value);
constexpr BweProbeResult_ResultType BweProbeResult_ResultType_ResultType_MIN = BweProbeResult_ResultType_SUCCESS;
constexpr BweProbeResult_ResultType BweProbeResult_ResultType_ResultType_MAX = BweProbeResult_ResultType_TIMEOUT;
constexpr int BweProbeResult_ResultType_ResultType_ARRAYSIZE = BweProbeResult_ResultType_ResultType_MAX + 1;

const std::string& BweProbeResult_ResultType_Name(BweProbeResult_ResultType value);
template<typename T>
inline const std::string& BweProbeResult_ResultType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, BweProbeResult_ResultType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function BweProbeResult_ResultType_Name.");
  return BweProbeResult_ResultType_Name(static_cast<BweProbeResult_ResultType>(enum_t_value));
}
bool BweProbeResult_ResultType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, BweProbeResult_ResultType* value);
enum IceCandidatePairConfig_IceCandidatePairConfigType : int {
  IceCandidatePairConfig_IceCandidatePairConfigType_ADDED = 0,
  IceCandidatePairConfig_IceCandidatePairConfigType_UPDATED = 1,
  IceCandidatePairConfig_IceCandidatePairConfigType_DESTROYED = 2,
  IceCandidatePairConfig_IceCandidatePairConfigType_SELECTED = 3
};
bool IceCandidatePairConfig_IceCandidatePairConfigType_IsValid(int value);
constexpr IceCandidatePairConfig_IceCandidatePairConfigType IceCandidatePairConfig_IceCandidatePairConfigType_IceCandidatePairConfigType_MIN = IceCandidatePairConfig_IceCandidatePairConfigType_ADDED;
constexpr IceCandidatePairConfig_IceCandidatePairConfigType IceCandidatePairConfig_IceCandidatePairConfigType_IceCandidatePairConfigType_MAX = IceCandidatePairConfig_IceCandidatePairConfigType_SELECTED;
constexpr int IceCandidatePairConfig_IceCandidatePairConfigType_IceCandidatePairConfigType_ARRAYSIZE = IceCandidatePairConfig_IceCandidatePairConfigType_IceCandidatePairConfigType_MAX + 1;

const std::string& IceCandidatePairConfig_IceCandidatePairConfigType_Name(IceCandidatePairConfig_IceCandidatePairConfigType value);
template<typename T>
inline const std::string& IceCandidatePairConfig_IceCandidatePairConfigType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, IceCandidatePairConfig_IceCandidatePairConfigType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function IceCandidatePairConfig_IceCandidatePairConfigType_Name.");
  return IceCandidatePairConfig_IceCandidatePairConfigType_Name(static_cast<IceCandidatePairConfig_IceCandidatePairConfigType>(enum_t_value));
}
bool IceCandidatePairConfig_IceCandidatePairConfigType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, IceCandidatePairConfig_IceCandidatePairConfigType* value);
enum IceCandidatePairConfig_IceCandidateType : int {
  IceCandidatePairConfig_IceCandidateType_LOCAL = 0,
  IceCandidatePairConfig_IceCandidateType_STUN = 1,
  IceCandidatePairConfig_IceCandidateType_PRFLX = 2,
  IceCandidatePairConfig_IceCandidateType_RELAY = 3,
  IceCandidatePairConfig_IceCandidateType_UNKNOWN_CANDIDATE_TYPE = 4
};
bool IceCandidatePairConfig_IceCandidateType_IsValid(int value);
constexpr IceCandidatePairConfig_IceCandidateType IceCandidatePairConfig_IceCandidateType_IceCandidateType_MIN = IceCandidatePairConfig_IceCandidateType_LOCAL;
constexpr IceCandidatePairConfig_IceCandidateType IceCandidatePairConfig_IceCandidateType_IceCandidateType_MAX = IceCandidatePairConfig_IceCandidateType_UNKNOWN_CANDIDATE_TYPE;
constexpr int IceCandidatePairConfig_IceCandidateType_IceCandidateType_ARRAYSIZE = IceCandidatePairConfig_IceCandidateType_IceCandidateType_MAX + 1;

const std::string& IceCandidatePairConfig_IceCandidateType_Name(IceCandidatePairConfig_IceCandidateType value);
template<typename T>
inline const std::string& IceCandidatePairConfig_IceCandidateType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, IceCandidatePairConfig_IceCandidateType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function IceCandidatePairConfig_IceCandidateType_Name.");
  return IceCandidatePairConfig_IceCandidateType_Name(static_cast<IceCandidatePairConfig_IceCandidateType>(enum_t_value));
}
bool IceCandidatePairConfig_IceCandidateType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, IceCandidatePairConfig_IceCandidateType* value);
enum IceCandidatePairConfig_Protocol : int {
  IceCandidatePairConfig_Protocol_UDP = 0,
  IceCandidatePairConfig_Protocol_TCP = 1,
  IceCandidatePairConfig_Protocol_SSLTCP = 2,
  IceCandidatePairConfig_Protocol_TLS = 3,
  IceCandidatePairConfig_Protocol_UNKNOWN_PROTOCOL = 4
};
bool IceCandidatePairConfig_Protocol_IsValid(int value);
constexpr IceCandidatePairConfig_Protocol IceCandidatePairConfig_Protocol_Protocol_MIN = IceCandidatePairConfig_Protocol_UDP;
constexpr IceCandidatePairConfig_Protocol IceCandidatePairConfig_Protocol_Protocol_MAX = IceCandidatePairConfig_Protocol_UNKNOWN_PROTOCOL;
constexpr int IceCandidatePairConfig_Protocol_Protocol_ARRAYSIZE = IceCandidatePairConfig_Protocol_Protocol_MAX + 1;

const std::string& IceCandidatePairConfig_Protocol_Name(IceCandidatePairConfig_Protocol value);
template<typename T>
inline const std::string& IceCandidatePairConfig_Protocol_Name(T enum_t_value) {
  static_assert(::std::is_same<T, IceCandidatePairConfig_Protocol>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function IceCandidatePairConfig_Protocol_Name.");
  return IceCandidatePairConfig_Protocol_Name(static_cast<IceCandidatePairConfig_Protocol>(enum_t_value));
}
bool IceCandidatePairConfig_Protocol_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, IceCandidatePairConfig_Protocol* value);
enum IceCandidatePairConfig_AddressFamily : int {
  IceCandidatePairConfig_AddressFamily_IPV4 = 0,
  IceCandidatePairConfig_AddressFamily_IPV6 = 1,
  IceCandidatePairConfig_AddressFamily_UNKNOWN_ADDRESS_FAMILY = 2
};
bool IceCandidatePairConfig_AddressFamily_IsValid(int value);
constexpr IceCandidatePairConfig_AddressFamily IceCandidatePairConfig_AddressFamily_AddressFamily_MIN = IceCandidatePairConfig_AddressFamily_IPV4;
constexpr IceCandidatePairConfig_AddressFamily IceCandidatePairConfig_AddressFamily_AddressFamily_MAX = IceCandidatePairConfig_AddressFamily_UNKNOWN_ADDRESS_FAMILY;
constexpr int IceCandidatePairConfig_AddressFamily_AddressFamily_ARRAYSIZE = IceCandidatePairConfig_AddressFamily_AddressFamily_MAX + 1;

const std::string& IceCandidatePairConfig_AddressFamily_Name(IceCandidatePairConfig_AddressFamily value);
template<typename T>
inline const std::string& IceCandidatePairConfig_AddressFamily_Name(T enum_t_value) {
  static_assert(::std::is_same<T, IceCandidatePairConfig_AddressFamily>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function IceCandidatePairConfig_AddressFamily_Name.");
  return IceCandidatePairConfig_AddressFamily_Name(static_cast<IceCandidatePairConfig_AddressFamily>(enum_t_value));
}
bool IceCandidatePairConfig_AddressFamily_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, IceCandidatePairConfig_AddressFamily* value);
enum IceCandidatePairConfig_NetworkType : int {
  IceCandidatePairConfig_NetworkType_ETHERNET = 0,
  IceCandidatePairConfig_NetworkType_LOOPBACK = 1,
  IceCandidatePairConfig_NetworkType_WIFI = 2,
  IceCandidatePairConfig_NetworkType_VPN = 3,
  IceCandidatePairConfig_NetworkType_CELLULAR = 4,
  IceCandidatePairConfig_NetworkType_UNKNOWN_NETWORK_TYPE = 5
};
bool IceCandidatePairConfig_NetworkType_IsValid(int value);
constexpr IceCandidatePairConfig_NetworkType IceCandidatePairConfig_NetworkType_NetworkType_MIN = IceCandidatePairConfig_NetworkType_ETHERNET;
constexpr IceCandidatePairConfig_NetworkType IceCandidatePairConfig_NetworkType_NetworkType_MAX = IceCandidatePairConfig_NetworkType_UNKNOWN_NETWORK_TYPE;
constexpr int IceCandidatePairConfig_NetworkType_NetworkType_ARRAYSIZE = IceCandidatePairConfig_NetworkType_NetworkType_MAX + 1;

const std::string& IceCandidatePairConfig_NetworkType_Name(IceCandidatePairConfig_NetworkType value);
template<typename T>
inline const std::string& IceCandidatePairConfig_NetworkType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, IceCandidatePairConfig_NetworkType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function IceCandidatePairConfig_NetworkType_Name.");
  return IceCandidatePairConfig_NetworkType_Name(static_cast<IceCandidatePairConfig_NetworkType>(enum_t_value));
}
bool IceCandidatePairConfig_NetworkType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, IceCandidatePairConfig_NetworkType* value);
enum IceCandidatePairEvent_IceCandidatePairEventType : int {
  IceCandidatePairEvent_IceCandidatePairEventType_CHECK_SENT = 0,
  IceCandidatePairEvent_IceCandidatePairEventType_CHECK_RECEIVED = 1,
  IceCandidatePairEvent_IceCandidatePairEventType_CHECK_RESPONSE_SENT = 2,
  IceCandidatePairEvent_IceCandidatePairEventType_CHECK_RESPONSE_RECEIVED = 3
};
bool IceCandidatePairEvent_IceCandidatePairEventType_IsValid(int value);
constexpr IceCandidatePairEvent_IceCandidatePairEventType IceCandidatePairEvent_IceCandidatePairEventType_IceCandidatePairEventType_MIN = IceCandidatePairEvent_IceCandidatePairEventType_CHECK_SENT;
constexpr IceCandidatePairEvent_IceCandidatePairEventType IceCandidatePairEvent_IceCandidatePairEventType_IceCandidatePairEventType_MAX = IceCandidatePairEvent_IceCandidatePairEventType_CHECK_RESPONSE_RECEIVED;
constexpr int IceCandidatePairEvent_IceCandidatePairEventType_IceCandidatePairEventType_ARRAYSIZE = IceCandidatePairEvent_IceCandidatePairEventType_IceCandidatePairEventType_MAX + 1;

const std::string& IceCandidatePairEvent_IceCandidatePairEventType_Name(IceCandidatePairEvent_IceCandidatePairEventType value);
template<typename T>
inline const std::string& IceCandidatePairEvent_IceCandidatePairEventType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, IceCandidatePairEvent_IceCandidatePairEventType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function IceCandidatePairEvent_IceCandidatePairEventType_Name.");
  return IceCandidatePairEvent_IceCandidatePairEventType_Name(static_cast<IceCandidatePairEvent_IceCandidatePairEventType>(enum_t_value));
}
bool IceCandidatePairEvent_IceCandidatePairEventType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, IceCandidatePairEvent_IceCandidatePairEventType* value);
enum MediaType : int {
  ANY = 0,
  AUDIO = 1,
  VIDEO = 2,
  DATA = 3
};
bool MediaType_IsValid(int value);
constexpr MediaType MediaType_MIN = ANY;
constexpr MediaType MediaType_MAX = DATA;
constexpr int MediaType_ARRAYSIZE = MediaType_MAX + 1;

const std::string& MediaType_Name(MediaType value);
template<typename T>
inline const std::string& MediaType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, MediaType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function MediaType_Name.");
  return MediaType_Name(static_cast<MediaType>(enum_t_value));
}
bool MediaType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, MediaType* value);
// ===================================================================

class EventStream final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.EventStream) */ {
 public:
  inline EventStream() : EventStream(nullptr) {}
  ~EventStream() override;
  explicit PROTOBUF_CONSTEXPR EventStream(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EventStream(const EventStream& from);
  EventStream(EventStream&& from) noexcept
    : EventStream() {
    *this = ::std::move(from);
  }

  inline EventStream& operator=(const EventStream& from) {
    CopyFrom(from);
    return *this;
  }
  inline EventStream& operator=(EventStream&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const EventStream& default_instance() {
    return *internal_default_instance();
  }
  static inline const EventStream* internal_default_instance() {
    return reinterpret_cast<const EventStream*>(
               &_EventStream_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(EventStream& a, EventStream& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(EventStream* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EventStream* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  EventStream* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<EventStream>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const EventStream& from);
  void MergeFrom(const EventStream& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(EventStream* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.EventStream";
  }
  protected:
  explicit EventStream(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kStreamFieldNumber = 1,
  };
  // repeated .webrtc.rtclog.Event stream = 1;
  int stream_size() const;
  private:
  int _internal_stream_size() const;
  public:
  void clear_stream();
  ::webrtc::rtclog::Event* mutable_stream(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::Event >*
      mutable_stream();
  private:
  const ::webrtc::rtclog::Event& _internal_stream(int index) const;
  ::webrtc::rtclog::Event* _internal_add_stream();
  public:
  const ::webrtc::rtclog::Event& stream(int index) const;
  ::webrtc::rtclog::Event* add_stream();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::Event >&
      stream() const;

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.EventStream)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::Event > stream_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class Event final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.Event) */ {
 public:
  inline Event() : Event(nullptr) {}
  ~Event() override;
  explicit PROTOBUF_CONSTEXPR Event(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Event(const Event& from);
  Event(Event&& from) noexcept
    : Event() {
    *this = ::std::move(from);
  }

  inline Event& operator=(const Event& from) {
    CopyFrom(from);
    return *this;
  }
  inline Event& operator=(Event&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const Event& default_instance() {
    return *internal_default_instance();
  }
  enum SubtypeCase {
    kRtpPacket = 3,
    kRtcpPacket = 4,
    kAudioPlayoutEvent = 5,
    kLossBasedBweUpdate = 6,
    kDelayBasedBweUpdate = 7,
    kVideoReceiverConfig = 8,
    kVideoSenderConfig = 9,
    kAudioReceiverConfig = 10,
    kAudioSenderConfig = 11,
    kAudioNetworkAdaptation = 16,
    kProbeCluster = 17,
    kProbeResult = 18,
    kAlrState = 19,
    kIceCandidatePairConfig = 20,
    kIceCandidatePairEvent = 21,
    kRemoteEstimate = 22,
    SUBTYPE_NOT_SET = 0,
  };

  static inline const Event* internal_default_instance() {
    return reinterpret_cast<const Event*>(
               &_Event_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Event& a, Event& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(Event* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Event* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Event* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Event>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const Event& from);
  void MergeFrom(const Event& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Event* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.Event";
  }
  protected:
  explicit Event(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  typedef Event_EventType EventType;
  static constexpr EventType UNKNOWN_EVENT =
    Event_EventType_UNKNOWN_EVENT;
  static constexpr EventType LOG_START =
    Event_EventType_LOG_START;
  static constexpr EventType LOG_END =
    Event_EventType_LOG_END;
  static constexpr EventType RTP_EVENT =
    Event_EventType_RTP_EVENT;
  static constexpr EventType RTCP_EVENT =
    Event_EventType_RTCP_EVENT;
  static constexpr EventType AUDIO_PLAYOUT_EVENT =
    Event_EventType_AUDIO_PLAYOUT_EVENT;
  static constexpr EventType LOSS_BASED_BWE_UPDATE =
    Event_EventType_LOSS_BASED_BWE_UPDATE;
  static constexpr EventType DELAY_BASED_BWE_UPDATE =
    Event_EventType_DELAY_BASED_BWE_UPDATE;
  static constexpr EventType VIDEO_RECEIVER_CONFIG_EVENT =
    Event_EventType_VIDEO_RECEIVER_CONFIG_EVENT;
  static constexpr EventType VIDEO_SENDER_CONFIG_EVENT =
    Event_EventType_VIDEO_SENDER_CONFIG_EVENT;
  static constexpr EventType AUDIO_RECEIVER_CONFIG_EVENT =
    Event_EventType_AUDIO_RECEIVER_CONFIG_EVENT;
  static constexpr EventType AUDIO_SENDER_CONFIG_EVENT =
    Event_EventType_AUDIO_SENDER_CONFIG_EVENT;
  static constexpr EventType AUDIO_NETWORK_ADAPTATION_EVENT =
    Event_EventType_AUDIO_NETWORK_ADAPTATION_EVENT;
  static constexpr EventType BWE_PROBE_CLUSTER_CREATED_EVENT =
    Event_EventType_BWE_PROBE_CLUSTER_CREATED_EVENT;
  static constexpr EventType BWE_PROBE_RESULT_EVENT =
    Event_EventType_BWE_PROBE_RESULT_EVENT;
  static constexpr EventType ALR_STATE_EVENT =
    Event_EventType_ALR_STATE_EVENT;
  static constexpr EventType ICE_CANDIDATE_PAIR_CONFIG =
    Event_EventType_ICE_CANDIDATE_PAIR_CONFIG;
  static constexpr EventType ICE_CANDIDATE_PAIR_EVENT =
    Event_EventType_ICE_CANDIDATE_PAIR_EVENT;
  static constexpr EventType REMOTE_ESTIMATE =
    Event_EventType_REMOTE_ESTIMATE;
  static inline bool EventType_IsValid(int value) {
    return Event_EventType_IsValid(value);
  }
  static constexpr EventType EventType_MIN =
    Event_EventType_EventType_MIN;
  static constexpr EventType EventType_MAX =
    Event_EventType_EventType_MAX;
  static constexpr int EventType_ARRAYSIZE =
    Event_EventType_EventType_ARRAYSIZE;
  template<typename T>
  static inline const std::string& EventType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, EventType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function EventType_Name.");
    return Event_EventType_Name(enum_t_value);
  }
  static inline bool EventType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      EventType* value) {
    return Event_EventType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kTimestampUsFieldNumber = 1,
    kTypeFieldNumber = 2,
    kRtpPacketFieldNumber = 3,
    kRtcpPacketFieldNumber = 4,
    kAudioPlayoutEventFieldNumber = 5,
    kLossBasedBweUpdateFieldNumber = 6,
    kDelayBasedBweUpdateFieldNumber = 7,
    kVideoReceiverConfigFieldNumber = 8,
    kVideoSenderConfigFieldNumber = 9,
    kAudioReceiverConfigFieldNumber = 10,
    kAudioSenderConfigFieldNumber = 11,
    kAudioNetworkAdaptationFieldNumber = 16,
    kProbeClusterFieldNumber = 17,
    kProbeResultFieldNumber = 18,
    kAlrStateFieldNumber = 19,
    kIceCandidatePairConfigFieldNumber = 20,
    kIceCandidatePairEventFieldNumber = 21,
    kRemoteEstimateFieldNumber = 22,
  };
  // optional int64 timestamp_us = 1;
  bool has_timestamp_us() const;
  private:
  bool _internal_has_timestamp_us() const;
  public:
  void clear_timestamp_us();
  int64_t timestamp_us() const;
  void set_timestamp_us(int64_t value);
  private:
  int64_t _internal_timestamp_us() const;
  void _internal_set_timestamp_us(int64_t value);
  public:

  // optional .webrtc.rtclog.Event.EventType type = 2;
  bool has_type() const;
  private:
  bool _internal_has_type() const;
  public:
  void clear_type();
  ::webrtc::rtclog::Event_EventType type() const;
  void set_type(::webrtc::rtclog::Event_EventType value);
  private:
  ::webrtc::rtclog::Event_EventType _internal_type() const;
  void _internal_set_type(::webrtc::rtclog::Event_EventType value);
  public:

  // .webrtc.rtclog.RtpPacket rtp_packet = 3;
  bool has_rtp_packet() const;
  private:
  bool _internal_has_rtp_packet() const;
  public:
  void clear_rtp_packet();
  const ::webrtc::rtclog::RtpPacket& rtp_packet() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::RtpPacket* release_rtp_packet();
  ::webrtc::rtclog::RtpPacket* mutable_rtp_packet();
  void set_allocated_rtp_packet(::webrtc::rtclog::RtpPacket* rtp_packet);
  private:
  const ::webrtc::rtclog::RtpPacket& _internal_rtp_packet() const;
  ::webrtc::rtclog::RtpPacket* _internal_mutable_rtp_packet();
  public:
  void unsafe_arena_set_allocated_rtp_packet(
      ::webrtc::rtclog::RtpPacket* rtp_packet);
  ::webrtc::rtclog::RtpPacket* unsafe_arena_release_rtp_packet();

  // .webrtc.rtclog.RtcpPacket rtcp_packet = 4;
  bool has_rtcp_packet() const;
  private:
  bool _internal_has_rtcp_packet() const;
  public:
  void clear_rtcp_packet();
  const ::webrtc::rtclog::RtcpPacket& rtcp_packet() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::RtcpPacket* release_rtcp_packet();
  ::webrtc::rtclog::RtcpPacket* mutable_rtcp_packet();
  void set_allocated_rtcp_packet(::webrtc::rtclog::RtcpPacket* rtcp_packet);
  private:
  const ::webrtc::rtclog::RtcpPacket& _internal_rtcp_packet() const;
  ::webrtc::rtclog::RtcpPacket* _internal_mutable_rtcp_packet();
  public:
  void unsafe_arena_set_allocated_rtcp_packet(
      ::webrtc::rtclog::RtcpPacket* rtcp_packet);
  ::webrtc::rtclog::RtcpPacket* unsafe_arena_release_rtcp_packet();

  // .webrtc.rtclog.AudioPlayoutEvent audio_playout_event = 5;
  bool has_audio_playout_event() const;
  private:
  bool _internal_has_audio_playout_event() const;
  public:
  void clear_audio_playout_event();
  const ::webrtc::rtclog::AudioPlayoutEvent& audio_playout_event() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::AudioPlayoutEvent* release_audio_playout_event();
  ::webrtc::rtclog::AudioPlayoutEvent* mutable_audio_playout_event();
  void set_allocated_audio_playout_event(::webrtc::rtclog::AudioPlayoutEvent* audio_playout_event);
  private:
  const ::webrtc::rtclog::AudioPlayoutEvent& _internal_audio_playout_event() const;
  ::webrtc::rtclog::AudioPlayoutEvent* _internal_mutable_audio_playout_event();
  public:
  void unsafe_arena_set_allocated_audio_playout_event(
      ::webrtc::rtclog::AudioPlayoutEvent* audio_playout_event);
  ::webrtc::rtclog::AudioPlayoutEvent* unsafe_arena_release_audio_playout_event();

  // .webrtc.rtclog.LossBasedBweUpdate loss_based_bwe_update = 6;
  bool has_loss_based_bwe_update() const;
  private:
  bool _internal_has_loss_based_bwe_update() const;
  public:
  void clear_loss_based_bwe_update();
  const ::webrtc::rtclog::LossBasedBweUpdate& loss_based_bwe_update() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::LossBasedBweUpdate* release_loss_based_bwe_update();
  ::webrtc::rtclog::LossBasedBweUpdate* mutable_loss_based_bwe_update();
  void set_allocated_loss_based_bwe_update(::webrtc::rtclog::LossBasedBweUpdate* loss_based_bwe_update);
  private:
  const ::webrtc::rtclog::LossBasedBweUpdate& _internal_loss_based_bwe_update() const;
  ::webrtc::rtclog::LossBasedBweUpdate* _internal_mutable_loss_based_bwe_update();
  public:
  void unsafe_arena_set_allocated_loss_based_bwe_update(
      ::webrtc::rtclog::LossBasedBweUpdate* loss_based_bwe_update);
  ::webrtc::rtclog::LossBasedBweUpdate* unsafe_arena_release_loss_based_bwe_update();

  // .webrtc.rtclog.DelayBasedBweUpdate delay_based_bwe_update = 7;
  bool has_delay_based_bwe_update() const;
  private:
  bool _internal_has_delay_based_bwe_update() const;
  public:
  void clear_delay_based_bwe_update();
  const ::webrtc::rtclog::DelayBasedBweUpdate& delay_based_bwe_update() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::DelayBasedBweUpdate* release_delay_based_bwe_update();
  ::webrtc::rtclog::DelayBasedBweUpdate* mutable_delay_based_bwe_update();
  void set_allocated_delay_based_bwe_update(::webrtc::rtclog::DelayBasedBweUpdate* delay_based_bwe_update);
  private:
  const ::webrtc::rtclog::DelayBasedBweUpdate& _internal_delay_based_bwe_update() const;
  ::webrtc::rtclog::DelayBasedBweUpdate* _internal_mutable_delay_based_bwe_update();
  public:
  void unsafe_arena_set_allocated_delay_based_bwe_update(
      ::webrtc::rtclog::DelayBasedBweUpdate* delay_based_bwe_update);
  ::webrtc::rtclog::DelayBasedBweUpdate* unsafe_arena_release_delay_based_bwe_update();

  // .webrtc.rtclog.VideoReceiveConfig video_receiver_config = 8;
  bool has_video_receiver_config() const;
  private:
  bool _internal_has_video_receiver_config() const;
  public:
  void clear_video_receiver_config();
  const ::webrtc::rtclog::VideoReceiveConfig& video_receiver_config() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::VideoReceiveConfig* release_video_receiver_config();
  ::webrtc::rtclog::VideoReceiveConfig* mutable_video_receiver_config();
  void set_allocated_video_receiver_config(::webrtc::rtclog::VideoReceiveConfig* video_receiver_config);
  private:
  const ::webrtc::rtclog::VideoReceiveConfig& _internal_video_receiver_config() const;
  ::webrtc::rtclog::VideoReceiveConfig* _internal_mutable_video_receiver_config();
  public:
  void unsafe_arena_set_allocated_video_receiver_config(
      ::webrtc::rtclog::VideoReceiveConfig* video_receiver_config);
  ::webrtc::rtclog::VideoReceiveConfig* unsafe_arena_release_video_receiver_config();

  // .webrtc.rtclog.VideoSendConfig video_sender_config = 9;
  bool has_video_sender_config() const;
  private:
  bool _internal_has_video_sender_config() const;
  public:
  void clear_video_sender_config();
  const ::webrtc::rtclog::VideoSendConfig& video_sender_config() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::VideoSendConfig* release_video_sender_config();
  ::webrtc::rtclog::VideoSendConfig* mutable_video_sender_config();
  void set_allocated_video_sender_config(::webrtc::rtclog::VideoSendConfig* video_sender_config);
  private:
  const ::webrtc::rtclog::VideoSendConfig& _internal_video_sender_config() const;
  ::webrtc::rtclog::VideoSendConfig* _internal_mutable_video_sender_config();
  public:
  void unsafe_arena_set_allocated_video_sender_config(
      ::webrtc::rtclog::VideoSendConfig* video_sender_config);
  ::webrtc::rtclog::VideoSendConfig* unsafe_arena_release_video_sender_config();

  // .webrtc.rtclog.AudioReceiveConfig audio_receiver_config = 10;
  bool has_audio_receiver_config() const;
  private:
  bool _internal_has_audio_receiver_config() const;
  public:
  void clear_audio_receiver_config();
  const ::webrtc::rtclog::AudioReceiveConfig& audio_receiver_config() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::AudioReceiveConfig* release_audio_receiver_config();
  ::webrtc::rtclog::AudioReceiveConfig* mutable_audio_receiver_config();
  void set_allocated_audio_receiver_config(::webrtc::rtclog::AudioReceiveConfig* audio_receiver_config);
  private:
  const ::webrtc::rtclog::AudioReceiveConfig& _internal_audio_receiver_config() const;
  ::webrtc::rtclog::AudioReceiveConfig* _internal_mutable_audio_receiver_config();
  public:
  void unsafe_arena_set_allocated_audio_receiver_config(
      ::webrtc::rtclog::AudioReceiveConfig* audio_receiver_config);
  ::webrtc::rtclog::AudioReceiveConfig* unsafe_arena_release_audio_receiver_config();

  // .webrtc.rtclog.AudioSendConfig audio_sender_config = 11;
  bool has_audio_sender_config() const;
  private:
  bool _internal_has_audio_sender_config() const;
  public:
  void clear_audio_sender_config();
  const ::webrtc::rtclog::AudioSendConfig& audio_sender_config() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::AudioSendConfig* release_audio_sender_config();
  ::webrtc::rtclog::AudioSendConfig* mutable_audio_sender_config();
  void set_allocated_audio_sender_config(::webrtc::rtclog::AudioSendConfig* audio_sender_config);
  private:
  const ::webrtc::rtclog::AudioSendConfig& _internal_audio_sender_config() const;
  ::webrtc::rtclog::AudioSendConfig* _internal_mutable_audio_sender_config();
  public:
  void unsafe_arena_set_allocated_audio_sender_config(
      ::webrtc::rtclog::AudioSendConfig* audio_sender_config);
  ::webrtc::rtclog::AudioSendConfig* unsafe_arena_release_audio_sender_config();

  // .webrtc.rtclog.AudioNetworkAdaptation audio_network_adaptation = 16;
  bool has_audio_network_adaptation() const;
  private:
  bool _internal_has_audio_network_adaptation() const;
  public:
  void clear_audio_network_adaptation();
  const ::webrtc::rtclog::AudioNetworkAdaptation& audio_network_adaptation() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::AudioNetworkAdaptation* release_audio_network_adaptation();
  ::webrtc::rtclog::AudioNetworkAdaptation* mutable_audio_network_adaptation();
  void set_allocated_audio_network_adaptation(::webrtc::rtclog::AudioNetworkAdaptation* audio_network_adaptation);
  private:
  const ::webrtc::rtclog::AudioNetworkAdaptation& _internal_audio_network_adaptation() const;
  ::webrtc::rtclog::AudioNetworkAdaptation* _internal_mutable_audio_network_adaptation();
  public:
  void unsafe_arena_set_allocated_audio_network_adaptation(
      ::webrtc::rtclog::AudioNetworkAdaptation* audio_network_adaptation);
  ::webrtc::rtclog::AudioNetworkAdaptation* unsafe_arena_release_audio_network_adaptation();

  // .webrtc.rtclog.BweProbeCluster probe_cluster = 17;
  bool has_probe_cluster() const;
  private:
  bool _internal_has_probe_cluster() const;
  public:
  void clear_probe_cluster();
  const ::webrtc::rtclog::BweProbeCluster& probe_cluster() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::BweProbeCluster* release_probe_cluster();
  ::webrtc::rtclog::BweProbeCluster* mutable_probe_cluster();
  void set_allocated_probe_cluster(::webrtc::rtclog::BweProbeCluster* probe_cluster);
  private:
  const ::webrtc::rtclog::BweProbeCluster& _internal_probe_cluster() const;
  ::webrtc::rtclog::BweProbeCluster* _internal_mutable_probe_cluster();
  public:
  void unsafe_arena_set_allocated_probe_cluster(
      ::webrtc::rtclog::BweProbeCluster* probe_cluster);
  ::webrtc::rtclog::BweProbeCluster* unsafe_arena_release_probe_cluster();

  // .webrtc.rtclog.BweProbeResult probe_result = 18;
  bool has_probe_result() const;
  private:
  bool _internal_has_probe_result() const;
  public:
  void clear_probe_result();
  const ::webrtc::rtclog::BweProbeResult& probe_result() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::BweProbeResult* release_probe_result();
  ::webrtc::rtclog::BweProbeResult* mutable_probe_result();
  void set_allocated_probe_result(::webrtc::rtclog::BweProbeResult* probe_result);
  private:
  const ::webrtc::rtclog::BweProbeResult& _internal_probe_result() const;
  ::webrtc::rtclog::BweProbeResult* _internal_mutable_probe_result();
  public:
  void unsafe_arena_set_allocated_probe_result(
      ::webrtc::rtclog::BweProbeResult* probe_result);
  ::webrtc::rtclog::BweProbeResult* unsafe_arena_release_probe_result();

  // .webrtc.rtclog.AlrState alr_state = 19;
  bool has_alr_state() const;
  private:
  bool _internal_has_alr_state() const;
  public:
  void clear_alr_state();
  const ::webrtc::rtclog::AlrState& alr_state() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::AlrState* release_alr_state();
  ::webrtc::rtclog::AlrState* mutable_alr_state();
  void set_allocated_alr_state(::webrtc::rtclog::AlrState* alr_state);
  private:
  const ::webrtc::rtclog::AlrState& _internal_alr_state() const;
  ::webrtc::rtclog::AlrState* _internal_mutable_alr_state();
  public:
  void unsafe_arena_set_allocated_alr_state(
      ::webrtc::rtclog::AlrState* alr_state);
  ::webrtc::rtclog::AlrState* unsafe_arena_release_alr_state();

  // .webrtc.rtclog.IceCandidatePairConfig ice_candidate_pair_config = 20;
  bool has_ice_candidate_pair_config() const;
  private:
  bool _internal_has_ice_candidate_pair_config() const;
  public:
  void clear_ice_candidate_pair_config();
  const ::webrtc::rtclog::IceCandidatePairConfig& ice_candidate_pair_config() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::IceCandidatePairConfig* release_ice_candidate_pair_config();
  ::webrtc::rtclog::IceCandidatePairConfig* mutable_ice_candidate_pair_config();
  void set_allocated_ice_candidate_pair_config(::webrtc::rtclog::IceCandidatePairConfig* ice_candidate_pair_config);
  private:
  const ::webrtc::rtclog::IceCandidatePairConfig& _internal_ice_candidate_pair_config() const;
  ::webrtc::rtclog::IceCandidatePairConfig* _internal_mutable_ice_candidate_pair_config();
  public:
  void unsafe_arena_set_allocated_ice_candidate_pair_config(
      ::webrtc::rtclog::IceCandidatePairConfig* ice_candidate_pair_config);
  ::webrtc::rtclog::IceCandidatePairConfig* unsafe_arena_release_ice_candidate_pair_config();

  // .webrtc.rtclog.IceCandidatePairEvent ice_candidate_pair_event = 21;
  bool has_ice_candidate_pair_event() const;
  private:
  bool _internal_has_ice_candidate_pair_event() const;
  public:
  void clear_ice_candidate_pair_event();
  const ::webrtc::rtclog::IceCandidatePairEvent& ice_candidate_pair_event() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::IceCandidatePairEvent* release_ice_candidate_pair_event();
  ::webrtc::rtclog::IceCandidatePairEvent* mutable_ice_candidate_pair_event();
  void set_allocated_ice_candidate_pair_event(::webrtc::rtclog::IceCandidatePairEvent* ice_candidate_pair_event);
  private:
  const ::webrtc::rtclog::IceCandidatePairEvent& _internal_ice_candidate_pair_event() const;
  ::webrtc::rtclog::IceCandidatePairEvent* _internal_mutable_ice_candidate_pair_event();
  public:
  void unsafe_arena_set_allocated_ice_candidate_pair_event(
      ::webrtc::rtclog::IceCandidatePairEvent* ice_candidate_pair_event);
  ::webrtc::rtclog::IceCandidatePairEvent* unsafe_arena_release_ice_candidate_pair_event();

  // .webrtc.rtclog.RemoteEstimate remote_estimate = 22;
  bool has_remote_estimate() const;
  private:
  bool _internal_has_remote_estimate() const;
  public:
  void clear_remote_estimate();
  const ::webrtc::rtclog::RemoteEstimate& remote_estimate() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::RemoteEstimate* release_remote_estimate();
  ::webrtc::rtclog::RemoteEstimate* mutable_remote_estimate();
  void set_allocated_remote_estimate(::webrtc::rtclog::RemoteEstimate* remote_estimate);
  private:
  const ::webrtc::rtclog::RemoteEstimate& _internal_remote_estimate() const;
  ::webrtc::rtclog::RemoteEstimate* _internal_mutable_remote_estimate();
  public:
  void unsafe_arena_set_allocated_remote_estimate(
      ::webrtc::rtclog::RemoteEstimate* remote_estimate);
  ::webrtc::rtclog::RemoteEstimate* unsafe_arena_release_remote_estimate();

  void clear_subtype();
  SubtypeCase subtype_case() const;
  // @@protoc_insertion_point(class_scope:webrtc.rtclog.Event)
 private:
  class _Internal;
  void set_has_rtp_packet();
  void set_has_rtcp_packet();
  void set_has_audio_playout_event();
  void set_has_loss_based_bwe_update();
  void set_has_delay_based_bwe_update();
  void set_has_video_receiver_config();
  void set_has_video_sender_config();
  void set_has_audio_receiver_config();
  void set_has_audio_sender_config();
  void set_has_audio_network_adaptation();
  void set_has_probe_cluster();
  void set_has_probe_result();
  void set_has_alr_state();
  void set_has_ice_candidate_pair_config();
  void set_has_ice_candidate_pair_event();
  void set_has_remote_estimate();

  inline bool has_subtype() const;
  inline void clear_has_subtype();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int64_t timestamp_us_;
  int type_;
  union SubtypeUnion {
    constexpr SubtypeUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    ::webrtc::rtclog::RtpPacket* rtp_packet_;
    ::webrtc::rtclog::RtcpPacket* rtcp_packet_;
    ::webrtc::rtclog::AudioPlayoutEvent* audio_playout_event_;
    ::webrtc::rtclog::LossBasedBweUpdate* loss_based_bwe_update_;
    ::webrtc::rtclog::DelayBasedBweUpdate* delay_based_bwe_update_;
    ::webrtc::rtclog::VideoReceiveConfig* video_receiver_config_;
    ::webrtc::rtclog::VideoSendConfig* video_sender_config_;
    ::webrtc::rtclog::AudioReceiveConfig* audio_receiver_config_;
    ::webrtc::rtclog::AudioSendConfig* audio_sender_config_;
    ::webrtc::rtclog::AudioNetworkAdaptation* audio_network_adaptation_;
    ::webrtc::rtclog::BweProbeCluster* probe_cluster_;
    ::webrtc::rtclog::BweProbeResult* probe_result_;
    ::webrtc::rtclog::AlrState* alr_state_;
    ::webrtc::rtclog::IceCandidatePairConfig* ice_candidate_pair_config_;
    ::webrtc::rtclog::IceCandidatePairEvent* ice_candidate_pair_event_;
    ::webrtc::rtclog::RemoteEstimate* remote_estimate_;
  } subtype_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class RtpPacket final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.RtpPacket) */ {
 public:
  inline RtpPacket() : RtpPacket(nullptr) {}
  ~RtpPacket() override;
  explicit PROTOBUF_CONSTEXPR RtpPacket(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RtpPacket(const RtpPacket& from);
  RtpPacket(RtpPacket&& from) noexcept
    : RtpPacket() {
    *this = ::std::move(from);
  }

  inline RtpPacket& operator=(const RtpPacket& from) {
    CopyFrom(from);
    return *this;
  }
  inline RtpPacket& operator=(RtpPacket&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const RtpPacket& default_instance() {
    return *internal_default_instance();
  }
  static inline const RtpPacket* internal_default_instance() {
    return reinterpret_cast<const RtpPacket*>(
               &_RtpPacket_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(RtpPacket& a, RtpPacket& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(RtpPacket* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RtpPacket* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RtpPacket* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RtpPacket>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const RtpPacket& from);
  void MergeFrom(const RtpPacket& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RtpPacket* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.RtpPacket";
  }
  protected:
  explicit RtpPacket(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHeaderFieldNumber = 4,
    kIncomingFieldNumber = 1,
    kTypeFieldNumber = 2,
    kPacketLengthFieldNumber = 3,
    kProbeClusterIdFieldNumber = 5,
  };
  // optional bytes header = 4;
  bool has_header() const;
  private:
  bool _internal_has_header() const;
  public:
  void clear_header();
  const std::string& header() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_header(ArgT0&& arg0, ArgT... args);
  std::string* mutable_header();
  PROTOBUF_NODISCARD std::string* release_header();
  void set_allocated_header(std::string* header);
  private:
  const std::string& _internal_header() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_header(const std::string& value);
  std::string* _internal_mutable_header();
  public:

  // optional bool incoming = 1;
  bool has_incoming() const;
  private:
  bool _internal_has_incoming() const;
  public:
  void clear_incoming();
  bool incoming() const;
  void set_incoming(bool value);
  private:
  bool _internal_incoming() const;
  void _internal_set_incoming(bool value);
  public:

  // optional .webrtc.rtclog.MediaType type = 2 [deprecated = true];
  PROTOBUF_DEPRECATED bool has_type() const;
  private:
  bool _internal_has_type() const;
  public:
  PROTOBUF_DEPRECATED void clear_type();
  PROTOBUF_DEPRECATED ::webrtc::rtclog::MediaType type() const;
  PROTOBUF_DEPRECATED void set_type(::webrtc::rtclog::MediaType value);
  private:
  ::webrtc::rtclog::MediaType _internal_type() const;
  void _internal_set_type(::webrtc::rtclog::MediaType value);
  public:

  // optional uint32 packet_length = 3;
  bool has_packet_length() const;
  private:
  bool _internal_has_packet_length() const;
  public:
  void clear_packet_length();
  uint32_t packet_length() const;
  void set_packet_length(uint32_t value);
  private:
  uint32_t _internal_packet_length() const;
  void _internal_set_packet_length(uint32_t value);
  public:

  // optional int32 probe_cluster_id = 5;
  bool has_probe_cluster_id() const;
  private:
  bool _internal_has_probe_cluster_id() const;
  public:
  void clear_probe_cluster_id();
  int32_t probe_cluster_id() const;
  void set_probe_cluster_id(int32_t value);
  private:
  int32_t _internal_probe_cluster_id() const;
  void _internal_set_probe_cluster_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.RtpPacket)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr header_;
  bool incoming_;
  int type_;
  uint32_t packet_length_;
  int32_t probe_cluster_id_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class RtcpPacket final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.RtcpPacket) */ {
 public:
  inline RtcpPacket() : RtcpPacket(nullptr) {}
  ~RtcpPacket() override;
  explicit PROTOBUF_CONSTEXPR RtcpPacket(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RtcpPacket(const RtcpPacket& from);
  RtcpPacket(RtcpPacket&& from) noexcept
    : RtcpPacket() {
    *this = ::std::move(from);
  }

  inline RtcpPacket& operator=(const RtcpPacket& from) {
    CopyFrom(from);
    return *this;
  }
  inline RtcpPacket& operator=(RtcpPacket&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const RtcpPacket& default_instance() {
    return *internal_default_instance();
  }
  static inline const RtcpPacket* internal_default_instance() {
    return reinterpret_cast<const RtcpPacket*>(
               &_RtcpPacket_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(RtcpPacket& a, RtcpPacket& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(RtcpPacket* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RtcpPacket* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RtcpPacket* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RtcpPacket>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const RtcpPacket& from);
  void MergeFrom(const RtcpPacket& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RtcpPacket* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.RtcpPacket";
  }
  protected:
  explicit RtcpPacket(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPacketDataFieldNumber = 3,
    kIncomingFieldNumber = 1,
    kTypeFieldNumber = 2,
  };
  // optional bytes packet_data = 3;
  bool has_packet_data() const;
  private:
  bool _internal_has_packet_data() const;
  public:
  void clear_packet_data();
  const std::string& packet_data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_packet_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_packet_data();
  PROTOBUF_NODISCARD std::string* release_packet_data();
  void set_allocated_packet_data(std::string* packet_data);
  private:
  const std::string& _internal_packet_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_packet_data(const std::string& value);
  std::string* _internal_mutable_packet_data();
  public:

  // optional bool incoming = 1;
  bool has_incoming() const;
  private:
  bool _internal_has_incoming() const;
  public:
  void clear_incoming();
  bool incoming() const;
  void set_incoming(bool value);
  private:
  bool _internal_incoming() const;
  void _internal_set_incoming(bool value);
  public:

  // optional .webrtc.rtclog.MediaType type = 2 [deprecated = true];
  PROTOBUF_DEPRECATED bool has_type() const;
  private:
  bool _internal_has_type() const;
  public:
  PROTOBUF_DEPRECATED void clear_type();
  PROTOBUF_DEPRECATED ::webrtc::rtclog::MediaType type() const;
  PROTOBUF_DEPRECATED void set_type(::webrtc::rtclog::MediaType value);
  private:
  ::webrtc::rtclog::MediaType _internal_type() const;
  void _internal_set_type(::webrtc::rtclog::MediaType value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.RtcpPacket)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr packet_data_;
  bool incoming_;
  int type_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class AudioPlayoutEvent final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.AudioPlayoutEvent) */ {
 public:
  inline AudioPlayoutEvent() : AudioPlayoutEvent(nullptr) {}
  ~AudioPlayoutEvent() override;
  explicit PROTOBUF_CONSTEXPR AudioPlayoutEvent(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AudioPlayoutEvent(const AudioPlayoutEvent& from);
  AudioPlayoutEvent(AudioPlayoutEvent&& from) noexcept
    : AudioPlayoutEvent() {
    *this = ::std::move(from);
  }

  inline AudioPlayoutEvent& operator=(const AudioPlayoutEvent& from) {
    CopyFrom(from);
    return *this;
  }
  inline AudioPlayoutEvent& operator=(AudioPlayoutEvent&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const AudioPlayoutEvent& default_instance() {
    return *internal_default_instance();
  }
  static inline const AudioPlayoutEvent* internal_default_instance() {
    return reinterpret_cast<const AudioPlayoutEvent*>(
               &_AudioPlayoutEvent_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(AudioPlayoutEvent& a, AudioPlayoutEvent& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(AudioPlayoutEvent* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AudioPlayoutEvent* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AudioPlayoutEvent* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AudioPlayoutEvent>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const AudioPlayoutEvent& from);
  void MergeFrom(const AudioPlayoutEvent& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(AudioPlayoutEvent* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.AudioPlayoutEvent";
  }
  protected:
  explicit AudioPlayoutEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLocalSsrcFieldNumber = 2,
  };
  // optional uint32 local_ssrc = 2;
  bool has_local_ssrc() const;
  private:
  bool _internal_has_local_ssrc() const;
  public:
  void clear_local_ssrc();
  uint32_t local_ssrc() const;
  void set_local_ssrc(uint32_t value);
  private:
  uint32_t _internal_local_ssrc() const;
  void _internal_set_local_ssrc(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.AudioPlayoutEvent)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t local_ssrc_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class LossBasedBweUpdate final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.LossBasedBweUpdate) */ {
 public:
  inline LossBasedBweUpdate() : LossBasedBweUpdate(nullptr) {}
  ~LossBasedBweUpdate() override;
  explicit PROTOBUF_CONSTEXPR LossBasedBweUpdate(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LossBasedBweUpdate(const LossBasedBweUpdate& from);
  LossBasedBweUpdate(LossBasedBweUpdate&& from) noexcept
    : LossBasedBweUpdate() {
    *this = ::std::move(from);
  }

  inline LossBasedBweUpdate& operator=(const LossBasedBweUpdate& from) {
    CopyFrom(from);
    return *this;
  }
  inline LossBasedBweUpdate& operator=(LossBasedBweUpdate&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const LossBasedBweUpdate& default_instance() {
    return *internal_default_instance();
  }
  static inline const LossBasedBweUpdate* internal_default_instance() {
    return reinterpret_cast<const LossBasedBweUpdate*>(
               &_LossBasedBweUpdate_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(LossBasedBweUpdate& a, LossBasedBweUpdate& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(LossBasedBweUpdate* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LossBasedBweUpdate* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LossBasedBweUpdate* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LossBasedBweUpdate>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const LossBasedBweUpdate& from);
  void MergeFrom(const LossBasedBweUpdate& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(LossBasedBweUpdate* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.LossBasedBweUpdate";
  }
  protected:
  explicit LossBasedBweUpdate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBitrateBpsFieldNumber = 1,
    kFractionLossFieldNumber = 2,
    kTotalPacketsFieldNumber = 3,
  };
  // optional int32 bitrate_bps = 1;
  bool has_bitrate_bps() const;
  private:
  bool _internal_has_bitrate_bps() const;
  public:
  void clear_bitrate_bps();
  int32_t bitrate_bps() const;
  void set_bitrate_bps(int32_t value);
  private:
  int32_t _internal_bitrate_bps() const;
  void _internal_set_bitrate_bps(int32_t value);
  public:

  // optional uint32 fraction_loss = 2;
  bool has_fraction_loss() const;
  private:
  bool _internal_has_fraction_loss() const;
  public:
  void clear_fraction_loss();
  uint32_t fraction_loss() const;
  void set_fraction_loss(uint32_t value);
  private:
  uint32_t _internal_fraction_loss() const;
  void _internal_set_fraction_loss(uint32_t value);
  public:

  // optional int32 total_packets = 3;
  bool has_total_packets() const;
  private:
  bool _internal_has_total_packets() const;
  public:
  void clear_total_packets();
  int32_t total_packets() const;
  void set_total_packets(int32_t value);
  private:
  int32_t _internal_total_packets() const;
  void _internal_set_total_packets(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.LossBasedBweUpdate)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int32_t bitrate_bps_;
  uint32_t fraction_loss_;
  int32_t total_packets_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class DelayBasedBweUpdate final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.DelayBasedBweUpdate) */ {
 public:
  inline DelayBasedBweUpdate() : DelayBasedBweUpdate(nullptr) {}
  ~DelayBasedBweUpdate() override;
  explicit PROTOBUF_CONSTEXPR DelayBasedBweUpdate(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DelayBasedBweUpdate(const DelayBasedBweUpdate& from);
  DelayBasedBweUpdate(DelayBasedBweUpdate&& from) noexcept
    : DelayBasedBweUpdate() {
    *this = ::std::move(from);
  }

  inline DelayBasedBweUpdate& operator=(const DelayBasedBweUpdate& from) {
    CopyFrom(from);
    return *this;
  }
  inline DelayBasedBweUpdate& operator=(DelayBasedBweUpdate&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const DelayBasedBweUpdate& default_instance() {
    return *internal_default_instance();
  }
  static inline const DelayBasedBweUpdate* internal_default_instance() {
    return reinterpret_cast<const DelayBasedBweUpdate*>(
               &_DelayBasedBweUpdate_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(DelayBasedBweUpdate& a, DelayBasedBweUpdate& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(DelayBasedBweUpdate* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DelayBasedBweUpdate* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DelayBasedBweUpdate* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DelayBasedBweUpdate>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const DelayBasedBweUpdate& from);
  void MergeFrom(const DelayBasedBweUpdate& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(DelayBasedBweUpdate* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.DelayBasedBweUpdate";
  }
  protected:
  explicit DelayBasedBweUpdate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  typedef DelayBasedBweUpdate_DetectorState DetectorState;
  static constexpr DetectorState BWE_NORMAL =
    DelayBasedBweUpdate_DetectorState_BWE_NORMAL;
  static constexpr DetectorState BWE_UNDERUSING =
    DelayBasedBweUpdate_DetectorState_BWE_UNDERUSING;
  static constexpr DetectorState BWE_OVERUSING =
    DelayBasedBweUpdate_DetectorState_BWE_OVERUSING;
  static inline bool DetectorState_IsValid(int value) {
    return DelayBasedBweUpdate_DetectorState_IsValid(value);
  }
  static constexpr DetectorState DetectorState_MIN =
    DelayBasedBweUpdate_DetectorState_DetectorState_MIN;
  static constexpr DetectorState DetectorState_MAX =
    DelayBasedBweUpdate_DetectorState_DetectorState_MAX;
  static constexpr int DetectorState_ARRAYSIZE =
    DelayBasedBweUpdate_DetectorState_DetectorState_ARRAYSIZE;
  template<typename T>
  static inline const std::string& DetectorState_Name(T enum_t_value) {
    static_assert(::std::is_same<T, DetectorState>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function DetectorState_Name.");
    return DelayBasedBweUpdate_DetectorState_Name(enum_t_value);
  }
  static inline bool DetectorState_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      DetectorState* value) {
    return DelayBasedBweUpdate_DetectorState_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kBitrateBpsFieldNumber = 1,
    kDetectorStateFieldNumber = 2,
  };
  // optional int32 bitrate_bps = 1;
  bool has_bitrate_bps() const;
  private:
  bool _internal_has_bitrate_bps() const;
  public:
  void clear_bitrate_bps();
  int32_t bitrate_bps() const;
  void set_bitrate_bps(int32_t value);
  private:
  int32_t _internal_bitrate_bps() const;
  void _internal_set_bitrate_bps(int32_t value);
  public:

  // optional .webrtc.rtclog.DelayBasedBweUpdate.DetectorState detector_state = 2;
  bool has_detector_state() const;
  private:
  bool _internal_has_detector_state() const;
  public:
  void clear_detector_state();
  ::webrtc::rtclog::DelayBasedBweUpdate_DetectorState detector_state() const;
  void set_detector_state(::webrtc::rtclog::DelayBasedBweUpdate_DetectorState value);
  private:
  ::webrtc::rtclog::DelayBasedBweUpdate_DetectorState _internal_detector_state() const;
  void _internal_set_detector_state(::webrtc::rtclog::DelayBasedBweUpdate_DetectorState value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.DelayBasedBweUpdate)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int32_t bitrate_bps_;
  int detector_state_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class VideoReceiveConfig final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.VideoReceiveConfig) */ {
 public:
  inline VideoReceiveConfig() : VideoReceiveConfig(nullptr) {}
  ~VideoReceiveConfig() override;
  explicit PROTOBUF_CONSTEXPR VideoReceiveConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VideoReceiveConfig(const VideoReceiveConfig& from);
  VideoReceiveConfig(VideoReceiveConfig&& from) noexcept
    : VideoReceiveConfig() {
    *this = ::std::move(from);
  }

  inline VideoReceiveConfig& operator=(const VideoReceiveConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline VideoReceiveConfig& operator=(VideoReceiveConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const VideoReceiveConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const VideoReceiveConfig* internal_default_instance() {
    return reinterpret_cast<const VideoReceiveConfig*>(
               &_VideoReceiveConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(VideoReceiveConfig& a, VideoReceiveConfig& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(VideoReceiveConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VideoReceiveConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VideoReceiveConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<VideoReceiveConfig>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const VideoReceiveConfig& from);
  void MergeFrom(const VideoReceiveConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(VideoReceiveConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.VideoReceiveConfig";
  }
  protected:
  explicit VideoReceiveConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  typedef VideoReceiveConfig_RtcpMode RtcpMode;
  static constexpr RtcpMode RTCP_COMPOUND =
    VideoReceiveConfig_RtcpMode_RTCP_COMPOUND;
  static constexpr RtcpMode RTCP_REDUCEDSIZE =
    VideoReceiveConfig_RtcpMode_RTCP_REDUCEDSIZE;
  static inline bool RtcpMode_IsValid(int value) {
    return VideoReceiveConfig_RtcpMode_IsValid(value);
  }
  static constexpr RtcpMode RtcpMode_MIN =
    VideoReceiveConfig_RtcpMode_RtcpMode_MIN;
  static constexpr RtcpMode RtcpMode_MAX =
    VideoReceiveConfig_RtcpMode_RtcpMode_MAX;
  static constexpr int RtcpMode_ARRAYSIZE =
    VideoReceiveConfig_RtcpMode_RtcpMode_ARRAYSIZE;
  template<typename T>
  static inline const std::string& RtcpMode_Name(T enum_t_value) {
    static_assert(::std::is_same<T, RtcpMode>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function RtcpMode_Name.");
    return VideoReceiveConfig_RtcpMode_Name(enum_t_value);
  }
  static inline bool RtcpMode_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      RtcpMode* value) {
    return VideoReceiveConfig_RtcpMode_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kRtxMapFieldNumber = 5,
    kHeaderExtensionsFieldNumber = 6,
    kDecodersFieldNumber = 7,
    kRemoteSsrcFieldNumber = 1,
    kLocalSsrcFieldNumber = 2,
    kRembFieldNumber = 4,
    kRtcpModeFieldNumber = 3,
  };
  // repeated .webrtc.rtclog.RtxMap rtx_map = 5;
  int rtx_map_size() const;
  private:
  int _internal_rtx_map_size() const;
  public:
  void clear_rtx_map();
  ::webrtc::rtclog::RtxMap* mutable_rtx_map(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtxMap >*
      mutable_rtx_map();
  private:
  const ::webrtc::rtclog::RtxMap& _internal_rtx_map(int index) const;
  ::webrtc::rtclog::RtxMap* _internal_add_rtx_map();
  public:
  const ::webrtc::rtclog::RtxMap& rtx_map(int index) const;
  ::webrtc::rtclog::RtxMap* add_rtx_map();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtxMap >&
      rtx_map() const;

  // repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 6;
  int header_extensions_size() const;
  private:
  int _internal_header_extensions_size() const;
  public:
  void clear_header_extensions();
  ::webrtc::rtclog::RtpHeaderExtension* mutable_header_extensions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension >*
      mutable_header_extensions();
  private:
  const ::webrtc::rtclog::RtpHeaderExtension& _internal_header_extensions(int index) const;
  ::webrtc::rtclog::RtpHeaderExtension* _internal_add_header_extensions();
  public:
  const ::webrtc::rtclog::RtpHeaderExtension& header_extensions(int index) const;
  ::webrtc::rtclog::RtpHeaderExtension* add_header_extensions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension >&
      header_extensions() const;

  // repeated .webrtc.rtclog.DecoderConfig decoders = 7;
  int decoders_size() const;
  private:
  int _internal_decoders_size() const;
  public:
  void clear_decoders();
  ::webrtc::rtclog::DecoderConfig* mutable_decoders(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::DecoderConfig >*
      mutable_decoders();
  private:
  const ::webrtc::rtclog::DecoderConfig& _internal_decoders(int index) const;
  ::webrtc::rtclog::DecoderConfig* _internal_add_decoders();
  public:
  const ::webrtc::rtclog::DecoderConfig& decoders(int index) const;
  ::webrtc::rtclog::DecoderConfig* add_decoders();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::DecoderConfig >&
      decoders() const;

  // optional uint32 remote_ssrc = 1;
  bool has_remote_ssrc() const;
  private:
  bool _internal_has_remote_ssrc() const;
  public:
  void clear_remote_ssrc();
  uint32_t remote_ssrc() const;
  void set_remote_ssrc(uint32_t value);
  private:
  uint32_t _internal_remote_ssrc() const;
  void _internal_set_remote_ssrc(uint32_t value);
  public:

  // optional uint32 local_ssrc = 2;
  bool has_local_ssrc() const;
  private:
  bool _internal_has_local_ssrc() const;
  public:
  void clear_local_ssrc();
  uint32_t local_ssrc() const;
  void set_local_ssrc(uint32_t value);
  private:
  uint32_t _internal_local_ssrc() const;
  void _internal_set_local_ssrc(uint32_t value);
  public:

  // optional bool remb = 4;
  bool has_remb() const;
  private:
  bool _internal_has_remb() const;
  public:
  void clear_remb();
  bool remb() const;
  void set_remb(bool value);
  private:
  bool _internal_remb() const;
  void _internal_set_remb(bool value);
  public:

  // optional .webrtc.rtclog.VideoReceiveConfig.RtcpMode rtcp_mode = 3;
  bool has_rtcp_mode() const;
  private:
  bool _internal_has_rtcp_mode() const;
  public:
  void clear_rtcp_mode();
  ::webrtc::rtclog::VideoReceiveConfig_RtcpMode rtcp_mode() const;
  void set_rtcp_mode(::webrtc::rtclog::VideoReceiveConfig_RtcpMode value);
  private:
  ::webrtc::rtclog::VideoReceiveConfig_RtcpMode _internal_rtcp_mode() const;
  void _internal_set_rtcp_mode(::webrtc::rtclog::VideoReceiveConfig_RtcpMode value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.VideoReceiveConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtxMap > rtx_map_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension > header_extensions_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::DecoderConfig > decoders_;
  uint32_t remote_ssrc_;
  uint32_t local_ssrc_;
  bool remb_;
  int rtcp_mode_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class DecoderConfig final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.DecoderConfig) */ {
 public:
  inline DecoderConfig() : DecoderConfig(nullptr) {}
  ~DecoderConfig() override;
  explicit PROTOBUF_CONSTEXPR DecoderConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DecoderConfig(const DecoderConfig& from);
  DecoderConfig(DecoderConfig&& from) noexcept
    : DecoderConfig() {
    *this = ::std::move(from);
  }

  inline DecoderConfig& operator=(const DecoderConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline DecoderConfig& operator=(DecoderConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const DecoderConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const DecoderConfig* internal_default_instance() {
    return reinterpret_cast<const DecoderConfig*>(
               &_DecoderConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(DecoderConfig& a, DecoderConfig& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(DecoderConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DecoderConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DecoderConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DecoderConfig>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const DecoderConfig& from);
  void MergeFrom(const DecoderConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(DecoderConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.DecoderConfig";
  }
  protected:
  explicit DecoderConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kPayloadTypeFieldNumber = 2,
  };
  // optional string name = 1;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional int32 payload_type = 2;
  bool has_payload_type() const;
  private:
  bool _internal_has_payload_type() const;
  public:
  void clear_payload_type();
  int32_t payload_type() const;
  void set_payload_type(int32_t value);
  private:
  int32_t _internal_payload_type() const;
  void _internal_set_payload_type(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.DecoderConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  int32_t payload_type_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class RtpHeaderExtension final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.RtpHeaderExtension) */ {
 public:
  inline RtpHeaderExtension() : RtpHeaderExtension(nullptr) {}
  ~RtpHeaderExtension() override;
  explicit PROTOBUF_CONSTEXPR RtpHeaderExtension(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RtpHeaderExtension(const RtpHeaderExtension& from);
  RtpHeaderExtension(RtpHeaderExtension&& from) noexcept
    : RtpHeaderExtension() {
    *this = ::std::move(from);
  }

  inline RtpHeaderExtension& operator=(const RtpHeaderExtension& from) {
    CopyFrom(from);
    return *this;
  }
  inline RtpHeaderExtension& operator=(RtpHeaderExtension&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const RtpHeaderExtension& default_instance() {
    return *internal_default_instance();
  }
  static inline const RtpHeaderExtension* internal_default_instance() {
    return reinterpret_cast<const RtpHeaderExtension*>(
               &_RtpHeaderExtension_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(RtpHeaderExtension& a, RtpHeaderExtension& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(RtpHeaderExtension* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RtpHeaderExtension* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RtpHeaderExtension* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RtpHeaderExtension>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const RtpHeaderExtension& from);
  void MergeFrom(const RtpHeaderExtension& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RtpHeaderExtension* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.RtpHeaderExtension";
  }
  protected:
  explicit RtpHeaderExtension(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kIdFieldNumber = 2,
  };
  // optional string name = 1;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional int32 id = 2;
  bool has_id() const;
  private:
  bool _internal_has_id() const;
  public:
  void clear_id();
  int32_t id() const;
  void set_id(int32_t value);
  private:
  int32_t _internal_id() const;
  void _internal_set_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.RtpHeaderExtension)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  int32_t id_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class RtxConfig final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.RtxConfig) */ {
 public:
  inline RtxConfig() : RtxConfig(nullptr) {}
  ~RtxConfig() override;
  explicit PROTOBUF_CONSTEXPR RtxConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RtxConfig(const RtxConfig& from);
  RtxConfig(RtxConfig&& from) noexcept
    : RtxConfig() {
    *this = ::std::move(from);
  }

  inline RtxConfig& operator=(const RtxConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline RtxConfig& operator=(RtxConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const RtxConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const RtxConfig* internal_default_instance() {
    return reinterpret_cast<const RtxConfig*>(
               &_RtxConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(RtxConfig& a, RtxConfig& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(RtxConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RtxConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RtxConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RtxConfig>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const RtxConfig& from);
  void MergeFrom(const RtxConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RtxConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.RtxConfig";
  }
  protected:
  explicit RtxConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRtxSsrcFieldNumber = 1,
    kRtxPayloadTypeFieldNumber = 2,
  };
  // optional uint32 rtx_ssrc = 1;
  bool has_rtx_ssrc() const;
  private:
  bool _internal_has_rtx_ssrc() const;
  public:
  void clear_rtx_ssrc();
  uint32_t rtx_ssrc() const;
  void set_rtx_ssrc(uint32_t value);
  private:
  uint32_t _internal_rtx_ssrc() const;
  void _internal_set_rtx_ssrc(uint32_t value);
  public:

  // optional int32 rtx_payload_type = 2;
  bool has_rtx_payload_type() const;
  private:
  bool _internal_has_rtx_payload_type() const;
  public:
  void clear_rtx_payload_type();
  int32_t rtx_payload_type() const;
  void set_rtx_payload_type(int32_t value);
  private:
  int32_t _internal_rtx_payload_type() const;
  void _internal_set_rtx_payload_type(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.RtxConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t rtx_ssrc_;
  int32_t rtx_payload_type_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class RtxMap final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.RtxMap) */ {
 public:
  inline RtxMap() : RtxMap(nullptr) {}
  ~RtxMap() override;
  explicit PROTOBUF_CONSTEXPR RtxMap(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RtxMap(const RtxMap& from);
  RtxMap(RtxMap&& from) noexcept
    : RtxMap() {
    *this = ::std::move(from);
  }

  inline RtxMap& operator=(const RtxMap& from) {
    CopyFrom(from);
    return *this;
  }
  inline RtxMap& operator=(RtxMap&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const RtxMap& default_instance() {
    return *internal_default_instance();
  }
  static inline const RtxMap* internal_default_instance() {
    return reinterpret_cast<const RtxMap*>(
               &_RtxMap_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(RtxMap& a, RtxMap& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(RtxMap* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RtxMap* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RtxMap* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RtxMap>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const RtxMap& from);
  void MergeFrom(const RtxMap& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RtxMap* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.RtxMap";
  }
  protected:
  explicit RtxMap(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kConfigFieldNumber = 2,
    kPayloadTypeFieldNumber = 1,
  };
  // optional .webrtc.rtclog.RtxConfig config = 2;
  bool has_config() const;
  private:
  bool _internal_has_config() const;
  public:
  void clear_config();
  const ::webrtc::rtclog::RtxConfig& config() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::RtxConfig* release_config();
  ::webrtc::rtclog::RtxConfig* mutable_config();
  void set_allocated_config(::webrtc::rtclog::RtxConfig* config);
  private:
  const ::webrtc::rtclog::RtxConfig& _internal_config() const;
  ::webrtc::rtclog::RtxConfig* _internal_mutable_config();
  public:
  void unsafe_arena_set_allocated_config(
      ::webrtc::rtclog::RtxConfig* config);
  ::webrtc::rtclog::RtxConfig* unsafe_arena_release_config();

  // optional int32 payload_type = 1;
  bool has_payload_type() const;
  private:
  bool _internal_has_payload_type() const;
  public:
  void clear_payload_type();
  int32_t payload_type() const;
  void set_payload_type(int32_t value);
  private:
  int32_t _internal_payload_type() const;
  void _internal_set_payload_type(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.RtxMap)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::webrtc::rtclog::RtxConfig* config_;
  int32_t payload_type_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class VideoSendConfig final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.VideoSendConfig) */ {
 public:
  inline VideoSendConfig() : VideoSendConfig(nullptr) {}
  ~VideoSendConfig() override;
  explicit PROTOBUF_CONSTEXPR VideoSendConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VideoSendConfig(const VideoSendConfig& from);
  VideoSendConfig(VideoSendConfig&& from) noexcept
    : VideoSendConfig() {
    *this = ::std::move(from);
  }

  inline VideoSendConfig& operator=(const VideoSendConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline VideoSendConfig& operator=(VideoSendConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const VideoSendConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const VideoSendConfig* internal_default_instance() {
    return reinterpret_cast<const VideoSendConfig*>(
               &_VideoSendConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(VideoSendConfig& a, VideoSendConfig& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(VideoSendConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VideoSendConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VideoSendConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<VideoSendConfig>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const VideoSendConfig& from);
  void MergeFrom(const VideoSendConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(VideoSendConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.VideoSendConfig";
  }
  protected:
  explicit VideoSendConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSsrcsFieldNumber = 1,
    kHeaderExtensionsFieldNumber = 2,
    kRtxSsrcsFieldNumber = 3,
    kEncoderFieldNumber = 5,
    kRtxPayloadTypeFieldNumber = 4,
  };
  // repeated uint32 ssrcs = 1;
  int ssrcs_size() const;
  private:
  int _internal_ssrcs_size() const;
  public:
  void clear_ssrcs();
  private:
  uint32_t _internal_ssrcs(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      _internal_ssrcs() const;
  void _internal_add_ssrcs(uint32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      _internal_mutable_ssrcs();
  public:
  uint32_t ssrcs(int index) const;
  void set_ssrcs(int index, uint32_t value);
  void add_ssrcs(uint32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      ssrcs() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      mutable_ssrcs();

  // repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 2;
  int header_extensions_size() const;
  private:
  int _internal_header_extensions_size() const;
  public:
  void clear_header_extensions();
  ::webrtc::rtclog::RtpHeaderExtension* mutable_header_extensions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension >*
      mutable_header_extensions();
  private:
  const ::webrtc::rtclog::RtpHeaderExtension& _internal_header_extensions(int index) const;
  ::webrtc::rtclog::RtpHeaderExtension* _internal_add_header_extensions();
  public:
  const ::webrtc::rtclog::RtpHeaderExtension& header_extensions(int index) const;
  ::webrtc::rtclog::RtpHeaderExtension* add_header_extensions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension >&
      header_extensions() const;

  // repeated uint32 rtx_ssrcs = 3;
  int rtx_ssrcs_size() const;
  private:
  int _internal_rtx_ssrcs_size() const;
  public:
  void clear_rtx_ssrcs();
  private:
  uint32_t _internal_rtx_ssrcs(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      _internal_rtx_ssrcs() const;
  void _internal_add_rtx_ssrcs(uint32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      _internal_mutable_rtx_ssrcs();
  public:
  uint32_t rtx_ssrcs(int index) const;
  void set_rtx_ssrcs(int index, uint32_t value);
  void add_rtx_ssrcs(uint32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
      rtx_ssrcs() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
      mutable_rtx_ssrcs();

  // optional .webrtc.rtclog.EncoderConfig encoder = 5;
  bool has_encoder() const;
  private:
  bool _internal_has_encoder() const;
  public:
  void clear_encoder();
  const ::webrtc::rtclog::EncoderConfig& encoder() const;
  PROTOBUF_NODISCARD ::webrtc::rtclog::EncoderConfig* release_encoder();
  ::webrtc::rtclog::EncoderConfig* mutable_encoder();
  void set_allocated_encoder(::webrtc::rtclog::EncoderConfig* encoder);
  private:
  const ::webrtc::rtclog::EncoderConfig& _internal_encoder() const;
  ::webrtc::rtclog::EncoderConfig* _internal_mutable_encoder();
  public:
  void unsafe_arena_set_allocated_encoder(
      ::webrtc::rtclog::EncoderConfig* encoder);
  ::webrtc::rtclog::EncoderConfig* unsafe_arena_release_encoder();

  // optional int32 rtx_payload_type = 4;
  bool has_rtx_payload_type() const;
  private:
  bool _internal_has_rtx_payload_type() const;
  public:
  void clear_rtx_payload_type();
  int32_t rtx_payload_type() const;
  void set_rtx_payload_type(int32_t value);
  private:
  int32_t _internal_rtx_payload_type() const;
  void _internal_set_rtx_payload_type(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.VideoSendConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t > ssrcs_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension > header_extensions_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t > rtx_ssrcs_;
  ::webrtc::rtclog::EncoderConfig* encoder_;
  int32_t rtx_payload_type_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class EncoderConfig final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.EncoderConfig) */ {
 public:
  inline EncoderConfig() : EncoderConfig(nullptr) {}
  ~EncoderConfig() override;
  explicit PROTOBUF_CONSTEXPR EncoderConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  EncoderConfig(const EncoderConfig& from);
  EncoderConfig(EncoderConfig&& from) noexcept
    : EncoderConfig() {
    *this = ::std::move(from);
  }

  inline EncoderConfig& operator=(const EncoderConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline EncoderConfig& operator=(EncoderConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const EncoderConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const EncoderConfig* internal_default_instance() {
    return reinterpret_cast<const EncoderConfig*>(
               &_EncoderConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(EncoderConfig& a, EncoderConfig& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(EncoderConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(EncoderConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  EncoderConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<EncoderConfig>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const EncoderConfig& from);
  void MergeFrom(const EncoderConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(EncoderConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.EncoderConfig";
  }
  protected:
  explicit EncoderConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kPayloadTypeFieldNumber = 2,
  };
  // optional string name = 1;
  bool has_name() const;
  private:
  bool _internal_has_name() const;
  public:
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // optional int32 payload_type = 2;
  bool has_payload_type() const;
  private:
  bool _internal_has_payload_type() const;
  public:
  void clear_payload_type();
  int32_t payload_type() const;
  void set_payload_type(int32_t value);
  private:
  int32_t _internal_payload_type() const;
  void _internal_set_payload_type(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.EncoderConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  int32_t payload_type_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class AudioReceiveConfig final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.AudioReceiveConfig) */ {
 public:
  inline AudioReceiveConfig() : AudioReceiveConfig(nullptr) {}
  ~AudioReceiveConfig() override;
  explicit PROTOBUF_CONSTEXPR AudioReceiveConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AudioReceiveConfig(const AudioReceiveConfig& from);
  AudioReceiveConfig(AudioReceiveConfig&& from) noexcept
    : AudioReceiveConfig() {
    *this = ::std::move(from);
  }

  inline AudioReceiveConfig& operator=(const AudioReceiveConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline AudioReceiveConfig& operator=(AudioReceiveConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const AudioReceiveConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const AudioReceiveConfig* internal_default_instance() {
    return reinterpret_cast<const AudioReceiveConfig*>(
               &_AudioReceiveConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(AudioReceiveConfig& a, AudioReceiveConfig& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(AudioReceiveConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AudioReceiveConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AudioReceiveConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AudioReceiveConfig>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const AudioReceiveConfig& from);
  void MergeFrom(const AudioReceiveConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(AudioReceiveConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.AudioReceiveConfig";
  }
  protected:
  explicit AudioReceiveConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHeaderExtensionsFieldNumber = 3,
    kRemoteSsrcFieldNumber = 1,
    kLocalSsrcFieldNumber = 2,
  };
  // repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 3;
  int header_extensions_size() const;
  private:
  int _internal_header_extensions_size() const;
  public:
  void clear_header_extensions();
  ::webrtc::rtclog::RtpHeaderExtension* mutable_header_extensions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension >*
      mutable_header_extensions();
  private:
  const ::webrtc::rtclog::RtpHeaderExtension& _internal_header_extensions(int index) const;
  ::webrtc::rtclog::RtpHeaderExtension* _internal_add_header_extensions();
  public:
  const ::webrtc::rtclog::RtpHeaderExtension& header_extensions(int index) const;
  ::webrtc::rtclog::RtpHeaderExtension* add_header_extensions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension >&
      header_extensions() const;

  // optional uint32 remote_ssrc = 1;
  bool has_remote_ssrc() const;
  private:
  bool _internal_has_remote_ssrc() const;
  public:
  void clear_remote_ssrc();
  uint32_t remote_ssrc() const;
  void set_remote_ssrc(uint32_t value);
  private:
  uint32_t _internal_remote_ssrc() const;
  void _internal_set_remote_ssrc(uint32_t value);
  public:

  // optional uint32 local_ssrc = 2;
  bool has_local_ssrc() const;
  private:
  bool _internal_has_local_ssrc() const;
  public:
  void clear_local_ssrc();
  uint32_t local_ssrc() const;
  void set_local_ssrc(uint32_t value);
  private:
  uint32_t _internal_local_ssrc() const;
  void _internal_set_local_ssrc(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.AudioReceiveConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension > header_extensions_;
  uint32_t remote_ssrc_;
  uint32_t local_ssrc_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class AudioSendConfig final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.AudioSendConfig) */ {
 public:
  inline AudioSendConfig() : AudioSendConfig(nullptr) {}
  ~AudioSendConfig() override;
  explicit PROTOBUF_CONSTEXPR AudioSendConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AudioSendConfig(const AudioSendConfig& from);
  AudioSendConfig(AudioSendConfig&& from) noexcept
    : AudioSendConfig() {
    *this = ::std::move(from);
  }

  inline AudioSendConfig& operator=(const AudioSendConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline AudioSendConfig& operator=(AudioSendConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const AudioSendConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const AudioSendConfig* internal_default_instance() {
    return reinterpret_cast<const AudioSendConfig*>(
               &_AudioSendConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(AudioSendConfig& a, AudioSendConfig& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(AudioSendConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AudioSendConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AudioSendConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AudioSendConfig>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const AudioSendConfig& from);
  void MergeFrom(const AudioSendConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(AudioSendConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.AudioSendConfig";
  }
  protected:
  explicit AudioSendConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHeaderExtensionsFieldNumber = 2,
    kSsrcFieldNumber = 1,
  };
  // repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 2;
  int header_extensions_size() const;
  private:
  int _internal_header_extensions_size() const;
  public:
  void clear_header_extensions();
  ::webrtc::rtclog::RtpHeaderExtension* mutable_header_extensions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension >*
      mutable_header_extensions();
  private:
  const ::webrtc::rtclog::RtpHeaderExtension& _internal_header_extensions(int index) const;
  ::webrtc::rtclog::RtpHeaderExtension* _internal_add_header_extensions();
  public:
  const ::webrtc::rtclog::RtpHeaderExtension& header_extensions(int index) const;
  ::webrtc::rtclog::RtpHeaderExtension* add_header_extensions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension >&
      header_extensions() const;

  // optional uint32 ssrc = 1;
  bool has_ssrc() const;
  private:
  bool _internal_has_ssrc() const;
  public:
  void clear_ssrc();
  uint32_t ssrc() const;
  void set_ssrc(uint32_t value);
  private:
  uint32_t _internal_ssrc() const;
  void _internal_set_ssrc(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.AudioSendConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension > header_extensions_;
  uint32_t ssrc_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class AudioNetworkAdaptation final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.AudioNetworkAdaptation) */ {
 public:
  inline AudioNetworkAdaptation() : AudioNetworkAdaptation(nullptr) {}
  ~AudioNetworkAdaptation() override;
  explicit PROTOBUF_CONSTEXPR AudioNetworkAdaptation(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AudioNetworkAdaptation(const AudioNetworkAdaptation& from);
  AudioNetworkAdaptation(AudioNetworkAdaptation&& from) noexcept
    : AudioNetworkAdaptation() {
    *this = ::std::move(from);
  }

  inline AudioNetworkAdaptation& operator=(const AudioNetworkAdaptation& from) {
    CopyFrom(from);
    return *this;
  }
  inline AudioNetworkAdaptation& operator=(AudioNetworkAdaptation&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const AudioNetworkAdaptation& default_instance() {
    return *internal_default_instance();
  }
  static inline const AudioNetworkAdaptation* internal_default_instance() {
    return reinterpret_cast<const AudioNetworkAdaptation*>(
               &_AudioNetworkAdaptation_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(AudioNetworkAdaptation& a, AudioNetworkAdaptation& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(AudioNetworkAdaptation* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AudioNetworkAdaptation* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AudioNetworkAdaptation* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AudioNetworkAdaptation>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const AudioNetworkAdaptation& from);
  void MergeFrom(const AudioNetworkAdaptation& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(AudioNetworkAdaptation* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.AudioNetworkAdaptation";
  }
  protected:
  explicit AudioNetworkAdaptation(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBitrateBpsFieldNumber = 1,
    kFrameLengthMsFieldNumber = 2,
    kUplinkPacketLossFractionFieldNumber = 3,
    kEnableFecFieldNumber = 4,
    kEnableDtxFieldNumber = 5,
    kNumChannelsFieldNumber = 6,
  };
  // optional int32 bitrate_bps = 1;
  bool has_bitrate_bps() const;
  private:
  bool _internal_has_bitrate_bps() const;
  public:
  void clear_bitrate_bps();
  int32_t bitrate_bps() const;
  void set_bitrate_bps(int32_t value);
  private:
  int32_t _internal_bitrate_bps() const;
  void _internal_set_bitrate_bps(int32_t value);
  public:

  // optional int32 frame_length_ms = 2;
  bool has_frame_length_ms() const;
  private:
  bool _internal_has_frame_length_ms() const;
  public:
  void clear_frame_length_ms();
  int32_t frame_length_ms() const;
  void set_frame_length_ms(int32_t value);
  private:
  int32_t _internal_frame_length_ms() const;
  void _internal_set_frame_length_ms(int32_t value);
  public:

  // optional float uplink_packet_loss_fraction = 3;
  bool has_uplink_packet_loss_fraction() const;
  private:
  bool _internal_has_uplink_packet_loss_fraction() const;
  public:
  void clear_uplink_packet_loss_fraction();
  float uplink_packet_loss_fraction() const;
  void set_uplink_packet_loss_fraction(float value);
  private:
  float _internal_uplink_packet_loss_fraction() const;
  void _internal_set_uplink_packet_loss_fraction(float value);
  public:

  // optional bool enable_fec = 4;
  bool has_enable_fec() const;
  private:
  bool _internal_has_enable_fec() const;
  public:
  void clear_enable_fec();
  bool enable_fec() const;
  void set_enable_fec(bool value);
  private:
  bool _internal_enable_fec() const;
  void _internal_set_enable_fec(bool value);
  public:

  // optional bool enable_dtx = 5;
  bool has_enable_dtx() const;
  private:
  bool _internal_has_enable_dtx() const;
  public:
  void clear_enable_dtx();
  bool enable_dtx() const;
  void set_enable_dtx(bool value);
  private:
  bool _internal_enable_dtx() const;
  void _internal_set_enable_dtx(bool value);
  public:

  // optional uint32 num_channels = 6;
  bool has_num_channels() const;
  private:
  bool _internal_has_num_channels() const;
  public:
  void clear_num_channels();
  uint32_t num_channels() const;
  void set_num_channels(uint32_t value);
  private:
  uint32_t _internal_num_channels() const;
  void _internal_set_num_channels(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.AudioNetworkAdaptation)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int32_t bitrate_bps_;
  int32_t frame_length_ms_;
  float uplink_packet_loss_fraction_;
  bool enable_fec_;
  bool enable_dtx_;
  uint32_t num_channels_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class BweProbeCluster final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.BweProbeCluster) */ {
 public:
  inline BweProbeCluster() : BweProbeCluster(nullptr) {}
  ~BweProbeCluster() override;
  explicit PROTOBUF_CONSTEXPR BweProbeCluster(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BweProbeCluster(const BweProbeCluster& from);
  BweProbeCluster(BweProbeCluster&& from) noexcept
    : BweProbeCluster() {
    *this = ::std::move(from);
  }

  inline BweProbeCluster& operator=(const BweProbeCluster& from) {
    CopyFrom(from);
    return *this;
  }
  inline BweProbeCluster& operator=(BweProbeCluster&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const BweProbeCluster& default_instance() {
    return *internal_default_instance();
  }
  static inline const BweProbeCluster* internal_default_instance() {
    return reinterpret_cast<const BweProbeCluster*>(
               &_BweProbeCluster_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(BweProbeCluster& a, BweProbeCluster& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(BweProbeCluster* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BweProbeCluster* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BweProbeCluster* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BweProbeCluster>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const BweProbeCluster& from);
  void MergeFrom(const BweProbeCluster& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(BweProbeCluster* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.BweProbeCluster";
  }
  protected:
  explicit BweProbeCluster(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kBitrateBpsFieldNumber = 2,
    kMinPacketsFieldNumber = 3,
    kMinBytesFieldNumber = 4,
  };
  // optional int32 id = 1;
  bool has_id() const;
  private:
  bool _internal_has_id() const;
  public:
  void clear_id();
  int32_t id() const;
  void set_id(int32_t value);
  private:
  int32_t _internal_id() const;
  void _internal_set_id(int32_t value);
  public:

  // optional int32 bitrate_bps = 2;
  bool has_bitrate_bps() const;
  private:
  bool _internal_has_bitrate_bps() const;
  public:
  void clear_bitrate_bps();
  int32_t bitrate_bps() const;
  void set_bitrate_bps(int32_t value);
  private:
  int32_t _internal_bitrate_bps() const;
  void _internal_set_bitrate_bps(int32_t value);
  public:

  // optional uint32 min_packets = 3;
  bool has_min_packets() const;
  private:
  bool _internal_has_min_packets() const;
  public:
  void clear_min_packets();
  uint32_t min_packets() const;
  void set_min_packets(uint32_t value);
  private:
  uint32_t _internal_min_packets() const;
  void _internal_set_min_packets(uint32_t value);
  public:

  // optional uint32 min_bytes = 4;
  bool has_min_bytes() const;
  private:
  bool _internal_has_min_bytes() const;
  public:
  void clear_min_bytes();
  uint32_t min_bytes() const;
  void set_min_bytes(uint32_t value);
  private:
  uint32_t _internal_min_bytes() const;
  void _internal_set_min_bytes(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.BweProbeCluster)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int32_t id_;
  int32_t bitrate_bps_;
  uint32_t min_packets_;
  uint32_t min_bytes_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class BweProbeResult final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.BweProbeResult) */ {
 public:
  inline BweProbeResult() : BweProbeResult(nullptr) {}
  ~BweProbeResult() override;
  explicit PROTOBUF_CONSTEXPR BweProbeResult(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BweProbeResult(const BweProbeResult& from);
  BweProbeResult(BweProbeResult&& from) noexcept
    : BweProbeResult() {
    *this = ::std::move(from);
  }

  inline BweProbeResult& operator=(const BweProbeResult& from) {
    CopyFrom(from);
    return *this;
  }
  inline BweProbeResult& operator=(BweProbeResult&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const BweProbeResult& default_instance() {
    return *internal_default_instance();
  }
  static inline const BweProbeResult* internal_default_instance() {
    return reinterpret_cast<const BweProbeResult*>(
               &_BweProbeResult_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(BweProbeResult& a, BweProbeResult& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(BweProbeResult* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BweProbeResult* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BweProbeResult* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BweProbeResult>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const BweProbeResult& from);
  void MergeFrom(const BweProbeResult& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(BweProbeResult* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.BweProbeResult";
  }
  protected:
  explicit BweProbeResult(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  typedef BweProbeResult_ResultType ResultType;
  static constexpr ResultType SUCCESS =
    BweProbeResult_ResultType_SUCCESS;
  static constexpr ResultType INVALID_SEND_RECEIVE_INTERVAL =
    BweProbeResult_ResultType_INVALID_SEND_RECEIVE_INTERVAL;
  static constexpr ResultType INVALID_SEND_RECEIVE_RATIO =
    BweProbeResult_ResultType_INVALID_SEND_RECEIVE_RATIO;
  static constexpr ResultType TIMEOUT =
    BweProbeResult_ResultType_TIMEOUT;
  static inline bool ResultType_IsValid(int value) {
    return BweProbeResult_ResultType_IsValid(value);
  }
  static constexpr ResultType ResultType_MIN =
    BweProbeResult_ResultType_ResultType_MIN;
  static constexpr ResultType ResultType_MAX =
    BweProbeResult_ResultType_ResultType_MAX;
  static constexpr int ResultType_ARRAYSIZE =
    BweProbeResult_ResultType_ResultType_ARRAYSIZE;
  template<typename T>
  static inline const std::string& ResultType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, ResultType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function ResultType_Name.");
    return BweProbeResult_ResultType_Name(enum_t_value);
  }
  static inline bool ResultType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      ResultType* value) {
    return BweProbeResult_ResultType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kResultFieldNumber = 2,
    kBitrateBpsFieldNumber = 3,
  };
  // optional int32 id = 1;
  bool has_id() const;
  private:
  bool _internal_has_id() const;
  public:
  void clear_id();
  int32_t id() const;
  void set_id(int32_t value);
  private:
  int32_t _internal_id() const;
  void _internal_set_id(int32_t value);
  public:

  // optional .webrtc.rtclog.BweProbeResult.ResultType result = 2;
  bool has_result() const;
  private:
  bool _internal_has_result() const;
  public:
  void clear_result();
  ::webrtc::rtclog::BweProbeResult_ResultType result() const;
  void set_result(::webrtc::rtclog::BweProbeResult_ResultType value);
  private:
  ::webrtc::rtclog::BweProbeResult_ResultType _internal_result() const;
  void _internal_set_result(::webrtc::rtclog::BweProbeResult_ResultType value);
  public:

  // optional int32 bitrate_bps = 3;
  bool has_bitrate_bps() const;
  private:
  bool _internal_has_bitrate_bps() const;
  public:
  void clear_bitrate_bps();
  int32_t bitrate_bps() const;
  void set_bitrate_bps(int32_t value);
  private:
  int32_t _internal_bitrate_bps() const;
  void _internal_set_bitrate_bps(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.BweProbeResult)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int32_t id_;
  int result_;
  int32_t bitrate_bps_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class RemoteEstimate final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.RemoteEstimate) */ {
 public:
  inline RemoteEstimate() : RemoteEstimate(nullptr) {}
  ~RemoteEstimate() override;
  explicit PROTOBUF_CONSTEXPR RemoteEstimate(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RemoteEstimate(const RemoteEstimate& from);
  RemoteEstimate(RemoteEstimate&& from) noexcept
    : RemoteEstimate() {
    *this = ::std::move(from);
  }

  inline RemoteEstimate& operator=(const RemoteEstimate& from) {
    CopyFrom(from);
    return *this;
  }
  inline RemoteEstimate& operator=(RemoteEstimate&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const RemoteEstimate& default_instance() {
    return *internal_default_instance();
  }
  static inline const RemoteEstimate* internal_default_instance() {
    return reinterpret_cast<const RemoteEstimate*>(
               &_RemoteEstimate_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(RemoteEstimate& a, RemoteEstimate& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(RemoteEstimate* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RemoteEstimate* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RemoteEstimate* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RemoteEstimate>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const RemoteEstimate& from);
  void MergeFrom(const RemoteEstimate& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RemoteEstimate* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.RemoteEstimate";
  }
  protected:
  explicit RemoteEstimate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLinkCapacityLowerKbpsFieldNumber = 1,
    kLinkCapacityUpperKbpsFieldNumber = 2,
  };
  // optional uint32 link_capacity_lower_kbps = 1;
  bool has_link_capacity_lower_kbps() const;
  private:
  bool _internal_has_link_capacity_lower_kbps() const;
  public:
  void clear_link_capacity_lower_kbps();
  uint32_t link_capacity_lower_kbps() const;
  void set_link_capacity_lower_kbps(uint32_t value);
  private:
  uint32_t _internal_link_capacity_lower_kbps() const;
  void _internal_set_link_capacity_lower_kbps(uint32_t value);
  public:

  // optional uint32 link_capacity_upper_kbps = 2;
  bool has_link_capacity_upper_kbps() const;
  private:
  bool _internal_has_link_capacity_upper_kbps() const;
  public:
  void clear_link_capacity_upper_kbps();
  uint32_t link_capacity_upper_kbps() const;
  void set_link_capacity_upper_kbps(uint32_t value);
  private:
  uint32_t _internal_link_capacity_upper_kbps() const;
  void _internal_set_link_capacity_upper_kbps(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.RemoteEstimate)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t link_capacity_lower_kbps_;
  uint32_t link_capacity_upper_kbps_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class AlrState final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.AlrState) */ {
 public:
  inline AlrState() : AlrState(nullptr) {}
  ~AlrState() override;
  explicit PROTOBUF_CONSTEXPR AlrState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AlrState(const AlrState& from);
  AlrState(AlrState&& from) noexcept
    : AlrState() {
    *this = ::std::move(from);
  }

  inline AlrState& operator=(const AlrState& from) {
    CopyFrom(from);
    return *this;
  }
  inline AlrState& operator=(AlrState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const AlrState& default_instance() {
    return *internal_default_instance();
  }
  static inline const AlrState* internal_default_instance() {
    return reinterpret_cast<const AlrState*>(
               &_AlrState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(AlrState& a, AlrState& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(AlrState* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AlrState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AlrState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AlrState>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const AlrState& from);
  void MergeFrom(const AlrState& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(AlrState* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.AlrState";
  }
  protected:
  explicit AlrState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInAlrFieldNumber = 1,
  };
  // optional bool in_alr = 1;
  bool has_in_alr() const;
  private:
  bool _internal_has_in_alr() const;
  public:
  void clear_in_alr();
  bool in_alr() const;
  void set_in_alr(bool value);
  private:
  bool _internal_in_alr() const;
  void _internal_set_in_alr(bool value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.AlrState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  bool in_alr_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class IceCandidatePairConfig final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.IceCandidatePairConfig) */ {
 public:
  inline IceCandidatePairConfig() : IceCandidatePairConfig(nullptr) {}
  ~IceCandidatePairConfig() override;
  explicit PROTOBUF_CONSTEXPR IceCandidatePairConfig(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  IceCandidatePairConfig(const IceCandidatePairConfig& from);
  IceCandidatePairConfig(IceCandidatePairConfig&& from) noexcept
    : IceCandidatePairConfig() {
    *this = ::std::move(from);
  }

  inline IceCandidatePairConfig& operator=(const IceCandidatePairConfig& from) {
    CopyFrom(from);
    return *this;
  }
  inline IceCandidatePairConfig& operator=(IceCandidatePairConfig&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const IceCandidatePairConfig& default_instance() {
    return *internal_default_instance();
  }
  static inline const IceCandidatePairConfig* internal_default_instance() {
    return reinterpret_cast<const IceCandidatePairConfig*>(
               &_IceCandidatePairConfig_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(IceCandidatePairConfig& a, IceCandidatePairConfig& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(IceCandidatePairConfig* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(IceCandidatePairConfig* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  IceCandidatePairConfig* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<IceCandidatePairConfig>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const IceCandidatePairConfig& from);
  void MergeFrom(const IceCandidatePairConfig& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(IceCandidatePairConfig* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.IceCandidatePairConfig";
  }
  protected:
  explicit IceCandidatePairConfig(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  typedef IceCandidatePairConfig_IceCandidatePairConfigType IceCandidatePairConfigType;
  static constexpr IceCandidatePairConfigType ADDED =
    IceCandidatePairConfig_IceCandidatePairConfigType_ADDED;
  static constexpr IceCandidatePairConfigType UPDATED =
    IceCandidatePairConfig_IceCandidatePairConfigType_UPDATED;
  static constexpr IceCandidatePairConfigType DESTROYED =
    IceCandidatePairConfig_IceCandidatePairConfigType_DESTROYED;
  static constexpr IceCandidatePairConfigType SELECTED =
    IceCandidatePairConfig_IceCandidatePairConfigType_SELECTED;
  static inline bool IceCandidatePairConfigType_IsValid(int value) {
    return IceCandidatePairConfig_IceCandidatePairConfigType_IsValid(value);
  }
  static constexpr IceCandidatePairConfigType IceCandidatePairConfigType_MIN =
    IceCandidatePairConfig_IceCandidatePairConfigType_IceCandidatePairConfigType_MIN;
  static constexpr IceCandidatePairConfigType IceCandidatePairConfigType_MAX =
    IceCandidatePairConfig_IceCandidatePairConfigType_IceCandidatePairConfigType_MAX;
  static constexpr int IceCandidatePairConfigType_ARRAYSIZE =
    IceCandidatePairConfig_IceCandidatePairConfigType_IceCandidatePairConfigType_ARRAYSIZE;
  template<typename T>
  static inline const std::string& IceCandidatePairConfigType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, IceCandidatePairConfigType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function IceCandidatePairConfigType_Name.");
    return IceCandidatePairConfig_IceCandidatePairConfigType_Name(enum_t_value);
  }
  static inline bool IceCandidatePairConfigType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      IceCandidatePairConfigType* value) {
    return IceCandidatePairConfig_IceCandidatePairConfigType_Parse(name, value);
  }

  typedef IceCandidatePairConfig_IceCandidateType IceCandidateType;
  static constexpr IceCandidateType LOCAL =
    IceCandidatePairConfig_IceCandidateType_LOCAL;
  static constexpr IceCandidateType STUN =
    IceCandidatePairConfig_IceCandidateType_STUN;
  static constexpr IceCandidateType PRFLX =
    IceCandidatePairConfig_IceCandidateType_PRFLX;
  static constexpr IceCandidateType RELAY =
    IceCandidatePairConfig_IceCandidateType_RELAY;
  static constexpr IceCandidateType UNKNOWN_CANDIDATE_TYPE =
    IceCandidatePairConfig_IceCandidateType_UNKNOWN_CANDIDATE_TYPE;
  static inline bool IceCandidateType_IsValid(int value) {
    return IceCandidatePairConfig_IceCandidateType_IsValid(value);
  }
  static constexpr IceCandidateType IceCandidateType_MIN =
    IceCandidatePairConfig_IceCandidateType_IceCandidateType_MIN;
  static constexpr IceCandidateType IceCandidateType_MAX =
    IceCandidatePairConfig_IceCandidateType_IceCandidateType_MAX;
  static constexpr int IceCandidateType_ARRAYSIZE =
    IceCandidatePairConfig_IceCandidateType_IceCandidateType_ARRAYSIZE;
  template<typename T>
  static inline const std::string& IceCandidateType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, IceCandidateType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function IceCandidateType_Name.");
    return IceCandidatePairConfig_IceCandidateType_Name(enum_t_value);
  }
  static inline bool IceCandidateType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      IceCandidateType* value) {
    return IceCandidatePairConfig_IceCandidateType_Parse(name, value);
  }

  typedef IceCandidatePairConfig_Protocol Protocol;
  static constexpr Protocol UDP =
    IceCandidatePairConfig_Protocol_UDP;
  static constexpr Protocol TCP =
    IceCandidatePairConfig_Protocol_TCP;
  static constexpr Protocol SSLTCP =
    IceCandidatePairConfig_Protocol_SSLTCP;
  static constexpr Protocol TLS =
    IceCandidatePairConfig_Protocol_TLS;
  static constexpr Protocol UNKNOWN_PROTOCOL =
    IceCandidatePairConfig_Protocol_UNKNOWN_PROTOCOL;
  static inline bool Protocol_IsValid(int value) {
    return IceCandidatePairConfig_Protocol_IsValid(value);
  }
  static constexpr Protocol Protocol_MIN =
    IceCandidatePairConfig_Protocol_Protocol_MIN;
  static constexpr Protocol Protocol_MAX =
    IceCandidatePairConfig_Protocol_Protocol_MAX;
  static constexpr int Protocol_ARRAYSIZE =
    IceCandidatePairConfig_Protocol_Protocol_ARRAYSIZE;
  template<typename T>
  static inline const std::string& Protocol_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Protocol>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Protocol_Name.");
    return IceCandidatePairConfig_Protocol_Name(enum_t_value);
  }
  static inline bool Protocol_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Protocol* value) {
    return IceCandidatePairConfig_Protocol_Parse(name, value);
  }

  typedef IceCandidatePairConfig_AddressFamily AddressFamily;
  static constexpr AddressFamily IPV4 =
    IceCandidatePairConfig_AddressFamily_IPV4;
  static constexpr AddressFamily IPV6 =
    IceCandidatePairConfig_AddressFamily_IPV6;
  static constexpr AddressFamily UNKNOWN_ADDRESS_FAMILY =
    IceCandidatePairConfig_AddressFamily_UNKNOWN_ADDRESS_FAMILY;
  static inline bool AddressFamily_IsValid(int value) {
    return IceCandidatePairConfig_AddressFamily_IsValid(value);
  }
  static constexpr AddressFamily AddressFamily_MIN =
    IceCandidatePairConfig_AddressFamily_AddressFamily_MIN;
  static constexpr AddressFamily AddressFamily_MAX =
    IceCandidatePairConfig_AddressFamily_AddressFamily_MAX;
  static constexpr int AddressFamily_ARRAYSIZE =
    IceCandidatePairConfig_AddressFamily_AddressFamily_ARRAYSIZE;
  template<typename T>
  static inline const std::string& AddressFamily_Name(T enum_t_value) {
    static_assert(::std::is_same<T, AddressFamily>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function AddressFamily_Name.");
    return IceCandidatePairConfig_AddressFamily_Name(enum_t_value);
  }
  static inline bool AddressFamily_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      AddressFamily* value) {
    return IceCandidatePairConfig_AddressFamily_Parse(name, value);
  }

  typedef IceCandidatePairConfig_NetworkType NetworkType;
  static constexpr NetworkType ETHERNET =
    IceCandidatePairConfig_NetworkType_ETHERNET;
  static constexpr NetworkType LOOPBACK =
    IceCandidatePairConfig_NetworkType_LOOPBACK;
  static constexpr NetworkType WIFI =
    IceCandidatePairConfig_NetworkType_WIFI;
  static constexpr NetworkType VPN =
    IceCandidatePairConfig_NetworkType_VPN;
  static constexpr NetworkType CELLULAR =
    IceCandidatePairConfig_NetworkType_CELLULAR;
  static constexpr NetworkType UNKNOWN_NETWORK_TYPE =
    IceCandidatePairConfig_NetworkType_UNKNOWN_NETWORK_TYPE;
  static inline bool NetworkType_IsValid(int value) {
    return IceCandidatePairConfig_NetworkType_IsValid(value);
  }
  static constexpr NetworkType NetworkType_MIN =
    IceCandidatePairConfig_NetworkType_NetworkType_MIN;
  static constexpr NetworkType NetworkType_MAX =
    IceCandidatePairConfig_NetworkType_NetworkType_MAX;
  static constexpr int NetworkType_ARRAYSIZE =
    IceCandidatePairConfig_NetworkType_NetworkType_ARRAYSIZE;
  template<typename T>
  static inline const std::string& NetworkType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, NetworkType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function NetworkType_Name.");
    return IceCandidatePairConfig_NetworkType_Name(enum_t_value);
  }
  static inline bool NetworkType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      NetworkType* value) {
    return IceCandidatePairConfig_NetworkType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kConfigTypeFieldNumber = 1,
    kCandidatePairIdFieldNumber = 2,
    kLocalCandidateTypeFieldNumber = 3,
    kLocalRelayProtocolFieldNumber = 4,
    kLocalNetworkTypeFieldNumber = 5,
    kLocalAddressFamilyFieldNumber = 6,
    kRemoteCandidateTypeFieldNumber = 7,
    kRemoteAddressFamilyFieldNumber = 8,
    kCandidatePairProtocolFieldNumber = 9,
  };
  // optional .webrtc.rtclog.IceCandidatePairConfig.IceCandidatePairConfigType config_type = 1;
  bool has_config_type() const;
  private:
  bool _internal_has_config_type() const;
  public:
  void clear_config_type();
  ::webrtc::rtclog::IceCandidatePairConfig_IceCandidatePairConfigType config_type() const;
  void set_config_type(::webrtc::rtclog::IceCandidatePairConfig_IceCandidatePairConfigType value);
  private:
  ::webrtc::rtclog::IceCandidatePairConfig_IceCandidatePairConfigType _internal_config_type() const;
  void _internal_set_config_type(::webrtc::rtclog::IceCandidatePairConfig_IceCandidatePairConfigType value);
  public:

  // optional uint32 candidate_pair_id = 2;
  bool has_candidate_pair_id() const;
  private:
  bool _internal_has_candidate_pair_id() const;
  public:
  void clear_candidate_pair_id();
  uint32_t candidate_pair_id() const;
  void set_candidate_pair_id(uint32_t value);
  private:
  uint32_t _internal_candidate_pair_id() const;
  void _internal_set_candidate_pair_id(uint32_t value);
  public:

  // optional .webrtc.rtclog.IceCandidatePairConfig.IceCandidateType local_candidate_type = 3;
  bool has_local_candidate_type() const;
  private:
  bool _internal_has_local_candidate_type() const;
  public:
  void clear_local_candidate_type();
  ::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType local_candidate_type() const;
  void set_local_candidate_type(::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType value);
  private:
  ::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType _internal_local_candidate_type() const;
  void _internal_set_local_candidate_type(::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType value);
  public:

  // optional .webrtc.rtclog.IceCandidatePairConfig.Protocol local_relay_protocol = 4;
  bool has_local_relay_protocol() const;
  private:
  bool _internal_has_local_relay_protocol() const;
  public:
  void clear_local_relay_protocol();
  ::webrtc::rtclog::IceCandidatePairConfig_Protocol local_relay_protocol() const;
  void set_local_relay_protocol(::webrtc::rtclog::IceCandidatePairConfig_Protocol value);
  private:
  ::webrtc::rtclog::IceCandidatePairConfig_Protocol _internal_local_relay_protocol() const;
  void _internal_set_local_relay_protocol(::webrtc::rtclog::IceCandidatePairConfig_Protocol value);
  public:

  // optional .webrtc.rtclog.IceCandidatePairConfig.NetworkType local_network_type = 5;
  bool has_local_network_type() const;
  private:
  bool _internal_has_local_network_type() const;
  public:
  void clear_local_network_type();
  ::webrtc::rtclog::IceCandidatePairConfig_NetworkType local_network_type() const;
  void set_local_network_type(::webrtc::rtclog::IceCandidatePairConfig_NetworkType value);
  private:
  ::webrtc::rtclog::IceCandidatePairConfig_NetworkType _internal_local_network_type() const;
  void _internal_set_local_network_type(::webrtc::rtclog::IceCandidatePairConfig_NetworkType value);
  public:

  // optional .webrtc.rtclog.IceCandidatePairConfig.AddressFamily local_address_family = 6;
  bool has_local_address_family() const;
  private:
  bool _internal_has_local_address_family() const;
  public:
  void clear_local_address_family();
  ::webrtc::rtclog::IceCandidatePairConfig_AddressFamily local_address_family() const;
  void set_local_address_family(::webrtc::rtclog::IceCandidatePairConfig_AddressFamily value);
  private:
  ::webrtc::rtclog::IceCandidatePairConfig_AddressFamily _internal_local_address_family() const;
  void _internal_set_local_address_family(::webrtc::rtclog::IceCandidatePairConfig_AddressFamily value);
  public:

  // optional .webrtc.rtclog.IceCandidatePairConfig.IceCandidateType remote_candidate_type = 7;
  bool has_remote_candidate_type() const;
  private:
  bool _internal_has_remote_candidate_type() const;
  public:
  void clear_remote_candidate_type();
  ::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType remote_candidate_type() const;
  void set_remote_candidate_type(::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType value);
  private:
  ::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType _internal_remote_candidate_type() const;
  void _internal_set_remote_candidate_type(::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType value);
  public:

  // optional .webrtc.rtclog.IceCandidatePairConfig.AddressFamily remote_address_family = 8;
  bool has_remote_address_family() const;
  private:
  bool _internal_has_remote_address_family() const;
  public:
  void clear_remote_address_family();
  ::webrtc::rtclog::IceCandidatePairConfig_AddressFamily remote_address_family() const;
  void set_remote_address_family(::webrtc::rtclog::IceCandidatePairConfig_AddressFamily value);
  private:
  ::webrtc::rtclog::IceCandidatePairConfig_AddressFamily _internal_remote_address_family() const;
  void _internal_set_remote_address_family(::webrtc::rtclog::IceCandidatePairConfig_AddressFamily value);
  public:

  // optional .webrtc.rtclog.IceCandidatePairConfig.Protocol candidate_pair_protocol = 9;
  bool has_candidate_pair_protocol() const;
  private:
  bool _internal_has_candidate_pair_protocol() const;
  public:
  void clear_candidate_pair_protocol();
  ::webrtc::rtclog::IceCandidatePairConfig_Protocol candidate_pair_protocol() const;
  void set_candidate_pair_protocol(::webrtc::rtclog::IceCandidatePairConfig_Protocol value);
  private:
  ::webrtc::rtclog::IceCandidatePairConfig_Protocol _internal_candidate_pair_protocol() const;
  void _internal_set_candidate_pair_protocol(::webrtc::rtclog::IceCandidatePairConfig_Protocol value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.IceCandidatePairConfig)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int config_type_;
  uint32_t candidate_pair_id_;
  int local_candidate_type_;
  int local_relay_protocol_;
  int local_network_type_;
  int local_address_family_;
  int remote_candidate_type_;
  int remote_address_family_;
  int candidate_pair_protocol_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// -------------------------------------------------------------------

class IceCandidatePairEvent final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.rtclog.IceCandidatePairEvent) */ {
 public:
  inline IceCandidatePairEvent() : IceCandidatePairEvent(nullptr) {}
  ~IceCandidatePairEvent() override;
  explicit PROTOBUF_CONSTEXPR IceCandidatePairEvent(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  IceCandidatePairEvent(const IceCandidatePairEvent& from);
  IceCandidatePairEvent(IceCandidatePairEvent&& from) noexcept
    : IceCandidatePairEvent() {
    *this = ::std::move(from);
  }

  inline IceCandidatePairEvent& operator=(const IceCandidatePairEvent& from) {
    CopyFrom(from);
    return *this;
  }
  inline IceCandidatePairEvent& operator=(IceCandidatePairEvent&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const IceCandidatePairEvent& default_instance() {
    return *internal_default_instance();
  }
  static inline const IceCandidatePairEvent* internal_default_instance() {
    return reinterpret_cast<const IceCandidatePairEvent*>(
               &_IceCandidatePairEvent_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(IceCandidatePairEvent& a, IceCandidatePairEvent& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(IceCandidatePairEvent* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(IceCandidatePairEvent* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  IceCandidatePairEvent* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<IceCandidatePairEvent>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const IceCandidatePairEvent& from);
  void MergeFrom(const IceCandidatePairEvent& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(IceCandidatePairEvent* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.rtclog.IceCandidatePairEvent";
  }
  protected:
  explicit IceCandidatePairEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  typedef IceCandidatePairEvent_IceCandidatePairEventType IceCandidatePairEventType;
  static constexpr IceCandidatePairEventType CHECK_SENT =
    IceCandidatePairEvent_IceCandidatePairEventType_CHECK_SENT;
  static constexpr IceCandidatePairEventType CHECK_RECEIVED =
    IceCandidatePairEvent_IceCandidatePairEventType_CHECK_RECEIVED;
  static constexpr IceCandidatePairEventType CHECK_RESPONSE_SENT =
    IceCandidatePairEvent_IceCandidatePairEventType_CHECK_RESPONSE_SENT;
  static constexpr IceCandidatePairEventType CHECK_RESPONSE_RECEIVED =
    IceCandidatePairEvent_IceCandidatePairEventType_CHECK_RESPONSE_RECEIVED;
  static inline bool IceCandidatePairEventType_IsValid(int value) {
    return IceCandidatePairEvent_IceCandidatePairEventType_IsValid(value);
  }
  static constexpr IceCandidatePairEventType IceCandidatePairEventType_MIN =
    IceCandidatePairEvent_IceCandidatePairEventType_IceCandidatePairEventType_MIN;
  static constexpr IceCandidatePairEventType IceCandidatePairEventType_MAX =
    IceCandidatePairEvent_IceCandidatePairEventType_IceCandidatePairEventType_MAX;
  static constexpr int IceCandidatePairEventType_ARRAYSIZE =
    IceCandidatePairEvent_IceCandidatePairEventType_IceCandidatePairEventType_ARRAYSIZE;
  template<typename T>
  static inline const std::string& IceCandidatePairEventType_Name(T enum_t_value) {
    static_assert(::std::is_same<T, IceCandidatePairEventType>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function IceCandidatePairEventType_Name.");
    return IceCandidatePairEvent_IceCandidatePairEventType_Name(enum_t_value);
  }
  static inline bool IceCandidatePairEventType_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      IceCandidatePairEventType* value) {
    return IceCandidatePairEvent_IceCandidatePairEventType_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kEventTypeFieldNumber = 1,
    kCandidatePairIdFieldNumber = 2,
  };
  // optional .webrtc.rtclog.IceCandidatePairEvent.IceCandidatePairEventType event_type = 1;
  bool has_event_type() const;
  private:
  bool _internal_has_event_type() const;
  public:
  void clear_event_type();
  ::webrtc::rtclog::IceCandidatePairEvent_IceCandidatePairEventType event_type() const;
  void set_event_type(::webrtc::rtclog::IceCandidatePairEvent_IceCandidatePairEventType value);
  private:
  ::webrtc::rtclog::IceCandidatePairEvent_IceCandidatePairEventType _internal_event_type() const;
  void _internal_set_event_type(::webrtc::rtclog::IceCandidatePairEvent_IceCandidatePairEventType value);
  public:

  // optional uint32 candidate_pair_id = 2;
  bool has_candidate_pair_id() const;
  private:
  bool _internal_has_candidate_pair_id() const;
  public:
  void clear_candidate_pair_id();
  uint32_t candidate_pair_id() const;
  void set_candidate_pair_id(uint32_t value);
  private:
  uint32_t _internal_candidate_pair_id() const;
  void _internal_set_candidate_pair_id(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.rtclog.IceCandidatePairEvent)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int event_type_;
  uint32_t candidate_pair_id_;
  friend struct ::TableStruct_rtc_5fevent_5flog_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// EventStream

// repeated .webrtc.rtclog.Event stream = 1;
inline int EventStream::_internal_stream_size() const {
  return stream_.size();
}
inline int EventStream::stream_size() const {
  return _internal_stream_size();
}
inline void EventStream::clear_stream() {
  stream_.Clear();
}
inline ::webrtc::rtclog::Event* EventStream::mutable_stream(int index) {
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.EventStream.stream)
  return stream_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::Event >*
EventStream::mutable_stream() {
  // @@protoc_insertion_point(field_mutable_list:webrtc.rtclog.EventStream.stream)
  return &stream_;
}
inline const ::webrtc::rtclog::Event& EventStream::_internal_stream(int index) const {
  return stream_.Get(index);
}
inline const ::webrtc::rtclog::Event& EventStream::stream(int index) const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.EventStream.stream)
  return _internal_stream(index);
}
inline ::webrtc::rtclog::Event* EventStream::_internal_add_stream() {
  return stream_.Add();
}
inline ::webrtc::rtclog::Event* EventStream::add_stream() {
  ::webrtc::rtclog::Event* _add = _internal_add_stream();
  // @@protoc_insertion_point(field_add:webrtc.rtclog.EventStream.stream)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::Event >&
EventStream::stream() const {
  // @@protoc_insertion_point(field_list:webrtc.rtclog.EventStream.stream)
  return stream_;
}

// -------------------------------------------------------------------

// Event

// optional int64 timestamp_us = 1;
inline bool Event::_internal_has_timestamp_us() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool Event::has_timestamp_us() const {
  return _internal_has_timestamp_us();
}
inline void Event::clear_timestamp_us() {
  timestamp_us_ = int64_t{0};
  _has_bits_[0] &= ~0x00000001u;
}
inline int64_t Event::_internal_timestamp_us() const {
  return timestamp_us_;
}
inline int64_t Event::timestamp_us() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.timestamp_us)
  return _internal_timestamp_us();
}
inline void Event::_internal_set_timestamp_us(int64_t value) {
  _has_bits_[0] |= 0x00000001u;
  timestamp_us_ = value;
}
inline void Event::set_timestamp_us(int64_t value) {
  _internal_set_timestamp_us(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.Event.timestamp_us)
}

// optional .webrtc.rtclog.Event.EventType type = 2;
inline bool Event::_internal_has_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool Event::has_type() const {
  return _internal_has_type();
}
inline void Event::clear_type() {
  type_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::webrtc::rtclog::Event_EventType Event::_internal_type() const {
  return static_cast< ::webrtc::rtclog::Event_EventType >(type_);
}
inline ::webrtc::rtclog::Event_EventType Event::type() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.type)
  return _internal_type();
}
inline void Event::_internal_set_type(::webrtc::rtclog::Event_EventType value) {
  assert(::webrtc::rtclog::Event_EventType_IsValid(value));
  _has_bits_[0] |= 0x00000002u;
  type_ = value;
}
inline void Event::set_type(::webrtc::rtclog::Event_EventType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.Event.type)
}

// .webrtc.rtclog.RtpPacket rtp_packet = 3;
inline bool Event::_internal_has_rtp_packet() const {
  return subtype_case() == kRtpPacket;
}
inline bool Event::has_rtp_packet() const {
  return _internal_has_rtp_packet();
}
inline void Event::set_has_rtp_packet() {
  _oneof_case_[0] = kRtpPacket;
}
inline void Event::clear_rtp_packet() {
  if (_internal_has_rtp_packet()) {
    if (GetArenaForAllocation() == nullptr) {
      delete subtype_.rtp_packet_;
    }
    clear_has_subtype();
  }
}
inline ::webrtc::rtclog::RtpPacket* Event::release_rtp_packet() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.Event.rtp_packet)
  if (_internal_has_rtp_packet()) {
    clear_has_subtype();
    ::webrtc::rtclog::RtpPacket* temp = subtype_.rtp_packet_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    subtype_.rtp_packet_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::webrtc::rtclog::RtpPacket& Event::_internal_rtp_packet() const {
  return _internal_has_rtp_packet()
      ? *subtype_.rtp_packet_
      : reinterpret_cast< ::webrtc::rtclog::RtpPacket&>(::webrtc::rtclog::_RtpPacket_default_instance_);
}
inline const ::webrtc::rtclog::RtpPacket& Event::rtp_packet() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.rtp_packet)
  return _internal_rtp_packet();
}
inline ::webrtc::rtclog::RtpPacket* Event::unsafe_arena_release_rtp_packet() {
  // @@protoc_insertion_point(field_unsafe_arena_release:webrtc.rtclog.Event.rtp_packet)
  if (_internal_has_rtp_packet()) {
    clear_has_subtype();
    ::webrtc::rtclog::RtpPacket* temp = subtype_.rtp_packet_;
    subtype_.rtp_packet_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_rtp_packet(::webrtc::rtclog::RtpPacket* rtp_packet) {
  clear_subtype();
  if (rtp_packet) {
    set_has_rtp_packet();
    subtype_.rtp_packet_ = rtp_packet;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.Event.rtp_packet)
}
inline ::webrtc::rtclog::RtpPacket* Event::_internal_mutable_rtp_packet() {
  if (!_internal_has_rtp_packet()) {
    clear_subtype();
    set_has_rtp_packet();
    subtype_.rtp_packet_ = CreateMaybeMessage< ::webrtc::rtclog::RtpPacket >(GetArenaForAllocation());
  }
  return subtype_.rtp_packet_;
}
inline ::webrtc::rtclog::RtpPacket* Event::mutable_rtp_packet() {
  ::webrtc::rtclog::RtpPacket* _msg = _internal_mutable_rtp_packet();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.Event.rtp_packet)
  return _msg;
}

// .webrtc.rtclog.RtcpPacket rtcp_packet = 4;
inline bool Event::_internal_has_rtcp_packet() const {
  return subtype_case() == kRtcpPacket;
}
inline bool Event::has_rtcp_packet() const {
  return _internal_has_rtcp_packet();
}
inline void Event::set_has_rtcp_packet() {
  _oneof_case_[0] = kRtcpPacket;
}
inline void Event::clear_rtcp_packet() {
  if (_internal_has_rtcp_packet()) {
    if (GetArenaForAllocation() == nullptr) {
      delete subtype_.rtcp_packet_;
    }
    clear_has_subtype();
  }
}
inline ::webrtc::rtclog::RtcpPacket* Event::release_rtcp_packet() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.Event.rtcp_packet)
  if (_internal_has_rtcp_packet()) {
    clear_has_subtype();
    ::webrtc::rtclog::RtcpPacket* temp = subtype_.rtcp_packet_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    subtype_.rtcp_packet_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::webrtc::rtclog::RtcpPacket& Event::_internal_rtcp_packet() const {
  return _internal_has_rtcp_packet()
      ? *subtype_.rtcp_packet_
      : reinterpret_cast< ::webrtc::rtclog::RtcpPacket&>(::webrtc::rtclog::_RtcpPacket_default_instance_);
}
inline const ::webrtc::rtclog::RtcpPacket& Event::rtcp_packet() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.rtcp_packet)
  return _internal_rtcp_packet();
}
inline ::webrtc::rtclog::RtcpPacket* Event::unsafe_arena_release_rtcp_packet() {
  // @@protoc_insertion_point(field_unsafe_arena_release:webrtc.rtclog.Event.rtcp_packet)
  if (_internal_has_rtcp_packet()) {
    clear_has_subtype();
    ::webrtc::rtclog::RtcpPacket* temp = subtype_.rtcp_packet_;
    subtype_.rtcp_packet_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_rtcp_packet(::webrtc::rtclog::RtcpPacket* rtcp_packet) {
  clear_subtype();
  if (rtcp_packet) {
    set_has_rtcp_packet();
    subtype_.rtcp_packet_ = rtcp_packet;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.Event.rtcp_packet)
}
inline ::webrtc::rtclog::RtcpPacket* Event::_internal_mutable_rtcp_packet() {
  if (!_internal_has_rtcp_packet()) {
    clear_subtype();
    set_has_rtcp_packet();
    subtype_.rtcp_packet_ = CreateMaybeMessage< ::webrtc::rtclog::RtcpPacket >(GetArenaForAllocation());
  }
  return subtype_.rtcp_packet_;
}
inline ::webrtc::rtclog::RtcpPacket* Event::mutable_rtcp_packet() {
  ::webrtc::rtclog::RtcpPacket* _msg = _internal_mutable_rtcp_packet();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.Event.rtcp_packet)
  return _msg;
}

// .webrtc.rtclog.AudioPlayoutEvent audio_playout_event = 5;
inline bool Event::_internal_has_audio_playout_event() const {
  return subtype_case() == kAudioPlayoutEvent;
}
inline bool Event::has_audio_playout_event() const {
  return _internal_has_audio_playout_event();
}
inline void Event::set_has_audio_playout_event() {
  _oneof_case_[0] = kAudioPlayoutEvent;
}
inline void Event::clear_audio_playout_event() {
  if (_internal_has_audio_playout_event()) {
    if (GetArenaForAllocation() == nullptr) {
      delete subtype_.audio_playout_event_;
    }
    clear_has_subtype();
  }
}
inline ::webrtc::rtclog::AudioPlayoutEvent* Event::release_audio_playout_event() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.Event.audio_playout_event)
  if (_internal_has_audio_playout_event()) {
    clear_has_subtype();
    ::webrtc::rtclog::AudioPlayoutEvent* temp = subtype_.audio_playout_event_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    subtype_.audio_playout_event_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::webrtc::rtclog::AudioPlayoutEvent& Event::_internal_audio_playout_event() const {
  return _internal_has_audio_playout_event()
      ? *subtype_.audio_playout_event_
      : reinterpret_cast< ::webrtc::rtclog::AudioPlayoutEvent&>(::webrtc::rtclog::_AudioPlayoutEvent_default_instance_);
}
inline const ::webrtc::rtclog::AudioPlayoutEvent& Event::audio_playout_event() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.audio_playout_event)
  return _internal_audio_playout_event();
}
inline ::webrtc::rtclog::AudioPlayoutEvent* Event::unsafe_arena_release_audio_playout_event() {
  // @@protoc_insertion_point(field_unsafe_arena_release:webrtc.rtclog.Event.audio_playout_event)
  if (_internal_has_audio_playout_event()) {
    clear_has_subtype();
    ::webrtc::rtclog::AudioPlayoutEvent* temp = subtype_.audio_playout_event_;
    subtype_.audio_playout_event_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_audio_playout_event(::webrtc::rtclog::AudioPlayoutEvent* audio_playout_event) {
  clear_subtype();
  if (audio_playout_event) {
    set_has_audio_playout_event();
    subtype_.audio_playout_event_ = audio_playout_event;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.Event.audio_playout_event)
}
inline ::webrtc::rtclog::AudioPlayoutEvent* Event::_internal_mutable_audio_playout_event() {
  if (!_internal_has_audio_playout_event()) {
    clear_subtype();
    set_has_audio_playout_event();
    subtype_.audio_playout_event_ = CreateMaybeMessage< ::webrtc::rtclog::AudioPlayoutEvent >(GetArenaForAllocation());
  }
  return subtype_.audio_playout_event_;
}
inline ::webrtc::rtclog::AudioPlayoutEvent* Event::mutable_audio_playout_event() {
  ::webrtc::rtclog::AudioPlayoutEvent* _msg = _internal_mutable_audio_playout_event();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.Event.audio_playout_event)
  return _msg;
}

// .webrtc.rtclog.LossBasedBweUpdate loss_based_bwe_update = 6;
inline bool Event::_internal_has_loss_based_bwe_update() const {
  return subtype_case() == kLossBasedBweUpdate;
}
inline bool Event::has_loss_based_bwe_update() const {
  return _internal_has_loss_based_bwe_update();
}
inline void Event::set_has_loss_based_bwe_update() {
  _oneof_case_[0] = kLossBasedBweUpdate;
}
inline void Event::clear_loss_based_bwe_update() {
  if (_internal_has_loss_based_bwe_update()) {
    if (GetArenaForAllocation() == nullptr) {
      delete subtype_.loss_based_bwe_update_;
    }
    clear_has_subtype();
  }
}
inline ::webrtc::rtclog::LossBasedBweUpdate* Event::release_loss_based_bwe_update() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.Event.loss_based_bwe_update)
  if (_internal_has_loss_based_bwe_update()) {
    clear_has_subtype();
    ::webrtc::rtclog::LossBasedBweUpdate* temp = subtype_.loss_based_bwe_update_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    subtype_.loss_based_bwe_update_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::webrtc::rtclog::LossBasedBweUpdate& Event::_internal_loss_based_bwe_update() const {
  return _internal_has_loss_based_bwe_update()
      ? *subtype_.loss_based_bwe_update_
      : reinterpret_cast< ::webrtc::rtclog::LossBasedBweUpdate&>(::webrtc::rtclog::_LossBasedBweUpdate_default_instance_);
}
inline const ::webrtc::rtclog::LossBasedBweUpdate& Event::loss_based_bwe_update() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.loss_based_bwe_update)
  return _internal_loss_based_bwe_update();
}
inline ::webrtc::rtclog::LossBasedBweUpdate* Event::unsafe_arena_release_loss_based_bwe_update() {
  // @@protoc_insertion_point(field_unsafe_arena_release:webrtc.rtclog.Event.loss_based_bwe_update)
  if (_internal_has_loss_based_bwe_update()) {
    clear_has_subtype();
    ::webrtc::rtclog::LossBasedBweUpdate* temp = subtype_.loss_based_bwe_update_;
    subtype_.loss_based_bwe_update_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_loss_based_bwe_update(::webrtc::rtclog::LossBasedBweUpdate* loss_based_bwe_update) {
  clear_subtype();
  if (loss_based_bwe_update) {
    set_has_loss_based_bwe_update();
    subtype_.loss_based_bwe_update_ = loss_based_bwe_update;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.Event.loss_based_bwe_update)
}
inline ::webrtc::rtclog::LossBasedBweUpdate* Event::_internal_mutable_loss_based_bwe_update() {
  if (!_internal_has_loss_based_bwe_update()) {
    clear_subtype();
    set_has_loss_based_bwe_update();
    subtype_.loss_based_bwe_update_ = CreateMaybeMessage< ::webrtc::rtclog::LossBasedBweUpdate >(GetArenaForAllocation());
  }
  return subtype_.loss_based_bwe_update_;
}
inline ::webrtc::rtclog::LossBasedBweUpdate* Event::mutable_loss_based_bwe_update() {
  ::webrtc::rtclog::LossBasedBweUpdate* _msg = _internal_mutable_loss_based_bwe_update();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.Event.loss_based_bwe_update)
  return _msg;
}

// .webrtc.rtclog.DelayBasedBweUpdate delay_based_bwe_update = 7;
inline bool Event::_internal_has_delay_based_bwe_update() const {
  return subtype_case() == kDelayBasedBweUpdate;
}
inline bool Event::has_delay_based_bwe_update() const {
  return _internal_has_delay_based_bwe_update();
}
inline void Event::set_has_delay_based_bwe_update() {
  _oneof_case_[0] = kDelayBasedBweUpdate;
}
inline void Event::clear_delay_based_bwe_update() {
  if (_internal_has_delay_based_bwe_update()) {
    if (GetArenaForAllocation() == nullptr) {
      delete subtype_.delay_based_bwe_update_;
    }
    clear_has_subtype();
  }
}
inline ::webrtc::rtclog::DelayBasedBweUpdate* Event::release_delay_based_bwe_update() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.Event.delay_based_bwe_update)
  if (_internal_has_delay_based_bwe_update()) {
    clear_has_subtype();
    ::webrtc::rtclog::DelayBasedBweUpdate* temp = subtype_.delay_based_bwe_update_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    subtype_.delay_based_bwe_update_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::webrtc::rtclog::DelayBasedBweUpdate& Event::_internal_delay_based_bwe_update() const {
  return _internal_has_delay_based_bwe_update()
      ? *subtype_.delay_based_bwe_update_
      : reinterpret_cast< ::webrtc::rtclog::DelayBasedBweUpdate&>(::webrtc::rtclog::_DelayBasedBweUpdate_default_instance_);
}
inline const ::webrtc::rtclog::DelayBasedBweUpdate& Event::delay_based_bwe_update() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.delay_based_bwe_update)
  return _internal_delay_based_bwe_update();
}
inline ::webrtc::rtclog::DelayBasedBweUpdate* Event::unsafe_arena_release_delay_based_bwe_update() {
  // @@protoc_insertion_point(field_unsafe_arena_release:webrtc.rtclog.Event.delay_based_bwe_update)
  if (_internal_has_delay_based_bwe_update()) {
    clear_has_subtype();
    ::webrtc::rtclog::DelayBasedBweUpdate* temp = subtype_.delay_based_bwe_update_;
    subtype_.delay_based_bwe_update_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_delay_based_bwe_update(::webrtc::rtclog::DelayBasedBweUpdate* delay_based_bwe_update) {
  clear_subtype();
  if (delay_based_bwe_update) {
    set_has_delay_based_bwe_update();
    subtype_.delay_based_bwe_update_ = delay_based_bwe_update;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.Event.delay_based_bwe_update)
}
inline ::webrtc::rtclog::DelayBasedBweUpdate* Event::_internal_mutable_delay_based_bwe_update() {
  if (!_internal_has_delay_based_bwe_update()) {
    clear_subtype();
    set_has_delay_based_bwe_update();
    subtype_.delay_based_bwe_update_ = CreateMaybeMessage< ::webrtc::rtclog::DelayBasedBweUpdate >(GetArenaForAllocation());
  }
  return subtype_.delay_based_bwe_update_;
}
inline ::webrtc::rtclog::DelayBasedBweUpdate* Event::mutable_delay_based_bwe_update() {
  ::webrtc::rtclog::DelayBasedBweUpdate* _msg = _internal_mutable_delay_based_bwe_update();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.Event.delay_based_bwe_update)
  return _msg;
}

// .webrtc.rtclog.VideoReceiveConfig video_receiver_config = 8;
inline bool Event::_internal_has_video_receiver_config() const {
  return subtype_case() == kVideoReceiverConfig;
}
inline bool Event::has_video_receiver_config() const {
  return _internal_has_video_receiver_config();
}
inline void Event::set_has_video_receiver_config() {
  _oneof_case_[0] = kVideoReceiverConfig;
}
inline void Event::clear_video_receiver_config() {
  if (_internal_has_video_receiver_config()) {
    if (GetArenaForAllocation() == nullptr) {
      delete subtype_.video_receiver_config_;
    }
    clear_has_subtype();
  }
}
inline ::webrtc::rtclog::VideoReceiveConfig* Event::release_video_receiver_config() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.Event.video_receiver_config)
  if (_internal_has_video_receiver_config()) {
    clear_has_subtype();
    ::webrtc::rtclog::VideoReceiveConfig* temp = subtype_.video_receiver_config_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    subtype_.video_receiver_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::webrtc::rtclog::VideoReceiveConfig& Event::_internal_video_receiver_config() const {
  return _internal_has_video_receiver_config()
      ? *subtype_.video_receiver_config_
      : reinterpret_cast< ::webrtc::rtclog::VideoReceiveConfig&>(::webrtc::rtclog::_VideoReceiveConfig_default_instance_);
}
inline const ::webrtc::rtclog::VideoReceiveConfig& Event::video_receiver_config() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.video_receiver_config)
  return _internal_video_receiver_config();
}
inline ::webrtc::rtclog::VideoReceiveConfig* Event::unsafe_arena_release_video_receiver_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:webrtc.rtclog.Event.video_receiver_config)
  if (_internal_has_video_receiver_config()) {
    clear_has_subtype();
    ::webrtc::rtclog::VideoReceiveConfig* temp = subtype_.video_receiver_config_;
    subtype_.video_receiver_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_video_receiver_config(::webrtc::rtclog::VideoReceiveConfig* video_receiver_config) {
  clear_subtype();
  if (video_receiver_config) {
    set_has_video_receiver_config();
    subtype_.video_receiver_config_ = video_receiver_config;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.Event.video_receiver_config)
}
inline ::webrtc::rtclog::VideoReceiveConfig* Event::_internal_mutable_video_receiver_config() {
  if (!_internal_has_video_receiver_config()) {
    clear_subtype();
    set_has_video_receiver_config();
    subtype_.video_receiver_config_ = CreateMaybeMessage< ::webrtc::rtclog::VideoReceiveConfig >(GetArenaForAllocation());
  }
  return subtype_.video_receiver_config_;
}
inline ::webrtc::rtclog::VideoReceiveConfig* Event::mutable_video_receiver_config() {
  ::webrtc::rtclog::VideoReceiveConfig* _msg = _internal_mutable_video_receiver_config();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.Event.video_receiver_config)
  return _msg;
}

// .webrtc.rtclog.VideoSendConfig video_sender_config = 9;
inline bool Event::_internal_has_video_sender_config() const {
  return subtype_case() == kVideoSenderConfig;
}
inline bool Event::has_video_sender_config() const {
  return _internal_has_video_sender_config();
}
inline void Event::set_has_video_sender_config() {
  _oneof_case_[0] = kVideoSenderConfig;
}
inline void Event::clear_video_sender_config() {
  if (_internal_has_video_sender_config()) {
    if (GetArenaForAllocation() == nullptr) {
      delete subtype_.video_sender_config_;
    }
    clear_has_subtype();
  }
}
inline ::webrtc::rtclog::VideoSendConfig* Event::release_video_sender_config() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.Event.video_sender_config)
  if (_internal_has_video_sender_config()) {
    clear_has_subtype();
    ::webrtc::rtclog::VideoSendConfig* temp = subtype_.video_sender_config_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    subtype_.video_sender_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::webrtc::rtclog::VideoSendConfig& Event::_internal_video_sender_config() const {
  return _internal_has_video_sender_config()
      ? *subtype_.video_sender_config_
      : reinterpret_cast< ::webrtc::rtclog::VideoSendConfig&>(::webrtc::rtclog::_VideoSendConfig_default_instance_);
}
inline const ::webrtc::rtclog::VideoSendConfig& Event::video_sender_config() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.video_sender_config)
  return _internal_video_sender_config();
}
inline ::webrtc::rtclog::VideoSendConfig* Event::unsafe_arena_release_video_sender_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:webrtc.rtclog.Event.video_sender_config)
  if (_internal_has_video_sender_config()) {
    clear_has_subtype();
    ::webrtc::rtclog::VideoSendConfig* temp = subtype_.video_sender_config_;
    subtype_.video_sender_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_video_sender_config(::webrtc::rtclog::VideoSendConfig* video_sender_config) {
  clear_subtype();
  if (video_sender_config) {
    set_has_video_sender_config();
    subtype_.video_sender_config_ = video_sender_config;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.Event.video_sender_config)
}
inline ::webrtc::rtclog::VideoSendConfig* Event::_internal_mutable_video_sender_config() {
  if (!_internal_has_video_sender_config()) {
    clear_subtype();
    set_has_video_sender_config();
    subtype_.video_sender_config_ = CreateMaybeMessage< ::webrtc::rtclog::VideoSendConfig >(GetArenaForAllocation());
  }
  return subtype_.video_sender_config_;
}
inline ::webrtc::rtclog::VideoSendConfig* Event::mutable_video_sender_config() {
  ::webrtc::rtclog::VideoSendConfig* _msg = _internal_mutable_video_sender_config();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.Event.video_sender_config)
  return _msg;
}

// .webrtc.rtclog.AudioReceiveConfig audio_receiver_config = 10;
inline bool Event::_internal_has_audio_receiver_config() const {
  return subtype_case() == kAudioReceiverConfig;
}
inline bool Event::has_audio_receiver_config() const {
  return _internal_has_audio_receiver_config();
}
inline void Event::set_has_audio_receiver_config() {
  _oneof_case_[0] = kAudioReceiverConfig;
}
inline void Event::clear_audio_receiver_config() {
  if (_internal_has_audio_receiver_config()) {
    if (GetArenaForAllocation() == nullptr) {
      delete subtype_.audio_receiver_config_;
    }
    clear_has_subtype();
  }
}
inline ::webrtc::rtclog::AudioReceiveConfig* Event::release_audio_receiver_config() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.Event.audio_receiver_config)
  if (_internal_has_audio_receiver_config()) {
    clear_has_subtype();
    ::webrtc::rtclog::AudioReceiveConfig* temp = subtype_.audio_receiver_config_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    subtype_.audio_receiver_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::webrtc::rtclog::AudioReceiveConfig& Event::_internal_audio_receiver_config() const {
  return _internal_has_audio_receiver_config()
      ? *subtype_.audio_receiver_config_
      : reinterpret_cast< ::webrtc::rtclog::AudioReceiveConfig&>(::webrtc::rtclog::_AudioReceiveConfig_default_instance_);
}
inline const ::webrtc::rtclog::AudioReceiveConfig& Event::audio_receiver_config() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.audio_receiver_config)
  return _internal_audio_receiver_config();
}
inline ::webrtc::rtclog::AudioReceiveConfig* Event::unsafe_arena_release_audio_receiver_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:webrtc.rtclog.Event.audio_receiver_config)
  if (_internal_has_audio_receiver_config()) {
    clear_has_subtype();
    ::webrtc::rtclog::AudioReceiveConfig* temp = subtype_.audio_receiver_config_;
    subtype_.audio_receiver_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_audio_receiver_config(::webrtc::rtclog::AudioReceiveConfig* audio_receiver_config) {
  clear_subtype();
  if (audio_receiver_config) {
    set_has_audio_receiver_config();
    subtype_.audio_receiver_config_ = audio_receiver_config;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.Event.audio_receiver_config)
}
inline ::webrtc::rtclog::AudioReceiveConfig* Event::_internal_mutable_audio_receiver_config() {
  if (!_internal_has_audio_receiver_config()) {
    clear_subtype();
    set_has_audio_receiver_config();
    subtype_.audio_receiver_config_ = CreateMaybeMessage< ::webrtc::rtclog::AudioReceiveConfig >(GetArenaForAllocation());
  }
  return subtype_.audio_receiver_config_;
}
inline ::webrtc::rtclog::AudioReceiveConfig* Event::mutable_audio_receiver_config() {
  ::webrtc::rtclog::AudioReceiveConfig* _msg = _internal_mutable_audio_receiver_config();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.Event.audio_receiver_config)
  return _msg;
}

// .webrtc.rtclog.AudioSendConfig audio_sender_config = 11;
inline bool Event::_internal_has_audio_sender_config() const {
  return subtype_case() == kAudioSenderConfig;
}
inline bool Event::has_audio_sender_config() const {
  return _internal_has_audio_sender_config();
}
inline void Event::set_has_audio_sender_config() {
  _oneof_case_[0] = kAudioSenderConfig;
}
inline void Event::clear_audio_sender_config() {
  if (_internal_has_audio_sender_config()) {
    if (GetArenaForAllocation() == nullptr) {
      delete subtype_.audio_sender_config_;
    }
    clear_has_subtype();
  }
}
inline ::webrtc::rtclog::AudioSendConfig* Event::release_audio_sender_config() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.Event.audio_sender_config)
  if (_internal_has_audio_sender_config()) {
    clear_has_subtype();
    ::webrtc::rtclog::AudioSendConfig* temp = subtype_.audio_sender_config_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    subtype_.audio_sender_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::webrtc::rtclog::AudioSendConfig& Event::_internal_audio_sender_config() const {
  return _internal_has_audio_sender_config()
      ? *subtype_.audio_sender_config_
      : reinterpret_cast< ::webrtc::rtclog::AudioSendConfig&>(::webrtc::rtclog::_AudioSendConfig_default_instance_);
}
inline const ::webrtc::rtclog::AudioSendConfig& Event::audio_sender_config() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.audio_sender_config)
  return _internal_audio_sender_config();
}
inline ::webrtc::rtclog::AudioSendConfig* Event::unsafe_arena_release_audio_sender_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:webrtc.rtclog.Event.audio_sender_config)
  if (_internal_has_audio_sender_config()) {
    clear_has_subtype();
    ::webrtc::rtclog::AudioSendConfig* temp = subtype_.audio_sender_config_;
    subtype_.audio_sender_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_audio_sender_config(::webrtc::rtclog::AudioSendConfig* audio_sender_config) {
  clear_subtype();
  if (audio_sender_config) {
    set_has_audio_sender_config();
    subtype_.audio_sender_config_ = audio_sender_config;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.Event.audio_sender_config)
}
inline ::webrtc::rtclog::AudioSendConfig* Event::_internal_mutable_audio_sender_config() {
  if (!_internal_has_audio_sender_config()) {
    clear_subtype();
    set_has_audio_sender_config();
    subtype_.audio_sender_config_ = CreateMaybeMessage< ::webrtc::rtclog::AudioSendConfig >(GetArenaForAllocation());
  }
  return subtype_.audio_sender_config_;
}
inline ::webrtc::rtclog::AudioSendConfig* Event::mutable_audio_sender_config() {
  ::webrtc::rtclog::AudioSendConfig* _msg = _internal_mutable_audio_sender_config();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.Event.audio_sender_config)
  return _msg;
}

// .webrtc.rtclog.AudioNetworkAdaptation audio_network_adaptation = 16;
inline bool Event::_internal_has_audio_network_adaptation() const {
  return subtype_case() == kAudioNetworkAdaptation;
}
inline bool Event::has_audio_network_adaptation() const {
  return _internal_has_audio_network_adaptation();
}
inline void Event::set_has_audio_network_adaptation() {
  _oneof_case_[0] = kAudioNetworkAdaptation;
}
inline void Event::clear_audio_network_adaptation() {
  if (_internal_has_audio_network_adaptation()) {
    if (GetArenaForAllocation() == nullptr) {
      delete subtype_.audio_network_adaptation_;
    }
    clear_has_subtype();
  }
}
inline ::webrtc::rtclog::AudioNetworkAdaptation* Event::release_audio_network_adaptation() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.Event.audio_network_adaptation)
  if (_internal_has_audio_network_adaptation()) {
    clear_has_subtype();
    ::webrtc::rtclog::AudioNetworkAdaptation* temp = subtype_.audio_network_adaptation_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    subtype_.audio_network_adaptation_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::webrtc::rtclog::AudioNetworkAdaptation& Event::_internal_audio_network_adaptation() const {
  return _internal_has_audio_network_adaptation()
      ? *subtype_.audio_network_adaptation_
      : reinterpret_cast< ::webrtc::rtclog::AudioNetworkAdaptation&>(::webrtc::rtclog::_AudioNetworkAdaptation_default_instance_);
}
inline const ::webrtc::rtclog::AudioNetworkAdaptation& Event::audio_network_adaptation() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.audio_network_adaptation)
  return _internal_audio_network_adaptation();
}
inline ::webrtc::rtclog::AudioNetworkAdaptation* Event::unsafe_arena_release_audio_network_adaptation() {
  // @@protoc_insertion_point(field_unsafe_arena_release:webrtc.rtclog.Event.audio_network_adaptation)
  if (_internal_has_audio_network_adaptation()) {
    clear_has_subtype();
    ::webrtc::rtclog::AudioNetworkAdaptation* temp = subtype_.audio_network_adaptation_;
    subtype_.audio_network_adaptation_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_audio_network_adaptation(::webrtc::rtclog::AudioNetworkAdaptation* audio_network_adaptation) {
  clear_subtype();
  if (audio_network_adaptation) {
    set_has_audio_network_adaptation();
    subtype_.audio_network_adaptation_ = audio_network_adaptation;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.Event.audio_network_adaptation)
}
inline ::webrtc::rtclog::AudioNetworkAdaptation* Event::_internal_mutable_audio_network_adaptation() {
  if (!_internal_has_audio_network_adaptation()) {
    clear_subtype();
    set_has_audio_network_adaptation();
    subtype_.audio_network_adaptation_ = CreateMaybeMessage< ::webrtc::rtclog::AudioNetworkAdaptation >(GetArenaForAllocation());
  }
  return subtype_.audio_network_adaptation_;
}
inline ::webrtc::rtclog::AudioNetworkAdaptation* Event::mutable_audio_network_adaptation() {
  ::webrtc::rtclog::AudioNetworkAdaptation* _msg = _internal_mutable_audio_network_adaptation();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.Event.audio_network_adaptation)
  return _msg;
}

// .webrtc.rtclog.BweProbeCluster probe_cluster = 17;
inline bool Event::_internal_has_probe_cluster() const {
  return subtype_case() == kProbeCluster;
}
inline bool Event::has_probe_cluster() const {
  return _internal_has_probe_cluster();
}
inline void Event::set_has_probe_cluster() {
  _oneof_case_[0] = kProbeCluster;
}
inline void Event::clear_probe_cluster() {
  if (_internal_has_probe_cluster()) {
    if (GetArenaForAllocation() == nullptr) {
      delete subtype_.probe_cluster_;
    }
    clear_has_subtype();
  }
}
inline ::webrtc::rtclog::BweProbeCluster* Event::release_probe_cluster() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.Event.probe_cluster)
  if (_internal_has_probe_cluster()) {
    clear_has_subtype();
    ::webrtc::rtclog::BweProbeCluster* temp = subtype_.probe_cluster_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    subtype_.probe_cluster_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::webrtc::rtclog::BweProbeCluster& Event::_internal_probe_cluster() const {
  return _internal_has_probe_cluster()
      ? *subtype_.probe_cluster_
      : reinterpret_cast< ::webrtc::rtclog::BweProbeCluster&>(::webrtc::rtclog::_BweProbeCluster_default_instance_);
}
inline const ::webrtc::rtclog::BweProbeCluster& Event::probe_cluster() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.probe_cluster)
  return _internal_probe_cluster();
}
inline ::webrtc::rtclog::BweProbeCluster* Event::unsafe_arena_release_probe_cluster() {
  // @@protoc_insertion_point(field_unsafe_arena_release:webrtc.rtclog.Event.probe_cluster)
  if (_internal_has_probe_cluster()) {
    clear_has_subtype();
    ::webrtc::rtclog::BweProbeCluster* temp = subtype_.probe_cluster_;
    subtype_.probe_cluster_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_probe_cluster(::webrtc::rtclog::BweProbeCluster* probe_cluster) {
  clear_subtype();
  if (probe_cluster) {
    set_has_probe_cluster();
    subtype_.probe_cluster_ = probe_cluster;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.Event.probe_cluster)
}
inline ::webrtc::rtclog::BweProbeCluster* Event::_internal_mutable_probe_cluster() {
  if (!_internal_has_probe_cluster()) {
    clear_subtype();
    set_has_probe_cluster();
    subtype_.probe_cluster_ = CreateMaybeMessage< ::webrtc::rtclog::BweProbeCluster >(GetArenaForAllocation());
  }
  return subtype_.probe_cluster_;
}
inline ::webrtc::rtclog::BweProbeCluster* Event::mutable_probe_cluster() {
  ::webrtc::rtclog::BweProbeCluster* _msg = _internal_mutable_probe_cluster();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.Event.probe_cluster)
  return _msg;
}

// .webrtc.rtclog.BweProbeResult probe_result = 18;
inline bool Event::_internal_has_probe_result() const {
  return subtype_case() == kProbeResult;
}
inline bool Event::has_probe_result() const {
  return _internal_has_probe_result();
}
inline void Event::set_has_probe_result() {
  _oneof_case_[0] = kProbeResult;
}
inline void Event::clear_probe_result() {
  if (_internal_has_probe_result()) {
    if (GetArenaForAllocation() == nullptr) {
      delete subtype_.probe_result_;
    }
    clear_has_subtype();
  }
}
inline ::webrtc::rtclog::BweProbeResult* Event::release_probe_result() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.Event.probe_result)
  if (_internal_has_probe_result()) {
    clear_has_subtype();
    ::webrtc::rtclog::BweProbeResult* temp = subtype_.probe_result_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    subtype_.probe_result_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::webrtc::rtclog::BweProbeResult& Event::_internal_probe_result() const {
  return _internal_has_probe_result()
      ? *subtype_.probe_result_
      : reinterpret_cast< ::webrtc::rtclog::BweProbeResult&>(::webrtc::rtclog::_BweProbeResult_default_instance_);
}
inline const ::webrtc::rtclog::BweProbeResult& Event::probe_result() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.probe_result)
  return _internal_probe_result();
}
inline ::webrtc::rtclog::BweProbeResult* Event::unsafe_arena_release_probe_result() {
  // @@protoc_insertion_point(field_unsafe_arena_release:webrtc.rtclog.Event.probe_result)
  if (_internal_has_probe_result()) {
    clear_has_subtype();
    ::webrtc::rtclog::BweProbeResult* temp = subtype_.probe_result_;
    subtype_.probe_result_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_probe_result(::webrtc::rtclog::BweProbeResult* probe_result) {
  clear_subtype();
  if (probe_result) {
    set_has_probe_result();
    subtype_.probe_result_ = probe_result;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.Event.probe_result)
}
inline ::webrtc::rtclog::BweProbeResult* Event::_internal_mutable_probe_result() {
  if (!_internal_has_probe_result()) {
    clear_subtype();
    set_has_probe_result();
    subtype_.probe_result_ = CreateMaybeMessage< ::webrtc::rtclog::BweProbeResult >(GetArenaForAllocation());
  }
  return subtype_.probe_result_;
}
inline ::webrtc::rtclog::BweProbeResult* Event::mutable_probe_result() {
  ::webrtc::rtclog::BweProbeResult* _msg = _internal_mutable_probe_result();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.Event.probe_result)
  return _msg;
}

// .webrtc.rtclog.AlrState alr_state = 19;
inline bool Event::_internal_has_alr_state() const {
  return subtype_case() == kAlrState;
}
inline bool Event::has_alr_state() const {
  return _internal_has_alr_state();
}
inline void Event::set_has_alr_state() {
  _oneof_case_[0] = kAlrState;
}
inline void Event::clear_alr_state() {
  if (_internal_has_alr_state()) {
    if (GetArenaForAllocation() == nullptr) {
      delete subtype_.alr_state_;
    }
    clear_has_subtype();
  }
}
inline ::webrtc::rtclog::AlrState* Event::release_alr_state() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.Event.alr_state)
  if (_internal_has_alr_state()) {
    clear_has_subtype();
    ::webrtc::rtclog::AlrState* temp = subtype_.alr_state_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    subtype_.alr_state_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::webrtc::rtclog::AlrState& Event::_internal_alr_state() const {
  return _internal_has_alr_state()
      ? *subtype_.alr_state_
      : reinterpret_cast< ::webrtc::rtclog::AlrState&>(::webrtc::rtclog::_AlrState_default_instance_);
}
inline const ::webrtc::rtclog::AlrState& Event::alr_state() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.alr_state)
  return _internal_alr_state();
}
inline ::webrtc::rtclog::AlrState* Event::unsafe_arena_release_alr_state() {
  // @@protoc_insertion_point(field_unsafe_arena_release:webrtc.rtclog.Event.alr_state)
  if (_internal_has_alr_state()) {
    clear_has_subtype();
    ::webrtc::rtclog::AlrState* temp = subtype_.alr_state_;
    subtype_.alr_state_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_alr_state(::webrtc::rtclog::AlrState* alr_state) {
  clear_subtype();
  if (alr_state) {
    set_has_alr_state();
    subtype_.alr_state_ = alr_state;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.Event.alr_state)
}
inline ::webrtc::rtclog::AlrState* Event::_internal_mutable_alr_state() {
  if (!_internal_has_alr_state()) {
    clear_subtype();
    set_has_alr_state();
    subtype_.alr_state_ = CreateMaybeMessage< ::webrtc::rtclog::AlrState >(GetArenaForAllocation());
  }
  return subtype_.alr_state_;
}
inline ::webrtc::rtclog::AlrState* Event::mutable_alr_state() {
  ::webrtc::rtclog::AlrState* _msg = _internal_mutable_alr_state();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.Event.alr_state)
  return _msg;
}

// .webrtc.rtclog.IceCandidatePairConfig ice_candidate_pair_config = 20;
inline bool Event::_internal_has_ice_candidate_pair_config() const {
  return subtype_case() == kIceCandidatePairConfig;
}
inline bool Event::has_ice_candidate_pair_config() const {
  return _internal_has_ice_candidate_pair_config();
}
inline void Event::set_has_ice_candidate_pair_config() {
  _oneof_case_[0] = kIceCandidatePairConfig;
}
inline void Event::clear_ice_candidate_pair_config() {
  if (_internal_has_ice_candidate_pair_config()) {
    if (GetArenaForAllocation() == nullptr) {
      delete subtype_.ice_candidate_pair_config_;
    }
    clear_has_subtype();
  }
}
inline ::webrtc::rtclog::IceCandidatePairConfig* Event::release_ice_candidate_pair_config() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.Event.ice_candidate_pair_config)
  if (_internal_has_ice_candidate_pair_config()) {
    clear_has_subtype();
    ::webrtc::rtclog::IceCandidatePairConfig* temp = subtype_.ice_candidate_pair_config_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    subtype_.ice_candidate_pair_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::webrtc::rtclog::IceCandidatePairConfig& Event::_internal_ice_candidate_pair_config() const {
  return _internal_has_ice_candidate_pair_config()
      ? *subtype_.ice_candidate_pair_config_
      : reinterpret_cast< ::webrtc::rtclog::IceCandidatePairConfig&>(::webrtc::rtclog::_IceCandidatePairConfig_default_instance_);
}
inline const ::webrtc::rtclog::IceCandidatePairConfig& Event::ice_candidate_pair_config() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.ice_candidate_pair_config)
  return _internal_ice_candidate_pair_config();
}
inline ::webrtc::rtclog::IceCandidatePairConfig* Event::unsafe_arena_release_ice_candidate_pair_config() {
  // @@protoc_insertion_point(field_unsafe_arena_release:webrtc.rtclog.Event.ice_candidate_pair_config)
  if (_internal_has_ice_candidate_pair_config()) {
    clear_has_subtype();
    ::webrtc::rtclog::IceCandidatePairConfig* temp = subtype_.ice_candidate_pair_config_;
    subtype_.ice_candidate_pair_config_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_ice_candidate_pair_config(::webrtc::rtclog::IceCandidatePairConfig* ice_candidate_pair_config) {
  clear_subtype();
  if (ice_candidate_pair_config) {
    set_has_ice_candidate_pair_config();
    subtype_.ice_candidate_pair_config_ = ice_candidate_pair_config;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.Event.ice_candidate_pair_config)
}
inline ::webrtc::rtclog::IceCandidatePairConfig* Event::_internal_mutable_ice_candidate_pair_config() {
  if (!_internal_has_ice_candidate_pair_config()) {
    clear_subtype();
    set_has_ice_candidate_pair_config();
    subtype_.ice_candidate_pair_config_ = CreateMaybeMessage< ::webrtc::rtclog::IceCandidatePairConfig >(GetArenaForAllocation());
  }
  return subtype_.ice_candidate_pair_config_;
}
inline ::webrtc::rtclog::IceCandidatePairConfig* Event::mutable_ice_candidate_pair_config() {
  ::webrtc::rtclog::IceCandidatePairConfig* _msg = _internal_mutable_ice_candidate_pair_config();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.Event.ice_candidate_pair_config)
  return _msg;
}

// .webrtc.rtclog.IceCandidatePairEvent ice_candidate_pair_event = 21;
inline bool Event::_internal_has_ice_candidate_pair_event() const {
  return subtype_case() == kIceCandidatePairEvent;
}
inline bool Event::has_ice_candidate_pair_event() const {
  return _internal_has_ice_candidate_pair_event();
}
inline void Event::set_has_ice_candidate_pair_event() {
  _oneof_case_[0] = kIceCandidatePairEvent;
}
inline void Event::clear_ice_candidate_pair_event() {
  if (_internal_has_ice_candidate_pair_event()) {
    if (GetArenaForAllocation() == nullptr) {
      delete subtype_.ice_candidate_pair_event_;
    }
    clear_has_subtype();
  }
}
inline ::webrtc::rtclog::IceCandidatePairEvent* Event::release_ice_candidate_pair_event() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.Event.ice_candidate_pair_event)
  if (_internal_has_ice_candidate_pair_event()) {
    clear_has_subtype();
    ::webrtc::rtclog::IceCandidatePairEvent* temp = subtype_.ice_candidate_pair_event_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    subtype_.ice_candidate_pair_event_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::webrtc::rtclog::IceCandidatePairEvent& Event::_internal_ice_candidate_pair_event() const {
  return _internal_has_ice_candidate_pair_event()
      ? *subtype_.ice_candidate_pair_event_
      : reinterpret_cast< ::webrtc::rtclog::IceCandidatePairEvent&>(::webrtc::rtclog::_IceCandidatePairEvent_default_instance_);
}
inline const ::webrtc::rtclog::IceCandidatePairEvent& Event::ice_candidate_pair_event() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.ice_candidate_pair_event)
  return _internal_ice_candidate_pair_event();
}
inline ::webrtc::rtclog::IceCandidatePairEvent* Event::unsafe_arena_release_ice_candidate_pair_event() {
  // @@protoc_insertion_point(field_unsafe_arena_release:webrtc.rtclog.Event.ice_candidate_pair_event)
  if (_internal_has_ice_candidate_pair_event()) {
    clear_has_subtype();
    ::webrtc::rtclog::IceCandidatePairEvent* temp = subtype_.ice_candidate_pair_event_;
    subtype_.ice_candidate_pair_event_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_ice_candidate_pair_event(::webrtc::rtclog::IceCandidatePairEvent* ice_candidate_pair_event) {
  clear_subtype();
  if (ice_candidate_pair_event) {
    set_has_ice_candidate_pair_event();
    subtype_.ice_candidate_pair_event_ = ice_candidate_pair_event;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.Event.ice_candidate_pair_event)
}
inline ::webrtc::rtclog::IceCandidatePairEvent* Event::_internal_mutable_ice_candidate_pair_event() {
  if (!_internal_has_ice_candidate_pair_event()) {
    clear_subtype();
    set_has_ice_candidate_pair_event();
    subtype_.ice_candidate_pair_event_ = CreateMaybeMessage< ::webrtc::rtclog::IceCandidatePairEvent >(GetArenaForAllocation());
  }
  return subtype_.ice_candidate_pair_event_;
}
inline ::webrtc::rtclog::IceCandidatePairEvent* Event::mutable_ice_candidate_pair_event() {
  ::webrtc::rtclog::IceCandidatePairEvent* _msg = _internal_mutable_ice_candidate_pair_event();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.Event.ice_candidate_pair_event)
  return _msg;
}

// .webrtc.rtclog.RemoteEstimate remote_estimate = 22;
inline bool Event::_internal_has_remote_estimate() const {
  return subtype_case() == kRemoteEstimate;
}
inline bool Event::has_remote_estimate() const {
  return _internal_has_remote_estimate();
}
inline void Event::set_has_remote_estimate() {
  _oneof_case_[0] = kRemoteEstimate;
}
inline void Event::clear_remote_estimate() {
  if (_internal_has_remote_estimate()) {
    if (GetArenaForAllocation() == nullptr) {
      delete subtype_.remote_estimate_;
    }
    clear_has_subtype();
  }
}
inline ::webrtc::rtclog::RemoteEstimate* Event::release_remote_estimate() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.Event.remote_estimate)
  if (_internal_has_remote_estimate()) {
    clear_has_subtype();
    ::webrtc::rtclog::RemoteEstimate* temp = subtype_.remote_estimate_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    subtype_.remote_estimate_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::webrtc::rtclog::RemoteEstimate& Event::_internal_remote_estimate() const {
  return _internal_has_remote_estimate()
      ? *subtype_.remote_estimate_
      : reinterpret_cast< ::webrtc::rtclog::RemoteEstimate&>(::webrtc::rtclog::_RemoteEstimate_default_instance_);
}
inline const ::webrtc::rtclog::RemoteEstimate& Event::remote_estimate() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.Event.remote_estimate)
  return _internal_remote_estimate();
}
inline ::webrtc::rtclog::RemoteEstimate* Event::unsafe_arena_release_remote_estimate() {
  // @@protoc_insertion_point(field_unsafe_arena_release:webrtc.rtclog.Event.remote_estimate)
  if (_internal_has_remote_estimate()) {
    clear_has_subtype();
    ::webrtc::rtclog::RemoteEstimate* temp = subtype_.remote_estimate_;
    subtype_.remote_estimate_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void Event::unsafe_arena_set_allocated_remote_estimate(::webrtc::rtclog::RemoteEstimate* remote_estimate) {
  clear_subtype();
  if (remote_estimate) {
    set_has_remote_estimate();
    subtype_.remote_estimate_ = remote_estimate;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.Event.remote_estimate)
}
inline ::webrtc::rtclog::RemoteEstimate* Event::_internal_mutable_remote_estimate() {
  if (!_internal_has_remote_estimate()) {
    clear_subtype();
    set_has_remote_estimate();
    subtype_.remote_estimate_ = CreateMaybeMessage< ::webrtc::rtclog::RemoteEstimate >(GetArenaForAllocation());
  }
  return subtype_.remote_estimate_;
}
inline ::webrtc::rtclog::RemoteEstimate* Event::mutable_remote_estimate() {
  ::webrtc::rtclog::RemoteEstimate* _msg = _internal_mutable_remote_estimate();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.Event.remote_estimate)
  return _msg;
}

inline bool Event::has_subtype() const {
  return subtype_case() != SUBTYPE_NOT_SET;
}
inline void Event::clear_has_subtype() {
  _oneof_case_[0] = SUBTYPE_NOT_SET;
}
inline Event::SubtypeCase Event::subtype_case() const {
  return Event::SubtypeCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// RtpPacket

// optional bool incoming = 1;
inline bool RtpPacket::_internal_has_incoming() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RtpPacket::has_incoming() const {
  return _internal_has_incoming();
}
inline void RtpPacket::clear_incoming() {
  incoming_ = false;
  _has_bits_[0] &= ~0x00000002u;
}
inline bool RtpPacket::_internal_incoming() const {
  return incoming_;
}
inline bool RtpPacket::incoming() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.RtpPacket.incoming)
  return _internal_incoming();
}
inline void RtpPacket::_internal_set_incoming(bool value) {
  _has_bits_[0] |= 0x00000002u;
  incoming_ = value;
}
inline void RtpPacket::set_incoming(bool value) {
  _internal_set_incoming(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.RtpPacket.incoming)
}

// optional .webrtc.rtclog.MediaType type = 2 [deprecated = true];
inline bool RtpPacket::_internal_has_type() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool RtpPacket::has_type() const {
  return _internal_has_type();
}
inline void RtpPacket::clear_type() {
  type_ = 0;
  _has_bits_[0] &= ~0x00000004u;
}
inline ::webrtc::rtclog::MediaType RtpPacket::_internal_type() const {
  return static_cast< ::webrtc::rtclog::MediaType >(type_);
}
inline ::webrtc::rtclog::MediaType RtpPacket::type() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.RtpPacket.type)
  return _internal_type();
}
inline void RtpPacket::_internal_set_type(::webrtc::rtclog::MediaType value) {
  assert(::webrtc::rtclog::MediaType_IsValid(value));
  _has_bits_[0] |= 0x00000004u;
  type_ = value;
}
inline void RtpPacket::set_type(::webrtc::rtclog::MediaType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.RtpPacket.type)
}

// optional uint32 packet_length = 3;
inline bool RtpPacket::_internal_has_packet_length() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool RtpPacket::has_packet_length() const {
  return _internal_has_packet_length();
}
inline void RtpPacket::clear_packet_length() {
  packet_length_ = 0u;
  _has_bits_[0] &= ~0x00000008u;
}
inline uint32_t RtpPacket::_internal_packet_length() const {
  return packet_length_;
}
inline uint32_t RtpPacket::packet_length() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.RtpPacket.packet_length)
  return _internal_packet_length();
}
inline void RtpPacket::_internal_set_packet_length(uint32_t value) {
  _has_bits_[0] |= 0x00000008u;
  packet_length_ = value;
}
inline void RtpPacket::set_packet_length(uint32_t value) {
  _internal_set_packet_length(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.RtpPacket.packet_length)
}

// optional bytes header = 4;
inline bool RtpPacket::_internal_has_header() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RtpPacket::has_header() const {
  return _internal_has_header();
}
inline void RtpPacket::clear_header() {
  header_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RtpPacket::header() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.RtpPacket.header)
  return _internal_header();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RtpPacket::set_header(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 header_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:webrtc.rtclog.RtpPacket.header)
}
inline std::string* RtpPacket::mutable_header() {
  std::string* _s = _internal_mutable_header();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.RtpPacket.header)
  return _s;
}
inline const std::string& RtpPacket::_internal_header() const {
  return header_.Get();
}
inline void RtpPacket::_internal_set_header(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  header_.Set(value, GetArenaForAllocation());
}
inline std::string* RtpPacket::_internal_mutable_header() {
  _has_bits_[0] |= 0x00000001u;
  return header_.Mutable(GetArenaForAllocation());
}
inline std::string* RtpPacket::release_header() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.RtpPacket.header)
  if (!_internal_has_header()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = header_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (header_.IsDefault()) {
    header_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void RtpPacket::set_allocated_header(std::string* header) {
  if (header != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  header_.SetAllocated(header, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (header_.IsDefault()) {
    header_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.RtpPacket.header)
}

// optional int32 probe_cluster_id = 5;
inline bool RtpPacket::_internal_has_probe_cluster_id() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool RtpPacket::has_probe_cluster_id() const {
  return _internal_has_probe_cluster_id();
}
inline void RtpPacket::clear_probe_cluster_id() {
  probe_cluster_id_ = 0;
  _has_bits_[0] &= ~0x00000010u;
}
inline int32_t RtpPacket::_internal_probe_cluster_id() const {
  return probe_cluster_id_;
}
inline int32_t RtpPacket::probe_cluster_id() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.RtpPacket.probe_cluster_id)
  return _internal_probe_cluster_id();
}
inline void RtpPacket::_internal_set_probe_cluster_id(int32_t value) {
  _has_bits_[0] |= 0x00000010u;
  probe_cluster_id_ = value;
}
inline void RtpPacket::set_probe_cluster_id(int32_t value) {
  _internal_set_probe_cluster_id(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.RtpPacket.probe_cluster_id)
}

// -------------------------------------------------------------------

// RtcpPacket

// optional bool incoming = 1;
inline bool RtcpPacket::_internal_has_incoming() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RtcpPacket::has_incoming() const {
  return _internal_has_incoming();
}
inline void RtcpPacket::clear_incoming() {
  incoming_ = false;
  _has_bits_[0] &= ~0x00000002u;
}
inline bool RtcpPacket::_internal_incoming() const {
  return incoming_;
}
inline bool RtcpPacket::incoming() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.RtcpPacket.incoming)
  return _internal_incoming();
}
inline void RtcpPacket::_internal_set_incoming(bool value) {
  _has_bits_[0] |= 0x00000002u;
  incoming_ = value;
}
inline void RtcpPacket::set_incoming(bool value) {
  _internal_set_incoming(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.RtcpPacket.incoming)
}

// optional .webrtc.rtclog.MediaType type = 2 [deprecated = true];
inline bool RtcpPacket::_internal_has_type() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool RtcpPacket::has_type() const {
  return _internal_has_type();
}
inline void RtcpPacket::clear_type() {
  type_ = 0;
  _has_bits_[0] &= ~0x00000004u;
}
inline ::webrtc::rtclog::MediaType RtcpPacket::_internal_type() const {
  return static_cast< ::webrtc::rtclog::MediaType >(type_);
}
inline ::webrtc::rtclog::MediaType RtcpPacket::type() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.RtcpPacket.type)
  return _internal_type();
}
inline void RtcpPacket::_internal_set_type(::webrtc::rtclog::MediaType value) {
  assert(::webrtc::rtclog::MediaType_IsValid(value));
  _has_bits_[0] |= 0x00000004u;
  type_ = value;
}
inline void RtcpPacket::set_type(::webrtc::rtclog::MediaType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.RtcpPacket.type)
}

// optional bytes packet_data = 3;
inline bool RtcpPacket::_internal_has_packet_data() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RtcpPacket::has_packet_data() const {
  return _internal_has_packet_data();
}
inline void RtcpPacket::clear_packet_data() {
  packet_data_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RtcpPacket::packet_data() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.RtcpPacket.packet_data)
  return _internal_packet_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RtcpPacket::set_packet_data(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 packet_data_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:webrtc.rtclog.RtcpPacket.packet_data)
}
inline std::string* RtcpPacket::mutable_packet_data() {
  std::string* _s = _internal_mutable_packet_data();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.RtcpPacket.packet_data)
  return _s;
}
inline const std::string& RtcpPacket::_internal_packet_data() const {
  return packet_data_.Get();
}
inline void RtcpPacket::_internal_set_packet_data(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  packet_data_.Set(value, GetArenaForAllocation());
}
inline std::string* RtcpPacket::_internal_mutable_packet_data() {
  _has_bits_[0] |= 0x00000001u;
  return packet_data_.Mutable(GetArenaForAllocation());
}
inline std::string* RtcpPacket::release_packet_data() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.RtcpPacket.packet_data)
  if (!_internal_has_packet_data()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = packet_data_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (packet_data_.IsDefault()) {
    packet_data_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void RtcpPacket::set_allocated_packet_data(std::string* packet_data) {
  if (packet_data != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  packet_data_.SetAllocated(packet_data, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (packet_data_.IsDefault()) {
    packet_data_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.RtcpPacket.packet_data)
}

// -------------------------------------------------------------------

// AudioPlayoutEvent

// optional uint32 local_ssrc = 2;
inline bool AudioPlayoutEvent::_internal_has_local_ssrc() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool AudioPlayoutEvent::has_local_ssrc() const {
  return _internal_has_local_ssrc();
}
inline void AudioPlayoutEvent::clear_local_ssrc() {
  local_ssrc_ = 0u;
  _has_bits_[0] &= ~0x00000001u;
}
inline uint32_t AudioPlayoutEvent::_internal_local_ssrc() const {
  return local_ssrc_;
}
inline uint32_t AudioPlayoutEvent::local_ssrc() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.AudioPlayoutEvent.local_ssrc)
  return _internal_local_ssrc();
}
inline void AudioPlayoutEvent::_internal_set_local_ssrc(uint32_t value) {
  _has_bits_[0] |= 0x00000001u;
  local_ssrc_ = value;
}
inline void AudioPlayoutEvent::set_local_ssrc(uint32_t value) {
  _internal_set_local_ssrc(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.AudioPlayoutEvent.local_ssrc)
}

// -------------------------------------------------------------------

// LossBasedBweUpdate

// optional int32 bitrate_bps = 1;
inline bool LossBasedBweUpdate::_internal_has_bitrate_bps() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool LossBasedBweUpdate::has_bitrate_bps() const {
  return _internal_has_bitrate_bps();
}
inline void LossBasedBweUpdate::clear_bitrate_bps() {
  bitrate_bps_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline int32_t LossBasedBweUpdate::_internal_bitrate_bps() const {
  return bitrate_bps_;
}
inline int32_t LossBasedBweUpdate::bitrate_bps() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.LossBasedBweUpdate.bitrate_bps)
  return _internal_bitrate_bps();
}
inline void LossBasedBweUpdate::_internal_set_bitrate_bps(int32_t value) {
  _has_bits_[0] |= 0x00000001u;
  bitrate_bps_ = value;
}
inline void LossBasedBweUpdate::set_bitrate_bps(int32_t value) {
  _internal_set_bitrate_bps(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.LossBasedBweUpdate.bitrate_bps)
}

// optional uint32 fraction_loss = 2;
inline bool LossBasedBweUpdate::_internal_has_fraction_loss() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool LossBasedBweUpdate::has_fraction_loss() const {
  return _internal_has_fraction_loss();
}
inline void LossBasedBweUpdate::clear_fraction_loss() {
  fraction_loss_ = 0u;
  _has_bits_[0] &= ~0x00000002u;
}
inline uint32_t LossBasedBweUpdate::_internal_fraction_loss() const {
  return fraction_loss_;
}
inline uint32_t LossBasedBweUpdate::fraction_loss() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.LossBasedBweUpdate.fraction_loss)
  return _internal_fraction_loss();
}
inline void LossBasedBweUpdate::_internal_set_fraction_loss(uint32_t value) {
  _has_bits_[0] |= 0x00000002u;
  fraction_loss_ = value;
}
inline void LossBasedBweUpdate::set_fraction_loss(uint32_t value) {
  _internal_set_fraction_loss(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.LossBasedBweUpdate.fraction_loss)
}

// optional int32 total_packets = 3;
inline bool LossBasedBweUpdate::_internal_has_total_packets() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool LossBasedBweUpdate::has_total_packets() const {
  return _internal_has_total_packets();
}
inline void LossBasedBweUpdate::clear_total_packets() {
  total_packets_ = 0;
  _has_bits_[0] &= ~0x00000004u;
}
inline int32_t LossBasedBweUpdate::_internal_total_packets() const {
  return total_packets_;
}
inline int32_t LossBasedBweUpdate::total_packets() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.LossBasedBweUpdate.total_packets)
  return _internal_total_packets();
}
inline void LossBasedBweUpdate::_internal_set_total_packets(int32_t value) {
  _has_bits_[0] |= 0x00000004u;
  total_packets_ = value;
}
inline void LossBasedBweUpdate::set_total_packets(int32_t value) {
  _internal_set_total_packets(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.LossBasedBweUpdate.total_packets)
}

// -------------------------------------------------------------------

// DelayBasedBweUpdate

// optional int32 bitrate_bps = 1;
inline bool DelayBasedBweUpdate::_internal_has_bitrate_bps() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool DelayBasedBweUpdate::has_bitrate_bps() const {
  return _internal_has_bitrate_bps();
}
inline void DelayBasedBweUpdate::clear_bitrate_bps() {
  bitrate_bps_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline int32_t DelayBasedBweUpdate::_internal_bitrate_bps() const {
  return bitrate_bps_;
}
inline int32_t DelayBasedBweUpdate::bitrate_bps() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.DelayBasedBweUpdate.bitrate_bps)
  return _internal_bitrate_bps();
}
inline void DelayBasedBweUpdate::_internal_set_bitrate_bps(int32_t value) {
  _has_bits_[0] |= 0x00000001u;
  bitrate_bps_ = value;
}
inline void DelayBasedBweUpdate::set_bitrate_bps(int32_t value) {
  _internal_set_bitrate_bps(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.DelayBasedBweUpdate.bitrate_bps)
}

// optional .webrtc.rtclog.DelayBasedBweUpdate.DetectorState detector_state = 2;
inline bool DelayBasedBweUpdate::_internal_has_detector_state() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool DelayBasedBweUpdate::has_detector_state() const {
  return _internal_has_detector_state();
}
inline void DelayBasedBweUpdate::clear_detector_state() {
  detector_state_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::webrtc::rtclog::DelayBasedBweUpdate_DetectorState DelayBasedBweUpdate::_internal_detector_state() const {
  return static_cast< ::webrtc::rtclog::DelayBasedBweUpdate_DetectorState >(detector_state_);
}
inline ::webrtc::rtclog::DelayBasedBweUpdate_DetectorState DelayBasedBweUpdate::detector_state() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.DelayBasedBweUpdate.detector_state)
  return _internal_detector_state();
}
inline void DelayBasedBweUpdate::_internal_set_detector_state(::webrtc::rtclog::DelayBasedBweUpdate_DetectorState value) {
  assert(::webrtc::rtclog::DelayBasedBweUpdate_DetectorState_IsValid(value));
  _has_bits_[0] |= 0x00000002u;
  detector_state_ = value;
}
inline void DelayBasedBweUpdate::set_detector_state(::webrtc::rtclog::DelayBasedBweUpdate_DetectorState value) {
  _internal_set_detector_state(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.DelayBasedBweUpdate.detector_state)
}

// -------------------------------------------------------------------

// VideoReceiveConfig

// optional uint32 remote_ssrc = 1;
inline bool VideoReceiveConfig::_internal_has_remote_ssrc() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool VideoReceiveConfig::has_remote_ssrc() const {
  return _internal_has_remote_ssrc();
}
inline void VideoReceiveConfig::clear_remote_ssrc() {
  remote_ssrc_ = 0u;
  _has_bits_[0] &= ~0x00000001u;
}
inline uint32_t VideoReceiveConfig::_internal_remote_ssrc() const {
  return remote_ssrc_;
}
inline uint32_t VideoReceiveConfig::remote_ssrc() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.VideoReceiveConfig.remote_ssrc)
  return _internal_remote_ssrc();
}
inline void VideoReceiveConfig::_internal_set_remote_ssrc(uint32_t value) {
  _has_bits_[0] |= 0x00000001u;
  remote_ssrc_ = value;
}
inline void VideoReceiveConfig::set_remote_ssrc(uint32_t value) {
  _internal_set_remote_ssrc(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.VideoReceiveConfig.remote_ssrc)
}

// optional uint32 local_ssrc = 2;
inline bool VideoReceiveConfig::_internal_has_local_ssrc() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool VideoReceiveConfig::has_local_ssrc() const {
  return _internal_has_local_ssrc();
}
inline void VideoReceiveConfig::clear_local_ssrc() {
  local_ssrc_ = 0u;
  _has_bits_[0] &= ~0x00000002u;
}
inline uint32_t VideoReceiveConfig::_internal_local_ssrc() const {
  return local_ssrc_;
}
inline uint32_t VideoReceiveConfig::local_ssrc() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.VideoReceiveConfig.local_ssrc)
  return _internal_local_ssrc();
}
inline void VideoReceiveConfig::_internal_set_local_ssrc(uint32_t value) {
  _has_bits_[0] |= 0x00000002u;
  local_ssrc_ = value;
}
inline void VideoReceiveConfig::set_local_ssrc(uint32_t value) {
  _internal_set_local_ssrc(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.VideoReceiveConfig.local_ssrc)
}

// optional .webrtc.rtclog.VideoReceiveConfig.RtcpMode rtcp_mode = 3;
inline bool VideoReceiveConfig::_internal_has_rtcp_mode() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool VideoReceiveConfig::has_rtcp_mode() const {
  return _internal_has_rtcp_mode();
}
inline void VideoReceiveConfig::clear_rtcp_mode() {
  rtcp_mode_ = 1;
  _has_bits_[0] &= ~0x00000008u;
}
inline ::webrtc::rtclog::VideoReceiveConfig_RtcpMode VideoReceiveConfig::_internal_rtcp_mode() const {
  return static_cast< ::webrtc::rtclog::VideoReceiveConfig_RtcpMode >(rtcp_mode_);
}
inline ::webrtc::rtclog::VideoReceiveConfig_RtcpMode VideoReceiveConfig::rtcp_mode() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.VideoReceiveConfig.rtcp_mode)
  return _internal_rtcp_mode();
}
inline void VideoReceiveConfig::_internal_set_rtcp_mode(::webrtc::rtclog::VideoReceiveConfig_RtcpMode value) {
  assert(::webrtc::rtclog::VideoReceiveConfig_RtcpMode_IsValid(value));
  _has_bits_[0] |= 0x00000008u;
  rtcp_mode_ = value;
}
inline void VideoReceiveConfig::set_rtcp_mode(::webrtc::rtclog::VideoReceiveConfig_RtcpMode value) {
  _internal_set_rtcp_mode(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.VideoReceiveConfig.rtcp_mode)
}

// optional bool remb = 4;
inline bool VideoReceiveConfig::_internal_has_remb() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool VideoReceiveConfig::has_remb() const {
  return _internal_has_remb();
}
inline void VideoReceiveConfig::clear_remb() {
  remb_ = false;
  _has_bits_[0] &= ~0x00000004u;
}
inline bool VideoReceiveConfig::_internal_remb() const {
  return remb_;
}
inline bool VideoReceiveConfig::remb() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.VideoReceiveConfig.remb)
  return _internal_remb();
}
inline void VideoReceiveConfig::_internal_set_remb(bool value) {
  _has_bits_[0] |= 0x00000004u;
  remb_ = value;
}
inline void VideoReceiveConfig::set_remb(bool value) {
  _internal_set_remb(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.VideoReceiveConfig.remb)
}

// repeated .webrtc.rtclog.RtxMap rtx_map = 5;
inline int VideoReceiveConfig::_internal_rtx_map_size() const {
  return rtx_map_.size();
}
inline int VideoReceiveConfig::rtx_map_size() const {
  return _internal_rtx_map_size();
}
inline void VideoReceiveConfig::clear_rtx_map() {
  rtx_map_.Clear();
}
inline ::webrtc::rtclog::RtxMap* VideoReceiveConfig::mutable_rtx_map(int index) {
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.VideoReceiveConfig.rtx_map)
  return rtx_map_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtxMap >*
VideoReceiveConfig::mutable_rtx_map() {
  // @@protoc_insertion_point(field_mutable_list:webrtc.rtclog.VideoReceiveConfig.rtx_map)
  return &rtx_map_;
}
inline const ::webrtc::rtclog::RtxMap& VideoReceiveConfig::_internal_rtx_map(int index) const {
  return rtx_map_.Get(index);
}
inline const ::webrtc::rtclog::RtxMap& VideoReceiveConfig::rtx_map(int index) const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.VideoReceiveConfig.rtx_map)
  return _internal_rtx_map(index);
}
inline ::webrtc::rtclog::RtxMap* VideoReceiveConfig::_internal_add_rtx_map() {
  return rtx_map_.Add();
}
inline ::webrtc::rtclog::RtxMap* VideoReceiveConfig::add_rtx_map() {
  ::webrtc::rtclog::RtxMap* _add = _internal_add_rtx_map();
  // @@protoc_insertion_point(field_add:webrtc.rtclog.VideoReceiveConfig.rtx_map)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtxMap >&
VideoReceiveConfig::rtx_map() const {
  // @@protoc_insertion_point(field_list:webrtc.rtclog.VideoReceiveConfig.rtx_map)
  return rtx_map_;
}

// repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 6;
inline int VideoReceiveConfig::_internal_header_extensions_size() const {
  return header_extensions_.size();
}
inline int VideoReceiveConfig::header_extensions_size() const {
  return _internal_header_extensions_size();
}
inline void VideoReceiveConfig::clear_header_extensions() {
  header_extensions_.Clear();
}
inline ::webrtc::rtclog::RtpHeaderExtension* VideoReceiveConfig::mutable_header_extensions(int index) {
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.VideoReceiveConfig.header_extensions)
  return header_extensions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension >*
VideoReceiveConfig::mutable_header_extensions() {
  // @@protoc_insertion_point(field_mutable_list:webrtc.rtclog.VideoReceiveConfig.header_extensions)
  return &header_extensions_;
}
inline const ::webrtc::rtclog::RtpHeaderExtension& VideoReceiveConfig::_internal_header_extensions(int index) const {
  return header_extensions_.Get(index);
}
inline const ::webrtc::rtclog::RtpHeaderExtension& VideoReceiveConfig::header_extensions(int index) const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.VideoReceiveConfig.header_extensions)
  return _internal_header_extensions(index);
}
inline ::webrtc::rtclog::RtpHeaderExtension* VideoReceiveConfig::_internal_add_header_extensions() {
  return header_extensions_.Add();
}
inline ::webrtc::rtclog::RtpHeaderExtension* VideoReceiveConfig::add_header_extensions() {
  ::webrtc::rtclog::RtpHeaderExtension* _add = _internal_add_header_extensions();
  // @@protoc_insertion_point(field_add:webrtc.rtclog.VideoReceiveConfig.header_extensions)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension >&
VideoReceiveConfig::header_extensions() const {
  // @@protoc_insertion_point(field_list:webrtc.rtclog.VideoReceiveConfig.header_extensions)
  return header_extensions_;
}

// repeated .webrtc.rtclog.DecoderConfig decoders = 7;
inline int VideoReceiveConfig::_internal_decoders_size() const {
  return decoders_.size();
}
inline int VideoReceiveConfig::decoders_size() const {
  return _internal_decoders_size();
}
inline void VideoReceiveConfig::clear_decoders() {
  decoders_.Clear();
}
inline ::webrtc::rtclog::DecoderConfig* VideoReceiveConfig::mutable_decoders(int index) {
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.VideoReceiveConfig.decoders)
  return decoders_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::DecoderConfig >*
VideoReceiveConfig::mutable_decoders() {
  // @@protoc_insertion_point(field_mutable_list:webrtc.rtclog.VideoReceiveConfig.decoders)
  return &decoders_;
}
inline const ::webrtc::rtclog::DecoderConfig& VideoReceiveConfig::_internal_decoders(int index) const {
  return decoders_.Get(index);
}
inline const ::webrtc::rtclog::DecoderConfig& VideoReceiveConfig::decoders(int index) const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.VideoReceiveConfig.decoders)
  return _internal_decoders(index);
}
inline ::webrtc::rtclog::DecoderConfig* VideoReceiveConfig::_internal_add_decoders() {
  return decoders_.Add();
}
inline ::webrtc::rtclog::DecoderConfig* VideoReceiveConfig::add_decoders() {
  ::webrtc::rtclog::DecoderConfig* _add = _internal_add_decoders();
  // @@protoc_insertion_point(field_add:webrtc.rtclog.VideoReceiveConfig.decoders)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::DecoderConfig >&
VideoReceiveConfig::decoders() const {
  // @@protoc_insertion_point(field_list:webrtc.rtclog.VideoReceiveConfig.decoders)
  return decoders_;
}

// -------------------------------------------------------------------

// DecoderConfig

// optional string name = 1;
inline bool DecoderConfig::_internal_has_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool DecoderConfig::has_name() const {
  return _internal_has_name();
}
inline void DecoderConfig::clear_name() {
  name_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& DecoderConfig::name() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.DecoderConfig.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DecoderConfig::set_name(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:webrtc.rtclog.DecoderConfig.name)
}
inline std::string* DecoderConfig::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.DecoderConfig.name)
  return _s;
}
inline const std::string& DecoderConfig::_internal_name() const {
  return name_.Get();
}
inline void DecoderConfig::_internal_set_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.Set(value, GetArenaForAllocation());
}
inline std::string* DecoderConfig::_internal_mutable_name() {
  _has_bits_[0] |= 0x00000001u;
  return name_.Mutable(GetArenaForAllocation());
}
inline std::string* DecoderConfig::release_name() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.DecoderConfig.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = name_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault()) {
    name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void DecoderConfig::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault()) {
    name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.DecoderConfig.name)
}

// optional int32 payload_type = 2;
inline bool DecoderConfig::_internal_has_payload_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool DecoderConfig::has_payload_type() const {
  return _internal_has_payload_type();
}
inline void DecoderConfig::clear_payload_type() {
  payload_type_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline int32_t DecoderConfig::_internal_payload_type() const {
  return payload_type_;
}
inline int32_t DecoderConfig::payload_type() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.DecoderConfig.payload_type)
  return _internal_payload_type();
}
inline void DecoderConfig::_internal_set_payload_type(int32_t value) {
  _has_bits_[0] |= 0x00000002u;
  payload_type_ = value;
}
inline void DecoderConfig::set_payload_type(int32_t value) {
  _internal_set_payload_type(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.DecoderConfig.payload_type)
}

// -------------------------------------------------------------------

// RtpHeaderExtension

// optional string name = 1;
inline bool RtpHeaderExtension::_internal_has_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RtpHeaderExtension::has_name() const {
  return _internal_has_name();
}
inline void RtpHeaderExtension::clear_name() {
  name_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& RtpHeaderExtension::name() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.RtpHeaderExtension.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RtpHeaderExtension::set_name(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:webrtc.rtclog.RtpHeaderExtension.name)
}
inline std::string* RtpHeaderExtension::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.RtpHeaderExtension.name)
  return _s;
}
inline const std::string& RtpHeaderExtension::_internal_name() const {
  return name_.Get();
}
inline void RtpHeaderExtension::_internal_set_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.Set(value, GetArenaForAllocation());
}
inline std::string* RtpHeaderExtension::_internal_mutable_name() {
  _has_bits_[0] |= 0x00000001u;
  return name_.Mutable(GetArenaForAllocation());
}
inline std::string* RtpHeaderExtension::release_name() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.RtpHeaderExtension.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = name_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault()) {
    name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void RtpHeaderExtension::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault()) {
    name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.RtpHeaderExtension.name)
}

// optional int32 id = 2;
inline bool RtpHeaderExtension::_internal_has_id() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RtpHeaderExtension::has_id() const {
  return _internal_has_id();
}
inline void RtpHeaderExtension::clear_id() {
  id_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline int32_t RtpHeaderExtension::_internal_id() const {
  return id_;
}
inline int32_t RtpHeaderExtension::id() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.RtpHeaderExtension.id)
  return _internal_id();
}
inline void RtpHeaderExtension::_internal_set_id(int32_t value) {
  _has_bits_[0] |= 0x00000002u;
  id_ = value;
}
inline void RtpHeaderExtension::set_id(int32_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.RtpHeaderExtension.id)
}

// -------------------------------------------------------------------

// RtxConfig

// optional uint32 rtx_ssrc = 1;
inline bool RtxConfig::_internal_has_rtx_ssrc() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RtxConfig::has_rtx_ssrc() const {
  return _internal_has_rtx_ssrc();
}
inline void RtxConfig::clear_rtx_ssrc() {
  rtx_ssrc_ = 0u;
  _has_bits_[0] &= ~0x00000001u;
}
inline uint32_t RtxConfig::_internal_rtx_ssrc() const {
  return rtx_ssrc_;
}
inline uint32_t RtxConfig::rtx_ssrc() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.RtxConfig.rtx_ssrc)
  return _internal_rtx_ssrc();
}
inline void RtxConfig::_internal_set_rtx_ssrc(uint32_t value) {
  _has_bits_[0] |= 0x00000001u;
  rtx_ssrc_ = value;
}
inline void RtxConfig::set_rtx_ssrc(uint32_t value) {
  _internal_set_rtx_ssrc(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.RtxConfig.rtx_ssrc)
}

// optional int32 rtx_payload_type = 2;
inline bool RtxConfig::_internal_has_rtx_payload_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RtxConfig::has_rtx_payload_type() const {
  return _internal_has_rtx_payload_type();
}
inline void RtxConfig::clear_rtx_payload_type() {
  rtx_payload_type_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline int32_t RtxConfig::_internal_rtx_payload_type() const {
  return rtx_payload_type_;
}
inline int32_t RtxConfig::rtx_payload_type() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.RtxConfig.rtx_payload_type)
  return _internal_rtx_payload_type();
}
inline void RtxConfig::_internal_set_rtx_payload_type(int32_t value) {
  _has_bits_[0] |= 0x00000002u;
  rtx_payload_type_ = value;
}
inline void RtxConfig::set_rtx_payload_type(int32_t value) {
  _internal_set_rtx_payload_type(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.RtxConfig.rtx_payload_type)
}

// -------------------------------------------------------------------

// RtxMap

// optional int32 payload_type = 1;
inline bool RtxMap::_internal_has_payload_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RtxMap::has_payload_type() const {
  return _internal_has_payload_type();
}
inline void RtxMap::clear_payload_type() {
  payload_type_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline int32_t RtxMap::_internal_payload_type() const {
  return payload_type_;
}
inline int32_t RtxMap::payload_type() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.RtxMap.payload_type)
  return _internal_payload_type();
}
inline void RtxMap::_internal_set_payload_type(int32_t value) {
  _has_bits_[0] |= 0x00000002u;
  payload_type_ = value;
}
inline void RtxMap::set_payload_type(int32_t value) {
  _internal_set_payload_type(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.RtxMap.payload_type)
}

// optional .webrtc.rtclog.RtxConfig config = 2;
inline bool RtxMap::_internal_has_config() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || config_ != nullptr);
  return value;
}
inline bool RtxMap::has_config() const {
  return _internal_has_config();
}
inline void RtxMap::clear_config() {
  if (config_ != nullptr) config_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::webrtc::rtclog::RtxConfig& RtxMap::_internal_config() const {
  const ::webrtc::rtclog::RtxConfig* p = config_;
  return p != nullptr ? *p : reinterpret_cast<const ::webrtc::rtclog::RtxConfig&>(
      ::webrtc::rtclog::_RtxConfig_default_instance_);
}
inline const ::webrtc::rtclog::RtxConfig& RtxMap::config() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.RtxMap.config)
  return _internal_config();
}
inline void RtxMap::unsafe_arena_set_allocated_config(
    ::webrtc::rtclog::RtxConfig* config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  config_ = config;
  if (config) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.RtxMap.config)
}
inline ::webrtc::rtclog::RtxConfig* RtxMap::release_config() {
  _has_bits_[0] &= ~0x00000001u;
  ::webrtc::rtclog::RtxConfig* temp = config_;
  config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::webrtc::rtclog::RtxConfig* RtxMap::unsafe_arena_release_config() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.RtxMap.config)
  _has_bits_[0] &= ~0x00000001u;
  ::webrtc::rtclog::RtxConfig* temp = config_;
  config_ = nullptr;
  return temp;
}
inline ::webrtc::rtclog::RtxConfig* RtxMap::_internal_mutable_config() {
  _has_bits_[0] |= 0x00000001u;
  if (config_ == nullptr) {
    auto* p = CreateMaybeMessage<::webrtc::rtclog::RtxConfig>(GetArenaForAllocation());
    config_ = p;
  }
  return config_;
}
inline ::webrtc::rtclog::RtxConfig* RtxMap::mutable_config() {
  ::webrtc::rtclog::RtxConfig* _msg = _internal_mutable_config();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.RtxMap.config)
  return _msg;
}
inline void RtxMap::set_allocated_config(::webrtc::rtclog::RtxConfig* config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete config_;
  }
  if (config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(config);
    if (message_arena != submessage_arena) {
      config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, config, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  config_ = config;
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.RtxMap.config)
}

// -------------------------------------------------------------------

// VideoSendConfig

// repeated uint32 ssrcs = 1;
inline int VideoSendConfig::_internal_ssrcs_size() const {
  return ssrcs_.size();
}
inline int VideoSendConfig::ssrcs_size() const {
  return _internal_ssrcs_size();
}
inline void VideoSendConfig::clear_ssrcs() {
  ssrcs_.Clear();
}
inline uint32_t VideoSendConfig::_internal_ssrcs(int index) const {
  return ssrcs_.Get(index);
}
inline uint32_t VideoSendConfig::ssrcs(int index) const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.VideoSendConfig.ssrcs)
  return _internal_ssrcs(index);
}
inline void VideoSendConfig::set_ssrcs(int index, uint32_t value) {
  ssrcs_.Set(index, value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.VideoSendConfig.ssrcs)
}
inline void VideoSendConfig::_internal_add_ssrcs(uint32_t value) {
  ssrcs_.Add(value);
}
inline void VideoSendConfig::add_ssrcs(uint32_t value) {
  _internal_add_ssrcs(value);
  // @@protoc_insertion_point(field_add:webrtc.rtclog.VideoSendConfig.ssrcs)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
VideoSendConfig::_internal_ssrcs() const {
  return ssrcs_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
VideoSendConfig::ssrcs() const {
  // @@protoc_insertion_point(field_list:webrtc.rtclog.VideoSendConfig.ssrcs)
  return _internal_ssrcs();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
VideoSendConfig::_internal_mutable_ssrcs() {
  return &ssrcs_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
VideoSendConfig::mutable_ssrcs() {
  // @@protoc_insertion_point(field_mutable_list:webrtc.rtclog.VideoSendConfig.ssrcs)
  return _internal_mutable_ssrcs();
}

// repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 2;
inline int VideoSendConfig::_internal_header_extensions_size() const {
  return header_extensions_.size();
}
inline int VideoSendConfig::header_extensions_size() const {
  return _internal_header_extensions_size();
}
inline void VideoSendConfig::clear_header_extensions() {
  header_extensions_.Clear();
}
inline ::webrtc::rtclog::RtpHeaderExtension* VideoSendConfig::mutable_header_extensions(int index) {
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.VideoSendConfig.header_extensions)
  return header_extensions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension >*
VideoSendConfig::mutable_header_extensions() {
  // @@protoc_insertion_point(field_mutable_list:webrtc.rtclog.VideoSendConfig.header_extensions)
  return &header_extensions_;
}
inline const ::webrtc::rtclog::RtpHeaderExtension& VideoSendConfig::_internal_header_extensions(int index) const {
  return header_extensions_.Get(index);
}
inline const ::webrtc::rtclog::RtpHeaderExtension& VideoSendConfig::header_extensions(int index) const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.VideoSendConfig.header_extensions)
  return _internal_header_extensions(index);
}
inline ::webrtc::rtclog::RtpHeaderExtension* VideoSendConfig::_internal_add_header_extensions() {
  return header_extensions_.Add();
}
inline ::webrtc::rtclog::RtpHeaderExtension* VideoSendConfig::add_header_extensions() {
  ::webrtc::rtclog::RtpHeaderExtension* _add = _internal_add_header_extensions();
  // @@protoc_insertion_point(field_add:webrtc.rtclog.VideoSendConfig.header_extensions)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension >&
VideoSendConfig::header_extensions() const {
  // @@protoc_insertion_point(field_list:webrtc.rtclog.VideoSendConfig.header_extensions)
  return header_extensions_;
}

// repeated uint32 rtx_ssrcs = 3;
inline int VideoSendConfig::_internal_rtx_ssrcs_size() const {
  return rtx_ssrcs_.size();
}
inline int VideoSendConfig::rtx_ssrcs_size() const {
  return _internal_rtx_ssrcs_size();
}
inline void VideoSendConfig::clear_rtx_ssrcs() {
  rtx_ssrcs_.Clear();
}
inline uint32_t VideoSendConfig::_internal_rtx_ssrcs(int index) const {
  return rtx_ssrcs_.Get(index);
}
inline uint32_t VideoSendConfig::rtx_ssrcs(int index) const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.VideoSendConfig.rtx_ssrcs)
  return _internal_rtx_ssrcs(index);
}
inline void VideoSendConfig::set_rtx_ssrcs(int index, uint32_t value) {
  rtx_ssrcs_.Set(index, value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.VideoSendConfig.rtx_ssrcs)
}
inline void VideoSendConfig::_internal_add_rtx_ssrcs(uint32_t value) {
  rtx_ssrcs_.Add(value);
}
inline void VideoSendConfig::add_rtx_ssrcs(uint32_t value) {
  _internal_add_rtx_ssrcs(value);
  // @@protoc_insertion_point(field_add:webrtc.rtclog.VideoSendConfig.rtx_ssrcs)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
VideoSendConfig::_internal_rtx_ssrcs() const {
  return rtx_ssrcs_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >&
VideoSendConfig::rtx_ssrcs() const {
  // @@protoc_insertion_point(field_list:webrtc.rtclog.VideoSendConfig.rtx_ssrcs)
  return _internal_rtx_ssrcs();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
VideoSendConfig::_internal_mutable_rtx_ssrcs() {
  return &rtx_ssrcs_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< uint32_t >*
VideoSendConfig::mutable_rtx_ssrcs() {
  // @@protoc_insertion_point(field_mutable_list:webrtc.rtclog.VideoSendConfig.rtx_ssrcs)
  return _internal_mutable_rtx_ssrcs();
}

// optional int32 rtx_payload_type = 4;
inline bool VideoSendConfig::_internal_has_rtx_payload_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool VideoSendConfig::has_rtx_payload_type() const {
  return _internal_has_rtx_payload_type();
}
inline void VideoSendConfig::clear_rtx_payload_type() {
  rtx_payload_type_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline int32_t VideoSendConfig::_internal_rtx_payload_type() const {
  return rtx_payload_type_;
}
inline int32_t VideoSendConfig::rtx_payload_type() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.VideoSendConfig.rtx_payload_type)
  return _internal_rtx_payload_type();
}
inline void VideoSendConfig::_internal_set_rtx_payload_type(int32_t value) {
  _has_bits_[0] |= 0x00000002u;
  rtx_payload_type_ = value;
}
inline void VideoSendConfig::set_rtx_payload_type(int32_t value) {
  _internal_set_rtx_payload_type(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.VideoSendConfig.rtx_payload_type)
}

// optional .webrtc.rtclog.EncoderConfig encoder = 5;
inline bool VideoSendConfig::_internal_has_encoder() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || encoder_ != nullptr);
  return value;
}
inline bool VideoSendConfig::has_encoder() const {
  return _internal_has_encoder();
}
inline void VideoSendConfig::clear_encoder() {
  if (encoder_ != nullptr) encoder_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::webrtc::rtclog::EncoderConfig& VideoSendConfig::_internal_encoder() const {
  const ::webrtc::rtclog::EncoderConfig* p = encoder_;
  return p != nullptr ? *p : reinterpret_cast<const ::webrtc::rtclog::EncoderConfig&>(
      ::webrtc::rtclog::_EncoderConfig_default_instance_);
}
inline const ::webrtc::rtclog::EncoderConfig& VideoSendConfig::encoder() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.VideoSendConfig.encoder)
  return _internal_encoder();
}
inline void VideoSendConfig::unsafe_arena_set_allocated_encoder(
    ::webrtc::rtclog::EncoderConfig* encoder) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(encoder_);
  }
  encoder_ = encoder;
  if (encoder) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.rtclog.VideoSendConfig.encoder)
}
inline ::webrtc::rtclog::EncoderConfig* VideoSendConfig::release_encoder() {
  _has_bits_[0] &= ~0x00000001u;
  ::webrtc::rtclog::EncoderConfig* temp = encoder_;
  encoder_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::webrtc::rtclog::EncoderConfig* VideoSendConfig::unsafe_arena_release_encoder() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.VideoSendConfig.encoder)
  _has_bits_[0] &= ~0x00000001u;
  ::webrtc::rtclog::EncoderConfig* temp = encoder_;
  encoder_ = nullptr;
  return temp;
}
inline ::webrtc::rtclog::EncoderConfig* VideoSendConfig::_internal_mutable_encoder() {
  _has_bits_[0] |= 0x00000001u;
  if (encoder_ == nullptr) {
    auto* p = CreateMaybeMessage<::webrtc::rtclog::EncoderConfig>(GetArenaForAllocation());
    encoder_ = p;
  }
  return encoder_;
}
inline ::webrtc::rtclog::EncoderConfig* VideoSendConfig::mutable_encoder() {
  ::webrtc::rtclog::EncoderConfig* _msg = _internal_mutable_encoder();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.VideoSendConfig.encoder)
  return _msg;
}
inline void VideoSendConfig::set_allocated_encoder(::webrtc::rtclog::EncoderConfig* encoder) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete encoder_;
  }
  if (encoder) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(encoder);
    if (message_arena != submessage_arena) {
      encoder = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, encoder, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  encoder_ = encoder;
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.VideoSendConfig.encoder)
}

// -------------------------------------------------------------------

// EncoderConfig

// optional string name = 1;
inline bool EncoderConfig::_internal_has_name() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool EncoderConfig::has_name() const {
  return _internal_has_name();
}
inline void EncoderConfig::clear_name() {
  name_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& EncoderConfig::name() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.EncoderConfig.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void EncoderConfig::set_name(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:webrtc.rtclog.EncoderConfig.name)
}
inline std::string* EncoderConfig::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.EncoderConfig.name)
  return _s;
}
inline const std::string& EncoderConfig::_internal_name() const {
  return name_.Get();
}
inline void EncoderConfig::_internal_set_name(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  name_.Set(value, GetArenaForAllocation());
}
inline std::string* EncoderConfig::_internal_mutable_name() {
  _has_bits_[0] |= 0x00000001u;
  return name_.Mutable(GetArenaForAllocation());
}
inline std::string* EncoderConfig::release_name() {
  // @@protoc_insertion_point(field_release:webrtc.rtclog.EncoderConfig.name)
  if (!_internal_has_name()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = name_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault()) {
    name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void EncoderConfig::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault()) {
    name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:webrtc.rtclog.EncoderConfig.name)
}

// optional int32 payload_type = 2;
inline bool EncoderConfig::_internal_has_payload_type() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool EncoderConfig::has_payload_type() const {
  return _internal_has_payload_type();
}
inline void EncoderConfig::clear_payload_type() {
  payload_type_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline int32_t EncoderConfig::_internal_payload_type() const {
  return payload_type_;
}
inline int32_t EncoderConfig::payload_type() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.EncoderConfig.payload_type)
  return _internal_payload_type();
}
inline void EncoderConfig::_internal_set_payload_type(int32_t value) {
  _has_bits_[0] |= 0x00000002u;
  payload_type_ = value;
}
inline void EncoderConfig::set_payload_type(int32_t value) {
  _internal_set_payload_type(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.EncoderConfig.payload_type)
}

// -------------------------------------------------------------------

// AudioReceiveConfig

// optional uint32 remote_ssrc = 1;
inline bool AudioReceiveConfig::_internal_has_remote_ssrc() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool AudioReceiveConfig::has_remote_ssrc() const {
  return _internal_has_remote_ssrc();
}
inline void AudioReceiveConfig::clear_remote_ssrc() {
  remote_ssrc_ = 0u;
  _has_bits_[0] &= ~0x00000001u;
}
inline uint32_t AudioReceiveConfig::_internal_remote_ssrc() const {
  return remote_ssrc_;
}
inline uint32_t AudioReceiveConfig::remote_ssrc() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.AudioReceiveConfig.remote_ssrc)
  return _internal_remote_ssrc();
}
inline void AudioReceiveConfig::_internal_set_remote_ssrc(uint32_t value) {
  _has_bits_[0] |= 0x00000001u;
  remote_ssrc_ = value;
}
inline void AudioReceiveConfig::set_remote_ssrc(uint32_t value) {
  _internal_set_remote_ssrc(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.AudioReceiveConfig.remote_ssrc)
}

// optional uint32 local_ssrc = 2;
inline bool AudioReceiveConfig::_internal_has_local_ssrc() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool AudioReceiveConfig::has_local_ssrc() const {
  return _internal_has_local_ssrc();
}
inline void AudioReceiveConfig::clear_local_ssrc() {
  local_ssrc_ = 0u;
  _has_bits_[0] &= ~0x00000002u;
}
inline uint32_t AudioReceiveConfig::_internal_local_ssrc() const {
  return local_ssrc_;
}
inline uint32_t AudioReceiveConfig::local_ssrc() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.AudioReceiveConfig.local_ssrc)
  return _internal_local_ssrc();
}
inline void AudioReceiveConfig::_internal_set_local_ssrc(uint32_t value) {
  _has_bits_[0] |= 0x00000002u;
  local_ssrc_ = value;
}
inline void AudioReceiveConfig::set_local_ssrc(uint32_t value) {
  _internal_set_local_ssrc(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.AudioReceiveConfig.local_ssrc)
}

// repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 3;
inline int AudioReceiveConfig::_internal_header_extensions_size() const {
  return header_extensions_.size();
}
inline int AudioReceiveConfig::header_extensions_size() const {
  return _internal_header_extensions_size();
}
inline void AudioReceiveConfig::clear_header_extensions() {
  header_extensions_.Clear();
}
inline ::webrtc::rtclog::RtpHeaderExtension* AudioReceiveConfig::mutable_header_extensions(int index) {
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.AudioReceiveConfig.header_extensions)
  return header_extensions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension >*
AudioReceiveConfig::mutable_header_extensions() {
  // @@protoc_insertion_point(field_mutable_list:webrtc.rtclog.AudioReceiveConfig.header_extensions)
  return &header_extensions_;
}
inline const ::webrtc::rtclog::RtpHeaderExtension& AudioReceiveConfig::_internal_header_extensions(int index) const {
  return header_extensions_.Get(index);
}
inline const ::webrtc::rtclog::RtpHeaderExtension& AudioReceiveConfig::header_extensions(int index) const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.AudioReceiveConfig.header_extensions)
  return _internal_header_extensions(index);
}
inline ::webrtc::rtclog::RtpHeaderExtension* AudioReceiveConfig::_internal_add_header_extensions() {
  return header_extensions_.Add();
}
inline ::webrtc::rtclog::RtpHeaderExtension* AudioReceiveConfig::add_header_extensions() {
  ::webrtc::rtclog::RtpHeaderExtension* _add = _internal_add_header_extensions();
  // @@protoc_insertion_point(field_add:webrtc.rtclog.AudioReceiveConfig.header_extensions)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension >&
AudioReceiveConfig::header_extensions() const {
  // @@protoc_insertion_point(field_list:webrtc.rtclog.AudioReceiveConfig.header_extensions)
  return header_extensions_;
}

// -------------------------------------------------------------------

// AudioSendConfig

// optional uint32 ssrc = 1;
inline bool AudioSendConfig::_internal_has_ssrc() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool AudioSendConfig::has_ssrc() const {
  return _internal_has_ssrc();
}
inline void AudioSendConfig::clear_ssrc() {
  ssrc_ = 0u;
  _has_bits_[0] &= ~0x00000001u;
}
inline uint32_t AudioSendConfig::_internal_ssrc() const {
  return ssrc_;
}
inline uint32_t AudioSendConfig::ssrc() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.AudioSendConfig.ssrc)
  return _internal_ssrc();
}
inline void AudioSendConfig::_internal_set_ssrc(uint32_t value) {
  _has_bits_[0] |= 0x00000001u;
  ssrc_ = value;
}
inline void AudioSendConfig::set_ssrc(uint32_t value) {
  _internal_set_ssrc(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.AudioSendConfig.ssrc)
}

// repeated .webrtc.rtclog.RtpHeaderExtension header_extensions = 2;
inline int AudioSendConfig::_internal_header_extensions_size() const {
  return header_extensions_.size();
}
inline int AudioSendConfig::header_extensions_size() const {
  return _internal_header_extensions_size();
}
inline void AudioSendConfig::clear_header_extensions() {
  header_extensions_.Clear();
}
inline ::webrtc::rtclog::RtpHeaderExtension* AudioSendConfig::mutable_header_extensions(int index) {
  // @@protoc_insertion_point(field_mutable:webrtc.rtclog.AudioSendConfig.header_extensions)
  return header_extensions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension >*
AudioSendConfig::mutable_header_extensions() {
  // @@protoc_insertion_point(field_mutable_list:webrtc.rtclog.AudioSendConfig.header_extensions)
  return &header_extensions_;
}
inline const ::webrtc::rtclog::RtpHeaderExtension& AudioSendConfig::_internal_header_extensions(int index) const {
  return header_extensions_.Get(index);
}
inline const ::webrtc::rtclog::RtpHeaderExtension& AudioSendConfig::header_extensions(int index) const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.AudioSendConfig.header_extensions)
  return _internal_header_extensions(index);
}
inline ::webrtc::rtclog::RtpHeaderExtension* AudioSendConfig::_internal_add_header_extensions() {
  return header_extensions_.Add();
}
inline ::webrtc::rtclog::RtpHeaderExtension* AudioSendConfig::add_header_extensions() {
  ::webrtc::rtclog::RtpHeaderExtension* _add = _internal_add_header_extensions();
  // @@protoc_insertion_point(field_add:webrtc.rtclog.AudioSendConfig.header_extensions)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::webrtc::rtclog::RtpHeaderExtension >&
AudioSendConfig::header_extensions() const {
  // @@protoc_insertion_point(field_list:webrtc.rtclog.AudioSendConfig.header_extensions)
  return header_extensions_;
}

// -------------------------------------------------------------------

// AudioNetworkAdaptation

// optional int32 bitrate_bps = 1;
inline bool AudioNetworkAdaptation::_internal_has_bitrate_bps() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool AudioNetworkAdaptation::has_bitrate_bps() const {
  return _internal_has_bitrate_bps();
}
inline void AudioNetworkAdaptation::clear_bitrate_bps() {
  bitrate_bps_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline int32_t AudioNetworkAdaptation::_internal_bitrate_bps() const {
  return bitrate_bps_;
}
inline int32_t AudioNetworkAdaptation::bitrate_bps() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.AudioNetworkAdaptation.bitrate_bps)
  return _internal_bitrate_bps();
}
inline void AudioNetworkAdaptation::_internal_set_bitrate_bps(int32_t value) {
  _has_bits_[0] |= 0x00000001u;
  bitrate_bps_ = value;
}
inline void AudioNetworkAdaptation::set_bitrate_bps(int32_t value) {
  _internal_set_bitrate_bps(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.AudioNetworkAdaptation.bitrate_bps)
}

// optional int32 frame_length_ms = 2;
inline bool AudioNetworkAdaptation::_internal_has_frame_length_ms() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool AudioNetworkAdaptation::has_frame_length_ms() const {
  return _internal_has_frame_length_ms();
}
inline void AudioNetworkAdaptation::clear_frame_length_ms() {
  frame_length_ms_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline int32_t AudioNetworkAdaptation::_internal_frame_length_ms() const {
  return frame_length_ms_;
}
inline int32_t AudioNetworkAdaptation::frame_length_ms() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.AudioNetworkAdaptation.frame_length_ms)
  return _internal_frame_length_ms();
}
inline void AudioNetworkAdaptation::_internal_set_frame_length_ms(int32_t value) {
  _has_bits_[0] |= 0x00000002u;
  frame_length_ms_ = value;
}
inline void AudioNetworkAdaptation::set_frame_length_ms(int32_t value) {
  _internal_set_frame_length_ms(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.AudioNetworkAdaptation.frame_length_ms)
}

// optional float uplink_packet_loss_fraction = 3;
inline bool AudioNetworkAdaptation::_internal_has_uplink_packet_loss_fraction() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool AudioNetworkAdaptation::has_uplink_packet_loss_fraction() const {
  return _internal_has_uplink_packet_loss_fraction();
}
inline void AudioNetworkAdaptation::clear_uplink_packet_loss_fraction() {
  uplink_packet_loss_fraction_ = 0;
  _has_bits_[0] &= ~0x00000004u;
}
inline float AudioNetworkAdaptation::_internal_uplink_packet_loss_fraction() const {
  return uplink_packet_loss_fraction_;
}
inline float AudioNetworkAdaptation::uplink_packet_loss_fraction() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.AudioNetworkAdaptation.uplink_packet_loss_fraction)
  return _internal_uplink_packet_loss_fraction();
}
inline void AudioNetworkAdaptation::_internal_set_uplink_packet_loss_fraction(float value) {
  _has_bits_[0] |= 0x00000004u;
  uplink_packet_loss_fraction_ = value;
}
inline void AudioNetworkAdaptation::set_uplink_packet_loss_fraction(float value) {
  _internal_set_uplink_packet_loss_fraction(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.AudioNetworkAdaptation.uplink_packet_loss_fraction)
}

// optional bool enable_fec = 4;
inline bool AudioNetworkAdaptation::_internal_has_enable_fec() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool AudioNetworkAdaptation::has_enable_fec() const {
  return _internal_has_enable_fec();
}
inline void AudioNetworkAdaptation::clear_enable_fec() {
  enable_fec_ = false;
  _has_bits_[0] &= ~0x00000008u;
}
inline bool AudioNetworkAdaptation::_internal_enable_fec() const {
  return enable_fec_;
}
inline bool AudioNetworkAdaptation::enable_fec() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.AudioNetworkAdaptation.enable_fec)
  return _internal_enable_fec();
}
inline void AudioNetworkAdaptation::_internal_set_enable_fec(bool value) {
  _has_bits_[0] |= 0x00000008u;
  enable_fec_ = value;
}
inline void AudioNetworkAdaptation::set_enable_fec(bool value) {
  _internal_set_enable_fec(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.AudioNetworkAdaptation.enable_fec)
}

// optional bool enable_dtx = 5;
inline bool AudioNetworkAdaptation::_internal_has_enable_dtx() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool AudioNetworkAdaptation::has_enable_dtx() const {
  return _internal_has_enable_dtx();
}
inline void AudioNetworkAdaptation::clear_enable_dtx() {
  enable_dtx_ = false;
  _has_bits_[0] &= ~0x00000010u;
}
inline bool AudioNetworkAdaptation::_internal_enable_dtx() const {
  return enable_dtx_;
}
inline bool AudioNetworkAdaptation::enable_dtx() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.AudioNetworkAdaptation.enable_dtx)
  return _internal_enable_dtx();
}
inline void AudioNetworkAdaptation::_internal_set_enable_dtx(bool value) {
  _has_bits_[0] |= 0x00000010u;
  enable_dtx_ = value;
}
inline void AudioNetworkAdaptation::set_enable_dtx(bool value) {
  _internal_set_enable_dtx(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.AudioNetworkAdaptation.enable_dtx)
}

// optional uint32 num_channels = 6;
inline bool AudioNetworkAdaptation::_internal_has_num_channels() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool AudioNetworkAdaptation::has_num_channels() const {
  return _internal_has_num_channels();
}
inline void AudioNetworkAdaptation::clear_num_channels() {
  num_channels_ = 0u;
  _has_bits_[0] &= ~0x00000020u;
}
inline uint32_t AudioNetworkAdaptation::_internal_num_channels() const {
  return num_channels_;
}
inline uint32_t AudioNetworkAdaptation::num_channels() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.AudioNetworkAdaptation.num_channels)
  return _internal_num_channels();
}
inline void AudioNetworkAdaptation::_internal_set_num_channels(uint32_t value) {
  _has_bits_[0] |= 0x00000020u;
  num_channels_ = value;
}
inline void AudioNetworkAdaptation::set_num_channels(uint32_t value) {
  _internal_set_num_channels(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.AudioNetworkAdaptation.num_channels)
}

// -------------------------------------------------------------------

// BweProbeCluster

// optional int32 id = 1;
inline bool BweProbeCluster::_internal_has_id() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool BweProbeCluster::has_id() const {
  return _internal_has_id();
}
inline void BweProbeCluster::clear_id() {
  id_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline int32_t BweProbeCluster::_internal_id() const {
  return id_;
}
inline int32_t BweProbeCluster::id() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.BweProbeCluster.id)
  return _internal_id();
}
inline void BweProbeCluster::_internal_set_id(int32_t value) {
  _has_bits_[0] |= 0x00000001u;
  id_ = value;
}
inline void BweProbeCluster::set_id(int32_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.BweProbeCluster.id)
}

// optional int32 bitrate_bps = 2;
inline bool BweProbeCluster::_internal_has_bitrate_bps() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool BweProbeCluster::has_bitrate_bps() const {
  return _internal_has_bitrate_bps();
}
inline void BweProbeCluster::clear_bitrate_bps() {
  bitrate_bps_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline int32_t BweProbeCluster::_internal_bitrate_bps() const {
  return bitrate_bps_;
}
inline int32_t BweProbeCluster::bitrate_bps() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.BweProbeCluster.bitrate_bps)
  return _internal_bitrate_bps();
}
inline void BweProbeCluster::_internal_set_bitrate_bps(int32_t value) {
  _has_bits_[0] |= 0x00000002u;
  bitrate_bps_ = value;
}
inline void BweProbeCluster::set_bitrate_bps(int32_t value) {
  _internal_set_bitrate_bps(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.BweProbeCluster.bitrate_bps)
}

// optional uint32 min_packets = 3;
inline bool BweProbeCluster::_internal_has_min_packets() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool BweProbeCluster::has_min_packets() const {
  return _internal_has_min_packets();
}
inline void BweProbeCluster::clear_min_packets() {
  min_packets_ = 0u;
  _has_bits_[0] &= ~0x00000004u;
}
inline uint32_t BweProbeCluster::_internal_min_packets() const {
  return min_packets_;
}
inline uint32_t BweProbeCluster::min_packets() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.BweProbeCluster.min_packets)
  return _internal_min_packets();
}
inline void BweProbeCluster::_internal_set_min_packets(uint32_t value) {
  _has_bits_[0] |= 0x00000004u;
  min_packets_ = value;
}
inline void BweProbeCluster::set_min_packets(uint32_t value) {
  _internal_set_min_packets(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.BweProbeCluster.min_packets)
}

// optional uint32 min_bytes = 4;
inline bool BweProbeCluster::_internal_has_min_bytes() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool BweProbeCluster::has_min_bytes() const {
  return _internal_has_min_bytes();
}
inline void BweProbeCluster::clear_min_bytes() {
  min_bytes_ = 0u;
  _has_bits_[0] &= ~0x00000008u;
}
inline uint32_t BweProbeCluster::_internal_min_bytes() const {
  return min_bytes_;
}
inline uint32_t BweProbeCluster::min_bytes() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.BweProbeCluster.min_bytes)
  return _internal_min_bytes();
}
inline void BweProbeCluster::_internal_set_min_bytes(uint32_t value) {
  _has_bits_[0] |= 0x00000008u;
  min_bytes_ = value;
}
inline void BweProbeCluster::set_min_bytes(uint32_t value) {
  _internal_set_min_bytes(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.BweProbeCluster.min_bytes)
}

// -------------------------------------------------------------------

// BweProbeResult

// optional int32 id = 1;
inline bool BweProbeResult::_internal_has_id() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool BweProbeResult::has_id() const {
  return _internal_has_id();
}
inline void BweProbeResult::clear_id() {
  id_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline int32_t BweProbeResult::_internal_id() const {
  return id_;
}
inline int32_t BweProbeResult::id() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.BweProbeResult.id)
  return _internal_id();
}
inline void BweProbeResult::_internal_set_id(int32_t value) {
  _has_bits_[0] |= 0x00000001u;
  id_ = value;
}
inline void BweProbeResult::set_id(int32_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.BweProbeResult.id)
}

// optional .webrtc.rtclog.BweProbeResult.ResultType result = 2;
inline bool BweProbeResult::_internal_has_result() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool BweProbeResult::has_result() const {
  return _internal_has_result();
}
inline void BweProbeResult::clear_result() {
  result_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline ::webrtc::rtclog::BweProbeResult_ResultType BweProbeResult::_internal_result() const {
  return static_cast< ::webrtc::rtclog::BweProbeResult_ResultType >(result_);
}
inline ::webrtc::rtclog::BweProbeResult_ResultType BweProbeResult::result() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.BweProbeResult.result)
  return _internal_result();
}
inline void BweProbeResult::_internal_set_result(::webrtc::rtclog::BweProbeResult_ResultType value) {
  assert(::webrtc::rtclog::BweProbeResult_ResultType_IsValid(value));
  _has_bits_[0] |= 0x00000002u;
  result_ = value;
}
inline void BweProbeResult::set_result(::webrtc::rtclog::BweProbeResult_ResultType value) {
  _internal_set_result(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.BweProbeResult.result)
}

// optional int32 bitrate_bps = 3;
inline bool BweProbeResult::_internal_has_bitrate_bps() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool BweProbeResult::has_bitrate_bps() const {
  return _internal_has_bitrate_bps();
}
inline void BweProbeResult::clear_bitrate_bps() {
  bitrate_bps_ = 0;
  _has_bits_[0] &= ~0x00000004u;
}
inline int32_t BweProbeResult::_internal_bitrate_bps() const {
  return bitrate_bps_;
}
inline int32_t BweProbeResult::bitrate_bps() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.BweProbeResult.bitrate_bps)
  return _internal_bitrate_bps();
}
inline void BweProbeResult::_internal_set_bitrate_bps(int32_t value) {
  _has_bits_[0] |= 0x00000004u;
  bitrate_bps_ = value;
}
inline void BweProbeResult::set_bitrate_bps(int32_t value) {
  _internal_set_bitrate_bps(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.BweProbeResult.bitrate_bps)
}

// -------------------------------------------------------------------

// RemoteEstimate

// optional uint32 link_capacity_lower_kbps = 1;
inline bool RemoteEstimate::_internal_has_link_capacity_lower_kbps() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool RemoteEstimate::has_link_capacity_lower_kbps() const {
  return _internal_has_link_capacity_lower_kbps();
}
inline void RemoteEstimate::clear_link_capacity_lower_kbps() {
  link_capacity_lower_kbps_ = 0u;
  _has_bits_[0] &= ~0x00000001u;
}
inline uint32_t RemoteEstimate::_internal_link_capacity_lower_kbps() const {
  return link_capacity_lower_kbps_;
}
inline uint32_t RemoteEstimate::link_capacity_lower_kbps() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.RemoteEstimate.link_capacity_lower_kbps)
  return _internal_link_capacity_lower_kbps();
}
inline void RemoteEstimate::_internal_set_link_capacity_lower_kbps(uint32_t value) {
  _has_bits_[0] |= 0x00000001u;
  link_capacity_lower_kbps_ = value;
}
inline void RemoteEstimate::set_link_capacity_lower_kbps(uint32_t value) {
  _internal_set_link_capacity_lower_kbps(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.RemoteEstimate.link_capacity_lower_kbps)
}

// optional uint32 link_capacity_upper_kbps = 2;
inline bool RemoteEstimate::_internal_has_link_capacity_upper_kbps() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RemoteEstimate::has_link_capacity_upper_kbps() const {
  return _internal_has_link_capacity_upper_kbps();
}
inline void RemoteEstimate::clear_link_capacity_upper_kbps() {
  link_capacity_upper_kbps_ = 0u;
  _has_bits_[0] &= ~0x00000002u;
}
inline uint32_t RemoteEstimate::_internal_link_capacity_upper_kbps() const {
  return link_capacity_upper_kbps_;
}
inline uint32_t RemoteEstimate::link_capacity_upper_kbps() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.RemoteEstimate.link_capacity_upper_kbps)
  return _internal_link_capacity_upper_kbps();
}
inline void RemoteEstimate::_internal_set_link_capacity_upper_kbps(uint32_t value) {
  _has_bits_[0] |= 0x00000002u;
  link_capacity_upper_kbps_ = value;
}
inline void RemoteEstimate::set_link_capacity_upper_kbps(uint32_t value) {
  _internal_set_link_capacity_upper_kbps(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.RemoteEstimate.link_capacity_upper_kbps)
}

// -------------------------------------------------------------------

// AlrState

// optional bool in_alr = 1;
inline bool AlrState::_internal_has_in_alr() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool AlrState::has_in_alr() const {
  return _internal_has_in_alr();
}
inline void AlrState::clear_in_alr() {
  in_alr_ = false;
  _has_bits_[0] &= ~0x00000001u;
}
inline bool AlrState::_internal_in_alr() const {
  return in_alr_;
}
inline bool AlrState::in_alr() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.AlrState.in_alr)
  return _internal_in_alr();
}
inline void AlrState::_internal_set_in_alr(bool value) {
  _has_bits_[0] |= 0x00000001u;
  in_alr_ = value;
}
inline void AlrState::set_in_alr(bool value) {
  _internal_set_in_alr(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.AlrState.in_alr)
}

// -------------------------------------------------------------------

// IceCandidatePairConfig

// optional .webrtc.rtclog.IceCandidatePairConfig.IceCandidatePairConfigType config_type = 1;
inline bool IceCandidatePairConfig::_internal_has_config_type() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool IceCandidatePairConfig::has_config_type() const {
  return _internal_has_config_type();
}
inline void IceCandidatePairConfig::clear_config_type() {
  config_type_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline ::webrtc::rtclog::IceCandidatePairConfig_IceCandidatePairConfigType IceCandidatePairConfig::_internal_config_type() const {
  return static_cast< ::webrtc::rtclog::IceCandidatePairConfig_IceCandidatePairConfigType >(config_type_);
}
inline ::webrtc::rtclog::IceCandidatePairConfig_IceCandidatePairConfigType IceCandidatePairConfig::config_type() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.IceCandidatePairConfig.config_type)
  return _internal_config_type();
}
inline void IceCandidatePairConfig::_internal_set_config_type(::webrtc::rtclog::IceCandidatePairConfig_IceCandidatePairConfigType value) {
  assert(::webrtc::rtclog::IceCandidatePairConfig_IceCandidatePairConfigType_IsValid(value));
  _has_bits_[0] |= 0x00000001u;
  config_type_ = value;
}
inline void IceCandidatePairConfig::set_config_type(::webrtc::rtclog::IceCandidatePairConfig_IceCandidatePairConfigType value) {
  _internal_set_config_type(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.IceCandidatePairConfig.config_type)
}

// optional uint32 candidate_pair_id = 2;
inline bool IceCandidatePairConfig::_internal_has_candidate_pair_id() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool IceCandidatePairConfig::has_candidate_pair_id() const {
  return _internal_has_candidate_pair_id();
}
inline void IceCandidatePairConfig::clear_candidate_pair_id() {
  candidate_pair_id_ = 0u;
  _has_bits_[0] &= ~0x00000002u;
}
inline uint32_t IceCandidatePairConfig::_internal_candidate_pair_id() const {
  return candidate_pair_id_;
}
inline uint32_t IceCandidatePairConfig::candidate_pair_id() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.IceCandidatePairConfig.candidate_pair_id)
  return _internal_candidate_pair_id();
}
inline void IceCandidatePairConfig::_internal_set_candidate_pair_id(uint32_t value) {
  _has_bits_[0] |= 0x00000002u;
  candidate_pair_id_ = value;
}
inline void IceCandidatePairConfig::set_candidate_pair_id(uint32_t value) {
  _internal_set_candidate_pair_id(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.IceCandidatePairConfig.candidate_pair_id)
}

// optional .webrtc.rtclog.IceCandidatePairConfig.IceCandidateType local_candidate_type = 3;
inline bool IceCandidatePairConfig::_internal_has_local_candidate_type() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool IceCandidatePairConfig::has_local_candidate_type() const {
  return _internal_has_local_candidate_type();
}
inline void IceCandidatePairConfig::clear_local_candidate_type() {
  local_candidate_type_ = 0;
  _has_bits_[0] &= ~0x00000004u;
}
inline ::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType IceCandidatePairConfig::_internal_local_candidate_type() const {
  return static_cast< ::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType >(local_candidate_type_);
}
inline ::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType IceCandidatePairConfig::local_candidate_type() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.IceCandidatePairConfig.local_candidate_type)
  return _internal_local_candidate_type();
}
inline void IceCandidatePairConfig::_internal_set_local_candidate_type(::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType value) {
  assert(::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType_IsValid(value));
  _has_bits_[0] |= 0x00000004u;
  local_candidate_type_ = value;
}
inline void IceCandidatePairConfig::set_local_candidate_type(::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType value) {
  _internal_set_local_candidate_type(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.IceCandidatePairConfig.local_candidate_type)
}

// optional .webrtc.rtclog.IceCandidatePairConfig.Protocol local_relay_protocol = 4;
inline bool IceCandidatePairConfig::_internal_has_local_relay_protocol() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool IceCandidatePairConfig::has_local_relay_protocol() const {
  return _internal_has_local_relay_protocol();
}
inline void IceCandidatePairConfig::clear_local_relay_protocol() {
  local_relay_protocol_ = 0;
  _has_bits_[0] &= ~0x00000008u;
}
inline ::webrtc::rtclog::IceCandidatePairConfig_Protocol IceCandidatePairConfig::_internal_local_relay_protocol() const {
  return static_cast< ::webrtc::rtclog::IceCandidatePairConfig_Protocol >(local_relay_protocol_);
}
inline ::webrtc::rtclog::IceCandidatePairConfig_Protocol IceCandidatePairConfig::local_relay_protocol() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.IceCandidatePairConfig.local_relay_protocol)
  return _internal_local_relay_protocol();
}
inline void IceCandidatePairConfig::_internal_set_local_relay_protocol(::webrtc::rtclog::IceCandidatePairConfig_Protocol value) {
  assert(::webrtc::rtclog::IceCandidatePairConfig_Protocol_IsValid(value));
  _has_bits_[0] |= 0x00000008u;
  local_relay_protocol_ = value;
}
inline void IceCandidatePairConfig::set_local_relay_protocol(::webrtc::rtclog::IceCandidatePairConfig_Protocol value) {
  _internal_set_local_relay_protocol(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.IceCandidatePairConfig.local_relay_protocol)
}

// optional .webrtc.rtclog.IceCandidatePairConfig.NetworkType local_network_type = 5;
inline bool IceCandidatePairConfig::_internal_has_local_network_type() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool IceCandidatePairConfig::has_local_network_type() const {
  return _internal_has_local_network_type();
}
inline void IceCandidatePairConfig::clear_local_network_type() {
  local_network_type_ = 0;
  _has_bits_[0] &= ~0x00000010u;
}
inline ::webrtc::rtclog::IceCandidatePairConfig_NetworkType IceCandidatePairConfig::_internal_local_network_type() const {
  return static_cast< ::webrtc::rtclog::IceCandidatePairConfig_NetworkType >(local_network_type_);
}
inline ::webrtc::rtclog::IceCandidatePairConfig_NetworkType IceCandidatePairConfig::local_network_type() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.IceCandidatePairConfig.local_network_type)
  return _internal_local_network_type();
}
inline void IceCandidatePairConfig::_internal_set_local_network_type(::webrtc::rtclog::IceCandidatePairConfig_NetworkType value) {
  assert(::webrtc::rtclog::IceCandidatePairConfig_NetworkType_IsValid(value));
  _has_bits_[0] |= 0x00000010u;
  local_network_type_ = value;
}
inline void IceCandidatePairConfig::set_local_network_type(::webrtc::rtclog::IceCandidatePairConfig_NetworkType value) {
  _internal_set_local_network_type(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.IceCandidatePairConfig.local_network_type)
}

// optional .webrtc.rtclog.IceCandidatePairConfig.AddressFamily local_address_family = 6;
inline bool IceCandidatePairConfig::_internal_has_local_address_family() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool IceCandidatePairConfig::has_local_address_family() const {
  return _internal_has_local_address_family();
}
inline void IceCandidatePairConfig::clear_local_address_family() {
  local_address_family_ = 0;
  _has_bits_[0] &= ~0x00000020u;
}
inline ::webrtc::rtclog::IceCandidatePairConfig_AddressFamily IceCandidatePairConfig::_internal_local_address_family() const {
  return static_cast< ::webrtc::rtclog::IceCandidatePairConfig_AddressFamily >(local_address_family_);
}
inline ::webrtc::rtclog::IceCandidatePairConfig_AddressFamily IceCandidatePairConfig::local_address_family() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.IceCandidatePairConfig.local_address_family)
  return _internal_local_address_family();
}
inline void IceCandidatePairConfig::_internal_set_local_address_family(::webrtc::rtclog::IceCandidatePairConfig_AddressFamily value) {
  assert(::webrtc::rtclog::IceCandidatePairConfig_AddressFamily_IsValid(value));
  _has_bits_[0] |= 0x00000020u;
  local_address_family_ = value;
}
inline void IceCandidatePairConfig::set_local_address_family(::webrtc::rtclog::IceCandidatePairConfig_AddressFamily value) {
  _internal_set_local_address_family(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.IceCandidatePairConfig.local_address_family)
}

// optional .webrtc.rtclog.IceCandidatePairConfig.IceCandidateType remote_candidate_type = 7;
inline bool IceCandidatePairConfig::_internal_has_remote_candidate_type() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool IceCandidatePairConfig::has_remote_candidate_type() const {
  return _internal_has_remote_candidate_type();
}
inline void IceCandidatePairConfig::clear_remote_candidate_type() {
  remote_candidate_type_ = 0;
  _has_bits_[0] &= ~0x00000040u;
}
inline ::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType IceCandidatePairConfig::_internal_remote_candidate_type() const {
  return static_cast< ::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType >(remote_candidate_type_);
}
inline ::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType IceCandidatePairConfig::remote_candidate_type() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.IceCandidatePairConfig.remote_candidate_type)
  return _internal_remote_candidate_type();
}
inline void IceCandidatePairConfig::_internal_set_remote_candidate_type(::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType value) {
  assert(::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType_IsValid(value));
  _has_bits_[0] |= 0x00000040u;
  remote_candidate_type_ = value;
}
inline void IceCandidatePairConfig::set_remote_candidate_type(::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType value) {
  _internal_set_remote_candidate_type(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.IceCandidatePairConfig.remote_candidate_type)
}

// optional .webrtc.rtclog.IceCandidatePairConfig.AddressFamily remote_address_family = 8;
inline bool IceCandidatePairConfig::_internal_has_remote_address_family() const {
  bool value = (_has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool IceCandidatePairConfig::has_remote_address_family() const {
  return _internal_has_remote_address_family();
}
inline void IceCandidatePairConfig::clear_remote_address_family() {
  remote_address_family_ = 0;
  _has_bits_[0] &= ~0x00000080u;
}
inline ::webrtc::rtclog::IceCandidatePairConfig_AddressFamily IceCandidatePairConfig::_internal_remote_address_family() const {
  return static_cast< ::webrtc::rtclog::IceCandidatePairConfig_AddressFamily >(remote_address_family_);
}
inline ::webrtc::rtclog::IceCandidatePairConfig_AddressFamily IceCandidatePairConfig::remote_address_family() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.IceCandidatePairConfig.remote_address_family)
  return _internal_remote_address_family();
}
inline void IceCandidatePairConfig::_internal_set_remote_address_family(::webrtc::rtclog::IceCandidatePairConfig_AddressFamily value) {
  assert(::webrtc::rtclog::IceCandidatePairConfig_AddressFamily_IsValid(value));
  _has_bits_[0] |= 0x00000080u;
  remote_address_family_ = value;
}
inline void IceCandidatePairConfig::set_remote_address_family(::webrtc::rtclog::IceCandidatePairConfig_AddressFamily value) {
  _internal_set_remote_address_family(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.IceCandidatePairConfig.remote_address_family)
}

// optional .webrtc.rtclog.IceCandidatePairConfig.Protocol candidate_pair_protocol = 9;
inline bool IceCandidatePairConfig::_internal_has_candidate_pair_protocol() const {
  bool value = (_has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool IceCandidatePairConfig::has_candidate_pair_protocol() const {
  return _internal_has_candidate_pair_protocol();
}
inline void IceCandidatePairConfig::clear_candidate_pair_protocol() {
  candidate_pair_protocol_ = 0;
  _has_bits_[0] &= ~0x00000100u;
}
inline ::webrtc::rtclog::IceCandidatePairConfig_Protocol IceCandidatePairConfig::_internal_candidate_pair_protocol() const {
  return static_cast< ::webrtc::rtclog::IceCandidatePairConfig_Protocol >(candidate_pair_protocol_);
}
inline ::webrtc::rtclog::IceCandidatePairConfig_Protocol IceCandidatePairConfig::candidate_pair_protocol() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.IceCandidatePairConfig.candidate_pair_protocol)
  return _internal_candidate_pair_protocol();
}
inline void IceCandidatePairConfig::_internal_set_candidate_pair_protocol(::webrtc::rtclog::IceCandidatePairConfig_Protocol value) {
  assert(::webrtc::rtclog::IceCandidatePairConfig_Protocol_IsValid(value));
  _has_bits_[0] |= 0x00000100u;
  candidate_pair_protocol_ = value;
}
inline void IceCandidatePairConfig::set_candidate_pair_protocol(::webrtc::rtclog::IceCandidatePairConfig_Protocol value) {
  _internal_set_candidate_pair_protocol(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.IceCandidatePairConfig.candidate_pair_protocol)
}

// -------------------------------------------------------------------

// IceCandidatePairEvent

// optional .webrtc.rtclog.IceCandidatePairEvent.IceCandidatePairEventType event_type = 1;
inline bool IceCandidatePairEvent::_internal_has_event_type() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool IceCandidatePairEvent::has_event_type() const {
  return _internal_has_event_type();
}
inline void IceCandidatePairEvent::clear_event_type() {
  event_type_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline ::webrtc::rtclog::IceCandidatePairEvent_IceCandidatePairEventType IceCandidatePairEvent::_internal_event_type() const {
  return static_cast< ::webrtc::rtclog::IceCandidatePairEvent_IceCandidatePairEventType >(event_type_);
}
inline ::webrtc::rtclog::IceCandidatePairEvent_IceCandidatePairEventType IceCandidatePairEvent::event_type() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.IceCandidatePairEvent.event_type)
  return _internal_event_type();
}
inline void IceCandidatePairEvent::_internal_set_event_type(::webrtc::rtclog::IceCandidatePairEvent_IceCandidatePairEventType value) {
  assert(::webrtc::rtclog::IceCandidatePairEvent_IceCandidatePairEventType_IsValid(value));
  _has_bits_[0] |= 0x00000001u;
  event_type_ = value;
}
inline void IceCandidatePairEvent::set_event_type(::webrtc::rtclog::IceCandidatePairEvent_IceCandidatePairEventType value) {
  _internal_set_event_type(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.IceCandidatePairEvent.event_type)
}

// optional uint32 candidate_pair_id = 2;
inline bool IceCandidatePairEvent::_internal_has_candidate_pair_id() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool IceCandidatePairEvent::has_candidate_pair_id() const {
  return _internal_has_candidate_pair_id();
}
inline void IceCandidatePairEvent::clear_candidate_pair_id() {
  candidate_pair_id_ = 0u;
  _has_bits_[0] &= ~0x00000002u;
}
inline uint32_t IceCandidatePairEvent::_internal_candidate_pair_id() const {
  return candidate_pair_id_;
}
inline uint32_t IceCandidatePairEvent::candidate_pair_id() const {
  // @@protoc_insertion_point(field_get:webrtc.rtclog.IceCandidatePairEvent.candidate_pair_id)
  return _internal_candidate_pair_id();
}
inline void IceCandidatePairEvent::_internal_set_candidate_pair_id(uint32_t value) {
  _has_bits_[0] |= 0x00000002u;
  candidate_pair_id_ = value;
}
inline void IceCandidatePairEvent::set_candidate_pair_id(uint32_t value) {
  _internal_set_candidate_pair_id(value);
  // @@protoc_insertion_point(field_set:webrtc.rtclog.IceCandidatePairEvent.candidate_pair_id)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace rtclog
}  // namespace webrtc

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::webrtc::rtclog::Event_EventType> : ::std::true_type {};
template <> struct is_proto_enum< ::webrtc::rtclog::DelayBasedBweUpdate_DetectorState> : ::std::true_type {};
template <> struct is_proto_enum< ::webrtc::rtclog::VideoReceiveConfig_RtcpMode> : ::std::true_type {};
template <> struct is_proto_enum< ::webrtc::rtclog::BweProbeResult_ResultType> : ::std::true_type {};
template <> struct is_proto_enum< ::webrtc::rtclog::IceCandidatePairConfig_IceCandidatePairConfigType> : ::std::true_type {};
template <> struct is_proto_enum< ::webrtc::rtclog::IceCandidatePairConfig_IceCandidateType> : ::std::true_type {};
template <> struct is_proto_enum< ::webrtc::rtclog::IceCandidatePairConfig_Protocol> : ::std::true_type {};
template <> struct is_proto_enum< ::webrtc::rtclog::IceCandidatePairConfig_AddressFamily> : ::std::true_type {};
template <> struct is_proto_enum< ::webrtc::rtclog::IceCandidatePairConfig_NetworkType> : ::std::true_type {};
template <> struct is_proto_enum< ::webrtc::rtclog::IceCandidatePairEvent_IceCandidatePairEventType> : ::std::true_type {};
template <> struct is_proto_enum< ::webrtc::rtclog::MediaType> : ::std::true_type {};

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_rtc_5fevent_5flog_2eproto
