/*
 * Copyright (C) 2017 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.google.android.exoplayer2.source.hls;

import com.google.android.exoplayer2.C;
import com.google.android.exoplayer2.upstream.DataSource;

/** Default implementation of {@link HlsDataSourceFactory}. */
public final class DefaultHlsDataSourceFactory implements HlsDataSourceFactory {

  private final DataSource.Factory dataSourceFactory;

  /**
   * @param dataSourceFactory The {@link DataSource.Factory} to use for all data types.
   */
  public DefaultHlsDataSourceFactory(DataSource.Factory dataSourceFactory) {
    this.dataSourceFactory = dataSourceFactory;
  }

  @Override
  public DataSource createDataSource(@C.DataType int dataType) {
    return dataSourceFactory.createDataSource();
  }
}
