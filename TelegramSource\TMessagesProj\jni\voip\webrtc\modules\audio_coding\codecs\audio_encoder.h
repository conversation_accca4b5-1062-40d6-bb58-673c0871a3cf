/*
 *  Copyright (c) 2012 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

// This file is for backwards compatibility only! Use
// webrtc/api/audio_codecs/audio_encoder.h instead!
// TODO(ossu): Remove it.

#ifndef MODULES_AUDIO_CODING_CODECS_AUDIO_ENCODER_H_
#define MODULES_AUDIO_CODING_CODECS_AUDIO_ENCODER_H_

#include "api/audio_codecs/audio_encoder.h"

#endif  // MODULES_AUDIO_CODING_CODECS_AUDIO_ENCODER_H_
