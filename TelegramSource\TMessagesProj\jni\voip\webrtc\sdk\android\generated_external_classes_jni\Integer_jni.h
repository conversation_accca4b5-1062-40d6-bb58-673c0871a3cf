// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     java/lang/Integer

#ifndef java_lang_Integer_JNI
#define java_lang_Integer_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_java_lang_Integer[];
const char kClassPath_java_lang_Integer[] = "java/lang/Integer";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_java_lang_Integer_clazz(nullptr);
#ifndef java_lang_Integer_clazz_defined
#define java_lang_Integer_clazz_defined
inline jclass java_lang_Integer_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_java_lang_Integer, &g_java_lang_Integer_clazz);
}
#endif


// Step 2: Constants (optional).

namespace JNI_Integer {

enum Java_Integer_constant_fields {
  BYTES = 4,
  MAX_VALUE = 2147483647,
  SIZE = 32,
};


}  // namespace JNI_Integer
// Step 3: Method stubs.
namespace JNI_Integer {


static std::atomic<jmethodID> g_java_lang_Integer_Constructor__String1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_Integer_Constructor__String(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Integer_Constructor__String(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Ljava/lang/String;)V",
          &g_java_lang_Integer_Constructor__String1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Integer_Constructor__int1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Integer_Constructor__int(JNIEnv*
    env, JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Integer_Constructor__int(JNIEnv* env,
    JniIntWrapper p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(I)V",
          &g_java_lang_Integer_Constructor__int1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, as_jint(p0));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Integer_bitCount1(nullptr);
[[maybe_unused]] static jint Java_Integer_bitCount(JNIEnv* env, JniIntWrapper p0);
static jint Java_Integer_bitCount(JNIEnv* env, JniIntWrapper p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "bitCount",
          "(I)I",
          &g_java_lang_Integer_bitCount1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_byteValue0(nullptr);
[[maybe_unused]] static jbyte Java_Integer_byteValue(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jbyte Java_Integer_byteValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "byteValue",
          "()B",
          &g_java_lang_Integer_byteValue0);

  jbyte ret =
      env->CallByteMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_compare2(nullptr);
[[maybe_unused]] static jint Java_Integer_compare(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1);
static jint Java_Integer_compare(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "compare",
          "(II)I",
          &g_java_lang_Integer_compare2);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_compareTo__Integer1(nullptr);
[[maybe_unused]] static jint Java_Integer_compareTo__Integer(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jint Java_Integer_compareTo__Integer(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "compareTo",
          "(Ljava/lang/Integer;)I",
          &g_java_lang_Integer_compareTo__Integer1);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_compareTo__Object1(nullptr);
[[maybe_unused]] static jint Java_Integer_compareTo__Object(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jint Java_Integer_compareTo__Object(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "compareTo",
          "(Ljava/lang/Object;)I",
          &g_java_lang_Integer_compareTo__Object1);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_compareUnsigned2(nullptr);
[[maybe_unused]] static jint Java_Integer_compareUnsigned(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1);
static jint Java_Integer_compareUnsigned(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "compareUnsigned",
          "(II)I",
          &g_java_lang_Integer_compareUnsigned2);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_decode1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Integer_decode(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Integer_decode(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "decode",
          "(Ljava/lang/String;)Ljava/lang/Integer;",
          &g_java_lang_Integer_decode1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Integer_divideUnsigned2(nullptr);
[[maybe_unused]] static jint Java_Integer_divideUnsigned(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1);
static jint Java_Integer_divideUnsigned(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "divideUnsigned",
          "(II)I",
          &g_java_lang_Integer_divideUnsigned2);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_doubleValue0(nullptr);
[[maybe_unused]] static jdouble Java_Integer_doubleValue(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jdouble Java_Integer_doubleValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "doubleValue",
          "()D",
          &g_java_lang_Integer_doubleValue0);

  jdouble ret =
      env->CallDoubleMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_equals1(nullptr);
[[maybe_unused]] static jboolean Java_Integer_equals(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_Integer_equals(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Integer_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "equals",
          "(Ljava/lang/Object;)Z",
          &g_java_lang_Integer_equals1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_floatValue0(nullptr);
[[maybe_unused]] static jfloat Java_Integer_floatValue(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jfloat Java_Integer_floatValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "floatValue",
          "()F",
          &g_java_lang_Integer_floatValue0);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_getInteger1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Integer_getInteger(JNIEnv* env,
    const jni_zero::JavaRef<jstring>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Integer_getInteger(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getInteger",
          "(Ljava/lang/String;)Ljava/lang/Integer;",
          &g_java_lang_Integer_getInteger1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Integer_getInteger__String__Integer2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_Integer_getInteger__String__Integer(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0,
    const jni_zero::JavaRef<jobject>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Integer_getInteger__String__Integer(JNIEnv* env,
    const jni_zero::JavaRef<jstring>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getInteger",
          "(Ljava/lang/String;Ljava/lang/Integer;)Ljava/lang/Integer;",
          &g_java_lang_Integer_getInteger__String__Integer2);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Integer_getInteger__String__int2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_Integer_getInteger__String__int(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Integer_getInteger__String__int(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getInteger",
          "(Ljava/lang/String;I)Ljava/lang/Integer;",
          &g_java_lang_Integer_getInteger__String__int2);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), as_jint(p1));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Integer_hashCode0(nullptr);
[[maybe_unused]] static jint Java_Integer_hashCode(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jint Java_Integer_hashCode(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "hashCode",
          "()I",
          &g_java_lang_Integer_hashCode0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_hashCode1(nullptr);
[[maybe_unused]] static jint Java_Integer_hashCode(JNIEnv* env, JniIntWrapper p0);
static jint Java_Integer_hashCode(JNIEnv* env, JniIntWrapper p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "hashCode",
          "(I)I",
          &g_java_lang_Integer_hashCode1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_highestOneBit1(nullptr);
[[maybe_unused]] static jint Java_Integer_highestOneBit(JNIEnv* env, JniIntWrapper p0);
static jint Java_Integer_highestOneBit(JNIEnv* env, JniIntWrapper p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "highestOneBit",
          "(I)I",
          &g_java_lang_Integer_highestOneBit1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_intValue0(nullptr);
[[maybe_unused]] static jint Java_Integer_intValue(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jint Java_Integer_intValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "intValue",
          "()I",
          &g_java_lang_Integer_intValue0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_longValue0(nullptr);
[[maybe_unused]] static jlong Java_Integer_longValue(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jlong Java_Integer_longValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "longValue",
          "()J",
          &g_java_lang_Integer_longValue0);

  jlong ret =
      env->CallLongMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_lowestOneBit1(nullptr);
[[maybe_unused]] static jint Java_Integer_lowestOneBit(JNIEnv* env, JniIntWrapper p0);
static jint Java_Integer_lowestOneBit(JNIEnv* env, JniIntWrapper p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "lowestOneBit",
          "(I)I",
          &g_java_lang_Integer_lowestOneBit1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_max2(nullptr);
[[maybe_unused]] static jint Java_Integer_max(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1);
static jint Java_Integer_max(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "max",
          "(II)I",
          &g_java_lang_Integer_max2);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_min2(nullptr);
[[maybe_unused]] static jint Java_Integer_min(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1);
static jint Java_Integer_min(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "min",
          "(II)I",
          &g_java_lang_Integer_min2);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_numberOfLeadingZeros1(nullptr);
[[maybe_unused]] static jint Java_Integer_numberOfLeadingZeros(JNIEnv* env, JniIntWrapper p0);
static jint Java_Integer_numberOfLeadingZeros(JNIEnv* env, JniIntWrapper p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "numberOfLeadingZeros",
          "(I)I",
          &g_java_lang_Integer_numberOfLeadingZeros1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_numberOfTrailingZeros1(nullptr);
[[maybe_unused]] static jint Java_Integer_numberOfTrailingZeros(JNIEnv* env, JniIntWrapper p0);
static jint Java_Integer_numberOfTrailingZeros(JNIEnv* env, JniIntWrapper p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "numberOfTrailingZeros",
          "(I)I",
          &g_java_lang_Integer_numberOfTrailingZeros1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_parseInt4(nullptr);
[[maybe_unused]] static jint Java_Integer_parseInt(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    JniIntWrapper p3);
static jint Java_Integer_parseInt(JNIEnv* env, const jni_zero::JavaRef<jobject>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    JniIntWrapper p3) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "parseInt",
          "(Ljava/lang/CharSequence;III)I",
          &g_java_lang_Integer_parseInt4);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0.obj(), as_jint(p1), as_jint(p2), as_jint(p3));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_parseInt1(nullptr);
[[maybe_unused]] static jint Java_Integer_parseInt(JNIEnv* env, const jni_zero::JavaRef<jstring>&
    p0);
static jint Java_Integer_parseInt(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "parseInt",
          "(Ljava/lang/String;)I",
          &g_java_lang_Integer_parseInt1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_parseInt2(nullptr);
[[maybe_unused]] static jint Java_Integer_parseInt(JNIEnv* env, const jni_zero::JavaRef<jstring>&
    p0,
    JniIntWrapper p1);
static jint Java_Integer_parseInt(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "parseInt",
          "(Ljava/lang/String;I)I",
          &g_java_lang_Integer_parseInt2);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0.obj(), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_parseUnsignedInt4(nullptr);
[[maybe_unused]] static jint Java_Integer_parseUnsignedInt(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    JniIntWrapper p3);
static jint Java_Integer_parseUnsignedInt(JNIEnv* env, const jni_zero::JavaRef<jobject>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    JniIntWrapper p3) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "parseUnsignedInt",
          "(Ljava/lang/CharSequence;III)I",
          &g_java_lang_Integer_parseUnsignedInt4);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0.obj(), as_jint(p1), as_jint(p2), as_jint(p3));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_parseUnsignedInt1(nullptr);
[[maybe_unused]] static jint Java_Integer_parseUnsignedInt(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0);
static jint Java_Integer_parseUnsignedInt(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "parseUnsignedInt",
          "(Ljava/lang/String;)I",
          &g_java_lang_Integer_parseUnsignedInt1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_parseUnsignedInt2(nullptr);
[[maybe_unused]] static jint Java_Integer_parseUnsignedInt(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1);
static jint Java_Integer_parseUnsignedInt(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "parseUnsignedInt",
          "(Ljava/lang/String;I)I",
          &g_java_lang_Integer_parseUnsignedInt2);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0.obj(), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_remainderUnsigned2(nullptr);
[[maybe_unused]] static jint Java_Integer_remainderUnsigned(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1);
static jint Java_Integer_remainderUnsigned(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "remainderUnsigned",
          "(II)I",
          &g_java_lang_Integer_remainderUnsigned2);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_reverse1(nullptr);
[[maybe_unused]] static jint Java_Integer_reverse(JNIEnv* env, JniIntWrapper p0);
static jint Java_Integer_reverse(JNIEnv* env, JniIntWrapper p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "reverse",
          "(I)I",
          &g_java_lang_Integer_reverse1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_reverseBytes1(nullptr);
[[maybe_unused]] static jint Java_Integer_reverseBytes(JNIEnv* env, JniIntWrapper p0);
static jint Java_Integer_reverseBytes(JNIEnv* env, JniIntWrapper p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "reverseBytes",
          "(I)I",
          &g_java_lang_Integer_reverseBytes1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_rotateLeft2(nullptr);
[[maybe_unused]] static jint Java_Integer_rotateLeft(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1);
static jint Java_Integer_rotateLeft(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "rotateLeft",
          "(II)I",
          &g_java_lang_Integer_rotateLeft2);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_rotateRight2(nullptr);
[[maybe_unused]] static jint Java_Integer_rotateRight(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1);
static jint Java_Integer_rotateRight(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "rotateRight",
          "(II)I",
          &g_java_lang_Integer_rotateRight2);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_shortValue0(nullptr);
[[maybe_unused]] static jshort Java_Integer_shortValue(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jshort Java_Integer_shortValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "shortValue",
          "()S",
          &g_java_lang_Integer_shortValue0);

  jshort ret =
      env->CallShortMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_signum1(nullptr);
[[maybe_unused]] static jint Java_Integer_signum(JNIEnv* env, JniIntWrapper p0);
static jint Java_Integer_signum(JNIEnv* env, JniIntWrapper p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "signum",
          "(I)I",
          &g_java_lang_Integer_signum1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_sum2(nullptr);
[[maybe_unused]] static jint Java_Integer_sum(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1);
static jint Java_Integer_sum(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "sum",
          "(II)I",
          &g_java_lang_Integer_sum2);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, as_jint(p0), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_toBinaryString1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Integer_toBinaryString(JNIEnv*
    env, JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Integer_toBinaryString(JNIEnv* env, JniIntWrapper
    p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toBinaryString",
          "(I)Ljava/lang/String;",
          &g_java_lang_Integer_toBinaryString1);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(p0)));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Integer_toHexString1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Integer_toHexString(JNIEnv* env,
    JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Integer_toHexString(JNIEnv* env, JniIntWrapper p0)
    {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toHexString",
          "(I)Ljava/lang/String;",
          &g_java_lang_Integer_toHexString1);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(p0)));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Integer_toOctalString1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Integer_toOctalString(JNIEnv*
    env, JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Integer_toOctalString(JNIEnv* env, JniIntWrapper
    p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toOctalString",
          "(I)Ljava/lang/String;",
          &g_java_lang_Integer_toOctalString1);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(p0)));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Integer_toString0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Integer_toString(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Integer_toString(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "toString",
          "()Ljava/lang/String;",
          &g_java_lang_Integer_toString0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Integer_toString1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Integer_toString(JNIEnv* env,
    JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Integer_toString(JNIEnv* env, JniIntWrapper p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toString",
          "(I)Ljava/lang/String;",
          &g_java_lang_Integer_toString1);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(p0)));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Integer_toString2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Integer_toString(JNIEnv* env,
    JniIntWrapper p0,
    JniIntWrapper p1);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Integer_toString(JNIEnv* env, JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toString",
          "(II)Ljava/lang/String;",
          &g_java_lang_Integer_toString2);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(p0), as_jint(p1)));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Integer_toUnsignedLong1(nullptr);
[[maybe_unused]] static jlong Java_Integer_toUnsignedLong(JNIEnv* env, JniIntWrapper p0);
static jlong Java_Integer_toUnsignedLong(JNIEnv* env, JniIntWrapper p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toUnsignedLong",
          "(I)J",
          &g_java_lang_Integer_toUnsignedLong1);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, as_jint(p0));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Integer_toUnsignedString1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Integer_toUnsignedString(JNIEnv*
    env, JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Integer_toUnsignedString(JNIEnv* env,
    JniIntWrapper p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toUnsignedString",
          "(I)Ljava/lang/String;",
          &g_java_lang_Integer_toUnsignedString1);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(p0)));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Integer_toUnsignedString2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Integer_toUnsignedString(JNIEnv*
    env, JniIntWrapper p0,
    JniIntWrapper p1);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Integer_toUnsignedString(JNIEnv* env,
    JniIntWrapper p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toUnsignedString",
          "(II)Ljava/lang/String;",
          &g_java_lang_Integer_toUnsignedString2);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(p0), as_jint(p1)));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Integer_valueOf__int1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Integer_valueOf__int(JNIEnv* env,
    JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Integer_valueOf__int(JNIEnv* env, JniIntWrapper
    p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "valueOf",
          "(I)Ljava/lang/Integer;",
          &g_java_lang_Integer_valueOf__int1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(p0));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Integer_valueOf__String1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Integer_valueOf__String(JNIEnv*
    env, const jni_zero::JavaRef<jstring>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Integer_valueOf__String(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "valueOf",
          "(Ljava/lang/String;)Ljava/lang/Integer;",
          &g_java_lang_Integer_valueOf__String1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Integer_valueOf2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Integer_valueOf(JNIEnv* env,
    const jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Integer_valueOf(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Integer_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Integer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "valueOf",
          "(Ljava/lang/String;I)Ljava/lang/Integer;",
          &g_java_lang_Integer_valueOf2);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), as_jint(p1));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace JNI_Integer

#endif  // java_lang_Integer_JNI
