/*
 *  Copyright (c) 2010 The WebM project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#include "blockd.h"
#include "vpx_mem/vpx_mem.h"

const unsigned char vp8_block2left[25] = { 0, 0, 0, 0, 1, 1, 1, 1, 2,
                                           2, 2, 2, 3, 3, 3, 3, 4, 4,
                                           5, 5, 6, 6, 7, 7, 8 };
const unsigned char vp8_block2above[25] = { 0, 1, 2, 3, 0, 1, 2, 3, 0,
                                            1, 2, 3, 0, 1, 2, 3, 4, 5,
                                            4, 5, 6, 7, 6, 7, 8 };
