/* FLAC - Free Lossless Audio Codec
 * Copyright (C) 2001-2009  <PERSON>
 * Copyright (C) 2011-2014  Xiph.Org Foundation
 *
 * This file is part the FLAC project.  FLAC is comprised of several
 * components distributed under different licenses.  The codec libraries
 * are distributed under Xiph.Org's BSD-like license (see the file
 * COPYING.Xiph in this distribution).  All other programs, libraries, and
 * plugins are distributed under the GPL (see COPYING.GPL).  The documentation
 * is distributed under the Gnu FDL (see COPYING.FDL).  Each file in the
 * FLAC distribution contains at the top the terms under which it may be
 * distributed.
 *
 * Since this particular file is relevant to all components of FLAC,
 * it may be distributed under the Xiph.Org license, which is the least
 * restrictive of those mentioned above.  See the file COPYING.Xiph in this
 * distribution.
 */

Current FLAC maintainer: <PERSON> <erik<PERSON>@mega-nerd.com>

Original author: <PERSON> <j<PERSON>@users.sourceforge.net>

Website : https://www.xiph.org/flac/

FLAC is an Open Source lossless audio codec originally developed by <PERSON>
between 2001 and 2009. From 2009 to 2012 FLAC was basically unmaintained. In
2012 the <PERSON> became the chief maintainer as part of the
Xiph.Org Foundation.

Other major contributors and their contributions:

"lvqcl" <<EMAIL>>
* Visual Studio build system.
* Optimisations in the encoder and decoder.

"Janne Hyvärinen" <<EMAIL>>
* Visual Studio build system.
* Unicode handling on Windows.

"Andrey Astafiev" <<EMAIL>>
* Russian translation of the HTML documentation

"Miroslav Lichvar" <<EMAIL>>
* IA-32 assembly versions of several libFLAC routines

"Brady Patterson" <<EMAIL>>
* AIFF file support, PPC assembly versions of libFLAC routines

"Daisuke Shimamura" <<EMAIL>>
* i18n support in the XMMS plugin

"X-Fixer" <<EMAIL>>
* Configuration system, tag editing, and file info in the Winamp2 plugin

"Matt Zimmerman" <<EMAIL>>
* Libtool/autoconf/automake make system, flac man page

