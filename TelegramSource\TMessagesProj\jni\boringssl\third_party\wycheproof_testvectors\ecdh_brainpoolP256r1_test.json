{"algorithm": "ECDH", "generatorVersion": "0.8r12", "numberOfTests": 522, "header": ["Test vectors of type EcdhTest are intended for", "testing an ECDH implementations using X509 encoded", "public keys and integers for private keys.", "Test vectors of this format are useful for testing", "Java providers."], "notes": {"AddSubChain": "The private key has a special value. Implementations using addition subtraction chains for the point multiplication may get the point at infinity as an intermediate result. See CVE_2017_10176", "CompressedPoint": "The point in the public key is compressed. Not every library supports points in compressed format.", "GroupIsomorphism": "Some EC groups have isomorphic groups that allow an efficient implementation. This is a test vector that contains values that are edge cases on such an isomorphic group.", "InvalidAsn": "The public key in this test uses an invalid ASN encoding. Some cases where the ASN parser is not strictly checking the ASN format are benign as long as the ECDH computation still returns the correct shared value.", "InvalidPublic": "The public key has been modified and is invalid. An implementation should always check whether the public key is valid and on the same curve as the private key. The test vector includes the shared secret computed with the original public key if the public point is on the curve of the private key. Generating a shared secret other than the one with the original key likely indicates that the bug is exploitable.", "IsomorphicPublicKey": "The public key in this test vector uses an isomorphic curve. Such isomorphisms are sometimes used to speed up implementations. For example the brainpool curves are using this.", "ModifiedPrime": "The modulus of the public key has been modified. The public point of the public key has been chosen so that it is both a point on both the curve of the modified public key and the private key.", "UnnamedCurve": "The public key does not use a named curve. RFC 3279 allows to encode such curves by explicitly encoding, the parameters of the curve equation, modulus, generator, order and cofactor. However, many crypto libraries only support named curves. Modifying some of the EC parameters and encoding the corresponding public key as an unnamed curve is a potential attack vector.", "UnusedParam": "A parameter that is typically not used for ECDH has been modified. Sometimes libraries ignore small differences between public and private key. For example, a library might ignore an incorrect cofactor in the public key. We consider ignoring such changes as acceptable as long as these differences do not change the outcome of the ECDH computation, i.e. as long as the computation is done on the curve from the private key.", "WeakPublicKey": "The vector contains a weak public key. The curve is not a named curve, the public key point has order 3 and has been chosen to be on the same curve as the private key. This test vector is used to check ECC implementations for missing steps in the verification of the public key.", "WrongOrder": "The order of the public key has been modified. If this order is used in a cryptographic primitive instead of the correct order then private keys may leak. E.g. ECDHC in BC 1.52 suffered from this."}, "schema": "ecdh_test_schema.json", "testGroups": [{"curve": "brainpoolP256r1", "encoding": "asn", "type": "EcdhTest", "tests": [{"tcId": 1, "comment": "normal case", "public": "305a301406072a8648ce3d020106092b2403030208010107034200044cee5e1072b30d64f70bf01958e22c044a2127ddd744ce3060c15990ff0fe1148c6ee56559829a5a84dd5c8646ee0c43d0b7c50181f234ec09eba43bc86b169e", "private": "0113db979e07d9c8fdbea5b06a682c0d2ad67170ffcb65d7547d8c442d3ac237", "shared": "3f00d9af7607fd32809ef7a4a30b396e3da9f465adf20597c9e2046f16a7b1be", "result": "valid", "flags": []}, {"tcId": 2, "comment": "compressed public key", "public": "303a301406072a8648ce3d020106092b2403030208010107032200024cee5e1072b30d64f70bf01958e22c044a2127ddd744ce3060c15990ff0fe114", "private": "0113db979e07d9c8fdbea5b06a682c0d2ad67170ffcb65d7547d8c442d3ac237", "shared": "3f00d9af7607fd32809ef7a4a30b396e3da9f465adf20597c9e2046f16a7b1be", "result": "acceptable", "flags": ["CompressedPoint"]}, {"tcId": 3, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b2403030208010107034200041284e50ecda6c7c3a15ae79c318dec591da197fc32b7a046b168774013e54687156d96fe0765ea03404ecf5f6efc74b0792c05c11f9a683b97f5c7ca74f8a8c2", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "0000000000000000000000000000000000000000000000000000000000000001", "result": "valid", "flags": []}, {"tcId": 4, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b24030302080101070342000498577499d7a42f4dfe44c2757fd9932ec7e45a0d902e0bf1fe4ac09e705eb8e12d9b9a0092c9c92a661e24b2138b6a778324c9db815eb05621c54cd4d4b2e887", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "0000000000000000000000000000000000000000000000000000000000000002", "result": "valid", "flags": []}, {"tcId": 5, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b2403030208010107034200045760aa78cfb0df84fcc2c442321a5c278fca245aa90a4fcb33ad8bbbe6e847df8b8f102d948a52b3c21f70067e106daf10626113b8ddc08e50fad4df13f90bed", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "0000000000000000000000000000000000000000000000000000000000000003", "result": "valid", "flags": []}, {"tcId": 6, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040b3e56747ee2a81edc487f7b257afc5886f3d80e9848472a944590eacf71172f5786410b71ce226824cd3f0ef462185b2557c9e8e1f02c654e8423488331b600", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "0000000000000000000000000000000100000000000000000000000000000000", "result": "valid", "flags": []}, {"tcId": 7, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b240303020801010703420004854a72d5fbeacfc57a5aea2e386e549bab87b8ee313ee6c1f12cf267826940769f1c56486b0ac2dcd96bc018f5f42729cc073c6291bde8c3e7c169329c798142", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "0000000000000000ffffffffffffffff0000000000000000ffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 8, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a7043cb065916e173837bc1a109526e946b66008398e697b40438a3e0a2b0d9d56c6faa83d8a4c4b9e2a9b4a6917a45aeee61491f4f4e47efebccb71bef8defe", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "00000000ffffffff00000000ffffffff00000000ffffffff0000000100000001", "result": "valid", "flags": []}, {"tcId": 9, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b24030302080101070342000494b231b2189576b84aa5911e80da3074a4d16988041f908e6b6a199c16e1f7ed26fb9184971bc6f71cbced6836b3107bb17061a440abca27e5b44252bf39c6dc", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "000003ffffff0000003ffffff0000003ffffff0000003ffffff0000004000002", "result": "valid", "flags": []}, {"tcId": 10, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047dc0f29a5542c7d67793f52dc1de986bcee45d7571395cab102893aa2a5bf8cf7d4b8443793b137491736b26061ebd705d194405cf108b741cd1c5b42f44f6ab", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff", "result": "valid", "flags": []}, {"tcId": 11, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b240303020801010703420004834777212988ed962bc9d57e98f20bcfa34d9c6587381db48e48ce20a1ce961d6985ed4f21bb019791a6f08c36c42924cd72fe653c90882b9747059b0cc1ea17", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "2310a87dfe5fc25d0c0f51f0d379ef19be687bef34795dc2fd3fcc2d1cfdf189", "result": "valid", "flags": []}, {"tcId": 12, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a67cccc5e7b64889f5a8e5c52cb875258f525754ccb6ae2c75c8b9de821c780c7f38e6d238e55725250fc53f67514a9852ff5dd2ee1acf582254cba7deb72267", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "3367a0bb5a87072418334cead5227aedfc063f1a7340dc1c7c8e576eb4118ebf", "result": "valid", "flags": []}, {"tcId": 13, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b2403030208010107034200044fe39fe909da0a3e5a05d4f9717661c8f9af24071d711df15de225e98d5edce462aca0b1ffb66a09b769a2c23314d5116fb30b71456ca798370abad10097d453", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "7f00000001fffffffc00000007fffffff00000001fffffffc000000080000002", "result": "valid", "flags": []}, {"tcId": 14, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b24030302080101070342000496c13e3b6f626cc2dd89992ec34b39851938a6c6015c1ee4889795dc536c1415330888344ffe63e0d54e4f9365dd209013933a0a84d5e30424f516b87189b835", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "7fff00000003fffffff00000003fffffff00000003fffffff00000003fffffff", "result": "valid", "flags": []}, {"tcId": 15, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b24030302080101070342000406d3939c4c916da103a5f15438ee2b0863cf18b269d3d1a66ccf684f2fd568cf7235f3bac71d7d0f1eb6db9ef7aff385f020991db678e5124ffb0667745b3e03", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "7fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0008000", "result": "valid", "flags": []}, {"tcId": 16, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b24030302080101070342000429f6ba59fbe18c3ed99bc7da638ae419ee7d7853ff6732f5172dea1f085e8fb5071439411bba430d2b8bc8f58c955e94ae167fc9ca06fb262c7c93d710fd62ba", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "7fffffff00000000000000ffffffffffffff0000000000000100000000000000", "result": "valid", "flags": []}, {"tcId": 17, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b24030302080101070342000485d4f416552f4e51f92d80b4d6e71c6487c6fb0a106ed48395393d63528e6e782ba4c5228e60191a762dc02ccdaadb63364eee8e5008b9b25e0577f81903cb53", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 18, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b2403030208010107034200043b072da3dee819aba673abc3b1acebff18e4a6a20af8d817af97c2b4a8c5350a68e3bfb1d035fdfdb876b2adf85d9af1ad32e57399ca903a2d3fa4902124d194", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "8000003ffffff0000007fffffe000000ffffffc000001ffffff8000004000000", "result": "valid", "flags": []}, {"tcId": 19, "comment": "edge case for shared secret", "public": "305a301406072a8648ce3d020106092b24030302080101070342000463e92d05cfcde1179188bff8ba2043beff5a30a1a6b8ec1dfcd8273ce2c330540607392bdd311a313e3ae53196b14480b1967a235ebdbf1827165477884b5be2", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5376", "result": "valid", "flags": []}, {"tcId": 20, "comment": "y-coordinate of the public key has many trailing 1's", "public": "305a301406072a8648ce3d020106092b240303020801010703420004292175f490fa8aead813bc9b4923233d999ea61e7f1f9d0b0a02f0f64bcd2a72795a299e368fa3b93ba25193147ad35cb013f8bce669086759f70a56d4083ae0", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "7ffffffffffffffffffffffffa1c6646ffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 21, "comment": "y-coordinate of the public key is small", "public": "305a301406072a8648ce3d020106092b24030302080101070342000415e8d9efabc51033eb4d7ab63b46f2b94d942c1abb9c430eda6079036eaed76e024f0f5436b94b6470ff9365849a86b40c14d5588f3dd5264bcf0ba344d9fbfb", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "7fffffffffffffffffffffffe966ec89ffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 22, "comment": "y-coordinate of the public key is large", "public": "305a301406072a8648ce3d020106092b24030302080101070342000415e8d9efabc51033eb4d7ab63b46f2b94d942c1abb9c430eda6079036eaed76ea7ac48876b355e57cd66772b18e906be622720cb45e84b01d4443c79da94577c", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "7fffffffffffffffffffffffe966ec89ffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 23, "comment": "y-coordinate of the public key has many trailing 0's", "public": "305a301406072a8648ce3d020106092b2403030208010107034200043704a3e3047e3c56e8c0d8c028ee1ce3ceef5ba1008bae069acae0f7df5d0f4e3940cddb1b2ee9a7839fc798a0f52925832b1da398c7dcb11e9faf36720ff60a", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "7fffffffffffffffffffffffe8e61519ffffffffffffffffffffffffffffffff", "result": "valid", "flags": []}, {"tcId": 24, "comment": "y-coordinate of the public key has many trailing 0's on brainpoolP256t1", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040a7f86bbfb3a6010d9a8fe72aa0f0fafecb0bf2c18fc9b2937a7b13faa286be3a093055a187b9e90bbcae8a77bf19fa4dd11bce1bbc0b5d7549653c4e7aaa13b", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "23484789fec2f42fdefb0c4ec656217c4f53074616300f86325958b709e3ffc0", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 25, "comment": "y-coordinate of the public key has many trailing 0's on brainpoolP256t1", "public": "305a301406072a8648ce3d020106092b24030302080101070342000454fd3196b31c9b74c0f2df901f480f72fe1e2cc3d01eb19ebb8b653ca13945a102d6d242e67d1e60b7b771cc73ff9323af456dbcdf0b4e2916cb2448dde4c63f", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "1d184add66ac6d1e4f53d3c3cc2d997a18765431d2e261ceb457ac61f7fef424", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 26, "comment": "y-coordinate of the public key is small on brainpoolP256t1", "public": "305a301406072a8648ce3d020106092b24030302080101070342000462c15670202cd9a0cdf881a0ec622d2b39b5e3e6f2f955301210f15da98acadf99faae96994530ff39dddb09f0ee722d6a6a4cfb51eee6508ca4df05b24606b4", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "3d607b612ed1fb6d2b6ae4fbb7024b9ccc379170c121f49f8fb6185ebf937635", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 27, "comment": "y-coordinate of the public key is small on brainpoolP256t1", "public": "305a301406072a8648ce3d020106092b240303020801010703420004799c728be08300ab54b53e64d277e79c027325dba1cbbad5d42f9a01c57d82d852d1d2095f1c854a48d13f60ec1c63da0f4099016b08baff06a680c43d2563b6", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "07678e0ee1ebd219fc4f8dbae8ec282f9b33164c9285c82bc5478527d5e9b7ae", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 28, "comment": "y-coordinate of the public key is large on brainpoolP256t1", "public": "305a301406072a8648ce3d020106092b24030302080101070342000462c15670202cd9a0cdf881a0ec622d2b39b5e3e6f2f955301210f15da98acadf1000a94508a978bd04882f86ac951b4503d1a928833739d7936e69176d284cc3", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "3d607b612ed1fb6d2b6ae4fbb7024b9ccc379170c121f49f8fb6185ebf937635", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 29, "comment": "y-coordinate of the public key is large on brainpoolP256t1", "public": "305a301406072a8648ce3d020106092b240303020801010703420004799c728be08300ab54b53e64d277e79c027325dba1cbbad5d42f9a01c57d82d8572985d242d22471f594cb2fb16729985efb5d226a1d6529196cc758e248efc1", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "07678e0ee1ebd219fc4f8dbae8ec282f9b33164c9285c82bc5478527d5e9b7ae", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 30, "comment": "y-coordinate of the public key has many trailing 1's on brainpoolP256t1", "public": "305a301406072a8648ce3d020106092b2403030208010107034200043d88f3a636fa94d06d5fb35314d34c51403b72361931bd3586fec2af98ba62a9973d2da83cfba4eb3c995f2bbf177f5051fff9ddb3862845b70da138628eef60", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "770c84309d95d813415d36b8127143d7c97dc5eecb764049631f1d8e6c6e3ce4", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 31, "comment": "y-coordinate of the public key has many trailing 1's on brainpoolP256t1", "public": "305a301406072a8648ce3d020106092b240303020801010703420004930fb9321b318dc43159a55b8b4ab6873fb2abd85b2792b4b146ba84ca1f0f817bbc8ce0a3f4272c01431da8cc4134820353b2dfdc0fb1d1ef0397b5dd102fda", "private": "06546f19660be42b6455813d02dab822a1c55529e43179dd5cb77cd16b2c4cf4", "shared": "844e5c76bccf3722be4395146b4a76ac00311cbade49d6e9c5bc35a5bc2c7548", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 32, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004000000000000000000000000000000000000000000000000000000000000000109e0e9e8d98fb89da2a32b2c7618b26bb99b920f02a5e831a142e6c8673110cd", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "599cde22070e96a88658c4a5a83f3723542a402ed506639ff9016fe4a26d4c25", "result": "valid", "flags": []}, {"tcId": 33, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004000000000000000000000000000000000000000000000000000000000000000226ccfda8234fa9b70316b5ec4da222972b34a970cfe6dd9983a05e2fa746b902", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "4b35a3caa10cd77034e0737e18bc52071f64b8a2e14d7a02df8a36886aa467e5", "result": "valid", "flags": []}, {"tcId": 34, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b24030302080101070342000400000000000000000000000000000000000000000000000000000000000000031e8d9392fba7ad1f3ed5b5746cd930370a81d7483fe32003e31264829c9fec8d", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "1d1d5a6ee25b5fe41e015f19cb434936323eb24da09bab873e3c25bea7af8749", "result": "valid", "flags": []}, {"tcId": 35, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004000000000000000000000000000000010000000000000000000000000000000001ef5fa5ab0cebc18b64113eb8c040dd743184e7c8ac68f123f3c3d945585524", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "111fbae46ba6389c5879d02e1863301ffa9e1961d721a57c6d88847154398812", "result": "valid", "flags": []}, {"tcId": 36, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040000000000000000ffffffffffffffff0000000000000000ffffffffffffffff2e65afc115d0fe1a86f314629014d6856716a4d9a0114c713051dd700dcc1ade", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "23b36c78c27076e23d6ab56c2e9653d272922c9fc1a176b8b6c2e70ab7c883fb", "result": "valid", "flags": []}, {"tcId": 37, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b24030302080101070342000400000000ffffffff00000000ffffffff00000000ffffffff000000010000000131625916fc4e157b1cf93f3c80352ba4dbf26effbd87d31a2a808d001081f06a", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "27dffb62a6ec9ffb3d0480c45ba00c748193afcc0879d76b47f8ee356785dee9", "result": "valid", "flags": []}, {"tcId": 38, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004000003ffffff0000003ffffff0000003ffffff0000003ffffff0000004000002330b902c4f6a2486744f36adc6000e116a2cbcb14ff9f47aa36319fef93ad5ea", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "9bffd451ceadc27df88a13829852ddab0b2938952f5c58fdae7dd22248840bb0", "result": "valid", "flags": []}, {"tcId": 39, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff0000ffff2c6fb3302dd93dc25d2c6792c2ac6f86247c4d39637ee11d9267658017f0055a", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "267a69f4b3945f8d323ff1a526bca566e7ed9806d6a6dc8f4587ab229a5f3ab2", "result": "valid", "flags": []}, {"tcId": 40, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200042310a87dfe5fc25d0c0f51f0d379ef19be687bef34795dc2fd3fcc2d1cfdf1892fb244825651a94d4d93ff90b74130f77483e5699d68a7a43ae693bca85688da", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "4d3d61bf26573a2ec1d9ae6be62d1a30b71aa963660a4ff309557e616742114b", "result": "valid", "flags": []}, {"tcId": 41, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200043367a0bb5a87072418334cead5227aedfc063f1a7340dc1c7c8e576eb4118ebf4648a7b0639656227ba7c4b346354b2465099f8422d1be92f0f45ee23b7e2d1a", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "799c4821e92815b9335e77741a9bca2333053886a3777ae637d02e4420d82a66", "result": "valid", "flags": []}, {"tcId": 42, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047f00000001fffffffc00000007fffffff00000001fffffffc0000000800000020cf9ab5899c59216d6d1bc786ddf6221e374cd37a8b745e826c6495bed0a56b0", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "a4a2d3bcc69380396089e719ab8c77e5437ee589e39d98eae7bd17ce5c1d5b03", "result": "valid", "flags": []}, {"tcId": 43, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047fff00000003fffffff00000003fffffff00000003fffffff00000003fffffff01a1ad42b3ff22ba6bf3c94b55cfa4d13c6e140d3c44963198f496ebbc50439a", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "3a6ff3c1d490f652f5178a794b8cf825b5044948f6ebda67310c11fec1ad2b53", "result": "valid", "flags": []}, {"tcId": 44, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047fff0001fffc0007fff0001fffc0007fff0001fffc0007fff0001fffc0008000369a4e24f010260d7c2560f7dc19c41cde6b5c503b6563678580f0d22c74dda4", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "2d59b895062c13d4e5b5e0b77ef273e94e558e724f6241af5bb11bf23acbf851", "result": "valid", "flags": []}, {"tcId": 45, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047fffffff00000000000000ffffffffffffff00000000000001000000000000004677b043dfe54f78c735543b752b3aec043f656e5f22dd6d956cd642e3390881", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "83640be428f7a4f80ae2671799b5ba1881241fd054a7f2dce0254c07531abe40", "result": "valid", "flags": []}, {"tcId": 46, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff178945df488779235a2637c39a4a85ab707bd56e7c22b9ad41b652560123b6af", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "801e5f5d61c666bc089a4265c40d2a3fee0db20a0f78e329bbf2793330ab0412", "result": "valid", "flags": []}, {"tcId": 47, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200048000003ffffff0000007fffffe000000ffffffc000001ffffff80000040000000424bbe5b0b8702258d2462a8bda59a343b97c3fb1d4005416802bebd628e7d0", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "a409a85067dd63e5eab3aec3d74cb7f071839247dbf97b6de592988095509d0b", "result": "valid", "flags": []}, {"tcId": 48, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e537613a0346db14d55d1bcc27079b68864ac32885b5bdfc3c9db6f85a35d3df4c39b", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "66d757884634861f28bbe45b502f895233b0ba1c69bf45802f80eac5c837750f", "result": "valid", "flags": []}, {"tcId": 49, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047ffffffffffffffffffffffffa1c6646ffffffffffffffffffffffffffffffff5407f4a41e7904d3e7a9e6e6d70b093361dc5ea097c3767e1013f5868fffffff", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "94456bf9e33c392315c5bc350a5d48e39fc7a9ff5ea6fe309e24a89292690dc9", "result": "valid", "flags": []}, {"tcId": 50, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047fffffffffffffffffffffffe966ec89ffffffffffffffffffffffffffffffff000000093b3d1910bada32f99c20977cff951d97072fea058e879c4bfeefd404", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "41e2153f9411102965a2f63f8874aa7db33571466b160ea33e9af6721ce5bbfd", "result": "valid", "flags": []}, {"tcId": 51, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047fffffffffffffffffffffffe966ec89ffffffffffffffffffffffffffffffffa9fb57d266b190ab838bd7970162f5f56ea6d88ccdf63622918babd1207e7f73", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "41e2153f9411102965a2f63f8874aa7db33571466b160ea33e9af6721ce5bbfd", "result": "valid", "flags": []}, {"tcId": 52, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047fffffffffffffffffffffffe8e61519ffffffffffffffffffffffffffffffff8a3af7f7a8a00701cfee7f390f84ed21d374623c20e5c822760d9de3b0000000", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "79c4bdada76d7219c0d65bf220b714440f838a2a1b36efd6276de35b79bee16d", "result": "valid", "flags": []}, {"tcId": 53, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b24030302080101070342000423484789fec2f42fdefb0c4ec656217c4f53074616300f86325958b709e3ffc099d1c0c49a359a0c6251b8653b65311a288b99ee6fcf2f22df7886eaec947aee", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "31219d91b716c4897dece400f35cb7103df610bffdfdb90bf76be274ff2bf9f6", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 54, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200043d607b612ed1fb6d2b6ae4fbb7024b9ccc379170c121f49f8fb6185ebf93763535f4a1001eea1bf1ebbfd643af146b990993e98e71af4414e736941c8e444716", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "703413b19d8f415989f70ae507b4afd9900504e3725264a121bef966a30f8922", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 55, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200043d607b612ed1fb6d2b6ae4fbb7024b9ccc379170c121f49f8fb6185ebf9376357406b6db83048dca52a6344cee6f21d964a80c956376dc1338dcb400912a0c61", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "703413b19d8f415989f70ae507b4afd9900504e3725264a121bef966a30f8922", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 56, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004770c84309d95d813415d36b8127143d7c97dc5eecb764049631f1d8e6c6e3ce492c0e8c5ca2371c082e9576485eef53d1536a27ff0d59ce5116a047df7f08ce0", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "86c401c681031d35991ebeaadf996a03b4b471951f6f647c155fc2d740254909", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 57, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b24030302080101070342000407678e0ee1ebd219fc4f8dbae8ec282f9b33164c9285c82bc5478527d5e9b7ae6df217e1744f8e84ebc17f412d4c31724e7836566a40b303857b9a0dd6f68217", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "4e651001d9daf76ec7ed6af918815845f74539aea615aa648abee3d1631adabf", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 58, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b24030302080101070342000407678e0ee1ebd219fc4f8dbae8ec282f9b33164c9285c82bc5478527d5e9b7ae3c093ffa2d9f1b3752a48b4f70375c001fc3bfcd6ae56d249a97ae0f4877d160", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "4e651001d9daf76ec7ed6af918815845f74539aea615aa648abee3d1631adabf", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 59, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004844e5c76bccf3722be4395146b4a76ac00311cbade49d6e9c5bc35a5bc2c7548483436a1adfbbd6f5faea546bec9b4313d3aac98e72ceeaef2225a3e06f6a033", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "28b3fc85e5559f77eb0d1d56b1c229ecbeb7402a5c4c4c5c3e66bcee0c0567fe", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 60, "comment": "edge cases for ephemeral key", "public": "305a301406072a8648ce3d020106092b2403030208010107034200041d184add66ac6d1e4f53d3c3cc2d997a18765431d2e261ceb457ac61f7fef4245143cb5707b62cd1f7d5e59f1a0568186544bce622e1e1c722c52e2e5ef4a409", "private": "3717e8add3e1bd89c0761b3aced7b673a0830e477c2b6a76bf1a909b3943fdca", "shared": "a13cc4866df5d6a35da6e0555e54617ee3764b3bc050c0ac3e302b1ba7705f1a", "result": "valid", "flags": ["GroupIsomorphism"]}, {"tcId": 61, "comment": "edge case for Jacobian and projective coordinates", "public": "305a301406072a8648ce3d020106092b24030302080101070342000479838c22d2b8dc9af2e6cf56f8826dc3dfe10fcb17b6aaaf551ee52bef12f8261e2ed3d453088c8552c6feecf898667bc1e15905002edec6b269feb7bea09d5b", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "5ef8dcc080d09e279e999efc5e1254be40172e818330c6c29642949eb3077657", "result": "valid", "flags": []}, {"tcId": 62, "comment": "edge case for Jacobian and projective coordinates", "public": "305a301406072a8648ce3d020106092b24030302080101070342000409ff7b7d82fb6494476c001a8bf8199121d13ddae4fc291249eec8cda427b44b33c51a99080b27c17b110c203918f1eb2025d237464de8bb8f112e218356d2c8", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "20095119f53df7b337620e628b924991482a342803eaf08ac79531d2f2d17beb", "result": "valid", "flags": []}, {"tcId": 63, "comment": "edge case for Jacobian and projective coordinates", "public": "305a301406072a8648ce3d020106092b240303020801010703420004189a707aba80560eed6685dabe11fd58cdca38c3489ca19b6f4223a0c8d80a144601e8db5446f98d29d9614b5f36d01c24a190191c49499c03975184a4e8c514", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "2673db0682f6a035e25406339c7129918aa9cf100056ebddc317d4e43a1642c2", "result": "valid", "flags": []}, {"tcId": 64, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000459305b2bb668f2423687c4478bb400b90bd414020384b415bee329eb0e9f5a95732b351e79fc7d4bbc2ca36ca36bdea2f0dcc3348e87956c1688c134716eab5c", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "2dbc00813cbb71778fc6fb76173e2afa0e8af32571340c91cea9f1b1cca07020", "result": "valid", "flags": []}, {"tcId": 65, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004941316bbc40902204a945f914899be7fc4dc282f0c229bc374f0997c11a23e1370397e8b750a575a5cf8cd716ddc1b607feb289c76997c4cd20b5c9d85aa0a43", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "8d1a91b2bc7bd7598f4ae3d17f6a8978f1df5fba942ef2b8b95952f5cec193bc", "result": "valid", "flags": []}, {"tcId": 66, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047b33abc06f6a8748a4a41361104e367558886f92345feab86ca9b9efd803689f2bed592a08e5bf9e123282d8416823509059b55c8af6ff04b00e678bf8afc5ee", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "8527197d2afde64e2daba8929a918fea5a5884bced175fce3380dbcfdf63b356", "result": "valid", "flags": []}, {"tcId": 67, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004545e3b057578604ee11c9515b4f7d66da668aa0617018144abf7fc58c6f7b57d6bbdd1eec7e40d6bbba3303c74502d74c0b5f1d8e5d2f5568f7b7cde745d7af0", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "94991725d115dddf73699a8923104bfe4dfd3c5e462a8a6ec1426decf5c86fae", "result": "valid", "flags": []}, {"tcId": 68, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200041da6a5f644cc0d093f0aea5635d0374bf4c071f7ac8b6e2b68c9b9f352ecc77f67515ea4adb5bb9688f5f83c1350b33332da9561a0265d6472b3ac49730cc7e3", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "5891c8d5a53dd551d96141b1d9a08e8de40c2a799697214adc13fd8be5e169df", "result": "valid", "flags": []}, {"tcId": 69, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200045e943ae77a68b60ffac9555176441fc589b36c324269eff7a41698608154e9f5936cf4f173d916681ef18d6d812637364cdfae0bf01d5300c764d8d187bca189", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "10e2df244dfe2e2b2f4b5cd6462b9341fc7963499a8429fdb3636ddd1ee0a19a", "result": "valid", "flags": []}, {"tcId": 70, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200048a8bc74d085f353cd588475e5ae9ebe5701c001dbe5586a7af9728113c9b7d989a11d19635eff71beaddcbb28400bb284a7a5d5d74ceee23249a53c977486b9a", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "37cc4a3060bb3f7b9135e49f574c1d42eb1a51afa780819f8f1abe21882347bd", "result": "valid", "flags": []}, {"tcId": 71, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200045273e1ad6e9158cdb5fc9a21dd03a7bbb0fe7cd136be58a8f0f8800eeb7afdaa7f963e8b0d564386569ac1851dfb6dfe9b12f7ab8a72ea478cc1146e0a7b9284", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "37c2b96d472364fa466807afb5bf11d63e81544efa6c67bccd1abf3e74d3df64", "result": "valid", "flags": []}, {"tcId": 72, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040ec66ca5dd8bf6cc2882b703c65e6a0a7a4c6e7e1d9a0935531bc859a8cac0aa31206f59a7ef585e96afbfa94349aacfaceff703d3885300fd23cc5b5b195cbd", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "006c23a1d0804c6a131facadf8c42e8fd55a3e8373c44d399caad9363ef1c33b", "result": "valid", "flags": []}, {"tcId": 73, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000431b05f2784a6172284d683d8453c170fa897a9e620e0d530f48d45625649bde2612cee16184099f97e28b0aadccd3786e14a8705f5537131e5c045a4e0eb7891", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "8590b7ab932dcb8c226b1ebe9ca4bca14a69676d7fe191514f656b87fae95f57", "result": "valid", "flags": []}, {"tcId": 74, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000446b63a006010e9311ffc5d67eba37d250c9524b2292ae09b0029568c15bde4ec6a70f734c1f16790ca43395ffc124d6a688e549fbe19c3ecf9c1e423f2b84224", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "3c5f64227dd205b686191b6dcf3af427ef7a94e84264abbc9c078b7bf58186f4", "result": "valid", "flags": []}, {"tcId": 75, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047d86ddaa5d7d6d3e81c4f8bdcfa010cb2a8a2e32f24866b309bc065a20851de931a285d0b816bf03bd8d112422e579cf9a73ebb17f8cf867bad767d495febb73", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "59708c853fce1a9bc84b80bf90293bd080cf633d39f5a2edf60e880e97c61f03", "result": "valid", "flags": []}, {"tcId": 76, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004726abf165197d1b2fa69bf628bd93c06216dbeb201f0ca2824863a7d0b1a0883945b78cd7400fe885ba6b31c0194a56a06413a60b1a4ee51dc1cef27e7e4d137", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "7a8bccfc86a1d684afe825d7e1ed7d309b22a9081fbebf348c5353ff55e97000", "result": "valid", "flags": []}, {"tcId": 77, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004117453236a15d7fc598a05848bc6ce25c4c7698cbc9a65c6d4c579dfafaed6139e48ea8eddbc1e8cf4ecf6c104dee3d3a8de94cdbf4fd00e408fd628f21436ee", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "a98fdfeb5e87797ef22821a54ecd5561932d784e2b4cb31a509f8630f0e118dd", "result": "valid", "flags": []}, {"tcId": 78, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000411b99a710f152389dbf9b21815368b52a33b28f5dafa05284e3ac9be5cfd5d3c46f5461c5da21d222c789e2009246a03e366bb29d29cd42f2493a3e2951e632a", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "1b8aa842417e57a1380c958a4cbd994740d9ea5c878b2b5d1092e9e894e3cdd7", "result": "valid", "flags": []}, {"tcId": 79, "comment": "edge case for Jacobian and projective coordinates in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047a73abc65f2a5734d23c588a139fce1238182cf3f4765766f5fd9dff960526fe58f45e67b3ebebbb73f88e81dfa9624c70ccb7567b14d2c6e2545c1fe2379689", "private": "00a315ffcbdc5cfdc801b6651307040e3b214bb904f863f6039e0aae147ec2a833", "shared": "3193809a0945b56525ec00ee105c2ae1d7a173133284862c1504f4c4ed8aba42", "result": "valid", "flags": []}, {"tcId": 80, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004265919d0d0a70b8e609fae908317d2cf25985bf22eccb1c702e9ccc48b8a3be17871a57d7ce5e73b800652b0ec110b33128e6a282e10e94fd0b77e11eae0c334", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "8af2edf71a0484171f5b55bbab191877b60d706b191c6cc845863ad817cd429b", "result": "valid", "flags": []}, {"tcId": 81, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200049640b80ece051500d9bc39a997c1bf7033b0dd0341dc0b64486690406c02c3ed9bd2afc87df3d2730411ecb781e1f6bfcc004f4d7ec3b7af7816199b466572e6", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "8d50765f6b67ec249a75563f734e5dfacdb5a7f59844eb4991eb333e7fb74be6", "result": "valid", "flags": []}, {"tcId": 82, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200043a8507a1c731bb9bd510299e4ac86fe042d21ed07851ae785d2841260bdf7ba5401c19a55215b85925e79b264b07bf7eb8bd058f78a9cbd48a8a8892ad1474f0", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "670624f5455b3df8884e52d276dc622102f9aa0f8f5f5392cdfa43812ee900b0", "result": "valid", "flags": []}, {"tcId": 83, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000460da835a9b47d346ca7357fa320d19fdddc9fd822c56aad82c4e5d8bd69c3745361ae18d9934cc20f44d1f37e59b617e9ee721cc78a979c77984c568a6d4d7ed", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "865595b87433fc3e8ad0800402fed46817ccf55d20b83a4a5d5129fd93bcf665", "result": "valid", "flags": []}, {"tcId": 84, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000410ede2e10b9d31db68df9a871213e474e2d39b2b211376cd7828bd98ab8caafe9ba97700687201d77561e99d97e27c1cd99d32bae9707149dfea7c06320c6ff6", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "0e4493ae56747fb681d5cb73e0d72e27d0ee0445c71a0f1c5816d2fa63dcb9ac", "result": "valid", "flags": []}, {"tcId": 85, "comment": "edge case for Jacobian and projective coordinates in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000428497f54a89f0ee7fc1fcc49232292a53bd85cba8293da00ccd50b70b0f1738b91f2908296e6da2ec9139eff5cdb1c51d821c00d054d779fcfd24264ef16a462", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "446d3a241eb40aaf6e506471c69deebf3e684c24345c0247f54a73ddb6c4883e", "result": "valid", "flags": []}, {"tcId": 86, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000440acbc781b198e428d8d1257d5b6d2b1cf53c97bb6038ccc3eb9ee87c21f5ee0a02debdf9ee94b22d6ec855ae393021bb57f929ca4fb03988d9e6c00ee075fa8", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "3f581427b0309a6872f3714565bd76e952ac6d8f8968b7c1f4b8531877b6f0e9", "result": "valid", "flags": []}, {"tcId": 87, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047ec6890a5d705927971ac2c601667002e6a831847f6f2b2026efd6379f138dff30abc08cb01fd44c31c18c8e5fef41216c9a4e1082dc928f2e1e22ade860b051", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "7ebea984d4fb21485e0807f816ccb0cbcc817100c1feecc4add22e3c40d21b4a", "result": "valid", "flags": []}, {"tcId": 88, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000434dbf90091cc4bde4aca6b8ca20bfa17903b34f9665ca4e3755cc62c8e350138085aae9dc3ac8dad22119f3cb79e80d40be82b7ec53462d4453b246ea8d33e0a", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "5c2aade0a98eb4b5eef2c931c8ad96507e7878fa71f27349dc73189e22003439", "result": "valid", "flags": []}, {"tcId": 89, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200042996d4ab42de8396fa82cba6180f402b0b491aebccaadc12cd8ba02b59d0a33823decd018757a83880ebbff1ba886348d5f9b78cd3483467386b1ae426f6f0d8", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "0422fa3db0fdc03a566d1ecaff954fe5e9f14bc5b9464145b709bd2f6bb7205d", "result": "valid", "flags": []}, {"tcId": 90, "comment": "edge case for Jacobian and projective coordinates in right to left addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004704485f4c9cfcf22b7f0d7d238cf83c4b8ed64e1f799362a864706617bdf863a3bd8fd90a27bcd939b956e982a2d699e93ac360fbfec7f3db28ae6f4d527689f", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "a8acd0b65fb0a5e6a8ce0f178843c3f05ccd2d69276426d9550c6469d2bb5edc", "result": "valid", "flags": []}, {"tcId": 91, "comment": "point with coordinate x = -1", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e537613a0346db14d55d1bcc27079b68864ac32885b5bdfc3c9db6f85a35d3df4c39b", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "39925adf80137a62af8826dbf0598ff4cda00ab03bdf19b298aceab0f661c888", "result": "valid", "flags": []}, {"tcId": 92, "comment": "point with coordinate x = -1", "public": "305a301406072a8648ce3d020106092b2403030208010107034200045a160c3d0106785d6a87f08d3d7993d07a8e6a9054b3d9ae1cb2a52f874a378418d4883b76395b9698e4e7c986d5daf3d4ea054c45213a20fef0d814ffc24be4", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "1d61f183a22fb5c51713d63904ed736016119f35e6555408694d418725693f57", "result": "valid", "flags": []}, {"tcId": 93, "comment": "point with coordinate x = -1", "public": "305a301406072a8648ce3d020106092b240303020801010703420004278346b3dd1537932f0ec7185de5dd9257f4872f3003e7d97251d35ee38683cf0b9411b5e8193f75c058762d524580f566910a84272e3aeefef4e25c1e801fd9", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "7da78e5caefd123a4b6cfa4614740f1e08b42427e8ed2ff77c4a6697b7d5eb69", "result": "valid", "flags": []}, {"tcId": 94, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000412944bbc0edb557f69092329c3ece041d6f0ad7cb1a901b550b35058dc6af5de649ca2342b06c43854e8a3af383f75e363b271ed90eb62f843f69f31f20b9bef", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "67db52eb947ab03e8b7d172c187821062de18050f0718633a82ff06a9d814519", "result": "valid", "flags": []}, {"tcId": 95, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200048ba74e0dd65280c32d8e6ec192a4c82b346bf942f28b9a9707fc762627989e2897852637a373adf4c9057233671c2274df62378b77829245950e39708dec2336", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "4a687a8fb8c6005cc8d193bebd92ff5ba841831bb7c203f41ee435a26d2c7653", "result": "valid", "flags": []}, {"tcId": 96, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000494c7badfde7294c9796cafde2ae5117ccac3c918baebe979bdd10cddc072f26548d886b6632376dbd326e2a81135cbe8c0dea7f6cc2ead7695600ab6d5bdfbb8", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "031a8c1b0a4182914a625b259c94a92590db143eea8f6d666e383ac1ba8a6978", "result": "valid", "flags": []}, {"tcId": 97, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004515e622ae19bdf58fa07f81b76559971b52f6af0cb533466b9144c1ae6803d5914efcd4cd56af5a06547f4c9719531b7c5e35ecafc625664a562d5c3c0b1f3bb", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "64edb052d3aa61fece989781237479fa87338d91c40f16149fea265f38960c85", "result": "valid", "flags": []}, {"tcId": 98, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200046cce5c289c33bf4f4837847b6da51cc7b82c168ad863be86a56dcc11133b869e78ac9e2b177ee9b9ed5bc3bfac66249c814c563a9d436ef0bac9cc4fd480d9d6", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "7359b5c5812afe38f5d18c0e81fb4d0cddaa9a9eda659207233c745bcb459241", "result": "valid", "flags": []}, {"tcId": 99, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047caaf04d363f3065af3e3347eb4dc66811e961fc3ab0f3b4241eaf1b003b975b5572ecbf8ae23075143c2a975fdc21ebdc3abdf052986401ad14d46cae2a2c67", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "565dee5179b1a537b44ea7ae050c45ab0fa0b875560254e7d0a34f5877b83638", "result": "valid", "flags": []}, {"tcId": 100, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000417242d695ae938ca1d5103ee8734b8b7ce32f1a873fd5963d8ca1fa45e5f63749862bd830b40a42581fa665c5b1c63964807be6a52e98fe6a7447c4fc9eb981c", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "92b9b1a45ca61b85654ddbf986bd6cc89449fa1d2df6ecf71af78735974f9a18", "result": "valid", "flags": []}, {"tcId": 101, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000434c3313a25683684a453047bfe89261e4d6354cb55a328e4a13a116ce30c4cfc7ac2ac3a1072efb485e7604a505cc9597d023fa553a42fbbbe73ecc0d21bbbf1", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "822f79c4790fc3cea7f7f195b1bd898894f68182b720ec93efb6c03acea796b9", "result": "valid", "flags": []}, {"tcId": 102, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004644bc081a758550f0dab1e8b00510c299791a39410ed42cbd6424fd2210b088c8cfb1055de8e3fb1621fd51c3cc730b716967864333b059c69440d287b0057cf", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "3181db0a74a0c5fa35ed90b0363b4922176f76b34f828364cb2ae053068df6a0", "result": "valid", "flags": []}, {"tcId": 103, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200044f3a315a5d225373f63a903e60a4f246be33f381c930ad8160ab6bd0939bf633a0d27d027115d0486fd42875f0cd67d905ee1e98db6c7310cf5a93977e01806b", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "10bc043a9d7ca889bed9701b962764234e1f49a7e016e2d66f5bd14cf11722ca", "result": "valid", "flags": []}, {"tcId": 104, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000443989e593f2cf4f52eccc7d57c921f158aa341025572eba13be75ac2644bb159892d748cae88efb849b201cc98664d2fd1e58df9ce4331b7437aa03351cf1fb8", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "1c21f450ba766ff3e5f6c7859b98043ac32c98b27b0963adc33802c7e1d958c7", "result": "valid", "flags": []}, {"tcId": 105, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004287ba22bc6af8c2628893ed2f27fb75ba8bde8f2e5048fbf0b67aaa48367170e2a2f3070cb6ae91ab795106003e0358437d8dd47e55925329331860cfd3e083c", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "70eb47f830cbfe6a354d3a6021452d3dbb240fcf344e42aa9fef6aeb6ef2f93e", "result": "valid", "flags": []}, {"tcId": 106, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200045b21dc0054fd3dcd5fb09e225c5a54009952eb8b1a86dd58f65c7684ec57fa39588ce54788cb96e6bd5a2f98d184a809ee83689d5df39e50da63b3861ab4ff0a", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "9903baf204175457c5cea4dd448379f4b576f08c17f93394edf52c57d614e24b", "result": "valid", "flags": []}, {"tcId": 107, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200042863b68e69171ae8334ab16dd3fd2200b643c136baeab4d434fd27fe608f619d99a90a8ec38969c1fd85c6ab1ec580f2eb08d2f4a2eecdbbe500eb301296f7e4", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "75ee10f087ebb5feb8eeedf965f8a495cf92ae202eb2a00e82df1de46a0321a3", "result": "valid", "flags": []}, {"tcId": 108, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200041479459c8ae95e8c50de6ebbe0985e5aa54fa01640a472b08a41205cad6db6253aecd03d4242c528898e832ca5b318752d5057f0e2ec8bb273d3d8ed593d4777", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "0b3b90742c9dc4a000be6ecb4ff5286905876947e0785ddd8626d0d2fe0c87b2", "result": "valid", "flags": []}, {"tcId": 109, "comment": "point with coordinate x = -1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004448e359d487d2baf8e10976de428601ccf99c85e8485df955fa3a8db95b5b2de7da7960b469127c1cc5218a40558460dcacae68224af71efa908cb543ec217f7", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "056d7db999c7a57377ad4ab71e77252e848c0ccf801239522ffc72826caad8fb", "result": "valid", "flags": []}, {"tcId": 110, "comment": "point with coordinate x = -1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000400c5f7c478f1ccabb9134b2967190006396a23f113529fd731cefd6c564a8fc62039b2cb85a160b87f2e5e41e8b181f2100ce8c3c01e3770a4f9d8775f9a3ceb", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "568ebed1630fba5492ef3f0d095870fff40d59459d4833046081b894dd16d515", "result": "valid", "flags": []}, {"tcId": 111, "comment": "point with coordinate x = -1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200049aad1d4ae273e8a97f0a3c38e1bfd5e59633821270874e1f6662772ad3f67e765c908d84dc312560f7ed7e45954a8b923212fe2b20a340616e3383ab9424de8b", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "30eebfdfc9a78fac2ceef195181e23a9102312afeed9c59e3510c7d45a9cb2a7", "result": "valid", "flags": []}, {"tcId": 112, "comment": "point with coordinate x = -1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200044b1c8f1514fec698013b56c91f121683eb60e5121d9aad0d57432f8402681fec837093a4cb9703f731a8eef90d2e9d0eeb14bf3bbe8bac53959d2a2fe63d7523", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "27f63272296a47cff20802c5ff410d0a527ab21941a82715a7c5a4e4b11888ff", "result": "valid", "flags": []}, {"tcId": 113, "comment": "point with coordinate x = -1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004301a5048fd998bd04bc45a6cd9632c469390d9b8d8fb7bd4366cd4b038c84f056a72d467703127853615839e94a767791f1b7a74a3b77604b9cbbc61ef2e6478", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "36ad058442d3a8438ea6a28bbf3f4735ee1f3ec0230d7322b3ae05062e4063cd", "result": "valid", "flags": []}, {"tcId": 114, "comment": "point with coordinate x = -1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047fb8518721f3f678f42c5bb120baf833112554883722ee476a2003146d9d9af146fe1a51f139175b58b3a81f82ea0df77430484c56eaea0de3fca66070d002f2", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "82fa50c7599c5c100de75ddda432b71c879fb6c84300488895715954c011a395", "result": "valid", "flags": []}, {"tcId": 115, "comment": "point with coordinate x = -1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200048cbdd02f5c9a83a49b78c605f01de7b0a0f536f3e945839dd632af759700474270c30be669e16b3598eb0050325cfa685af650599619b663ed77d3d54360e88a", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "93c388d007b564e0023f17c812d6bdbb35fccea596213b619a0ac47638dfc4ba", "result": "valid", "flags": []}, {"tcId": 116, "comment": "point with coordinate x = -1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200042c9e063b6fa47a58aab0d022332ada1ba8fae3122cf112e146f783031e150a41a45d8036e713d2d6f7135ee836d63323690456193a9099a0760d5b171f962f90", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "20faa186754e98b84c04adf1124cea12042f81ad62393025727aa03887e53940", "result": "valid", "flags": []}, {"tcId": 117, "comment": "point with coordinate x = -1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a1997b75844ce54f3e26df6cbfc3b1135bdcdef0477a5588e40c59fdcbe1d1f30ad6b83fd2af20c9cd69c04123b91be130331251624443c5f6476306937dd749", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "93e04e723db8d655597c6949c2d465eb9f12ab1ae12e579322ada9726e7b9a48", "result": "valid", "flags": []}, {"tcId": 118, "comment": "point with coordinate x = -1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200042bcc4ab01834aa4ad5fa6270c0f7de71e56fe651c581bb2ddd465103600d291e790a3b9f70f115fb08a4a834245a6193aa803302d3b95b141d1e094ac0dc60a7", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "6b2ae63c6bd94f50ed8ae59b83b5036a5590c7c3daef6cee65bc6b9a28cf70d3", "result": "valid", "flags": []}, {"tcId": 119, "comment": "point with coordinate x = -1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000494e40912ae20088ac6c082884bab706e5e7391b16d2207aecec5ce6c72c99da666419d6830de0c29190f81493e2c63c4e75f6313c8ea3543a5c8e6db6bf653f9", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "1e2676a091b9ab8212a52562aaef6485544720bb403bd02ce009f41c17770bde", "result": "valid", "flags": []}, {"tcId": 120, "comment": "point with coordinate x = -1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000416914595d7e668c0214a69ffeb49b040be369b594c4cf338cc17f53693611c81470359d21fa707c54905536166552ff788b780b638f9e8b1adda69b932d0a6b2", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "4db7ea61e053e3f7357404e0253407a7919c048183d1b9bb49a741f6db96d74f", "result": "valid", "flags": []}, {"tcId": 121, "comment": "point with coordinate x = 1", "public": "305a301406072a8648ce3d020106092b240303020801010703420004000000000000000000000000000000000000000000000000000000000000000109e0e9e8d98fb89da2a32b2c7618b26bb99b920f02a5e831a142e6c8673110cd", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "947d518aa290bce91c5926510ca8dfbcfa2d112b8a5cab33c8feb5a30e76f14b", "result": "valid", "flags": []}, {"tcId": 122, "comment": "point with coordinate x = 1", "public": "305a301406072a8648ce3d020106092b24030302080101070342000402ba091e6f306c157c2b845250c94e3fdcb17841a9af6f757492c7adcbda4e7aa21390953b607dd7f004d4321f892f960234d3184dcc4c73cff7ab2d2fb1a507", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "1745132349d4a76c834e95049c5a6fa556da8d2fb2abf81414729b9e78590f98", "result": "valid", "flags": []}, {"tcId": 123, "comment": "point with coordinate x = 1", "public": "305a301406072a8648ce3d020106092b24030302080101070342000462d3c46be6c2c8356d45380646873ae1362ba4806e40994752c489e81ff5be040e60fda525542228e6adb7e85b5364f2ea0bb029d3a2a3c6f407aef9ff3bfadc", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "9e52724a880cc349d7ef51eb54afa817165ac33a053c7c0e627b982af1a9557a", "result": "valid", "flags": []}, {"tcId": 124, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a0b267c82ea3ec2d29be912617d61de705380922519f13ecc177a3ab602a2e0a69e0213c68012f6c530cc3ed44f285551c1f400d42cda9cdc3d75b9ab33ab1ed", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "972837593848c7544a3c98477a88ea5cf79fbe3b273b8c92812ca0fe97234ab4", "result": "valid", "flags": []}, {"tcId": 125, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200042bb3d2c597bbbb17edcdae8a39b05fa4c117df5376ce2a539fc6a61f541ad3629e6ca7655d464a5ac862bf4f614ff0e390f1e6e4e893cb6e4ffc3c78da224115", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "2adf696b6137c93fdf589139f2424d3094661b01df2c89d9e1bfe4e1bbe51ca1", "result": "valid", "flags": []}, {"tcId": 126, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200049f28c20f2805d0f1a15f1bbd664bfb6f9f8db107e3cd3587f224bb08c292dd502ffb70609473868d5437be403923ed741c0ce0e845cddbabecbf11af65e5e6c3", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "0f5c2b3b6c679b25706d1d25ba4401c1d6d361ab1a5f2d8389808056902eee30", "result": "valid", "flags": []}, {"tcId": 127, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047bc212053d5d71e3366569cbe405f556919b84f34f0699eb5068354697f63f6f1fdbe5552c39fd3ab545652ad8da80db2130b602a6f23447b0430d71972ce148", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "a403f02418d94512e023e0b4656e392edae0674935e752d11821233d9f8b9049", "result": "valid", "flags": []}, {"tcId": 128, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004487651e08452be0448517bf9d0f05c12c751f4dc44867f580d325464b92efbc31391495825e104161777cfce15355b22e51aa853974636f126d28fad0a84c34b", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "593a63e0515ccfdc9683e359c467719ed2030e57891f8c3e34befb9d6ac12b03", "result": "valid", "flags": []}, {"tcId": 129, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004877bc1db7b1a0d01421b4fe73c714e61db0079e87bb504fc8fce55998de93b3528cf81863e835f66722e43201bfeb8a37bd95485e0d1b7a5134b3bf52b2151ab", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "83d9dc4c116f94ef454336720e2e38c701c042891797db45874b6de852277329", "result": "valid", "flags": []}, {"tcId": 130, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000489214fe2c950e77e503d4c396bedbb82488c5a8f7da767e3a99486acefe62aa38c698caed157d7a5a248304c41d6169e00d3c306bdc07a0663a5e9f6d7f93384", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "94633fae53bfe2b52f9afb58d69b0f2d1ccc462829fa8fbceda76b97cdfaecde", "result": "valid", "flags": []}, {"tcId": 131, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200044cec3002a442dda5fa459cbfd8b110beccec77be82a26f95a8320d99d68698e247ecad54a6042c23346ffdca6c5463f5ff93f4763b0e494cc048aa2ab2857c9e", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "a12f5b82ea93b59d2f3c43f4226033fb3a7a648929d16b5ee351ae7ad8ec64f6", "result": "valid", "flags": []}, {"tcId": 132, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004559d8e5cf359468da72dcd6742ff1af5ea42febf4d6376d9730e6525c26cef105cd0cc3512a644715fd0747dce112d291e09ebd863c70a564fcbc2fcf50f70da", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "81f854b12ced0b0de475fadc30ccb228f1e6abc99a89046d89ebf9f456858ede", "result": "valid", "flags": []}, {"tcId": 133, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000469f14207c8ee3fcceb57b41fa47f45e21588c51959f593f85477262d0080f61502a7e4aa375ebd7e3658825ed6c74f1b1dd867aaccd846ed06672582b05af85d", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "57769adc946ce0b5071f28c90db9e1138f743e87acf0cac31b7d9b371d0285c4", "result": "valid", "flags": []}, {"tcId": 134, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200043856644a00a3d3b4040131bda58078371f8790ea321c86ba5bd7382b8b60ed0a27e647e311a68712370b55ba77888bd3f3adba6a23aeace98172d942c87272c5", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "0e12f6ded6644d6f410731ad68331b81572c34ff90c1cc62709c6ee5db0c7a0a", "result": "valid", "flags": []}, {"tcId": 135, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000437276432794d8bd9b61fb35af8060160e9a56b4d4610992fb41f39c271d5e62064b4f11b727ba123fdf7ded0a6c1f0a624547047af2b087bd64e5685cafc9a0e", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "4d546979234e052de72f4771b1300520f680bc7ee7d44e7cada6afff5508e46a", "result": "valid", "flags": []}, {"tcId": 136, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047f32dd17d93a7bbde57ec57d2bc5a9f68dd2209c54178b4938259217723a6f4e0a3f81514058951e10cf82e5393736c103f683a6576679483d46a71297d4f302", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "3b1807dae4e79ac3c5e48c572ad304d8bd6d71531380c1336bfde5577b6afa30", "result": "valid", "flags": []}, {"tcId": 137, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040972fd984f4a22a78ef6c7f910cadf4616514be2b7053b25df56d7dd93b0e8cd9b2e53332294616b6567e66e43ee9db748fe81e38e5072f96dfe7319e9b17b3f", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "6548a491cd0e47f2b1008691f47adedaccbe9cfbfed63bef0c3810ac3696c087", "result": "valid", "flags": []}, {"tcId": 138, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200042a5ddc0c7dbd53db0aac701a5b4882ad5862b0e4e4d6f3670587e47b84f70d41444399edf0ff23e1fce170e8debad0c020b3c5011c0a60156abe0fc766159d70", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "7cc5cc4367792d2fd9117b56c43ebc3288087556d3ad1d476a7e261db365f090", "result": "valid", "flags": []}, {"tcId": 139, "comment": "point with coordinate x = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200042834c1507442d2aed8b00e596529b154d27ee619276b03f42f206d24190cd8231e6600d123ffebf5ebe8b8e32357b0ace739814ee6fd02abbc4bf3c8d7b3d63e", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "6c990982910fb9df8a3c884e6ec351aaeedacdbfe4f929edf9d50de8a301f09c", "result": "valid", "flags": []}, {"tcId": 140, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200044cb79067b0dd9e22b273103cb7223aa54207202d41914f254a1170a33fa8dae81e06919428aa21bc0edc24086ee135078b571f3b368edbec83fb57e5f10eb781", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "5f8e75e166e6a8eb4710fb1dcaecb8f23782b863d1da0ec61336a4f27c48b18f", "result": "valid", "flags": []}, {"tcId": 141, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040e9d546da7ab23bb7170c9df54f566300c58cda3b0b9b9e8584f2e6001d72f1063f4a41666231d50697d6ecd66d673b7cb2bf2618bc17f531d04ce2e726fadfd", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "3d7e1baa3dc69dc651e39294983832e0e5d97decae95fe706ce88413322f86c8", "result": "valid", "flags": []}, {"tcId": 142, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000413a592b6dab1e1ada18ec44664ce3475346486e1bada9937fc55ac45504af70e33b7251baf7269045b25372b2aa145da3087a7bf0ca75a8fef012e105c31b459", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "844b43dae6cdfc361efe1fc8d9eed67ff857ea6ddbb5d34e087a77da7db3ab4f", "result": "valid", "flags": []}, {"tcId": 143, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000412959aa1bade85d553e8064088b96f3454a6cf54050abd8a2f0b113b821ad46293a915b6adc717e5dcd2956019b72646cbf1007e28e7b2a1e4e9547618acf6c7", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "603b2a294a51d76af6c5118825e6fde2e552668c2471ef179fa97c2e390e9618", "result": "valid", "flags": []}, {"tcId": 144, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040fda4094b8777d0c22dd32765545f275ddea0fa3155b46c2c4146b227ccaac3849c0a1cd7816cf3e5e97d07f58b9819d660000bd17147a002761efe0fd49c075", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "15d59319887a1f66108ae332b7a945d1bc17ac37f8193fee970d727b7621ccb4", "result": "valid", "flags": []}, {"tcId": 145, "comment": "point with coordinate x = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200045bfb60946e4e21f5d1cb397a7ad3a68a680895257cdac5aa2f9b9e351c80af742123eece5c662ee4526b2bc186b6ace8445656ace1733f5ffa2a861a204042b7", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "7550925424a6b211c68cb5cd2e85baf83687438534511ee422fa4c0c1ccabd29", "result": "valid", "flags": []}, {"tcId": 146, "comment": "point with coordinate x = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000495e54fd47dd4e20a2aa72d94b42089730c087b784c75bf317beb9649a883d95a2be6a485b4d1420a8f790c54fc9050126143733eeee7f6de55c4f95ea3c12547", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "7f18805e96a9787e3d691e376e5d2dfc9e60d09bbe603f6e8303a4ba6d82c77d", "result": "valid", "flags": []}, {"tcId": 147, "comment": "point with coordinate x = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040aa99f2b5048e1cc604b46b01880a442661d0e303522973b74196aaff56ad7f45fd33087996abef6756ee13d275c016bab976dc2c77ba7dbab71d44f5eff130d", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "8c9e5ab38b14cde870c413cb5b0754734bc8afd111d5ca9a49ae95ffa6a3e372", "result": "valid", "flags": []}, {"tcId": 148, "comment": "point with coordinate x = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200041da7d036ed78c8231fa1fa4b85c43d4bfd58fba1ba98dbe16f7dcb5a23844f412fa94d3d5332f3bbd418ab34073604b64dd4baceb620d898670d6a0e1883ca7e", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "7e9b7f0bff1bd12abbcf4e369f252f44d0aabb4889514a19f9339775041bc286", "result": "valid", "flags": []}, {"tcId": 149, "comment": "point with coordinate x = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004897ee1bdcb674d36ca7665ab2e3596bc3af736d267a519d99ed7e614a5880c8f8e96a4c62dc3c309fe82691d5acff4481a55643df6c934fa7655761a5c8f2138", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "9467dacf4ace20bbc6287e2384b2cbab17ddf95064a64fe490089f3f6f122e3d", "result": "valid", "flags": []}, {"tcId": 150, "comment": "point with coordinate x = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200041aacd6c801aec88535d0da08f57f0ff4677327c8a8e470e1452b99c9d2244159158681fc2bcf7058027331a71246d546961cd71e325137985cb514547ddb76ba", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "7202665440b2d420d5efa63bc219a8e0ad882c984625e94711914780ce4d8eb5", "result": "valid", "flags": []}, {"tcId": 151, "comment": "point with coordinate y = 1", "public": "305a301406072a8648ce3d020106092b24030302080101070342000424fe5f16209c41136256ce24a1e960bd7829ee2162e4f62f538304c349d5ec170000000000000000000000000000000000000000000000000000000000000001", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "21f687a931090df12bd38028bc0d8cdd1f507dcc37abbf9f655b24f7e13e44e1", "result": "valid", "flags": []}, {"tcId": 152, "comment": "point with coordinate y = 1", "public": "305a301406072a8648ce3d020106092b2403030208010107034200042f265aee84ee784aacd6071cf8cfb8c1d0533251b5bf2d915fd68f221260418914b703ade5c3093d65e383e5c814a513956dbd03b964898914a62dd3cbc8616f", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "4bc4979441380fce9b2873f6754b00fd7c1a78b303584390af64fa3fc0a416c0", "result": "valid", "flags": []}, {"tcId": 153, "comment": "point with coordinate y = 1", "public": "305a301406072a8648ce3d020106092b2403030208010107034200048d943e300c2b988904b445788c3ff7f378d07038b5f2a65ea2e9cad526fd851ea82ac5cbc079ec089014f78b9012d9128dd1bffa88275350e26173efda72d407", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "2f0828c7f7e4cfaa5a9deb0ded0b63130109e0e95f1ff4508efc0c470940c4b1", "result": "valid", "flags": []}, {"tcId": 154, "comment": "point with coordinate y = 1", "public": "305a301406072a8648ce3d020106092b24030302080101070342000412f5ee44fdaf865a072c4be3913475124eb7c50ac52014ee094ee422a46cb6000000000000000000000000000000000000000000000000000000000000000001", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "3394f75e23e3b07de16a9342285915b6328493d5493792d0443130a6399a1b9d", "result": "valid", "flags": []}, {"tcId": 155, "comment": "point with coordinate y = 1", "public": "305a301406072a8648ce3d020106092b2403030208010107034200049f8d4b899fb77f07267750fea8457a286e9963f193b9a0c87f8feb86369d3456779dba5b0936831a3457dc9a8c85b12fc24eb1e58f15aa28d3e9d21a46373a65", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "2d1943326a8613b10e1952661591f92498fdeb722e198ef346d2dcef45a70b8e", "result": "valid", "flags": []}, {"tcId": 156, "comment": "point with coordinate y = 1", "public": "305a301406072a8648ce3d020106092b2403030208010107034200049a213d889a201d871e2790c57adb131f0319f885cddc7c6370b4d2895390f89314ac13f63d8337917febfe131ee5b799364dfd62e198a2fc5723a26fb10bb29d", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "3d39961f5d189c9eaeb99675cefa778091451ab6a86387928fc402d559037869", "result": "valid", "flags": []}, {"tcId": 157, "comment": "point with coordinate y = 1", "public": "305a301406072a8648ce3d020106092b24030302080101070342000472070a8083a2e24ed4e2f0886a65b7a2a75a42f7ad21150ac3415f37312bb1600000000000000000000000000000000000000000000000000000000000000001", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "748b6120056d98a88d16eca4f61f6f6de3526685411ec6bfa2bc2a3358f7e277", "result": "valid", "flags": []}, {"tcId": 158, "comment": "point with coordinate y = 1", "public": "305a301406072a8648ce3d020106092b24030302080101070342000494abf8361354e64c05cae09f35d7a72c752be13258cedcbb565e7b4d3d5e66da7fef178be63fb76a624f438e5d2782619fe5ef4d32c0012fdbabd61646f3fde5", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "3a2b34662f2096d608da6ab9aba42a0b7105a0f90fb974df3eb3350f6e95d7a0", "result": "valid", "flags": []}, {"tcId": 159, "comment": "point with coordinate y = 1", "public": "305a301406072a8648ce3d020106092b240303020801010703420004298b5cfef2a4e2cce0c52cc2b399fa52b9b9611bef825e7fdac0d67d59256d8fa4a150bbed3df86f94391ed88dd796541155a848c7b50c3403bad2e9569c71dd", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "3566e914c8201356094d7077c6da26b1c9e8e4e022e8863155f2826f8dc6ed76", "result": "valid", "flags": []}, {"tcId": 160, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000475849269b7899f1afd7ce505af36bd1392e1fe57dcce83ce1284d0aa3492d124a81f1d17433441e437f96767bd58b951d483e80cbe5ab40f53bbb4aa2df7152f", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "22c0ad9d52e55afccb40c6292bed37c5258263731be04c9aefaf4e1348aa015a", "result": "valid", "flags": []}, {"tcId": 161, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200049445c0a92109e3bb2884c0d3208ab01b4e726e360eb96b9c03d93e30b3c6c14b5b2c356f71395fbd440f8359e497dfb0d82269a099b74be7d46e1b42ad785957", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "80b474d4a9928a125962845b70c398b72c0dac232dcb51fa2873ac7213b94fc4", "result": "valid", "flags": []}, {"tcId": 162, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000472e0e0d685edae12e7eab049a427d4b68d96827fe10a24994708a673c5696c2e2045387e854cda2eb9873455f469ddaffc5a4bb1eccf00aca90ff85190663154", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "0a6718aa034c39b89fa2ec6dedcc169febf0019e7fef3d5a9acc63c4fd3604cc", "result": "valid", "flags": []}, {"tcId": 163, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200041a2c7da12dca058d3053973a6eb22549a18c21e551c278c42207d04452e6ee3f32c4fd148525e9c3820049501811ddfc592d434b85886b56f4b53d09903bf165", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "04e66c5154295b74d7c0bb08834c724fe20b24be864faea3ffe4d39aa765a075", "result": "valid", "flags": []}, {"tcId": 164, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000447c942ebc394e0559e608d101733ef06bc55caf109fa4f0a309164c4668e420b55fa9e65a7b6ba92df1879eb61b84559e3f8f5d5c4bb98dbcd43f3a0d8a22feb", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "2dcbdd347cdf73d7526dc66cede3d6bea9454db1d5aa9c11279cea57790f63b4", "result": "valid", "flags": []}, {"tcId": 165, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000408ec0e29a2bbbf6aa702754951e96c9158d83a9ca3a7cd83ae3a797d671ade021e22f0caabcccb919bc420fdb6b0cae667e2746d21e7de176ef4fb59f9b4a5a3", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "7ad471b35f5fa6eb1de31e1dc68c061531b99163fb79e660e0b878d597fa8e28", "result": "valid", "flags": []}, {"tcId": 166, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004621373590e034e6e3cf023b4020e975965eaee14624d4f1f2d0781b307179ea7618f4bc160c562fedb8f2787f8907c7685269c16ab4e49f394f359fd957355b9", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "21169780718380755c6351abd848311195c2f0784364fef0a94d5edaf20660eb", "result": "valid", "flags": []}, {"tcId": 167, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200045731725e8ccfd44540725e1b43a86d7e2f4865d548354a3578f168b792f787c3343449430c8ef206cccc58d6ba74c9b2337c289b34dc4e7bfac788bb5c6fefd5", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "745608fe1f3b63eb0eb68506575e7f7c23a110b1ce3cf2c42a917b50d41b29f9", "result": "valid", "flags": []}, {"tcId": 168, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000478dd352f74c89e0d4344932480a6eca389df800eee743df6b574549a3103931505ad5329b1eebdd5525c7c157ef6d518c79fda52ee63b14e1b479f7d5d13a027", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "98e2bd2997e99d70a31e7170d2916736cdb87b8641d755e2fafd26a1d3b155e1", "result": "valid", "flags": []}, {"tcId": 169, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a8f61ff6f1fb0b76209ac0926f8d8f4aa165bb5bee9f6e8dd004a6c1655fdf3a5680ee08da27d5ac218b2f278194917b7d95d1ff9be9b0a503c930f29f344dae", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "82a8b2df490294dd06b399332e42132ec6d034e2c2aaa73853a8b7b990289b5b", "result": "valid", "flags": []}, {"tcId": 170, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004778c3625639911f9b02a6ac5618faedf1ea90ee0797d8c59d373e5c7396dc7779a4bcfe6ad247adf667810f2cc7e3b00fea2b1a1ac81b5bc93999f13b2358ac6", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "74c8950b4993e49806e8c8d0c35bb187d5c2d1dc698a664f3a0e676410299d61", "result": "valid", "flags": []}, {"tcId": 171, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004636b5fe1ccd454bbe884af592a21925cd8ae3221bdc1c5fc89ef00a27b2b366da69c2bdd8f251af605e23da49ac77cc55c7a6b03d172a62b96f2e9aa5715b1a1", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "54909d25e9213a9ff7312550551d718d6bd5c385aa05fcb685b9370ab3a309c4", "result": "valid", "flags": []}, {"tcId": 172, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200046e18254f7ad08c2745ecd66680101d3d2102ae978b521946f19f6ed0ebed48f417962642b9925cb80849f789eb637a5d5af43598dd19b6b008d3d9007c79263f", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "13cc36f18461f59b092f9b13b118cd40b7ff0cbefc41ae1508377e969c4d0ac4", "result": "valid", "flags": []}, {"tcId": 173, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200045afc06477f7e173905c6e8ccdbb0c6ff3b85390d4a1165933f0ea134cbf582fe8553311ba4d7a9e001c360504965f87652cb412f7e36d687c18c09f72d5e2d12", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "85ba203d78dcb785ef971690f0e6374bbaaf9adc73a9f636682fb6e8a566392e", "result": "valid", "flags": []}, {"tcId": 174, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000453740ffdca356a221b12f7d1726b2e8c04466ba0e41e0798af800900807bf4f783b85ab8d5adaf79e51da61f140ce5214efdcaa0c4148d515a52f548e2eb3afc", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "1e7e7edae8899e627e5e2b35c990d487d022a75115074327cbd73a535215f71c", "result": "valid", "flags": []}, {"tcId": 175, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200044de11446644cc68f1c13579910cac4d73fbf4a886338191f6759572fd1c564e26384baab5d76d86250c21c1da99d9effc50fb302a6eb883f5305367d7f80e672", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "5e93b13d3959a5d12c79af52febe4a0929bbbcab124435d572659e9013f0b596", "result": "valid", "flags": []}, {"tcId": 176, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000439f085f2457204c631675de86a1afaae3a38efc4b2cd0aeabd5ae1e925d0f6cc940a5700ffdef6fa2078a0bf0e7b372ef34154b7fad13c5b58962783b6e4a515", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "5ffa75246bc1bca4534ba478dca98d8fcfe50d3de0cdfa2c3e2c469a8a173d1a", "result": "valid", "flags": []}, {"tcId": 177, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004408dfdc9c36cfd1494a2a6127b87da00e5fd2e03bfaf4c5f085cbac6393e9b1ba1f7fa3830be778e72455f479584f5826296dfed56d071a809083c65ee3f50d9", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "78d3e04e7065814eaa27a39e8654ebd1e7f6e872608804eea1664788a0b699f0", "result": "valid", "flags": []}, {"tcId": 178, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200044bfcf99cc7a7ba7f61c331da42e07f3bdb0f9e750e77da93962c95dd5643a4268a47942646b3ab874a0e09afc517259e04a6c8001dafd49f4d5cffb95703fda9", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "7a5df56ad0323d83a6baa30a6f74a06f133e62adb2a4c0dd1e5fd5adeb6e51db", "result": "valid", "flags": []}, {"tcId": 179, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200049d2fe9dbc7b2be8a0616e5af37ae2174af9b32f0961589bb05171029bd96d592361941790142abd97041750c4d6d393b543253045bc2009b5bd9f50880a7a1f1", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "09e0393ba53d08a3503fbdb3f1fb0a1eb9c1e85e9286dfa80bacd6de7833fcd2", "result": "valid", "flags": []}, {"tcId": 180, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200041a139903c154ed2794a3ad03fcca58db6f50083db9c20e8903092d8d67c597832c97f97c709983f847ee9bc12005836f27f2e60253c4b0527508c6e6fe828b5b", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "6053a603fe6aaf7e0d96011d7ff6b2ec9449bfcb2850812eadc2ebdba95fc2d3", "result": "valid", "flags": []}, {"tcId": 181, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000491ebd6f77f265ed5349da7f9d613dc8911176e76a30b13302a43fe64acbf5a6c0a0fa3b97332e3599bf68ec081f6208f6f49ae589dbc9335e7352be77ef6a4f9", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "45f3a6557b654db383d725c69189cbbc45e6ccc71b972c696743634b9660e3b1", "result": "valid", "flags": []}, {"tcId": 182, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040e5eb7c5b2d34444bc4d038275c9df2625e7f6cb980edd83f1d52a37c3aaef3331845244428fb6a2c4c269df3ae34774ea09ab8346204866399c9f6645ad97ee", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "63334611ffab29084273a19ab69da589b3a5847efac5bfee53be804f76161d2e", "result": "valid", "flags": []}, {"tcId": 183, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000445c671bdd06ec24da83ca2097e682a58c97a39f23b61ca6122c1585fa5f5627f3493464f20e5b71c7778a9e198fdddce729cf6ee67336231d37bd1b4ec008866", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "7cc77087e2c9b62a6906174ba2e7b5244e86e841f82238b35d8ad3f8d11db32f", "result": "valid", "flags": []}, {"tcId": 184, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200041d8ebe4abcde565bd523874a2804425771b962e7cc5dd990037f61962a8087f19d6375b4d6643b2261edcfbf9812b95839d71d45b46fb4e4abccb0f2cf41b0c0", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "286eb724e42e78d6458c0e11ffc459acbe489d8227858d2c8aa8c1e6bbc6b9bf", "result": "valid", "flags": []}, {"tcId": 185, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200041ca7dbe478eb8e7af3db30980df9b1ac34f10f039b00a5c8d07274b2f6ec401a046d4c05eaf5e77c32a1138a7ec29d9b9db1a977630874532a052d4a87c08d94", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "9b06859cdaecdbe1d5c7d5bf2cabc3c9fc966aa465cca1b895123652995630e9", "result": "valid", "flags": []}, {"tcId": 186, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200045248d89b361aecba3239035df60b8b3bbd707a5036cb99ecc1d8964001cf3db1a15ee715dedcb729737b2714089560061e94232538bdac52887e1f28cc9e5c66", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "79214200530336e4025ec831216c924c89b8b5ea12b11644ca065d86cb96d426", "result": "valid", "flags": []}, {"tcId": 187, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000420bcdd2b86fa7fe38a8b8132dfe5645d68db1de1fb24c14a59cc4c0d7910ec326abf736d4cf534c14be4e00891551017711cde6edd2e8840834316ff7c1bf646", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "0bf8a2aee459b7365907422347247407353c3e8c44d12e0cc1c5f6666b6534bd", "result": "valid", "flags": []}, {"tcId": 188, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000444e9332518f9acf5176bd681ca9a5366dca9c6b78f7a559e60c30fd93be1469e23233d75edc048cd8d7b6d0acf0b57ee7ca85fe6b0caef614366a02fa6620dfa", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "5bbce2b997718b40bd81a14a8d56f840f144bb09d506c3aa27759ef9ad4e897b", "result": "valid", "flags": []}, {"tcId": 189, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200049df694fce405939bc8c1f4bf29c1c5de47f85d761738922f2e1849ec8cdf47884f0715471956b7da830aff5aa832a54539cced866a7d566c64075e56438390e2", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "24f32161a691fd208990f688d87978054155f1009c0308b8f1281d7139b53ed8", "result": "valid", "flags": []}, {"tcId": 190, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004134facb5953a06fde695188b4bc34514dde86067fcafc2e21453240d3983670929a276f34ebeec6c54f18ea655f16889031c5760e643428f6e3649351a8c0d22", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "08d94e8da31eb59ce7adbeb7fbfc4768edf3d40c79854b39eed5a574167ac5f6", "result": "valid", "flags": []}, {"tcId": 191, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a7957634f55b2f066f65df57eb758c55aa7b1c167f8c213f0909e780e4f88a0f3154cc2eb5e1d06f0a973047d5cbabd0b47132df3acef1a99358b899c2066a50", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "27d104bd2fd4b6db63d3c0e1efa5f60ad79e0f2ae5b283e17598390979407a29", "result": "valid", "flags": []}, {"tcId": 192, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a60bb1ea2bf7108f5855f4495448bab9a54d4e950045533134f40021742d04805e7f3ed1a2f4e827d95a126e498f178ebe4a7b80a4c6b4d6782f391e55a324af", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "1f6f8cd2d9e50c471c2fcc975ef817c845b857a16dccd497d2debf80952a28e3", "result": "valid", "flags": []}, {"tcId": 193, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000431a3628efdb75d662903cba8486805f9674203538b6b28a09f9691e20529b5e0812e864d25da1373c0ef7e2d2733e02ef274f6e1c9a407e1d101590f12bd21dc", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "8ce991aca490d0db8de298f85f6f7b58082f685cb38dec4342b4d51bbc90fba1", "result": "valid", "flags": []}, {"tcId": 194, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000435e8b3c3f2293554ed7d71603e0ea58df34b309908d7c1134fb6a436fc5012357fb13177809394d1d821ee0ea42c318dd164d8f28a5ab61da5cfef1cc0ab803a", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "24306fe62609a3c28eb1c8c8b399d5e8474019470925dbe10da3f0e2a034ba30", "result": "valid", "flags": []}, {"tcId": 195, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004265c2960ffe5886c11c20ad0b2f0ddb03906133a2961aec92806805f9c1856af32602a17e1b1bf80300f358a363793c32c7067d952f1583a7d15f755f3f19575", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "4f8d39b1951c00d69ad0e772abbc0dc6ffd708a7f163001c59d116210b216130", "result": "valid", "flags": []}, {"tcId": 196, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200043eed78579999d961470e341dbff52899055fcd2433134ae51208319cb09696a04e89e186a6a8d6cc4dc78b4d5eef30069ce0e4a8c3fe440600f546b45d164a48", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "10639c1156cb1e99b80e76c0175f0bd82a974c46df09cbf1743b338090eb1d6e", "result": "valid", "flags": []}, {"tcId": 197, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200046fd7aeb6f3313906069bd10f502898004e30d3d5b8e231621b99343a922174953d558b8b2d96a5b3bc8753e60c4e6053bc6bd88bc59ff5f80b68adfbb839596c", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "a2d93a9e9c1399ce0595d9a931d3e5f1ed2274da37ce31e795d26b7b9df9a922", "result": "valid", "flags": []}, {"tcId": 198, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200049fa932a5f7256c141a956a24173e7b9c94c5fb04058335fde220da96f9529f1153ce697c6383dfc8502b261911289ac6b4ff31688e4fcc618944bf6298634d6c", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "a21f5b30b503d84f6be490c6fb6fa9721c0ade10fabb5c657845ea1c94083b9b", "result": "valid", "flags": []}, {"tcId": 199, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200041f0affe5dd2ffc551a30e947c9a096ad2915b6c4089b2ec39ae905b1558785cc9c123603a519891d50e13fc284acafec02a4459926c652c794a05392e95c7c69", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "85f2c9101a2c99147c0a9bd8071644e54be209fbcc9c072d4e32d6d7668b41c5", "result": "valid", "flags": []}, {"tcId": 200, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000436cfd03656800ec35ba9492492a671549dbdba26d3dee4ffae6d20a9814996b6a88fd5a0a4fd2dad3dd180e4312f5cbe4ed87d6d1cfbec5b78c0b958c7309636", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "916045c38bea621aaea30a2060635a3fda598a4a74fb7bf93f89d91536e68395", "result": "valid", "flags": []}, {"tcId": 201, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040faf0c019eb161dd3da1f6b331589b7df18dfd71be2184d4c96fa9655c60f288a2b74ac8e9941d5f6a90cb79ea44d58a79bd3ba62476065b44fdcf2bae0d1aaa", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "352fcdeab4faa200b9248894a79750e68599087a33c18514d01d39a807784ab0", "result": "valid", "flags": []}, {"tcId": 202, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200048409b2b16955a5b8c4bae7491b8e7645d99582dea5ab43ce80120ff182b179ee3d119840a5fcf7f5b95c56db4442735847d7bafbedd6f6d1b4fd960d5dc9f9e1", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "33aeb7a6d15423e561d86a1b9361f45f314c7ed039753d091dd113d76a1476b6", "result": "valid", "flags": []}, {"tcId": 203, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000455171fc5ac8ce03c18f51674a7d7d9285e0d8042303442467bdc6b56e9d83fee4b160722cc83c738657a8ef4d1cf35f60d589c309591201832080d8d72d1d24e", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "308783912b4c7773becf7f88241f3cca66fbf5fe004c52fdfb994850437f1309", "result": "valid", "flags": []}, {"tcId": 204, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000454dff75ddc13d69f0dc84d57499ec40ceb53cde6abdade0bb4e9458aa3d4d9a81f2044c926d762ca384e9bcc53e5bfbdcd5b6e61ea9c08be4065179a84356c4c", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "41e361105f1830f76bad53731cf7e151c319b09e78e5553ad87188840db0f3ba", "result": "valid", "flags": []}, {"tcId": 205, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004774ea26d372a38d8200d270dc11af694565b871a909eccfd58f57d9e235577141d1f0fc7474e76ccf47798c2158d3c64d93ab52cfd1c3bc6589f79cce6fef762", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "79159cf32d78d0ea4d51b463a245ef210a5ec046f007cc1e63f15dc8962fcda2", "result": "valid", "flags": []}, {"tcId": 206, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200045bf2455f8bbbf650bbcc8a9cafcdd53604466f855a6bd6c85c7bcdfd8b6250e3550695f4a42ffeace4074009a3c16f93ed520c566c550cceb18a545434f9c132", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "08728da778ce21b21615e85951fd2c8f4c9f6fc68ca9a472faadd5ae1ad5bcc9", "result": "valid", "flags": []}, {"tcId": 207, "comment": "point with coordinate y = 1 in left to right addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200041fc7ca73d208fcd7df0291d32b3199487e932eb20da4c452ebb0265ae110800ca3d24016d1937381869e813126d5f53f40ee6ff78775fd80a2f65b4e89e03b4d", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "a8952235fa4be19eb4de0ed1049d3d1a200fd7151af4fd5e81fdcbf5cc925ed7", "result": "valid", "flags": []}, {"tcId": 208, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200044b8aaec09efaa2458be5e2ae873266b221e43be4126e7c66a053be29e85161f1224be215c2799f8a0c3e41622eb11d0a8c98d9a73a26b71bea59801dbaa5e67d", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "a535269371fc45f7b7df8778f0f9e20ebebbde117ec9e700f3f27202b145dcf6", "result": "valid", "flags": []}, {"tcId": 209, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200042e2f7f0e9e63464affd6be94051b6128c58195f58c14e4de72b01aa76dc20b3c802a95736a31f6adf4c8d3ca402903f7398f5b61d5622050e002fe278e69303f", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "8bdd5eb1bbdb38ebf2069b815db8cf43e262531390b6c39c5d3182452274f331", "result": "valid", "flags": []}, {"tcId": 210, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200044e2cea29825212a35bb6bb59205493341d1d5b00ab7105487ddde2ded25ac7b251a86b9c3e494bb3610c1fce106a089b638779e6ba0cd802063eefefbf84d6d5", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "2eeeb22623e0d249c9cb164ba9bcd8d9e9fee0529f5621aa2ef53992bfb335d7", "result": "valid", "flags": []}, {"tcId": 211, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047971bcfce2be4f84b9ee921e2df2258d2e476e29214aec393c9f459d4877427aa6644f47d6b3cf7f78e5f5a9507fb75bbac44693a622ec8c8ff87588382d5218", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "34e59c5037347a2c250daa40af4ed0fa6b489b58732722f49f3f2d33b680e1f1", "result": "valid", "flags": []}, {"tcId": 212, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000424d970cfebe3b4e3f9618056e34c90338c730af4f6b9eeb2d106e884515757a85d27eae6219445e121cdc06d369a373c034909691f7c80ccee933b1be109d73d", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "2900f9cc5ec773152716e64bcdc04a2ee53ca3b1dc138c4b5b71436e6344ef78", "result": "valid", "flags": []}, {"tcId": 213, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200043b73ef1a5fd07e256ae5cc40392e7cbe20b36de081e828eb77c4b1ccd6d90ff0436c1c2f757276c38264ede00853ce879b2778f14e7b84d7755358f5681db960", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "3047d055f5cf6ae06905b2655c4d8ad3301a3f34a6f1a5bd356c266f95d06708", "result": "valid", "flags": []}, {"tcId": 214, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000448de36ef3f548a2cb7f863d464040e14c286f7e40d102190d50b78524b3329cf4a60bb85b37b1f11308bdf1b9190d2dbc5e80742333075d71cad09aaea31d5ea", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "a814230d305c8215ed52daf558ce3b5896c842f77ef361734448b67c4c947f59", "result": "valid", "flags": []}, {"tcId": 215, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000481ea509d1fa6f49aa4a84af1652cbdb90be821401f144a696a699e7d22faabc48e3026535a28c07c302701340131d6cbd63b6e55e891a1d9df035bed3652e0b9", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "1db476357f5828660eae97ce7b79f199041094a6e9793160b2b784e69d69ca6b", "result": "valid", "flags": []}, {"tcId": 216, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a78dcdbdda58c091ab76e03374e9cbea42fac753e27c02f10f3320c686163e7fa8b030a8d24b1f1341ec17dcd23abf31f865db7c8a8e140d9c7aea6975a33555", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "00fa0cb833104c9dba60306e716de702ca2e33300bbf204c3afef8debc911d6e", "result": "valid", "flags": []}, {"tcId": 217, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004319c113f9d04179bdebc2b96c77f8bd33beefd7f620ce0f715293cc5cd210dc82907828a1daa584b2fad0a7af7d95d53629bae4e1401522fc26dd61b84e56797", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "1c0ce9dc92805206988f2e2b3c4bc458e772b4b6b1e8be09b79b673576029235", "result": "valid", "flags": []}, {"tcId": 218, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047d486de815e8887516e8f53a65621b7953c93f341e9cf0af3b42cf43298c8e57125d9578a3576bebb7c4fb908e3650af165d1945d96b4258bbb39e352aef4fc5", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "59f74bdf57a57933e867588904c4663c8774b1d381f6a8b51cbe2f5c50fa9b48", "result": "valid", "flags": []}, {"tcId": 219, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a1b827d1d6f620b2f997064cb30e52e6a5fea633da877c2e7266ece760856a6f0338a8496cceccf0c6f1fb78176299fec063d4195b7588d07b9c0447eb23f832", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "9111f711f5b9f26e60f14cf61327350c9f1b89ce64eb5909c42a8f9a56296be7", "result": "valid", "flags": []}, {"tcId": 220, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004240445ac1bc534f4e296aeaaed44d0624452c427131cd2c4512e5613046f98c21c426b8b43c3f6efaea6726144953a9533685e922578c3ab1d6b58e6f0dc4566", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "2500a28d622b6a743754333f15982bb1e29260984c4d5fb9d7f683a7c1e2d48b", "result": "valid", "flags": []}, {"tcId": 221, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200042fe368d6bbe7754666d2f0b81442c3e68c665b0b54634c856a30506ad35f5b6622e83c33c1997313bf759144cc9767d14e04c84aa4b0564ba89ab1fd6beff2a0", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "4522ebbd6537d73f0666dcf44e37297bf7c9dd047b3949c58ccfb585506cc628", "result": "valid", "flags": []}, {"tcId": 222, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040b1e4269bbee96b202e33d2ddefed6963718a35a4fb357a95b80c609bcd4d8b31f5ae0dc28cb2ff8fab8294bca63c81153da43a10d3d16e4b50d08d0d9ef2ffd", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "2e1368162b345158a375445d40a19c96eaec3bb92da89fafac7a67842a1af89f", "result": "valid", "flags": []}, {"tcId": 223, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004137bba4b39557ae1a6e87774e63f97dc59a0883cf7efbe779426443091e519a40e765fefca53deb2666fc12c5a01111327d5e9bbf6e0a77bb0b60f23efbd013c", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "26be9691805c8be7563fd688632ad420245b03a3ff7bf72a9adf11678e39acbf", "result": "valid", "flags": []}, {"tcId": 224, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200042bfdd6b9f46db8f0d9676c0e8d980a6b2db0be4f4ca85e5e8607cce29a7aec1f179f0256167c7c48b68acfd6f1fa4a4e156d67a47d2d27fec735832d5e87a473", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "27cc4db172cfd01ecb6075dd036e0c8782acf638027a7ae5f2a5bc50d9776eb6", "result": "valid", "flags": []}, {"tcId": 225, "comment": "point with coordinate y = 1 in precomputation or right to left addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004594db0774135c4cf016e30e992790e6d508e47ae287fd43d4ba60b52a5bb0862997da7cf82637de60ad6c95d948341938008df19d0a140b0fe73429bef20fe9c", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "1a0f4deb41de86a4ca612a8c482caf4adfa538ea2dc3f34fd708054fe01ad9c1", "result": "valid", "flags": []}, {"tcId": 226, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b240303020801010703420004623ab2cc478638a3fb50eb5f0755107bbb46bb05f2448c77da041de8afb312c37ca2fb6b6b90688371a9d6497c553824dd068faa528d92c4a85056a62110148b", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "a575c88115f54c89b9b093d21215018dce872ceab605eb49a44c658253de4c6a", "result": "valid", "flags": []}, {"tcId": 227, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000405bf1d240543c07acce87d4c352b66f2ddd31092f36d956d5607d7c1f8631dfa3640d421ffd3674d4610242087eb58c79229dac37da55e0fa2ed47c5d7903c96", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "970ed466c5b17f04d584251bf215af8263b35226249a58bb9d6652fad215bfb8", "result": "valid", "flags": []}, {"tcId": 228, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200042a730de641b6ba4a827a0845292579faebe8ed866d10d6a5de05f5dea3ad891336328fa32396ed0f64a9cd03dc77d164ba41fb6a807d8f28b372ce862e246029", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "a72ebbb1f6334a44236b8c624205e7d94799573edc7266d7f573d5e850c7d3fc", "result": "valid", "flags": []}, {"tcId": 229, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000481b8cffe032b7f68cbc4df30323e7b2a6dfadcb837e31a6424f29e7accd90d4366289e713a02b0c9e45389ab3342b75f6a8b72f00089a214eab1d8ce9c1eacff", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "96acd3ac412b9b3855a3cab0e0ca9d0c7d0dc86cb4f517a1585b30958aec4b8e", "result": "valid", "flags": []}, {"tcId": 230, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047606be11be87b744dd3d85434cb968ce9b9ef321358dd43273eb7faec1a3f5b81da4d26b1d14e5d39192183c9af4d66599e6b12c6c3d1450c6f1fca8e28b130d", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "3c3dc22d093b9f271e2d76997d50e4096389b4752eb9561330b3ba76cf925f75", "result": "valid", "flags": []}, {"tcId": 231, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200047789d3d7abb6cdacc70700995e2063b8b90318cefa69ab912a8e7bb829f99da08a6078e811002421366a66f751e37d8f65266cd7fd9cd107188d13ce5dc705c8", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "86d34e5a0f28fb3009b9caffa453f4e377e4e7012860f6721c733d14d530359f", "result": "valid", "flags": []}, {"tcId": 232, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000473a1cc1e7ec4080260dcdcc100c5f74a987d546d6717c944db9dc32af355096403ec0ccbea58d65a7932cd2a446b5f6d2008034ea304022669ea63856a54d962", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "281beb20d1850e7e0c0b9d1b9580614995f888cd15b30afe09902f82fce7ed43", "result": "valid", "flags": []}, {"tcId": 233, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200045e29292fa17ccba4e769c53bca35f03166bbb78176f2da749b57513726de49824221c1f014dd14b3db11824c967c13aa77c6a1c22bb069b8a20239cffac8ea3d", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "4a87f45ff159486ccd0e49bd610cc3056b549c54f592d22e2ccba4509be6191f", "result": "valid", "flags": []}, {"tcId": 234, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200045fc1593002ae094bdf2350f918011344f98e0c125af26b30bcb714d01a8a03242b236c70d5d745ba208cea8964beaed88d02d4afdb7d20f09f7ef98ed9934449", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "054882f3b8870d9560294e6ed1cf0b5505811114f1892e137bbda800b303bb50", "result": "valid", "flags": []}, {"tcId": 235, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200048a68a03b1a949e0fd6e78e84c8bbf5d9278f24912b9a25e69e856e7acc4939ab6444dcfc262a0f65780a7d57b2634489e25560b0f0102caccc0e1cb2d73cdb27", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "17ee4b57288e3b571a6f8ad077418f8bc0639bf9377c58eedd544dec353099ba", "result": "valid", "flags": []}, {"tcId": 236, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040f8d7d56ba5e9a1e004e44c41b6e46e5e18f4f179f8897ba0d8bb4a0d21822c101efc288dc7dec62b7773b0b903ebfa406e19fbf514fd0ca2719bbe0ef1a60e3", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "3c005df485bfd1f200d8e5832852656bc40fbb79d858280452458fa61c7dc9a7", "result": "valid", "flags": []}, {"tcId": 237, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000465e5267a280538245dff385e2f1f79e2a05481af4d2251e4258f028bb06d14fb60a163067ff8c12350fbba22f9d45933cbd0ec408e513c7ea21ebbc42cdbb2fa", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "6c58e6dca998187269632b2e11cb69bf4385819014269d16682e7e9f29168bbd", "result": "valid", "flags": []}, {"tcId": 238, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000460bcdd84f5abf37a21014dce7f4d819b45524fb0c59707742b0e9d3a6bbb56cb5e2dbf8c6a65c5067619db7a597fd8aa5f2adbaac5c83d1d7d657d1fd4ff43eb", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "190e9613bebb5ff113e3d8469b8f56f76efc7d182474b752061ee4862613890e", "result": "valid", "flags": []}, {"tcId": 239, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b2403030208010107034200042284aebbac998f34fc1ace78279c7f1e4fc95298899b31dbb345bdcea3d0a35446832c0f0b8517523e6750b11a2407706233e2265714777bd4b8592e50e88dc7", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "42d40fb5e87eb5da66ccb416d5d1eedf5845e488671116768ac70fc36fb1f838", "result": "valid", "flags": []}, {"tcId": 240, "comment": "point with coordinate y = 1 in right to left addition chain", "public": "305a301406072a8648ce3d020106092b24030302080101070342000433a36d7baba659199c29ebf65b8577f25b490525263677cd44a0d2304f89a43d6cf0495a230f69f5d7384146ca91bc097fbc523e2e19db675c19ce6cb357d70e", "private": "0091681129cac74ad9a479e1b015b418e4d5a801b11077f24d56cb485e4684c3a5", "shared": "0d4c7aee3f217cd9ddbd965ac8c82238faa96e68f1197ca3785d6eb649cb1889", "result": "valid", "flags": []}, {"tcId": 241, "comment": "edge case private key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a4597cfee2797aaace662caa92a444592c9f626f04beca98a06b6dfcaf53f4b377d67b1c109154309bcf3d2f3928e58747806f08a8cf88436ac1b2110b83493b", "private": "03", "shared": "a3e1a44851d0dee744378c5e495f3081dff2a0fefccdd08fdc2331687d5c4a80", "result": "valid", "flags": []}, {"tcId": 242, "comment": "edge case private key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a4597cfee2797aaace662caa92a444592c9f626f04beca98a06b6dfcaf53f4b377d67b1c109154309bcf3d2f3928e58747806f08a8cf88436ac1b2110b83493b", "private": "00ffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "shared": "1a68ab756ecae2853fcf6285974efae32ccdd01c58d05b4cc58f74d16c2661fc", "result": "valid", "flags": []}, {"tcId": 243, "comment": "edge case private key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a4597cfee2797aaace662caa92a444592c9f626f04beca98a06b6dfcaf53f4b377d67b1c109154309bcf3d2f3928e58747806f08a8cf88436ac1b2110b83493b", "private": "0100000000000000000000000000000000000000000000000000000000000000", "shared": "0bc5fea1dd6793664ea15dafba3e06c1524150d9fde32e17d7c6813fdc8c8fd3", "result": "valid", "flags": []}, {"tcId": 244, "comment": "edge case private key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a4597cfee2797aaace662caa92a444592c9f626f04beca98a06b6dfcaf53f4b377d67b1c109154309bcf3d2f3928e58747806f08a8cf88436ac1b2110b83493b", "private": "7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff", "shared": "2d44ec4cbf228a4e32409e4de1ef8859c93996a9df73c317e5d04e64137f49dc", "result": "valid", "flags": []}, {"tcId": 245, "comment": "edge case private key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a4597cfee2797aaace662caa92a444592c9f626f04beca98a06b6dfcaf53f4b377d67b1c109154309bcf3d2f3928e58747806f08a8cf88436ac1b2110b83493b", "private": "008000000000000000000000000000000000000000000000000000000000000000", "shared": "9e1990fec2a345118aaa96a473fb921dfb5c3d69c528cf6fc0989d075552822e", "result": "valid", "flags": []}, {"tcId": 246, "comment": "edge case private key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a4597cfee2797aaace662caa92a444592c9f626f04beca98a06b6dfcaf53f4b377d67b1c109154309bcf3d2f3928e58747806f08a8cf88436ac1b2110b83493b", "private": "00a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f6901e0e82974856a7", "shared": "605641e1bd08b22a1a51022d8ef80f9b6c2f70b2562387db88c124ce60660834", "result": "valid", "flags": []}, {"tcId": 247, "comment": "edge case private key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a4597cfee2797aaace662caa92a444592c9f626f04beca98a06b6dfcaf53f4b377d67b1c109154309bcf3d2f3928e58747806f08a8cf88436ac1b2110b83493b", "private": "00a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7900e0e82974856a7", "shared": "7db568a027cd6204457ce31e7859cd06fc76ddbc97d5bdefd57a65752fd79340", "result": "valid", "flags": []}, {"tcId": 248, "comment": "edge case private key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a4597cfee2797aaace662caa92a444592c9f626f04beca98a06b6dfcaf53f4b377d67b1c109154309bcf3d2f3928e58747806f08a8cf88436ac1b2110b83493b", "private": "00a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f790160e82974856a7", "shared": "6878ca1bdd7c0353b505d5a60b2fbed422b5c37c7b54cbed3a22e0894e7b15b8", "result": "valid", "flags": []}, {"tcId": 249, "comment": "edge case private key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a4597cfee2797aaace662caa92a444592c9f626f04beca98a06b6dfcaf53f4b377d67b1c109154309bcf3d2f3928e58747806f08a8cf88436ac1b2110b83493b", "private": "00a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e81974856a7", "shared": "3bc54238c701df3e18c6395f35435059c54ff0d06d8ab19a3d179ab37c1fd8d8", "result": "valid", "flags": []}, {"tcId": 250, "comment": "edge case private key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a4597cfee2797aaace662caa92a444592c9f626f04beca98a06b6dfcaf53f4b377d67b1c109154309bcf3d2f3928e58747806f08a8cf88436ac1b2110b83493b", "private": "00a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974855f5", "shared": "1950b7ce510d4d8648e80c6385a42d005433fc5ca61e2022a1405fe18142c246", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 251, "comment": "edge case private key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a4597cfee2797aaace662caa92a444592c9f626f04beca98a06b6dfcaf53f4b377d67b1c109154309bcf3d2f3928e58747806f08a8cf88436ac1b2110b83493b", "private": "00a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e8297485675", "shared": "5c05c4d877a0e2af5ffa004c122630bb87157cf346dbeb8ae13017162da208f4", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 252, "comment": "edge case private key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a4597cfee2797aaace662caa92a444592c9f626f04beca98a06b6dfcaf53f4b377d67b1c109154309bcf3d2f3928e58747806f08a8cf88436ac1b2110b83493b", "private": "00a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e8297485695", "shared": "9639bbd4e22194ce3892a814c82eddbd21dde05cfac20e99396e3d6ef0841f7c", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 253, "comment": "edge case private key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a4597cfee2797aaace662caa92a444592c9f626f04beca98a06b6dfcaf53f4b377d67b1c109154309bcf3d2f3928e58747806f08a8cf88436ac1b2110b83493b", "private": "00a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a4", "shared": "a3e1a44851d0dee744378c5e495f3081dff2a0fefccdd08fdc2331687d5c4a80", "result": "valid", "flags": []}, {"tcId": 254, "comment": "edge case private key", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a4597cfee2797aaace662caa92a444592c9f626f04beca98a06b6dfcaf53f4b377d67b1c109154309bcf3d2f3928e58747806f08a8cf88436ac1b2110b83493b", "private": "00a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a5", "shared": "341cbdf61f9dd620ba6873a74804afe30a06b0a113a6916a4104d2d4cc196aec", "result": "valid", "flags": ["AddSub<PERSON><PERSON><PERSON>"]}, {"tcId": 255, "comment": "point is not on curve", "public": "305a301406072a8648ce3d020106092b24030302080101070342000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 256, "comment": "point is not on curve", "public": "305a301406072a8648ce3d020106092b24030302080101070342000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 257, "comment": "point is not on curve", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040000000000000000000000000000000000000000000000000000000000000000a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5376", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 258, "comment": "point is not on curve", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040000000000000000000000000000000000000000000000000000000000000000a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 259, "comment": "point is not on curve", "public": "305a301406072a8648ce3d020106092b24030302080101070342000400000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 260, "comment": "point is not on curve", "public": "305a301406072a8648ce3d020106092b24030302080101070342000400000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 261, "comment": "point is not on curve", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040000000000000000000000000000000000000000000000000000000000000001a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5376", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 262, "comment": "point is not on curve", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040000000000000000000000000000000000000000000000000000000000000001a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 263, "comment": "point is not on curve", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e53760000000000000000000000000000000000000000000000000000000000000000", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 264, "comment": "point is not on curve", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e53760000000000000000000000000000000000000000000000000000000000000001", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 265, "comment": "point is not on curve", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5376a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5376", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 266, "comment": "point is not on curve", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5376a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 267, "comment": "point is not on curve", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e53770000000000000000000000000000000000000000000000000000000000000000", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 268, "comment": "point is not on curve", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e53770000000000000000000000000000000000000000000000000000000000000001", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 269, "comment": "point is not on curve", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5376", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 270, "comment": "point is not on curve", "public": "305a301406072a8648ce3d020106092b240303020801010703420004a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 271, "comment": "", "public": "3019301406072a8648ce3d020106092b2403030208010107030100", "private": "0b56c9a5fb87b2090dc62f82c7ddde9d762eebf772640b236041ca71bfc7fb41", "shared": "", "result": "invalid", "flags": []}, {"tcId": 272, "comment": "public point not on curve", "public": "305a301406072a8648ce3d020106092b240303020801010703420004751e0e4636e839f06f4998e14b72809d29031e895e4c7f3c99a9cc131ba39b761db38f944bdaf663a2606109345dc2a5d2f25c5011e164b12cf0408922342e1a", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "", "result": "invalid", "flags": ["InvalidPublic"]}, {"tcId": 273, "comment": "public point = (0,0)", "public": "305a301406072a8648ce3d020106092b24030302080101070342000400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "", "result": "invalid", "flags": ["InvalidPublic"]}, {"tcId": 274, "comment": "order = -76884956397045344220809746629001649092737531784414529538755519063063536359079", "public": "308201333081ec06072a8648ce3d02013081e0020101302c06072a8648ce3d0101022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377304404207d5a0975fc2c3057eef67530417affe7fb8055c126dc5c6ce94a4b44f330b5d9042026dc5c6ce94a4b44f330b5d9bbd77cbf958416295cf7e1ce6bccdc18ff8c07b60441048bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262547ef835c3dac4fd97f8461a14611dc9c27745132ded8e545c1d54c72f0469970221ff5604a8245e115643c199f56f627c728e73c6855c4a9e59086fe1f17d68b7a95902010103420004751e0e4636e839f06f4998e14b72809d29031e895e4c7f3c99a9cc131ba39b761db38f944bdaf663a2606109345dc2a5d2f25c5011e164b12cf0408922342e18", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "59668d33e4163a2e06b8ebac45ef559110d93c6079eb2566f576c6c75fad2657", "result": "invalid", "flags": ["WrongOrder", "InvalidPublic", "UnnamedCurve"]}, {"tcId": 275, "comment": "order = 0", "public": "308201133081cc06072a8648ce3d02013081c0020101302c06072a8648ce3d0101022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377304404207d5a0975fc2c3057eef67530417affe7fb8055c126dc5c6ce94a4b44f330b5d9042026dc5c6ce94a4b44f330b5d9bbd77cbf958416295cf7e1ce6bccdc18ff8c07b60441048bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262547ef835c3dac4fd97f8461a14611dc9c27745132ded8e545c1d54c72f04699702010002010103420004751e0e4636e839f06f4998e14b72809d29031e895e4c7f3c99a9cc131ba39b761db38f944bdaf663a2606109345dc2a5d2f25c5011e164b12cf0408922342e18", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "59668d33e4163a2e06b8ebac45ef559110d93c6079eb2566f576c6c75fad2657", "result": "invalid", "flags": ["WrongOrder", "InvalidPublic", "UnnamedCurve"]}, {"tcId": 276, "comment": "order = 1", "public": "308201133081cc06072a8648ce3d02013081c0020101302c06072a8648ce3d0101022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377304404207d5a0975fc2c3057eef67530417affe7fb8055c126dc5c6ce94a4b44f330b5d9042026dc5c6ce94a4b44f330b5d9bbd77cbf958416295cf7e1ce6bccdc18ff8c07b60441048bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262547ef835c3dac4fd97f8461a14611dc9c27745132ded8e545c1d54c72f04699702010102010103420004751e0e4636e839f06f4998e14b72809d29031e895e4c7f3c99a9cc131ba39b761db38f944bdaf663a2606109345dc2a5d2f25c5011e164b12cf0408922342e18", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "59668d33e4163a2e06b8ebac45ef559110d93c6079eb2566f576c6c75fad2657", "result": "acceptable", "flags": ["WrongOrder", "UnusedParam", "UnnamedCurve"]}, {"tcId": 277, "comment": "order = 17901173885223768702896718547912698493510841341785744190857633730178", "public": "3082012f3081e806072a8648ce3d02013081dc020101302c06072a8648ce3d0101022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377304404207d5a0975fc2c3057eef67530417affe7fb8055c126dc5c6ce94a4b44f330b5d9042026dc5c6ce94a4b44f330b5d9bbd77cbf958416295cf7e1ce6bccdc18ff8c07b60441048bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262547ef835c3dac4fd97f8461a14611dc9c27745132ded8e545c1d54c72f046997021d00a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e8202010103420004751e0e4636e839f06f4998e14b72809d29031e895e4c7f3c99a9cc131ba39b761db38f944bdaf663a2606109345dc2a5d2f25c5011e164b12cf0408922342e18", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "59668d33e4163a2e06b8ebac45ef559110d93c6079eb2566f576c6c75fad2657", "result": "acceptable", "flags": ["WrongOrder", "UnusedParam", "UnnamedCurve"]}, {"tcId": 278, "comment": "generator = (0,0)", "public": "308201333081ec06072a8648ce3d02013081e0020101302c06072a8648ce3d0101022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377304404207d5a0975fc2c3057eef67530417affe7fb8055c126dc5c6ce94a4b44f330b5d9042026dc5c6ce94a4b44f330b5d9bbd77cbf958416295cf7e1ce6bccdc18ff8c07b604410400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a702010103420004751e0e4636e839f06f4998e14b72809d29031e895e4c7f3c99a9cc131ba39b761db38f944bdaf663a2606109345dc2a5d2f25c5011e164b12cf0408922342e18", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "59668d33e4163a2e06b8ebac45ef559110d93c6079eb2566f576c6c75fad2657", "result": "acceptable", "flags": ["UnusedParam", "UnnamedCurve"]}, {"tcId": 279, "comment": "generator not on curve", "public": "308201333081ec06072a8648ce3d02013081e0020101302c06072a8648ce3d0101022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377304404207d5a0975fc2c3057eef67530417affe7fb8055c126dc5c6ce94a4b44f330b5d9042026dc5c6ce94a4b44f330b5d9bbd77cbf958416295cf7e1ce6bccdc18ff8c07b60441048bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262547ef835c3dac4fd97f8461a14611dc9c27745132ded8e545c1d54c72f046999022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a702010103420004751e0e4636e839f06f4998e14b72809d29031e895e4c7f3c99a9cc131ba39b761db38f944bdaf663a2606109345dc2a5d2f25c5011e164b12cf0408922342e18", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "59668d33e4163a2e06b8ebac45ef559110d93c6079eb2566f576c6c75fad2657", "result": "acceptable", "flags": ["UnusedParam", "UnnamedCurve"]}, {"tcId": 280, "comment": "cofactor = -1", "public": "308201333081ec06072a8648ce3d02013081e0020101302c06072a8648ce3d0101022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377304404207d5a0975fc2c3057eef67530417affe7fb8055c126dc5c6ce94a4b44f330b5d9042026dc5c6ce94a4b44f330b5d9bbd77cbf958416295cf7e1ce6bccdc18ff8c07b60441048bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262547ef835c3dac4fd97f8461a14611dc9c27745132ded8e545c1d54c72f046997022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a70201ff03420004751e0e4636e839f06f4998e14b72809d29031e895e4c7f3c99a9cc131ba39b761db38f944bdaf663a2606109345dc2a5d2f25c5011e164b12cf0408922342e18", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "59668d33e4163a2e06b8ebac45ef559110d93c6079eb2566f576c6c75fad2657", "result": "invalid", "flags": ["InvalidPublic", "UnnamedCurve"]}, {"tcId": 281, "comment": "cofactor = 0", "public": "308201333081ec06072a8648ce3d02013081e0020101302c06072a8648ce3d0101022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377304404207d5a0975fc2c3057eef67530417affe7fb8055c126dc5c6ce94a4b44f330b5d9042026dc5c6ce94a4b44f330b5d9bbd77cbf958416295cf7e1ce6bccdc18ff8c07b60441048bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262547ef835c3dac4fd97f8461a14611dc9c27745132ded8e545c1d54c72f046997022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a702010003420004751e0e4636e839f06f4998e14b72809d29031e895e4c7f3c99a9cc131ba39b761db38f944bdaf663a2606109345dc2a5d2f25c5011e164b12cf0408922342e18", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "59668d33e4163a2e06b8ebac45ef559110d93c6079eb2566f576c6c75fad2657", "result": "invalid", "flags": ["InvalidPublic", "UnnamedCurve"]}, {"tcId": 282, "comment": "cofactor = 2", "public": "308201333081ec06072a8648ce3d02013081e0020101302c06072a8648ce3d0101022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377304404207d5a0975fc2c3057eef67530417affe7fb8055c126dc5c6ce94a4b44f330b5d9042026dc5c6ce94a4b44f330b5d9bbd77cbf958416295cf7e1ce6bccdc18ff8c07b60441048bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262547ef835c3dac4fd97f8461a14611dc9c27745132ded8e545c1d54c72f046997022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a702010203420004751e0e4636e839f06f4998e14b72809d29031e895e4c7f3c99a9cc131ba39b761db38f944bdaf663a2606109345dc2a5d2f25c5011e164b12cf0408922342e18", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "59668d33e4163a2e06b8ebac45ef559110d93c6079eb2566f576c6c75fad2657", "result": "acceptable", "flags": ["UnusedParam", "UnnamedCurve"]}, {"tcId": 283, "comment": "cofactor = 76884956397045344220809746629001649092737531784414529538755519063063536359079", "public": "308201553082010d06072a8648ce3d020130820100020101302c06072a8648ce3d0101022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377304404207d5a0975fc2c3057eef67530417affe7fb8055c126dc5c6ce94a4b44f330b5d9042026dc5c6ce94a4b44f330b5d9bbd77cbf958416295cf7e1ce6bccdc18ff8c07b60441048bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262547ef835c3dac4fd97f8461a14611dc9c27745132ded8e545c1d54c72f046997022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a703420004751e0e4636e839f06f4998e14b72809d29031e895e4c7f3c99a9cc131ba39b761db38f944bdaf663a2606109345dc2a5d2f25c5011e164b12cf0408922342e18", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "59668d33e4163a2e06b8ebac45ef559110d93c6079eb2566f576c6c75fad2657", "result": "invalid", "flags": ["InvalidPublic", "UnnamedCurve"]}, {"tcId": 284, "comment": "cofactor = None", "public": "308201303081e906072a8648ce3d02013081dd020101302c06072a8648ce3d0101022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377304404207d5a0975fc2c3057eef67530417affe7fb8055c126dc5c6ce94a4b44f330b5d9042026dc5c6ce94a4b44f330b5d9bbd77cbf958416295cf7e1ce6bccdc18ff8c07b60441048bd2aeb9cb7e57cb2c4b482ffc81b7afb9de27e1e3bd23c23a4453bd9ace3262547ef835c3dac4fd97f8461a14611dc9c27745132ded8e545c1d54c72f046997022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a703420004751e0e4636e839f06f4998e14b72809d29031e895e4c7f3c99a9cc131ba39b761db38f944bdaf663a2606109345dc2a5d2f25c5011e164b12cf0408922342e18", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "59668d33e4163a2e06b8ebac45ef559110d93c6079eb2566f576c6c75fad2657", "result": "acceptable", "flags": ["UnusedParam", "UnnamedCurve"]}, {"tcId": 285, "comment": "modified prime", "public": "308201333081ec06072a8648ce3d02013081e0020101302c06072a8648ce3d01010221009cc5080e320d05229fe633a03f5947a56ae6a469b258ad050ad2d32af932c171304404207d5a0975fc2c3057eef67530417affe7fb8055c126dc5c6ce94a4b44f330b5d9042026dc5c6ce94a4b44f330b5d9bbd77cbf958416295cf7e1ce6bccdc18ff8c07b60441040000000000000000000004d52ab6d699450000000000000000000000000000522f63e0ae9874b3327af5880fd557e161861d66e42d7ad495941804bdc861d30b022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a7020101034200040000000000000000000004d52ab6d699450000000000000000000000000000522f63e0ae9874b3327af5880fd557e161861d66e42d7ad495941804bdc861d30b", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "a696db5b55e4e3aa5ada30aa4acc9b5ccc347e31a2d0b7bc3e7c320eaeaa8d01", "result": "invalid", "flags": ["ModifiedPrime", "InvalidPublic", "UnnamedCurve"]}, {"tcId": 286, "comment": "using secp224r1", "public": "304e301006072a8648ce3d020106052b81040021033a0004074f56dc2ea648ef89c3b72e23bbd2da36f60243e4d2067b70604af1c2165cec2f86603d60c8a611d5b84ba3d91dfe1a480825bcc4af3bcf", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "", "result": "invalid", "flags": ["InvalidPublic"]}, {"tcId": 287, "comment": "using secp256r1", "public": "3059301306072a8648ce3d020106082a8648ce3d03010703420004cbf6606595a3ee50f9fceaa2798c2740c82540516b4e5a7d361ff24e9dd15364e5408b2e679f9d5310d1f6893b36ce16b4a507509175fcb52aea53b781556b39", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "", "result": "invalid", "flags": ["InvalidPublic"]}, {"tcId": 288, "comment": "using secp256k1", "public": "3056301006072a8648ce3d020106052b8104000a03420004a1263e75b87ae0937060ff1472f330ee55cdf8f4329d6284a9ebfbcc856c11684225e72cbebff41e54fb6f00e11afe53a17937bedbf2df787f8ef9584f775838", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "", "result": "invalid", "flags": ["InvalidPublic"]}, {"tcId": 289, "comment": "a = 0", "public": "308201143081cd06072a8648ce3d02013081c1020101302c06072a8648ce3d0101022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e5377302504010004207ec47e457bfa6d6de5c0681c57f6b7c710a28e78eaab3f2d25ae2fb1cafae1ce044104751e0e4636e839f06f4998e14b72809d29031e895e4c7f3c99a9cc131ba39b761db38f944bdaf663a2606109345dc2a5d2f25c5011e164b12cf0408922342e18022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a702010103420004751e0e4636e839f06f4998e14b72809d29031e895e4c7f3c99a9cc131ba39b761db38f944bdaf663a2606109345dc2a5d2f25c5011e164b12cf0408922342e18", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "59668d33e4163a2e06b8ebac45ef559110d93c6079eb2566f576c6c75fad2657", "result": "acceptable", "flags": ["UnusedParam", "UnnamedCurve"]}, {"tcId": 290, "comment": "public key of order 3", "public": "308201333081ec06072a8648ce3d02013081e0020101302c06072a8648ce3d0101022100a9fb57dba1eea9bc3e660a909d838d726e3bf623d52620282013481d1f6e53773044042084042ca67e606ff470a8d56f1ee16172b80f8d359f5e2ddfe7c7b9eaadfd73e504202c4a43dd9a1903d36cb82589db97d3befc0565f50d97ca0803bde2bd5d08c5a2044104565ee81aad8920b6393576cbc832673fdd6e9eb3e6f6a64aab751cf68c380ca885f77f07c303103c062a3fd1533965ff25459eb2537e6ecd42e09baa6b4fd9d6022100a9fb57dba1eea9bc3e660a909d838d718c397aa3b561a6f7901e0e82974856a702010103420004565ee81aad8920b6393576cbc832673fdd6e9eb3e6f6a64aab751cf68c380ca82403d8d3deeb9980383bcabf4a4a277348f6577181a7b15add32ac72b41e79a1", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "", "result": "invalid", "flags": ["WeakPublicKey", "InvalidPublic", "UnnamedCurve"]}, {"tcId": 291, "comment": "public key on isomorphic curve brainpoolP256t1", "public": "305a301406072a8648ce3d020106092b24030302080101080342000481ac2aef36e3d128e4360e715d4885b4d5cdea3b1def7ca2865ff76800e538133be2e51584027d139f6059e254a037f86db12c390fdc3047d3bea6812f19c04d", "private": "0083a8125793c89bec64338abf2db8be8ad5d680ea134645d40c9ee420f3852ebb", "shared": "", "result": "invalid", "flags": ["IsomorphicPublicKey", "InvalidPublic"]}, {"tcId": 292, "comment": "Public key uses wrong curve: secp224r1", "public": "304e301006072a8648ce3d020106052b81040021033a0004debff6a4a9297b3404bd112bb32f212df745b36c698d82f928fad9eb9a9ab56957e44409addfb8b3002f17987032dccecc3375ea8bbc8918", "private": "00898a9c54d72da30e36b42dcf63b37b39c96441dccfcd701b8ed9e0b0c7f6fe7d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 293, "comment": "Public key uses wrong curve: secp256r1", "public": "3059301306072a8648ce3d020106082a8648ce3d030107034200042e0f441d258b51e9f02035b72dc92d19fd7f861b1dc22959c4525aaf7d923ea61fcbb7b5a4fff19b19c316fb2126b9e2325573df3c6886b1d87c8b7ce1e323b0", "private": "00898a9c54d72da30e36b42dcf63b37b39c96441dccfcd701b8ed9e0b0c7f6fe7d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 294, "comment": "Public key uses wrong curve: secp384r1", "public": "3076301006072a8648ce3d020106052b81040022036200045889a117e567fdc7c23cd9a8fc4a87912c7394c76d0436e3ad7eba2c1cd52b4d354a79c2f1e96365e5a19475925f078546cb829455e7e5f09c2c707b962bfc1684581bcdbecc2764d75d98c8fb1f6c1a908d3d7225af157df5891140d30b6e0c", "private": "00898a9c54d72da30e36b42dcf63b37b39c96441dccfcd701b8ed9e0b0c7f6fe7d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 295, "comment": "Public key uses wrong curve: secp521r1", "public": "30819b301006072a8648ce3d020106052b81040023038186000401571fd75d7f8c5ebd5741f8b50575a469f6b429284793ba6dbd57df9f8cc1ae3efa23fd93b69af726cc3758d731c172b9d9db377f28a22ab3463145a3ca89b20d5301b36f5db20089cc7f261ae7fcc4fc0bcdd425e0c80c3d01be586f9092ec64e9c8eeaa4ebe1ede4d0d844a45528d280514ced8ee882348a76d30776b256683dd2a40", "private": "00898a9c54d72da30e36b42dcf63b37b39c96441dccfcd701b8ed9e0b0c7f6fe7d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 296, "comment": "Public key uses wrong curve: secp256k1", "public": "3056301006072a8648ce3d020106052b8104000a03420004628cd86e0686a0f83900ea5650c950a241bcfb585b58591bbd60d474b330ee435d4e5bc111de4e07f4f2ffc40e009b9780a9565c1521da78547ef36192c54cf0", "private": "00898a9c54d72da30e36b42dcf63b37b39c96441dccfcd701b8ed9e0b0c7f6fe7d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 297, "comment": "Public key uses wrong curve: secp224k1", "public": "304e301006072a8648ce3d020106052b81040020033a000486d756e2a21d18a34dc74655ed82d063e3587f99faf16c67ea6060f3aca747711cb13ea08e24066077cba46fb9508c412e649f64836b6156", "private": "00898a9c54d72da30e36b42dcf63b37b39c96441dccfcd701b8ed9e0b0c7f6fe7d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 298, "comment": "Public key uses wrong curve: brainpoolP224r1", "public": "3052301406072a8648ce3d020106092b2403030208010105033a0004bcdd35ef1d7823cae92732fe1994eefa75e69bff629bf1a551f3e07360fd2f612d9631e62da7b82376b9605028768f114f830daeba7e3c22", "private": "00898a9c54d72da30e36b42dcf63b37b39c96441dccfcd701b8ed9e0b0c7f6fe7d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 299, "comment": "Public key uses wrong curve: brainpoolP320r1", "public": "306a301406072a8648ce3d020106092b240303020801010903520004947b72e0343407d82a0fdac7c7f6d410d0196caf9dd5108cced9eff722ab9e4431b0906ff15f3f9fb5ccea8e2c11bc12b8c3d98115f1f326e4594848bd8035bae0fe980ca7b13141f7c244e141c3e578", "private": "00898a9c54d72da30e36b42dcf63b37b39c96441dccfcd701b8ed9e0b0c7f6fe7d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 300, "comment": "Public key uses wrong curve: brainpoolP384r1", "public": "307a301406072a8648ce3d020106092b240303020801010b036200042e8b483b6008ed7d5bdd423ab8e39d68faab00b30c743af1444ef204888684eb6371c4ea9cc0ad803b56abc541f35e4634c48fb32ba6fb975844dfac838cc1bff1286c6ded1f4afe42d68477be54d1ce744cd1fc9cc2e4ddbc897b0dc67a661d", "private": "00898a9c54d72da30e36b42dcf63b37b39c96441dccfcd701b8ed9e0b0c7f6fe7d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 301, "comment": "Public key uses wrong curve: brainpoolP512r1", "public": "30819b301406072a8648ce3d020106092b240303020801010d038182000415172c036cb3cd779740fbdaf26930536c9303e0a8d717916abbb6f010058191e1ee0835b4117c2efe4cbeadd3df260aa34d2417734cc52fd4f22d6fd56a6850927f17c97fa71ee829219c7bfe205872d61eb319b7cfa369715e63c25dc739dc6236cc71779bd4fa61bc34e76290f47b47f2b5430984edf61f4c4325a235024f", "private": "00898a9c54d72da30e36b42dcf63b37b39c96441dccfcd701b8ed9e0b0c7f6fe7d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 302, "comment": "Public key uses wrong curve: brainpoolP224t1", "public": "3052301406072a8648ce3d020106092b2403030208010106033a0004ad19d0abbc921f2f1fb23eca6765b2ba41b9c51e6e90d22a532c6b8c0ce699f758347ea939e79da8a8289403a5f1f8983aa17a70bbc86ca2", "private": "00898a9c54d72da30e36b42dcf63b37b39c96441dccfcd701b8ed9e0b0c7f6fe7d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 303, "comment": "Public key uses wrong curve: brainpoolP256t1", "public": "305a301406072a8648ce3d020106092b24030302080101080342000459e7f5220b13269549d11a1052c9ea852eee4c98df598d0c43824aeaf45340fd5a5aa6950b94d6b055ef7b011a506a2b1277ee50e215efa16246a231f0be64df", "private": "00898a9c54d72da30e36b42dcf63b37b39c96441dccfcd701b8ed9e0b0c7f6fe7d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 304, "comment": "Public key uses wrong curve: brainpoolP320t1", "public": "306a301406072a8648ce3d020106092b240303020801010a035200041a5c93cebdcf7fa5e5c1aedbf463ca7fc7a5a318e7bc281a775aa325c0b2117060c148c4094358fa47b779b11ef3d4b38411cb0a414ace506aa2142f568e141b6a2938122f103d7f849d0763a340e6a0", "private": "00898a9c54d72da30e36b42dcf63b37b39c96441dccfcd701b8ed9e0b0c7f6fe7d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 305, "comment": "Public key uses wrong curve: brainpoolP384t1", "public": "307a301406072a8648ce3d020106092b240303020801010c0362000443e497389d1cc949a87b0bc81b27462b64e8cfe890e2b2d0fadf69c955a39c48d995be3f0597fafafda4719a40e156f761c85d8225dc504c81c6a380f099d82144038fe96bafe0d437d9f3a58a62fe10868aa364fd527f1606ce3c05ad24c653", "private": "00898a9c54d72da30e36b42dcf63b37b39c96441dccfcd701b8ed9e0b0c7f6fe7d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 306, "comment": "Public key uses wrong curve: brainpoolP512t1", "public": "30819b301406072a8648ce3d020106092b240303020801010e0381820004903c2e5edd60b9831e78cb27c7eddedece338a8c82a9dbd31b1d1a78f8f5de37e4986adf56dfef5136fc30a97f675feeb75fc0488630bb32cf6be8a2cf917dfe589cf0ad661f53093e4a172b3cbbfabbc19a3c7a16f6379060eada96b050061d3bcd9577d5ae602456f6deb6fcb65684240ed6d6420630f07bb38be1cd60a339", "private": "00898a9c54d72da30e36b42dcf63b37b39c96441dccfcd701b8ed9e0b0c7f6fe7d", "shared": "", "result": "invalid", "flags": []}, {"tcId": 307, "comment": "invalid public key", "public": "303a301406072a8648ce3d020106092b2403030208010107032200029cf25745a7ec04b3c00e795c7e8f8d7da33f55732003be4fd4094d842ea82d8c", "private": "25d405a46ccd1e34658b9be7423e8fce7a997120db963933545d19762c71d8dd", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 308, "comment": "public key is a low order point on twist", "public": "303a301406072a8648ce3d020106092b2403030208010107032200020a8944f96de0fe0d82489cbc7e71f2f529cfcfea03ca593d91462278731e19a5", "private": "66008d35e34ab5f875c3fd8115c335cd9b5f764323dbe44570960155570e71db", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 309, "comment": "public key is a low order point on twist", "public": "303a301406072a8648ce3d020106092b240303020801010703220002575862a7c5fc68e9ffcd58ce9bd0ef78c0a26a3a22ee96b0be16b399adb3ac4a", "private": "622853938bb5dc82716cd8d6d076ae6955da307bb14534ae56fd6dfefd5ed270", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 310, "comment": "public key is a low order point on twist", "public": "303a301406072a8648ce3d020106092b240303020801010703220003575862a7c5fc68e9ffcd58ce9bd0ef78c0a26a3a22ee96b0be16b399adb3ac4a", "private": "622853938bb5dc82716cd8d6d076ae6955da307bb14534ae56fd6dfefd5ed26f", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 311, "comment": "public key is a low order point on twist", "public": "303a301406072a8648ce3d020106092b2403030208010107032200030a8944f96de0fe0d82489cbc7e71f2f529cfcfea03ca593d91462278731e19a5", "private": "66008d35e34ab5f875c3fd8115c335cd9b5f764323dbe44570960155570e71da", "shared": "", "result": "invalid", "flags": ["CompressedPoint"]}, {"tcId": 312, "comment": "long form encoding of length of sequence", "public": "30815a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 313, "comment": "long form encoding of length of sequence", "public": "305b30811406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 314, "comment": "length of sequence contains leading 0", "public": "3082005a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 315, "comment": "length of sequence contains leading 0", "public": "305c3082001406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 316, "comment": "wrong length of sequence", "public": "305b301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 317, "comment": "wrong length of sequence", "public": "3059301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 318, "comment": "wrong length of sequence", "public": "305a301506072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 319, "comment": "wrong length of sequence", "public": "305a301306072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 320, "comment": "uint32 overflow in length of sequence", "public": "3085010000005a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 321, "comment": "uint32 overflow in length of sequence", "public": "305f3085010000001406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 322, "comment": "uint64 overflow in length of sequence", "public": "308901000000000000005a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 323, "comment": "uint64 overflow in length of sequence", "public": "3063308901000000000000001406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 324, "comment": "length of sequence = 2**31 - 1", "public": "30847fffffff301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 325, "comment": "length of sequence = 2**31 - 1", "public": "305e30847fffffff06072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 326, "comment": "length of sequence = 2**32 - 1", "public": "3084ffffffff301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 327, "comment": "length of sequence = 2**32 - 1", "public": "305e3084ffffffff06072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 328, "comment": "length of sequence = 2**40 - 1", "public": "3085ffffffffff301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 329, "comment": "length of sequence = 2**40 - 1", "public": "305f3085ffffffffff06072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 330, "comment": "length of sequence = 2**64 - 1", "public": "3088ffffffffffffffff301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 331, "comment": "length of sequence = 2**64 - 1", "public": "30623088ffffffffffffffff06072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 332, "comment": "incorrect length of sequence", "public": "30ff301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 333, "comment": "incorrect length of sequence", "public": "305a30ff06072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 334, "comment": "indefinite length without termination", "public": "3080301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 335, "comment": "indefinite length without termination", "public": "305a308006072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 336, "comment": "indefinite length without termination", "public": "305a301406802a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 337, "comment": "indefinite length without termination", "public": "305a301406072a8648ce3d020106802b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 338, "comment": "indefinite length without termination", "public": "305a301406072a8648ce3d020106092b2403030208010107038000040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 339, "comment": "removing sequence", "public": "", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 340, "comment": "removing sequence", "public": "3044034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 341, "comment": "lonely sequence tag", "public": "30", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 342, "comment": "lonely sequence tag", "public": "304530034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 343, "comment": "appending 0's to sequence", "public": "305c301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b70000", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 344, "comment": "appending 0's to sequence", "public": "305c301606072a8648ce3d020106092b24030302080101070000034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 345, "comment": "prepending 0's to sequence", "public": "305c0000301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 346, "comment": "prepending 0's to sequence", "public": "305c3016000006072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 347, "comment": "appending unused 0's to sequence", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b70000", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 348, "comment": "appending unused 0's to sequence", "public": "305c301406072a8648ce3d020106092b24030302080101070000034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 349, "comment": "appending null value to sequence", "public": "305c301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b70500", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 350, "comment": "appending null value to sequence", "public": "305c301606072a8648ce3d020106092b24030302080101070500034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 351, "comment": "including garbage", "public": "305f498177305a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 352, "comment": "including garbage", "public": "305e2500305a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 353, "comment": "including garbage", "public": "305c305a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b70004deadbeef", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 354, "comment": "including garbage", "public": "305f3019498177301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 355, "comment": "including garbage", "public": "305e30182500301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 356, "comment": "including garbage", "public": "30623016301406072a8648ce3d020106092b24030302080101070004deadbeef034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 357, "comment": "including garbage", "public": "305f3019260c49817706072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 358, "comment": "including garbage", "public": "305e3018260b250006072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 359, "comment": "including garbage", "public": "3062301c260906072a8648ce3d02010004deadbeef06092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 360, "comment": "including garbage", "public": "305f301906072a8648ce3d0201260e49817706092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 361, "comment": "including garbage", "public": "305e301806072a8648ce3d0201260d250006092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 362, "comment": "including garbage", "public": "3062301c06072a8648ce3d0201260b06092b24030302080101070004deadbeef034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 363, "comment": "including garbage", "public": "305f301406072a8648ce3d020106092b24030302080101072347498177034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 364, "comment": "including garbage", "public": "305e301406072a8648ce3d020106092b240303020801010723462500034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 365, "comment": "including garbage", "public": "3062301406072a8648ce3d020106092b24030302080101072344034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b70004deadbeef", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 366, "comment": "including undefined tags", "public": "3062aa00bb00cd00305a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 367, "comment": "including undefined tags", "public": "3060aa02aabb305a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 368, "comment": "including undefined tags", "public": "3062301caa00bb00cd00301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 369, "comment": "including undefined tags", "public": "3060301aaa02aabb301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 370, "comment": "including undefined tags", "public": "3062301c260faa00bb00cd0006072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 371, "comment": "including undefined tags", "public": "3060301a260daa02aabb06072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 372, "comment": "including undefined tags", "public": "3062301c06072a8648ce3d02012611aa00bb00cd0006092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 373, "comment": "including undefined tags", "public": "3060301a06072a8648ce3d0201260faa02aabb06092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 374, "comment": "including undefined tags", "public": "3062301406072a8648ce3d020106092b2403030208010107234aaa00bb00cd00034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 375, "comment": "including undefined tags", "public": "3060301406072a8648ce3d020106092b24030302080101072348aa02aabb034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 376, "comment": "truncated length of sequence", "public": "3081", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 377, "comment": "truncated length of sequence", "public": "30463081034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 378, "comment": "Replacing sequence with NULL", "public": "0500", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 379, "comment": "Replacing sequence with NULL", "public": "30460500034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 380, "comment": "changing tag value of sequence", "public": "2e5a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 381, "comment": "changing tag value of sequence", "public": "2f5a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 382, "comment": "changing tag value of sequence", "public": "315a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 383, "comment": "changing tag value of sequence", "public": "325a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 384, "comment": "changing tag value of sequence", "public": "ff5a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 385, "comment": "changing tag value of sequence", "public": "305a2e1406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 386, "comment": "changing tag value of sequence", "public": "305a2f1406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 387, "comment": "changing tag value of sequence", "public": "305a311406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 388, "comment": "changing tag value of sequence", "public": "305a321406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 389, "comment": "changing tag value of sequence", "public": "305aff1406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 390, "comment": "dropping value of sequence", "public": "3000", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 391, "comment": "dropping value of sequence", "public": "30463000034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 392, "comment": "truncated sequence", "public": "3059301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 393, "comment": "truncated sequence", "public": "30591406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 394, "comment": "truncated sequence", "public": "3059301306072a8648ce3d020106092b24030302080101034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 395, "comment": "truncated sequence", "public": "30593013072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 396, "comment": "indefinite length", "public": "3080301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b70000", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 397, "comment": "indefinite length", "public": "305c308006072a8648ce3d020106092b24030302080101070000034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 398, "comment": "indefinite length with truncated delimiter", "public": "3080301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b700", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 399, "comment": "indefinite length with truncated delimiter", "public": "305b308006072a8648ce3d020106092b240303020801010700034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 400, "comment": "indefinite length with additional element", "public": "3080301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b705000000", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 401, "comment": "indefinite length with additional element", "public": "305e308006072a8648ce3d020106092b240303020801010705000000034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 402, "comment": "indefinite length with truncated element", "public": "3080301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7060811220000", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 403, "comment": "indefinite length with truncated element", "public": "3060308006072a8648ce3d020106092b2403030208010107060811220000034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 404, "comment": "indefinite length with garbage", "public": "3080301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b70000fe02beef", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 405, "comment": "indefinite length with garbage", "public": "3060308006072a8648ce3d020106092b24030302080101070000fe02beef034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 406, "comment": "indefinite length with nonempty EOC", "public": "3080301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b70002beef", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 407, "comment": "indefinite length with nonempty EOC", "public": "305e308006072a8648ce3d020106092b24030302080101070002beef034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 408, "comment": "prepend empty sequence", "public": "305c3000301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 409, "comment": "prepend empty sequence", "public": "305c3016300006072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 410, "comment": "append empty sequence", "public": "305c301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b73000", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 411, "comment": "append empty sequence", "public": "305c301606072a8648ce3d020106092b24030302080101073000034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 412, "comment": "append garbage with high tag number", "public": "305d301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7bf7f00", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 413, "comment": "append garbage with high tag number", "public": "305d301706072a8648ce3d020106092b2403030208010107bf7f00034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 414, "comment": "sequence of sequence", "public": "305c305a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 415, "comment": "sequence of sequence", "public": "305c3016301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 416, "comment": "truncated sequence: removed last 1 elements", "public": "3016301406072a8648ce3d020106092b2403030208010107", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 417, "comment": "truncated sequence: removed last 1 elements", "public": "304f300906072a8648ce3d0201034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 418, "comment": "repeating element in sequence", "public": "30819e301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 419, "comment": "repeating element in sequence", "public": "3065301f06072a8648ce3d020106092b240303020801010706092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 420, "comment": "long form encoding of length of oid", "public": "305b30150681072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 421, "comment": "long form encoding of length of oid", "public": "305b301506072a8648ce3d02010681092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 422, "comment": "length of oid contains leading 0", "public": "305c3016068200072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 423, "comment": "length of oid contains leading 0", "public": "305c301606072a8648ce3d0201068200092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 424, "comment": "wrong length of oid", "public": "305a301406082a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 425, "comment": "wrong length of oid", "public": "305a301406062a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 426, "comment": "wrong length of oid", "public": "305a301406072a8648ce3d0201060a2b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 427, "comment": "wrong length of oid", "public": "305a301406072a8648ce3d020106082b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 428, "comment": "uint32 overflow in length of oid", "public": "305f3019068501000000072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 429, "comment": "uint32 overflow in length of oid", "public": "305f301906072a8648ce3d0201068501000000092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 430, "comment": "uint64 overflow in length of oid", "public": "3063301d06890100000000000000072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 431, "comment": "uint64 overflow in length of oid", "public": "3063301d06072a8648ce3d020106890100000000000000092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 432, "comment": "length of oid = 2**31 - 1", "public": "305e301806847fffffff2a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 433, "comment": "length of oid = 2**31 - 1", "public": "305e301806072a8648ce3d020106847fffffff2b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 434, "comment": "length of oid = 2**32 - 1", "public": "305e30180684ffffffff2a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 435, "comment": "length of oid = 2**32 - 1", "public": "305e301806072a8648ce3d02010684ffffffff2b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 436, "comment": "length of oid = 2**40 - 1", "public": "305f30190685ffffffffff2a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 437, "comment": "length of oid = 2**40 - 1", "public": "305f301906072a8648ce3d02010685ffffffffff2b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 438, "comment": "length of oid = 2**64 - 1", "public": "3062301c0688ffffffffffffffff2a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 439, "comment": "length of oid = 2**64 - 1", "public": "3062301c06072a8648ce3d02010688ffffffffffffffff2b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 440, "comment": "incorrect length of oid", "public": "305a301406ff2a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 441, "comment": "incorrect length of oid", "public": "305a301406072a8648ce3d020106ff2b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 442, "comment": "removing oid", "public": "3051300b06092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 443, "comment": "lonely oid tag", "public": "3052300c0606092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 444, "comment": "lonely oid tag", "public": "3050300a06072a8648ce3d020106034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 445, "comment": "appending 0's to oid", "public": "305c301606092a8648ce3d0201000006092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 446, "comment": "appending 0's to oid", "public": "305c301606072a8648ce3d0201060b2b24030302080101070000034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 447, "comment": "prepending 0's to oid", "public": "305c3016060900002a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 448, "comment": "prepending 0's to oid", "public": "305c301606072a8648ce3d0201060b00002b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 449, "comment": "appending unused 0's to oid", "public": "305c301606072a8648ce3d0201000006092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 450, "comment": "appending null value to oid", "public": "305c301606092a8648ce3d0201050006092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 451, "comment": "appending null value to oid", "public": "305c301606072a8648ce3d0201060b2b24030302080101070500034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 452, "comment": "truncated length of oid", "public": "3053300d068106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 453, "comment": "truncated length of oid", "public": "3051300b06072a8648ce3d02010681034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 454, "comment": "Replacing oid with NULL", "public": "3053300d050006092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 455, "comment": "Replacing oid with NULL", "public": "3051300b06072a8648ce3d02010500034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 456, "comment": "changing tag value of oid", "public": "305a301404072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 457, "comment": "changing tag value of oid", "public": "305a301405072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 458, "comment": "changing tag value of oid", "public": "305a301407072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 459, "comment": "changing tag value of oid", "public": "305a301408072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 460, "comment": "changing tag value of oid", "public": "305a3014ff072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 461, "comment": "changing tag value of oid", "public": "305a301406072a8648ce3d020104092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 462, "comment": "changing tag value of oid", "public": "305a301406072a8648ce3d020105092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 463, "comment": "changing tag value of oid", "public": "305a301406072a8648ce3d020107092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 464, "comment": "changing tag value of oid", "public": "305a301406072a8648ce3d020108092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 465, "comment": "changing tag value of oid", "public": "305a301406072a8648ce3d0201ff092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 466, "comment": "dropping value of oid", "public": "3053300d060006092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 467, "comment": "dropping value of oid", "public": "3051300b06072a8648ce3d02010600034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 468, "comment": "modify first byte of oid", "public": "305a30140607288648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 469, "comment": "modify first byte of oid", "public": "305a301406072a8648ce3d02010609292403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 470, "comment": "modify last byte of oid", "public": "305a301406072a8648ce3d028106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 471, "comment": "modify last byte of oid", "public": "305a301406072a8648ce3d020106092b2403030208010187034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 472, "comment": "truncated oid", "public": "3059301306062a8648ce3d0206092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 473, "comment": "truncated oid", "public": "3059301306068648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 474, "comment": "truncated oid", "public": "3059301306072a8648ce3d020106082b24030302080101034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 475, "comment": "truncated oid", "public": "3059301306072a8648ce3d020106082403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 476, "comment": "wrong oid", "public": "3058301206052b0e03021a06092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 477, "comment": "wrong oid", "public": "305c3016060960864801650304020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 478, "comment": "wrong oid", "public": "3056301006072a8648ce3d020106052b0e03021a034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 479, "comment": "wrong oid", "public": "305a301406072a8648ce3d02010609608648016503040201034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 480, "comment": "longer oid", "public": "305b301506082a8648ce3d02010106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 481, "comment": "longer oid", "public": "305b301506072a8648ce3d0201060a2b240303020801010701034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 482, "comment": "oid with modified node", "public": "305a301406072a8648ce3d021106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 483, "comment": "oid with modified node", "public": "305e3018060b2a8648ce3d02888080800106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 484, "comment": "oid with modified node", "public": "305a301406072a8648ce3d020106092b2403030208010117034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 485, "comment": "oid with modified node", "public": "305e301806072a8648ce3d0201060d2b240303020801018880808007034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 486, "comment": "large integer in oid", "public": "3063301d06102a8648ce3d028280808080808080800106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 487, "comment": "large integer in oid", "public": "3063301d06072a8648ce3d020106122b2403030208010182808080808080808007034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 488, "comment": "oid with invalid node", "public": "305b301506082a8648ce3d0201e006092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 489, "comment": "oid with invalid node", "public": "305b301506082a808648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 490, "comment": "oid with invalid node", "public": "305b301506072a8648ce3d0201060a2b2403030208010107e0034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 491, "comment": "oid with invalid node", "public": "305b301506072a8648ce3d0201060a2b802403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 492, "comment": "long form encoding of length of bit string", "public": "305b301406072a8648ce3d020106092b240303020801010703814200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 493, "comment": "length of bit string contains leading 0", "public": "305c301406072a8648ce3d020106092b24030302080101070382004200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 494, "comment": "wrong length of bit string", "public": "305a301406072a8648ce3d020106092b2403030208010107034300040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 495, "comment": "wrong length of bit string", "public": "305a301406072a8648ce3d020106092b2403030208010107034100040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 496, "comment": "uint32 overflow in length of bit string", "public": "305f301406072a8648ce3d020106092b24030302080101070385010000004200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 497, "comment": "uint64 overflow in length of bit string", "public": "3063301406072a8648ce3d020106092b2403030208010107038901000000000000004200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 498, "comment": "length of bit string = 2**31 - 1", "public": "305e301406072a8648ce3d020106092b240303020801010703847fffffff00040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 499, "comment": "length of bit string = 2**32 - 1", "public": "305e301406072a8648ce3d020106092b24030302080101070384ffffffff00040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 500, "comment": "length of bit string = 2**40 - 1", "public": "305f301406072a8648ce3d020106092b24030302080101070385ffffffffff00040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 501, "comment": "length of bit string = 2**64 - 1", "public": "3062301406072a8648ce3d020106092b24030302080101070388ffffffffffffffff00040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 502, "comment": "incorrect length of bit string", "public": "305a301406072a8648ce3d020106092b240303020801010703ff00040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 503, "comment": "lonely bit string tag", "public": "3017301406072a8648ce3d020106092b240303020801010703", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 504, "comment": "appending 0's to bit string", "public": "305c301406072a8648ce3d020106092b2403030208010107034400040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b70000", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 505, "comment": "prepending 0's to bit string", "public": "305c301406072a8648ce3d020106092b24030302080101070344000000040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 506, "comment": "appending null value to bit string", "public": "305c301406072a8648ce3d020106092b2403030208010107034400040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b70500", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 507, "comment": "truncated length of bit string", "public": "3018301406072a8648ce3d020106092b24030302080101070381", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 508, "comment": "Replacing bit string with NULL", "public": "3018301406072a8648ce3d020106092b24030302080101070500", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 509, "comment": "changing tag value of bit string", "public": "305a301406072a8648ce3d020106092b2403030208010107014200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 510, "comment": "changing tag value of bit string", "public": "305a301406072a8648ce3d020106092b2403030208010107024200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 511, "comment": "changing tag value of bit string", "public": "305a301406072a8648ce3d020106092b2403030208010107044200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 512, "comment": "changing tag value of bit string", "public": "305a301406072a8648ce3d020106092b2403030208010107054200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 513, "comment": "changing tag value of bit string", "public": "305a301406072a8648ce3d020106092b2403030208010107ff4200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 514, "comment": "dropping value of bit string", "public": "3018301406072a8648ce3d020106092b24030302080101070300", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 515, "comment": "modify first byte of bit string", "public": "305a301406072a8648ce3d020106092b2403030208010107034202040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 516, "comment": "modify last byte of bit string", "public": "305a301406072a8648ce3d020106092b2403030208010107034200040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e637", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 517, "comment": "truncated bit string", "public": "3059301406072a8648ce3d020106092b2403030208010107034100040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 518, "comment": "truncated bit string", "public": "3059301406072a8648ce3d020106092b24030302080101070341040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 519, "comment": "declaring bits as unused in bit string", "public": "305a301406072a8648ce3d020106092b2403030208010107034201040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 520, "comment": "unused bits in bit string", "public": "305e301406072a8648ce3d020106092b2403030208010107034620040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b701020304", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 521, "comment": "unused bits in empty bit-string", "public": "3019301406072a8648ce3d020106092b2403030208010107030103", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}, {"tcId": 522, "comment": "128 unused bits", "public": "305a301406072a8648ce3d020106092b2403030208010107034280040bc9c96c6a1a53a682d047de92f68503d8183dbfd0289a23f122328a02139eb470f35f5d9f0719466138c0f03af1832cae9faffdc57b23fdd323a4a1cd99e6b7", "private": "06886a22d99d63e4def932f8c050560452c110975f8cffee6482df732fd11696", "shared": "2b55ca46c52f0d48efc170194f6fccd38fe9505f2765f64232eef3af9ed42a83", "result": "acceptable", "flags": ["InvalidAsn"]}]}]}