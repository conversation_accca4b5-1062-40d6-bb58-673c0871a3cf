import json
import os
import time
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from urllib.parse import quote_plus
import uuid
import requests
from base_plugin import BasePlugin, Hook<PERSON><PERSON><PERSON>, HookStrategy
from android_utils import run_on_ui_thread
from client_utils import run_on_queue, get_last_fragment, send_message, get_send_messages_helper
from ui.settings import Header, Input, Divider, Text, Selector
from ui.alert import AlertDialogBuilder
from ui.bulletin import BulletinHelper
from markdown_utils import parse_markdown
from java.io import File
from java.util import ArrayList
from org.telegram.messenger import ApplicationLoader, AndroidUtilities
from org.telegram.tgnet import TLRPC

__id__ = "Message_Tracker"
__name__ = "🚀Message Tracker🚀"
__description__ = "Отслеживает количество сообщений, отправленных вами за выбранный период и показывает статистику. Доступно 2 вида: Статистика-сообщение and статистика-диаграмма"
__author__ = "@SaturnFake"
__version__ = "1.15.0"
__icon__ = "error/11"
__min_version__ = "11.12.0"

MAX_HISTORY_DAYS = 365

PIE_COLORS = [
    "rgba(255, 99, 132, 0.8)", "rgba(54, 162, 235, 0.8)", "rgba(255, 206, 86, 0.8)",
    "rgba(75, 192, 192, 0.8)", "rgba(153, 102, 255, 0.8)", "rgba(255, 159, 64, 0.8)",
    "rgba(199, 199, 199, 0.8)", "rgba(83, 102, 255, 0.8)", "rgba(255, 102, 102, 0.8)",
    "rgba(102, 255, 102, 0.8)", "rgba(102, 102, 255, 0.8)", "rgba(255, 102, 255, 0.8)",
    "rgba(102, 255, 255, 0.8)", "rgba(255, 255, 102, 0.8)", "rgba(128, 0, 0, 0.8)",
    "rgba(0, 128, 0, 0.8)", "rgba(0, 0, 128, 0.8)", "rgba(128, 128, 0, 0.8)",
    "rgba(128, 0, 128, 0.8)", "rgba(0, 128, 128, 0.8)", "rgba(200, 50, 100, 0.8)",
    "rgba(50, 200, 100, 0.8)", "rgba(100, 50, 200, 0.8)", "rgba(200, 100, 50, 0.8)",
    "rgba(50, 100, 200, 0.8)", "rgba(100, 200, 50, 0.8)", "rgba(220, 180, 0, 0.8)",
    "rgba(0, 220, 180, 0.8)", "rgba(180, 0, 220, 0.8)", "rgba(220, 0, 180, 0.8)",
    "rgba(0, 200, 0, 0.8)", "rgba(0, 0, 200, 0.8)", "rgba(200, 200, 0, 0.8)",
    "rgba(200, 0, 200, 0.8)", "rgba(0, 200, 200, 0.8)", "rgba(150, 150, 150, 0.8)",
    "rgba(240, 128, 128, 0.8)", "rgba(128, 240, 128, 0.8)", "rgba(128, 128, 240, 0.8)",
    "rgba(255, 165, 0, 0.8)", "rgba(173, 216, 230, 0.8)", "rgba(144, 238, 144, 0.8)",
    "rgba(255, 215, 0, 0.8)", "rgba(135, 206, 250, 0.8)", "rgba(255, 192, 203, 0.8)",
    "rgba(60, 179, 113, 0.8)", "rgba(218, 112, 214, 0.8)", "rgba(255, 69, 0, 0.8)",
    "rgba(106, 90, 205, 0.8)", "rgba(0, 191, 255, 0.8)", "rgba(175, 238, 238, 0.8)",
    "rgba(255, 228, 196, 0.8)", "rgba(189, 183, 107, 0.8)", "rgba(160, 82, 45, 0.8)"
]

class MessageTrackerPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.message_counts: Dict[str, int] = {}
        self.custom_st_command: str = ".m"
        self.custom_chart_command: str = ".d"
        self.custom_settings_command: str = ".set"
        self.timeframe_days: int = 7
        self.chart_type_map = {
            "bar": {"display": "Вертикальная гистограмма", "chart_js_type": "bar"},
            "horizontalBar": {"display": "Горизонтальная гистограмма", "chart_js_type": "horizontalBar"},
            "line": {"display": "Линейная", "chart_js_type": "line"},
            "pie": {"display": "Круговая", "chart_js_type": "pie"},
            "doughnut": {"display": "Кольцевая", "chart_js_type": "doughnut"},
            "radar": {"display": "Лепестковая (радар)", "chart_js_type": "radar"},
            "polarArea": {"display": "Полярная область", "chart_js_type": "polarArea"},
            "scatter": {"display": "Точечная (Scatter)", "chart_js_type": "scatter"},
            "bubble": {"display": "Пузырьковая (Bubble)", "chart_js_type": "bubble"}
        }
        self.chart_type_display_names = [v["display"] for v in self.chart_type_map.values()]
        self.chart_type_js_names = [v["chart_js_type"] for v in self.chart_type_map.values()]
        self.selected_chart_type_index: int = 0
        self.selected_chart_type_js_name: str = "bar"

        self.progress_dialog: Optional[AlertDialogBuilder] = None
        self.awaiting_command_input: Optional[Dict[str, Any]] = None 

    def on_plugin_load(self):
        self.load_settings()
        self.add_on_send_message_hook()

    def on_plugin_unload(self):
        if self.progress_dialog:
            run_on_ui_thread(lambda: self.progress_dialog.dismiss())
            self.progress_dialog = None
        self.awaiting_command_input = None

    def load_settings(self):
        counts_str = self.get_setting("message_counts_json", "{}")
        try:
            self.message_counts = json.loads(counts_str)
            self.message_counts = {k: int(v) for k, v in self.message_counts.items()}
        except json.JSONDecodeError:
            self.message_counts = {}

        self.custom_st_command = self._normalize_command(self.get_setting("custom_st_command", ".m"))
        self.custom_chart_command = self._normalize_command(self.get_setting("custom_chart_command", ".d"))
        self.custom_settings_command = self._normalize_command(self.get_setting("custom_settings_command", ".set"))

        selected_timeframe_index = self.get_setting("chart_timeframe", 0)
        self.timeframe_days = [7, 14, 30][selected_timeframe_index]

        self.selected_chart_type_index = self.get_setting("chart_type_setting", 0)
        if not (0 <= self.selected_chart_type_index < len(self.chart_type_js_names)):
            self.selected_chart_type_index = 0
        self.selected_chart_type_js_name = self.chart_type_js_names[self.selected_chart_type_index]

        self.prune_old_data()
        self.save_message_counts()

    def save_message_counts(self):
        self.set_setting("message_counts_json", json.dumps(self.message_counts))

    def get_today_date_str(self) -> str:
        return datetime.now().strftime("%Y-%m-%d")

    def get_last_N_days_dates(self, num_days: int) -> List[str]:
        dates = []
        today = datetime.now()
        for i in range(num_days):
            date = today - timedelta(days=i)
            dates.append(date.strftime("%Y-%m-%d"))
        return dates[::-1]

    def prune_old_data(self):
        oldest_date_to_keep_dt = datetime.now() - timedelta(days=MAX_HISTORY_DAYS - 1)
        oldest_date_to_keep_str = oldest_date_to_keep_dt.strftime("%Y-%m-%d")
        keys_to_remove = [date_str for date_str in self.message_counts if date_str < oldest_date_to_keep_str]
        for key in keys_to_remove:
            del self.message_counts[key]

    def increment_message_count(self):
        today_str = self.get_today_date_str()
        self.message_counts[today_str] = self.message_counts.get(today_str, 0) + 1
        self.prune_old_data()
        self.save_message_counts()

    def _get_caption_and_entities(self, text: str):
        if not text:
            return None, None
        parsed = parse_markdown(text)
        entities_list = ArrayList()
        for entity in parsed.entities:
            entities_list.add(entity.to_tlrpc_object())
        return parsed.text, entities_list

    def _process_chart_request(self, params: Any, progress_dialog: Optional[AlertDialogBuilder]):
        try:
            dates_in_period = self.get_last_N_days_dates(self.timeframe_days)
            data_points_raw = []
            labels_raw = []
            for date_str in dates_in_period:
                count = self.message_counts.get(date_str, 0)
                labels_raw.append(datetime.strptime(date_str, "%Y-%m-%d").strftime("%d.%m"))
                data_points_raw.append(count)

            if self.selected_chart_type_js_name in ["pie", "doughnut", "polarArea"]:
                non_zero_data_points = []
                non_zero_labels = []
                for i, dp in enumerate(data_points_raw):
                    if dp > 0:
                        non_zero_data_points.append(dp)
                        non_zero_labels.append(labels_raw[i])

                if not non_zero_data_points:
                    raise ValueError("Нет данных для отображения диаграммы (все значения равны 0 или отсутствуют).")

                labels = non_zero_labels
                data_points = non_zero_data_points
            else:
                labels = labels_raw
                data_points = data_points_raw

            datasets = []
            if self.selected_chart_type_js_name in ["scatter", "bubble"]:
                transformed_data = []
                for i, count in enumerate(data_points_raw):
                    x_val = i
                    y_val = count
                    data_point = {"x": x_val, "y": y_val}
                    if self.selected_chart_type_js_name == "bubble":
                        data_point["r"] = max(5, count / 2) if count > 0 else 5
                    transformed_data.append(data_point)

                datasets.append({
                    "label": "Сообщений",
                    "data": transformed_data
                })
                labels = labels_raw
            else:
                datasets.append({
                    "label": "Сообщений",
                    "data": data_points
                })

            chart_config = {
                "type": self.selected_chart_type_js_name,
                "data": {
                    "labels": labels,
                    "datasets": datasets
                },
                "options": {
                    "title": {
                        "display": True,
                        "text": f"📊 Отправленные сообщения за {self.timeframe_days} дней",
                        "fontSize": 18,
                        "fontColor": "#2c3e50"
                    },
                    "legend": {
                        "display": False
                    },
                    "responsive": True,
                    "maintainAspectRatio": False,
                    "plugins": {
                        "datalabels": {
                            "display": False
                        }
                    }
                }
            }

            if self.selected_chart_type_js_name == "bar":
                chart_config["data"]["datasets"][0].update({
                    "backgroundColor": "rgba(52, 152, 219, 0.8)",
                    "borderColor": "rgba(41, 128, 185, 1)",
                    "borderWidth": 1,
                    "borderRadius": 5
                })
                chart_config["options"]["scales"] = {
                    "yAxes": [{
                        "ticks": {
                            "beginAtZero": True,
                            "stepSize": 1,
                            "precision": 0,
                            "fontColor": "#34495e"
                        },
                        "gridLines": {
                            "color": "rgba(0, 0, 0, 0.1)",
                            "drawBorder": False
                        }
                    }],
                    "xAxes": [{
                        "ticks": {
                            "fontColor": "#34495e"
                        },
                        "gridLines": {
                            "display": False
                        },
                        "barPercentage": 0.8,
                        "categoryPercentage": 0.8
                    }]
                }
                chart_config["options"]["plugins"]["datalabels"].update({
                    "display": True,
                    "anchor": "end",
                    "align": "top",
                    "color": "#34495e",
                    "font": {
                        "weight": "bold"
                    }
                })
            elif self.selected_chart_type_js_name == "horizontalBar":
                chart_config["data"]["datasets"][0].update({
                    "backgroundColor": "rgba(52, 152, 219, 0.8)",
                    "borderColor": "rgba(41, 128, 185, 1)",
                    "borderWidth": 1,
                    "borderRadius": 5
                })
                chart_config["options"]["scales"] = {
                    "xAxes": [{
                        "ticks": {
                            "beginAtZero": True,
                            "stepSize": 1,
                            "precision": 0,
                            "fontColor": "#34495e"
                        },
                        "gridLines": {
                            "color": "rgba(0, 0, 0, 0.1)",
                            "drawBorder": False
                        }
                    }],
                    "yAxes": [{
                        "ticks": {
                            "fontColor": "#34495e"
                        },
                        "gridLines": {
                            "display": False
                        },
                        "barPercentage": 0.8,
                        "categoryPercentage": 0.8
                    }]
                }
                chart_config["options"]["plugins"]["datalabels"].update({
                    "display": True,
                    "anchor": "end",
                    "align": "right",
                    "color": "#34495e",
                    "font": {
                        "weight": "bold"
                    }
                })
            elif self.selected_chart_type_js_name == "line":
                chart_config["data"]["datasets"][0].update({
                    "backgroundColor": "rgba(52, 152, 219, 0.4)",
                    "borderColor": "rgba(41, 128, 185, 1)",
                    "borderWidth": 2,
                    "fill": False,
                    "tension": 0.3,
                    "pointRadius": 4,
                    "pointBackgroundColor": "rgba(41, 128, 185, 1)",
                    "pointBorderColor": "#fff"
                })
                chart_config["options"]["scales"] = {
                    "yAxes": [{
                        "ticks": {
                            "beginAtZero": True,
                            "stepSize": 1,
                            "precision": 0,
                            "fontColor": "#34495e"
                        },
                        "gridLines": {
                            "color": "rgba(0, 0, 0, 0.1)",
                            "drawBorder": False
                        }
                    }],
                    "xAxes": [{
                        "ticks": {
                            "fontColor": "#34495e"
                        },
                        "gridLines": {
                            "display": False
                        }
                    }]
                }
                chart_config["options"]["plugins"]["datalabels"].update({
                    "display": True,
                    "anchor": "end",
                    "align": "end",
                    "color": "#34495e",
                    "font": {
                        "weight": "bold"
                    }
                })
            elif self.selected_chart_type_js_name in ["pie", "doughnut", "polarArea"]:
                chart_config["data"]["datasets"][0].update({
                    "backgroundColor": PIE_COLORS[:len(data_points)],
                    "borderColor": "#fff",
                    "borderWidth": 1
                })
                if "scales" in chart_config["options"]:
                    del chart_config["options"]["scales"]

                chart_config["options"]["plugins"]["datalabels"].update({
                    "display": True,
                    "formatter": "function(value, ctx) { return ctx.chart.data.labels[ctx.dataIndex] + ': ' + value; }",
                    "color": "#fff",
                    "font": {
                        "weight": "bold"
                    },
                    "textShadowBlur": 2,
                    "textShadowColor": "rgba(0,0,0,0.5)"
                })
                chart_config["options"]["legend"]["display"] = True
                chart_config["options"]["legend"]["position"] = "bottom"

            elif self.selected_chart_type_js_name == "radar":
                chart_config["data"]["datasets"][0].update({
                    "backgroundColor": "rgba(52, 152, 219, 0.4)",
                    "borderColor": "rgba(41, 128, 185, 1)",
                    "borderWidth": 2,
                    "pointBackgroundColor": "rgba(41, 128, 185, 1)",
                    "pointBorderColor": "#fff",
                    "pointHoverBackgroundColor": "#fff",
                    "pointHoverBorderColor": "rgba(41, 128, 185, 1)"
                })
                chart_config["options"]["scales"] = {
                    "r": {
                        "angleLines": { "display": False },
                        "ticks": {
                            "beginAtZero": True,
                            "backdropColor": "rgba(255, 255, 255, 0.75)",
                            "fontColor": "#34495e",
                            "precision": 0
                        },
                        "gridLines": {
                            "color": "rgba(0, 0, 0, 0.1)"
                        }
                    }
                }
                chart_config["options"]["plugins"]["datalabels"]["display"] = False
                chart_config["options"]["legend"]["display"] = False

            elif self.selected_chart_type_js_name == "scatter":
                chart_config["data"]["datasets"][0].update({
                    "backgroundColor": "rgba(54, 162, 235, 0.6)",
                    "borderColor": "rgba(54, 162, 235, 1)",
                    "pointRadius": 5,
                    "pointHoverRadius": 7
                })
                chart_config["options"]["scales"] = {
                    "x": {
                        "type": "linear",
                        "position": "bottom",
                        "ticks": {
                            "callback": "function(value, index, values) { return ctx.chart.data.labels[value]; }",
                            "fontColor": "#34495e",
                            "autoSkip": True,
                            "maxRotation": 45,
                            "minRotation": 0
                        },
                        "gridLines": {
                            "color": "rgba(0, 0, 0, 0.1)"
                        },
                        "title": {
                            "display": True,
                            "text": "День",
                            "font": {"size": 14, "weight": "bold"}
                        }
                    },
                    "y": {
                        "ticks": {
                            "beginAtZero": True,
                            "stepSize": 1,
                            "precision": 0,
                            "fontColor": "#34495e"
                        },
                        "gridLines": {
                            "color": "rgba(0, 0, 0, 0.1)"
                        },
                        "title": {
                            "display": True,
                            "text": "Количество сообщений",
                            "font": {"size": 14, "weight": "bold"}
                        }
                    }
                }
                chart_config["options"]["plugins"]["datalabels"]["display"] = False

            elif self.selected_chart_type_js_name == "bubble":
                chart_config["data"]["datasets"][0].update({
                    "backgroundColor": PIE_COLORS[0],
                    "borderColor": "rgba(0,0,0,0.5)",
                    "borderWidth": 1
                })
                chart_config["options"]["scales"] = {
                    "x": {
                        "type": "linear",
                        "position": "bottom",
                        "ticks": {
                            "callback": "function(value, index, values) { return ctx.chart.data.labels[value]; }",
                            "fontColor": "#34495e",
                            "autoSkip": True,
                            "maxRotation": 45,
                            "minRotation": 0
                        },
                        "gridLines": {
                            "color": "rgba(0, 0, 0, 0.1)"
                        },
                        "title": {
                            "display": True,
                            "text": "День",
                            "font": {"size": 14, "weight": "bold"}
                        }
                    },
                    "y": {
                        "ticks": {
                            "beginAtZero": True,
                            "stepSize": 1,
                            "precision": 0,
                            "fontColor": "#34495e"
                        },
                        "gridLines": {
                            "color": "rgba(0, 0, 0, 0.1)"
                        },
                        "title": {
                            "display": True,
                            "text": "Количество сообщений",
                            "font": {"size": 14, "weight": "bold"}
                        }
                    }
                }
                chart_config["options"]["plugins"]["datalabels"]["display"] = False
            chart_config["options"]["plugins"]["datalabels"]["font"] = {
                "size": 10,
                "weight": "bold"
            }
            if "scales" in chart_config["options"]:
                for axis_key in chart_config["options"]["scales"].keys():
                    axis = chart_config["options"]["scales"][axis_key]
                    if "ticks" in axis:
                        axis["ticks"]["font"] = {"size": 10}
                    if "title" in axis:
                        axis["title"]["font"] = {"size": 12, "weight": "bold"}

            chart_json = json.dumps(chart_config)
            encoded_chart_json = quote_plus(chart_json)
            chart_url = f"https://quickchart.io/chart?c={encoded_chart_json}&width=800&height=500&backgroundColor=white&f=png"

            response = requests.get(chart_url, stream=True, timeout=15)
            response.raise_for_status()

            cache_dir = ApplicationLoader.applicationContext.getCacheDir().getAbsolutePath()
            temp_file_name = f"chart_{uuid.uuid4()}.png"
            temp_path = os.path.join(cache_dir, temp_file_name)

            with open(temp_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)

            if not os.path.exists(temp_path) or os.path.getsize(temp_path) == 0:
                BulletinHelper.show_error("Не удалось создать файл изображения диаграммы.")
                if progress_dialog: run_on_ui_thread(lambda: progress_dialog.dismiss())
                return

            send_helper = get_send_messages_helper()
            generated_photo = send_helper.generatePhotoSizes(temp_path, None)

            if not generated_photo:
                BulletinHelper.show_error("Не удалось обработать изображение диаграммы.")
                os.remove(temp_path)
                if progress_dialog: run_on_ui_thread(lambda: progress_dialog.dismiss())
                return

            params.photo = generated_photo
            params.path = temp_path

            caption_text_with_link = f"📊 Ваша статистика сообщений за {self.timeframe_days} дней ({self.chart_type_map[self.selected_chart_type_js_name]['display'].lower()}): [Ссылка]({chart_url})"
            caption, entities = self._get_caption_and_entities(caption_text_with_link)

            params.caption = caption
            params.entities = entities if entities is not None else ArrayList()

            params.message = None

            run_on_ui_thread(lambda: send_helper.sendMessage(params))

            def cleanup():
                time.sleep(10)
                try:
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                except Exception:
                    pass
            run_on_queue(cleanup)

        except ValueError as ve:
            error_message = f"Произошла ошибка при генерации диаграммы: {ve}"
            BulletinHelper.show_error(error_message)
            send_message({"message": error_message, "peer": params.peer})
        except requests.exceptions.RequestException as e:
            error_message = f"Произошла ошибка сети при попытке сгенерировать диаграмму: {e}"
            BulletinHelper.show_error(error_message)
            send_message({"message": error_message, "peer": params.peer})
        except Exception as e:
            error_message = f"Произошла общая ошибка при генерации диаграммы: {e}"
            BulletinHelper.show_error(error_message)
            send_message({"message": error_message, "peer": params.peer})
        finally:
            if progress_dialog:
                try:
                    run_on_ui_thread(lambda: progress_dialog.dismiss())
                except Exception:
                    pass

    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        message_text = params.message

        if not isinstance(message_text, str):
            run_on_queue(self.increment_message_count)
            return HookResult()

        cleaned_message_text = message_text.strip().lower()

        if self.awaiting_command_input and params.peer == self.awaiting_command_input.get("peer_id"):
            setting_key = self.awaiting_command_input["setting_key"]

            if not message_text.strip().startswith("."):
                BulletinHelper.show_error("Команда должна начинаться с точки. Попробуйте еще раз.")
                return HookResult(strategy=HookStrategy.CANCEL) 

            normalized_command = self._normalize_command(message_text)
            self.set_setting(setting_key, normalized_command)

            if setting_key == "custom_st_command":
                self.custom_st_command = normalized_command
            elif setting_key == "custom_chart_command":
                self.custom_chart_command = normalized_command
            elif setting_key == "custom_settings_command":
                self.custom_settings_command = normalized_command

            BulletinHelper.show_success(f"Команда '{setting_key.replace('custom_', '').replace('_command', '')}' изменена на '{normalized_command}'.")
            self.awaiting_command_input = None
            self.load_settings()
            return HookResult(strategy=HookStrategy.CANCEL)

        if cleaned_message_text == self.custom_st_command.lower():
            output_message = f"💎 Сообщения за последние {self.timeframe_days} дней:\n\n"
            dates = self.get_last_N_days_dates(self.timeframe_days)

            for date_str in dates:
                count = self.message_counts.get(date_str, 0)
                display_date = datetime.strptime(date_str, "%Y-%m-%d").strftime("%d.%m.%Y")
                output_message += f"• {display_date}: {count}\n"

            params.message = output_message
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        if cleaned_message_text == self.custom_chart_command.lower():
            current_fragment = get_last_fragment()
            if not current_fragment:
                 BulletinHelper.show_error("Не удалось показать индикатор загрузки для генерации диаграммы.")
                 return HookResult(strategy=HookStrategy.CANCEL)

            current_activity = current_fragment.getParentActivity()
            if not current_activity:
                BulletinHelper.show_error("Не удалось показать индикатор загрузки для генерации диаграммы.")
                return HookResult(strategy=HookStrategy.CANCEL)

            self.progress_dialog = AlertDialogBuilder(
                current_activity,
                AlertDialogBuilder.ALERT_TYPE_SPINNER
            )
            self.progress_dialog.set_title("Генерация диаграммы...")
            self.progress_dialog.set_cancelable(False)
            run_on_ui_thread(lambda: self.progress_dialog.show())

            run_on_queue(lambda: self._process_chart_request(params, self.progress_dialog))

            return HookResult(strategy=HookStrategy.CANCEL)

        if cleaned_message_text == self.custom_settings_command.lower():
            run_on_ui_thread(lambda: self._show_settings_main_dialog(params.peer))
            return HookResult(strategy=HookStrategy.CANCEL)

        run_on_queue(self.increment_message_count)
        return HookResult()

    def _normalize_command(self, cmd: str) -> str:
        cmd_str = str(cmd).strip()
        if not cmd_str.startswith("."):
            cmd_str = "." + cmd_str
        return cmd_str.lower()

    def _on_st_command_change_from_main_settings(self, new_value: str):
        self.custom_st_command = self._normalize_command(new_value)
        self.set_setting("custom_st_command", self.custom_st_command)
        self.load_settings()

    def _on_chart_command_change_from_main_settings(self, new_value: str):
        self.custom_chart_command = self._normalize_command(new_value)
        self.set_setting("custom_chart_command", self.custom_chart_command)
        self.load_settings()

    def _on_settings_command_change_from_main_settings(self, new_value: str):
        self.custom_settings_command = self._normalize_command(new_value)
        self.set_setting("custom_settings_command", self.custom_settings_command)
        self.load_settings()

    def _on_timeframe_change_from_dialog(self, bld: Any, which: int):
        self.timeframe_days = [7, 14, 30][which]
        self.set_setting("chart_timeframe", which)
        BulletinHelper.show_success(f"Период статистики изменен на {self.timeframe_days} дней.")
        bld.dismiss()
        run_on_ui_thread(lambda: self._show_settings_main_dialog(get_last_fragment().getDialogId()))

    def _on_chart_type_change_from_dialog(self, bld: Any, which: int):
        self.selected_chart_type_index = which
        self.selected_chart_type_js_name = self.chart_type_js_names[which]
        self.set_setting("chart_type_setting", which)
        BulletinHelper.show_success(f"Тип диаграммы изменен на '{self.chart_type_display_names[which]}'.")
        bld.dismiss()
        run_on_ui_thread(lambda: self._show_settings_main_dialog(get_last_fragment().getDialogId()))

    def _show_settings_main_dialog(self, peer_id: Any):
        current_fragment = get_last_fragment()
        if not current_fragment: return
        activity = current_fragment.getParentActivity()
        if not activity: return

        self.load_settings()

        db_size_str = self._get_database_size_human_readable()

        dialog_items = [
            f"📊 Период статистики (сейчас: {self.timeframe_days} дней)",
            f"📈 Тип диаграммы (сейчас: {self.chart_type_display_names[self.selected_chart_type_index]})",
            "💬 Настройки команд",
            f"📥 Экспортировать данные ({db_size_str})",
            "🗑️ Очистить все данные"
        ]

        def on_item_click(bld, which):
            bld.dismiss()
            if which == 0:
                self._show_timeframe_dialog(peer_id)
            elif which == 1:
                self._show_chart_type_dialog(peer_id)
            elif which == 2:
                self._show_command_settings_dialog(peer_id)
            elif which == 3:
                self._export_data(None)
                run_on_ui_thread(lambda: self._show_settings_main_dialog(peer_id))
            elif which == 4:
                self._clear_data_prompt(None)
                run_on_ui_thread(lambda: self._show_settings_main_dialog(peer_id))

        builder = AlertDialogBuilder(activity)
        builder.set_title("Настройки Message Tracker")
        builder.set_items(dialog_items, on_item_click)
        builder.set_negative_button("Закрыть", lambda b, w: b.dismiss())
        builder.show()

    def _show_command_settings_dialog(self, peer_id: Any):
        current_fragment = get_last_fragment()
        if not current_fragment: return
        activity = current_fragment.getParentActivity()
        if not activity: return

        self.load_settings()

        dialog_items = [
            f"Изменить команду статистики ({self.custom_st_command})",
            f"Изменить команду диаграммы ({self.custom_chart_command})",
            f"Изменить команду настроек ({self.custom_settings_command})"
        ]

        def on_item_click(bld, which):
            bld.dismiss()
            if which == 0:
                self._show_edit_command_dialog("custom_st_command", "Команда для текстовой статистики", self.custom_st_command, peer_id)
            elif which == 1:
                self._show_edit_command_dialog("custom_chart_command", "Команда для диаграммы", self.custom_chart_command, peer_id)
            elif which == 2:
                self._show_edit_command_dialog("custom_settings_command", "Команда для быстрого доступа:", self.custom_settings_command, peer_id)

        builder = AlertDialogBuilder(activity)
        builder.set_title("Настройки команд")
        builder.set_items(dialog_items, on_item_click)
        builder.set_negative_button("Назад", lambda b, w: run_on_ui_thread(lambda: self._show_settings_main_dialog(peer_id)))
        builder.show()

    def _show_edit_command_dialog(self, setting_key: str, dialog_title: str, current_value: str, peer_id: Any):
        current_fragment = get_last_fragment()
        if not current_fragment: return
        activity = current_fragment.getParentActivity()
        if not activity: return

        self.awaiting_command_input = {
            "setting_key": setting_key,
            "peer_id": peer_id
        }

        BulletinHelper.show_info(f"Введите новую команду для '{dialog_title}' прямо в чат. Она должна начинаться с точки, например: .newcmd")

        builder = AlertDialogBuilder(activity)
        builder.set_title(dialog_title)
        builder.set_message(f"Чтобы изменить команду, введите её в чат.\n\nТекущая: {current_value}\nНапример: {current_value.replace('.', '.new')}")
        builder.set_negative_button("Отмена", lambda b, w: run_on_ui_thread(lambda: self._cancel_command_input_and_return_to_command_settings(peer_id)))
        builder.show()

    def _cancel_command_input_and_return_to_command_settings(self, peer_id: Any):
        self.awaiting_command_input = None
        BulletinHelper.show_info("Изменение команды отменено.")
        run_on_ui_thread(lambda: self._show_command_settings_dialog(peer_id))

    def _on_timeframe_change(self, new_index: int):
        self.timeframe_days = [7, 14, 30][new_index]
        self.set_setting("chart_timeframe", new_index)

    def _on_chart_type_change(self, new_index: int):
        self.selected_chart_type_index = new_index
        self.selected_chart_type_js_name = self.chart_type_js_names[new_index]
        self.set_setting("chart_type_setting", new_index)

    def _show_timeframe_dialog(self, peer_id: Any):
        current_fragment = get_last_fragment()
        if not current_fragment: return
        activity = current_fragment.getParentActivity()
        if not activity: return

        items = ["Последние 7 дней", "Последние 14 дней", "Последние 30 дней"]
        builder = AlertDialogBuilder(activity)
        builder.set_title("Выберите период для статистики")
        builder.set_items(items, self._on_timeframe_change_from_dialog)
        builder.set_negative_button("Отмена", lambda b, w: run_on_ui_thread(lambda: self._show_settings_main_dialog(peer_id)))
        builder.show()

    def _show_chart_type_dialog(self, peer_id: Any):
        current_fragment = get_last_fragment()
        if not current_fragment: return
        activity = current_fragment.getParentActivity()
        if not activity: return

        builder = AlertDialogBuilder(activity)
        builder.set_title("Выберите тип диаграммы")
        builder.set_items(self.chart_type_display_names, self._on_chart_type_change_from_dialog)
        builder.set_negative_button("Отмена", lambda b, w: run_on_ui_thread(lambda: self._show_settings_main_dialog(peer_id)))
        builder.show()

    def _get_database_size_human_readable(self) -> str:
        try:
            files_dir = ApplicationLoader.applicationContext.getFilesDir()
            plugin_settings_dir = File(files_dir, f"plugins/{__id__}/")
            settings_file = File(plugin_settings_dir, "settings.json")

            if settings_file.exists():
                size_bytes = settings_file.length()
                return AndroidUtilities.formatFileSize(size_bytes)
            else:
                return "0 байт"
        except Exception:
            return "Ошибка"

    def _export_data(self, view: Any):
        run_on_queue(self._perform_export_data)

    def _perform_export_data(self):
        try:
            data_to_export = self.get_setting("message_counts_json", "{}")
            run_on_ui_thread(lambda: AndroidUtilities.addToClipboard(data_to_export))
            run_on_ui_thread(lambda: BulletinHelper.show_success("Данные скопированы в буфер обмена!"))
        except Exception as e:
            run_on_ui_thread(lambda: BulletinHelper.show_error(f"Не удалось экспортировать данные: {e}"))

    def _clear_data_prompt(self, view: Any):
        current_fragment = get_last_fragment()
        if not current_fragment:
            return

        activity = current_fragment.getParentActivity()
        if not activity:
            return

        def _on_clear_confirm(bld, which):
            run_on_queue(self._perform_clear_data)
            bld.dismiss()

        builder = AlertDialogBuilder(activity)
        builder.set_title("Очистка данных")
        builder.set_message("Вы уверены, что хотите полностью удалить все данные о количестве отправленных сообщений? Это действие необратимо.")
        builder.set_positive_button("Очистить", _on_clear_confirm)
        builder.set_negative_button("Отмена", lambda b, w: b.dismiss())
        run_on_ui_thread(builder.show)

    def _perform_clear_data(self):
        try:
            self.message_counts = {}
            self.save_message_counts()
            run_on_ui_thread(lambda: BulletinHelper.show_success("Данные о сообщениях очищены!"))
        except Exception as e:
            run_on_ui_thread(lambda: BulletinHelper.show_error(f"Не удалось очистить данные: {e}"))

    def create_settings(self):
        db_size_str = self._get_database_size_human_readable()

        current_timeframe_index = [7, 14, 30].index(self.timeframe_days)

        return [
            Header(text="Настройки отслеживания сообщений"),
            Input(
                key="custom_st_command",
                text="Команда для текстовой статистики",
                default=".m",
                icon="msg_emoji_gif",
                subtext="Например: .mystats",
                on_change=self._on_st_command_change_from_main_settings
            ),
            Input(
                key="custom_chart_command",
                text="Команда для диаграммы",
                default=".d",
                icon="msg_chart",
                subtext="Например: .chart",
                on_change=self._on_chart_command_change_from_main_settings
            ),
            Input(
                key="custom_settings_command",
                text="Команда для быстрого доступа:",
                default=".set",
                icon="msg_settings",
                subtext="Например: .menu",
                on_change=self._on_settings_command_change_from_main_settings
            ),
            Selector(
                key="chart_timeframe",
                text="Период для статистики",
                default=current_timeframe_index,
                items=["Последние 7 дней", "Последние 14 дней", "Последние 30 дней"],
                icon="msg_schedule",
                on_change=self._on_timeframe_change
            ),
            Selector(
                key="chart_type_setting",
                text="Тип диаграммы",
                default=self.selected_chart_type_index,
                items=self.chart_type_display_names,
                icon="msg_photo2",
                on_change=self._on_chart_type_change
            ),
            Divider(),
            Header(text="Управление данными"),
            Text(
                text=f"Размер базы данных: {db_size_str}",
                icon="msg_storage"
            ),
            Text(
                text="Скопировать данные сообщений в буфер обмена (JSON)",
                icon="msg_copy",
                on_click=self._export_data
            ),
            Text(
                text="Очистить все данные о сообщениях",
                icon="msg_clear",
                red=True,
                on_click=self._clear_data_prompt
            )
        ]