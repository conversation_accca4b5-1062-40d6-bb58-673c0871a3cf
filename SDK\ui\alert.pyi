# alert.pyi
from typing import Callable, List, Optional, Dict, Any

_BUTTON_CLICK_LISTENER_CALLBACK = Callable[["AlertDialogBuilder", int], None]
_ITEMS_CLICK_LISTENER_CALLBACK = Callable[["AlertDialogBuilder", int], None]
_DISMISS_LISTENER_CALLBACK = Callable[["AlertDialogBuilder"], None]
_CANCEL_LISTENER_CALLBACK = Callable[["AlertDialogBuilder"], None]


class _ButtonClickListenerProxy(object):
    def __init__(self, py_callable: _BUTTON_CLICK_LISTENER_CALLBACK, builder_instance: "AlertDialogBuilder") -> None: ...
    def onClick(self, dialog_java_instance: Any, which: int) -> None: ...

class _ItemsClickListenerProxy(object):
    def __init__(self, py_callable: _ITEMS_CLICK_LISTENER_CALLBACK, builder_instance: "AlertDialogBuilder") -> None: ...
    def onClick(self, dialog_java_instance: Any, which: int) -> None: ...

class _DismissListenerProxy(object):
    def __init__(self, py_callable: _DISMISS_LISTENER_CALLBACK, builder_instance: "AlertDialogBuilder") -> None: ...
    def onDismiss(self, dialog_java_instance: Any) -> None: ...

class _CancelListenerProxy(object):
    def __init__(self, py_callable: _CANCEL_LISTENER_CALLBACK, builder_instance: "AlertDialogBuilder") -> None: ...
    def onCancel(self, dialog_java_instance: Any) -> None: ...


class AlertDialogBuilder:
    ALERT_TYPE_MESSAGE: int
    ALERT_TYPE_LOADING: int
    ALERT_TYPE_SPINNER: int

    BUTTON_POSITIVE: int
    BUTTON_NEGATIVE: int
    BUTTON_NEUTRAL: int

    _context: Any
    _java_builder: Any
    _alert_dialog: Optional[Any]
    _red_buttons: List[int]
    _progress_style: int

    def __init__(self, context: Any, progress_style: int = ..., resources_provider: Optional[Any] = None) -> None: ...
    def get_context(self) -> Any: ...
    def set_title(self, title: str) -> "AlertDialogBuilder": ...
    def set_message(self, message: str) -> "AlertDialogBuilder": ...
    def set_message_text_view_clickable(self, clickable: bool) -> "AlertDialogBuilder": ...
    def set_positive_button(self, text: str, listener: Optional[_BUTTON_CLICK_LISTENER_CALLBACK] = None) -> "AlertDialogBuilder": ...
    def set_negative_button(self, text: str, listener: Optional[_BUTTON_CLICK_LISTENER_CALLBACK] = None) -> "AlertDialogBuilder": ...
    def set_neutral_button(self, text: str, listener: Optional[_BUTTON_CLICK_LISTENER_CALLBACK] = None) -> "AlertDialogBuilder": ...
    def make_button_red(self, button_type: int) -> "AlertDialogBuilder": ...
    def set_on_back_button_listener(self, listener: Optional[_BUTTON_CLICK_LISTENER_CALLBACK] = None) -> "AlertDialogBuilder": ...
    def set_view(self, view: Any, height: int = ...) -> "AlertDialogBuilder": ...
    def set_items(self, items: List[str], listener: Optional[_ITEMS_CLICK_LISTENER_CALLBACK] = None, icons: Optional[List[int]] = None) -> "AlertDialogBuilder": ...
    def set_on_dismiss_listener(self, listener: Optional[_DISMISS_LISTENER_CALLBACK] = None) -> "AlertDialogBuilder": ...
    def set_on_cancel_listener(self, listener: Optional[_CANCEL_LISTENER_CALLBACK] = None) -> "AlertDialogBuilder": ...
    def set_top_image(self, res_id: int, background_color: int) -> "AlertDialogBuilder": ...
    def set_top_drawable(self, drawable: Any, background_color: int) -> "AlertDialogBuilder": ...
    def set_top_animation(self, res_id: int, size: int, auto_repeat: bool, background_color: int, layer_colors: Optional[Dict[str, int]] = None) -> "AlertDialogBuilder": ...
    def set_top_animation_is_new(self, is_new: bool) -> "AlertDialogBuilder": ...
    def set_dim_enabled(self, enabled: bool) -> "AlertDialogBuilder": ...
    def set_dialog_button_color_key(self, theme_key: int) -> "AlertDialogBuilder": ...
    def set_blurred_background(self, blur: bool, blur_behind_if_possible: bool = True) -> "AlertDialogBuilder": ...
    def create(self) -> "AlertDialogBuilder": ...
    def show(self) -> "AlertDialogBuilder": ...
    def _apply_red_buttons(self) -> None: ...
    def dismiss(self) -> None: ...
    def get_dialog(self) -> Optional[Any]: ...
    def get_button(self, button_type: int) -> Optional[Any]: ...
    def set_progress(self, progress: int) -> None: ...
    def set_cancelable(self, cancelable: bool) -> None: ...
    def set_canceled_on_touch_outside(self, cancel: bool) -> None: ...