{"algorithm": "DSA", "generatorVersion": "0.8r12", "numberOfTests": 906, "header": ["Test vectors of test DsaVerify are intended for checking the signature", "verification of DSA signatures."], "notes": {"EdgeCase": "Some implementations of DSA do not properly check for boundaries. In some cases the modular inverse of 0 is simply 0. As a result there are implementations where values such as r=1, s=0 lead to forgeries.", "NoLeadingZero": "ASN encoded integers with a leading hex-digit in the range 8 .. F are negative. If the first hex-digit of a positive integer is 8 .. F then a leading 0 must be added. Some libraries forgot to do this and therefore generated invalid DSA signatures. Some providers accept such legacy signatures for compatibility."}, "schema": "dsa_verify_schema.json", "testGroups": [{"key": {"g": "0835aa8c358bbf01a1846d1206323fabe408b0e98789fcc6239da14d4b3f86c276a8f48aa85a59507e620ad1bc745f0f1cbf63ec98c229c2610d77c634d1642e404354771655b2d5662f7a45227178ce3430af0f6b3bb94b52f7f51e97bad659b1ba0684e208be624c28d82fb1162f18dd9dce45216461654cf3374624d15a8d", "keySize": 1024, "p": "00b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f", "q": "00b90b38ba0a50a43ec6898d3f9b68049777f489b1", "type": "DsaPublicKey", "y": "173931dda31eff32f24b383091bf77eacdc6efd557624911d8e9b9debf0f256d0cffac5567b33f6eaae9d3275bbed7ef9f5f94c4003c959e49a1ed3f58c31b21baccc0ed8840b46145f121b8906d072129bae01f071947997e8ef760d2d9ea21d08a5eb7e89390b21a85664713c549e25feda6e9e6c31970866bdfbc8fa981f6"}, "keyDer": "308201b63082012b06072a8648ce3804013082011e02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f021500b90b38ba0a50a43ec6898d3f9b68049777f489b10281800835aa8c358bbf01a1846d1206323fabe408b0e98789fcc6239da14d4b3f86c276a8f48aa85a59507e620ad1bc745f0f1cbf63ec98c229c2610d77c634d1642e404354771655b2d5662f7a45227178ce3430af0f6b3bb94b52f7f51e97bad659b1ba0684e208be624c28d82fb1162f18dd9dce45216461654cf3374624d15a8d03818400028180173931dda31eff32f24b383091bf77eacdc6efd557624911d8e9b9debf0f256d0cffac5567b33f6eaae9d3275bbed7ef9f5f94c4003c959e49a1ed3f58c31b21baccc0ed8840b46145f121b8906d072129bae01f071947997e8ef760d2d9ea21d08a5eb7e89390b21a85664713c549e25feda6e9e6c31970866bdfbc8fa981f6", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIBtjCCASsGByqGSM44BAEwggEeAoGBALNM6cHngpTTJYRzhCAF0qSMjFZs/Kj4\nTAYG8lKbWabTiq4HG1O7IWfqpPw7Af4Xbnh+SBtgN6rGLLw9CJeZU2qGn6jN/qHo\nsf0tHNOjA1CFmizWs+wvm/u2i7EbS74q2qGNZKk2OVQ65eFik+MRwM+MjW4YDfBd\nCML9LZPVcHUfAhUAuQs4ugpQpD7GiY0/m2gEl3f0ibECgYAINaqMNYu/AaGEbRIG\nMj+r5Aiw6YeJ/MYjnaFNSz+Gwnao9IqoWllQfmIK0bx0Xw8cv2PsmMIpwmENd8Y0\n0WQuQENUdxZVstVmL3pFInF4zjQwrw9rO7lLUvf1Hpe61lmxugaE4gi+Ykwo2C+x\nFi8Y3Z3ORSFkYWVM8zdGJNFajQOBhAACgYAXOTHdox7/MvJLODCRv3fqzcbv1Vdi\nSRHY6bnevw8lbQz/rFVnsz9uqunTJ1u+1++fX5TEADyVnkmh7T9YwxshuszA7YhA\ntGFF8SG4kG0HISm64B8HGUeZfo73YNLZ6iHQil636JOQshqFZkcTxUniX+2m6ebD\nGXCGa9+8j6mB9g==\n-----END PUBLIC KEY-----", "sha": "SHA-1", "type": "DsaVerify", "tests": [{"tcId": 1, "comment": "Legacy:ASN encoding of r misses leading 0", "msg": "313233343030", "sig": "302c0214aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "acceptable", "flags": ["NoLeadingZero"]}, {"tcId": 2, "comment": "valid", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "valid", "flags": []}, {"tcId": 3, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "30812d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 4, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "3082002d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 5, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "302e021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 6, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "302c021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "3085010000002d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "308901000000000000002d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3080021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "302d028000aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0280496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "302f021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0000", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "302f0000021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0000", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "302f021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0500", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "including garbage", "msg": "313233343030", "sig": "3032498177302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "including garbage", "msg": "313233343030", "sig": "30312500302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "302f302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0004deadbeef", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "3032221a498177021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "303122192500021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "30352217021500aa6a258fbf7d90e15614676d377df8b10e38db4a0004deadbeef0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "3032021500aa6a258fbf7d90e15614676d377df8b10e38db4a22194981770214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "3031021500aa6a258fbf7d90e15614676d377df8b10e38db4a221825000214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "3035021500aa6a258fbf7d90e15614676d377df8b10e38db4a22160214496d5220b5f67d3532d1f991203bc3523b964c3b0004deadbeef", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including undefined tags", "msg": "313233343030", "sig": "3035aa00bb00cd00302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including undefined tags", "msg": "313233343030", "sig": "3033aa02aabb302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "3035221daa00bb00cd00021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "3033221baa02aabb021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "3035021500aa6a258fbf7d90e15614676d377df8b10e38db4a221caa00bb00cd000214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "3033021500aa6a258fbf7d90e15614676d377df8b10e38db4a221aaa02aabb0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3080302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0000", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30312280021500aa6a258fbf7d90e15614676d377df8b10e38db4a00000214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3031021500aa6a258fbf7d90e15614676d377df8b10e38db4a22800214496d5220b5f67d3532d1f991203bc3523b964c3b0000", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3080312d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0000", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30312280031500aa6a258fbf7d90e15614676d377df8b10e38db4a00000214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3031021500aa6a258fbf7d90e15614676d377df8b10e38db4a22800314496d5220b5f67d3532d1f991203bc3523b964c3b0000", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e2d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f2d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "312d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "322d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff2d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "using composition for sequence", "msg": "313233343030", "sig": "3031300102302c1500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "truncated sequence", "msg": "313233343030", "sig": "302c021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "truncated sequence", "msg": "313233343030", "sig": "302c1500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "indefinite length", "msg": "313233343030", "sig": "3080021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0000", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "3080021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b00", "result": "invalid", "flags": []}, {"tcId": 57, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "3080021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b05000000", "result": "invalid", "flags": []}, {"tcId": 58, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "3080021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b060811220000", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "3080021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0000fe02beef", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "3080021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0002beef", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "302f3000021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "append empty sequence", "msg": "313233343030", "sig": "302f021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b3000", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "3030021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3bbf7f00", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "sequence of sequence", "msg": "313233343030", "sig": "302f302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "3017021500aa6a258fbf7d90e15614676d377df8b10e38db4a", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "3043021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "302e02811500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "302e021500aa6a258fbf7d90e15614676d377df8b10e38db4a028114496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 69, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "302f0282001500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 70, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "302f021500aa6a258fbf7d90e15614676d377df8b10e38db4a02820014496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 71, "comment": "wrong length of integer", "msg": "313233343030", "sig": "302d021600aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 72, "comment": "wrong length of integer", "msg": "313233343030", "sig": "302d021400aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0215496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0213496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "30320285010000001500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "3032021500aa6a258fbf7d90e15614676d377df8b10e38db4a02850100000014496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3036028901000000000000001500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3036021500aa6a258fbf7d90e15614676d377df8b10e38db4a0289010000000000000014496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "303102847fffffff00aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "3031021500aa6a258fbf7d90e15614676d377df8b10e38db4a02847fffffff496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30310284ffffffff00aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "3031021500aa6a258fbf7d90e15614676d377df8b10e38db4a0284ffffffff496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "30320285ffffffffff00aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "3032021500aa6a258fbf7d90e15614676d377df8b10e38db4a0285ffffffffff496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "30350288ffffffffffffffff00aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "3035021500aa6a258fbf7d90e15614676d377df8b10e38db4a0288ffffffffffffffff496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "302d02ff00aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a02ff496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "removing integer", "msg": "313233343030", "sig": "30160214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "lonely integer tag", "msg": "313233343030", "sig": "3017020214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "lonely integer tag", "msg": "313233343030", "sig": "3018021500aa6a258fbf7d90e15614676d377df8b10e38db4a02", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "302f021700aa6a258fbf7d90e15614676d377df8b10e38db4a00000214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "302f021500aa6a258fbf7d90e15614676d377df8b10e38db4a0216496d5220b5f67d3532d1f991203bc3523b964c3b0000", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "302f0217000000aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "302f021500aa6a258fbf7d90e15614676d377df8b10e38db4a02160000496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 96, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "302f021500aa6a258fbf7d90e15614676d377df8b10e38db4a00000214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 97, "comment": "appending null value to integer", "msg": "313233343030", "sig": "302f021700aa6a258fbf7d90e15614676d377df8b10e38db4a05000214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 98, "comment": "appending null value to integer", "msg": "313233343030", "sig": "302f021500aa6a258fbf7d90e15614676d377df8b10e38db4a0216496d5220b5f67d3532d1f991203bc3523b964c3b0500", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "truncated length of integer", "msg": "313233343030", "sig": "301802810214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "truncated length of integer", "msg": "313233343030", "sig": "3019021500aa6a258fbf7d90e15614676d377df8b10e38db4a0281", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "301805000214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "3019021500aa6a258fbf7d90e15614676d377df8b10e38db4a0500", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "302d001500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "302d011500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "302d031500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "302d041500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "302dff1500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0014496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0114496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0314496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0414496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38db4aff14496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "dropping value of integer", "msg": "313233343030", "sig": "301802000214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "dropping value of integer", "msg": "313233343030", "sig": "3019021500aa6a258fbf7d90e15614676d377df8b10e38db4a0200", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "using composition for integer", "msg": "313233343030", "sig": "303122190201000214aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "using composition for integer", "msg": "313233343030", "sig": "3031021500aa6a258fbf7d90e15614676d377df8b10e38db4a221802014902136d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "302d021502aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a02144b6d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38dbca0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964cbb", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "truncated integer", "msg": "313233343030", "sig": "302c021400aa6a258fbf7d90e15614676d377df8b10e38db0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "truncated integer", "msg": "313233343030", "sig": "302c021500aa6a258fbf7d90e15614676d377df8b10e38db4a0213496d5220b5f67d3532d1f991203bc3523b964c", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "302c021500aa6a258fbf7d90e15614676d377df8b10e38db4a02136d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "leading ff in integer", "msg": "313233343030", "sig": "302e0216ff00aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "302e021500aa6a258fbf7d90e15614676d377df8b10e38db4a0215ff496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "30190901800214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "301a021500aa6a258fbf7d90e15614676d377df8b10e38db4a090180", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "30190201000214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "301a021500aa6a258fbf7d90e15614676d377df8b10e38db4a020100", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "302d02150163755e49c9ce35201c9df4acd2e5fd48862d64fb0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "302c0214f15eecd5b52ceca28f8ada2d9c15f419964451990214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "302d0215ff5595da7040826f1ea9eb9892c882074ef1c724b60214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "302c02140ea1132a4ad3135d707525d263ea0be669bbae670214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "302d0215fe9c8aa1b63631cadfe3620b532d1a02b779d29b050214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "302d021501aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "302c02145595da7040826f1ea9eb9892c882074ef1c724b60214496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "302e021500aa6a258fbf7d90e15614676d377df8b10e38db4a02150102788adac0472173f95b86d0bba3c7e9b38ad5ec", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a021490621966aba5d8f66c486c5184d3bebac3a1c28a", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214b692addf4a0982cacd2e066edfc43cadc469b3c5", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a02146f9de699545a270993b793ae7b2c41453c5e3d76", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "302e021500aa6a258fbf7d90e15614676d377df8b10e38db4a0215fefd8775253fb8de8c06a4792f445c38164c752a14", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "302e021500aa6a258fbf7d90e15614676d377df8b10e38db4a021501496d5220b5f67d3532d1f991203bc3523b964c3b", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "302e021500aa6a258fbf7d90e15614676d377df8b10e38db4a021500b692addf4a0982cacd2e066edfc43cadc469b3c5", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a0201000215ff46f4c745f5af5bc1397672c06497fb68880b764f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301902010002145c859c5d0528521f6344c69fcdb4024bbbfa44d8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301902010002145c859c5d0528521f6344c69fcdb4024bbbfa44d9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a020100021500b90b38ba0a50a43ec6898d3f9b68049777f489b0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a020100021500b90b38ba0a50a43ec6898d3f9b68049777f489b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a020100021500b90b38ba0a50a43ec6898d3f9b68049777f489b2", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a0201000215010000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30818702010002818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a0201010215ff46f4c745f5af5bc1397672c06497fb68880b764f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301902010102145c859c5d0528521f6344c69fcdb4024bbbfa44d8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301902010102145c859c5d0528521f6344c69fcdb4024bbbfa44d9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a020101021500b90b38ba0a50a43ec6898d3f9b68049777f489b0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a020101021500b90b38ba0a50a43ec6898d3f9b68049777f489b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a020101021500b90b38ba0a50a43ec6898d3f9b68049777f489b2", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a0201010215010000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30818702010102818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a0201ff0215ff46f4c745f5af5bc1397672c06497fb68880b764f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30190201ff02145c859c5d0528521f6344c69fcdb4024bbbfa44d8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30190201ff02145c859c5d0528521f6344c69fcdb4024bbbfa44d9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a0201ff021500b90b38ba0a50a43ec6898d3f9b68049777f489b0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a0201ff021500b90b38ba0a50a43ec6898d3f9b68049777f489b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a0201ff021500b90b38ba0a50a43ec6898d3f9b68049777f489b2", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a0201ff0215010000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3081870201ff02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d80215ff46f4c745f5af5bc1397672c06497fb68880b764f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301902145c859c5d0528521f6344c69fcdb4024bbbfa44d8020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301902145c859c5d0528521f6344c69fcdb4024bbbfa44d8020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301902145c859c5d0528521f6344c69fcdb4024bbbfa44d80201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302c02145c859c5d0528521f6344c69fcdb4024bbbfa44d802145c859c5d0528521f6344c69fcdb4024bbbfa44d8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302c02145c859c5d0528521f6344c69fcdb4024bbbfa44d802145c859c5d0528521f6344c69fcdb4024bbbfa44d9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d8021500b90b38ba0a50a43ec6898d3f9b68049777f489b0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d8021500b90b38ba0a50a43ec6898d3f9b68049777f489b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d8021500b90b38ba0a50a43ec6898d3f9b68049777f489b2", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d80215010000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30819a02145c859c5d0528521f6344c69fcdb4024bbbfa44d802818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301b02145c859c5d0528521f6344c69fcdb4024bbbfa44d8090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301902145c859c5d0528521f6344c69fcdb4024bbbfa44d8090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d90215ff46f4c745f5af5bc1397672c06497fb68880b764f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301902145c859c5d0528521f6344c69fcdb4024bbbfa44d9020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301902145c859c5d0528521f6344c69fcdb4024bbbfa44d9020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301902145c859c5d0528521f6344c69fcdb4024bbbfa44d90201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302c02145c859c5d0528521f6344c69fcdb4024bbbfa44d902145c859c5d0528521f6344c69fcdb4024bbbfa44d8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302c02145c859c5d0528521f6344c69fcdb4024bbbfa44d902145c859c5d0528521f6344c69fcdb4024bbbfa44d9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d9021500b90b38ba0a50a43ec6898d3f9b68049777f489b0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d9021500b90b38ba0a50a43ec6898d3f9b68049777f489b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d9021500b90b38ba0a50a43ec6898d3f9b68049777f489b2", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d90215010000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30819a02145c859c5d0528521f6344c69fcdb4024bbbfa44d902818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301b02145c859c5d0528521f6344c69fcdb4024bbbfa44d9090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301902145c859c5d0528521f6344c69fcdb4024bbbfa44d9090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b00215ff46f4c745f5af5bc1397672c06497fb68880b764f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b0020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b0020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b00201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d021500b90b38ba0a50a43ec6898d3f9b68049777f489b002145c859c5d0528521f6344c69fcdb4024bbbfa44d8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d021500b90b38ba0a50a43ec6898d3f9b68049777f489b002145c859c5d0528521f6344c69fcdb4024bbbfa44d9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b0021500b90b38ba0a50a43ec6898d3f9b68049777f489b0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b0021500b90b38ba0a50a43ec6898d3f9b68049777f489b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b0021500b90b38ba0a50a43ec6898d3f9b68049777f489b2", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b00215010000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30819b021500b90b38ba0a50a43ec6898d3f9b68049777f489b002818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301c021500b90b38ba0a50a43ec6898d3f9b68049777f489b0090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b0090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b10215ff46f4c745f5af5bc1397672c06497fb68880b764f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b1020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b1020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 225, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b10201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 226, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d021500b90b38ba0a50a43ec6898d3f9b68049777f489b102145c859c5d0528521f6344c69fcdb4024bbbfa44d8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 227, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d021500b90b38ba0a50a43ec6898d3f9b68049777f489b102145c859c5d0528521f6344c69fcdb4024bbbfa44d9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 228, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b1021500b90b38ba0a50a43ec6898d3f9b68049777f489b0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 229, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b1021500b90b38ba0a50a43ec6898d3f9b68049777f489b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 230, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b1021500b90b38ba0a50a43ec6898d3f9b68049777f489b2", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 231, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b10215010000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 232, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30819b021500b90b38ba0a50a43ec6898d3f9b68049777f489b102818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 233, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301c021500b90b38ba0a50a43ec6898d3f9b68049777f489b1090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 234, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b1090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 235, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b20215ff46f4c745f5af5bc1397672c06497fb68880b764f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 236, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b2020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 237, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b2020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 238, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b20201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 239, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d021500b90b38ba0a50a43ec6898d3f9b68049777f489b202145c859c5d0528521f6344c69fcdb4024bbbfa44d8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 240, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d021500b90b38ba0a50a43ec6898d3f9b68049777f489b202145c859c5d0528521f6344c69fcdb4024bbbfa44d9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 241, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b2021500b90b38ba0a50a43ec6898d3f9b68049777f489b0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 242, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b2021500b90b38ba0a50a43ec6898d3f9b68049777f489b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 243, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b2021500b90b38ba0a50a43ec6898d3f9b68049777f489b2", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 244, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b20215010000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 245, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30819b021500b90b38ba0a50a43ec6898d3f9b68049777f489b202818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 246, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301c021500b90b38ba0a50a43ec6898d3f9b68049777f489b2090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 247, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b2090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 248, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e02150100000000000000000000000000000000000000000215ff46f4c745f5af5bc1397672c06497fb68880b764f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 249, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a0215010000000000000000000000000000000000000000020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 250, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a0215010000000000000000000000000000000000000000020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 251, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a02150100000000000000000000000000000000000000000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 252, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d021501000000000000000000000000000000000000000002145c859c5d0528521f6344c69fcdb4024bbbfa44d8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 253, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302d021501000000000000000000000000000000000000000002145c859c5d0528521f6344c69fcdb4024bbbfa44d9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 254, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e0215010000000000000000000000000000000000000000021500b90b38ba0a50a43ec6898d3f9b68049777f489b0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 255, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e0215010000000000000000000000000000000000000000021500b90b38ba0a50a43ec6898d3f9b68049777f489b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 256, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e0215010000000000000000000000000000000000000000021500b90b38ba0a50a43ec6898d3f9b68049777f489b2", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 257, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302e02150100000000000000000000000000000000000000000215010000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 258, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30819b021501000000000000000000000000000000000000000002818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 259, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301c0215010000000000000000000000000000000000000000090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 260, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301a0215010000000000000000000000000000000000000000090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 261, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30819b02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f0215ff46f4c745f5af5bc1397672c06497fb68880b764f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 262, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30818702818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 263, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30818702818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 264, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30818702818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 265, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30819a02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f02145c859c5d0528521f6344c69fcdb4024bbbfa44d8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 266, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30819a02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f02145c859c5d0528521f6344c69fcdb4024bbbfa44d9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 267, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30819b02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f021500b90b38ba0a50a43ec6898d3f9b68049777f489b0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 268, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30819b02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f021500b90b38ba0a50a43ec6898d3f9b68049777f489b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 269, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30819b02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f021500b90b38ba0a50a43ec6898d3f9b68049777f489b2", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 270, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30819b02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f0215010000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 271, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010802818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 272, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30818902818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 273, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30818702818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 274, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301c090380fe010215ff46f4c745f5af5bc1397672c06497fb68880b764f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 275, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008090380fe01020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 276, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008090380fe01020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 277, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008090380fe010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 278, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301b090380fe0102145c859c5d0528521f6344c69fcdb4024bbbfa44d8", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 279, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301b090380fe0102145c859c5d0528521f6344c69fcdb4024bbbfa44d9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 280, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301c090380fe01021500b90b38ba0a50a43ec6898d3f9b68049777f489b0", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 281, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301c090380fe01021500b90b38ba0a50a43ec6898d3f9b68049777f489b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 282, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301c090380fe01021500b90b38ba0a50a43ec6898d3f9b68049777f489b2", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 283, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "301c090380fe010215010000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 284, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308189090380fe0102818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 285, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "300a090380fe01090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 286, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008090380fe01090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 287, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 288, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 289, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 290, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 291, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 292, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 293, "comment": "random signature", "msg": "313233343030", "sig": "302e0215008854bdb52d20ff9ea499483fba4d3c101a586fc7021500b23045900995d3fe3c4c638a3e06458a25a1e9dd", "result": "valid", "flags": []}, {"tcId": 294, "comment": "random signature", "msg": "313233343030", "sig": "302c02144f6bf18941abbf33211d9561a14f9aebd03f4e940214218cda350def7f75617fcc799d0cf2cf6b23438d", "result": "valid", "flags": []}, {"tcId": 295, "comment": "random signature", "msg": "313233343030", "sig": "302c0214459eaf0886160081b47fc573fb3d152d680d3b4b02144293dbb94db4c930d67e27fc4ec8538b58d1c7cd", "result": "valid", "flags": []}, {"tcId": 296, "comment": "random signature", "msg": "313233343030", "sig": "302c02140a6c12b8ff5ca21c4ea0c7acea38d76fd170b97f021429ce2cf2672fa640031680dce2223932f613f6a5", "result": "valid", "flags": []}, {"tcId": 297, "comment": "random signature", "msg": "313233343030", "sig": "302c021434bce4773e5e11875ea2202bc33e01fe00b3321a02142b294e01a97296d84e4c60bfba05d2760981c920", "result": "valid", "flags": []}]}, {"key": {"g": "0835aa8c358bbf01a1846d1206323fabe408b0e98789fcc6239da14d4b3f86c276a8f48aa85a59507e620ad1bc745f0f1cbf63ec98c229c2610d77c634d1642e404354771655b2d5662f7a45227178ce3430af0f6b3bb94b52f7f51e97bad659b1ba0684e208be624c28d82fb1162f18dd9dce45216461654cf3374624d15a8d", "keySize": 1024, "p": "00b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f", "q": "00b90b38ba0a50a43ec6898d3f9b68049777f489b1", "type": "DsaPublicKey", "y": "713e9f8108a6a7075485a37ce1a3b040cce563a0445614fe099fb1bffd68acb36f9e04d8ad17ace3c136da66f730eb7ff18936424ffa4e5ae5b1e7dac375d8d164697254b8b7e848f5e79da25c79df5c0727d5da3498405cd0f4e46d136c351d703cc4bf0d3f4fbb165392888684964a93ad30fa179488cad4a6655dd4fa9754"}, "keyDer": "308201b63082012b06072a8648ce3804013082011e02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f021500b90b38ba0a50a43ec6898d3f9b68049777f489b10281800835aa8c358bbf01a1846d1206323fabe408b0e98789fcc6239da14d4b3f86c276a8f48aa85a59507e620ad1bc745f0f1cbf63ec98c229c2610d77c634d1642e404354771655b2d5662f7a45227178ce3430af0f6b3bb94b52f7f51e97bad659b1ba0684e208be624c28d82fb1162f18dd9dce45216461654cf3374624d15a8d03818400028180713e9f8108a6a7075485a37ce1a3b040cce563a0445614fe099fb1bffd68acb36f9e04d8ad17ace3c136da66f730eb7ff18936424ffa4e5ae5b1e7dac375d8d164697254b8b7e848f5e79da25c79df5c0727d5da3498405cd0f4e46d136c351d703cc4bf0d3f4fbb165392888684964a93ad30fa179488cad4a6655dd4fa9754", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIIBtjCCASsGByqGSM44BAEwggEeAoGBALNM6cHngpTTJYRzhCAF0qSMjFZs/Kj4\nTAYG8lKbWabTiq4HG1O7IWfqpPw7Af4Xbnh+SBtgN6rGLLw9CJeZU2qGn6jN/qHo\nsf0tHNOjA1CFmizWs+wvm/u2i7EbS74q2qGNZKk2OVQ65eFik+MRwM+MjW4YDfBd\nCML9LZPVcHUfAhUAuQs4ugpQpD7GiY0/m2gEl3f0ibECgYAINaqMNYu/AaGEbRIG\nMj+r5Aiw6YeJ/MYjnaFNSz+Gwnao9IqoWllQfmIK0bx0Xw8cv2PsmMIpwmENd8Y0\n0WQuQENUdxZVstVmL3pFInF4zjQwrw9rO7lLUvf1Hpe61lmxugaE4gi+Ykwo2C+x\nFi8Y3Z3ORSFkYWVM8zdGJNFajQOBhAACgYBxPp+BCKanB1SFo3zho7BAzOVjoERW\nFP4Jn7G//Wiss2+eBNitF6zjwTbaZvcw63/xiTZCT/pOWuWx59rDddjRZGlyVLi3\n6Ej1552iXHnfXAcn1do0mEBc0PTkbRNsNR1wPMS/DT9PuxZTkoiGhJZKk60w+heU\niMrUpmVd1PqXVA==\n-----END PUBLIC KEY-----", "sha": "SHA-1", "type": "DsaVerify", "tests": [{"tcId": 298, "comment": "r,s = 1,1", "msg": "54657374", "sig": "3006020101020101", "result": "valid", "flags": []}, {"tcId": 299, "comment": "r,s = 1,5", "msg": "54657374", "sig": "3006020101020105", "result": "valid", "flags": []}, {"tcId": 300, "comment": "u2 small", "msg": "54657374", "sig": "3019020101021425023e8b9ba9ba72f481e90cb8ae67517e641b8a", "result": "valid", "flags": []}, {"tcId": 301, "comment": "s == q-1", "msg": "54657374", "sig": "301a020101021500b90b38ba0a50a43ec6898d3f9b68049777f489b0", "result": "valid", "flags": []}]}, {"key": {"g": "0835aa8c358bbf01a1846d1206323fabe408b0e98789fcc6239da14d4b3f86c276a8f48aa85a59507e620ad1bc745f0f1cbf63ec98c229c2610d77c634d1642e404354771655b2d5662f7a45227178ce3430af0f6b3bb94b52f7f51e97bad659b1ba0684e208be624c28d82fb1162f18dd9dce45216461654cf3374624d15a8d", "keySize": 1024, "p": "00b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f", "q": "00b90b38ba0a50a43ec6898d3f9b68049777f489b1", "type": "DsaPublicKey", "y": "61fe5b61f6d555ada7dc0ebac3459fccd8dfbad18ba94dbea52437cd7fb431df404d4738c594e720a6d786275acd02259ca613a08a2de118d0150d2ccae602102aca0cd03666a53f67c0b9943df5046c15baeaf496a9f018b7c939de1509de71ce47dd6f44c57f4e01e569be46932773190c154470cefbd1f4af82d28e4b31"}, "keyDer": "308201b43082012b06072a8648ce3804013082011e02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f021500b90b38ba0a50a43ec6898d3f9b68049777f489b10281800835aa8c358bbf01a1846d1206323fabe408b0e98789fcc6239da14d4b3f86c276a8f48aa85a59507e620ad1bc745f0f1cbf63ec98c229c2610d77c634d1642e404354771655b2d5662f7a45227178ce3430af0f6b3bb94b52f7f51e97bad659b1ba0684e208be624c28d82fb1162f18dd9dce45216461654cf3374624d15a8d03818200027f61fe5b61f6d555ada7dc0ebac3459fccd8dfbad18ba94dbea52437cd7fb431df404d4738c594e720a6d786275acd02259ca613a08a2de118d0150d2ccae602102aca0cd03666a53f67c0b9943df5046c15baeaf496a9f018b7c939de1509de71ce47dd6f44c57f4e01e569be46932773190c154470cefbd1f4af82d28e4b31", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIIBtDCCASsGByqGSM44BAEwggEeAoGBALNM6cHngpTTJYRzhCAF0qSMjFZs/Kj4\nTAYG8lKbWabTiq4HG1O7IWfqpPw7Af4Xbnh+SBtgN6rGLLw9CJeZU2qGn6jN/qHo\nsf0tHNOjA1CFmizWs+wvm/u2i7EbS74q2qGNZKk2OVQ65eFik+MRwM+MjW4YDfBd\nCML9LZPVcHUfAhUAuQs4ugpQpD7GiY0/m2gEl3f0ibECgYAINaqMNYu/AaGEbRIG\nMj+r5Aiw6YeJ/MYjnaFNSz+Gwnao9IqoWllQfmIK0bx0Xw8cv2PsmMIpwmENd8Y0\n0WQuQENUdxZVstVmL3pFInF4zjQwrw9rO7lLUvf1Hpe61lmxugaE4gi+Ykwo2C+x\nFi8Y3Z3ORSFkYWVM8zdGJNFajQOBggACf2H+W2H21VWtp9wOusNFn8zY37rRi6lN\nvqUkN81/tDHfQE1HOMWU5yCm14YnWs0CJZymE6CKLeEY0BUNLMrmAhAqygzQNmal\nP2fAuZQ99QRsFbrq9Jap8Bi3yTneFQnecc5H3W9ExX9OAeVpvkaTJ3MZDBVEcM77\n0fSvgtKOSzE=\n-----END PUBLIC KEY-----", "sha": "SHA-1", "type": "DsaVerify", "tests": [{"tcId": 302, "comment": "s == 1", "msg": "54657374", "sig": "3019021462ba827381396dc44facc66c344f91788f11c6fc020101", "result": "valid", "flags": []}]}, {"key": {"g": "16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde", "keySize": 2048, "p": "008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "q": "00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "type": "DsaPublicKey", "y": "1e77f842b1ae0fcd9929d394161d41e14614ff7507a9a31f4a1f14d22e2a627a1f4e596624883f1a5b168e9425146f22d5f6ee28757414714bb994ba1129f015d6e04a717edf9b530a5d5cab94f14631e8b4cf79aeb358cc741845553841e8ac461630e804a62f43676ba6794af66899c377b869ea612a7b9fe6611aa96be52eb8b62c979117bbbcca8a7ec1e1ffab1c7dfcfc7048700d3ae3858136e897701d7c2921b5dfef1d1f897f50d96ca1b5c2edc58cada18919e35642f0807eebfa00c99a32f4d095c3188f78ed54711be0325c4b532aeccd6540a567c327225440ea15319bde06510479a1861799e25b57decc73c036d75a0702bd373ca231349931"}, "keyDer": "308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde0382010500028201001e77f842b1ae0fcd9929d394161d41e14614ff7507a9a31f4a1f14d22e2a627a1f4e596624883f1a5b168e9425146f22d5f6ee28757414714bb994ba1129f015d6e04a717edf9b530a5d5cab94f14631e8b4cf79aeb358cc741845553841e8ac461630e804a62f43676ba6794af66899c377b869ea612a7b9fe6611aa96be52eb8b62c979117bbbcca8a7ec1e1ffab1c7dfcfc7048700d3ae3858136e897701d7c2921b5dfef1d1f897f50d96ca1b5c2edc58cada18919e35642f0807eebfa00c99a32f4d095c3188f78ed54711be0325c4b532aeccd6540a567c327225440ea15319bde06510479a1861799e25b57decc73c036d75a0702bd373ca231349931", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIDQjCCAjUGByqGSM44BAEwggIoAoIBAQCPeTXZuarpv6vtiHrPSVG28y7Fnjuv\nNxjo6sSWHz79NgbnQ1GpxBgzObgJ58KuHFObp0dbhdARrbi0eYd1SYRpXKwOjxSz\nNggooi/6JxEKPWKpk0U0CaD+aWxGWPhL3SCBnDcJoBBXsZWtzQAjPbpUhLYpH51k\njviDRIZ3l5zsBLQ0pqwudemYXeI9sCkvwRGMn/qdgYHnM423krcw17njSVkvaAmY\nchU5Feo9a4tGU8YzRY+AOzKkwuDycpAlbk4/ijsIOKHEUOThjBopo33fXqFD3ktm\n/wSQPtXPFiPhWNSHxgjpfyEc2B3KI8tuOAdl+CLjQr5ITAV2OTlgHNZnAh0AuvaW\npoV499/e5/pnyXfHhe8ysjO65YDAvNVpXQKCAQAWplxYIEhQcE51AqOXVwQNNNo6\nNHjBVNTkpcAtJC7gT5bmHkvQkEq9rI837rHgnzGC0jyQQ8tkL4gAQWDt+coJsyB2\np5wypifyRz6Rh5uixOdEvSCBVEy1W4AsNo0fqD7UielOD6BojjJCilx4xHjGjQUn\ntxyaOrsLC+EsRGiWOefTznTbEBplqiuH9kxoJts+xy9LVZmDS7TtsC98kOmkltOl\nXVNb6/xF1PYZ9j897buHOSXC8iTgdzEpbaiH7B5HSPh++1/et1SEMWsiMt7lU92v\nAhErDR8C2jCXMiT+J67ai51LKSLZuovjntnhA6Y8UoELxoi34u1DFuHvF9veA4IB\nBQACggEAHnf4QrGuD82ZKdOUFh1B4UYU/3UHqaMfSh8U0i4qYnofTllmJIg/GlsW\njpQlFG8i1fbuKHV0FHFLuZS6ESnwFdbgSnF+35tTCl1cq5TxRjHotM95rrNYzHQY\nRVU4QeisRhYw6ASmL0Nna6Z5SvZomcN3uGnqYSp7n+ZhGqlr5S64tiyXkRe7vMqK\nfsHh/6scffz8cEhwDTrjhYE26JdwHXwpIbXf7x0fiX9Q2WyhtcLtxYytoYkZ41ZC\n8IB+6/oAyZoy9NCVwxiPeO1UcRvgMlxLUyrszWVApWfDJyJUQOoVMZveBlEEeaGG\nF5niW1fezHPANtdaBwK9NzyiMTSZMQ==\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "DsaVerify", "tests": [{"tcId": 303, "comment": "Legacy:ASN encoding of s misses leading 0", "msg": "48656c6c6f", "sig": "303c021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021cade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "acceptable", "flags": ["NoLeadingZero"]}, {"tcId": 304, "comment": "valid", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "valid", "flags": []}, {"tcId": 305, "comment": "long form encoding of length of sequence", "msg": "48656c6c6f", "sig": "30813d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 306, "comment": "length of sequence contains leading 0", "msg": "48656c6c6f", "sig": "3082003d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 307, "comment": "wrong length of sequence", "msg": "48656c6c6f", "sig": "303e021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 308, "comment": "wrong length of sequence", "msg": "48656c6c6f", "sig": "303c021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 309, "comment": "uint32 overflow in length of sequence", "msg": "48656c6c6f", "sig": "3085010000003d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 310, "comment": "uint64 overflow in length of sequence", "msg": "48656c6c6f", "sig": "308901000000000000003d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 311, "comment": "length of sequence = 2**31 - 1", "msg": "48656c6c6f", "sig": "30847fffffff021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 312, "comment": "length of sequence = 2**32 - 1", "msg": "48656c6c6f", "sig": "3084ffffffff021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 313, "comment": "length of sequence = 2**40 - 1", "msg": "48656c6c6f", "sig": "3085ffffffffff021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 314, "comment": "length of sequence = 2**64 - 1", "msg": "48656c6c6f", "sig": "3088ffffffffffffffff021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 315, "comment": "incorrect length of sequence", "msg": "48656c6c6f", "sig": "30ff021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 316, "comment": "indefinite length without termination", "msg": "48656c6c6f", "sig": "3080021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 317, "comment": "indefinite length without termination", "msg": "48656c6c6f", "sig": "303d02801e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 318, "comment": "indefinite length without termination", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd028000ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 319, "comment": "removing sequence", "msg": "48656c6c6f", "sig": "", "result": "invalid", "flags": []}, {"tcId": 320, "comment": "lonely sequence tag", "msg": "48656c6c6f", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 321, "comment": "appending 0's to sequence", "msg": "48656c6c6f", "sig": "303f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000", "result": "invalid", "flags": []}, {"tcId": 322, "comment": "prepending 0's to sequence", "msg": "48656c6c6f", "sig": "303f0000021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 323, "comment": "appending unused 0's to sequence", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000", "result": "invalid", "flags": []}, {"tcId": 324, "comment": "appending null value to sequence", "msg": "48656c6c6f", "sig": "303f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360500", "result": "invalid", "flags": []}, {"tcId": 325, "comment": "including garbage", "msg": "48656c6c6f", "sig": "3042498177303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 326, "comment": "including garbage", "msg": "48656c6c6f", "sig": "30412500303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 327, "comment": "including garbage", "msg": "48656c6c6f", "sig": "303f303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360004deadbeef", "result": "invalid", "flags": []}, {"tcId": 328, "comment": "including garbage", "msg": "48656c6c6f", "sig": "30422221498177021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 329, "comment": "including garbage", "msg": "48656c6c6f", "sig": "304122202500021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 330, "comment": "including garbage", "msg": "48656c6c6f", "sig": "3045221e021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0004deadbeef021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 331, "comment": "including garbage", "msg": "48656c6c6f", "sig": "3042021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd2222498177021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 332, "comment": "including garbage", "msg": "48656c6c6f", "sig": "3041021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd22212500021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 333, "comment": "including garbage", "msg": "48656c6c6f", "sig": "3045021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd221f021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360004deadbeef", "result": "invalid", "flags": []}, {"tcId": 334, "comment": "including undefined tags", "msg": "48656c6c6f", "sig": "3045aa00bb00cd00303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 335, "comment": "including undefined tags", "msg": "48656c6c6f", "sig": "3043aa02aabb303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 336, "comment": "including undefined tags", "msg": "48656c6c6f", "sig": "30452224aa00bb00cd00021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 337, "comment": "including undefined tags", "msg": "48656c6c6f", "sig": "30432222aa02aabb021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 338, "comment": "including undefined tags", "msg": "48656c6c6f", "sig": "3045021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd2225aa00bb00cd00021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 339, "comment": "including undefined tags", "msg": "48656c6c6f", "sig": "3043021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd2223aa02aabb021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 340, "comment": "truncated length of sequence", "msg": "48656c6c6f", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 341, "comment": "using composition with indefinite length", "msg": "48656c6c6f", "sig": "3080303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000", "result": "invalid", "flags": []}, {"tcId": 342, "comment": "using composition with indefinite length", "msg": "48656c6c6f", "sig": "30412280021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0000021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 343, "comment": "using composition with indefinite length", "msg": "48656c6c6f", "sig": "3041021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd2280021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000", "result": "invalid", "flags": []}, {"tcId": 344, "comment": "using composition with wrong tag", "msg": "48656c6c6f", "sig": "3080313d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000", "result": "invalid", "flags": []}, {"tcId": 345, "comment": "using composition with wrong tag", "msg": "48656c6c6f", "sig": "30412280031c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0000021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 346, "comment": "using composition with wrong tag", "msg": "48656c6c6f", "sig": "3041021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd2280031d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000", "result": "invalid", "flags": []}, {"tcId": 347, "comment": "Replacing sequence with NULL", "msg": "48656c6c6f", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 348, "comment": "changing tag value of sequence", "msg": "48656c6c6f", "sig": "2e3d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 349, "comment": "changing tag value of sequence", "msg": "48656c6c6f", "sig": "2f3d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 350, "comment": "changing tag value of sequence", "msg": "48656c6c6f", "sig": "313d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 351, "comment": "changing tag value of sequence", "msg": "48656c6c6f", "sig": "323d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 352, "comment": "changing tag value of sequence", "msg": "48656c6c6f", "sig": "ff3d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 353, "comment": "dropping value of sequence", "msg": "48656c6c6f", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 354, "comment": "using composition for sequence", "msg": "48656c6c6f", "sig": "3041300102303c1c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 355, "comment": "truncated sequence", "msg": "48656c6c6f", "sig": "303c021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862", "result": "invalid", "flags": []}, {"tcId": 356, "comment": "truncated sequence", "msg": "48656c6c6f", "sig": "303c1c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 357, "comment": "indefinite length", "msg": "48656c6c6f", "sig": "3080021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000", "result": "invalid", "flags": []}, {"tcId": 358, "comment": "indefinite length with truncated delimiter", "msg": "48656c6c6f", "sig": "3080021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe878623600", "result": "invalid", "flags": []}, {"tcId": 359, "comment": "indefinite length with additional element", "msg": "48656c6c6f", "sig": "3080021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe878623605000000", "result": "invalid", "flags": []}, {"tcId": 360, "comment": "indefinite length with truncated element", "msg": "48656c6c6f", "sig": "3080021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236060811220000", "result": "invalid", "flags": []}, {"tcId": 361, "comment": "indefinite length with garbage", "msg": "48656c6c6f", "sig": "3080021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000fe02beef", "result": "invalid", "flags": []}, {"tcId": 362, "comment": "indefinite length with nonempty EOC", "msg": "48656c6c6f", "sig": "3080021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360002beef", "result": "invalid", "flags": []}, {"tcId": 363, "comment": "prepend empty sequence", "msg": "48656c6c6f", "sig": "303f3000021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 364, "comment": "append empty sequence", "msg": "48656c6c6f", "sig": "303f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862363000", "result": "invalid", "flags": []}, {"tcId": 365, "comment": "append garbage with high tag number", "msg": "48656c6c6f", "sig": "3040021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236bf7f00", "result": "invalid", "flags": []}, {"tcId": 366, "comment": "sequence of sequence", "msg": "48656c6c6f", "sig": "303f303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 367, "comment": "truncated sequence: removed last 1 elements", "msg": "48656c6c6f", "sig": "301e021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd", "result": "invalid", "flags": []}, {"tcId": 368, "comment": "repeating element in sequence", "msg": "48656c6c6f", "sig": "305c021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 369, "comment": "long form encoding of length of integer", "msg": "48656c6c6f", "sig": "303e02811c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 370, "comment": "long form encoding of length of integer", "msg": "48656c6c6f", "sig": "303e021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd02811d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 371, "comment": "length of integer contains leading 0", "msg": "48656c6c6f", "sig": "303f0282001c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 372, "comment": "length of integer contains leading 0", "msg": "48656c6c6f", "sig": "303f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0282001d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 373, "comment": "wrong length of integer", "msg": "48656c6c6f", "sig": "303d021d1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 374, "comment": "wrong length of integer", "msg": "48656c6c6f", "sig": "303d021b1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 375, "comment": "wrong length of integer", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021e00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 376, "comment": "wrong length of integer", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021c00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 377, "comment": "uint32 overflow in length of integer", "msg": "48656c6c6f", "sig": "30420285010000001c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 378, "comment": "uint32 overflow in length of integer", "msg": "48656c6c6f", "sig": "3042021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0285010000001d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 379, "comment": "uint64 overflow in length of integer", "msg": "48656c6c6f", "sig": "3046028901000000000000001c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 380, "comment": "uint64 overflow in length of integer", "msg": "48656c6c6f", "sig": "3046021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd028901000000000000001d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 381, "comment": "length of integer = 2**31 - 1", "msg": "48656c6c6f", "sig": "304102847fffffff1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 382, "comment": "length of integer = 2**31 - 1", "msg": "48656c6c6f", "sig": "3041021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd02847fffffff00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 383, "comment": "length of integer = 2**32 - 1", "msg": "48656c6c6f", "sig": "30410284ffffffff1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 384, "comment": "length of integer = 2**32 - 1", "msg": "48656c6c6f", "sig": "3041021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0284ffffffff00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 385, "comment": "length of integer = 2**40 - 1", "msg": "48656c6c6f", "sig": "30420285ffffffffff1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 386, "comment": "length of integer = 2**40 - 1", "msg": "48656c6c6f", "sig": "3042021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0285ffffffffff00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 387, "comment": "length of integer = 2**64 - 1", "msg": "48656c6c6f", "sig": "30450288ffffffffffffffff1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 388, "comment": "length of integer = 2**64 - 1", "msg": "48656c6c6f", "sig": "3045021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0288ffffffffffffffff00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 389, "comment": "incorrect length of integer", "msg": "48656c6c6f", "sig": "303d02ff1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 390, "comment": "incorrect length of integer", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd02ff00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 391, "comment": "removing integer", "msg": "48656c6c6f", "sig": "301f021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 392, "comment": "lonely integer tag", "msg": "48656c6c6f", "sig": "302002021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 393, "comment": "lonely integer tag", "msg": "48656c6c6f", "sig": "301f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd02", "result": "invalid", "flags": []}, {"tcId": 394, "comment": "appending 0's to integer", "msg": "48656c6c6f", "sig": "303f021e1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0000021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 395, "comment": "appending 0's to integer", "msg": "48656c6c6f", "sig": "303f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021f00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000", "result": "invalid", "flags": []}, {"tcId": 396, "comment": "prepending 0's to integer", "msg": "48656c6c6f", "sig": "303f021e00001e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 397, "comment": "prepending 0's to integer", "msg": "48656c6c6f", "sig": "303f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021f000000ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 398, "comment": "appending unused 0's to integer", "msg": "48656c6c6f", "sig": "303f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0000021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 399, "comment": "appending null value to integer", "msg": "48656c6c6f", "sig": "303f021e1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0500021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 400, "comment": "appending null value to integer", "msg": "48656c6c6f", "sig": "303f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021f00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360500", "result": "invalid", "flags": []}, {"tcId": 401, "comment": "truncated length of integer", "msg": "48656c6c6f", "sig": "30210281021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 402, "comment": "truncated length of integer", "msg": "48656c6c6f", "sig": "3020021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0281", "result": "invalid", "flags": []}, {"tcId": 403, "comment": "Replacing integer with NULL", "msg": "48656c6c6f", "sig": "30210500021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 404, "comment": "Replacing integer with NULL", "msg": "48656c6c6f", "sig": "3020021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0500", "result": "invalid", "flags": []}, {"tcId": 405, "comment": "changing tag value of integer", "msg": "48656c6c6f", "sig": "303d001c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 406, "comment": "changing tag value of integer", "msg": "48656c6c6f", "sig": "303d011c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 407, "comment": "changing tag value of integer", "msg": "48656c6c6f", "sig": "303d031c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 408, "comment": "changing tag value of integer", "msg": "48656c6c6f", "sig": "303d041c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 409, "comment": "changing tag value of integer", "msg": "48656c6c6f", "sig": "303dff1c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 410, "comment": "changing tag value of integer", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd001d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 411, "comment": "changing tag value of integer", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd011d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 412, "comment": "changing tag value of integer", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd031d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 413, "comment": "changing tag value of integer", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd041d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 414, "comment": "changing tag value of integer", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cdff1d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 415, "comment": "dropping value of integer", "msg": "48656c6c6f", "sig": "30210200021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 416, "comment": "dropping value of integer", "msg": "48656c6c6f", "sig": "3020021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0200", "result": "invalid", "flags": []}, {"tcId": 417, "comment": "using composition for integer", "msg": "48656c6c6f", "sig": "3041222002011e021b41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 418, "comment": "using composition for integer", "msg": "48656c6c6f", "sig": "3041021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd2221020100021cade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 419, "comment": "modify first byte of integer", "msg": "48656c6c6f", "sig": "303d021c1c41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 420, "comment": "modify first byte of integer", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d02ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 421, "comment": "modify last byte of integer", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c94d021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 422, "comment": "modify last byte of integer", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862b6", "result": "invalid", "flags": []}, {"tcId": 423, "comment": "truncated integer", "msg": "48656c6c6f", "sig": "303c021b1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 424, "comment": "truncated integer", "msg": "48656c6c6f", "sig": "303c021b41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 425, "comment": "truncated integer", "msg": "48656c6c6f", "sig": "303c021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021c00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862", "result": "invalid", "flags": []}, {"tcId": 426, "comment": "leading ff in integer", "msg": "48656c6c6f", "sig": "303e021dff1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 427, "comment": "leading ff in integer", "msg": "48656c6c6f", "sig": "303e021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021eff00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 428, "comment": "replaced integer by infinity", "msg": "48656c6c6f", "sig": "3022090180021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 429, "comment": "replaced integer by infinity", "msg": "48656c6c6f", "sig": "3021021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd090180", "result": "invalid", "flags": []}, {"tcId": 430, "comment": "replacing integer with zero", "msg": "48656c6c6f", "sig": "3022020100021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 431, "comment": "replacing integer with zero", "msg": "48656c6c6f", "sig": "3021021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd020100", "result": "invalid", "flags": []}, {"tcId": 432, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "48656c6c6f", "sig": "303e021d00d9384b2032d060e59848f87cb4535936bc25fa77959e96d7f88e332a021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 433, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "48656c6c6f", "sig": "303e021dff634b1dd327de7125da7903ad2163ca2addc096101fd395567ee36070021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 434, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "48656c6c6f", "sig": "303d021ce1be4b8652a896fa469f01eb15246e4f330cb7bc2546e9e8c4473633021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 435, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "48656c6c6f", "sig": "303e021d009cb4e22cd8218eda2586fc52de9c35d5223f69efe02c6aa9811c9f90021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 436, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "48656c6c6f", "sig": "303e021dff26c7b4dfcd2f9f1a67b707834baca6c943da05886a6169280771ccd6021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 437, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "48656c6c6f", "sig": "303e021d011e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 438, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "48656c6c6f", "sig": "303e021d00e1be4b8652a896fa469f01eb15246e4f330cb7bc2546e9e8c4473633021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 439, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d0168dcf02f57b0caef7ddc183bee1ca94ee09c1a02ee4b0200a54dcb93", "result": "invalid", "flags": []}, {"tcId": 440, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "48656c6c6f", "sig": "303c021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021cf2efc2e24cbedb2fc00c236c5b2d1a430236b59b7880007f2ba2f8d9", "result": "invalid", "flags": []}, {"tcId": 441, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021dff5219a6772dc82cf0610be22bdb5b1e370e969830cc9a7ec017879dca", "result": "invalid", "flags": []}, {"tcId": 442, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "48656c6c6f", "sig": "303c021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021c0d103d1db34124d03ff3dc93a4d2e5bcfdc94a64877fff80d45d0727", "result": "invalid", "flags": []}, {"tcId": 443, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021dfe97230fd0a84f35108223e7c411e356b11f63e5fd11b4fdff5ab2346d", "result": "invalid", "flags": []}, {"tcId": 444, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "48656c6c6f", "sig": "303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d01ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236", "result": "invalid", "flags": []}, {"tcId": 445, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "48656c6c6f", "sig": "303c021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021c5219a6772dc82cf0610be22bdb5b1e370e969830cc9a7ec017879dca", "result": "invalid", "flags": []}, {"tcId": 446, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022020100021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 447, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 448, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 449, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 450, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3021020100021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 451, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3021020100021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 452, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 453, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 454, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 455, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022020100021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 456, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3082010802010002820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 457, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 458, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 459, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022020101021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 460, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 461, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 462, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 463, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3021020101021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 464, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3021020101021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 465, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 466, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 467, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 468, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022020101021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 469, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3082010802010102820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 470, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 471, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 472, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30220201ff021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 473, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 474, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 475, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 476, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30210201ff021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 477, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30210201ff021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 478, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 479, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 480, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 481, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30220201ff021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 482, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "308201080201ff02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 483, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 484, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 485, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 486, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 487, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 488, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 489, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 490, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 491, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 492, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 493, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 494, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 495, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30820123021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 496, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3023021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 497, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 498, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 499, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 500, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 501, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 502, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 503, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 504, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 505, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 506, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 507, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 508, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30820123021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 509, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3023021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 510, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 511, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 512, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 513, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 514, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 515, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 516, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 517, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 518, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 519, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 520, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 521, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 522, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 523, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 524, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 525, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 526, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 527, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 528, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 529, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 530, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 531, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 532, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 533, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 534, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 535, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 536, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 537, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 538, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 539, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 540, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 541, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 542, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 543, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 544, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 545, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 546, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 547, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 548, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 549, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 550, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 551, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022021d0100000000000000000000000000000000000000000000000000000000020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 552, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022021d0100000000000000000000000000000000000000000000000000000000020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 553, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022021d01000000000000000000000000000000000000000000000000000000000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 554, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021d0100000000000000000000000000000000000000000000000000000000021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 555, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303d021d0100000000000000000000000000000000000000000000000000000000021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 556, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 557, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 558, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 559, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 560, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "30820124021d010000000000000000000000000000000000000000000000000000000002820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 561, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3024021d0100000000000000000000000000000000000000000000000000000000090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 562, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3022021d0100000000000000000000000000000000000000000000000000000000090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 563, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 564, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 565, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 566, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd6670201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 567, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3082012302820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 568, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3082012302820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 569, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 570, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 571, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 572, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 573, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3082020a02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd66702820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 574, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3082010a02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 575, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 576, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3024090380fe01021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 577, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3008090380fe01020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 578, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3008090380fe01020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 579, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3008090380fe010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 580, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3023090380fe01021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 581, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3023090380fe01021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 582, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 583, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 584, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 585, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3024090380fe01021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 586, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3082010a090380fe0102820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 587, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "300a090380fe01090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 588, "comment": "Signatures with special case values for r and s.", "msg": "48656c6c6f", "sig": "3008090380fe01090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 589, "comment": "Signature encoding contains wrong type.", "msg": "48656c6c6f", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 590, "comment": "Signature encoding contains wrong type.", "msg": "48656c6c6f", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 591, "comment": "Signature encoding contains wrong type.", "msg": "48656c6c6f", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 592, "comment": "Signature encoding contains wrong type.", "msg": "48656c6c6f", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 593, "comment": "Signature encoding contains wrong type.", "msg": "48656c6c6f", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 594, "comment": "Signature encoding contains wrong type.", "msg": "48656c6c6f", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 595, "comment": "random signature", "msg": "48656c6c6f", "sig": "303e021d0085c771ecf9c77debae0c54f749dba4b07ae519ca1037091dd6f294cf021d00a02f74985198cf88e310e55277ba598b336164850fdd5308a7beb1a7", "result": "valid", "flags": []}, {"tcId": 596, "comment": "random signature", "msg": "48656c6c6f", "sig": "303c021c043cd5580b8bfb5975edef132d6de7848096392e0adf46342bad7bb7021c142b6572ce61b032d45d4597a88c4e54dd593fa7ca6c8e6882df77f6", "result": "valid", "flags": []}, {"tcId": 597, "comment": "random signature", "msg": "48656c6c6f", "sig": "303d021d00914e4f7ba8df44cf63c57d219c6b5da093fe3a94604bd2ef88b630f5021c6a7b804cbfc69937903bbd0c8f67306a6e8bf0d0501ae3f3190dca19", "result": "valid", "flags": []}, {"tcId": 598, "comment": "random signature", "msg": "48656c6c6f", "sig": "303d021c0a8ffb160da0ffe319e7d3a5ea299f531f8421bfdcfd6bb66c6adcf2021d008a75e2cbaa709b2b5d81d346f6e90a8dc353c5835393b1d5a6653f3c", "result": "valid", "flags": []}, {"tcId": 599, "comment": "random signature", "msg": "48656c6c6f", "sig": "303d021d00a7f164d32f44684fcde89f0fa9647128bc0c290f08c9b9e6f7db3fe1021c2dca34db2f320c95de4e9de4986bbf50860753d5deb728237678afb6", "result": "valid", "flags": []}]}, {"key": {"g": "16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde", "keySize": 2048, "p": "008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "q": "00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "type": "DsaPublicKey", "y": "6978b68d31334ee5bc7b3e91ab6c2336fab45c64836bd92cb5337b734db9e8e44f889f8869829f4fe174dc9344c164a0ba5b0126259ba8a43f607564fa4a1d0d49645e1d5886a1fc485e2fe91e56eae330da05e17b0b3d018c290285b249bc409e7af54300fc7c3eb34911457e2371931ad9302e8450cd95df3d561ea0ad94d0a2eabcafe0dd6728fb280029b556d9f4fa7c0f46a7804329936708e97e11fc22b2a50761a890c65b5fea2a1a4172f6be9eaa60e738cdf60c015142e2e562bb62a11e810ccdf0bf633307382f2d9a9769b115dfcdab4bacae73feca289db209dce34cbe126e8c7f9d9e4f8f711349a608d567b48c050e9dfb32bc184ecaa4f0f0"}, "keyDer": "308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde0382010500028201006978b68d31334ee5bc7b3e91ab6c2336fab45c64836bd92cb5337b734db9e8e44f889f8869829f4fe174dc9344c164a0ba5b0126259ba8a43f607564fa4a1d0d49645e1d5886a1fc485e2fe91e56eae330da05e17b0b3d018c290285b249bc409e7af54300fc7c3eb34911457e2371931ad9302e8450cd95df3d561ea0ad94d0a2eabcafe0dd6728fb280029b556d9f4fa7c0f46a7804329936708e97e11fc22b2a50761a890c65b5fea2a1a4172f6be9eaa60e738cdf60c015142e2e562bb62a11e810ccdf0bf633307382f2d9a9769b115dfcdab4bacae73feca289db209dce34cbe126e8c7f9d9e4f8f711349a608d567b48c050e9dfb32bc184ecaa4f0f0", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIDQjCCAjUGByqGSM44BAEwggIoAoIBAQCPeTXZuarpv6vtiHrPSVG28y7Fnjuv\nNxjo6sSWHz79NgbnQ1GpxBgzObgJ58KuHFObp0dbhdARrbi0eYd1SYRpXKwOjxSz\nNggooi/6JxEKPWKpk0U0CaD+aWxGWPhL3SCBnDcJoBBXsZWtzQAjPbpUhLYpH51k\njviDRIZ3l5zsBLQ0pqwudemYXeI9sCkvwRGMn/qdgYHnM423krcw17njSVkvaAmY\nchU5Feo9a4tGU8YzRY+AOzKkwuDycpAlbk4/ijsIOKHEUOThjBopo33fXqFD3ktm\n/wSQPtXPFiPhWNSHxgjpfyEc2B3KI8tuOAdl+CLjQr5ITAV2OTlgHNZnAh0AuvaW\npoV499/e5/pnyXfHhe8ysjO65YDAvNVpXQKCAQAWplxYIEhQcE51AqOXVwQNNNo6\nNHjBVNTkpcAtJC7gT5bmHkvQkEq9rI837rHgnzGC0jyQQ8tkL4gAQWDt+coJsyB2\np5wypifyRz6Rh5uixOdEvSCBVEy1W4AsNo0fqD7UielOD6BojjJCilx4xHjGjQUn\ntxyaOrsLC+EsRGiWOefTznTbEBplqiuH9kxoJts+xy9LVZmDS7TtsC98kOmkltOl\nXVNb6/xF1PYZ9j897buHOSXC8iTgdzEpbaiH7B5HSPh++1/et1SEMWsiMt7lU92v\nAhErDR8C2jCXMiT+J67ai51LKSLZuovjntnhA6Y8UoELxoi34u1DFuHvF9veA4IB\nBQACggEAaXi2jTEzTuW8ez6Rq2wjNvq0XGSDa9kstTN7c0256ORPiJ+IaYKfT+F0\n3JNEwWSgulsBJiWbqKQ/YHVk+kodDUlkXh1YhqH8SF4v6R5W6uMw2gXhews9AYwp\nAoWySbxAnnr1QwD8fD6zSRFFfiNxkxrZMC6EUM2V3z1WHqCtlNCi6ryv4N1nKPso\nACm1Vtn0+nwPRqeAQymTZwjpfhH8IrKlB2GokMZbX+oqGkFy9r6eqmDnOM32DAFR\nQuLlYrtioR6BDM3wv2MzBzgvLZqXabEV382rS6yuc/7KKJ2yCdzjTL4Sbox/nZ5P\nj3ETSaYI1We0jAUOnfsyvBhOyqTw8A==\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "DsaVerify", "tests": [{"tcId": 600, "comment": "r,s = 1,1", "msg": "54657374", "sig": "3006020101020101", "result": "valid", "flags": []}, {"tcId": 601, "comment": "r,s = 1,5", "msg": "54657374", "sig": "3006020101020105", "result": "valid", "flags": []}, {"tcId": 602, "comment": "u2 small", "msg": "54657374", "sig": "3022020101021d009592121ed12d93197f1ffb863ac63937f28ef4f62f1e009a30aabab1", "result": "valid", "flags": []}, {"tcId": 603, "comment": "s == q-1", "msg": "54657374", "sig": "3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "valid", "flags": []}]}, {"key": {"g": "16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde", "keySize": 2048, "p": "008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "q": "00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "type": "DsaPublicKey", "y": "2a64953bde40789f80ed8227192286115b92d09d5de96904e803ec4ecfbd73e0f08e82910febf19fa3cdc55ff20eb970d9c712f44785c0fd592c17fb43f4625357a4ac8a1a628f72040ae5360839c7c1f6b214e7a15530fe22887139ea0f05a9daf9d95bd6b7467abf9107c9fbe31e36330276eeccce3d59635206d60ca256f9af60627626b0594984b5a075c42c42067fa8c330f258bcf145df27a97da8ee419b54e3ab296c7ce9ef6a0113389b3cac7885b44b3722d27cad60e4e5a924a1ed0342cea9e99256f6bc1308d4af2c0af9379b1cf2119ce113c085705f5519ccc1ba8562a2236190d3f0c0a10f01466ad79a48127c28433f6b34e24a539af60f3d"}, "keyDer": "308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde0382010500028201002a64953bde40789f80ed8227192286115b92d09d5de96904e803ec4ecfbd73e0f08e82910febf19fa3cdc55ff20eb970d9c712f44785c0fd592c17fb43f4625357a4ac8a1a628f72040ae5360839c7c1f6b214e7a15530fe22887139ea0f05a9daf9d95bd6b7467abf9107c9fbe31e36330276eeccce3d59635206d60ca256f9af60627626b0594984b5a075c42c42067fa8c330f258bcf145df27a97da8ee419b54e3ab296c7ce9ef6a0113389b3cac7885b44b3722d27cad60e4e5a924a1ed0342cea9e99256f6bc1308d4af2c0af9379b1cf2119ce113c085705f5519ccc1ba8562a2236190d3f0c0a10f01466ad79a48127c28433f6b34e24a539af60f3d", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIDQjCCAjUGByqGSM44BAEwggIoAoIBAQCPeTXZuarpv6vtiHrPSVG28y7Fnjuv\nNxjo6sSWHz79NgbnQ1GpxBgzObgJ58KuHFObp0dbhdARrbi0eYd1SYRpXKwOjxSz\nNggooi/6JxEKPWKpk0U0CaD+aWxGWPhL3SCBnDcJoBBXsZWtzQAjPbpUhLYpH51k\njviDRIZ3l5zsBLQ0pqwudemYXeI9sCkvwRGMn/qdgYHnM423krcw17njSVkvaAmY\nchU5Feo9a4tGU8YzRY+AOzKkwuDycpAlbk4/ijsIOKHEUOThjBopo33fXqFD3ktm\n/wSQPtXPFiPhWNSHxgjpfyEc2B3KI8tuOAdl+CLjQr5ITAV2OTlgHNZnAh0AuvaW\npoV499/e5/pnyXfHhe8ysjO65YDAvNVpXQKCAQAWplxYIEhQcE51AqOXVwQNNNo6\nNHjBVNTkpcAtJC7gT5bmHkvQkEq9rI837rHgnzGC0jyQQ8tkL4gAQWDt+coJsyB2\np5wypifyRz6Rh5uixOdEvSCBVEy1W4AsNo0fqD7UielOD6BojjJCilx4xHjGjQUn\ntxyaOrsLC+EsRGiWOefTznTbEBplqiuH9kxoJts+xy9LVZmDS7TtsC98kOmkltOl\nXVNb6/xF1PYZ9j897buHOSXC8iTgdzEpbaiH7B5HSPh++1/et1SEMWsiMt7lU92v\nAhErDR8C2jCXMiT+J67ai51LKSLZuovjntnhA6Y8UoELxoi34u1DFuHvF9veA4IB\nBQACggEAKmSVO95AeJ+A7YInGSKGEVuS0J1d6WkE6APsTs+9c+DwjoKRD+vxn6PN\nxV/yDrlw2ccS9EeFwP1ZLBf7Q/RiU1ekrIoaYo9yBArlNgg5x8H2shTnoVUw/iKI\ncTnqDwWp2vnZW9a3Rnq/kQfJ++MeNjMCdu7Mzj1ZY1IG1gyiVvmvYGJ2JrBZSYS1\noHXELEIGf6jDMPJYvPFF3yepfajuQZtU46spbHzp72oBEzibPKx4hbRLNyLSfK1g\n5OWpJKHtA0LOqemSVva8EwjUrywK+TebHPIRnOETwIVwX1UZzMG6hWKiI2GQ0/DA\noQ8BRmrXmkgSfChDP2s04kpTmvYPPQ==\n-----END PUBLIC KEY-----", "sha": "SHA-224", "type": "DsaVerify", "tests": [{"tcId": 604, "comment": "s == 1", "msg": "54657374", "sig": "3021021c5a252f4fc55618747fd94b13c9bee62bb958d85777cb07dd90710d24020101", "result": "valid", "flags": []}]}, {"key": {"g": "16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde", "keySize": 2048, "p": "008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "q": "00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "type": "DsaPublicKey", "y": "1e77f842b1ae0fcd9929d394161d41e14614ff7507a9a31f4a1f14d22e2a627a1f4e596624883f1a5b168e9425146f22d5f6ee28757414714bb994ba1129f015d6e04a717edf9b530a5d5cab94f14631e8b4cf79aeb358cc741845553841e8ac461630e804a62f43676ba6794af66899c377b869ea612a7b9fe6611aa96be52eb8b62c979117bbbcca8a7ec1e1ffab1c7dfcfc7048700d3ae3858136e897701d7c2921b5dfef1d1f897f50d96ca1b5c2edc58cada18919e35642f0807eebfa00c99a32f4d095c3188f78ed54711be0325c4b532aeccd6540a567c327225440ea15319bde06510479a1861799e25b57decc73c036d75a0702bd373ca231349931"}, "keyDer": "308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde0382010500028201001e77f842b1ae0fcd9929d394161d41e14614ff7507a9a31f4a1f14d22e2a627a1f4e596624883f1a5b168e9425146f22d5f6ee28757414714bb994ba1129f015d6e04a717edf9b530a5d5cab94f14631e8b4cf79aeb358cc741845553841e8ac461630e804a62f43676ba6794af66899c377b869ea612a7b9fe6611aa96be52eb8b62c979117bbbcca8a7ec1e1ffab1c7dfcfc7048700d3ae3858136e897701d7c2921b5dfef1d1f897f50d96ca1b5c2edc58cada18919e35642f0807eebfa00c99a32f4d095c3188f78ed54711be0325c4b532aeccd6540a567c327225440ea15319bde06510479a1861799e25b57decc73c036d75a0702bd373ca231349931", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIDQjCCAjUGByqGSM44BAEwggIoAoIBAQCPeTXZuarpv6vtiHrPSVG28y7Fnjuv\nNxjo6sSWHz79NgbnQ1GpxBgzObgJ58KuHFObp0dbhdARrbi0eYd1SYRpXKwOjxSz\nNggooi/6JxEKPWKpk0U0CaD+aWxGWPhL3SCBnDcJoBBXsZWtzQAjPbpUhLYpH51k\njviDRIZ3l5zsBLQ0pqwudemYXeI9sCkvwRGMn/qdgYHnM423krcw17njSVkvaAmY\nchU5Feo9a4tGU8YzRY+AOzKkwuDycpAlbk4/ijsIOKHEUOThjBopo33fXqFD3ktm\n/wSQPtXPFiPhWNSHxgjpfyEc2B3KI8tuOAdl+CLjQr5ITAV2OTlgHNZnAh0AuvaW\npoV499/e5/pnyXfHhe8ysjO65YDAvNVpXQKCAQAWplxYIEhQcE51AqOXVwQNNNo6\nNHjBVNTkpcAtJC7gT5bmHkvQkEq9rI837rHgnzGC0jyQQ8tkL4gAQWDt+coJsyB2\np5wypifyRz6Rh5uixOdEvSCBVEy1W4AsNo0fqD7UielOD6BojjJCilx4xHjGjQUn\ntxyaOrsLC+EsRGiWOefTznTbEBplqiuH9kxoJts+xy9LVZmDS7TtsC98kOmkltOl\nXVNb6/xF1PYZ9j897buHOSXC8iTgdzEpbaiH7B5HSPh++1/et1SEMWsiMt7lU92v\nAhErDR8C2jCXMiT+J67ai51LKSLZuovjntnhA6Y8UoELxoi34u1DFuHvF9veA4IB\nBQACggEAHnf4QrGuD82ZKdOUFh1B4UYU/3UHqaMfSh8U0i4qYnofTllmJIg/GlsW\njpQlFG8i1fbuKHV0FHFLuZS6ESnwFdbgSnF+35tTCl1cq5TxRjHotM95rrNYzHQY\nRVU4QeisRhYw6ASmL0Nna6Z5SvZomcN3uGnqYSp7n+ZhGqlr5S64tiyXkRe7vMqK\nfsHh/6scffz8cEhwDTrjhYE26JdwHXwpIbXf7x0fiX9Q2WyhtcLtxYytoYkZ41ZC\n8IB+6/oAyZoy9NCVwxiPeO1UcRvgMlxLUyrszWVApWfDJyJUQOoVMZveBlEEeaGG\nF5niW1fezHPANtdaBwK9NzyiMTSZMQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "DsaVerify", "tests": [{"tcId": 605, "comment": "Legacy:ASN encoding of r misses leading 0", "msg": "54657374", "sig": "303c021c9b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "acceptable", "flags": ["NoLeadingZero"]}, {"tcId": 606, "comment": "valid", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "valid", "flags": []}, {"tcId": 607, "comment": "long form encoding of length of sequence", "msg": "54657374", "sig": "30813d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 608, "comment": "length of sequence contains leading 0", "msg": "54657374", "sig": "3082003d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 609, "comment": "wrong length of sequence", "msg": "54657374", "sig": "303e021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 610, "comment": "wrong length of sequence", "msg": "54657374", "sig": "303c021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 611, "comment": "uint32 overflow in length of sequence", "msg": "54657374", "sig": "3085010000003d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 612, "comment": "uint64 overflow in length of sequence", "msg": "54657374", "sig": "308901000000000000003d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 613, "comment": "length of sequence = 2**31 - 1", "msg": "54657374", "sig": "30847fffffff021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 614, "comment": "length of sequence = 2**32 - 1", "msg": "54657374", "sig": "3084ffffffff021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 615, "comment": "length of sequence = 2**40 - 1", "msg": "54657374", "sig": "3085ffffffffff021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 616, "comment": "length of sequence = 2**64 - 1", "msg": "54657374", "sig": "3088ffffffffffffffff021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 617, "comment": "incorrect length of sequence", "msg": "54657374", "sig": "30ff021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 618, "comment": "indefinite length without termination", "msg": "54657374", "sig": "3080021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 619, "comment": "indefinite length without termination", "msg": "54657374", "sig": "303d0280009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 620, "comment": "indefinite length without termination", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee9302805fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 621, "comment": "removing sequence", "msg": "54657374", "sig": "", "result": "invalid", "flags": []}, {"tcId": 622, "comment": "lonely sequence tag", "msg": "54657374", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 623, "comment": "appending 0's to sequence", "msg": "54657374", "sig": "303f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000", "result": "invalid", "flags": []}, {"tcId": 624, "comment": "prepending 0's to sequence", "msg": "54657374", "sig": "303f0000021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 625, "comment": "appending unused 0's to sequence", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000", "result": "invalid", "flags": []}, {"tcId": 626, "comment": "appending null value to sequence", "msg": "54657374", "sig": "303f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160500", "result": "invalid", "flags": []}, {"tcId": 627, "comment": "including garbage", "msg": "54657374", "sig": "3042498177303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 628, "comment": "including garbage", "msg": "54657374", "sig": "30412500303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 629, "comment": "including garbage", "msg": "54657374", "sig": "303f303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160004deadbeef", "result": "invalid", "flags": []}, {"tcId": 630, "comment": "including garbage", "msg": "54657374", "sig": "30422222498177021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 631, "comment": "including garbage", "msg": "54657374", "sig": "304122212500021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 632, "comment": "including garbage", "msg": "54657374", "sig": "3045221f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930004deadbeef021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 633, "comment": "including garbage", "msg": "54657374", "sig": "3042021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee932221498177021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 634, "comment": "including garbage", "msg": "54657374", "sig": "3041021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee9322202500021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 635, "comment": "including garbage", "msg": "54657374", "sig": "3045021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93221e021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160004deadbeef", "result": "invalid", "flags": []}, {"tcId": 636, "comment": "including undefined tags", "msg": "54657374", "sig": "3045aa00bb00cd00303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 637, "comment": "including undefined tags", "msg": "54657374", "sig": "3043aa02aabb303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 638, "comment": "including undefined tags", "msg": "54657374", "sig": "30452225aa00bb00cd00021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 639, "comment": "including undefined tags", "msg": "54657374", "sig": "30432223aa02aabb021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 640, "comment": "including undefined tags", "msg": "54657374", "sig": "3045021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee932224aa00bb00cd00021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 641, "comment": "including undefined tags", "msg": "54657374", "sig": "3043021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee932222aa02aabb021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 642, "comment": "truncated length of sequence", "msg": "54657374", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 643, "comment": "using composition with indefinite length", "msg": "54657374", "sig": "3080303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000", "result": "invalid", "flags": []}, {"tcId": 644, "comment": "using composition with indefinite length", "msg": "54657374", "sig": "30412280021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930000021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 645, "comment": "using composition with indefinite length", "msg": "54657374", "sig": "3041021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee932280021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000", "result": "invalid", "flags": []}, {"tcId": 646, "comment": "using composition with wrong tag", "msg": "54657374", "sig": "3080313d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000", "result": "invalid", "flags": []}, {"tcId": 647, "comment": "using composition with wrong tag", "msg": "54657374", "sig": "30412280031d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930000021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 648, "comment": "using composition with wrong tag", "msg": "54657374", "sig": "3041021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee932280031c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000", "result": "invalid", "flags": []}, {"tcId": 649, "comment": "Replacing sequence with NULL", "msg": "54657374", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 650, "comment": "changing tag value of sequence", "msg": "54657374", "sig": "2e3d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 651, "comment": "changing tag value of sequence", "msg": "54657374", "sig": "2f3d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 652, "comment": "changing tag value of sequence", "msg": "54657374", "sig": "313d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 653, "comment": "changing tag value of sequence", "msg": "54657374", "sig": "323d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 654, "comment": "changing tag value of sequence", "msg": "54657374", "sig": "ff3d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 655, "comment": "dropping value of sequence", "msg": "54657374", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 656, "comment": "using composition for sequence", "msg": "54657374", "sig": "3041300102303c1d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 657, "comment": "truncated sequence", "msg": "54657374", "sig": "303c021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1", "result": "invalid", "flags": []}, {"tcId": 658, "comment": "truncated sequence", "msg": "54657374", "sig": "303c1d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 659, "comment": "indefinite length", "msg": "54657374", "sig": "3080021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000", "result": "invalid", "flags": []}, {"tcId": 660, "comment": "indefinite length with truncated delimiter", "msg": "54657374", "sig": "3080021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b11600", "result": "invalid", "flags": []}, {"tcId": 661, "comment": "indefinite length with additional element", "msg": "54657374", "sig": "3080021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b11605000000", "result": "invalid", "flags": []}, {"tcId": 662, "comment": "indefinite length with truncated element", "msg": "54657374", "sig": "3080021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116060811220000", "result": "invalid", "flags": []}, {"tcId": 663, "comment": "indefinite length with garbage", "msg": "54657374", "sig": "3080021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000fe02beef", "result": "invalid", "flags": []}, {"tcId": 664, "comment": "indefinite length with nonempty EOC", "msg": "54657374", "sig": "3080021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160002beef", "result": "invalid", "flags": []}, {"tcId": 665, "comment": "prepend empty sequence", "msg": "54657374", "sig": "303f3000021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 666, "comment": "append empty sequence", "msg": "54657374", "sig": "303f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1163000", "result": "invalid", "flags": []}, {"tcId": 667, "comment": "append garbage with high tag number", "msg": "54657374", "sig": "3040021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116bf7f00", "result": "invalid", "flags": []}, {"tcId": 668, "comment": "sequence of sequence", "msg": "54657374", "sig": "303f303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 669, "comment": "truncated sequence: removed last 1 elements", "msg": "54657374", "sig": "301f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93", "result": "invalid", "flags": []}, {"tcId": 670, "comment": "repeating element in sequence", "msg": "54657374", "sig": "305b021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 671, "comment": "long form encoding of length of integer", "msg": "54657374", "sig": "303e02811d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 672, "comment": "long form encoding of length of integer", "msg": "54657374", "sig": "303e021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee9302811c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 673, "comment": "length of integer contains leading 0", "msg": "54657374", "sig": "303f0282001d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 674, "comment": "length of integer contains leading 0", "msg": "54657374", "sig": "303f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930282001c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 675, "comment": "wrong length of integer", "msg": "54657374", "sig": "303d021e009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 676, "comment": "wrong length of integer", "msg": "54657374", "sig": "303d021c009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 677, "comment": "wrong length of integer", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021d5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 678, "comment": "wrong length of integer", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021b5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 679, "comment": "uint32 overflow in length of integer", "msg": "54657374", "sig": "30420285010000001d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 680, "comment": "uint32 overflow in length of integer", "msg": "54657374", "sig": "3042021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930285010000001c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 681, "comment": "uint64 overflow in length of integer", "msg": "54657374", "sig": "3046028901000000000000001d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 682, "comment": "uint64 overflow in length of integer", "msg": "54657374", "sig": "3046021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93028901000000000000001c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 683, "comment": "length of integer = 2**31 - 1", "msg": "54657374", "sig": "304102847fffffff009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 684, "comment": "length of integer = 2**31 - 1", "msg": "54657374", "sig": "3041021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee9302847fffffff5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 685, "comment": "length of integer = 2**32 - 1", "msg": "54657374", "sig": "30410284ffffffff009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 686, "comment": "length of integer = 2**32 - 1", "msg": "54657374", "sig": "3041021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930284ffffffff5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 687, "comment": "length of integer = 2**40 - 1", "msg": "54657374", "sig": "30420285ffffffffff009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 688, "comment": "length of integer = 2**40 - 1", "msg": "54657374", "sig": "3042021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930285ffffffffff5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 689, "comment": "length of integer = 2**64 - 1", "msg": "54657374", "sig": "30450288ffffffffffffffff009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 690, "comment": "length of integer = 2**64 - 1", "msg": "54657374", "sig": "3045021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930288ffffffffffffffff5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 691, "comment": "incorrect length of integer", "msg": "54657374", "sig": "303d02ff009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 692, "comment": "incorrect length of integer", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee9302ff5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 693, "comment": "removing integer", "msg": "54657374", "sig": "301e021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 694, "comment": "lonely integer tag", "msg": "54657374", "sig": "301f02021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 695, "comment": "lonely integer tag", "msg": "54657374", "sig": "3020021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee9302", "result": "invalid", "flags": []}, {"tcId": 696, "comment": "appending 0's to integer", "msg": "54657374", "sig": "303f021f009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930000021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 697, "comment": "appending 0's to integer", "msg": "54657374", "sig": "303f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021e5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000", "result": "invalid", "flags": []}, {"tcId": 698, "comment": "prepending 0's to integer", "msg": "54657374", "sig": "303f021f0000009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 699, "comment": "prepending 0's to integer", "msg": "54657374", "sig": "303f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021e00005fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 700, "comment": "appending unused 0's to integer", "msg": "54657374", "sig": "303f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930000021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 701, "comment": "appending null value to integer", "msg": "54657374", "sig": "303f021f009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930500021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 702, "comment": "appending null value to integer", "msg": "54657374", "sig": "303f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021e5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160500", "result": "invalid", "flags": []}, {"tcId": 703, "comment": "truncated length of integer", "msg": "54657374", "sig": "30200281021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 704, "comment": "truncated length of integer", "msg": "54657374", "sig": "3021021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930281", "result": "invalid", "flags": []}, {"tcId": 705, "comment": "Replacing integer with NULL", "msg": "54657374", "sig": "30200500021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 706, "comment": "Replacing integer with NULL", "msg": "54657374", "sig": "3021021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930500", "result": "invalid", "flags": []}, {"tcId": 707, "comment": "changing tag value of integer", "msg": "54657374", "sig": "303d001d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 708, "comment": "changing tag value of integer", "msg": "54657374", "sig": "303d011d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 709, "comment": "changing tag value of integer", "msg": "54657374", "sig": "303d031d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 710, "comment": "changing tag value of integer", "msg": "54657374", "sig": "303d041d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 711, "comment": "changing tag value of integer", "msg": "54657374", "sig": "303dff1d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 712, "comment": "changing tag value of integer", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93001c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 713, "comment": "changing tag value of integer", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93011c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 714, "comment": "changing tag value of integer", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93031c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 715, "comment": "changing tag value of integer", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93041c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 716, "comment": "changing tag value of integer", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93ff1c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 717, "comment": "dropping value of integer", "msg": "54657374", "sig": "30200200021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 718, "comment": "dropping value of integer", "msg": "54657374", "sig": "3021021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930200", "result": "invalid", "flags": []}, {"tcId": 719, "comment": "using composition for integer", "msg": "54657374", "sig": "30412221020100021c9b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 720, "comment": "using composition for integer", "msg": "54657374", "sig": "3041021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93222002015f021be8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 721, "comment": "modify first byte of integer", "msg": "54657374", "sig": "303d021d029b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 722, "comment": "modify first byte of integer", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5de8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 723, "comment": "modify last byte of integer", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee13021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 724, "comment": "modify last byte of integer", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b196", "result": "invalid", "flags": []}, {"tcId": 725, "comment": "truncated integer", "msg": "54657374", "sig": "303c021c009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 726, "comment": "truncated integer", "msg": "54657374", "sig": "303c021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021b5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1", "result": "invalid", "flags": []}, {"tcId": 727, "comment": "truncated integer", "msg": "54657374", "sig": "303c021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021be8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 728, "comment": "leading ff in integer", "msg": "54657374", "sig": "303e021eff009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 729, "comment": "leading ff in integer", "msg": "54657374", "sig": "303e021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021dff5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 730, "comment": "replaced integer by infinity", "msg": "54657374", "sig": "3021090180021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 731, "comment": "replaced integer by infinity", "msg": "54657374", "sig": "3022021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93090180", "result": "invalid", "flags": []}, {"tcId": 732, "comment": "replacing integer with zero", "msg": "54657374", "sig": "3021020100021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 733, "comment": "replacing integer with zero", "msg": "54657374", "sig": "3022021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93020100", "result": "invalid", "flags": []}, {"tcId": 734, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "54657374", "sig": "303d021d0156667b48514d3e5d546ca89ff45ada90474113ed248b873430ab57f0021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 735, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "54657374", "sig": "303c021ce0794dfb465b4e9d969cb3d0616b4b8468dbaf85aec085b2b7008536021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 736, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "54657374", "sig": "303d021dff64901b5e342bb9828a7b51c7d51cecf5a7f19e469659f98c8c2a116d021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 737, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "54657374", "sig": "303c021c1f86b204b9a4b16269634c2f9e94b47b9724507a513f7a4d48ff7aca021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 738, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "54657374", "sig": "303d021dfea99984b7aeb2c1a2ab9357600ba5256fb8beec12db7478cbcf54a810021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 739, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "54657374", "sig": "303d021d019b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 740, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "54657374", "sig": "303c021c64901b5e342bb9828a7b51c7d51cecf5a7f19e469659f98c8c2a116d021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 741, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "54657374", "sig": "303e021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021d011adeb9ed974f878dc2fc26f4bf86ffda5f7abe6c26ebabf9b8181a73", "result": "invalid", "flags": []}, {"tcId": 742, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021ca4f18ca08c5d97ce052c32252c9770ce81155a04b120aa783e6d47b9", "result": "invalid", "flags": []}, {"tcId": 743, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021ca017dcb8ee2970521bebd37309f0c7ab8fb7f3c793f9d4c704bd4eea", "result": "invalid", "flags": []}, {"tcId": 744, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "54657374", "sig": "303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5b0e735f73a26831fad3cddad3688f317eeaa5fb4edf5587c192b847", "result": "invalid", "flags": []}, {"tcId": 745, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "54657374", "sig": "303e021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021dfee521461268b078723d03d90b40790025a0854193d914540647e7e58d", "result": "invalid", "flags": []}, {"tcId": 746, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "54657374", "sig": "303e021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021d015fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116", "result": "invalid", "flags": []}, {"tcId": 747, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "54657374", "sig": "303e021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021d00a017dcb8ee2970521bebd37309f0c7ab8fb7f3c793f9d4c704bd4eea", "result": "invalid", "flags": []}, {"tcId": 748, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022020100021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 749, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 750, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 751, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 752, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3021020100021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 753, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3021020100021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 754, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 755, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 756, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 757, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022020100021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 758, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3082010802010002820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 759, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 760, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 761, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022020101021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 762, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 763, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 764, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 765, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3021020101021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 766, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3021020101021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 767, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 768, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 769, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 770, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022020101021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 771, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3082010802010102820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 772, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 773, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 774, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30220201ff021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 775, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 776, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 777, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 778, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30210201ff021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 779, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30210201ff021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 780, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 781, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 782, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 783, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30220201ff021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 784, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "308201080201ff02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 785, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 786, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 787, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 788, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 789, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 790, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 791, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 792, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 793, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 794, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 795, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 796, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 797, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30820123021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 798, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3023021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 799, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 800, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 801, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 802, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 803, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 804, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 805, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 806, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 807, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 808, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 809, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 810, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30820123021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 811, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3023021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 812, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 813, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 814, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 815, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 816, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 817, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 818, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 819, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 820, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 821, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 822, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 823, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 824, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 825, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 826, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 827, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 828, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 829, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 830, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 831, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 832, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 833, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 834, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 835, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 836, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 837, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 838, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 839, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 840, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 841, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 842, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 843, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 844, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 845, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 846, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 847, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 848, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 849, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 850, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 851, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 852, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 853, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022021d0100000000000000000000000000000000000000000000000000000000020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 854, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022021d0100000000000000000000000000000000000000000000000000000000020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 855, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022021d01000000000000000000000000000000000000000000000000000000000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 856, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021d0100000000000000000000000000000000000000000000000000000000021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 857, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303d021d0100000000000000000000000000000000000000000000000000000000021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 858, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 859, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 860, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 861, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 862, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "30820124021d010000000000000000000000000000000000000000000000000000000002820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 863, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3024021d0100000000000000000000000000000000000000000000000000000000090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 864, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3022021d0100000000000000000000000000000000000000000000000000000000090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 865, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 866, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 867, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 868, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd6670201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 869, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3082012302820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 870, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3082012302820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 871, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 872, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 873, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 874, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 875, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3082020a02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd66702820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 876, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3082010a02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 877, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 878, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3024090380fe01021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 879, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3008090380fe01020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 880, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3008090380fe01020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 881, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3008090380fe010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 882, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3023090380fe01021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 883, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3023090380fe01021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 884, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 885, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 886, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 887, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3024090380fe01021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 888, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3082010a090380fe0102820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 889, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "300a090380fe01090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 890, "comment": "Signatures with special case values for r and s.", "msg": "54657374", "sig": "3008090380fe01090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 891, "comment": "Signature encoding contains wrong type.", "msg": "54657374", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 892, "comment": "Signature encoding contains wrong type.", "msg": "54657374", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 893, "comment": "Signature encoding contains wrong type.", "msg": "54657374", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 894, "comment": "Signature encoding contains wrong type.", "msg": "54657374", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 895, "comment": "Signature encoding contains wrong type.", "msg": "54657374", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 896, "comment": "Signature encoding contains wrong type.", "msg": "54657374", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 897, "comment": "random signature", "msg": "54657374", "sig": "303c021c296410b8cb6200edafd1205e7377a09ad2011ac7b15b8bc9b9b4c6db021c25ca283c868dc2a5ce86aafcf681ce21d660b461da48270f15b53889", "result": "valid", "flags": []}, {"tcId": 898, "comment": "random signature", "msg": "54657374", "sig": "303d021c347c4f6875bf4476afbdd6b2b1f9e35c870e785e708e661109bd068e021d00b0b908a617d3ad6c8bc277f397095c00e659c86ca7c600090571ab17", "result": "valid", "flags": []}, {"tcId": 899, "comment": "random signature", "msg": "54657374", "sig": "303c021c3c76bc6f17369414d4c21c5361ed0cca6e79f73f90706f1f7ca9f05a021c3cc60d8a0d44fb967baa0e5621e12cd434aafd748cba3e7cdc733b2f", "result": "valid", "flags": []}, {"tcId": 900, "comment": "random signature", "msg": "54657374", "sig": "303e021d0086a5efea8e6a8033b8a0034b52ae614e1f14fbcbfa0bb50194efa6a7021d00b3d66f6d2b10cfe62fe96b78fcf41ca7b442aceb98ab109a01409e4a", "result": "valid", "flags": []}, {"tcId": 901, "comment": "random signature", "msg": "54657374", "sig": "303d021c16727d52bd711e9a63e0dd2c4db045cfb993942b1e39e4f43a65c11a021d009fb9c02d10c968e75bb15acab8467f30b84481f679e136e8af65a266", "result": "valid", "flags": []}]}, {"key": {"g": "16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde", "keySize": 2048, "p": "008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "q": "00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "type": "DsaPublicKey", "y": "00848177b9bcff136c52caef2a4a9bcb64dbefbac69e18aae499696b5ec7b270e90478b413bb8ad8f8eee8ad32107d7ba492c36b007f9ef30ebe1ee484d0ea7cb0ff4afaa8c705ad5e16576975414f1bc0efed25c2190a3ed0068bffa1f03bf6f21056c9bb383350851997cbc89cf8729b394527f08ab93ce9b360aa055a47177e82a4ce6fe76c8dffddbd6ee20fa08d0085d3983edd2c8d9a366ad2245b4ed28d6754769f5f3a798be4be19cf469399865d464e3f640438bce03c962c2344d0d550542aed3db55c153833bea44b4146878ba347c8614436c6aac4fd1a60f25c62b3f869a7d55cab4b7122d5e9af4322a3fc8214fa55dc1ee021459fb2c4595827"}, "keyDer": "308203433082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde03820106000282010100848177b9bcff136c52caef2a4a9bcb64dbefbac69e18aae499696b5ec7b270e90478b413bb8ad8f8eee8ad32107d7ba492c36b007f9ef30ebe1ee484d0ea7cb0ff4afaa8c705ad5e16576975414f1bc0efed25c2190a3ed0068bffa1f03bf6f21056c9bb383350851997cbc89cf8729b394527f08ab93ce9b360aa055a47177e82a4ce6fe76c8dffddbd6ee20fa08d0085d3983edd2c8d9a366ad2245b4ed28d6754769f5f3a798be4be19cf469399865d464e3f640438bce03c962c2344d0d550542aed3db55c153833bea44b4146878ba347c8614436c6aac4fd1a60f25c62b3f869a7d55cab4b7122d5e9af4322a3fc8214fa55dc1ee021459fb2c4595827", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIDQzCCAjUGByqGSM44BAEwggIoAoIBAQCPeTXZuarpv6vtiHrPSVG28y7Fnjuv\nNxjo6sSWHz79NgbnQ1GpxBgzObgJ58KuHFObp0dbhdARrbi0eYd1SYRpXKwOjxSz\nNggooi/6JxEKPWKpk0U0CaD+aWxGWPhL3SCBnDcJoBBXsZWtzQAjPbpUhLYpH51k\njviDRIZ3l5zsBLQ0pqwudemYXeI9sCkvwRGMn/qdgYHnM423krcw17njSVkvaAmY\nchU5Feo9a4tGU8YzRY+AOzKkwuDycpAlbk4/ijsIOKHEUOThjBopo33fXqFD3ktm\n/wSQPtXPFiPhWNSHxgjpfyEc2B3KI8tuOAdl+CLjQr5ITAV2OTlgHNZnAh0AuvaW\npoV499/e5/pnyXfHhe8ysjO65YDAvNVpXQKCAQAWplxYIEhQcE51AqOXVwQNNNo6\nNHjBVNTkpcAtJC7gT5bmHkvQkEq9rI837rHgnzGC0jyQQ8tkL4gAQWDt+coJsyB2\np5wypifyRz6Rh5uixOdEvSCBVEy1W4AsNo0fqD7UielOD6BojjJCilx4xHjGjQUn\ntxyaOrsLC+EsRGiWOefTznTbEBplqiuH9kxoJts+xy9LVZmDS7TtsC98kOmkltOl\nXVNb6/xF1PYZ9j897buHOSXC8iTgdzEpbaiH7B5HSPh++1/et1SEMWsiMt7lU92v\nAhErDR8C2jCXMiT+J67ai51LKSLZuovjntnhA6Y8UoELxoi34u1DFuHvF9veA4IB\nBgACggEBAISBd7m8/xNsUsrvKkqby2Tb77rGnhiq5Jlpa17HsnDpBHi0E7uK2Pju\n6K0yEH17pJLDawB/nvMOvh7khNDqfLD/SvqoxwWtXhZXaXVBTxvA7+0lwhkKPtAG\ni/+h8Dv28hBWybs4M1CFGZfLyJz4cps5RSfwirk86bNgqgVaRxd+gqTOb+dsjf/d\nvW7iD6CNAIXTmD7dLI2aNmrSJFtO0o1nVHafXzp5i+S+Gc9Gk5mGXUZOP2QEOLzg\nPJYsI0TQ1VBUKu09tVwVODO+pEtBRoeLo0fIYUQ2xqrE/Rpg8lxis/hpp9Vcq0tx\nItXpr0Mio/yCFPpV3B7gIUWfssRZWCc=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "DsaVerify", "tests": [{"tcId": 902, "comment": "r,s = 1,1", "msg": "54657374", "sig": "3006020101020101", "result": "valid", "flags": []}, {"tcId": 903, "comment": "r,s = 1,5", "msg": "54657374", "sig": "3006020101020105", "result": "valid", "flags": []}, {"tcId": 904, "comment": "u2 small", "msg": "54657374", "sig": "3022020101021d009592121ed12d93197f1ffb863ac63937f28ef4f62f1e009a30aabab1", "result": "valid", "flags": []}, {"tcId": 905, "comment": "s == q-1", "msg": "54657374", "sig": "3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "valid", "flags": []}]}, {"key": {"g": "16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde", "keySize": 2048, "p": "008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "q": "00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "type": "DsaPublicKey", "y": "629374929537e2c3b09f30d881554ca7357f89e25105474dbbce06e4001efd61481a457aa0d7d7e565e90b7a3d9c688005fb404bf3b6d3e61e402300beee7c58ceeaf00b112ddfeef3cbc2020ba2206dd4ef0563d7fa52c321b4ee6280eb8585041d03cadb9244dff21dc90417bbe6f06b91c2ca6484437c3846926b18ee22275081b60726e7a26a29a947eabd035ede83d65927b3ceb0d4d8c2f34e94a3de0f57e4ea99af059657529f6954b1ac9bb4484ca76b4083e1cf4264eff028662137761e4d7f35b1eda3cf516856f25553840e43ae38379d234b06c891822132081d19f0d5db9f23b4bbd5f5667dd78f3dd7f1fe5f25ca48515f6335ce1c9fd0a64b"}, "keyDer": "308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde038201050002820100629374929537e2c3b09f30d881554ca7357f89e25105474dbbce06e4001efd61481a457aa0d7d7e565e90b7a3d9c688005fb404bf3b6d3e61e402300beee7c58ceeaf00b112ddfeef3cbc2020ba2206dd4ef0563d7fa52c321b4ee6280eb8585041d03cadb9244dff21dc90417bbe6f06b91c2ca6484437c3846926b18ee22275081b60726e7a26a29a947eabd035ede83d65927b3ceb0d4d8c2f34e94a3de0f57e4ea99af059657529f6954b1ac9bb4484ca76b4083e1cf4264eff028662137761e4d7f35b1eda3cf516856f25553840e43ae38379d234b06c891822132081d19f0d5db9f23b4bbd5f5667dd78f3dd7f1fe5f25ca48515f6335ce1c9fd0a64b", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIDQjCCAjUGByqGSM44BAEwggIoAoIBAQCPeTXZuarpv6vtiHrPSVG28y7Fnjuv\nNxjo6sSWHz79NgbnQ1GpxBgzObgJ58KuHFObp0dbhdARrbi0eYd1SYRpXKwOjxSz\nNggooi/6JxEKPWKpk0U0CaD+aWxGWPhL3SCBnDcJoBBXsZWtzQAjPbpUhLYpH51k\njviDRIZ3l5zsBLQ0pqwudemYXeI9sCkvwRGMn/qdgYHnM423krcw17njSVkvaAmY\nchU5Feo9a4tGU8YzRY+AOzKkwuDycpAlbk4/ijsIOKHEUOThjBopo33fXqFD3ktm\n/wSQPtXPFiPhWNSHxgjpfyEc2B3KI8tuOAdl+CLjQr5ITAV2OTlgHNZnAh0AuvaW\npoV499/e5/pnyXfHhe8ysjO65YDAvNVpXQKCAQAWplxYIEhQcE51AqOXVwQNNNo6\nNHjBVNTkpcAtJC7gT5bmHkvQkEq9rI837rHgnzGC0jyQQ8tkL4gAQWDt+coJsyB2\np5wypifyRz6Rh5uixOdEvSCBVEy1W4AsNo0fqD7UielOD6BojjJCilx4xHjGjQUn\ntxyaOrsLC+EsRGiWOefTznTbEBplqiuH9kxoJts+xy9LVZmDS7TtsC98kOmkltOl\nXVNb6/xF1PYZ9j897buHOSXC8iTgdzEpbaiH7B5HSPh++1/et1SEMWsiMt7lU92v\nAhErDR8C2jCXMiT+J67ai51LKSLZuovjntnhA6Y8UoELxoi34u1DFuHvF9veA4IB\nBQACggEAYpN0kpU34sOwnzDYgVVMpzV/ieJRBUdNu84G5AAe/WFIGkV6oNfX5WXp\nC3o9nGiABftAS/O20+YeQCMAvu58WM7q8AsRLd/u88vCAguiIG3U7wVj1/pSwyG0\n7mKA64WFBB0DytuSRN/yHckEF7vm8GuRwspkhEN8OEaSaxjuIidQgbYHJueiaimp\nR+q9A17eg9ZZJ7POsNTYwvNOlKPeD1fk6pmvBZZXUp9pVLGsm7RITKdrQIPhz0Jk\n7/AoZiE3dh5NfzWx7aPPUWhW8lVThA5Drjg3nSNLBsiRgiEyCB0Z8NXbnyO0u9X1\nZn3Xjz3X8f5fJcpIUV9jNc4cn9CmSw==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "DsaVerify", "tests": [{"tcId": 906, "comment": "s == 1", "msg": "54657374", "sig": "3021021c5a252f4fc55618747fd94b13c9bee62bb958d85777cb07dd90710d24020101", "result": "valid", "flags": []}]}]}