from typing import Any, Optional

JavaClass = Any
JavaObject = Any

def find_class(class_name: str) -> Optional[Any]: ...
def get_private_field(obj: Any, field_name: str) -> Optional[Any]: ...
def set_private_field(obj: Any, field_name: str, new_value: Any) -> bool: ...
def get_static_private_field(clazz: Any, field_name: str) -> Optional[Any]: ...
def set_static_private_field(clazz: Any, field_name: str, new_value: Any) -> bool: ...