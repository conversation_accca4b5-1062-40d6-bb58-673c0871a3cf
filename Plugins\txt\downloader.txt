import os
import uuid
import time
import requests
import traceback
import threading
import socket
from java.util import Locale
from ui.settings import Header, Input, Divider, Text, Switch, Selector
from base_plugin import BasePlugin, HookResult, HookStrategy, MenuItemData, MenuItemType
from android_utils import log, run_on_ui_thread
from ui.bulletin import BulletinHelper
from ui.alert import AlertDialogBuilder
from java.io import File
from client_utils import get_account_instance, get_media_data_controller, get_last_fragment, get_messages_controller
from org.telegram.tgnet import TLRPC
from org.telegram.messenger import ApplicationLoader, SendMessagesHelper
from java.util import ArrayList
from android.content import ClipData, Context, Intent
from com.exteragram.messenger.plugins import PluginsController
from com.exteragram.messenger.plugins.ui import PluginSettingsActivity
from android.net import Uri

__name__ = "Media Downloader"
__description__ = "Download and upload media (photo, video, audio) from Internet"
__icon__ = "exteraPluginsSup/0"
__version__ = "1.6.5"
__id__ = "downloader"
__author__ = "@itsv1eds"
__min_version__ = "11.9.1"

DEFAULT_COBALT_API = "https://co.itsv1eds.ru"
COBALT_API = [DEFAULT_COBALT_API, "https://co.eepy.today", "https://co.otomir23.me"]
TEMP_DIR_NAME = "DownloaderTemp"
VIDEO_QUALITY_OPTIONS = ["144","240","360","480","720","1080","1440","2160","4320","max"]
DOWNLOAD_MODE_OPTIONS = ["auto","audio","mute"]
AUDIO_BITRATE_OPTIONS = ["64","96","128","192","256","320"]
YOUTUBE_VIDEO_CODEC_OPTIONS = ["h264","av1","vp9"]
SERVICES_INFO = "YouTube, TikTok, Instagram, Twitter/X, VK (Video/Clips), Reddit, SoundCloud, Facebook, Pinterest, RuTube"
progress_dialog = None

TRANSLATIONS = {
    "api_title": ("Настройки API", "API Settings", "Configurações de API"),
    "api_url": ("Cobalt API URL", "Cobalt API URL", "URL da API Cobalt"),
    "api_key": ("API ключ", "API Key", "Chave da API"),
    "api_help": ("Справка", "Help", "Ajuda"),
    "api_desc": ("Укажите URL и API (если требуется) вашего Cobalt инстанса. Узнать больше: github.com/imputnet/cobalt", "Enter URL and API (if needed) for your Cobalt instance. Learn more: github.com/imputnet/cobalt", "Insira a URL e a API (se necessário) para sua instância do Cobalt. Saiba mais: github.com.imputnet/cobalt"),
    "custom_api": ("Свой API", "Custom API", "API personalizado"),
    "custom_api_url": ("Свой URL API", "Custom API URL", "URL da API personalizada"),
    "proxy_title": ("Настройки прокси", "Proxy Settings", "Configurações de proxy"),
    "proxy_type": ("Тип прокси", "Proxy Type", "Tipo de proxy"),
    "proxy_type_items": (["Нет", "HTTP", "HTTPS", "SOCKS"], ["None", "HTTP", "HTTPS", "SOCKS"], ["Nenhum", "HTTP", "HTTPS", "SOCKS"]),
    "proxy_url": ("URL прокси", "Proxy URL", "URL do proxy"),
    "proxy_url_desc": ("Введите прокси host:port", "Enter proxy host:port", "Digite host:porta do proxy"),
    "proxy_user": ("Логин прокси", "Proxy Username", "Usuário do proxy"),
    "proxy_password": ("Пароль прокси", "Proxy Password", "Senha do proxy"),
    "settings_title": ("Настройки загрузки", "Download Settings", "Configurações de download"),
    "include_source": ("Включить ссылку источника", "Include source link", "Incluir link de origem"),
    "include_source_desc": ("Добавить ссылку источника в подпись сообщения", "Add source link to message caption", "Adicionar link de origem na legenda da mensagem"),
    "send_as_file": ("Отправлять как файл", "Send as file", "Enviar como arquivo"),
    "send_as_file_desc": ("Скачанный контент будет отправлен как документ", "Downloaded media will be sent as document", "O conteúdo baixado será enviado como documento"),
    "advanced_settings": ("Дополнительные настройки", "Advanced Settings", "Configurações avançadas"),
    "advanced_settings_desc": ("Показать качество видео, режим загрузки, битрейт аудио и приоритет хука", "Show video quality, download mode, audio bitrate, and hook priority", "Mostrar qualidade de vídeo, modo de download, taxa de bits de áudio e prioridade do hook"),
    "video_quality": ("Качество видео", "Video Quality", "Qualidade do vídeo"),
    "download_mode": ("Режим загрузки", "Download Mode", "Modo de download"),
    "audio_bitrate": ("Битрейт аудио", "Audio Bitrate", "Taxa de bits de áudio"),
    "youtube_video_codec": ("Кодек видео YouTube", "YouTube Video Codec", "Codec de vídeo do YouTube"),
    "priority": ("Приоритет хука", "Hook Priority", "Prioridade do hook"),
    "priority_desc": ("Порядок выполнения хуков при отправке сообщений (если не знаете, что это такое, не меняйте)", "Execution order for send message hooks (if you don't know what this is, don't change it)", "Ordem de execução dos hooks de envio de mensagem (se você não sabe o que é isso, não altere)"),
    "services": ("Поддерживаемые сервисы", "Supported services", "Serviços suportados"),
    "usage_cmd": (".down/.dl [URL] - Скачивает и отправляет медиа\nПример: .dl youtube.com/watch?v=dQw4w9WgXcQ", "Command: .down/.dl [URL] - Download and send video/audio\nExample: .dl youtube.com/watch?v=dQw4w9WgXcQ", "Comando: .down/.dl [URL] - Baixar e enviar vídeo/áudio\nExemplo: .dl youtube.com/watch?v=dQw4w9WgXcQ"),
    "donate_title": ("Поддержать разработку", "Support development", "Apoiar desenvolvimento"),
    "donate_info": ("Другая информация и реквизиты", "Other info and requisites", "Outras informações e requisitos"),
    "convert_gif": ("Конвертировать GIF", "Convert GIF", "Converter GIF"),
    "convert_gif_desc": ("Конвертировать GIF из Twitter в настоящий формат GIF; Не включать, все равно не отправит GIF", "Convert Twitter GIFs to actual GIF format; the GIF itself will not be sent", "Converter GIFs do Twitter para o formato GIF real; o GIF em si não é enviado"),
    "disable_metadata": ("Отключить метаданные", "Disable metadata", "Desabilitar metadados"),
    "disable_metadata_desc": ("Название, исполнитель и другие данные не будут добавлены в файл", "Title, artist, and other info will not be added to the file", "Título, artista e outras informações não serão adicionadas ao arquivo"),
    "show_settings_buttons": ("Кнопка настроек в меню", "Settings button in menu", "Botão de configurações no menu"),
    "show_settings_buttons_desc": ("Добавляет кнопку открытия настроек плагина в меню", "Adds plugin settings button to menu", "Adiciona botão de configurações do plugin no menu"),
}

def Z(key):
    lang = Locale.getDefault().getLanguage()
    if lang.startswith('ru'):
        idx = 0
    elif lang.startswith('pt'):
        idx = 2
    else:
        idx = 1
    return TRANSLATIONS.get(key, (None, None, None))[idx]

class VideoDownloaderPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self._temp_dir = None
        self._cancel_requested = False
        self._drawer_settings_item = None
        self._chat_settings_item = None

    def _open_plugin_settings(self, java_plugin):
        try:
            get_last_fragment().presentFragment(PluginSettingsActivity(java_plugin))
        except Exception as e:
            log(f"[{__id__}] Error opening plugin settings: {e}")

    def _add_settings_menu_items(self):
        try:
            self._drawer_settings_item = self.add_menu_item(MenuItemData(
                menu_type=MenuItemType.DRAWER_MENU,
                text=Z("settings_title"),
                icon="msg_settings_14",
                priority=5,
                on_click=lambda ctx: run_on_ui_thread(lambda: self._open_plugin_settings(PluginsController.getInstance().plugins.get(self.id)))
            ))
            self._chat_settings_item = self.add_menu_item(MenuItemData(
                menu_type=MenuItemType.CHAT_ACTION_MENU,
                text=Z("settings_title"),
                icon="msg_settings_14",
                priority=5,
                on_click=lambda ctx: run_on_ui_thread(lambda: self._open_plugin_settings(PluginsController.getInstance().plugins.get(self.id)))
            ))
        except Exception as e:
            log(f"[{__id__}] Failed to add settings menu items: {e}")

    def _socks_create_connection(self, dest_host, dest_port):
        url_setting = self.get_setting('proxy_url_set','').split('://')[-1].strip()
        if not url_setting or ':' not in url_setting:
            return socket.create_connection((dest_host, dest_port))
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        url = url_setting
        user = self.get_setting('proxy_username_set','').strip()
        pwd = self.get_setting('proxy_password_set','').strip()
        host, port = url.split(':',1)
        sock.connect((host, int(port)))
        methods = b'\x02' if user and pwd else b'\x00'
        sock.send(b'\x05\x01' + methods)
        _, nm = sock.recv(2)
        if nm == 2:
            sock.send(b'\x01' + bytes([len(user)]) + user.encode() + bytes([len(pwd)]) + pwd.encode())
            sock.recv(2)
        data = dest_host.encode()
        sock.send(b'\x05\x01\x00\x03' + bytes([len(data)]) + data + int(dest_port).to_bytes(2,'big'))
        sock.recv(4)
        return sock

    def _get_proxies(self):
        proxy_type = self.get_setting("proxy_type_set", 0)
        url = self.get_setting("proxy_url_set", "").strip()
        user = self.get_setting("proxy_username_set", "").strip()
        pwd = self.get_setting("proxy_password_set", "").strip()
        proxies = None
        if proxy_type == 3 and url:
            try:
                m = requests.packages.urllib3.util.connection
                m.create_connection = lambda addr, timeout=None, **kw: self._socks_create_connection(addr[0], addr[1])
            except Exception as e:
                log(f"[{__id__}] Failed patching socks proxy: {e}")
            hostport = url.split('://',1)[-1]
            scheme = f"socks5h://{user}:{pwd}@{hostport}" if user and pwd else f"socks5h://{hostport}"
            proxies = {"http": scheme, "https": scheme}
        elif proxy_type in (1,2):
            scheme = "http" if proxy_type == 1 else "https"
            if url:
                if "://" not in url:
                    url = f"{scheme}://{url}"
                if user and pwd:
                    parts = url.split("://",1)
                    url = f"{parts[0]}://{user}:{pwd}@{parts[1]}"
                proxies = {"http": url, "https": url}
        return proxies

    def on_plugin_load(self):
        hook_priority = self.get_setting("hook_priority_set", 1)
        try:
            priority = int(hook_priority)
        except:
            priority = 1
        self.add_on_send_message_hook(priority)
        self._temp_dir = self._get_temp_dir()
        if self._temp_dir:
            log("VideoDownloaderPlugin loaded")
            self._cleanup_old_files()
            try:
                if self.get_setting("show_settings_buttons_set", True):
                    self._add_settings_menu_items()
            except Exception as e:
                log(f"[{__id__}] Failed to add settings menu items: {e}")
        else:
            log("Failed to initialize temp directory")

    def create_settings(self):
        lang = Locale.getDefault().getLanguage()
        if lang.startswith('ru'):
            idx = 0
        elif lang.startswith('pt'):
            idx = 2
        else:
            idx = 1
        api_idx = self.get_setting("api_url_choice_set", 0)
        proxy_type_val = self.get_setting("proxy_type_set", 0)
        proxy_icon = "msg2_proxy_off" if proxy_type_val == 0 else "msg2_proxy_on"
        settings = [
            Header(text=Z("api_title")),
            Selector(key="api_url_choice_set", text=Z("api_url"), icon="msg2_devices", default=api_idx, items=COBALT_API + [Z("custom_api")]),
        ]
        if api_idx == len(COBALT_API):
            settings.extend([
                Input(key="api_url_set", text=Z("custom_api_url"), icon="msg_instant_link", default=self.get_setting("api_url_set", "")),
                Input(key="api_key_set", text=Z("api_key"), icon="msg_pin_code", default=self.get_setting("api_key_set", "")),
                Text(text=Z("api_help"), icon="msg_psa", accent=True, on_click=lambda view: self.show_my_info_alert(title=Z("api_title"), message=Z("api_desc"), neutral_button="GitHub", neutral_link="https://github.com/imputnet/cobalt", neutral_type="link")),
            ])
        settings.extend([
            Header(text=Z("proxy_title")),
            Selector(key="proxy_type_set", text=Z("proxy_type"), icon=proxy_icon, default=proxy_type_val, items=TRANSLATIONS["proxy_type_items"][idx]),
        ])
        if self.get_setting("proxy_type_set", 0) != 0:
            settings.extend([
                Input(key="proxy_url_set", text=Z("proxy_url"), icon="msg_link", default="", subtext=Z("proxy_url_desc")),
                Input(key="proxy_username_set", text=Z("proxy_user"), icon="msg_contacts", default=""),
                Input(key="proxy_password_set", text=Z("proxy_password"), icon="msg_pin_code", default=""),
            ])
        settings.extend([
            Header(text=Z("settings_title")),
            Switch(key="show_settings_buttons_set", text=Z("show_settings_buttons"), icon="msg_reorder", default=self.get_setting("show_settings_buttons_set", True), subtext=Z("show_settings_buttons_desc"), on_change=self._on_show_settings_buttons_change),
            Switch(key="include_source_set", text=Z("include_source"), icon="msg_link", default=self.get_setting("include_source_set", True), subtext=Z("include_source_desc")),
            Switch(key="send_as_file_set", text=Z("send_as_file"), icon="msg_sendfile", default=self.get_setting("send_as_file_set", False), subtext=Z("send_as_file_desc")),
        ])
        if self.get_setting("send_as_file_set", False):
            settings.extend([
                Switch(key="disable_metadata_set", text=Z("disable_metadata"), icon="menu_intro", default=self.get_setting("disable_metadata_set", False), subtext=Z("disable_metadata_desc")),
            ])
        settings.extend([
            Switch(key="show_advanced_set", text=Z("advanced_settings"), icon="msg_settings_14", default=self.get_setting("show_advanced_set", False), subtext=Z("advanced_settings_desc")),
        ])
        if self.get_setting("show_advanced_set", False):
            settings.extend([
                Selector(key="video_quality_set", text=Z("video_quality"), icon="msg_video", default=self.get_setting("video_quality_set", 5), items=VIDEO_QUALITY_OPTIONS),
                Selector(key="download_mode_set", text=Z("download_mode"), icon="msg_gallery", default=self.get_setting("download_mode_set", 0), items=DOWNLOAD_MODE_OPTIONS),
                Selector(key="audio_bitrate_set", text=Z("audio_bitrate"), icon="input_mic", default=self.get_setting("audio_bitrate_set", 5), items=AUDIO_BITRATE_OPTIONS),
                Selector(key="youtube_video_codec_set", text=Z("youtube_video_codec"), icon="menu_feature_pack", default=self.get_setting("youtube_video_codec_set", 0), items=YOUTUBE_VIDEO_CODEC_OPTIONS),
                Input(key="hook_priority_set", text=Z("priority"), icon="menu_random", default=self.get_setting("hook_priority_set", 1), subtext=Z("priority_desc")),
                Switch(key="convert_gif_set", text=Z("convert_gif"), icon="msg_gif", default=self.get_setting("convert_gif_set", False), subtext=Z("convert_gif_desc")),
            ])
        settings.extend([
            Text(text=Z("services"), accent=True, icon="menu_premium_main", on_click=lambda view: self.show_my_info_alert(title=Z("services"),message=SERVICES_INFO)),
            Divider(text=Z("usage_cmd")),
            Header(text=Z("donate_title")),
            Text(text="TON", icon="msg_ton", accent=True, on_click=lambda view: run_on_ui_thread(lambda: self._copy_to_clipboard("TON", "exteralover.ton"))),
            Text(text=Z("donate_info"), icon="msg_reactions", accent=True, on_click=lambda view: run_on_ui_thread(lambda: get_messages_controller().openByUserName("v1edsinfo", get_last_fragment(), 1))),
        ])
        return settings

    def _get_temp_dir(self):
        try:
            base_dir = ApplicationLoader.getFilesDirFixed()
            if not base_dir:
                return None
            temp_dir = File(base_dir, TEMP_DIR_NAME)
            if not temp_dir.exists() and not temp_dir.mkdirs():
                return None
            return temp_dir
        except Exception as e:
            log(f"Error creating temp dir: {e}")
            return None

    def _cleanup_old_files(self, max_age_hours=12):
        try:
            now = time.time()
            max_age_seconds = max_age_hours * 3600
            for file in self._temp_dir.listFiles():
                if file.isFile() and now - file.lastModified() / 1000 > max_age_seconds:
                    file.delete()
        except Exception as e:
            log(f"Cleanup error: {e}")

    def _download_video(self, video_url, mode_override=None):
        try:
            api_idx = self.get_setting("api_url_choice_set", 0)
            if api_idx < len(COBALT_API):
                api_url = COBALT_API[api_idx].rstrip('/')
            else:
                custom = self.get_setting("api_url_set", "")
                api_url = custom.rstrip('/') if custom else DEFAULT_COBALT_API
            api_key = self.get_setting("api_key_set", "").strip()
            proxies = self._get_proxies()
            log(f"Using proxies: {proxies}")
            quality_setting = self.get_setting("video_quality_set", 4)
            if isinstance(quality_setting, int) and 0 <= quality_setting < len(VIDEO_QUALITY_OPTIONS):
                video_quality = VIDEO_QUALITY_OPTIONS[quality_setting]
            else:
                video_quality = str(quality_setting)
            if 'youtube.com' in video_url.lower() or 'youtu.be' in video_url.lower():
                video_quality = VIDEO_QUALITY_OPTIONS[-1]
            payload = {
                "url": video_url,
                "videoQuality": video_quality
            }
            mode_setting = self.get_setting("download_mode_set", 0)
            if isinstance(mode_setting, int) and 0 <= mode_setting < len(DOWNLOAD_MODE_OPTIONS):
                download_mode = DOWNLOAD_MODE_OPTIONS[mode_setting]
            else:
                download_mode = str(mode_setting)
            payload["downloadMode"] = mode_override if mode_override in DOWNLOAD_MODE_OPTIONS else download_mode
            bitrate_setting = self.get_setting("audio_bitrate_set", 5)
            if isinstance(bitrate_setting, int) and 0 <= bitrate_setting < len(AUDIO_BITRATE_OPTIONS):
                audio_bitrate = AUDIO_BITRATE_OPTIONS[bitrate_setting]
            else:
                audio_bitrate = str(bitrate_setting)
            if 'youtube.com' in video_url.lower() or 'youtu.be' in video_url.lower():
                audio_bitrate = AUDIO_BITRATE_OPTIONS[-1]
            payload["audioBitrate"] = audio_bitrate
            youtube_video_codec_setting = self.get_setting("youtube_video_codec_set", 0)
            if isinstance(youtube_video_codec_setting, int) and 0 <= youtube_video_codec_setting < len(YOUTUBE_VIDEO_CODEC_OPTIONS):
                youtube_video_codec = YOUTUBE_VIDEO_CODEC_OPTIONS[youtube_video_codec_setting]
            else:
                youtube_video_codec = str(youtube_video_codec_setting)
            payload["youtubeVideoCodec"] = youtube_video_codec
            if self.get_setting("disable_metadata_set", False):
                payload["disableMetadata"] = True
            if self.get_setting("convert_gif_set", False) and 'twitter.com' in video_url.lower():
                payload["convertGif"] = True

            headers = {"Accept": "application/json", "Content-Type": "application/json"}
            if api_key:
                headers["Authorization"] = f"Api-Key {api_key}"

            log(f"Calling Cobalt API in background thread: {video_url}")
            log(f"Downloader request -> URL: {api_url}/, payload: {payload}, headers: {headers}")

            try:
                resp = requests.post(f"{api_url}/", json=payload, headers=headers, timeout=30, proxies=proxies)
                resp.raise_for_status()
            except Exception as e:
                log(f"[{__id__}] HTTPS request failed: {e}")
                if api_url.startswith("https://"):
                    fallback_url = api_url.replace("https://", "http://")
                    log(f"[{__id__}] Retrying Cobalt API with HTTP: {fallback_url}")
                    resp = requests.post(f"{fallback_url}/", json=payload, headers=headers, timeout=30, proxies=proxies)
                    resp.raise_for_status()
                else:
                    raise

            data = resp.json()
            log(f"[{__id__}] Cobalt response status={data.get('status')} keys={list(data.keys())}")
            log(f"[{__id__}] Cobalt full response: {data}")
            status = data.get("status")

            if status == "error":
                code = data.get("error", {}).get("code", "Unknown error")
                BulletinHelper.show_error(f"Cobalt API error: {code}")
                return None
            if status == "picker":
                items = data.get("picker", [])
                if not items:
                    BulletinHelper.show_error("No items to download")
                    return None
                item = items[0]
                direct_url = item.get("url")
                filename = item.get("filename")
            else:
                direct_url = data.get("url")
                filename = data.get("filename")

            if not direct_url:
                BulletinHelper.show_error("Invalid Cobalt API response")
                return None

            filename = filename or f"video_{uuid.uuid4()}.mp4"
            file_path = File(self._temp_dir, filename).getAbsolutePath()

            log(f"Downloading video in background thread: {filename}")
            try:
                video_resp = requests.get(direct_url, stream=True, timeout=60, proxies=proxies)
                video_resp.raise_for_status()
            except Exception as e:
                log(f"[{__id__}] Download failed: {e}")
                if direct_url.startswith("https://"):
                    fallback_dl = direct_url.replace("https://", "http://")
                    log(f"[{__id__}] Retrying direct download with HTTP: {fallback_dl}")
                    video_resp = requests.get(fallback_dl, stream=True, timeout=60, proxies=proxies)
                    video_resp.raise_for_status()
                else:
                    raise

            content_length = video_resp.headers.get("content-length")
            total_length = int(content_length) if content_length else 0
            downloaded = 0

            with open(file_path, "wb") as f:
                for chunk in video_resp.iter_content(chunk_size=8192):
                    if self._cancel_requested:
                        log("Download canceled by user")
                        try:
                            os.remove(file_path)
                        except Exception:
                            pass
                        return None
                    f.write(chunk)
                    if total_length:
                        downloaded += len(chunk)
                        percent = int(downloaded * 100 / total_length)
                        run_on_ui_thread(lambda p=percent: progress_dialog.set_progress(p))

            try:
                size = os.path.getsize(file_path)
                log(f"Downloaded file saved at: {file_path}, size: {size} bytes")
            except Exception as dbg_e:
                log(f"Debug error checking file: {dbg_e}")

            return file_path
        except Exception as e:
            self._dismiss_dialog()
            log(f"Download error in background thread: {e}\n{traceback.format_exc()}")
            BulletinHelper.show_error(f"Download error: {e}")
            return None

    def send_video(self, video_path: str, dialog_id: int, caption: str = None, notify: bool = True, schedule_date: int = 0, reply_to_msg=None, reply_to_top_msg=None):
        ttl = 0
        force_document = False
        has_media_spoilers = False
        cover_path = None
        quick_reply_shortcut = None
        quick_reply_shortcut_id = 0
        effect_id = 0
        stars = 0
        log(f"VideoDownloaderPlugin: send_video entry for path: {video_path}, dialog: {dialog_id} (from background thread)")
        ext = os.path.splitext(video_path)[1].lower()
        if ext in [".mp3", ".wav", ".ogg", ".opus", ".m4a"]:
            return self.send_audio(video_path, dialog_id, caption, notify, schedule_date, reply_to_msg, reply_to_top_msg)
        try:
            account_instance = get_account_instance()
            if account_instance is None:
                log("Error: Could not get AccountInstance.")
                return
            entities = None
            if caption:
                mdc = get_media_data_controller()
                if mdc:
                    entities = mdc.getEntities([caption], True)

            SendMessagesHelper.prepareSendingVideo(
                account_instance,
                video_path,
                None,
                cover_path,
                None,
                dialog_id,
                reply_to_msg,
                reply_to_top_msg,
                None,
                None,
                entities,
                ttl,
                None,
                notify,
                schedule_date,
                force_document,
                has_media_spoilers,
                caption,
                quick_reply_shortcut,
                quick_reply_shortcut_id,
                effect_id,
                stars
            )
            log(f"Video sending initiated for path: {video_path} to dialog: {dialog_id} (from background thread)")

        except Exception as e:
            log(f"Error preparing video for sending in background thread: {e}\n{traceback.format_exc()}")
            BulletinHelper.show_error(f"Error sending video: {e}")

    def send_audio(self, audio_path: str, dialog_id: int, caption: str = None, notify: bool = True, schedule_date: int = 0, reply_to_msg=None, reply_to_top_msg=None):
        log(f"VideoDownloaderPlugin: send_audio entry for path: {audio_path}, dialog: {dialog_id}")
        try:
            account_instance = get_account_instance()
            if account_instance is None:
                log("Error: Could not get AccountInstance.")
                return
            import mimetypes
            mime, _ = mimetypes.guess_type(audio_path)
            if mime is None:
                ext = os.path.splitext(audio_path)[1].lower()
                if ext == ".mp3":
                    mime = "audio/mpeg"
                elif ext == ".wav":
                    mime = "audio/wav"
                elif ext in [".ogg", ".opus", ".m4a"]:
                    mime = "audio/ogg"
                else:
                    mime = "application/octet-stream"
            ext_cache_root = ApplicationLoader.applicationContext.getExternalCacheDir()
            plugin_ext_dir = File(ext_cache_root, TEMP_DIR_NAME)
            if not plugin_ext_dir.exists() and not plugin_ext_dir.mkdirs():
                log("Failed to create external temp dir")
            external_path = File(plugin_ext_dir, File(audio_path).getName()).getAbsolutePath()
            with open(audio_path, 'rb') as f_in, open(external_path, 'wb') as f_out:
                while True:
                    chunk = f_in.read(8192)
                    if not chunk:
                        break
                    f_out.write(chunk)
            audio_path = external_path
            SendMessagesHelper.prepareSendingDocument(
                account_instance,
                audio_path,
                audio_path,
                None,
                caption,
                mime,
                dialog_id,
                reply_to_msg, reply_to_top_msg, None, None, None,
                notify, schedule_date, None, None, 0, False
            )
        except Exception as e:
            log(f"Error preparing audio for sending: {e}, type: {type(e)}")
            log(traceback.format_exc())
            BulletinHelper.show_error(f"Error sending audio: {e}")

    def _delete_file_delayed(self, path, delay=60):
        def action():
            try:
                time.sleep(delay)
                if os.path.exists(path):
                    os.remove(path)
                    log(f"Deleted temp file: {path}")
            except Exception as e:
                log(f"Delayed delete error: {e}")

        threading.Thread(target=action, daemon=True).start()

    def _process_download_and_send(self, url, dialog_id, notify, schedule_date, mode_override=None, reply_to_msg=None, reply_to_top_msg=None):
        try:
            video_path = self._download_video(url, mode_override)
            if video_path:
                if os.path.exists(video_path):
                    log(f"File exists, proceeding to send: {video_path}")
                    ext = os.path.splitext(video_path)[1].lower()
                    include_source = self.get_setting("include_source_set", True)
                    caption_text = f"Source: {url}" if include_source else None
                    account_instance = get_account_instance()
                    send_as_file = self.get_setting("send_as_file_set", False)
                    if send_as_file:
                        from java.util import Locale
                        if ext == ".gif":
                            lang = Locale.getDefault().getLanguage()
                            BulletinHelper.show_error("Извини, я немощный, не могу отправить .gif" if lang.startswith('ru') else "Sorry, I'm powerless, I cannot send .gif")
                            self._delete_file_delayed(video_path)
                            self._dismiss_dialog()
                            return
                        import mimetypes
                        ext_cache_root = ApplicationLoader.applicationContext.getExternalCacheDir()
                        plugin_ext_dir = File(ext_cache_root, TEMP_DIR_NAME)
                        if not plugin_ext_dir.exists() and not plugin_ext_dir.mkdirs():
                            log("Failed to create external cache dir for document")
                        external_path = File(plugin_ext_dir, File(video_path).getName()).getAbsolutePath()
                        with open(video_path, 'rb') as f_in, open(external_path, 'wb') as f_out:
                            while True:
                                chunk = f_in.read(8192)
                                if not chunk:
                                    break
                                f_out.write(chunk)
                        mime, _ = mimetypes.guess_type(video_path)
                        if not mime:
                            mime = "application/octet-stream"
                        SendMessagesHelper.prepareSendingDocument(
                            account_instance, external_path, external_path, None,
                            caption_text, mime, dialog_id,
                            reply_to_msg, reply_to_top_msg, None, None, None,
                            notify, schedule_date, None, None, 0, False
                        )
                        self._delete_file_delayed(video_path)
                        self._dismiss_dialog()
                        return
                    image_exts = [".jpg", ".jpeg", ".png"]
                    audio_exts = [".mp3", ".wav", ".ogg", ".opus", ".m4a"]
                    if ext in image_exts:
                        send_as_file = self.get_setting("send_as_file_set", False)
                        if send_as_file:
                            mime = "application/octet-stream"
                            run_on_ui_thread(lambda: SendMessagesHelper.prepareSendingDocument(
                                account_instance,
                                video_path,
                                video_path,
                                None,
                                caption_text,
                                mime,
                                dialog_id,
                                reply_to_msg, reply_to_top_msg, None, None, None,
                                notify, schedule_date, None, None, 0, False
                            ))
                            self._delete_file_delayed(video_path)
                            self._dismiss_dialog()
                            return
                        if include_source:
                            entities = ArrayList()
                            ent = TLRPC.TL_messageEntityTextUrl()
                            ent.offset = 0; ent.length = len("Source"); ent.url = url
                            entities.add(ent)
                            SendMessagesHelper.prepareSendingPhoto(
                                account_instance, video_path, None, dialog_id,
                                reply_to_msg, reply_to_top_msg, None, "Source", entities,
                                None, None, 0, None, notify, schedule_date,
                                0, None, 0
                            )
                        else:
                            SendMessagesHelper.prepareSendingPhoto(
                                account_instance, video_path, None, dialog_id,
                                reply_to_msg, reply_to_top_msg, None, None, None,
                                None, None, 0, None, notify, schedule_date,
                                0, None, 0
                            )
                    elif ext in audio_exts:
                        self.send_audio(video_path, dialog_id, caption_text, notify, schedule_date, reply_to_msg, reply_to_top_msg)
                    elif ext == ".gif":
                        from java.util import Locale
                        lang = Locale.getDefault().getLanguage()
                        BulletinHelper.show_error("Извини, я немощный, не могу отправить .gif" if lang.startswith('ru') else "Sorry, I'm powerless, I cannot send .gif")
                        self._delete_file_delayed(video_path)
                        self._dismiss_dialog()
                        return
                    else:
                        if include_source:
                            entities = ArrayList()
                            ent = TLRPC.TL_messageEntityTextUrl()
                            ent.offset = 0; ent.length = len("Source"); ent.url = url
                            entities.add(ent)
                            SendMessagesHelper.prepareSendingVideo(
                                account_instance, video_path, None, None, None, dialog_id,
                                reply_to_msg, reply_to_top_msg, None, None, entities,
                                0, None, notify, schedule_date,
                                False, False, "Source", None, 0, 0, 0
                            )
                        else:
                            self.send_video(video_path, dialog_id, None, notify, schedule_date, reply_to_msg, reply_to_top_msg)
                    self._delete_file_delayed(video_path)
                    self._dismiss_dialog()
                else:
                    log(f"Downloaded file not found after download: {video_path}")
                    BulletinHelper.show_error(f"Internal error: File not found after download")
                    self._dismiss_dialog()
                    return
            else:
                log(f"Failed to download video for url: {url}")
                self._dismiss_dialog()
                return

        except Exception as e:
            self._dismiss_dialog()
            log(f"Error in _process_download_and_send: {e}\n{traceback.format_exc()}")
            BulletinHelper.show_error(f"An unexpected error occurred: {e}")

    def _dismiss_dialog(self):
        global progress_dialog
        def action():
            global progress_dialog
            if progress_dialog is not None:
                try:
                    dlg = progress_dialog.get_dialog() if hasattr(progress_dialog, 'get_dialog') else progress_dialog
                    if dlg and dlg.isShowing():
                        dlg.dismiss()
                except Exception:
                    pass
                finally:
                    progress_dialog = None
        run_on_ui_thread(action)

    def _show_loading_alert(self):
        global progress_dialog
        self._cancel_requested = False
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        builder = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_LOADING)
        builder.set_title("Downloading...")
        builder.set_negative_button("Cancel", self._on_progress_cancel)
        builder.set_cancelable(False)
        progress_dialog = builder.show()
        progress_dialog.set_progress(0)

    def _on_progress_cancel(self, builder, which):
        self._cancel_requested = True
        self._dismiss_dialog()

    def _copy_to_clipboard(self, label, text):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        clipboard = ctx.getSystemService(Context.CLIPBOARD_SERVICE)
        clip = ClipData.newPlainText(label, text)
        clipboard.setPrimaryClip(clip)
        BulletinHelper.show_info(f"Copied {label} to clipboard")

    def _open_link(self, url):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        run_on_ui_thread(lambda: ctx.startActivity(intent))

    def show_my_info_alert(self, title="TITLE", message="MESSAGE", positive_button="OK", neutral_button=None, neutral_link=None, neutral_type=None):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        builder = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
        builder.set_title(title)
        builder.set_message(message)
        builder.set_positive_button(positive_button, lambda b, w: self._dismiss_dialog(b))
        if neutral_button:
            if neutral_type == "link":
                builder.set_neutral_button(neutral_button, lambda b, w: self._open_link(neutral_link))
            else:
                builder.set_neutral_button(neutral_button, lambda b, w: self._copy_to_clipboard(neutral_button, neutral_link))
        self.alert_builder_instance = builder.show()

    def _on_show_settings_buttons_change(self, enabled: bool):
        def _toggle():
            try:
                if enabled:
                    if not self._drawer_settings_item:
                        self._drawer_settings_item = self.add_menu_item(MenuItemData(
                            menu_type=MenuItemType.DRAWER_MENU,
                            text=Z("settings_title"),
                            icon="msg_settings_14",
                            priority=5,
                            on_click=lambda ctx: run_on_ui_thread(lambda: self._open_plugin_settings(PluginsController.getInstance().plugins.get(self.id)))
                        ))
                    if not self._chat_settings_item:
                        self._chat_settings_item = self.add_menu_item(MenuItemData(
                            menu_type=MenuItemType.CHAT_ACTION_MENU,
                            text=Z("settings_title"),
                            icon="msg_settings_14",
                            priority=5,
                            on_click=lambda ctx: run_on_ui_thread(lambda: self._open_plugin_settings(PluginsController.getInstance().plugins.get(self.id)))
                        ))
                else:
                    if self._drawer_settings_item:
                        self.remove_menu_item(self._drawer_settings_item)
                        self._drawer_settings_item = None
                    if self._chat_settings_item:
                        self.remove_menu_item(self._chat_settings_item)
                        self._chat_settings_item = None
            except Exception as e:
                log(f"[{__id__}] Failed toggling settings buttons: {e}")
        run_on_ui_thread(_toggle)

    def on_send_message_hook(self, account, params):
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()

        msg = params.message.strip()
        if msg.startswith(".down ") or msg.startswith(".up ") or msg.startswith(".dl "):
            parts = msg.split()
            mode_override = None
            if len(parts) > 2:
                mode_override = parts[2].lower()
            if len(parts) < 2 or not parts[1]:
                BulletinHelper.show_error("No URL provided")
                return HookResult(strategy=HookStrategy.CANCEL)

            url = parts[1].strip()
            dialog_id = params.peer
            notify = True
            schedule_date = 0

            log(f"Received command: {msg}. Starting background thread for URL: {url}")

            thread = threading.Thread(
                target=self._process_download_and_send,
                args=(url, dialog_id, notify, schedule_date, mode_override, params.replyToMsg, params.replyToTopMsg),
                daemon=True
            )
            thread.start()

            log("Background thread started. Cancelling original message.")
            run_on_ui_thread(lambda: self._show_loading_alert())
            return HookResult(strategy=HookStrategy.CANCEL)

        return HookResult()
