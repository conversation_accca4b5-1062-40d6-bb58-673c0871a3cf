# Imported from <PERSON>ych<PERSON><PERSON><PERSON>'s dsa_test.json.
# This file is generated by convert_wycheproof.go. Do not edit by hand.
#
# Algorithm: DSA
# Generator version: 0.8r12

[key.g = 0835aa8c358bbf01a1846d1206323fabe408b0e98789fcc6239da14d4b3f86c276a8f48aa85a59507e620ad1bc745f0f1cbf63ec98c229c2610d77c634d1642e404354771655b2d5662f7a45227178ce3430af0f6b3bb94b52f7f51e97bad659b1ba0684e208be624c28d82fb1162f18dd9dce45216461654cf3374624d15a8d]
[key.keySize = 1024]
[key.p = 00b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f]
[key.q = 00b90b38ba0a50a43ec6898d3f9b68049777f489b1]
[key.type = DsaPublicKey]
[key.y = 173931dda31eff32f24b383091bf77eacdc6efd557624911d8e9b9debf0f256d0cffac5567b33f6eaae9d3275bbed7ef9f5f94c4003c959e49a1ed3f58c31b21baccc0ed8840b46145f121b8906d072129bae01f071947997e8ef760d2d9ea21d08a5eb7e89390b21a85664713c549e25feda6e9e6c31970866bdfbc8fa981f6]
[keyDer = 308201b63082012b06072a8648ce3804013082011e02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f021500b90b38ba0a50a43ec6898d3f9b68049777f489b10281800835aa8c358bbf01a1846d1206323fabe408b0e98789fcc6239da14d4b3f86c276a8f48aa85a59507e620ad1bc745f0f1cbf63ec98c229c2610d77c634d1642e404354771655b2d5662f7a45227178ce3430af0f6b3bb94b52f7f51e97bad659b1ba0684e208be624c28d82fb1162f18dd9dce45216461654cf3374624d15a8d03818400028180173931dda31eff32f24b383091bf77eacdc6efd557624911d8e9b9debf0f256d0cffac5567b33f6eaae9d3275bbed7ef9f5f94c4003c959e49a1ed3f58c31b21baccc0ed8840b46145f121b8906d072129bae01f071947997e8ef760d2d9ea21d08a5eb7e89390b21a85664713c549e25feda6e9e6c31970866bdfbc8fa981f6]
[sha = SHA-1]

# tcId = 1
# Legacy:ASN encoding of r misses leading 0
msg = 313233343030
result = acceptable
sig = 302c0214aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b
flags = NoLeadingZero

# tcId = 2
# valid
msg = 313233343030
result = valid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 3
# long form encoding of length of sequence
msg = 313233343030
result = invalid
sig = 30812d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 4
# length of sequence contains leading 0
msg = 313233343030
result = invalid
sig = 3082002d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 5
# wrong length of sequence
msg = 313233343030
result = invalid
sig = 302e021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 6
# wrong length of sequence
msg = 313233343030
result = invalid
sig = 302c021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 7
# uint32 overflow in length of sequence
msg = 313233343030
result = invalid
sig = 3085010000002d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 8
# uint64 overflow in length of sequence
msg = 313233343030
result = invalid
sig = 308901000000000000002d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 9
# length of sequence = 2**31 - 1
msg = 313233343030
result = invalid
sig = 30847fffffff021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 10
# length of sequence = 2**32 - 1
msg = 313233343030
result = invalid
sig = 3084ffffffff021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 11
# length of sequence = 2**40 - 1
msg = 313233343030
result = invalid
sig = 3085ffffffffff021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 12
# length of sequence = 2**64 - 1
msg = 313233343030
result = invalid
sig = 3088ffffffffffffffff021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 13
# incorrect length of sequence
msg = 313233343030
result = invalid
sig = 30ff021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 14
# indefinite length without termination
msg = 313233343030
result = invalid
sig = 3080021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 15
# indefinite length without termination
msg = 313233343030
result = invalid
sig = 302d028000aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 16
# indefinite length without termination
msg = 313233343030
result = invalid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0280496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 17
# removing sequence
msg = 313233343030
result = invalid
sig = 

# tcId = 18
# lonely sequence tag
msg = 313233343030
result = invalid
sig = 30

# tcId = 19
# appending 0's to sequence
msg = 313233343030
result = invalid
sig = 302f021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0000

# tcId = 20
# prepending 0's to sequence
msg = 313233343030
result = invalid
sig = 302f0000021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 21
# appending unused 0's to sequence
msg = 313233343030
result = invalid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0000

# tcId = 22
# appending null value to sequence
msg = 313233343030
result = invalid
sig = 302f021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0500

# tcId = 23
# including garbage
msg = 313233343030
result = invalid
sig = 3032498177302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 24
# including garbage
msg = 313233343030
result = invalid
sig = 30312500302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 25
# including garbage
msg = 313233343030
result = invalid
sig = 302f302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0004deadbeef

# tcId = 26
# including garbage
msg = 313233343030
result = invalid
sig = 3032221a498177021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 27
# including garbage
msg = 313233343030
result = invalid
sig = 303122192500021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 28
# including garbage
msg = 313233343030
result = invalid
sig = 30352217021500aa6a258fbf7d90e15614676d377df8b10e38db4a0004deadbeef0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 29
# including garbage
msg = 313233343030
result = invalid
sig = 3032021500aa6a258fbf7d90e15614676d377df8b10e38db4a22194981770214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 30
# including garbage
msg = 313233343030
result = invalid
sig = 3031021500aa6a258fbf7d90e15614676d377df8b10e38db4a221825000214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 31
# including garbage
msg = 313233343030
result = invalid
sig = 3035021500aa6a258fbf7d90e15614676d377df8b10e38db4a22160214496d5220b5f67d3532d1f991203bc3523b964c3b0004deadbeef

# tcId = 32
# including undefined tags
msg = 313233343030
result = invalid
sig = 3035aa00bb00cd00302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 33
# including undefined tags
msg = 313233343030
result = invalid
sig = 3033aa02aabb302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 34
# including undefined tags
msg = 313233343030
result = invalid
sig = 3035221daa00bb00cd00021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 35
# including undefined tags
msg = 313233343030
result = invalid
sig = 3033221baa02aabb021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 36
# including undefined tags
msg = 313233343030
result = invalid
sig = 3035021500aa6a258fbf7d90e15614676d377df8b10e38db4a221caa00bb00cd000214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 37
# including undefined tags
msg = 313233343030
result = invalid
sig = 3033021500aa6a258fbf7d90e15614676d377df8b10e38db4a221aaa02aabb0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 38
# truncated length of sequence
msg = 313233343030
result = invalid
sig = 3081

# tcId = 39
# using composition with indefinite length
msg = 313233343030
result = invalid
sig = 3080302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0000

# tcId = 40
# using composition with indefinite length
msg = 313233343030
result = invalid
sig = 30312280021500aa6a258fbf7d90e15614676d377df8b10e38db4a00000214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 41
# using composition with indefinite length
msg = 313233343030
result = invalid
sig = 3031021500aa6a258fbf7d90e15614676d377df8b10e38db4a22800214496d5220b5f67d3532d1f991203bc3523b964c3b0000

# tcId = 42
# using composition with wrong tag
msg = 313233343030
result = invalid
sig = 3080312d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0000

# tcId = 43
# using composition with wrong tag
msg = 313233343030
result = invalid
sig = 30312280031500aa6a258fbf7d90e15614676d377df8b10e38db4a00000214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 44
# using composition with wrong tag
msg = 313233343030
result = invalid
sig = 3031021500aa6a258fbf7d90e15614676d377df8b10e38db4a22800314496d5220b5f67d3532d1f991203bc3523b964c3b0000

# tcId = 45
# Replacing sequence with NULL
msg = 313233343030
result = invalid
sig = 0500

# tcId = 46
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = 2e2d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 47
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = 2f2d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 48
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = 312d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 49
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = 322d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 50
# changing tag value of sequence
msg = 313233343030
result = invalid
sig = ff2d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 51
# dropping value of sequence
msg = 313233343030
result = invalid
sig = 3000

# tcId = 52
# using composition for sequence
msg = 313233343030
result = invalid
sig = 3031300102302c1500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 53
# truncated sequence
msg = 313233343030
result = invalid
sig = 302c021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c

# tcId = 54
# truncated sequence
msg = 313233343030
result = invalid
sig = 302c1500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 55
# indefinite length
msg = 313233343030
result = invalid
sig = 3080021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0000

# tcId = 56
# indefinite length with truncated delimiter
msg = 313233343030
result = invalid
sig = 3080021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b00

# tcId = 57
# indefinite length with additional element
msg = 313233343030
result = invalid
sig = 3080021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b05000000

# tcId = 58
# indefinite length with truncated element
msg = 313233343030
result = invalid
sig = 3080021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b060811220000

# tcId = 59
# indefinite length with garbage
msg = 313233343030
result = invalid
sig = 3080021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0000fe02beef

# tcId = 60
# indefinite length with nonempty EOC
msg = 313233343030
result = invalid
sig = 3080021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0002beef

# tcId = 61
# prepend empty sequence
msg = 313233343030
result = invalid
sig = 302f3000021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 62
# append empty sequence
msg = 313233343030
result = invalid
sig = 302f021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b3000

# tcId = 63
# append garbage with high tag number
msg = 313233343030
result = invalid
sig = 3030021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3bbf7f00

# tcId = 64
# sequence of sequence
msg = 313233343030
result = invalid
sig = 302f302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 65
# truncated sequence: removed last 1 elements
msg = 313233343030
result = invalid
sig = 3017021500aa6a258fbf7d90e15614676d377df8b10e38db4a

# tcId = 66
# repeating element in sequence
msg = 313233343030
result = invalid
sig = 3043021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 67
# long form encoding of length of integer
msg = 313233343030
result = invalid
sig = 302e02811500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 68
# long form encoding of length of integer
msg = 313233343030
result = invalid
sig = 302e021500aa6a258fbf7d90e15614676d377df8b10e38db4a028114496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 69
# length of integer contains leading 0
msg = 313233343030
result = invalid
sig = 302f0282001500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 70
# length of integer contains leading 0
msg = 313233343030
result = invalid
sig = 302f021500aa6a258fbf7d90e15614676d377df8b10e38db4a02820014496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 71
# wrong length of integer
msg = 313233343030
result = invalid
sig = 302d021600aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 72
# wrong length of integer
msg = 313233343030
result = invalid
sig = 302d021400aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 73
# wrong length of integer
msg = 313233343030
result = invalid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0215496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 74
# wrong length of integer
msg = 313233343030
result = invalid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0213496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 75
# uint32 overflow in length of integer
msg = 313233343030
result = invalid
sig = 30320285010000001500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 76
# uint32 overflow in length of integer
msg = 313233343030
result = invalid
sig = 3032021500aa6a258fbf7d90e15614676d377df8b10e38db4a02850100000014496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 77
# uint64 overflow in length of integer
msg = 313233343030
result = invalid
sig = 3036028901000000000000001500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 78
# uint64 overflow in length of integer
msg = 313233343030
result = invalid
sig = 3036021500aa6a258fbf7d90e15614676d377df8b10e38db4a0289010000000000000014496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 79
# length of integer = 2**31 - 1
msg = 313233343030
result = invalid
sig = 303102847fffffff00aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 80
# length of integer = 2**31 - 1
msg = 313233343030
result = invalid
sig = 3031021500aa6a258fbf7d90e15614676d377df8b10e38db4a02847fffffff496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 81
# length of integer = 2**32 - 1
msg = 313233343030
result = invalid
sig = 30310284ffffffff00aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 82
# length of integer = 2**32 - 1
msg = 313233343030
result = invalid
sig = 3031021500aa6a258fbf7d90e15614676d377df8b10e38db4a0284ffffffff496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 83
# length of integer = 2**40 - 1
msg = 313233343030
result = invalid
sig = 30320285ffffffffff00aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 84
# length of integer = 2**40 - 1
msg = 313233343030
result = invalid
sig = 3032021500aa6a258fbf7d90e15614676d377df8b10e38db4a0285ffffffffff496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 85
# length of integer = 2**64 - 1
msg = 313233343030
result = invalid
sig = 30350288ffffffffffffffff00aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 86
# length of integer = 2**64 - 1
msg = 313233343030
result = invalid
sig = 3035021500aa6a258fbf7d90e15614676d377df8b10e38db4a0288ffffffffffffffff496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 87
# incorrect length of integer
msg = 313233343030
result = invalid
sig = 302d02ff00aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 88
# incorrect length of integer
msg = 313233343030
result = invalid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a02ff496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 89
# removing integer
msg = 313233343030
result = invalid
sig = 30160214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 90
# lonely integer tag
msg = 313233343030
result = invalid
sig = 3017020214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 91
# lonely integer tag
msg = 313233343030
result = invalid
sig = 3018021500aa6a258fbf7d90e15614676d377df8b10e38db4a02

# tcId = 92
# appending 0's to integer
msg = 313233343030
result = invalid
sig = 302f021700aa6a258fbf7d90e15614676d377df8b10e38db4a00000214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 93
# appending 0's to integer
msg = 313233343030
result = invalid
sig = 302f021500aa6a258fbf7d90e15614676d377df8b10e38db4a0216496d5220b5f67d3532d1f991203bc3523b964c3b0000

# tcId = 94
# prepending 0's to integer
msg = 313233343030
result = invalid
sig = 302f0217000000aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 95
# prepending 0's to integer
msg = 313233343030
result = invalid
sig = 302f021500aa6a258fbf7d90e15614676d377df8b10e38db4a02160000496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 96
# appending unused 0's to integer
msg = 313233343030
result = invalid
sig = 302f021500aa6a258fbf7d90e15614676d377df8b10e38db4a00000214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 97
# appending null value to integer
msg = 313233343030
result = invalid
sig = 302f021700aa6a258fbf7d90e15614676d377df8b10e38db4a05000214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 98
# appending null value to integer
msg = 313233343030
result = invalid
sig = 302f021500aa6a258fbf7d90e15614676d377df8b10e38db4a0216496d5220b5f67d3532d1f991203bc3523b964c3b0500

# tcId = 99
# truncated length of integer
msg = 313233343030
result = invalid
sig = 301802810214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 100
# truncated length of integer
msg = 313233343030
result = invalid
sig = 3019021500aa6a258fbf7d90e15614676d377df8b10e38db4a0281

# tcId = 101
# Replacing integer with NULL
msg = 313233343030
result = invalid
sig = 301805000214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 102
# Replacing integer with NULL
msg = 313233343030
result = invalid
sig = 3019021500aa6a258fbf7d90e15614676d377df8b10e38db4a0500

# tcId = 103
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 302d001500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 104
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 302d011500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 105
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 302d031500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 106
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 302d041500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 107
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 302dff1500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 108
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0014496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 109
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0114496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 110
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0314496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 111
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0414496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 112
# changing tag value of integer
msg = 313233343030
result = invalid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38db4aff14496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 113
# dropping value of integer
msg = 313233343030
result = invalid
sig = 301802000214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 114
# dropping value of integer
msg = 313233343030
result = invalid
sig = 3019021500aa6a258fbf7d90e15614676d377df8b10e38db4a0200

# tcId = 115
# using composition for integer
msg = 313233343030
result = invalid
sig = 303122190201000214aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 116
# using composition for integer
msg = 313233343030
result = invalid
sig = 3031021500aa6a258fbf7d90e15614676d377df8b10e38db4a221802014902136d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 117
# modify first byte of integer
msg = 313233343030
result = invalid
sig = 302d021502aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 118
# modify first byte of integer
msg = 313233343030
result = invalid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a02144b6d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 119
# modify last byte of integer
msg = 313233343030
result = invalid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38dbca0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 120
# modify last byte of integer
msg = 313233343030
result = invalid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964cbb

# tcId = 121
# truncated integer
msg = 313233343030
result = invalid
sig = 302c021400aa6a258fbf7d90e15614676d377df8b10e38db0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 122
# truncated integer
msg = 313233343030
result = invalid
sig = 302c021500aa6a258fbf7d90e15614676d377df8b10e38db4a0213496d5220b5f67d3532d1f991203bc3523b964c

# tcId = 123
# truncated integer
msg = 313233343030
result = invalid
sig = 302c021500aa6a258fbf7d90e15614676d377df8b10e38db4a02136d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 124
# leading ff in integer
msg = 313233343030
result = invalid
sig = 302e0216ff00aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 125
# leading ff in integer
msg = 313233343030
result = invalid
sig = 302e021500aa6a258fbf7d90e15614676d377df8b10e38db4a0215ff496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 126
# replaced integer by infinity
msg = 313233343030
result = invalid
sig = 30190901800214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 127
# replaced integer by infinity
msg = 313233343030
result = invalid
sig = 301a021500aa6a258fbf7d90e15614676d377df8b10e38db4a090180

# tcId = 128
# replacing integer with zero
msg = 313233343030
result = invalid
sig = 30190201000214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 129
# replacing integer with zero
msg = 313233343030
result = invalid
sig = 301a021500aa6a258fbf7d90e15614676d377df8b10e38db4a020100

# tcId = 130
# Modified r or s, e.g. by adding or subtracting the group order
msg = 313233343030
result = invalid
sig = 302d02150163755e49c9ce35201c9df4acd2e5fd48862d64fb0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 131
# Modified r or s, e.g. by adding or subtracting the group order
msg = 313233343030
result = invalid
sig = 302c0214f15eecd5b52ceca28f8ada2d9c15f419964451990214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 132
# Modified r or s, e.g. by adding or subtracting the group order
msg = 313233343030
result = invalid
sig = 302d0215ff5595da7040826f1ea9eb9892c882074ef1c724b60214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 133
# Modified r or s, e.g. by adding or subtracting the group order
msg = 313233343030
result = invalid
sig = 302c02140ea1132a4ad3135d707525d263ea0be669bbae670214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 134
# Modified r or s, e.g. by adding or subtracting the group order
msg = 313233343030
result = invalid
sig = 302d0215fe9c8aa1b63631cadfe3620b532d1a02b779d29b050214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 135
# Modified r or s, e.g. by adding or subtracting the group order
msg = 313233343030
result = invalid
sig = 302d021501aa6a258fbf7d90e15614676d377df8b10e38db4a0214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 136
# Modified r or s, e.g. by adding or subtracting the group order
msg = 313233343030
result = invalid
sig = 302c02145595da7040826f1ea9eb9892c882074ef1c724b60214496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 137
# Modified r or s, e.g. by adding or subtracting the group order
msg = 313233343030
result = invalid
sig = 302e021500aa6a258fbf7d90e15614676d377df8b10e38db4a02150102788adac0472173f95b86d0bba3c7e9b38ad5ec

# tcId = 138
# Modified r or s, e.g. by adding or subtracting the group order
msg = 313233343030
result = invalid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a021490621966aba5d8f66c486c5184d3bebac3a1c28a

# tcId = 139
# Modified r or s, e.g. by adding or subtracting the group order
msg = 313233343030
result = invalid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a0214b692addf4a0982cacd2e066edfc43cadc469b3c5

# tcId = 140
# Modified r or s, e.g. by adding or subtracting the group order
msg = 313233343030
result = invalid
sig = 302d021500aa6a258fbf7d90e15614676d377df8b10e38db4a02146f9de699545a270993b793ae7b2c41453c5e3d76

# tcId = 141
# Modified r or s, e.g. by adding or subtracting the group order
msg = 313233343030
result = invalid
sig = 302e021500aa6a258fbf7d90e15614676d377df8b10e38db4a0215fefd8775253fb8de8c06a4792f445c38164c752a14

# tcId = 142
# Modified r or s, e.g. by adding or subtracting the group order
msg = 313233343030
result = invalid
sig = 302e021500aa6a258fbf7d90e15614676d377df8b10e38db4a021501496d5220b5f67d3532d1f991203bc3523b964c3b

# tcId = 143
# Modified r or s, e.g. by adding or subtracting the group order
msg = 313233343030
result = invalid
sig = 302e021500aa6a258fbf7d90e15614676d377df8b10e38db4a021500b692addf4a0982cacd2e066edfc43cadc469b3c5

# tcId = 144
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a0201000215ff46f4c745f5af5bc1397672c06497fb68880b764f
flags = EdgeCase

# tcId = 145
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 3006020100020100
flags = EdgeCase

# tcId = 146
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 3006020100020101
flags = EdgeCase

# tcId = 147
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30060201000201ff
flags = EdgeCase

# tcId = 148
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301902010002145c859c5d0528521f6344c69fcdb4024bbbfa44d8
flags = EdgeCase

# tcId = 149
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301902010002145c859c5d0528521f6344c69fcdb4024bbbfa44d9
flags = EdgeCase

# tcId = 150
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a020100021500b90b38ba0a50a43ec6898d3f9b68049777f489b0
flags = EdgeCase

# tcId = 151
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a020100021500b90b38ba0a50a43ec6898d3f9b68049777f489b1
flags = EdgeCase

# tcId = 152
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a020100021500b90b38ba0a50a43ec6898d3f9b68049777f489b2
flags = EdgeCase

# tcId = 153
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a0201000215010000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 154
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30818702010002818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f
flags = EdgeCase

# tcId = 155
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 3008020100090380fe01
flags = EdgeCase

# tcId = 156
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 3006020100090142
flags = EdgeCase

# tcId = 157
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a0201010215ff46f4c745f5af5bc1397672c06497fb68880b764f
flags = EdgeCase

# tcId = 158
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 3006020101020100
flags = EdgeCase

# tcId = 159
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 3006020101020101
flags = EdgeCase

# tcId = 160
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30060201010201ff
flags = EdgeCase

# tcId = 161
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301902010102145c859c5d0528521f6344c69fcdb4024bbbfa44d8
flags = EdgeCase

# tcId = 162
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301902010102145c859c5d0528521f6344c69fcdb4024bbbfa44d9
flags = EdgeCase

# tcId = 163
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a020101021500b90b38ba0a50a43ec6898d3f9b68049777f489b0
flags = EdgeCase

# tcId = 164
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a020101021500b90b38ba0a50a43ec6898d3f9b68049777f489b1
flags = EdgeCase

# tcId = 165
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a020101021500b90b38ba0a50a43ec6898d3f9b68049777f489b2
flags = EdgeCase

# tcId = 166
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a0201010215010000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 167
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30818702010102818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f
flags = EdgeCase

# tcId = 168
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 3008020101090380fe01
flags = EdgeCase

# tcId = 169
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 3006020101090142
flags = EdgeCase

# tcId = 170
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a0201ff0215ff46f4c745f5af5bc1397672c06497fb68880b764f
flags = EdgeCase

# tcId = 171
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30060201ff020100
flags = EdgeCase

# tcId = 172
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30060201ff020101
flags = EdgeCase

# tcId = 173
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30060201ff0201ff
flags = EdgeCase

# tcId = 174
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30190201ff02145c859c5d0528521f6344c69fcdb4024bbbfa44d8
flags = EdgeCase

# tcId = 175
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30190201ff02145c859c5d0528521f6344c69fcdb4024bbbfa44d9
flags = EdgeCase

# tcId = 176
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a0201ff021500b90b38ba0a50a43ec6898d3f9b68049777f489b0
flags = EdgeCase

# tcId = 177
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a0201ff021500b90b38ba0a50a43ec6898d3f9b68049777f489b1
flags = EdgeCase

# tcId = 178
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a0201ff021500b90b38ba0a50a43ec6898d3f9b68049777f489b2
flags = EdgeCase

# tcId = 179
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a0201ff0215010000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 180
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 3081870201ff02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f
flags = EdgeCase

# tcId = 181
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30080201ff090380fe01
flags = EdgeCase

# tcId = 182
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30060201ff090142
flags = EdgeCase

# tcId = 183
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d80215ff46f4c745f5af5bc1397672c06497fb68880b764f
flags = EdgeCase

# tcId = 184
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301902145c859c5d0528521f6344c69fcdb4024bbbfa44d8020100
flags = EdgeCase

# tcId = 185
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301902145c859c5d0528521f6344c69fcdb4024bbbfa44d8020101
flags = EdgeCase

# tcId = 186
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301902145c859c5d0528521f6344c69fcdb4024bbbfa44d80201ff
flags = EdgeCase

# tcId = 187
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302c02145c859c5d0528521f6344c69fcdb4024bbbfa44d802145c859c5d0528521f6344c69fcdb4024bbbfa44d8
flags = EdgeCase

# tcId = 188
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302c02145c859c5d0528521f6344c69fcdb4024bbbfa44d802145c859c5d0528521f6344c69fcdb4024bbbfa44d9
flags = EdgeCase

# tcId = 189
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d8021500b90b38ba0a50a43ec6898d3f9b68049777f489b0
flags = EdgeCase

# tcId = 190
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d8021500b90b38ba0a50a43ec6898d3f9b68049777f489b1
flags = EdgeCase

# tcId = 191
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d8021500b90b38ba0a50a43ec6898d3f9b68049777f489b2
flags = EdgeCase

# tcId = 192
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d80215010000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 193
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30819a02145c859c5d0528521f6344c69fcdb4024bbbfa44d802818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f
flags = EdgeCase

# tcId = 194
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301b02145c859c5d0528521f6344c69fcdb4024bbbfa44d8090380fe01
flags = EdgeCase

# tcId = 195
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301902145c859c5d0528521f6344c69fcdb4024bbbfa44d8090142
flags = EdgeCase

# tcId = 196
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d90215ff46f4c745f5af5bc1397672c06497fb68880b764f
flags = EdgeCase

# tcId = 197
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301902145c859c5d0528521f6344c69fcdb4024bbbfa44d9020100
flags = EdgeCase

# tcId = 198
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301902145c859c5d0528521f6344c69fcdb4024bbbfa44d9020101
flags = EdgeCase

# tcId = 199
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301902145c859c5d0528521f6344c69fcdb4024bbbfa44d90201ff
flags = EdgeCase

# tcId = 200
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302c02145c859c5d0528521f6344c69fcdb4024bbbfa44d902145c859c5d0528521f6344c69fcdb4024bbbfa44d8
flags = EdgeCase

# tcId = 201
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302c02145c859c5d0528521f6344c69fcdb4024bbbfa44d902145c859c5d0528521f6344c69fcdb4024bbbfa44d9
flags = EdgeCase

# tcId = 202
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d9021500b90b38ba0a50a43ec6898d3f9b68049777f489b0
flags = EdgeCase

# tcId = 203
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d9021500b90b38ba0a50a43ec6898d3f9b68049777f489b1
flags = EdgeCase

# tcId = 204
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d9021500b90b38ba0a50a43ec6898d3f9b68049777f489b2
flags = EdgeCase

# tcId = 205
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d02145c859c5d0528521f6344c69fcdb4024bbbfa44d90215010000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 206
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30819a02145c859c5d0528521f6344c69fcdb4024bbbfa44d902818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f
flags = EdgeCase

# tcId = 207
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301b02145c859c5d0528521f6344c69fcdb4024bbbfa44d9090380fe01
flags = EdgeCase

# tcId = 208
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301902145c859c5d0528521f6344c69fcdb4024bbbfa44d9090142
flags = EdgeCase

# tcId = 209
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b00215ff46f4c745f5af5bc1397672c06497fb68880b764f
flags = EdgeCase

# tcId = 210
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b0020100
flags = EdgeCase

# tcId = 211
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b0020101
flags = EdgeCase

# tcId = 212
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b00201ff
flags = EdgeCase

# tcId = 213
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d021500b90b38ba0a50a43ec6898d3f9b68049777f489b002145c859c5d0528521f6344c69fcdb4024bbbfa44d8
flags = EdgeCase

# tcId = 214
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d021500b90b38ba0a50a43ec6898d3f9b68049777f489b002145c859c5d0528521f6344c69fcdb4024bbbfa44d9
flags = EdgeCase

# tcId = 215
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b0021500b90b38ba0a50a43ec6898d3f9b68049777f489b0
flags = EdgeCase

# tcId = 216
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b0021500b90b38ba0a50a43ec6898d3f9b68049777f489b1
flags = EdgeCase

# tcId = 217
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b0021500b90b38ba0a50a43ec6898d3f9b68049777f489b2
flags = EdgeCase

# tcId = 218
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b00215010000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 219
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30819b021500b90b38ba0a50a43ec6898d3f9b68049777f489b002818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f
flags = EdgeCase

# tcId = 220
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301c021500b90b38ba0a50a43ec6898d3f9b68049777f489b0090380fe01
flags = EdgeCase

# tcId = 221
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b0090142
flags = EdgeCase

# tcId = 222
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b10215ff46f4c745f5af5bc1397672c06497fb68880b764f
flags = EdgeCase

# tcId = 223
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b1020100
flags = EdgeCase

# tcId = 224
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b1020101
flags = EdgeCase

# tcId = 225
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b10201ff
flags = EdgeCase

# tcId = 226
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d021500b90b38ba0a50a43ec6898d3f9b68049777f489b102145c859c5d0528521f6344c69fcdb4024bbbfa44d8
flags = EdgeCase

# tcId = 227
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d021500b90b38ba0a50a43ec6898d3f9b68049777f489b102145c859c5d0528521f6344c69fcdb4024bbbfa44d9
flags = EdgeCase

# tcId = 228
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b1021500b90b38ba0a50a43ec6898d3f9b68049777f489b0
flags = EdgeCase

# tcId = 229
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b1021500b90b38ba0a50a43ec6898d3f9b68049777f489b1
flags = EdgeCase

# tcId = 230
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b1021500b90b38ba0a50a43ec6898d3f9b68049777f489b2
flags = EdgeCase

# tcId = 231
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b10215010000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 232
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30819b021500b90b38ba0a50a43ec6898d3f9b68049777f489b102818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f
flags = EdgeCase

# tcId = 233
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301c021500b90b38ba0a50a43ec6898d3f9b68049777f489b1090380fe01
flags = EdgeCase

# tcId = 234
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b1090142
flags = EdgeCase

# tcId = 235
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b20215ff46f4c745f5af5bc1397672c06497fb68880b764f
flags = EdgeCase

# tcId = 236
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b2020100
flags = EdgeCase

# tcId = 237
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b2020101
flags = EdgeCase

# tcId = 238
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b20201ff
flags = EdgeCase

# tcId = 239
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d021500b90b38ba0a50a43ec6898d3f9b68049777f489b202145c859c5d0528521f6344c69fcdb4024bbbfa44d8
flags = EdgeCase

# tcId = 240
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d021500b90b38ba0a50a43ec6898d3f9b68049777f489b202145c859c5d0528521f6344c69fcdb4024bbbfa44d9
flags = EdgeCase

# tcId = 241
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b2021500b90b38ba0a50a43ec6898d3f9b68049777f489b0
flags = EdgeCase

# tcId = 242
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b2021500b90b38ba0a50a43ec6898d3f9b68049777f489b1
flags = EdgeCase

# tcId = 243
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b2021500b90b38ba0a50a43ec6898d3f9b68049777f489b2
flags = EdgeCase

# tcId = 244
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e021500b90b38ba0a50a43ec6898d3f9b68049777f489b20215010000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 245
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30819b021500b90b38ba0a50a43ec6898d3f9b68049777f489b202818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f
flags = EdgeCase

# tcId = 246
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301c021500b90b38ba0a50a43ec6898d3f9b68049777f489b2090380fe01
flags = EdgeCase

# tcId = 247
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a021500b90b38ba0a50a43ec6898d3f9b68049777f489b2090142
flags = EdgeCase

# tcId = 248
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e02150100000000000000000000000000000000000000000215ff46f4c745f5af5bc1397672c06497fb68880b764f
flags = EdgeCase

# tcId = 249
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a0215010000000000000000000000000000000000000000020100
flags = EdgeCase

# tcId = 250
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a0215010000000000000000000000000000000000000000020101
flags = EdgeCase

# tcId = 251
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a02150100000000000000000000000000000000000000000201ff
flags = EdgeCase

# tcId = 252
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d021501000000000000000000000000000000000000000002145c859c5d0528521f6344c69fcdb4024bbbfa44d8
flags = EdgeCase

# tcId = 253
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302d021501000000000000000000000000000000000000000002145c859c5d0528521f6344c69fcdb4024bbbfa44d9
flags = EdgeCase

# tcId = 254
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e0215010000000000000000000000000000000000000000021500b90b38ba0a50a43ec6898d3f9b68049777f489b0
flags = EdgeCase

# tcId = 255
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e0215010000000000000000000000000000000000000000021500b90b38ba0a50a43ec6898d3f9b68049777f489b1
flags = EdgeCase

# tcId = 256
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e0215010000000000000000000000000000000000000000021500b90b38ba0a50a43ec6898d3f9b68049777f489b2
flags = EdgeCase

# tcId = 257
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 302e02150100000000000000000000000000000000000000000215010000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 258
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30819b021501000000000000000000000000000000000000000002818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f
flags = EdgeCase

# tcId = 259
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301c0215010000000000000000000000000000000000000000090380fe01
flags = EdgeCase

# tcId = 260
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301a0215010000000000000000000000000000000000000000090142
flags = EdgeCase

# tcId = 261
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30819b02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f0215ff46f4c745f5af5bc1397672c06497fb68880b764f
flags = EdgeCase

# tcId = 262
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30818702818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f020100
flags = EdgeCase

# tcId = 263
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30818702818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f020101
flags = EdgeCase

# tcId = 264
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30818702818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f0201ff
flags = EdgeCase

# tcId = 265
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30819a02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f02145c859c5d0528521f6344c69fcdb4024bbbfa44d8
flags = EdgeCase

# tcId = 266
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30819a02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f02145c859c5d0528521f6344c69fcdb4024bbbfa44d9
flags = EdgeCase

# tcId = 267
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30819b02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f021500b90b38ba0a50a43ec6898d3f9b68049777f489b0
flags = EdgeCase

# tcId = 268
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30819b02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f021500b90b38ba0a50a43ec6898d3f9b68049777f489b1
flags = EdgeCase

# tcId = 269
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30819b02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f021500b90b38ba0a50a43ec6898d3f9b68049777f489b2
flags = EdgeCase

# tcId = 270
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30819b02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f0215010000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 271
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 3082010802818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f
flags = EdgeCase

# tcId = 272
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30818902818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f090380fe01
flags = EdgeCase

# tcId = 273
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 30818702818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f090142
flags = EdgeCase

# tcId = 274
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301c090380fe010215ff46f4c745f5af5bc1397672c06497fb68880b764f
flags = EdgeCase

# tcId = 275
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 3008090380fe01020100
flags = EdgeCase

# tcId = 276
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 3008090380fe01020101
flags = EdgeCase

# tcId = 277
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 3008090380fe010201ff
flags = EdgeCase

# tcId = 278
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301b090380fe0102145c859c5d0528521f6344c69fcdb4024bbbfa44d8
flags = EdgeCase

# tcId = 279
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301b090380fe0102145c859c5d0528521f6344c69fcdb4024bbbfa44d9
flags = EdgeCase

# tcId = 280
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301c090380fe01021500b90b38ba0a50a43ec6898d3f9b68049777f489b0
flags = EdgeCase

# tcId = 281
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301c090380fe01021500b90b38ba0a50a43ec6898d3f9b68049777f489b1
flags = EdgeCase

# tcId = 282
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301c090380fe01021500b90b38ba0a50a43ec6898d3f9b68049777f489b2
flags = EdgeCase

# tcId = 283
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 301c090380fe010215010000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 284
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 308189090380fe0102818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f
flags = EdgeCase

# tcId = 285
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 300a090380fe01090380fe01
flags = EdgeCase

# tcId = 286
# Signatures with special case values for r and s.
msg = 313233343030
result = invalid
sig = 3008090380fe01090142
flags = EdgeCase

# tcId = 287
# Signature encoding contains wrong type.
msg = 313233343030
result = invalid
sig = 30060201010c0130

# tcId = 288
# Signature encoding contains wrong type.
msg = 313233343030
result = invalid
sig = 30050201010c00

# tcId = 289
# Signature encoding contains wrong type.
msg = 313233343030
result = invalid
sig = 30090c0225730c03732573

# tcId = 290
# Signature encoding contains wrong type.
msg = 313233343030
result = invalid
sig = 30080201013003020100

# tcId = 291
# Signature encoding contains wrong type.
msg = 313233343030
result = invalid
sig = 3003020101

# tcId = 292
# Signature encoding contains wrong type.
msg = 313233343030
result = invalid
sig = 3006020101010100

# tcId = 293
# random signature
msg = 313233343030
result = valid
sig = 302e0215008854bdb52d20ff9ea499483fba4d3c101a586fc7021500b23045900995d3fe3c4c638a3e06458a25a1e9dd

# tcId = 294
# random signature
msg = 313233343030
result = valid
sig = 302c02144f6bf18941abbf33211d9561a14f9aebd03f4e940214218cda350def7f75617fcc799d0cf2cf6b23438d

# tcId = 295
# random signature
msg = 313233343030
result = valid
sig = 302c0214459eaf0886160081b47fc573fb3d152d680d3b4b02144293dbb94db4c930d67e27fc4ec8538b58d1c7cd

# tcId = 296
# random signature
msg = 313233343030
result = valid
sig = 302c02140a6c12b8ff5ca21c4ea0c7acea38d76fd170b97f021429ce2cf2672fa640031680dce2223932f613f6a5

# tcId = 297
# random signature
msg = 313233343030
result = valid
sig = 302c021434bce4773e5e11875ea2202bc33e01fe00b3321a02142b294e01a97296d84e4c60bfba05d2760981c920

[key.g = 0835aa8c358bbf01a1846d1206323fabe408b0e98789fcc6239da14d4b3f86c276a8f48aa85a59507e620ad1bc745f0f1cbf63ec98c229c2610d77c634d1642e404354771655b2d5662f7a45227178ce3430af0f6b3bb94b52f7f51e97bad659b1ba0684e208be624c28d82fb1162f18dd9dce45216461654cf3374624d15a8d]
[key.keySize = 1024]
[key.p = 00b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f]
[key.q = 00b90b38ba0a50a43ec6898d3f9b68049777f489b1]
[key.type = DsaPublicKey]
[key.y = 713e9f8108a6a7075485a37ce1a3b040cce563a0445614fe099fb1bffd68acb36f9e04d8ad17ace3c136da66f730eb7ff18936424ffa4e5ae5b1e7dac375d8d164697254b8b7e848f5e79da25c79df5c0727d5da3498405cd0f4e46d136c351d703cc4bf0d3f4fbb165392888684964a93ad30fa179488cad4a6655dd4fa9754]
[keyDer = 308201b63082012b06072a8648ce3804013082011e02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f021500b90b38ba0a50a43ec6898d3f9b68049777f489b10281800835aa8c358bbf01a1846d1206323fabe408b0e98789fcc6239da14d4b3f86c276a8f48aa85a59507e620ad1bc745f0f1cbf63ec98c229c2610d77c634d1642e404354771655b2d5662f7a45227178ce3430af0f6b3bb94b52f7f51e97bad659b1ba0684e208be624c28d82fb1162f18dd9dce45216461654cf3374624d15a8d03818400028180713e9f8108a6a7075485a37ce1a3b040cce563a0445614fe099fb1bffd68acb36f9e04d8ad17ace3c136da66f730eb7ff18936424ffa4e5ae5b1e7dac375d8d164697254b8b7e848f5e79da25c79df5c0727d5da3498405cd0f4e46d136c351d703cc4bf0d3f4fbb165392888684964a93ad30fa179488cad4a6655dd4fa9754]
[sha = SHA-1]

# tcId = 298
# r,s = 1,1
msg = 54657374
result = valid
sig = 3006020101020101

# tcId = 299
# r,s = 1,5
msg = 54657374
result = valid
sig = 3006020101020105

# tcId = 300
# u2 small
msg = 54657374
result = valid
sig = 3019020101021425023e8b9ba9ba72f481e90cb8ae67517e641b8a

# tcId = 301
# s == q-1
msg = 54657374
result = valid
sig = 301a020101021500b90b38ba0a50a43ec6898d3f9b68049777f489b0

[key.g = 0835aa8c358bbf01a1846d1206323fabe408b0e98789fcc6239da14d4b3f86c276a8f48aa85a59507e620ad1bc745f0f1cbf63ec98c229c2610d77c634d1642e404354771655b2d5662f7a45227178ce3430af0f6b3bb94b52f7f51e97bad659b1ba0684e208be624c28d82fb1162f18dd9dce45216461654cf3374624d15a8d]
[key.keySize = 1024]
[key.p = 00b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f]
[key.q = 00b90b38ba0a50a43ec6898d3f9b68049777f489b1]
[key.type = DsaPublicKey]
[key.y = 61fe5b61f6d555ada7dc0ebac3459fccd8dfbad18ba94dbea52437cd7fb431df404d4738c594e720a6d786275acd02259ca613a08a2de118d0150d2ccae602102aca0cd03666a53f67c0b9943df5046c15baeaf496a9f018b7c939de1509de71ce47dd6f44c57f4e01e569be46932773190c154470cefbd1f4af82d28e4b31]
[keyDer = 308201b43082012b06072a8648ce3804013082011e02818100b34ce9c1e78294d3258473842005d2a48c8c566cfca8f84c0606f2529b59a6d38aae071b53bb2167eaa4fc3b01fe176e787e481b6037aac62cbc3d089799536a869fa8cdfea1e8b1fd2d1cd3a30350859a2cd6b3ec2f9bfbb68bb11b4bbe2adaa18d64a93639543ae5e16293e311c0cf8c8d6e180df05d08c2fd2d93d570751f021500b90b38ba0a50a43ec6898d3f9b68049777f489b10281800835aa8c358bbf01a1846d1206323fabe408b0e98789fcc6239da14d4b3f86c276a8f48aa85a59507e620ad1bc745f0f1cbf63ec98c229c2610d77c634d1642e404354771655b2d5662f7a45227178ce3430af0f6b3bb94b52f7f51e97bad659b1ba0684e208be624c28d82fb1162f18dd9dce45216461654cf3374624d15a8d03818200027f61fe5b61f6d555ada7dc0ebac3459fccd8dfbad18ba94dbea52437cd7fb431df404d4738c594e720a6d786275acd02259ca613a08a2de118d0150d2ccae602102aca0cd03666a53f67c0b9943df5046c15baeaf496a9f018b7c939de1509de71ce47dd6f44c57f4e01e569be46932773190c154470cefbd1f4af82d28e4b31]
[sha = SHA-1]

# tcId = 302
# s == 1
msg = 54657374
result = valid
sig = 3019021462ba827381396dc44facc66c344f91788f11c6fc020101

[key.g = 16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde]
[key.keySize = 2048]
[key.p = 008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667]
[key.q = 00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d]
[key.type = DsaPublicKey]
[key.y = 1e77f842b1ae0fcd9929d394161d41e14614ff7507a9a31f4a1f14d22e2a627a1f4e596624883f1a5b168e9425146f22d5f6ee28757414714bb994ba1129f015d6e04a717edf9b530a5d5cab94f14631e8b4cf79aeb358cc741845553841e8ac461630e804a62f43676ba6794af66899c377b869ea612a7b9fe6611aa96be52eb8b62c979117bbbcca8a7ec1e1ffab1c7dfcfc7048700d3ae3858136e897701d7c2921b5dfef1d1f897f50d96ca1b5c2edc58cada18919e35642f0807eebfa00c99a32f4d095c3188f78ed54711be0325c4b532aeccd6540a567c327225440ea15319bde06510479a1861799e25b57decc73c036d75a0702bd373ca231349931]
[keyDer = 308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde0382010500028201001e77f842b1ae0fcd9929d394161d41e14614ff7507a9a31f4a1f14d22e2a627a1f4e596624883f1a5b168e9425146f22d5f6ee28757414714bb994ba1129f015d6e04a717edf9b530a5d5cab94f14631e8b4cf79aeb358cc741845553841e8ac461630e804a62f43676ba6794af66899c377b869ea612a7b9fe6611aa96be52eb8b62c979117bbbcca8a7ec1e1ffab1c7dfcfc7048700d3ae3858136e897701d7c2921b5dfef1d1f897f50d96ca1b5c2edc58cada18919e35642f0807eebfa00c99a32f4d095c3188f78ed54711be0325c4b532aeccd6540a567c327225440ea15319bde06510479a1861799e25b57decc73c036d75a0702bd373ca231349931]
[sha = SHA-224]

# tcId = 303
# Legacy:ASN encoding of s misses leading 0
msg = 48656c6c6f
result = acceptable
sig = 303c021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021cade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236
flags = NoLeadingZero

# tcId = 304
# valid
msg = 48656c6c6f
result = valid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 305
# long form encoding of length of sequence
msg = 48656c6c6f
result = invalid
sig = 30813d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 306
# length of sequence contains leading 0
msg = 48656c6c6f
result = invalid
sig = 3082003d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 307
# wrong length of sequence
msg = 48656c6c6f
result = invalid
sig = 303e021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 308
# wrong length of sequence
msg = 48656c6c6f
result = invalid
sig = 303c021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 309
# uint32 overflow in length of sequence
msg = 48656c6c6f
result = invalid
sig = 3085010000003d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 310
# uint64 overflow in length of sequence
msg = 48656c6c6f
result = invalid
sig = 308901000000000000003d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 311
# length of sequence = 2**31 - 1
msg = 48656c6c6f
result = invalid
sig = 30847fffffff021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 312
# length of sequence = 2**32 - 1
msg = 48656c6c6f
result = invalid
sig = 3084ffffffff021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 313
# length of sequence = 2**40 - 1
msg = 48656c6c6f
result = invalid
sig = 3085ffffffffff021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 314
# length of sequence = 2**64 - 1
msg = 48656c6c6f
result = invalid
sig = 3088ffffffffffffffff021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 315
# incorrect length of sequence
msg = 48656c6c6f
result = invalid
sig = 30ff021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 316
# indefinite length without termination
msg = 48656c6c6f
result = invalid
sig = 3080021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 317
# indefinite length without termination
msg = 48656c6c6f
result = invalid
sig = 303d02801e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 318
# indefinite length without termination
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd028000ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 319
# removing sequence
msg = 48656c6c6f
result = invalid
sig = 

# tcId = 320
# lonely sequence tag
msg = 48656c6c6f
result = invalid
sig = 30

# tcId = 321
# appending 0's to sequence
msg = 48656c6c6f
result = invalid
sig = 303f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000

# tcId = 322
# prepending 0's to sequence
msg = 48656c6c6f
result = invalid
sig = 303f0000021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 323
# appending unused 0's to sequence
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000

# tcId = 324
# appending null value to sequence
msg = 48656c6c6f
result = invalid
sig = 303f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360500

# tcId = 325
# including garbage
msg = 48656c6c6f
result = invalid
sig = 3042498177303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 326
# including garbage
msg = 48656c6c6f
result = invalid
sig = 30412500303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 327
# including garbage
msg = 48656c6c6f
result = invalid
sig = 303f303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360004deadbeef

# tcId = 328
# including garbage
msg = 48656c6c6f
result = invalid
sig = 30422221498177021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 329
# including garbage
msg = 48656c6c6f
result = invalid
sig = 304122202500021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 330
# including garbage
msg = 48656c6c6f
result = invalid
sig = 3045221e021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0004deadbeef021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 331
# including garbage
msg = 48656c6c6f
result = invalid
sig = 3042021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd2222498177021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 332
# including garbage
msg = 48656c6c6f
result = invalid
sig = 3041021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd22212500021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 333
# including garbage
msg = 48656c6c6f
result = invalid
sig = 3045021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd221f021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360004deadbeef

# tcId = 334
# including undefined tags
msg = 48656c6c6f
result = invalid
sig = 3045aa00bb00cd00303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 335
# including undefined tags
msg = 48656c6c6f
result = invalid
sig = 3043aa02aabb303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 336
# including undefined tags
msg = 48656c6c6f
result = invalid
sig = 30452224aa00bb00cd00021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 337
# including undefined tags
msg = 48656c6c6f
result = invalid
sig = 30432222aa02aabb021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 338
# including undefined tags
msg = 48656c6c6f
result = invalid
sig = 3045021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd2225aa00bb00cd00021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 339
# including undefined tags
msg = 48656c6c6f
result = invalid
sig = 3043021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd2223aa02aabb021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 340
# truncated length of sequence
msg = 48656c6c6f
result = invalid
sig = 3081

# tcId = 341
# using composition with indefinite length
msg = 48656c6c6f
result = invalid
sig = 3080303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000

# tcId = 342
# using composition with indefinite length
msg = 48656c6c6f
result = invalid
sig = 30412280021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0000021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 343
# using composition with indefinite length
msg = 48656c6c6f
result = invalid
sig = 3041021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd2280021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000

# tcId = 344
# using composition with wrong tag
msg = 48656c6c6f
result = invalid
sig = 3080313d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000

# tcId = 345
# using composition with wrong tag
msg = 48656c6c6f
result = invalid
sig = 30412280031c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0000021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 346
# using composition with wrong tag
msg = 48656c6c6f
result = invalid
sig = 3041021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd2280031d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000

# tcId = 347
# Replacing sequence with NULL
msg = 48656c6c6f
result = invalid
sig = 0500

# tcId = 348
# changing tag value of sequence
msg = 48656c6c6f
result = invalid
sig = 2e3d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 349
# changing tag value of sequence
msg = 48656c6c6f
result = invalid
sig = 2f3d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 350
# changing tag value of sequence
msg = 48656c6c6f
result = invalid
sig = 313d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 351
# changing tag value of sequence
msg = 48656c6c6f
result = invalid
sig = 323d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 352
# changing tag value of sequence
msg = 48656c6c6f
result = invalid
sig = ff3d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 353
# dropping value of sequence
msg = 48656c6c6f
result = invalid
sig = 3000

# tcId = 354
# using composition for sequence
msg = 48656c6c6f
result = invalid
sig = 3041300102303c1c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 355
# truncated sequence
msg = 48656c6c6f
result = invalid
sig = 303c021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862

# tcId = 356
# truncated sequence
msg = 48656c6c6f
result = invalid
sig = 303c1c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 357
# indefinite length
msg = 48656c6c6f
result = invalid
sig = 3080021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000

# tcId = 358
# indefinite length with truncated delimiter
msg = 48656c6c6f
result = invalid
sig = 3080021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe878623600

# tcId = 359
# indefinite length with additional element
msg = 48656c6c6f
result = invalid
sig = 3080021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe878623605000000

# tcId = 360
# indefinite length with truncated element
msg = 48656c6c6f
result = invalid
sig = 3080021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236060811220000

# tcId = 361
# indefinite length with garbage
msg = 48656c6c6f
result = invalid
sig = 3080021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000fe02beef

# tcId = 362
# indefinite length with nonempty EOC
msg = 48656c6c6f
result = invalid
sig = 3080021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360002beef

# tcId = 363
# prepend empty sequence
msg = 48656c6c6f
result = invalid
sig = 303f3000021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 364
# append empty sequence
msg = 48656c6c6f
result = invalid
sig = 303f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862363000

# tcId = 365
# append garbage with high tag number
msg = 48656c6c6f
result = invalid
sig = 3040021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236bf7f00

# tcId = 366
# sequence of sequence
msg = 48656c6c6f
result = invalid
sig = 303f303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 367
# truncated sequence: removed last 1 elements
msg = 48656c6c6f
result = invalid
sig = 301e021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd

# tcId = 368
# repeating element in sequence
msg = 48656c6c6f
result = invalid
sig = 305c021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 369
# long form encoding of length of integer
msg = 48656c6c6f
result = invalid
sig = 303e02811c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 370
# long form encoding of length of integer
msg = 48656c6c6f
result = invalid
sig = 303e021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd02811d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 371
# length of integer contains leading 0
msg = 48656c6c6f
result = invalid
sig = 303f0282001c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 372
# length of integer contains leading 0
msg = 48656c6c6f
result = invalid
sig = 303f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0282001d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 373
# wrong length of integer
msg = 48656c6c6f
result = invalid
sig = 303d021d1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 374
# wrong length of integer
msg = 48656c6c6f
result = invalid
sig = 303d021b1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 375
# wrong length of integer
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021e00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 376
# wrong length of integer
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021c00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 377
# uint32 overflow in length of integer
msg = 48656c6c6f
result = invalid
sig = 30420285010000001c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 378
# uint32 overflow in length of integer
msg = 48656c6c6f
result = invalid
sig = 3042021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0285010000001d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 379
# uint64 overflow in length of integer
msg = 48656c6c6f
result = invalid
sig = 3046028901000000000000001c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 380
# uint64 overflow in length of integer
msg = 48656c6c6f
result = invalid
sig = 3046021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd028901000000000000001d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 381
# length of integer = 2**31 - 1
msg = 48656c6c6f
result = invalid
sig = 304102847fffffff1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 382
# length of integer = 2**31 - 1
msg = 48656c6c6f
result = invalid
sig = 3041021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd02847fffffff00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 383
# length of integer = 2**32 - 1
msg = 48656c6c6f
result = invalid
sig = 30410284ffffffff1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 384
# length of integer = 2**32 - 1
msg = 48656c6c6f
result = invalid
sig = 3041021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0284ffffffff00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 385
# length of integer = 2**40 - 1
msg = 48656c6c6f
result = invalid
sig = 30420285ffffffffff1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 386
# length of integer = 2**40 - 1
msg = 48656c6c6f
result = invalid
sig = 3042021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0285ffffffffff00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 387
# length of integer = 2**64 - 1
msg = 48656c6c6f
result = invalid
sig = 30450288ffffffffffffffff1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 388
# length of integer = 2**64 - 1
msg = 48656c6c6f
result = invalid
sig = 3045021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0288ffffffffffffffff00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 389
# incorrect length of integer
msg = 48656c6c6f
result = invalid
sig = 303d02ff1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 390
# incorrect length of integer
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd02ff00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 391
# removing integer
msg = 48656c6c6f
result = invalid
sig = 301f021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 392
# lonely integer tag
msg = 48656c6c6f
result = invalid
sig = 302002021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 393
# lonely integer tag
msg = 48656c6c6f
result = invalid
sig = 301f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd02

# tcId = 394
# appending 0's to integer
msg = 48656c6c6f
result = invalid
sig = 303f021e1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0000021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 395
# appending 0's to integer
msg = 48656c6c6f
result = invalid
sig = 303f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021f00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360000

# tcId = 396
# prepending 0's to integer
msg = 48656c6c6f
result = invalid
sig = 303f021e00001e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 397
# prepending 0's to integer
msg = 48656c6c6f
result = invalid
sig = 303f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021f000000ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 398
# appending unused 0's to integer
msg = 48656c6c6f
result = invalid
sig = 303f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0000021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 399
# appending null value to integer
msg = 48656c6c6f
result = invalid
sig = 303f021e1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0500021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 400
# appending null value to integer
msg = 48656c6c6f
result = invalid
sig = 303f021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021f00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862360500

# tcId = 401
# truncated length of integer
msg = 48656c6c6f
result = invalid
sig = 30210281021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 402
# truncated length of integer
msg = 48656c6c6f
result = invalid
sig = 3020021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0281

# tcId = 403
# Replacing integer with NULL
msg = 48656c6c6f
result = invalid
sig = 30210500021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 404
# Replacing integer with NULL
msg = 48656c6c6f
result = invalid
sig = 3020021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0500

# tcId = 405
# changing tag value of integer
msg = 48656c6c6f
result = invalid
sig = 303d001c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 406
# changing tag value of integer
msg = 48656c6c6f
result = invalid
sig = 303d011c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 407
# changing tag value of integer
msg = 48656c6c6f
result = invalid
sig = 303d031c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 408
# changing tag value of integer
msg = 48656c6c6f
result = invalid
sig = 303d041c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 409
# changing tag value of integer
msg = 48656c6c6f
result = invalid
sig = 303dff1c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 410
# changing tag value of integer
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd001d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 411
# changing tag value of integer
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd011d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 412
# changing tag value of integer
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd031d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 413
# changing tag value of integer
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd041d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 414
# changing tag value of integer
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cdff1d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 415
# dropping value of integer
msg = 48656c6c6f
result = invalid
sig = 30210200021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 416
# dropping value of integer
msg = 48656c6c6f
result = invalid
sig = 3020021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd0200

# tcId = 417
# using composition for integer
msg = 48656c6c6f
result = invalid
sig = 3041222002011e021b41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 418
# using composition for integer
msg = 48656c6c6f
result = invalid
sig = 3041021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd2221020100021cade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 419
# modify first byte of integer
msg = 48656c6c6f
result = invalid
sig = 303d021c1c41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 420
# modify first byte of integer
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d02ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 421
# modify last byte of integer
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c94d021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 422
# modify last byte of integer
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862b6

# tcId = 423
# truncated integer
msg = 48656c6c6f
result = invalid
sig = 303c021b1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 424
# truncated integer
msg = 48656c6c6f
result = invalid
sig = 303c021b41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 425
# truncated integer
msg = 48656c6c6f
result = invalid
sig = 303c021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021c00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe87862

# tcId = 426
# leading ff in integer
msg = 48656c6c6f
result = invalid
sig = 303e021dff1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 427
# leading ff in integer
msg = 48656c6c6f
result = invalid
sig = 303e021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021eff00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 428
# replaced integer by infinity
msg = 48656c6c6f
result = invalid
sig = 3022090180021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 429
# replaced integer by infinity
msg = 48656c6c6f
result = invalid
sig = 3021021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd090180

# tcId = 430
# replacing integer with zero
msg = 48656c6c6f
result = invalid
sig = 3022020100021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 431
# replacing integer with zero
msg = 48656c6c6f
result = invalid
sig = 3021021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd020100

# tcId = 432
# Modified r or s, e.g. by adding or subtracting the group order
msg = 48656c6c6f
result = invalid
sig = 303e021d00d9384b2032d060e59848f87cb4535936bc25fa77959e96d7f88e332a021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 433
# Modified r or s, e.g. by adding or subtracting the group order
msg = 48656c6c6f
result = invalid
sig = 303e021dff634b1dd327de7125da7903ad2163ca2addc096101fd395567ee36070021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 434
# Modified r or s, e.g. by adding or subtracting the group order
msg = 48656c6c6f
result = invalid
sig = 303d021ce1be4b8652a896fa469f01eb15246e4f330cb7bc2546e9e8c4473633021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 435
# Modified r or s, e.g. by adding or subtracting the group order
msg = 48656c6c6f
result = invalid
sig = 303e021d009cb4e22cd8218eda2586fc52de9c35d5223f69efe02c6aa9811c9f90021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 436
# Modified r or s, e.g. by adding or subtracting the group order
msg = 48656c6c6f
result = invalid
sig = 303e021dff26c7b4dfcd2f9f1a67b707834baca6c943da05886a6169280771ccd6021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 437
# Modified r or s, e.g. by adding or subtracting the group order
msg = 48656c6c6f
result = invalid
sig = 303e021d011e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 438
# Modified r or s, e.g. by adding or subtracting the group order
msg = 48656c6c6f
result = invalid
sig = 303e021d00e1be4b8652a896fa469f01eb15246e4f330cb7bc2546e9e8c4473633021d00ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 439
# Modified r or s, e.g. by adding or subtracting the group order
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d0168dcf02f57b0caef7ddc183bee1ca94ee09c1a02ee4b0200a54dcb93

# tcId = 440
# Modified r or s, e.g. by adding or subtracting the group order
msg = 48656c6c6f
result = invalid
sig = 303c021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021cf2efc2e24cbedb2fc00c236c5b2d1a430236b59b7880007f2ba2f8d9

# tcId = 441
# Modified r or s, e.g. by adding or subtracting the group order
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021dff5219a6772dc82cf0610be22bdb5b1e370e969830cc9a7ec017879dca

# tcId = 442
# Modified r or s, e.g. by adding or subtracting the group order
msg = 48656c6c6f
result = invalid
sig = 303c021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021c0d103d1db34124d03ff3dc93a4d2e5bcfdc94a64877fff80d45d0727

# tcId = 443
# Modified r or s, e.g. by adding or subtracting the group order
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021dfe97230fd0a84f35108223e7c411e356b11f63e5fd11b4fdff5ab2346d

# tcId = 444
# Modified r or s, e.g. by adding or subtracting the group order
msg = 48656c6c6f
result = invalid
sig = 303d021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021d01ade65988d237d30f9ef41dd424a4e1c8f16967cf3365813fe8786236

# tcId = 445
# Modified r or s, e.g. by adding or subtracting the group order
msg = 48656c6c6f
result = invalid
sig = 303c021c1e41b479ad576905b960fe14eadb91b0ccf34843dab916173bb8c9cd021c5219a6772dc82cf0610be22bdb5b1e370e969830cc9a7ec017879dca

# tcId = 446
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022020100021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 447
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3006020100020100
flags = EdgeCase

# tcId = 448
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3006020100020101
flags = EdgeCase

# tcId = 449
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30060201000201ff
flags = EdgeCase

# tcId = 450
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3021020100021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 451
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3021020100021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 452
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 453
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 454
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 455
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022020100021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 456
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3082010802010002820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 457
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3008020100090380fe01
flags = EdgeCase

# tcId = 458
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3006020100090142
flags = EdgeCase

# tcId = 459
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022020101021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 460
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3006020101020100
flags = EdgeCase

# tcId = 461
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3006020101020101
flags = EdgeCase

# tcId = 462
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30060201010201ff
flags = EdgeCase

# tcId = 463
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3021020101021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 464
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3021020101021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 465
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 466
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 467
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 468
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022020101021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 469
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3082010802010102820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 470
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3008020101090380fe01
flags = EdgeCase

# tcId = 471
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3006020101090142
flags = EdgeCase

# tcId = 472
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30220201ff021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 473
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30060201ff020100
flags = EdgeCase

# tcId = 474
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30060201ff020101
flags = EdgeCase

# tcId = 475
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30060201ff0201ff
flags = EdgeCase

# tcId = 476
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30210201ff021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 477
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30210201ff021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 478
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 479
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 480
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 481
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30220201ff021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 482
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 308201080201ff02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 483
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30080201ff090380fe01
flags = EdgeCase

# tcId = 484
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30060201ff090142
flags = EdgeCase

# tcId = 485
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 486
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae020100
flags = EdgeCase

# tcId = 487
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae020101
flags = EdgeCase

# tcId = 488
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae0201ff
flags = EdgeCase

# tcId = 489
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 490
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 491
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 492
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 493
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 494
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 495
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30820123021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 496
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3023021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae090380fe01
flags = EdgeCase

# tcId = 497
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae090142
flags = EdgeCase

# tcId = 498
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 499
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af020100
flags = EdgeCase

# tcId = 500
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af020101
flags = EdgeCase

# tcId = 501
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af0201ff
flags = EdgeCase

# tcId = 502
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 503
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 504
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 505
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 506
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 507
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 508
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30820123021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 509
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3023021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af090380fe01
flags = EdgeCase

# tcId = 510
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af090142
flags = EdgeCase

# tcId = 511
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 512
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c020100
flags = EdgeCase

# tcId = 513
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c020101
flags = EdgeCase

# tcId = 514
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c0201ff
flags = EdgeCase

# tcId = 515
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 516
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 517
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 518
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 519
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 520
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 521
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 522
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c090380fe01
flags = EdgeCase

# tcId = 523
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c090142
flags = EdgeCase

# tcId = 524
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 525
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d020100
flags = EdgeCase

# tcId = 526
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d020101
flags = EdgeCase

# tcId = 527
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0201ff
flags = EdgeCase

# tcId = 528
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 529
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 530
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 531
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 532
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 533
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 534
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 535
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d090380fe01
flags = EdgeCase

# tcId = 536
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d090142
flags = EdgeCase

# tcId = 537
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 538
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e020100
flags = EdgeCase

# tcId = 539
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e020101
flags = EdgeCase

# tcId = 540
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e0201ff
flags = EdgeCase

# tcId = 541
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 542
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 543
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 544
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 545
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 546
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 547
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 548
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e090380fe01
flags = EdgeCase

# tcId = 549
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e090142
flags = EdgeCase

# tcId = 550
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d0100000000000000000000000000000000000000000000000000000000021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 551
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022021d0100000000000000000000000000000000000000000000000000000000020100
flags = EdgeCase

# tcId = 552
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022021d0100000000000000000000000000000000000000000000000000000000020101
flags = EdgeCase

# tcId = 553
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022021d01000000000000000000000000000000000000000000000000000000000201ff
flags = EdgeCase

# tcId = 554
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021d0100000000000000000000000000000000000000000000000000000000021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 555
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303d021d0100000000000000000000000000000000000000000000000000000000021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 556
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 557
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 558
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 559
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 303e021d0100000000000000000000000000000000000000000000000000000000021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 560
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 30820124021d010000000000000000000000000000000000000000000000000000000002820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 561
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3024021d0100000000000000000000000000000000000000000000000000000000090380fe01
flags = EdgeCase

# tcId = 562
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3022021d0100000000000000000000000000000000000000000000000000000000090142
flags = EdgeCase

# tcId = 563
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 564
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667020100
flags = EdgeCase

# tcId = 565
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667020101
flags = EdgeCase

# tcId = 566
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd6670201ff
flags = EdgeCase

# tcId = 567
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3082012302820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 568
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3082012302820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 569
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 570
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 571
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 572
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 573
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3082020a02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd66702820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 574
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3082010a02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667090380fe01
flags = EdgeCase

# tcId = 575
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667090142
flags = EdgeCase

# tcId = 576
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3024090380fe01021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 577
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3008090380fe01020100
flags = EdgeCase

# tcId = 578
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3008090380fe01020101
flags = EdgeCase

# tcId = 579
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3008090380fe010201ff
flags = EdgeCase

# tcId = 580
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3023090380fe01021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 581
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3023090380fe01021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 582
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 583
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 584
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 585
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3024090380fe01021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 586
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3082010a090380fe0102820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 587
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 300a090380fe01090380fe01
flags = EdgeCase

# tcId = 588
# Signatures with special case values for r and s.
msg = 48656c6c6f
result = invalid
sig = 3008090380fe01090142
flags = EdgeCase

# tcId = 589
# Signature encoding contains wrong type.
msg = 48656c6c6f
result = invalid
sig = 30060201010c0130

# tcId = 590
# Signature encoding contains wrong type.
msg = 48656c6c6f
result = invalid
sig = 30050201010c00

# tcId = 591
# Signature encoding contains wrong type.
msg = 48656c6c6f
result = invalid
sig = 30090c0225730c03732573

# tcId = 592
# Signature encoding contains wrong type.
msg = 48656c6c6f
result = invalid
sig = 30080201013003020100

# tcId = 593
# Signature encoding contains wrong type.
msg = 48656c6c6f
result = invalid
sig = 3003020101

# tcId = 594
# Signature encoding contains wrong type.
msg = 48656c6c6f
result = invalid
sig = 3006020101010100

# tcId = 595
# random signature
msg = 48656c6c6f
result = valid
sig = 303e021d0085c771ecf9c77debae0c54f749dba4b07ae519ca1037091dd6f294cf021d00a02f74985198cf88e310e55277ba598b336164850fdd5308a7beb1a7

# tcId = 596
# random signature
msg = 48656c6c6f
result = valid
sig = 303c021c043cd5580b8bfb5975edef132d6de7848096392e0adf46342bad7bb7021c142b6572ce61b032d45d4597a88c4e54dd593fa7ca6c8e6882df77f6

# tcId = 597
# random signature
msg = 48656c6c6f
result = valid
sig = 303d021d00914e4f7ba8df44cf63c57d219c6b5da093fe3a94604bd2ef88b630f5021c6a7b804cbfc69937903bbd0c8f67306a6e8bf0d0501ae3f3190dca19

# tcId = 598
# random signature
msg = 48656c6c6f
result = valid
sig = 303d021c0a8ffb160da0ffe319e7d3a5ea299f531f8421bfdcfd6bb66c6adcf2021d008a75e2cbaa709b2b5d81d346f6e90a8dc353c5835393b1d5a6653f3c

# tcId = 599
# random signature
msg = 48656c6c6f
result = valid
sig = 303d021d00a7f164d32f44684fcde89f0fa9647128bc0c290f08c9b9e6f7db3fe1021c2dca34db2f320c95de4e9de4986bbf50860753d5deb728237678afb6

[key.g = 16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde]
[key.keySize = 2048]
[key.p = 008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667]
[key.q = 00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d]
[key.type = DsaPublicKey]
[key.y = 6978b68d31334ee5bc7b3e91ab6c2336fab45c64836bd92cb5337b734db9e8e44f889f8869829f4fe174dc9344c164a0ba5b0126259ba8a43f607564fa4a1d0d49645e1d5886a1fc485e2fe91e56eae330da05e17b0b3d018c290285b249bc409e7af54300fc7c3eb34911457e2371931ad9302e8450cd95df3d561ea0ad94d0a2eabcafe0dd6728fb280029b556d9f4fa7c0f46a7804329936708e97e11fc22b2a50761a890c65b5fea2a1a4172f6be9eaa60e738cdf60c015142e2e562bb62a11e810ccdf0bf633307382f2d9a9769b115dfcdab4bacae73feca289db209dce34cbe126e8c7f9d9e4f8f711349a608d567b48c050e9dfb32bc184ecaa4f0f0]
[keyDer = 308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde0382010500028201006978b68d31334ee5bc7b3e91ab6c2336fab45c64836bd92cb5337b734db9e8e44f889f8869829f4fe174dc9344c164a0ba5b0126259ba8a43f607564fa4a1d0d49645e1d5886a1fc485e2fe91e56eae330da05e17b0b3d018c290285b249bc409e7af54300fc7c3eb34911457e2371931ad9302e8450cd95df3d561ea0ad94d0a2eabcafe0dd6728fb280029b556d9f4fa7c0f46a7804329936708e97e11fc22b2a50761a890c65b5fea2a1a4172f6be9eaa60e738cdf60c015142e2e562bb62a11e810ccdf0bf633307382f2d9a9769b115dfcdab4bacae73feca289db209dce34cbe126e8c7f9d9e4f8f711349a608d567b48c050e9dfb32bc184ecaa4f0f0]
[sha = SHA-224]

# tcId = 600
# r,s = 1,1
msg = 54657374
result = valid
sig = 3006020101020101

# tcId = 601
# r,s = 1,5
msg = 54657374
result = valid
sig = 3006020101020105

# tcId = 602
# u2 small
msg = 54657374
result = valid
sig = 3022020101021d009592121ed12d93197f1ffb863ac63937f28ef4f62f1e009a30aabab1

# tcId = 603
# s == q-1
msg = 54657374
result = valid
sig = 3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c

[key.g = 16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde]
[key.keySize = 2048]
[key.p = 008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667]
[key.q = 00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d]
[key.type = DsaPublicKey]
[key.y = 2a64953bde40789f80ed8227192286115b92d09d5de96904e803ec4ecfbd73e0f08e82910febf19fa3cdc55ff20eb970d9c712f44785c0fd592c17fb43f4625357a4ac8a1a628f72040ae5360839c7c1f6b214e7a15530fe22887139ea0f05a9daf9d95bd6b7467abf9107c9fbe31e36330276eeccce3d59635206d60ca256f9af60627626b0594984b5a075c42c42067fa8c330f258bcf145df27a97da8ee419b54e3ab296c7ce9ef6a0113389b3cac7885b44b3722d27cad60e4e5a924a1ed0342cea9e99256f6bc1308d4af2c0af9379b1cf2119ce113c085705f5519ccc1ba8562a2236190d3f0c0a10f01466ad79a48127c28433f6b34e24a539af60f3d]
[keyDer = 308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde0382010500028201002a64953bde40789f80ed8227192286115b92d09d5de96904e803ec4ecfbd73e0f08e82910febf19fa3cdc55ff20eb970d9c712f44785c0fd592c17fb43f4625357a4ac8a1a628f72040ae5360839c7c1f6b214e7a15530fe22887139ea0f05a9daf9d95bd6b7467abf9107c9fbe31e36330276eeccce3d59635206d60ca256f9af60627626b0594984b5a075c42c42067fa8c330f258bcf145df27a97da8ee419b54e3ab296c7ce9ef6a0113389b3cac7885b44b3722d27cad60e4e5a924a1ed0342cea9e99256f6bc1308d4af2c0af9379b1cf2119ce113c085705f5519ccc1ba8562a2236190d3f0c0a10f01466ad79a48127c28433f6b34e24a539af60f3d]
[sha = SHA-224]

# tcId = 604
# s == 1
msg = 54657374
result = valid
sig = 3021021c5a252f4fc55618747fd94b13c9bee62bb958d85777cb07dd90710d24020101

[key.g = 16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde]
[key.keySize = 2048]
[key.p = 008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667]
[key.q = 00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d]
[key.type = DsaPublicKey]
[key.y = 1e77f842b1ae0fcd9929d394161d41e14614ff7507a9a31f4a1f14d22e2a627a1f4e596624883f1a5b168e9425146f22d5f6ee28757414714bb994ba1129f015d6e04a717edf9b530a5d5cab94f14631e8b4cf79aeb358cc741845553841e8ac461630e804a62f43676ba6794af66899c377b869ea612a7b9fe6611aa96be52eb8b62c979117bbbcca8a7ec1e1ffab1c7dfcfc7048700d3ae3858136e897701d7c2921b5dfef1d1f897f50d96ca1b5c2edc58cada18919e35642f0807eebfa00c99a32f4d095c3188f78ed54711be0325c4b532aeccd6540a567c327225440ea15319bde06510479a1861799e25b57decc73c036d75a0702bd373ca231349931]
[keyDer = 308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde0382010500028201001e77f842b1ae0fcd9929d394161d41e14614ff7507a9a31f4a1f14d22e2a627a1f4e596624883f1a5b168e9425146f22d5f6ee28757414714bb994ba1129f015d6e04a717edf9b530a5d5cab94f14631e8b4cf79aeb358cc741845553841e8ac461630e804a62f43676ba6794af66899c377b869ea612a7b9fe6611aa96be52eb8b62c979117bbbcca8a7ec1e1ffab1c7dfcfc7048700d3ae3858136e897701d7c2921b5dfef1d1f897f50d96ca1b5c2edc58cada18919e35642f0807eebfa00c99a32f4d095c3188f78ed54711be0325c4b532aeccd6540a567c327225440ea15319bde06510479a1861799e25b57decc73c036d75a0702bd373ca231349931]
[sha = SHA-256]

# tcId = 605
# Legacy:ASN encoding of r misses leading 0
msg = 54657374
result = acceptable
sig = 303c021c9b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116
flags = NoLeadingZero

# tcId = 606
# valid
msg = 54657374
result = valid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 607
# long form encoding of length of sequence
msg = 54657374
result = invalid
sig = 30813d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 608
# length of sequence contains leading 0
msg = 54657374
result = invalid
sig = 3082003d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 609
# wrong length of sequence
msg = 54657374
result = invalid
sig = 303e021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 610
# wrong length of sequence
msg = 54657374
result = invalid
sig = 303c021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 611
# uint32 overflow in length of sequence
msg = 54657374
result = invalid
sig = 3085010000003d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 612
# uint64 overflow in length of sequence
msg = 54657374
result = invalid
sig = 308901000000000000003d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 613
# length of sequence = 2**31 - 1
msg = 54657374
result = invalid
sig = 30847fffffff021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 614
# length of sequence = 2**32 - 1
msg = 54657374
result = invalid
sig = 3084ffffffff021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 615
# length of sequence = 2**40 - 1
msg = 54657374
result = invalid
sig = 3085ffffffffff021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 616
# length of sequence = 2**64 - 1
msg = 54657374
result = invalid
sig = 3088ffffffffffffffff021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 617
# incorrect length of sequence
msg = 54657374
result = invalid
sig = 30ff021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 618
# indefinite length without termination
msg = 54657374
result = invalid
sig = 3080021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 619
# indefinite length without termination
msg = 54657374
result = invalid
sig = 303d0280009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 620
# indefinite length without termination
msg = 54657374
result = invalid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee9302805fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 621
# removing sequence
msg = 54657374
result = invalid
sig = 

# tcId = 622
# lonely sequence tag
msg = 54657374
result = invalid
sig = 30

# tcId = 623
# appending 0's to sequence
msg = 54657374
result = invalid
sig = 303f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000

# tcId = 624
# prepending 0's to sequence
msg = 54657374
result = invalid
sig = 303f0000021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 625
# appending unused 0's to sequence
msg = 54657374
result = invalid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000

# tcId = 626
# appending null value to sequence
msg = 54657374
result = invalid
sig = 303f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160500

# tcId = 627
# including garbage
msg = 54657374
result = invalid
sig = 3042498177303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 628
# including garbage
msg = 54657374
result = invalid
sig = 30412500303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 629
# including garbage
msg = 54657374
result = invalid
sig = 303f303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160004deadbeef

# tcId = 630
# including garbage
msg = 54657374
result = invalid
sig = 30422222498177021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 631
# including garbage
msg = 54657374
result = invalid
sig = 304122212500021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 632
# including garbage
msg = 54657374
result = invalid
sig = 3045221f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930004deadbeef021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 633
# including garbage
msg = 54657374
result = invalid
sig = 3042021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee932221498177021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 634
# including garbage
msg = 54657374
result = invalid
sig = 3041021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee9322202500021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 635
# including garbage
msg = 54657374
result = invalid
sig = 3045021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93221e021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160004deadbeef

# tcId = 636
# including undefined tags
msg = 54657374
result = invalid
sig = 3045aa00bb00cd00303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 637
# including undefined tags
msg = 54657374
result = invalid
sig = 3043aa02aabb303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 638
# including undefined tags
msg = 54657374
result = invalid
sig = 30452225aa00bb00cd00021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 639
# including undefined tags
msg = 54657374
result = invalid
sig = 30432223aa02aabb021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 640
# including undefined tags
msg = 54657374
result = invalid
sig = 3045021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee932224aa00bb00cd00021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 641
# including undefined tags
msg = 54657374
result = invalid
sig = 3043021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee932222aa02aabb021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 642
# truncated length of sequence
msg = 54657374
result = invalid
sig = 3081

# tcId = 643
# using composition with indefinite length
msg = 54657374
result = invalid
sig = 3080303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000

# tcId = 644
# using composition with indefinite length
msg = 54657374
result = invalid
sig = 30412280021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930000021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 645
# using composition with indefinite length
msg = 54657374
result = invalid
sig = 3041021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee932280021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000

# tcId = 646
# using composition with wrong tag
msg = 54657374
result = invalid
sig = 3080313d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000

# tcId = 647
# using composition with wrong tag
msg = 54657374
result = invalid
sig = 30412280031d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930000021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 648
# using composition with wrong tag
msg = 54657374
result = invalid
sig = 3041021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee932280031c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000

# tcId = 649
# Replacing sequence with NULL
msg = 54657374
result = invalid
sig = 0500

# tcId = 650
# changing tag value of sequence
msg = 54657374
result = invalid
sig = 2e3d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 651
# changing tag value of sequence
msg = 54657374
result = invalid
sig = 2f3d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 652
# changing tag value of sequence
msg = 54657374
result = invalid
sig = 313d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 653
# changing tag value of sequence
msg = 54657374
result = invalid
sig = 323d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 654
# changing tag value of sequence
msg = 54657374
result = invalid
sig = ff3d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 655
# dropping value of sequence
msg = 54657374
result = invalid
sig = 3000

# tcId = 656
# using composition for sequence
msg = 54657374
result = invalid
sig = 3041300102303c1d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 657
# truncated sequence
msg = 54657374
result = invalid
sig = 303c021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1

# tcId = 658
# truncated sequence
msg = 54657374
result = invalid
sig = 303c1d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 659
# indefinite length
msg = 54657374
result = invalid
sig = 3080021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000

# tcId = 660
# indefinite length with truncated delimiter
msg = 54657374
result = invalid
sig = 3080021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b11600

# tcId = 661
# indefinite length with additional element
msg = 54657374
result = invalid
sig = 3080021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b11605000000

# tcId = 662
# indefinite length with truncated element
msg = 54657374
result = invalid
sig = 3080021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116060811220000

# tcId = 663
# indefinite length with garbage
msg = 54657374
result = invalid
sig = 3080021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000fe02beef

# tcId = 664
# indefinite length with nonempty EOC
msg = 54657374
result = invalid
sig = 3080021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160002beef

# tcId = 665
# prepend empty sequence
msg = 54657374
result = invalid
sig = 303f3000021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 666
# append empty sequence
msg = 54657374
result = invalid
sig = 303f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1163000

# tcId = 667
# append garbage with high tag number
msg = 54657374
result = invalid
sig = 3040021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116bf7f00

# tcId = 668
# sequence of sequence
msg = 54657374
result = invalid
sig = 303f303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 669
# truncated sequence: removed last 1 elements
msg = 54657374
result = invalid
sig = 301f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93

# tcId = 670
# repeating element in sequence
msg = 54657374
result = invalid
sig = 305b021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 671
# long form encoding of length of integer
msg = 54657374
result = invalid
sig = 303e02811d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 672
# long form encoding of length of integer
msg = 54657374
result = invalid
sig = 303e021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee9302811c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 673
# length of integer contains leading 0
msg = 54657374
result = invalid
sig = 303f0282001d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 674
# length of integer contains leading 0
msg = 54657374
result = invalid
sig = 303f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930282001c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 675
# wrong length of integer
msg = 54657374
result = invalid
sig = 303d021e009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 676
# wrong length of integer
msg = 54657374
result = invalid
sig = 303d021c009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 677
# wrong length of integer
msg = 54657374
result = invalid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021d5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 678
# wrong length of integer
msg = 54657374
result = invalid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021b5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 679
# uint32 overflow in length of integer
msg = 54657374
result = invalid
sig = 30420285010000001d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 680
# uint32 overflow in length of integer
msg = 54657374
result = invalid
sig = 3042021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930285010000001c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 681
# uint64 overflow in length of integer
msg = 54657374
result = invalid
sig = 3046028901000000000000001d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 682
# uint64 overflow in length of integer
msg = 54657374
result = invalid
sig = 3046021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93028901000000000000001c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 683
# length of integer = 2**31 - 1
msg = 54657374
result = invalid
sig = 304102847fffffff009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 684
# length of integer = 2**31 - 1
msg = 54657374
result = invalid
sig = 3041021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee9302847fffffff5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 685
# length of integer = 2**32 - 1
msg = 54657374
result = invalid
sig = 30410284ffffffff009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 686
# length of integer = 2**32 - 1
msg = 54657374
result = invalid
sig = 3041021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930284ffffffff5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 687
# length of integer = 2**40 - 1
msg = 54657374
result = invalid
sig = 30420285ffffffffff009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 688
# length of integer = 2**40 - 1
msg = 54657374
result = invalid
sig = 3042021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930285ffffffffff5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 689
# length of integer = 2**64 - 1
msg = 54657374
result = invalid
sig = 30450288ffffffffffffffff009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 690
# length of integer = 2**64 - 1
msg = 54657374
result = invalid
sig = 3045021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930288ffffffffffffffff5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 691
# incorrect length of integer
msg = 54657374
result = invalid
sig = 303d02ff009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 692
# incorrect length of integer
msg = 54657374
result = invalid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee9302ff5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 693
# removing integer
msg = 54657374
result = invalid
sig = 301e021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 694
# lonely integer tag
msg = 54657374
result = invalid
sig = 301f02021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 695
# lonely integer tag
msg = 54657374
result = invalid
sig = 3020021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee9302

# tcId = 696
# appending 0's to integer
msg = 54657374
result = invalid
sig = 303f021f009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930000021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 697
# appending 0's to integer
msg = 54657374
result = invalid
sig = 303f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021e5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160000

# tcId = 698
# prepending 0's to integer
msg = 54657374
result = invalid
sig = 303f021f0000009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 699
# prepending 0's to integer
msg = 54657374
result = invalid
sig = 303f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021e00005fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 700
# appending unused 0's to integer
msg = 54657374
result = invalid
sig = 303f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930000021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 701
# appending null value to integer
msg = 54657374
result = invalid
sig = 303f021f009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930500021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 702
# appending null value to integer
msg = 54657374
result = invalid
sig = 303f021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021e5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1160500

# tcId = 703
# truncated length of integer
msg = 54657374
result = invalid
sig = 30200281021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 704
# truncated length of integer
msg = 54657374
result = invalid
sig = 3021021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930281

# tcId = 705
# Replacing integer with NULL
msg = 54657374
result = invalid
sig = 30200500021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 706
# Replacing integer with NULL
msg = 54657374
result = invalid
sig = 3021021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930500

# tcId = 707
# changing tag value of integer
msg = 54657374
result = invalid
sig = 303d001d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 708
# changing tag value of integer
msg = 54657374
result = invalid
sig = 303d011d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 709
# changing tag value of integer
msg = 54657374
result = invalid
sig = 303d031d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 710
# changing tag value of integer
msg = 54657374
result = invalid
sig = 303d041d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 711
# changing tag value of integer
msg = 54657374
result = invalid
sig = 303dff1d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 712
# changing tag value of integer
msg = 54657374
result = invalid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93001c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 713
# changing tag value of integer
msg = 54657374
result = invalid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93011c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 714
# changing tag value of integer
msg = 54657374
result = invalid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93031c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 715
# changing tag value of integer
msg = 54657374
result = invalid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93041c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 716
# changing tag value of integer
msg = 54657374
result = invalid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93ff1c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 717
# dropping value of integer
msg = 54657374
result = invalid
sig = 30200200021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 718
# dropping value of integer
msg = 54657374
result = invalid
sig = 3021021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee930200

# tcId = 719
# using composition for integer
msg = 54657374
result = invalid
sig = 30412221020100021c9b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 720
# using composition for integer
msg = 54657374
result = invalid
sig = 3041021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93222002015f021be8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 721
# modify first byte of integer
msg = 54657374
result = invalid
sig = 303d021d029b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 722
# modify first byte of integer
msg = 54657374
result = invalid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5de8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 723
# modify last byte of integer
msg = 54657374
result = invalid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee13021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 724
# modify last byte of integer
msg = 54657374
result = invalid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b196

# tcId = 725
# truncated integer
msg = 54657374
result = invalid
sig = 303c021c009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 726
# truncated integer
msg = 54657374
result = invalid
sig = 303c021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021b5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b1

# tcId = 727
# truncated integer
msg = 54657374
result = invalid
sig = 303c021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021be8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 728
# leading ff in integer
msg = 54657374
result = invalid
sig = 303e021eff009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 729
# leading ff in integer
msg = 54657374
result = invalid
sig = 303e021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021dff5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 730
# replaced integer by infinity
msg = 54657374
result = invalid
sig = 3021090180021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 731
# replaced integer by infinity
msg = 54657374
result = invalid
sig = 3022021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93090180

# tcId = 732
# replacing integer with zero
msg = 54657374
result = invalid
sig = 3021020100021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 733
# replacing integer with zero
msg = 54657374
result = invalid
sig = 3022021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93020100

# tcId = 734
# Modified r or s, e.g. by adding or subtracting the group order
msg = 54657374
result = invalid
sig = 303d021d0156667b48514d3e5d546ca89ff45ada90474113ed248b873430ab57f0021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 735
# Modified r or s, e.g. by adding or subtracting the group order
msg = 54657374
result = invalid
sig = 303c021ce0794dfb465b4e9d969cb3d0616b4b8468dbaf85aec085b2b7008536021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 736
# Modified r or s, e.g. by adding or subtracting the group order
msg = 54657374
result = invalid
sig = 303d021dff64901b5e342bb9828a7b51c7d51cecf5a7f19e469659f98c8c2a116d021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 737
# Modified r or s, e.g. by adding or subtracting the group order
msg = 54657374
result = invalid
sig = 303c021c1f86b204b9a4b16269634c2f9e94b47b9724507a513f7a4d48ff7aca021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 738
# Modified r or s, e.g. by adding or subtracting the group order
msg = 54657374
result = invalid
sig = 303d021dfea99984b7aeb2c1a2ab9357600ba5256fb8beec12db7478cbcf54a810021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 739
# Modified r or s, e.g. by adding or subtracting the group order
msg = 54657374
result = invalid
sig = 303d021d019b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 740
# Modified r or s, e.g. by adding or subtracting the group order
msg = 54657374
result = invalid
sig = 303c021c64901b5e342bb9828a7b51c7d51cecf5a7f19e469659f98c8c2a116d021c5fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 741
# Modified r or s, e.g. by adding or subtracting the group order
msg = 54657374
result = invalid
sig = 303e021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021d011adeb9ed974f878dc2fc26f4bf86ffda5f7abe6c26ebabf9b8181a73

# tcId = 742
# Modified r or s, e.g. by adding or subtracting the group order
msg = 54657374
result = invalid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021ca4f18ca08c5d97ce052c32252c9770ce81155a04b120aa783e6d47b9

# tcId = 743
# Modified r or s, e.g. by adding or subtracting the group order
msg = 54657374
result = invalid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021ca017dcb8ee2970521bebd37309f0c7ab8fb7f3c793f9d4c704bd4eea

# tcId = 744
# Modified r or s, e.g. by adding or subtracting the group order
msg = 54657374
result = invalid
sig = 303d021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021c5b0e735f73a26831fad3cddad3688f317eeaa5fb4edf5587c192b847

# tcId = 745
# Modified r or s, e.g. by adding or subtracting the group order
msg = 54657374
result = invalid
sig = 303e021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021dfee521461268b078723d03d90b40790025a0854193d914540647e7e58d

# tcId = 746
# Modified r or s, e.g. by adding or subtracting the group order
msg = 54657374
result = invalid
sig = 303e021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021d015fe8234711d68fade4142c8cf60f385470480c386c062b38fb42b116

# tcId = 747
# Modified r or s, e.g. by adding or subtracting the group order
msg = 54657374
result = invalid
sig = 303e021d009b6fe4a1cbd4467d7584ae382ae3130a580e61b969a6067373d5ee93021d00a017dcb8ee2970521bebd37309f0c7ab8fb7f3c793f9d4c704bd4eea

# tcId = 748
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022020100021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 749
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3006020100020100
flags = EdgeCase

# tcId = 750
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3006020100020101
flags = EdgeCase

# tcId = 751
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30060201000201ff
flags = EdgeCase

# tcId = 752
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3021020100021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 753
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3021020100021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 754
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 755
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 756
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 757
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022020100021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 758
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3082010802010002820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 759
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3008020100090380fe01
flags = EdgeCase

# tcId = 760
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3006020100090142
flags = EdgeCase

# tcId = 761
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022020101021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 762
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3006020101020100
flags = EdgeCase

# tcId = 763
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3006020101020101
flags = EdgeCase

# tcId = 764
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30060201010201ff
flags = EdgeCase

# tcId = 765
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3021020101021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 766
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3021020101021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 767
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 768
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 769
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 770
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022020101021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 771
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3082010802010102820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 772
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3008020101090380fe01
flags = EdgeCase

# tcId = 773
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3006020101090142
flags = EdgeCase

# tcId = 774
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30220201ff021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 775
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30060201ff020100
flags = EdgeCase

# tcId = 776
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30060201ff020101
flags = EdgeCase

# tcId = 777
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30060201ff0201ff
flags = EdgeCase

# tcId = 778
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30210201ff021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 779
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30210201ff021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 780
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 781
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 782
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 783
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30220201ff021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 784
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 308201080201ff02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 785
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30080201ff090380fe01
flags = EdgeCase

# tcId = 786
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30060201ff090142
flags = EdgeCase

# tcId = 787
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 788
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae020100
flags = EdgeCase

# tcId = 789
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae020101
flags = EdgeCase

# tcId = 790
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae0201ff
flags = EdgeCase

# tcId = 791
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 792
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 793
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 794
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 795
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 796
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 797
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30820123021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 798
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3023021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae090380fe01
flags = EdgeCase

# tcId = 799
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae090142
flags = EdgeCase

# tcId = 800
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 801
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af020100
flags = EdgeCase

# tcId = 802
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af020101
flags = EdgeCase

# tcId = 803
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af0201ff
flags = EdgeCase

# tcId = 804
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 805
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 806
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 807
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 808
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 809
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 810
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30820123021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 811
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3023021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af090380fe01
flags = EdgeCase

# tcId = 812
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af090142
flags = EdgeCase

# tcId = 813
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 814
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c020100
flags = EdgeCase

# tcId = 815
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c020101
flags = EdgeCase

# tcId = 816
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c0201ff
flags = EdgeCase

# tcId = 817
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 818
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 819
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 820
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 821
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 822
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 823
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 824
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c090380fe01
flags = EdgeCase

# tcId = 825
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c090142
flags = EdgeCase

# tcId = 826
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 827
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d020100
flags = EdgeCase

# tcId = 828
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d020101
flags = EdgeCase

# tcId = 829
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0201ff
flags = EdgeCase

# tcId = 830
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 831
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 832
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 833
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 834
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 835
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 836
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 837
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d090380fe01
flags = EdgeCase

# tcId = 838
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d090142
flags = EdgeCase

# tcId = 839
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 840
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e020100
flags = EdgeCase

# tcId = 841
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e020101
flags = EdgeCase

# tcId = 842
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e0201ff
flags = EdgeCase

# tcId = 843
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 844
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 845
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 846
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 847
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 848
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 849
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 850
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e090380fe01
flags = EdgeCase

# tcId = 851
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e090142
flags = EdgeCase

# tcId = 852
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d0100000000000000000000000000000000000000000000000000000000021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 853
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022021d0100000000000000000000000000000000000000000000000000000000020100
flags = EdgeCase

# tcId = 854
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022021d0100000000000000000000000000000000000000000000000000000000020101
flags = EdgeCase

# tcId = 855
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022021d01000000000000000000000000000000000000000000000000000000000201ff
flags = EdgeCase

# tcId = 856
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021d0100000000000000000000000000000000000000000000000000000000021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 857
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303d021d0100000000000000000000000000000000000000000000000000000000021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 858
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 859
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 860
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 861
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 303e021d0100000000000000000000000000000000000000000000000000000000021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 862
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 30820124021d010000000000000000000000000000000000000000000000000000000002820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 863
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3024021d0100000000000000000000000000000000000000000000000000000000090380fe01
flags = EdgeCase

# tcId = 864
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3022021d0100000000000000000000000000000000000000000000000000000000090142
flags = EdgeCase

# tcId = 865
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 866
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667020100
flags = EdgeCase

# tcId = 867
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667020101
flags = EdgeCase

# tcId = 868
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd6670201ff
flags = EdgeCase

# tcId = 869
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3082012302820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 870
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3082012302820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 871
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 872
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 873
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 874
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 875
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3082020a02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd66702820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 876
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3082010a02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667090380fe01
flags = EdgeCase

# tcId = 877
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667090142
flags = EdgeCase

# tcId = 878
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3024090380fe01021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3
flags = EdgeCase

# tcId = 879
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3008090380fe01020100
flags = EdgeCase

# tcId = 880
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3008090380fe01020101
flags = EdgeCase

# tcId = 881
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3008090380fe010201ff
flags = EdgeCase

# tcId = 882
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3023090380fe01021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae
flags = EdgeCase

# tcId = 883
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3023090380fe01021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af
flags = EdgeCase

# tcId = 884
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c
flags = EdgeCase

# tcId = 885
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d
flags = EdgeCase

# tcId = 886
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e
flags = EdgeCase

# tcId = 887
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3024090380fe01021d0100000000000000000000000000000000000000000000000000000000
flags = EdgeCase

# tcId = 888
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3082010a090380fe0102820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667
flags = EdgeCase

# tcId = 889
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 300a090380fe01090380fe01
flags = EdgeCase

# tcId = 890
# Signatures with special case values for r and s.
msg = 54657374
result = invalid
sig = 3008090380fe01090142
flags = EdgeCase

# tcId = 891
# Signature encoding contains wrong type.
msg = 54657374
result = invalid
sig = 30060201010c0130

# tcId = 892
# Signature encoding contains wrong type.
msg = 54657374
result = invalid
sig = 30050201010c00

# tcId = 893
# Signature encoding contains wrong type.
msg = 54657374
result = invalid
sig = 30090c0225730c03732573

# tcId = 894
# Signature encoding contains wrong type.
msg = 54657374
result = invalid
sig = 30080201013003020100

# tcId = 895
# Signature encoding contains wrong type.
msg = 54657374
result = invalid
sig = 3003020101

# tcId = 896
# Signature encoding contains wrong type.
msg = 54657374
result = invalid
sig = 3006020101010100

# tcId = 897
# random signature
msg = 54657374
result = valid
sig = 303c021c296410b8cb6200edafd1205e7377a09ad2011ac7b15b8bc9b9b4c6db021c25ca283c868dc2a5ce86aafcf681ce21d660b461da48270f15b53889

# tcId = 898
# random signature
msg = 54657374
result = valid
sig = 303d021c347c4f6875bf4476afbdd6b2b1f9e35c870e785e708e661109bd068e021d00b0b908a617d3ad6c8bc277f397095c00e659c86ca7c600090571ab17

# tcId = 899
# random signature
msg = 54657374
result = valid
sig = 303c021c3c76bc6f17369414d4c21c5361ed0cca6e79f73f90706f1f7ca9f05a021c3cc60d8a0d44fb967baa0e5621e12cd434aafd748cba3e7cdc733b2f

# tcId = 900
# random signature
msg = 54657374
result = valid
sig = 303e021d0086a5efea8e6a8033b8a0034b52ae614e1f14fbcbfa0bb50194efa6a7021d00b3d66f6d2b10cfe62fe96b78fcf41ca7b442aceb98ab109a01409e4a

# tcId = 901
# random signature
msg = 54657374
result = valid
sig = 303d021c16727d52bd711e9a63e0dd2c4db045cfb993942b1e39e4f43a65c11a021d009fb9c02d10c968e75bb15acab8467f30b84481f679e136e8af65a266

[key.g = 16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde]
[key.keySize = 2048]
[key.p = 008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667]
[key.q = 00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d]
[key.type = DsaPublicKey]
[key.y = 00848177b9bcff136c52caef2a4a9bcb64dbefbac69e18aae499696b5ec7b270e90478b413bb8ad8f8eee8ad32107d7ba492c36b007f9ef30ebe1ee484d0ea7cb0ff4afaa8c705ad5e16576975414f1bc0efed25c2190a3ed0068bffa1f03bf6f21056c9bb383350851997cbc89cf8729b394527f08ab93ce9b360aa055a47177e82a4ce6fe76c8dffddbd6ee20fa08d0085d3983edd2c8d9a366ad2245b4ed28d6754769f5f3a798be4be19cf469399865d464e3f640438bce03c962c2344d0d550542aed3db55c153833bea44b4146878ba347c8614436c6aac4fd1a60f25c62b3f869a7d55cab4b7122d5e9af4322a3fc8214fa55dc1ee021459fb2c4595827]
[keyDer = 308203433082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde03820106000282010100848177b9bcff136c52caef2a4a9bcb64dbefbac69e18aae499696b5ec7b270e90478b413bb8ad8f8eee8ad32107d7ba492c36b007f9ef30ebe1ee484d0ea7cb0ff4afaa8c705ad5e16576975414f1bc0efed25c2190a3ed0068bffa1f03bf6f21056c9bb383350851997cbc89cf8729b394527f08ab93ce9b360aa055a47177e82a4ce6fe76c8dffddbd6ee20fa08d0085d3983edd2c8d9a366ad2245b4ed28d6754769f5f3a798be4be19cf469399865d464e3f640438bce03c962c2344d0d550542aed3db55c153833bea44b4146878ba347c8614436c6aac4fd1a60f25c62b3f869a7d55cab4b7122d5e9af4322a3fc8214fa55dc1ee021459fb2c4595827]
[sha = SHA-256]

# tcId = 902
# r,s = 1,1
msg = 54657374
result = valid
sig = 3006020101020101

# tcId = 903
# r,s = 1,5
msg = 54657374
result = valid
sig = 3006020101020105

# tcId = 904
# u2 small
msg = 54657374
result = valid
sig = 3022020101021d009592121ed12d93197f1ffb863ac63937f28ef4f62f1e009a30aabab1

# tcId = 905
# s == q-1
msg = 54657374
result = valid
sig = 3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c

[key.g = 16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde]
[key.keySize = 2048]
[key.p = 008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667]
[key.q = 00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d]
[key.type = DsaPublicKey]
[key.y = 629374929537e2c3b09f30d881554ca7357f89e25105474dbbce06e4001efd61481a457aa0d7d7e565e90b7a3d9c688005fb404bf3b6d3e61e402300beee7c58ceeaf00b112ddfeef3cbc2020ba2206dd4ef0563d7fa52c321b4ee6280eb8585041d03cadb9244dff21dc90417bbe6f06b91c2ca6484437c3846926b18ee22275081b60726e7a26a29a947eabd035ede83d65927b3ceb0d4d8c2f34e94a3de0f57e4ea99af059657529f6954b1ac9bb4484ca76b4083e1cf4264eff028662137761e4d7f35b1eda3cf516856f25553840e43ae38379d234b06c891822132081d19f0d5db9f23b4bbd5f5667dd78f3dd7f1fe5f25ca48515f6335ce1c9fd0a64b]
[keyDer = 308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde038201050002820100629374929537e2c3b09f30d881554ca7357f89e25105474dbbce06e4001efd61481a457aa0d7d7e565e90b7a3d9c688005fb404bf3b6d3e61e402300beee7c58ceeaf00b112ddfeef3cbc2020ba2206dd4ef0563d7fa52c321b4ee6280eb8585041d03cadb9244dff21dc90417bbe6f06b91c2ca6484437c3846926b18ee22275081b60726e7a26a29a947eabd035ede83d65927b3ceb0d4d8c2f34e94a3de0f57e4ea99af059657529f6954b1ac9bb4484ca76b4083e1cf4264eff028662137761e4d7f35b1eda3cf516856f25553840e43ae38379d234b06c891822132081d19f0d5db9f23b4bbd5f5667dd78f3dd7f1fe5f25ca48515f6335ce1c9fd0a64b]
[sha = SHA-256]

# tcId = 906
# s == 1
msg = 54657374
result = valid
sig = 3021021c5a252f4fc55618747fd94b13c9bee62bb958d85777cb07dd90710d24020101

