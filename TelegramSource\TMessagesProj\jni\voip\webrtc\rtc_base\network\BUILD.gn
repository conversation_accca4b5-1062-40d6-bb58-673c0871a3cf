# Copyright (c) 2018 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../webrtc.gni")

rtc_library("sent_packet") {
  sources = [
    "sent_packet.cc",
    "sent_packet.h",
  ]
  deps = [ "../system:rtc_export" ]
  absl_deps = [ "//third_party/abseil-cpp/absl/types:optional" ]
}

rtc_library("received_packet") {
  visibility = [ "*" ]
  sources = [
    "received_packet.cc",
    "received_packet.h",
  ]
  deps = [
    "..:socket_address",
    "../../api:array_view",
    "../../api/units:timestamp",
    "../system:rtc_export",
  ]
  absl_deps = [
    "//third_party/abseil-cpp/absl/functional:any_invocable",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}
