// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     java/lang/Long

#ifndef java_lang_Long_JNI
#define java_lang_Long_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_java_lang_Long[];
const char kClassPath_java_lang_Long[] = "java/lang/Long";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_java_lang_Long_clazz(nullptr);
#ifndef java_lang_Long_clazz_defined
#define java_lang_Long_clazz_defined
inline jclass java_lang_Long_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_java_lang_Long, &g_java_lang_Long_clazz);
}
#endif


// Step 2: Constants (optional).

namespace JNI_Long {

enum Java_Long_constant_fields {
  BYTES = 8,
  SIZE = 64,
};


}  // namespace JNI_Long
// Step 3: Method stubs.
namespace JNI_Long {


static std::atomic<jmethodID> g_java_lang_Long_Constructor__String1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Long_Constructor__String(JNIEnv*
    env, const jni_zero::JavaRef<jstring>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Long_Constructor__String(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Ljava/lang/String;)V",
          &g_java_lang_Long_Constructor__String1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Long_Constructor__long1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Long_Constructor__long(JNIEnv*
    env, jlong p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Long_Constructor__long(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(J)V",
          &g_java_lang_Long_Constructor__long1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, p0);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Long_bitCount1(nullptr);
[[maybe_unused]] static jint Java_Long_bitCount(JNIEnv* env, jlong p0);
static jint Java_Long_bitCount(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "bitCount",
          "(J)I",
          &g_java_lang_Long_bitCount1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_byteValue0(nullptr);
[[maybe_unused]] static jbyte Java_Long_byteValue(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jbyte Java_Long_byteValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "byteValue",
          "()B",
          &g_java_lang_Long_byteValue0);

  jbyte ret =
      env->CallByteMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_compare2(nullptr);
[[maybe_unused]] static jint Java_Long_compare(JNIEnv* env, jlong p0,
    jlong p1);
static jint Java_Long_compare(JNIEnv* env, jlong p0,
    jlong p1) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "compare",
          "(JJ)I",
          &g_java_lang_Long_compare2);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0, p1);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_compareTo__Long1(nullptr);
[[maybe_unused]] static jint Java_Long_compareTo__Long(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jint Java_Long_compareTo__Long(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "compareTo",
          "(Ljava/lang/Long;)I",
          &g_java_lang_Long_compareTo__Long1);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_compareTo__Object1(nullptr);
[[maybe_unused]] static jint Java_Long_compareTo__Object(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jint Java_Long_compareTo__Object(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "compareTo",
          "(Ljava/lang/Object;)I",
          &g_java_lang_Long_compareTo__Object1);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_compareUnsigned2(nullptr);
[[maybe_unused]] static jint Java_Long_compareUnsigned(JNIEnv* env, jlong p0,
    jlong p1);
static jint Java_Long_compareUnsigned(JNIEnv* env, jlong p0,
    jlong p1) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "compareUnsigned",
          "(JJ)I",
          &g_java_lang_Long_compareUnsigned2);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0, p1);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_decode1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Long_decode(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Long_decode(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "decode",
          "(Ljava/lang/String;)Ljava/lang/Long;",
          &g_java_lang_Long_decode1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Long_divideUnsigned2(nullptr);
[[maybe_unused]] static jlong Java_Long_divideUnsigned(JNIEnv* env, jlong p0,
    jlong p1);
static jlong Java_Long_divideUnsigned(JNIEnv* env, jlong p0,
    jlong p1) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "divideUnsigned",
          "(JJ)J",
          &g_java_lang_Long_divideUnsigned2);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0, p1);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_doubleValue0(nullptr);
[[maybe_unused]] static jdouble Java_Long_doubleValue(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jdouble Java_Long_doubleValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "doubleValue",
          "()D",
          &g_java_lang_Long_doubleValue0);

  jdouble ret =
      env->CallDoubleMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_equals1(nullptr);
[[maybe_unused]] static jboolean Java_Long_equals(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_Long_equals(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Long_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "equals",
          "(Ljava/lang/Object;)Z",
          &g_java_lang_Long_equals1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_floatValue0(nullptr);
[[maybe_unused]] static jfloat Java_Long_floatValue(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jfloat Java_Long_floatValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "floatValue",
          "()F",
          &g_java_lang_Long_floatValue0);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_getLong1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Long_getLong(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Long_getLong(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getLong",
          "(Ljava/lang/String;)Ljava/lang/Long;",
          &g_java_lang_Long_getLong1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Long_getLong__String__Long2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_Long_getLong__String__Long(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0,
    const jni_zero::JavaRef<jobject>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Long_getLong__String__Long(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getLong",
          "(Ljava/lang/String;Ljava/lang/Long;)Ljava/lang/Long;",
          &g_java_lang_Long_getLong__String__Long2);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Long_getLong__String__long2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_Long_getLong__String__long(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0,
    jlong p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Long_getLong__String__long(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0,
    jlong p1) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getLong",
          "(Ljava/lang/String;J)Ljava/lang/Long;",
          &g_java_lang_Long_getLong__String__long2);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), p1);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Long_hashCode0(nullptr);
[[maybe_unused]] static jint Java_Long_hashCode(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj);
static jint Java_Long_hashCode(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "hashCode",
          "()I",
          &g_java_lang_Long_hashCode0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_hashCode1(nullptr);
[[maybe_unused]] static jint Java_Long_hashCode(JNIEnv* env, jlong p0);
static jint Java_Long_hashCode(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "hashCode",
          "(J)I",
          &g_java_lang_Long_hashCode1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_highestOneBit1(nullptr);
[[maybe_unused]] static jlong Java_Long_highestOneBit(JNIEnv* env, jlong p0);
static jlong Java_Long_highestOneBit(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "highestOneBit",
          "(J)J",
          &g_java_lang_Long_highestOneBit1);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_intValue0(nullptr);
[[maybe_unused]] static jint Java_Long_intValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj);
static jint Java_Long_intValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "intValue",
          "()I",
          &g_java_lang_Long_intValue0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_longValue0(nullptr);
[[maybe_unused]] static jlong Java_Long_longValue(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jlong Java_Long_longValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "longValue",
          "()J",
          &g_java_lang_Long_longValue0);

  jlong ret =
      env->CallLongMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_lowestOneBit1(nullptr);
[[maybe_unused]] static jlong Java_Long_lowestOneBit(JNIEnv* env, jlong p0);
static jlong Java_Long_lowestOneBit(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "lowestOneBit",
          "(J)J",
          &g_java_lang_Long_lowestOneBit1);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_max2(nullptr);
[[maybe_unused]] static jlong Java_Long_max(JNIEnv* env, jlong p0,
    jlong p1);
static jlong Java_Long_max(JNIEnv* env, jlong p0,
    jlong p1) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "max",
          "(JJ)J",
          &g_java_lang_Long_max2);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0, p1);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_min2(nullptr);
[[maybe_unused]] static jlong Java_Long_min(JNIEnv* env, jlong p0,
    jlong p1);
static jlong Java_Long_min(JNIEnv* env, jlong p0,
    jlong p1) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "min",
          "(JJ)J",
          &g_java_lang_Long_min2);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0, p1);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_numberOfLeadingZeros1(nullptr);
[[maybe_unused]] static jint Java_Long_numberOfLeadingZeros(JNIEnv* env, jlong p0);
static jint Java_Long_numberOfLeadingZeros(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "numberOfLeadingZeros",
          "(J)I",
          &g_java_lang_Long_numberOfLeadingZeros1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_numberOfTrailingZeros1(nullptr);
[[maybe_unused]] static jint Java_Long_numberOfTrailingZeros(JNIEnv* env, jlong p0);
static jint Java_Long_numberOfTrailingZeros(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "numberOfTrailingZeros",
          "(J)I",
          &g_java_lang_Long_numberOfTrailingZeros1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_parseLong4(nullptr);
[[maybe_unused]] static jlong Java_Long_parseLong(JNIEnv* env, const jni_zero::JavaRef<jobject>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    JniIntWrapper p3);
static jlong Java_Long_parseLong(JNIEnv* env, const jni_zero::JavaRef<jobject>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    JniIntWrapper p3) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "parseLong",
          "(Ljava/lang/CharSequence;III)J",
          &g_java_lang_Long_parseLong4);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0.obj(), as_jint(p1), as_jint(p2), as_jint(p3));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_parseLong1(nullptr);
[[maybe_unused]] static jlong Java_Long_parseLong(JNIEnv* env, const jni_zero::JavaRef<jstring>&
    p0);
static jlong Java_Long_parseLong(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "parseLong",
          "(Ljava/lang/String;)J",
          &g_java_lang_Long_parseLong1);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_parseLong2(nullptr);
[[maybe_unused]] static jlong Java_Long_parseLong(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1);
static jlong Java_Long_parseLong(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "parseLong",
          "(Ljava/lang/String;I)J",
          &g_java_lang_Long_parseLong2);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0.obj(), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_parseUnsignedLong4(nullptr);
[[maybe_unused]] static jlong Java_Long_parseUnsignedLong(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    JniIntWrapper p3);
static jlong Java_Long_parseUnsignedLong(JNIEnv* env, const jni_zero::JavaRef<jobject>& p0,
    JniIntWrapper p1,
    JniIntWrapper p2,
    JniIntWrapper p3) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "parseUnsignedLong",
          "(Ljava/lang/CharSequence;III)J",
          &g_java_lang_Long_parseUnsignedLong4);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0.obj(), as_jint(p1), as_jint(p2), as_jint(p3));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_parseUnsignedLong1(nullptr);
[[maybe_unused]] static jlong Java_Long_parseUnsignedLong(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0);
static jlong Java_Long_parseUnsignedLong(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "parseUnsignedLong",
          "(Ljava/lang/String;)J",
          &g_java_lang_Long_parseUnsignedLong1);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_parseUnsignedLong2(nullptr);
[[maybe_unused]] static jlong Java_Long_parseUnsignedLong(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1);
static jlong Java_Long_parseUnsignedLong(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "parseUnsignedLong",
          "(Ljava/lang/String;I)J",
          &g_java_lang_Long_parseUnsignedLong2);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0.obj(), as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_remainderUnsigned2(nullptr);
[[maybe_unused]] static jlong Java_Long_remainderUnsigned(JNIEnv* env, jlong p0,
    jlong p1);
static jlong Java_Long_remainderUnsigned(JNIEnv* env, jlong p0,
    jlong p1) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "remainderUnsigned",
          "(JJ)J",
          &g_java_lang_Long_remainderUnsigned2);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0, p1);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_reverse1(nullptr);
[[maybe_unused]] static jlong Java_Long_reverse(JNIEnv* env, jlong p0);
static jlong Java_Long_reverse(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "reverse",
          "(J)J",
          &g_java_lang_Long_reverse1);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_reverseBytes1(nullptr);
[[maybe_unused]] static jlong Java_Long_reverseBytes(JNIEnv* env, jlong p0);
static jlong Java_Long_reverseBytes(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "reverseBytes",
          "(J)J",
          &g_java_lang_Long_reverseBytes1);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_rotateLeft2(nullptr);
[[maybe_unused]] static jlong Java_Long_rotateLeft(JNIEnv* env, jlong p0,
    JniIntWrapper p1);
static jlong Java_Long_rotateLeft(JNIEnv* env, jlong p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "rotateLeft",
          "(JI)J",
          &g_java_lang_Long_rotateLeft2);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0, as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_rotateRight2(nullptr);
[[maybe_unused]] static jlong Java_Long_rotateRight(JNIEnv* env, jlong p0,
    JniIntWrapper p1);
static jlong Java_Long_rotateRight(JNIEnv* env, jlong p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "rotateRight",
          "(JI)J",
          &g_java_lang_Long_rotateRight2);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0, as_jint(p1));
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_shortValue0(nullptr);
[[maybe_unused]] static jshort Java_Long_shortValue(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jshort Java_Long_shortValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "shortValue",
          "()S",
          &g_java_lang_Long_shortValue0);

  jshort ret =
      env->CallShortMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_signum1(nullptr);
[[maybe_unused]] static jint Java_Long_signum(JNIEnv* env, jlong p0);
static jint Java_Long_signum(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "signum",
          "(J)I",
          &g_java_lang_Long_signum1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_sum2(nullptr);
[[maybe_unused]] static jlong Java_Long_sum(JNIEnv* env, jlong p0,
    jlong p1);
static jlong Java_Long_sum(JNIEnv* env, jlong p0,
    jlong p1) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "sum",
          "(JJ)J",
          &g_java_lang_Long_sum2);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0, p1);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Long_toBinaryString1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Long_toBinaryString(JNIEnv* env,
    jlong p0);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Long_toBinaryString(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toBinaryString",
          "(J)Ljava/lang/String;",
          &g_java_lang_Long_toBinaryString1);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Long_toHexString1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Long_toHexString(JNIEnv* env,
    jlong p0);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Long_toHexString(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toHexString",
          "(J)Ljava/lang/String;",
          &g_java_lang_Long_toHexString1);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Long_toOctalString1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Long_toOctalString(JNIEnv* env,
    jlong p0);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Long_toOctalString(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toOctalString",
          "(J)Ljava/lang/String;",
          &g_java_lang_Long_toOctalString1);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Long_toString0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Long_toString(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Long_toString(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "toString",
          "()Ljava/lang/String;",
          &g_java_lang_Long_toString0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Long_toString1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Long_toString(JNIEnv* env, jlong
    p0);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Long_toString(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toString",
          "(J)Ljava/lang/String;",
          &g_java_lang_Long_toString1);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Long_toString2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Long_toString(JNIEnv* env, jlong
    p0,
    JniIntWrapper p1);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Long_toString(JNIEnv* env, jlong p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toString",
          "(JI)Ljava/lang/String;",
          &g_java_lang_Long_toString2);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0, as_jint(p1)));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Long_toUnsignedString1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Long_toUnsignedString(JNIEnv*
    env, jlong p0);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Long_toUnsignedString(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toUnsignedString",
          "(J)Ljava/lang/String;",
          &g_java_lang_Long_toUnsignedString1);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Long_toUnsignedString2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Long_toUnsignedString(JNIEnv*
    env, jlong p0,
    JniIntWrapper p1);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Long_toUnsignedString(JNIEnv* env, jlong p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toUnsignedString",
          "(JI)Ljava/lang/String;",
          &g_java_lang_Long_toUnsignedString2);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0, as_jint(p1)));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Long_valueOf__long1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Long_valueOf__long(JNIEnv* env,
    jlong p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Long_valueOf__long(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "valueOf",
          "(J)Ljava/lang/Long;",
          &g_java_lang_Long_valueOf__long1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Long_valueOf__String1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Long_valueOf__String(JNIEnv* env,
    const jni_zero::JavaRef<jstring>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Long_valueOf__String(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "valueOf",
          "(Ljava/lang/String;)Ljava/lang/Long;",
          &g_java_lang_Long_valueOf__String1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Long_valueOf2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Long_valueOf(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Long_valueOf(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0,
    JniIntWrapper p1) {
  jclass clazz = java_lang_Long_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Long_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "valueOf",
          "(Ljava/lang/String;I)Ljava/lang/Long;",
          &g_java_lang_Long_valueOf2);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj(), as_jint(p1));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace JNI_Long

#endif  // java_lang_Long_JNI
