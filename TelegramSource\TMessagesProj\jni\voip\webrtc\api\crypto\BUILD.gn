# Copyright (c) 2019 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../webrtc.gni")

group("crypto") {
  deps = [
    ":frame_decryptor_interface",
    ":frame_encryptor_interface",
    ":options",
  ]
}

rtc_library("options") {
  visibility = [ "*" ]
  sources = [
    "crypto_options.cc",
    "crypto_options.h",
  ]
  deps = [
    "../../rtc_base:ssl",
    "../../rtc_base/system:rtc_export",
  ]
}

rtc_source_set("frame_decryptor_interface") {
  visibility = [ "*" ]
  sources = [ "frame_decryptor_interface.h" ]
  deps = [
    "..:array_view",
    "..:rtp_parameters",
    "../../rtc_base:refcount",
  ]
}

rtc_source_set("frame_encryptor_interface") {
  visibility = [ "*" ]
  sources = [ "frame_encryptor_interface.h" ]
  deps = [
    "..:array_view",
    "..:rtp_parameters",
    "../../rtc_base:refcount",
  ]
}
