// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/YuvHelper

#ifndef org_webrtc_YuvHelper_JNI
#define org_webrtc_YuvHelper_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_YuvHelper[];
const char kClassPath_org_webrtc_YuvHelper[] = "org/webrtc/YuvHelper";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_YuvHelper_clazz(nullptr);
#ifndef org_webrtc_YuvHelper_clazz_defined
#define org_webrtc_YuvHelper_clazz_defined
inline jclass org_webrtc_YuvHelper_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_YuvHelper,
      &g_org_webrtc_YuvHelper_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {

static void JNI_YuvHelper_ABGRToI420(JNIEnv* env, const jni_zero::JavaParamRef<jobject>& src,
    jint srcStride,
    const jni_zero::JavaParamRef<jobject>& dstY,
    jint dstStrideY,
    const jni_zero::JavaParamRef<jobject>& dstU,
    jint dstStrideU,
    const jni_zero::JavaParamRef<jobject>& dstV,
    jint dstStrideV,
    jint width,
    jint height);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_YuvHelper_nativeABGRToI420(
    JNIEnv* env,
    jclass jcaller,
    jobject src,
    jint srcStride,
    jobject dstY,
    jint dstStrideY,
    jobject dstU,
    jint dstStrideU,
    jobject dstV,
    jint dstStrideV,
    jint width,
    jint height) {
  return JNI_YuvHelper_ABGRToI420(env, jni_zero::JavaParamRef<jobject>(env, src), srcStride,
      jni_zero::JavaParamRef<jobject>(env, dstY), dstStrideY, jni_zero::JavaParamRef<jobject>(env,
      dstU), dstStrideU, jni_zero::JavaParamRef<jobject>(env, dstV), dstStrideV, width, height);
}

static void JNI_YuvHelper_CopyPlane(JNIEnv* env, const jni_zero::JavaParamRef<jobject>& src,
    jint srcStride,
    const jni_zero::JavaParamRef<jobject>& dst,
    jint dstStride,
    jint width,
    jint height);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_YuvHelper_nativeCopyPlane(
    JNIEnv* env,
    jclass jcaller,
    jobject src,
    jint srcStride,
    jobject dst,
    jint dstStride,
    jint width,
    jint height) {
  return JNI_YuvHelper_CopyPlane(env, jni_zero::JavaParamRef<jobject>(env, src), srcStride,
      jni_zero::JavaParamRef<jobject>(env, dst), dstStride, width, height);
}

static void JNI_YuvHelper_I420Copy(JNIEnv* env, const jni_zero::JavaParamRef<jobject>& srcY,
    jint srcStrideY,
    const jni_zero::JavaParamRef<jobject>& srcU,
    jint srcStrideU,
    const jni_zero::JavaParamRef<jobject>& srcV,
    jint srcStrideV,
    const jni_zero::JavaParamRef<jobject>& dstY,
    jint dstStrideY,
    const jni_zero::JavaParamRef<jobject>& dstU,
    jint dstStrideU,
    const jni_zero::JavaParamRef<jobject>& dstV,
    jint dstStrideV,
    jint width,
    jint height);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_YuvHelper_nativeI420Copy(
    JNIEnv* env,
    jclass jcaller,
    jobject srcY,
    jint srcStrideY,
    jobject srcU,
    jint srcStrideU,
    jobject srcV,
    jint srcStrideV,
    jobject dstY,
    jint dstStrideY,
    jobject dstU,
    jint dstStrideU,
    jobject dstV,
    jint dstStrideV,
    jint width,
    jint height) {
  return JNI_YuvHelper_I420Copy(env, jni_zero::JavaParamRef<jobject>(env, srcY), srcStrideY,
      jni_zero::JavaParamRef<jobject>(env, srcU), srcStrideU, jni_zero::JavaParamRef<jobject>(env,
      srcV), srcStrideV, jni_zero::JavaParamRef<jobject>(env, dstY), dstStrideY,
      jni_zero::JavaParamRef<jobject>(env, dstU), dstStrideU, jni_zero::JavaParamRef<jobject>(env,
      dstV), dstStrideV, width, height);
}

static void JNI_YuvHelper_I420Rotate(JNIEnv* env, const jni_zero::JavaParamRef<jobject>& srcY,
    jint srcStrideY,
    const jni_zero::JavaParamRef<jobject>& srcU,
    jint srcStrideU,
    const jni_zero::JavaParamRef<jobject>& srcV,
    jint srcStrideV,
    const jni_zero::JavaParamRef<jobject>& dstY,
    jint dstStrideY,
    const jni_zero::JavaParamRef<jobject>& dstU,
    jint dstStrideU,
    const jni_zero::JavaParamRef<jobject>& dstV,
    jint dstStrideV,
    jint srcWidth,
    jint srcHeight,
    jint rotationMode);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_YuvHelper_nativeI420Rotate(
    JNIEnv* env,
    jclass jcaller,
    jobject srcY,
    jint srcStrideY,
    jobject srcU,
    jint srcStrideU,
    jobject srcV,
    jint srcStrideV,
    jobject dstY,
    jint dstStrideY,
    jobject dstU,
    jint dstStrideU,
    jobject dstV,
    jint dstStrideV,
    jint srcWidth,
    jint srcHeight,
    jint rotationMode) {
  return JNI_YuvHelper_I420Rotate(env, jni_zero::JavaParamRef<jobject>(env, srcY), srcStrideY,
      jni_zero::JavaParamRef<jobject>(env, srcU), srcStrideU, jni_zero::JavaParamRef<jobject>(env,
      srcV), srcStrideV, jni_zero::JavaParamRef<jobject>(env, dstY), dstStrideY,
      jni_zero::JavaParamRef<jobject>(env, dstU), dstStrideU, jni_zero::JavaParamRef<jobject>(env,
      dstV), dstStrideV, srcWidth, srcHeight, rotationMode);
}

static void JNI_YuvHelper_I420ToNV12(JNIEnv* env, const jni_zero::JavaParamRef<jobject>& srcY,
    jint srcStrideY,
    const jni_zero::JavaParamRef<jobject>& srcU,
    jint srcStrideU,
    const jni_zero::JavaParamRef<jobject>& srcV,
    jint srcStrideV,
    const jni_zero::JavaParamRef<jobject>& dstY,
    jint dstStrideY,
    const jni_zero::JavaParamRef<jobject>& dstUV,
    jint dstStrideUV,
    jint width,
    jint height);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_YuvHelper_nativeI420ToNV12(
    JNIEnv* env,
    jclass jcaller,
    jobject srcY,
    jint srcStrideY,
    jobject srcU,
    jint srcStrideU,
    jobject srcV,
    jint srcStrideV,
    jobject dstY,
    jint dstStrideY,
    jobject dstUV,
    jint dstStrideUV,
    jint width,
    jint height) {
  return JNI_YuvHelper_I420ToNV12(env, jni_zero::JavaParamRef<jobject>(env, srcY), srcStrideY,
      jni_zero::JavaParamRef<jobject>(env, srcU), srcStrideU, jni_zero::JavaParamRef<jobject>(env,
      srcV), srcStrideV, jni_zero::JavaParamRef<jobject>(env, dstY), dstStrideY,
      jni_zero::JavaParamRef<jobject>(env, dstUV), dstStrideUV, width, height);
}


}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_YuvHelper_JNI
