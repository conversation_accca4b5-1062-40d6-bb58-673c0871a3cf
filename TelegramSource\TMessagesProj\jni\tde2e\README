git clone https://github.com/tdlib/td
cd td/example/android
# copy build-tdlib.sh and CMakeLists.txt into this folder
./fetch-sdk.sh
# copy boringssl from TMessagesProj/jni/boringssl into td/example/android/third-party/openssl with such structure:
# third-party/
#   openssl/
#     $ARCH/
#       include/ <- TMessagesProj/jni/boringssl/include
#       lib/ <- TMessagesProj/jni/boringssl/lib/$ARCH
./build-tdlib.sh
# gather libtde2e.a and libtdutils.a into TMessagesProj/jni/tde2e/$ARCH