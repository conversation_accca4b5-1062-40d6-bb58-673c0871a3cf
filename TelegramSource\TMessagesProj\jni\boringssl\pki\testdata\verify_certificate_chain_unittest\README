This directory contains test data for verifying certificate chains.

Tests are grouped into directories that contain the keys, python to generate
chains, and test expectations. "DIR" is used as a generic placeholder below to
identify such a directory.

===============================
DIR/generate-chains.py
===============================

Python script that generates one or more ".pem" file containing a sequence of
CERTIFICATE blocks. In most cases it will generate a single chain called
"chain.pem".

===============================
DIR/keys/*.key
===============================

The keys used (as well as generated) by the .py file generate-chains.py. The
private keys shouldn't be needed to run the tests, however are useful when
re-generating the test data to have stable results (at least for signature
types which are deterministic, like RSASSA PKCS#1 which is used by most of the
certificates data).

===============================
DIR/*.pem
===============================

A sequence of CERTIFICATE blocks that was created by the generate-chains.py
script. (Although in a few cases there are manually created .pem files that
lack a generator script).

===============================
DIR/*.test
===============================

A sequence of key-value pairs that identify the inputs to certificate
verification, as well as the expected outputs. The format is essentially a
newline separated sequence of key/value pairs:

key: value\n

All keys must be specified by tests, although they can be in any order.
The possible keys are:

  "chain" - The value is a file path (relative to the test file) to a .pem
      containing the CERTIFICATE chain.

  "last_cert_trust" - The value identifies the trustedness of the last
      certificate in the chain (i.e. whether it is a trust anchor or not). This
      maps to the CertificateTrustType enum. Possible values are:
          "TRUSTED_ANCHOR"
          "TRUSTED_ANCHOR_WITH_EXPIRATION"
          "TRUSTED_ANCHOR_WITH_CONSTRAINTS"
          "UNSPECIFIED"
          "DISTRUSTED"

  "utc_time" - A string encoding for the generalized time at which verification
      should be done. Example "150302120000Z"

  "key_purpose" - The expected EKU to use when verifying. Maps to
      KeyPurpose enum. Possible values are:
      "ANY_EKU"
      "SERVER_AUTH"
      "CLIENT_AUTH"

  "errors" - This has special parsing rules: it is interpreted as the
      final key in the file. All lines after "errors:\n" are read as being the
      error string (this allows embedding newlines in it).

Additionally, it is possible to add python-style comments by starting a line
with "#".

===============================
pkits_errors/*.txt
===============================

These files contain the expected errors for PKITS tests
(third_party/nist-pkits). The file name correspond so the PKITS tests number.
They are baselined specifically for VerifyCertificateChain().

===============================
generate-all.sh
===============================

Runs all of the generate-chains.py scripts and cleans up the temp files
afterwards.
