/*
 *  Copyright (c) 2018 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#include "modules/rtp_rtcp/source/fec_private_tables_random.h"

namespace {
// clang-format off
#define kMaskRandom1_1 \
  0x80, 0x00

#define kMaskRandom2_1 \
  0xc0, 0x00

#define kMaskRandom2_2 \
  0xc0, 0x00, \
  0x80, 0x00

#define kMaskRandom3_1 \
  0xe0, 0x00

#define kMaskRandom3_2 \
  0xc0, 0x00, \
  0xa0, 0x00

#define kMaskRandom3_3 \
  0xc0, 0x00, \
  0xa0, 0x00, \
  0x60, 0x00

#define kMaskRandom4_1 \
  0xf0, 0x00

#define kMaskRandom4_2 \
  0xc0, 0x00, \
  0xb0, 0x00

#define kMaskRandom4_3 \
  0xc0, 0x00, \
  0xb0, 0x00, \
  0x60, 0x00

#define kMaskRandom4_4 \
  0xc0, 0x00, \
  0xa0, 0x00, \
  0x30, 0x00, \
  0x50, 0x00

#define kMaskRandom5_1 \
  0xf8, 0x00

#define kMaskRandom5_2 \
  0xa8, 0x00, \
  0xd0, 0x00

#define kMaskRandom5_3 \
  0xb0, 0x00, \
  0xc8, 0x00, \
  0x50, 0x00

#define kMaskRandom5_4 \
  0xc8, 0x00, \
  0xb0, 0x00, \
  0x50, 0x00, \
  0x28, 0x00

#define kMaskRandom5_5 \
  0xc0, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0xa0, 0x00, \
  0x48, 0x00

#define kMaskRandom6_1 \
  0xfc, 0x00

#define kMaskRandom6_2 \
  0xa8, 0x00, \
  0xd4, 0x00

#define kMaskRandom6_3 \
  0xd0, 0x00, \
  0x68, 0x00, \
  0xa4, 0x00

#define kMaskRandom6_4 \
  0xa8, 0x00, \
  0x58, 0x00, \
  0x64, 0x00, \
  0x94, 0x00

#define kMaskRandom6_5 \
  0xa8, 0x00, \
  0x84, 0x00, \
  0x64, 0x00, \
  0x90, 0x00, \
  0x58, 0x00

#define kMaskRandom6_6 \
  0x98, 0x00, \
  0x64, 0x00, \
  0x50, 0x00, \
  0x14, 0x00, \
  0xa8, 0x00, \
  0xe0, 0x00

#define kMaskRandom7_1 \
  0xfe, 0x00

#define kMaskRandom7_2 \
  0xd4, 0x00, \
  0xaa, 0x00

#define kMaskRandom7_3 \
  0xd0, 0x00, \
  0xaa, 0x00, \
  0x64, 0x00

#define kMaskRandom7_4 \
  0xd0, 0x00, \
  0xaa, 0x00, \
  0x64, 0x00, \
  0x1c, 0x00

#define kMaskRandom7_5 \
  0x0c, 0x00, \
  0xb0, 0x00, \
  0x1a, 0x00, \
  0xc4, 0x00, \
  0x62, 0x00

#define kMaskRandom7_6 \
  0x8c, 0x00, \
  0x4a, 0x00, \
  0x64, 0x00, \
  0xd0, 0x00, \
  0xa0, 0x00, \
  0x32, 0x00

#define kMaskRandom7_7 \
  0x4a, 0x00, \
  0x94, 0x00, \
  0x1a, 0x00, \
  0xc4, 0x00, \
  0x28, 0x00, \
  0xc2, 0x00, \
  0x34, 0x00

#define kMaskRandom8_1 \
  0xff, 0x00

#define kMaskRandom8_2 \
  0xaa, 0x00, \
  0xd5, 0x00

#define kMaskRandom8_3 \
  0xc5, 0x00, \
  0x92, 0x00, \
  0x6a, 0x00

#define kMaskRandom8_4 \
  0x45, 0x00, \
  0xb4, 0x00, \
  0x6a, 0x00, \
  0x89, 0x00

#define kMaskRandom8_5 \
  0x8c, 0x00, \
  0x92, 0x00, \
  0x2b, 0x00, \
  0x51, 0x00, \
  0x64, 0x00

#define kMaskRandom8_6 \
  0xa1, 0x00, \
  0x52, 0x00, \
  0x91, 0x00, \
  0x2a, 0x00, \
  0xc4, 0x00, \
  0x4c, 0x00

#define kMaskRandom8_7 \
  0x15, 0x00, \
  0xc2, 0x00, \
  0x25, 0x00, \
  0x62, 0x00, \
  0x58, 0x00, \
  0x8c, 0x00, \
  0xa3, 0x00

#define kMaskRandom8_8 \
  0x25, 0x00, \
  0x8a, 0x00, \
  0x91, 0x00, \
  0x68, 0x00, \
  0x32, 0x00, \
  0x43, 0x00, \
  0xc4, 0x00, \
  0x1c, 0x00

#define kMaskRandom9_1 \
  0xff, 0x80

#define kMaskRandom9_2 \
  0xaa, 0x80, \
  0xd5, 0x00

#define kMaskRandom9_3 \
  0xa5, 0x00, \
  0xc8, 0x00, \
  0x52, 0x80

#define kMaskRandom9_4 \
  0xa2, 0x00, \
  0xc9, 0x00, \
  0x52, 0x80, \
  0x24, 0x80

#define kMaskRandom9_5 \
  0x8c, 0x00, \
  0x25, 0x00, \
  0x92, 0x80, \
  0x41, 0x80, \
  0x58, 0x00

#define kMaskRandom9_6 \
  0x84, 0x80, \
  0x27, 0x00, \
  0x51, 0x80, \
  0x1a, 0x00, \
  0x68, 0x00, \
  0x89, 0x00

#define kMaskRandom9_7 \
  0x8c, 0x00, \
  0x47, 0x00, \
  0x81, 0x80, \
  0x12, 0x80, \
  0x58, 0x00, \
  0x28, 0x80, \
  0xb4, 0x00

#define kMaskRandom9_8 \
  0x2c, 0x00, \
  0x91, 0x00, \
  0x40, 0x80, \
  0x06, 0x80, \
  0xc8, 0x00, \
  0x45, 0x00, \
  0x30, 0x80, \
  0xa2, 0x00

#define kMaskRandom9_9 \
  0x4c, 0x00, \
  0x62, 0x00, \
  0x91, 0x00, \
  0x42, 0x80, \
  0xa4, 0x00, \
  0x13, 0x00, \
  0x30, 0x80, \
  0x88, 0x80, \
  0x09, 0x00

#define kMaskRandom10_1 \
  0xff, 0xc0

#define kMaskRandom10_10 \
  0x4c, 0x00, \
  0x51, 0x00, \
  0xa0, 0x40, \
  0x04, 0xc0, \
  0x03, 0x80, \
  0x86, 0x00, \
  0x29, 0x00, \
  0x42, 0x40, \
  0x98, 0x00, \
  0x30, 0x80

#define kMaskRandom10_2 \
  0xaa, 0x80, \
  0xd5, 0x40

#define kMaskRandom10_3 \
  0xa4, 0x40, \
  0xc9, 0x00, \
  0x52, 0x80

#define kMaskRandom10_4 \
  0xca, 0x00, \
  0x32, 0x80, \
  0xa1, 0x40, \
  0x55, 0x00

#define kMaskRandom10_5 \
  0xca, 0x00, \
  0x32, 0x80, \
  0xa1, 0x40, \
  0x55, 0x00, \
  0x08, 0xc0

#define kMaskRandom10_6 \
  0x0e, 0x00, \
  0x33, 0x00, \
  0x10, 0xc0, \
  0x45, 0x40, \
  0x88, 0x80, \
  0xe0, 0x00

#define kMaskRandom10_7 \
  0x46, 0x00, \
  0x33, 0x00, \
  0x80, 0xc0, \
  0x0c, 0x40, \
  0x28, 0x80, \
  0x94, 0x00, \
  0xc1, 0x00

#define kMaskRandom10_8 \
  0x2c, 0x00, \
  0x81, 0x80, \
  0xa0, 0x40, \
  0x05, 0x40, \
  0x18, 0x80, \
  0xc2, 0x00, \
  0x22, 0x80, \
  0x50, 0x40

#define kMaskRandom10_9 \
  0x4c, 0x00, \
  0x23, 0x00, \
  0x88, 0xc0, \
  0x21, 0x40, \
  0x52, 0x80, \
  0x94, 0x00, \
  0x26, 0x00, \
  0x48, 0x40, \
  0x91, 0x80

#define kMaskRandom11_1 \
  0xff, 0xe0

#define kMaskRandom11_10 \
  0x64, 0x40, \
  0x51, 0x40, \
  0xa9, 0x00, \
  0x04, 0xc0, \
  0xd0, 0x00, \
  0x82, 0x40, \
  0x21, 0x20, \
  0x0c, 0x20, \
  0x4a, 0x00, \
  0x12, 0xa0

#define kMaskRandom11_11 \
  0x46, 0x40, \
  0x33, 0x20, \
  0x99, 0x00, \
  0x05, 0x80, \
  0x80, 0xa0, \
  0x84, 0x40, \
  0x40, 0x60, \
  0x0a, 0x80, \
  0x68, 0x00, \
  0x10, 0x20, \
  0x30, 0x40

#define kMaskRandom11_2 \
  0xec, 0xc0, \
  0x9b, 0xa0

#define kMaskRandom11_3 \
  0xca, 0xc0, \
  0xf1, 0x40, \
  0xb6, 0x20

#define kMaskRandom11_4 \
  0xc4, 0xc0, \
  0x31, 0x60, \
  0x4b, 0x20, \
  0x2c, 0xa0

#define kMaskRandom11_5 \
  0x86, 0x80, \
  0x23, 0x20, \
  0x16, 0x20, \
  0x4c, 0x20, \
  0x41, 0xc0

#define kMaskRandom11_6 \
  0x64, 0x40, \
  0x51, 0x40, \
  0x0c, 0xa0, \
  0xa1, 0x20, \
  0x12, 0xa0, \
  0x8a, 0x40

#define kMaskRandom11_7 \
  0x46, 0x40, \
  0x33, 0x20, \
  0x91, 0x80, \
  0xa4, 0x20, \
  0x50, 0xa0, \
  0x84, 0xc0, \
  0x09, 0x60

#define kMaskRandom11_8 \
  0x0c, 0x80, \
  0x80, 0x60, \
  0xa0, 0x80, \
  0x05, 0x40, \
  0x43, 0x00, \
  0x1a, 0x00, \
  0x60, 0x20, \
  0x14, 0x20

#define kMaskRandom11_9 \
  0x46, 0x40, \
  0x62, 0x60, \
  0x8c, 0x00, \
  0x01, 0x60, \
  0x07, 0x80, \
  0xa0, 0x80, \
  0x18, 0xa0, \
  0x91, 0x00, \
  0x78, 0x00

#define kMaskRandom12_1 \
  0xff, 0xf0

#define kMaskRandom12_10 \
  0x51, 0x40, \
  0x45, 0x10, \
  0x80, 0xd0, \
  0x24, 0x20, \
  0x0a, 0x20, \
  0x00, 0xe0, \
  0xb8, 0x00, \
  0x09, 0x10, \
  0x56, 0x00, \
  0xa2, 0x80

#define kMaskRandom12_11 \
  0x53, 0x60, \
  0x21, 0x30, \
  0x10, 0x90, \
  0x00, 0x70, \
  0x0c, 0x10, \
  0x40, 0xc0, \
  0x6a, 0x00, \
  0x86, 0x00, \
  0x24, 0x80, \
  0x89, 0x00, \
  0xc0, 0x20

#define kMaskRandom12_12 \
  0x10, 0x60, \
  0x02, 0x30, \
  0x40, 0x50, \
  0x21, 0x80, \
  0x81, 0x10, \
  0x14, 0x80, \
  0x98, 0x00, \
  0x08, 0x90, \
  0x62, 0x00, \
  0x24, 0x20, \
  0x8a, 0x00, \
  0x84, 0x40

#define kMaskRandom12_2 \
  0xec, 0xc0, \
  0x93, 0xb0

#define kMaskRandom12_3 \
  0x9b, 0x80, \
  0x4f, 0x10, \
  0x3c, 0x60

#define kMaskRandom12_4 \
  0x8b, 0x20, \
  0x14, 0xb0, \
  0x22, 0xd0, \
  0x45, 0x50

#define kMaskRandom12_5 \
  0x53, 0x60, \
  0x64, 0x20, \
  0x0c, 0xc0, \
  0x82, 0xa0, \
  0x09, 0x30

#define kMaskRandom12_6 \
  0x51, 0x40, \
  0xc5, 0x10, \
  0x21, 0x80, \
  0x12, 0x30, \
  0x08, 0xe0, \
  0x2e, 0x00

#define kMaskRandom12_7 \
  0x53, 0x60, \
  0x21, 0x30, \
  0x90, 0x90, \
  0x02, 0x50, \
  0x06, 0xa0, \
  0x2c, 0x00, \
  0x88, 0x60

#define kMaskRandom12_8 \
  0x20, 0x60, \
  0x80, 0x30, \
  0x42, 0x40, \
  0x01, 0x90, \
  0x14, 0x10, \
  0x0a, 0x80, \
  0x38, 0x00, \
  0xc5, 0x00

#define kMaskRandom12_9 \
  0x53, 0x60, \
  0xe4, 0x20, \
  0x24, 0x40, \
  0xa1, 0x10, \
  0x18, 0x30, \
  0x03, 0x90, \
  0x8a, 0x10, \
  0x04, 0x90, \
  0x00, 0xe0

#define kPacketMaskRandom1 1, \
  kMaskRandom1_1

#define kPacketMaskRandom2 2, \
  kMaskRandom2_1, \
  kMaskRandom2_2

#define kPacketMaskRandom3 3, \
  kMaskRandom3_1, \
  kMaskRandom3_2, \
  kMaskRandom3_3

#define kPacketMaskRandom4 4, \
  kMaskRandom4_1, \
  kMaskRandom4_2, \
  kMaskRandom4_3, \
  kMaskRandom4_4

#define kPacketMaskRandom5 5, \
  kMaskRandom5_1, \
  kMaskRandom5_2, \
  kMaskRandom5_3, \
  kMaskRandom5_4, \
  kMaskRandom5_5

#define kPacketMaskRandom6 6, \
  kMaskRandom6_1, \
  kMaskRandom6_2, \
  kMaskRandom6_3, \
  kMaskRandom6_4, \
  kMaskRandom6_5, \
  kMaskRandom6_6

#define kPacketMaskRandom7 7, \
  kMaskRandom7_1, \
  kMaskRandom7_2, \
  kMaskRandom7_3, \
  kMaskRandom7_4, \
  kMaskRandom7_5, \
  kMaskRandom7_6, \
  kMaskRandom7_7

#define kPacketMaskRandom8 8, \
  kMaskRandom8_1, \
  kMaskRandom8_2, \
  kMaskRandom8_3, \
  kMaskRandom8_4, \
  kMaskRandom8_5, \
  kMaskRandom8_6, \
  kMaskRandom8_7, \
  kMaskRandom8_8

#define kPacketMaskRandom9 9, \
  kMaskRandom9_1, \
  kMaskRandom9_2, \
  kMaskRandom9_3, \
  kMaskRandom9_4, \
  kMaskRandom9_5, \
  kMaskRandom9_6, \
  kMaskRandom9_7, \
  kMaskRandom9_8, \
  kMaskRandom9_9

#define kPacketMaskRandom10 10, \
  kMaskRandom10_1, \
  kMaskRandom10_2, \
  kMaskRandom10_3, \
  kMaskRandom10_4, \
  kMaskRandom10_5, \
  kMaskRandom10_6, \
  kMaskRandom10_7, \
  kMaskRandom10_8, \
  kMaskRandom10_9, \
  kMaskRandom10_10

#define kPacketMaskRandom11 11, \
  kMaskRandom11_1, \
  kMaskRandom11_2, \
  kMaskRandom11_3, \
  kMaskRandom11_4, \
  kMaskRandom11_5, \
  kMaskRandom11_6, \
  kMaskRandom11_7, \
  kMaskRandom11_8, \
  kMaskRandom11_9, \
  kMaskRandom11_10, \
  kMaskRandom11_11

#define kPacketMaskRandom12 12, \
  kMaskRandom12_1, \
  kMaskRandom12_2, \
  kMaskRandom12_3, \
  kMaskRandom12_4, \
  kMaskRandom12_5, \
  kMaskRandom12_6, \
  kMaskRandom12_7, \
  kMaskRandom12_8, \
  kMaskRandom12_9, \
  kMaskRandom12_10, \
  kMaskRandom12_11, \
  kMaskRandom12_12

// clang-format on
}  // namespace

namespace webrtc {
namespace fec_private_tables {

const uint8_t kPacketMaskRandomTbl[] = {
    12,
    kPacketMaskRandom1,  // 2 byte entries.
    kPacketMaskRandom2,
    kPacketMaskRandom3,
    kPacketMaskRandom4,
    kPacketMaskRandom5,
    kPacketMaskRandom6,
    kPacketMaskRandom7,
    kPacketMaskRandom8,
    kPacketMaskRandom9,
    kPacketMaskRandom10,
    kPacketMaskRandom11,
    kPacketMaskRandom12,
};

}  // namespace fec_private_tables
}  // namespace webrtc
