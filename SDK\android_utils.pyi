from typing import Any, Callable

class OnClickListener(object):
    def __init__(self, fn: Callable[[], Any]) -> None: ...
    def onClick(self, _view: Any) -> None: ...

class OnLongClickListener(object):
    def __init__(self, fn: Callable[[], Any]) -> None: ...
    def onLongClick(self, _view: Any) -> bool: ...

def run_on_ui_thread(func: Callable[..., Any], delay: int = 0) -> None: ...
def log(data: Any) -> None: ...

class R(object):
    def __init__(self, fn: Callable[..., Any]) -> None: ...
    def run(self) -> None: ...