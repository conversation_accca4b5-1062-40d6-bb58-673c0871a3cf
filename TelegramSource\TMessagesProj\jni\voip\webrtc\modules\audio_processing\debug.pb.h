// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: debug.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_debug_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_debug_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3020000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3020003 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/message_lite.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_util.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_debug_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_debug_2eproto {
  static const uint32_t offsets[];
};
namespace webrtc {
namespace audioproc {
class Config;
struct ConfigDefaultTypeInternal;
extern ConfigDefaultTypeInternal _Config_default_instance_;
class Event;
struct EventDefaultTypeInternal;
extern EventDefaultTypeInternal _Event_default_instance_;
class Init;
struct InitDefaultTypeInternal;
extern InitDefaultTypeInternal _Init_default_instance_;
class PlayoutAudioDeviceInfo;
struct PlayoutAudioDeviceInfoDefaultTypeInternal;
extern PlayoutAudioDeviceInfoDefaultTypeInternal _PlayoutAudioDeviceInfo_default_instance_;
class ReverseStream;
struct ReverseStreamDefaultTypeInternal;
extern ReverseStreamDefaultTypeInternal _ReverseStream_default_instance_;
class RuntimeSetting;
struct RuntimeSettingDefaultTypeInternal;
extern RuntimeSettingDefaultTypeInternal _RuntimeSetting_default_instance_;
class Stream;
struct StreamDefaultTypeInternal;
extern StreamDefaultTypeInternal _Stream_default_instance_;
}  // namespace audioproc
}  // namespace webrtc
PROTOBUF_NAMESPACE_OPEN
template<> ::webrtc::audioproc::Config* Arena::CreateMaybeMessage<::webrtc::audioproc::Config>(Arena*);
template<> ::webrtc::audioproc::Event* Arena::CreateMaybeMessage<::webrtc::audioproc::Event>(Arena*);
template<> ::webrtc::audioproc::Init* Arena::CreateMaybeMessage<::webrtc::audioproc::Init>(Arena*);
template<> ::webrtc::audioproc::PlayoutAudioDeviceInfo* Arena::CreateMaybeMessage<::webrtc::audioproc::PlayoutAudioDeviceInfo>(Arena*);
template<> ::webrtc::audioproc::ReverseStream* Arena::CreateMaybeMessage<::webrtc::audioproc::ReverseStream>(Arena*);
template<> ::webrtc::audioproc::RuntimeSetting* Arena::CreateMaybeMessage<::webrtc::audioproc::RuntimeSetting>(Arena*);
template<> ::webrtc::audioproc::Stream* Arena::CreateMaybeMessage<::webrtc::audioproc::Stream>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace webrtc {
namespace audioproc {

enum Event_Type : int {
  Event_Type_INIT = 0,
  Event_Type_REVERSE_STREAM = 1,
  Event_Type_STREAM = 2,
  Event_Type_CONFIG = 3,
  Event_Type_UNKNOWN_EVENT = 4,
  Event_Type_RUNTIME_SETTING = 5
};
bool Event_Type_IsValid(int value);
constexpr Event_Type Event_Type_Type_MIN = Event_Type_INIT;
constexpr Event_Type Event_Type_Type_MAX = Event_Type_RUNTIME_SETTING;
constexpr int Event_Type_Type_ARRAYSIZE = Event_Type_Type_MAX + 1;

const std::string& Event_Type_Name(Event_Type value);
template<typename T>
inline const std::string& Event_Type_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Event_Type>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Event_Type_Name.");
  return Event_Type_Name(static_cast<Event_Type>(enum_t_value));
}
bool Event_Type_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Event_Type* value);
// ===================================================================

class Init final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.audioproc.Init) */ {
 public:
  inline Init() : Init(nullptr) {}
  ~Init() override;
  explicit PROTOBUF_CONSTEXPR Init(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Init(const Init& from);
  Init(Init&& from) noexcept
    : Init() {
    *this = ::std::move(from);
  }

  inline Init& operator=(const Init& from) {
    CopyFrom(from);
    return *this;
  }
  inline Init& operator=(Init&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const Init& default_instance() {
    return *internal_default_instance();
  }
  static inline const Init* internal_default_instance() {
    return reinterpret_cast<const Init*>(
               &_Init_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Init& a, Init& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(Init* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Init* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Init* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Init>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const Init& from);
  void MergeFrom(const Init& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Init* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.audioproc.Init";
  }
  protected:
  explicit Init(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSampleRateFieldNumber = 1,
    kDeviceSampleRateFieldNumber = 2,
    kNumInputChannelsFieldNumber = 3,
    kNumOutputChannelsFieldNumber = 4,
    kNumReverseChannelsFieldNumber = 5,
    kReverseSampleRateFieldNumber = 6,
    kOutputSampleRateFieldNumber = 7,
    kReverseOutputSampleRateFieldNumber = 8,
    kTimestampMsFieldNumber = 10,
    kNumReverseOutputChannelsFieldNumber = 9,
  };
  // optional int32 sample_rate = 1;
  bool has_sample_rate() const;
  private:
  bool _internal_has_sample_rate() const;
  public:
  void clear_sample_rate();
  int32_t sample_rate() const;
  void set_sample_rate(int32_t value);
  private:
  int32_t _internal_sample_rate() const;
  void _internal_set_sample_rate(int32_t value);
  public:

  // optional int32 device_sample_rate = 2 [deprecated = true];
  PROTOBUF_DEPRECATED bool has_device_sample_rate() const;
  private:
  bool _internal_has_device_sample_rate() const;
  public:
  PROTOBUF_DEPRECATED void clear_device_sample_rate();
  PROTOBUF_DEPRECATED int32_t device_sample_rate() const;
  PROTOBUF_DEPRECATED void set_device_sample_rate(int32_t value);
  private:
  int32_t _internal_device_sample_rate() const;
  void _internal_set_device_sample_rate(int32_t value);
  public:

  // optional int32 num_input_channels = 3;
  bool has_num_input_channels() const;
  private:
  bool _internal_has_num_input_channels() const;
  public:
  void clear_num_input_channels();
  int32_t num_input_channels() const;
  void set_num_input_channels(int32_t value);
  private:
  int32_t _internal_num_input_channels() const;
  void _internal_set_num_input_channels(int32_t value);
  public:

  // optional int32 num_output_channels = 4;
  bool has_num_output_channels() const;
  private:
  bool _internal_has_num_output_channels() const;
  public:
  void clear_num_output_channels();
  int32_t num_output_channels() const;
  void set_num_output_channels(int32_t value);
  private:
  int32_t _internal_num_output_channels() const;
  void _internal_set_num_output_channels(int32_t value);
  public:

  // optional int32 num_reverse_channels = 5;
  bool has_num_reverse_channels() const;
  private:
  bool _internal_has_num_reverse_channels() const;
  public:
  void clear_num_reverse_channels();
  int32_t num_reverse_channels() const;
  void set_num_reverse_channels(int32_t value);
  private:
  int32_t _internal_num_reverse_channels() const;
  void _internal_set_num_reverse_channels(int32_t value);
  public:

  // optional int32 reverse_sample_rate = 6;
  bool has_reverse_sample_rate() const;
  private:
  bool _internal_has_reverse_sample_rate() const;
  public:
  void clear_reverse_sample_rate();
  int32_t reverse_sample_rate() const;
  void set_reverse_sample_rate(int32_t value);
  private:
  int32_t _internal_reverse_sample_rate() const;
  void _internal_set_reverse_sample_rate(int32_t value);
  public:

  // optional int32 output_sample_rate = 7;
  bool has_output_sample_rate() const;
  private:
  bool _internal_has_output_sample_rate() const;
  public:
  void clear_output_sample_rate();
  int32_t output_sample_rate() const;
  void set_output_sample_rate(int32_t value);
  private:
  int32_t _internal_output_sample_rate() const;
  void _internal_set_output_sample_rate(int32_t value);
  public:

  // optional int32 reverse_output_sample_rate = 8;
  bool has_reverse_output_sample_rate() const;
  private:
  bool _internal_has_reverse_output_sample_rate() const;
  public:
  void clear_reverse_output_sample_rate();
  int32_t reverse_output_sample_rate() const;
  void set_reverse_output_sample_rate(int32_t value);
  private:
  int32_t _internal_reverse_output_sample_rate() const;
  void _internal_set_reverse_output_sample_rate(int32_t value);
  public:

  // optional int64 timestamp_ms = 10;
  bool has_timestamp_ms() const;
  private:
  bool _internal_has_timestamp_ms() const;
  public:
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // optional int32 num_reverse_output_channels = 9;
  bool has_num_reverse_output_channels() const;
  private:
  bool _internal_has_num_reverse_output_channels() const;
  public:
  void clear_num_reverse_output_channels();
  int32_t num_reverse_output_channels() const;
  void set_num_reverse_output_channels(int32_t value);
  private:
  int32_t _internal_num_reverse_output_channels() const;
  void _internal_set_num_reverse_output_channels(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.audioproc.Init)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int32_t sample_rate_;
  int32_t device_sample_rate_;
  int32_t num_input_channels_;
  int32_t num_output_channels_;
  int32_t num_reverse_channels_;
  int32_t reverse_sample_rate_;
  int32_t output_sample_rate_;
  int32_t reverse_output_sample_rate_;
  int64_t timestamp_ms_;
  int32_t num_reverse_output_channels_;
  friend struct ::TableStruct_debug_2eproto;
};
// -------------------------------------------------------------------

class ReverseStream final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.audioproc.ReverseStream) */ {
 public:
  inline ReverseStream() : ReverseStream(nullptr) {}
  ~ReverseStream() override;
  explicit PROTOBUF_CONSTEXPR ReverseStream(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ReverseStream(const ReverseStream& from);
  ReverseStream(ReverseStream&& from) noexcept
    : ReverseStream() {
    *this = ::std::move(from);
  }

  inline ReverseStream& operator=(const ReverseStream& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReverseStream& operator=(ReverseStream&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const ReverseStream& default_instance() {
    return *internal_default_instance();
  }
  static inline const ReverseStream* internal_default_instance() {
    return reinterpret_cast<const ReverseStream*>(
               &_ReverseStream_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ReverseStream& a, ReverseStream& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(ReverseStream* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReverseStream* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ReverseStream* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ReverseStream>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const ReverseStream& from);
  void MergeFrom(const ReverseStream& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(ReverseStream* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.audioproc.ReverseStream";
  }
  protected:
  explicit ReverseStream(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kChannelFieldNumber = 2,
    kDataFieldNumber = 1,
  };
  // repeated bytes channel = 2;
  int channel_size() const;
  private:
  int _internal_channel_size() const;
  public:
  void clear_channel();
  const std::string& channel(int index) const;
  std::string* mutable_channel(int index);
  void set_channel(int index, const std::string& value);
  void set_channel(int index, std::string&& value);
  void set_channel(int index, const char* value);
  void set_channel(int index, const void* value, size_t size);
  std::string* add_channel();
  void add_channel(const std::string& value);
  void add_channel(std::string&& value);
  void add_channel(const char* value);
  void add_channel(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& channel() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_channel();
  private:
  const std::string& _internal_channel(int index) const;
  std::string* _internal_add_channel();
  public:

  // optional bytes data = 1;
  bool has_data() const;
  private:
  bool _internal_has_data() const;
  public:
  void clear_data();
  const std::string& data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_data();
  PROTOBUF_NODISCARD std::string* release_data();
  void set_allocated_data(std::string* data);
  private:
  const std::string& _internal_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_data(const std::string& value);
  std::string* _internal_mutable_data();
  public:

  // @@protoc_insertion_point(class_scope:webrtc.audioproc.ReverseStream)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> channel_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr data_;
  friend struct ::TableStruct_debug_2eproto;
};
// -------------------------------------------------------------------

class Stream final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.audioproc.Stream) */ {
 public:
  inline Stream() : Stream(nullptr) {}
  ~Stream() override;
  explicit PROTOBUF_CONSTEXPR Stream(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Stream(const Stream& from);
  Stream(Stream&& from) noexcept
    : Stream() {
    *this = ::std::move(from);
  }

  inline Stream& operator=(const Stream& from) {
    CopyFrom(from);
    return *this;
  }
  inline Stream& operator=(Stream&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const Stream& default_instance() {
    return *internal_default_instance();
  }
  static inline const Stream* internal_default_instance() {
    return reinterpret_cast<const Stream*>(
               &_Stream_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Stream& a, Stream& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(Stream* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Stream* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Stream* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Stream>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const Stream& from);
  void MergeFrom(const Stream& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Stream* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.audioproc.Stream";
  }
  protected:
  explicit Stream(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputChannelFieldNumber = 7,
    kOutputChannelFieldNumber = 8,
    kInputDataFieldNumber = 1,
    kOutputDataFieldNumber = 2,
    kDelayFieldNumber = 3,
    kDriftFieldNumber = 4,
    kAppliedInputVolumeFieldNumber = 5,
    kKeypressFieldNumber = 6,
  };
  // repeated bytes input_channel = 7;
  int input_channel_size() const;
  private:
  int _internal_input_channel_size() const;
  public:
  void clear_input_channel();
  const std::string& input_channel(int index) const;
  std::string* mutable_input_channel(int index);
  void set_input_channel(int index, const std::string& value);
  void set_input_channel(int index, std::string&& value);
  void set_input_channel(int index, const char* value);
  void set_input_channel(int index, const void* value, size_t size);
  std::string* add_input_channel();
  void add_input_channel(const std::string& value);
  void add_input_channel(std::string&& value);
  void add_input_channel(const char* value);
  void add_input_channel(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& input_channel() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_input_channel();
  private:
  const std::string& _internal_input_channel(int index) const;
  std::string* _internal_add_input_channel();
  public:

  // repeated bytes output_channel = 8;
  int output_channel_size() const;
  private:
  int _internal_output_channel_size() const;
  public:
  void clear_output_channel();
  const std::string& output_channel(int index) const;
  std::string* mutable_output_channel(int index);
  void set_output_channel(int index, const std::string& value);
  void set_output_channel(int index, std::string&& value);
  void set_output_channel(int index, const char* value);
  void set_output_channel(int index, const void* value, size_t size);
  std::string* add_output_channel();
  void add_output_channel(const std::string& value);
  void add_output_channel(std::string&& value);
  void add_output_channel(const char* value);
  void add_output_channel(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& output_channel() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_output_channel();
  private:
  const std::string& _internal_output_channel(int index) const;
  std::string* _internal_add_output_channel();
  public:

  // optional bytes input_data = 1;
  bool has_input_data() const;
  private:
  bool _internal_has_input_data() const;
  public:
  void clear_input_data();
  const std::string& input_data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_input_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_input_data();
  PROTOBUF_NODISCARD std::string* release_input_data();
  void set_allocated_input_data(std::string* input_data);
  private:
  const std::string& _internal_input_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_input_data(const std::string& value);
  std::string* _internal_mutable_input_data();
  public:

  // optional bytes output_data = 2;
  bool has_output_data() const;
  private:
  bool _internal_has_output_data() const;
  public:
  void clear_output_data();
  const std::string& output_data() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_output_data(ArgT0&& arg0, ArgT... args);
  std::string* mutable_output_data();
  PROTOBUF_NODISCARD std::string* release_output_data();
  void set_allocated_output_data(std::string* output_data);
  private:
  const std::string& _internal_output_data() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_output_data(const std::string& value);
  std::string* _internal_mutable_output_data();
  public:

  // optional int32 delay = 3;
  bool has_delay() const;
  private:
  bool _internal_has_delay() const;
  public:
  void clear_delay();
  int32_t delay() const;
  void set_delay(int32_t value);
  private:
  int32_t _internal_delay() const;
  void _internal_set_delay(int32_t value);
  public:

  // optional sint32 drift = 4;
  bool has_drift() const;
  private:
  bool _internal_has_drift() const;
  public:
  void clear_drift();
  int32_t drift() const;
  void set_drift(int32_t value);
  private:
  int32_t _internal_drift() const;
  void _internal_set_drift(int32_t value);
  public:

  // optional int32 applied_input_volume = 5;
  bool has_applied_input_volume() const;
  private:
  bool _internal_has_applied_input_volume() const;
  public:
  void clear_applied_input_volume();
  int32_t applied_input_volume() const;
  void set_applied_input_volume(int32_t value);
  private:
  int32_t _internal_applied_input_volume() const;
  void _internal_set_applied_input_volume(int32_t value);
  public:

  // optional bool keypress = 6;
  bool has_keypress() const;
  private:
  bool _internal_has_keypress() const;
  public:
  void clear_keypress();
  bool keypress() const;
  void set_keypress(bool value);
  private:
  bool _internal_keypress() const;
  void _internal_set_keypress(bool value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.audioproc.Stream)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> input_channel_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> output_channel_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr input_data_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr output_data_;
  int32_t delay_;
  int32_t drift_;
  int32_t applied_input_volume_;
  bool keypress_;
  friend struct ::TableStruct_debug_2eproto;
};
// -------------------------------------------------------------------

class Config final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.audioproc.Config) */ {
 public:
  inline Config() : Config(nullptr) {}
  ~Config() override;
  explicit PROTOBUF_CONSTEXPR Config(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Config(const Config& from);
  Config(Config&& from) noexcept
    : Config() {
    *this = ::std::move(from);
  }

  inline Config& operator=(const Config& from) {
    CopyFrom(from);
    return *this;
  }
  inline Config& operator=(Config&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const Config& default_instance() {
    return *internal_default_instance();
  }
  static inline const Config* internal_default_instance() {
    return reinterpret_cast<const Config*>(
               &_Config_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Config& a, Config& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(Config* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Config* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Config* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Config>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const Config& from);
  void MergeFrom(const Config& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Config* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.audioproc.Config";
  }
  protected:
  explicit Config(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kExperimentsDescriptionFieldNumber = 17,
    kAecEnabledFieldNumber = 1,
    kAecDelayAgnosticEnabledFieldNumber = 2,
    kAecDriftCompensationEnabledFieldNumber = 3,
    kAecExtendedFilterEnabledFieldNumber = 4,
    kAecSuppressionLevelFieldNumber = 5,
    kAecmRoutingModeFieldNumber = 8,
    kAecmEnabledFieldNumber = 6,
    kAecmComfortNoiseEnabledFieldNumber = 7,
    kAgcEnabledFieldNumber = 9,
    kAgcLimiterEnabledFieldNumber = 11,
    kAgcModeFieldNumber = 10,
    kNoiseRobustAgcEnabledFieldNumber = 12,
    kHpfEnabledFieldNumber = 13,
    kNsEnabledFieldNumber = 14,
    kTransientSuppressionEnabledFieldNumber = 16,
    kNsLevelFieldNumber = 15,
    kPreAmplifierEnabledFieldNumber = 19,
    kPreAmplifierFixedGainFactorFieldNumber = 20,
  };
  // optional string experiments_description = 17;
  bool has_experiments_description() const;
  private:
  bool _internal_has_experiments_description() const;
  public:
  void clear_experiments_description();
  const std::string& experiments_description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_experiments_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_experiments_description();
  PROTOBUF_NODISCARD std::string* release_experiments_description();
  void set_allocated_experiments_description(std::string* experiments_description);
  private:
  const std::string& _internal_experiments_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_experiments_description(const std::string& value);
  std::string* _internal_mutable_experiments_description();
  public:

  // optional bool aec_enabled = 1;
  bool has_aec_enabled() const;
  private:
  bool _internal_has_aec_enabled() const;
  public:
  void clear_aec_enabled();
  bool aec_enabled() const;
  void set_aec_enabled(bool value);
  private:
  bool _internal_aec_enabled() const;
  void _internal_set_aec_enabled(bool value);
  public:

  // optional bool aec_delay_agnostic_enabled = 2;
  bool has_aec_delay_agnostic_enabled() const;
  private:
  bool _internal_has_aec_delay_agnostic_enabled() const;
  public:
  void clear_aec_delay_agnostic_enabled();
  bool aec_delay_agnostic_enabled() const;
  void set_aec_delay_agnostic_enabled(bool value);
  private:
  bool _internal_aec_delay_agnostic_enabled() const;
  void _internal_set_aec_delay_agnostic_enabled(bool value);
  public:

  // optional bool aec_drift_compensation_enabled = 3;
  bool has_aec_drift_compensation_enabled() const;
  private:
  bool _internal_has_aec_drift_compensation_enabled() const;
  public:
  void clear_aec_drift_compensation_enabled();
  bool aec_drift_compensation_enabled() const;
  void set_aec_drift_compensation_enabled(bool value);
  private:
  bool _internal_aec_drift_compensation_enabled() const;
  void _internal_set_aec_drift_compensation_enabled(bool value);
  public:

  // optional bool aec_extended_filter_enabled = 4;
  bool has_aec_extended_filter_enabled() const;
  private:
  bool _internal_has_aec_extended_filter_enabled() const;
  public:
  void clear_aec_extended_filter_enabled();
  bool aec_extended_filter_enabled() const;
  void set_aec_extended_filter_enabled(bool value);
  private:
  bool _internal_aec_extended_filter_enabled() const;
  void _internal_set_aec_extended_filter_enabled(bool value);
  public:

  // optional int32 aec_suppression_level = 5;
  bool has_aec_suppression_level() const;
  private:
  bool _internal_has_aec_suppression_level() const;
  public:
  void clear_aec_suppression_level();
  int32_t aec_suppression_level() const;
  void set_aec_suppression_level(int32_t value);
  private:
  int32_t _internal_aec_suppression_level() const;
  void _internal_set_aec_suppression_level(int32_t value);
  public:

  // optional int32 aecm_routing_mode = 8 [deprecated = true];
  PROTOBUF_DEPRECATED bool has_aecm_routing_mode() const;
  private:
  bool _internal_has_aecm_routing_mode() const;
  public:
  PROTOBUF_DEPRECATED void clear_aecm_routing_mode();
  PROTOBUF_DEPRECATED int32_t aecm_routing_mode() const;
  PROTOBUF_DEPRECATED void set_aecm_routing_mode(int32_t value);
  private:
  int32_t _internal_aecm_routing_mode() const;
  void _internal_set_aecm_routing_mode(int32_t value);
  public:

  // optional bool aecm_enabled = 6;
  bool has_aecm_enabled() const;
  private:
  bool _internal_has_aecm_enabled() const;
  public:
  void clear_aecm_enabled();
  bool aecm_enabled() const;
  void set_aecm_enabled(bool value);
  private:
  bool _internal_aecm_enabled() const;
  void _internal_set_aecm_enabled(bool value);
  public:

  // optional bool aecm_comfort_noise_enabled = 7 [deprecated = true];
  PROTOBUF_DEPRECATED bool has_aecm_comfort_noise_enabled() const;
  private:
  bool _internal_has_aecm_comfort_noise_enabled() const;
  public:
  PROTOBUF_DEPRECATED void clear_aecm_comfort_noise_enabled();
  PROTOBUF_DEPRECATED bool aecm_comfort_noise_enabled() const;
  PROTOBUF_DEPRECATED void set_aecm_comfort_noise_enabled(bool value);
  private:
  bool _internal_aecm_comfort_noise_enabled() const;
  void _internal_set_aecm_comfort_noise_enabled(bool value);
  public:

  // optional bool agc_enabled = 9;
  bool has_agc_enabled() const;
  private:
  bool _internal_has_agc_enabled() const;
  public:
  void clear_agc_enabled();
  bool agc_enabled() const;
  void set_agc_enabled(bool value);
  private:
  bool _internal_agc_enabled() const;
  void _internal_set_agc_enabled(bool value);
  public:

  // optional bool agc_limiter_enabled = 11;
  bool has_agc_limiter_enabled() const;
  private:
  bool _internal_has_agc_limiter_enabled() const;
  public:
  void clear_agc_limiter_enabled();
  bool agc_limiter_enabled() const;
  void set_agc_limiter_enabled(bool value);
  private:
  bool _internal_agc_limiter_enabled() const;
  void _internal_set_agc_limiter_enabled(bool value);
  public:

  // optional int32 agc_mode = 10;
  bool has_agc_mode() const;
  private:
  bool _internal_has_agc_mode() const;
  public:
  void clear_agc_mode();
  int32_t agc_mode() const;
  void set_agc_mode(int32_t value);
  private:
  int32_t _internal_agc_mode() const;
  void _internal_set_agc_mode(int32_t value);
  public:

  // optional bool noise_robust_agc_enabled = 12;
  bool has_noise_robust_agc_enabled() const;
  private:
  bool _internal_has_noise_robust_agc_enabled() const;
  public:
  void clear_noise_robust_agc_enabled();
  bool noise_robust_agc_enabled() const;
  void set_noise_robust_agc_enabled(bool value);
  private:
  bool _internal_noise_robust_agc_enabled() const;
  void _internal_set_noise_robust_agc_enabled(bool value);
  public:

  // optional bool hpf_enabled = 13;
  bool has_hpf_enabled() const;
  private:
  bool _internal_has_hpf_enabled() const;
  public:
  void clear_hpf_enabled();
  bool hpf_enabled() const;
  void set_hpf_enabled(bool value);
  private:
  bool _internal_hpf_enabled() const;
  void _internal_set_hpf_enabled(bool value);
  public:

  // optional bool ns_enabled = 14;
  bool has_ns_enabled() const;
  private:
  bool _internal_has_ns_enabled() const;
  public:
  void clear_ns_enabled();
  bool ns_enabled() const;
  void set_ns_enabled(bool value);
  private:
  bool _internal_ns_enabled() const;
  void _internal_set_ns_enabled(bool value);
  public:

  // optional bool transient_suppression_enabled = 16;
  bool has_transient_suppression_enabled() const;
  private:
  bool _internal_has_transient_suppression_enabled() const;
  public:
  void clear_transient_suppression_enabled();
  bool transient_suppression_enabled() const;
  void set_transient_suppression_enabled(bool value);
  private:
  bool _internal_transient_suppression_enabled() const;
  void _internal_set_transient_suppression_enabled(bool value);
  public:

  // optional int32 ns_level = 15;
  bool has_ns_level() const;
  private:
  bool _internal_has_ns_level() const;
  public:
  void clear_ns_level();
  int32_t ns_level() const;
  void set_ns_level(int32_t value);
  private:
  int32_t _internal_ns_level() const;
  void _internal_set_ns_level(int32_t value);
  public:

  // optional bool pre_amplifier_enabled = 19;
  bool has_pre_amplifier_enabled() const;
  private:
  bool _internal_has_pre_amplifier_enabled() const;
  public:
  void clear_pre_amplifier_enabled();
  bool pre_amplifier_enabled() const;
  void set_pre_amplifier_enabled(bool value);
  private:
  bool _internal_pre_amplifier_enabled() const;
  void _internal_set_pre_amplifier_enabled(bool value);
  public:

  // optional float pre_amplifier_fixed_gain_factor = 20;
  bool has_pre_amplifier_fixed_gain_factor() const;
  private:
  bool _internal_has_pre_amplifier_fixed_gain_factor() const;
  public:
  void clear_pre_amplifier_fixed_gain_factor();
  float pre_amplifier_fixed_gain_factor() const;
  void set_pre_amplifier_fixed_gain_factor(float value);
  private:
  float _internal_pre_amplifier_fixed_gain_factor() const;
  void _internal_set_pre_amplifier_fixed_gain_factor(float value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.audioproc.Config)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr experiments_description_;
  bool aec_enabled_;
  bool aec_delay_agnostic_enabled_;
  bool aec_drift_compensation_enabled_;
  bool aec_extended_filter_enabled_;
  int32_t aec_suppression_level_;
  int32_t aecm_routing_mode_;
  bool aecm_enabled_;
  bool aecm_comfort_noise_enabled_;
  bool agc_enabled_;
  bool agc_limiter_enabled_;
  int32_t agc_mode_;
  bool noise_robust_agc_enabled_;
  bool hpf_enabled_;
  bool ns_enabled_;
  bool transient_suppression_enabled_;
  int32_t ns_level_;
  bool pre_amplifier_enabled_;
  float pre_amplifier_fixed_gain_factor_;
  friend struct ::TableStruct_debug_2eproto;
};
// -------------------------------------------------------------------

class PlayoutAudioDeviceInfo final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.audioproc.PlayoutAudioDeviceInfo) */ {
 public:
  inline PlayoutAudioDeviceInfo() : PlayoutAudioDeviceInfo(nullptr) {}
  ~PlayoutAudioDeviceInfo() override;
  explicit PROTOBUF_CONSTEXPR PlayoutAudioDeviceInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PlayoutAudioDeviceInfo(const PlayoutAudioDeviceInfo& from);
  PlayoutAudioDeviceInfo(PlayoutAudioDeviceInfo&& from) noexcept
    : PlayoutAudioDeviceInfo() {
    *this = ::std::move(from);
  }

  inline PlayoutAudioDeviceInfo& operator=(const PlayoutAudioDeviceInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline PlayoutAudioDeviceInfo& operator=(PlayoutAudioDeviceInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const PlayoutAudioDeviceInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const PlayoutAudioDeviceInfo* internal_default_instance() {
    return reinterpret_cast<const PlayoutAudioDeviceInfo*>(
               &_PlayoutAudioDeviceInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(PlayoutAudioDeviceInfo& a, PlayoutAudioDeviceInfo& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(PlayoutAudioDeviceInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PlayoutAudioDeviceInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PlayoutAudioDeviceInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PlayoutAudioDeviceInfo>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const PlayoutAudioDeviceInfo& from);
  void MergeFrom(const PlayoutAudioDeviceInfo& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(PlayoutAudioDeviceInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.audioproc.PlayoutAudioDeviceInfo";
  }
  protected:
  explicit PlayoutAudioDeviceInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kMaxVolumeFieldNumber = 2,
  };
  // optional int32 id = 1;
  bool has_id() const;
  private:
  bool _internal_has_id() const;
  public:
  void clear_id();
  int32_t id() const;
  void set_id(int32_t value);
  private:
  int32_t _internal_id() const;
  void _internal_set_id(int32_t value);
  public:

  // optional int32 max_volume = 2;
  bool has_max_volume() const;
  private:
  bool _internal_has_max_volume() const;
  public:
  void clear_max_volume();
  int32_t max_volume() const;
  void set_max_volume(int32_t value);
  private:
  int32_t _internal_max_volume() const;
  void _internal_set_max_volume(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.audioproc.PlayoutAudioDeviceInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  int32_t id_;
  int32_t max_volume_;
  friend struct ::TableStruct_debug_2eproto;
};
// -------------------------------------------------------------------

class RuntimeSetting final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.audioproc.RuntimeSetting) */ {
 public:
  inline RuntimeSetting() : RuntimeSetting(nullptr) {}
  ~RuntimeSetting() override;
  explicit PROTOBUF_CONSTEXPR RuntimeSetting(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RuntimeSetting(const RuntimeSetting& from);
  RuntimeSetting(RuntimeSetting&& from) noexcept
    : RuntimeSetting() {
    *this = ::std::move(from);
  }

  inline RuntimeSetting& operator=(const RuntimeSetting& from) {
    CopyFrom(from);
    return *this;
  }
  inline RuntimeSetting& operator=(RuntimeSetting&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const RuntimeSetting& default_instance() {
    return *internal_default_instance();
  }
  static inline const RuntimeSetting* internal_default_instance() {
    return reinterpret_cast<const RuntimeSetting*>(
               &_RuntimeSetting_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(RuntimeSetting& a, RuntimeSetting& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(RuntimeSetting* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RuntimeSetting* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RuntimeSetting* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RuntimeSetting>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const RuntimeSetting& from);
  void MergeFrom(const RuntimeSetting& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RuntimeSetting* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.audioproc.RuntimeSetting";
  }
  protected:
  explicit RuntimeSetting(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPlayoutAudioDeviceChangeFieldNumber = 5,
    kCapturePreGainFieldNumber = 1,
    kCustomRenderProcessingSettingFieldNumber = 2,
    kCaptureFixedPostGainFieldNumber = 3,
    kPlayoutVolumeChangeFieldNumber = 4,
    kCaptureOutputUsedFieldNumber = 6,
    kCapturePostGainFieldNumber = 7,
  };
  // optional .webrtc.audioproc.PlayoutAudioDeviceInfo playout_audio_device_change = 5;
  bool has_playout_audio_device_change() const;
  private:
  bool _internal_has_playout_audio_device_change() const;
  public:
  void clear_playout_audio_device_change();
  const ::webrtc::audioproc::PlayoutAudioDeviceInfo& playout_audio_device_change() const;
  PROTOBUF_NODISCARD ::webrtc::audioproc::PlayoutAudioDeviceInfo* release_playout_audio_device_change();
  ::webrtc::audioproc::PlayoutAudioDeviceInfo* mutable_playout_audio_device_change();
  void set_allocated_playout_audio_device_change(::webrtc::audioproc::PlayoutAudioDeviceInfo* playout_audio_device_change);
  private:
  const ::webrtc::audioproc::PlayoutAudioDeviceInfo& _internal_playout_audio_device_change() const;
  ::webrtc::audioproc::PlayoutAudioDeviceInfo* _internal_mutable_playout_audio_device_change();
  public:
  void unsafe_arena_set_allocated_playout_audio_device_change(
      ::webrtc::audioproc::PlayoutAudioDeviceInfo* playout_audio_device_change);
  ::webrtc::audioproc::PlayoutAudioDeviceInfo* unsafe_arena_release_playout_audio_device_change();

  // optional float capture_pre_gain = 1;
  bool has_capture_pre_gain() const;
  private:
  bool _internal_has_capture_pre_gain() const;
  public:
  void clear_capture_pre_gain();
  float capture_pre_gain() const;
  void set_capture_pre_gain(float value);
  private:
  float _internal_capture_pre_gain() const;
  void _internal_set_capture_pre_gain(float value);
  public:

  // optional float custom_render_processing_setting = 2;
  bool has_custom_render_processing_setting() const;
  private:
  bool _internal_has_custom_render_processing_setting() const;
  public:
  void clear_custom_render_processing_setting();
  float custom_render_processing_setting() const;
  void set_custom_render_processing_setting(float value);
  private:
  float _internal_custom_render_processing_setting() const;
  void _internal_set_custom_render_processing_setting(float value);
  public:

  // optional float capture_fixed_post_gain = 3;
  bool has_capture_fixed_post_gain() const;
  private:
  bool _internal_has_capture_fixed_post_gain() const;
  public:
  void clear_capture_fixed_post_gain();
  float capture_fixed_post_gain() const;
  void set_capture_fixed_post_gain(float value);
  private:
  float _internal_capture_fixed_post_gain() const;
  void _internal_set_capture_fixed_post_gain(float value);
  public:

  // optional int32 playout_volume_change = 4;
  bool has_playout_volume_change() const;
  private:
  bool _internal_has_playout_volume_change() const;
  public:
  void clear_playout_volume_change();
  int32_t playout_volume_change() const;
  void set_playout_volume_change(int32_t value);
  private:
  int32_t _internal_playout_volume_change() const;
  void _internal_set_playout_volume_change(int32_t value);
  public:

  // optional bool capture_output_used = 6;
  bool has_capture_output_used() const;
  private:
  bool _internal_has_capture_output_used() const;
  public:
  void clear_capture_output_used();
  bool capture_output_used() const;
  void set_capture_output_used(bool value);
  private:
  bool _internal_capture_output_used() const;
  void _internal_set_capture_output_used(bool value);
  public:

  // optional float capture_post_gain = 7;
  bool has_capture_post_gain() const;
  private:
  bool _internal_has_capture_post_gain() const;
  public:
  void clear_capture_post_gain();
  float capture_post_gain() const;
  void set_capture_post_gain(float value);
  private:
  float _internal_capture_post_gain() const;
  void _internal_set_capture_post_gain(float value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.audioproc.RuntimeSetting)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::webrtc::audioproc::PlayoutAudioDeviceInfo* playout_audio_device_change_;
  float capture_pre_gain_;
  float custom_render_processing_setting_;
  float capture_fixed_post_gain_;
  int32_t playout_volume_change_;
  bool capture_output_used_;
  float capture_post_gain_;
  friend struct ::TableStruct_debug_2eproto;
};
// -------------------------------------------------------------------

class Event final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:webrtc.audioproc.Event) */ {
 public:
  inline Event() : Event(nullptr) {}
  ~Event() override;
  explicit PROTOBUF_CONSTEXPR Event(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Event(const Event& from);
  Event(Event&& from) noexcept
    : Event() {
    *this = ::std::move(from);
  }

  inline Event& operator=(const Event& from) {
    CopyFrom(from);
    return *this;
  }
  inline Event& operator=(Event&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  inline const std::string& unknown_fields() const {
    return _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString);
  }
  inline std::string* mutable_unknown_fields() {
    return _internal_metadata_.mutable_unknown_fields<std::string>();
  }

  static const Event& default_instance() {
    return *internal_default_instance();
  }
  static inline const Event* internal_default_instance() {
    return reinterpret_cast<const Event*>(
               &_Event_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(Event& a, Event& b) {
    a.Swap(&b);
  }
  PROTOBUF_NOINLINE void Swap(Event* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Event* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Event* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Event>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const Event& from);
  void MergeFrom(const Event& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(Event* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "webrtc.audioproc.Event";
  }
  protected:
  explicit Event(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  typedef Event_Type Type;
  static constexpr Type INIT =
    Event_Type_INIT;
  static constexpr Type REVERSE_STREAM =
    Event_Type_REVERSE_STREAM;
  static constexpr Type STREAM =
    Event_Type_STREAM;
  static constexpr Type CONFIG =
    Event_Type_CONFIG;
  static constexpr Type UNKNOWN_EVENT =
    Event_Type_UNKNOWN_EVENT;
  static constexpr Type RUNTIME_SETTING =
    Event_Type_RUNTIME_SETTING;
  static inline bool Type_IsValid(int value) {
    return Event_Type_IsValid(value);
  }
  static constexpr Type Type_MIN =
    Event_Type_Type_MIN;
  static constexpr Type Type_MAX =
    Event_Type_Type_MAX;
  static constexpr int Type_ARRAYSIZE =
    Event_Type_Type_ARRAYSIZE;
  template<typename T>
  static inline const std::string& Type_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Type>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Type_Name.");
    return Event_Type_Name(enum_t_value);
  }
  static inline bool Type_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Type* value) {
    return Event_Type_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kInitFieldNumber = 2,
    kReverseStreamFieldNumber = 3,
    kStreamFieldNumber = 4,
    kConfigFieldNumber = 5,
    kRuntimeSettingFieldNumber = 6,
    kTypeFieldNumber = 1,
  };
  // optional .webrtc.audioproc.Init init = 2;
  bool has_init() const;
  private:
  bool _internal_has_init() const;
  public:
  void clear_init();
  const ::webrtc::audioproc::Init& init() const;
  PROTOBUF_NODISCARD ::webrtc::audioproc::Init* release_init();
  ::webrtc::audioproc::Init* mutable_init();
  void set_allocated_init(::webrtc::audioproc::Init* init);
  private:
  const ::webrtc::audioproc::Init& _internal_init() const;
  ::webrtc::audioproc::Init* _internal_mutable_init();
  public:
  void unsafe_arena_set_allocated_init(
      ::webrtc::audioproc::Init* init);
  ::webrtc::audioproc::Init* unsafe_arena_release_init();

  // optional .webrtc.audioproc.ReverseStream reverse_stream = 3;
  bool has_reverse_stream() const;
  private:
  bool _internal_has_reverse_stream() const;
  public:
  void clear_reverse_stream();
  const ::webrtc::audioproc::ReverseStream& reverse_stream() const;
  PROTOBUF_NODISCARD ::webrtc::audioproc::ReverseStream* release_reverse_stream();
  ::webrtc::audioproc::ReverseStream* mutable_reverse_stream();
  void set_allocated_reverse_stream(::webrtc::audioproc::ReverseStream* reverse_stream);
  private:
  const ::webrtc::audioproc::ReverseStream& _internal_reverse_stream() const;
  ::webrtc::audioproc::ReverseStream* _internal_mutable_reverse_stream();
  public:
  void unsafe_arena_set_allocated_reverse_stream(
      ::webrtc::audioproc::ReverseStream* reverse_stream);
  ::webrtc::audioproc::ReverseStream* unsafe_arena_release_reverse_stream();

  // optional .webrtc.audioproc.Stream stream = 4;
  bool has_stream() const;
  private:
  bool _internal_has_stream() const;
  public:
  void clear_stream();
  const ::webrtc::audioproc::Stream& stream() const;
  PROTOBUF_NODISCARD ::webrtc::audioproc::Stream* release_stream();
  ::webrtc::audioproc::Stream* mutable_stream();
  void set_allocated_stream(::webrtc::audioproc::Stream* stream);
  private:
  const ::webrtc::audioproc::Stream& _internal_stream() const;
  ::webrtc::audioproc::Stream* _internal_mutable_stream();
  public:
  void unsafe_arena_set_allocated_stream(
      ::webrtc::audioproc::Stream* stream);
  ::webrtc::audioproc::Stream* unsafe_arena_release_stream();

  // optional .webrtc.audioproc.Config config = 5;
  bool has_config() const;
  private:
  bool _internal_has_config() const;
  public:
  void clear_config();
  const ::webrtc::audioproc::Config& config() const;
  PROTOBUF_NODISCARD ::webrtc::audioproc::Config* release_config();
  ::webrtc::audioproc::Config* mutable_config();
  void set_allocated_config(::webrtc::audioproc::Config* config);
  private:
  const ::webrtc::audioproc::Config& _internal_config() const;
  ::webrtc::audioproc::Config* _internal_mutable_config();
  public:
  void unsafe_arena_set_allocated_config(
      ::webrtc::audioproc::Config* config);
  ::webrtc::audioproc::Config* unsafe_arena_release_config();

  // optional .webrtc.audioproc.RuntimeSetting runtime_setting = 6;
  bool has_runtime_setting() const;
  private:
  bool _internal_has_runtime_setting() const;
  public:
  void clear_runtime_setting();
  const ::webrtc::audioproc::RuntimeSetting& runtime_setting() const;
  PROTOBUF_NODISCARD ::webrtc::audioproc::RuntimeSetting* release_runtime_setting();
  ::webrtc::audioproc::RuntimeSetting* mutable_runtime_setting();
  void set_allocated_runtime_setting(::webrtc::audioproc::RuntimeSetting* runtime_setting);
  private:
  const ::webrtc::audioproc::RuntimeSetting& _internal_runtime_setting() const;
  ::webrtc::audioproc::RuntimeSetting* _internal_mutable_runtime_setting();
  public:
  void unsafe_arena_set_allocated_runtime_setting(
      ::webrtc::audioproc::RuntimeSetting* runtime_setting);
  ::webrtc::audioproc::RuntimeSetting* unsafe_arena_release_runtime_setting();

  // required .webrtc.audioproc.Event.Type type = 1;
  bool has_type() const;
  private:
  bool _internal_has_type() const;
  public:
  void clear_type();
  ::webrtc::audioproc::Event_Type type() const;
  void set_type(::webrtc::audioproc::Event_Type value);
  private:
  ::webrtc::audioproc::Event_Type _internal_type() const;
  void _internal_set_type(::webrtc::audioproc::Event_Type value);
  public:

  // @@protoc_insertion_point(class_scope:webrtc.audioproc.Event)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::webrtc::audioproc::Init* init_;
  ::webrtc::audioproc::ReverseStream* reverse_stream_;
  ::webrtc::audioproc::Stream* stream_;
  ::webrtc::audioproc::Config* config_;
  ::webrtc::audioproc::RuntimeSetting* runtime_setting_;
  int type_;
  friend struct ::TableStruct_debug_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Init

// optional int32 sample_rate = 1;
inline bool Init::_internal_has_sample_rate() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool Init::has_sample_rate() const {
  return _internal_has_sample_rate();
}
inline void Init::clear_sample_rate() {
  sample_rate_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline int32_t Init::_internal_sample_rate() const {
  return sample_rate_;
}
inline int32_t Init::sample_rate() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Init.sample_rate)
  return _internal_sample_rate();
}
inline void Init::_internal_set_sample_rate(int32_t value) {
  _has_bits_[0] |= 0x00000001u;
  sample_rate_ = value;
}
inline void Init::set_sample_rate(int32_t value) {
  _internal_set_sample_rate(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Init.sample_rate)
}

// optional int32 device_sample_rate = 2 [deprecated = true];
inline bool Init::_internal_has_device_sample_rate() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool Init::has_device_sample_rate() const {
  return _internal_has_device_sample_rate();
}
inline void Init::clear_device_sample_rate() {
  device_sample_rate_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline int32_t Init::_internal_device_sample_rate() const {
  return device_sample_rate_;
}
inline int32_t Init::device_sample_rate() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Init.device_sample_rate)
  return _internal_device_sample_rate();
}
inline void Init::_internal_set_device_sample_rate(int32_t value) {
  _has_bits_[0] |= 0x00000002u;
  device_sample_rate_ = value;
}
inline void Init::set_device_sample_rate(int32_t value) {
  _internal_set_device_sample_rate(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Init.device_sample_rate)
}

// optional int32 num_input_channels = 3;
inline bool Init::_internal_has_num_input_channels() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool Init::has_num_input_channels() const {
  return _internal_has_num_input_channels();
}
inline void Init::clear_num_input_channels() {
  num_input_channels_ = 0;
  _has_bits_[0] &= ~0x00000004u;
}
inline int32_t Init::_internal_num_input_channels() const {
  return num_input_channels_;
}
inline int32_t Init::num_input_channels() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Init.num_input_channels)
  return _internal_num_input_channels();
}
inline void Init::_internal_set_num_input_channels(int32_t value) {
  _has_bits_[0] |= 0x00000004u;
  num_input_channels_ = value;
}
inline void Init::set_num_input_channels(int32_t value) {
  _internal_set_num_input_channels(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Init.num_input_channels)
}

// optional int32 num_output_channels = 4;
inline bool Init::_internal_has_num_output_channels() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool Init::has_num_output_channels() const {
  return _internal_has_num_output_channels();
}
inline void Init::clear_num_output_channels() {
  num_output_channels_ = 0;
  _has_bits_[0] &= ~0x00000008u;
}
inline int32_t Init::_internal_num_output_channels() const {
  return num_output_channels_;
}
inline int32_t Init::num_output_channels() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Init.num_output_channels)
  return _internal_num_output_channels();
}
inline void Init::_internal_set_num_output_channels(int32_t value) {
  _has_bits_[0] |= 0x00000008u;
  num_output_channels_ = value;
}
inline void Init::set_num_output_channels(int32_t value) {
  _internal_set_num_output_channels(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Init.num_output_channels)
}

// optional int32 num_reverse_channels = 5;
inline bool Init::_internal_has_num_reverse_channels() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool Init::has_num_reverse_channels() const {
  return _internal_has_num_reverse_channels();
}
inline void Init::clear_num_reverse_channels() {
  num_reverse_channels_ = 0;
  _has_bits_[0] &= ~0x00000010u;
}
inline int32_t Init::_internal_num_reverse_channels() const {
  return num_reverse_channels_;
}
inline int32_t Init::num_reverse_channels() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Init.num_reverse_channels)
  return _internal_num_reverse_channels();
}
inline void Init::_internal_set_num_reverse_channels(int32_t value) {
  _has_bits_[0] |= 0x00000010u;
  num_reverse_channels_ = value;
}
inline void Init::set_num_reverse_channels(int32_t value) {
  _internal_set_num_reverse_channels(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Init.num_reverse_channels)
}

// optional int32 reverse_sample_rate = 6;
inline bool Init::_internal_has_reverse_sample_rate() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool Init::has_reverse_sample_rate() const {
  return _internal_has_reverse_sample_rate();
}
inline void Init::clear_reverse_sample_rate() {
  reverse_sample_rate_ = 0;
  _has_bits_[0] &= ~0x00000020u;
}
inline int32_t Init::_internal_reverse_sample_rate() const {
  return reverse_sample_rate_;
}
inline int32_t Init::reverse_sample_rate() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Init.reverse_sample_rate)
  return _internal_reverse_sample_rate();
}
inline void Init::_internal_set_reverse_sample_rate(int32_t value) {
  _has_bits_[0] |= 0x00000020u;
  reverse_sample_rate_ = value;
}
inline void Init::set_reverse_sample_rate(int32_t value) {
  _internal_set_reverse_sample_rate(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Init.reverse_sample_rate)
}

// optional int32 output_sample_rate = 7;
inline bool Init::_internal_has_output_sample_rate() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool Init::has_output_sample_rate() const {
  return _internal_has_output_sample_rate();
}
inline void Init::clear_output_sample_rate() {
  output_sample_rate_ = 0;
  _has_bits_[0] &= ~0x00000040u;
}
inline int32_t Init::_internal_output_sample_rate() const {
  return output_sample_rate_;
}
inline int32_t Init::output_sample_rate() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Init.output_sample_rate)
  return _internal_output_sample_rate();
}
inline void Init::_internal_set_output_sample_rate(int32_t value) {
  _has_bits_[0] |= 0x00000040u;
  output_sample_rate_ = value;
}
inline void Init::set_output_sample_rate(int32_t value) {
  _internal_set_output_sample_rate(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Init.output_sample_rate)
}

// optional int32 reverse_output_sample_rate = 8;
inline bool Init::_internal_has_reverse_output_sample_rate() const {
  bool value = (_has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool Init::has_reverse_output_sample_rate() const {
  return _internal_has_reverse_output_sample_rate();
}
inline void Init::clear_reverse_output_sample_rate() {
  reverse_output_sample_rate_ = 0;
  _has_bits_[0] &= ~0x00000080u;
}
inline int32_t Init::_internal_reverse_output_sample_rate() const {
  return reverse_output_sample_rate_;
}
inline int32_t Init::reverse_output_sample_rate() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Init.reverse_output_sample_rate)
  return _internal_reverse_output_sample_rate();
}
inline void Init::_internal_set_reverse_output_sample_rate(int32_t value) {
  _has_bits_[0] |= 0x00000080u;
  reverse_output_sample_rate_ = value;
}
inline void Init::set_reverse_output_sample_rate(int32_t value) {
  _internal_set_reverse_output_sample_rate(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Init.reverse_output_sample_rate)
}

// optional int32 num_reverse_output_channels = 9;
inline bool Init::_internal_has_num_reverse_output_channels() const {
  bool value = (_has_bits_[0] & 0x00000200u) != 0;
  return value;
}
inline bool Init::has_num_reverse_output_channels() const {
  return _internal_has_num_reverse_output_channels();
}
inline void Init::clear_num_reverse_output_channels() {
  num_reverse_output_channels_ = 0;
  _has_bits_[0] &= ~0x00000200u;
}
inline int32_t Init::_internal_num_reverse_output_channels() const {
  return num_reverse_output_channels_;
}
inline int32_t Init::num_reverse_output_channels() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Init.num_reverse_output_channels)
  return _internal_num_reverse_output_channels();
}
inline void Init::_internal_set_num_reverse_output_channels(int32_t value) {
  _has_bits_[0] |= 0x00000200u;
  num_reverse_output_channels_ = value;
}
inline void Init::set_num_reverse_output_channels(int32_t value) {
  _internal_set_num_reverse_output_channels(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Init.num_reverse_output_channels)
}

// optional int64 timestamp_ms = 10;
inline bool Init::_internal_has_timestamp_ms() const {
  bool value = (_has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool Init::has_timestamp_ms() const {
  return _internal_has_timestamp_ms();
}
inline void Init::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
  _has_bits_[0] &= ~0x00000100u;
}
inline int64_t Init::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t Init::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Init.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void Init::_internal_set_timestamp_ms(int64_t value) {
  _has_bits_[0] |= 0x00000100u;
  timestamp_ms_ = value;
}
inline void Init::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Init.timestamp_ms)
}

// -------------------------------------------------------------------

// ReverseStream

// optional bytes data = 1;
inline bool ReverseStream::_internal_has_data() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool ReverseStream::has_data() const {
  return _internal_has_data();
}
inline void ReverseStream::clear_data() {
  data_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& ReverseStream::data() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.ReverseStream.data)
  return _internal_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ReverseStream::set_data(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 data_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:webrtc.audioproc.ReverseStream.data)
}
inline std::string* ReverseStream::mutable_data() {
  std::string* _s = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:webrtc.audioproc.ReverseStream.data)
  return _s;
}
inline const std::string& ReverseStream::_internal_data() const {
  return data_.Get();
}
inline void ReverseStream::_internal_set_data(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  data_.Set(value, GetArenaForAllocation());
}
inline std::string* ReverseStream::_internal_mutable_data() {
  _has_bits_[0] |= 0x00000001u;
  return data_.Mutable(GetArenaForAllocation());
}
inline std::string* ReverseStream::release_data() {
  // @@protoc_insertion_point(field_release:webrtc.audioproc.ReverseStream.data)
  if (!_internal_has_data()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = data_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (data_.IsDefault()) {
    data_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void ReverseStream::set_allocated_data(std::string* data) {
  if (data != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  data_.SetAllocated(data, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (data_.IsDefault()) {
    data_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:webrtc.audioproc.ReverseStream.data)
}

// repeated bytes channel = 2;
inline int ReverseStream::_internal_channel_size() const {
  return channel_.size();
}
inline int ReverseStream::channel_size() const {
  return _internal_channel_size();
}
inline void ReverseStream::clear_channel() {
  channel_.Clear();
}
inline std::string* ReverseStream::add_channel() {
  std::string* _s = _internal_add_channel();
  // @@protoc_insertion_point(field_add_mutable:webrtc.audioproc.ReverseStream.channel)
  return _s;
}
inline const std::string& ReverseStream::_internal_channel(int index) const {
  return channel_.Get(index);
}
inline const std::string& ReverseStream::channel(int index) const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.ReverseStream.channel)
  return _internal_channel(index);
}
inline std::string* ReverseStream::mutable_channel(int index) {
  // @@protoc_insertion_point(field_mutable:webrtc.audioproc.ReverseStream.channel)
  return channel_.Mutable(index);
}
inline void ReverseStream::set_channel(int index, const std::string& value) {
  channel_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.ReverseStream.channel)
}
inline void ReverseStream::set_channel(int index, std::string&& value) {
  channel_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:webrtc.audioproc.ReverseStream.channel)
}
inline void ReverseStream::set_channel(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  channel_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:webrtc.audioproc.ReverseStream.channel)
}
inline void ReverseStream::set_channel(int index, const void* value, size_t size) {
  channel_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:webrtc.audioproc.ReverseStream.channel)
}
inline std::string* ReverseStream::_internal_add_channel() {
  return channel_.Add();
}
inline void ReverseStream::add_channel(const std::string& value) {
  channel_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:webrtc.audioproc.ReverseStream.channel)
}
inline void ReverseStream::add_channel(std::string&& value) {
  channel_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:webrtc.audioproc.ReverseStream.channel)
}
inline void ReverseStream::add_channel(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  channel_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:webrtc.audioproc.ReverseStream.channel)
}
inline void ReverseStream::add_channel(const void* value, size_t size) {
  channel_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:webrtc.audioproc.ReverseStream.channel)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ReverseStream::channel() const {
  // @@protoc_insertion_point(field_list:webrtc.audioproc.ReverseStream.channel)
  return channel_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ReverseStream::mutable_channel() {
  // @@protoc_insertion_point(field_mutable_list:webrtc.audioproc.ReverseStream.channel)
  return &channel_;
}

// -------------------------------------------------------------------

// Stream

// optional bytes input_data = 1;
inline bool Stream::_internal_has_input_data() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool Stream::has_input_data() const {
  return _internal_has_input_data();
}
inline void Stream::clear_input_data() {
  input_data_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& Stream::input_data() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Stream.input_data)
  return _internal_input_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Stream::set_input_data(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 input_data_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Stream.input_data)
}
inline std::string* Stream::mutable_input_data() {
  std::string* _s = _internal_mutable_input_data();
  // @@protoc_insertion_point(field_mutable:webrtc.audioproc.Stream.input_data)
  return _s;
}
inline const std::string& Stream::_internal_input_data() const {
  return input_data_.Get();
}
inline void Stream::_internal_set_input_data(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  input_data_.Set(value, GetArenaForAllocation());
}
inline std::string* Stream::_internal_mutable_input_data() {
  _has_bits_[0] |= 0x00000001u;
  return input_data_.Mutable(GetArenaForAllocation());
}
inline std::string* Stream::release_input_data() {
  // @@protoc_insertion_point(field_release:webrtc.audioproc.Stream.input_data)
  if (!_internal_has_input_data()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = input_data_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (input_data_.IsDefault()) {
    input_data_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Stream::set_allocated_input_data(std::string* input_data) {
  if (input_data != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  input_data_.SetAllocated(input_data, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (input_data_.IsDefault()) {
    input_data_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:webrtc.audioproc.Stream.input_data)
}

// optional bytes output_data = 2;
inline bool Stream::_internal_has_output_data() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool Stream::has_output_data() const {
  return _internal_has_output_data();
}
inline void Stream::clear_output_data() {
  output_data_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000002u;
}
inline const std::string& Stream::output_data() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Stream.output_data)
  return _internal_output_data();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Stream::set_output_data(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000002u;
 output_data_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Stream.output_data)
}
inline std::string* Stream::mutable_output_data() {
  std::string* _s = _internal_mutable_output_data();
  // @@protoc_insertion_point(field_mutable:webrtc.audioproc.Stream.output_data)
  return _s;
}
inline const std::string& Stream::_internal_output_data() const {
  return output_data_.Get();
}
inline void Stream::_internal_set_output_data(const std::string& value) {
  _has_bits_[0] |= 0x00000002u;
  output_data_.Set(value, GetArenaForAllocation());
}
inline std::string* Stream::_internal_mutable_output_data() {
  _has_bits_[0] |= 0x00000002u;
  return output_data_.Mutable(GetArenaForAllocation());
}
inline std::string* Stream::release_output_data() {
  // @@protoc_insertion_point(field_release:webrtc.audioproc.Stream.output_data)
  if (!_internal_has_output_data()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000002u;
  auto* p = output_data_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (output_data_.IsDefault()) {
    output_data_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Stream::set_allocated_output_data(std::string* output_data) {
  if (output_data != nullptr) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  output_data_.SetAllocated(output_data, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (output_data_.IsDefault()) {
    output_data_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:webrtc.audioproc.Stream.output_data)
}

// optional int32 delay = 3;
inline bool Stream::_internal_has_delay() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool Stream::has_delay() const {
  return _internal_has_delay();
}
inline void Stream::clear_delay() {
  delay_ = 0;
  _has_bits_[0] &= ~0x00000004u;
}
inline int32_t Stream::_internal_delay() const {
  return delay_;
}
inline int32_t Stream::delay() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Stream.delay)
  return _internal_delay();
}
inline void Stream::_internal_set_delay(int32_t value) {
  _has_bits_[0] |= 0x00000004u;
  delay_ = value;
}
inline void Stream::set_delay(int32_t value) {
  _internal_set_delay(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Stream.delay)
}

// optional sint32 drift = 4;
inline bool Stream::_internal_has_drift() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool Stream::has_drift() const {
  return _internal_has_drift();
}
inline void Stream::clear_drift() {
  drift_ = 0;
  _has_bits_[0] &= ~0x00000008u;
}
inline int32_t Stream::_internal_drift() const {
  return drift_;
}
inline int32_t Stream::drift() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Stream.drift)
  return _internal_drift();
}
inline void Stream::_internal_set_drift(int32_t value) {
  _has_bits_[0] |= 0x00000008u;
  drift_ = value;
}
inline void Stream::set_drift(int32_t value) {
  _internal_set_drift(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Stream.drift)
}

// optional int32 applied_input_volume = 5;
inline bool Stream::_internal_has_applied_input_volume() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool Stream::has_applied_input_volume() const {
  return _internal_has_applied_input_volume();
}
inline void Stream::clear_applied_input_volume() {
  applied_input_volume_ = 0;
  _has_bits_[0] &= ~0x00000010u;
}
inline int32_t Stream::_internal_applied_input_volume() const {
  return applied_input_volume_;
}
inline int32_t Stream::applied_input_volume() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Stream.applied_input_volume)
  return _internal_applied_input_volume();
}
inline void Stream::_internal_set_applied_input_volume(int32_t value) {
  _has_bits_[0] |= 0x00000010u;
  applied_input_volume_ = value;
}
inline void Stream::set_applied_input_volume(int32_t value) {
  _internal_set_applied_input_volume(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Stream.applied_input_volume)
}

// optional bool keypress = 6;
inline bool Stream::_internal_has_keypress() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool Stream::has_keypress() const {
  return _internal_has_keypress();
}
inline void Stream::clear_keypress() {
  keypress_ = false;
  _has_bits_[0] &= ~0x00000020u;
}
inline bool Stream::_internal_keypress() const {
  return keypress_;
}
inline bool Stream::keypress() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Stream.keypress)
  return _internal_keypress();
}
inline void Stream::_internal_set_keypress(bool value) {
  _has_bits_[0] |= 0x00000020u;
  keypress_ = value;
}
inline void Stream::set_keypress(bool value) {
  _internal_set_keypress(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Stream.keypress)
}

// repeated bytes input_channel = 7;
inline int Stream::_internal_input_channel_size() const {
  return input_channel_.size();
}
inline int Stream::input_channel_size() const {
  return _internal_input_channel_size();
}
inline void Stream::clear_input_channel() {
  input_channel_.Clear();
}
inline std::string* Stream::add_input_channel() {
  std::string* _s = _internal_add_input_channel();
  // @@protoc_insertion_point(field_add_mutable:webrtc.audioproc.Stream.input_channel)
  return _s;
}
inline const std::string& Stream::_internal_input_channel(int index) const {
  return input_channel_.Get(index);
}
inline const std::string& Stream::input_channel(int index) const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Stream.input_channel)
  return _internal_input_channel(index);
}
inline std::string* Stream::mutable_input_channel(int index) {
  // @@protoc_insertion_point(field_mutable:webrtc.audioproc.Stream.input_channel)
  return input_channel_.Mutable(index);
}
inline void Stream::set_input_channel(int index, const std::string& value) {
  input_channel_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Stream.input_channel)
}
inline void Stream::set_input_channel(int index, std::string&& value) {
  input_channel_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Stream.input_channel)
}
inline void Stream::set_input_channel(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  input_channel_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:webrtc.audioproc.Stream.input_channel)
}
inline void Stream::set_input_channel(int index, const void* value, size_t size) {
  input_channel_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:webrtc.audioproc.Stream.input_channel)
}
inline std::string* Stream::_internal_add_input_channel() {
  return input_channel_.Add();
}
inline void Stream::add_input_channel(const std::string& value) {
  input_channel_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:webrtc.audioproc.Stream.input_channel)
}
inline void Stream::add_input_channel(std::string&& value) {
  input_channel_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:webrtc.audioproc.Stream.input_channel)
}
inline void Stream::add_input_channel(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  input_channel_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:webrtc.audioproc.Stream.input_channel)
}
inline void Stream::add_input_channel(const void* value, size_t size) {
  input_channel_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:webrtc.audioproc.Stream.input_channel)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
Stream::input_channel() const {
  // @@protoc_insertion_point(field_list:webrtc.audioproc.Stream.input_channel)
  return input_channel_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
Stream::mutable_input_channel() {
  // @@protoc_insertion_point(field_mutable_list:webrtc.audioproc.Stream.input_channel)
  return &input_channel_;
}

// repeated bytes output_channel = 8;
inline int Stream::_internal_output_channel_size() const {
  return output_channel_.size();
}
inline int Stream::output_channel_size() const {
  return _internal_output_channel_size();
}
inline void Stream::clear_output_channel() {
  output_channel_.Clear();
}
inline std::string* Stream::add_output_channel() {
  std::string* _s = _internal_add_output_channel();
  // @@protoc_insertion_point(field_add_mutable:webrtc.audioproc.Stream.output_channel)
  return _s;
}
inline const std::string& Stream::_internal_output_channel(int index) const {
  return output_channel_.Get(index);
}
inline const std::string& Stream::output_channel(int index) const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Stream.output_channel)
  return _internal_output_channel(index);
}
inline std::string* Stream::mutable_output_channel(int index) {
  // @@protoc_insertion_point(field_mutable:webrtc.audioproc.Stream.output_channel)
  return output_channel_.Mutable(index);
}
inline void Stream::set_output_channel(int index, const std::string& value) {
  output_channel_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Stream.output_channel)
}
inline void Stream::set_output_channel(int index, std::string&& value) {
  output_channel_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Stream.output_channel)
}
inline void Stream::set_output_channel(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  output_channel_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:webrtc.audioproc.Stream.output_channel)
}
inline void Stream::set_output_channel(int index, const void* value, size_t size) {
  output_channel_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:webrtc.audioproc.Stream.output_channel)
}
inline std::string* Stream::_internal_add_output_channel() {
  return output_channel_.Add();
}
inline void Stream::add_output_channel(const std::string& value) {
  output_channel_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:webrtc.audioproc.Stream.output_channel)
}
inline void Stream::add_output_channel(std::string&& value) {
  output_channel_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:webrtc.audioproc.Stream.output_channel)
}
inline void Stream::add_output_channel(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  output_channel_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:webrtc.audioproc.Stream.output_channel)
}
inline void Stream::add_output_channel(const void* value, size_t size) {
  output_channel_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:webrtc.audioproc.Stream.output_channel)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
Stream::output_channel() const {
  // @@protoc_insertion_point(field_list:webrtc.audioproc.Stream.output_channel)
  return output_channel_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
Stream::mutable_output_channel() {
  // @@protoc_insertion_point(field_mutable_list:webrtc.audioproc.Stream.output_channel)
  return &output_channel_;
}

// -------------------------------------------------------------------

// Config

// optional bool aec_enabled = 1;
inline bool Config::_internal_has_aec_enabled() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool Config::has_aec_enabled() const {
  return _internal_has_aec_enabled();
}
inline void Config::clear_aec_enabled() {
  aec_enabled_ = false;
  _has_bits_[0] &= ~0x00000002u;
}
inline bool Config::_internal_aec_enabled() const {
  return aec_enabled_;
}
inline bool Config::aec_enabled() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.aec_enabled)
  return _internal_aec_enabled();
}
inline void Config::_internal_set_aec_enabled(bool value) {
  _has_bits_[0] |= 0x00000002u;
  aec_enabled_ = value;
}
inline void Config::set_aec_enabled(bool value) {
  _internal_set_aec_enabled(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.aec_enabled)
}

// optional bool aec_delay_agnostic_enabled = 2;
inline bool Config::_internal_has_aec_delay_agnostic_enabled() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool Config::has_aec_delay_agnostic_enabled() const {
  return _internal_has_aec_delay_agnostic_enabled();
}
inline void Config::clear_aec_delay_agnostic_enabled() {
  aec_delay_agnostic_enabled_ = false;
  _has_bits_[0] &= ~0x00000004u;
}
inline bool Config::_internal_aec_delay_agnostic_enabled() const {
  return aec_delay_agnostic_enabled_;
}
inline bool Config::aec_delay_agnostic_enabled() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.aec_delay_agnostic_enabled)
  return _internal_aec_delay_agnostic_enabled();
}
inline void Config::_internal_set_aec_delay_agnostic_enabled(bool value) {
  _has_bits_[0] |= 0x00000004u;
  aec_delay_agnostic_enabled_ = value;
}
inline void Config::set_aec_delay_agnostic_enabled(bool value) {
  _internal_set_aec_delay_agnostic_enabled(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.aec_delay_agnostic_enabled)
}

// optional bool aec_drift_compensation_enabled = 3;
inline bool Config::_internal_has_aec_drift_compensation_enabled() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool Config::has_aec_drift_compensation_enabled() const {
  return _internal_has_aec_drift_compensation_enabled();
}
inline void Config::clear_aec_drift_compensation_enabled() {
  aec_drift_compensation_enabled_ = false;
  _has_bits_[0] &= ~0x00000008u;
}
inline bool Config::_internal_aec_drift_compensation_enabled() const {
  return aec_drift_compensation_enabled_;
}
inline bool Config::aec_drift_compensation_enabled() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.aec_drift_compensation_enabled)
  return _internal_aec_drift_compensation_enabled();
}
inline void Config::_internal_set_aec_drift_compensation_enabled(bool value) {
  _has_bits_[0] |= 0x00000008u;
  aec_drift_compensation_enabled_ = value;
}
inline void Config::set_aec_drift_compensation_enabled(bool value) {
  _internal_set_aec_drift_compensation_enabled(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.aec_drift_compensation_enabled)
}

// optional bool aec_extended_filter_enabled = 4;
inline bool Config::_internal_has_aec_extended_filter_enabled() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool Config::has_aec_extended_filter_enabled() const {
  return _internal_has_aec_extended_filter_enabled();
}
inline void Config::clear_aec_extended_filter_enabled() {
  aec_extended_filter_enabled_ = false;
  _has_bits_[0] &= ~0x00000010u;
}
inline bool Config::_internal_aec_extended_filter_enabled() const {
  return aec_extended_filter_enabled_;
}
inline bool Config::aec_extended_filter_enabled() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.aec_extended_filter_enabled)
  return _internal_aec_extended_filter_enabled();
}
inline void Config::_internal_set_aec_extended_filter_enabled(bool value) {
  _has_bits_[0] |= 0x00000010u;
  aec_extended_filter_enabled_ = value;
}
inline void Config::set_aec_extended_filter_enabled(bool value) {
  _internal_set_aec_extended_filter_enabled(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.aec_extended_filter_enabled)
}

// optional int32 aec_suppression_level = 5;
inline bool Config::_internal_has_aec_suppression_level() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool Config::has_aec_suppression_level() const {
  return _internal_has_aec_suppression_level();
}
inline void Config::clear_aec_suppression_level() {
  aec_suppression_level_ = 0;
  _has_bits_[0] &= ~0x00000020u;
}
inline int32_t Config::_internal_aec_suppression_level() const {
  return aec_suppression_level_;
}
inline int32_t Config::aec_suppression_level() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.aec_suppression_level)
  return _internal_aec_suppression_level();
}
inline void Config::_internal_set_aec_suppression_level(int32_t value) {
  _has_bits_[0] |= 0x00000020u;
  aec_suppression_level_ = value;
}
inline void Config::set_aec_suppression_level(int32_t value) {
  _internal_set_aec_suppression_level(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.aec_suppression_level)
}

// optional bool aecm_enabled = 6;
inline bool Config::_internal_has_aecm_enabled() const {
  bool value = (_has_bits_[0] & 0x00000080u) != 0;
  return value;
}
inline bool Config::has_aecm_enabled() const {
  return _internal_has_aecm_enabled();
}
inline void Config::clear_aecm_enabled() {
  aecm_enabled_ = false;
  _has_bits_[0] &= ~0x00000080u;
}
inline bool Config::_internal_aecm_enabled() const {
  return aecm_enabled_;
}
inline bool Config::aecm_enabled() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.aecm_enabled)
  return _internal_aecm_enabled();
}
inline void Config::_internal_set_aecm_enabled(bool value) {
  _has_bits_[0] |= 0x00000080u;
  aecm_enabled_ = value;
}
inline void Config::set_aecm_enabled(bool value) {
  _internal_set_aecm_enabled(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.aecm_enabled)
}

// optional bool aecm_comfort_noise_enabled = 7 [deprecated = true];
inline bool Config::_internal_has_aecm_comfort_noise_enabled() const {
  bool value = (_has_bits_[0] & 0x00000100u) != 0;
  return value;
}
inline bool Config::has_aecm_comfort_noise_enabled() const {
  return _internal_has_aecm_comfort_noise_enabled();
}
inline void Config::clear_aecm_comfort_noise_enabled() {
  aecm_comfort_noise_enabled_ = false;
  _has_bits_[0] &= ~0x00000100u;
}
inline bool Config::_internal_aecm_comfort_noise_enabled() const {
  return aecm_comfort_noise_enabled_;
}
inline bool Config::aecm_comfort_noise_enabled() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.aecm_comfort_noise_enabled)
  return _internal_aecm_comfort_noise_enabled();
}
inline void Config::_internal_set_aecm_comfort_noise_enabled(bool value) {
  _has_bits_[0] |= 0x00000100u;
  aecm_comfort_noise_enabled_ = value;
}
inline void Config::set_aecm_comfort_noise_enabled(bool value) {
  _internal_set_aecm_comfort_noise_enabled(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.aecm_comfort_noise_enabled)
}

// optional int32 aecm_routing_mode = 8 [deprecated = true];
inline bool Config::_internal_has_aecm_routing_mode() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool Config::has_aecm_routing_mode() const {
  return _internal_has_aecm_routing_mode();
}
inline void Config::clear_aecm_routing_mode() {
  aecm_routing_mode_ = 0;
  _has_bits_[0] &= ~0x00000040u;
}
inline int32_t Config::_internal_aecm_routing_mode() const {
  return aecm_routing_mode_;
}
inline int32_t Config::aecm_routing_mode() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.aecm_routing_mode)
  return _internal_aecm_routing_mode();
}
inline void Config::_internal_set_aecm_routing_mode(int32_t value) {
  _has_bits_[0] |= 0x00000040u;
  aecm_routing_mode_ = value;
}
inline void Config::set_aecm_routing_mode(int32_t value) {
  _internal_set_aecm_routing_mode(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.aecm_routing_mode)
}

// optional bool agc_enabled = 9;
inline bool Config::_internal_has_agc_enabled() const {
  bool value = (_has_bits_[0] & 0x00000200u) != 0;
  return value;
}
inline bool Config::has_agc_enabled() const {
  return _internal_has_agc_enabled();
}
inline void Config::clear_agc_enabled() {
  agc_enabled_ = false;
  _has_bits_[0] &= ~0x00000200u;
}
inline bool Config::_internal_agc_enabled() const {
  return agc_enabled_;
}
inline bool Config::agc_enabled() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.agc_enabled)
  return _internal_agc_enabled();
}
inline void Config::_internal_set_agc_enabled(bool value) {
  _has_bits_[0] |= 0x00000200u;
  agc_enabled_ = value;
}
inline void Config::set_agc_enabled(bool value) {
  _internal_set_agc_enabled(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.agc_enabled)
}

// optional int32 agc_mode = 10;
inline bool Config::_internal_has_agc_mode() const {
  bool value = (_has_bits_[0] & 0x00000800u) != 0;
  return value;
}
inline bool Config::has_agc_mode() const {
  return _internal_has_agc_mode();
}
inline void Config::clear_agc_mode() {
  agc_mode_ = 0;
  _has_bits_[0] &= ~0x00000800u;
}
inline int32_t Config::_internal_agc_mode() const {
  return agc_mode_;
}
inline int32_t Config::agc_mode() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.agc_mode)
  return _internal_agc_mode();
}
inline void Config::_internal_set_agc_mode(int32_t value) {
  _has_bits_[0] |= 0x00000800u;
  agc_mode_ = value;
}
inline void Config::set_agc_mode(int32_t value) {
  _internal_set_agc_mode(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.agc_mode)
}

// optional bool agc_limiter_enabled = 11;
inline bool Config::_internal_has_agc_limiter_enabled() const {
  bool value = (_has_bits_[0] & 0x00000400u) != 0;
  return value;
}
inline bool Config::has_agc_limiter_enabled() const {
  return _internal_has_agc_limiter_enabled();
}
inline void Config::clear_agc_limiter_enabled() {
  agc_limiter_enabled_ = false;
  _has_bits_[0] &= ~0x00000400u;
}
inline bool Config::_internal_agc_limiter_enabled() const {
  return agc_limiter_enabled_;
}
inline bool Config::agc_limiter_enabled() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.agc_limiter_enabled)
  return _internal_agc_limiter_enabled();
}
inline void Config::_internal_set_agc_limiter_enabled(bool value) {
  _has_bits_[0] |= 0x00000400u;
  agc_limiter_enabled_ = value;
}
inline void Config::set_agc_limiter_enabled(bool value) {
  _internal_set_agc_limiter_enabled(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.agc_limiter_enabled)
}

// optional bool noise_robust_agc_enabled = 12;
inline bool Config::_internal_has_noise_robust_agc_enabled() const {
  bool value = (_has_bits_[0] & 0x00001000u) != 0;
  return value;
}
inline bool Config::has_noise_robust_agc_enabled() const {
  return _internal_has_noise_robust_agc_enabled();
}
inline void Config::clear_noise_robust_agc_enabled() {
  noise_robust_agc_enabled_ = false;
  _has_bits_[0] &= ~0x00001000u;
}
inline bool Config::_internal_noise_robust_agc_enabled() const {
  return noise_robust_agc_enabled_;
}
inline bool Config::noise_robust_agc_enabled() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.noise_robust_agc_enabled)
  return _internal_noise_robust_agc_enabled();
}
inline void Config::_internal_set_noise_robust_agc_enabled(bool value) {
  _has_bits_[0] |= 0x00001000u;
  noise_robust_agc_enabled_ = value;
}
inline void Config::set_noise_robust_agc_enabled(bool value) {
  _internal_set_noise_robust_agc_enabled(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.noise_robust_agc_enabled)
}

// optional bool hpf_enabled = 13;
inline bool Config::_internal_has_hpf_enabled() const {
  bool value = (_has_bits_[0] & 0x00002000u) != 0;
  return value;
}
inline bool Config::has_hpf_enabled() const {
  return _internal_has_hpf_enabled();
}
inline void Config::clear_hpf_enabled() {
  hpf_enabled_ = false;
  _has_bits_[0] &= ~0x00002000u;
}
inline bool Config::_internal_hpf_enabled() const {
  return hpf_enabled_;
}
inline bool Config::hpf_enabled() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.hpf_enabled)
  return _internal_hpf_enabled();
}
inline void Config::_internal_set_hpf_enabled(bool value) {
  _has_bits_[0] |= 0x00002000u;
  hpf_enabled_ = value;
}
inline void Config::set_hpf_enabled(bool value) {
  _internal_set_hpf_enabled(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.hpf_enabled)
}

// optional bool ns_enabled = 14;
inline bool Config::_internal_has_ns_enabled() const {
  bool value = (_has_bits_[0] & 0x00004000u) != 0;
  return value;
}
inline bool Config::has_ns_enabled() const {
  return _internal_has_ns_enabled();
}
inline void Config::clear_ns_enabled() {
  ns_enabled_ = false;
  _has_bits_[0] &= ~0x00004000u;
}
inline bool Config::_internal_ns_enabled() const {
  return ns_enabled_;
}
inline bool Config::ns_enabled() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.ns_enabled)
  return _internal_ns_enabled();
}
inline void Config::_internal_set_ns_enabled(bool value) {
  _has_bits_[0] |= 0x00004000u;
  ns_enabled_ = value;
}
inline void Config::set_ns_enabled(bool value) {
  _internal_set_ns_enabled(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.ns_enabled)
}

// optional int32 ns_level = 15;
inline bool Config::_internal_has_ns_level() const {
  bool value = (_has_bits_[0] & 0x00010000u) != 0;
  return value;
}
inline bool Config::has_ns_level() const {
  return _internal_has_ns_level();
}
inline void Config::clear_ns_level() {
  ns_level_ = 0;
  _has_bits_[0] &= ~0x00010000u;
}
inline int32_t Config::_internal_ns_level() const {
  return ns_level_;
}
inline int32_t Config::ns_level() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.ns_level)
  return _internal_ns_level();
}
inline void Config::_internal_set_ns_level(int32_t value) {
  _has_bits_[0] |= 0x00010000u;
  ns_level_ = value;
}
inline void Config::set_ns_level(int32_t value) {
  _internal_set_ns_level(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.ns_level)
}

// optional bool transient_suppression_enabled = 16;
inline bool Config::_internal_has_transient_suppression_enabled() const {
  bool value = (_has_bits_[0] & 0x00008000u) != 0;
  return value;
}
inline bool Config::has_transient_suppression_enabled() const {
  return _internal_has_transient_suppression_enabled();
}
inline void Config::clear_transient_suppression_enabled() {
  transient_suppression_enabled_ = false;
  _has_bits_[0] &= ~0x00008000u;
}
inline bool Config::_internal_transient_suppression_enabled() const {
  return transient_suppression_enabled_;
}
inline bool Config::transient_suppression_enabled() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.transient_suppression_enabled)
  return _internal_transient_suppression_enabled();
}
inline void Config::_internal_set_transient_suppression_enabled(bool value) {
  _has_bits_[0] |= 0x00008000u;
  transient_suppression_enabled_ = value;
}
inline void Config::set_transient_suppression_enabled(bool value) {
  _internal_set_transient_suppression_enabled(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.transient_suppression_enabled)
}

// optional string experiments_description = 17;
inline bool Config::_internal_has_experiments_description() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool Config::has_experiments_description() const {
  return _internal_has_experiments_description();
}
inline void Config::clear_experiments_description() {
  experiments_description_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& Config::experiments_description() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.experiments_description)
  return _internal_experiments_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Config::set_experiments_description(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 experiments_description_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.experiments_description)
}
inline std::string* Config::mutable_experiments_description() {
  std::string* _s = _internal_mutable_experiments_description();
  // @@protoc_insertion_point(field_mutable:webrtc.audioproc.Config.experiments_description)
  return _s;
}
inline const std::string& Config::_internal_experiments_description() const {
  return experiments_description_.Get();
}
inline void Config::_internal_set_experiments_description(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  experiments_description_.Set(value, GetArenaForAllocation());
}
inline std::string* Config::_internal_mutable_experiments_description() {
  _has_bits_[0] |= 0x00000001u;
  return experiments_description_.Mutable(GetArenaForAllocation());
}
inline std::string* Config::release_experiments_description() {
  // @@protoc_insertion_point(field_release:webrtc.audioproc.Config.experiments_description)
  if (!_internal_has_experiments_description()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = experiments_description_.Release();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (experiments_description_.IsDefault()) {
    experiments_description_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void Config::set_allocated_experiments_description(std::string* experiments_description) {
  if (experiments_description != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  experiments_description_.SetAllocated(experiments_description, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (experiments_description_.IsDefault()) {
    experiments_description_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:webrtc.audioproc.Config.experiments_description)
}

// optional bool pre_amplifier_enabled = 19;
inline bool Config::_internal_has_pre_amplifier_enabled() const {
  bool value = (_has_bits_[0] & 0x00020000u) != 0;
  return value;
}
inline bool Config::has_pre_amplifier_enabled() const {
  return _internal_has_pre_amplifier_enabled();
}
inline void Config::clear_pre_amplifier_enabled() {
  pre_amplifier_enabled_ = false;
  _has_bits_[0] &= ~0x00020000u;
}
inline bool Config::_internal_pre_amplifier_enabled() const {
  return pre_amplifier_enabled_;
}
inline bool Config::pre_amplifier_enabled() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.pre_amplifier_enabled)
  return _internal_pre_amplifier_enabled();
}
inline void Config::_internal_set_pre_amplifier_enabled(bool value) {
  _has_bits_[0] |= 0x00020000u;
  pre_amplifier_enabled_ = value;
}
inline void Config::set_pre_amplifier_enabled(bool value) {
  _internal_set_pre_amplifier_enabled(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.pre_amplifier_enabled)
}

// optional float pre_amplifier_fixed_gain_factor = 20;
inline bool Config::_internal_has_pre_amplifier_fixed_gain_factor() const {
  bool value = (_has_bits_[0] & 0x00040000u) != 0;
  return value;
}
inline bool Config::has_pre_amplifier_fixed_gain_factor() const {
  return _internal_has_pre_amplifier_fixed_gain_factor();
}
inline void Config::clear_pre_amplifier_fixed_gain_factor() {
  pre_amplifier_fixed_gain_factor_ = 0;
  _has_bits_[0] &= ~0x00040000u;
}
inline float Config::_internal_pre_amplifier_fixed_gain_factor() const {
  return pre_amplifier_fixed_gain_factor_;
}
inline float Config::pre_amplifier_fixed_gain_factor() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Config.pre_amplifier_fixed_gain_factor)
  return _internal_pre_amplifier_fixed_gain_factor();
}
inline void Config::_internal_set_pre_amplifier_fixed_gain_factor(float value) {
  _has_bits_[0] |= 0x00040000u;
  pre_amplifier_fixed_gain_factor_ = value;
}
inline void Config::set_pre_amplifier_fixed_gain_factor(float value) {
  _internal_set_pre_amplifier_fixed_gain_factor(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Config.pre_amplifier_fixed_gain_factor)
}

// -------------------------------------------------------------------

// PlayoutAudioDeviceInfo

// optional int32 id = 1;
inline bool PlayoutAudioDeviceInfo::_internal_has_id() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool PlayoutAudioDeviceInfo::has_id() const {
  return _internal_has_id();
}
inline void PlayoutAudioDeviceInfo::clear_id() {
  id_ = 0;
  _has_bits_[0] &= ~0x00000001u;
}
inline int32_t PlayoutAudioDeviceInfo::_internal_id() const {
  return id_;
}
inline int32_t PlayoutAudioDeviceInfo::id() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.PlayoutAudioDeviceInfo.id)
  return _internal_id();
}
inline void PlayoutAudioDeviceInfo::_internal_set_id(int32_t value) {
  _has_bits_[0] |= 0x00000001u;
  id_ = value;
}
inline void PlayoutAudioDeviceInfo::set_id(int32_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.PlayoutAudioDeviceInfo.id)
}

// optional int32 max_volume = 2;
inline bool PlayoutAudioDeviceInfo::_internal_has_max_volume() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool PlayoutAudioDeviceInfo::has_max_volume() const {
  return _internal_has_max_volume();
}
inline void PlayoutAudioDeviceInfo::clear_max_volume() {
  max_volume_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline int32_t PlayoutAudioDeviceInfo::_internal_max_volume() const {
  return max_volume_;
}
inline int32_t PlayoutAudioDeviceInfo::max_volume() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.PlayoutAudioDeviceInfo.max_volume)
  return _internal_max_volume();
}
inline void PlayoutAudioDeviceInfo::_internal_set_max_volume(int32_t value) {
  _has_bits_[0] |= 0x00000002u;
  max_volume_ = value;
}
inline void PlayoutAudioDeviceInfo::set_max_volume(int32_t value) {
  _internal_set_max_volume(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.PlayoutAudioDeviceInfo.max_volume)
}

// -------------------------------------------------------------------

// RuntimeSetting

// optional float capture_pre_gain = 1;
inline bool RuntimeSetting::_internal_has_capture_pre_gain() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool RuntimeSetting::has_capture_pre_gain() const {
  return _internal_has_capture_pre_gain();
}
inline void RuntimeSetting::clear_capture_pre_gain() {
  capture_pre_gain_ = 0;
  _has_bits_[0] &= ~0x00000002u;
}
inline float RuntimeSetting::_internal_capture_pre_gain() const {
  return capture_pre_gain_;
}
inline float RuntimeSetting::capture_pre_gain() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.RuntimeSetting.capture_pre_gain)
  return _internal_capture_pre_gain();
}
inline void RuntimeSetting::_internal_set_capture_pre_gain(float value) {
  _has_bits_[0] |= 0x00000002u;
  capture_pre_gain_ = value;
}
inline void RuntimeSetting::set_capture_pre_gain(float value) {
  _internal_set_capture_pre_gain(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.RuntimeSetting.capture_pre_gain)
}

// optional float custom_render_processing_setting = 2;
inline bool RuntimeSetting::_internal_has_custom_render_processing_setting() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  return value;
}
inline bool RuntimeSetting::has_custom_render_processing_setting() const {
  return _internal_has_custom_render_processing_setting();
}
inline void RuntimeSetting::clear_custom_render_processing_setting() {
  custom_render_processing_setting_ = 0;
  _has_bits_[0] &= ~0x00000004u;
}
inline float RuntimeSetting::_internal_custom_render_processing_setting() const {
  return custom_render_processing_setting_;
}
inline float RuntimeSetting::custom_render_processing_setting() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.RuntimeSetting.custom_render_processing_setting)
  return _internal_custom_render_processing_setting();
}
inline void RuntimeSetting::_internal_set_custom_render_processing_setting(float value) {
  _has_bits_[0] |= 0x00000004u;
  custom_render_processing_setting_ = value;
}
inline void RuntimeSetting::set_custom_render_processing_setting(float value) {
  _internal_set_custom_render_processing_setting(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.RuntimeSetting.custom_render_processing_setting)
}

// optional float capture_fixed_post_gain = 3;
inline bool RuntimeSetting::_internal_has_capture_fixed_post_gain() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  return value;
}
inline bool RuntimeSetting::has_capture_fixed_post_gain() const {
  return _internal_has_capture_fixed_post_gain();
}
inline void RuntimeSetting::clear_capture_fixed_post_gain() {
  capture_fixed_post_gain_ = 0;
  _has_bits_[0] &= ~0x00000008u;
}
inline float RuntimeSetting::_internal_capture_fixed_post_gain() const {
  return capture_fixed_post_gain_;
}
inline float RuntimeSetting::capture_fixed_post_gain() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.RuntimeSetting.capture_fixed_post_gain)
  return _internal_capture_fixed_post_gain();
}
inline void RuntimeSetting::_internal_set_capture_fixed_post_gain(float value) {
  _has_bits_[0] |= 0x00000008u;
  capture_fixed_post_gain_ = value;
}
inline void RuntimeSetting::set_capture_fixed_post_gain(float value) {
  _internal_set_capture_fixed_post_gain(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.RuntimeSetting.capture_fixed_post_gain)
}

// optional int32 playout_volume_change = 4;
inline bool RuntimeSetting::_internal_has_playout_volume_change() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  return value;
}
inline bool RuntimeSetting::has_playout_volume_change() const {
  return _internal_has_playout_volume_change();
}
inline void RuntimeSetting::clear_playout_volume_change() {
  playout_volume_change_ = 0;
  _has_bits_[0] &= ~0x00000010u;
}
inline int32_t RuntimeSetting::_internal_playout_volume_change() const {
  return playout_volume_change_;
}
inline int32_t RuntimeSetting::playout_volume_change() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.RuntimeSetting.playout_volume_change)
  return _internal_playout_volume_change();
}
inline void RuntimeSetting::_internal_set_playout_volume_change(int32_t value) {
  _has_bits_[0] |= 0x00000010u;
  playout_volume_change_ = value;
}
inline void RuntimeSetting::set_playout_volume_change(int32_t value) {
  _internal_set_playout_volume_change(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.RuntimeSetting.playout_volume_change)
}

// optional .webrtc.audioproc.PlayoutAudioDeviceInfo playout_audio_device_change = 5;
inline bool RuntimeSetting::_internal_has_playout_audio_device_change() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || playout_audio_device_change_ != nullptr);
  return value;
}
inline bool RuntimeSetting::has_playout_audio_device_change() const {
  return _internal_has_playout_audio_device_change();
}
inline void RuntimeSetting::clear_playout_audio_device_change() {
  if (playout_audio_device_change_ != nullptr) playout_audio_device_change_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::webrtc::audioproc::PlayoutAudioDeviceInfo& RuntimeSetting::_internal_playout_audio_device_change() const {
  const ::webrtc::audioproc::PlayoutAudioDeviceInfo* p = playout_audio_device_change_;
  return p != nullptr ? *p : reinterpret_cast<const ::webrtc::audioproc::PlayoutAudioDeviceInfo&>(
      ::webrtc::audioproc::_PlayoutAudioDeviceInfo_default_instance_);
}
inline const ::webrtc::audioproc::PlayoutAudioDeviceInfo& RuntimeSetting::playout_audio_device_change() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.RuntimeSetting.playout_audio_device_change)
  return _internal_playout_audio_device_change();
}
inline void RuntimeSetting::unsafe_arena_set_allocated_playout_audio_device_change(
    ::webrtc::audioproc::PlayoutAudioDeviceInfo* playout_audio_device_change) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(playout_audio_device_change_);
  }
  playout_audio_device_change_ = playout_audio_device_change;
  if (playout_audio_device_change) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.audioproc.RuntimeSetting.playout_audio_device_change)
}
inline ::webrtc::audioproc::PlayoutAudioDeviceInfo* RuntimeSetting::release_playout_audio_device_change() {
  _has_bits_[0] &= ~0x00000001u;
  ::webrtc::audioproc::PlayoutAudioDeviceInfo* temp = playout_audio_device_change_;
  playout_audio_device_change_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::webrtc::audioproc::PlayoutAudioDeviceInfo* RuntimeSetting::unsafe_arena_release_playout_audio_device_change() {
  // @@protoc_insertion_point(field_release:webrtc.audioproc.RuntimeSetting.playout_audio_device_change)
  _has_bits_[0] &= ~0x00000001u;
  ::webrtc::audioproc::PlayoutAudioDeviceInfo* temp = playout_audio_device_change_;
  playout_audio_device_change_ = nullptr;
  return temp;
}
inline ::webrtc::audioproc::PlayoutAudioDeviceInfo* RuntimeSetting::_internal_mutable_playout_audio_device_change() {
  _has_bits_[0] |= 0x00000001u;
  if (playout_audio_device_change_ == nullptr) {
    auto* p = CreateMaybeMessage<::webrtc::audioproc::PlayoutAudioDeviceInfo>(GetArenaForAllocation());
    playout_audio_device_change_ = p;
  }
  return playout_audio_device_change_;
}
inline ::webrtc::audioproc::PlayoutAudioDeviceInfo* RuntimeSetting::mutable_playout_audio_device_change() {
  ::webrtc::audioproc::PlayoutAudioDeviceInfo* _msg = _internal_mutable_playout_audio_device_change();
  // @@protoc_insertion_point(field_mutable:webrtc.audioproc.RuntimeSetting.playout_audio_device_change)
  return _msg;
}
inline void RuntimeSetting::set_allocated_playout_audio_device_change(::webrtc::audioproc::PlayoutAudioDeviceInfo* playout_audio_device_change) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete playout_audio_device_change_;
  }
  if (playout_audio_device_change) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(playout_audio_device_change);
    if (message_arena != submessage_arena) {
      playout_audio_device_change = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, playout_audio_device_change, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  playout_audio_device_change_ = playout_audio_device_change;
  // @@protoc_insertion_point(field_set_allocated:webrtc.audioproc.RuntimeSetting.playout_audio_device_change)
}

// optional bool capture_output_used = 6;
inline bool RuntimeSetting::_internal_has_capture_output_used() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool RuntimeSetting::has_capture_output_used() const {
  return _internal_has_capture_output_used();
}
inline void RuntimeSetting::clear_capture_output_used() {
  capture_output_used_ = false;
  _has_bits_[0] &= ~0x00000020u;
}
inline bool RuntimeSetting::_internal_capture_output_used() const {
  return capture_output_used_;
}
inline bool RuntimeSetting::capture_output_used() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.RuntimeSetting.capture_output_used)
  return _internal_capture_output_used();
}
inline void RuntimeSetting::_internal_set_capture_output_used(bool value) {
  _has_bits_[0] |= 0x00000020u;
  capture_output_used_ = value;
}
inline void RuntimeSetting::set_capture_output_used(bool value) {
  _internal_set_capture_output_used(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.RuntimeSetting.capture_output_used)
}

// optional float capture_post_gain = 7;
inline bool RuntimeSetting::_internal_has_capture_post_gain() const {
  bool value = (_has_bits_[0] & 0x00000040u) != 0;
  return value;
}
inline bool RuntimeSetting::has_capture_post_gain() const {
  return _internal_has_capture_post_gain();
}
inline void RuntimeSetting::clear_capture_post_gain() {
  capture_post_gain_ = 0;
  _has_bits_[0] &= ~0x00000040u;
}
inline float RuntimeSetting::_internal_capture_post_gain() const {
  return capture_post_gain_;
}
inline float RuntimeSetting::capture_post_gain() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.RuntimeSetting.capture_post_gain)
  return _internal_capture_post_gain();
}
inline void RuntimeSetting::_internal_set_capture_post_gain(float value) {
  _has_bits_[0] |= 0x00000040u;
  capture_post_gain_ = value;
}
inline void RuntimeSetting::set_capture_post_gain(float value) {
  _internal_set_capture_post_gain(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.RuntimeSetting.capture_post_gain)
}

// -------------------------------------------------------------------

// Event

// required .webrtc.audioproc.Event.Type type = 1;
inline bool Event::_internal_has_type() const {
  bool value = (_has_bits_[0] & 0x00000020u) != 0;
  return value;
}
inline bool Event::has_type() const {
  return _internal_has_type();
}
inline void Event::clear_type() {
  type_ = 0;
  _has_bits_[0] &= ~0x00000020u;
}
inline ::webrtc::audioproc::Event_Type Event::_internal_type() const {
  return static_cast< ::webrtc::audioproc::Event_Type >(type_);
}
inline ::webrtc::audioproc::Event_Type Event::type() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Event.type)
  return _internal_type();
}
inline void Event::_internal_set_type(::webrtc::audioproc::Event_Type value) {
  assert(::webrtc::audioproc::Event_Type_IsValid(value));
  _has_bits_[0] |= 0x00000020u;
  type_ = value;
}
inline void Event::set_type(::webrtc::audioproc::Event_Type value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:webrtc.audioproc.Event.type)
}

// optional .webrtc.audioproc.Init init = 2;
inline bool Event::_internal_has_init() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || init_ != nullptr);
  return value;
}
inline bool Event::has_init() const {
  return _internal_has_init();
}
inline void Event::clear_init() {
  if (init_ != nullptr) init_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::webrtc::audioproc::Init& Event::_internal_init() const {
  const ::webrtc::audioproc::Init* p = init_;
  return p != nullptr ? *p : reinterpret_cast<const ::webrtc::audioproc::Init&>(
      ::webrtc::audioproc::_Init_default_instance_);
}
inline const ::webrtc::audioproc::Init& Event::init() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Event.init)
  return _internal_init();
}
inline void Event::unsafe_arena_set_allocated_init(
    ::webrtc::audioproc::Init* init) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(init_);
  }
  init_ = init;
  if (init) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.audioproc.Event.init)
}
inline ::webrtc::audioproc::Init* Event::release_init() {
  _has_bits_[0] &= ~0x00000001u;
  ::webrtc::audioproc::Init* temp = init_;
  init_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::webrtc::audioproc::Init* Event::unsafe_arena_release_init() {
  // @@protoc_insertion_point(field_release:webrtc.audioproc.Event.init)
  _has_bits_[0] &= ~0x00000001u;
  ::webrtc::audioproc::Init* temp = init_;
  init_ = nullptr;
  return temp;
}
inline ::webrtc::audioproc::Init* Event::_internal_mutable_init() {
  _has_bits_[0] |= 0x00000001u;
  if (init_ == nullptr) {
    auto* p = CreateMaybeMessage<::webrtc::audioproc::Init>(GetArenaForAllocation());
    init_ = p;
  }
  return init_;
}
inline ::webrtc::audioproc::Init* Event::mutable_init() {
  ::webrtc::audioproc::Init* _msg = _internal_mutable_init();
  // @@protoc_insertion_point(field_mutable:webrtc.audioproc.Event.init)
  return _msg;
}
inline void Event::set_allocated_init(::webrtc::audioproc::Init* init) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete init_;
  }
  if (init) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(init);
    if (message_arena != submessage_arena) {
      init = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, init, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  init_ = init;
  // @@protoc_insertion_point(field_set_allocated:webrtc.audioproc.Event.init)
}

// optional .webrtc.audioproc.ReverseStream reverse_stream = 3;
inline bool Event::_internal_has_reverse_stream() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  PROTOBUF_ASSUME(!value || reverse_stream_ != nullptr);
  return value;
}
inline bool Event::has_reverse_stream() const {
  return _internal_has_reverse_stream();
}
inline void Event::clear_reverse_stream() {
  if (reverse_stream_ != nullptr) reverse_stream_->Clear();
  _has_bits_[0] &= ~0x00000002u;
}
inline const ::webrtc::audioproc::ReverseStream& Event::_internal_reverse_stream() const {
  const ::webrtc::audioproc::ReverseStream* p = reverse_stream_;
  return p != nullptr ? *p : reinterpret_cast<const ::webrtc::audioproc::ReverseStream&>(
      ::webrtc::audioproc::_ReverseStream_default_instance_);
}
inline const ::webrtc::audioproc::ReverseStream& Event::reverse_stream() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Event.reverse_stream)
  return _internal_reverse_stream();
}
inline void Event::unsafe_arena_set_allocated_reverse_stream(
    ::webrtc::audioproc::ReverseStream* reverse_stream) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(reverse_stream_);
  }
  reverse_stream_ = reverse_stream;
  if (reverse_stream) {
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.audioproc.Event.reverse_stream)
}
inline ::webrtc::audioproc::ReverseStream* Event::release_reverse_stream() {
  _has_bits_[0] &= ~0x00000002u;
  ::webrtc::audioproc::ReverseStream* temp = reverse_stream_;
  reverse_stream_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::webrtc::audioproc::ReverseStream* Event::unsafe_arena_release_reverse_stream() {
  // @@protoc_insertion_point(field_release:webrtc.audioproc.Event.reverse_stream)
  _has_bits_[0] &= ~0x00000002u;
  ::webrtc::audioproc::ReverseStream* temp = reverse_stream_;
  reverse_stream_ = nullptr;
  return temp;
}
inline ::webrtc::audioproc::ReverseStream* Event::_internal_mutable_reverse_stream() {
  _has_bits_[0] |= 0x00000002u;
  if (reverse_stream_ == nullptr) {
    auto* p = CreateMaybeMessage<::webrtc::audioproc::ReverseStream>(GetArenaForAllocation());
    reverse_stream_ = p;
  }
  return reverse_stream_;
}
inline ::webrtc::audioproc::ReverseStream* Event::mutable_reverse_stream() {
  ::webrtc::audioproc::ReverseStream* _msg = _internal_mutable_reverse_stream();
  // @@protoc_insertion_point(field_mutable:webrtc.audioproc.Event.reverse_stream)
  return _msg;
}
inline void Event::set_allocated_reverse_stream(::webrtc::audioproc::ReverseStream* reverse_stream) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reverse_stream_;
  }
  if (reverse_stream) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(reverse_stream);
    if (message_arena != submessage_arena) {
      reverse_stream = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, reverse_stream, submessage_arena);
    }
    _has_bits_[0] |= 0x00000002u;
  } else {
    _has_bits_[0] &= ~0x00000002u;
  }
  reverse_stream_ = reverse_stream;
  // @@protoc_insertion_point(field_set_allocated:webrtc.audioproc.Event.reverse_stream)
}

// optional .webrtc.audioproc.Stream stream = 4;
inline bool Event::_internal_has_stream() const {
  bool value = (_has_bits_[0] & 0x00000004u) != 0;
  PROTOBUF_ASSUME(!value || stream_ != nullptr);
  return value;
}
inline bool Event::has_stream() const {
  return _internal_has_stream();
}
inline void Event::clear_stream() {
  if (stream_ != nullptr) stream_->Clear();
  _has_bits_[0] &= ~0x00000004u;
}
inline const ::webrtc::audioproc::Stream& Event::_internal_stream() const {
  const ::webrtc::audioproc::Stream* p = stream_;
  return p != nullptr ? *p : reinterpret_cast<const ::webrtc::audioproc::Stream&>(
      ::webrtc::audioproc::_Stream_default_instance_);
}
inline const ::webrtc::audioproc::Stream& Event::stream() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Event.stream)
  return _internal_stream();
}
inline void Event::unsafe_arena_set_allocated_stream(
    ::webrtc::audioproc::Stream* stream) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(stream_);
  }
  stream_ = stream;
  if (stream) {
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.audioproc.Event.stream)
}
inline ::webrtc::audioproc::Stream* Event::release_stream() {
  _has_bits_[0] &= ~0x00000004u;
  ::webrtc::audioproc::Stream* temp = stream_;
  stream_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::webrtc::audioproc::Stream* Event::unsafe_arena_release_stream() {
  // @@protoc_insertion_point(field_release:webrtc.audioproc.Event.stream)
  _has_bits_[0] &= ~0x00000004u;
  ::webrtc::audioproc::Stream* temp = stream_;
  stream_ = nullptr;
  return temp;
}
inline ::webrtc::audioproc::Stream* Event::_internal_mutable_stream() {
  _has_bits_[0] |= 0x00000004u;
  if (stream_ == nullptr) {
    auto* p = CreateMaybeMessage<::webrtc::audioproc::Stream>(GetArenaForAllocation());
    stream_ = p;
  }
  return stream_;
}
inline ::webrtc::audioproc::Stream* Event::mutable_stream() {
  ::webrtc::audioproc::Stream* _msg = _internal_mutable_stream();
  // @@protoc_insertion_point(field_mutable:webrtc.audioproc.Event.stream)
  return _msg;
}
inline void Event::set_allocated_stream(::webrtc::audioproc::Stream* stream) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete stream_;
  }
  if (stream) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(stream);
    if (message_arena != submessage_arena) {
      stream = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, stream, submessage_arena);
    }
    _has_bits_[0] |= 0x00000004u;
  } else {
    _has_bits_[0] &= ~0x00000004u;
  }
  stream_ = stream;
  // @@protoc_insertion_point(field_set_allocated:webrtc.audioproc.Event.stream)
}

// optional .webrtc.audioproc.Config config = 5;
inline bool Event::_internal_has_config() const {
  bool value = (_has_bits_[0] & 0x00000008u) != 0;
  PROTOBUF_ASSUME(!value || config_ != nullptr);
  return value;
}
inline bool Event::has_config() const {
  return _internal_has_config();
}
inline void Event::clear_config() {
  if (config_ != nullptr) config_->Clear();
  _has_bits_[0] &= ~0x00000008u;
}
inline const ::webrtc::audioproc::Config& Event::_internal_config() const {
  const ::webrtc::audioproc::Config* p = config_;
  return p != nullptr ? *p : reinterpret_cast<const ::webrtc::audioproc::Config&>(
      ::webrtc::audioproc::_Config_default_instance_);
}
inline const ::webrtc::audioproc::Config& Event::config() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Event.config)
  return _internal_config();
}
inline void Event::unsafe_arena_set_allocated_config(
    ::webrtc::audioproc::Config* config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(config_);
  }
  config_ = config;
  if (config) {
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.audioproc.Event.config)
}
inline ::webrtc::audioproc::Config* Event::release_config() {
  _has_bits_[0] &= ~0x00000008u;
  ::webrtc::audioproc::Config* temp = config_;
  config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::webrtc::audioproc::Config* Event::unsafe_arena_release_config() {
  // @@protoc_insertion_point(field_release:webrtc.audioproc.Event.config)
  _has_bits_[0] &= ~0x00000008u;
  ::webrtc::audioproc::Config* temp = config_;
  config_ = nullptr;
  return temp;
}
inline ::webrtc::audioproc::Config* Event::_internal_mutable_config() {
  _has_bits_[0] |= 0x00000008u;
  if (config_ == nullptr) {
    auto* p = CreateMaybeMessage<::webrtc::audioproc::Config>(GetArenaForAllocation());
    config_ = p;
  }
  return config_;
}
inline ::webrtc::audioproc::Config* Event::mutable_config() {
  ::webrtc::audioproc::Config* _msg = _internal_mutable_config();
  // @@protoc_insertion_point(field_mutable:webrtc.audioproc.Event.config)
  return _msg;
}
inline void Event::set_allocated_config(::webrtc::audioproc::Config* config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete config_;
  }
  if (config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(config);
    if (message_arena != submessage_arena) {
      config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, config, submessage_arena);
    }
    _has_bits_[0] |= 0x00000008u;
  } else {
    _has_bits_[0] &= ~0x00000008u;
  }
  config_ = config;
  // @@protoc_insertion_point(field_set_allocated:webrtc.audioproc.Event.config)
}

// optional .webrtc.audioproc.RuntimeSetting runtime_setting = 6;
inline bool Event::_internal_has_runtime_setting() const {
  bool value = (_has_bits_[0] & 0x00000010u) != 0;
  PROTOBUF_ASSUME(!value || runtime_setting_ != nullptr);
  return value;
}
inline bool Event::has_runtime_setting() const {
  return _internal_has_runtime_setting();
}
inline void Event::clear_runtime_setting() {
  if (runtime_setting_ != nullptr) runtime_setting_->Clear();
  _has_bits_[0] &= ~0x00000010u;
}
inline const ::webrtc::audioproc::RuntimeSetting& Event::_internal_runtime_setting() const {
  const ::webrtc::audioproc::RuntimeSetting* p = runtime_setting_;
  return p != nullptr ? *p : reinterpret_cast<const ::webrtc::audioproc::RuntimeSetting&>(
      ::webrtc::audioproc::_RuntimeSetting_default_instance_);
}
inline const ::webrtc::audioproc::RuntimeSetting& Event::runtime_setting() const {
  // @@protoc_insertion_point(field_get:webrtc.audioproc.Event.runtime_setting)
  return _internal_runtime_setting();
}
inline void Event::unsafe_arena_set_allocated_runtime_setting(
    ::webrtc::audioproc::RuntimeSetting* runtime_setting) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(runtime_setting_);
  }
  runtime_setting_ = runtime_setting;
  if (runtime_setting) {
    _has_bits_[0] |= 0x00000010u;
  } else {
    _has_bits_[0] &= ~0x00000010u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:webrtc.audioproc.Event.runtime_setting)
}
inline ::webrtc::audioproc::RuntimeSetting* Event::release_runtime_setting() {
  _has_bits_[0] &= ~0x00000010u;
  ::webrtc::audioproc::RuntimeSetting* temp = runtime_setting_;
  runtime_setting_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::webrtc::audioproc::RuntimeSetting* Event::unsafe_arena_release_runtime_setting() {
  // @@protoc_insertion_point(field_release:webrtc.audioproc.Event.runtime_setting)
  _has_bits_[0] &= ~0x00000010u;
  ::webrtc::audioproc::RuntimeSetting* temp = runtime_setting_;
  runtime_setting_ = nullptr;
  return temp;
}
inline ::webrtc::audioproc::RuntimeSetting* Event::_internal_mutable_runtime_setting() {
  _has_bits_[0] |= 0x00000010u;
  if (runtime_setting_ == nullptr) {
    auto* p = CreateMaybeMessage<::webrtc::audioproc::RuntimeSetting>(GetArenaForAllocation());
    runtime_setting_ = p;
  }
  return runtime_setting_;
}
inline ::webrtc::audioproc::RuntimeSetting* Event::mutable_runtime_setting() {
  ::webrtc::audioproc::RuntimeSetting* _msg = _internal_mutable_runtime_setting();
  // @@protoc_insertion_point(field_mutable:webrtc.audioproc.Event.runtime_setting)
  return _msg;
}
inline void Event::set_allocated_runtime_setting(::webrtc::audioproc::RuntimeSetting* runtime_setting) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete runtime_setting_;
  }
  if (runtime_setting) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(runtime_setting);
    if (message_arena != submessage_arena) {
      runtime_setting = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, runtime_setting, submessage_arena);
    }
    _has_bits_[0] |= 0x00000010u;
  } else {
    _has_bits_[0] &= ~0x00000010u;
  }
  runtime_setting_ = runtime_setting;
  // @@protoc_insertion_point(field_set_allocated:webrtc.audioproc.Event.runtime_setting)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace audioproc
}  // namespace webrtc

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::webrtc::audioproc::Event_Type> : ::std::true_type {};

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_debug_2eproto
