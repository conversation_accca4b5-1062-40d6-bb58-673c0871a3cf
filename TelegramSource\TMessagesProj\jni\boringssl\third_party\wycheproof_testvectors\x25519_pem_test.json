{"algorithm": "XDH", "generatorVersion": "0.8rc14", "numberOfTests": 518, "header": ["Test vectors of type XdhPemComp are intended for verifying XDH."], "notes": {"LowOrderPublic": "The curves and its twists contain some points of low order. This test vector contains a public key with such a point. While many libraries reject such public keys, doing so is not a strict requirement according to RFC 7748.", "NonCanonicalPublic": "The public key is in non-canonical form. RFC 7748, section 5 defines the value that this public key represents. Section 7 of the same RFC recommends accepting such keys. If a non-canonical key is accepted then it must follow the RFC.", "SmallPublicKey": "The public key is insecure and does not belong to a valid private key. Some libraries reject such keys.", "Twist": "Public keys are either points on a given curve or points on its twist. The functions X25519 and X448 are defined for points on a twist with the goal that the output of computations do not leak private keys. Implementations may accept or reject points on a twist. If a point multiplication is performed then it is important that the result is correct, since otherwise attacks with invalid keys are possible.", "ZeroSharedSecret": "Some libraries include a check that the shared secret is not all-zero. This check is described in Section 6.1 of RFC 7748. "}, "schema": "xdh_pem_comp_schema.json", "testGroups": [{"curve": "curve25519", "type": "XdhPemComp", "tests": [{"tcId": 1, "comment": "normal case", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAUEo2mZ9InNL9vAi6/z2I+gBWm6mGy6IlSP/egPmAaCk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMip1akQka2FHGaLBzbByaApNsDTrWJnCFgIgEe6BXR1\n-----END PRIVATE KEY-----", "shared": "436a2c040cf45fea9b29a0cb81b1f41458f863d0d61b453d0a982720d6d61320", "result": "valid", "flags": []}, {"tcId": 2, "comment": "public key on twist", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAY6pAxuODRsXK8jpt8KXmyAiJoIZH5VGzVjRJvvz8lzM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINhdjAYaUIBKxIitd0rHFsP1unFLJxLgSEkTeaUAIRlY\n-----END PRIVATE KEY-----", "shared": "279df67a7c4611db4708a0e8282b195e5ac0ed6f4b2f292c6fbd0acac30d1332", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 3, "comment": "public key on twist", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAD4PDb97Z0y+t9O+jrpOpC7XPpmiTvEEsQ/pyh9u5l3k=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMi0W/0y5VMl2f1kjLMChIA5AAs5DkTVIeWKqzspppZL\n-----END PRIVATE KEY-----", "shared": "4bc7e01e7d83d6cf67632bf90033487a5fc29eba5328890ea7b1026d23b9a45f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 4, "comment": "public key on twist", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAC4IRorYEkJf2hxxsBS08X8G6F9qeMq5FhAOwW7KDCSo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPh240vL4fR/vA/d/XweGqU9V7/g9m0kMGe0JLtiEL5R\n-----END PRIVATE KEY-----", "shared": "119d37ed4b109cbd6418b1f28dea83c836c844715cdf98a3a8c362191debd514", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 5, "comment": "public key on twist", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEANDrCCjucaiexAIF2UJrTBzWFbsHI2PyuE5EtCNFS9Gw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIABqwfOmU6TNsdN7upRzj4uVele+sk1kbplNwponaq1F\n-----END PRIVATE KEY-----", "shared": "cc4873aed3fcee4b3aaea7f0d20716b4276359081f634b7bea4b705bfc8a4d3e", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 6, "comment": "public key on twist", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA+mlfx76NG+W/cEiY84jEUrr907jq6AX4aBqNFcLU4UI=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIAjad7JtBt/52ff9TFs3afjN1bMFFqWrgGvjJP8+tp5g\n-----END PRIVATE KEY-----", "shared": "b6f8e2fcb1affc79e2ff798319b2701139b95ad6dd07f05cbac78bd83edfd92e", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 7, "comment": "public key on twist", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINA+3enz57eZBF+aw3k9SpJ32t6txBvsApD4H3RPc3df\n-----END PRIVATE KEY-----", "shared": "b87a1722cc6c1e2feecb54e97abd5a22acc27616f78f6e315fd2b73d9f221e57", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 8, "comment": "public key on twist", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOCdV6kU48KQNv2aRCulJrXNzfKCFhU+Y2wQZ3rKtr1q\n-----END PRIVATE KEY-----", "shared": "a29d8dad28d590cd3017aa97a4761f851bf1d3672b042a4256a45881e2ad9035", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 9, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA/wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIODteObuAvCL7BwV1m+75bg//DfqFOFRLMG9Sy6m2AZv\n-----END PRIVATE KEY-----", "shared": "e703bc8aa94b7d87ba34e2678353d12cdaaa1a97b5ca3e1b8c060c4636087f07", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 10, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA//8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKihouyfqZFa56rOajfGhZHTnhWZXE7169NWHAL3LdpB\n-----END PRIVATE KEY-----", "shared": "ff5cf041e924dbe1a64ac9bdba96bdcdfaf7d59d91c7e33e76ed0e4c8c836446", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 11, "comment": "public key on twist", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKjJ31gg6zmdRx36MhXZYFWzx9D06kn4qwKNam4xlFF7\n-----END PRIVATE KEY-----", "shared": "a92a96fa029960f9530e6fe37e2429cd113be4d8f3f4431f8546e6c76351475d", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 12, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA////DwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINDTHEkcvTknGFm0pjoxaCZQex24xwFwn9D/4+shxEZ8\n-----END PRIVATE KEY-----", "shared": "9f8954868158ec62b6b586b8cae1d67d1b9f4c03d5b3ca0393cee71accc9ab65", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 13, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA/////wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINBT578ZAmGc1hycc54J1UxBR/RtGQcglm994dnP+71O\n-----END PRIVATE KEY-----", "shared": "6cbf1dc9af97bc148513a18be4a257de1a3b065584df94e8b43c1ab89720b110", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 14, "comment": "public key on twist", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKAh11AJpFluWjPxKSHBDzZwkzvIDd47uiKIG2EgWCFE\n-----END PRIVATE KEY-----", "shared": "38284b7086095a9406028c1f800c071ea106039ad7a1d7f82fe00906fd90594b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 15, "comment": "public key on twist", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKicZof5m9VpoB/YvUOCNhYNFc4sV8HXHrqj8tqIIzhj\n-----END PRIVATE KEY-----", "shared": "c721041df0244071794a8db06b9f7eaeec690c257265343666f4416f4166840f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 16, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA//////////8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGiWS8pRRlvw9bpSSxSCzv8Olgoe2fSNzDDxYI0OUBpQ\n-----END PRIVATE KEY-----", "shared": "25ff9a6631b143dbdbdc207b38e38f832ae079a52a618c534322e77345fd9049", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 17, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKjla7E6nyszuOZ1C0puZiHcJq6MXGJKCZLI8NW5EPFw\n-----END PRIVATE KEY-----", "shared": "f294e7922c6cea587aefe72911630d50f2456a2ba7f21207d57f1ecce04f6213", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 18, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\n<PERSON>owBQYDK2VuAyEA/////////////////////////////////////wAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOBF9VwVlFHpeBTXRwUP13ab1HhDSgGHalblU/ZjhKdM\n-----END PRIVATE KEY-----", "shared": "ff4715bd8cf847b77c244ce2d9b008b19efaa8e845feb85ce4889b5b2c6a4b4d", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 19, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA////AwAA+P//HwAAwP///wAAAP7//wcAAPD//z8AAAA=\n-----<PERSON>ND PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBBdYh4e8znD2ZJFz7d806W9DEQnoOTYdSw7UfBFiJtP\n-----END PRIVATE KEY-----", "shared": "61eace52da5f5ecefafa4f199b077ff64f2e3d2a6ece6f8ec0497826b212ef5f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 20, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\n<PERSON>owBQYDK2VuAyEA//////////////////////////////////////9/AAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINiKRB5wb2Bq5/Yw+LIfPCVUc54+VJ+AQRjAN3H2CAF7\n-----END PRIVATE KEY-----", "shared": "ff1b509a0a1a54726086f1e1c0acf040ab463a2a542e5d54e92c6df8126cf636", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 21, "comment": "public key on twist", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIC7rRaCIidiAKr9NvfyX9wCVjLYv59jVLt2Lgb7Y+JQ\n-----END PRIVATE KEY-----", "shared": "f134e6267bf93903085117b99932cc0c7ba26f25fca12102a26d7533d9c4272a", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 22, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA/////////////////////////////////////////x8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGjhNAkulOYiyKDNGK/1W+I9q9mU697pgtkGAfbw9LNp\n-----END PRIVATE KEY-----", "shared": "74bfc15e5597e9f5193f941e10a5c008fc89f051392723886a4a8fe5093a7354", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 23, "comment": "public key on twist", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOjkP8HrrAu8m5nIA17hrFm5DxmhbELAuQ+WrfzF/e54\n-----END PRIVATE KEY-----", "shared": "0d41a5b3af770bf2fcd34ff7972243a0e2cf4d34f2046a144581ae1ec68df03b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 24, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAA/P//BwAA4P//PwAAAP///wEAAPj//w8AAMD//38=\n-----<PERSON><PERSON> PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBi/+xb5JoCp4mdHPkPEZEdtU3Ld0fZk89Bnjv58mLx5\n-----END PRIVATE KEY-----", "shared": "5894e0963583ae14a0b80420894167f4b759c8d2eb9b69cb675543f66510f646", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 25, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>2VuAyEA/////////wAAAAAAAAD/////////AAAAAAAAAP///38=\n-----<PERSON><PERSON> PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDADBesAK/hscf6cCzEZk3J7ncYY0M5yUdDf2FUtF5Bd\n-----END PRIVATE KEY-----", "shared": "f8624d6e35e6c548ac47832f2e5d151a8e53b9290363b28d2ab8d84ab7cb6a72", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 26, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\n<PERSON>owBQYDK2VuAyEAAAAAAP////8AAAAA/////wAAAAD/////AAAAAP///38=\n-----<PERSON><PERSON> PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIDanwKEIkfUreXdusUdvOVep9yihE5/l6uJh85/2Lxx\n-----END PRIVATE KEY-----", "shared": "bfe183ba3d4157a7b53ef178613db619e27800f85359c0b39a9fd6e32152c208", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 27, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f/////////////////+/////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIBufybKMkbegYKUbL7Qn1K5XaYmyCPHtQRQABpHt7JS\n-----END PRIVATE KEY-----", "shared": "bca4a0724f5c1feb184078448c898c8620e7caf81f64cca746f557dff2498859", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 28, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f////////7//////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFg1T9ZLwCLLo6cbKuZCgeTqe/bWX9uurRRA7rGGBP5i\n-----END PRIVATE KEY-----", "shared": "b3418a52464c15ab0cacbbd43887a1199206d59229ced49202300638d7a40f04", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 29, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f//////7////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPABnPBRWXlMyAUrAMLnW39G+2aTxLOMArEqT+Jy6FVq\n-----END PRIVATE KEY-----", "shared": "fcde6e0a3d5fd5b63f10c2d3aad4efa05196f26bc0cb26fd6d9d3bd015eaa74f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 30, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f7//////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIND8pkzF86DI51yCTosJ0WFap5rroTm7cwLiuy/L5UtA\n-----END PRIVATE KEY-----", "shared": "7d62f189444c6231a48afab10a0af2eee4a52e431ea05ff781d616af2114672f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 31, "comment": "public key on twist", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA6v///////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINAkVuRWkR08bNBUkzGZgHcy39yVhkKtGuvpAMeTvvJK\n-----END PRIVATE KEY-----", "shared": "07ba5fcbda21a9a17845c401492b10e6de0a168d5c94b606694c11bac39bea41", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 32, "comment": "public key = 0", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIgidJQDjyu4EdR4BbzfBKKsWFrafy8jOJv9Rlj53dRe\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["SmallPublicKey", "LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 33, "comment": "public key = 1", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEgjLolythx+YZMOuUULUHDq4cZwR1aFVB8EdiF+SBhP\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["SmallPublicKey", "LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 34, "comment": "edge case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKg4b38WxQcx1k+C5qFwsUKk408x/Xdo/LiQKSXn0eJa\n-----END PRIVATE KEY-----", "shared": "34b7e4fa53264420d9f943d15513902342b386b172a0b0b7c8b8f2dd3d669f59", "result": "valid", "flags": []}, {"tcId": 35, "comment": "edge case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINBavQi/XmJTjLml7RBdvt1t440HlACFBytDEcJnjtd9\n-----END PRIVATE KEY-----", "shared": "3aa227a30781ed746bd4b3365e5f61461b844d09410c70570abd0d75574dfc77", "result": "valid", "flags": []}, {"tcId": 36, "comment": "edge case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPC4sJmMg5Q2TX3LJaOIXlcTdPkWFSdUQNsGRe58Cm9r\n-----END PRIVATE KEY-----", "shared": "97755e7e775789184e176847ffbc2f8ef98799d46a709c6a1c0ffd29081d7039", "result": "valid", "flags": []}, {"tcId": 37, "comment": "edge case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINAMNdwXRg82C/rnuUZHvE6aetnOgqvq21Ci8aBzbiF1\n-----END PRIVATE KEY-----", "shared": "c212bfceb91f8588d46cd94684c2c9ee0734087796dc0a9f3404ff534012123d", "result": "valid", "flags": []}, {"tcId": 38, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA////////DwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDhfyAWJAKhQId2SQl0vs5pi1OI67x1RBMTC2IcS055N\n-----END PRIVATE KEY-----", "shared": "388faffb4a85d06702ba3e479c6b216a8f33efce0542979bf129d860f93b9f02", "result": "valid", "flags": []}, {"tcId": 39, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA/////////wAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOBhSwxAivJNnSTApy+RN/vWsW8CzMlHl+o5casWBzp/\n-----END PRIVATE KEY-----", "shared": "877fec0669d8c1a5c866641420eea9f6bd1dfd38d36a5d55a8c0ab2bf3105c68", "result": "valid", "flags": []}, {"tcId": 40, "comment": "edge case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPAEuP0F2f/9hTzcbSJmOJtzfo38KWrQC1ppsqnc9ylW\n-----END PRIVATE KEY-----", "shared": "180373ea0f23ea73447e5a90398a97d490b541c69320719d7dd733fb80d5480f", "result": "valid", "flags": []}, {"tcId": 41, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\n<PERSON>owBQ<PERSON>DK2VuAyEA//////////////////8AAAAAAAAAAAAAAAAAAAAAAAA=\n-----<PERSON><PERSON> PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOgL8OYJvzsDW1Uvnbfp7LxEoEt5ELFJNmGlJPRsPCJ3\n-----END PRIVATE KEY-----", "shared": "208142350af938aba52a156dce19d3c27ab1628729683cf4ef2667c3dc60cf38", "result": "valid", "flags": []}, {"tcId": 42, "comment": "edge case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEiJDpXRsD5gO8tR/fbylvHx0Q9d8Q4AuKJcmAn5qhpU\n-----END PRIVATE KEY-----", "shared": "1c3263890f7a081cefe50cb92abd496582d90dcc2b9cb858bd286854aa6b0a7e", "result": "valid", "flags": []}, {"tcId": 43, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\n<PERSON>owB<PERSON><PERSON>DK2VuAyEA/////////////////////wAAAAAAAAAAAAAAAAAAAAA=\n-----<PERSON><PERSON> PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKgG8eObdCYVp93jspQV7YJ8aPB9Skek2VlcQMf8y5Jj\n-----END PRIVATE KEY-----", "shared": "56128e78d7c66f48e863e7e6f2caa9c0988fd439deac11d4aac9664083087f7a", "result": "valid", "flags": []}, {"tcId": 44, "comment": "edge case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJiZ1eJl4fx8MjRSJ9Zpmm1rVRfPM7Q6sVbuIN9IeHlO\n-----END PRIVATE KEY-----", "shared": "30eca56f1f1c2e8ff780134e0e9382c5927d305d86b53477e9aeca79fc9ced05", "result": "valid", "flags": []}, {"tcId": 45, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\n<PERSON>owBQYDK2VuAyEA////////////////////////////////AAAAAAAAAAA=\n-----<PERSON>ND PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINhCMW5Udq6u6DggQligbxXeARukC5licF5/boif5x9A\n-----END PRIVATE KEY-----", "shared": "cb21b7aa3f992ecfc92954849154b3af6b96a01f17bf21c612da748db38eb364", "result": "valid", "flags": []}, {"tcId": 46, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\n<PERSON>owBQYDK2VuAyEA/////wAAAAD/////AAAAAP////8AAAAA/////wAAAAA=\n-----<PERSON>ND PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKCTPuMFErJe5OkAqqB/c+UHqOxTtTpEYm4PWJr04DVs\n-----END PRIVATE KEY-----", "shared": "c5caf8cabc36f086deaf1ab226434098c222abdf8acd3ce75c75e9debb271524", "result": "valid", "flags": []}, {"tcId": 47, "comment": "edge case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDjWQD4Td3NM3OmChegg8lata3ada1YSvPQs8rl5RcBz\n-----END PRIVATE KEY-----", "shared": "4d46052c7eabba215df8d91327e0c4610421d2d9129b1486d914c766cf104c27", "result": "valid", "flags": []}, {"tcId": 48, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA/////////////////////////////////////////wM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBghkbcFLpzWMO8IAH/GtDvHZSkTvmd04v0nG3G5YqZB\n-----END PRIVATE KEY-----", "shared": "a0e0315175788362d4ebe05e6ac76d52d40187bd687492af05abc7ba7c70197d", "result": "valid", "flags": []}, {"tcId": 49, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA////DwAAAP///w8AAAD///8PAAAA////DwAAAP///w8=\n-----<PERSON>ND PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBBiIf5WlKcQ1uFHaWxdW5PWiH1YTyTyKBguvhsdLbhd\n-----END PRIVATE KEY-----", "shared": "5e64924b91873b499a5402fa64337c65d4b2ed54beeb3fa5d7347809e43aef1c", "result": "valid", "flags": []}, {"tcId": 50, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAA/P//AwAA4P//HwAAAP///wAAAPj//wcAAMD//z8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINA13pRWCA2FqRIIOy48fd15cfeG8lqWxeeCz29DduNi\n-----END PRIVATE KEY-----", "shared": "c052466f9712d9ec4ef40f276bb7e6441c5434a83efd8e41d20ce83f2dbf5952", "result": "valid", "flags": []}, {"tcId": 51, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA/////////////////////////////////////////z8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKjzcxikx2Dzyy2JSCKRhzVoPLHtrPPmZuFWlBVJeP1t\n-----END PRIVATE KEY-----", "shared": "d151b97cba9c25d48e6d576338b97d53dd8b25e84f65f7a2091a17016317c553", "result": "valid", "flags": []}, {"tcId": 52, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f///////////////////////////////////////18=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICDU1iTPcy+CbwnoCIAXdC8T8tqY9Nz0tAUZrbeQzr9k\n-----END PRIVATE KEY-----", "shared": "5716296baf2b1a6b9cd15b23ba86829743d60b0396569be1d5b40014c06b477d", "result": "valid", "flags": []}, {"tcId": 53, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f////////////////////////////////////9//38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINgGpzXROO+ztARoPJ2ESFq0r1QNCvJTtXQyPYkTADxm\n-----END PRIVATE KEY-----", "shared": "ddbd56d0454b794c1d1d4923f023a51f6f34ef3f4868e3d6659307c683c74126", "result": "valid", "flags": []}, {"tcId": 54, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA//////7//3///////v//f//////+//9///////7//38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBhBmMYigXfz70HcmjQSWPgYGuNl/p7JjZNjmwu+4UZ9\n-----END PRIVATE KEY-----", "shared": "8039eebed1a4f3b811ea92102a6267d4da412370f3f0d6b70f1faaa2e8d5236d", "result": "valid", "flags": []}, {"tcId": 55, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f////////////////////////////////////7//38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPCkan9LmJ/lFe3EQRCTRrp0bsFRaJbsW35PTZAwZLRj\n-----END PRIVATE KEY-----", "shared": "b69524e3955da23df6ad1a7cd38540047f50860f1c8fded9b1fdfcc9e812a035", "result": "valid", "flags": []}, {"tcId": 56, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f///////////////////////////////v///////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIgYdP2jqZwPAhbhFy+9B6scffeGAsxrESZOV6q18jpJ\n-----END PRIVATE KEY-----", "shared": "e417bb8854f3b4f70ecea557454c5c4e5f3804ae537960a8097b9f338410d757", "result": "valid", "flags": []}, {"tcId": 57, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f////////////////////7//////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILjQ8a4FpQcoMUQxUOICrG2wAyLN80H0Z+nylliLBNty\n-----END PRIVATE KEY-----", "shared": "afca72bb8ef727b60c530c937a2f7d06bb39c39b903a7f4435b3f5d8fc1ca810", "result": "valid", "flags": []}, {"tcId": 58, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f/////////+/////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMhhm6mIhZ231vIPvz/7ixE0GMwngGW06LttTls+fLVp\n-----END PRIVATE KEY-----", "shared": "7e41c2886fed4af04c1641a59af93802f25af0f9cba7a29ae72e2a92f35a1e5a", "result": "valid", "flags": []}, {"tcId": 59, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f////7//////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPjUyh83ow7JrNbb5abhUOW8RH0is1XYC6ACxbBcJpNd\n-----END PRIVATE KEY-----", "shared": "dd3abd4746bf4f2a0d93c02a7d19f76d921c090d07e6ea5abae7f28848355947", "result": "valid", "flags": []}, {"tcId": 60, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f//7////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIgDesjjPHLCxRA3x8jFKIu6kmXIL9jDF5bdfqXfmqpK\n-----END PRIVATE KEY-----", "shared": "8c27b3bff8d3c1f6daf2d3b7b3479cf9ad2056e2002be247992a3b29de13a625", "result": "valid", "flags": []}, {"tcId": 61, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f/+/////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFA07nv4OhPZFn34awZAKU82IPT02QMOXik/kZCCSuVi\n-----END PRIVATE KEY-----", "shared": "8e1d2207b47432f881677448b9d426a30de1a1f3fd38cad6f4b23dbdfe8a2901", "result": "valid", "flags": []}, {"tcId": 62, "comment": "edge case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA6////////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEC9ThyvOdne92Y4I1AtrT59MOtusB6biVFtTy9Ft81/\n-----END PRIVATE KEY-----", "shared": "2cf6974b0c070e3707bf92e721d3ea9de3db6f61ed810e0a23d72d433365f631", "result": "valid", "flags": []}, {"tcId": 63, "comment": "public key with low order", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA4Ot6fDtBuK4WVuP68Z/EatoJjeucMrH9hmIFFl9JuAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOD5eN/NOo8aUJNBjeVBNqWEwgt7NJr99sBSCIb5WxJy\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 64, "comment": "public key with low order", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAX5yVvKNQjCSx0LFVnIPvWwREXMRYHI6G2CJO3dCfEVc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDhzVdmVYWCQUDqvrUnaAfs9w+2pYnBOrua4b54gySV5\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 65, "comment": "public key with low order", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7P///////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMj+Dfkq5ooDAj/Aya25VX0xvn/u0NOrNsVYFD2vTbtA\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "Twist", "ZeroSharedSecret"]}, {"tcId": 66, "comment": "public key with low order", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA4Ot6fDtBuK4WVuP68Z/EatoJjeucMrH9hmIFFl9JuIA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMjXSs3lk05kuYldX/evv/1/cE99/M/3rCj6YqHmQQNH\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "NonCanonicalPublic", "Twist", "ZeroSharedSecret"]}, {"tcId": 67, "comment": "public key with low order", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAX5yVvKNQjCSx0LFVnIPvWwREXMRYHI6G2CJO3dCfEdc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILhWSdUSDgHozK97L7jYG2LorW89XAVT/d4ZBsudecBQ\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "NonCanonicalPublic", "Twist", "ZeroSharedSecret"]}, {"tcId": 68, "comment": "public key with low order", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7P////////////////////////////////////////8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICBksvTJ3JfsfPWJMv36MmW6bqTRHwJZuO/Ir7NduIxI\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "NonCanonicalPublic", "ZeroSharedSecret"]}, {"tcId": 69, "comment": "public key with low order", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHhqM6T3ryl6IOdkKSWTK/UJ5wcPobw2mGrx6xP09QtV\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 70, "comment": "public key with low order", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHhqM6T3ryl6IOdkKSWTK/UJ5wcPobw2mGrx6xP09QtV\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 71, "comment": "public key with low order", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7P///////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHhqM6T3ryl6IOdkKSWTK/UJ5wcPobw2mGrx6xP09QtV\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 72, "comment": "public key with low order", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAX5yVvKNQjCSx0LFVnIPvWwREXMRYHI6G2CJO3dCfEVc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHhqM6T3ryl6IOdkKSWTK/UJ5wcPobw2mGrx6xP09QtV\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 73, "comment": "public key with low order", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA4Ot6fDtBuK4WVuP68Z/EatoJjeucMrH9hmIFFl9JuAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHhqM6T3ryl6IOdkKSWTK/UJ5wcPobw2mGrx6xP09QtV\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 74, "comment": "public key with low order", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f///////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHhqM6T3ryl6IOdkKSWTK/UJ5wcPobw2mGrx6xP09QtV\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 75, "comment": "public key with low order", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7v///////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHhqM6T3ryl6IOdkKSWTK/UJ5wcPobw2mGrx6xP09QtV\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 76, "comment": "public key with low order", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHhqM6T3ryl6IOdkKSWTK/UJ5wcPobw2mGrx6xP09QtV\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 77, "comment": "public key with low order", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHhqM6T3ryl6IOdkKSWTK/UJ5wcPobw2mGrx6xP09QtV\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 78, "comment": "public key with low order", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7P////////////////////////////////////////8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHhqM6T3ryl6IOdkKSWTK/UJ5wcPobw2mGrx6xP09QtV\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 79, "comment": "public key with low order", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAX5yVvKNQjCSx0LFVnIPvWwREXMRYHI6G2CJO3dCfEdc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHhqM6T3ryl6IOdkKSWTK/UJ5wcPobw2mGrx6xP09QtV\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 80, "comment": "public key with low order", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA4Ot6fDtBuK4WVuP68Z/EatoJjeucMrH9hmIFFl9JuIA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHhqM6T3ryl6IOdkKSWTK/UJ5wcPobw2mGrx6xP09QtV\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 81, "comment": "public key with low order", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f////////////////////////////////////////8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHhqM6T3ryl6IOdkKSWTK/UJ5wcPobw2mGrx6xP09QtV\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 82, "comment": "public key with low order", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7v////////////////////////////////////////8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHhqM6T3ryl6IOdkKSWTK/UJ5wcPobw2mGrx6xP09QtV\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 83, "comment": "public key = 57896044618658097711785492504343953926634992332820282019728792003956564819949", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f///////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIED/WG5z1h8JYNwtdjrBnpgiXxGU9v5D1d2XrVWz01lh\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["SmallPublicKey", "LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 84, "comment": "public key = 57896044618658097711785492504343953926634992332820282019728792003956564819950", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7v///////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFhPzq666US/6TsuDQpXX3Bs5a2h2isTEcO0IfkYbHpv\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["SmallPublicKey", "LowOrderPublic", "NonCanonicalPublic", "ZeroSharedSecret"]}, {"tcId": 85, "comment": "non-canonical public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7////////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIAAWtir1yr3oxAk46/IQjgXSf6BTPthdcAFa1K05di1U\n-----END PRIVATE KEY-----", "shared": "b4d10e832714972f96bd3382e4d082a21a8333a16315b3ffb536061d2482360d", "result": "acceptable", "flags": ["NonCanonicalPublic", "Twist"]}, {"tcId": 86, "comment": "non-canonical public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA8P///////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINg2ULp87BFYgZFiVeP6X6DWuNz5aHMb0snSrsP1YfZJ\n-----END PRIVATE KEY-----", "shared": "515eac8f1ed0b00c70762322c3ef86716cd2c51fe77cec3d31b6388bc6eea336", "result": "acceptable", "flags": ["NonCanonicalPublic", "Twist"]}, {"tcId": 87, "comment": "non-canonical public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA8f///////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIjdFOJxHr0LACbGUSZMqWXn49pQgnifurfiRCXntDd+\n-----END PRIVATE KEY-----", "shared": "6919992d6a591e77b3f2bacbd74caf3aea4be4802b18b2bc07eb09ade3ad6662", "result": "acceptable", "flags": ["NonCanonicalPublic"]}, {"tcId": 88, "comment": "non-canonical public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA/////////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJjCsIy6wU4VlTFU47VY1CuxJoo2Ww7y8iclEp2Kxct/\n-----END PRIVATE KEY-----", "shared": "9c034fcd8d3bf69964958c0105161fcb5d1ea5b8f8abb371491e42a7684c2322", "result": "acceptable", "flags": ["NonCanonicalPublic"]}, {"tcId": 89, "comment": "non-canonical public key", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMBpe28F4PNDO0TqNS8gUI6wYjCYp3cIU69coJcnNAxO\n-----END PRIVATE KEY-----", "shared": "ed18b06da512cab63f22d2d51d77d99facd3c4502e4abf4e97b094c20a9ddf10", "result": "acceptable", "flags": ["NonCanonicalPublic", "Twist"]}, {"tcId": 90, "comment": "non-canonical public key", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAAwAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBhCK1ihjg9FGbeoh7jPtkngv+SzTXWWM1CplE5bf1t+\n-----END PRIVATE KEY-----", "shared": "448ce410fffc7e6149c5abec0ad5f3607dfde8a34e2ac3243c3009176168b432", "result": "acceptable", "flags": ["NonCanonicalPublic", "Twist"]}, {"tcId": 91, "comment": "non-canonical public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICBiDYJIdwe+357jVJ6Vy5OQ0mGPUM9qy6R/+qEDIkpv\n-----END PRIVATE KEY-----", "shared": "03a633df01480d0d5048d92f51b20dc1d11f73e9515c699429b90a4f6903122a", "result": "acceptable", "flags": ["NonCanonicalPublic"]}, {"tcId": 92, "comment": "non-canonical public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA2v////////////////////////////////////////8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIChaanzutxIvLHjZnFOyqQK0kIkvff8yb4nRJnPDEBtT\n-----END PRIVATE KEY-----", "shared": "9b01287717d72f4cfb583ec85f8f936849b17d978dbae7b837db56a62f100a68", "result": "acceptable", "flags": ["NonCanonicalPublic"]}, {"tcId": 93, "comment": "non-canonical public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA2/////////////////////////////////////////8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMjgMwrp3O7/iH+6dhIlh5pL0uDbCHmSRBNuRyGyyIlw\n-----END PRIVATE KEY-----", "shared": "dfe60831c9f4f96c816e51048804dbdc27795d760eced75ef575cbe3b464054b", "result": "acceptable", "flags": ["NonCanonicalPublic"]}, {"tcId": 94, "comment": "non-canonical public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA3P////////////////////////////////////////8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBDbYhD8H7EzgkcvoXh7AEtdEYaKs6eVEODO4w9KbfJr\n-----END PRIVATE KEY-----", "shared": "50bfa826ca77036dd2bbfd092c3f78e2e4a1f980d7c8e78f2f14dca3cce5cc3c", "result": "acceptable", "flags": ["NonCanonicalPublic", "Twist"]}, {"tcId": 95, "comment": "non-canonical public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA6v////////////////////////////////////////8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJBBxuBEonffhGYnXKi17g2nvAKGSAVK3lxZKt0wV0dO\n-----END PRIVATE KEY-----", "shared": "13da5695a4c206115409b5277a934782fe985fa050bc902cba5616f9156fe277", "result": "acceptable", "flags": ["NonCanonicalPublic"]}, {"tcId": 96, "comment": "non-canonical public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA6/////////////////////////////////////////8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILjUmQQaZxPA9vh223QGWH/bRFgvlUI1auic+pWKNNJm\n-----END PRIVATE KEY-----", "shared": "63483b5d69236c63cddbed33d8e22baecc2b0ccf886598e863c844d2bf256704", "result": "acceptable", "flags": ["NonCanonicalPublic"]}, {"tcId": 97, "comment": "non-canonical public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA//////////////////////////////////////////8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMhfCOYMhF+CCZFBpm3EWD0rEEBGLFRNM9BFOyCxpjd+\n-----END PRIVATE KEY-----", "shared": "e9db74bc88d0d9bf046ddd13f943bccbe6dbb47d49323f8dfeedc4a694991a3c", "result": "acceptable", "flags": ["NonCanonicalPublic"]}, {"tcId": 98, "comment": "public key = 57896044618658097711785492504343953926634992332820282019728792003956564819968", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHiHiJusTGKaEB03JPLti5jZNv3nnhofd9hneWJr+PJj\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["SmallPublicKey", "LowOrderPublic", "NonCanonicalPublic", "ZeroSharedSecret"]}, {"tcId": 99, "comment": "public key = 57896044618658097711785492504343953926634992332820282019728792003956564819969", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOB5ce6CDkiwsmbYvjzbu16QCkP1nuhTXGVyQYYV3kli\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["SmallPublicKey", "LowOrderPublic", "NonCanonicalPublic", "Twist", "ZeroSharedSecret"]}, {"tcId": 100, "comment": "RFC 7748", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA5ttoZ1gwMNs1lMGkJLFffHJmJOwmszU7EKkDptCrHEw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKBG42vwUnydOxYVS4JGXt1iFEwKwfxaGFBqIkS6RJpE\n-----END PRIVATE KEY-----", "shared": "c3da55379de9c6908e94ea4df28d084f32eccf03491c71f754b4075577a28552", "result": "valid", "flags": []}, {"tcId": 101, "comment": "RFC 7748", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA5SEPEnhoEdP0t5WdBTiuLDHb5xBvwDw+/EzVSccVpBM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEhm6dTRtGc8WtImkZV9avXBG2Qh4OoB1CykFp55GLpN\n-----END PRIVATE KEY-----", "shared": "95cbde9476e8907d7aade45cb4b873f88b595a68799fa152e6f8f7647aac7957", "result": "valid", "flags": []}, {"tcId": 102, "comment": "RFC 8037, Section A.6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA3p7bfXt9wbTTW2HC7OQ1Nz+DQ8hbeGdNrfx+FG+IK08=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHcHbQpzGKV9PBbBclGyZkXfTC+H68CZKrF3+6UduSwq\n-----END PRIVATE KEY-----", "shared": "4a5d9d5ba4ce2de1728e3bf480350f25e07e21c947d19e3376f09b3c1e161742", "result": "valid", "flags": []}, {"tcId": 103, "comment": "edge case for shared secret", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAt7bTnHZctgwMhUL085Uv+1HTAC1K65+P+YixkgQ+bQo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCjpPEwuYpb5LHO23y4VYSjUg4ULUdNycy5CaBzqXZ/\n-----END PRIVATE KEY-----", "shared": "0200000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 104, "comment": "edge case for shared secret", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAOxjfHlC4mevViMMWHL07+Y68wsH331O4Eb0OkbTVFT0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCjpPEwuYpb5LHO23y4VYSjUg4ULUdNycy5CaBzqXZ/\n-----END PRIVATE KEY-----", "shared": "0900000000000000000000000000000000000000000000000000000000000000", "result": "valid", "flags": []}, {"tcId": 105, "comment": "edge case for shared secret", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAyrb559jOAN/OqbvY8Gnvf7KsUEq/g7h9tgG1rgp/dhU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCjpPEwuYpb5LHO23y4VYSjUg4ULUdNycy5CaBzqXZ/\n-----END PRIVATE KEY-----", "shared": "1000000000000000000000000000000000000000000000000000000000000000", "result": "valid", "flags": []}, {"tcId": 106, "comment": "edge case for shared secret", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEASXfQ2JfhulZlkPYPLrDbb3skwT1DaRjM/TJwjfrX4kc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCjpPEwuYpb5LHO23y4VYSjUg4ULUdNycy5CaBzqXZ/\n-----END PRIVATE KEY-----", "shared": "feffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff3f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 107, "comment": "edge case for shared secret", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAmHMLwD4p6LBX+x0g74wL/8giSF09t/RfTjzCw8bR0Uw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCjpPEwuYpb5LHO23y4VYSjUg4ULUdNycy5CaBzqXZ/\n-----END PRIVATE KEY-----", "shared": "fcffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff3f", "result": "valid", "flags": []}, {"tcId": 108, "comment": "edge case for shared secret", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAl7T/9oLffwls0XVlaeJS20gtRUBqMZihr/KCpdpHTEk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCjpPEwuYpb5LHO23y4VYSjUg4ULUdNycy5CaBzqXZ/\n-----END PRIVATE KEY-----", "shared": "f9ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff3f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 109, "comment": "edge case for shared secret", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAMXeBsBY7rnSszAbA1E75qRGiKw03+vdyZiFZH5ND6i8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCjpPEwuYpb5LHO23y4VYSjUg4ULUdNycy5CaBzqXZ/\n-----END PRIVATE KEY-----", "shared": "f3ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff3f", "result": "valid", "flags": []}, {"tcId": 110, "comment": "edge case for shared secret", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAfib48ky1kAJ/nRvEmw4aJCx9j0NiTT6PqyjuCOAstF4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCjpPEwuYpb5LHO23y4VYSjUg4ULUdNycy5CaBzqXZ/\n-----END PRIVATE KEY-----", "shared": "ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff03", "result": "valid", "flags": []}, {"tcId": 111, "comment": "edge case for shared secret", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA6W0ngOVGmnRiCrWqL2IVHRQMRzMg2+GwKPGkj452+V8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCjpPEwuYpb5LHO23y4VYSjUg4ULUdNycy5CaBzqXZ/\n-----END PRIVATE KEY-----", "shared": "e5ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 112, "comment": "edge case for shared secret", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAjWEsWDGqZLBXMA5+MQ86ozKvNAZv78qysInJWSh4+DI=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCjpPEwuYpb5LHO23y4VYSjUg4ULUdNycy5CaBzqXZ/\n-----END PRIVATE KEY-----", "shared": "e3ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 113, "comment": "edge case for shared secret", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAjUQQjQXZQNPf5WR+p6h74k0NA2yfCpWiOGuDnnt78UU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCjpPEwuYpb5LHO23y4VYSjUg4ULUdNycy5CaBzqXZ/\n-----END PRIVATE KEY-----", "shared": "ddffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f", "result": "valid", "flags": []}, {"tcId": 114, "comment": "edge case for shared secret", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAIaNdXbG2I3xzm1Y0WpMK7uNzzc+0cBJmeCqKxZSROyk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCjpPEwuYpb5LHO23y4VYSjUg4ULUdNycy5CaBzqXZ/\n-----END PRIVATE KEY-----", "shared": "dbffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 115, "comment": "edge case for shared secret", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAPl77Y8NSzpQnYkgryTN6XTW6VWZHQ6xek9EflXM2yxA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCjpPEwuYpb5LHO23y4VYSjUg4ULUdNycy5CaBzqXZ/\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000002", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 116, "comment": "edge case for shared secret", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAjkHwXqPHZXK+EErYeI6XCGPG4so9quZNHC9G3s//pXE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCjpPEwuYpb5LHO23y4VYSjUg4ULUdNycy5CaBzqXZ/\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000008000", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 117, "comment": "special case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMjQfEa7+4J3U7kscOSVg86L+kRkGnOCJY6pA9aoMslr\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["SmallPublicKey", "LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 118, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJC37yN6BV80jctMQ2SlnX0x7cerePLKJU4sgQl1w/VD\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["SmallPublicKey", "LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 119, "comment": "special case public key", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAAgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOCovmMxXE8PCj/uYH9E0wpVvmPwlWHZr5PgocnPDtdR\n-----END PRIVATE KEY-----", "shared": "0c50ac2bfb6815b47d0734c5981379882a24a2de6166853c735329d978baee4d", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 120, "comment": "special case public key", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAEgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIAhAqK9bxMSNqIUOlz1+FCIPRcGSzqQCDTd+7NJcfDZD\n-----END PRIVATE KEY-----", "shared": "77557137a2a2a651c49627a9b239ac1f2bf78b8a3e72168ccecc10a51fc5ae66", "result": "valid", "flags": []}, {"tcId": 121, "comment": "special case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAFAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIACSIpx1OnEoTQhTkJRwrYR6ti9DnqUUgvtB0wzDtEdD\n-----END PRIVATE KEY-----", "shared": "c88e719ae5c2248b5f90da346a92ae214f44a5d129fd4e9c26cf6a0da1efe077", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 122, "comment": "special case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILjaK9LXzyWj5U5fh+4VkR7/uf+GuuxAdtVsjpU2cL9b\n-----END PRIVATE KEY-----", "shared": "4bf6789c7ea036f973cde0af02d6fdb9b64a0b957022111439570fad7d7a453f", "result": "valid", "flags": []}, {"tcId": 123, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\n<PERSON>owBQ<PERSON>DK2VuAyEA//////////////////8AAAAAAAAAAAAAAAAAAAAAAAA=\n-----<PERSON><PERSON> PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGhM1CCvQauz0Qxh53MjjPcpwhVflBrCfhX0w39JspV2\n-----END PRIVATE KEY-----", "shared": "bcac235ae15cc7148372e11f9315e3bc76ceb904b3d2a8246bd9d9be2082bb62", "result": "valid", "flags": []}, {"tcId": 124, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAAQAAAAAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDjPrKpEYHlrTeQ0vdZznw0ENnH5f6gpUXUR5rR6qTR0\n-----END PRIVATE KEY-----", "shared": "5dd7d16fff25cc5fdf9e03c3157cb0a235cea17d618f36e6f13461567edeb943", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 125, "comment": "special case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDCDLoy2J6wZX3exEFJY5LsYuZpe2URAS/rLOgOfvbFL\n-----END PRIVATE KEY-----", "shared": "2816fd031d51d6750f9225ede950625cca47441ca97e43092650396991afcb6d", "result": "valid", "flags": []}, {"tcId": 126, "comment": "special case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINgY/Wlx5UZEfzYdM9Pbs+rc8C+yjyRvHVEHuQc6k81P\n-----END PRIVATE KEY-----", "shared": "7ed8f2d5424e7ebb3edbdf4abe455447e5a48b658e64abd06c218f33bd151f64", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 127, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\n<PERSON>owBQYDK2VuAyEA/////////////////////////////////////wAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBAhzYaCvcP12pEArf9bIjCzrNg2s6RV24NSosJ+adF+\n-----END PRIVATE KEY-----", "shared": "e8620ed5ca89c72c5ea5503e6dcd01131cd5e875c30e13d5dc619ce28ec7d559", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 128, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICDkySRxAikmVdZ2XX2ExvzlMJuABARdrqbX19ytRihx\n-----END PRIVATE KEY-----", "shared": "ceadb264379dcadd6e3bb8ad24dd653d2a609dd703d41da6caf3ad00f001862c", "result": "valid", "flags": []}, {"tcId": 129, "comment": "special case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAqLnHNyEYpTqd6erwho47Gj2I6Byy5Af/cSXp9cUIhxU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJCxUNRi3lEgVtW9VRcwdJabSW8mL7aRa3M/YmOoB4lx\n-----END PRIVATE KEY-----", "shared": "f86cc7bf1be49574fc97a074282e9bb5cd238e002bc8e9a7b8552b2d60eccb52", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 130, "comment": "special case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAqrnHNyEYpTqd6erwho47Gj2I6Byy5Af/cSXp9cUIhxU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJiHKGsyYcjYV6FvbbISd/ddiNToYbPr51lmmQR+gWZo\n-----END PRIVATE KEY-----", "shared": "ccbb8fd9dee165a398b2dbd7c8396f81736c1b3da36b35fbec8f326f38f92767", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 131, "comment": "special case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAWFAHpZMNd2I88pdWA4yhl9Pr/Z5MgKaVhe/gJ0CSwRU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICDKLIXMh2Lpa3BHvxXHHAUP/g7RYWBAqVOuMqEpethx\n-----END PRIVATE KEY-----", "shared": "46add6f48ffff461777d4f89b6fdf1155aa051a96387d45f3e5e371a236b6e52", "result": "valid", "flags": []}, {"tcId": 132, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA+////////////////////////////////////////x8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINAnZWYFsQvxjeoovFJUb58fCM7wbK/SAPyE+H27Tr5G\n-----END PRIVATE KEY-----", "shared": "1adbe32207e21f71e1af53884d2a2276481e298e557f4dacb3720f2458e3082d", "result": "valid", "flags": []}, {"tcId": 133, "comment": "special case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEhnqD7p0Bt1EIQIZ9sa9qYEm9uwVrdEQ/cMNY4WLIhn\n-----END PRIVATE KEY-----", "shared": "e12cc58fbeb70a5e35c861c33710be6516a6a92e52376060211b2487db542b4f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 134, "comment": "special case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAr6AOSicb7sR45C+tBhhDL6fX+z2ZAE0rC9/BT4Akgys=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKAVlwqK3ZQPylsbXSOHU5fVR9jUlPyzFPIEWmei0SxL\n-----END PRIVATE KEY-----", "shared": "421bed1b26da1e9adbeada1f32b91a0fb4ced0f1110e0a4a88e735a19ee4571e", "result": "valid", "flags": []}, {"tcId": 135, "comment": "special case public key", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAsaAOSicb7sR45C+tBhhDL6fX+z2ZAE0rC9/BT4Akgys=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEBYy2uaq6AqM4qqOS28EAOeJunkRBF+dY4kxdiyMupe\n-----END PRIVATE KEY-----", "shared": "d7b47463e2f4ca9a1a7deea098da8e74ac3b4a109083d997259b12992e7e7e06", "result": "valid", "flags": []}, {"tcId": 136, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA+////////////////////////////////////////y8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILh2sF2v8FMLE52eESUFY0GAdxeCRsX6cAW6AOm2ZHdj\n-----END PRIVATE KEY-----", "shared": "686eb910a937211b9147c8a051a1197906818fdc626668eb5f5d394afd86d41b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 137, "comment": "special case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAIiMcZO9zrWIxi4qHvDjicuG7i/GmDXwAR20LBZ17PDU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINh/1qpdje723ulhmlaEaggpYgWQ8tpAg12OJRWX45B4\n-----END PRIVATE KEY-----", "shared": "09559733b35bcc6bb8ac574b5abe3a4d8841deff051c294a07487e3eec3c5558", "result": "valid", "flags": []}, {"tcId": 138, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA9v///////////////////////////////////////z8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJADYyG2N1H3YiqpPaNNheWc6BAJrFuaBokh2DvEcVtX\n-----END PRIVATE KEY-----", "shared": "f7d5cbcf39eb722b01ed20c85563ebb81d076511aead4ccc429027866b9fd270", "result": "valid", "flags": []}, {"tcId": 139, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA9////////////////////////////////////////z8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKBngf1MSgh04A5yuhMbndh6g7KQTilN4Xboqa8faV1n\n-----END PRIVATE KEY-----", "shared": "e995ad6a1ec6c5ab32922cff9d204721704673143c4a11deaa203f3c81989b3f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 140, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA/v///////////////////////////////////////z8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILgi1y2LaL20+/Z+VqYdZyssd0fpRHn+WuQHLQrM3WVx\n-----END PRIVATE KEY-----", "shared": "32b6dabe01d13867f3b5b0892fefd80dca666f2edc5afb43cd0baf703c3e6926", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 141, "comment": "special case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINCM4SN+JI0CzfYZ0gvqWEit5Pb/0XG43uh5P8Z8RZZA\n-----END PRIVATE KEY-----", "shared": "a93d83fc9ea0f6cb0cc8b631da600019b76cbb2ec57222f2e42dd540e3da850b", "result": "valid", "flags": []}, {"tcId": 142, "comment": "special case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAy9zjmxCMUp3OdHV4Q8cdjR5EdA5Z8oP/uJL0+mKEw0o=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBgK48koUUz7nt0G59wdXQZhYOlnRFpcWORGO2ntIF5t\n-----END PRIVATE KEY-----", "shared": "017cbfa2b38e9ef3297a339ecce1a917bdcf7e910036086a41d1e22d04241870", "result": "valid", "flags": []}, {"tcId": 143, "comment": "special case public key", "public": "-----<PERSON><PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAPF/xtdjkETuHG9BS+ee80FgoBMJm/7LU9CA+sH/bfFQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOiB2AahEFYM2P7omdWcAknxIzpDIsQao2nHoqmfW1li\n-----END PRIVATE KEY-----", "shared": "71133905b8a57ea8c38de0ecf213699a75b096c2df21f07f7e9eb03e9fa53f5c", "result": "valid", "flags": []}, {"tcId": 144, "comment": "special case public key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAPl/xtdjkETuHG9BS+ee80FgoBMJm/7LU9CA+sH/bfFQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIAjkEOHX6LlBEjavSjXWtipdiTFHjkxiGXz6+0kUZ7Fi\n-----END PRIVATE KEY-----", "shared": "3dc7b70e110766b2bf525252ebed98a100b2e532dc69544464da1bbab8625f6d", "result": "valid", "flags": []}, {"tcId": 145, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA8v///////////////////////////////////////18=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOAv334O49VbREDwFDLdJTyUl5O8BNpE3ezoPlTIw5tA\n-----END PRIVATE KEY-----", "shared": "e317e5cc438b5f79ead5533ac7c45519a117b31033cc2140b19edf8572011240", "result": "valid", "flags": []}, {"tcId": 146, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA9v///////////////////////////////////////18=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPBdGPaO96WGXBTbOpwlX98tq+oqo2WB6U9otye1goZ7\n-----END PRIVATE KEY-----", "shared": "d86810516aeddc18061036f599a9eb84d1c6146b0f543652dd4526743ba42c04", "result": "valid", "flags": []}, {"tcId": 147, "comment": "special case public key", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAla/4WmzyiJ3DDWip/HNeaCwUAmGzf1lqehAf2L9tPmo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIADBA1eNXAede8wiwcMeeHwbFcV/y0k/2v76IDcc/HRr\n-----END PRIVATE KEY-----", "shared": "dfa988a477003be125b95ccbf2223d97729577d25e1d6e89e3da0afabdd0ae71", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 148, "comment": "special case public key", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAQ0Y4yN7nWsViFhUPeXHE5cJ3F+NNG/gAjtoWCjr3eGo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHAFu5J0hcQ1ZCtCSj3eAUvPdjReW+ZK5umyTbOeHNtR\n-----END PRIVATE KEY-----", "shared": "d450af45b8ed5fe140cc5263ffb7b52e66736899a8b872b6e28552129819b25b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 149, "comment": "special case public key", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEARUY4yN7nWsViFhUPeXHE5cJ3F+NNG/gAjtoWCjr3eGo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIAgiA5pdwTxA/MzzRuKndptP0nIFLUMmCtYmRopQ1EFi\n-----END PRIVATE KEY-----", "shared": "58002c89bf8bc32ae6fc205b796acd13ef7f8476f6492ae4b2be47f1095e8a4f", "result": "valid", "flags": []}, {"tcId": 150, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7P/////////////////+/////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIECmNJwD8NwKQjWPY1PKZ2Mq9oexTJ3/YmxU4hHo/DVa\n-----END PRIVATE KEY-----", "shared": "7773aad6e72eb1735b65ad51f7dad258c11d7bfff53094424cb103cd6bfb4368", "result": "valid", "flags": []}, {"tcId": 151, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7v/////////////////+/////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFBpbU0FIJlx1roGduonQmK6Y5qsdPp15d9FcHaK2K50\n-----END PRIVATE KEY-----", "shared": "c118ddf6462fbea80f14ef1f2972a1ab12cafa511d1323d4d22d0d426d651b5b", "result": "valid", "flags": []}, {"tcId": 152, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7f///////////////3///////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGi7aAyFP05NqkfFhtyIbPRWjXsDg3cPbfQ5pTvkoyNt\n-----END PRIVATE KEY-----", "shared": "cc0775bfd970a2706b11c7222a4436a3d17160382c83b76f89b66192c81b4408", "result": "valid", "flags": []}, {"tcId": 153, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA6////////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILD2wo29xkcGinbXGAXvdw8IfPdrgq/cDSbEW3Gs5Jdo\n-----END PRIVATE KEY-----", "shared": "f0097fa0ba70d019126277ab15c56ecc170ca88180b2bf9d80fcda3d7d74552a", "result": "valid", "flags": []}, {"tcId": 154, "comment": "special case public key", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7P///////////////////////////////////////38=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBhjD5NZhjfDXaYjp0VZz5RDdKVZEUx5N4EQQfyGBVZK\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "Twist", "ZeroSharedSecret"]}, {"tcId": 155, "comment": "special case for E in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAAAAAAAAAAAAAAgAAAAAAAAAAAAAAAAAAAAAAAAAAAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFgey9paSiKARP79bgPfI0VYw8eRUsbixeYLFCxPJqhR\n-----END PRIVATE KEY-----", "shared": "59e7b1e6f47065a48bd34913d910176b6792a1372aad22e73cd7df45fcf91a0e", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 156, "comment": "special case for E in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAd68NOJenFd/iXfXVOM8TO8mretUt9r2SKi+3ViHVmQE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILBWGjgAB5W3y1N7Vel16kUsIRhQYpXV6xX9nIO2f3pQ\n-----END PRIVATE KEY-----", "shared": "179f6b020748acba349133eaa4518f1bd8bab7bfc4fb05fd4c24e7553da1e960", "result": "valid", "flags": []}, {"tcId": 157, "comment": "special case for E in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEATjmGYSe2oSpUkU4QaquGRkr1VjHzy2F2bVmZqo0uBw4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILAPffLUcShEHHJwuah+7kW2BW/GQjale9+B28zPX11C\n-----END PRIVATE KEY-----", "shared": "43c5ee1451f213ef7624729e595a0fee7c9af7ee5d27eb03278ee9f94c202352", "result": "valid", "flags": []}, {"tcId": 158, "comment": "special case for E in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEArcZ5nthJXtWrbrHvlVR5ubUKqc4MNJ6JkqZmVXLR+BE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMj3oMC/senHJXbFNPhoVPvkr1IdT6gH9n4kQOEA7IhS\n-----END PRIVATE KEY-----", "shared": "2f350bcf0b40784d1d756c9ca3e38ec9dd68ba80faf1f9847de50779c0d4902a", "result": "valid", "flags": []}, {"tcId": 159, "comment": "special case for E in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAdw9CGO8jT14YVGbjJELDArvsIbu2zSjJeeeD/lATMz8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFgYH1gao3Ai/3HFbG5o5hddlnxcmVokmIX2ZWUHTe1N\n-----END PRIVATE KEY-----", "shared": "d5d650dc621072eca952e4344efc7320b2b1459aba48f5e2480db881c50cc650", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 160, "comment": "special case for E in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAXGEYxMdM+4QtmodEn52NuLmS1GxakJPOL8t6SbU1xFE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDAck1yuQ1cHCwra+c1hkoMLLJicFTcp7tmfWJ60X4hL\n-----END PRIVATE KEY-----", "shared": "909cc57275d54f20c67b45f9af9484fd67581afb7d887bee1db5461f303ef257", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 161, "comment": "special case for E in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAQDmGYSe2oSpUkU4QaquGRkr1VjHzy2F2bVmZqo0uB24=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINACKS1DWaPUK8h2fxOAAJMy56DfLzN5ARq3j3ifa6pU\n-----END PRIVATE KEY-----", "shared": "4a7e2c5caf1d8180eb1c4f22692f29a14b4cdc9b193bd1d16e2f27438eef1448", "result": "valid", "flags": []}, {"tcId": 162, "comment": "special case for E in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAB4+lI0mPtRy6ERLYOyCvRIuACdjuoUNoVk0BuPm2CG8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINDCxJ5kSrc4JwcH/5kXBllCaH4vEohtlhFh20bAW1Zf\n-----END PRIVATE KEY-----", "shared": "c0ee59d3685fc2c3c803608b5ee39a7f8da30b48e4293ae011f0ea1e5aeb7173", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 163, "comment": "special case for E in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAn8Z5nthJXtWrbrHvlVR5ubUKqc4MNJ6JkqZmVXLR+HE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPCH04snTB2tG85uqja0jiGQuQub+MpZZpzF4ARkU0NC\n-----END PRIVATE KEY-----", "shared": "b252bc8eabfaa68c56e54d61b99061a35d11e3a7b9bda417d90f69b1119bcf45", "result": "valid", "flags": []}, {"tcId": 164, "comment": "special case for E in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAdlDyx2hY6iAdogIqxzDsxDZUhSrSCUJt1dBIqd4qZn4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEjbzFppXxUUu7qmrQCEK2nZrlIWsZY63Qf7KUfJe4RH\n-----END PRIVATE KEY-----", "shared": "fbda33bc930c08df837208e19afdc1cfe3fd0f8f0e3976be34775e58a4a7771f", "result": "valid", "flags": []}, {"tcId": 165, "comment": "D = 0 in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA4Ot6fDtBuK4WVuP68Z/EatoJjeucMrH9hmIFFl9JuAA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFiRyScs+aGXc1twHlcVJo0210NrfjUaPpl6CGLkgH1N\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 166, "comment": "D = 0 in multiplication by 2", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAX5yVvKNQjCSx0LFVnIPvWwREXMRYHI6G2CJO3dCfEVc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMD5xgrqc3Mdkqte2fTOoSL5pusld72nL5SUj+pNTMZd\n-----END PRIVATE KEY-----", "shared": "0000000000000000000000000000000000000000000000000000000000000000", "result": "acceptable", "flags": ["LowOrderPublic", "ZeroSharedSecret"]}, {"tcId": 167, "comment": "special case for DA - CB in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAsCJOcTTPktQKMVFfLw6JwqJ3forC/nQdsNw5OZ/fJwI=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIABm3XZ0/lH5MmweI5uHX4rAcBquaagEwl/kNZXoZgtF\n-----END PRIVATE KEY-----", "shared": "8dacfe7beaaa62b94bf6e50ee5214d99ad7cda5a431ea0c62f2b20a89d73c62e", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 168, "comment": "special case for DA - CB in multiplication by 2", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAYB4/67hI7D5X/OZFiKrYKvycKvmbvN/8xM1Y1LPRXAc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIAGfzD0DWExi0IMhZ/OEoyQF6uBtHt2AopXvDDVhWhG\n-----END PRIVATE KEY-----", "shared": "20f1d3fe90e08bc6f152bf5dacc3ed35899785333f1470e6a62c3b8cbe28d260", "result": "valid", "flags": []}, {"tcId": 169, "comment": "special case for DA - CB in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAgqOAe73sL6mTj7QUHifcV0VmBjAfeP9xM88k89E+4Rc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFhFd2adIc4K4+MLAsl4P/6XcJy/45aImqMejuQzUtxS\n-----END PRIVATE KEY-----", "shared": "2b28cc5140b816add5ad3a77a81b1c073d67bf51bf95bda2064a14eb12d5f766", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 170, "comment": "special case for DA - CB in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA8ymrI3ZGLl8xKKJoIIYlPBkiKsHivKRWkvDDtSj0xCg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBjll6TizNtegFLVfJAJk4wtTEPW2Mn5PJhye3MRA1lT\n-----END PRIVATE KEY-----", "shared": "8392160083b9af9e0ef44fcfce53ba8ff7282ee7a6c71ab66f8843a55d09cd68", "result": "valid", "flags": []}, {"tcId": 171, "comment": "special case for DA in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAT847tsiq8CLb0QDjzeOUGzfVQ/AEAdun2pvBQ9/FVwk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIgoHMUdVRLYgU6lJJuHncutAyPThRLa+9x7qFu6jI1d\n-----END PRIVATE KEY-----", "shared": "42184e22c535530c457bd3b4f1084cbf5e297f502fe136b8d1daecf5334cc96c", "result": "valid", "flags": []}, {"tcId": 172, "comment": "special case for DA in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAFcaIUcHbhEtaHvNFamWfGIhUsadfvbL2j1FMkonOcR8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINDnlUUN8KgTxlc0luxXk8oC4b260Q7Qjfg/2u1oszhf\n-----END PRIVATE KEY-----", "shared": "f654d78e5945b24bc63e3e6d790e0ae986e53937764068b1bce920e1d79b756f", "result": "valid", "flags": []}, {"tcId": 173, "comment": "special case for DA in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAQgCiQkNDN7iRT0k0UwHteCsTWU+e3gicQfsefqgskFM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDC2mhzB6y0Lg+ohOEbpCiySIIi98pSmmVv25ud8ZGxB\n-----END PRIVATE KEY-----", "shared": "cd8a09b04795edcc7061867373981aa748651ebdce5ec218a335b878cefe4872", "result": "valid", "flags": []}, {"tcId": 174, "comment": "special case for DA in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAuqvwF0qq6k3kjMg637BAFGGnQZA+pvsTDX1kt78DqWY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHizC7Y82K3nG3p31Cb0QZ0F8Zn/7zSeifqp2aXyH2ZU\n-----END PRIVATE KEY-----", "shared": "c9f8258f237db1c80702c5c4d9048dfba9dfe259da4aeee90dc2945526961275", "result": "valid", "flags": []}, {"tcId": 175, "comment": "special case for x_2 in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA8S8YvVnBJjSPanqfSl/dn8r1gTRQc6hR+6CY5dZLSgw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMCzhvTvDUaYaGQEl357YMtsH4tgEqIuKdYiTFlHQ5BB\n-----END PRIVATE KEY-----", "shared": "6600cbe900616a770a126b8b19156d5e27e1174bd538d0944eb3c0be4899c758", "result": "valid", "flags": []}, {"tcId": 176, "comment": "special case for x_2 in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAvuOGUnt3JJCuuW/E0juTBAN8tEMPZLIo89iztJgxnyI=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJiGYC5xm6yv6gkrt1tRrnJYq+GjZMF2hX89wYjAPmdZ\n-----END PRIVATE KEY-----", "shared": "3fe710d6344ff0cb342e52349e1c5b57b7a271f2a133bb5249bbe40dc86e1b40", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 177, "comment": "special case for x_2 in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAz5EayRsNlEBJzsZq5e8MRUnR5hLhB8aOhyY6L7z4Mj8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILg5YPXQYTzarG3aaQNRZm6fJ3u6a9QGsOJ6GIa7LT5G\n-----END PRIVATE KEY-----", "shared": "71373ebe67f39a2c230027c7db4b3b74bab80ed212b232679785ee10f47c304e", "result": "valid", "flags": []}, {"tcId": 178, "comment": "special case for x_2 in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAHm7lNuTya7+2MTmVGhDzurYuGe0e+DlxeNnF0EMHzUA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINA7dfCayAff0u41LASh8lmEcg94X/qgr4i8Xbb/nDRT\n-----END PRIVATE KEY-----", "shared": "238eef43c589822e1d3de41c1cc46dcfec7a93febf37c8546b6625e1a123815d", "result": "valid", "flags": []}, {"tcId": 179, "comment": "special case for x_2 in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEALxx5rYSI229RRpA7LcRs+/yDS7zwm03XDCdMS2fOYF0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINA2lIwOwiPw7ld+OQ2/hyIjWO0ZnygjNFrRVLvEy8xH\n-----END PRIVATE KEY-----", "shared": "87a79c9c231d3b9526b49bf3d683bf38c3c319af7c7c5d1456487398da535010", "result": "valid", "flags": []}, {"tcId": 180, "comment": "special case for x_2 in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA/M/nQqY+2ctwlYVgtaAiYDUKfsuvjFeuBF9nGim0tXM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINBU3tYT/r8pUKxckn/LEgw4feC6YbMxzTMCTItuc3BI\n-----END PRIVATE KEY-----", "shared": "d683ca6194452d878c12d7da35f22833f99728bba89931a51274f61210336a5f", "result": "valid", "flags": []}, {"tcId": 181, "comment": "special case for AA in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAyz1KkPhrMBHaM2nZmIWXx//xSZJztKBPhNDibtFoPA0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOgsSAYx+xU7oiEf5gMDKz5xsWLb08Eb7AMgj/zVEGVf\n-----END PRIVATE KEY-----", "shared": "dbf6203516635840cf69a02db87cf0d95dae315da7fc1ec7ce2b29e1f2db6666", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 182, "comment": "special case for AA in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAEB4T97wFcPomOMqiCmfG4MIdqxMvS0VhkVkCZMST0Bg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMDAHSjByrAfWXAKyl8Y0ml2WLN/3VSjOf85HAoaGxZF\n-----END PRIVATE KEY-----", "shared": "1fe314744390d525278b1f5fbf108101b8ded587081375ed4ac4ac690d92414f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 183, "comment": "special case for AA in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA3OHsCEP6jwXZxzVd9Zg5Hz3iVOzQtLqebqb9mztsL2c=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMgr3nLfNkeWiMSFqL9EL0o0QS5CnALbl3BPA9r039VC\n-----END PRIVATE KEY-----", "shared": "ad454395ee392be677be7b9cb914038d57d2d87ec56cc98678dd84f19920912b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 184, "comment": "special case for AA in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAIcK1bweUz+4lzJYmZ3poOAAOtm2MS1+wey8dkS6Xw3I=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFA/aXYX+wKnuO8AujTn/IzpP57D4cv+S/LAW87gy5dX\n-----END PRIVATE KEY-----", "shared": "c6d6499255133398f9dd7f32525db977a538118800bfaf3aad8bcd26f02c3863", "result": "valid", "flags": []}, {"tcId": 185, "comment": "special case for BB in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAzD1KkPhrMBHaM2nZmIWXx//xSZJztKBPhNDibtFoPA0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFjNTKHkMxGI3isoiUGc4g7F74ig6TrwkgmQZVUbkE5B\n-----END PRIVATE KEY-----", "shared": "0d74214da1344b111d59dfad3713eb56effe7c560c59cbbb99ec313962dbba58", "result": "valid", "flags": []}, {"tcId": 186, "comment": "special case for BB in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAER4T97wFcPomOMqiCmfG4MIdqxMvS0VhkVkCZMST0Bg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIABOo0SLhMpQnv7F/MJMY+6YTe9jsp3rkDeJRwlwnAlX\n-----END PRIVATE KEY-----", "shared": "7b9dbf8d6c6d65898b518167bf4011d54ddc265d953c0743d7868e22d9909e67", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 187, "comment": "special case for BB in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA3eHsCEP6jwXZxzVd9Zg5Hz3iVOzQtLqebqb9mztsL2c=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMim6wCk10u9/yOVIsPIke184ZBL4qMpzQrgBholPJVC\n-----END PRIVATE KEY-----", "shared": "fb0e0209c5b9d51b401183d7e56a59081d37a62ab1e05753a0667eebd377fd39", "result": "valid", "flags": []}, {"tcId": 188, "comment": "special case for BB in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAIsK1bweUz+4lzJYmZ3poOAAOtm2MS1+wey8dkS6Xw3I=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFAyL/DQ3N1rFPMHwE3+zv5bfN6vkr/7kZ6dYu0nB5BA\n-----END PRIVATE KEY-----", "shared": "dbe7a1fe3b337c9720123e6fcc02cf96953a17dc9b395a2206cb1bf91d41756e", "result": "valid", "flags": []}, {"tcId": 189, "comment": "special case for D in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA5Yuszt4yvPM7O249acAq+ChKljHedLavPwRqk2nfBA8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOAyjH0YjZj68qxy1yi30U8ru9epTQ+9jo95q+Cx/hBV\n-----END PRIVATE KEY-----", "shared": "97bd42093e0d48f973f059dd7ab9f97d13d5b0d5eedffdf6da3c3c432872c549", "result": "valid", "flags": []}, {"tcId": 190, "comment": "special case for D in multiplication by 2", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAxtXGk/wKTi32spACaGBWahZrbXrr48mIKNSSdFyN+TY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFAXZ5oXvSOt+VrUfjEPxlJvS6nKOwg5tTvQ2Sg561tP\n-----END PRIVATE KEY-----", "shared": "99bcbc7b9aa5e25580f92bf589e95dae874b83e420225d8a93e18e96dac00b63", "result": "valid", "flags": []}, {"tcId": 191, "comment": "special case for D in multiplication by 2", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA0V9L8u9ce9pO6VGW88DfcQ3109IGNg/DF06nXDqjp0M=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIChkqvYcFG3wbMJWsGX2azSYXMAV2lsdZHpu1OLHa/xD\n-----END PRIVATE KEY-----", "shared": "afa2adb52a670aa9c3ec3020d5fda285474ede5c4f4c30e9238b884a77969443", "result": "valid", "flags": []}, {"tcId": 192, "comment": "special case for D in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAbf+woliIvyPPGscBv73t6KGOMjudTT0x5RagX8586HI=\n-----END PUBLIC KEY-----", "private": "-----B<PERSON>IN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBhKbPury9FQei6kH1J5ZYPb24UbiKhXge6OPCh4LDNJ\n-----END PRIVATE KEY-----", "shared": "e6a2fc8ed93ce3530178fef94bb0056f43118e5be3a6eabee7d2ed384a73800c", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 193, "comment": "special case for D in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAIfhtEjySOpKq8lY9+UtbXJOHT1t6uZVKqlPj1y8P9n4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMhflUuFvBAqynmWcXk0UhdlONB3hi7kXgslNhl2ff9C\n-----END PRIVATE KEY-----", "shared": "7fc28781631410c5a6f25c9cfd91ec0a848adb7a9eb40bc5b495d0f4753f2260", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 194, "comment": "special case for D in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAWHw0fIyySVZKt3OD3jWMwqGf5zcKhHbUMJESNZiUHH8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFDj5amhm+LuNUiwlkZy+14xNMsNL3rfAA5FVtD/o3ZD\n-----END PRIVATE KEY-----", "shared": "314d8a2b5c76cc7ee1217df2283b7e6724436e273aeb80628dce0600ab478a63", "result": "valid", "flags": []}, {"tcId": 195, "comment": "special case for DA + CB in multiplication by 2", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA9cYxGh3RueD4z9A0rG0BvyjZ0PlioZNK4suXyxc92BA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIAjs5YC7bd+WVZuB16l91FMd72zHjUSKcM66vdJsqrFG\n-----END PRIVATE KEY-----", "shared": "2bfd8e5308c34498eb2b4daf9ed51cf623da3beaeb0efd3d687f2b8becbf3101", "result": "valid", "flags": []}, {"tcId": 196, "comment": "special case for DA + CB in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAkxbAbSeySrxnP/tRBcW5qJvfqnnoHNu4lVYHQ3fHAyA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKiGAz6dwrapE//7wr1ALowR7DTUnA3A+hQpMptpSihf\n-----END PRIVATE KEY-----", "shared": "d53c3d6f538c126b9336785d1d4e6935dc8b21f3d7e9c25bc240a03e39023363", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 197, "comment": "special case for DA + CB in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAikF5gHsHZJ4E9xG/lHOnmZP4QpPkqLmv7kSiLvEACyE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJixzCAgqOxXXVxGx2Akz3x612KOuQlzC8T0YKrw5tpL\n-----END PRIVATE KEY-----", "shared": "4531881ad9cf011693ddf02842fbdab86d71e27680e9b4b3f93b4cf15e737e50", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 198, "comment": "special case for DA + CB in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAp3MneuECn4VHSRN7DzoCtbNWC5xMpNvesxJeyJa4GEE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMjhk94WKqNJo0MsegwFIdksvF47+CYV5ClV3WfsEjRf\n-----END PRIVATE KEY-----", "shared": "7ba4d3de697aa11addf3911e93c94b7e943beff3e3b1b56b7de4461f9e48be6b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 199, "comment": "special case for DA + CB in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAHs6ys3YyMbw8mdxiJmoJq102YcdWUkzdxaq87e6S2mE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIjgEjezNgFAdWdggq+95R1ZXUfh+lIUtRo1Grv2SRRC\n-----END PRIVATE KEY-----", "shared": "bcf0884052f912a63bbab8c5c674b91c4989ae051fa07fcf30cb5317fb1f2e72", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 200, "comment": "special case for DA + CB in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAmirLs7WjhqYQLjcovjqX3gOYHVxx/S2VRgS+49PQzmI=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOgjE+RRoZjc5K6VxoMqgoHYR/yHso2wD+Q3V8FsxJxK\n-----END PRIVATE KEY-----", "shared": "e5772a92b103ee696a999705cf07110c460f0545682db3fac5d875d69648bc68", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 201, "comment": "special case for DA + CB in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAJ0MOHC0wiXCLylbXpa0DeSgo1HaFthMeAj3QgIcWuGM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICgoWU0Wdo5YbfOWAezIbT+tY4nYcrU/yj7cr2+5WPZT\n-----END PRIVATE KEY-----", "shared": "378c29e3be97a21b9f81afca0d0f5c242fd4f896114f77a77155d06ce5fbfa5e", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 202, "comment": "special case for z_2 in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEATvNnkBqsi6kKUODPhspOSj/xZPsSFgW+NG4uSNBKyRI=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKhPSI4ZMTn5hrDlskljWxN9OF5CA0Ku8fGU/N4f5ehQ\n-----END PRIVATE KEY-----", "shared": "7eb48a60b14fb9ea5728f6410aef627d1522fad481b934af64e2c483b64d585f", "result": "valid", "flags": []}, {"tcId": 203, "comment": "special case for z_2 in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA0d4wPE3dBdV8Kd+SrRct2Mj0JOY+yTRFvq6kT50SSxc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDD9KngeCVw0pIOQez3S2L0nNuJ5YXv6a4tODhz5D71G\n-----END PRIVATE KEY-----", "shared": "b71bdbed78023a06deed1c182e14c98f7cf46bc627a4a2c102ad23c41cf32454", "result": "valid", "flags": []}, {"tcId": 204, "comment": "special case for z_2 in multiplication by 2", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAW8zXOf11F9k0S/aysPGaHgw42TSaJa0flK9KLNz16Dc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICgxLhe0fdMtkFYRaCRRh5Y8dGmjHIgeSlyUOEJitxlZ\n-----END PRIVATE KEY-----", "shared": "5bb56877caf2cdac98611b60367fbb74265984614e5e73996e8ea1bd6f749f1a", "result": "valid", "flags": []}, {"tcId": 205, "comment": "special case for z_2 in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAinqTkxDffqdoRU31G80N+9e+T8uy/8mEKdkT7GkR8zc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKh2QM+CN7Rzxjiz6d8IZE6GB+VjtZZDY8zEITOymWdC\n-----END PRIVATE KEY-----", "shared": "b568ed46d04f6291f8c176dca8aff6d221de4c9cce4b404d5401fbe70a324501", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 206, "comment": "special case for z_2 in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA/jWQ/Dgtp6guKNB/r+QNSvyRGDpFNuPmtVD+6EpLe0s=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHgMW4gnINheXd+vEDPpoThd+eIWie7aTcx0RK0oMwpQ\n-----END PRIVATE KEY-----", "shared": "11fb44e810bce8536a957eaa56e02d04dd866700298f13b04ebeb48e20d93647", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 207, "comment": "special case for z_2 in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA+tmrPoA7SfyBsn7mnbb8n9uC41RTtZ74+rKjvrXhE0w=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICCeXgrhmUvYWc6JkrYuw6Zt8utQIyvMOj0ntmFPawFN\n-----END PRIVATE KEY-----", "shared": "85d9db8f182bc68db67de3471f786b45b1619aec0f32b108ace30ee7b2624305", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 208, "comment": "special case for z_2 in multiplication by 2", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAmL7ZVfFRbHpEJ1GsWQBG19Uspk9234K+CdMuXTO0kHM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIBtHe5f9q6oSoSJFpkaie82JVg+G9SuCz3SXCUkpP9G\n-----END PRIVATE KEY-----", "shared": "61d4ef71cbe7be3128be829ab26ed3463eb4ab25937c309788e876b23412aa7c", "result": "valid", "flags": []}, {"tcId": 209, "comment": "special case for z_2 in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA5ZvkkXs/Bbb8h0jJuQ8bkQJzycbhf/lu9BX/PZJ9mH4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIAD5iwKuDfUnTMiZ9SbrG4dyieCWNEClfdl+QUzdL3xR\n-----END PRIVATE KEY-----", "shared": "5ba4394ed1a664811b01557944becf7585652a8acbdbf806742911207bd79346", "result": "valid", "flags": []}, {"tcId": 210, "comment": "special case for A in multiplication by 2", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAjJiFomyzNAVHAKJw96X0qsBrrYJjtlHr8HEuyh67ZBY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINhsGPK+OWs7ty8i5uziLic69uFQahwJrU0BvdL0OfhD\n-----END PRIVATE KEY-----", "shared": "a5952588613eb7a5cd49dd526f1f20a4f0ffe9423e82cea302c2dd90ce559955", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 211, "comment": "special case for A in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA9hNf6XQcLJ3n3PdifvCIMvNRyzJduzom+TorSGIOFyc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPgarbkFPraYmW0PeB2c2mf4Ld76OYfSdv9alP/fXSVf\n-----END PRIVATE KEY-----", "shared": "cb6fb623084b6197443ec9ba1050c0923332e5e829ae0194269cfaf920a43601", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 212, "comment": "special case for A in multiplication by 2", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA9v//////////////////v////////////////////z8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDBbTbQyG0kj/FWb+R32d9DhLDoxsW7GVctwi3WdfBFN\n-----END PRIVATE KEY-----", "shared": "9e526079c2fcf12426ae6c2a54b5ffb70f2ec662e29ea5ce0c8385c3b21cd162", "result": "valid", "flags": []}, {"tcId": 213, "comment": "special case for A in multiplication by 2", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\n<PERSON>owBQYDK2VuAyEA9v//////////////////PwAAAAAAAAAAAAAAAAAAAEA=\n-----<PERSON><PERSON> PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJAGONGXmALbm1Lk3YT6GVefYc177zwLYvzMrqoV+khN\n-----END PRIVATE KEY-----", "shared": "6329c7dc2318ec36153ef4f6f91bc6e7d1e008f5293065d9586ab88abb58f241", "result": "valid", "flags": []}, {"tcId": 214, "comment": "special case for A in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA9uugFovj02IYIwidgQ93zQyuNM2iRMXZBsXUt53x6Fg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDhXXPfIaR7Mec1fjX1HA6pIWS/25/ZHMcLZihmurlFP\n-----END PRIVATE KEY-----", "shared": "603f4fc410081f880944e0e13d56fc542a430eec813fad302b7c5ac380576f1c", "result": "valid", "flags": []}, {"tcId": 215, "comment": "special case for A in multiplication by 2", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAYGd6XZNMy/q4/12PCFoLVT+UUn2cSa4UD47RNeFEm2k=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOiL0CxwFlR6JPQovCqdzMrWxviAwXv/z2b8aEWWJ69O\n-----END PRIVATE KEY-----", "shared": "834bbad5470e1498c4b0148782dfe630e8bfadff1997de802ac8ce302a1bda28", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 216, "comment": "special case for B in multiplication by 2", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAjZiFomyzNAVHAKJw96X0qsBrrYJjtlHr8HEuyh67ZBY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJA27X1o90SKxEDcUSFrSYQNyr09XjLjtP/DKl/p6WdC\n-----END PRIVATE KEY-----", "shared": "ec9070ad3491a5ff50d7d0db6c9c844783dde1c6fbd4fe163e9ade1ce9cd041d", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 217, "comment": "special case for B in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA9xNf6XQcLJ3n3PdifvCIMvNRyzJduzom+TorSGIOFyc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJDFXneqD+SvsShxCf0BD1JjZN6hjYji/YcKwBtm4/pO\n-----END PRIVATE KEY-----", "shared": "dc6d05b92edcdb5dc334b1fc3dff58fe5b24a5c5f0b2d4311555d0fc945d7759", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 218, "comment": "special case for B in multiplication by 2", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA9///////////////////v////////////////////z8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKAhui/U461XvL8gTW9sPoAY2JeFUmM7bf8bdEe/UpRZ\n-----END PRIVATE KEY-----", "shared": "1b174b189981d81bc6887932083e8488df8bbbed57f9214c9cfa59d59b572359", "result": "valid", "flags": []}, {"tcId": 219, "comment": "special case for B in multiplication by 2", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\n<PERSON>owBQYDK2VuAyEA9///////////////////PwAAAAAAAAAAAAAAAAAAAEA=\n-----<PERSON><PERSON> PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDA1CD6YSDdYf2tzRq+HG/P8lYHFDrVcg676vu1ozuNJ\n-----END PRIVATE KEY-----", "shared": "15a052148abaad1b0f2e7481a34edb61403589439b5bd5e5646cecebe2a1be2b", "result": "valid", "flags": []}, {"tcId": 220, "comment": "special case for B in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA9+ugFovj02IYIwidgQ93zQyuNM2iRMXZBsXUt53x6Fg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDBDXOGH8nI/mjveoO74kiB+FS5M7omF+nLS20FHvSpT\n-----END PRIVATE KEY-----", "shared": "1d048cbe2f8df07c233a8f93706f307d17130c2497fb752eeaa31fe3edfc725a", "result": "valid", "flags": []}, {"tcId": 221, "comment": "special case for B in multiplication by 2", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAYWd6XZNMy/q4/12PCFoLVT+UUn2cSa4UD47RNeFEm2k=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFgPCpu6coGjD7AzSQ4PQp8i4/JnhSyurO+j5SkfDmFO\n-----END PRIVATE KEY-----", "shared": "cb92a98b6aa99ac9e3c5750cea6f0846b0181faa5992845b798923d419e82756", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 222, "comment": "special case for C in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAyCObcQE2/kMftNmENhV+R8nnihDwn/kumLr/FZkmBhw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHCQmP6y4lxntL/TvgoBr0Ca222lKz++PZcGQt0smDhW\n-----END PRIVATE KEY-----", "shared": "f1bd12d9d32c6f4c5b2dcb3a5c52d9fd454d52ca704c2c137956ec8ad9aef107", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 223, "comment": "special case for C in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAt6L3ng3ptYFHaRtVRtnsRj2oMl4UQOWLsgqhKdG5cyc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBhaxi5yn4hSiVCSbA3nxIHJJL+c8moSL0Q7hh6LavZA\n-----END PRIVATE KEY-----", "shared": "e6f1c494c9e4bd2325c17183e82d31ab0bbee6c847d4b0e4a99c7c6891117c3f", "result": "valid", "flags": []}, {"tcId": 224, "comment": "special case for C in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEALcYk4WY/Qqe5M2NQ8ndUG1C43cfuDYYTOtUyc67U5i4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPA3Q+6tfC93GXlDJPJxBygX0aBMvaQrIy877kPzl8xA\n-----END PRIVATE KEY-----", "shared": "aa2a12edf752d279bdb000fb1405a5df8c5f1d41309b4f2bd41aed7ac1ed0149", "result": "valid", "flags": []}, {"tcId": 225, "comment": "special case for C in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEADl7O7pEEpk+CyQk7m/e0B27lvHCBWvfun5Qu8BV1YXY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKj7tPkNpFeUmBQF1Z7zEGIePDtrd2C14wMIx4IsiK5f\n-----END PRIVATE KEY-----", "shared": "74d5606ba0b6ad1d8ba36ae6f264d6315f479b3984de573e9b001e0555247c32", "result": "valid", "flags": []}, {"tcId": 226, "comment": "special case for CB in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAc31FR34r63emw4uY4qGbBcOV332pmMuR9t+rWBlhTyc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMiHiG/QcQfHIh9tndNsMF7Hec7KEyrJM/932rK+rGNF\n-----END PRIVATE KEY-----", "shared": "8cf4538ae5f445cc6d273df4ad300a45d7bb2f6e373a562440f1b37773904e32", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 227, "comment": "special case for CB in multiplication by 2", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAhz+LJg6p2d2sCLewMHJ78AcjFatUB17MOTo3qXWIK34=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFgJbuKTYZePYwrR+wDBJnxakB+ZxQL5VpuTOtDczg9Q\n-----END PRIVATE KEY-----", "shared": "d5766753211d9968de4ac2559998f22ef44e8aa879f3328cbc46aa858dcb433c", "result": "valid", "flags": []}, {"tcId": 228, "comment": "special case for CB in multiplication by 2", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAdeFYfF7vyDcV1xAgqmvlNHu57J2Rzlsoqbu3TJLvQH4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIAgppJBG3OLAerKEQNutFGRT4SiWDoXdLmppoVEoc91E\n-----END PRIVATE KEY-----", "shared": "761d8cecf13f93b379a772e5fac5b9ffe996cad9af06152580afe87ff9651c71", "result": "valid", "flags": []}, {"tcId": 229, "comment": "special case for x_2 in multiplication by 3", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA+FoGBl6iUnI4/F7Bt16tkmLmsa7WH+/4O5EjCutLfQE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFh6w2uaI1lGMmea3qGoJvL2LXlzgiD7SHRkA582yiNy\n-----END PRIVATE KEY-----", "shared": "f12acd36f6299a4d192c03aa4efeea7df51e2d15d763172e68accf7bc6f5c230", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 230, "comment": "special case for x_2 in multiplication by 3", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAbg8dALEJnSpx976GZV/riYi7pVd7AvlkBDpJ8Ax0lhM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKikQrfAqZIntMtcdfueWnLOol66igvfBycbtKk8K2Zl\n-----END PRIVATE KEY-----", "shared": "b2bbbd173f41d952d329251da973a9500300628177ad0fb79d01e2e263905b38", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case for x_2 in multiplication by 3", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAaWdXztMJf6lgyDkKCei9bTkNvejR+hcCYfNCLtwZKSk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINj3Iz6WEsAMncosdR7B0/X2e613wucUog5x6z8iCmZx\n-----END PRIVATE KEY-----", "shared": "45ecfa275f1daa25d3fadf33cdf89a152afea25eae37e68e00b30c367789887a", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 232, "comment": "special case for x_2 in multiplication by 3", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA/YSz8vv6Fq6/QMJ/RuGNd7r6DHlxvt3kkJIS53G9PDU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINgMfHVXyZB+GxHoRL8Tacumabw46beyU+UfI5vaMiN0\n-----END PRIVATE KEY-----", "shared": "595e144e07bbe65b38e0e4163d02ad75a65e422e74067db35c90dfa6e055d456", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 233, "comment": "special case for x_2 in multiplication by 3", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAgFSFcDzPxKIh7ygSZ/UrYc68h58PE7Hl9SHBc1KgeE8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIACqFEVrXtBxQ+E81+sdQ7o4Zc0gHECgw/2owa+7URk\n-----END PRIVATE KEY-----", "shared": "226e16a279ac81e268437eb3e09e07406324cb72a9d4ee58e4cf009147497201", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 234, "comment": "special case for x_2 in multiplication by 3", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAgGQqMnnaa/X8E9sUpWnHCJ2wFCJc/K59/1oNJezJI1s=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHgtsMjj5o8Qb+DFZBXgvRPYEt6g6Uy9GL32dhKVYTpt\n-----END PRIVATE KEY-----", "shared": "790d09b1726d210957ce8f65869ca1ec8fa0b2b06b6bcf9483b3eb55e49e9272", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 235, "comment": "special case for z_2 in multiplication by 3", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAhOgn94yuDPBj5DQBmPeIwoTgdDCzqUo4c984sfhyzgI=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJCfsL2/U6aaL+OciySXq9T6V9LVTgRrX1FFleLA8z1j\n-----END PRIVATE KEY-----", "shared": "684cc83af806bcd9cd251e1858f3c10f0166e0a0cd2be154339a886b13e7c76f", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case for z_2 in multiplication by 3", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA1EXh3wCDu2uOiG5mMiUYBxcdTojEGBb8aENzwJ1+XW4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHimeQl1ckhmX3k3HrAUglq2vUrzVx8UA4nGNuAEvPRr\n-----END PRIVATE KEY-----", "shared": "e426e4a3c54d3e77f4f157301e0ac7d9e12337a2b58df16780041cf6d6198c5a", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case for z_2 in multiplication by 3", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA8mqmFRpLIjkBdvYjPnQvQPLs1RNxZvsuHsmy8kVKwnc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIChqMC1bB20qunwqTa+efMnYU5t8A5EwfbZaL0Ig0w9w\n-----END PRIVATE KEY-----", "shared": "862df92e25277bd94f9af2e1dda51f905a6e2a3f6068a92fabfc6c53da21ec11", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 238, "comment": "special case for DA - CB in multiplication by 3", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAKwLbPIJHf+Iap6lNhd83n1cchEm0PL0GBdCsxTxHLwU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKg4tw0XFhyzgiL3vGmjyFdgMtWAJ1s7fWP7oIkIy0h5\n-----END PRIVATE KEY-----", "shared": "3f438dbf03947995c99fd4cb366ca7e00e8cfbce64c3039c26d9fad00fa49c70", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case for DA - CB in multiplication by 3", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA1x3X2xIjMMm7qrXabPH24cJTRe5qZrF1ErGASs4oc1k=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILBzO0IDJnqzyUxQasrblJp2zGAEhvzWAUePze95wp1s\n-----END PRIVATE KEY-----", "shared": "95f3f1849b0a070184e6077c92ae36ba3324bf1441168b89bb4b9167edd67308", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 240, "comment": "special case for BB in multiplication by 3", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAc3vAfeBym7z77joI5pb5fzdwV35LAewQj1nK9GQG0gU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINhEo2tYrv2wi5gXlgKaJ2YQGISzSPcO7ZR8JUEGTK9q\n-----END PRIVATE KEY-----", "shared": "6a969af6d236aba08fa83160f699e9ed76fb6355f0662f03dbc5915a3c23063e", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 241, "comment": "special case for BB in multiplication by 3", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAl1gGGns+LAL7XCCHWua1WxH7Z5WZCg9P3NEUe+VSFgc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKC30xLZuDLhJNG8jLIdtUVEDjzxTnRz7pzL6baC8hVs\n-----END PRIVATE KEY-----", "shared": "ab39db4aa29ac4017c7446f1ad0c7daa9a37f1b6b4f2e9d2902ccefb84839d28", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case for BB in multiplication by 3", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAN81l0zA2IF80SehlWlDUsMhv7AIQC08tt9qS3PXjqgo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHh/Hd14zGRz0+Y5SUCa0/Nb/gzgc48lXe5oLyv7yA9/\n-----END PRIVATE KEY-----", "shared": "13de41659e3e308d6e26c94282fcc3e0364ddf0809ddee6c8e7abb5091b02b00", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 243, "comment": "special case for BB in multiplication by 3", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAqbboCBRgODrcWHyPkaAsWaejVXbKYkNszRtf7xuSVF0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIECArmCoXB+pWq2b6r2YtAXn8oFBvwjyyaT9veHFaAJl\n-----END PRIVATE KEY-----", "shared": "69ed8a0a27812ae6741474bd5c6a4e683a126649f7245aa0f91a3a384bcde25a", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 244, "comment": "special case for E in multiplication by 3", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA/Ros0XqT+FDeuMRaLTRTkjLf2KVYMEIJeBxstYIphw4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIAj59KT6xNtBMxX3SlmBiyRS/Ht2hVkuJlVndfm4bZB/\n-----END PRIVATE KEY-----", "shared": "010218bd67b1b92fee3e7fa4578c13617d73195de10279747e53ba01a254525a", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case for E in multiplication by 3", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAuIEZ5a5tnmuRLVJSRznmEu8Zq35d09lGy5vAA8N4+B8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBiIz64whYZ2V7CUNcQrdMx2JFeDlFGjZZ2yGNQhT91j\n-----END PRIVATE KEY-----", "shared": "e6b298de9cb6358fbbb00f11890f5714a3858e8f05a2a8d1cf39fe78cc55dd4e", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case for E in multiplication by 3", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAe3Dinc4Eec3ko2x/l4ZYLxBLwHiPBGtIr0leZ724jzY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHic4T7QB4GNelGB5inu2USiCgWM/jlmnJgxv6UhWhJp\n-----END PRIVATE KEY-----", "shared": "967bbe298494b4a5f95853cfde9dc85970b2a4b5dd2c92782901e853957f5809", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case for E in multiplication by 3", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAKiCeKs4OPWlz/790A/mFf/l6X9zSfyxwmLRE/DwWZzg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIAACK0N3WrL0uRvBy1TJf3gCYonqrwKr7tBMqE9zbGhs\n-----END PRIVATE KEY-----", "shared": "9f66848681d534e52b659946ea2c92d2fabed43fe6e69032c11153db43dca75b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 248, "comment": "special case for E in multiplication by 3", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA9QcJrKfzFOjQW1/5ekJ+QnvV6FxOhnEhJQdqdxviFEg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIICXpS/FYuilFmgvU2PMXnyI6ceOMI3w3u9ASXs1zBJ9\n-----END PRIVATE KEY-----", "shared": "ea7572e27a9120de1f13b85710ba69a3471b7b3f5d12bc430c12c4bbf8aa3957", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case for E in multiplication by 3", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEADxOVWXi5PXufmi5w2W35IoUKj/2EEuI2+wdK75nTfVQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEAogCAw2KgiGnFg7rvxhGEWwcJTq8Rn1uQ8uFDxRZhg\n-----END PRIVATE KEY-----", "shared": "e23d63a46be67c7443c07b9371ff6a06afcd7a5794bf2537926074b88190307a", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case for E in multiplication by 3", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAGP/pkqcpznDDt83FW6tV8iENJ5E0swgqn2gtOgsTEnM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINhRXUXHqyuVKYFlQxUAaLjku2FM8raKipk2OXWvUD10\n-----END PRIVATE KEY-----", "shared": "33ccaf24e1e26290ed7e462093e9f77607ef52a0626b2cd2511c41cd24c13849", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case for AA in multiplication by 3", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAw7ooBXco0FM5Zew0l5/nvZPPbLZE6NoDi6qHmXuNwg4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINiBW9FEUY+lJr7903P1+c/yVNXTxGYOipDvKiLGh2p0\n-----END PRIVATE KEY-----", "shared": "74f95b4700f0185f33c5b5528ed5012a3363f8bbd6f6a840aa1f0f3bdb7c9650", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 252, "comment": "special case for AA in multiplication by 3", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEATrCVqG0eeBuxgiMwdevx2xCdVxNb+R1U/bGOs3FCdkA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKgtmWCT7v2vKD9ASbuk9a9uzC5kiU8yXuH5yh4VbQVn\n-----END PRIVATE KEY-----", "shared": "e9677b854851c41cc489e03981ae78690be6cbf0054ea9834759de3e27bcf03e", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case for AA in multiplication by 3", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAg/Z9fJKxHI+wckhGQqAfQ96wIrVNlKQBXjmEmi4ulVU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMAmCd89VDbBI9zX7hHyPx2jIWZsCfN503kUIDNAUQhh\n-----END PRIVATE KEY-----", "shared": "f148716ebe7269a7076f0cf1f22b6978d3c7e3607b0bcc87a8c7a85b9fd20c2f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 254, "comment": "special case for AA in multiplication by 3", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAIMx103bYRTudBJyE9Y6vz2ESbAigNmHnNfCoviKP1GY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKDjt4wPO+KnYLLJFvJE3yGWJP3aLp4xsVMo9Kd2kClq\n-----END PRIVATE KEY-----", "shared": "1d5c123e88e9dc7a3b16ec90b60578dfca7e11eab9b88c6eca7bc33d91fde83b", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case for AA in multiplication by 3", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA7zG0PRnApUNN61YSnBYpijlKcDKi5Sy5l0dr3soyW3M=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHAfEwopBYTLKMfWU5UGoaBU+SahfvfFaK5DBHwF4Q9g\n-----END PRIVATE KEY-----", "shared": "2fc065ba8f5040a0a659f6f7330554bd1b9d7c893b91e316e0af90c37af4f135", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case for AA in multiplication by 3", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA2MjixvM6mFJd83Z9HQRDDasL2kHx+QTJW8YcwSLKynQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINDmf2gYOkwa7ZxWhks2J4u3u3XVengyG8fCT/YWNmB6\n-----END PRIVATE KEY-----", "shared": "ef7612c156078dae3a81e50ef33951cab661fb07731d8f419bc0105c4d6d6050", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 257, "comment": "special case for AA in multiplication by 3", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAGDNhlRa4DbDAWyJVCeZpjfAo2Dtm7Wusbw9jCJcNLH0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIjrd3XazDKwRc6zXyYbNhYxXvqYt4DgjHnVRO2ttUZ9\n-----END PRIVATE KEY-----", "shared": "a3cf3d81ec56896a68fca0da6335171d0c622568738c0db26fe117033726a049", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 258, "comment": "special case for AA in multiplication by 3", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA4umJqtI5f8NLbL4tsn1atpsoBIODyR2egibVSCU/q34=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHBVscBXbnq2yJ/MHOSeecjDcb+fwrIrj4OWqbZMWuJt\n-----END PRIVATE KEY-----", "shared": "e7f45823a45b6a46192b37d73e8609b5bda68cd7cfbdccaa49082080993e640f", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case for D in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAub15NiTWp+gISGEQBYhT7bJeE2vU1qeV1tLvU7JeOAQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJBqm/z9cQFNGJZ2gNRQnqpBxmZCSvmL+f9/9J6xurpB\n-----END PRIVATE KEY-----", "shared": "7c6148134c9e8b2ba5daeca41e6a1f3a82d8f75d0b292b23c40fe7f5ce0a2b7a", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 260, "comment": "special case for D in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA4/RE4gjakEPz90wg4o1/QEu2h6NGcJq81VUVb4hgeCA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICg5KxsDWoRlqiKqu1cQYcbv/u1AzCUwtijk/UA5WuBK\n-----END PRIVATE KEY-----", "shared": "ea5e772bac4693ce69ea3ac761011fa7674037653a433c7f05456e7291cd3c4e", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 261, "comment": "special case for D in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAh7Q/kPdtEvs6Rp+oaHwn42nUqC+Vz5Xo3Dlw3o+G2Ss=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHjLs1IEzIhnbBTg/xgXE5LpmEEbI9kF1MTc6rcFEfRC\n-----END PRIVATE KEY-----", "shared": "81c395aed5cc5f5e2a206a8a4cacecd501df5b81e49433835ad8a3779edffb30", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 262, "comment": "special case for D in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAhkQeoGxc0qNMa1EmHpOi8w6n2w904UxC8PxEPGc1lzw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKgiW0nve3Mw4954fLxAR5ZE23qxJjcClclBiWc0MNdF\n-----END PRIVATE KEY-----", "shared": "513eba5870dc5187e2552fe3ba8292b516d2af9ecb9a9bdc51eac2ce2de40112", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 263, "comment": "special case for D in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEARiSqSunRJyW/krhfk+PozqFre9g/2g6xj6stvg6L90I=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIAhB4aXHQguUtsxpkTFuvdYIYmM5wJ0PZ7JAiFiLnQ1J\n-----END PRIVATE KEY-----", "shared": "983b7e236ffaddb4b759b7353fe87846f59fb6f28a3ed65c256176b6609b7c6e", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 264, "comment": "special case for D in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEApiWlt6BM6kYtEjtIXDnqRKgHmqIjxZ6cqXq80wtQDks=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIAjs924xojA56ooV7kdLYlGp1yW/8aV1HrXs3p19Ti9J\n-----END PRIVATE KEY-----", "shared": "c941369b085c7465d50d23ceaf6717ab06e24638f217a7b8055ce8ebd3ca1225", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case for D in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAil8gY/JZ8zF64+C0WfgsRndmbkmi65vwNpruZjYxJls=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGA4+wqDDRAByo6nSmE+qY9quFEmROVejUWikHG9S+9F\n-----END PRIVATE KEY-----", "shared": "a3f7e169db44d0d179c242e66347364ab92744dc6ad80e4775aef7f4ff9d5f34", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 266, "comment": "special case for D in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAVM+2rQ0D4xFayv7hJgY5fyu0aoxfMmolXElBGK6tO2I=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMBM8SnwszMy4mVPjkUiXAQtf6bLx5PIi9THMZhSibBF\n-----END PRIVATE KEY-----", "shared": "401aabfbb73fe6694c446ecfffb43006427a9d4756e049a1ffc79578d62f1660", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 267, "comment": "special case for E in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEADuO+6Ms6CvzsIvoiM3BujsKczxryEsCmdHReu6NPnQg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDgGsDbJLXvAdxmY0k29opRbYB1CRJvT7Eu/N1fQG4lN\n-----END PRIVATE KEY-----", "shared": "20322dd024fb5a40f327cf7c00da203734c2a279b9666a9ff7d8527c927b675e", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case for E in multiplication by 4", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAeX7HUSr78K2RjQ5JR5A76VI086vzZ1Co+FSIjRF7d04=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDgNkFa1ovSz3/sw5s63IqxGhCRfG++vtWYbyMeprUxD\n-----END PRIVATE KEY-----", "shared": "46152d59c2d2f3ecf03ce652d2b6978d401d5ede4570a6c911771bdcfb37cd41", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case for E in multiplication by 4", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA1XDHgQ9p5QKzVSU6+nxme/pQYNkNyG41irRF9jgeQV0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDhJKaQsjY3xRtuVCOLyGk6M1NmcGxM43xekV+iK+wBD\n-----END PRIVATE KEY-----", "shared": "37567f7ec0449c7b823cf7b0e219e9dd880e56a1464d0417a9e67eff42332866", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case for E in multiplication by 4", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEALGEcuURI8ceCJCWkz1NWI2uQpVWx7UdHggun9znI9X0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEiphoJbJoDi8lR7p1qVmbBO1X+O0Y2Y5wmcVE773yhL\n-----END PRIVATE KEY-----", "shared": "fbf6587ec181116cf1ace7dcd548029d69c130e50fcf6ad5dfcd25c23ee9f939", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case for B in multiplication by 4", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA5VnEF9p/1YUTUvUIuQAx1JtdLQqsiKnItftugBZawQs=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJhFKtffTia8Sz1AP56/crste2t9WGDb9vuaT3jcAnBK\n-----END PRIVATE KEY-----", "shared": "c7c6f6d7ce1e4f54c727e5900686c34e6a6953254bd470bbbf0c7c18bbddad73", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 272, "comment": "special case for B in multiplication by 4", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAdG2X53dCkqPXA/YE552HZMmaai/igOqpgREV9eA48ho=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKjbyb5QNO1/5/RpJk8hNenGfNMPUlVw0thB5L3qxSNJ\n-----END PRIVATE KEY-----", "shared": "cf7d2a66ea4dfed94469b2d343533ff302a576f8402ed2187904437038e54665", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 273, "comment": "special case for B in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAHzVKqP/E6uK0Da0uv4MNs/6wfioaLaOeVd+HyMYT3h0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPjSaHjf8lztAtOyfOdAAmlbuHmzxDKJMJNDFeyuhCtH\n-----END PRIVATE KEY-----", "shared": "b204d3bbcbdc624f9f1a743fa3daa8f4c8785ed088d37d08cd13c601170a461b", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case for B in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAnD8AI+GkgyWGrySDu+xkzp8G8+qAbUAZpeSrsbVicCk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIND16cQ8lbH/w2+DK5Q2AdXhdkf3144udxCs5j/ydNRH\n-----END PRIVATE KEY-----", "shared": "b9f21465615f39dddcc37520ce9b956f7de9883ac93a870d74e388b8e1775463", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 275, "comment": "special case for B in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA0FZWqgFNR2Ai38VejTtIhO0L34Ugm+i1U1E5TVK+aEs=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHAGeejCTfgo8uUhKjJj1ek+phZ5mIKYurO0gPRvlhpI\n-----END PRIVATE KEY-----", "shared": "20f1fc613874495f20562c10b7a8be47bfc12c168d829d6321aa2de17060e40d", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case for B in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAxKGbhobhjCk1mqVIQn8Go2jVWoc3SD1Ik1I62sZ5Wkw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINDQd8lGH3R+VmC+hcxiBCi0zv6AXeD9JUrapGXqXnhP\n-----END PRIVATE KEY-----", "shared": "652b18ffd41cfb7d1f0b6dc79baa3b2a392ef1617f5cf6259b5b4ff065916a16", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 277, "comment": "special case for B in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEASYneeYU/81voyfkvyUZ0/u84oOZXiEccUh+OJZrfAV0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIABxGsCO+Iw9Q6PL2me2/l809Ucj2+bXJcijVpBwq5pO\n-----END PRIVATE KEY-----", "shared": "679825c259392d86f8edb15328d4faf52300779d979a503a76e27be3d7a85e03", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case for B in multiplication by 4", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAqYFIPLDqQ4X/u1UoJsPdEQ1K6J/1LtDNYBj5nTOHmHs=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJiadbQEUROew2ymqgQ3ZcYaGL4yOlmH/LAlwtrY1L1A\n-----END PRIVATE KEY-----", "shared": "9cadc14ac153fa383ef66d1833f589100dff90523272e32b06e2c6f1f4424040", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case for BB in multiplication by 4", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAHfPf2rdP84F32sKUstovSaNIvDs7xs6TEr6l7z7N0ws=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJDDz+3ZGaLM1R+0VWSeOtLaHvD/YZtZp/nFWmioIZZF\n-----END PRIVATE KEY-----", "shared": "bcc95fb4890ed311f3fb4f44c2b60866cdddec97db820a7f79f475337e16284a", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 280, "comment": "special case for BB in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA/Gtxi6i0fSSxz9a10N2LIP2SCWD6vDAtvk+TvSoG6TM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOj+9cm2D4SYTog21TWss3IJa6gVmCSgtJoX7M2oQ71B\n-----END PRIVATE KEY-----", "shared": "06f1b495b04a0010845c9d39b13bf2784ade860d9632c8847618c0b34297c249", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case for BB in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAsnm2wGX5XHBA8Ui8tKPTEONL2wBZMah5vkaVc97t0EE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMDgW953J9tONStefwNTJ7TYakLVE8oRbiLWSk7eVkNK\n-----END PRIVATE KEY-----", "shared": "cce7bb644df94501421db49d15e821c7b0aaabecdf8837ab989b1f23bac08f35", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 282, "comment": "special case for BB in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAmOLNTBBVTkGwo+QQgsi2thtVRH0mwKqX+aBrrutUtVs=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINhzCL91NXP1lqyDMLIEAUshUtvfyYgaDZl1BYWCvfZG\n-----END PRIVATE KEY-----", "shared": "71fdd3405c30805701ae4dfad98c493aecfcf2e3b563e7068373c1b19137c268", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case for BB in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAhyiX8b0YhdoIudA+RoEQRPuwQYa6MMgG84uU69wnGGo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINgAWaijh+Fvbe1ufpgOgG0feLRwu2EQPQynBiPM7otP\n-----END PRIVATE KEY-----", "shared": "bf280aeecb74ab34e1310aa6fe8dc972f94dc40c7f88b72137ccfe34ed343c13", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 284, "comment": "special case for x_2 in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAwI9ydg2ctKVCqtbir3d5IMRFY72QNWFow2CMa5ry7w8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILCk/mNRUWm9gmObUV/35cSshbugpTu6yoBHfrO0JQ1E\n-----END PRIVATE KEY-----", "shared": "72566a91ccd2bcf38cf639e4a5fcb296f0b67de192c6091242a62fae467fb635", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 285, "comment": "special case for x_2 in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEATwOEnCTVhFNNdDAiIM/ckOG8Ngu14pfA/Q/V+NeZ5BY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJhCVrEu8VT/bC4dAwgmFky6NhTj33aI2CtZ4WIByRFN\n-----END PRIVATE KEY-----", "shared": "24acb4afa63919621df795206c3929b599ec9d253693895d51a0555072e89a34", "result": "valid", "flags": []}, {"tcId": 286, "comment": "special case for x_2 in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEASVl3GpMeJC1XE9XLdvMzEMaig98WZFYEKJVTgJzaZRg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGhHFB1dQ3evlqKmR8ZC7oFgD+SNNGfjpw8+4xK7YhdC\n-----END PRIVATE KEY-----", "shared": "5ba2112a41b5bb381f202446fa9f23c54d2de149f9ad233753417263840ea432", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 287, "comment": "special case for x_2 in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA9v5pDPVHBJY1uzp3hVN7Q3nJ7ga0YSBJO4vbFS4JyB0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOhfEWTiq2+vYmZ8dLA85Sm0mg4gQbGsD6JC5SLSt2lM\n-----END PRIVATE KEY-----", "shared": "a87c9fdf40c409b9edab481b2cc69687ee1ab92e340c3db0107d40b5de6e7a20", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 288, "comment": "special case for x_2 in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAtGhoGhJ1hQwR037HNq+TmnWnCYUU4Ez8HGyngjmohCY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICgeG7+nEd5pkhpkxdIYPDONtVBGBs4ra0zhzdVLQeFK\n-----END PRIVATE KEY-----", "shared": "3be98798f01e71639f3cb8fd4a17bf273e10c67f8974dd9802eed59d847d4020", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 289, "comment": "special case for x_2 in multiplication by 4", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEALXHoRXCZ4/RF+eKhTxiw9ZFLs19IL5wGm2S/Y3ENQig=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICCqzxkCs81gnX7hXMlkU8wi4omdfReFJoDypyi6xtxK\n-----END PRIVATE KEY-----", "shared": "338c9917dbf11a0cabe8ad4a65959229bc00f99c211e752b20b8b49b87756d0b", "result": "valid", "flags": []}, {"tcId": 290, "comment": "special case for x_2 in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA+o8k6UTeXQA3RtRjA1DA9PYXWjJpwZGEgkEFOY+90yk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIACejp+pk4BNzpTOy5ax3iVoJFqXBZ5NeuEW7NsbrdFB\n-----END PRIVATE KEY-----", "shared": "56e2bfc7f6ab7da8fc734afc515e57d0794d002434f9bc8e18bd0b72c0df3c4a", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 291, "comment": "special case for x_2 in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEArk4371PHniXoJ1pg8vwd/Cd+vF07iEKMZDLD+YSUISw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPAVdGQ/Ix/6wFW9I17nTdQWuUyOVaKrK00TqLeI2QFI\n-----END PRIVATE KEY-----", "shared": "17fa1276d9fd5025172736449a1c0ae33512e5037014a18db5903e47bb3bc950", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 292, "comment": "special case for x_2 in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAleVqgweSR498QlBAQ6nKuOLuv/X9kJg3CeKeA8CkG2Q=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDgApCZZlUKByiZtfPHqnbbXmJGkBqcPnoTDVwpqEtJO\n-----END PRIVATE KEY-----", "shared": "167a3b2fdce9413c89ee892daf9f839a2eea80ea8044924035db1724a5b0217c", "result": "valid", "flags": []}, {"tcId": 293, "comment": "special case for x_2 in multiplication by 4", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAXxaqfMq/Taa2hr0ox0YOEGuxuXqCN5JSd2XCmprY/HE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHCoJrGGliIY26/KETMZ2u+13fPPFOFf4/qtxMCi5GZI\n-----END PRIVATE KEY-----", "shared": "30a4ba793f2dffe1700c61428b4d84b5fcd0aa99a23b903f84a48eca5cc9fb0a", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 294, "comment": "special case for DA + CB in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAR/t4ERgFoRmCo9bF2D6OGJ5/zEYsmr+AXTYlvnpurBE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKhaXtoKJpUAs6sLWElfwlTCaRAorFM0lLX4bUTp3GVM\n-----END PRIVATE KEY-----", "shared": "2bf9ab750bd58ff6f877b783eda45a71a65cc9b7c037fcfef4cb5f4c8842f529", "result": "valid", "flags": []}, {"tcId": 295, "comment": "special case for DA + CB in multiplication by 4", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAA7jKXv0Xd9bWJalF21K4HxEhTa8BXQn9yd99R7mFDjE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBg/KOyGdiTvXspIJ+0HFKVSXvIdXjUDiyTTB6M5GihG\n-----END PRIVATE KEY-----", "shared": "35e9289234bd5e531da65d161a065a14f785076088d741c9a2d886efd7d17921", "result": "valid", "flags": []}, {"tcId": 296, "comment": "special case for DA + CB in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEATspfhzGw+gwQas9Xi4OjUPqBc6KQ8euoA5Vt407rdnE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIiMZET/XrSCsrEL1OigG9zLZfMpNNgCYQbxapE0n0hM\n-----END PRIVATE KEY-----", "shared": "833afb867054b8b9ac70d6013c163e8b7676fd45ae49a1325f3acb75975d8c13", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 297, "comment": "special case for A in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEApVYrS6hrRk3/TCz66Fs4S+IRdx7+ipaX5R2E3kfx6xQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMioXRQLoVD1xqjTyzY7y8t1Nl5RxhZA6XSgcltenVlA\n-----END PRIVATE KEY-----", "shared": "8a914760129575c8ab3270d04b0465fc2f327acaf1676463113803bbb2ec8021", "result": "valid", "flags": []}, {"tcId": 298, "comment": "special case for A in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAiK4WMc0Iq1TCSjHh/shgOR/im8UNsj62Zwk2LsQmSSk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJCjrrFBfD1hwe/vGsBSIY+1XTpZxP6TC1ozzFGDtIVH\n-----END PRIVATE KEY-----", "shared": "c1988b6e1f020151ec913b4fb2695bae2c21cc553d0f91cf0c668623a3e5a43d", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 299, "comment": "special case for A in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAy8TVXVv93QvFxe2+OgSDayxwHSUZWyYiHL6hkxHlWj0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILhY10FL2auaPr6nkGSrh7wFDnRAf01HSPYvpNnSA7ZA\n-----END PRIVATE KEY-----", "shared": "bb24817bd9fff423dc0972908e2c03fddf4dbe100016b459f28fe9594adb3714", "result": "valid", "flags": []}, {"tcId": 300, "comment": "special case for A in multiplication by 4", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA1movn3V34t9KVstRliswVv9cwElMYPOVEXgueZI+3UE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPgl7fH3nt3XFacrOsJn1rLpfhi7E7yv2sWUA3C4W6ZL\n-----END PRIVATE KEY-----", "shared": "b3b4513f8a3102e1ae782fbc69888177f2c24c569303a5d01ab1c3c5e285524a", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 301, "comment": "special case for DA - CB in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA3g/tL6tuAUkmdbx1y+Rde0WwMGzsjcZ2EWmYEcmq7xY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILCnELRw4yS7VqfY/4eI0F6zJ2FhKbhJckgkJepK1PNL\n-----END PRIVATE KEY-----", "shared": "471ba91a99634f9acf34fd7fd58f72682be97ee1c821486d62ba4e448cbc0417", "result": "valid", "flags": []}, {"tcId": 302, "comment": "special case for DA - CB in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAZBjUn+RAp1XJ/xo1gtNdybRMgYSY8VeCyVKE/oaKkUw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILiY8DKXlHR9MyaaOYm2fkOnq1pV+hIQsOXboZP0+glO\n-----END PRIVATE KEY-----", "shared": "cdb3ca02d5fdb536dbc7395bab12bdcfd55b1ae771a4176dedb55eb4d755c752", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 303, "comment": "special case for DA - CB in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAqJvPoja7zPB8Q0tZ+GVfsIW2y+XtY3YoHfgTr7oit1I=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKBSjtmo7CLr6cwuMvr8P0Z1AKmiL1N3OC32YE7c309E\n-----END PRIVATE KEY-----", "shared": "cd3245403fd9edfcf91c9581ebb2eb7c77ad6837fca372479e78de9faf60a34a", "result": "valid", "flags": []}, {"tcId": 304, "comment": "special case for DA - CB in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAzbH5X26swkttApxu2XZmbcUXlNuOSqlmuoUP1/UEiWU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPBoiL3nXWidBWh09kNgAEl9Ititm5Whxn3h3aStoxZN\n-----END PRIVATE KEY-----", "shared": "ab7c47ecb0c0167156f44f66a527264b958fc992c21ce98cef3ae214d66bd82d", "result": "valid", "flags": []}, {"tcId": 305, "comment": "special case for DA - CB in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAlJGoJ0Txy2EFt2sEQuVOYFrGf0ehsrO1UtSG91vZjmo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOA0/Ko65AYD+bIq8Vn9Z+8Ak4CUbekssdg8xInos1BB\n-----END PRIVATE KEY-----", "shared": "1bfa264a7c7229147a20dd021211891e61f5d8c76cd83f0be24bc70e466a815b", "result": "valid", "flags": []}, {"tcId": 306, "comment": "special case for C in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEATRnhVuCE/lgqDrebLxK2HQsD8/IpIn55ipM+6lobYSk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHAqdEjA7Vjh9ODjMtCWo2NgvsovaVXIFbwSCzppHXdC\n-----END PRIVATE KEY-----", "shared": "c46057fcf63088b3a80e0be5ce24c8026dfadd341b5d8215b8afcb2a5a02bb2b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 307, "comment": "special case for C in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAzEcpxOrikuQx7Dpc9QIOGfm+pQ7zIY2aeQA0Umw+4Uo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFACXLUIrU+qBvr9D0ozt0fM8bNXOIXTQmUA1RtWMAFE\n-----END PRIVATE KEY-----", "shared": "d4361e26127adfbe37c2ed8f42cce4ebab8ab74ed9e74f14c3435d612c1a992a", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 308, "comment": "special case for C in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEASkdCSa+Pdx8M+xEW8k/aTEL0E20q+3ZtGykcc8ZmjVo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHCC/FMpmk0w5dDDg8A1k1se7r2UCP5NBLk+7CS+UutH\n-----END PRIVATE KEY-----", "shared": "80dfae7a28bb13d9e51ff199267cec2a19dfc8b6f4974e3446b2f62fe9b62470", "result": "valid", "flags": []}, {"tcId": 309, "comment": "special case for C in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEADypcu+UDE5UxrAUpGD2o5iTSUob2410UB6sfTXbrwmA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJj/fnEdZcx/2dCsEt/ouJTgqTYCyp51vw6rvwv+ZwFI\n-----END PRIVATE KEY-----", "shared": "7a5c373065e339b26ee537cff1cf4597cfcb4bf2dc7c4bcfec9884443281c273", "result": "valid", "flags": []}, {"tcId": 310, "comment": "special case for z_2 in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAL+Edcj26Y1WeG5YUeJPLfshicRgGMW2qhs1Np2nUsi0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILCA9KwedYu/v6iIp4y41iTZe4aIACsgF+NfUvPXx5ZJ\n-----END PRIVATE KEY-----", "shared": "c5edcc5d447071c08dfa8281414ae6a02de753e2f7bb80af5f6253e56db43422", "result": "valid", "flags": []}, {"tcId": 311, "comment": "special case for z_2 in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAmOEhHc9mUfqfLQDrCDrlhVhpoqU+g18uA7MMChm6gFE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOgVv5qWfhIIr450zpr20RPasXwByQ8a4rwl4+L546RK\n-----END PRIVATE KEY-----", "shared": "263a38fe538b50e8e988bf07ae86f33d49886b14c7143efd1d2025c840e36a25", "result": "valid", "flags": []}, {"tcId": 312, "comment": "special case for z_2 in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEALxuTi4GkyQ4SURNa1/q+g19qi8XiLUsqsRn29neHdnc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEBRsBzfkK848Klv+4P41BM6vk+wNbb+b2UnZEfKpzFP\n-----END PRIVATE KEY-----", "shared": "340acf2801de71c18f4c79cfea372bc354e4c8a5eb5c2cce8b45d885df162f45", "result": "valid", "flags": []}, {"tcId": 313, "comment": "special case for CB in multiplication by 4", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEANAufYTVQ0U48YlbK8CmzHK0/5ttYgpTi0683YFpo2Dc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJjAkjYxhOWK1s5RC9MrMJydWkb42e5vZKadgYC7xstF\n-----END PRIVATE KEY-----", "shared": "9efe5cd71102d899a333a45ea6d2c089604b926db8c2645ce5ff21492f27a314", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 314, "comment": "special case for CB in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA7fvW8JqjJDVECwyoukNjCDGWE/jy1QETPFJsP/Vcez0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGhuUcABFtHBkaqdWCO5bllWEC6P519c8jdtmZifb0NC\n-----END PRIVATE KEY-----", "shared": "196182095bcd2ef46b18f64c63607e0ab162a0869e6265ac8ae35e358c3d8a63", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 315, "comment": "special case for CB in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAmwU4zWGLCk3gnkVCD4TVTXRRT7saMcGkqh6TMG8gcj8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICCK8slEKza1Ifw6Hs7+NCqsMIvW5ilu4JHBltwC565A\n-----END PRIVATE KEY-----", "shared": "a3c6b75168211e8e0a49ca815bfe3f469f29864dc8166152b456e7074afa9b5b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 316, "comment": "special case for CB in multiplication by 4", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEArozy/N3nEMLBGEUkvDJDCHTfoIwSX2HWkZ2vjmbbQVo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMDYYabV/5H5HjvQWTQWH/CrDzzn5KK1tPyzGuNLRmZP\n-----END PRIVATE KEY-----", "shared": "deaae6c9952844a3a1d01688e7105b0bbeadc160763c2002b6d0bcf35c22d123", "result": "valid", "flags": []}, {"tcId": 317, "comment": "special case for AA in multiplication by 4", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAKln0eEAtKCnNO2Lp98wBRF6Oc6QssRrwC2uanw5Eyzs=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHB4XK0WCXK3ETGGWbR7V09pQe9toeoGUIsmUPV+yeVK\n-----END PRIVATE KEY-----", "shared": "c204bd15f01a11a2efdabe2e902b7cd0aa079316f60e911b3ee5d46262e98631", "result": "valid", "flags": []}, {"tcId": 318, "comment": "special case for AA in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAg2yORd2JDmWMM+abb1eKWndMSLQ1vDuRrGk9+UoFWFc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCvyOsfh99LVSh/PEaYxfi5l7KKc8Vz/Cc+nEZ/t+RM\n-----END PRIVATE KEY-----", "shared": "c5457487e90932f57b94af2e8750403e09c9ac727e2bd213590462b6937b0753", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 319, "comment": "special case for AA in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAWVGerXmVpt+Ju1TIQNYahIGIEJi4pPg8ai9rqAAzglc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKg8EbKDQTa5qvAVLZDnbjwnF3aTooNOi+2go1cbzmlH\n-----END PRIVATE KEY-----", "shared": "4ed6f8d62932541c6bea16e03835f1f758a5c41722b5c9989c9c7cc08e34e37b", "result": "valid", "flags": []}, {"tcId": 320, "comment": "special case for AA in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAMvNNqEq0v8o2nEuIRpG+z1S+f77RZEnchpadp+qav2I=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILgNh5VzWAZXnnF1mJSTnXWIU1khJ+/oT8gut83uRQFP\n-----END PRIVATE KEY-----", "shared": "521a5b8149a132d155e6b4ed113900506cfc2f76d2a3e14196d69eb85db3c952", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 321, "comment": "special case for AA in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAgq5I3PWbxeRp+aEbGKMtR1OsgYaS364n1nVBGiJys2M=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOCP+kXvvh+WWEx2JUVUrbkXe1jtCWCabOSZ5b0i01xF\n-----END PRIVATE KEY-----", "shared": "e831d6cee95ca1b4c96bb89457562fff36cb4d08b81da89b810b425ecdbafd78", "result": "valid", "flags": []}, {"tcId": 322, "comment": "special case for AA in multiplication by 4", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAszvTrRS2aJb5ccvfJ3hfw6o8+zmtxsKSV9IupN+Mv2M=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGiOG7tRFPNOhTHCeLLZcUugfDKnrqbmJxNb0fxlI4BF\n-----END PRIVATE KEY-----", "shared": "350e3ab9d0dbff78f3f2157428beba189333be274827c10d59673f21c0c48a24", "result": "valid", "flags": []}, {"tcId": 323, "comment": "special case for AA in multiplication by 4", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAGOWN9r++GEsOPHxL8qBR7QVbeTUBwNT8R7yKlcTe7Hw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIA2pOLpPp7YLZnXGlIqrJKJvZkF/kHQHQikmTdqJYRC\n-----END PRIVATE KEY-----", "shared": "ade71d6460287fe808e947560e67a9d6ff2f96eaa1355d2e9fbbe549e883381b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 324, "comment": "special case for DA in multiplication by 4", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAdy4x53bo1PI7evIDevKKN+aPYedAs5BPTsTJAVe+FHg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJAbIPDNp0B2w9S/TgJlPNQG7UgMNVFZ4iykS5hPEHZP\n-----END PRIVATE KEY-----", "shared": "91a9bec28cf18c7094e2d80d2764df59ada0cb1946be422864bd7ad0e533b663", "result": "valid", "flags": []}, {"tcId": 325, "comment": "special case for z_2 in multiplication by 5", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAqNVdXBE36btiZVf51u6o0xIOk2T4vNm2eTQmCxoJGAE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINg+t6/9G8wewLSCPO5c8LFbX1cIWqJwjtQ3opJTKbVQ\n-----END PRIVATE KEY-----", "shared": "6c1b8e240edfa5db2abb3dc12bcf9e8ac9ca10dd3507083746f6f36dc035d755", "result": "valid", "flags": []}, {"tcId": 326, "comment": "special case for z_2 in multiplication by 5", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAM8lL5YsPDmzzY+GxKi6/uTBAcVvpFRjyHfKVPuq1+wE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJie7jF7nCVNwCP5417/AiS8LgvIcZlrlGqWlw51Bqhe\n-----END PRIVATE KEY-----", "shared": "d4c3b3467714f2d105904a84cc7e81d7f291304e908041682d8906a683c12125", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 327, "comment": "special case for z_2 in multiplication by 5", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAohiuliSwfOBReLnQzBtx3uIfJ4UqLOsYYQtAUrJE8A8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILg1VFXTWPLdfFcHssaXPJwnuZ59isFlDHkeX9vL6klX\n-----END PRIVATE KEY-----", "shared": "1ebe6ca711a649ae487b332747e3dc0306340560cab6bc6029e44f6a7e0ee41c", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 328, "comment": "special case for z_2 in multiplication by 5", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA1wZ/rq/T6WbldSX5MLMxfJ6LnJqa6UbnbB5GAqWafjM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIBlVn7wgrFsIIU0h/VIkwErpHYiJOXFnyUN+/glgeha\n-----END PRIVATE KEY-----", "shared": "03e7a777e648bdc612189f3cd42d34e35736d3e52e6edc8ac873a58e244a6073", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 329, "comment": "special case for z_2 in multiplication by 5", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAjfloLL6IAkeKhTE3fnUs3eVHONUo1jm+qer0dwL4vzs=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIAC1FEgTmmH+bF+/k5WHfVPYIO9Z2jvoVkWLXrkJhbpT\n-----END PRIVATE KEY-----", "shared": "308ef99dae1064a444fa90775b5dd5b1952d7224a0e5ae031df432640f416208", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 330, "comment": "special case for z_2 in multiplication by 5", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAfZJwaGiqCVOGONYzwlXzM7naA7x0tJs1lBxXggzT/Uc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOjrn29i+T28MluDOqdjqQ8T8KyywsS4sz3s1HHOcMRf\n-----END PRIVATE KEY-----", "shared": "f33e2e86443a2c68823b72a2b59d6a028e0a8e283cfe29fea4f7aa22bd1afe72", "result": "valid", "flags": []}, {"tcId": 331, "comment": "special case for E in multiplication by 5", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA37H/wXav+E2zAYLSN4+Dco+D3Rsz15hW89pUWc+d+Qc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGihp8zFC6tLAeVeGMvUZK/0MTH7B0HmjVPN6/xU8zBR\n-----END PRIVATE KEY-----", "shared": "7b535fc31c6c2a3803d8bd45410a1781bd90a09205da28c9df120df23a9fa32d", "result": "valid", "flags": []}, {"tcId": 332, "comment": "special case for E in multiplication by 5", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAEugeg4sh6sltwTBDJXEhbXqbSoF/GThyHSJn3RUOvyA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOB1vPwWWkcbL3bDAD+wFyyC9wcTfeL6cILkOoeiVZNc\n-----END PRIVATE KEY-----", "shared": "ca23a781da0911e4115a29a9f56447157c23bee187b0c17369c4f7730d781718", "result": "valid", "flags": []}, {"tcId": 333, "comment": "special case for E in multiplication by 5", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAgypGrsAiQNcW/iLeqUrVZqP6++7czjXIPkHlgHbJl0k=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMDhljTb9kYOFIaTDEboVWs8FtbelZkEYAVJuz4IYDRV\n-----END PRIVATE KEY-----", "shared": "cd0686b32ea4cddb8e13ff20a78d380749a5d4f6a3dc55d72f4813d949a0ea57", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 334, "comment": "special case for E in multiplication by 5", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAjIAzQyvMEtR59n1th2scjonxaiNLmwkzIu/6ne6UVU0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILhMqhisw9s3Il0yyrT2Dm+6SsqxJ34gQl0w+UyrLixV\n-----END PRIVATE KEY-----", "shared": "a950aa57bb2beb9ed5d3228c7ef448dab69552f3d3b1e466accf41bfb6d5b874", "result": "valid", "flags": []}, {"tcId": 335, "comment": "special case for E in multiplication by 5", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAbfeZu6bN9fRqV6sif5P7pJHa0pai/bfkkZIdYQzOj14=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICiWgYzd9XJSGUPp8MXoRfUwt0BCdYig9t4lBL1b9AxT\n-----END PRIVATE KEY-----", "shared": "54f5ae57e676d08c8f8a3cf891e36ddaab751093f92f409060c57e745941700e", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 336, "comment": "special case for AA in multiplication by 5", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEADICQ4c/n92HP3wjZRNSut6UJoHphAWRbmkx8npw9Rgk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKAfDK2YzykFuBLTUwUxuzrImTkavR6vSj6+2WrGEm9Y\n-----END PRIVATE KEY-----", "shared": "2d49b09f81f3f6fab2c67e32f1bcead2ad09ac9e0d642b0873becfb64de2ab23", "result": "valid", "flags": []}, {"tcId": 337, "comment": "special case for AA in multiplication by 5", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEACDUpNsiv2FQ6yV8kvOmgfj4yNXY+pRKlhCmJZ7g8Bwo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBBrNjRMxKWjidgWgTd4aAb/A81KAPhja7fnWNRWFR1Z\n-----END PRIVATE KEY-----", "shared": "a199368e683c3036a48f4c5f32b32a547dd39f3d1007ca0a0bebcad0a8ac6f5c", "result": "valid", "flags": []}, {"tcId": 338, "comment": "special case for AA in multiplication by 5", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAc73u+MwET1rY1qJBJz4ZleAAfcnmV5BG34aqbNl/XSo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIj5oNI1St/Lqy0SoOCbPHcZyUQ4Tt+6on/gcxy5xvxa\n-----END PRIVATE KEY-----", "shared": "5aa750de4207869ec7fddab34c639559b1eb27ef244aaf2a702c84963b6d6e7c", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 339, "comment": "special case for AA in multiplication by 5", "public": "-----<PERSON><PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAf905m270o/XK3mLnQROynCfbFSA/m445jSxvIwBRzSs=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIAgR8uVgogXpbii8MSvK1F/ovv77f22l+qA1MR7tgLJR\n-----END PRIVATE KEY-----", "shared": "a6947ee089ff28ce3644ea4c6eb33dbb20c7974fb8d853f4e146e2466177502d", "result": "valid", "flags": []}, {"tcId": 340, "comment": "special case for DA - CB in multiplication by 5", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA8Bc6lic8ZG+2PROwxoa4njdnb8xxePr0pvRgHzBoFQ0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIECtmEBmppCA+0oxWHjnNglsxXfa5MQsQNiT2MIXO3ha\n-----END PRIVATE KEY-----", "shared": "230b6aa1f24df90a60839179ba5e9de673cff11cab59e8020b20626c22090b0a", "result": "valid", "flags": []}, {"tcId": 341, "comment": "special case for DA - CB in multiplication by 5", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAJVu+cjDNK+6Q0oP0GKR0qzAUbOXoAaD17WDuje8+ZVg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEixDNRWObu/g6Cyjw3TrQt7AMr0jQVTRIBVaoJ4EW1Z\n-----END PRIVATE KEY-----", "shared": "2299e384958bedd2c3d367759155136d1ff76e4434dc1d9e8212cdca52ea8421", "result": "valid", "flags": []}, {"tcId": 342, "comment": "special case for DA - CB in multiplication by 5", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAIazPl7f+4XMAHM/KshY3wXXvUYb/AAJQKz1S+oxR52Y=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOj613lG4N5M9CNnmEkLg4lIuCz7KfjnaGABsR6NlhZX\n-----END PRIVATE KEY-----", "shared": "97fca065acd3b943c654997c0f125767f9abc4b7c9d8b7246942f12be65d9231", "result": "valid", "flags": []}, {"tcId": 343, "comment": "special case for BB in multiplication by 5", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAW0B3foD/bv43i16BlZzNy7TKBLnXftxrMAbeuZkm+iI=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINB7q+2QsnxOrK/chxcDvQNrcgqCtcCU3OtHSe6uuBBS\n-----END PRIVATE KEY-----", "shared": "f482531e523d058d6e3fe3a427fc40dbce6dd6f18defbc097bfd7d0cdd2f710d", "result": "valid", "flags": []}, {"tcId": 344, "comment": "special case for BB in multiplication by 5", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEASNlSopJP8WfwN3B0aexxXacrtl9JqvTc5+xaFwOd20I=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGijBJrvjAabkGz3QyhtOVKoiL8rm5O8h3X7Wt3gbp9T\n-----END PRIVATE KEY-----", "shared": "de88af905d37417d8331105345dabaab9fd2d3cb1ee902911c1c8eae2991d911", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 345, "comment": "special case for BB in multiplication by 5", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEApe8mXMvFxUAh00+CNkpGJAMPW51f9+Y9ejeeUz3l50I=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBjYw9Kk42YYWoXDhpjZN+E7u6/b2rGgqD276Jut9wdW\n-----END PRIVATE KEY-----", "shared": "075d18ccc984761b70752279e7f6a757208f6c11e29480c32b40aba128a4d52b", "result": "valid", "flags": []}, {"tcId": 346, "comment": "special case for x_2 in multiplication by 5", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAkFHlWkBQ703OCwxAgR8WNx6LFpMlQdo38GlAbYSOpCQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBjvzV/jRb5JhTFmlTkdLJUu7hOw4e51hHIfvosZ1Pxf\n-----END PRIVATE KEY-----", "shared": "212dbf9bc89b6873a60dfc8731a10be11ab2dca4b172142e6c9f06614cd72852", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 347, "comment": "special case for x_2 in multiplication by 5", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAQZrbix8vh94Bawx40QKaIQSS64yt0WSxLNZbHVe/NjQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICjsfGk+IixyrAgV8f02ZhNX4Kjae8mW2u7q/NIcATRR\n-----END PRIVATE KEY-----", "shared": "379f9221abebf3582681a0e857f3da578a1b0121982b96f14b94de5dc8b24528", "result": "valid", "flags": []}, {"tcId": 348, "comment": "special case for x_2 in multiplication by 5", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAE+ANrjsczJfM1kkIjEp/MsqZdiFNZFZnvQggObvZq3o=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHizXnrlSTCLZBS7YQGWwE8q951CZshuipzgwCu9uI1Z\n-----END PRIVATE KEY-----", "shared": "cff2596b7afe36f4cab9c70133d7aa0f9914f9abc6c3b9895472e2a5894a8037", "result": "valid", "flags": []}, {"tcId": 349, "comment": "special case for C in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEARBxIekjwpJidkxzXemFCoKE9GqutgmI7qNlLXDdPTwg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPDenF+Kk3LzDEHKR6VXQ85pfUbjLnqa4m0yUD/VIidn\n-----END PRIVATE KEY-----", "shared": "d47c46b4329bedcbc1986b3c6d2aa9bcd027d6b68925175d35bbb536b3440801", "result": "valid", "flags": []}, {"tcId": 350, "comment": "special case for C in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEADmfuXGtlqoAiWYELJgX416zPm0m/FMtKU2ko6IMXKRU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGhr5aErMQQg+b+yCTgf1FmlzNVcdSuIM36+ieGSGudl\n-----END PRIVATE KEY-----", "shared": "1d730158da880533dbf1e6c64a8e99f9169611660969b0a84fb42dd8dc2efa3d", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 351, "comment": "special case for C in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA3J1+8ctJwZHiWGY6lOcxucBmwRoX2LX96hmH9dmgBWg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKDAM3xb7FyiTeovHXAUmK4rrYe4JprCO+ETkp/k6xlj\n-----END PRIVATE KEY-----", "shared": "07732529a628badeb8d74946775ba457c700bf8390f46bc523fb64e471c86a7e", "result": "valid", "flags": []}, {"tcId": 352, "comment": "special case for C in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAVWs+580NN5eQVuzB9WpWd6STW+bknOKOOU+L+3PRO2o=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILiCTPzlVQteF7EvdOKEWcqzTrSYlcw2v2RaDPAOPS1n\n-----END PRIVATE KEY-----", "shared": "9e3aae35fa1cc80a359878e212180294ff6608dcb4929e91901abbf976f39c16", "result": "valid", "flags": []}, {"tcId": 353, "comment": "special case for C in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAEhG+WAlgW1T1cn0jPHg6KhmaPbJO1Emde0jHYD5K03E=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOAtunM1r4+5Fo3i/NMQwuLfSj4lJj4Kua2oe/uCWKZr\n-----END PRIVATE KEY-----", "shared": "880f6dc73220307a597670f3282fc366aa66f04a0a9ca30d895fdde337afe825", "result": "valid", "flags": []}, {"tcId": 354, "comment": "special case for CB in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAUF54UeI1LjEcqVNqH+bA2V1kgZc3TOCOS4oPvd9ikQs=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDDOcfhWzrh0/lgAOcpn6Jbm0IIHpzzVXbcFkSfBNCtn\n-----END PRIVATE KEY-----", "shared": "ea62b0eda2d7b249a42417675a2b82b1e6c0d69a4e7cef336448844d2f432251", "result": "valid", "flags": []}, {"tcId": 355, "comment": "special case for CB in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA3fTpBQPdgmEMOgNLklqIC3Lb3jDGJgCSArNYxusA9Bg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOiB9G1BQepppnFkm5O2PpfcZ8ElIdRFhi8IeyYm+itv\n-----END PRIVATE KEY-----", "shared": "302c4f83b5c5bf30c1e3afd9f643f65bfe56ca1628ee042b1ab7393bafe36c06", "result": "valid", "flags": []}, {"tcId": 356, "comment": "special case for CB in multiplication by 6", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEADpxEMZme8c4XfpANN+xq5mXjh+LU+ifLqOe668ZcZSA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOh5dSaDzXOoNCUcZXSRNeBuuQZNOuNQldiM3hSgK6Nm\n-----END PRIVATE KEY-----", "shared": "8ff2ac65c85ee2fe9452fce460f8c87f9570d769cadddc87fe93ef8b7657c726", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 357, "comment": "special case for CB in multiplication by 6", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAV2HWwIYkEE1BF/8Xx16SEaWRycqa7Mo6Zlp+2EQZUiU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICBXarRW2ibBjaX78G7E0WVk4RG/riqSufbhknwVdwpi\n-----END PRIVATE KEY-----", "shared": "97c91a23c3e4f3ff727d188a352b67ad490b62381566fb3e111cb67aa9e3435c", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 358, "comment": "special case for CB in multiplication by 6", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA6S1Fs+xWUxJmMDxRE8RjEMQWUAAQZbTYewKzgvyCZi4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKhGdBi5JMLAA8VuFhCjVGk1Y2DCnVKqVXorsw+4qaRk\n-----END PRIVATE KEY-----", "shared": "24346bb133dd9ae3ff02d2f50510b3a92d9030834d60e5af08b0eebbf1d4dd6f", "result": "valid", "flags": []}, {"tcId": 359, "comment": "special case for CB in multiplication by 6", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA84tjRZ0F5CKtAkwtzqUCmgp6a2xMHSCTzlVqqzMeJUA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPD14WKSPXwpk4i+14EZlBet4JdHVRUWLZWQl2oZb7Fv\n-----END PRIVATE KEY-----", "shared": "b3453c9c82a2d1d956156de2399cb70dd4e1ec53aea967e035753c1cdae13c39", "result": "valid", "flags": []}, {"tcId": 360, "comment": "special case for CB in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAp97Q7qRaQAuPVjcVTUKXSqmMkpYjFNgi74iwE4Op2k0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCPz3h/54lkSgm8q5WPBzeqganinVBfUQNceON0ueRr\n-----END PRIVATE KEY-----", "shared": "ebeb0c7b7a4165cd02a278f3a222c236eed83266b806d13494c1c3f98a2f3425", "result": "valid", "flags": []}, {"tcId": 361, "comment": "special case for CB in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAew7LTHLuFHeJ10gTztPr5A9Fw9pSbtEnKVLkU+Q7eW0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFijOW0pHrI1cbUtmKMVSeUU5QHo0JWK2fJf5adsUD5p\n-----END PRIVATE KEY-----", "shared": "9213a53f22ff0cb5eca87b27b193c773bfdf4c01a193a11f37c157474e15cb07", "result": "valid", "flags": []}, {"tcId": 362, "comment": "special case for x_2 in multiplication by 6", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAokRBPdw6IF0DjWQmaDPuoe+6UbpiycbNzb6UO+UrsAw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINgFpwFHVd1lb5jSszHy0tSRJyXvPQN1Lyb3TcGtYWZq\n-----END PRIVATE KEY-----", "shared": "66484a4120e0eb0c7e0505e1d2c5d15de9b52b72e094c9bac88634200c557267", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 363, "comment": "special case for x_2 in multiplication by 6", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA7DyLDBCx+mXbvRfPG6X4Y4EoR2VwmwfF8EKOPVvNOSA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEDLH+BrCPBo9wgLoHxpXtqRor6+rdTblcl918ka8lZt\n-----END PRIVATE KEY-----", "shared": "384f2221618e71d456b1551651efdb708a161d7f89f5604b27eb872d4aa93276", "result": "valid", "flags": []}, {"tcId": 364, "comment": "special case for x_2 in multiplication by 6", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAYzDT4oqLYSas4WWp38zG5L1A28l2jPsWMwy38n+QYjA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIAhRkxkydbTwMhS9pctEZabBMngZlYvp/DV+g2Y661i\n-----END PRIVATE KEY-----", "shared": "8daf5f4b84730144ea8a53ce39cc907e39a89ed09f0202e7be0d3bda38da663b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 365, "comment": "special case for x_2 in multiplication by 6", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAhniqKcvAbniyGNIqPmbDjsDaj9sPJXDFhcYlF8lwTzc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHB6LXELMvVcbro0iYAgovuYHWGx6CL8qExH2TIeJ5Jo\n-----END PRIVATE KEY-----", "shared": "da8b7eba6f72c3f3ef33d8982093492e06be39bb0db29c465d95a8e52ef64341", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 366, "comment": "special case for x_2 in multiplication by 6", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAMDKJwrEHnqWUEvrM/rqMET0imbnc/t6rxCaXsIKcRlg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICBKQ96nnXeVd1gbjCpRvmbh7/zpZCW3QiucplvfGkhn\n-----END PRIVATE KEY-----", "shared": "0419a71a08d3fdd574cbc932e8f1605933ddcdd9774f5614269b7ed850c8650e", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 367, "comment": "special case for x_2 in multiplication by 6", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAPm4W4C1E69lGgIMuBlrt3Lt0r2T7t8bYNn52Bb4T/1s=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFjkdBc10liTIhUZR6HOL1gpkIYmiGlByxYx0lqKaEFp\n-----END PRIVATE KEY-----", "shared": "9f2fcd0c756288c1716ecd1f2a74864b93a7717bfaf5248858dcb6fdbea12864", "result": "valid", "flags": []}, {"tcId": 368, "comment": "special case for x_2 in multiplication by 6", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAp8FxakHtI6iHBDhxT/l0X7Dkb3pbrrN8mi2D/kd9FGw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINCvNCjqUgX2v41PG05JA8128EI2ocCz7P3K8oshNI5j\n-----END PRIVATE KEY-----", "shared": "261ab6267c35a9755359e957473870522b7f923fe839f2b155408649cc5e8004", "result": "valid", "flags": []}, {"tcId": 369, "comment": "special case for DA - CB in multiplication by 6", "public": "-----<PERSON><PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA2tmBVSxXVBxX7zle13DOXtxI+AFUYbK6eqgx7Fk86xU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMDql+RC5dwcgUK/q3CJ7Lm7nFrjcvmQfCgl5nje+uVn\n-----END PRIVATE KEY-----", "shared": "9093bfa3ed3491d0891f02ae466e5e13c980df229db7404c5b9d34e4ed21c653", "result": "valid", "flags": []}, {"tcId": 370, "comment": "special case for DA - CB in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAxYjf5ucz2QWBy+ESB5dJ2Oswq4YxE07Cmr+5izLnZSI=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILAzPwmsHqrNPNYX64gy6d5Ii0WLc1y0tTRfUXEwwl1r\n-----END PRIVATE KEY-----", "shared": "6e88bb6bf75596bbe5f1fbe91e365a527a156f4f1b57c13ac1e3e6db93191239", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 371, "comment": "special case for DA - CB in multiplication by 6", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEABnARakNejZt6Ev/EMi/WsUnQsdx5m1wJV9nW5CVG6CQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBBxkJncY7zCgu9SWEXBCIl6yfrpWQtZPg1QXRzxZ8Bh\n-----END PRIVATE KEY-----", "shared": "e6de74d2c5cea54094d7a70af03c768afe05d52a038bb72d56dcacf0ba502d74", "result": "valid", "flags": []}, {"tcId": 372, "comment": "special case for DA - CB in multiplication by 6", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAiyAN0ibFwPfhFuU4i6FiQ4yvHd307cO2uoOMIbWSlzc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBDiDk/aVwhMqQ961XKniqjmV1xlnNAfMMQ8WAQMIOhg\n-----END PRIVATE KEY-----", "shared": "78c9c3aff9416a538ce3ea8fa553244528d1fbecbcf91695a33ca464ef76b85a", "result": "valid", "flags": []}, {"tcId": 373, "comment": "special case for DA - CB in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAQZoHaxefeXIAluqrrwNHfo+J1h+IXI1/WPbqpPp3318=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKgxLfRzrf7HFx4WNfW61E8HU6iKazF07FrnYnA64l5g\n-----END PRIVATE KEY-----", "shared": "c1a96ccba08bdd82d0fc12e8cde4cc1f25cfd5276dce7f18e407ed0e4a898466", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 374, "comment": "special case for DA + CB in multiplication by 6", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAqjTXcums5DxNkvT4VZarnM2MNsT0y93IGa/iozy4shY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBCWl/QAIQ+akt6AqL7SZAlxmbwkDiJ2e1TYuyIFC3ph\n-----END PRIVATE KEY-----", "shared": "2533b845bb83e3d48cffa8dbd1edd5d601778662d5da03759152a5e0a84b357d", "result": "valid", "flags": []}, {"tcId": 375, "comment": "special case for DA + CB in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAHwbP5GTMwOJ6XsX57dm8e8girS/1BoylyWPSDt0aLSI=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINA2MIpTwRvrywLoNoitdP7EP4Ri702AYnJnZjfZmzdl\n-----END PRIVATE KEY-----", "shared": "eb40a3974b1b0310b1597d1f1f4101c08dca727455a9d8224cd061a7aa3cb628", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 376, "comment": "special case for DA + CB in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAnUsu14FxMq9YMOiZYn6pfcOb03cugvLQV2mpGCc9wC4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHhuWl/zdAXHadDTeIw8GwWmKoRCw4VXDkQ4vF8uqs1n\n-----END PRIVATE KEY-----", "shared": "9509757e289553cfa2cc71313473c3ff1eebce484ee237eae554fda3d3d22f0e", "result": "valid", "flags": []}, {"tcId": 377, "comment": "special case for DA + CB in multiplication by 6", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEATgVrMXox3Zb47BS0hHSvWH0ZXvzCpw8B8FLviC17OkU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMAfZssJQonXKEId1GxvlxhBLhxUba1w5YaFG+TaWL9n\n-----END PRIVATE KEY-----", "shared": "bad9f7b27dac64b0fc980a41f1cefa50c5ca40c714296c0c4042095c2db60e11", "result": "valid", "flags": []}, {"tcId": 378, "comment": "special case for DA + CB in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAcsYFNenEI/MC1qEHltlU13gDLNTb1Ayg81niBNZ7b0w=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDh32c4lzt7etXJgTy0SPfaFaQwm4YH3d+0zMCuCCClm\n-----END PRIVATE KEY-----", "shared": "51c359768ab0219003af193e2bdb8e5cc9f8e176b8db49e597afca3e7125e370", "result": "valid", "flags": []}, {"tcId": 379, "comment": "special case for DA + CB in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAWFY1jtQgBHzQhPF65pa615pNJsbVu3m/uCu8YzJELVE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFC4RhjQc8Rhj5qmmjuFGNp227IScoYhT7Q6K0RQO5lp\n-----END PRIVATE KEY-----", "shared": "fa9fb0df4cfbacd0fbf3262d3a1bf8d7aacb45f73bf94671775e509c8043df7d", "result": "valid", "flags": []}, {"tcId": 380, "comment": "special case for DA + CB in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAwx43sEMyq8qDFfMXFxVmrvOBEfYi2L/6KcI8AVHNrW4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBCaz6Y44RL2u+wh41KnTo/Jt//l2dwoY07rUW5Zgwpj\n-----END PRIVATE KEY-----", "shared": "91ac72b0ed8d7fc4c8846b8a2530d9fb8f0532064880c00dab100c977697db28", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 381, "comment": "special case for z_2 in multiplication by 6", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAt3XgFrMql/SZcRIZBnY/OgtBaJCSuVg7ZxDPfe4Dphw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGhcB4SqbRlMG4Wb2kTE4nzR3980d25JjdA9Cfh65opl\n-----END PRIVATE KEY-----", "shared": "11393bb548813e04fb54133edbe0626458e80981885e1fe5f3377e8ebe9afa52", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 382, "comment": "special case for z_2 in multiplication by 6", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA+L0OfPbsYYbyBasDq3LI9rPN6PatmxZpFqBNQ9HW1UY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBjpoFogQ2zw28PVuS2sjZluYuoR+7NEXykZX8dai+tp\n-----END PRIVATE KEY-----", "shared": "0a83a224fbfcbc5d0f07f6dd8ebb2e9bbee8134f0fab268002ce837f5495d833", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 383, "comment": "special case for z_2 in multiplication by 6", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAjf7kitizZ0iOpNr89whuMFNWqAkB+HxyAUml9SIzdFM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIADgmesjEl2rXsNaQZ1FXQuowB2hYPk1Tp+yHmpV1Vxk\n-----END PRIVATE KEY-----", "shared": "45dc39831f3471d7466bbe29c8142b1a6d6b00c47fea021be2ffc452d9046806", "result": "valid", "flags": []}, {"tcId": 384, "comment": "special case for z_2 in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAj2i/xX15LDIuuyf0SjfByT5+sVxdX87f/B3oUEh7M3I=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILDKJR4NuucySmygwsjWqIjt0S0UR9QApHvLoAS2SHFu\n-----END PRIVATE KEY-----", "shared": "a29005c6b9dbf1707dc2adce4506b55831e8675b7d2d54b0c1037741e3bc611b", "result": "valid", "flags": []}, {"tcId": 385, "comment": "special case for D in multiplication by 6", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA/w8VreqzNK/to5Fnhd3TjSUtzph2wjV7ZDtdwsBqOx0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKi2S47Tl3c7gpBCXKXC98PlD6x6R4G9SlTBM3gcmhNg\n-----END PRIVATE KEY-----", "shared": "9f04e42c1b2f311d87e1470a4708bba25ac6ffd3f7b486f9b6b502ecbb2c004e", "result": "valid", "flags": []}, {"tcId": 386, "comment": "special case for D in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAEHb9yCfyVQ7pX/mhXQRK7frGW16bqAn2JDjM6lRjeik=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINDNDbUf8jKvoJGdMQb8s6iuWB7xLQnId6pvMe907tBo\n-----END PRIVATE KEY-----", "shared": "688000bd60af375b4eeac4a7d0e0782c0e6188eabdc608b732f49b4d6ccab44f", "result": "valid", "flags": []}, {"tcId": 387, "comment": "special case for D in multiplication by 6", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA7RyCCCt0zCquvz3HcroJVXwPwUE5qIFPxfk3C7jpiFg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICBKO1ZShU/0jiXNOFyr5jYPZM5E/qViHbH6L24hnzBj\n-----END PRIVATE KEY-----", "shared": "e0a82f313046024b3cea93b98e2f8ecf228cbfab8ae10b10292c32feccff1603", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 388, "comment": "special case for D in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAEuFYmjQJSvXxIcm9PBEZ8rHwUmTFc/Znp0hoPFYzpH4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIgQmx0Oe6zkTUGhXVvLzTaWjFuLR8Cixga1fEpozF9m\n-----END PRIVATE KEY-----", "shared": "1fcc50333eb90706935f25b02f437bfd22b6b16cc375afff8a1aa7432fb86251", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 389, "comment": "special case for DA in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAFR9UqKiZcRdXs7EY/FUBd51iHSUievU9CvALdYO6iCQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFCC5JfEKXnNv90bOwZTz+pvLOt9B2OevzVBhmu2Dtti\n-----END PRIVATE KEY-----", "shared": "fac30a74f4ca99f6cf233065e9acd826690cab364bf69320b58095783ed76e11", "result": "valid", "flags": []}, {"tcId": 390, "comment": "special case for DA in multiplication by 6", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAqBnGZ+1Ga9mmnqCzhkLujlP0ClA3ewUetZAULdJ+NDE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPhajbRPnlaxFyn1FoKpdp/FBPk1l8vjlERhayJFMhBu\n-----END PRIVATE KEY-----", "shared": "17f6543c4727e7f129ee82477655577635c125a20c3dc8ba206ca3cc4854ca6c", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 391, "comment": "special case for DA in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAQLBT0FZmiYKh9VC+leFjSOMDlF9To6xkSRqaVtQJW3E=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFBaB2ZB+sOY/H2MYpk39C21WdteEgUq02bUbXsg6Vdp\n-----END PRIVATE KEY-----", "shared": "889a8d611e0a7da71475e7c93a2d7f6f7228c787a00ee5cf55474adc376ff762", "result": "valid", "flags": []}, {"tcId": 392, "comment": "special case for DA in multiplication by 6", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA590FSadlu+80vi6NoYobwbmJqLBhTTWOvzjBKpymQHk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOjbK/GvW4kHQgeJxW5xQUcGrvDZ9v+u0MJJw7erFL9l\n-----END PRIVATE KEY-----", "shared": "37232fb397af27f5fb5ca493284ff1c5d25786b0d716c73b33aca8d42265f318", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 393, "comment": "special case for z_2 in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAHuG5p0YErDHD24MoAXDjgRUE/MeMdia1ssB6mdgNqgo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMAGqxdicgiCAX0Qa5pGdf3UcAVlcVXJDKYdTL98xPlz\n-----END PRIVATE KEY-----", "shared": "a1b30418436ba1908804ffcce1be2cdcf50c61a8e3938d95c790abdb786b8022", "result": "valid", "flags": []}, {"tcId": 394, "comment": "special case for z_2 in multiplication by 7", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA8ibC1r14Me2htR7lrsKUQ6UH7596BOI0DzSdvxSTOEQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINBxgH1geVPaQy2FdNXz9CBnba/bxqKFo24dc3Yk13x1\n-----END PRIVATE KEY-----", "shared": "a5976fda89954a81e442107f9e416a2b4b481bbd4654ebc0c7b57a78b45b4979", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 395, "comment": "special case for z_2 in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAxRlzEt46ej7hGymHO64/yMhRCcZnhIBPiUNdshD8wks=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDBLUm9v6ZRzGYDAl1UpvKTQYQF/vsVvYHDUJnjT4RF3\n-----END PRIVATE KEY-----", "shared": "55b5b5eb38b127617ffe00056d84d35a5071d18783e3a82b5f4e131b1538b150", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 396, "comment": "special case for z_2 in multiplication by 7", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAWQ7QuHkxnDihmWKl0hb/K/rzNVVRiHeWnCDAVMvkPlY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJgt3ywDV4k3m4pYkX1cPGwGG1A7GaACjgGJTC6zcdB5\n-----END PRIVATE KEY-----", "shared": "0080e5b9985a960a832133812a7ab9951c6b2c75894deb3e35509190a6bdf457", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 397, "comment": "special case for z_2 in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAfF8BQ6ZoL2DMrRbyEVDHu1vG+AclTQizU/yWzge8628=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHjMPsBofj5T2c7Fa3nRG/BJ0XPxJ/W0D64SKm0AFs12\n-----END PRIVATE KEY-----", "shared": "5241222226638c4bbbc98792cdbd74882ca2e08aa2edf313070425031009e925", "result": "valid", "flags": []}, {"tcId": 398, "comment": "special case for BB in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAAQhQoJdNPonAKdJStG9zlUgpTA+aIxg4Y/lFW5VZwhE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMhvx2ZQzztYg3qg8GM1YEFSQcbE+PKTugIit9ajh1dz\n-----END PRIVATE KEY-----", "shared": "63788190b10d7451f5fc2b82c421151db4f3e22782e392da6d8d3aba2c344306", "result": "valid", "flags": []}, {"tcId": 399, "comment": "special case for BB in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEArR3YLCPWoNX+DypFYdHBZzOj4eavptkC3Qd9xDqWFig=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIiNUcCiIwNp5bZagUsyE93i5i8uuV0JcUhrcz5PkMF0\n-----END PRIVATE KEY-----", "shared": "e4b40974a166ac49ed831715c071c751752744b891465e6c45001855aacdc362", "result": "valid", "flags": []}, {"tcId": 400, "comment": "special case for BB in multiplication by 7", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA0MDWOTxB9Nfg1ehQt3FvQB7aHgKKTtSgW+qL+BrP2TA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGi+1CXVNDFVhNgPedpuq5t+YDa1H+YuGtkz4mZkC0Zz\n-----END PRIVATE KEY-----", "shared": "514a4cd0676f1c3101c8c45c17ad416bd33e20a405544fc1a60449abb22fa104", "result": "valid", "flags": []}, {"tcId": 401, "comment": "special case for E in multiplication by 7", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAD0YBANiKHTFt/wLRsi/7LkLZnQuSR0/D7H1iVn0M8RI=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJj/KFbvRLT6FNhngup5OCi99vHvm2acrBquM4p7tpN2\n-----END PRIVATE KEY-----", "shared": "ed83e810ce5ff0868f8589623bb13478dec1c22326c92765ae5e48c84bbabb24", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 402, "comment": "special case for E in multiplication by 7", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAE3VqQR/zrgw5Ii3eCBDwjEMkYxYtge8GEHEkmkhDnhU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILDNv92YvZiNfGpTBFXFHFfdM/0seu45YZcb06MTiPxx\n-----END PRIVATE KEY-----", "shared": "ff94862117d3c6edc9dd5f4852fa8a589452b924ca8a75cb23b3d68dfed88c4b", "result": "valid", "flags": []}, {"tcId": 403, "comment": "special case for E in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAj8H66WmmGFQE2yJ0nvbSJd6GdzpNG/OFfrj7vYKaG0c=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOBndkTtSTXwHgUumWcwLQ+3j/IruS+64GBfPuVOL2h4\n-----END PRIVATE KEY-----", "shared": "1c94868bc8acb3137498209b2812feb53501389f5aa37fecbfd5cb54e1358e0e", "result": "valid", "flags": []}, {"tcId": 404, "comment": "special case for E in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAe6sIkey55yoVdx8KT/+QVHAkIGM5w0Cxov21O8+4a1k=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIh7YVU4Q8qZrRypIlOm/ggrgklHUlE/1T/2Uw9UxAVy\n-----END PRIVATE KEY-----", "shared": "adbf3b439b16dbc653578f53374ed3a86f9c0bf1f736573349773bc3b8d60734", "result": "valid", "flags": []}, {"tcId": 405, "comment": "special case for AA in multiplication by 7", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAEC6V6tynw8KOXVIzbIV7rZnqJG8pmwYzT0ASdvScqBQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIABhXkaXAU/BJITvU6FEAgZBCo33jKoL//ghYduD/qV0\n-----END PRIVATE KEY-----", "shared": "3952efb93573ae9ce2162d10e4b8c46435859f3f2778db89f72bc579e695cb51", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 406, "comment": "special case for AA in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEANUjBa/Ma/c1EWtm+8OYNe9YZWqWRyoyCgTzX1EYiZyA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFgXURNVD6rVZFj7N1pss/Bd8vb/PE7gnUprpkPgItF6\n-----END PRIVATE KEY-----", "shared": "96128f929fc03c1269d429f609a1a8acac7a758e3446a125ecf4a359a0e37b73", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 407, "comment": "special case for AA in multiplication by 7", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAunTnZtRIVeyTvUQapBBYpMStK+Y8Y5o/moe95R7quiA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIACXOOHm7++eLK2LQW/pCgmOtcsBmfLfUhgWbHsYHqB5\n-----END PRIVATE KEY-----", "shared": "fec3e94cb5f316625b090c2c820828ce0f3ee431e8d6e12abccc7ef2bd0be81a", "result": "valid", "flags": []}, {"tcId": 408, "comment": "special case for AA in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAmlodN+UBDDVqqAr7NHw9YTVC3foL56u46M3NZnRBFEk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMggGRWb55J0ejnziOpIqMVoWU4zgyc+URAHIbN26Lpz\n-----END PRIVATE KEY-----", "shared": "96903bac9dc60b6178d734890c25db4bed9ea4dbcf6fcbcdc90e6f5694c8b21c", "result": "valid", "flags": []}, {"tcId": 409, "comment": "special case for AA in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAYwhH4oJ0265UkSEDA8haNZB07nQpV7D8PJ/1XZ4BmlA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBCsn4ODJi7ygPqsHk2hWn3k8st0rzO1Dg2C3Lhdi8tw\n-----END PRIVATE KEY-----", "shared": "50050d0ab1ddd2dd90c460ab8f09e1f80e37cae57d4231adae10c10a4a2b003e", "result": "valid", "flags": []}, {"tcId": 410, "comment": "special case for AA in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAEXSbAKRQZ68sfn1Q+NF41an+248babI5djiFvGEbE2w=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILhMCYOC9uN9UQzDPmLdxmTgLIu27Z7Q5fp4zAmaJv5z\n-----END PRIVATE KEY-----", "shared": "9170c4c628d5fcfd0ec719cf6e1796dab0a69e46d6379fffa247d444a0056041", "result": "valid", "flags": []}, {"tcId": 411, "comment": "special case for AA in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA3xAh2PlZUK/ed8hrpe4vWHbvd4N2p/3H77jf8OSDbns=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHjN6JMKHYGu9mAfcUCXKIVJh1eLD4NJWIwErb4sH250\n-----END PRIVATE KEY-----", "shared": "d7d2a82953f680cee0c81c4d00fe628ac530ce682eb7fb3b0af24f804a58ef5c", "result": "valid", "flags": []}, {"tcId": 412, "comment": "special case for x_2 in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAJ0O6QI1faMZTJKSFCGoAS2u/eEzJ6LGn2+uMS5QUsBg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILD+ewa5lQYAs6fOHXuyodmEGUzJ1siWRQTDZN1ch1t0\n-----END PRIVATE KEY-----", "shared": "a6b97da989dccf730f122d455152328051c8ed9abc1815c19eec6501d6cfc77c", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 413, "comment": "special case for x_2 in multiplication by 7", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAzCdaLN2RJeUvIM4qutQfkgr6WmQ/t/J270Fvdh1onx4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPDJw5hIVNW9WZ04GXOKAj63lek1htwOXimxyHDGEtF4\n-----END PRIVATE KEY-----", "shared": "b210e368729501d9f9b6ebefbebae38f195f91eaf2a5a3a49288bb615ff2216c", "result": "valid", "flags": []}, {"tcId": 414, "comment": "special case for x_2 in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEASSlUMQHueuI5BZzRNMNdQA5Q0IIUQTUdD6bD1U77NC4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJBsLxK+iXAtsm+n7pBc42Ul0t7k6WqHnKB9oJemqlB1\n-----END PRIVATE KEY-----", "shared": "b9e3796c58701ded4237c52994501cee14e18f2fb02b781a8400923484bd4a6c", "result": "valid", "flags": []}, {"tcId": 415, "comment": "special case for x_2 in multiplication by 7", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAEyTgNoWXsxgVVbtbLMe367pGkxrqu28Fq6vUJA8PuTM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPAmAx6jc+HRbm5+A1e8lrwJP0trt2pzjLtU/mz9LqJx\n-----END PRIVATE KEY-----", "shared": "6dcdf8e86903b0caded124d8a7da18e623430ca869aaf267d31029d93de99e66", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 416, "comment": "special case for x_2 in multiplication by 7", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAx/OEIpfWlBysY9bxva6gcJQ3yC28kWH8G65sedZo60Q=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHA/SshmfXf5U2BFz3SPGNQjReOcyrEMGN3g9RcNMH9z\n-----END PRIVATE KEY-----", "shared": "385ddbf2505ebf537bf5e976b61a4b69d190ae965b7e4a81ae4e1c16b7148748", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 417, "comment": "special case for x_2 in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAHkZguoZfuAha/UaSiF10I3+jvKWvS4S6PeQA8WpaxFw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMipauTncnGgaA3ST8sJ+cXT7oMWU27sfMInZZflD+N/\n-----END PRIVATE KEY-----", "shared": "0fbaea73f9518795e026c1fc1079c3738aeb9ee9c8dc9761d65bbf8f94e30154", "result": "valid", "flags": []}, {"tcId": 418, "comment": "special case for x_2 in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAJIi7b623nUZYX/AcFgxbQXJ5nZK9Fo7c62XO3txJJ2I=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINDd6O2jjDeDRChkwMtGoOmDLc94TCEmiiG+0srOh81w\n-----END PRIVATE KEY-----", "shared": "510c64151e5d0737fc324bd15fb5d3966908751cd1a06954b556196655ee5540", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 419, "comment": "special case for x_2 in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAoMEIeBGvFJEXG8UWkbjKhHFq82xLqnZOxTYoDMGYPW0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMCc1H4c5TYE8U5OE0JsjwiWL1VrzYH411N1sVB8b9p4\n-----END PRIVATE KEY-----", "shared": "23ef825e1c8e6e64428001a7463e32a9701c81cf78203e6ae753740c91570e6b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 420, "comment": "special case for x_2 in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAzFyXk0YH2LmBvOHWojK7OuzDAB9piuGuhJOPvyhhB3s=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOCaX3TzGPAjA4V6oCCNdpE9niQKgFSdEgExGLrWIFl/\n-----END PRIVATE KEY-----", "shared": "0e55a7ec1a2ddbea1ac5981200812232f7f4c3a60ee3c9ab09f2163bd13da329", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 421, "comment": "special case for DA - CB in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAI43n/Mij8ZTDVUwyjvsSFdBkCsZ0thqY75NOwATP1zs=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHBs7l+bNXwDsvGRMpT25PDKWhkKh9MCaDJ9DLa91bx5\n-----END PRIVATE KEY-----", "shared": "0681036a0d27583ba6f2be7630613171a33fb8a6c8991c53b379999f0f15923b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 422, "comment": "special case for DA - CB in multiplication by 7", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEArJ/YCkXaEJ+iMpOQ5alRz8AwZde7SnhVgmzLIsO/6z0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEDjAMsf8mBXT4Wz8EqsR4RkqG5iA7PUZWQY9DBRV4d7\n-----END PRIVATE KEY-----", "shared": "67b88774f19bd1081d6f23656a135803e34ae1cdcae10818124a78569c299f42", "result": "valid", "flags": []}, {"tcId": 423, "comment": "special case for DA - CB in multiplication by 7", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEApFqx3C+ixQcY+0mF2XkUAejS00/+PNk8/7TocMzl6FU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIgveLRVi3+qg1kEySNeMvMA/Ite8KcYQGpchSDKVNBx\n-----END PRIVATE KEY-----", "shared": "a512e864bd898a5ba6551adcebd836c6a78e7871728e1b8ee528d483af276104", "result": "valid", "flags": []}, {"tcId": 424, "comment": "special case for DA - CB in multiplication by 7", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAF2HT1Quka0RmVapqjZuLdapbskp5UyCNW2n8w48Y7Ho=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINhkm3NVkKF9D8TDePv0wvfWYAVpsuhMvg/3vNusC19x\n-----END PRIVATE KEY-----", "shared": "518b778cf5e976c60235abcf6211a18bad2a8e693ab261074c7fab43dbb5da27", "result": "valid", "flags": []}, {"tcId": 425, "comment": "special case for D in multiplication by 8", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA3JmtADFGPkU3wB4WYplm0bliwLTkhy8GfKPCbMyVcAE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKjt7Fmua6I4E+xU1m3xUuBiZ2K5fUsMIODdilaV2G5H\n-----END PRIVATE KEY-----", "shared": "6cfa935f24b031ff261a7cd3526660fd6b396c5c30e299575f6a322281191e03", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 426, "comment": "special case for D in multiplication by 8", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAsydQ/YDS18Ysa445ZwZUuupXGaPgcumVB/1byyOJgmQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBCYcj/+Vn6m3MjQTswB76/uoK7kThxzO+ix5dl8i4BB\n-----END PRIVATE KEY-----", "shared": "c623e2d2083f18110a525f2b66d89ed82d313b6a2dd082f6b7a6e733134f5a06", "result": "valid", "flags": []}, {"tcId": 427, "comment": "special case for D in multiplication by 8", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA57MgV3ezdfGxUVpQoWpgZ5U/8iHhK09BbXT7KMHIWGU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKDyDfmLSSGKyDLyb6jCGKDWhy63rqB8HUPJ/2mbRltH\n-----END PRIVATE KEY-----", "shared": "388ea421650a8d837bad8904018195e99ef494c2d170b93ee721a67d2c108729", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 428, "comment": "special case for DA + CB in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAIcwzjXhp5YYzScxznIppRs/Hl8uC+/YtzSFUhEsQYAM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDBHOnepg3T2fVvUPfIxzhQpFq6g0nHnIzP6R9xEGgJH\n-----END PRIVATE KEY-----", "shared": "b9e5728b37435b1d339988f93267d59f3bd1c517851c5a258e74cb64aea73d2d", "result": "valid", "flags": []}, {"tcId": 429, "comment": "special case for DA + CB in multiplication by 8", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAw0IXwCBy1+K8oEVFJQMHgM+2AhXXyoLb7I9KWQNMX0M=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINhle+OjD8hfsvOmjpKs4bMbJudua9tnJ66lB8t8ENxF\n-----END PRIVATE KEY-----", "shared": "20b67b205e22ce87fd44a8e8fd10a6d8890b9270b60e1c6a68b4aa78e6e37961", "result": "valid", "flags": []}, {"tcId": 430, "comment": "special case for DA + CB in multiplication by 8", "public": "-----<PERSON><PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAiruM/WDG+KTYTQdQ07QKT4RrMO3yBS/vffhBQs0Nnkc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIgvVXiuShPY9a9HO93hcJvy4FnfgJ7gW1BfNN6FfDRH\n-----END PRIVATE KEY-----", "shared": "5faba645fc21f9421ebd35c69bdb1d85b46f95e3746ff7f4886bc280a9ab2522", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 431, "comment": "special case for DA + CB in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAn9e0mgjyBmiNcttzffjlF6p7dk9d58miscP8uqmF9kw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJgpTbfL9JWL+z7SHV1ckeE8yNwns8cWyG9xZ6SBn4dB\n-----END PRIVATE KEY-----", "shared": "9cb8a0f4ad86a27b96ca61242eab198db2767d3862dd323e41368fcdcc5fab68", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 432, "comment": "special case for DA + CB in multiplication by 8", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAxP76x6zUSOj9TWrE9d0bwh8sZ9Y4REBgkY+zRKp351c=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHibxAR62BubZlbu8pi3Zuh2Oi+Opk43SmA9wf3y7uFG\n-----END PRIVATE KEY-----", "shared": "4b42fcf84b51b2b82f1f70b3cf49bd9dc6ab2672920a8de37e81ba7e99acf734", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 433, "comment": "special case for DA + CB in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAqDQd7swL5tsRQB73+ISsOt41ZQzCHxS1zbClzw7msVo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIAf/k4PbuuKUMj+eWY/9YX51q68+/S37cZ2xpOQDLFB\n-----END PRIVATE KEY-----", "shared": "e55fc931669bd02d1c64689eda62648212b1078c43b5caf97cf9763ff87a3455", "result": "valid", "flags": []}, {"tcId": 434, "comment": "special case for DA + CB in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAVaDmYxpS8p+5Chd3zLxp/5RUdFnVQfcugxbk1hZTWmc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOBOQSODpjszi3Dhvl/XWZU1AyHe5CiqTzumKlCjsN5E\n-----END PRIVATE KEY-----", "shared": "87f7976a17f3e03a7f1eb74e6db950b8c0994f40b7903495599d227725809e01", "result": "valid", "flags": []}, {"tcId": 435, "comment": "special case for DA + CB in multiplication by 8", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAeXbVIPGiUS1WSvQcaDE/U1GwFW1RGL5IF/GSeYrpd30=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDgtvp8QFYv7t9HXmjWngJIUiZprhXKzW1WHXXm9LxZA\n-----END PRIVATE KEY-----", "shared": "3bb3e30105a71901b115065e39bdb3e053d387b39027b12c92cdf4c638adf00d", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 436, "comment": "special case for AA in multiplication by 8", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAompyL3unHM/JbtjhCNfJ+ELRf5IFHufUKep/p5CKuQc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGDJr39NAxNqYDSuUt6t/Z1PJ0rYEigS65KlMWnINUFB\n-----END PRIVATE KEY-----", "shared": "f5cb3a1b76185a29a6360b2142feebb11f3d08f4fd8d73df3a5228624a521c02", "result": "valid", "flags": []}, {"tcId": 437, "comment": "special case for AA in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAyjotlvXdpIKwAjJMu9zx2syYFeq3l8cVHDqIx1ze1iE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICg/rovYspTeKEgFZEl1GWWrtcf6hrpMLFzcO7Uk2tFA\n-----END PRIVATE KEY-----", "shared": "b0b47868e70465ee2dd737f1ba5a6399e09cd813d72da7585ab45c946cc28d4d", "result": "valid", "flags": []}, {"tcId": 438, "comment": "special case for AA in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA7r2FiFC1b+u3B/J6eq1f9atLDgxzuchuxMoPQufzjnU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEAVOXA8pJgNtLpCxZ/CnoO0GJ8t3qU7pUypZsBomKZA\n-----END PRIVATE KEY-----", "shared": "581e4b12b0f39a7cc42dee4513ecfdd20b595f905f17ad8c1fbf1b5cb2068b31", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 439, "comment": "special case for z_2 in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAyAC/eZeDJ165MxK0PcAyzN+wCkt3yLN3LNL+yNt+Sgk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMjrBWKG4JjmsseeQvAH68arNwU0bNvazpSbXeHow2dD\n-----END PRIVATE KEY-----", "shared": "6bf264532fc70a6a7e459f4579eca6b84f8f76ab85c3264b20bca725a6eb6c40", "result": "valid", "flags": []}, {"tcId": 440, "comment": "special case for z_2 in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAe7xQTgTRNO7cE/Bt/fxpxRglej83QECkmo0h2sEJEQw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEh4gpVsScaf0OLXJ3ok+x2+SwNls2oT9jRAJIvKL7tC\n-----END PRIVATE KEY-----", "shared": "690305c9e192cd8a513f705b3f101ecdf3db1ea15a09c4a1bce3a8cdc3a1a93f", "result": "valid", "flags": []}, {"tcId": 441, "comment": "special case for z_2 in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAEyUz22Kv9PoG6WMUODv1jr3sUYOhny5MsXVSrhmjNm4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJh2AQ9NZMd//E19zNcrmsggeN64g2CWULjP+KaGcZ1G\n-----END PRIVATE KEY-----", "shared": "c58591b33e490e4766ff7addff570ce4e89a98338015a55df3d2f232aea3fc4f", "result": "valid", "flags": []}, {"tcId": 442, "comment": "special case for B in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAzrkMVlCM8zDH8lurQrBbVhKoMQaQEHrGOkBMCt54gAk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKil1PeJSlGVN7q/rHNt42BU9Qja5DS0/mPNVjOEaiZH\n-----END PRIVATE KEY-----", "shared": "3d145851b6ff2b92b5807ed1df21eb50c9f24c4474d4721db3abb7356df7b764", "result": "valid", "flags": []}, {"tcId": 443, "comment": "special case for B in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAZqCXZ6DYO7GNQE4SADdadF0fH3SdXcb4SiBe+moRvGU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPg+RkfoLFYKoILFlkHhO/Nmvo8k3AHRSAHmeEEWC+1H\n-----END PRIVATE KEY-----", "shared": "1401829aac4e64bcfa297a7effc60477090d3627a64a35b872ae055d2091785f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 444, "comment": "special case for B in multiplication by 8", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAOdQxMWMHyFdHvSvPT54PiJLuRd8V94Bs5lFH2X9QNHg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFjGuUvOmxX2SUbCqmpOODsLLUNlt5l+sjEKxO7xgDFF\n-----END PRIVATE KEY-----", "shared": "a0ebe6908c5472f937769b9aeb313224437fc5d73f4f866fe7ef41f30e359e09", "result": "valid", "flags": []}, {"tcId": 445, "comment": "special case for C in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAhMktjs89DLIt3n1yHwQUDC2cF5zIE85s+NstzmFoiA0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHhqlyB629Sw1r/J9JsYZgrTYGwS4yUES4aQtPoHh0ZB\n-----END PRIVATE KEY-----", "shared": "07538f1b6583041c4949fafae3349d62f9dd302d3d86857af0dedc0d5ad6741f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 446, "comment": "special case for C in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAqc7bnpQqRyIeQpaVMiDRAAfbMn0qy2jabvOk+He47x4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICgjECEOV1pZOTzxm75uJHUtwkdwbx4AMeXTmy3k//dF\n-----END PRIVATE KEY-----", "shared": "1223505fbb534c1bc6108e6b98b4f0af29e11158c02d333d6559beecd6d3e558", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 447, "comment": "special case for C in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAZOHAxfWUBbvGx9tBo0hcyfkcGDsPK34YlKer2Pu+6yM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMi/L9TEDQDxRlqtpoKxL6kt7BA0NISrYriHEzfeHTNF\n-----END PRIVATE KEY-----", "shared": "ee031868165f456f75907bf39742b820e0f8e6df9f9768d757d408e1cc92ff7b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 448, "comment": "special case for C in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEApo0vVeYOrHmDkmMQ9PrhP5Wyu/FAvl6pF1GITZAKtE0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMBqSktw9hMTbxjA+I4iRQhsPRpScXIQohrJ1jaC8udA\n-----END PRIVATE KEY-----", "shared": "c954fa7b042c32943e03191e367d54be0085fa8950ef2bec99620df79ecbea4b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 449, "comment": "special case for x_2 in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAbTzWI/JqdFP6BaAa51i6hNPFjZPWDOMnNaFeDQU9WxI=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICBZbh3FZZaCPTdpjfppnHmHSq7955f4Y++SE1mA+yBD\n-----END PRIVATE KEY-----", "shared": "7c3219b3c1fae1f95590ac843efd2084a1f4bd3efa2f592f022032db64ebcd77", "result": "valid", "flags": []}, {"tcId": 450, "comment": "special case for x_2 in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAjxlVRzRrPVO36k90KyLx73s8wBp9Pc0ZqnxbA/Mb0hQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDgUFRjo5e+h0DHGxNlUgCOfbDC4zNjHUangS9OuwXNC\n-----END PRIVATE KEY-----", "shared": "a31f6b249d64a87c4aed329c6c05c3f2240b3ca938ccdc920ba8016c1aeaeb45", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 451, "comment": "special case for x_2 in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA/8T+LCEnownHOVZWUemBL4NKhtutu3h3aXf3huzbAhc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICBxR/K2j+8e/BCgT5iPDrGLJzsLXtF6p68yyQSA4ZtD\n-----END PRIVATE KEY-----", "shared": "4cff9f53ce82064882329a18ea4e4d0bc6d80a631c87c9e6fdc918f9c1bda34a", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 452, "comment": "special case for x_2 in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAhHW6vuq5mA1Car1TI9+zNbIZ4Sm92uTWzrzaUHVKaCU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEiAhFN7hA+ck8pXs+6ASRQY1EIhET4D9WNVMCYE0DVH\n-----END PRIVATE KEY-----", "shared": "248d3d1a49b7d173eb080ab716ac8fde6bd1c3ed8e7fd5b448af21bcdc2c1616", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 453, "comment": "special case for x_2 in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAgfkKL2Yz0wwrcqJXldKklGOoC2sO3Fqmi65L9zgYVTk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICjPwdA/XHQo/z4gsTcmizPMx02wNYLSEnxWbfSsmfRB\n-----END PRIVATE KEY-----", "shared": "66c6e70cf630be90a2c88fcde7f58cff3868660fa96406e8df4ac677dbd85f50", "result": "valid", "flags": []}, {"tcId": 454, "comment": "special case for x_2 in multiplication by 8", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAQWJuM7PI9IvRnkne0wfytjvecFxPPN+dT5K/N8SMukI=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMjjfRDz0D2z9D5Ge935j1lctSmtJTwg1JEoLRQAuedA\n-----END PRIVATE KEY-----", "shared": "06283fcf69dc83e99d92e5336f499a1d8fa75ed2c819b5ae6ea8094454324b27", "result": "valid", "flags": []}, {"tcId": 455, "comment": "special case for x_2 in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA67MveBwOibJS5hH52PefhWeHTJZlmDFLLxaqRM/AeEM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIAAjfpFAantNth54DFl2+7kmzazi+9/bz85l5tvneCpC\n-----END PRIVATE KEY-----", "shared": "7d2affb43355f5db1294daff55f59b1f17e7d25bca20746f12484d78e5015517", "result": "valid", "flags": []}, {"tcId": 456, "comment": "special case for x_2 in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA+nXm8IyoFbTkKvJKjgV8ngDoKOM9EsDpTRASp1gzZ0Q=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEicQYSiOo9e7GijG0GqLAOSzW+xI/EKzbTedSkrS5pD\n-----END PRIVATE KEY-----", "shared": "ef8e78cab091d667888489fd3a2ec93fb633427d02eb77b328d556f2b2b0e266", "result": "valid", "flags": []}, {"tcId": 457, "comment": "special case for x_2 in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEATZYyDNsMpSZV6REYwz+Tr+SuaenlE/9FBnULjqeEzkY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMBZV/vDoOLCKirvYnZRyh6ZMHuCoMYXD3lQozTzAElB\n-----END PRIVATE KEY-----", "shared": "c8d85bfa74b4b26461297b350c975183fea9d33ba29c3a4934509c2ecda58a79", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 458, "comment": "special case for x_2 in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAwO8bfCAjfbNwUB8kJ05OupGZiuRUX5NwB+HEouq2M2U=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGARHGYp9zY1mFvpZLhF+HqIrlZS1FuxRRzoz9LqRf5B\n-----END PRIVATE KEY-----", "shared": "22557e0d8741ed2a63afd5e313aa1579fc0c88c7772e23a676c94b60c89df577", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 459, "comment": "special case for x_2 in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA1TTY/01Wpz73YV6UUjsX417bPQ+4fpjGhTb2PxFKjWw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFh4WImiFtFUVlgtTh496enKSkMpVEFtgcr1KytDTBdG\n-----END PRIVATE KEY-----", "shared": "54d7fc17bad00296ba50b0f3d5bf8fb83f82d571952a5fdb5a494120cc61446b", "result": "valid", "flags": []}, {"tcId": 460, "comment": "special case for x_2 in multiplication by 8", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAczpxG6AbbptkoL5M3KjHzzxm3yQ11SSPtEE/7G7gP3A=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGC+84o4kOwe0Fwpn863fbXq1LiNnpMbDyHWZPd9+bVE\n-----END PRIVATE KEY-----", "shared": "db6851b12585bc11be9362c96a545c6f2ba55f04009792463b96a38cb9b3f07c", "result": "valid", "flags": []}, {"tcId": 461, "comment": "special case for x_2 in multiplication by 8", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEANXON1TnWD2nNGhz/yKQravaP595FOS0Cgx4qd1AOong=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFhU7lZoeO+Lfrr1oFgwbyUO3wyE/VKvLXS3zjwe3adG\n-----END PRIVATE KEY-----", "shared": "f6d1a664257fa5de3d4d57f04eda2976bf1e35cc3ac513e1ee84d57d2135ed13", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 462, "comment": "special case for x_2 in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAzpMrWvS+RyH5b3t5uhxDsgaH1K9Jw3tY3IlCeeBLtXg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJhbVRJh/OON3I/zrdMvXCaBHScbmheU4knddqON8oRG\n-----END PRIVATE KEY-----", "shared": "f8f7625ac5bde63f753a9bb4aefbfb9c4647207708af9d774ef08ff1b1e5a354", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 463, "comment": "special case for E in multiplication by 8", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA42VUSDOeSFCAbrWKu6DIkYVRHqcsN8SelYPubdI10hM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIgVBSNE3K2X79E0HpByqAjPmZ5G5SzwTgz7zZkB4Y1D\n-----END PRIVATE KEY-----", "shared": "5e10dfbff4443efcae2ccc78c289a41460d5a82f79df726b8824ccbef7146d40", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 464, "comment": "special case for E in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEATRaWWxY36deuj+tJntBVOWKpqgAi0WIMkoBy9lAbxBs=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILjgMunl/7qgBDkPOguQC8fPXREji37JZK/EvaKqbDRE\n-----END PRIVATE KEY-----", "shared": "19d7b44c1847c44e8f37a22ab69c180fd9d787f204123013e1b16800b9cd0f57", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 465, "comment": "special case for E in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAxrnmKIc3rUBFLOwQIocdkK8WQtEL0Kl3krGpyJmOIiA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHAShSIR9lNvynmTfn4xbJFJsOIOoD+VHhuwcolcoOBE\n-----END PRIVATE KEY-----", "shared": "db990d979f4f22f766e7826d93554e771b361de461274d6c37baadeb8ef7be4e", "result": "valid", "flags": []}, {"tcId": 466, "comment": "special case for E in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA1Wb6tQWsTHo9w7lAPvEhOSy74hIW5by46rLclAiYbjQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINA5wbnsR2PgrYoO8rCHApfQ+LSH5mBZWkhBBdGA4UpH\n-----END PRIVATE KEY-----", "shared": "6d7fc5d4a8f534b1bc0fa5e078104234675c02664736957abdb27df6faf07c00", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 467, "comment": "special case for E in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEARo017Pttm3JyUjJ2zF4TdgUZZn8OHjiI2kxWlV/pEVE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFjvy8h3fBtU8Jxhohbv1CcpLrEjEtuzsyvUUlSmaD5H\n-----END PRIVATE KEY-----", "shared": "539c8d629ab51c2f3ea7278fd5f1c31b6c150a82fe3f786b93ffa159fd6d9316", "result": "valid", "flags": []}, {"tcId": 468, "comment": "special case for E in multiplication by 8", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAGSlTh0OXff6iC/SSfdq7LzuxXKwkYQVFCISXGIVLVWg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMjXNEYCbNDqeVdzwut7FjSM1fIo41Lbx3Mowti5zeJA\n-----END PRIVATE KEY-----", "shared": "dee3fd19c8f296415448b21af44385ec46727bbe67d4839b93efe2f680e76d34", "result": "valid", "flags": []}, {"tcId": 469, "comment": "special case for E in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEALXq0xvWYZTVe6OneV9sZqt93CLfB0agYSHw0BiO63G0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJi1WVI7x3iwQYr1PAwy9v9c93H/XfiufL98O3Ku21tD\n-----END PRIVATE KEY-----", "shared": "2a0340aaafa05d00529c09057ed0145f34d2de66a3e149cf084ea97168914f39", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 470, "comment": "special case for E in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAQ4OfSmqiBsgsWnP0nYyeVzgms7pyNdMSmHwXrr7mJ3Y=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFiYFQJ8r4JxTpbJ+Rus5m7Euj6S3z+hS5uP5QNVbkVD\n-----END PRIVATE KEY-----", "shared": "00313717d33e3b41a0865986157582e053502a172b88d01bb7b10831a9fc4e6c", "result": "valid", "flags": []}, {"tcId": 471, "comment": "special case for E in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAPDIefwueVVvCZKLOphfmsrVi66sh/gwibD5Ie335on0=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIBxX2cnDJl4mFXOrqmbmVfM2jMyb3a7RHSrUqsew3BB\n-----END PRIVATE KEY-----", "shared": "9b6be9e6f2fdb5d3321842225d3e91d14828cc53ba6654dabe190b0c3edeb309", "result": "valid", "flags": []}, {"tcId": 472, "comment": "special case for DA - CB in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAQuWmuOllS7StYkrz9JGHeXdRPMh3XI+zEq0Z2/OQOig=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBAbmQvYPWhBJv8EfZMMJ9CGpYjdGWg9Jinw409DdKtB\n-----END PRIVATE KEY-----", "shared": "223f1eb552308373026d11c954684ce6db870b638b190b9443e50aae219f4e3e", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 473, "comment": "special case for DA - CB in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAClHdkKuYX23q9y8WxFAU2ibfhIaX9lgtdWiPUiM0K1E=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICAAibcS2aIFBZd3nUY3EvzSI+PWeHnA+3YG+PXw7+5A\n-----END PRIVATE KEY-----", "shared": "fb95ce4a3c1f325638b7d47f4216d39a7c6c5da9a01caa297c37b62816555b2a", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 474, "comment": "special case for DA - CB in multiplication by 8", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAiEIxc1e96CXvQ4ocU5BvuLBOo2D37zOMeOZoWGBHk2o=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPBPh/TmI69MMc7KC7h/rC1bElF7WnKEkCrXWDjmXx5B\n-----END PRIVATE KEY-----", "shared": "488b8341c9cb1bbf124510b9f8dae4faf2e0dca9b84e00e952a63b5aa328a860", "result": "valid", "flags": []}, {"tcId": 475, "comment": "special case for DA - CB in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAxx2S08ktv67XVfsyeXtmfMhrDnk2JJjirKOMaJcTsW4=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIDg8vVo90JAdCaPKw9Onepec7PFeIGpVPkyj8kuQeDlF\n-----END PRIVATE KEY-----", "shared": "1129eae97bf75f7314f2e1b403b18737ad830c80429e2ba0d4866b362399855f", "result": "valid", "flags": []}, {"tcId": 476, "comment": "special case for DA - CB in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAOiHRz3s3RNGtJhlzNYRJgsKgxqWqg1SSvQPEAaT+Z3g=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHAd8J5XuYrsN1dF3xR7cpSaayuyyjo0iBUS7jHnkK1C\n-----END PRIVATE KEY-----", "shared": "072f51d94727f392d59dc7caff1f4460452352ec39c32a1c9f071e388833da56", "result": "valid", "flags": []}, {"tcId": 477, "comment": "special case for CB in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA0SjqPhMyXtbr1lM6n9MEWlXyWti2fe8wkShDUEwaqyk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILD/pfSSK7EXrXX/Q6ysYjMe+qRVNv6IMG5KTLWNtzpH\n-----END PRIVATE KEY-----", "shared": "30512142d3e3a4cad6726d9d35f2e043fca9dfb750884ae22b2547c840f3587b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 478, "comment": "special case for CB in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA4HnI+EIxZcfgosSLSr6Qrs5ObZA9eloWJfrQQQzVWzI=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGheMnHSAVdBdWYSqTDoWLkwrPIBgUXzgsg9jM7S4iBE\n-----END PRIVATE KEY-----", "shared": "5b81b3761a66d199e8ef99d2494bd57a0229d4564a7f6d6055f22aa48681bd3a", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 479, "comment": "special case for BB in multiplication by 8", "public": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAZZIqBum+Topeis6xpOCP6Q8B4Q7y3ScxVCfO38+V7DI=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPjhYdaSl+AX18UbGx/zunA9TEz4/CuP9H90w/+MfTVB\n-----END PRIVATE KEY-----", "shared": "038de7fdb9cc0030f5c11dda00589f0a95f65658815b06ed013553a02b6c5017", "result": "valid", "flags": []}, {"tcId": 480, "comment": "special case for BB in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA02okDpctwW6bl6mXraM38Cdg0FxG1/jXtOnqmmNcfGQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBBddYn4q+8KzwlA2oSmno8vMG+nPJr9JzQih8HbqABE\n-----END PRIVATE KEY-----", "shared": "22b0dea3b3b7ca55eceeaae6443426548c7c15cc7ddf31780318d1c23879c16a", "result": "valid", "flags": []}, {"tcId": 481, "comment": "special case for BB in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAT1uLmJK4pG3wjXakdFscWNTno5SQVDWHVojKEfHp2Go=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBiT1DiLDpDwtQIIqo8Mwk9XbQNkG68cPt2yo++mnJ1A\n-----END PRIVATE KEY-----", "shared": "a25e1306684ad7870a31f0404566e8d28f2d83d4b9497822c57f8781b18fec20", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 482, "comment": "special case for BB in multiplication by 8", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAqi8CYoJpE5p6ihb96VybrX2n/71UOcOWp9d7bDIT5n8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIABlFxMBv2uQ+xbvo1UJFh8b1rO5MTDUkK+f4iTdFV9F\n-----END PRIVATE KEY-----", "shared": "bb4431bea7a5871c1be27a2674094627eaaa4425c99cd3fa41bd7e13cbd7bf7e", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 483, "comment": "special case for A in multiplication by 8", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA2ZXLKH6anFeR88rj1JSltRah4my8kw9D5zyLcLadeDs=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBDIGk542CFFsmbh10s4ab8cJ0J4A+uxHJL/gHPR5MxG\n-----END PRIVATE KEY-----", "shared": "330f5d0b5bccc90f7694dfdd9c6449a62d93af8840eaf571e3e0610e0198b03f", "result": "valid", "flags": []}, {"tcId": 484, "comment": "special case for A in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAR5r7HnPcd8N0PlHp7AvMYc5m7QhNwQv6J5S0w+SVN2k=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEi5i0qZ6t1zASwH/lxKC5WQrFXoITU7QdX2ZeFxiLxB\n-----END PRIVATE KEY-----", "shared": "bdef00caa514b2f8ab1fb2241e83787a02601ecdff6cf166c4210f8c1ade4211", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 485, "comment": "special case for DA in multiplication by 8", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAN47aQUcLDyOKIA+AgJrVYspB5iQRph/rf36bdStVRkI=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBiXZ444Iiph/hBdxmQ8HrWUDo28c+1sAPJaNDKPQ6ZB\n-----END PRIVATE KEY-----", "shared": "bfd5b5acd2d89f213a26caf54062f9a24e6f6fd8ddd0cd2e5e47b7fea4a9c537", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 486, "comment": "special case for DA in multiplication by 8", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEADK11Ra3i/ZP8rgB8l2SDSPJthYKb23Ijpj7MuE5W1HU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKiYr4E44RrkW7zvpzcYKlcYhfktUVwyBWx8sNferEdB\n-----END PRIVATE KEY-----", "shared": "c8085877800c175e949cdd88e196eb9c4841da2ac446dfed9085bda5bbec265d", "result": "valid", "flags": []}, {"tcId": 487, "comment": "special case for AA in multiplication by 9", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAYPJ+0KJ4BM7SN888HMd2ZQ+zILrm1ay1ZOl7VsuiUhA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILC/727AlbWh+TkX0y8Woh0EYsH94XRG9aWQIy2ciV9K\n-----END PRIVATE KEY-----", "shared": "4c300895827382a9d1079028bd6f694a7a12ddac9c76abac6fdf5d29457a3310", "result": "valid", "flags": []}, {"tcId": 488, "comment": "special case for AA in multiplication by 9", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA+TpzJwrBkZS45P/QK+SxQ4Ul+Ep2IkaI6omp3Wob1iM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGBJfURk7YgjxQ+8a2hiCCbE9inB2RkwWN9r+FfGrsxL\n-----END PRIVATE KEY-----", "shared": "7285fbb3f76340a979ab6e288727a2113332cf933809b018b8739a796a09d00b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 489, "comment": "special case for AA in multiplication by 9", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAz4DDD8v9U1Zmyh2kmeLpnMU3Bj4t4ZRY/PkvXuNKz0c=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIAjGy+A3kqOCnwborVTFXbETI2rA3MmrapprEO7RBBtI\n-----END PRIVATE KEY-----", "shared": "dabc3bd49f19cf7071802e43c863ed0b1d93a841588098b98a0c581bf4fe0a11", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 490, "comment": "special case for AA in multiplication by 9", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAaY7/4K1C4V7h9G/eb8UHT/2hg7zxstuGR/Vh3dGR3WA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFAETaMxXdCC6d+2oZlKq7Mx9T4NHBJjM4Oyo8hnjP5M\n-----END PRIVATE KEY-----", "shared": "a61a3b150b4770532373676298c9a5da28adcc4365b06fe07c959ca80e477a57", "result": "valid", "flags": []}, {"tcId": 491, "comment": "special case for AA in multiplication by 9", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAvRVltKP4UV3/V3vm3LQUUR09TsLeFeC9RbKOnMTK72A=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIChWQNp6SCUuNd3OYMFK3bcwl/vJrC+HyNJ3LOiapr5N\n-----END PRIVATE KEY-----", "shared": "916ab4f3bfc8321e1087d9c5444f8f7a43e9ca6d29e7ba98a19dc05fff34ed4c", "result": "valid", "flags": []}, {"tcId": 492, "comment": "special case for AA in multiplication by 9", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAuGSeE4Q/gM9XAjmOSpqMN48p2pbf1lefHrT36jTfZ2U=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHgyccIRmboulOrZLNndefcKqzeLWUl0VdMnpZB9r8tK\n-----END PRIVATE KEY-----", "shared": "844a5dd5139554ca7b41cbe6a4796193912e7aa4e201cc68944ce2a55774a10f", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 493, "comment": "special case for AA in multiplication by 9", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAw5aThzer33keCal+uld8Q32bZ8La6U4T6rcpbsD8c34=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINBnaguaBGxi1bLnQNnMQ/o3ll3qk8IyVPe/Vp8r66pK\n-----END PRIVATE KEY-----", "shared": "10780333b2a6170136265bb5ebc6c818817f2e48ae372528c8f34433fdd6215a", "result": "valid", "flags": []}, {"tcId": 494, "comment": "special case for DA - CB in multiplication by 9", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAVXuCUBLZjwZbuVoqubLS2Lg/0gN5ElCMJj+G1+NsTyQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGCMhNK3b8zaV56XTbPTss45prwNrUQFmdsiQRtgRnhJ\n-----END PRIVATE KEY-----", "shared": "5ce84842dbae8b795b3d545343558045508f271383bfb3dd3943f4101398c864", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 495, "comment": "special case for z_2 in multiplication by 9", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEArpgpbUovvLtAtHL0BjIxYIuxRlwibIpKLf8pr9kViCo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIIDyM5NqiCGTbTkRTITZKeeXYLJ2gHeeUAnhcJQQ3Y5P\n-----END PRIVATE KEY-----", "shared": "4f11aa0c313195f96f25cadcbf49f06a932d8b051879ea537d1c6dfee7f36d35", "result": "valid", "flags": []}, {"tcId": 496, "comment": "special case for z_2 in multiplication by 9", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAi50kmCn76BMz2FBQ2oiZj2P6xmVnnifbviG3Rd0U4UU=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIMjYCxo08hGU8Eem8DKLuUfi56/2oENVOqB/Kr+ZqvBI\n-----END PRIVATE KEY-----", "shared": "1d619070bf5626064be10025e74e336c81ef3166b743f99c751fb90587c31d7e", "result": "valid", "flags": []}, {"tcId": 497, "comment": "special case for z_2 in multiplication by 9", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAYYlgk+JpfHgjCv3aEmOcvkNCgnuNKwkygfFI62C5A0s=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJAhR3tFI2FYAFk2TG+U9Jge6U6j+bfTdDm8gq5FgW9N\n-----END PRIVATE KEY-----", "shared": "532e797861db56b9d5db8825fb72f8629c2422f8abea721ad2d7b9e77a95b576", "result": "valid", "flags": []}, {"tcId": 498, "comment": "special case for z_2 in multiplication by 9", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAzMHcGGIp26mpNgoPf/ACR6NzJiWsqs0Y6hOpqLQPrE8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIGB52uBMQKWepODIwXCS5MheqRM9FDMHNjSHg2304wNJ\n-----END PRIVATE KEY-----", "shared": "4f678b64fd1f85cbbd5f7e7f3c8ac95ec7500e102e9006d6d42f48fb2473ab02", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 499, "comment": "special case for z_2 in multiplication by 9", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAaeNowLfnjrnzpTv0WPbnncSIO/lFjwSowSxN3ZTWIVE=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEICgdtqWsmkfUp7K5Gof2U2zmLU5RKbjWR7l/nFBAFIlM\n-----END PRIVATE KEY-----", "shared": "e069fd06702f10f33adb8cf0766880634865b510e2da409241fb5f178050514a", "result": "valid", "flags": []}, {"tcId": 500, "comment": "special case for z_2 in multiplication by 9", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA8h+brdmN2KEDzCq1SE+sbCv90mce5uZ0E0qGuJzukWA=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEINgw88R4WCmg+UWFfg6F4K5yNwK1d4O5M80qKtBUhP5J\n-----END PRIVATE KEY-----", "shared": "fee218eb1f92864486e83c1731f04bb8c7e6d7143e3915bcbf80fe03ff69dc77", "result": "valid", "flags": []}, {"tcId": 501, "comment": "special case for E in multiplication by 9", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA6FMGKy1vONAh1kUWPqII0OGTpHnxH5mXG5jiEYj9Cyw=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIBAjC9ByH0yMS5IYgd2IxgOvUB7oDiEC+KzDDPiyrNNJ\n-----END PRIVATE KEY-----", "shared": "64bdfa0207a174ca17eeba8df74d79b25f54510e6174923034a4d6ee0c167e7b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 502, "comment": "special case for E in multiplication by 9", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEANi65Laufsp9+0OA4Q9zBV5eSjCtOUewmAgQXnBwSlF8=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIPCjTW12iW4Xy49m/tojEV/7lvJGuCO7Y97AgzV4fedM\n-----END PRIVATE KEY-----", "shared": "d7f4583ee4fe86af3a3f1dfcb295ba3a3e37bced7b9c6f000a95336530318902", "result": "valid", "flags": []}, {"tcId": 503, "comment": "special case for E in multiplication by 9", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEA/1Q/HoGZbohjHwMM66fmA7EwM+/SBeaL02soRoE0qnM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIJBzwdChc8f/AtyWahZZk9nEyTV1FPemu3qqSwgncYlI\n-----END PRIVATE KEY-----", "shared": "c1b5e5f4401c98fa14eba8aafae30a641bfd8fb132be03413f3bf29290d49e0b", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 504, "comment": "special case for x_2 in multiplication by 9", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAkO9whE6tFhP2nffXjAV4E/hmwNlebSLK7koBK5wcSzM=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILDBgiVm4BbBKuNewDXt0Jrzy3pI9VyQKOBeEXiow4JO\n-----END PRIVATE KEY-----", "shared": "9369ebb3d2b744341cba77302719a4b2d63aff612872f86d9877a76bc919ca1c", "result": "valid", "flags": []}, {"tcId": 505, "comment": "special case for x_2 in multiplication by 9", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAiMGuV1rQc92mbG6st7f0NuH4rXKg21wE5WYLe3GeTEs=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIOBv5k4hF3lvmXu807ytMGfPEpFkCjpkP7NZgJpAFoNN\n-----END PRIVATE KEY-----", "shared": "335394be9c154901c0b4063300001804b1cd01b27fa562e44f3302168837166e", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 506, "comment": "special case for x_2 in multiplication by 9", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEA3P/EweH7pf2p1cmEIdmcJXr6kJIbwhKgRtkPZoPopGc=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHB+6B8ROiRMnYdgixIVjFD5rB8siUjRcK0WqwrYZtdL\n-----END PRIVATE KEY-----", "shared": "7ecdd54c5e15f7b4061be2c30b5a4884a0256581f87df60d579a3345653eb641", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 507, "comment": "special case for BB in multiplication by 9", "public": "-----BEGIN PUBLIC KEY-----\nMCowBQYDK2VuAyEAbABEzRBXjFr/H/SRewQbdsmpriNmTrjPl4vXqhks8kk=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHCJZUuqy7Zb0AzYy53kaA50gHXohCymnUSPtQ/qhedO\n-----END PRIVATE KEY-----", "shared": "0d8c21fa800ee63ce5e473d4c2975495062d8afa655091122cb41799d374594f", "result": "valid", "flags": []}, {"tcId": 508, "comment": "special case for BB in multiplication by 9", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEA2Qid6QLhQ9zZEH5aM5Oj9/4F2SbDV7R+MHojbLWQ/WQ=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIICJeExSzWfkU25WghjHtwM7KEE/lC/KJO1p5DSW76FL\n-----END PRIVATE KEY-----", "shared": "db6fec44bf118316a6bdfbae9af447baede4d82daa16bed596ea6f05d4a51400", "result": "valid", "flags": []}, {"tcId": 509, "comment": "special case for BB in multiplication by 9", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAjEomqjGcLMSkFYwrxpoNWzQLYGKKFM8xuwrl3cOK6GY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIADnPk4BMUi58FJzutYmuxJqQOxFWPVCUJa0iUfgqd5K\n-----END PRIVATE KEY-----", "shared": "ecc1204bc753c4cec4c9059fd7b504944ebf995ab1b1d49f0b3b325353be3a15", "result": "valid", "flags": []}, {"tcId": 510, "comment": "special case for BB in multiplication by 9", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAznKV0SJ8kGKquc8C/FZx+4FjLnJTZ/Ex1BIoJKYTLWg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIHjtTJv59E242TOImFGR7PWSJrnBIF/n52LDJ1gcdYhO\n-----END PRIVATE KEY-----", "shared": "3740de297ff0122067951e8985247123440e0f27171da99e263d5b4450f59f3d", "result": "valid", "flags": []}, {"tcId": 511, "comment": "private key == -1 (mod order)", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAbAWHE1KkUdvhgu1ea6VU8gNEVv/gQaBU/5zFa46UY3Y=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIKAjzdCD71u4LxDWLlnhWmgAAAAAAAAAAAAAAAAAAABQ\n-----END PRIVATE KEY-----", "shared": "6c05871352a451dbe182ed5e6ba554f2034456ffe041a054ff9cc56b8e946376", "result": "valid", "flags": []}, {"tcId": 512, "comment": "private key == 1 (mod order) on twist", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEALq5ew91JTp8tN9JY+HOo5unQ29Hjg+9k2Yu5Gz4L4DU=\n-----END PUBLIC KEY-----", "private": "-----<PERSON><PERSON><PERSON> PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIFgIPdJhrZHv+VIyLsgkxoL///////////////////9f\n-----END PRIVATE KEY-----", "shared": "2eae5ec3dd494e9f2d37d258f873a8e6e9d0dbd1e383ef64d98bb91b3e0be035", "result": "acceptable", "flags": ["Twist"]}, {"tcId": 513, "comment": "special case private key", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAPj53CO9ypt142FgCUIl2WxwwoZcVrBno2RcGfSCOBmY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEhVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV\n-----END PRIVATE KEY-----", "shared": "63ef7d1c586476ec78bb7f747e321e01102166bf967a9ea9ba9741f49d439510", "result": "valid", "flags": []}, {"tcId": 514, "comment": "special case private key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAn0C7MPaKtnscS4tmSYL9qwT/OFzYUN6scy9/twXmATo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEhVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV\n-----END PRIVATE KEY-----", "shared": "8b98ef4d6bf30df7f88e58d51505d37ed6845a969fe598747c033dcd08014065", "result": "valid", "flags": []}, {"tcId": 515, "comment": "special case private key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAvjs+3v+vg8VK5SY3myPdefHLQURuNof+80frm18Nwwg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEIEhVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV\n-----END PRIVATE KEY-----", "shared": "cfa83e098829fe82fd4c14355f70829015219942c01e2b85bdd9ac4889ec2921", "result": "valid", "flags": []}, {"tcId": 516, "comment": "special case private key", "public": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMCowBQYDK2VuAyEAPj53CO9ypt142FgCUIl2WxwwoZcVrBno2RcGfSCOBmY=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILiqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqpq\n-----END PRIVATE KEY-----", "shared": "4782036d6b136ca44a2fd7674d8afb0169943230ac8eab5160a212376c06d778", "result": "valid", "flags": []}, {"tcId": 517, "comment": "special case private key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAn0C7MPaKtnscS4tmSYL9qwT/OFzYUN6scy9/twXmATo=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILiqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqpq\n-----END PRIVATE KEY-----", "shared": "65fc1e7453a3f8c7ebcd577ade4b8efe1035efc181ab3bdb2fcc7484cbcf1e4e", "result": "valid", "flags": []}, {"tcId": 518, "comment": "special case private key", "public": "-----B<PERSON>IN PUBLIC KEY-----\nMCowBQYDK2VuAyEAvjs+3v+vg8VK5SY3myPdefHLQURuNof+80frm18Nwwg=\n-----END PUBLIC KEY-----", "private": "-----BEGIN PRIVATE KEY-----\nMC4CAQAwBQYDK2VuBCIEILiqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqpq\n-----END PRIVATE KEY-----", "shared": "e3c649beae7cc4a0698d519a0a61932ee5493cbb590dbe14db0274cc8611f914", "result": "valid", "flags": []}]}]}