#
# SSLeay example configuration file.
# This is mostly being used for generation of certificate requests.
#

RANDFILE		= ./.rnd
CN2 = Brother 2

####################################################################
[ req ]
default_bits		= 2048
default_keyfile 	= keySS.pem
distinguished_name	= req_distinguished_name
encrypt_rsa_key		= no
default_md		    = sha256
prompt              = no

[ req_distinguished_name ]
countryName         = AU
organizationName    = Dodgy Brothers
0.commonName        = Brother 1
1.commonName		= $ENV::CN2

[ v3_ee ]
subjectKeyIdentifier=hash
authorityKeyIdentifier=keyid,issuer:always
basicConstraints = CA:false
keyUsage = nonRepudiation, digitalSignature, keyEncipherment

[ v3_ee_dsa ]
subjectKeyIdentifier=hash
authorityKeyIdentifier=keyid:always
basicConstraints = CA:false
keyUsage = nonRepudiation, digitalSignature

[ v3_ee_ec ]
subjectKeyIdentifier=hash
authorityKeyIdentifier=keyid:always
basicConstraints = CA:false
keyUsage = nonRepudiation, digitalSignature, keyAgreement

