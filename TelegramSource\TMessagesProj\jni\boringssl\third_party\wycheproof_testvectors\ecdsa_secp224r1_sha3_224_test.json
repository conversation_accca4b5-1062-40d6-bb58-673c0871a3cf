{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 384, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of ASN encoded ECDSA signatures."], "notes": {"BER": "This is a signature with correct values for (r, s) but using some alternative BER encoding instead of DER encoding. Implementations should not accept such signatures to limit signature malleability.", "EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "MissingZero": "Some implementations of ECDSA and DSA incorrectly encode r and s by not including leading zeros in the ASN encoding of integers when necessary. Hence, some implementations (e.g. jdk) allow signatures with incorrect ASN encodings assuming that the signature is otherwise valid.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission."}, "schema": "ecdsa_verify_schema.json", "testGroups": [{"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5", "wx": "00eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7", "wy": "00eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004eada93be10b2449e1e8bb58305d52008013c57107c1a20a317a6cba7eca672340c03d1d2e09663286691df55069fa25490c9dd9f9c0bb2b5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE6tqTvhCyRJ4ei7WDBdUgCAE8VxB8GiCj\nF6bLp+ymcjQMA9HS4JZjKGaR31UGn6JUkMndn5wLsrU=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "303d021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021c56c80970d9a308a9f639ed199ac088f93ba9afd04c53f48e4fa88d3a", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Legacy:ASN encoding of r misses leading 0", "msg": "313233343030", "sig": "303d021cbdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 3, "comment": "Legacy:ASN encoding of s misses leading 0", "msg": "313233343030", "sig": "303d021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021ca937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "acceptable", "flags": ["MissingZero"]}, {"tcId": 4, "comment": "valid", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "valid", "flags": []}, {"tcId": 5, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "30813e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": ["BER"]}, {"tcId": 6, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "3082003e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": ["BER"]}, {"tcId": 7, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "303f021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "303d021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "3085010000003e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "308901000000000000003e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3080021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "303e028000bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376028000a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "3040021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d030000", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "30400000021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d030000", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "3040021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d030500", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "3043498177303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "30422500303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "3040303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d030004deadbeef", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "30432222498177021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "304222212500021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "3046221f021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453760004deadbeef021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "3043021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453762222498177021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including garbage", "msg": "313233343030", "sig": "3042021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac1584537622212500021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including garbage", "msg": "313233343030", "sig": "3046021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376221f021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d030004deadbeef", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "3046aa00bb00cd00303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "3044aa02aabb303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "30462225aa00bb00cd00021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "30442223aa02aabb021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "including undefined tags", "msg": "313233343030", "sig": "3046021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453762225aa00bb00cd00021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "including undefined tags", "msg": "313233343030", "sig": "3044021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453762223aa02aabb021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3080303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d030000", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30422280021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453760000021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3042021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453762280021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d030000", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3080313e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d030000", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30422280031d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453760000021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3042021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453762280031d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d030000", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e3e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f3e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "313e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "323e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff3e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "using composition for sequence", "msg": "313233343030", "sig": "3042300102303d1d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "truncated sequence", "msg": "313233343030", "sig": "303d021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "truncated sequence", "msg": "313233343030", "sig": "303d1d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 57, "comment": "indefinite length", "msg": "313233343030", "sig": "3080021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d030000", "result": "invalid", "flags": ["BER"]}, {"tcId": 58, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "3080021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d0300", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "3080021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d0305000000", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "3080021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03060811220000", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "3080021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d030000fe02beef", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "3080021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d030002beef", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "30403000021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "append empty sequence", "msg": "313233343030", "sig": "3040021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d033000", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "3041021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03bf7f00", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "sequence of sequence", "msg": "313233343030", "sig": "3040303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "301f021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "305d021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 69, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "303f02811d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": ["BER"]}, {"tcId": 70, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "303f021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac1584537602811d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": ["BER"]}, {"tcId": 71, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "30400282001d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": ["BER"]}, {"tcId": 72, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "3040021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453760282001d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": ["BER"]}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303e021e00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303e021c00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021e00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021c00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "30430285010000001d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "3043021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453760285010000001d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3047028901000000000000001d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3047021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376028901000000000000001d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304202847fffffff00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "3042021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac1584537602847fffffff00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30420284ffffffff00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "3042021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453760284ffffffff00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "30430285ffffffffff00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "3043021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453760285ffffffffff00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "30460288ffffffffffffffff00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "3046021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453760288ffffffffffffffff00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "303e02ff00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac1584537602ff00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "removing integer", "msg": "313233343030", "sig": "301f021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "lonely integer tag", "msg": "313233343030", "sig": "302002021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "lonely integer tag", "msg": "313233343030", "sig": "3020021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac1584537602", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3040021f00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453760000021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "3040021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021f00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d030000", "result": "invalid", "flags": []}, {"tcId": 96, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "3040021f000000bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": ["BER"]}, {"tcId": 97, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "3040021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021f000000a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": ["BER"]}, {"tcId": 98, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "3040021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453760000021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3040021f00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453760500021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "appending null value to integer", "msg": "313233343030", "sig": "3040021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021f00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d030500", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "truncated length of integer", "msg": "313233343030", "sig": "30210281021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "truncated length of integer", "msg": "313233343030", "sig": "3021021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453760281", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "30210500021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "3021021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453760500", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e001d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e011d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e031d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e041d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303eff1d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376001d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376011d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376031d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376041d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376ff1d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "dropping value of integer", "msg": "313233343030", "sig": "30210200021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "dropping value of integer", "msg": "313233343030", "sig": "3021021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453760200", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "using composition for integer", "msg": "313233343030", "sig": "30422221020100021cbdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "using composition for integer", "msg": "313233343030", "sig": "3042021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453762221020100021ca937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "303e021d02bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d02a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453f6021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d83", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "303d021c00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac158453021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "truncated integer", "msg": "313233343030", "sig": "303d021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021c00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "303f021eff00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "leading ff in integer", "msg": "313233343030", "sig": "303f021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021eff00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3022090180021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3022021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376090180", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3022020100021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3022021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376020100", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d01bdeb8edbcb30885c65bcb58d6ea1eba154c61a02d5e2fff171e07db3021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021cbdeb8edbcb30885c65bcb58d6ea3be5b93543986ae28ad66b9282939021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021dff4214712434cf77a39a434a72915d2b018bf2d63b3dfa2953ea7bac8a021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c4214712434cf77a39a434a72915c41a46cabc67951d7529946d7d6c7021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021dfe4214712434cf77a39a434a72915e145eab39e5fd2a1d000e8e1f824d021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d01bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021c4214712434cf77a39a434a72915d2b018bf2d63b3dfa2953ea7bac8a021d00a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d01a937f68f265cf75609c612e6653da44c85c830abdb665dfc690fc740", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021ca937f68f265cf75609c612e6653f7706c456502fb3ac0b71b05772c6", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021dff56c80970d9a308a9f639ed199ac172565af0bf923876cb48f34c62fd", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021dfe56c80970d9a308a9f639ed199ac25bb37a37cf542499a20396f038c0", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303e021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021d01a937f68f265cf75609c612e6653e8da9a50f406dc78934b70cb39d03", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "303d021d00bdeb8edbcb30885c65bcb58d6ea2d4fe740d29c4c205d6ac15845376021c56c80970d9a308a9f639ed199ac172565af0bf923876cb48f34c62fd", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020100021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022020101021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30220201ff021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff0000000000000000000000010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000001021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffffffff000000000000000000000001090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000001090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff0000000000000000000000020201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffffffff000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffffffff000000000000000000000002021d00ffffffffffffffffffffffffffffffff000000000000000000000002", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3024021d00ffffffffffffffffffffffffffffffff000000000000000000000002090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffffffff000000000000000000000002090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 225, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 226, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 227, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 228, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 229, "comment": "Signature encoding contains wrong types.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 230, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "34373731", "sig": "303d021c326bc06353f7f9c9f77b8f4b55464e8619944e7879402cca572e041a021d00bd6b1d7ab97ac1b607c22e042ffcc0062c744160c958ad0b1943a944", "result": "valid", "flags": []}, {"tcId": 231, "comment": "special case hash", "msg": "38363138363536383336", "sig": "303d021c476bab7a32e1f66958492deb681033dc135276f62d9265c7c7fddff4021d00bcce78ad8017bb499490eb1bf00dd9f35b23b5e8bd03fe5bb09e3f5f", "result": "valid", "flags": []}, {"tcId": 232, "comment": "special case hash", "msg": "34323136353939373634", "sig": "303e021d008355270aae4ef8dda33cdb3fad664dfb0124f6dcc0e79a9a7b6bb19f021d00ec8d3e43977e2692ec27c702a6f349d4536d00cc017b55f325227da7", "result": "valid", "flags": []}, {"tcId": 233, "comment": "special case hash", "msg": "33353035313435373534", "sig": "303c021c6222915ddf6e69eaefce3ebda56ac501428b3d69b7b94c0e9ccf0010021c5acbd1d130b50c08778175172a9d0d0e0e36b6a68c80af9aeae41b6f", "result": "valid", "flags": []}, {"tcId": 234, "comment": "special case hash", "msg": "34323833323334303630", "sig": "303c021c6b8e3edaf6aa9e6322e916ba1cd2bce6ce694ca8e8f9f999efe9cc07021c793b8d557b98e504bf05b2a57b1fd1eaffb38eda30db7c5e8a559c93", "result": "valid", "flags": []}, {"tcId": 235, "comment": "special case hash", "msg": "31343238353939333836", "sig": "303d021d00a8b78286f4b4ade20d7a8f7c1ce3c29d6616432eb99b34cf8a46d421021c66b05e86c8a7e41fecb51047a7b8d7c4a6baf806e9d360f0c6715c6a", "result": "valid", "flags": []}, {"tcId": 236, "comment": "special case hash", "msg": "31303532363634353530", "sig": "303c021c33529fcafcd107596f846563605f0d9c479f5ac9498e325e034fd001021c75e231e760bc10eb97901c2b8ccf908099ce7fc54472fcb419784d36", "result": "valid", "flags": []}, {"tcId": 237, "comment": "special case hash", "msg": "3534383039373837313931", "sig": "303e021d00d4514aa4da10577bf2974ff7f6e410e82f9267877b73631e0b336ecb021d00936e3ddc7846ceebb4f9e8c262d014f8ec5ae90cebed2359b49aa559", "result": "valid", "flags": []}, {"tcId": 238, "comment": "special case hash", "msg": "36323339373639313139", "sig": "303d021c65d35e97f9455bbc13c8ec28f8b8d13ab7327fe77c38b40f5b855c37021d00a21cad033d04659bd2539e7838e8377b5b11f14d0c016616775586f1", "result": "valid", "flags": []}, {"tcId": 239, "comment": "special case hash", "msg": "3835343839383334353535", "sig": "303e021d00d444ef96649d01d2a47a9dd6210b45fffec0ed1a4cb7438e8cccf048021d00a828341bea5c28b55097e77332dd7e303df789a2a67946de23dd3473", "result": "valid", "flags": []}, {"tcId": 240, "comment": "special case hash", "msg": "37333237333733363539", "sig": "303c021c09463baa1c7630494a9ed5d64fa2fac19ac452b3142f8bf19f585574021c3897d58b8aff942a074a583604b174ddeaf230d7cead58e74835d89d", "result": "valid", "flags": []}, {"tcId": 241, "comment": "special case hash", "msg": "35333335313137303032", "sig": "303c021c4c2ba44adbb44f6b0f57de21830870d5acfc68e03c8f35e1dda14cec021c6aff00cd6417ac43c1ea7e107fcfada404b88f4a79a0d12df96ab028", "result": "valid", "flags": []}, {"tcId": 242, "comment": "special case hash", "msg": "31363630313837373733", "sig": "303d021d00dbbc41eea7c5c204388b7941cd19acc7a2eb38b8e848845bcdb4244d021c7c4e411b930f26ffaa494d2522381ac86f38b37591d697d1229253cd", "result": "valid", "flags": []}, {"tcId": 243, "comment": "special case hash", "msg": "32373637393334333030", "sig": "303d021c595b6900f6eb4494366219c35b40397ffdb3141bffb2d8d216f97973021d00f53261795ddfb36ed4a83f783710f15a8f606cf9fb3f9ca1981f2605", "result": "valid", "flags": []}, {"tcId": 244, "comment": "special case hash", "msg": "34363632343338363339", "sig": "303d021c6a785a5bc1b857f7c10120d85f36d9d444a5bb6ed0991eac4a5a26a8021d00eefa7d6774ee5851dd7f1c45d204fff4387ee126acbd56452d342439", "result": "valid", "flags": []}, {"tcId": 245, "comment": "special case hash", "msg": "3131333132313436393936", "sig": "303c021c4e0f67b081cd6b87e3f4d792f1ccdd66e780d8028eaeb5c40047b615021c14c42ea50c712c3fb7a0e18fe06b23822e9063f15bf2759dfc70383e", "result": "valid", "flags": []}, {"tcId": 246, "comment": "special case hash", "msg": "39373533373339323833", "sig": "303d021d00b16e31a9a07bed7d2adab9bfbf9fb8a279f6387791d229e79ff435c7021c0cac45c70351a77cf2d0377601be4f7bbf5acddf0310f9e10b1c7022", "result": "valid", "flags": []}, {"tcId": 247, "comment": "special case hash", "msg": "32353031313933373134", "sig": "303e021d008753a04e4ce34ab8997df5f36934cd16368cb0f3e849890d74242acc021d00d035d2d78feab9ced6c25735b3740a2309d96cb5d57fea729a9639d1", "result": "valid", "flags": []}, {"tcId": 248, "comment": "special case hash", "msg": "3136303339353338363330", "sig": "303c021c11974f58c95f8c44522f8359ae5e942577b8fb575a2ba18ff383df71021c6c141327f9e405729c300f16b301de140c8df92c05637db952216e4c", "result": "valid", "flags": []}, {"tcId": 249, "comment": "special case hash", "msg": "35383739303634393638", "sig": "303d021d009d5d514b884b4d892e92e373663e394901e483eca8c9bcd780910c82021c7da1cd12c575744ff70cfb3513c5eabc0e3632cf2ce50ecf0f55c822", "result": "valid", "flags": []}, {"tcId": 250, "comment": "special case hash", "msg": "33303935393737323131", "sig": "303d021d00f68c1b642a35a1988f6c2fa77a5533a2f635abf02c6748f5a2b9d1de021c63ee52149fec97e52b2da4556fe28acf8f598636455f322cc9f47175", "result": "valid", "flags": []}, {"tcId": 251, "comment": "special case hash", "msg": "37353735303634353636", "sig": "303d021c48948b8dd7e62760af368fdc3c225afdbfb6b98a1125d8aeb62419df021d00bf889dd8eca1456c24d88abc16a5dc0217d3ac72b0e57935bb803550", "result": "valid", "flags": []}, {"tcId": 252, "comment": "special case hash", "msg": "333333333939363533", "sig": "303d021c0d002750cc80c0f5a6b2b1e6e08afafb4840cbdef6e32b726a4c1959021d00ee4095e31b594d159691777ff1f616989a65c4572b264215806f9268", "result": "valid", "flags": []}, {"tcId": 253, "comment": "special case hash", "msg": "31353236353532363534", "sig": "303d021c0977b6caba191d7cbe0b5319917f2748304e66577202335842e009cf021d00b0680b5d606ef9baa292f6bffdc84c11c59299854b4624539c2efc74", "result": "valid", "flags": []}, {"tcId": 254, "comment": "special case hash", "msg": "3938393131373931", "sig": "303c021c023a1d2a8e49deac352acb3a6a758070b5c8a4e75fcbaffd9f32e862021c5d2d511ed37cf7d023a5335c48fc2f63cf0733a1a786c49ae929ce5b", "result": "valid", "flags": []}, {"tcId": 255, "comment": "special case hash", "msg": "34393338333931383530", "sig": "303e021d00c7cbad38e1c76603254dc2e9fd69332d0ef8f1a5879edb5be1bb578b021d00dddcf1aa863f291c0a287fa1d0159dabfa7d98e646596e8ac41f1b66", "result": "valid", "flags": []}, {"tcId": 256, "comment": "special case hash", "msg": "333333343935333833", "sig": "303c021c5c845a275521649bde9e3bfa07d6ca528f6e143d19e97b1e9e305e71021c60b4bd522c44b4c32e87b11b6b80b2061da98b4cf5c56dbf5f0651dc", "result": "valid", "flags": []}, {"tcId": 257, "comment": "special case hash", "msg": "31353435373932343633", "sig": "303c021c260418182c819de5bdb8851c5ac937ab8d83ab70640010f7eeae13f4021c034c2f5ffaa2b4f1f111f4758e5adfbab5b7cfecfa48c8d88f5b6816", "result": "valid", "flags": []}, {"tcId": 258, "comment": "special case hash", "msg": "33313331343036343430", "sig": "303c021c0346d6521c74bfb34342c4b03c067d3cdfe35d3ea121580668301431021c571dc84cc071e25b98d47c87edd3f6db73f995e5a4fa038760c43cc3", "result": "valid", "flags": []}, {"tcId": 259, "comment": "special case hash", "msg": "36333034343731323831", "sig": "303d021d00f6d5049e2f0377f24fd3edd8ec14947251c9d687a4ec104f36b9238f021c713a07b0dd9aa2b08c30d9167b0373b852595579a7dfc48199056178", "result": "valid", "flags": []}, {"tcId": 260, "comment": "special case hash", "msg": "38393037303230373134", "sig": "303e021d00a7994553f1a793ebce2dfdcd357d3e4a01d0f7c1caaa9099fbb4b07a021d00d8f771084d362ef2a0dee50496e450b6e812e40c2e6d342495571508", "result": "valid", "flags": []}, {"tcId": 261, "comment": "special case hash", "msg": "36393536373534323535", "sig": "303c021c43236a22ba681bd71f99a8ee2b425b784ba6ff55cae154bf1b8ef454021c09cffb77306a5ea7675578bcfa2d2142c9dbd84401e09f78ec29fa74", "result": "valid", "flags": []}, {"tcId": 262, "comment": "special case hash", "msg": "34353533303132313836", "sig": "303d021d008c63dae1f19d97135df18f8cf1a09e528b11ed2eac9f4b340621c73d021c3095be2232fe57d372796cc0c846445836ff35f25a38ce13585858b2", "result": "valid", "flags": []}, {"tcId": 263, "comment": "special case hash", "msg": "39373837393632323939", "sig": "303e021d008005bcb6b955f80b2a2b6e45d86154c561b543083ab065b50bffb499021d00e6515a9eb3fd8138ca117515d0f9a6549f226a72cccd49741adffec1", "result": "valid", "flags": []}, {"tcId": 264, "comment": "special case hash", "msg": "34353433363234313238", "sig": "303d021d00be66cc8eb0d9e5dd7ef6c754018502e7371c0b0db97cce378b9aa355021c562b771c5104385878c3b918379b101ac888b1f15f0f52d15e0a92ba", "result": "valid", "flags": []}, {"tcId": 265, "comment": "special case hash", "msg": "3231313831373330313339", "sig": "303e021d00f42940ed7287b281f00c795abd671feffded542fc63c4ecfc7336427021d009049e62c464723f50c265fdbcf7c6794b5294a58b3100e82ad9cd724", "result": "valid", "flags": []}, {"tcId": 266, "comment": "special case hash", "msg": "34303439363439373434", "sig": "303d021d00d0b092867808c7d3b34fec07c4ebe8324a1a4ff2bfc2e20aaffbd248021c4eb922b3de3bd244938adb20e21cdd560030d13cc0191c37ccd38e28", "result": "valid", "flags": []}, {"tcId": 267, "comment": "special case hash", "msg": "36323533333234353034", "sig": "303e021d00a73472e1f303bfe50128770c9134bd93af9b064f9b782a45dd85c33c021d00da4b8bd6e2ba5c635dcaf9a7d2bf774b10c2b09ba074cdc58d9c8da2", "result": "valid", "flags": []}, {"tcId": 268, "comment": "special case hash", "msg": "33373935323533363938", "sig": "303d021d009832ae82080ec02833e3eae913f7a98eb9e2d05133e5f2c7fa20479d021c0e90c676df7737adb84e54cda47af9f5d6ab2f34eaec837b628f646e", "result": "valid", "flags": []}, {"tcId": 269, "comment": "special case hash", "msg": "3833343930343234303533", "sig": "303d021c3eac05a747f43988dab987f487fbca8003e5c7e9bb580634afef6e03021d00dd656679e1c1800f8a258781f45489e6630a6d934b3e2a05d41a0c4f", "result": "valid", "flags": []}, {"tcId": 270, "comment": "special case hash", "msg": "343736373036363938", "sig": "303d021c5f3a639c163daaf3e2c0d7d1a3ca82ebe42951491114b6e257e28f69021d00ea0e12e23d485a932f1aee974761b0e0c9c3d5ba1822ab646819886a", "result": "valid", "flags": []}, {"tcId": 271, "comment": "special case hash", "msg": "393238353633373633", "sig": "303d021d00c0ad06ec18c4ed4469c5f4f4be4bfb41b6fc024456b2eed1d8096a75021c45e08a93ae4e33bdf35379161843266ecd2f200ce292ac99894748ef", "result": "valid", "flags": []}, {"tcId": 272, "comment": "special case hash", "msg": "31343936333331313539", "sig": "303d021c0f5a1bb9c572a6ddfe5072de6a077b1490096a88cb2be9af8d976483021d0092e7258459df848ffbcc7fbfa99fdf4db16b734dc5b9701427034f80", "result": "valid", "flags": []}, {"tcId": 273, "comment": "special case hash", "msg": "37323437303933303734", "sig": "303e021d00a097b8422452dcb2a93ae32ff8d6befc31d76051704b1023ac5c7645021d00f4faaf5f0dc78023885823c5547a59320bed2eb2eb3b948d07ce49a9", "result": "valid", "flags": []}, {"tcId": 274, "comment": "special case hash", "msg": "31373634353939333930", "sig": "303d021c7b4d49838bffa979d0d9772f8af39340023a6b11a0e2173ce92d1b8e021d00f878af3f516288abf324b0c52dbc2d7106d2dc397871374bd144c272", "result": "valid", "flags": []}, {"tcId": 275, "comment": "special case hash", "msg": "35393833373634333139", "sig": "303d021d00feda6c1c40c483f87f4586d23381a71a6b051ce28916f199295eaed3021c75be57d1b46ff0364308db2725b19bcc1ce820cf57e37f825d30e199", "result": "valid", "flags": []}, {"tcId": 276, "comment": "special case hash", "msg": "31393236363739363031", "sig": "303d021d009969b8d03ed1313ecfd1739e4c9234c0e7287f15839ea1e6feed50b1021c45f6a820ac45d835ecdd52c836f157ab2b9279a560d2fe966ee23a4e", "result": "valid", "flags": []}, {"tcId": 277, "comment": "special case hash", "msg": "3333353839393638", "sig": "303e021d00a62e952192d3a9dbfc2ab20c57c719bf52eef859d994c860a9564f12021d00be1007b52e9308bde867f380f0bd3002554d8ac854f9db3d4a7d6898", "result": "valid", "flags": []}, {"tcId": 278, "comment": "special case hash", "msg": "33313731393734323238", "sig": "303d021d00f526b9288e25aaea8d657ec7a4ebde46d8adc4c6d909fcfd7e2dcdb0021c5d10087d1263db5259579e9987b410001b774f81c3e489df85b2715e", "result": "valid", "flags": []}, {"tcId": 279, "comment": "special case hash", "msg": "32353736383731313033", "sig": "303e021d00c5ff7ab638b6702535ea719e7c2f7753c53c1611c5919868fb708b00021d00ab29aac043d35847280a3d77590ae93a4b26db18238a53c67cbd162b", "result": "valid", "flags": []}, {"tcId": 280, "comment": "special case hash", "msg": "36303432353831383130", "sig": "303d021d00e69b43a68f452d26516e1316d54cd51416a047d42945350cdc506518021c66d1abd5aa58eccf9ebe9391cce8cefab162a9130439235f051fc437", "result": "valid", "flags": []}, {"tcId": 281, "comment": "special case hash", "msg": "38363531303032303530", "sig": "303c021c0c6822e256d0190b4962c5b4bfdabce10d277cd347caf0850892288f021c3f59727e4d9e3b92f4b0ce710c5112caa18e4051cc71450f6989cb9a", "result": "valid", "flags": []}, {"tcId": 282, "comment": "special case hash", "msg": "343037313238303135", "sig": "303d021d00850b43461e107174a77a4f9a3011e03854d6b5b4a0a6250e2fc472a2021c0e7da9a21a2373d0944031fd121dce0594aa8721da1269f846747f59", "result": "valid", "flags": []}, {"tcId": 283, "comment": "special case hash", "msg": "34383833313734353738", "sig": "303d021d00a6e142069366be8c48a7b651ec45b0e2dd9d79701108c35997ca1a4b021c20b576040b9f3e73ae7ff3af223e34df82dce8533bf1901486a8b3d2", "result": "valid", "flags": []}, {"tcId": 284, "comment": "special case hash", "msg": "34333631333834353035", "sig": "303d021c58e4c5558f2d4d2baee361da0e907e068bbc697b3abdbae29474084e021d00951de902c7af71b5d7a3c6117d258242a04a8661bfdd4d047694f7fa", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d37fb4abe8c504504f010539eb764c10c14250645e846eaf41b99953c4e2c1c277056982c5b81305ed3110a064ff6ae8e0545f0c35ff8871", "wx": "00d37fb4abe8c504504f010539eb764c10c14250645e846eaf41b99953", "wy": "00c4e2c1c277056982c5b81305ed3110a064ff6ae8e0545f0c35ff8871"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d37fb4abe8c504504f010539eb764c10c14250645e846eaf41b99953c4e2c1c277056982c5b81305ed3110a064ff6ae8e0545f0c35ff8871", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE03+0q+jFBFBPAQU563ZMEMFCUGRehG6v\nQbmZU8TiwcJ3BWmCxbgTBe0xEKBk/2ro4FRfDDX/iHE=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 285, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "3030020f00e95c1f470fc1ec22d6baa3a3d5c1021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a", "result": "valid", "flags": []}, {"tcId": 286, "comment": "r too large", "msg": "313233343030", "sig": "303e021d00fffffffffffffffffffffffffffffffefffffffffffffffffffffffe021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3a", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d9aac11d0277a4b23514c4d02a483e922dc40c92a774b8c62179690b9cfe0c9b060b1a49598318631668083e4cf34e4bab29d14d81c2b049", "wx": "00d9aac11d0277a4b23514c4d02a483e922dc40c92a774b8c62179690b", "wy": "009cfe0c9b060b1a49598318631668083e4cf34e4bab29d14d81c2b049"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d9aac11d0277a4b23514c4d02a483e922dc40c92a774b8c62179690b9cfe0c9b060b1a49598318631668083e4cf34e4bab29d14d81c2b049", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE2arBHQJ3pLI1FMTQKkg+ki3EDJKndLjG\nIXlpC5z+DJsGCxpJWYMYYxZoCD5M805LqynRTYHCsEk=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 287, "comment": "r,s are large", "msg": "313233343030", "sig": "303e021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3c021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a3b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c359b31b3ee10cc0bab7d21f0cc5cecb632186e8ca608a74f921986f27787cc204c5ed561897c14961f7827b5f97395996de6cff87862771", "wx": "00c359b31b3ee10cc0bab7d21f0cc5cecb632186e8ca608a74f921986f", "wy": "27787cc204c5ed561897c14961f7827b5f97395996de6cff87862771"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004c359b31b3ee10cc0bab7d21f0cc5cecb632186e8ca608a74f921986f27787cc204c5ed561897c14961f7827b5f97395996de6cff87862771", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEw1mzGz7hDMC6t9IfDMXOy2MhhujKYIp0\n+SGYbyd4fMIExe1WGJfBSWH3gntflzlZlt5s/4eGJ3E=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 288, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021c3d5052691b8dc89debad360466f2a39e82e8ae2aefb77c3c92ad7cd1", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b21fde4e399d8cbf8cbb8ea8ac770eb97ff85b018683433982ca2b353e7b4325b4319bbd71fe9c3e49c4daec895501afceaa554040129b71", "wx": "00b21fde4e399d8cbf8cbb8ea8ac770eb97ff85b018683433982ca2b35", "wy": "3e7b4325b4319bbd71fe9c3e49c4daec895501afceaa554040129b71"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b21fde4e399d8cbf8cbb8ea8ac770eb97ff85b018683433982ca2b353e7b4325b4319bbd71fe9c3e49c4daec895501afceaa554040129b71", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsh/eTjmdjL+Mu46orHcOuX/4WwGGg0M5\ngsorNT57QyW0MZu9cf6cPknE2uyJVQGvzqpVQEASm3E=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 289, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "303d021c7fffffffffffffffffffffffffffffffffffffffffffffffffffffff021d00bf19ab4d3ebf5a1a49d765909308daa88c2b7be3969db552ea30562b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04fc0341bdbbce3beee1be9f02e46148af9da53128e0e3c45af1abe4c792acfd718352e7107fe08ea6a35d8badcf54f57065dc4e8c9f2705d2", "wx": "00fc0341bdbbce3beee1be9f02e46148af9da53128e0e3c45af1abe4c7", "wy": "0092acfd718352e7107fe08ea6a35d8badcf54f57065dc4e8c9f2705d2"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004fc0341bdbbce3beee1be9f02e46148af9da53128e0e3c45af1abe4c792acfd718352e7107fe08ea6a35d8badcf54f57065dc4e8c9f2705d2", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE/ANBvbvOO+7hvp8C5GFIr52lMSjg48Ra\n8avkx5Ks/XGDUucQf+COpqNdi63PVPVwZdxOjJ8nBdI=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 290, "comment": "small r and s", "msg": "313233343030", "sig": "3006020103020101", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0450b14256f6ea50d9843bd9e2b4c2d9daf75f76ac4e4e757c712b3053594d68e1683ec977b2efcc8a7ba6c46a0e6a668a03f4f50a3e21e4ce", "wx": "50b14256f6ea50d9843bd9e2b4c2d9daf75f76ac4e4e757c712b3053", "wy": "594d68e1683ec977b2efcc8a7ba6c46a0e6a668a03f4f50a3e21e4ce"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000450b14256f6ea50d9843bd9e2b4c2d9daf75f76ac4e4e757c712b3053594d68e1683ec977b2efcc8a7ba6c46a0e6a668a03f4f50a3e21e4ce", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEULFCVvbqUNmEO9nitMLZ2vdfdqxOTnV8\ncSswU1lNaOFoPsl3su/MinumxGoOamaKA/T1Cj4h5M4=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 291, "comment": "small r and s", "msg": "313233343030", "sig": "3006020103020103", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "047801e48011fce2685a2f563faab34fff728ebb6e92eb029fef124eb5a9be2c1b86e99e44ef60e6c02a04a16cbd968482ed2ec4c1463efeef", "wx": "7801e48011fce2685a2f563faab34fff728ebb6e92eb029fef124eb5", "wy": "00a9be2c1b86e99e44ef60e6c02a04a16cbd968482ed2ec4c1463efeef"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00047801e48011fce2685a2f563faab34fff728ebb6e92eb029fef124eb5a9be2c1b86e99e44ef60e6c02a04a16cbd968482ed2ec4c1463efeef", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEeAHkgBH84mhaL1Y/qrNP/3KOu26S6wKf\n7xJOtam+LBuG6Z5E72DmwCoEoWy9loSC7S7EwUY+/u8=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 292, "comment": "small r and s", "msg": "313233343030", "sig": "3006020103020104", "result": "valid", "flags": []}, {"tcId": 293, "comment": "r is larger than n", "msg": "313233343030", "sig": "3022021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c2a40020104", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "046cce004abbcdccdb3fda691e70a71a4d8a920219af2a20880f59c53d86023ea85caa2bebffcb9f360082e6264466ea065afb07820dfb1a9a", "wx": "6cce004abbcdccdb3fda691e70a71a4d8a920219af2a20880f59c53d", "wy": "0086023ea85caa2bebffcb9f360082e6264466ea065afb07820dfb1a9a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00046cce004abbcdccdb3fda691e70a71a4d8a920219af2a20880f59c53d86023ea85caa2bebffcb9f360082e6264466ea065afb07820dfb1a9a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEbM4ASrvNzNs/2mkecKcaTYqSAhmvKiCI\nD1nFPYYCPqhcqivr/8ufNgCC5iZEZuoGWvsHgg37Gpo=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 294, "comment": "s is larger than n", "msg": "313233343030", "sig": "3022020103021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c6f00c4", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "045d1b27dd47711d7fa90b2651e202c240cad281ed803e1a3236c789fa0ea5420664e2a8bd9cea3740218e23735ee2715f8130beb437419539", "wx": "5d1b27dd47711d7fa90b2651e202c240cad281ed803e1a3236c789fa", "wy": "0ea5420664e2a8bd9cea3740218e23735ee2715f8130beb437419539"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00045d1b27dd47711d7fa90b2651e202c240cad281ed803e1a3236c789fa0ea5420664e2a8bd9cea3740218e23735ee2715f8130beb437419539", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEXRsn3UdxHX+pCyZR4gLCQMrSge2APhoy\nNseJ+g6lQgZk4qi9nOo3QCGOI3Ne4nFfgTC+tDdBlTk=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 295, "comment": "small r and s^-1", "msg": "313233343030", "sig": "302302020100021d00c993264c993264c993264c99326411d2e55b3214a8d67528812a55ab", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ddf53cec8d9c6062904d2a04f790f4596c67696dd4f5422a3cb84c9caf10f2d1eb0e0ff28fa8e40a91d8d4addb20c085d635158de1a67bdd", "wx": "00ddf53cec8d9c6062904d2a04f790f4596c67696dd4f5422a3cb84c9c", "wy": "00af10f2d1eb0e0ff28fa8e40a91d8d4addb20c085d635158de1a67bdd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ddf53cec8d9c6062904d2a04f790f4596c67696dd4f5422a3cb84c9caf10f2d1eb0e0ff28fa8e40a91d8d4addb20c085d635158de1a67bdd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE3fU87I2cYGKQTSoE95D0WWxnaW3U9UIq\nPLhMnK8Q8tHrDg/yj6jkCpHY1K3bIMCF1jUVjeGme90=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 296, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "302702072d9b4d347952cc021c3e85d56474b5c55fbe86608442a84b2bf093b7d75f53a47250e1c70c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04f43b4a87dc12c65bf27f4b8610486402327adc0133c1db8adf4e3f9ba61aadb4c58ac0b5518d1c2929068eaa0d6a5d5f84dacf66e5b276ff", "wx": "00f43b4a87dc12c65bf27f4b8610486402327adc0133c1db8adf4e3f9b", "wy": "00a61aadb4c58ac0b5518d1c2929068eaa0d6a5d5f84dacf66e5b276ff"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004f43b4a87dc12c65bf27f4b8610486402327adc0133c1db8adf4e3f9ba61aadb4c58ac0b5518d1c2929068eaa0d6a5d5f84dacf66e5b276ff", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE9DtKh9wSxlvyf0uGEEhkAjJ63AEzwduK\n304/m6YarbTFisC1UY0cKSkGjqoNal1fhNrPZuWydv8=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 297, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "302d020d1033e67e37b32b445580bf4efb021c02fd02fd02fd02fd02fd02fd02fd0043a4fd2da317247308c74dc6b8", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "046bd0a5dc98a6761a24d4e5e6c31187af8c7ed874d42af841806583b6022e6bf9480c23d1be341f59b043afdaa76bad8622204fa84e26dd3e", "wx": "6bd0a5dc98a6761a24d4e5e6c31187af8c7ed874d42af841806583b6", "wy": "022e6bf9480c23d1be341f59b043afdaa76bad8622204fa84e26dd3e"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00046bd0a5dc98a6761a24d4e5e6c31187af8c7ed874d42af841806583b6022e6bf9480c23d1be341f59b043afdaa76bad8622204fa84e26dd3e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEa9Cl3Jimdhok1OXmwxGHr4x+2HTUKvhB\ngGWDtgIua/lIDCPRvjQfWbBDr9qna62GIiBPqE4m3T4=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 298, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "302302020100021d00d05434abacd859ed74185e75b751c6d9f60c7921dacfbb8e19cdba8e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0475b65cd61449faf0d4bb2d2300b134757b714fbc4efbd6631e664cbfb488633f42e50b11c301bf3736a461286eccad2447180835d508deb2", "wx": "75b65cd61449faf0d4bb2d2300b134757b714fbc4efbd6631e664cbf", "wy": "00b488633f42e50b11c301bf3736a461286eccad2447180835d508deb2"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000475b65cd61449faf0d4bb2d2300b134757b714fbc4efbd6631e664cbfb488633f42e50b11c301bf3736a461286eccad2447180835d508deb2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEdbZc1hRJ+vDUuy0jALE0dXtxT7xO+9Zj\nHmZMv7SIYz9C5QsRwwG/NzakYShuzK0kRxgINdUI3rI=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 299, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "302e020d062522bbd3ecbe7c39e93e7c24021d00d05434abacd859ed74185e75b751c6d9f60c7921dacfbb8e19cdba8e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04bd18c7797449c64e9fc1ad2ca9c49132fc34b4741831fdbc6cbd87cff830c108fd501bf9b7b3b898072397b9a6e72216db784c877882c87b", "wx": "00bd18c7797449c64e9fc1ad2ca9c49132fc34b4741831fdbc6cbd87cf", "wy": "00f830c108fd501bf9b7b3b898072397b9a6e72216db784c877882c87b"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004bd18c7797449c64e9fc1ad2ca9c49132fc34b4741831fdbc6cbd87cff830c108fd501bf9b7b3b898072397b9a6e72216db784c877882c87b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEvRjHeXRJxk6fwa0sqcSRMvw0tHQYMf28\nbL2Hz/gwwQj9UBv5t7O4mAcjl7mm5yIW23hMh3iCyHs=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 300, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "303d021d00ffffffffffffffffffffffffffff16a2e0b8f03e13dd29455c5c29bd021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04af736667b618cfa5526f073f7048d5e6b672a05569cd2912bce8914d6a030aa73fd79517ee8175800484f2dcebf02871825cc67c41b1a8fc", "wx": "00af736667b618cfa5526f073f7048d5e6b672a05569cd2912bce8914d", "wy": "6a030aa73fd79517ee8175800484f2dcebf02871825cc67c41b1a8fc"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004af736667b618cfa5526f073f7048d5e6b672a05569cd2912bce8914d6a030aa73fd79517ee8175800484f2dcebf02871825cc67c41b1a8fc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEr3NmZ7YYz6VSbwc/cEjV5rZyoFVpzSkS\nvOiRTWoDCqc/15UX7oF1gASE8tzr8ChxglzGfEGxqPw=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 301, "comment": "s == 1", "msg": "313233343030", "sig": "3021021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14020101", "result": "valid", "flags": []}, {"tcId": 302, "comment": "s == 0", "msg": "313233343030", "sig": "3021021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14020100", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04aa2f981add5480e7f2a8ae50fc52258612ad6420a1a2cc2c252c169332c1ff19c331d3e52a98add7e7f4f8ac122ca961b8cbe4260ed83e4c", "wx": "00aa2f981add5480e7f2a8ae50fc52258612ad6420a1a2cc2c252c1693", "wy": "32c1ff19c331d3e52a98add7e7f4f8ac122ca961b8cbe4260ed83e4c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004aa2f981add5480e7f2a8ae50fc52258612ad6420a1a2cc2c252c169332c1ff19c331d3e52a98add7e7f4f8ac122ca961b8cbe4260ed83e4c", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEqi+YGt1UgOfyqK5Q/FIlhhKtZCChosws\nJSwWkzLB/xnDMdPlKpit1+f0+KwSLKlhuMvkJg7YPkw=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 303, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151e021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048feb4b153b7dfe4081069ec708fdb161716ec3ed17c81efb1bb3e396bbc90cfae2c3957f2cec75239445239a1c0e9e0a032385d063f1d2ff", "wx": "008feb4b153b7dfe4081069ec708fdb161716ec3ed17c81efb1bb3e396", "wy": "00bbc90cfae2c3957f2cec75239445239a1c0e9e0a032385d063f1d2ff"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048feb4b153b7dfe4081069ec708fdb161716ec3ed17c81efb1bb3e396bbc90cfae2c3957f2cec75239445239a1c0e9e0a032385d063f1d2ff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEj+tLFTt9/kCBBp7HCP2xYXFuw+0XyB77\nG7PjlrvJDPriw5V/LOx1I5RFI5ocDp4KAyOF0GPx0v8=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 304, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04af2a29d356133f4d726c64e8ff7d80851649cf3e35d2b9de2725bbab6d2199d9f3e0f0863e671deb987afdb25b6e6b7744bc53faa15cac53", "wx": "00af2a29d356133f4d726c64e8ff7d80851649cf3e35d2b9de2725bbab", "wy": "6d2199d9f3e0f0863e671deb987afdb25b6e6b7744bc53faa15cac53"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004af2a29d356133f4d726c64e8ff7d80851649cf3e35d2b9de2725bbab6d2199d9f3e0f0863e671deb987afdb25b6e6b7744bc53faa15cac53", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEryop01YTP01ybGTo/32AhRZJzz410rne\nJyW7q20hmdnz4PCGPmcd65h6/bJbbmt3RLxT+qFcrFM=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 305, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "303c021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f021c7fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e151f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "047bb0bc9529b06a424e8efbaafdec5aa339de5599f82ec9e195f0cede381dc950caa8b0454fab70c57e06a15bc771b693ebb4013bc85b56ac", "wx": "7bb0bc9529b06a424e8efbaafdec5aa339de5599f82ec9e195f0cede", "wy": "381dc950caa8b0454fab70c57e06a15bc771b693ebb4013bc85b56ac"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00047bb0bc9529b06a424e8efbaafdec5aa339de5599f82ec9e195f0cede381dc950caa8b0454fab70c57e06a15bc771b693ebb4013bc85b56ac", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEe7C8lSmwakJOjvuq/exaozneVZn4Lsnh\nlfDO3jgdyVDKqLBFT6twxX4GoVvHcbaT67QBO8hbVqw=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 306, "comment": "u1 == 1", "msg": "313233343030", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c0e77efebdac83c01dce3f8c4162e286b38b7e23de83637a72531eab7", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "042da3ea3145a1e68772e139f1b5d9b85e201de7df4775d5c4f77825964d3a2380099d7f3cf3ad18c1fb13ab1e054c097633fd51e67c1a9ca0", "wx": "2da3ea3145a1e68772e139f1b5d9b85e201de7df4775d5c4f7782596", "wy": "4d3a2380099d7f3cf3ad18c1fb13ab1e054c097633fd51e67c1a9ca0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00042da3ea3145a1e68772e139f1b5d9b85e201de7df4775d5c4f77825964d3a2380099d7f3cf3ad18c1fb13ab1e054c097633fd51e67c1a9ca0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAELaPqMUWh5ody4Tnxtdm4XiAd599HddXE\n93gllk06I4AJnX88860YwfsTqx4FTAl2M/1R5nwanKA=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 307, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "303d021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021d00f18810142537c3fe231c073be9d0ee37a8010e002ba6f19e372a3f86", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ffd40a19c09ce0a21124f163c72558e1f15a11aecde9dde08c465bcee3cc54426c7850ae17670e1cc19931e9d934610f42f456b8472a8047", "wx": "00ffd40a19c09ce0a21124f163c72558e1f15a11aecde9dde08c465bce", "wy": "00e3cc54426c7850ae17670e1cc19931e9d934610f42f456b8472a8047"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ffd40a19c09ce0a21124f163c72558e1f15a11aecde9dde08c465bcee3cc54426c7850ae17670e1cc19931e9d934610f42f456b8472a8047", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE/9QKGcCc4KIRJPFjxyVY4fFaEa7N6d3g\njEZbzuPMVEJseFCuF2cOHMGZMenZNGEPQvRWuEcqgEc=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 308, "comment": "u2 == 1", "msg": "313233343030", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0406081ee7d06ae8d4b84075e53264125b0efa082dc6e1d9190e9cd8f6361012db570e279336fbc8f748d7d1c77967cc0ae188aedf8cb4d0f6", "wx": "06081ee7d06ae8d4b84075e53264125b0efa082dc6e1d9190e9cd8f6", "wy": "361012db570e279336fbc8f748d7d1c77967cc0ae188aedf8cb4d0f6"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000406081ee7d06ae8d4b84075e53264125b0efa082dc6e1d9190e9cd8f6361012db570e279336fbc8f748d7d1c77967cc0ae188aedf8cb4d0f6", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEBgge59Bq6NS4QHXlMmQSWw76CC3G4dkZ\nDpzY9jYQEttXDieTNvvI90jX0cd5Z8wK4Yiu34y00PY=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 309, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "303d021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021d00aaaaaaaaaaaaaaaaaaaaaaaaaaaa0f17407b4ad40d3e1b8392e81c29", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04f480a474e28f987e1e76e73c7a9c5c12307f5bdc99d97e515e71ae420e310ab3403eb44f8f17e217914d136c8e2341f71177052d4f07dcb3", "wx": "00f480a474e28f987e1e76e73c7a9c5c12307f5bdc99d97e515e71ae42", "wy": "0e310ab3403eb44f8f17e217914d136c8e2341f71177052d4f07dcb3"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004f480a474e28f987e1e76e73c7a9c5c12307f5bdc99d97e515e71ae420e310ab3403eb44f8f17e217914d136c8e2341f71177052d4f07dcb3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE9ICkdOKPmH4educ8epxcEjB/W9yZ2X5R\nXnGuQg4xCrNAPrRPjxfiF5FNE2yOI0H3EXcFLU8H3LM=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 310, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c04d2a54e9e42beab49a152ec0764b823bd92a0bf4d6767e261bb4e3d", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044655a8b7d46613d3d8ace597a9d381b7d2e30c57aad490e4134811054aca5463f0377db9c9638d280129cf14f5e60c8ebcef4c8ebdc9b15d", "wx": "4655a8b7d46613d3d8ace597a9d381b7d2e30c57aad490e413481105", "wy": "4aca5463f0377db9c9638d280129cf14f5e60c8ebcef4c8ebdc9b15d"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044655a8b7d46613d3d8ace597a9d381b7d2e30c57aad490e4134811054aca5463f0377db9c9638d280129cf14f5e60c8ebcef4c8ebdc9b15d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAERlWot9RmE9PYrOWXqdOBt9LjDFeq1JDk\nE0gRBUrKVGPwN325yWONKAEpzxT15gyOvO9Mjr3JsV0=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 311, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00f9f9e7f101b4ea468eb78fd9ba89156454b414fc17d02840ca81ca78", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "046c3c1069fb76da43e5a9ae1a69fb679740171f2b457956b13f5829c04f0b1000d41a56d96eca18a626d0636f20cee184f3d2f5b87ab68c4a", "wx": "6c3c1069fb76da43e5a9ae1a69fb679740171f2b457956b13f5829c0", "wy": "4f0b1000d41a56d96eca18a626d0636f20cee184f3d2f5b87ab68c4a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00046c3c1069fb76da43e5a9ae1a69fb679740171f2b457956b13f5829c04f0b1000d41a56d96eca18a626d0636f20cee184f3d2f5b87ab68c4a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEbDwQaft22kPlqa4aaftnl0AXHytFeVax\nP1gpwE8LEADUGlbZbsoYpibQY28gzuGE89L1uHq2jEo=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 312, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00be7ed7384bc8b90113e58ca87a68a06fc2fe5efa96f0a956733dc65c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "049a24375b03b78c20230867b842c680bdb88604fa93f7c5931734831060090ff5dec7b6fb6df459befdfc5e9d440198e8610a267daa9548fa", "wx": "009a24375b03b78c20230867b842c680bdb88604fa93f7c59317348310", "wy": "60090ff5dec7b6fb6df459befdfc5e9d440198e8610a267daa9548fa"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00049a24375b03b78c20230867b842c680bdb88604fa93f7c5931734831060090ff5dec7b6fb6df459befdfc5e9d440198e8610a267daa9548fa", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEmiQ3WwO3jCAjCGe4QsaAvbiGBPqT98WT\nFzSDEGAJD/Xex7b7bfRZvv38Xp1EAZjoYQomfaqVSPo=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 313, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c79823515d525dc02f18810142537553a6da56048fb999d3fdff85f70", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0471f01e6070a5bd694092417f75b1f1b35457421e9997fa5086dfef4b2b8f67510ac820380907503b3bcdb89fdb5f2688434dba79d3a40a11", "wx": "71f01e6070a5bd694092417f75b1f1b35457421e9997fa5086dfef4b", "wy": "2b8f67510ac820380907503b3bcdb89fdb5f2688434dba79d3a40a11"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000471f01e6070a5bd694092417f75b1f1b35457421e9997fa5086dfef4b2b8f67510ac820380907503b3bcdb89fdb5f2688434dba79d3a40a11", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEcfAeYHClvWlAkkF/dbHxs1RXQh6Zl/pQ\nht/vSyuPZ1EKyCA4CQdQOzvNuJ/bXyaIQ026edOkChE=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 314, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c23515d525dc02f18810142537c3fc1ffd9a43852c262974a2a1640c8", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04453355214278474735b32b1d45c9a203421578c10acd426e9a569d5f6b5655138346d0bef9cde0ebb97b4938e3c28dc612b4eaaba862182d", "wx": "453355214278474735b32b1d45c9a203421578c10acd426e9a569d5f", "wy": "6b5655138346d0bef9cde0ebb97b4938e3c28dc612b4eaaba862182d"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004453355214278474735b32b1d45c9a203421578c10acd426e9a569d5f6b5655138346d0bef9cde0ebb97b4938e3c28dc612b4eaaba862182d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAERTNVIUJ4R0c1sysdRcmiA0IVeMEKzUJu\nmladX2tWVRODRtC++c3g67l7STjjwo3GErTqq6hiGC0=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 315, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c46a2baa4bb805e31020284a6f87f83ffb34870a584c52e94542c8190", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04fa86cdd0976acf06b0a7a3dcae70f287e62950d8874b32abcd59f755bd00817cea3c6b5e8d3266bef1f3df944fc4953e7a960902901ff380", "wx": "00fa86cdd0976acf06b0a7a3dcae70f287e62950d8874b32abcd59f755", "wy": "00bd00817cea3c6b5e8d3266bef1f3df944fc4953e7a960902901ff380"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004fa86cdd0976acf06b0a7a3dcae70f287e62950d8874b32abcd59f755bd00817cea3c6b5e8d3266bef1f3df944fc4953e7a960902901ff380", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE+obN0Jdqzwawp6PcrnDyh+YpUNiHSzKr\nzVn3Vb0AgXzqPGtejTJmvvHz35RPxJU+epYJApAf84A=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 316, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00d525dc02f18810142537c3fe231b44eeebc76f3ab76829dc0fb7e7ff", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "046fc6ce348cb17cf57fe18e21ed13e8f33e5a724bf87f151ea7579633bdd1fb53ba4ec9a477a6f3e5193003aaf462c857bc4a20bb62446552", "wx": "6fc6ce348cb17cf57fe18e21ed13e8f33e5a724bf87f151ea7579633", "wy": "00bdd1fb53ba4ec9a477a6f3e5193003aaf462c857bc4a20bb62446552"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00046fc6ce348cb17cf57fe18e21ed13e8f33e5a724bf87f151ea7579633bdd1fb53ba4ec9a477a6f3e5193003aaf462c857bc4a20bb62446552", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEb8bONIyxfPV/4Y4h7RPo8z5ackv4fxUe\np1eWM73R+1O6Tsmkd6bz5RkwA6r0YshXvEogu2JEZVI=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 317, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7d96ad58b0dea0aa5b2f5689fc4d2f3f919327bf633ae0b17d506e00", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0498f2f5da454958c7b04ef2220d45ed857a157d3874d033a25af6db8790c711d7574128fb7de7f316f7896b898670c97798d05a97f9eab7b0", "wx": "0098f2f5da454958c7b04ef2220d45ed857a157d3874d033a25af6db87", "wy": "0090c711d7574128fb7de7f316f7896b898670c97798d05a97f9eab7b0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000498f2f5da454958c7b04ef2220d45ed857a157d3874d033a25af6db8790c711d7574128fb7de7f316f7896b898670c97798d05a97f9eab7b0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEmPL12kVJWMewTvIiDUXthXoVfTh00DOi\nWvbbh5DHEddXQSj7fefzFveJa4mGcMl3mNBal/nqt7A=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 318, "comment": "edge case for u1", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00fb2d5ab161bd4154b65ead13f89a5e7f23264f7ec675c162faa0dc00", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043ccb08210d0b2283f3ccb779079bb160cee3cec9263d356565f770b39fb0edb83cae0b730fddd5c0d63e10a99e527497a58c18b84dae8e8e", "wx": "3ccb08210d0b2283f3ccb779079bb160cee3cec9263d356565f770b3", "wy": "009fb0edb83cae0b730fddd5c0d63e10a99e527497a58c18b84dae8e8e"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00043ccb08210d0b2283f3ccb779079bb160cee3cec9263d356565f770b39fb0edb83cae0b730fddd5c0d63e10a99e527497a58c18b84dae8e8e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEPMsIIQ0LIoPzzLd5B5uxYM7jzskmPTVl\nZfdws5+w7bg8rgtzD93VwNY+EKmeUnSXpYwYuE2ujo4=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 319, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c78c4080a129be1ff118e039df4e8771bd400870015d378cf1b951fc3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "042dd3cd29db7616a6dc77bb1a66e849133b1408c540ee2ebb01e07bc4d3e5786401c4533e15697c6bf86e14def5088590c19aec9d96f8538a", "wx": "2dd3cd29db7616a6dc77bb1a66e849133b1408c540ee2ebb01e07bc4", "wy": "00d3e5786401c4533e15697c6bf86e14def5088590c19aec9d96f8538a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00042dd3cd29db7616a6dc77bb1a66e849133b1408c540ee2ebb01e07bc4d3e5786401c4533e15697c6bf86e14def5088590c19aec9d96f8538a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAELdPNKdt2Fqbcd7saZuhJEzsUCMVA7i67\nAeB7xNPleGQBxFM+FWl8a/huFN71CIWQwZrsnZb4U4o=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 320, "comment": "edge case for u1", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c5f3f6b9c25e45c8089f2c6543d345037e17f2f7d4b7854ab399ee32e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0453e89d29406622bbaa1bcf7c980d523209646cc20a4b3104aa3442647781d3de43413dfa061aa9b2d7c29eca9c8ed42b285fbcbbe016cc1e", "wx": "53e89d29406622bbaa1bcf7c980d523209646cc20a4b3104aa344264", "wy": "7781d3de43413dfa061aa9b2d7c29eca9c8ed42b285fbcbbe016cc1e"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000453e89d29406622bbaa1bcf7c980d523209646cc20a4b3104aa3442647781d3de43413dfa061aa9b2d7c29eca9c8ed42b285fbcbbe016cc1e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEU+idKUBmIruqG898mA1SMglkbMIKSzEE\nqjRCZHeB095DQT36BhqpstfCnsqcjtQrKF+8u+AWzB4=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 321, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fffffffffffffffffffffffffffb2364ae85014b149b86c741eb8be", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04049c4d998c0f368ebf1a69274fddf807dae245b4d3144d696d813ed9a5d3f586315ee7f5b263efe47a1b0a2e94847242710370d92ceb24a4", "wx": "049c4d998c0f368ebf1a69274fddf807dae245b4d3144d696d813ed9", "wy": "00a5d3f586315ee7f5b263efe47a1b0a2e94847242710370d92ceb24a4"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004049c4d998c0f368ebf1a69274fddf807dae245b4d3144d696d813ed9a5d3f586315ee7f5b263efe47a1b0a2e94847242710370d92ceb24a4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEBJxNmYwPNo6/GmknT934B9riRbTTFE1p\nbYE+2aXT9YYxXuf1smPv5HobCi6UhHJCcQNw2SzrJKQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 322, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00855f5b2dc8e46ec428a593f73219cf65dae793e8346e30cc3701309c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044b1b3c491443d1a45cbe5fb2f6ed36ac3ebde2a2456a7f9afe628dd7d328db165ee1110765797569b30b041984790ea3aa65bd0ba3341818", "wx": "4b1b3c491443d1a45cbe5fb2f6ed36ac3ebde2a2456a7f9afe628dd7", "wy": "00d328db165ee1110765797569b30b041984790ea3aa65bd0ba3341818"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044b1b3c491443d1a45cbe5fb2f6ed36ac3ebde2a2456a7f9afe628dd7d328db165ee1110765797569b30b041984790ea3aa65bd0ba3341818", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAESxs8SRRD0aRcvl+y9u02rD694qJFan+a\n/mKN19Mo2xZe4REHZXl1abMLBBmEeQ6jqmW9C6M0GBg=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 323, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c2db5f61aea817276af2064e104c7a30e32034cb526dd0aacfa56566f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b19e1369acb5fc913d1ac6e92ee3770590c5a45ce52fdf64a9f651be2fac7a017a6b6cfc1c381c9254564c1b929b3c101f89195a6d27907e", "wx": "00b19e1369acb5fc913d1ac6e92ee3770590c5a45ce52fdf64a9f651be", "wy": "2fac7a017a6b6cfc1c381c9254564c1b929b3c101f89195a6d27907e"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b19e1369acb5fc913d1ac6e92ee3770590c5a45ce52fdf64a9f651be2fac7a017a6b6cfc1c381c9254564c1b929b3c101f89195a6d27907e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsZ4Taay1/JE9GsbpLuN3BZDFpFzlL99k\nqfZRvi+segF6a2z8HDgcklRWTBuSmzwQH4kZWm0nkH4=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 324, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0084a6c7513e5f48c07fffffffffff8713f3cba1293e4f3e95597fe6bd", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d0c80d942da3dbe662467e1bcc69ceb322dc311152bf15557ed3f7aff7b627b0ba59524170527cc1161abdfa4a4a25dfd09c59a98db7ea04", "wx": "00d0c80d942da3dbe662467e1bcc69ceb322dc311152bf15557ed3f7af", "wy": "00f7b627b0ba59524170527cc1161abdfa4a4a25dfd09c59a98db7ea04"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d0c80d942da3dbe662467e1bcc69ceb322dc311152bf15557ed3f7aff7b627b0ba59524170527cc1161abdfa4a4a25dfd09c59a98db7ea04", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE0MgNlC2j2+ZiRn4bzGnOsyLcMRFSvxVV\nftP3r/e2J7C6WVJBcFJ8wRYavfpKSiXf0JxZqY236gQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 325, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c6c7513e5f48c07ffffffffffffff9d21fd1b31544cb13ca86a75b25e", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04a781c88681c98aadf31e26541b9ab6efa52a49412cf7282944f13720b68ddeaf8a09af0b372b007e122d402e724fdcea1c619a80b32bfe5b", "wx": "00a781c88681c98aadf31e26541b9ab6efa52a49412cf7282944f13720", "wy": "00b68ddeaf8a09af0b372b007e122d402e724fdcea1c619a80b32bfe5b"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004a781c88681c98aadf31e26541b9ab6efa52a49412cf7282944f13720b68ddeaf8a09af0b372b007e122d402e724fdcea1c619a80b32bfe5b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEp4HIhoHJiq3zHiZUG5q276UqSUEs9ygp\nRPE3ILaN3q+KCa8LNysAfhItQC5yT9zqHGGagLMr/ls=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 326, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00d8ea27cbe9180fffffffffffffff3a43fa3662a899627950d4eb64bc", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04931606b9f180d16409efebc996bb1df442b84e19bcc9bed0e236cb6450edd2162625a979a25b231fba17878b756a77c167223886613afb03", "wx": "00931606b9f180d16409efebc996bb1df442b84e19bcc9bed0e236cb64", "wy": "50edd2162625a979a25b231fba17878b756a77c167223886613afb03"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004931606b9f180d16409efebc996bb1df442b84e19bcc9bed0e236cb6450edd2162625a979a25b231fba17878b756a77c167223886613afb03", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEkxYGufGA0WQJ7+vJlrsd9EK4Thm8yb7Q\n4jbLZFDt0hYmJal5olsjH7oXh4t1anfBZyI4hmE6+wM=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 327, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3e5f48c07fffffffffffffffffffc724968c0ecf9ed783744a7337b3", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "041df7985b13decb2aa170b325ed2421d8d42474152b1040724ed7f28d77ab5d47a2fb85754a3515682f20b3a47d13b26bd59b72f3bda83532", "wx": "1df7985b13decb2aa170b325ed2421d8d42474152b1040724ed7f28d", "wy": "77ab5d47a2fb85754a3515682f20b3a47d13b26bd59b72f3bda83532"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00041df7985b13decb2aa170b325ed2421d8d42474152b1040724ed7f28d77ab5d47a2fb85754a3515682f20b3a47d13b26bd59b72f3bda83532", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEHfeYWxPeyyqhcLMl7SQh2NQkdBUrEEBy\nTtfyjXerXUei+4V1SjUVaC8gs6R9E7Jr1Zty872oNTI=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 328, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d00bfffffffffffffffffffffffffff3d87bb44c833bb384d0f224ccdde", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048a46670a9c9d3cf03a9e9d48525c75e572680a26278adc0888d5030f8c88e59829d5a802c0245aa8b5641779877c5647ad2b9b2a736535eb", "wx": "008a46670a9c9d3cf03a9e9d48525c75e572680a26278adc0888d5030f", "wy": "008c88e59829d5a802c0245aa8b5641779877c5647ad2b9b2a736535eb"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048a46670a9c9d3cf03a9e9d48525c75e572680a26278adc0888d5030f8c88e59829d5a802c0245aa8b5641779877c5647ad2b9b2a736535eb", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEikZnCpydPPA6np1IUlx15XJoCiYnitwI\niNUDD4yI5Zgp1agCwCRaqLVkF3mHfFZHrSubKnNlNes=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 329, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c7fffffffffffffffffffffffffff646c95d0a029629370d8e83d717f", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0416428097e6eb65688905678aae661f3d83b7e5ecc7787ed22fc029e868d62cb89be54d10ca5b575aa86f9c8fb475c6d90f59d4477595c01c", "wx": "16428097e6eb65688905678aae661f3d83b7e5ecc7787ed22fc029e8", "wy": "68d62cb89be54d10ca5b575aa86f9c8fb475c6d90f59d4477595c01c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000416428097e6eb65688905678aae661f3d83b7e5ecc7787ed22fc029e868d62cb89be54d10ca5b575aa86f9c8fb475c6d90f59d4477595c01c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEFkKAl+brZWiJBWeKrmYfPYO35ezHeH7S\nL8Ap6GjWLLib5U0QyltXWqhvnI+0dcbZD1nUR3WVwBw=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 330, "comment": "edge case for u2", "msg": "313233343030", "sig": "303c021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021c3fffffffffffffffffffffffffff8b51705c781f09ee94a2ae2e1520", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "043fa563f806be913b57347adf8b1e862b7e0253bea701f6b201e57dca0231cd68a2c24493deced00771e100bd001bc79902756d56d6ff87f1", "wx": "3fa563f806be913b57347adf8b1e862b7e0253bea701f6b201e57dca", "wy": "0231cd68a2c24493deced00771e100bd001bc79902756d56d6ff87f1"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00043fa563f806be913b57347adf8b1e862b7e0253bea701f6b201e57dca0231cd68a2c24493deced00771e100bd001bc79902756d56d6ff87f1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEP6Vj+Aa+kTtXNHrfix6GK34CU76nAfay\nAeV9ygIxzWiiwkST3s7QB3HhAL0AG8eZAnVtVtb/h/E=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 331, "comment": "edge case for u2", "msg": "313233343030", "sig": "303d021c7ffffffffffffffffffffffffffffffffffffffffffffffffffffffd021d0096dafb0d7540b93b5790327082635cd8895e1e799d5d19f92b594056", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04feba9230660ac3fd64ae02f7306297aa4975436c0ad1105854e4066bddcdba26679436b4e13f95d8f4f659142e7bf405c772e21788047b08", "wx": "00feba9230660ac3fd64ae02f7306297aa4975436c0ad1105854e4066b", "wy": "00ddcdba26679436b4e13f95d8f4f659142e7bf405c772e21788047b08"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004feba9230660ac3fd64ae02f7306297aa4975436c0ad1105854e4066bddcdba26679436b4e13f95d8f4f659142e7bf405c772e21788047b08", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE/rqSMGYKw/1krgL3MGKXqkl1Q2wK0RBY\nVOQGa93NuiZnlDa04T+V2PT2WRQue/QFx3LiF4gEewg=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 332, "comment": "point duplication during verification", "msg": "313233343030", "sig": "303d021d00c44503dae85dd5210780f02928b3d927171c578f8603d16b240663c7021c141a6efb8f6e19016b61c76573c9759309a18c16e48c136dca552602", "result": "valid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04feba9230660ac3fd64ae02f7306297aa4975436c0ad1105854e4066b223245d9986bc94b1ec06a270b09a6ead1840bfa388d1de877fb84f9", "wx": "00feba9230660ac3fd64ae02f7306297aa4975436c0ad1105854e4066b", "wy": "223245d9986bc94b1ec06a270b09a6ead1840bfa388d1de877fb84f9"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004feba9230660ac3fd64ae02f7306297aa4975436c0ad1105854e4066b223245d9986bc94b1ec06a270b09a6ead1840bfa388d1de877fb84f9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE/rqSMGYKw/1krgL3MGKXqkl1Q2wK0RBY\nVOQGayIyRdmYa8lLHsBqJwsJpurRhAv6OI0d6Hf7hPk=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 333, "comment": "duplication bug", "msg": "313233343030", "sig": "303d021d00c44503dae85dd5210780f02928b3d927171c578f8603d16b240663c7021c141a6efb8f6e19016b61c76573c9759309a18c16e48c136dca552602", "result": "invalid", "flags": ["PointDuplication"]}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "040025094ea9e4ce76d8c47356e4ae604eb6469669b0161fff805a765f6f807430f186ba63ffa0f315be721ec43baef24b8fba10b04f19189f", "wx": "25094ea9e4ce76d8c47356e4ae604eb6469669b0161fff805a765f", "wy": "6f807430f186ba63ffa0f315be721ec43baef24b8fba10b04f19189f"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00040025094ea9e4ce76d8c47356e4ae604eb6469669b0161fff805a765f6f807430f186ba63ffa0f315be721ec43baef24b8fba10b04f19189f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEACUJTqnkznbYxHNW5K5gTrZGlmmwFh//\ngFp2X2+AdDDxhrpj/6DzFb5yHsQ7rvJLj7oQsE8ZGJ8=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 334, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "303c021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04754267d9f090524d4c97bb1a5622e0c9f804dfd70cc68d9872f0a4f8708c24a49b81307db458c020fc7374770858faaea1d6ee37bf7beae3", "wx": "754267d9f090524d4c97bb1a5622e0c9f804dfd70cc68d9872f0a4f8", "wy": "708c24a49b81307db458c020fc7374770858faaea1d6ee37bf7beae3"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004754267d9f090524d4c97bb1a5622e0c9f804dfd70cc68d9872f0a4f8708c24a49b81307db458c020fc7374770858faaea1d6ee37bf7beae3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEdUJn2fCQUk1Ml7saViLgyfgE39cMxo2Y\ncvCk+HCMJKSbgTB9tFjAIPxzdHcIWPquodbuN7976uM=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 335, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0475a0d5291f48c17b4029baaea4d5796bda7c9d5802d534c0c265794ac93769dcaf7965f3c12864f50ccb22f10f193d5f0f7c33449131d2c4", "wx": "75a0d5291f48c17b4029baaea4d5796bda7c9d5802d534c0c265794a", "wy": "00c93769dcaf7965f3c12864f50ccb22f10f193d5f0f7c33449131d2c4"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000475a0d5291f48c17b4029baaea4d5796bda7c9d5802d534c0c265794ac93769dcaf7965f3c12864f50ccb22f10f193d5f0f7c33449131d2c4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEdaDVKR9IwXtAKbqupNV5a9p8nVgC1TTA\nwmV5Ssk3adyveWXzwShk9QzLIvEPGT1fD3wzRJEx0sQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 336, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021d00db6db6db6db6db6db6db6db6db6ceed4c09e84c77ebd9116e17391eb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04d357674da166e0f4a058203df23b8f8ada82858034355d23aff5a812edf6d5265ed40b6da2adbe9f8cb6050ebf61ebc0e56a10a1d6cf6e2e", "wx": "00d357674da166e0f4a058203df23b8f8ada82858034355d23aff5a812", "wy": "00edf6d5265ed40b6da2adbe9f8cb6050ebf61ebc0e56a10a1d6cf6e2e"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004d357674da166e0f4a058203df23b8f8ada82858034355d23aff5a812edf6d5265ed40b6da2adbe9f8cb6050ebf61ebc0e56a10a1d6cf6e2e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE01dnTaFm4PSgWCA98juPitqChYA0NV0j\nr/WoEu321SZe1Attoq2+n4y2BQ6/YevA5WoQodbPbi4=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 337, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0485675f96009ddf740108f5bdb82523d59414d39b74e11570a6d0faa313cd5841a8e2a3e3ebd099b43205ca46664a6e6cf19481e8552fb4d8", "wx": "0085675f96009ddf740108f5bdb82523d59414d39b74e11570a6d0faa3", "wy": "13cd5841a8e2a3e3ebd099b43205ca46664a6e6cf19481e8552fb4d8"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000485675f96009ddf740108f5bdb82523d59414d39b74e11570a6d0faa313cd5841a8e2a3e3ebd099b43205ca46664a6e6cf19481e8552fb4d8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEhWdflgCd33QBCPW9uCUj1ZQU05t04RVw\nptD6oxPNWEGo4qPj69CZtDIFykZmSm5s8ZSB6FUvtNg=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 338, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021d00cccccccccccccccccccccccccccc121be6fa59cb431754377d168831", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04c1678bddce5f00e4f41ebcab86f801b4dc050a1b2da8f9747b5abfc9f1cf0d67d9c93456988a004dbcb8e95d17dde4070577e51d881d8859", "wx": "00c1678bddce5f00e4f41ebcab86f801b4dc050a1b2da8f9747b5abfc9", "wy": "00f1cf0d67d9c93456988a004dbcb8e95d17dde4070577e51d881d8859"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004c1678bddce5f00e4f41ebcab86f801b4dc050a1b2da8f9747b5abfc9f1cf0d67d9c93456988a004dbcb8e95d17dde4070577e51d881d8859", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEwWeL3c5fAOT0HryrhvgBtNwFChstqPl0\ne1q/yfHPDWfZyTRWmIoATby46V0X3eQHBXflHYgdiFk=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 339, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c249249249249249249249249249227ce201a6b76951f982e7ae89852", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0409d2cfd575986bfe1a7420c7dabc0476e0dd13e54e01aa6f97b9e027464ba5b84c4d92ed9fddd0bbcb2382f0e9b9d5bb201b2ea8eb8d3a50", "wx": "09d2cfd575986bfe1a7420c7dabc0476e0dd13e54e01aa6f97b9e027", "wy": "464ba5b84c4d92ed9fddd0bbcb2382f0e9b9d5bb201b2ea8eb8d3a50"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000409d2cfd575986bfe1a7420c7dabc0476e0dd13e54e01aa6f97b9e027464ba5b84c4d92ed9fddd0bbcb2382f0e9b9d5bb201b2ea8eb8d3a50", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAECdLP1XWYa/4adCDH2rwEduDdE+VOAapv\nl7ngJ0ZLpbhMTZLtn93Qu8sjgvDpudW7IBsuqOuNOlA=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 340, "comment": "extreme value for k", "msg": "313233343030", "sig": "303c021c706a46dc76dcb76798e60e6d89474788d16dc18032d268fd1a704fa6021c0eb10e5ab95facded4061029d63a46f46f12947411f2ea561a592057", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04006fcc244352869febdc7425204f7b297ca85d5b0da8aa6ffe87198062feef58d8c5a878d0255cd547af4f132906555017c330648d5b1ae4", "wx": "6fcc244352869febdc7425204f7b297ca85d5b0da8aa6ffe871980", "wy": "62feef58d8c5a878d0255cd547af4f132906555017c330648d5b1ae4"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004006fcc244352869febdc7425204f7b297ca85d5b0da8aa6ffe87198062feef58d8c5a878d0255cd547af4f132906555017c330648d5b1ae4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEAG/MJENShp/r3HQlIE97KXyoXVsNqKpv\n/ocZgGL+71jYxah40CVc1UevTxMpBlVQF8MwZI1bGuQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 341, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c5555555555555555555555555555078ba03da56a069f0dc1c9740e14", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "040171e08f58fbf9812ae32570c2102e843968f5f35c596ff8591b03a66c07f75d9ecc5e39dda090ae92157e5d86fcf1a8c395490a446dc7ff", "wx": "0171e08f58fbf9812ae32570c2102e843968f5f35c596ff8591b03a6", "wy": "6c07f75d9ecc5e39dda090ae92157e5d86fcf1a8c395490a446dc7ff"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00040171e08f58fbf9812ae32570c2102e843968f5f35c596ff8591b03a66c07f75d9ecc5e39dda090ae92157e5d86fcf1a8c395490a446dc7ff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEAXHgj1j7+YEq4yVwwhAuhDlo9fNcWW/4\nWRsDpmwH912ezF453aCQrpIVfl2G/PGow5VJCkRtx/8=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 342, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303e021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021d00db6db6db6db6db6db6db6db6db6ceed4c09e84c77ebd9116e17391eb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04330586236257b3dabb8fa249fc6a65fe0f97e4d51c162028e535b74d20101ba5cc9967511f794fa7c7f6114d40e14b7dc589148bdb1c275a", "wx": "330586236257b3dabb8fa249fc6a65fe0f97e4d51c162028e535b74d", "wy": "20101ba5cc9967511f794fa7c7f6114d40e14b7dc589148bdb1c275a"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004330586236257b3dabb8fa249fc6a65fe0f97e4d51c162028e535b74d20101ba5cc9967511f794fa7c7f6114d40e14b7dc589148bdb1c275a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEMwWGI2JXs9q7j6JJ/Gpl/g+X5NUcFiAo\n5TW3TSAQG6XMmWdRH3lPp8f2EU1A4Ut9xYkUi9scJ1o=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 343, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c33333333333333333333333333330486f9be9672d0c5d50ddf45a20c", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "048044b73305e9f59021dbd0ce462d0d8a9b22e75887c56505c9c94c2e6291a5c5364bad138d3ac3b538f77b266903bf71b6d4cc25ac0b6f12", "wx": "008044b73305e9f59021dbd0ce462d0d8a9b22e75887c56505c9c94c2e", "wy": "6291a5c5364bad138d3ac3b538f77b266903bf71b6d4cc25ac0b6f12"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00048044b73305e9f59021dbd0ce462d0d8a9b22e75887c56505c9c94c2e6291a5c5364bad138d3ac3b538f77b266903bf71b6d4cc25ac0b6f12", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEgES3MwXp9ZAh29DORi0Nipsi51iHxWUF\nyclMLmKRpcU2S60TjTrDtTj3eyZpA79xttTMJawLbxI=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 344, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303e021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021d00cccccccccccccccccccccccccccc121be6fa59cb431754377d168831", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "046fdbd44d22532a8803568b512f27c107572a4f0e99c90ab15d6c1bc7014e5943da516a92cf6cf1ffb4f859bc93e5a8dd9f9b1906d69acd92", "wx": "6fdbd44d22532a8803568b512f27c107572a4f0e99c90ab15d6c1bc7", "wy": "014e5943da516a92cf6cf1ffb4f859bc93e5a8dd9f9b1906d69acd92"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00046fdbd44d22532a8803568b512f27c107572a4f0e99c90ab15d6c1bc7014e5943da516a92cf6cf1ffb4f859bc93e5a8dd9f9b1906d69acd92", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEb9vUTSJTKogDVotRLyfBB1cqTw6ZyQqx\nXWwbxwFOWUPaUWqSz2zx/7T4WbyT5ajdn5sZBtaazZI=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 345, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c249249249249249249249249249227ce201a6b76951f982e7ae89852", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "041b9a838b8899a5c7b9d982bcc080787521d8334fdbd2bb544ab10f4676548c21fd012875c5364e1552a03a1ea237a2f1adafe6279419877d", "wx": "1b9a838b8899a5c7b9d982bcc080787521d8334fdbd2bb544ab10f46", "wy": "76548c21fd012875c5364e1552a03a1ea237a2f1adafe6279419877d"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00041b9a838b8899a5c7b9d982bcc080787521d8334fdbd2bb544ab10f4676548c21fd012875c5364e1552a03a1ea237a2f1adafe6279419877d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEG5qDi4iZpce52YK8wIB4dSHYM0/b0rtU\nSrEPRnZUjCH9ASh1xTZOFVKgOh6iN6Lxra/mJ5QZh30=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 346, "comment": "extreme value for k", "msg": "313233343030", "sig": "303d021d00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21021c0eb10e5ab95facded4061029d63a46f46f12947411f2ea561a592057", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34", "wx": "00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21", "wy": "00bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21bd376388b5f723fb4c22dfe6cd4375a05a07476444d5819985007e34", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEtw4MvWu0v38yE5C5SgPB01bCESI0MoDW\nEVwdIb03Y4i19yP7TCLf5s1DdaBaB0dkRNWBmYUAfjQ=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 347, "comment": "testing point duplication", "msg": "313233343030", "sig": "303c021c0e77efebdac83c01dce3f8c4162e286b38b7e23de83637a72531eab7021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}, {"tcId": 348, "comment": "testing point duplication", "msg": "313233343030", "sig": "303d021d00f18810142537c3fe231c073be9d0ee37a8010e002ba6f19e372a3f86021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d2142c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd", "wx": "00b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d21", "wy": "42c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b70e0cbd6bb4bf7f321390b94a03c1d356c21122343280d6115c1d2142c89c774a08dc04b3dd201932bc8a5ea5f8b89bbb2a7e667aff81cd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEtw4MvWu0v38yE5C5SgPB01bCESI0MoDW\nEVwdIULInHdKCNwEs90gGTK8il6l+Libuyp+Znr/gc0=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 349, "comment": "testing point duplication", "msg": "313233343030", "sig": "303c021c0e77efebdac83c01dce3f8c4162e286b38b7e23de83637a72531eab7021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}, {"tcId": 350, "comment": "testing point duplication", "msg": "313233343030", "sig": "303d021d00f18810142537c3fe231c073be9d0ee37a8010e002ba6f19e372a3f86021c249249249249249249249249249227ce201a6b76951f982e7ae89851", "result": "invalid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "044c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176", "wx": "4c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466", "wy": "00ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a00044c246670658a1d41f5d77bce246cbe386ac22848e269b9d4cd67c466ddd947153d39b2d42533a460def26880408caf2dd3dd48fe888cd176", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAETCRmcGWKHUH113vOJGy+OGrCKEjiabnU\nzWfEZt3ZRxU9ObLUJTOkYN7yaIBAjK8t091I/oiM0XY=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 351, "comment": "pseudorandom signature", "msg": "", "sig": "303c021c474b086cf4754c270d20f88be569b7d7b6eb6e55de6ce21382160e81021c60692fdb35b4cb824a2729fb175f709d06bc9f4e8bbb4b1058c53788", "result": "valid", "flags": []}, {"tcId": 352, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "303e021d00dde359fe51a6d8ca9aec41e3376bd3e9fff8a41a3e44a64db81d6326021d00a1c29d577309f7135b688b1990433fb45c5dc17a021557272c1256fc", "result": "valid", "flags": []}, {"tcId": 353, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "303d021c0cdc4a54a091e61e0f764ddb12ffb243f457ad571a8ae7999caa0f06021d00d5cdd524f2092bcbe2fc7c328b0876d436d9190058700af370dab0b7", "result": "valid", "flags": []}, {"tcId": 354, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "303c021c6efa32457e09b8abe22168501bc4ae051d2294674114a9dca94c51ae021c3173b652c78324b877dc5bdfe80324aeb01b171fd2626124a44f0b36", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf8a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000", "wx": "00aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf", "wy": "008a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004aed6fcad2400c4d94e55dbb6b012ce3d4c2b46843fbe99d4289e6ecf8a24a89e71343d7d151d258d2cb690349c2d56b366dd10a600000000", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAErtb8rSQAxNlOVdu2sBLOPUwrRoQ/vpnU\nKJ5uz4okqJ5xND19FR0ljSy2kDScLVazZt0QpgAAAAA=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 355, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021c41a3e0370cf8148bccbdd03a7e763d382695263da11b9470b0e103d6021d0087a612990d0a4a9f811e20ac520a3476d91848444cccc4c8138ad5d6", "result": "valid", "flags": []}, {"tcId": 356, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303c021c6f138512bf05addbb536b976b9125e1228f43f32f766325d1c270e16021c556205464ff65c9a5d4d9475167059863835644b06862f1b49cca20c", "result": "valid", "flags": []}, {"tcId": 357, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303e021d00840dccce1760f476240b516d5cfbf9a10fbd44b25c68fd69a96f67a3021d00b79884b6495a1c65f07853fc5d56ac06b84366bddb3ddf56b0fc9328", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f173d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff", "wx": "00bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f1", "wy": "73d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004bf19ecfe43ffe289f699f479316145b9a7f7370b9ece5ab1212174f173d528949ae9142f818bade71a960407963be0b6482a6a60ffffffff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEvxns/kP/4on2mfR5MWFFuaf3Nwuezlqx\nISF08XPVKJSa6RQvgYut5xqWBAeWO+C2SCpqYP////8=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 358, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303e021d00949e76e0e35be5a08fcb60d8f86ecb0c650fc9b37ecd61a059649315021d00cd870903fc1437d59e43eade139aa8eb717039d2e8d3282f27d484f0", "result": "valid", "flags": []}, {"tcId": 359, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303e021d00a6364e1e4dd41327b4f88a9998412bf168551acc561357d2bbfd2aaf021d00f5e48ced76655eb729f4371d20f5d4ec53a23844313423063bb85fba", "result": "valid", "flags": []}, {"tcId": 360, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303e021d00f572bd069ffef726222db033664205220bf694f374282c795959945f021d00ca942ef4fd6becd3bc4c3280ca29b84c8d29555dda402a50af1d7665", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0426e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0", "wx": "26e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000", "wy": "00eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000426e5abf135cb54eaaa16b69e4b0b292275344e88a09df6df80000000eab891de54e3f26ff50ab989f333dac551583d468ae623c596434af0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEJuWr8TXLVOqqFraeSwspInU0Toignfbf\ngAAAAOq4kd5U4/Jv9Qq5ifMz2sVRWD1GiuYjxZZDSvA=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 361, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303e021d00e8b29401b22688776f8b24951f239893e12d822868dbbe1f6bc860ee021d00bfefb3641875aa10c3e8468665071658d0ab312cdf8f9a669bd82a7a", "result": "valid", "flags": []}, {"tcId": 362, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021c291d8cc81f715fd52a74168946ed2bf2d692dae0955249e4cdad3209021d00ef56a69ebd78125a4ca12bdacd193e2111a35158d5d78b5d30460a34", "result": "valid", "flags": []}, {"tcId": 363, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "303d021d00f5123a9f9212f64b8c607cfd5b0418f3694ce5ef4d161186afac7d30021c44c4b5d7647dfc1ed10f1d8d9283d4bca6961a4ea78cf1cad632f95b", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd", "wx": "00ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff", "wy": "41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ec627f345545d03f8c6dbd08e575527116567fe375f9ecaaffffffff41bf705697d5f716bcf78718d5393b63a98691f4a1f24246375538fd", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE7GJ/NFVF0D+Mbb0I5XVScRZWf+N1+eyq\n/////0G/cFaX1fcWvPeHGNU5O2OphpH0ofJCRjdVOP0=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 364, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021c6119c72d6d5fe72d181614d3d40b36eec756ca7d9e4f2ab3095d52a4021d00fbbaf5bb4a97b6096792f17f95b116a645fe1c62fa1d83e969c0e8d6", "result": "valid", "flags": []}, {"tcId": 365, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021d008c3d675a759d30b8afcbee3f37746c226eb992177f8d76f162d4093e021c643322d3f37ba532e7e5f0f0c14b691a3678075c03281203eb529745", "result": "valid", "flags": []}, {"tcId": 366, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "303d021d00e1f7acc96fd3eafc29991165223b0b899c2b04cc239372eef4d060a5021c2a0b6f214fa197cfbe834a4f80a74de9748e62f2a894214fe92bdeeb", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a00000000762d28f1fdc219184f81681fbff566d465b5f1f31e872df5", "wx": "15016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a", "wy": "762d28f1fdc219184f81681fbff566d465b5f1f31e872df5"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a00000000762d28f1fdc219184f81681fbff566d465b5f1f31e872df5", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEFQFuUrNkctU2R3YF+4Bd05AwgqBi0eow\nr55VWgAAAAB2LSjx/cIZGE+BaB+/9WbUZbXx8x6HLfU=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 367, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021d00ab49a92649a5b95bee1a0e4dd897d5f5aae7581faa41673ad4d18eb6021c5df9e1a65ad72a88f58b0b711e162c6de169ade7106c01571486c7df", "result": "valid", "flags": []}, {"tcId": 368, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d00b3edcc316dd5cbe6cd0054e48bc1a77e55cc4cf3dddbf552ffc4e929021d00cf4de654980e88f7908109c5f637113cb03bdf8ba5443dda852e6313", "result": "valid", "flags": []}, {"tcId": 369, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021c32538b93b71e7ea2b3cef6027271e0037b84f3818a0727accaaea6d9021d00e6225061a7d76b93c6562de31d50608444a533bb5853b8ab94160fea", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555affffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c", "wx": "15016e52b36472d536477605fb805dd3903082a062d1ea30af9e555a", "wy": "00ffffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000415016e52b36472d536477605fb805dd3903082a062d1ea30af9e555affffffff89d2d70e023de6e7b07e97df400a992b9a4a0e0ce178d20c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEFQFuUrNkctU2R3YF+4Bd05AwgqBi0eow\nr55VWv////+J0tcOAj3m57B+l99ACpkrmkoODOF40gw=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 370, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303e021d00846e6b4ae059cf673ac2f424c07be6b64456554effd14d4a0a85d5d1021d00c67353e85fdfb421fb3abb014f1d3d21a8b744ba146e4709e722a5e6", "result": "valid", "flags": []}, {"tcId": 371, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021d0095073bb824196016092413631dd4208a571744d0030dd54352611921021c100ef7ac960937e740f868bf9b37c6845317ab1865ce13881ce5156b", "result": "valid", "flags": []}, {"tcId": 372, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303c021c3186a92aa760960996aea13f3137acf00b82b2b2036e607ec9c44b67021c1835944e96b6ca1f445cf3350f105a97a37252f85cf6d8e628c96a02", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "0400000000f7e4713d085112112c37cdf4601ff688da796016b71a727ade5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1", "wx": "00f7e4713d085112112c37cdf4601ff688da796016b71a727a", "wy": "00de5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a000400000000f7e4713d085112112c37cdf4601ff688da796016b71a727ade5a9ec165054cc987f9dc87e9991b92e4fa649ca655eeae9f2a30e1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEAAAAAPfkcT0IURIRLDfN9GAf9ojaeWAW\ntxpyet5ansFlBUzJh/nch+mZG5Lk+mScplXurp8qMOE=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 373, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021d00cdb43f063ed3ce20f94e453d40e9d7de39936906484114c307078e22021c26d2436f2f66a954010e580b95e21a174a4b667aa8249f2676fe1be7", "result": "valid", "flags": []}, {"tcId": 374, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021d00b29a2bfe9fab394b34550483f3cc811d9b86345f6f8e35d6a6bb0b34021c26120ea06b1910c44bc370cd0479c6addc5bce896c4a606810194e2e", "result": "valid", "flags": []}, {"tcId": 375, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303e021d00ac247dff26016109d28200505270cf2d2c2b06067bd5d330a1c2a41a021d00910a8d69da5d6a508c88aad4f091ed5b5286d029c1095fc57af2a106", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f72586c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e", "wx": "00ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f725", "wy": "0086c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004ffffffffeadf7cee8d34d04cf22c8f7de35674fb2f501d242a76f72586c409309d398e60ce1e0a4c9e05a9d32627577e8ce2cc7f3afa2c3e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAE/////+rffO6NNNBM8iyPfeNWdPsvUB0k\nKnb3JYbECTCdOY5gzh4KTJ4FqdMmJ1d+jOLMfzr6LD4=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 376, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303e021d00bfdbfcd3bee9803c5060dbc69d6749fb5e4dc40a1c00002e0d235354021d00a84a6ff4c2ef80c074c8a8a9305e79e0e75321b9afab9348c02a7e29", "result": "valid", "flags": []}, {"tcId": 377, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021c20fd72eb73edda9cb0aca2473c5c582c78318b0705a9a6d7180ac767021d00b07a3e773fa28f513202b69903b5cc65f2b4f7714b5b28c83b52bedd", "result": "valid", "flags": []}, {"tcId": 378, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021d0086aa1eba72a1aca7dd83cb2c2659241d12fd8ac17cbd798cc44afabb021c66e0a66a78c6c31ce99e45162a0b4757deff5ed80be8348283f1b7a5", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1000000000e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc", "wx": "00b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1", "wy": "0e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1000000000e2ab0e8495e859eb2afb00769d6e7fe626a119167c0b6bc", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsAE8b7/28J/s2hwmPvZTmdTPmJyl/E+P\n/w/p4QAAAAAOKrDoSV6FnrKvsAdp1uf+YmoRkWfAtrw=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 379, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021c01cfaa7ccae0522b5363b48ab1fa5e0e102666f2ac5218c5b32523d3021d00a61491de2a4a05bda0bf2769453faa845451207c4dd3a95aab169b0c", "result": "valid", "flags": []}, {"tcId": 380, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303c021c500c9d08a0ab52d35fe5cf9d4eddea0eb8cc00e8e8db0a29a512de10021c482d0f78f2808e83f10bee9fad61f4bdba83ab9a4f7d71c9b7083e13", "result": "valid", "flags": []}, {"tcId": 381, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "303d021c75bf80b060ff123c0e525c98ca74fa82d716a09c21d67accd34a60bc021d00eda7a9644563a349ff3a2483b6e7563f0aa4c4d319551ca0c3bd1fe4", "result": "valid", "flags": []}]}, {"key": {"curve": "secp224r1", "keySize": 224, "type": "EcPublicKey", "uncompressed": "04b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945", "wx": "00b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1", "wy": "00fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945"}, "keyDer": "304e301006072a8648ce3d020106052b81040021033a0004b0013c6fbff6f09fecda1c263ef65399d4cf989ca5fc4f8fff0fe9e1fffffffff1d54f17b6a17a614d504ff7962918019d95ee6e983f4945", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nME4wEAYHKoZIzj0CAQYFK4EEACEDOgAEsAE8b7/28J/s2hwmPvZTmdTPmJyl/E+P\n/w/p4f/////x1U8XtqF6YU1QT/eWKRgBnZXubpg/SUU=\n-----END PUBLIC KEY-----", "sha": "SHA3-224", "type": "EcdsaVerify", "tests": [{"tcId": 382, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021c3ef832679f6277b901b6a986833d03e4b574d23fd73edfd936689761021d00e82af05ebafe22ccd384336c9530738036d99f17b62ef3dfc4e0948b", "result": "valid", "flags": []}, {"tcId": 383, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303e021d009504b624b116a2c28efa037e35581c71b2e7d01f30d8f68946c13e88021d008082639374a8d5e067a6df09d6df11a972967a081a5307a3b7f1785b", "result": "valid", "flags": []}, {"tcId": 384, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "303d021c2eb5db8a9e1c82ed4ce643c953c9f11c3de264ef92d7607c91dbce76021d00b6a97c943aa7a62b5783786356f7b75b36b88356eb62d5a3d15a7029", "result": "valid", "flags": []}]}]}