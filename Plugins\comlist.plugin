"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║   ██████╗ ██████╗ ███╗   ███╗███╗   ███╗ █████╗ ███╗   ██╗██████╗            ║
║  ██╔════╝██╔═══██╗████╗ ████║████╗ ████║██╔══██╗████╗  ██║██╔══██╗           ║
║  ██║     ██║   ██║██╔████╔██║██╔████╔██║███████║██╔██╗ ██║██║  ██║           ║
║  ██║     ██║   ██║██║╚██╔╝██║██║╚██╔╝██║██╔══██║██║╚██╗██║██║  ██║           ║
║  ╚██████╗╚██████╔╝██║ ╚═╝ ██║██║ ╚═╝ ██║██║  ██║██║ ╚████║██████╔╝           ║
║   ╚═════╝ ╚═════╝ ╚═╝     ╚═╝╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═════╝            ║
║                                                                              ║
║                          ██╗     ██╗███████╗████████╗                        ║
║                          ██║     ██║██╔════╝╚══██╔══╝                        ║
║                          ██║     ██║███████╗   ██║                           ║
║                          ██║     ██║╚════██║   ██║                           ║
║                          ███████╗██║███████║   ██║                           ║
║                          ╚══════╝╚═╝╚══════╝   ╚═╝                           ║
║                                                                              ║
║                      Command List Plugin                                     ║
║                                                                              ║
║  ⚠️  ВАЖНО: При форке или модификации этого плагина обязательно              ║
║      указывайте оригинального автора: @mihailk<PERSON><PERSON><PERSON> & @mishabotov          ║
║                                                                              ║
║  ⚠️  IMPORTANT: When forking or modifying this plugin, please                ║
║      credit the original author: @mihailkotovski & @mishabotov               ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
"""

import os
import ast
import re
import traceback
import plugins_manager
import time
import json
from typing import Set, Dict, List, Any, Optional
from base_plugin import BasePlugin, HookResult, HookStrategy
from ui.settings import Header, Divider, Input, Switch, Text, Selector
from markdown_utils import parse_markdown
from ui.bulletin import BulletinHelper
from android_utils import run_on_ui_thread
from java.util import ArrayList, Locale
from org.telegram.tgnet import TLRPC

__id__ = "command_list_by_mihailkotovski"
__name__ = "Command List"
__version__ = "3.0"
__description__ = "Command list with search, filtering, caching, statistics and export features. Use .cmdstats to view command usage statistics."
__author__ = "@mihailkotovski & @mishabotov"
__min_version__ = "11.9.0"
__icon__ = "DMJDuckX2/10"

AUTOUPDATE_CHANNEL_ID = 2349438816
AUTOUPDATE_CHANNEL_USERNAME = "mishabotov"
AUTOUPDATE_MESSAGE_ID = 65

zwylib: Optional[Any] = None
DEFAULT_CMD = ".cmds"

class CommandCache:
    """Manages caching of command data for better performance."""
    def __init__(self):
        self.cache_data = {}
        self.cache_timestamp = 0
        self.cache_duration = 300

    def is_cache_valid(self) -> bool:
        return time.time() - self.cache_timestamp < self.cache_duration

    def get_cached_commands(self) -> Optional[Dict[str, Dict[str, Any]]]:
        if self.is_cache_valid():
            return self.cache_data
        return None

    def cache_commands(self, commands: Dict[str, Dict[str, Any]]):
        self.cache_data = commands
        self.cache_timestamp = time.time()

    def invalidate_cache(self):
        self.cache_data = {}
        self.cache_timestamp = 0

class CommandStatistics:
    """Tracks command usage statistics."""
    def __init__(self, plugin_instance):
        self.plugin = plugin_instance
        self.stats = self._load_stats()

    def _load_stats(self) -> Dict[str, int]:
        try:
            if is_zwylib_present():
                cache_file = zwylib.JsonCacheFile("command_stats.json", {})
                return cache_file.content
            else:
                return self.plugin.get_setting("command_stats", {})
        except:
            return {}

    def _save_stats(self):
        try:
            if is_zwylib_present():
                cache_file = zwylib.JsonCacheFile("command_stats.json", {})
                cache_file.content = self.stats
                cache_file.write()
            else:
                self.plugin.set_setting("command_stats", self.stats)
        except:
            pass

    def increment_usage(self, command: str):
        self.stats[command] = self.stats.get(command, 0) + 1
        self._save_stats()

    def get_usage_count(self, command: str) -> int:
        return self.stats.get(command, 0)

    def get_popular_commands(self, limit: int = 10) -> List[tuple]:
        return sorted(self.stats.items(), key=lambda x: x[1], reverse=True)[:limit]

    def get_total_usage(self) -> int:
        return sum(self.stats.values())

    def get_total_commands(self) -> int:
        return len(self.stats)

    def get_stats_summary(self) -> Dict[str, Any]:
        if not self.stats:
            return {
                "total_commands": 0,
                "total_usage": 0,
                "most_used": None,
                "least_used": None,
                "popular_commands": []
            }

        sorted_stats = sorted(self.stats.items(), key=lambda x: x[1], reverse=True)
        return {
            "total_commands": len(self.stats),
            "total_usage": sum(self.stats.values()),
            "most_used": sorted_stats[0] if sorted_stats else None,
            "least_used": sorted_stats[-1] if sorted_stats else None,
            "popular_commands": sorted_stats[:10]
        }



class LocalizationManager:
    """Manages multi-language strings for the plugin."""
    strings = {
        "ru": {
            "zwylib_not_found": "Для работы плагина требуется ZwyLib!",
            "settings_header": "Настройки Command List",
            "cmd_alias_title": "Команда для вызова",
            "cmd_alias_subtitle": "Команда, по которой будет выводиться список всех команд.",
            "blockquote_title": "Использовать цитату",
            "blockquote_subtitle": "Отображать список команд в виде сворачиваемой цитаты.",
            "debug_header": "Отладка",
            "debug_mode_title": "Режим отладки",
            "debug_mode_subtitle": "Выводит подробную информацию в logcat.",
            "cache_header": "Кэширование",
            "enable_cache_title": "Включить кэширование",
            "enable_cache_subtitle": "Кэшировать результаты сканирования для ускорения работы.",
            "cache_duration_title": "Время кэша (мин)",
            "cache_duration_subtitle": "Время жизни кэша в минутах (1-60).",
            "display_header": "Отображение",
            "sort_mode_title": "Сортировка команд",
            "sort_mode_subtitle": "Способ сортировки команд в списке.",
            "show_descriptions_title": "Показывать описания",
            "show_descriptions_subtitle": "Отображать описания команд, если доступны.",
            "show_plugin_stats_title": "Показывать статистику",
            "show_plugin_stats_subtitle": "Отображать количество команд для каждого плагина.",
            "info_divider": "Этот плагин анализирует код других плагинов для поиска команд.",
            "cmd_list_header": "📜 **Список обнаруженных команд ({plugin_count} плагинов, {command_count} команд):**\n\n",
            "plugin_status_on": "Включен",
            "plugin_status_off": "Выключен",
            "cmd_note": "*⚠️ Примечание: Некоторые команды могут быть определены динамически и не показаны здесь.*",
            "error_collecting_cmds": "Произошла ошибка при сборе команд:\n\n",
            "no_cmds_found": "Не удалось найти команды в других плагинах.",
            "dir_error": "Не удалось определить директорию плагинов.",
            "cache_cleared": "Кэш команд очищен!",
            "clear_cache_title": "Очистить кэш",
            "clear_cache_subtitle": "Принудительно обновить список команд.",
            "cached_data_info": "📊 Данные из кэша (обновлено {time_ago} назад)",
            "sort_alphabetical": "По алфавиту",
            "sort_by_plugin": "По плагинам",
            "sort_by_usage": "По популярности",
            "search_results": "🔍 **Результаты поиска для '{query}':**\n\n",
            "search_no_results": "🔍 **Поиск '{query}':** Команды не найдены.",
            "search_help": "Используйте {cmd} [поисковый запрос] для поиска команд",
            "stats_unavailable": "📊 **Статистика команд недоступна**\n\nСистема статистики не инициализирована.",
            "stats_no_usage": "📊 **Статистика команд**\n\n*Команды еще не использовались.*",
            "stats_header": "📊 **Статистика использования команд**",
            "stats_general": "**Общая статистика:**",
            "stats_unique_commands": "• Уникальных команд:",
            "stats_total_usage": "• Всего использований:",
            "stats_most_popular": "**Самая популярная команда:**",
            "stats_top_commands": "**Топ-{count} команд:**",
            "stats_error": "Произошла ошибка при получении статистики команд:"
        },
        "en": {
            "zwylib_not_found": "ZwyLib is required for this plugin to work!",
            "settings_header": "Command List Settings",
            "cmd_alias_title": "Trigger Command",
            "cmd_alias_subtitle": "The command to show the list of all commands.",
            "blockquote_title": "Use blockquote",
            "blockquote_subtitle": "Display the command list as a collapsible blockquote.",
            "debug_header": "Debugging",
            "debug_mode_title": "Debug Mode",
            "debug_mode_subtitle": "Prints detailed information to logcat.",
            "cache_header": "Caching",
            "enable_cache_title": "Enable caching",
            "enable_cache_subtitle": "Cache scan results for better performance.",
            "cache_duration_title": "Cache time (min)",
            "cache_duration_subtitle": "Cache lifetime in minutes (1-60).",
            "display_header": "Display",
            "sort_mode_title": "Command sorting",
            "sort_mode_subtitle": "How to sort commands in the list.",
            "show_descriptions_title": "Show descriptions",
            "show_descriptions_subtitle": "Display command descriptions if available.",
            "show_plugin_stats_title": "Show statistics",
            "show_plugin_stats_subtitle": "Display command count for each plugin.",
            "info_divider": "This plugin analyzes the code of other plugins to find commands.",
            "cmd_list_header": "📜 **Discovered Commands ({plugin_count} plugins, {command_count} commands):**\n\n",
            "plugin_status_on": "Enabled",
            "plugin_status_off": "Disabled",
            "cmd_note": "*⚠️ Note: Some commands might be defined dynamically and not shown here.*",
            "error_collecting_cmds": "An error occurred while collecting commands:\n\n",
            "no_cmds_found": "Could not find commands in other plugins.",
            "dir_error": "Could not determine the plugins directory.",
            "cache_cleared": "Command cache cleared!",
            "clear_cache_title": "Clear cache",
            "clear_cache_subtitle": "Force refresh command list.",
            "cached_data_info": "📊 Cached data (updated {time_ago} ago)",
            "sort_alphabetical": "Alphabetical",
            "sort_by_plugin": "By plugin",
            "sort_by_usage": "By popularity",
            "search_results": "🔍 **Search results for '{query}':**\n\n",
            "search_no_results": "🔍 **Search '{query}':** No commands found.",
            "search_help": "Use {cmd} [search query] to search commands",
            "stats_unavailable": "📊 **Command statistics unavailable**\n\nStatistics system is not initialized.",
            "stats_no_usage": "📊 **Command Statistics**\n\n*No commands have been used yet.*",
            "stats_header": "📊 **Command Usage Statistics**",
            "stats_general": "**General Statistics:**",
            "stats_unique_commands": "• Unique commands:",
            "stats_total_usage": "• Total usage:",
            "stats_most_popular": "**Most popular command:**",
            "stats_top_commands": "**Top {count} commands:**",
            "stats_error": "An error occurred while getting command statistics:"
        },
        "uk": {
            "zwylib_not_found": "Для роботи плагіна потрібен ZwyLib!",
            "settings_header": "Налаштування Command List",
            "cmd_alias_title": "Команда для виклику",
            "cmd_alias_subtitle": "Команда, за якою буде виводитись список усіх команд.",
            "blockquote_title": "Використовувати цитату",
            "blockquote_subtitle": "Відображати список команд у вигляді цитати, що згортається.",
            "debug_header": "Налагодження",
            "debug_mode_title": "Режим налагодження",
            "debug_mode_subtitle": "Виводить детальну інформацію в logcat.",
            "cache_header": "Кешування",
            "enable_cache_title": "Увімкнути кешування",
            "enable_cache_subtitle": "Кешувати результати сканування для прискорення роботи.",
            "cache_duration_title": "Час кешу (хв)",
            "cache_duration_subtitle": "Час життя кешу в хвилинах (1-60).",
            "display_header": "Відображення",
            "sort_mode_title": "Сортування команд",
            "sort_mode_subtitle": "Спосіб сортування команд у списку.",
            "show_descriptions_title": "Показувати описи",
            "show_descriptions_subtitle": "Відображати описи команд, якщо доступні.",
            "show_plugin_stats_title": "Показувати статистику",
            "show_plugin_stats_subtitle": "Відображати кількість команд для кожного плагіна.",
            "info_divider": "Цей плагін аналізує код інших плагінів для пошуку команд.",
            "cmd_list_header": "📜 **Список знайдених команд ({plugin_count} плагінів, {command_count} команд):**\n\n",
            "plugin_status_on": "Увімкнено",
            "plugin_status_off": "Вимкнено",
            "cmd_note": "*⚠️ Примітка: Деякі команди можуть бути визначені динамічно і не показані тут.*",
            "error_collecting_cmds": "Сталася помилка під час збору команд:\n\n",
            "no_cmds_found": "Не вдалося знайти команди в інших плагінах.",
            "dir_error": "Не вдалося визначити директорію плагінів.",
            "cache_cleared": "Кеш команд очищено!",
            "clear_cache_title": "Очистити кеш",
            "clear_cache_subtitle": "Примусово оновити список команд.",
            "cached_data_info": "📊 Дані з кешу (оновлено {time_ago} тому)",
            "sort_alphabetical": "За алфавітом",
            "sort_by_plugin": "За плагінами",
            "sort_by_usage": "За популярністю",
            "search_results": "🔍 **Результати пошуку для '{query}':**\n\n",
            "search_no_results": "🔍 **Пошук '{query}':** Команди не знайдено.",
            "search_help": "Використовуйте {cmd} [пошуковий запит] для пошуку команд",
            "stats_unavailable": "📊 **Статистика команд недоступна**\n\nСистема статистики не ініціалізована.",
            "stats_no_usage": "📊 **Статистика команд**\n\n*Команди ще не використовувались.*",
            "stats_header": "📊 **Статистика використання команд**",
            "stats_general": "**Загальна статистика:**",
            "stats_unique_commands": "• Унікальних команд:",
            "stats_total_usage": "• Всього використань:",
            "stats_most_popular": "**Найпопулярніша команда:**",
            "stats_top_commands": "**Топ-{count} команд:**",
            "stats_error": "Сталася помилка під час отримання статистики команд:"
        }
    }

    def __init__(self):
        self.language = Locale.getDefault().getLanguage()
        self.language = self.language if self.language in self.strings else "en"

    def get_string(self, key: str) -> str:
        return self.strings[self.language].get(key, self.strings["en"].get(key, key))

locali = LocalizationManager()

def _pluralize_commands(count: int) -> str:
    """Returns correct Russian plural form for 'команда'."""
    if count % 10 == 1 and count % 100 != 11:
        return f"{count} команда"
    elif count % 10 in [2, 3, 4] and count % 100 not in [12, 13, 14]:
        return f"{count} команды"
    else:
        return f"{count} команд"

def _pluralize_usages(count: int) -> str:
    """Returns correct Russian plural form for 'использование'."""
    if count % 10 == 1 and count % 100 != 11:
        return f"{count} использование"
    elif count % 10 in [2, 3, 4] and count % 100 not in [12, 13, 14]:
        return f"{count} использования"
    else:
        return f"{count} использований"

def import_zwylib(show_bulletin: bool = True):
    """Imports zwylib and handles import errors."""
    global zwylib
    try:
        import zwylib
    except ImportError:
        if show_bulletin:
            BulletinHelper.show_error(locali.get_string("zwylib_not_found"))

def is_zwylib_present() -> bool:
    return zwylib is not None

class CommandVisitor(ast.NodeVisitor):
    """
    Enhanced AST visitor to find commands and their descriptions.
    Analyzes Python's Abstract Syntax Tree (AST) to find potential commands.
    It looks for string literals starting with '.' in comparisons, `startswith`
    calls, inside command-related Enums, dynamic command patterns, and more.
    """
    VALID_COMMAND_RE = re.compile(r"^\.[\w-]{1,20}$")
    INVALID_PATTERNS = [
        r"^\._-$",
        r"^\.[\w-]*\*[\w-]*$",
        r"^\.[\w-]*\+[\w-]*$",
        r"^\.[\w-]*\?[\w-]*$",
    ]

    def __init__(self):
        self.commands: Set[str] = set()
        self.command_descriptions: Dict[str, str] = {}
        self.variables: Dict[str, Any] = {}
        self.current_function = None
        self.has_dynamic_commands = False
        self.default_commands = []
        self.description_commands = []  # Commands found in __description__

    def visit_Assign(self, node: ast.Assign):
        if len(node.targets) == 1 and isinstance(node.targets[0], ast.Name):
            try:
                value = ast.literal_eval(node.value)
                var_name = node.targets[0].id
                self.variables[var_name] = value

                # Handle various command variable patterns
                if var_name in ["DEFAULT_COMMANDS", "COMMANDS", "COMMAND_LIST"] and isinstance(value, list):
                    self.default_commands = value
                    for cmd in value:
                        if isinstance(cmd, str) and self.VALID_COMMAND_RE.match(cmd):
                            self.commands.add(cmd)

                # Handle command dictionaries like {"emojify": [".emojify", ".emoji"]}
                elif var_name in ["DEFAULT_COMMANDS", "COMMANDS"] and isinstance(value, dict):
                    for key, cmd_list in value.items():
                        if isinstance(cmd_list, list):
                            for cmd in cmd_list:
                                if isinstance(cmd, str) and self.VALID_COMMAND_RE.match(cmd):
                                    self.commands.add(cmd)

                # Handle single command variables
                elif var_name in ["DEFAULT_COMMAND", "COMMAND"] and isinstance(value, str):
                    if self.VALID_COMMAND_RE.match(value):
                        self.commands.add(value)

                # Handle __description__ variable
                elif var_name == "__description__" and isinstance(value, str):
                    self._extract_commands_from_text(value)

            except (ValueError, TypeError, AttributeError, SyntaxError):
                pass
        self.generic_visit(node)



    def _add_command(self, value: Any, description: str = None, context: str = None):
        """Add command with improved validation and context awareness."""
        if isinstance(value, str):
            commands_to_check = []

            if context == "startswith_check":
                commands_to_check = [value.strip()]
            elif context == "dict_key":
                # Handle dictionary keys that might be commands
                if value.startswith("."):
                    commands_to_check = [value.strip()]
            elif "/" in value and "." in value:
                parts = value.split("/")
                for part in parts:
                    part = part.strip()
                    if part.startswith("."):
                        commands_to_check.append(part.split()[0])
            else:
                command = value.strip().split(" ")[0]
                if command.startswith("."):
                    commands_to_check.append(command)

            for command in commands_to_check:
                if self.VALID_COMMAND_RE.match(command):
                    is_invalid = any(re.match(pattern, command) for pattern in self.INVALID_PATTERNS)

                    if not is_invalid and not self._is_likely_false_positive(command):
                        self.commands.add(command)
                        if description and command not in self.command_descriptions:
                            self.command_descriptions[command] = description

    def _is_likely_false_positive(self, command: str) -> bool:
        """Check if a command is likely a false positive."""
        # Check for Morse code patterns (dots and dashes only)
        if self._is_morse_code(command):
            return True

        false_positive_patterns = [
            # Single character or very short patterns
            r"^\.up$", r"^\.s$", r"^\.d$", r"^\.x$", r"^\.o$",

            # File extensions - audio
            r"^\.wav$", r"^\.mp3$", r"^\.ogg$", r"^\.opus$", r"^\.m4a$", r"^\.flac$", r"^\.aac$", r"^\.wma$",

            # File extensions - image
            r"^\.gif$", r"^\.jpg$", r"^\.jpeg$", r"^\.png$", r"^\.webp$", r"^\.bmp$", r"^\.tiff$", r"^\.svg$",

            # File extensions - video
            r"^\.mp4$", r"^\.avi$", r"^\.mkv$", r"^\.webm$", r"^\.mov$", r"^\.wmv$", r"^\.flv$", r"^\.m4v$",

            # File extensions - documents
            r"^\.pdf$", r"^\.doc$", r"^\.docx$", r"^\.xls$", r"^\.xlsx$", r"^\.ppt$", r"^\.pptx$",

            # File extensions - code/text
            r"^\.html$", r"^\.css$", r"^\.js$", r"^\.json$", r"^\.xml$", r"^\.py$", r"^\.java$", r"^\.cpp$",
            r"^\.txt$", r"^\.log$", r"^\.tmp$", r"^\.bak$", r"^\.md$", r"^\.yml$", r"^\.yaml$",

            # File extensions - archives
            r"^\.zip$", r"^\.rar$", r"^\.7z$", r"^\.tar$", r"^\.gz$",

            # Telegram entity types and message types
            r"^\.blockquote$", r"^\.bold$", r"^\.chat_photos$", r"^\.code$",
            r"^\.contacts$", r"^\.custom_emoji$", r"^\.document$", r"^\.empty$",
            r"^\.geo$", r"^\.italic$", r"^\.music$", r"^\.my_mentions$",
            r"^\.phone_calls$", r"^\.photo_video$", r"^\.photo_video_document$",
            r"^\.photos$", r"^\.pinned$", r"^\.pre$", r"^\.round_video$",
            r"^\.round_voice$", r"^\.spoiler$", r"^\.strikethrough$",
            r"^\.text_link$", r"^\.underline$", r"^\.url$", r"^\.video$", r"^\.voice$",

            # Common programming/system patterns
            r"^\.class$", r"^\.exe$", r"^\.dll$", r"^\.so$", r"^\.dylib$",
            r"^\.config$", r"^\.ini$", r"^\.conf$", r"^\.properties$",

            # Version patterns
            r"^\.0$", r"^\.1$", r"^\.2$", r"^\.3$", r"^\.4$", r"^\.5$", r"^\.6$", r"^\.7$", r"^\.8$", r"^\.9$",
        ]

        return any(re.match(pattern, command) for pattern in false_positive_patterns)

    def _is_morse_code(self, text: str) -> bool:
        """Check if text looks like Morse code (only dots, dashes, and numbers)."""
        if not text.startswith("."):
            return False

        # Remove the leading dot
        content = text[1:]

        # Morse code patterns: only dots, dashes, and sometimes numbers
        morse_pattern = re.compile(r"^[-\.0-9]*$")

        # Must contain at least one dash or be very short with only dots/numbers
        if morse_pattern.match(content):
            # Additional checks for common Morse patterns
            if len(content) <= 5 and ("-" in content or content.isdigit() or all(c == "." for c in content)):
                return True

        return False

    def _extract_description_from_comment(self, node) -> Optional[str]:
        """Extract description from nearby comments or docstrings."""
        try:
            if hasattr(node, 'lineno'):
                return None
        except:
            return None

    def visit_FunctionDef(self, node: ast.FunctionDef):
        """Visit function definitions to extract commands from docstrings and function names."""
        # Check if function name suggests it handles commands
        if any(keyword in node.name.lower() for keyword in ['command', 'cmd', 'hook', 'handle']):
            # Extract commands from docstring
            if node.body and isinstance(node.body[0], ast.Expr) and isinstance(node.body[0].value, ast.Constant):
                docstring = node.body[0].value.value
                if isinstance(docstring, str):
                    self._extract_commands_from_text(docstring)

        # Special handling for _get_commands_list method
        if node.name == '_get_commands_list':
            self.has_dynamic_commands = True

        self.generic_visit(node)

    def _extract_commands_from_text(self, text: str):
        """Extract commands from any text (docstrings, comments, descriptions)."""
        if not isinstance(text, str):
            return

        # Look for various command patterns in text
        patterns = [
            r'\[(\.[a-zA-Z][\w-]*)\]',      # [.cmd]
            r'\((\.[a-zA-Z][\w-]*)\)',      # (.cmd)
            r'"(\.[a-zA-Z][\w-]*)"',        # ".cmd"
            r"'(\.[a-zA-Z][\w-]*)'",        # '.cmd'
            r'`(\.[a-zA-Z][\w-]*)`',        # `.cmd`
            r'\s(\.[a-zA-Z][\w-]*)\s',      # .cmd (surrounded by spaces)
            r'^(\.[a-zA-Z][\w-]*)\s',       # .cmd at start of line
            r'\s(\.[a-zA-Z][\w-]*)[,\.]',   # .cmd followed by comma or period
            r'команд[аы]?\s+(\.[a-zA-Z][\w-]*)', # "команда .cmd" in Russian
            r'command[s]?\s+(\.[a-zA-Z][\w-]*)', # "command .cmd" in English
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                if self.VALID_COMMAND_RE.match(match) and not self._is_likely_false_positive(match):
                    self.commands.add(match)

    def _is_in_startswith_context(self, node) -> bool:
        """Check if we're in a startswith method call context."""
        parent = getattr(node, 'parent', None)
        if parent and isinstance(parent, ast.Call):
            if isinstance(parent.func, ast.Attribute) and parent.func.attr == 'startswith':
                return True
        return False

    def visit_Compare(self, node: ast.Compare):
        op = node.ops[0]
        if isinstance(op, (ast.Eq, ast.In)):
            description = self._extract_description_from_comment(node)
            for item in [node.left] + node.comparators:
                if isinstance(item, ast.Constant):
                    if not self._is_in_extension_context(item.value) and not self._is_in_telegram_context(item.value):
                        self._add_command(item.value, description)
                elif isinstance(item, ast.Name) and item.id in self.variables:
                    var_value = self.variables[item.id]
                    if not self._is_in_extension_context(var_value) and not self._is_in_telegram_context(var_value):
                        self._add_command(var_value, description)
                elif isinstance(item, (ast.List, ast.Tuple)):
                    for element in item.elts:
                        if isinstance(element, ast.Constant):
                            if not self._is_in_extension_context(element.value) and not self._is_in_telegram_context(element.value):
                                self._add_command(element.value, description)
                elif isinstance(item, ast.Dict):
                    # Handle dictionary literals in comparisons (but check if it's a Morse dictionary)
                    is_morse_dict = self._is_morse_dictionary(item)
                    for key in item.keys:
                        if isinstance(key, ast.Constant) and isinstance(key.value, str):
                            if (not self._is_in_extension_context(key.value) and
                                not self._is_in_telegram_context(key.value) and
                                not is_morse_dict):
                                self._add_command(key.value, description, context="dict_key")
        self.generic_visit(node)

    def _is_in_extension_context(self, value: Any) -> bool:
        """Check if the value is likely a file extension rather than a command."""
        if isinstance(value, str):
            if value.startswith(".") and len(value) <= 6 and value.count(".") == 1:
                extension = value[1:].lower()
                common_extensions = {
                    # Audio
                    "mp3", "wav", "ogg", "opus", "m4a", "flac", "aac", "wma",
                    # Video
                    "mp4", "avi", "mkv", "webm", "mov", "wmv", "flv", "m4v",
                    # Image
                    "gif", "jpg", "jpeg", "png", "webp", "bmp", "tiff", "svg",
                    # Document
                    "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
                    # Code/Text
                    "html", "css", "js", "json", "xml", "py", "java", "cpp",
                    "txt", "log", "tmp", "bak", "md", "yml", "yaml",
                    # Archive
                    "zip", "rar", "7z", "tar", "gz",
                    # System
                    "exe", "dll", "so", "dylib", "class"
                }
                return extension in common_extensions
        return False

    def _is_in_telegram_context(self, value: Any) -> bool:
        """Check if the value is likely a Telegram entity type or message type."""
        if isinstance(value, str) and value.startswith("."):
            telegram_types = {
                "blockquote", "bold", "chat_photos", "code", "contacts",
                "custom_emoji", "document", "empty", "geo", "italic", "music",
                "my_mentions", "phone_calls", "photo_video", "photo_video_document",
                "photos", "pinned", "pre", "round_video", "round_voice",
                "spoiler", "strikethrough", "text_link", "underline", "url",
                "video", "voice"
            }
            return value[1:].lower() in telegram_types
        return False

    def visit_Dict(self, node: ast.Dict):
        """Handle dictionary literals that might contain commands as keys."""
        # Check if this looks like a Morse code dictionary by examining keys
        is_morse_dict = self._is_morse_dictionary(node)

        for key, value in zip(node.keys, node.values):
            if isinstance(key, ast.Constant) and isinstance(key.value, str):
                # Check if key looks like a command (but skip if it's a Morse dictionary)
                if key.value.startswith(".") and not is_morse_dict:
                    self._add_command(key.value, None, context="dict_key")
                # Also check if value contains commands (for nested structures)
                elif isinstance(value, (ast.List, ast.Tuple)):
                    for element in value.elts:
                        if isinstance(element, ast.Constant) and isinstance(element.value, str):
                            if element.value.startswith(".") and not self._is_morse_code(element.value):
                                self._add_command(element.value, None)
        self.generic_visit(node)

    def _is_morse_dictionary(self, dict_node: ast.Dict) -> bool:
        """Check if a dictionary looks like it contains Morse code mappings."""
        morse_key_count = 0
        total_dot_keys = 0

        for key in dict_node.keys:
            if isinstance(key, ast.Constant) and isinstance(key.value, str):
                if key.value.startswith("."):
                    total_dot_keys += 1
                    if self._is_morse_code(key.value):
                        morse_key_count += 1

        # If more than 70% of dot-starting keys look like Morse code, consider it a Morse dictionary
        if total_dot_keys > 5 and morse_key_count / total_dot_keys > 0.7:
            return True

        return False

    def visit_BoolOp(self, node: ast.BoolOp):
        """Handle boolean operations like 'or' that combine multiple startswith checks."""
        if isinstance(node.op, ast.Or):
            startswith_commands = []
            for value in node.values:
                if (isinstance(value, ast.Call) and
                    isinstance(value.func, ast.Attribute) and
                    value.func.attr == 'startswith' and
                    value.args and
                    isinstance(value.args[0], ast.Constant)):
                    cmd_value = value.args[0].value
                    if isinstance(cmd_value, str):
                        # Handle both ".cmd " and ".cmd" patterns
                        cmd = cmd_value.rstrip() if cmd_value.endswith(" ") else cmd_value
                        startswith_commands.append(cmd)

            for cmd in startswith_commands:
                self._add_command(cmd, None, context="startswith_check")

        self.generic_visit(node)

    def visit_Call(self, node: ast.Call):
        # Handle startswith() calls
        if isinstance(node.func, ast.Attribute) and node.func.attr == 'startswith':
            if node.args:
                description = self._extract_description_from_comment(node)
                arg = node.args[0]
                if isinstance(arg, ast.Constant):
                    self._add_command(arg.value, description, context="startswith_check")
                elif isinstance(arg, ast.Name) and arg.id in self.variables:
                    self._add_command(self.variables[arg.id], description, context="startswith_check")
                # Handle tuple/list arguments in startswith
                elif isinstance(arg, (ast.Tuple, ast.List)):
                    for element in arg.elts:
                        if isinstance(element, ast.Constant):
                            self._add_command(element.value, description, context="startswith_check")

        # Handle get_setting() calls for dynamic commands
        elif (isinstance(node.func, ast.Attribute) and
              node.func.attr == 'get_setting' and
              node.args and
              isinstance(node.args[0], ast.Constant)):
            setting_key = node.args[0].value
            if setting_key in ["custom_command", "command_alias", "trigger_command"]:
                self.has_dynamic_commands = True
                if len(node.args) > 1 and isinstance(node.args[1], ast.Constant):
                    default_cmd = node.args[1].value
                    if isinstance(default_cmd, str) and self.VALID_COMMAND_RE.match(default_cmd):
                        self.commands.add(default_cmd)

        self.generic_visit(node)

    def visit_ClassDef(self, node: ast.ClassDef):
        """
        Visits class definitions to find commands defined in Enums,
        which is a common pattern in more complex plugins like adminTools.
        """
        is_enum = False
        for base in node.bases:
            if isinstance(base, ast.Name) and 'Enum' in base.id:
                is_enum = True
                break

        if is_enum and self._is_command_enum(node):
            for item in node.body:
                if isinstance(item, ast.Assign) and len(item.targets) == 1:
                    if isinstance(item.value, ast.Constant) and isinstance(item.value.value, str):
                        command_stem = item.value.value
                        # Try both with and without dot prefix
                        full_command = f".{command_stem}"
                        if self.VALID_COMMAND_RE.match(full_command) and not self._is_likely_false_positive(full_command):
                            self.commands.add(full_command)
                        # Also check if the value itself is already a command
                        elif self.VALID_COMMAND_RE.match(command_stem) and not self._is_likely_false_positive(command_stem):
                            self.commands.add(command_stem)

        self.generic_visit(node)

    def _is_command_enum(self, node: ast.ClassDef) -> bool:
        """Check if an Enum class is likely to contain commands rather than other data."""
        class_name = node.name.lower()

        non_command_enum_patterns = [
            'searchfilter', 'tlentitytype', 'entitytype', 'filter', 'type',
            'status', 'state', 'mode', 'level', 'priority', 'category'
        ]

        if any(pattern in class_name for pattern in non_command_enum_patterns):
            return False

        command_like_values = 0
        total_values = 0

        for item in node.body:
            if isinstance(item, ast.Assign) and len(item.targets) == 1:
                if isinstance(item.value, ast.Constant) and isinstance(item.value.value, str):
                    total_values += 1
                    value = item.value.value
                    if len(value) <= 15 and re.match(r'^[a-zA-Z][a-zA-Z0-9_]*$', value):
                        command_like_values += 1

        if total_values > 0 and command_like_values / total_values < 0.7:
            return False

        return True


class CommandHarvesterPlugin(BasePlugin):
    """
    Plugin class to harvest and display commands from other plugins.
    """
    def __init__(self):
        super().__init__()
        self.command_cache = CommandCache()
        self.command_stats = None

    def on_plugin_load(self):
        """Called when the plugin is loaded."""
        try:
            self.add_on_send_message_hook()
            self.command_stats = CommandStatistics(self)

            import_zwylib()
            if is_zwylib_present():
                zwylib.add_autoupdater_task(__id__, AUTOUPDATE_CHANNEL_ID, AUTOUPDATE_CHANNEL_USERNAME, AUTOUPDATE_MESSAGE_ID)
        except Exception:
            pass

    def on_plugin_unload(self):
        """Called when the plugin is unloaded."""
        if is_zwylib_present():
            zwylib.remove_autoupdater_task(__id__)

    def create_settings(self) -> List[Any]:
        """Creates the settings UI for the plugin."""
        try:
            return [
                Header(text="Command List"),
                Input(
                    key="command_alias",
                    text="Команда для вызова",
                    icon="msg_edit",
                    default=DEFAULT_CMD,
                    subtext="Команда, по которой будет выводиться список всех команд"
                ),
                Switch(
                    key="use_blockquote",
                    text="Использовать цитату",
                    icon="menu_quote_specific",
                    subtext="Отображать список команд в виде сворачиваемой цитаты",
                    default=True
                ),
                Divider(),
                Header(text="Кэширование"),
                Switch(
                    key="enable_cache",
                    text="Включить кэширование",
                    icon="menu_storage_path",
                    subtext="Кэшировать результаты сканирования для ускорения работы",
                    default=True
                ),
                Input(
                    key="cache_duration",
                    text="Время кэша (мин)",
                    icon="menu_views_recent",
                    default="5",
                    subtext="Время жизни кэша в минутах (1-60)"
                ),
                Text(
                    text="Очистить кэш",
                    icon="msg_delete",
                    on_click=self._clear_cache_setting
                ),
                Divider(),
                Header(text="Отображение"),
                Selector(
                    key="sort_mode",
                    text="Сортировка команд",
                    icon="menu_tag_filter",
                    default=0,
                    items=["По алфавиту", "По плагинам", "По популярности"]
                ),
                Switch(
                    key="show_descriptions",
                    text="Показывать описания",
                    icon="msg_info",
                    subtext="Отображать описания команд, если доступны",
                    default=False
                ),
                Switch(
                    key="show_plugin_stats",
                    text="Показывать статистику",
                    icon="msg_stats",
                    subtext="Отображать количество команд для каждого плагина",
                    default=True
                ),
                Switch(
                    key="debug_mode",
                    text="Режим отладки",
                    icon="msg_log",
                    subtext="Показывать дополнительную информацию об обнаружении команд",
                    default=False
                ),
                Divider(text="Этот плагин анализирует код других плагинов для поиска команд")
            ]
        except Exception:
            return [Divider(text="Ошибка загрузки настроек")]

    def _toggle_cache(self, new_value: bool):
        """Callback to toggle caching."""
        if not new_value:
            self.command_cache.invalidate_cache()

    def _update_cache_duration(self, new_value: str):
        """Callback to update cache duration."""
        try:
            duration = int(new_value)
            if 1 <= duration <= 60:
                self.command_cache.cache_duration = duration * 60
        except ValueError:
            pass

    def _clear_cache_setting(self, view):
        """Clear cache from settings."""
        self.command_cache.invalidate_cache()
        run_on_ui_thread(lambda: BulletinHelper.show_success("Кэш очищен"))

    def _get_plugin_id_from_file(self, file_path: str) -> Optional[str]:
        """Extracts the __id__ from a plugin file without executing it."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()
            tree = ast.parse(content, filename=file_path)
            for node in ast.walk(tree):
                if (isinstance(node, ast.Assign) and len(node.targets) == 1 and
                        isinstance(node.targets[0], ast.Name) and node.targets[0].id == '__id__'):
                    return ast.literal_eval(node.value)
        except Exception:
            pass
        return None

    def _get_all_commands(self) -> Dict[str, Dict[str, Any]]:
        """Scans all plugin files and extracts commands with caching support."""
        if self.get_setting("enable_cache", True):
            cached_commands = self.command_cache.get_cached_commands()
            if cached_commands:
                return cached_commands
        plugin_data: Dict[str, Dict[str, Any]] = {}
        loaded_plugins_map = {p.id: p for p in plugins_manager.PluginsManager._plugins.values()}
        
        pm_instance = plugins_manager.PluginsManager
        if not hasattr(pm_instance, '_plugins_dir') or not pm_instance._plugins_dir:
            raise ValueError(locali.get_string("dir_error"))
        plugins_dir = pm_instance._plugins_dir

        for filename in os.listdir(plugins_dir):
            if not filename.endswith(('.py', '.plugin')):
                continue

            file_path = os.path.join(plugins_dir, filename)

            try:
                plugin_id_from_file = self._get_plugin_id_from_file(file_path)
                if not plugin_id_from_file or plugin_id_from_file not in loaded_plugins_map:
                    continue
                if plugin_id_from_file == __id__:
                    continue

                plugin_obj = loaded_plugins_map[plugin_id_from_file]
                with open(file_path, "r", encoding="utf-8") as f:
                    content = f.read()

                tree = ast.parse(content, filename=file_path)
                visitor = CommandVisitor()
                visitor.visit(tree)

                dynamic_commands = self._get_dynamic_commands(plugin_obj, visitor)
                all_commands = visitor.commands.union(dynamic_commands)

                if self.get_setting("debug_mode", False) and plugin_obj.name == "Media Downloader":
                    debug_info = f"🔍 Отладка для {plugin_obj.name}:\n"
                    debug_info += f"  Статические команды: {sorted(list(visitor.commands))}\n"
                    debug_info += f"  Динамические команды: {sorted(list(dynamic_commands))}\n"
                    debug_info += f"  Итого команд: {len(all_commands)}\n"

                if all_commands:
                    plugin_data[plugin_obj.name] = {
                        "commands": sorted(list(all_commands)),
                        "descriptions": visitor.command_descriptions,
                        "enabled": plugin_obj.enabled,
                        "has_dynamic": visitor.has_dynamic_commands or bool(dynamic_commands)
                    }
            except Exception:
                continue

        if self.get_setting("enable_cache", True):
            self.command_cache.cache_commands(plugin_data)

        return plugin_data

    def _get_dynamic_commands(self, plugin_obj, visitor) -> Set[str]:
        """Try to extract dynamic commands from plugin instance."""
        dynamic_commands = set()

        try:
            # Check for _get_commands_list method
            if hasattr(plugin_obj, '_get_commands_list'):
                try:
                    commands_list = plugin_obj._get_commands_list()
                    if isinstance(commands_list, list):
                        for cmd in commands_list:
                            if isinstance(cmd, str) and CommandVisitor.VALID_COMMAND_RE.match(cmd):
                                is_invalid = any(re.match(pattern, cmd) for pattern in CommandVisitor.INVALID_PATTERNS)
                                if not is_invalid and not visitor._is_likely_false_positive(cmd):
                                    dynamic_commands.add(cmd)
                    elif not commands_list and hasattr(plugin_obj, 'get_setting'):
                        no_command_mode = plugin_obj.get_setting("no_command_mode", False)
                        if no_command_mode:
                            dynamic_commands.add("*без команд*")
                        else:
                            # Try common default command patterns
                            for default_key in ["custom_command", "command_alias", "trigger_command"]:
                                try:
                                    custom_cmd = plugin_obj.get_setting(default_key, None)
                                    if custom_cmd and isinstance(custom_cmd, str) and CommandVisitor.VALID_COMMAND_RE.match(custom_cmd):
                                        is_invalid = any(re.match(pattern, custom_cmd) for pattern in CommandVisitor.INVALID_PATTERNS)
                                        if not is_invalid and not visitor._is_likely_false_positive(custom_cmd):
                                            dynamic_commands.add(custom_cmd)
                                except:
                                    pass
                except:
                    pass

            # Check for common setting keys that might contain commands
            if hasattr(plugin_obj, 'get_setting'):
                setting_keys = [
                    "custom_command", "command_alias", "trigger_command",
                    "main_command", "base_command", "cmd", "command"
                ]

                for setting_key in setting_keys:
                    try:
                        cmd_value = plugin_obj.get_setting(setting_key, None)
                        if cmd_value and isinstance(cmd_value, str) and CommandVisitor.VALID_COMMAND_RE.match(cmd_value):
                            is_invalid = any(re.match(pattern, cmd_value) for pattern in CommandVisitor.INVALID_PATTERNS)
                            if not is_invalid and not visitor._is_likely_false_positive(cmd_value):
                                dynamic_commands.add(cmd_value)
                    except:
                        pass

            # Add commands from visitor's default_commands
            if visitor.default_commands:
                for cmd in visitor.default_commands:
                    if isinstance(cmd, str) and CommandVisitor.VALID_COMMAND_RE.match(cmd):
                        is_invalid = any(re.match(pattern, cmd) for pattern in CommandVisitor.INVALID_PATTERNS)
                        if not is_invalid and not visitor._is_likely_false_positive(cmd):
                            dynamic_commands.add(cmd)

            # Extract commands from plugin description if available
            if hasattr(plugin_obj, '__description__'):
                try:
                    description = getattr(plugin_obj, '__description__', '')
                    if isinstance(description, str):
                        visitor._extract_commands_from_text(description)
                except:
                    pass

        except Exception:
            pass

        return dynamic_commands

    def _format_command_list(self, all_commands: Dict[str, Dict[str, Any]]) -> str:
        """Formats the collected commands into a readable string with options."""
        if not all_commands:
            return locali.get_string("no_cmds_found")

        total_plugins = len(all_commands)
        total_commands = sum(len(data["commands"]) for data in all_commands.values())

        cache_info = ""
        if self.get_setting("enable_cache", True) and self.command_cache.is_cache_valid():
            time_ago = int((time.time() - self.command_cache.cache_timestamp) / 60)
            if locali.language == "ru":
                time_unit = "мин"
            elif locali.language == "uk":
                time_unit = "хв"
            else:
                time_unit = "min"
            cache_info = f"\n{locali.get_string('cached_data_info').format(time_ago=f'{time_ago} {time_unit}')}\n"

        if locali.language == "ru":
            message = f"📜 **Список обнаруженных команд ({total_plugins} плагинов, {_pluralize_commands(total_commands)}):**\n\n" + cache_info
        else:
            message = locali.get_string("cmd_list_header").format(plugin_count=total_plugins, command_count=total_commands) + cache_info

        sort_mode = self.get_setting("sort_mode", 0)
        show_stats = self.get_setting("show_plugin_stats", True)

        if sort_mode == 0:
            sorted_plugins = sorted(all_commands.items(), key=lambda item: item[0].lower())
        elif sort_mode == 1:
            sorted_plugins = sorted(all_commands.items(), key=lambda item: (not item[1]["enabled"], item[0].lower()))
        else:
            if self.command_stats:
                def get_plugin_usage(plugin_data):
                    plugin_name, data = plugin_data
                    total_usage = sum(self.command_stats.get_usage_count(cmd) for cmd in data["commands"])
                    return total_usage
                sorted_plugins = sorted(all_commands.items(), key=get_plugin_usage, reverse=True)
            else:
                sorted_plugins = sorted(all_commands.items(), key=lambda item: item[0].lower())

        show_descriptions = self.get_setting("show_descriptions", False)

        for plugin_name, data in sorted_plugins:
            commands = data["commands"]
            descriptions = data.get("descriptions", {})
            is_enabled = data["enabled"]
            has_dynamic = data.get("has_dynamic", False)
            status_icon = "✅" if is_enabled else "❌"
            status_text = locali.get_string("plugin_status_on") if is_enabled else locali.get_string("plugin_status_off")
            status_str = f"||[{status_icon} {status_text}]||"

            dynamic_indicator = " 🔄" if has_dynamic else ""
            plugin_header = f"**🧩 {plugin_name}**{dynamic_indicator} {status_str}"

            if show_stats:
                cmd_count = len(commands)
                plugin_header += f" `({_pluralize_commands(cmd_count)})`"

            message += f"{plugin_header}\n"

            formatted_commands = []
            for cmd in commands:
                if cmd == "*без команд*":
                    formatted_commands.append("*режим без команд*")
                else:
                    formatted_commands.append(cmd)

            if sort_mode == 2 and self.command_stats:
                cmd_usage = [(cmd, self.command_stats.get_usage_count(cmd)) for cmd in formatted_commands]
                cmd_usage.sort(key=lambda x: x[1], reverse=True)
                if show_descriptions:
                    commands_with_info = []
                    for cmd, usage in cmd_usage:
                        cmd_str = f"{cmd}"
                        if cmd in descriptions:
                            cmd_str += f" - {descriptions[cmd]}"
                        commands_with_info.append(cmd_str)
                    commands_str = '` `'.join(commands_with_info)
                else:
                    commands_sorted_by_usage = [cmd for cmd, usage in cmd_usage]
                    commands_str = '` `'.join(commands_sorted_by_usage)
            else:
                if show_descriptions:
                    commands_with_desc = []
                    for cmd in sorted(formatted_commands):
                        cmd_str = cmd
                        if cmd in descriptions:
                            cmd_str += f" - {descriptions[cmd]}"
                        commands_with_desc.append(cmd_str)
                    commands_str = '` `'.join(commands_with_desc)
                else:
                    commands_str = '` `'.join(sorted(formatted_commands))

            message += f"`{commands_str}`\n\n"

        message += locali.get_string("cmd_note")
        if locali.language == "ru":
            message += f"\n*🔄 Плагины с динамическими командами могут изменять команды в настройках.*"
        elif locali.language == "uk":
            message += f"\n*🔄 Плагіни з динамічними командами можуть змінювати команди в налаштуваннях.*"
        else:
            message += f"\n*🔄 Plugins with dynamic commands can change commands in settings.*"

        if self.get_setting("debug_mode", False):
            message += f"\n\n🔧 **Режим отладки включен** - улучшенное обнаружение команд активно"

        message += f"\n\n{locali.get_string('search_help').format(cmd=self.get_setting('command_alias', DEFAULT_CMD))}"

        if locali.language == "ru":
            message += f"\n*Используйте `.cmdstats` для просмотра статистики использования команд.*"
        elif locali.language == "uk":
            message += f"\n*Використовуйте `.cmdstats` для перегляду статистики використання команд.*"
        else:
            message += f"\n*Use `.cmdstats` to view command usage statistics.*"

        return message

    def _format_command_stats(self) -> str:
        """Formats command usage statistics into a readable string."""
        if not self.command_stats:
            return locali.get_string("stats_unavailable")

        stats_summary = self.command_stats.get_stats_summary()

        if stats_summary["total_commands"] == 0:
            return locali.get_string("stats_no_usage")

        message = locali.get_string("stats_header") + "\n\n"

        total_commands = stats_summary["total_commands"]
        total_usage = stats_summary["total_usage"]

        message += locali.get_string("stats_general") + "\n"

        if locali.language == "ru":
            message += f"{locali.get_string('stats_unique_commands')} {_pluralize_commands(total_commands)}\n"
            message += f"{locali.get_string('stats_total_usage')} {_pluralize_usages(total_usage)}\n\n"
        else:
            message += f"{locali.get_string('stats_unique_commands')} {total_commands}\n"
            message += f"{locali.get_string('stats_total_usage')} {total_usage}\n\n"

        if stats_summary["most_used"]:
            most_used_cmd, most_used_count = stats_summary["most_used"]
            message += locali.get_string("stats_most_popular") + "\n"
            message += f"`{most_used_cmd}`\n\n"

        popular_commands = stats_summary["popular_commands"]
        if popular_commands:
            count = min(10, len(popular_commands))
            message += locali.get_string("stats_top_commands").format(count=count) + "\n"

            for i, (cmd, count) in enumerate(popular_commands, 1):
                message += f"{i}) `{cmd}`\n"

        return message

    def _handle_search_command(self, params: Any, search_query: str) -> HookResult:
        """Handle search functionality for commands."""
        try:
            all_commands = self._get_all_commands()
            search_results = {}

            search_lower = search_query.lower()

            for plugin_name, data in all_commands.items():
                matching_commands = []
                for cmd in data["commands"]:
                    if (search_lower in cmd.lower() or
                        search_lower in plugin_name.lower()):
                        matching_commands.append(cmd)

                if matching_commands:
                    search_results[plugin_name] = {
                        "commands": matching_commands,
                        "descriptions": data.get("descriptions", {}),
                        "enabled": data["enabled"]
                    }

            if search_results:
                response_text = locali.get_string("search_results").format(query=search_query)
                response_text += self._format_command_list(search_results)
            else:
                response_text = locali.get_string("search_no_results").format(query=search_query)

            if not hasattr(params, "entities") or params.entities is None:
                params.entities = ArrayList()
            else:
                params.entities.clear()

            if self.get_setting("use_blockquote", True):
                parsed_for_quote = parse_markdown(response_text)
                params.message = parsed_for_quote.text

                entity = TLRPC.TL_messageEntityBlockquote()
                entity.collapsed = True
                entity.offset = 0
                entity.length = len(params.message.encode('utf_16_le')) // 2
                params.entities.add(entity)
            else:
                parsed = parse_markdown(response_text)
                params.message = parsed.text
                for entity in parsed.entities:
                    params.entities.add(entity.to_tlrpc_object())

            return HookResult(strategy=HookStrategy.MODIFY_FINAL, params=params)

        except Exception:
            return HookResult()

    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        """Hook to intercept outgoing messages and handle the command."""
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()

        message_text = params.message.strip()

        if self.command_stats and message_text.startswith('.'):
            command = message_text.split()[0]
            if re.match(r"^\.[\w-]+$", command):
                self.command_stats.increment_usage(command)

        trigger_command = self.get_setting("command_alias", DEFAULT_CMD)

        if message_text == ".cmdstats":
            try:
                if not hasattr(params, "entities") or params.entities is None:
                    params.entities = ArrayList()
                else:
                    params.entities.clear()

                response_text = self._format_command_stats()

                if self.get_setting("use_blockquote", True):
                    parsed_for_quote = parse_markdown(response_text)
                    params.message = parsed_for_quote.text

                    entity = TLRPC.TL_messageEntityBlockquote()
                    entity.collapsed = True
                    entity.offset = 0
                    entity.length = len(params.message.encode('utf_16_le')) // 2
                    params.entities.add(entity)
                else:
                    parsed = parse_markdown(response_text)
                    params.message = parsed.text
                    for entity in parsed.entities:
                        params.entities.add(entity.to_tlrpc_object())

                return HookResult(strategy=HookStrategy.MODIFY_FINAL, params=params)

            except Exception:
                error_text = locali.get_string("stats_error") + "\n\n" + f"```\n{traceback.format_exc()}\n```"
                parsed_error = parse_markdown(error_text)
                params.message = parsed_error.text
                if hasattr(params, "entities") and params.entities is not None:
                    params.entities.clear()
                for entity in parsed_error.entities:
                    params.entities.add(entity.to_tlrpc_object())
                return HookResult(strategy=HookStrategy.MODIFY_FINAL, params=params)

        if message_text.startswith(f"{trigger_command} "):
            search_query = message_text[len(trigger_command):].strip()
            if search_query:
                return self._handle_search_command(params, search_query)

        if message_text == trigger_command:
            try:
                all_commands = self._get_all_commands()

                if not hasattr(params, "entities") or params.entities is None:
                    params.entities = ArrayList()
                else:
                    params.entities.clear()
                
                response_text = self._format_command_list(all_commands)

                if self.get_setting("use_blockquote", True):
                    parsed_for_quote = parse_markdown(response_text)
                    params.message = parsed_for_quote.text

                    entity = TLRPC.TL_messageEntityBlockquote()
                    entity.collapsed = True
                    entity.offset = 0
                    entity.length = len(params.message.encode('utf_16_le')) // 2
                    params.entities.add(entity)
                else:
                    parsed = parse_markdown(response_text)
                    params.message = parsed.text
                    for entity in parsed.entities:
                        params.entities.add(entity.to_tlrpc_object())

                return HookResult(strategy=HookStrategy.MODIFY_FINAL, params=params)

            except Exception:
                error_text = locali.get_string("error_collecting_cmds") + f"```\n{traceback.format_exc()}\n```"
                parsed_error = parse_markdown(error_text)
                params.message = parsed_error.text
                if hasattr(params, "entities") and params.entities is not None:
                    params.entities.clear()
                for entity in parsed_error.entities:
                    params.entities.add(entity.to_tlrpc_object())
                return HookResult(strategy=HookStrategy.MODIFY_FINAL, params=params)

        return HookResult()

plugin_instance = CommandHarvesterPlugin()