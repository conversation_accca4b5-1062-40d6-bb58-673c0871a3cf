/*
 *  Copyright (c) 2021 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

/*
 * This file contains definitions that are common to the IvfFileReader and
 * IvfFileWriter classes.
 */

#ifndef MODULES_VIDEO_CODING_UTILITY_IVF_DEFINES_H_
#define MODULES_VIDEO_CODING_UTILITY_IVF_DEFINES_H_

#include <stddef.h>

namespace webrtc {
constexpr size_t kIvfHeaderSize = 32;
}  // namespace webrtc

#endif  // MODULES_VIDEO_CODING_UTILITY_IVF_DEFINES_H_
