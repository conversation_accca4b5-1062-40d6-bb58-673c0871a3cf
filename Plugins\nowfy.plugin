import requests
import base64
import os
import time
import threading
import uuid
from datetime import datetime
from io import BytesIO
from java.util import Locale, ArrayList
from java.io import File
from PIL import Image, ImageFilter, ImageDraw, ImageFont, ImageOps
from android.content import Intent
from android.net import <PERSON><PERSON>
from org.telegram.messenger import ApplicationLoader
from org.telegram.ui.ActionBar import AlertDialog, BaseFragment
from ui.settings import Header, Divider, Input, Switch, Selector, Text
from ui.alert import AlertDialogBuilder
from ui.bulletin import BulletinHelper
from client_utils import get_send_messages_helper, run_on_queue, get_last_fragment
from android_utils import run_on_ui_thread
from markdown_utils import parse_markdown
from com.exteragram.messenger.plugins import PluginsController
from com.exteragram.messenger.plugins.ui import PluginSettingsActivity
from base_plugin import BasePlugin, HookResult, HookStrategy, MenuItemData, MenuItemType


# Powered by @AGeekApple - Dotted Production
# nowFy Plugin for exteraGram by @ApplePlugins

__id__ = "nowfy"
__name__ = "nowFy"
__description__ = (
    "Shows and controls the current playing Spotify track with a styled preview.\n\n"
    "How to use:\n"
    "• Use .now to show current playing track\n"
    "• Use .fm to show the current scrobbling track\n"
    "• Use .help for instructions\n"
    "• Use .use or .myfm to see all available commands"
)
__author__ = "@AGeekApple, @exteraDev"
__version__ = "1.0.3"
__min_version__ = "11.12.0"
__icon__ = "ApplePlugins/10"

# Langs
TRANSLATIONS = {
    "command_help_like": {
        "en": "Use .like to like/unlike the current track or .like <number> to like a track from your recent search.",
        "pt": "Use .like para curtir/descurtir a música atual ou .like <número> para curtir uma música da sua busca recente.",
        "es": "Use .like para dar/quitar me gusta a la pista actual o .like <número> para dar me gusta a una pista de su búsqueda reciente.",
        "ru": "Используйте .like чтобы добавить/удалить текущий трек в избранное или .like <номер> чтобы добавить трек из последнего поиска в избранное.",
        "fr": "Utilisez .like pour aimer/ne plus aimer la piste actuelle ou .like <numéro> pour aimer une piste de votre recherche récente."
    },
    "track_unlike_success": {
        "en": "Track removed from Liked Songs",
        "pt": "Música removida das Curtidas",
        "es": "Pista eliminada de Canciones Favoritas",
        "ru": "Трек удален из любимых песен",
        "fr": "Morceau retiré des titres likés"
    },
    "track_added_to_queue": {
        "en": "{title} - {artists} added to queue",
        "pt": "{title} - {artists} adicionada à fila",
        "es": "{title} - {artists} añadida a la cola",
        "ru": "{title} - {artists} добавлен в очередь",
        "fr": "{title} - {artists} ajouté à la file d'attente"
    },
    "mix_created_success": {
        "en": "Playlist '{name}' created successfully!",
        "pt": "Playlist '{name}' criada com sucesso!",
        "es": "¡Lista de reproducción '{name}' creada con éxito!",
        "ru": "Плейлист '{name}' успешно создан!",
        "fr": "Playlist '{name}' créée avec succès !"
    },
    "shuffle_enabled": {
        "en": "Shuffle mode enabled",
        "pt": "Modo aleatório ativado",
        "es": "Modo aleatorio activado",
        "ru": "Режим перемешивания включен",
        "fr": "Mode aléatoire activé"
    },
    "shuffle_disabled": {
        "en": "Shuffle mode disabled",
        "pt": "Modo aleatório desativado",
        "es": "Modo aleatorio desactivado",
        "ru": "Режим перемешивания выключен",
        "fr": "Mode aléatoire désactivé"
    },
    "repeat_off": {
        "en": "Repeat: disabled",
        "pt": "Repetição: desativada",
        "es": "Repetición: desactivada",
        "ru": "Повтор: выключен",
        "fr": "Répétition: désactivée"
    },
    "repeat_context": {
        "en": "Repeat: playlist/album enabled",
        "pt": "Repetição: playlist/álbum ativada",
        "es": "Repetición: playlist/álbum activada",
        "ru": "Повтор: плейлист/альбом включен",
        "fr": "Répétition: playlist/album activée"
    },
    "repeat_track": {
        "en": "Repeat: current track enabled",
        "pt": "Repetição: música atual ativada",
        "es": "Repetición: pista actual activada",
        "ru": "Повтор: текущий трек включен",
        "fr": "Répétition: piste actuelle activée"
    },
    "error_like_exception": {
        "en": "Error liking track: {}",
        "pt": "Erro ao curtir a música: {}",
        "es": "Error al dar me gusta a la pista: {}",
        "ru": "Ошибка при добавлении трека в избранное: {}",
        "fr": "Erreur lors de l'ajout du morceau aux favoris: {}"
    },
    "error_unauthorized": {
        "en": "Unauthorized. Please check your Spotify credentials.",
        "pt": "Não autorizado. Verifique suas credenciais do Spotify.",
        "es": "No autorizado. Verifique sus credenciales de Spotify.",
        "ru": "Не авторизован. Проверьте учетные данные Spotify.",
        "fr": "Non autorisé. Veuillez vérifier vos identifiants Spotify."
    },
    "error_like_failed": {
        "en": "Failed to like the track. Please try again.",
        "pt": "Falha ao curtir a música. Tente novamente.",
        "es": "No se pudo dar me gusta a la pista. Inténtelo de nuevo.",
        "ru": "Не удалось добавить трек в избранное. Попробуйте еще раз.",
        "fr": "Impossible d'ajouter le morceau aux favoris. Veuillez réessayer."
    },
    "bulletin_like_success": {
        "en": "Track added to your Liked Songs!",
        "pt": "Música adicionada às suas Músicas Curtidas!",
        "es": "¡Pista añadida a tus Canciones Favoritas!",
        "ru": "Трек добавлен в ваши любимые песни!",
        "fr": "Morceau ajouté à vos titres likés !"
    },
    "liked_track": {
        "en": "Added to Liked Songs",
        "pt": "Adicionada às Músicas Curtidas",
        "es": "Añadida a Canciones Favoritas",
        "ru": "Добавлено в любимые песни",
        "fr": "Ajouté aux titres likés"
    },
    "customfm_settings_header": {
        "en": "CustomFM Theme Settings",
        "pt": "Configurações do Tema CustomFM",
        "es": "Configuración del Tema CustomFM",
        "ru": "Настройки темы CustomFM",
        "fr": "Paramètres du thème CustomFM"
    },
    "customfm_use_background": {
        "en": "Cover BG",
        "pt": "Capa de Fundo",
        "es": "Fondo Álbum",
        "ru": "Фон Обложки",
        "fr": "Fond Album"
    },
    "customfm_background_color": {
        "en": "Background Color (hex)",
        "pt": "Cor do Fundo (hex)",
        "es": "Color de Fondo (hex)",
        "ru": "Цвет фона (hex)",
        "fr": "Couleur de fond (hex)"
    },
    "customfm_text_color": {
        "en": "Text Color (hex)",
        "pt": "Cor do Texto (hex)",
        "es": "Color del Texto (hex)",
        "ru": "Цвет текста (hex)",
        "fr": "Couleur du texte (hex)"
    },
    "reset_customfm_settings": {
        "en": "Reset CustomFM",
        "pt": "Redefinir CustomFM",
        "es": "Restablecer CustomFM",
        "ru": "Сброс CustomFM",
        "fr": "Réinit. CustomFM"
    },
    "settings_reset_success": {
        "en": "CustomFM reset",
        "pt": "CustomFM redefinido",
        "es": "CustomFM restablecido",
        "ru": "CustomFM сброшен",
        "fr": "CustomFM réinitialisé"
    },
    "spotify_link_text": {
        "en": "Open on Spotify",
        "pt": "Abrir no Spotify",
        "es": "Abrir en Spotify",
        "ru": "Открыть в Spotify",
        "fr": "Ouvrir sur Spotify"
    },
    "lastfm_profile": {
        "en": "Last.FM Profile",
        "pt": "Perfil Last.FM",
        "es": "Perfil Last.FM",
        "ru": "Профиль Last.FM",
        "fr": "Profil Last.FM"
    },
    "lastfm_commands_title": {
        "en": "Last.FM Commands",
        "pt": "Comandos Last.FM",
        "es": "Comandos Last.FM",
        "ru": "Команды Last.FM",
        "fr": "Commandes Last.FM"
    },
    "lastfm_commands_description": {
        "en": "Commands to interact with Last.FM service",
        "pt": "Comandos para interagir com o serviço Last.FM",
        "es": "Comandos para interactuar con el servicio Last.FM",
        "ru": "Команды для взаимодействия с сервисом Last.FM",
        "fr": "Commandes pour interagir avec le service Last.FM"
    },
    "lastfm_setuser_help": {
        "en": "Set your Last.FM username",
        "pt": "Define seu nome de usuário do Last.FM",
        "es": "Establece tu nombre de usuario de Last.FM",
        "ru": "Установите ваше имя пользователя Last.FM",
        "fr": "Définir votre nom d'utilisateur Last.FM"
    },
    "lastfm_setkey_help": {
        "en": "Set your Last.FM API key",
        "pt": "Define sua chave de API do Last.FM",
        "es": "Establece tu clave API de Last.FM",
        "ru": "Установите ваш API ключ Last.FM",
        "fr": "Définir votre clé API Last.FM"
    },
    "lastfm_now_help": {
        "en": "Show currently playing track on Last.FM",
        "pt": "Mostra a música atual sendo reproduzida no Last.FM",
        "es": "Muestra la pista actual que se reproduce en Last.FM",
        "ru": "Показывает текущий трек, воспроизводимый на Last.FM",
        "fr": "Affiche la piste en cours de lecture sur Last.FM"
    },
    "lastfm_topsongs_help": {
        "en": "Show your top 10 most played tracks on Last.FM",
        "pt": "Mostra suas 10 músicas mais tocadas no Last.FM",
        "es": "Muestra tus 10 pistas más reproducidas en Last.FM",
        "ru": "Показывает ваши 10 самых прослушиваемых треков на Last.FM",
        "fr": "Affiche vos 10 pistes les plus écoutées sur Last.FM"
    },
    "lastfm_topartists_help": {
        "en": "Show your top 10 most played artists on Last.FM",
        "pt": "Mostra seus 10 artistas mais tocados no Last.FM",
        "es": "Muestra tus 10 artistas más reproducidos en Last.FM",
        "ru": "Показывает ваших 10 самых прослушиваемых исполнителей на Last.FM",
        "fr": "Affiche vos 10 artistes les plus écoutés sur Last.FM"
    },
    "lastfm_topalbums_help": {
        "en": "Show your top 10 most played albums on Last.FM",
        "pt": "Mostra seus 10 álbuns mais tocados no Last.FM",
        "es": "Muestra tus 10 álbumes más reproducidos en Last.FM",
        "ru": "Показывает ваши 10 самых прослушиваемых альбомов на Last.FM",
        "fr": "Affiche vos 10 albums les plus écoutés sur Last.FM"
    },
    "lastfm_collage_help": {
        "en": "Generate a collage of your top albums/artists",
        "pt": "Gera um collage dos seus álbuns/artistas mais ouvidos",
        "es": "Genera un collage de tus álbumes/artistas más escuchados",
        "ru": "Создает коллаж ваших самых прослушиваемых альбомов/исполнителей",
        "fr": "Génère un collage de vos albums/artistes les plus écoutés"
    },
    "lastfm_recap_help": {
        "en": "Show your weekly/monthly recap",
        "pt": "Mostra seu resumo semanal/mensal",
        "es": "Muestra tu resumen semanal/mensual",
        "ru": "Показывает ваш еженедельный/ежемесячный обзор",
        "fr": "Affiche votre récapitulatif hebdomadaire/mensuel"
    },
    "lastfm_history_help": {
        "en": "Show your recent listening history",
        "pt": "Mostra seu histórico recente de escuta",
        "es": "Muestra tu historial reciente de escucha",
        "ru": "Показывает вашу недавнюю историю прослушивания",
        "fr": "Affiche votre historique d'écoute récent"
    },
    "lastfm_tags_help": {
        "en": "Show tags for current track/artist",
        "pt": "Mostra tags para a música/artista atual",
        "es": "Muestra etiquetas para la pista/artista actual",
        "ru": "Показывает теги для текущего трека/исполнителя",
        "fr": "Affiche les tags pour la piste/artiste actuel"
    },
    "lastfm_compare_help": {
        "en": "Compare your taste with another user",
        "pt": "Compara seu gosto musical com outro usuário",
        "es": "Compara tu gusto musical con otro usuario",
        "ru": "Сравнивает ваш музыкальный вкус с другим пользователем",
        "fr": "Compare vos goûts musicaux avec un autre utilisateur"
    },
    "lastfm_today_help": {
        "en": "Show your today's scrobbles",
        "pt": "Mostra suas scrobbles de hoje",
        "es": "Muestra tus scrobbles de hoy",
        "ru": "Показывает ваши сегодняшние скробблы",
        "fr": "Affiche vos scrobbles d'aujourd'hui"
    },
    "lastfm_week_help": {
        "en": "Show your weekly scrobbles",
        "pt": "Mostra suas scrobbles da semana",
        "es": "Muestra tus scrobbles de la semana",
        "ru": "Показывает ваши еженедельные скробблы",
        "fr": "Affiche vos scrobbles hebdomadaires"
    },
    "lastfm_month_help": {
        "en": "Show your monthly scrobbles",
        "pt": "Mostra suas scrobbles do mês",
        "es": "Muestra tus scrobbles del mes",
        "ru": "Показывает ваши ежемесячные скробблы",
        "fr": "Affiche vos scrobbles mensuels"
    },
    "lastfm_alltime_help": {
        "en": "Show your all-time scrobbles",
        "pt": "Mostra suas scrobbles de todos os tempos",
        "es": "Muestra tus scrobbles de todos los tiempos",
        "ru": "Показывает ваши скробблы за все время",
        "fr": "Affiche vos scrobbles de tous les temps"
    },
    "lastfm_scrobbles_help": {
        "en": "Show your total scrobble count",
        "pt": "Mostra seu total de scrobbles",
        "es": "Muestra tu conteo total de scrobbles",
        "ru": "Показывает общее количество ваших скробблов",
        "fr": "Affiche votre nombre total de scrobbles"
    },
    "lastfm_api_guide": {
        "en": "To use Last.FM features:\n1. Create API key at https://www.last.fm/api/account/create\n2. Set username with: .setuser YOUR_USERNAME\n3. Set API key with: .setkey YOUR_API_KEY",
        "pt": "Para usar os recursos do Last.FM:\n1. Crie uma chave API em https://www.last.fm/api/account/create\n2. Defina o usuário com: .setuser SEU_USUARIO\n3. Defina a chave API com: .setkey SUA_CHAVE_API",
        "es": "Para usar las funciones de Last.FM:\n1. Crea una clave API en https://www.last.fm/api/account/create\n2. Establece usuario con: .setuser TU_USUARIO\n3. Establece clave API con: .setkey TU_CLAVE_API",
        "ru": "Чтобы использовать функции Last.FM:\n1. Создайте API ключ на https://www.last.fm/api/account/create\n2. Установите имя пользователя: .setuser ВАШ_ПОЛЬЗОВАТЕЛЬ\n3. Установите API ключ: .setkey ВАШ_API_КЛЮЧ",
        "fr": "Pour utiliser les fonctionnalités Last.FM:\n1. Créez une clé API sur https://www.last.fm/api/account/create\n2. Définissez l'utilisateur avec: .setuser VOTRE_UTILISATEUR\n3. Définissez la clé API avec: .setkey VOTRE_CLE_API"
    },
    "nowfy_plugin_title": {
        "en": "nowFy - Spotify Plugin",
        "pt": "nowFy - Plugin do Spotify",
        "es": "nowFy - Plugin de Spotify",
        "ru": "nowFy - Плагин Spotify",
        "fr": "nowFy - Plugin Spotify"
    },
    "guide_with_images": {
        "en": "Guide with images:",
        "pt": "Guia com imagens:",
        "es": "Guía con imágenes:",
        "ru": "Руководство с изображениями:",
        "fr": "Guide avec images:"
    },
    "setup_commands": {
        "en": "SETUP COMMANDS:",
        "pt": "COMANDOS DE CONFIGURAÇÃO:",
        "es": "COMANDOS DE CONFIGURACIÓN:",
        "ru": "КОМАНДЫ НАСТРОЙКИ:",
        "fr": "COMMANDES DE CONFIGURATION:"
    },
    "playback_controls": {
        "en": "Playback Controls:",
        "pt": "Controles de Reprodução:",
        "es": "Controles de Reproducción:",
        "ru": "Управление воспроизведением:",
        "fr": "Contrôles de lecture:"
    },
    "need_help": {
        "en": "Need help? Join @exteraDevPlugins",
        "pt": "Precisa de ajuda? Entre em @exteraDevPlugins",
        "es": "¿Necesitas ayuda? Únete a @exteraDevPlugins",
        "ru": "Нужна помощь? Присоединяйтесь к @exteraDevPlugins",
        "fr": "Besoin d'aide ? Rejoignez @exteraDevPlugins"
    },
    "powered_by": {
        "en": "Powered by CrocsLabs",
        "pt": "Desenvolvido por CrocsLabs",
        "es": "Desarrollado por CrocsLabs",
        "ru": "Работает на CrocsLabs",
        "fr": "Propulsé par CrocsLabs"
    },
    "no_active_device": {
        "en": "No active device found. Please start Spotify on any device first.",
        "pt": "Nenhum dispositivo ativo encontrado. Por favor, inicie o Spotify em qualquer dispositivo primeiro.",
        "es": "No se encontró ningún dispositivo activo. Por favor, inicie Spotify en cualquier dispositivo primero.",
        "ru": "Активное устройство не найдено. Пожалуйста, сначала запустите Spotify на любом устройстве.",
        "fr": "Aucun appareil actif trouvé. Veuillez d'abord démarrer Spotify sur n'importe quel appareil."
    },
    "now_playing": {
        "en": "♪ ıllıllı NOW PLAYING:",
        "pt": "♪ ıllıllı REPRODUZINDO AGORA:",
        "es": "♪ ıllıllı REPRODUCIENDO AHORA:",
        "ru": "♪ ıllıllı СЕЙЧАС ИГРАЕТ:",
        "fr": "♪ ıllıllı EN LECTURE:"
    },
    "powered_by_nowfy": {
        "en": "• Powered by nowFy",
        "pt": "• Desenvolvido por nowFy",
        "es": "• Desarrollado por nowFy",
        "ru": "• Работает на nowFy",
        "fr": "• Propulsé par nowFy"
    },
    "no_track_playing": {
        "en": "No track currently playing",
        "pt": "Nenhuma música tocando no momento",
        "es": "No hay ninguna pista reproduciéndose",
        "ru": "Сейчас ничего не воспроизводится",
        "fr": "Aucune piste en cours de lecture"
    },
    "search_results": {
        "en": "⌕ Your nowFy Search Results:",
        "pt": "⌕ Seus Resultados de Busca nowFy:",
        "es": "⌕ Tus Resultados de Búsqueda nowFy:",
        "ru": "⌕ Результаты поиска nowFy:",
        "fr": "⌕ Vos Résultats de Recherche nowFy:"
    },
    "use_play_command": {
        "en": "ⓘ Use .play <number> to play {0}.\nUse .queue <number> to add it next in the queue. Enjoy! ;)",
        "pt": "ⓘ Use .play <número> para reproduzir {0}.\nUse .queue <número> para adicioná-la como próxima na fila. Aproveite! ;)",
        "es": "ⓘ Usa .play <número> para reproducir {0}.\nUsa .queue <número> para añadirla como la siguiente en la cola. ¡Disfruta! ;)",
        "ru": "ⓘ Используйте .play <номер> для воспроизведения {0}.\nИспользуйте .queue <номер>, чтобы добавить следующий трек в очередь. Наслаждайтесь! ;)",
        "fr": "ⓘ Utilisez .play <numéro> pour lire {0}.\nUtilisez .queue <numéro> pour l'ajouter comme prochain dans la file. Profitez-en ! ;)"
    },
    "playback_highlights": {
        "en": "≡ Your Playback Highlights",
        "pt": "≡ Seus Destaques de Reprodução",
        "es": "≡ Tus Destacados de Reproducción",
        "ru": "≡ Ваши Основные Моменты",
        "fr": "≡ Vos Temps Forts d'Écoute"
    },
    "lastfm_stats_summary": {
        "en": "**Your Last.FM Stats**\n\nTotal Scrobbles: {scrobbles}\nMember since: {registered}",
        "pt": "**Suas Estatísticas no Last.FM**\n\nTotal de Scrobbles: {scrobbles}\nMembro desde: {registered}",
        "es": "**Tus Estadísticas de Last.FM**\n\nScrobbles totales: {scrobbles}\nMiembro desde: {registered}",
        "fr": "**Vos Statistiques Last.FM**\n\nNombre total de scrobbles : {scrobbles}\nMembre depuis : {registered}",
        "ru": "**Ваша статистика Last.FM**\n\nВсего скробблов: {scrobbles}\nС вами с: {registered}"
    },
    "your_top_artists": {
        "en": "Your Top Artists\n\n",
        "pt": "Seus Top Artistas\n\n",
        "es": "Tus Mejores Artistas\n\n",
        "fr": "Vos Meilleurs Artistes\n\n",
        "ru": "Ваши лучшие исполнители\n\n"
    },
    "update_info": {
        "en": "To generate the new permission URL:\n\n1. Copy your Client ID\n2. Use the command: .update YOUR_CLIENT_ID\n\nNotes:\n• You only need to do this once. Once working, there's no need to update again.\n• If this is your first time using nowFy from version 1.0.1, start with the regular method - use .help for more information.",
        "pt": "Para gerar a nova permissão:\n\n1. Copie o seu Client ID\n2. Use o comando: .update YOUR_CLIENT_ID\n\nNotas:\n• Você só precisa fazer isto uma única vez. Uma vez que feito e funcionando, não há necessidade em realizar esta atualização novamente.\n• Se é a sua primeira vez usando o nowFy a partir dessa versão (1.0.1), você deve começar pelo método comum, veja o .help para obter mais informações.",
        "es": "Para generar el nuevo permiso:\n\n1. Copia tu Client ID\n2. Usa el comando: .update YOUR_CLIENT_ID\n\nNotas:\n• Solo necesitas hacer esto una vez. Una vez que funcione, no hay necesidad de actualizar nuevamente.\n• Si es tu primera vez usando nowFy desde la versión 1.0.1, debes comenzar con el método regular - usa .help para más información.",
        "ru": "Для генерации новых разрешений:\n\n1. Скопируйте ваш Client ID\n2. Используйте команду: .update YOUR_CLIENT_ID\n\nПримечания:\n• Это нужно сделать только один раз. После успешной настройки обновление больше не требуется.\n• Если вы впервые используете nowFy начиная с версии 1.0.1, начните с обычного метода - используйте .help для получения дополнительной информации.",
        "fr": "Pour générer la nouvelle permission :\n\n1. Copiez votre Client ID\n2. Utilisez la commande : .update YOUR_CLIENT_ID\n\nRemarques :\n• Vous n'avez besoin de faire ceci qu'une seule fois. Une fois que ça fonctionne, il n'est pas nécessaire de mettre à jour à nouveau.\n• Si c'est votre première utilisation de nowFy depuis la version 1.0.1, commencez par la méthode régulière - utilisez .help pour plus d'informations."
    },
    "settings_header": {
        "en": "nowFy Settings",
        "pt": "Configurações do nowFy",
        "es": "Ajustes de nowFy",
        "ru": "Настройки nowFy",
        "fr": "Paramètres nowFy"
    },
    "youtube_api_key": {
        "en": "YouTube API Key",
        "pt": "Chave da API do YouTube",
        "es": "Clave de API de YouTube",
        "ru": "Ключ API YouTube",
        "fr": "Clé API YouTube"
    },
    "youtube_api_key_help": {
        "en": "To get your YouTube API Key, follow these steps:\n\n1. Visit [Google Cloud Console](https://console.cloud.google.com/)\n2. Sign in with your Google account\n3. Click 'Select project' → 'New project'\n4. Name it and click 'Create'\n5. Go to 'APIs & Services' → 'Library'\n6. Search for 'YouTube Data API v3' and click 'Enable'\n7. Go to 'Credentials' → 'Create credentials' → 'API key'\n8. Copy the generated key\n\nFor detailed instructions, visit our [official guide](https://github.com/soumaki/nowFy/blob/main/ytkey/guide.md#youtube-api-key-youtube-data-api-v3)",
        "pt": "Para obter sua Chave da API do YouTube, siga estes passos:\n\n1. Acesse o [Google Cloud Console](https://console.cloud.google.com/)\n2. Faça login com sua conta Google\n3. Clique em 'Selecionar projeto' → 'Novo projeto'\n4. Dê um nome e clique em 'Criar'\n5. Vá em 'APIs e serviços' → 'Bibliotecas'\n6. Pesquise por 'YouTube Data API v3' e clique em 'Ativar'\n7. Vá em 'Credenciais' → 'Criar credenciais' → 'Chave de API'\n8. Copie a chave gerada\n\nPara instruções detalhadas, visite nosso [guia oficial](https://github.com/soumaki/nowFy/blob/main/ytkey/guide.md#youtube-api-key-youtube-data-api-v3)",
        "es": "Para obtener tu Clave de API de YouTube, sigue estos pasos:\n\n1. Ve a [Google Cloud Console](https://console.cloud.google.com/)\n2. Inicia sesión con tu cuenta de Google\n3. Haz clic en 'Seleccionar proyecto' → 'Nuevo proyecto'\n4. Ponle un nombre y haz clic en 'Crear'\n5. Ve a 'APIs y servicios' → 'Biblioteca'\n6. Busca 'YouTube Data API v3' y haz clic en 'Activar'\n7. Ve a 'Credenciales' → 'Crear credenciales' → 'Clave de API'\n8. Copia la clave generada\n\nPara instrucciones detalladas, visita nuestra [guía oficial](https://github.com/soumaki/nowFy/blob/main/ytkey/guide.md#youtube-api-key-youtube-data-api-v3)",
        "ru": "Чтобы получить ключ API YouTube, выполните следующие действия:\n\n1. Перейдите в [Google Cloud Console](https://console.cloud.google.com/)\n2. Войдите в свою учетную запись Google\n3. Нажмите 'Выбрать проект' → 'Создать проект'\n4. Укажите имя и нажмите 'Создать'\n5. Перейдите в 'API и сервисы' → 'Библиотека'\n6. Найдите 'YouTube Data API v3' и нажмите 'Включить'\n7. Перейдите в 'Учетные данные' → 'Создать учетные данные' → 'Ключ API'\n8. Скопируйте сгенерированный ключ\n\nДля подробных инструкций посетите наше [официальное руководство](https://github.com/soumaki/nowFy/blob/main/ytkey/guide.md#youtube-api-key-youtube-data-api-v3)",
        "fr": "Pour obtenir votre Clé API YouTube, suivez ces étapes :\n\n1. Allez sur [Google Cloud Console](https://console.cloud.google.com/)\n2. Connectez-vous avec votre compte Google\n3. Cliquez sur 'Sélectionner un projet' → 'Nouveau projet'\n4. Donnez-lui un nom et cliquez sur 'Créer'\n5. Allez dans 'API et services' → 'Bibliothèque'\n6. Recherchez 'YouTube Data API v3' et cliquez sur 'Activer'\n7. Allez dans 'Identifiants' → 'Créer des identifiants' → 'Clé API'\n8. Copiez la clé générée\n\nPour des instructions détaillées, visitez notre [guide officiel](https://github.com/soumaki/nowFy/blob/main/ytkey/guide.md#youtube-api-key-youtube-data-api-v3)"
    },
    "spotify_login_title": {
        "en": "𝗦𝗽𝗼𝘁𝗶𝗳𝘆 𝗟𝗼𝗴𝗶𝗻 – Step-by-step",
        "pt": "𝗟𝗼𝗴𝗶𝗻 𝗱𝗼 𝗦𝗽𝗼𝘁𝗶𝗳𝘆 – Passo a passo",
        "es": "𝗜𝗻𝗶𝗰𝗶𝗼 𝗱𝗲 𝗦𝗲𝘀𝗶ó𝗻 𝗱𝗲 𝗦𝗽𝗼𝘁𝗶𝗳𝘆 – Paso a paso",
        "ru": "𝗔𝘂𝘁𝗼𝗿𝗶𝘇𝗮𝗰𝗶я 𝗦𝗽𝗼𝘁𝗶𝗳𝘆 – Пошаговая инструкция",
        "fr": "𝗖𝗼𝗻𝗻𝗲𝘅𝗶𝗼𝗻 𝗦𝗽𝗼𝘁𝗶𝗳𝘆 – Étape par étape"
    },
    "lastfm_login_title": {
        "en": "𝗟𝗮𝘀𝘁𝗙𝗠 𝗟𝗼𝗴𝗶𝗻 – Step-by-step",
        "pt": "𝗟𝗼𝗴𝗶𝗻 𝗱𝗼 𝗟𝗮𝘀𝘁𝗙𝗠 – Passo a passo",
        "es": "𝗜𝗻𝗶𝗰𝗶𝗼 𝗱𝗲 𝗦𝗲𝘀𝗶ó𝗻 𝗱𝗲 𝗟𝗮𝘀𝘁𝗙𝗠 – Paso a paso",
        "ru": "𝗔𝘂𝘁𝗼𝗿𝗶𝘇𝗮𝗰𝗶я 𝗟𝗮𝘀𝘁𝗙𝗠 – Пошаговая инструкция",
        "fr": "𝗖𝗼𝗻𝗻𝗲𝘅𝗶𝗼𝗻 𝗟𝗮𝘀𝘁𝗙𝗠 – Étape par étape"
    },
    "youtube_api_title": {
    "en": "YouTube API Setup",
    "pt": "Configuração da API do YouTube",
    "es": "Configuración de la API de YouTube",
    "ru": "Настройка API YouTube",
    "fr": "Configuration de l'API YouTube"
    },
    "setup_steps": {
        "en": "Step-by-step",
        "pt": "Passo a passo",
        "es": "Paso a paso",
        "ru": "Пошаговая инструкция",
        "fr": "Étape par étape"
    },
    "create_app": {
        "en": "Create an app and make sure to:",
        "pt": "Crie um aplicativo e certifique-se de:",
        "es": "Crea una aplicación y asegúrate de:",
        "ru": "Создайте приложение и убедитесь, что:",
        "fr": "Créez une application et assurez-vous de :"
    },
    "set_redirect_uri": {
        "en": "Set a Redirect URI (e.g. https://example.com/callback) – required",
        "pt": "Definir uma URI de Redirecionamento (ex: https://example.com/callback) – obrigatório",
        "es": "Establecer una URI de Redirección (ej: https://example.com/callback) – requerido",
        "ru": "Установить URI перенаправления (напр. https://example.com/callback) – обязательно",
        "fr": "Définir une URI de redirection (ex : https://example.com/callback) – requis"
    },
    "accept_terms": {
        "en": "Accept Spotify's terms by checking the \"I understand\" box",
        "pt": "Aceitar os termos do Spotify marcando a caixa \"Eu entendo\"",
        "es": "Aceptar los términos de Spotify marcando la casilla \"Entiendo\"",
        "ru": "Принять условия Spotify, отметив галочку \"Я понимаю\"",
        "fr": "Accepter les conditions de Spotify en cochant la case \"Je comprends\""
    },
    "copy_credentials": {
        "en": "Copy your Client ID and Client Secret",
        "pt": "Copie seu Client ID e Client Secret",
        "es": "Copia tu Client ID y Client Secret",
        "ru": "Скопируйте ваш Client ID и Client Secret",
        "fr": "Copiez votre Client ID et Client Secret"
    },
    "click_agree": {
        "en": "Click \"Agree\" to authorize access",
        "pt": "Clique em \"Concordar\" para autorizar o acesso",
        "es": "Haz clic en \"Aceptar\" para autorizar el acceso",
        "ru": "Нажмите \"Согласен\" для авторизации доступа",
        "fr": "Cliquez sur \"Accepter\" pour autoriser l'accès"
    },
    "return_to_chat": {
        "en": "Return to the chat and open the same link again",
        "pt": "Volte ao chat e abra o mesmo link novamente",
        "es": "Vuelve al chat y abre el mismo enlace de nuevo",
        "ru": "Вернитесь в чат и откройте ту же ссылку снова",
        "fr": "Retournez au chat et ouvrez le même lien à nouveau"
    },
    "url_example": {
        "en": "You'll now see a URL like:",
        "pt": "Você verá uma URL como:",
        "es": "Verás una URL como:",
        "ru": "Вы увидите URL вида:",
        "fr": "Vous verrez une URL comme :"
    },
    "copy_code": {
        "en": "Copy everything after 'code='",
        "pt": "Copie tudo depois de 'code='",
        "es": "Copia todo lo que está después de 'code='",
        "ru": "Скопируйте всё после 'code='",
        "fr": "Copiez tout ce qui suit 'code='"
    },
    "save_client_id": {
        "en": "Save your Client ID",
        "pt": "Salvar seu Client ID",
        "es": "Guardar tu Client ID",
        "ru": "Сохранить ваш Client ID",
        "fr": "Sauvegarder votre Client ID"
    },
    "save_client_secret": {
        "en": "Save your Client Secret",
        "pt": "Salvar seu Client Secret",
        "es": "Guardar tu Client Secret",
        "ru": "Сохранить ваш Client Secret",
        "fr": "Sauvegarder votre Client Secret"
    },
    "generate_login_link": {
        "en": "Generates your personal Spotify login link",
        "pt": "Gera seu link pessoal de login do Spotify",
        "es": "Genera tu enlace personal de inicio de sesión de Spotify",
        "ru": "Генерирует вашу персональную ссылку для входа в Spotify",
        "fr": "Génère votre lien de connexion personnel Spotify"
    },
    "open_link_login": {
        "en": "Open the link and log in to Spotify",
        "pt": "Abra o link e faça login no Spotify",
        "es": "Abre el enlace e inicia sesión en Spotify",
        "ru": "Откройте ссылку и войдите в Spotify",
        "fr": "Ouvrez le lien et connectez-vous à Spotify"
    },
    "paste_code": {
        "en": "Paste the code you copied",
        "pt": "Cole o código que você copiou",
        "es": "Pega el código que copiaste",
        "ru": "Вставьте скопированный код",
        "fr": "Collez le code que vous avez copié"
    },
    "verify_setup": {
        "en": "Verify your setup",
        "pt": "Verificar sua configuração",
        "es": "Verificar tu configuración",
        "ru": "Проверить вашу настройку",
        "fr": "Vérifier votre configuration"
    },
    "apple_theme": {
    "en": "Custom text for Apple theme",
    "pt": "Texto customizado do tema Apple",
    "es": "Texto personalizado tema Apple",
    "ru": "Текст для темы Apple",
    "fr": "Texte thème Apple personnalisé"
    },
    "get_api_key": {
        "en": "Get your API key",
        "pt": "Obtenha sua chave de API",
        "es": "Obtén tu clave de API",
        "ru": "Получите ваш ключ API",
        "fr": "Obtenez votre clé API"
    },
    "youtube_api_guide": {
    "en": "You need a YouTube API key to use this feature. Tap the button below to open a step-by-step guide on how to generate your key.",
    "pt": "Você precisa de uma chave de API do YouTube para usar este recurso. Toque no botão abaixo para abrir um guia passo a passo sobre como gerar sua chave.",
    "es": "Necesitas una clave de API de YouTube para usar esta función. Pulsa el botón de abajo para abrir una guía paso a paso sobre cómo generar tu clave.",
    "ru": "Для использования этой функции необходим ключ API YouTube. Нажмите кнопку ниже, чтобы открыть пошаговое руководство по созданию ключа.",
    "fr": "Vous avez besoin d'une clé API YouTube pour utiliser cette fonctionnalité. Appuyez sur le bouton ci-dessous pour ouvrir un guide étape par étape sur la génération de votre clé."
    },
    "go_to_console": {
        "en": "Go to https://console.cloud.google.com",
        "pt": "Acesse https://console.cloud.google.com",
        "es": "Ve a https://console.cloud.google.com",
        "ru": "Перейдите на https://console.cloud.google.com",
        "fr": "Allez sur https://console.cloud.google.com"
    },
    "create_project": {
        "en": "Create a new project or select an existing one",
        "pt": "Crie um novo projeto ou selecione um existente",
        "es": "Crea un nuevo proyecto o selecciona uno existente",
        "ru": "Создайте новый проект или выберите существующий",
        "fr": "Créez un nouveau projet ou sélectionnez un existant"
    },
    "enable_api": {
        "en": "Enable the YouTube Data API v3",
        "pt": "Ative a API de Dados do YouTube v3",
        "es": "Habilita la API de Datos de YouTube v3",
        "ru": "Включите YouTube Data API v3",
        "fr": "Activez l'API de données YouTube v3"
    },
    "create_credentials": {
        "en": "Create credentials (API Key)",
        "pt": "Crie credenciais (Chave de API)",
        "es": "Crea credenciales (Clave de API)",
        "ru": "Создайте учетные данные (Ключ API)",
        "fr": "Créez des identifiants (Clé API)"
    },
    "show_playing_spotify": {
        "en": "Show what's playing on Spotify",
        "pt": "Mostrar o que está tocando no Spotify",
        "es": "Mostrar qué está sonando en Spotify",
        "ru": "Показать что играет в Spotify",
        "fr": "Afficher ce qui joue sur Spotify"
    },
    "read_docs_theme": { 
        "en": "Use these default values unless you want to customize:\n- Background: #d2d2d2\n- Text: #000000\n\n• coolors.co/generate",
        "pt": "Use estes valores padrão a menos que queira personalizar:\n- Fundo: #d2d2d2\n- Texto: #000000\n\n• coolors.co/generate",
        "es": "Usa estos valores predeterminados, a menos que desees personalizar:\n- Fondo: #d2d2d2\n- Texto: #000000\n\n• coolors.co/generate",
        "ru": "Используйте эти значения по умолчанию, если не хотите настраивать:\n- Фон: #d2d2d2\n- Текст: #000000\n\n• coolors.co/generate",
        "fr": "Utilisez ces valeurs par défaut sauf si vous souhaitez personnaliser:\n- Fond : #d2d2d2\n- Texte : #000000\n\n• coolors.co/generate"
    },
    "read_docs_warning": {
        "pt": "É importante que leia toda a documentação, guia, FAQ para não restar dúvidas.\n\nSe precisar, releia, está tudo muito bem explicado.\nO uso do Last.FM no nowFy é simples e para tudo funcionar corretamente, basta seguir as instruções.\n\nTambém criamos uma FAQ no canal: @nowfyDOCS\n\nSe tiver realmente dúvidas, algo que não esteja nas docs, guias, aí sim, pode entrar em contato, vamos tentar ajudar.",
        "en": "It's important to read all the documentation, guides, and FAQ to clear any doubts.\n\nIf needed, read them again; everything is well explained.\nUsing Last.FM on nowFy is simple and to make it work correctly, just follow the instructions.\n\nWe also created a FAQ channel: @nowfyDOCS\n\nIf you still have real doubts not covered in the docs or guides, then feel free to contact us; we'll try to help.",
        "es": "Es importante leer toda la documentación, guías y FAQ para no tener dudas.\n\nSi es necesario, léelas de nuevo; todo está bien explicado.\nUsar Last.FM en nowFy es sencillo y para que funcione correctamente, solo sigue las instrucciones.\n\nTambién creamos un canal FAQ: @nowfyDOCS\n\nSi realmente tienes dudas que no estén en las docs o guías, entonces contáctanos; intentaremos ayudarte.",
        "fr": "Il est important de lire toute la documentation, les guides et la FAQ pour ne pas avoir de doutes.\n\nSi besoin, relisez-les ; tout est bien expliqué.\nL'utilisation de Last.FM sur nowFy est simple, il suffit de suivre les instructions pour que cela fonctionne correctement.\n\nNous avons aussi créé un canal FAQ : @nowfyDOCS\n\nSi vous avez encore de réels doutes qui ne sont pas couverts par les docs ou guides, alors contactez-nous ; nous essaierons de vous aider.",
        "ru": "Важно прочитать всю документацию, руководства и FAQ, чтобы не оставалось вопросов.\n\nЕсли нужно, перечитайте их снова; всё хорошо объяснено.\nИспользование Last.FM в nowFy простое, чтобы всё работало правильно, просто следуйте инструкциям.\nМы также создали канал FAQ: @nowfyDOCS\n\nЕсли у вас всё ещё есть вопросы, не описанные в документации или руководствах, свяжитесь с нами; мы постараемся помочь."
    },
    "legacy_mode_note": {
        "pt": "Bem-vindo(a)!\n\nAntes de configurar, é importante entender o que é o Spotify Legacy.\n\nEsse modo é exclusivo para quem utiliza a API oficial do Spotify obtida pelo site Spotify for Developers.\n\nPor isso, as configurações do Spotify (Spotify Legacy) ficam separadas das configurações do Last.FM.",
        "en": "Welcome!\n\nBefore configuring, it's important to understand what Spotify Legacy is.\n\nThis mode is exclusive to those using the official Spotify API obtained from the Spotify for Developers website.\n\nThat's why the Spotify settings (Spotify Legacy) are separated from the Last.FM settings.",
        "es": "¡Bienvenido(a)!\n\nAntes de configurar, es importante entender qué es Spotify Legacy.\n\nEste modo es exclusivo para quienes usan la API oficial de Spotify obtenida desde el sitio de Spotify for Developers.\n\nPor eso, las configuraciones de Spotify (Spotify Legacy) están separadas de las de Last.FM.",
        "fr": "Bienvenue !\n\nAvant de configurer, il est important de comprendre ce qu'est Spotify Legacy.\n\nCe mode est réservé à ceux qui utilisent l'API officielle de Spotify obtenue sur le site Spotify for Developers.\n\nC'est pourquoi les paramètres de Spotify (Spotify Legacy) sont séparés de ceux de Last.FM.",
        "ru": "Добро пожаловать!\n\nПеред настройкой важно понять, что такое Spotify Legacy.\n\nЭтот режим предназначен только для тех, кто использует официальное API Spotify, полученное через сайт Spotify for Developers.\n\nПоэтому настройки Spotify (Spotify Legacy) отделены от настроек Last.FM."
    },
    "show_playing_lastfm": {
        "en": "Show what's playing on LastFM",
        "pt": "Mostrar o que está tocando no LastFM",
        "es": "Mostrar qué está sonando en LastFM",
        "ru": "Показать что играет в LastFM",
        "fr": "Afficher ce qui joue sur LastFM"
    },
    "next_track": {
        "en": "Next track ⏭",
        "pt": "Próxima faixa ⏭",
        "es": "Siguiente pista ⏭",
        "ru": "Следующий трек ⏭",
        "fr": "Piste suivante ⏭"
    },
    "previous_track": {
        "en": "Previous track ⏮",
        "pt": "Faixa anterior ⏮",
        "es": "Pista anterior ⏮",
        "ru": "Предыдущий трек ⏮",
        "fr": "Piste précédente ⏮"
    },
    "play_pause": {
        "en": "Play/Pause ⏯",
        "pt": "Reproduzir/Pausar ⏯",
        "es": "Reproducir/Pausar ⏯",
        "ru": "Воспроизведение/Пауза ⏯",
        "fr": "Lecture/Pause ⏯"
    },
    "switch_display": {
        "en": "Switch display styles",
        "pt": "Alternar estilos de exibição",
        "es": "Cambiar estilos de visualización",
        "ru": "Переключить стили отображения",
        "fr": "Changer les styles d'affichage"
    },
    "update_plugin": {
        "en": "Also updates the plugin to the latest version",
        "pt": "Também atualiza o plugin para a última versão",
        "es": "También actualiza el plugin a la última versión",
        "ru": "Также обновляет плагин до последней версии",
        "fr": "Met également à jour le plugin vers la dernière version"
    },
    "disable_caption_label": {
        "en": "Disable caption",
        "pt": "Desativar legenda",
        "es": "Desactivar subtítulo",
        "ru": "Отключить подпись",
        "fr": "Désactiver la légende"
    },
    "client_credentials": {
        "en": "Spotify Credentials",
        "pt": "Credenciais do Spotify",
        "es": "Credenciales de Spotify",
        "ru": "Данные Spotify",
        "fr": "Identifiants Spotify"
    },
    "use_now": {
        "en": "Use .now to get current track",
        "pt": "Use .now para ver a faixa atual",
        "es": "Usa .now para ver la pista actual",
        "ru": "Используйте .now для текущего трека",
        "fr": "Utilisez .now pour voir la piste en cours"
    },
    "footer_caption": {
        "en": "Caption",
        "pt": "Legenda",
        "es": "Pie de página (leyenda) debajo de la imagen",
        "ru": "Нижний колонтитул (подпись) под изображением",
        "fr": "Pied de page (légende) sous l'image"
    },
    "show_track_link": {
        "en": "Show Track Link",
        "pt": "Mostrar link da música",
        "es": "Mostrar enlace de la pista",
        "ru": "Показать ссылку на трек",
        "fr": "Afficher le lien de la piste"
    },
    "use_spotify_link": {
        "en": "Use Spotify Link",
        "pt": "Usar link do Spotify",
        "es": "Usar enlace de Spotify",
        "ru": "Использовать ссылку Spotify",
        "fr": "Utiliser le lien Spotify"
    },
    "your_top_tracks": {
        "en": "🎵 **Your Top Tracks**\n\n",
        "pt": "🎵 **Suas Top Músicas**\n\n",
        "es": "🎵 **Tus Mejores Canciones**\n\n",
        "fr": "🎵 **Vos Meilleurs Titres**\n\n",
        "ru": "🎵 **Ваши лучшие треки**\n\n"
    },
    "no_recent_tracks_found": {
        "en": "No recent tracks found.\nMake sure you've played something recently.",
        "pt": "Nenhuma faixa recente encontrada.\nCertifique-se de ter escutado algo recentemente.",
        "es": "No se encontraron pistas recientes.\nAsegúrate de haber reproducido algo hace poco.",
        "fr": "Aucune piste récente trouvée.\nAssurez-vous d'avoir écouté de la musique récemment.",
        "ru": "Недавние треки не найдены.\nУбедитесь, что вы недавно что-то слушали."
    },
    "no_track_playing_lastfm": {
        "pt": "Tentando conectar.\nPode levar alguns segundos para o Last.FM reconhecer sua música. Toque algo e tente de novo.",
        "en": "Trying to connect.\nIt may take a few seconds for Last.FM to recognize your music. Play something and try again.",
        "es": "Intentando conectar.\nPuede tardar unos segundos en que Last.FM reconozca tu música. Reproduce algo y vuelve a intentarlo.",
        "fr": "Tentative de connexion.\nIl peut falloir quelques secondes pour que Last.FM reconnaisse votre musique. Lancez une chanson et réessayez.",
        "ru": "Попытка подключения.\nLast.FM может потребоваться несколько секунд, чтобы распознать вашу музыку. Воспроизведите что-нибудь и попробуйте снова."
    },   
    "show_quick_buttons": {
        "en": "Show nowFy",
        "pt": "Acesso rápido",
        "es": "Mostrar botones de acceso rápido",
        "fr": "Afficher les boutons d'accès rapide",
        "ru": "Показать быстрые кнопки"
    },
    "show_now_playing": {
        "en": "Now Playing (from .s)",
        "pt": "Tocando (via .s)",
        "es": "Reproduciendo (desde .s)",
        "ru": "Сейчас играет (.s)",
        "fr": "Lecture (depuis .s)"
    },
    "launch_spotify": {
        "en": "Launch Spotify to get started. ;)",
        "pt": "Abra o Spotify para começar. ;)",
        "es": "Abre Spotify para empezar. ;)",
        "ru": "Запустите Spotify, чтобы начать. ;)",
        "fr": "Lancez Spotify pour commencer. ;)"
    },
    "settings_in_chat_menu": {
        "en": "In Chat Menu",
        "pt": "Menu do Chat",
        "es": "Menú del Chat",
        "ru": "Меню в чате",
        "fr": "Menu du Chat"
    },
    "getting_lastfm_track": {
        "en": "Fetching your Last.FM data...",
        "pt": "Obtendo dados do seu Last.FM...",
        "es": "Obteniendo tus datos de Last.FM...",
        "fr": "Récupération de vos données Last.FM...",
        "ru": "Получение данных вашего профиля Last.FM..."
    },
    "top_track_all_time": {
        "en": "Most played track of all time\n\n",
        "pt": "Música mais ouvida de todos os tempos\n\n",
        "es": "Canción más escuchada de todos los tiempos\n\n",
        "fr": "Titre le plus écouté de tous les temps\n\n",
        "ru": "Самый прослушиваемый трек за всё время\n\n"
    },
    "changelog": {
        "en": "Changelog",
        "pt": "Registro de Alterações",
        "es": "Registro de Cambios",
        "ru": "История изменений",
        "fr": "Journal des modifications"
    },
    "about_custom_fonts": {
        "en": "About Custom Fonts",
        "pt": "Sobre Fontes Personalizadas",
        "es": "Acerca de Fuentes Personalizadas",
        "ru": "О пользовательских шрифтах",
        "fr": "À propos des polices personnalisées"
    },
    "nowfy_settings_divider": {
        "en": "NOWFY SETTINGS",
        "pt": "CONFIGURAÇÕES DO NOWFY",
        "es": "AJUSTES DE NOWFY",
        "ru": "НАСТРОЙКИ NOWFY",
        "fr": "PARAMÈTRES NOWFY"
    },
    "Font Settings": {
        "en": "FONT SETTINGS",
        "pt": "Configurações de Fonte",
        "es": "Ajustes de Fuente",
        "ru": "Настройки шрифта",
        "fr": "Paramètres de police"
    },
    "Font": {
        "en": "Fonts",
        "pt": "Fontes",
        "es": "Fuente",
        "ru": "Шрифт",
        "fr": "Police"
    },
    "custom_font": {
        "en": "Custom Font",
        "pt": "Fonte Personalizada",
        "es": "Fuente Personalizada",
        "ru": "Пользовательский шрифт",
        "fr": "Police Personnalisée"
    },
    "font_path": {
        "en": "Font Path",
        "pt": "Diretório",
        "es": "Ruta de la Fuente",
        "ru": "Путь к шрифту",
        "fr": "Chemin de la Police"
    },
    "thanks": {
        "en": "Thank you for using nowFy! Feedback?\nJoin: @exteraDevPlugins",
        "pt": "Obrigado por usar o nowFy! Feedback?\nEntre: @exteraDevPlugins",
        "es": "¡Gracias por usar nowFy! ¿Comentarios?\nÚnete: @exteraDevPlugins",
        "ru": "Спасибо за использование nowFy! Обратная связь?\nПрисоединяйтесь: @exteraDevPlugins",
        "fr": "Merci d'utiliser nowFy ! Retour ?\nRejoignez : @exteraDevPlugins"
    },
    "bulletin_shuffle_on": {
        "en": "Shuffle mode enabled",
        "pt": "Modo aleatório ativado",
        "es": "Modo aleatorio activado",
        "ru": "Режим перемешивания включен",
        "fr": "Mode aléatoire activé"
    },
    "bulletin_shuffle_off": {
        "en": "Shuffle mode disabled",
        "pt": "Modo aleatório desativado",
        "es": "Modo aleatorio desactivado",
        "ru": "Режим перемешивания выключен",
        "fr": "Mode aléatoire désactivé"
    },
    "bulletin_repeat_off": {
        "en": "Repeat mode: Off",
        "pt": "Modo de repetição: Desativado",
        "es": "Modo de repetición: Desactivado",
        "ru": "Режим повтора: Выключен",
        "fr": "Mode répétition: Désactivé"
    },
    "bulletin_repeat_context": {
        "en": "Repeat mode: Context",
        "pt": "Modo de repetição: Contexto",
        "es": "Modo de repetición: Contexto",
        "ru": "Режим повтора: Контекст",
        "fr": "Mode répétition: Contexte"
    },
    "bulletin_repeat_track": {
        "en": "Repeat mode: Track",
        "pt": "Modo de repetição: Faixa",
        "es": "Modo de repetición: Pista",
        "ru": "Режим повтора: Трек",
        "fr": "Mode répétition: Piste"
    },
    "bulletin_queue_add": {
        "en": "Added to queue",
        "pt": "Adicionado à fila",
        "es": "Añadido a la cola",
        "ru": "Добавлено в очередь",
        "fr": "Ajouté à la file d'attente"
    },
    "loading_recent_tracks": {
        "en": "Loading recently played tracks...",
        "pt": "Carregando músicas reproduzidas recentemente...",
        "es": "Cargando pistas reproducidas recientemente...",
        "ru": "Загрузка недавно прослушанных треков...",
        "fr": "Chargement des pistes récemment jouées..."
    },
    "loading_image": {
        "en": "Loading image...",
        "pt": "Carregando imagem...",
        "es": "Cargando imagen...",
        "ru": "Загрузка изображения...",
        "fr": "Chargement de l'image..."
    },
    "next_track": {
        "en": "Skipping to next track...",
        "pt": "Pulando para a próxima música...",
        "es": "Saltando a la siguiente pista...",
        "ru": "Переход к следующему треку...",
        "fr": "Passage à la piste suivante..."
    },
    "previous_track": {
        "en": "Going back to previous track...",
        "pt": "Voltando para a música anterior...",
        "es": "Volviendo a la pista anterior...",
        "ru": "Возврат к предыдущему треку...",
        "fr": "Retour à la piste précédente..."
    },
    "user_profile_summary": {
        "en": "👤 **Profile of {target_user}**\n\nScrobbles: {scrobbles}\nCountry: {country}\nMember since: {registered}",
        "pt": "👤 **Perfil de {target_user}**\n\nScrobbles: {scrobbles}\nPaís: {country}\nMembro desde: {registered}",
        "es": "👤 **Perfil de {target_user}**\n\nScrobbles: {scrobbles}\nPaís: {country}\nMiembro desde: {registered}",
        "fr": "👤 **Profil de {target_user}**\n\nScrobbles : {scrobbles}\nPays : {country}\nMembre depuis : {registered}",
        "ru": "👤 **Профиль {target_user}**\n\nСкробблы: {scrobbles}\nСтрана: {country}\nУчастник с: {registered}"
    },
    "error_skip": {
        "en": "ⓘ Could not skip to next track.\n",
        "pt": "ⓘ Não foi possível pular a música.\n",
        "es": "ⓘ No se pudo saltar a la siguiente pista.\n",
        "ru": "ⓘ Не удалось перейти к следующему треку.\n",
        "fr": "ⓘ Impossible de passer à la piste suivante.\n"
    },
    "error_previous": {
        "en": "ⓘ Could not go back to previous track.\n",
        "pt": "ⓘ Não foi possível voltar a música.\n",
        "es": "ⓘ No se pudo volver a la pista anterior.\n",
        "ru": "ⓘ Не удалось вернуться к предыдущему треку.\n",
        "fr": "ⓘ Impossible de revenir à la piste précédente.\n"
    },
    "comparison_with_user": {
        "en": "🤝 **Comparison with {target_user}**\n\nCompatibility: {score:.1f}%\n\nShared artists:\n",
        "pt": "🤝 **Comparação com {target_user}**\n\nCompatibilidade: {score:.1f}%\n\nArtistas em comum:\n",
        "es": "🤝 **Comparación con {target_user}**\n\nCompatibilidad: {score:.1f}%\n\nArtistas en común:\n",
        "fr": "🤝 **Comparaison avec {target_user}**\n\nCompatibilité : {score:.1f}%\n\nArtistes en commun :\n",
        "ru": "🤝 **Сравнение с {target_user}**\n\nСовместимость: {score:.1f}%\n\nОбщие исполнители:\n"
    },
    "error_playback": {
        "en": "ⓘ Could not change playback state.\n",
        "pt": "ⓘ Não foi possível alterar o estado da reprodução.\n",
        "es": "ⓘ No se pudo cambiar el estado de reproducción.\n",
        "ru": "ⓘ Не удалось изменить состояние воспроизведения.\n",
        "fr": "ⓘ Impossible de modifier l'état de lecture.\n"
    },
    "weekly_summary_title": {
        "en": "🎵 **Your Weekly Summary**\n\n",
        "pt": "🎵 **Seu Resumo Semanal**\n\n",
        "es": "🎵 **Tu Resumen Semanal**\n\n",
        "fr": "🎵 **Votre Résumé Hebdomadaire**\n\n",
        "ru": "🎵 **Ваш недельный отчёт**\n\n"
    },
    "top_artists_section": {
        "en": "\nTop artists:\n",
        "pt": "\nTop artistas:\n",
        "es": "\nArtistas más escuchados:\n",
        "fr": "\nArtistes les plus écoutés :\n",
        "ru": "\nТоп исполнителей:\n"
    },
    "your_top_albums": {
        "en": "Your Top Albums\n\n",
        "pt": "Seus Top Álbuns\n\n",
        "es": "Tus Álbumes Top\n\n",
        "fr": "Vos Meilleurs Albums\n\n",
        "ru": "Ваши лучшие альбомы\n\n"
    },
    "top_genres_title": {
        "en": "**Your Most Listened Genres**\n\n",
        "pt": "**Seus Gêneros Mais Ouvidos**\n\n",
        "es": "**Tus Géneros Más Escuchados**\n\n",
        "fr": "**Vos Genres les Plus Écoutés**\n\n",
        "ru": "**Ваши самые прослушиваемые жанры**\n\n"
    },
    "validating_credentials": {
        "en": "Validating credentials...",
        "pt": "Validando credenciais...",
        "es": "Validando credenciales...",
        "fr": "Validation des identifiants...",
        "ru": "Проверка учетных данных..."
    },
    "minute_bar_legacy_only": {
        "en": "MusicBar • MinuteBar Style is currently available only for the Legacy theme.",
        "pt": "MusicBar • MinuteBar Style está disponível apenas para o tema Legacy (por enquanto).",
        "es": "MusicBar • MinuteBar Style está disponible solo para el tema Legacy (por ahora).",
        "fr": "MusicBar • MinuteBar Style est uniquement disponible pour le thème Legacy (pour le moment).",
        "ru": "MusicBar • MinuteBar Style доступен только для темы Legacy (пока что)."
    },
    "scrobble_history_title": {
        "en": "**Scrobble History**\n\n",
        "pt": "**Histórico de Scrobbles**\n\n",
        "es": "**Historial de Scrobbles**\n\n",
        "fr": "**Historique des Scrobbles**\n\n",
        "ru": "**История скробблов**\n\n"
    },
    "advanced_options_title": {
        "en": "Advanced Options",
        "pt": "Opções Avançadas",
        "es": "Opciones Avanzadas",
        "fr": "Options Avancées",
        "ru": "Расширенные настройки"
    },
    "total_track_plays": {
        "en": "Total plays: {count}",
        "pt": "Total de plays: {count}",
        "es": "Reproducciones totales: {count}",
        "fr": "Nombre total d'écoutes : {count}",
        "ru": "Всего прослушиваний: {count}"
    },
    "total_track_plays": {
        "en": "Total plays: {count}",
        "pt": "Total de plays: {count}",
        "es": "Reproducciones totales: {count}",
        "fr": "Lectures totales : {count}",
        "ru": "Всего прослушиваний: {count}"
    },
    "bulletin_pausing": {
        "en": "Pausing music...",
        "pt": "Pausando música...",
        "es": "Pausando música...",
        "fr": "Mise en pause de la musique...",
        "ru": "Приостановка музыки..."
    },
    "bulletin_playing": {
        "en": "Resuming music...",
        "pt": "Reproduzindo música...",
        "es": "Reanudando música...",
        "fr": "Reprise de la musique...",
        "ru": "Возобновление музыки..."
    },
    "bulletin_play_start": {
        "en": "Starting playback...",
        "pt": "Iniciando reprodução...",
        "es": "Iniciando reproducción...",
        "fr": "Démarrage de la lecture...",
        "ru": "Начало воспроизведения..."
    },
    "top_tracks_section": {
        "en": "\nTop tracks:\n",
        "pt": "\nTop músicas:\n",
        "es": "\nCanciones más escuchadas:\n",
        "fr": "\nTitres les plus écoutés :\n",
        "ru": "\nТоп треков:\n"
},
    "error_no_device": {
        "en": "No active device found. Start playback in Spotify first.",
        "pt": "Nenhum dispositivo ativo encontrado. Inicie a reprodução no Spotify primeiro.",
        "es": "No se encontró ningún dispositivo activo. Inicie la reproducción en Spotify primero.",
        "ru": "Активное устройство не найдено. Сначала начните воспроизведение в Spotify.",
        "fr": "Aucun appareil actif trouvé. Démarrez d'abord la lecture dans Spotify."
    },
    "error_permission": {
        "en": "This feature is only available for Spotify Premium users.\n• FAQ @nowFyDOCS",
        "pt": "Este recurso está disponível apenas para assinantes do Spotify Premium.\n• FAQ @nowFyDOCS",
        "es": "Esta función solo está disponible para usuarios de Spotify Premium.\n• FAQ @nowFyDOCS",
        "ru": "Эта функция доступна только пользователям Spotify Premium.\n• FAQ @nowFyDOCS",
        "fr": "Cette fonctionnalité est réservée aux abonnés Spotify Premium.\n• FAQ @nowFyDOCS"
    },
    "error_token": {
        "en": "Token expired. Use .check to validate your credentials.",
        "pt": "Token expirado. Use .check para validar suas credenciais.",
        "es": "Token expirado. Use .check para validar sus credenciales.",
        "ru": "Токен истек. Используйте .check для проверки учетных данных.",
        "fr": "Jeton expiré. Utilisez .check pour valider vos identifiants."
    },
    "error_unknown": {
        "en": "Error: {}",
        "pt": "Erro: {}",
        "es": "Error: {}",
        "ru": "Ошибка: {}",
        "fr": "Erreur: {}"
    },
    "apple_theme_text": {
        "en": "Apple Caption",
        "pt": "Legenda Apple",
        "es": "Subtítulo Apple",
        "fr": "Légende Apple",
        "ru": "Подпись Apple"
    },
    "error_youtube_api_key": {
        "en": "YouTube API Key not configured. Configure in Settings > nowFy > YouTube API Key",
        "pt": "YouTube API Key não configurada. Configure em Configurações > nowFy > YouTube API Key",
        "es": "Clave de API de YouTube no configurada. Configure en Ajustes > nowFy > YouTube API Key",
        "ru": "Ключ API YouTube не настроен. Настройте в Настройки > nowFy > YouTube API Key",
        "fr": "Clé API YouTube non configurée. Configurez dans Paramètres > nowFy > YouTube API Key"
    },
    "error_youtube_api": {
        "en": "YouTube API Error: {}",
        "pt": "Erro na API do YouTube: {}",
        "es": "Error de API de YouTube: {}",
        "ru": "Ошибка API YouTube: {}",
        "fr": "Erreur API YouTube: {}"
    },
    "youtube_api_key_success": {
        "en": "✅ YouTube API key set successfully!",
        "pt": "✅ API do YT configurada com sucesso!",
        "es": "✅ ¡API de YouTube configurada exitosamente!",
        "ru": "✅ Ключ API YouTube успешно установлен!",
        "fr": "✅ Clé API YouTube configurée avec succès !"
    },
    "your_lastfm": {
        "en": "Your Last.FM",
        "pt": "Seu Last.FM",
        "es": "Tu Last.FM",
        "fr": "Ton Last.FM",
        "ru": "Твой Last.FM"
    }, 
    "optional": {
        "en": "Optional",
        "pt": "Opcional",
        "es": "Opcional",
        "fr": "Optionnel",
        "ru": "Необязательно"
    },   
    "show_lastfm_profile_link": {
        "en": "Show Last.FM Profile",
        "pt": "Meu Last.FM",
        "es": "Perfil Last.FM",
        "ru": "Профиль Last.FM",
        "fr": "Profil Last.FM"
    },
    "error_youtube_quota": {
        "en": "YouTube API quota exceeded. Try again later.",
        "pt": "Limite da API do YouTube excedido. Tente novamente mais tarde.",
        "es": "Cuota de API de YouTube excedida. Inténtelo de nuevo más tarde.",
        "ru": "Превышена квота API YouTube. Повторите попытку позже.",
        "fr": "Quota API YouTube dépassé. Réessayez plus tard."
    },
    "error_youtube_no_results": {
        "en": "No results found for: {}",
        "pt": "Nenhum resultado encontrado para: {}",
        "es": "No se encontraron resultados para: {}",
        "ru": "Результаты не найдены для: {}",
        "fr": "Aucun résultat trouvé pour: {}"
    },
    "checking_lastfm_status": {
        "en": "ⓘ Please wait, we're checking if something is playing.\nCould it be an error? Yes. Common? Also yes. Just try again.",
        "pt": "ⓘ Aguarde, estamos verificando se há algo tocando.\nPode ser um erro? Pode. É comum? Também. Tente novamente.",
        "es": "ⓘ Espera un momento, estamos verificando si hay algo reproduciéndose.\n¿Puede ser un error? Sí. ¿Común? También. Intenta otra vez.",
        "fr": "ⓘ Veuillez patienter, nous vérifions si quelque chose est en cours de lecture.\nUne erreur ? C’est possible. Fréquent ? Aussi. Réessayez simplement.",
        "ru": "ⓘ Подождите, мы проверяем, играет ли что-нибудь.\nОшибка? Возможно. Обычное дело? Тоже да. Просто попробуйте снова."
    },
    "playblack_image": {
        "en": "https://i.postimg.cc/TPdtLvzK/playback.png",
        "pt": "https://i.postimg.cc/TPdtLvzK/playback.png",
        "es": "https://i.postimg.cc/TPdtLvzK/playback.png",
        "ru": "https://i.postimg.cc/TPdtLvzK/playback.png",
        "fr": "https://i.postimg.cc/TPdtLvzK/playback.png"
    },
    "error_play_exception": {
        "en": "Failed to play the playlist. Try again later.",
        "pt": "Não foi possível reproduzir a playlist. Tente novamente mais tarde.",
        "es": "No se pudo reproducir la lista. Inténtalo de nuevo más tarde.",
        "ru": "Не удалось воспроизвести плейлист. Попробуйте позже.",
        "fr": "Impossible de lire la playlist. Réessayez plus tard."
    },
    "error_code": {
        "en": "Error code: {}",
        "pt": "Código de erro: {}",
        "es": "Código de error: {}",
        "ru": "Код ошибки: {}",
        "fr": "Code d'erreur: {}"
    },
    "error_auth": {
        "en": "Could not authenticate with Spotify.",
        "pt": "Não foi possível autenticar com o Spotify.",
        "es": "No se pudo autenticar con Spotify.",
        "ru": "Не удалось пройти аутентификацию в Spotify.",
        "fr": "Impossible de s'authentifier avec Spotify."
    },
    "error_skip_exception": {
        "en": "Error skipping track: {}",
        "pt": "Erro ao pular música: {}",
        "es": "Error al saltar la pista: {}",
        "ru": "Ошибка при пропуске трека: {}",
        "fr": "Erreur lors du passage à la piste suivante: {}"
    },
    "error_previous_exception": {
        "en": "Error going to previous track: {}",
        "pt": "Erro ao voltar música: {}",
        "es": "Error al volver a la pista anterior: {}",
        "ru": "Ошибка при возврате к предыдущему треку: {}",
        "fr": "Erreur lors du retour à la piste précédente: {}"
    },
    "error_playback_exception": {
        "en": "Error changing playback: {}",
        "pt": "Erro ao alterar reprodução: {}",
        "es": "Error al cambiar la reproducción: {}",
        "ru": "Ошибка при изменении воспроизведения: {}",
        "fr": "Erreur lors du changement de lecture: {}"
    },
    "update_info": {
        "en": "To generate the new permission URL:\n\n1. Copy your Client ID\n2. Use the command: .update YOUR_CLIENT_ID\n\nNotes:\n• You only need to do this once. Once working, there's no need to update again.\n• If this is your first time using nowFy from version 1.0.1, start with the regular method - use .help for more information.",
        "pt": "Para gerar a nova permissão:\n\n1. Copie o seu Client ID\n2. Use o comando: .update YOUR_CLIENT_ID\n\nNotas:\n• Você só precisa fazer isto uma única vez. Uma vez que feito e funcionando, não há necessidade em realizar esta atualização novamente.\n• Se é a sua primeira vez usando o nowFy a partir dessa versão (1.0.1), você deve começar pelo método comum, veja o .help para obter mais informações.",
        "es": "Para generar el nuevo permiso:\n\n1. Copia tu Client ID\n2. Usa el comando: .update YOUR_CLIENT_ID\n\nNotas:\n• Solo necesitas hacer esto una vez. Una vez que funcione, no hay necesidad de actualizar nuevamente.\n• Si es tu primera vez usando nowFy desde la versión 1.0.1, debes comenzar con el método regular - usa .help para más información.",
        "ru": "Для генерации новых разрешений:\n\n1. Скопируйте ваш Client ID\n2. Используйте команду: .update YOUR_CLIENT_ID\n\nПримечания:\n• Это нужно сделать только один раз. После успешной настройки обновление больше не требуется.\n• Если вы впервые используете nowFy начиная с версии 1.0.1, начните с обычного метода - используйте .help для получения дополнительной информации.",
        "fr": "Pour générer la nouvelle permission :\n\n1. Copiez votre Client ID\n2. Utilisez la commande : .update YOUR_CLIENT_ID\n\nRemarques :\n• Vous n'avez besoin de faire ceci qu'une seule fois. Une fois que ça fonctionne, il n'est pas nécessaire de mettre à jour à nouveau.\n• Si c'est votre première utilisation de nowFy depuis la version 1.0.1, commencez par la méthode régulière - utilisez .help pour plus d'informations."
    },
    "lastfm_link_text": {
        "en": "LastFM Link Text",
        "pt": "Texto do Link do LastFM",
        "es": "Texto del Enlace de LastFM",
        "ru": "Текст ссылки LastFM",
        "fr": "Texte du Lien LastFM"
    },
    "top_track_all_time": {
        "en": "Your Most Played Track of All Time\n\n{track} - {artist}\n{plays} plays",
        "pt": "Sua Música Mais Ouvida de Todos os Tempos\n\n{track} - {artist}\n{plays} execuções",
        "es": "Tu Canción Más Escuchada de Todos los Tiempos\n\n{track} - {artist}\n{plays} reproducciones",
        "fr": "Votre Morceau le Plus Écouté de Tous les Temps\n\n{track} - {artist}\n{plays} lectures",
        "ru": "Ваш самый прослушиваемый трек за всё время\n\n{track} - {artist}\n{plays} прослушиваний"
    },
    "lastfm_caption": {
        "en": "LastFM Caption",
        "pt": "Legenda do LastFM",
        "es": "Pie de foto de LastFM",
        "ru": "Подпись LastFM",
        "fr": "Légende LastFM"
    },
    "show_lastfm_link": {
        "en": "Show LastFM Link",
        "pt": "Mostrar Link do LastFM",
        "es": "Mostrar Enlace de LastFM",
        "ru": "Показать ссылку LastFM",
        "fr": "Afficher le Lien LastFM"
    },
    "use_spotify_link": {
        "en": "Use Spotify Link",
        "pt": "Usar Link do Spotify",
        "es": "Usar Enlace de Spotify",
        "ru": "Использовать ссылку Spotify",
        "fr": "Utiliser le Lien Spotify"
    },
    "show_track_link": {
        "en": "Show Track Link",
        "pt": "Mostrar Link da Música",
        "es": "Mostrar Enlace de la Pista",
        "ru": "Показать ссылку на трек",
        "fr": "Afficher le Lien de la Piste"
    },
    "lastfm_profile": {
        "en": "Last.FM Profile",
        "pt": "Perfil Last.FM",
        "es": "Perfil de Last.FM",
        "ru": "Профиль Last.FM",
        "fr": "Profil Last.FM"
    },
    "youtube_settings": {
        "en": "YouTube Settings",
        "pt": "Configurações do YouTube",
        "es": "Configuración de YouTube",
        "ru": "Настройки YouTube",
        "fr": "Paramètres YouTube"
    },
    "youtube_api_key": {
        "en": "YouTube API Key",
        "pt": "Chave da API do YouTube",
        "es": "Clave de API de YouTube",
        "ru": "Ключ API YouTube",
        "fr": "Clé API YouTube"
    },
    "current_video_url": {
        "en": "Current Video URL",
        "pt": "URL do Vídeo Atual",
        "es": "URL del Video Actual",
        "ru": "URL текущего видео",
        "fr": "URL de la Vidéo Actuelle"
    },
    "current_player": {
        "en": "Media Source",
        "pt": "Fonte da Mídia",
        "es": "Fuente de Medios",
        "ru": "Источник медиа",
        "fr": "Source Média"
    },
    "top_track_period": {
        "en": "🎵 **Most Played Track {period_name}**\n\n**{track}** - {artist}\nPlays: {playcount}",
        "pt": "🎵 **Música Mais Ouvida {period_name}**\n\n**{track}** - {artist}\nExecuções: {playcount}",
        "es": "🎵 **Canción Más Escuchada {period_name}**\n\n**{track}** - {artist}\nReproducciones: {playcount}",
        "fr": "🎵 **Titre le Plus Écouté {period_name}**\n\n**{track}** - {artist}\nLectures : {playcount}",
        "ru": "🎵 **Самый прослушиваемый трек {period_name}**\n\n**{track}** - {artist}\nПрослушиваний: {playcount}"
    },
    "command_list": {
    "en": "˪ Available nowFy Commands:\n\n• .now - Show current playing track\n• .n - Skip to next track\n• .b - Go back to previous track\n• .p - Play/pause current track\n• .vol - Control Spotify volume\n• .shuffle - Toggle shuffle mode\n• .repeat - Toggle repeat mode\n• .list - Show last 5 played tracks\n• .s - Search and play a track\n• .s .album - Search and play an album\n• .like - Like/Unlike current track\n• .like <number> - Like track from search results\n• .queue <number> - Add track to queue\n• .mix <query> - Create mix playlist\n\n• .fm - Show current LastFM track\n• .myfm - Show LastFM commands\n• .setuser - Set your LastFM username\n• .setkey - Set your LastFM API key\n• .collage - Generate album art collage\n• .recap - Show weekly music summary\n• .history - View scrobble history\n• .tags - Show top music genres\n• .compare - Compare music taste with another user\n• .today - Show today's top track\n• .week - Show week's top track\n• .month - Show month's top track\n• .alltime - Show all-time top track\n• .scrobbles - Show total scrobbles\n\n• .setid - Set your Spotify Client ID\n• .setsecret - Set your Spotify Client Secret\n• .code - Set authorization code\n• .check - Validate your credentials\n• .update - Generate new permission URL\n• .dev - Open plugin settings quickly\n• .help - Show setup instructions\n• .use - Show this command list",

    "pt": "˪ Comandos disponíveis do nowFy:\n\n• .now - Mostra a música atual\n• .n - Pula para próxima música\n• .b - Volta para música anterior\n• .p - Pausa/reproduz a música atual\n• .vol - Controla o volume do Spotify\n• .shuffle - Alterna modo aleatório\n• .repeat - Alterna modo de repetição\n• .list - Mostra as últimas 5 músicas tocadas\n• .s - Pesquisa e toca uma música\n• .s .album - Pesquisa e toca um álbum\n• .like - Curtir/Descurtir música atual\n• .like <número> - Curtir música da busca\n• .queue <número> - Adicionar música à fila\n• .mix <busca> - Criar playlist mix\n\n• .fm - Mostra a música atual do LastFM\n• .myfm - Mostra comandos do LastFM\n• .setuser - Define seu usuário do LastFM\n• .setkey - Define sua chave API do LastFM\n• .collage - Gera colagem de capas de álbuns\n• .recap - Mostra resumo semanal de músicas\n• .history - Visualiza histórico de scrobbles\n• .tags - Mostra principais gêneros musicais\n• .compare - Compara gosto musical com outro usuário\n• .today - Mostra música mais tocada do dia\n• .week - Mostra música mais tocada da semana\n• .month - Mostra música mais tocada do mês\n• .alltime - Mostra música mais tocada de todos os tempos\n• .scrobbles - Mostra total de scrobbles\n\n• .setid - Define seu Client ID do Spotify\n• .setsecret - Define seu Client Secret do Spotify\n• .code - Define o código de autorização\n• .check - Valida suas credenciais\n• .update - Gera nova URL de permissão\n• .dev - Abre configurações do plugin\n• .help - Mostra instruções de configuração\n• .use - Mostra esta lista de comandos",

    "es": "˪ Comandos disponibles de nowFy:\n\n• .now - Muestra la pista actual\n• .n - Salta a la siguiente pista\n• .b - Vuelve a la pista anterior\n• .p - Pausa/reproduce la pista actual\n• .vol - Controla el volumen de Spotify\n• .shuffle - Alterna modo aleatorio\n• .repeat - Alterna modo de repetición\n• .list - Muestra las últimas 5 pistas reproducidas\n• .s - Busca y reproduce una pista\n• .s .album - Busca y reproduce un álbum\n• .like - Me gusta/No me gusta pista actual\n• .like <número> - Me gusta pista de búsqueda\n• .queue <número> - Añadir pista a la cola\n• .mix <búsqueda> - Crear lista de reproducción mix\n\n• .fm - Muestra la pista actual de LastFM\n• .myfm - Muestra comandos de LastFM\n• .setuser - Establece tu usuario de LastFM\n• .setkey - Establece tu clave API de LastFM\n• .collage - Genera collage de portadas de álbumes\n• .recap - Muestra resumen semanal de música\n• .history - Ver historial de scrobbles\n• .tags - Muestra principales géneros musicales\n• .compare - Compara gusto musical con otro usuario\n• .today - Muestra pista más reproducida del día\n• .week - Muestra pista más reproducida de la semana\n• .month - Muestra pista más reproducida del mes\n• .alltime - Muestra pista más reproducida de todos los tiempos\n• .scrobbles - Muestra total de scrobbles\n\n• .setid - Establece tu Client ID de Spotify\n• .setsecret - Establece tu Client Secret de Spotify\n• .code - Establece el código de autorización\n• .check - Valida tus credenciales\n• .update - Genera nueva URL de permiso\n• .dev - Abre configuraciones del plugin\n• .help - Muestra instrucciones de configuración\n• .use - Muestra esta lista de comandos",

    "ru": "˪ Доступные команды nowFy:\n\n• .now - Показать текущий трек\n• .n - Перейти к следующему треку\n• .b - Вернуться к предыдущему треку\n• .p - Воспроизвести/приостановить текущий трек\n• .vol - Управление громкостью Spotify\n• .shuffle - Включить/выключить случайный порядок\n• .repeat - Переключить режим повтора\n• .list - Показать последние 5 треков\n• .s - Поиск и воспроизведение трека\n• .s .album - Поиск и воспроизведение альбома\n• .like - Лайк/Дизлайк текущего трека\n• .like <номер> - Лайкнуть трек из поиска\n• .queue <номер> - Добавить трек в очередь\n• .mix <запрос> - Создать плейлист микс\n\n• .fm - Показать текущий трек LastFM\n• .myfm - Показать команды LastFM\n• .setuser - Установить имя пользователя LastFM\n• .setkey - Установить API ключ LastFM\n• .collage - Создать коллаж из обложек альбомов\n• .recap - Показать еженедельный музыкальный обзор\n• .history - Просмотр истории скробблинга\n• .tags - Показать основные музыкальные жанры\n• .compare - Сравнить музыкальный вкус с другим пользователем\n• .today - Показать самый популярный трек дня\n• .week - Показать самый популярный трек недели\n• .month - Показать самый популярный трек месяца\n• .alltime - Показать самый популярный трек за все время\n• .scrobbles - Показать общее количество скробблов\n\n• .setid - Установить Client ID Spotify\n• .setsecret - Установить Client Secret Spotify\n• .code - Установить код авторизации\n• .check - Проверить учетные данные\n• .update - Сгенерировать новый URL разрешений\n• .dev - Открыть настройки плагина\n• .help - Показать инструкции по настройке\n• .use - Показать этот список команд",

    "fr": "˪ Commandes nowFy disponibles:\n\n• .now - Afficher la piste en cours\n• .n - Passer à la piste suivante\n• .b - Revenir à la piste précédente\n• .p - Lecture/pause de la piste actuelle\n• .vol - Contrôler le volume Spotify\n• .shuffle - Activer/désactiver la lecture aléatoire\n• .repeat - Changer le mode de répétition\n• .list - Afficher les 5 dernières pistes\n• .s - Rechercher et lire une piste\n• .s .album - Rechercher et lire un album\n• .like - Aimer/Ne plus aimer la piste actuelle\n• .like <numéro> - Aimer une piste de la recherche\n• .queue <numéro> - Ajouter la piste à la file\n• .mix <recherche> - Créer une playlist mix\n\n• .fm - Afficher piste actuelle LastFM\n• .myfm - Afficher commandes LastFM\n• .setuser - Définir nom utilisateur LastFM\n• .setkey - Définir clé API LastFM\n• .collage - Générer collage de pochettes\n• .recap - Résumé musical hebdomadaire\n• .history - Historique des scrobbles\n• .tags - Genres musicaux principaux\n• .compare - Comparer goûts musicaux\n• .today - Piste la plus écoutée du jour\n• .week - Piste la plus écoutée de la semaine\n• .month - Piste la plus écoutée du mois\n• .alltime - Piste la plus écoutée de tous les temps\n• .scrobbles - Total des scrobbles\n\n• .setid - Définir Client ID Spotify\n• .setsecret - Définir Client Secret Spotify\n• .code - Définir code autorisation\n• .check - Valider identifiants\n• .update - Générer nouvelle URL\n• .dev - Ouvrir paramètres plugin\n• .help - Instructions de configuration\n• .use - Afficher cette liste de commandes"
    }, 
    "color_input_guidelines": {
    "en": (
        "When customizing background or text colors:\n\n"
        "• If the fields are left empty, default colors will be used automatically.\n"
        "• Always include the '#' symbol (hash prefix) before the color code.\n"
        "  - For example: use '#d2d2d2' instead of 'd2d2d2'.\n\n"
        "Using the correct format ensures that your custom colors are applied properly."
    ),
    "pt": (
        "Ao personalizar as cores de fundo ou do texto:\n\n"
        "• Se os campos forem deixados em branco, as cores padrão serão aplicadas automaticamente.\n"
        "• Sempre inclua o símbolo '#' (prefixo hash) antes do código da cor.\n"
        "  - Por exemplo: use '#d2d2d2' em vez de 'd2d2d2'.\n\n"
        "Usar o formato correto garante que as cores personalizadas sejam aplicadas corretamente."
    ),
    "es": (
        "Al personalizar los colores de fondo o texto:\n\n"
        "• Si los campos se dejan vacíos, se aplicarán automáticamente los colores predeterminados.\n"
        "• Siempre incluye el símbolo '#' (prefijo hash) antes del código de color.\n"
        "  - Por ejemplo: usa '#d2d2d2' en lugar de 'd2d2d2'.\n\n"
        "Usar el formato correcto garantiza que los colores personalizados se apliquen correctamente."
    ),
    "ru": (
        "При настройке цвета фона или текста:\n\n"
        "• Если поля оставить пустыми, будут автоматически применены цвета по умолчанию.\n"
        "• Всегда указывайте символ '#' (префикс hash) перед кодом цвета.\n"
        "  - Например: используйте '#d2d2d2', а не 'd2d2d2'.\n\n"
        "Правильный формат гарантирует корректное применение выбранных цветов."
    ),
    "fr": (
        "Lors de la personnalisation des couleurs d’arrière-plan ou du texte :\n\n"
        "• Si les champs sont laissés vides, les couleurs par défaut seront utilisées automatiquement.\n"
        "• Ajoutez toujours le symbole '#' (préfixe hash) avant le code couleur.\n"
        "  - Par exemple : utilisez '#d2d2d2' au lieu de 'd2d2d2'.\n\n"
        "Utiliser le bon format garantit que vos couleurs personnalisées s’appliqueront correctement."
    ),
    },
    "lastfm_credentials": {
        "en": "Last.FM Credentials",
        "pt": "Credenciais do Last.FM",
        "es": "Credenciales de Last.FM",
        "ru": "Данные Last.FM",
        "fr": "Identifiants Last.FM"
    },
    "no_search_results": {
        "en": "No search results found.",
        "pt": "Nenhum resultado de busca encontrado.",
        "es": "No se encontraron resultados de búsqueda.",
        "ru": "Результаты поиска не найдены.",
        "fr": "Aucun résultat de recherche trouvé."
    },
    "error_invalid_number": {
        "en": "Invalid track number.",
        "pt": "Número da faixa inválido.",
        "es": "Número de pista inválido.",
        "ru": "Неверный номер трека.",
        "fr": "Numéro de piste invalide."
    },
    "error_invalid_item": {
        "en": "Invalid track data.",
        "pt": "Dados da faixa inválidos.",
        "es": "Datos de pista no válidos.",
        "ru": "Неверные данные трека.",
        "fr": "Données de piste invalides."
    },
    "error_unauthorized": {
        "en": "Unauthorized access. Please re-authenticate.",
        "pt": "Acesso não autorizado. Por favor, reautentique-se.",
        "es": "Acceso no autorizado. Por favor, reautentíquese.",
        "ru": "Неавторизованный доступ. Пожалуйста, повторно авторизуйтесь.",
        "fr": "Accès non autorisé. Veuillez vous réauthentifier."
    },
    "error_like_failed": {
        "en": "Failed to like the track.",
        "pt": "Falha ao curtir a faixa.",
        "es": "Error al dar me gusta a la pista.",
        "ru": "Не удалось добавить трек в избранное.",
        "fr": "Échec de l'ajout de la piste aux favoris."
    },
    "bulletin_like_success": {
        "en": "Track liked successfully!",
        "pt": "Faixa curtida com sucesso!",
        "es": "¡Pista agregada a favoritos con éxito!",
        "ru": "Трек успешно добавлен в избранное!",
        "fr": "Piste ajoutée avec succès !"
    },
    "liked_track": {
        "en": "Liked Track",
        "pt": "Faixa Curtida",
        "es": "Pista Favorita",
        "ru": "Избранный трек",
        "fr": "Piste aimée"
    },
    "error_like_exception": {
        "en": "Error liking track: {}",
        "pt": "Erro ao curtir a faixa: {}",
        "es": "Error al dar me gusta a la pista: {}",
        "ru": "Ошибка при добавлении трека в избранное: {}",
        "fr": "Erreur lors de l'ajout de la piste aux favoris : {}"
    },
        "no_search_results": {
        "en": "No search results found.",
        "pt": "Nenhum resultado de busca encontrado.",
        "es": "No se encontraron resultados de búsqueda.",
        "ru": "Результаты поиска не найдены.",
        "fr": "Aucun résultat de recherche trouvé."
    },
    "error_invalid_number": {
        "en": "Invalid track number.",
        "pt": "Número da faixa inválido.",
        "es": "Número de pista inválido.",
        "ru": "Неверный номер трека.",
        "fr": "Numéro de piste invalide."
    },
    "error_invalid_item": {
        "en": "Invalid track data.",
        "pt": "Dados da faixa inválidos.",
        "es": "Datos de pista no válidos.",
        "ru": "Неверные данные трека.",
        "fr": "Données de piste invalides."
    },
    "error_unauthorized": {
        "en": "Unauthorized access. Please re-authenticate.",
        "pt": "Acesso não autorizado. Por favor, reautentique-se.",
        "es": "Acceso no autorizado. Por favor, reautentíquese.",
        "ru": "Неавторизованный доступ. Пожалуйста, повторно авторизуйтесь.",
        "fr": "Accès non autorisé. Veuillez vous réauthentifier."
    },
    "error_like_failed": {
        "en": "Failed to like the track.",
        "pt": "Falha ao curtir a faixa.",
        "es": "Error al dar me gusta a la pista.",
        "ru": "Не удалось добавить трек в избранное.",
        "fr": "Échec de l'ajout de la piste aux favoris."
    },
    "bulletin_like_success": {
        "en": "Track liked successfully!",
        "pt": "Faixa curtida com sucesso!",
        "es": "¡Pista agregada a favoritos con éxito!",
        "ru": "Трек успешно добавлен в избранное!",
        "fr": "Piste ajoutée avec succès !"
    },
    "liked_track": {
        "en": "Liked Track",
        "pt": "Faixa Curtida",
        "es": "Pista Favorita",
        "ru": "Избранный трек",
        "fr": "Piste aimée"
    },
    "error_like_exception": {
        "en": "Error liking track: {}",
        "pt": "Erro ao curtir a faixa: {}",
        "es": "Error al dar me gusta a la pista: {}",
        "ru": "Ошибка при добавлении трека в избранное: {}",
        "fr": "Erreur lors de l'ajout de la piste aux favoris : {}"
    },
    "playing": {
        "en": "Playing",
        "pt": "Tocando",
        "es": "Sonando",
        "ru": "Играет",
        "fr": "Joue"
    },
    "error_no_active_device": {
        "en": "No active device found. Please open Spotify on any device.",
        "pt": "Nenhum dispositivo ativo encontrado. Abra o Spotify em algum dispositivo.",
        "es": "No se encontró ningún dispositivo activo. Abra Spotify en algún dispositivo.",
        "ru": "Активное устройство не найдено. Пожалуйста, откройте Spotify на любом устройстве.",
        "fr": "Aucun appareil actif trouvé. Veuillez ouvrir Spotify sur un appareil."
    },
    "error_playlist_private": {
        "en": "This playlist is private and cannot be played.",
        "pt": "Esta playlist é privada e não pode ser reproduzida.",
        "es": "Esta lista de reproducción es privada y no se puede reproducir.",
        "ru": "Этот плейлист является приватным и не может быть воспроизведен.",
        "fr": "Cette playlist est privée et ne peut pas être lue."
    },
    "error_playlist_not_found": {
        "en": "Playlist not found. It may have been deleted.",
        "pt": "Playlist não encontrada. Ela pode ter sido excluída.",
        "es": "Lista de reproducción no encontrada. Puede haber sido eliminada.",
        "ru": "Плейлист не найден. Возможно, он был удален.",
        "fr": "Playlist introuvable. Elle a peut-être été supprimée."
    },
    "error_search_exception": {
        "en": "Error searching: {}",
        "pt": "Erro ao buscar: {}",
        "es": "Error al buscar: {}",
        "ru": "Ошибка поиска: {}",
        "fr": "Erreur de recherche: {}"
    },
    "error_playback_start": {
        "en": "This is next in queue!",
        "pt": "Esta é a próxima da fila!",
        "es": "¡Esta es la siguiente en la cola!",
        "ru": "Это следующий трек в очереди!",
        "fr": "C'est le prochain dans la file d'attente !"
    },
    "shuffle_is_on": {
        "en": "Shuffle is on",
        "pt": "Modo aleatório ativado",
        "es": "Aleatorio activado",
        "ru": "Случайный режим вкл",
        "fr": "Lecture aléatoire activée"
    },
    "shuffle_error": {
        "en": "Shuffle error",
        "pt": "Erro no aleatório",
        "es": "Error aleatorio",
        "ru": "Ошибка случайного режима",
        "fr": "Erreur aléatoire"
    },
}

def tr(key):
    lang = Locale.getDefault().getLanguage()
    return TRANSLATIONS.get(key, {}).get(lang, TRANSLATIONS.get(key, {}).get("en", key))

class nowFyPlugin(BasePlugin):
    def __init__(self):
        self._message_lock = threading.Lock()
        self._search_cache = {}
        self.error_message = ""
        self._overlay_cache = None
        self._overlay_url = "https://i.postimg.cc/ydQhT816/catpwapple.png"
        self._overlay_lock = threading.Lock()
        self._temp_dir = File(ApplicationLoader.getFilesDirFixed(), "nowFy")
        if not self._temp_dir.exists():
            self._temp_dir.mkdirs()

    def tr(self, key):
        return tr(key)

    def _get_masked_setting(self, key):
        value = super().get_setting(key, "")
        if value and isinstance(value, str) and len(value) > 8:
            return value[:4] + "****" + value[-4:]
        elif value:
            return "****"
        return ""

    def _detect_current_player(self):
        try:
            player_index = self.get_setting("current_player", 0)
            players = ["Spotify", "YouTube", "YouTube Music"] 

            if isinstance(player_index, int):
                if player_index >= len(players):
                    return "Spotify"
                return players[player_index]

            return player_index or "Spotify"

        except Exception as e:
            print(f"Error detecting player: {e}")
            return "Spotify"

    def _get_service_icon(self, player):
        try:
            icon_urls = {
                "Spotify": "https://i.postimg.cc/15LMgNHp/spfy.png",
                "SpotifyBlack": "https://i.postimg.cc/sXKB3TFm/spfynow.png",
                "YouTube": "https://i.postimg.cc/pLzCKXG3/yt.png",
                "YouTube Music": "https://i.postimg.cc/fbsKCn1y/ytm.png"
            }
            return icon_urls.get(player)
        except Exception as e:
            print(f"Error getting service icon: {e}")
            return None

    def _open_link(self, url):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        run_on_ui_thread(lambda: ctx.startActivity(intent))

    def on_setting_changed(self, key, value):
        if key in ["client_id", "client_secret", "refresh_token"]:
            if isinstance(value, str) and "****" in value:
                return 
        super().set_setting(key, value)

    def show_info_yt(self, title, message, positive_button,
                    neutral_button=None, neutral_link=None, neutral_type=None):
        try:
            fragment = get_last_fragment()
            ctx = fragment.getParentActivity() if fragment and fragment.getParentActivity() else ApplicationLoader.applicationContext
            
            builder = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
            builder.set_title(title)
            builder.set_message(message)
            builder.set_positive_button(positive_button, None)
            
            if neutral_button and neutral_link:
                if neutral_type == "link":
                    builder.set_neutral_button(neutral_button, lambda b, w: self._open_link(neutral_link))
                elif neutral_type == "copy":
                    builder.set_neutral_button(neutral_button, lambda b, w: BulletinHelper.show_copied_to_clipboard())
            
            run_on_ui_thread(lambda: builder.show())
        except Exception as e:
            print(f"Error showing info dialog: {e}")

    def create_tneming_settings(self):
        return [
            Header(text=self.tr("apple_theme")),
            Input(
                key="apple_theme_text",
                text=self.tr("apple_theme_text"),
                icon="device_phone_ios",
                default="            Powered by nowFy! =D"
            ),
            Selector(
                key="minute_bar_style",
                icon="gradient_right",
                text="MinuteBar Style",
                default=0,
                items=[
                    "Default",
                    "Glow Warm",
                    "SunGlow",
                    "Skyline",
                    "Gradient Glow",
                    "Gold Lux",
                    "Wave Segmented"
                ]
            ),
            Switch(
                key="show_music_bar",
                icon="msg_noise_on",
                text="MusicBar",
                default=True
            ),
            Divider(text=self.tr("minute_bar_legacy_only")),
            Input(
                key="spotify_link_text",
                text=self.tr("spotify_link_text"),
                icon="msg_link2",
                default="Open on Spotify"
            ),
            Switch(
                key="use_spotify_link",
                icon="menu_feature_links",
                text="Spotify Link",
                default=False
            ),
        ]

    def create_customfm_settings(self):
        return [
            Divider(text=self.tr("color_input_guidelines")),
            Header(text=self.tr("customfm_settings_header")),
            Switch(
                key="customfm_use_background",
                text=self.tr("customfm_use_background"),
                icon="msg_copy_photo",
                default=True
            ),
            Input(
                key="customfm_background_color",
                text=self.tr("customfm_background_color"),
                icon="ic_colorpicker_solar",
                default="#d2d2d2"
            ),
            Input(
                key="customfm_text_color",
                text=self.tr("customfm_text_color"),
                icon="menu_feature_color_name",
                default="#000000"
            ),
            Divider(text=self.tr("read_docs_theme"))
        ]

    def create_lastfm_credentials_secure(self):
        return [
            Input(
                key="lastfm_user",
                text="Username",
                icon="filled_username",
                default=self._get_masked_setting("lastfm_user"),
            ),
            Input(
                key="lastfm_api_key",
                text="Last.FM API Key",
                icon="filled_access_fingerprint",
                default=self._get_masked_setting("lastfm_api_key"),
            ),
        ]

    def create_lastfm_settings(self):
        return [
            Header(text=self.tr(" ")),
            Text( 
                text=self.tr("lastfm_credentials"),
                icon="msg_permissions_solar",
                create_sub_fragment=self.create_lastfm_credentials_secure
            ), 
            Divider(text=self.tr("Extras")),
            Input(
                key="youtube_api_key",
                text=self.tr("youtube_api_key"),
                icon="menu_feature_reliable",
                default=self._get_masked_setting("youtube_api_key"),
            ),
            Text(
                text=self.tr("youtube_api_title"),
                icon="msg2_help",
                accent=True,
                on_click=lambda view: self.show_info_yt(
                    title=self.tr("youtube_api_title"),
                    message=self.tr("youtube_api_guide"),
                    positive_button="OK",
                    neutral_button=self.tr("get_api_key"),
                    neutral_link="https://github.com/soumaki/nowFy/blob/main/ytkey/guide.md#youtube-api-key-youtube-data-api-v3",
                    neutral_type="link"
                ),
            ),
            Selector(
                key="current_player",
                icon="header_goinline_solar",
                text=self.tr("current_player"),
                default=0,
                items=[
                    "Spotify",
                    "YouTube",
                    "YouTube Music"
                ]
            ),
            Selector(
                key="lastfm_theme_style",
                icon="msg_theme",
                text="Last.FM Style",
                default=0,
                items=[
                    "Default",
                    "Darker",
                    "CustomFM"
                ]
            ),
            Text(
                text="CustomFM Visual",
                icon="msg_theme_solar",
                create_sub_fragment=self.create_customfm_settings
            ),
            Switch(
                key="show_profile_link",
                icon="filled_username",
                text=self.tr("show_lastfm_profile_link"),
                default=True
            ),
            Switch(
                key="use_spotify_link",
                icon="menu_feature_links",
                text="Spotify Link",
                default=False
            ),
            Divider(text=self.tr("read_docs_warning"))
        ]


    def create_spotify_credentials(self):
        return [
            Input(
                key="client_id",
                text="Client ID",
                icon="msg_contacts",
                default=self._get_masked_setting("client_id"),
            ),
            Input(
                key="client_secret",
                text="Client Secret",
                icon="filled_access_fingerprint",
                default=self._get_masked_setting("client_secret"),
            ),
            Input(
                key="refresh_token",
                text="Refresh Token",
                icon="menu_browser_refresh",
                default=self._get_masked_setting("refresh_token"),
            ),
        ]

    def create_spotify_settings(self):
        enable_advanced = self.get_setting("enable_advanced_nowfy", False)
        return [
            Header(text=self.tr(" ")),
            Text(
                text=self.tr("client_credentials"),
                icon="msg_contacts_hw_solar",
                create_sub_fragment=self.create_spotify_credentials
            ),
            Divider(text=self.tr("nowfy_settings_divider")),
            Input(
                key="custom_footer_text",
                text=self.tr("footer_caption"),
                icon="group_edit",
                default="♪ ıllıllı - I'm using nowFy!\n╰ by @exteraDevPlugins"
            ),
            Switch(
                key="show_footer_caption",
                icon="msg_photo_text2_solar",
                text=self.tr("disable_caption_label"),
                default=True
            ),
            Selector(
                key="now_theme_style",
                icon="msg_theme",
                text="Theme Style",
                default=0,
                items=[
                    "Legacy",
                    "LastFy",
                    "LastFy Light",
                    "LastFy Dark",
                    "Mini Player",
                    "Discord",
                    "Discord Light",
                    "Apple"
                ]
            ),
            Text(
                text=self.tr("advanced_options_title"),
                icon="msg_theme_solar",
                create_sub_fragment=self.create_tneming_settings
            ),
            Switch(
                key="show_now_playing",
                icon="msg2_chats_add",
                text=self.tr("show_now_playing"),
                default=True
            ) if enable_advanced else None,
            Selector(
                key="font_family",
                icon="msg_photo_text_framed3",
                text=self.tr("Font"),
                default=0,
                items=[
                    "Noto Sans CJK (Default)",
                    "ComingSoon",
                    "MiClockTibetan-Thin",
                    "NotoNaskhArabic-Bold",
                    "SourceSansPro-Bold"
                ] + [os.path.splitext(f)[0] for f in self._list_custom_fonts()]
            ),
            Switch(
                key="show_track_link",
                icon="menu_feature_links",
                text=self.tr("show_track_link"),
                default=True
            ),
            Divider(text=self.tr("NOWFY INFO")),
            Text(
                text=self.tr("about_custom_fonts"),
                icon="msg_photo_text_framed2",
                accent=True,
                on_click=lambda view: self._about_custom_fonts_dialog()
            ),
        ]


    def create_settings(self):
        return [
            Divider(text=self.tr("legacy_mode_note")),
            Text(
                text=self.tr("Spotify Legacy"),
                icon="filled_widget_music",
                create_sub_fragment=self.create_spotify_settings
            ),
            Text(
                text=self.tr("Last.FM"),
                icon="voicechat_muted",
                create_sub_fragment=self.create_lastfm_settings
            ),
            Divider(text=self.tr("optional")),
            Switch(
                key="show_settings_buttons",
                icon="msg_list",
                text=self.tr("show_quick_buttons"),
                default=True,
                on_change=self._on_show_settings_buttons_change,
            ),
            Switch(
                key="show_chat_menu",
                text=self.tr("settings_in_chat_menu"),
                icon="ic_ab_other",
                default=True
            ),
            Text(
                text=self.tr("changelog"),
                icon="msg_channel_solar",
                accent=True,
                on_click=lambda view: self._nowfy_info_dialog()
            ),
            Divider(text=self.tr("thanks"))
        ]

    def _about_custom_fonts_dialog(self, view=None):
        try:
            import requests
            import json
            import time

            fragment = get_last_fragment()
            ctx = fragment.getParentActivity() if fragment and fragment.getParentActivity() else ApplicationLoader.applicationContext
            
            builder = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
            builder.set_title(self.tr("about_custom_fonts"))

            try:
                timestamp = int(time.time())
                response = requests.get(f'https://raw.githubusercontent.com/soumaki/nowFy/refs/heads/main/customfonts.json?t={timestamp}')
                fonts_data = json.loads(response.text)

                if 'image_url' in fonts_data:
                    try:
                        if fonts_data['image_url'].endswith('.svg'):
                            builder.set_icon(fonts_data['image_url'], "#FFFFFF")
                        else:
                            img_response = requests.get(fonts_data['image_url'], timeout=5)
                            if img_response.status_code == 200:
                                builder.set_icon_from_bytes(img_response.content)
                    except Exception as e:
                        print(f"Error loading custom fonts image: {e}")

                system_language = ApplicationLoader.applicationContext.getResources().getConfiguration().locale.getLanguage()
                fonts_info = fonts_data.get('custom_fonts_info', {})
                
                lang_content = fonts_info.get(system_language) or fonts_info.get('en')
                
                if lang_content:
                    message = lang_content['description'] + '\n\n'
                    for step in lang_content['steps']:
                        message += f'• {step}\n'
                else:
                    message = 'Information not available in your language.'

                builder.set_message(message)
            except Exception as e:
                builder.set_message(f"Error loading custom fonts info: {str(e)}\n\nPlease try again later.")
            builder.set_positive_button("OK", None)
            run_on_ui_thread(lambda: builder.show())
        except Exception as e:
            print(f"Error showing custom fonts dialog: {e}")

    def _nowfy_info_dialog(self, view=None):
        try:
            import requests
            import json

            fragment = get_last_fragment()
            ctx = fragment.getParentActivity() if fragment and fragment.getParentActivity() else ApplicationLoader.applicationContext
            
            builder = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
            builder.set_title(self.tr("changelog"))

            try:
                import time
                timestamp = int(time.time())
                response = requests.get(f'https://raw.githubusercontent.com/soumaki/nowFy/refs/heads/main/changelog/nowfy.json?t={timestamp}')
                changelog_data = json.loads(response.text)

                if 'image_url' in changelog_data:
                    try:
                        if changelog_data['image_url'].endswith('.svg'):
                            builder.set_icon(changelog_data['image_url'], "#FFFFFF")
                        else:
                            img_response = requests.get(changelog_data['image_url'], timeout=5)
                            if img_response.status_code == 200:
                                builder.set_icon_from_bytes(img_response.content)
                    except Exception as e:
                        print(f"Error loading changelog image: {e}")

                message = f"[{changelog_data['current_version']}] - Current Version\n\n"

                for version in changelog_data['changelog']:
                    sections = version['sections']
                    
                    for section_name, items in sections.items():
                        if items:
                            message += f"{section_name.title()}\n\n"
                            for item in items:
                                message += f"  • {item}\n"
                            message += "\n"

                builder.set_message(message)
            except Exception as e:
                builder.set_message(f"Error loading changelog: {str(e)}\n\nPlease try again later.")
            builder.set_positive_button("OK", None)
            run_on_ui_thread(lambda: builder.show())
        except Exception as e:
            print(f"Error showing changelog dialog: {e}")

    def _on_show_settings_buttons_change(self, value):
        try:
            if value:
                self._add_menu_items()
            else:
                for menu_item in self.menu_items:
                    if menu_item.text == self.tr("settings_header"):
                        self.remove_menu_item(menu_item)
        except Exception as e:
            print(f"Error toggling menu items: {e}")

    def _get_overlay_cached(self):
        with self._overlay_lock:
            if self._overlay_cache is not None:
                return self._overlay_cache
            try:
                resp = requests.get(self._overlay_url, timeout=5)
                if resp.status_code == 200:
                    overlay = Image.open(BytesIO(resp.content)).convert("RGBA")
                    overlay = overlay.resize((640, 640), Image.LANCZOS)
                    self._overlay_cache = overlay
                    return overlay
            except Exception as e:
                print("Overlay error:", e)
            return None

    def _fix_android_font_path(self, path):
        if not path:
            return path
        return path

    def _get_custom_fonts_path(self):
        return "/storage/emulated/0/Android/media/com.exteragram.messenger/fonts"

    def _list_custom_fonts(self):
        fonts_dir = self._get_custom_fonts_path()
        custom_fonts = []
        
        if os.path.exists(fonts_dir):
            for file in os.listdir(fonts_dir):
                if file.lower().endswith(('.ttf', '.otf')):
                    custom_fonts.append(file)
        
        return custom_fonts

    def _get_font_set(self):
        try:
            font_family = self.get_setting("font_family", 0)
            custom_fonts = self._list_custom_fonts()
            
            if font_family > 4 and custom_fonts: 
                custom_font_index = font_family - 5 
                if custom_font_index < len(custom_fonts):
                    font_path = os.path.join(self._get_custom_fonts_path(), custom_fonts[custom_font_index])
                    try:
                        return {
                            "now": ImageFont.truetype(font_path, 23),
                            "title": ImageFont.truetype(font_path, 28),
                            "small": ImageFont.truetype(font_path, 22)
                        }
                    except Exception as e:
                        print(f"Error loading custom font {font_path}: {e}")
 
            font_paths = {
                0: "/system/fonts/NotoSansCJK-Regular.ttc",
                1: "/system/fonts/ComingSoon.ttf",
                2: "/system/fonts/MiClockTibetan-Thin.ttf",
                3: "/system/fonts/NotoNaskhArabic-Bold.ttf",
                4: "/system/fonts/SourceSansPro-Bold.ttf"
            }
            font_path = font_paths.get(font_family, font_paths[0])
            
            return {
                "now": ImageFont.truetype(font_path, 23),
                "title": ImageFont.truetype(font_path, 28),
                "small": ImageFont.truetype(font_path, 22)
            }
        except Exception as e:
            print(f"Error in _get_font_set: {e}")
            f = ImageFont.load_default()
            return {"now": f, "title": f, "small": f}

    def _send_markdown_msg(self, params, text):
        with self._message_lock:
            caption_md = parse_markdown(text)
            params.message = caption_md.text
            params.entities = ArrayList()
            for entity in caption_md.entities:
                params.entities.add(entity.to_tlrpc_object())
            run_on_ui_thread(lambda: get_send_messages_helper().sendMessage(params))

    def _send_image(self, params, image_data, caption=None):
        try:
            send_helper = get_send_messages_helper()
            if isinstance(image_data, BytesIO):
                temp_dir = File(ApplicationLoader.getFilesDirFixed(), "nowFy")
                if not temp_dir.exists():
                    temp_dir.mkdirs()
                filename = f"{uuid.uuid4()}.png"
                temp_path = File(temp_dir, filename).getAbsolutePath()
                
                with open(temp_path, "wb") as f:
                    f.write(image_data.getvalue())
                
                photo = send_helper.generatePhotoSizes(temp_path, None)
                params.photo = photo
                params.path = temp_path
            else:
                params.photo = send_helper.generatePhotoSizes(image_data, None)
                params.path = image_data
            
            if caption:
                caption_md = parse_markdown(caption)
                params.caption = caption_md.text
                params.entities = ArrayList()
                for entity in caption_md.entities:
                    params.entities.add(entity.to_tlrpc_object())
            else:
                params.message = None
                
            send_helper.sendMessage(params)
        except Exception as e:
            self._send_msg(params, f"❌ Error sending image: {str(e)}")

    def on_plugin_load(self):
        self.add_on_send_message_hook()
        self._add_menu_items()
    
    def _add_menu_items(self):
        try:
            self.add_menu_item(MenuItemData(
                menu_type=MenuItemType.DRAWER_MENU,
                text=self.tr("settings_header"),
                icon="msg_settings_14",
                priority=5,
                on_click=lambda ctx: run_on_ui_thread(lambda: self._open_plugin_settings())
            ))

            if self.get_setting("show_chat_menu", True):
                self.add_menu_item(MenuItemData(
                    menu_type=MenuItemType.CHAT_ACTION_MENU,
                    text=self.tr("settings_header"),
                    icon="msg_settings_14",
                    priority=5,
                    on_click=lambda ctx: run_on_ui_thread(lambda: self._open_plugin_settings())
                ))
        except Exception as e:
            print(f"Failed to add menu items: {e}")

    def on_setting_changed(self, key, value):
        if key == "show_chat_menu":
            self.remove_menu_items()
            self._add_menu_items()

    def _open_plugin_settings(self):
        try:
            print("Attempting to open plugin settings...")
            controller = PluginsController.getInstance()
            print(f"Controller instance: {controller}")
            plugin = controller.plugins.get(self.id)
            print(f"Plugin instance: {plugin}")
            fragment = get_last_fragment()
            print(f"Fragment instance: {fragment}")
            if plugin and fragment:
                fragment.presentFragment(PluginSettingsActivity(plugin))
                print("Settings opened successfully")
            else:
                print(f"Failed to open settings - Plugin: {plugin}, Fragment: {fragment}")
        except Exception as e:
            print(f"Error opening plugin settings: {str(e)}\nType: {type(e)}")
            import traceback
            print(traceback.format_exc())

    def on_send_message_hook(self, account, params):
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()

        msg = params.message.strip()

        if msg.startswith(".setid "):
            fragment = get_last_fragment()
            ctx = fragment.getParentActivity() if fragment and fragment.getParentActivity() else ApplicationLoader.applicationContext
            progress_dialog = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_LOADING)
            progress_dialog.set_message("Setting up Client ID...")
            progress_dialog.show()
            def run_setid():
                try:
                    run_on_ui_thread(lambda: progress_dialog.set_progress(30))
                    time.sleep(0.5)
                    self.set_setting("client_id", msg.split(" ", 1)[1].strip())
                    run_on_ui_thread(lambda: progress_dialog.set_progress(70))
                    time.sleep(0.5)
                    message = "[⤵️](5256182535917940722) Client ID set successfully!"
                    parsed = parse_markdown(message)
                    params.message = parsed.text
                    params.entities = ArrayList()
                    for entity in parsed.entities:
                        params.entities.add(entity.to_tlrpc_object())
                    run_on_ui_thread(lambda: progress_dialog.set_progress(100))
                    time.sleep(0.2)
                    run_on_ui_thread(lambda: get_send_messages_helper().sendMessage(params))
                except Exception as e:
                    BulletinHelper.show_info(self.tr("error_unknown").format(str(e)))
                finally:
                    run_on_ui_thread(lambda: progress_dialog.dismiss())
            threading.Thread(target=run_setid).start()
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg.startswith(".setsecret "):
            fragment = get_last_fragment()
            ctx = fragment.getParentActivity() if fragment and fragment.getParentActivity() else ApplicationLoader.applicationContext
            progress_dialog = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_LOADING)
            progress_dialog.set_message("Setting up Client Secret...")
            progress_dialog.show()
            def run_setsecret():
                try:
                    run_on_ui_thread(lambda: progress_dialog.set_progress(30))
                    time.sleep(0.5)
                    self.set_setting("client_secret", msg.split(" ", 1)[1].strip())
                    run_on_ui_thread(lambda: progress_dialog.set_progress(70))
                    time.sleep(0.5)
                    message = "[⤵️](5256182535917940722) Client Secret set successfully!"
                    parsed = parse_markdown(message)
                    params.message = parsed.text
                    params.entities = ArrayList()
                    for entity in parsed.entities:
                        params.entities.add(entity.to_tlrpc_object())
                    run_on_ui_thread(lambda: progress_dialog.set_progress(100))
                    time.sleep(0.2)
                    run_on_ui_thread(lambda: get_send_messages_helper().sendMessage(params))
                except Exception as e:
                    BulletinHelper.show_info(self.tr("error_unknown").format(str(e)))
                finally:
                    run_on_ui_thread(lambda: progress_dialog.dismiss())
            threading.Thread(target=run_setsecret).start()
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg.startswith(".code "):
            fragment = get_last_fragment()
            ctx = fragment.getParentActivity() if fragment and fragment.getParentActivity() else ApplicationLoader.applicationContext
            progress_dialog = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_LOADING)
            progress_dialog.set_message("Exchanging authorization code...")
            progress_dialog.show()

            def run_code():
                try:
                    run_on_ui_thread(lambda: progress_dialog.set_progress(30))
                    time.sleep(0.5)
                    code = msg.split(" ", 1)[1].strip()
                    if "code=" in code:
                        code = code.split("code=")[1].split("&")[0] if "&" in code else code.split("code=")[1]
                    self._exchange_code(params, code, progress_dialog)
                    run_on_ui_thread(lambda: progress_dialog.set_progress(70))
                    time.sleep(0.5)

                    run_on_ui_thread(lambda: BulletinHelper.show_info("Authorization code exchanged successfully!"))

                    run_on_ui_thread(lambda: progress_dialog.set_progress(100))
                    time.sleep(0.2)
                except Exception as e:
                    run_on_ui_thread(lambda: BulletinHelper.show_info(self.tr("error_unknown").format(str(e))))
                finally:
                    run_on_ui_thread(lambda: progress_dialog.dismiss())

            run_on_queue(run_code)
            return HookResult(strategy=HookStrategy.CANCEL)


        if msg == ".check":
            fragment = get_last_fragment()
            ctx = fragment.getParentActivity() if fragment and fragment.getParentActivity() else ApplicationLoader.applicationContext
            progress_dialog = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_LOADING)
            progress_dialog.set_message(self.tr("validating_credentials"))
            progress_dialog.show()
            def run_check():
                try:
                    self._validate_credentials(params, progress_dialog)
                except Exception as e:
                    BulletinHelper.show_info(self.tr("error_unknown").format(str(e)))
                finally:
                    run_on_ui_thread(lambda: progress_dialog.dismiss())
            run_on_queue(run_check)
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".n":
            run_on_queue(lambda: self._skip_track(params))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".b":
            run_on_queue(lambda: self._previous_track(params))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg.startswith(".setuser "):
            self.set_setting("lastfm_user", msg.split(" ", 1)[1].strip())
            self._send_msg(params, "✅ LastFM username set successfully!")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg.startswith(".setkey "):
            self.set_setting("lastfm_api_key", msg.split(" ", 1)[1].strip())
            self._send_msg(params, "✅ LastFM API key set successfully!")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".setyt":
            self._send_markdown_msg(params, self.tr("youtube_api_key_help"))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg.startswith(".setyt "):
            self.set_setting("youtube_api_key", msg.split(" ", 1)[1].strip())
            self._send_msg(params, self.tr("youtube_api_key_success"))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".fm":
            try:
                fragment = get_last_fragment()
                ctx = fragment.getParentActivity() if fragment and fragment.getParentActivity() else ApplicationLoader.applicationContext
                progress_dialog = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_LOADING)
                progress_dialog.set_message(self.tr("getting_lastfm_track"))
                progress_dialog.show()
            except Exception:
                progress_dialog = None

            def run_lastfm():
                user = self.get_setting("lastfm_user", "")
                api_key = self.get_setting("lastfm_api_key", "")
                if not user or not api_key:
                    BulletinHelper.show_info(self.tr("error_lastfm_credentials"))
                    if progress_dialog:
                        run_on_ui_thread(lambda: progress_dialog.dismiss())
                    return

                self._get_current_track_lastfm(account, params, progress_dialog)

            threading.Thread(target=run_lastfm).start()
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg in [".topsongs", ".tops"]:
            try:
                user = self.get_setting("lastfm_user", "")
                api_key = self.get_setting("lastfm_api_key", "")
                if not user or not api_key:
                    self._send_msg(params, "❌ Missing LastFM credentials in settings.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                resp = requests.get(
                    "https://ws.audioscrobbler.com/2.0/",
                    params={
                        "method": "user.gettoptracks",
                        "user": user,
                        "api_key": api_key,
                        "format": "json",
                        "limit": 10
                    },
                    timeout=10
                )
                data = resp.json()
                tracks = data.get("toptracks", {}).get("track", [])
                if not tracks:
                    self._send_msg(params, "❌ No top tracks found.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                message = self.tr("your_top_tracks")
                for i, track in enumerate(tracks, 1):
                    plays = track.get("playcount", "0")
                    message += f"{i}. {track['name']} - {track['artist']['name']} ({plays} plays)\n"

                self._send_markdown_msg(params, message)
            except Exception as e:
                self._send_msg(params, f"❌ Error: {str(e)}")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".topartists":
            try:
                user = self.get_setting("lastfm_user", "")
                api_key = self.get_setting("lastfm_api_key", "")
                if not user or not api_key:
                    self._send_msg(params, "❌ Missing LastFM credentials in settings.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                resp = requests.get(
                    "https://ws.audioscrobbler.com/2.0/",
                    params={
                        "method": "user.gettopartists",
                        "user": user,
                        "api_key": api_key,
                        "format": "json",
                        "limit": 10
                    },
                    timeout=10
                )
                data = resp.json()
                artists = data.get("topartists", {}).get("artist", [])
                if not artists:
                    self._send_msg(params, "❌ No top artists found.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                message = self.tr("your_top_artists")
                for i, artist in enumerate(artists, 1):
                    plays = artist.get("playcount", "0")
                    message += f"{i}. {artist['name']} ({plays} plays)\n"

                self._send_markdown_msg(params, message)
            except Exception as e:
                self._send_msg(params, f"❌ Error: {str(e)}")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".topalbums":
            try:
                user = self.get_setting("lastfm_user", "")
                api_key = self.get_setting("lastfm_api_key", "")
                if not user or not api_key:
                    self._send_msg(params, "❌ Missing LastFM credentials in settings.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                resp = requests.get(
                    "https://ws.audioscrobbler.com/2.0/",
                    params={
                        "method": "user.gettopalbums",
                        "user": user,
                        "api_key": api_key,
                        "format": "json",
                        "limit": 10
                    },
                    timeout=10
                )
                data = resp.json()
                albums = data.get("topalbums", {}).get("album", [])
                if not albums:
                    self._send_msg(params, "❌ No top albums found.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                message = self.tr("your_top_albums")
                for i, album in enumerate(albums, 1):
                    plays = album.get("playcount", "0")
                    message += f"{i}. {album['name']} - {album['artist']['name']} ({plays} plays)\n"

                self._send_markdown_msg(params, message)
            except Exception as e:
                self._send_msg(params, f"❌ Error: {str(e)}")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".alltime":
            try:
                user = self.get_setting("lastfm_user", "")
                api_key = self.get_setting("lastfm_api_key", "")
                if not user or not api_key:
                    self._send_msg(params, "❌ Missing LastFM credentials in settings.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                resp = requests.get(
                    "https://ws.audioscrobbler.com/2.0/",
                    params={
                        "method": "user.gettoptracks",
                        "user": user,
                        "api_key": api_key,
                        "format": "json",
                        "limit": 1
                    },
                    timeout=10
                )
                data = resp.json()
                track = data.get("toptracks", {}).get("track", [])[0]
                if not track:
                    self._send_msg(params, "❌ No tracks found.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                plays = track.get("playcount", "0")
                message = self.tr("top_track_all_time").format(
                track=track["name"],
                artist=track["artist"]["name"],
                plays=plays
)

                self._send_markdown_msg(params, message)
            except Exception as e:
                self._send_msg(params, f"❌ Error: {str(e)}")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".scrobbles":
            try:
                user = self.get_setting("lastfm_user", "")
                api_key = self.get_setting("lastfm_api_key", "")
                if not user or not api_key:
                    self._send_msg(params, "❌ Missing LastFM credentials in settings.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                resp = requests.get(
                    "https://ws.audioscrobbler.com/2.0/",
                    params={
                        "method": "user.getinfo",
                        "user": user,
                        "api_key": api_key,
                        "format": "json"
                    },
                    timeout=10
                )
                data = resp.json()
                user_info = data.get("user", {})
                if not user_info:
                    self._send_msg(params, "❌ User info not found.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                scrobbles = user_info.get("playcount", "0")
                registered = datetime.fromtimestamp(int(user_info.get("registered", {}).get("unixtime", 0))).strftime("%d/%m/%Y")
                message = self.tr("lastfm_stats_summary").format(scrobbles=scrobbles, registered=registered)

                self._send_markdown_msg(params, message)
            except Exception as e:
                self._send_msg(params, f"❌ Error: {str(e)}")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg.startswith(".user "):
            try:
                target_user = msg.split(" ", 1)[1].strip()
                api_key = self.get_setting("lastfm_api_key", "")
                if not api_key:
                    self._send_msg(params, "❌ Missing LastFM API key in settings.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                resp = requests.get(
                    "https://ws.audioscrobbler.com/2.0/",
                    params={
                        "method": "user.getinfo",
                        "user": target_user,
                        "api_key": api_key,
                        "format": "json"
                    },
                    timeout=10
                )
                data = resp.json()
                user_info = data.get("user", {})
                if not user_info:
                    self._send_msg(params, "❌ User not found.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                scrobbles = user_info.get("playcount", "0")
                registered = datetime.fromtimestamp(int(user_info.get("registered", {}).get("unixtime", 0))).strftime("%d/%m/%Y")
                country = user_info.get("country", "N/A")
                message = self.tr("user_profile_summary").format(target_user=target_user,scrobbles=scrobbles,country=country,registered=registered)

                self._send_markdown_msg(params, message)
            except Exception as e:
                self._send_msg(params, f"❌ Error: {str(e)}")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg.startswith(".compare "):
            try:
                target_user = msg.split(" ", 1)[1].strip()
                user = self.get_setting("lastfm_user", "")
                api_key = self.get_setting("lastfm_api_key", "")
                if not user or not api_key:
                    self._send_msg(params, "❌ Missing LastFM credentials in settings.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                resp = requests.get(
                    "https://ws.audioscrobbler.com/2.0/",
                    params={
                        "method": "tasteometer.compare",
                        "type1": "user",
                        "type2": "user",
                        "value1": user,
                        "value2": target_user,
                        "api_key": api_key,
                        "format": "json"
                    },
                    timeout=10
                )
                data = resp.json()
                comparison = data.get("comparison", {})
                if not comparison:
                    self._send_msg(params, "❌ Could not compare users.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                score = float(comparison.get("result", {}).get("score", 0)) * 100
                artists = comparison.get("result", {}).get("artists", {}).get("artist", [])
                message = self.tr("comparison_with_user").format(target_user=target_user,score=score)

                for artist in artists[:5]:
                    message += f"• {artist['name']}\n"

                self._send_markdown_msg(params, message)
            except Exception as e:
                self._send_msg(params, f"❌ Error: {str(e)}")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".tags":
            try:
                user = self.get_setting("lastfm_user", "")
                api_key = self.get_setting("lastfm_api_key", "")
                if not user or not api_key:
                    self._send_msg(params, "Missing LastFM credentials in settings.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                resp = requests.get(
                    "https://ws.audioscrobbler.com/2.0/",
                    params={
                        "method": "user.gettopartists",
                        "user": user,
                        "api_key": api_key,
                        "format": "json",
                        "limit": 5
                    },
                    timeout=10
                )
                data = resp.json()
                artists = data.get("topartists", {}).get("artist", [])
                if not artists:
                    self._send_msg(params, "❌ No artists found.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                tags = {}
                for artist in artists:
                    artist_resp = requests.get(
                        "https://ws.audioscrobbler.com/2.0/",
                        params={
                            "method": "artist.gettoptags",
                            "artist": artist["name"],
                            "api_key": api_key,
                            "format": "json"
                        },
                        timeout=10
                    )
                    artist_data = artist_resp.json()
                    artist_tags = artist_data.get("toptags", {}).get("tag", [])
                    for tag in artist_tags[:3]:
                        tag_name = tag["name"].lower()
                        if tag_name not in ["seen live", "all", "under 2000 listeners"]:
                            tags[tag_name] = tags.get(tag_name, 0) + 1

                sorted_tags = sorted(tags.items(), key=lambda x: x[1], reverse=True)
                message = self.tr("top_genres_title")
                for tag, count in sorted_tags[:10]:
                    message += f"• {tag.title()}\n"

                self._send_markdown_msg(params, message)
            except Exception as e:
                self._send_msg(params, f"❌ Error: {str(e)}")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".history":
            try:
                user = self.get_setting("lastfm_user", "")
                api_key = self.get_setting("lastfm_api_key", "")
                if not user or not api_key:
                    self._send_msg(params, "Missing LastFM credentials in settings.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                resp = requests.get(
                    "https://ws.audioscrobbler.com/2.0/",
                    params={
                        "method": "user.getrecenttracks",
                        "user": user,
                        "api_key": api_key,
                        "format": "json",
                        "limit": 200
                    },
                    timeout=10
                )
                data = resp.json()
                tracks = data.get("recenttracks", {}).get("track", [])
                if not tracks:
                    self._send_msg(params, "No recent tracks found.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                scrobbles_by_day = {}
                for track in tracks:
                    if "@attr" in track and track["@attr"].get("nowplaying") == "true":
                        continue
                    date = datetime.fromtimestamp(int(track["date"]["uts"])).strftime("%Y-%m-%d")
                    scrobbles_by_day[date] = scrobbles_by_day.get(date, 0) + 1

                dates = sorted(scrobbles_by_day.keys(), reverse=True)[:7]
                message = self.tr("scrobble_history_title")
                for date in dates:
                    count = scrobbles_by_day[date]
                    bar = "█" * min(count // 5, 20)
                    message += f"{date}: {count} {bar}\n"

                self._send_markdown_msg(params, message)
            except Exception as e:
                self._send_msg(params, f"❌ Error: {str(e)}")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".recap":
            try:
                user = self.get_setting("lastfm_user", "")
                api_key = self.get_setting("lastfm_api_key", "")
                if not user or not api_key:
                    self._send_msg(params, "Missing LastFM credentials in settings.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                resp = requests.get(
                    "https://ws.audioscrobbler.com/2.0/",
                    params={
                        "method": "user.gettoptracks",
                        "user": user,
                        "api_key": api_key,
                        "format": "json",
                        "period": "7day",
                        "limit": 5
                    },
                    timeout=10
                )
                data = resp.json()
                tracks = data.get("toptracks", {}).get("track", [])

                resp = requests.get(
                    "https://ws.audioscrobbler.com/2.0/",
                    params={
                        "method": "user.gettopartists",
                        "user": user,
                        "api_key": api_key,
                        "format": "json",
                        "period": "7day",
                        "limit": 3
                    },
                    timeout=10
                )
                data = resp.json()
                artists = data.get("topartists", {}).get("artist", [])

                message = self.tr("weekly_summary_title")
                if tracks:
                    message += self.tr("top_tracks_section")
                    for i, track in enumerate(tracks, 1):
                        plays = track.get("playcount", "0")
                        message += f"{i}. {track['name']} - {track['artist']['name']} ({plays} plays)\n"

                if artists:
                    message += self.tr("top_artists_section")
                    for i, artist in enumerate(artists, 1):
                        plays = artist.get("playcount", "0")
                        message += f"{i}. {artist['name']} ({plays} plays)\n"

                self._send_markdown_msg(params, message)
            except Exception as e:
                self._send_msg(params, f"❌ Error: {str(e)}")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".collage":
            try:
                user = self.get_setting("lastfm_user", "")
                api_key = self.get_setting("lastfm_api_key", "")
                if not user or not api_key:
                    self._send_msg(params, "Missing LastFM credentials in settings.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                resp = requests.get(
                    "https://ws.audioscrobbler.com/2.0/",
                    params={
                        "method": "user.gettopalbums",
                        "user": user,
                        "api_key": api_key,
                        "format": "json",
                        "period": "7day",
                        "limit": 9
                    },
                    timeout=10
                )
                data = resp.json()
                albums = data.get("topalbums", {}).get("album", [])
                if len(albums) < 9:
                    self._send_msg(params, "Not enough albums for collage.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                collage = Image.new("RGB", (900, 900), "#2b2b2b")
                for i, album in enumerate(albums):
                    image_url = album.get("image", [{}])[-1].get("#text", "")
                    if not image_url:
                        continue

                    img_resp = requests.get(image_url)
                    img = Image.open(BytesIO(img_resp.content))
                    img = img.resize((300, 300))

                    x = (i % 3) * 300
                    y = (i // 3) * 300
                    collage.paste(img, (x, y))

                output = BytesIO()
                collage.save(output, format="PNG")
                output.seek(0)

                self._send_image(params, output)
            except Exception as e:
                self._send_msg(params, f"❌ Error: {str(e)}")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg in [".today", ".week", ".month"]:
            try:
                user = self.get_setting("lastfm_user", "")
                api_key = self.get_setting("lastfm_api_key", "")
                if not user or not api_key:
                    self._send_msg(params, "Missing LastFM credentials in settings.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                period = {
                    ".today": "today",
                    ".week": "7day",
                    ".month": "1month"
                }[msg]

                resp = requests.get(
                    "https://ws.audioscrobbler.com/2.0/",
                    params={
                        "method": "user.gettoptracks",
                        "user": user,
                        "api_key": api_key,
                        "format": "json",
                        "period": period,
                        "limit": 1
                    },
                    timeout=10
                )
                data = resp.json()
                tracks = data.get("toptracks", {}).get("track", [])
                if not tracks:
                    self._send_msg(params, "No tracks found.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                track = tracks[0]
                period_name = {
                    "today": "Hoje",
                    "7day": "Esta Semana",
                    "1month": "Este Mês"
                }[period]

                message = self.tr("top_track_period").format(
                    period_name=period_name,
                    track=track["name"],
                    artist=track["artist"]["name"],
                    playcount=track.get("playcount", "0")
                )

                self._send_markdown_msg(params, message)
            except Exception as e:
                self._send_msg(params, f"❌ Error: {str(e)}")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".alltime":
            try:
                user = self.get_setting("lastfm_user", "")
                api_key = self.get_setting("lastfm_api_key", "")
                if not user or not api_key:
                    self._send_msg(params, "Missing LastFM credentials in settings.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                resp = requests.get(
                    "https://ws.audioscrobbler.com/2.0/",
                    params={
                        "method": "user.gettoptracks",
                        "user": user,
                        "api_key": api_key,
                        "format": "json",
                        "period": "overall",
                        "limit": 1
                    },
                    timeout=10
                )
                data = resp.json()
                tracks = data.get("toptracks", {}).get("track", [])
                if not tracks:
                    self._send_msg(params, "No tracks found.")
                    return HookResult(strategy=HookStrategy.CANCEL)

                track = tracks[0]
                message = self.tr("top_track_all_time")
                message += f"**{track['name']}** - {track['artist']['name']}\n"
                message += self.tr("total_track_plays").format(count=track.get("playcount", "0"))

                self._send_markdown_msg(params, message)
            except Exception as e:
                self._send_msg(params, f"❌ Error: {str(e)}")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".now":
            try:
                fragment = get_last_fragment()
                ctx = fragment.getParentActivity() if fragment and fragment.getParentActivity() else ApplicationLoader.applicationContext
                progress_dialog = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_LOADING)
                progress_dialog.set_message("Powered by nowFy")
                progress_dialog.show()
            except Exception:
                progress_dialog = None
            def run_and_close():
                try:
                    t = threading.Thread(target=self._get_current_track, args=(account, params, progress_dialog))
                    t.start()
                finally:
                    pass
            try:
                run_on_queue(run_and_close)
            except Exception as e:
                run_on_ui_thread(lambda: progress_dialog.dismiss())
                BulletinHelper.show_info(self.tr("error_unknown").format(str(e)))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".p":
            run_on_queue(lambda: self._toggle_playback(params))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".use":
            self._send_markdown_msg(params, self.tr("command_list"))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".update":
            self._send_markdown_msg(params, self.tr("update_info"))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".dev":
            run_on_ui_thread(lambda: self._open_plugin_settings())
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg.startswith(".update "):
            client_id = msg.split(" ", 1)[1].strip()
            auth_url = f"https://accounts.spotify.com/authorize?client_id={client_id}&response_type=code&redirect_uri=https://example.com/callback&scope=user-read-currently-playing%20user-read-playback-state%20user-modify-playback-state%20user-read-recently-played%20user-library-modify%20playlist-modify-private"
            caption = f"[🎵](5294137402430858861) [Update Available • Click to Open]({auth_url})"
            caption_md = parse_markdown(caption)
            params.message = caption_md.text
            params.entities = ArrayList()
            for entity in caption_md.entities:
                params.entities.add(entity.to_tlrpc_object())
            
            if progress_dialog:
                progress_dialog.dismiss()
                params.entities.add(entity.to_tlrpc_object())
            run_on_ui_thread(lambda: get_send_messages_helper().sendMessage(params))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg.startswith(".s "):
            query = msg.split(" ", 1)[1].strip()
            run_on_queue(lambda: self._search_tracks(account, params, query))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg.startswith(".vol "):
            try:
                value = msg.split(" ", 1)[1].strip()
                if value.startswith("+") or value.startswith("-"):
                    offset = int(value)
                    run_on_queue(lambda: self._adjust_volume(account, params, offset))
                else:
                    fixed = int(value)
                    run_on_queue(lambda: self._set_volume(account, params, fixed))
            except:
                self._send_markdown_msg(params, "Invalid value. Use `.vol 50` or `.vol -5`")
            return HookResult(strategy=HookStrategy.CANCEL)
                    
        if msg.startswith(".play "):
            track_number = msg.split(" ", 1)[1].strip()
            run_on_queue(lambda: self._play_track(account, params, track_number))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".like":
            run_on_queue(lambda: self._like_track(account, params))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg.startswith(".like "):
            track_number = msg.split(" ", 1)[1].strip()
            run_on_queue(lambda: self._like_track(account, params, track_number))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".shuffle":
            run_on_queue(lambda: self._toggle_shuffle(account, params))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".repeat":
            run_on_queue(lambda: self._toggle_repeat(account, params))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg.startswith(".queue "):
            track_number = msg.split(" ", 1)[1].strip()
            run_on_queue(lambda: self._add_to_queue(account, params, track_number))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg.startswith(".mix "):
            query = msg.split(" ", 1)[1].strip()
            run_on_queue(lambda: self._create_mix(account, params, query))
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".list":
            try:
                fragment = get_last_fragment()
                ctx = fragment.getParentActivity() if fragment and fragment.getParentActivity() else ApplicationLoader.applicationContext
                progress_dialog = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_LOADING)
                progress_dialog.set_message(self.tr("loading_recent_tracks"))
                progress_dialog.show()
                def run_and_close():
                    try:
                        run_on_ui_thread(lambda: progress_dialog.set_progress(10))
                        img_resp = requests.get(self.tr('playblack_image'), stream=True, timeout=5)
                        if not img_resp.ok:
                            raise Exception(f"Failed to download image: {img_resp.status_code}")
                        img_bytes = BytesIO()
                        for chunk in img_resp.iter_content(8192):
                            img_bytes.write(chunk)
                        img_bytes.seek(0)
                        
                        run_on_ui_thread(lambda: progress_dialog.set_progress(30))
                        img = Image.open(img_bytes).convert("RGBA")
                        img = img.resize((935, 514), Image.LANCZOS)
                        img = img.convert("RGB")
                        
                        run_on_ui_thread(lambda: progress_dialog.set_progress(40))
                        base_dir = ApplicationLoader.getFilesDirFixed()
                        temp_dir = File(base_dir, "nowFy")
                        if not temp_dir.exists():
                            temp_dir.mkdirs()
                        filename = f"{uuid.uuid4()}.png"
                        temp_path = File(temp_dir, filename).getAbsolutePath()
                        img.save(temp_path, format='PNG', quality=95, optimize=True)
                        
                        run_on_ui_thread(lambda: progress_dialog.set_progress(50))
                        send_helper = get_send_messages_helper()
                        photo = send_helper.generatePhotoSizes(temp_path, None)
                        
                        run_on_ui_thread(lambda: progress_dialog.set_progress(60))
                        token = self._get_access_token()
                        if not token:
                            run_on_ui_thread(lambda: progress_dialog.dismiss())
                            self._send_markdown_msg(params, "Could not authenticate with Spotify.")
                            return
                            
                        run_on_ui_thread(lambda: progress_dialog.set_progress(70))
                        resp = requests.get(
                            "https://api.spotify.com/v1/me/player/recently-played?limit=5",
                            headers={"Authorization": f"Bearer {token}"},
                            timeout=5
                        )
                        
                        run_on_ui_thread(lambda: progress_dialog.set_progress(80))
                        if resp.status_code == 200:
                            data = resp.json()
                            tracks = data.get("items", [])
                            
                            if tracks:
                                message = self.tr("playback_highlights") + "\n\n"
                                for item in tracks:
                                    track = item["track"]
                                    title = track["name"]
                                    artists = ", ".join([a["name"] for a in track["artists"]])
                                    spotify_link = track["external_urls"]["spotify"]
                                    universal_link = self.get_universal_song_link(spotify_link) or spotify_link
                                    spotify_link_text = self.get_setting("spotify_link_text", "")
                                    if not spotify_link_text.strip():
                                        spotify_link_text = self.tr("spotify_link_text")
                                    if self.get_setting("use_spotify_link", False):
                                        message += f"˪ {title}\nby {artists}\n[🎵](5294137402430858861) [{spotify_link_text}]({spotify_link})\n\n"
                                    else:
                                        message += f"˪ {title}\nby {artists}\n\n"
                                
                                caption_md = parse_markdown(message.strip())
                                params.caption = caption_md.text
                                params.entities = ArrayList()
                                for entity in caption_md.entities:
                                    params.entities.add(entity.to_tlrpc_object())
                        
                        run_on_ui_thread(lambda: progress_dialog.set_progress(90))
                        params.photo = photo
                        params.path = temp_path
                        params.message = None
                        run_on_ui_thread(lambda: progress_dialog.set_progress(100))
                        run_on_ui_thread(lambda: send_helper.sendMessage(params))
                        run_on_ui_thread(lambda: progress_dialog.dismiss())
                    except Exception as e:
                        run_on_ui_thread(lambda: progress_dialog.dismiss())
                        BulletinHelper.show_info(self.tr("error_unknown").format(str(e)))
                run_on_queue(run_and_close)
            except Exception as e:
                if progress_dialog:
                    run_on_ui_thread(lambda: progress_dialog.dismiss())
                self._send_markdown_msg(params, f"❌ Error: {str(e)}")
            return HookResult(strategy=HookStrategy.CANCEL)

        if msg == ".myfm":
            self._send_msg(params, (
                f"{self.tr('lastfm_commands_title')}\n\n"
                f"{self.tr('lastfm_commands_description')}\n\n"
                "• .setuser [username] - " + self.tr("lastfm_setuser_help") + "\n"
                "• .setkey [api_key] - " + self.tr("lastfm_setkey_help") + "\n"
                "• .fm - " + self.tr("lastfm_now_help") + "\n"
                "• .topsongs - " + self.tr("lastfm_topsongs_help") + "\n"
                "• .topartists - " + self.tr("lastfm_topartists_help") + "\n"
                "• .topalbums - " + self.tr("lastfm_topalbums_help") + "\n"
                "• .collage - " + self.tr("lastfm_collage_help") + "\n"
                "• .recap - " + self.tr("lastfm_recap_help") + "\n"
                "• .history - " + self.tr("lastfm_history_help") + "\n"
                "• .tags - " + self.tr("lastfm_tags_help") + "\n"
                "• .compare - " + self.tr("lastfm_compare_help") + "\n"
                "• .today - " + self.tr("lastfm_today_help") + "\n"
                "• .week - " + self.tr("lastfm_week_help") + "\n"
                "• .month - " + self.tr("lastfm_month_help") + "\n"
                "• .alltime - " + self.tr("lastfm_alltime_help") + "\n"
                "• .scrobbles - " + self.tr("lastfm_scrobbles_help") + "\n\n"
                f"{self.tr('lastfm_api_guide')}"
            ))
            return HookResult(strategy=HookStrategy.CANCEL)
            
        if msg == ".help":
            self._send_msg(params, (
                    f"{self.tr('nowfy_plugin_title')}\n\n"
                    f"{self.tr('guide_with_images')}\n"
                    "https://github.com/soumaki/nowFy/blob/main/guide/nowfy.md#spotify-plugin--step-by-step-login-guide\n\n"
                    f"{self.tr('spotify_login_title')}\n\n"
                    "1. Go to https://developer.spotify.com/dashboard/create\n"
                    f"2. {self.tr('create_app')}\n"
                    f"   • {self.tr('set_redirect_uri')}\n"
                    f"   • {self.tr('accept_terms')}\n"
                    f"3. {self.tr('copy_credentials')}\n\n"
                    f"{self.tr('setup_commands')}\n\n"
                    f"• .setid YOUR_CLIENT_ID – {self.tr('save_client_id')}\n"
                    f"• .setsecret YOUR_CLIENT_SECRET – {self.tr('save_client_secret')}\n"
                    f"• .update – {self.tr('generate_login_link')}\n"
                    f"   - {self.tr('open_link_login')}\n"
                    f"   - {self.tr('click_agree')}\n"
                    f"   - {self.tr('return_to_chat')}\n"
                    f"   - {self.tr('url_example')}\n"
                    "     https://example.com/callback?code=YOUR_CODE_HERE\n"
                    f"   - {self.tr('copy_code')}\n"
                    f"• .code YOUR_CODE – {self.tr('paste_code')}\n"
                    f"• .check – {self.tr('verify_setup')}\n\n"
                    f"{self.tr('lastfm_login_title')}\n\n"
                    "1. Go to https://www.last.fm/api/account/create\n"
                    f"2. {self.tr('get_api_key')}\n"
                    "3. Use:\n"
                    "   • .setuser YOUR_LASTFM_USERNAME\n"
                    "   • .setkey YOUR_LASTFM_API_KEY\n\n"
                    f"{self.tr('youtube_api_guide')}\n"
                    "https://github.com/soumaki/nowFy/blob/main/ytkey/guide.md#youtube-api-key-youtube-data-api-v3\n\n"
                    f"{self.tr('youtube_api_title')}\n\n"
                    f"1. {self.tr('go_to_console')}\n"
                    f"2. {self.tr('create_project')}\n"
                    f"3. {self.tr('enable_api')}\n"
                    f"4. {self.tr('create_credentials')}\n"
                    "5. Use:\n"
                    "   • .setyt YOUR_YOUTUBE_API_KEY\n\n"
                    f"{self.tr('playback_controls')}\n"
                    f"• .now – {self.tr('show_playing_spotify')}\n"
                    f"• .fm – {self.tr('show_playing_lastfm')}\n"
                    f"• .n – {self.tr('next_track')}\n"
                    f"• .b – {self.tr('previous_track')}\n"
                    f"• .p – {self.tr('play_pause')}\n"
                    f"• .use – {self.tr('switch_display')}\n"
                    f"• .update – {self.tr('update_plugin')}\n\n"
                    f"{self.tr('need_help')}\n\n"
                    f"FAQ: @nowFyDOCS\n\n"
                    f"{self.tr('powered_by')}"

            ))
            return HookResult(strategy=HookStrategy.CANCEL)

        return HookResult()

    def _exchange_code(self, params, code, progress_dialog=None):
        client_id = self.get_setting("client_id", "")
        client_secret = self.get_setting("client_secret", "")
        if not client_id or not client_secret:
            self._send_markdown_msg(params, "ⓘ Missing credentials in settings. Skill issues? 🤨")
            return

        try:
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(40))

            background.save(temp_path, "PNG")
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(60))
                time.sleep(0.5)

            auth = base64.b64encode(f"{client_id}:{client_secret}".encode()).decode()
            resp = requests.post(
                "https://accounts.spotify.com/api/token",
                data={"grant_type": "authorization_code", "code": code, "redirect_uri": "https://example.com/callback"},
                headers={"Authorization": f"Basic {auth}"},
                timeout=10
            )
            data = resp.json()

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(70))
                time.sleep(0.5)

            if "refresh_token" in data:
                self.set_setting("refresh_token", data["refresh_token"])
            else:
                BulletinHelper.show_info(self.tr("error_refresh_token"))
        except Exception:
            BulletinHelper.show_info(self.tr("error_exchange_code"))

    def _validate_credentials(self, params, progress_dialog=None):
        try:
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(30))
                time.sleep(0.5)

            token = self._get_access_token()
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(70))
                time.sleep(0.5)

            if token:
                message = f"[⤵️](5256182535917940722) Spotify credentials are valid!"
                parsed = parse_markdown(message)
                params.message = parsed.text
                params.entities = ArrayList()
                for entity in parsed.entities:
                    params.entities.add(entity.to_tlrpc_object())
                if progress_dialog:
                    run_on_ui_thread(lambda: progress_dialog.set_progress(100))
                    time.sleep(0.2)
                run_on_ui_thread(lambda: get_send_messages_helper().sendMessage(params))
                return True
            else:
                self._send_markdown_msg(params, "Error validating Spotify credentials.")
                return False
        except Exception as e:
            self._send_markdown_msg(params, f"❌ Error: {str(e)}")
            return False

    def _skip_track(self, params):
        progress_dialog = []
        current_progress = 0
        start_time = time.time()
        
        def create_and_show_dialog():
            fragment = get_last_fragment()
            ctx = fragment.getParentActivity() if fragment and fragment.getParentActivity() else ApplicationLoader.applicationContext
            dialog = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_LOADING)
            dialog.set_message(self.tr("next_track"))
            dialog.set_progress(0)
            dialog.show()
            progress_dialog.append(dialog)
            
            def update_progress():
                if progress_dialog:
                    elapsed = time.time() - start_time
                    progress = min(int((elapsed / 1.5) * 100), 100)  # Cap at 100%
                    nonlocal current_progress
                    current_progress = progress
                    run_on_ui_thread(lambda: progress_dialog[0].set_progress(progress))
                    if progress < 100:
                        run_on_ui_thread(update_progress, delay=100)
            
            run_on_ui_thread(update_progress, delay=100)
        
        run_on_ui_thread(create_and_show_dialog)
        
        try:
            token = self._get_access_token()
            if not token:
                self._send_msg(params, self.tr("error_auth"))
                return
            
            resp = requests.post(
                "https://api.spotify.com/v1/me/player/next",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if resp.status_code in [200, 202, 204]:
                while time.time() - start_time < 1.5 and current_progress < 100:
                    time.sleep(0.1)
            else:
                if resp.status_code == 404:
                    BulletinHelper.show_info(self.tr("error_no_device"))
                elif resp.status_code == 403:
                    BulletinHelper.show_info(self.tr("error_permission"))
                elif resp.status_code == 401:
                    BulletinHelper.show_info(self.tr("error_token"))
                else:
                    try:
                        error_data = resp.json()
                        BulletinHelper.show_info(self.tr("error_unknown").format(error_data.get('error', {}).get('message', 'Unknown')))
                    except:
                        BulletinHelper.show_info(self.tr("error_code").format(resp.status_code))
        except Exception as e:
            BulletinHelper.show_info(self.tr("error_skip_exception").format(str(e)))
        finally:
            if progress_dialog:
                try:
                    run_on_ui_thread(lambda: progress_dialog[0].dismiss() if progress_dialog else None)
                except Exception:
                    pass

    def _previous_track(self, params):
        progress_dialog = []
        current_progress = 0
        start_time = time.time()
        
        def create_and_show_dialog():
            fragment = get_last_fragment()
            ctx = fragment.getParentActivity() if fragment and fragment.getParentActivity() else ApplicationLoader.applicationContext
            dialog = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_LOADING)
            dialog.set_message(self.tr("previous_track"))
            dialog.set_progress(0)
            dialog.show()
            progress_dialog.append(dialog)
            
            def update_progress():
                if progress_dialog:
                    elapsed = time.time() - start_time
                    progress = min(int((elapsed / 1.5) * 100), 100)  # Cap at 100%
                    nonlocal current_progress
                    current_progress = progress
                    run_on_ui_thread(lambda: progress_dialog[0].set_progress(progress))
                    if progress < 100:
                        run_on_ui_thread(update_progress, delay=100)
            
            run_on_ui_thread(update_progress, delay=100)
        
        run_on_ui_thread(create_and_show_dialog)
        
        try:
            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info(self.tr("error_auth"))
                return
            
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(20))
            
            resp = requests.post(
                "https://api.spotify.com/v1/me/player/previous",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if resp.status_code in [200, 202, 204]:
                while time.time() - start_time < 1.5 and current_progress < 100:
                    time.sleep(0.1)
            else:
                if resp.status_code == 404:
                    BulletinHelper.show_info(self.tr("error_no_device"))
                elif resp.status_code == 403:
                    BulletinHelper.show_info(self.tr("error_permission"))
                elif resp.status_code == 401:
                    BulletinHelper.show_info(self.tr("error_token"))
                else:
                    try:
                        error_data = resp.json()
                        BulletinHelper.show_info(self.tr("error_unknown").format(error_data.get('error', {}).get('message', 'Unknown')))
                    except:
                        BulletinHelper.show_info(self.tr("error_code").format(resp.status_code))
        except Exception as e:
            BulletinHelper.show_info(self.tr("error_previous_exception").format(str(e)))
        finally:
            if progress_dialog:
                try:
                    run_on_ui_thread(lambda: progress_dialog[0].dismiss() if progress_dialog else None)
                except Exception:
                    pass

    def _toggle_playback(self, params):
        try:
            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info(self.tr("error_auth"))
                return

            status_resp = requests.get(
                "https://api.spotify.com/v1/me/player",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if status_resp.status_code == 204:
                BulletinHelper.show_info(self.tr("no_active_device"))
                return

            status_data = status_resp.json()
            is_playing = status_data.get("is_playing", False)

            if is_playing:
                BulletinHelper.show_info(self.tr("bulletin_pausing"))
            else:
                BulletinHelper.show_info(self.tr("bulletin_playing"))

            endpoint = "pause" if is_playing else "play"
            resp = requests.put(
                f"https://api.spotify.com/v1/me/player/{endpoint}",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if resp.status_code not in [200, 202, 204]:
                error_msg = self.tr("error_playback")
                if resp.status_code == 404:
                    error_msg += self.tr("error_no_device")
                elif resp.status_code == 403:
                    error_msg += self.tr("error_permission")
                elif resp.status_code == 401:
                    error_msg += self.tr("error_token")
                else:
                    try:
                        error_data = resp.json()
                        error_msg += self.tr("error_unknown").format(error_data.get('error', {}).get('message', 'Unknown'))
                    except:
                        error_msg += self.tr("error_code").format(resp.status_code)
                BulletinHelper.show_info(error_msg)

        except Exception as e:
            BulletinHelper.show_info(self.tr("error_playback_exception").format(str(e)))

    def _get_access_token(self):
        client_id = self.get_setting("client_id", "")
        client_secret = self.get_setting("client_secret", "")
        refresh_token = self.get_setting("refresh_token", "")
        if not client_id or not client_secret or not refresh_token:
            return None
        try:
            auth = base64.b64encode(f"{client_id}:{client_secret}".encode()).decode()
            resp = requests.post(
                "https://accounts.spotify.com/api/token",
                data={"grant_type": "refresh_token", "refresh_token": refresh_token},
                headers={"Authorization": f"Basic {auth}"},
                timeout=5
            )
            if resp.status_code != 200:
                print(f"Error refreshing token: {resp.status_code} - {resp.text}")
                return None
            data = resp.json()
            if "access_token" not in data:
                print(f"No access token in response: {data}")
                return None
            return data["access_token"]
        except Exception as e:
            print(f"Exception in _get_access_token: {str(e)}")
            return None

    def _truncate(self, text, limit=36):
        return text if len(text) <= limit else text[:limit - 3] + "..."

    def _limit_words(self, text, max_words=4):
        words = text.split()
        return " ".join(words[:max_words]) + ("..." if len(words) > max_words else "")

    def _draw_progress_bar_style_0(self, draw, bar_margin, bar_y, bar_width, bar_height, progress_width):
        draw.rounded_rectangle(
            (bar_margin, bar_y, bar_margin + bar_width, bar_y + bar_height),
            radius=bar_height // 2,
            fill=(100, 100, 100, 120)
        )
        draw.rounded_rectangle(
            (bar_margin, bar_y, bar_margin + progress_width, bar_y + bar_height),
            radius=bar_height // 2,
            fill=(80, 200, 255, 255)
        )
        draw.ellipse(
            (bar_margin + progress_width - bar_height, bar_y,
            bar_margin + progress_width + bar_height, bar_y + bar_height),
            fill=(255, 255, 255)
        )

    def _draw_progress_bar_style_1(self, draw, bar_margin, bar_y, bar_width, bar_height, progress_width):
        glow = (255, 140, 80)
        draw.rounded_rectangle(
            (bar_margin, bar_y, bar_margin + bar_width, bar_y + bar_height),
            radius=bar_height // 2,
            fill=(60, 40, 30, 130)
        )
        draw.rounded_rectangle(
            (bar_margin, bar_y, bar_margin + progress_width, bar_y + bar_height),
            radius=bar_height // 2,
            fill=glow
        )
        draw.ellipse(
            (bar_margin + progress_width - bar_height, bar_y,
            bar_margin + progress_width + bar_height, bar_y + bar_height),
            fill=glow
        )

    def _draw_progress_bar_style_2(self, draw, bar_margin, bar_y, bar_width, bar_height, progress_width):
        draw.rounded_rectangle(
            (bar_margin, bar_y, bar_margin + bar_width, bar_y + bar_height),
            radius=bar_height // 2,
            fill=(150, 70, 30, 110)
        )
        draw.rounded_rectangle(
            (bar_margin, bar_y, bar_margin + progress_width, bar_y + bar_height),
            radius=bar_height // 2,
            fill=(255, 140, 0, 220)
        )
        marker_x = bar_margin + progress_width
        halo_radius = bar_height
        core_radius = bar_height // 2
        draw.ellipse(
            (marker_x - halo_radius, bar_y - halo_radius // 3,
            marker_x + halo_radius, bar_y + bar_height + halo_radius // 3),
            fill=(255, 215, 0, 90)
        )
        draw.ellipse(
            (marker_x - core_radius, bar_y + bar_height // 4,
            marker_x + core_radius, bar_y + bar_height - bar_height // 4),
            fill=(255, 69, 0, 255)
        )

    def _draw_progress_bar_style_3(self, draw, bar_margin, bar_y, bar_width, bar_height, progress_width):
        base_color = (50, 120, 180, 140)
        progress_color = (100, 200, 255, 230)
        draw.rounded_rectangle(
            (bar_margin, bar_y, bar_margin + bar_width, bar_y + bar_height),
            radius=bar_height // 2,
            fill=base_color
        )
        draw.rounded_rectangle(
            (bar_margin, bar_y, bar_margin + progress_width, bar_y + bar_height),
            radius=bar_height // 2,
            fill=progress_color
        )
        draw.ellipse(
            (
                bar_margin + progress_width - bar_height // 2,
                bar_y - bar_height // 4,
                bar_margin + progress_width + bar_height // 2,
                bar_y + bar_height + bar_height // 4
            ),
            fill=progress_color
        )

    def _draw_progress_bar_style_4(self, draw, bar_margin, bar_y, bar_width, bar_height, progress_width):
        for i in range(bar_width):
            r = int(255 * i / bar_width)
            g = 50
            b = 255 - r
            draw.line(
                [(bar_margin + i, bar_y), (bar_margin + i, bar_y + bar_height)],
                fill=(r, g, b),
                width=1
            )
        draw.ellipse(
            (
                bar_margin + progress_width - bar_height,
                bar_y,
                bar_margin + progress_width + bar_height,
                bar_y + bar_height
            ),
            fill=(255, 255, 255)
        )

    def _draw_progress_bar_style_5(self, draw, bar_margin, bar_y, bar_width, bar_height, progress_width):
        draw.rounded_rectangle((bar_margin, bar_y, bar_margin + bar_width, bar_y + bar_height),
        radius=bar_height // 2, fill=(80, 60, 30))
        draw.rounded_rectangle((bar_margin, bar_y, bar_margin + progress_width, bar_y + bar_height),
        radius=bar_height // 2, fill=(255, 215, 0))
        draw.ellipse((bar_margin + progress_width - bar_height, bar_y, bar_margin + progress_width + bar_height, bar_y + bar_height), fill=(255, 245, 180))

    def _draw_progress_bar_style_6(self, draw, bar_margin, bar_y, bar_width, bar_height, progress_width):
        seg_w = 12
        gap = 4
        count = (bar_width + gap) // (seg_w + gap)
        filled = int(count * (progress_width / bar_width))
        for i in range(int(count)):
            x = bar_margin + i * (seg_w + gap)
            height = bar_height + (i % 2) * 3
            color = (255, 255, 255) if i < filled else (120, 120, 120, 80)
            draw.rounded_rectangle((x, bar_y, x + seg_w, bar_y + height), radius=4, fill=color)

    def _get_current_track_lastfy(self, account, params, progress_dialog=None):
        try:
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(10))
            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info(self.tr("error_auth"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except Exception: pass
                return

            resp = requests.get(
                "https://api.spotify.com/v1/me/player/currently-playing",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(30))

            if resp.status_code == 204 or not resp.content:
                BulletinHelper.show_info(self.tr("no_track_playing"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except Exception: pass
                return

            data = resp.json()
            track = data["item"]
            title = self._truncate(track["name"]).upper()
            artists = ", ".join([a["name"] for a in track["artists"]])
            album = self._limit_words(track["album"]["name"]).upper()
            images = track["album"]["images"]
            if not images:
                raise Exception("No album images found.")
            cover_url = max(images, key=lambda i: i["width"])["url"]
            spotify_link = track["external_urls"]["spotify"]
            universal_link = self.get_universal_song_link(spotify_link) or spotify_link

            send_helper = get_send_messages_helper()
            base_dir = ApplicationLoader.getFilesDirFixed()
            temp_dir = File(base_dir, "nowFy")
            if not temp_dir.exists():
                temp_dir.mkdirs()
            filename = f"{uuid.uuid4()}.png"
            temp_path = File(temp_dir, filename).getAbsolutePath()

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(50))
            try:
                img_resp = requests.get(cover_url, stream=True, timeout=10)
                img_resp.raise_for_status()
                album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                album_cover = album_cover.resize((350, 350), Image.LANCZOS)
            except:
                album_cover = Image.new("RGBA", (350, 350), color=(30, 30, 30))

            card_width, card_height = 1012, 512
            bg_cover = album_cover.copy()
            bg_ratio = max(card_width / bg_cover.width, card_height / bg_cover.height)
            new_size = (int(bg_cover.width * bg_ratio), int(bg_cover.height * bg_ratio))
            bg_cover = bg_cover.resize(new_size, Image.LANCZOS)
            background = Image.new("RGBA", (card_width, card_height))
            blur_bg = bg_cover.filter(ImageFilter.GaussianBlur(30))
            background.paste(blur_bg, (0, 0))
            overlay = Image.new("RGBA", (card_width, card_height), (0, 0, 0, 150))
            background = Image.alpha_composite(background.convert("RGBA"), overlay)

            mask = Image.new("L", (350, 350), 0)
            draw_mask = ImageDraw.Draw(mask)
            draw_mask.rounded_rectangle((0, 0, 350, 350), radius=23, fill=255)
            album_cover.putalpha(mask)

            padding_left = 50
            padding_top = (card_height - 350) // 2
            background.paste(album_cover, (padding_left, padding_top), album_cover)

            font_scale = 1.0
            base_title_size = 33
            base_artist_size = 27
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(70))
            try:
                font_family = self.get_setting("font_family", 0)
                custom_fonts = self._list_custom_fonts()
                if font_family > 4 and custom_fonts:
                    custom_font_index = font_family - 5
                    if custom_font_index < len(custom_fonts):
                        font_path = os.path.join(self._get_custom_fonts_path(), custom_fonts[custom_font_index])
                    else:
                        font_path = "/system/fonts/NotoSansCJK-Regular.ttc"
                else:
                    font_paths = {
                        0: "/system/fonts/NotoSansCJK-Regular.ttc",
                        1: "/system/fonts/ComingSoon.ttf",
                        2: "/system/fonts/MiClockTibetan-Thin.ttf",
                        3: "/system/fonts/NotoNaskhArabic-Bold.ttf",
                        4: "/system/fonts/SourceSansPro-Bold.ttf"
                    }
                    font_path = font_paths.get(font_family, font_paths[0])
                font_title = ImageFont.truetype(font_path, int(base_title_size * font_scale))
                font_artist = ImageFont.truetype(font_path, int(base_artist_size * font_scale))
            except:
                font_title = font_artist = ImageFont.load_default()

            def limit_text_by_chars(text, max_chars):
                return text[:max_chars].rstrip() + "..." if len(text) > max_chars else text

            draw = ImageDraw.Draw(background)
            text_x = padding_left + 350 + 40
            text_y = padding_top + 50

            draw.text((text_x, text_y), "Listening to", font=font_artist, fill=(210, 210, 210))
            text_y += font_artist.getbbox("Listening to")[3] + 8

            title_display = limit_text_by_chars(title, 21)
            draw.text((text_x, text_y), title_display, font=font_title, fill="white")
            text_y += font_title.getbbox(title_display)[3] + 6

            artist_display = limit_text_by_chars(artists, 25)
            draw.text((text_x, text_y), f"by {artist_display}", font=font_artist, fill=(180, 180, 180))
            text_y += font_artist.getbbox(f"by {artist_display}")[3] + 12

            album_display = limit_text_by_chars(album, 20)
            draw.text((text_x, text_y), f"• {album_display}", font=font_artist, fill=(200, 200, 200))
            text_y += font_artist.getbbox(f"• {album_display}")[3] + 20

            icon_url = self._get_service_icon("Spotify")
            icon_size = 40
            if icon_url:
                try:
                    response = requests.get(icon_url, stream=True)
                    if response.status_code == 200:
                        icon_image = Image.open(BytesIO(response.content)).convert("RGBA")
                        icon_image = icon_image.resize((icon_size, icon_size), Image.LANCZOS)
                        background.paste(icon_image, (text_x, text_y), icon_image)
                except Exception as e:
                    print(f"Spotify icon error: {e}")

            background.convert("RGB").save(temp_path)

            footer = self.get_setting("custom_footer_text", "♪ ıllıllı - I'm using nowFy!\n╰ by @exteraDevPlugins")
            show_footer = self.get_setting("show_footer_caption", True)
            show_link = self.get_setting("show_track_link", True)
            use_spotify = self.get_setting("use_spotify_link", False)
            link_text = self.get_setting("spotify_link_text", "").strip()

            if not link_text:
                link_text = "Spotify" if use_spotify else "song.link"
            link = spotify_link if use_spotify else universal_link

            if not show_footer:
                footer = ""

            if show_link:
                caption = f"{footer} [{link_text}]({link})"
            else:
                caption = footer

            caption_md = parse_markdown(caption)
            params.caption = caption_md.text
            params.entities = ArrayList()
            for entity in caption_md.entities:
                params.entities.add(entity.to_tlrpc_object())

            photo = send_helper.generatePhotoSizes(temp_path, None)
            params.photo = photo
            params.path = temp_path
            params.message = None
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(90))
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(90))
                
            run_on_ui_thread(lambda: send_helper.sendMessage(params))
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(100))
                run_on_ui_thread(lambda: progress_dialog.dismiss())

        except Exception as e:
            if progress_dialog:
                try: progress_dialog.dismiss()
                except Exception: pass
            self._send_markdown_msg(params, f"❌ Error: {str(e)}")


    def _get_current_track_lastfyw(self, account, params, progress_dialog=None):
        try:
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(10))
            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info(self.tr("error_auth"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except Exception: pass
                return

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(20))

            resp = requests.get(
                "https://api.spotify.com/v1/me/player/currently-playing",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(30))

            if resp.status_code == 204 or not resp.content:
                BulletinHelper.show_info(self.tr("no_track_playing"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except Exception: pass
                return

            data = resp.json()
            track = data["item"]
            title = self._truncate(track["name"]).upper()
            artists = ", ".join([a["name"] for a in track["artists"]])
            album = self._limit_words(track["album"]["name"]).upper()
            images = track["album"]["images"]
            if not images:
                raise Exception("No album images found.")
            cover_url = max(images, key=lambda i: i["width"])["url"]
            spotify_link = track["external_urls"]["spotify"]
            universal_link = self.get_universal_song_link(spotify_link) or spotify_link

            send_helper = get_send_messages_helper()
            base_dir = ApplicationLoader.getFilesDirFixed()
            temp_dir = File(base_dir, "nowFy")
            if not temp_dir.exists():
                temp_dir.mkdirs()
            filename = f"{uuid.uuid4()}.png"
            temp_path = File(temp_dir, filename).getAbsolutePath()

            try:
                img_resp = requests.get(cover_url, stream=True, timeout=10)
                img_resp.raise_for_status()
                album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                album_cover = album_cover.resize((350, 350), Image.LANCZOS)
            except:
                album_cover = Image.new("RGBA", (350, 350), color=(30, 30, 30))

            card_width, card_height = 1012, 512
            background = Image.new("RGBA", (card_width, card_height), (245, 245, 245, 255))  # Branco suave

            glass_panel = Image.new("RGBA", (card_width - 100, card_height - 100), (255, 255, 255, 20))
            draw_glass = ImageDraw.Draw(glass_panel)
            draw_glass.rounded_rectangle((0, 0, glass_panel.width, glass_panel.height), 40, fill=(255, 255, 255, 70))
            background.paste(glass_panel, (50, 50), glass_panel)

            mask = Image.new("L", (350, 350), 0)
            draw_mask = ImageDraw.Draw(mask)
            draw_mask.rounded_rectangle((0, 0, 350, 350), radius=30, fill=255)
            album_cover.putalpha(mask)

            padding_left = 50
            padding_top = (card_height - 350) // 2
            background.paste(album_cover, (padding_left, padding_top), album_cover)

            font_scale = 1.0
            base_title_size = 34
            base_artist_size = 28

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(70))
            try:
                font_family = self.get_setting("font_family", 0)
                font_paths = {
                    0: "/system/fonts/NotoSansCJK-Regular.ttc",
                    1: "/system/fonts/ComingSoon.ttf",
                    2: "/system/fonts/MiClockTibetan-Thin.ttf",
                    3: "/system/fonts/NotoNaskhArabic-Bold.ttf",
                    4: "/system/fonts/SourceSansPro-Bold.ttf"
                }
                font_path = font_paths.get(font_family, font_paths[0])
                font_title = ImageFont.truetype(font_path, int(base_title_size * font_scale))
                font_artist = ImageFont.truetype(font_path, int(base_artist_size * font_scale))
            except:
                font_title = font_artist = ImageFont.load_default()

            def limit_text_by_chars(text, max_chars):
                return text[:max_chars].rstrip() + "..." if len(text) > max_chars else text

            draw = ImageDraw.Draw(background)
            text_x = padding_left + 350 + 40
            text_y = padding_top + 50

            draw.text((text_x, text_y), "Listening to", font=font_artist, fill=(30, 30, 30))
            text_y += font_artist.getbbox("Listening to")[3] + 8

            title_display = limit_text_by_chars(title, 24)
            draw.text((text_x, text_y), title_display, font=font_title, fill=(0, 0, 0))
            text_y += font_title.getbbox(title_display)[3] + 6

            artist_display = limit_text_by_chars(artists, 30)
            draw.text((text_x, text_y), f"by {artist_display}", font=font_artist, fill=(50, 50, 50))
            text_y += font_artist.getbbox(f"by {artist_display}")[3] + 12

            album_display = limit_text_by_chars(album, 24)
            draw.text((text_x, text_y), f"• {album_display}", font=font_artist, fill=(70, 70, 70))

            icon_url = self._get_service_icon("SpotifyBlack")
            if icon_url:
                try:
                    icon_resp = requests.get(icon_url, stream=True)
                    if icon_resp.status_code == 200:
                        icon = Image.open(BytesIO(icon_resp.content)).convert("RGBA")
                        icon = icon.resize((40, 40), Image.LANCZOS)
                        background.paste(icon, (text_x, text_y + 45), icon)
                except: pass

            background.convert("RGB").save(temp_path)

            footer = self.get_setting("custom_footer_text", "♪ ıllıllı - I'm using nowFy!\n╰ by @exteraDevPlugins")
            show_footer = self.get_setting("show_footer_caption", True)
            show_link = self.get_setting("show_track_link", True)
            use_spotify = self.get_setting("use_spotify_link", False)
            link_text = self.get_setting("spotify_link_text", "").strip()

            if not link_text:
                link_text = "Spotify" if use_spotify else "song.link"
            link = spotify_link if use_spotify else universal_link

            if not show_footer:
                footer = ""

            if show_link:
                caption = f"{footer} [{link_text}]({link})"
            else:
                caption = footer

            caption_md = parse_markdown(caption)
            params.caption = caption_md.text
            params.entities = ArrayList()
            for entity in caption_md.entities:
                params.entities.add(entity.to_tlrpc_object())

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(90))
            photo = send_helper.generatePhotoSizes(temp_path, None)
            params.photo = photo
            params.path = temp_path
            params.message = None
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(80))
            run_on_ui_thread(lambda: send_helper.sendMessage(params))
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(100))
                run_on_ui_thread(lambda: progress_dialog.dismiss())

        except Exception as e:
            if progress_dialog:
                try: progress_dialog.dismiss()
                except Exception: pass
            self._send_markdown_msg(params, f"❌ Error: {str(e)}")

    def _get_current_track_lastfyblack(self, account, params, progress_dialog=None): 
        try:
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(10))
            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info(self.tr("error_auth"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except Exception: pass
                return

            resp = requests.get(
                "https://api.spotify.com/v1/me/player/currently-playing",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if resp.status_code == 204 or not resp.content:
                BulletinHelper.show_info(self.tr("no_track_playing"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except Exception: pass
                return

            data = resp.json()
            track = data["item"]
            title = self._truncate(track["name"]).upper()
            artists = ", ".join([a["name"] for a in track["artists"]])
            album = self._limit_words(track["album"]["name"]).upper()
            images = track["album"]["images"]
            if not images:
                raise Exception("No album images found.")
            cover_url = max(images, key=lambda i: i["width"])["url"]
            spotify_link = track["external_urls"]["spotify"]
            universal_link = self.get_universal_song_link(spotify_link) or spotify_link

            send_helper = get_send_messages_helper()
            base_dir = ApplicationLoader.getFilesDirFixed()
            temp_dir = File(base_dir, "nowFy")
            if not temp_dir.exists():
                temp_dir.mkdirs()
            filename = f"{uuid.uuid4()}.png"
            temp_path = File(temp_dir, filename).getAbsolutePath()

            try:
                img_resp = requests.get(cover_url, stream=True, timeout=10)
                img_resp.raise_for_status()
                album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                album_cover = album_cover.resize((350, 350), Image.LANCZOS)
            except:
                album_cover = Image.new("RGBA", (350, 350), color=(30, 30, 30))

            card_width, card_height = 1012, 512
            background = Image.new("RGBA", (card_width, card_height), (0, 0, 0, 255))

            mask = Image.new("L", (350, 350), 0)
            draw_mask = ImageDraw.Draw(mask)
            draw_mask.rounded_rectangle((0, 0, 350, 350), radius=30, fill=255)
            album_cover.putalpha(mask)

            padding_left = 50
            padding_top = (card_height - 350) // 2
            background.paste(album_cover, (padding_left, padding_top), album_cover)

            font_scale = 1.0
            base_title_size = 34
            base_artist_size = 28

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(70))

            try:
                font_family = self.get_setting("font_family", 0)
                font_paths = {
                    0: "/system/fonts/NotoSansCJK-Regular.ttc",
                    1: "/system/fonts/ComingSoon.ttf",
                    2: "/system/fonts/MiClockTibetan-Thin.ttf",
                    3: "/system/fonts/NotoNaskhArabic-Bold.ttf",
                    4: "/system/fonts/SourceSansPro-Bold.ttf"
                }
                font_path = font_paths.get(font_family, font_paths[0])
                font_title = ImageFont.truetype(font_path, int(base_title_size * font_scale))
                font_artist = ImageFont.truetype(font_path, int(base_artist_size * font_scale))
            except:
                font_title = font_artist = ImageFont.load_default()

            def limit_text_by_chars(text, max_chars):
                return text[:max_chars].rstrip() + "..." if len(text) > max_chars else text

            draw = ImageDraw.Draw(background)
            text_x = padding_left + 350 + 40
            text_y = padding_top + 50

            draw.text((text_x, text_y), "Listening to", font=font_artist, fill=(220, 220, 220))
            text_y += font_artist.getbbox("Listening to")[3] + 8

            title_display = limit_text_by_chars(title, 24)
            draw.text((text_x, text_y), title_display, font=font_title, fill=(255, 255, 255))
            text_y += font_title.getbbox(title_display)[3] + 6

            artist_display = limit_text_by_chars(artists, 30)
            draw.text((text_x, text_y), f"by {artist_display}", font=font_artist, fill=(200, 200, 200))
            text_y += font_artist.getbbox(f"by {artist_display}")[3] + 12

            album_display = limit_text_by_chars(album, 24)
            draw.text((text_x, text_y), f"• {album_display}", font=font_artist, fill=(170, 170, 170))

            icon_url = self._get_service_icon("Spotify")
            if icon_url:
                try:
                    icon_resp = requests.get(icon_url, stream=True)
                    if icon_resp.status_code == 200:
                        icon = Image.open(BytesIO(icon_resp.content)).convert("RGBA")
                        icon = icon.resize((40, 40), Image.LANCZOS)
                        background.paste(icon, (text_x, text_y + 45), icon)
                except:
                    pass

            background.convert("RGB").save(temp_path)

            footer = self.get_setting("custom_footer_text", "♪ ıllıllı - I'm using nowFy!\n╰ by @exteraDevPlugins")
            show_footer = self.get_setting("show_footer_caption", True)
            show_link = self.get_setting("show_track_link", True)
            use_spotify = self.get_setting("use_spotify_link", False)
            link_text = self.get_setting("spotify_link_text", "").strip()

            if not link_text:
                link_text = "Spotify" if use_spotify else "song.link"
            link = spotify_link if use_spotify else universal_link

            if not show_footer:
                footer = ""

            if show_link:
                caption = f"{footer} [{link_text}]({link})"
            else:
                caption = footer

            caption_md = parse_markdown(caption)
            params.caption = caption_md.text
            params.entities = ArrayList()
            for entity in caption_md.entities:
                params.entities.add(entity.to_tlrpc_object())

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(90))
            photo = send_helper.generatePhotoSizes(temp_path, None)
            params.photo = photo
            params.path = temp_path
            params.message = None
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(100))
            run_on_ui_thread(lambda: send_helper.sendMessage(params))
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.dismiss())

        except Exception as e:
            if progress_dialog:
                try: progress_dialog.dismiss()
                except Exception: pass
            self._send_markdown_msg(params, f"❌ Error: {str(e)}")

    def _get_current_track_miniplayer(self, account, params, progress_dialog=None):
        try:
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(10))

            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info(self.tr("error_auth"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except Exception: pass
                return

            resp = requests.get(
                "https://api.spotify.com/v1/me/player/currently-playing",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if resp.status_code == 204 or not resp.content:
                BulletinHelper.show_info(self.tr("no_track_playing"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except Exception: pass
                return

            data = resp.json()
            track = data["item"]

            def limit_text_by_chars(text, max_chars):
                return text[:max_chars].rstrip() + "..." if len(text) > max_chars else text

            def limit_text_by_words(text, max_words):
                words = text.split()
                return " ".join(words[:max_words]) + "..." if len(words) > max_words else text

            title_raw = track["name"].upper()
            title = limit_text_by_chars(title_raw, 24)
            artists_raw = ", ".join([a["name"] for a in track["artists"]])
            artists = limit_text_by_chars(artists_raw, 30)
            album_raw = track["album"]["name"].upper()
            album = limit_text_by_words(album_raw, 6)

            images = track["album"]["images"]
            if not images:
                raise Exception("No album images found.")
            cover_url = max(images, key=lambda i: i["width"])["url"]
            spotify_link = track["external_urls"]["spotify"]
            universal_link = self.get_universal_song_link(spotify_link) or spotify_link

            card_width, card_height = 573, 573
            scale_raw = self.get_setting("miniplayer_scale", 1.0)
            try:
                scale = float(scale_raw)
            except Exception:
                scale = 0.5

            vertical_offset = 120
            margin = int(40 * scale)
            cover_size = int(220 * scale)
            icon_size = int(55 * scale)

            try:
                bg_resp = requests.get(cover_url, timeout=10)
                bg_img = Image.open(BytesIO(bg_resp.content)).convert("RGBA")
                bg_blur = bg_img.resize((card_width, card_height), Image.LANCZOS).filter(ImageFilter.GaussianBlur(30))
                overlay = Image.new("RGBA", (card_width, card_height), (0, 0, 0, 140))
                background = Image.alpha_composite(bg_blur, overlay)
            except:
                background = Image.new("RGBA", (card_width, card_height), "#202020")

            draw = ImageDraw.Draw(background)

            img_resp = requests.get(cover_url, timeout=10)
            album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
            album_cover = album_cover.resize((cover_size, cover_size), Image.LANCZOS)
            mask = Image.new("L", (cover_size, cover_size), 0)
            ImageDraw.Draw(mask).rounded_rectangle((0, 0, cover_size, cover_size), radius=int(24 * scale), fill=255)
            album_cover.putalpha(mask)
            cover_x = margin
            cover_y = margin + vertical_offset
            background.paste(album_cover, (cover_x, cover_y), album_cover)

            font_family = self.get_setting("font_family", 0)
            font_paths = {
                0: "/system/fonts/NotoSansCJK-Regular.ttc",
                1: "/system/fonts/ComingSoon.ttf",
                2: "/system/fonts/MiClockTibetan-Thin.ttf",
                3: "/system/fonts/NotoNaskhArabic-Bold.ttf",
                4: "/system/fonts/SourceSansPro-Bold.ttf"
            }
            font_path = font_paths.get(font_family, font_paths[0])
            try:
                font_title = ImageFont.truetype(font_path, int(30 * scale))
                font_artist = ImageFont.truetype(font_path, int(24 * scale))
                font_np = ImageFont.truetype(font_path, int(23 * scale))
            except:
                font_title = font_artist = font_np = ImageFont.load_default()

            text_x = cover_x
            text_y = cover_y + cover_size + int(30 * scale)
            draw.text((text_x, text_y), title, font=font_title, fill="white")
            text_y += font_title.getbbox(title)[3] + int(8 * scale)
            draw.text((text_x, text_y), artists, font=font_artist, fill=(240, 240, 240))
            text_y += font_artist.getbbox(artists)[3] + int(8 * scale)
            draw.text((text_x, text_y), "NOW PLAYING", font=font_np, fill=(135, 206, 250))

            icon_url = self._get_service_icon("Spotify")
            if icon_url:
                try:
                    icon_resp = requests.get(icon_url, timeout=5)
                    icon_img = Image.open(BytesIO(icon_resp.content)).convert("RGBA")
                    icon_img = icon_img.resize((icon_size, icon_size), Image.LANCZOS)
                    icon_x = card_width - icon_size - margin
                    icon_y = margin
                    background.paste(icon_img, (icon_x, icon_y), icon_img)
                except: pass

            base_dir = ApplicationLoader.getFilesDirFixed()
            temp_dir = File(base_dir, "nowFy")
            if not temp_dir.exists():
                temp_dir.mkdirs()
            filename = f"{uuid.uuid4()}.png"
            temp_path = File(temp_dir, filename).getAbsolutePath()
            background.convert("RGB").save(temp_path, quality=95, optimize=True)

            footer = self.get_setting("custom_footer_text", "").strip()
            if not footer:
                footer = "♪ ıllıllı - I'm using nowFy!\n╰ by @exteraDevPlugins"

            show_footer = self.get_setting("show_footer_caption", True)
            show_link = self.get_setting("show_track_link", True)
            use_spotify = self.get_setting("use_spotify_link", False)
            link_text = self.get_setting("spotify_link_text", "").strip()

            if not link_text:
                link_text = "Spotify" if use_spotify else "song.link"
            link = spotify_link if use_spotify else universal_link

            if not show_footer:
                footer = ""

            if show_link:
                caption = f"{footer} [{link_text}]({link})"
            else:
                caption = footer

            caption_md = parse_markdown(caption)
            params.caption = caption_md.text
            params.entities = ArrayList()
            for entity in caption_md.entities:
                params.entities.add(entity.to_tlrpc_object())

            send_helper = get_send_messages_helper()
            photo = send_helper.generatePhotoSizes(temp_path, None)
            params.photo = photo
            params.path = temp_path
            params.message = None

            run_on_ui_thread(lambda: send_helper.sendMessage(params))
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.dismiss())

        except Exception as e:
            if progress_dialog:
                try: progress_dialog.dismiss()
                except Exception: pass
            self._send_markdown_msg(params, f"❌ Error: {str(e)}")

    def _get_spotify_profile(self):
        try:
            token = self._get_access_token()
            if not token:
                return None, None

            resp = requests.get(
                "https://api.spotify.com/v1/me",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if resp.status_code == 200:
                data = resp.json()
                username = data.get("display_name", "Spotify User")
                images = data.get("images", [])
                avatar_url = images[0]["url"] if images else None
                return username, avatar_url
        except Exception as e:
            log(f"[SpotifyProfile] Failed to get user profile: {str(e)}")

        return "Spotify User", None

    def _get_current_track_discord(self, account, params, progress_dialog=None):
        def smart_wrap_text(text, font, max_width, max_lines=2):
            words = text.split()
            lines = []
            current_line = ""

            def break_long_word(word):
                parts = []
                part = ""
                for char in word:
                    test = part + char
                    if font.getlength(test) <= max_width:
                        part = test
                    else:
                        if part:
                            parts.append(part)
                        part = char
                if part:
                    parts.append(part)
                return parts

            for word in words:
                test_line = f"{current_line} {word}".strip()
                if font.getlength(test_line) <= max_width:
                    current_line = test_line
                else:
                    if font.getlength(word) > max_width:
                        if current_line:
                            lines.append(current_line)
                        lines.extend(break_long_word(word))
                        current_line = ""
                    else:
                        lines.append(current_line)
                        current_line = word

                if len(lines) >= max_lines:
                    break

            if current_line and len(lines) < max_lines:
                lines.append(current_line)

            if len(lines) > max_lines:
                lines = lines[:max_lines]
                last = lines[-1]
                while font.getlength(last + "...") > max_width and len(last) > 1:
                    last = last[:-1]
                lines[-1] = last + "..."

            return lines

        try:
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(5))

            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info(self.tr("error_auth"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except: pass
                return

            resp = requests.get(
                "https://api.spotify.com/v1/me/player/currently-playing",
                headers={"Authorization": f"Bearer {token}"},
                timeout=10
            )

            if resp.status_code == 204 or not resp.content:
                BulletinHelper.show_info(self.tr("no_track_playing"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except: pass
                return

            data = resp.json()
            track = data.get("item")
            if not track:
                BulletinHelper.show_info(self.tr("no_track_playing"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except: pass
                return

            raw_title = track["name"]
            artists = ", ".join(a["name"] for a in track["artists"])
            album = track["album"]["name"].upper()
            images = track["album"].get("images", [])
            if not images:
                raise Exception("No album images found.")
            cover_url = max(images, key=lambda i: i["width"])["url"]
            spotify_link = track["external_urls"]["spotify"]
            universal_link = self.get_universal_song_link(spotify_link) or spotify_link
            username, avatar_url = self._get_spotify_profile()

            send_helper = get_send_messages_helper()
            base_dir = ApplicationLoader.getFilesDirFixed()
            temp_dir = File(base_dir, "nowFy")
            if not temp_dir.exists():
                temp_dir.mkdirs()
            temp_path = File(temp_dir, f"{uuid.uuid4()}.png").getAbsolutePath()

            card_width, card_height = 1080, 546
            padding, radius = 40, 40

            scale_raw = self.get_setting("scale_settings", "")
            try:
                scale = json.loads(scale_raw) if scale_raw else {}
            except Exception:
                scale = {}

            background = Image.new("RGBA", (card_width, card_height), "#1c1c20")
            shape = Image.new("RGBA", (card_width - 2*padding, card_height - 2*padding), (0,0,0,0))
            ImageDraw.Draw(shape).rounded_rectangle((0,0)+shape.size, radius=radius, fill="#212129")
            background.paste(shape, (padding, padding), shape)

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(15))

            album_size = card_height - 2*padding - 60
            try:
                album_resp = requests.get(cover_url, timeout=10)
                album_resp.raise_for_status()
                album_cover = Image.open(BytesIO(album_resp.content)).convert("RGBA")
            except:
                album_cover = Image.new("RGBA", (album_size, album_size), (30,30,30,255))

            album_cover = album_cover.resize((album_size, album_size), Image.LANCZOS)
            mask = Image.new("L", (album_size, album_size), 0)
            ImageDraw.Draw(mask).rounded_rectangle((0,0)+album_cover.size, radius=35, fill=255)
            album_cover.putalpha(mask)
            album_x = padding + 30
            album_y = padding + (card_height - 2*padding - album_size) // 2
            background.paste(album_cover, (album_x, album_y), album_cover)

            avatar_size = scale.get("avatar_size", 75)
            try:
                avatar_resp = requests.get(avatar_url, timeout=10)
                avatar_resp.raise_for_status()
                avatar = Image.open(BytesIO(avatar_resp.content)).convert("RGBA")
            except:
                avatar = Image.new("RGBA", (avatar_size, avatar_size), (70,200,120,255))

            avatar = avatar.resize((avatar_size, avatar_size), Image.LANCZOS)
            mask_avatar = Image.new("L", (avatar_size, avatar_size), 0)
            ImageDraw.Draw(mask_avatar).ellipse((0,0,avatar_size,avatar_size), fill=255)
            avatar.putalpha(mask_avatar)
            group_x = scale.get("group_x", album_x + album_size + 50) - 20
            group_y = scale.get("group_y", album_y + 10)

            offset_avatar = (0, 0)
            offset_user = (0, avatar_size + 15)

            max_text_width = card_width - group_x - 40

            font_family = self.get_setting("font_family", 0)
            font_paths = {
                0: "/system/fonts/NotoSansCJK-Regular.ttc",
                1: "/system/fonts/ComingSoon.ttf",
                2: "/system/fonts/MiClockTibetan-Thin.ttf",
                3: "/system/fonts/NotoNaskhArabic-Bold.ttf",
                4: "/system/fonts/SourceSansPro-Bold.ttf"
            }
            font_path = font_paths.get(font_family, font_paths[4])

            try:
                font_title = ImageFont.truetype(font_path, scale.get("title_size", 36))
                font_artist = ImageFont.truetype(font_path, scale.get("artist_size", 24))
                font_user = ImageFont.truetype(font_path, scale.get("user_size", 40))
            except Exception:
                font_title = ImageFont.load_default()
                font_artist = ImageFont.load_default()
                font_user = ImageFont.load_default()

            title_lines = smart_wrap_text(raw_title, font_title, max_text_width, max_lines=2)
            title_height = len(title_lines) * (font_title.size + 4)
            offset_title = (0, avatar_size + 15 + font_user.getbbox(username)[3] + 8 + 100)
            offset_artist = (0, offset_title[1] + title_height + 6)
            background.paste(avatar, (group_x + offset_avatar[0], group_y + offset_avatar[1]), avatar)
            draw = ImageDraw.Draw(background)
            draw.text((group_x + offset_user[0], group_y + offset_user[1]), username, font=font_user, fill="white")

            for i, line in enumerate(title_lines):
                draw.text((group_x + offset_title[0], group_y + offset_title[1] + i * (font_title.size + 4)), line, font=font_title, fill="white")

            artist_lines = smart_wrap_text(artists, font_artist, max_text_width, max_lines=2)
            for i, line in enumerate(artist_lines):
                draw.text((group_x + offset_artist[0], group_y + offset_artist[1] + i * (font_artist.size + 4)), line, font=font_artist, fill="#bbbbbb")

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(30))

            try:
                overlay_url = "https://i.postimg.cc/sXYDMX99/Spotify-Icon.png"
                overlay_resp = requests.get(overlay_url, timeout=10)
                overlay_resp.raise_for_status()
                overlay_img = Image.open(BytesIO(overlay_resp.content)).convert("RGBA")
                overlay_img = overlay_img.resize((card_width, card_height), Image.LANCZOS)
                background = Image.alpha_composite(background.convert("RGBA"), overlay_img)
            except Exception as e:
                print(f"[nowFy] Error loading overlay image: {e}")

            background.convert("RGB").save(temp_path, format="PNG")

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(60))

            show_link = self.get_setting("show_track_link", True)
            use_spotify = self.get_setting("use_spotify_link", False)
            link_text = self.get_setting("spotify_link_text", "")
            if not link_text.strip():
                link_text = self.tr("spotify_link_text") 

            link = spotify_link if use_spotify else universal_link
            footer = self.get_setting("custom_footer_text", "♪ ıllıllı - I'm using nowFy!\n╰ by @exteraDevPlugins")
            show_footer = self.get_setting("show_footer_caption", True)
            if not show_footer:
                footer = ""

            if show_link:
                caption = f"{footer} [{link_text}]({link})"
                caption_md = parse_markdown(caption)
                params.caption = caption_md.text
                params.entities = ArrayList()
                for entity in caption_md.entities:
                    params.entities.add(entity.to_tlrpc_object())
            else:
                params.caption = footer
                params.entities = ArrayList()

            params.photo = send_helper.generatePhotoSizes(temp_path, None)
            params.path = temp_path
            params.message = None

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(90))
            run_on_ui_thread(lambda: send_helper.sendMessage(params))
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.dismiss())

        except Exception as e:
            if progress_dialog:
                try: progress_dialog.dismiss()
                except: pass
            self._send_markdown_msg(params, f"Error (discord theme): {e}")

    def _get_current_track_discord_light(self, account, params, progress_dialog=None):
        try:
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(5))

            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info(self.tr("error_auth"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except: pass
                return

            resp = requests.get(
                "https://api.spotify.com/v1/me/player/currently-playing",
                headers={"Authorization": f"Bearer {token}"},
                timeout=10
            )

            if resp.status_code == 204 or not resp.content:
                BulletinHelper.show_info(self.tr("no_track_playing"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except: pass
                return

            data = resp.json()
            track = data.get("item")
            if not track:
                BulletinHelper.show_info(self.tr("no_track_playing"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except: pass
                return

            def smart_wrap_text(text, font, max_width, max_lines=2):
                words = text.split()
                lines = []
                current_line = ""

                def break_long_word(word):
                    parts = []
                    part = ""
                    for char in word:
                        test = part + char
                        if font.getlength(test) <= max_width:
                            part = test
                        else:
                            if part:
                                parts.append(part)
                            part = char
                    if part:
                        parts.append(part)
                    return parts

                for word in words:
                    test_line = f"{current_line} {word}".strip()
                    if font.getlength(test_line) <= max_width:
                        current_line = test_line
                    else:
                        if font.getlength(word) > max_width:
                            if current_line:
                                lines.append(current_line)
                            lines.extend(break_long_word(word))
                            current_line = ""
                        else:
                            lines.append(current_line)
                            current_line = word

                    if len(lines) >= max_lines:
                        break

                if current_line and len(lines) < max_lines:
                    lines.append(current_line)

                if len(lines) > max_lines:
                    lines = lines[:max_lines]
                    last = lines[-1]
                    while font.getlength(last + "...") > max_width and len(last) > 1:
                        last = last[:-1]
                    lines[-1] = last + "..."

                return lines

            raw_title = track["name"]
            artists = ", ".join(a["name"] for a in track["artists"])
            album = track["album"]["name"].upper()
            images = track["album"].get("images", [])
            if not images:
                raise Exception("No album images found.")
            cover_url = max(images, key=lambda i: i["width"])["url"]
            spotify_link = track["external_urls"]["spotify"]
            universal_link = self.get_universal_song_link(spotify_link) or spotify_link
            username, avatar_url = self._get_spotify_profile()

            send_helper = get_send_messages_helper()
            base_dir = ApplicationLoader.getFilesDirFixed()
            temp_dir = File(base_dir, "nowFy")
            if not temp_dir.exists():
                temp_dir.mkdirs()
            temp_path = File(temp_dir, f"{uuid.uuid4()}.png").getAbsolutePath()

            card_width, card_height = 1080, 546
            padding, radius = 40, 40

            try:
                scale_raw = self.get_setting("scale_settings", "")
                scale = json.loads(scale_raw) if scale_raw else {}
            except Exception:
                scale = {}

            font_family = self.get_setting("font_family", 0) 
            font_paths = {
                0: "/system/fonts/NotoSansCJK-Regular.ttc",
                1: "/system/fonts/ComingSoon.ttf",
                2: "/system/fonts/MiClockTibetan-Thin.ttf",
                3: "/system/fonts/NotoNaskhArabic-Bold.ttf",
                4: "/system/fonts/SourceSansPro-Bold.ttf"
            }
            font_path = font_paths.get(font_family, font_paths[4])

            try:
                font_title = ImageFont.truetype(font_path, scale.get("title_size", 36))
                font_artist = ImageFont.truetype(font_path, scale.get("artist_size", 24))
                font_user = ImageFont.truetype(font_path, scale.get("user_size", 40))
            except:
                font_title = ImageFont.load_default()
                font_artist = ImageFont.load_default()
                font_user = ImageFont.load_default()

            background = Image.new("RGBA", (card_width, card_height), "#dbd6d6")
            shape = Image.new("RGBA", (card_width - 2 * padding, card_height - 2 * padding), (0, 0, 0, 0))
            ImageDraw.Draw(shape).rounded_rectangle((0, 0) + shape.size, radius=radius, fill="#f0f0f0")
            background.paste(shape, (padding, padding), shape)

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(15))

            album_size = card_height - 2 * padding - 60
            try:
                album_resp = requests.get(cover_url, timeout=10)
                album_resp.raise_for_status()
                album_cover = Image.open(BytesIO(album_resp.content)).convert("RGBA")
            except:
                album_cover = Image.new("RGBA", (album_size, album_size), (200, 200, 200, 255))

            album_cover = album_cover.resize((album_size, album_size), Image.LANCZOS)
            mask = Image.new("L", (album_size, album_size), 0)
            ImageDraw.Draw(mask).rounded_rectangle((0, 0) + album_cover.size, radius=35, fill=255)
            album_cover.putalpha(mask)
            album_x = padding + 30
            album_y = padding + (card_height - 2 * padding - album_size) // 2
            background.paste(album_cover, (album_x, album_y), album_cover)

            avatar_size = scale.get("avatar_size", 75)
            try:
                avatar_resp = requests.get(avatar_url, timeout=10)
                avatar_resp.raise_for_status()
                avatar = Image.open(BytesIO(avatar_resp.content)).convert("RGBA")
            except:
                avatar = Image.new("RGBA", (avatar_size, avatar_size), (100, 100, 100, 255))

            avatar = avatar.resize((avatar_size, avatar_size), Image.LANCZOS)
            mask_avatar = Image.new("L", (avatar_size, avatar_size), 0)
            ImageDraw.Draw(mask_avatar).ellipse((0, 0, avatar_size, avatar_size), fill=255)
            avatar.putalpha(mask_avatar)
            group_x = scale.get("group_x", album_x + album_size + 50) - 20
            group_y = scale.get("group_y", album_y + 10)

            offset_avatar = (0, 0)
            offset_user = (0, avatar_size + 15)

            draw = ImageDraw.Draw(background)
            draw.text((group_x + offset_user[0], group_y + offset_user[1]), username, font=font_user, fill="#222222")

            max_text_width = card_width - group_x - 40
            wrapped_title = smart_wrap_text(raw_title, font_title, max_text_width)
            title_height = len(wrapped_title) * (font_title.size + 6)
            offset_title = (0, offset_user[1] + font_user.getbbox(username)[3] + 8 + 100)

            for i, line in enumerate(wrapped_title):
                draw.text((group_x + offset_title[0], group_y + offset_title[1] + i * (font_title.size + 6)), line, font=font_title, fill="#000000")

            offset_artist = (0, offset_title[1] + title_height + 6)
            wrapped_artist = smart_wrap_text(artists, font_artist, max_text_width)
            for i, line in enumerate(wrapped_artist):
                draw.text((group_x + offset_artist[0], group_y + offset_artist[1] + i * (font_artist.size + 6)), line, font=font_artist, fill="#555555")

            background.paste(avatar, (group_x + offset_avatar[0], group_y + offset_avatar[1]), avatar)

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(70))

            try:
                overlay_url = "https://i.postimg.cc/TP7jsDdk/Spotify-Icon-Light.png"
                overlay_resp = requests.get(overlay_url, timeout=10)
                overlay_resp.raise_for_status()
                overlay_img = Image.open(BytesIO(overlay_resp.content)).convert("RGBA")
                overlay_img = overlay_img.resize((card_width, card_height), Image.LANCZOS)
                background = Image.alpha_composite(background.convert("RGBA"), overlay_img)
            except Exception as e:
                print(f"[nowFy] Error loading overlay image: {e}")

            background.convert("RGB").save(temp_path, format="PNG")

            show_link = self.get_setting("show_track_link", True)
            use_spotify = self.get_setting("use_spotify_link", False)
            link_text = self.get_setting("spotify_link_text", "")
            if not link_text.strip():
                link_text = self.tr("spotify_link_text")

            link = spotify_link if use_spotify else universal_link
            footer = self.get_setting("custom_footer_text", "♪ ıllıllı - I'm using nowFy!\n╰ by @exteraDevPlugins")
            show_footer = self.get_setting("show_footer_caption", True)
            if not show_footer:
                footer = ""

            if show_link:
                caption = f"{footer} [{link_text}]({link})"
                caption_md = parse_markdown(caption)
                params.caption = caption_md.text
                params.entities = ArrayList()
                for entity in caption_md.entities:
                    params.entities.add(entity.to_tlrpc_object())
            else:
                params.caption = footer
                params.entities = ArrayList()

            params.photo = send_helper.generatePhotoSizes(temp_path, None)
            params.path = temp_path
            params.message = None

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(90))
            run_on_ui_thread(lambda: send_helper.sendMessage(params))
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.dismiss())

        except Exception as e:
            if progress_dialog:
                try: progress_dialog.dismiss()
                except: pass
            self._send_markdown_msg(params, f"Error (discord_light theme): {e}")

    def _get_current_track_apple(self, account, params, progress_dialog=None):
        try:
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(10))

            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info(self.tr("error_auth"))
                return

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(20))

            resp = requests.get("https://api.spotify.com/v1/me/player/currently-playing",
                                headers={"Authorization": f"Bearer {token}"}, timeout=10)
            if resp.status_code == 204 or not resp.content:
                BulletinHelper.show_info(self.tr("no_track_playing"))
                return

            data = resp.json()
            track = data.get("item")
            if not track:
                BulletinHelper.show_info(self.tr("no_track_playing"))
                return

            raw_title = track["name"]
            artists = ", ".join(a["name"] for a in track["artists"])
            images = track["album"].get("images", [])
            if not images:
                raise Exception("No album images found.")
            cover_url = max(images, key=lambda i: i["width"])["url"]
            spotify_link = track["external_urls"]["spotify"]
            universal_link = self.get_universal_song_link(spotify_link) or spotify_link

            genres = []
            try:
                first_artist_id = track["artists"][0]["id"]
                artist_resp = requests.get(
                    f"https://api.spotify.com/v1/artists/{first_artist_id}",
                    headers={"Authorization": f"Bearer {token}"},
                    timeout=10,
                )
                artist_resp.raise_for_status()
                artist_data = artist_resp.json()
                genres = artist_data.get("genres", [])[:2]
            except:
                genres = []

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(30))

            playing_text = self.tr("playing")

            send_helper = get_send_messages_helper()
            base_dir = ApplicationLoader.getFilesDirFixed()
            temp_dir = File(base_dir, "nowFy")
            if not temp_dir.exists():
                temp_dir.mkdirs()
            temp_path = File(temp_dir, f"{uuid.uuid4()}.png").getAbsolutePath()

            card_width, card_height = 1080, 604
            padding = 40

            group_offset = (
                self.get_setting("group_offset_x", 0),
                self.get_setting("group_offset_y", -60),
            )

            group = {
                "album": {
                    "size": 298,
                    "radius": 30,
                    "position": (padding + 30, (card_height - 298) // 2),
                },
                "footer": {
                    "size": (500, 100),
                    "radius": 60,
                    "position": (padding + 30, card_height - 130),
                    "fill": "#121d23",
                    "text": self.get_setting("apple_theme_text", "            Powered by nowFy! =D")[:32],
                    "font_size": 28
                },
                "text": {
                    "genre": {"font_size": 26, "fill": "#bbbbbb", "offset_y": 0},
                    "title": {"font_size": 42, "fill": "white", "offset_y": 50},
                    "artist": {"font_size": 25, "fill": "#bbbbbb", "offset_y": 110},
                },
            }

            text_base_x = group["album"]["position"][0] + group["album"]["size"] + 60
            text_base_y = group["album"]["position"][1]

            background = Image.new("RGBA", (card_width, card_height), "#121212")
            shape = Image.new(
                "RGBA", (card_width - 2 * padding, card_height - 2 * padding), (0, 0, 0, 0)
            )
            ImageDraw.Draw(shape).rounded_rectangle(
                (0, 0) + shape.size, radius=40, fill="#142129"
            )
            background.paste(shape, (padding, padding), shape)

            try:
                album_resp = requests.get(cover_url, timeout=10)
                album_resp.raise_for_status()
                album_cover = Image.open(BytesIO(album_resp.content)).convert("RGBA")
            except:
                album_cover = Image.new(
                    "RGBA", (group["album"]["size"], group["album"]["size"]), (30, 30, 30, 255)
                )

            album_cover = album_cover.resize((group["album"]["size"], group["album"]["size"]))
            mask = Image.new("L", (group["album"]["size"], group["album"]["size"]), 0)
            ImageDraw.Draw(mask).rounded_rectangle(
                (0, 0, group["album"]["size"], group["album"]["size"]),
                radius=group["album"]["radius"],
                fill=255,
            )
            album_cover.putalpha(mask)

            album_pos = (
                group["album"]["position"][0] + group_offset[0],
                group["album"]["position"][1] + group_offset[1],
            )
            background.paste(album_cover, album_pos, album_cover)

            font_paths = {
                0: "/system/fonts/NotoSansCJK-Regular.ttc",
                1: "/system/fonts/ComingSoon.ttf",
                2: "/system/fonts/MiClockTibetan-Thin.ttf",
                3: "/system/fonts/NotoNaskhArabic-Bold.ttf",
                4: "/system/fonts/SourceSansPro-Bold.ttf",
            }
            font_path = font_paths.get(self.get_setting("font_family", 0), font_paths[4])

            font_title = ImageFont.truetype(font_path, group["text"]["title"]["font_size"])
            font_artist = ImageFont.truetype("/system/fonts/NotoSansCJK-Regular.ttc", group["text"]["artist"]["font_size"])
            font_genre = ImageFont.truetype("/system/fonts/NotoSansCJK-Regular.ttc", group["text"]["genre"]["font_size"])
            font_footer = ImageFont.truetype("/system/fonts/NotoSansCJK-Regular.ttc", group["footer"]["font_size"])

            draw = ImageDraw.Draw(background)

            def split_text(text):
                if " " in text:
                    return text.split()
                elif len(text) <= 5:
                    return [text]  
                else:
                    return list(text) 

            def draw_multiline_text(draw, text, position, font, fill, max_width, line_spacing=4):

                if " " in text:
                    words = text.split()
                elif len(text) < 5:
                    words = [text]
                else:
                    words = list(text)

                lines = []
                current_line = ""

                for word in words:
                    test_line = current_line + " " + word if current_line else word
                    w, h = draw.textsize(test_line, font=font)
                    if w <= max_width:
                        current_line = test_line
                    else:
                        if current_line:
                            lines.append(current_line)
                        current_line = word
                if current_line:
                    lines.append(current_line)

                x, y = position
                line_height = font.getsize("Ay")[1] + line_spacing
                for line in lines:
                    draw.text((x, y), line, font=font, fill=fill)
                    y += line_height
                return y

            def get_multiline_height(draw, text, font, max_width, line_spacing=4):
                words = split_text(text)
                lines = []
                current_line = ""

                for word in words:
                    test_line = current_line + word if current_line else word
                    w, h = draw.textsize(test_line, font=font)
                    if w <= max_width:
                        current_line = test_line
                    else:
                        if current_line:
                            lines.append(current_line)
                        current_line = word
                if current_line:
                    lines.append(current_line)

                line_height = font.getsize("Ay")[1] + line_spacing
                return line_height * len(lines)

            max_text_width = (card_width - 2 * padding) - (text_base_x + group_offset[0]) - 30

            draw_multiline_text(
                draw,
                playing_text,
                (text_base_x + group_offset[0], text_base_y + group_offset[1] + group["text"]["genre"]["offset_y"]),
                font_genre,
                group["text"]["genre"]["fill"],
                max_text_width,
            )

            draw_multiline_text(
                draw,
                raw_title,
                (text_base_x + group_offset[0], text_base_y + group_offset[1] + group["text"]["title"]["offset_y"]),
                font_title,
                group["text"]["title"]["fill"],
                max_text_width,
            )

            title_height = get_multiline_height(draw, raw_title, font_title, max_text_width)

            draw_multiline_text(
                draw,
                artists,
                (text_base_x + group_offset[0], text_base_y + group_offset[1] + group["text"]["title"]["offset_y"] + title_height + 10),
                font_artist,
                group["text"]["artist"]["fill"],
                max_text_width,
            )

            footer_shape = Image.new("RGBA", group["footer"]["size"], (0, 0, 0, 0))
            draw_footer = ImageDraw.Draw(footer_shape)
            draw_footer.rounded_rectangle(
                (0, 0) + group["footer"]["size"],
                radius=group["footer"]["radius"],
                fill=group["footer"]["fill"],
            )
            draw_footer.text((30, 30), group["footer"]["text"], font=font_footer, fill="#cccccc")

            footer_pos = (
                group["footer"]["position"][0] + group_offset[0],
                group["footer"]["position"][1] + group_offset[1],
            )
            background.paste(footer_shape, footer_pos, footer_shape)

            try:
                overlay_url = "https://i.postimg.cc/1zygr1hw/appleicon.png"
                overlay_resp = requests.get(overlay_url, timeout=10)
                overlay_resp.raise_for_status()
                overlay_img = Image.open(BytesIO(overlay_resp.content)).convert("RGBA")
                overlay_img = overlay_img.resize((card_width, card_height), Image.LANCZOS)
                background = Image.alpha_composite(background.convert("RGBA"), overlay_img)
            except Exception as e:
                print(f"[nowFy] Error loading overlay image: {e}")

            background.convert("RGB").save(temp_path, format="PNG")

            show_link = self.get_setting("show_track_link", True)
            use_spotify = self.get_setting("use_spotify_link", False)
            link_text = self.get_setting("spotify_link_text", self.tr("spotify_link_text"))
            footer = self.get_setting("custom_footer_text", "♪ ıllıllı - I'm using nowFy!\n╰ by @exteraDevPlugins")
            show_footer = self.get_setting("show_footer_caption", True)
            if not show_footer:
                footer = ""
            link = spotify_link if use_spotify else universal_link

            if show_link:
                caption = f"{footer} [{link_text}]({link})"
                caption_md = parse_markdown(caption)
                params.caption = caption_md.text
                params.entities = ArrayList()
                for entity in caption_md.entities:
                    params.entities.add(entity.to_tlrpc_object())
            else:
                params.caption = footer
                params.entities = ArrayList()

            params.photo = send_helper.generatePhotoSizes(temp_path, None)
            params.path = temp_path
            params.message = None

            run_on_ui_thread(lambda: send_helper.sendMessage(params))
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.dismiss())

        except Exception as e:
            if progress_dialog:
                try:
                    progress_dialog.dismiss()
                except:
                    pass
            self._send_markdown_msg(params, f"AppleWidget theme error:\n`{e}`")

    def _get_current_track_legacy(self, account, params, progress_dialog=None):
        try:
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(10))

            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info(self.tr("error_auth"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except Exception: pass
                return

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(20))

            resp = requests.get(
                "https://api.spotify.com/v1/me/player/currently-playing",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(30))

            if resp.status_code == 204 or not resp.content:
                BulletinHelper.show_info(self.tr("no_track_playing"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except Exception: pass
                return

            if resp.status_code != 200:
                BulletinHelper.show_info(self.tr("error_code").format(resp.status_code))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except Exception: pass
                return

            data = resp.json()
            track = data["item"]
            title = self._truncate(track["name"]).upper()
            artists = ", ".join([a["name"] for a in track["artists"]])
            album = self._limit_words(track["album"]["name"]).upper()
            images = track["album"].get("images", [])
            if not images:
                raise Exception("No album images found.")
            cover_url = max(images, key=lambda i: i["width"])["url"]
            spotify_link = track["external_urls"]["spotify"]
            universal_link = self.get_universal_song_link(spotify_link) or spotify_link
            progress_ms = data.get("progress_ms", 0)
            duration_ms = track["duration_ms"]

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(40))

            img_resp = requests.get(cover_url, stream=True, timeout=5)
            img_bytes = BytesIO()
            for chunk in img_resp.iter_content(8192):
                img_bytes.write(chunk)
            img_bytes.seek(0)
            img = Image.open(img_bytes).convert("RGBA")
            img = img.resize((640, 640), Image.LANCZOS)

            bar_margin = int(img.width * 0.08)
            bar_width = img.width - 2 * bar_margin
            bar_height = int(img.height * 0.007)
            progress_width = int((progress_ms / duration_ms) * bar_width)
            footer_y = img.height - int(img.height * 0.35)
            bar_y = footer_y - 20

            if self.get_setting("show_music_bar", True):
                style = self.get_setting("minute_bar_style", 0)
                bar_fill_color = (100, 100, 100, 100)
                bar_y_center = bar_y + bar_height // 2

                style_map = {
                    0: self._draw_progress_bar_style_0,
                    1: self._draw_progress_bar_style_1,
                    2: self._draw_progress_bar_style_2,
                    3: self._draw_progress_bar_style_3,
                    4: self._draw_progress_bar_style_4,
                    5: self._draw_progress_bar_style_5,
                    6: self._draw_progress_bar_style_6
                }

                if style == 7:
                    img = self.generate_premium_one_card(cover_url, title, artists, album, progress_ms/1000, duration_ms/1000)
                else:
                    draw = ImageDraw.Draw(img)
                    draw_fn = style_map.get(style)
                    if draw_fn:
                        draw_fn(draw, bar_margin, bar_y, bar_width, bar_height, progress_width)

            blur_box = img.crop((0, footer_y, img.width, img.height)).filter(ImageFilter.GaussianBlur(19))
            overlay = Image.new("RGBA", blur_box.size, (0, 0, 0, 75))
            blur_box = Image.alpha_composite(blur_box.convert("RGBA"), overlay)
            img.paste(blur_box.convert("RGB"), (0, footer_y))

            fonts = self._get_font_set()
            margin = 50
            current_y = footer_y + 25
            draw = ImageDraw.Draw(img)
            draw.text((margin, current_y), title, font=fonts["title"], fill="white")
            current_y += fonts["title"].getsize(title)[1] + 2
            draw.text((margin, current_y), artists, font=fonts["now"], fill="white")
            current_y += fonts["now"].getsize(artists)[1] + 1
            draw.text((margin, current_y), album, font=fonts["small"], fill="white")

            overlay_img = self._get_overlay_cached()
            if overlay_img:
                img = Image.alpha_composite(img, overlay_img)

            img = img.convert("RGB")
            base_dir = ApplicationLoader.getFilesDirFixed()
            temp_dir = File(base_dir, "nowFy")
            if not temp_dir.exists():
                temp_dir.mkdirs()
            filename = f"{uuid.uuid4()}.png"
            temp_path = File(temp_dir, filename).getAbsolutePath()
            img.save(temp_path)

            send_helper = get_send_messages_helper()
            footer = self.get_setting("custom_footer_text")
            show_footer = self.get_setting("show_footer_caption", True)
            if not show_footer:
                footer = ""
            if not footer or str(footer).lower() == "none":
                footer = ""
            show_link = self.get_setting("show_track_link", True)
            if show_link:
                spotify_link_text = self.get_setting("spotify_link_text", "")
                if not spotify_link_text.strip():
                    spotify_link_text = self.tr("spotify_link_text")
                use_spotify = self.get_setting("use_spotify_link", False)
                link = spotify_link if use_spotify else universal_link
                link_text = spotify_link_text if use_spotify else "song.link"
                caption = f"{footer}   [{link_text}]({link})"
                caption_md = parse_markdown(caption)
                params.caption = caption_md.text
                params.entities = ArrayList()
                for entity in caption_md.entities:
                    params.entities.add(entity.to_tlrpc_object())
            else:
                params.caption = footer
                params.entities = ArrayList()

            photo = send_helper.generatePhotoSizes(temp_path, None)
            params.photo = photo
            params.path = temp_path
            params.message = None

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(100))
            run_on_ui_thread(lambda: send_helper.sendMessage(params))
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.dismiss())

        except Exception as e:
            self._send_markdown_msg(params, f"""𝗢𝗼𝗽𝘀...

    {str(e)}""")
            if progress_dialog:
                try: progress_dialog.dismiss()
                except Exception: pass

    def _get_current_track_lastfm(self, account, params, progress_dialog=None):
        try:
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(10))

            user = self.get_setting("lastfm_user", "")
            api_key = self.get_setting("lastfm_api_key", "")
            
            resp = requests.get(
                "https://ws.audioscrobbler.com/2.0/",
                params={
                    "method": "user.getrecenttracks",
                    "user": user,
                    "api_key": api_key,
                    "format": "json",
                    "limit": 1
                },
                timeout=10
            )
            
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(30))

            data = resp.json()
            tracks = data.get("recenttracks", {}).get("track", [])
            if not tracks:
                BulletinHelper.show_info(self.tr("no_track_playing"))
                if progress_dialog:
                    try: progress_dialog.dismiss()
                    except Exception: pass
                return

            track = tracks[0]
            title = self._truncate(track["name"]).upper()
            artist = track["artist"]["#text"]
            album = self._limit_words(track["album"]["#text"]).upper()
            image_url = track["image"][-1]["#text"]
            
            theme_style = self.get_setting("lastfm_theme_style", 0)
            if theme_style == 0:
                return self._get_current_track_lastfm_default(title, artist, album, image_url, params, progress_dialog)
            elif theme_style == 1:
                return self._get_current_track_lastfm_dark(title, artist, album, image_url, params, progress_dialog)
            elif theme_style == 2:
                return self._get_current_track_lastfm_customfm(title, artist, album, image_url, params, progress_dialog)
         
        except Exception as e:
            if progress_dialog:
                try: progress_dialog.dismiss()
                except Exception: pass
            self._send_markdown_msg(params, f"Error: {str(e)}")

    def _get_youtube_cover(self, song_title, artists=''):
        try:
            youtube_api_key = self.get_setting('youtube_api_key', '')
            if not youtube_api_key:
                raise Exception(self.tr('error_youtube_api_key'))

            search_query = f"{song_title} {artists} official music video"
            search_url = f"https://www.googleapis.com/youtube/v3/search?part=snippet&q={requests.utils.quote(search_query)}&type=video&maxResults=1&key={youtube_api_key}"

            response = requests.get(search_url, timeout=10)
            data = response.json()

            if 'error' in data:
                raise Exception(self.tr('error_youtube_api').format(data['error'].get('message', self.tr('error_unknown'))))

            if 'items' in data and data['items']:
                video_id = data['items'][0]['id']['videoId']
                return f"https://img.youtube.com/vi/{video_id}/maxresdefault.jpg"

            return "https://i.postimg.cc/K8QK8bjS/icondefault.png"

        except Exception as e:
            print(self.tr('error_unknown').format(str(e)))
            if 'quota' in str(e).lower():
                print(self.tr('error_youtube_quota'))
            return "https://i.postimg.cc/K8QK8bjS/icondefault.png"

    def _get_current_track_lastfm_default(self, title, artist, album, image_url, params, progress_dialog=None):
        try:
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(50))

            player_name = self._detect_current_player()

            send_helper = get_send_messages_helper()
            base_dir = ApplicationLoader.getFilesDirFixed()
            temp_dir = File(base_dir, "nowFy")
            if not temp_dir.exists():
                temp_dir.mkdirs()
            filename = f"{uuid.uuid4()}.png"
            temp_path = File(temp_dir, filename).getAbsolutePath()
            card_width, card_height = 1012, 512
            padding_left = 50
            padding_top = (card_height - 350) // 2


            if player_name in ["YouTube", "YouTube Music"]:
                youtube_cover = self._get_youtube_cover(title, artist)
                if youtube_cover:
                    try:
                        img_resp = requests.get(youtube_cover, stream=True, timeout=10)
                        img_resp.raise_for_status()
                        album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                        album_cover = ImageOps.fit(album_cover, (350, 350), method=Image.LANCZOS, centering=(0.5, 0.5))
                    except Exception as e:
                        print(f"Erro ao carregar capa do YouTube: {e}")
                        try:
                            img_resp = requests.get("https://i.postimg.cc/K8QK8bjS/icondefault.png", stream=True, timeout=10)
                            img_resp.raise_for_status()
                            album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                            album_cover = album_cover.resize((350, 350), Image.LANCZOS)
                        except Exception as e:
                            print(f"Falha ao obter capa fallback do YouTube: {e}")
                            album_cover = Image.new("RGBA", (350, 350), color=(30, 30, 30))
                else:
                    try:
                        img_resp = requests.get("https://i.postimg.cc/K8QK8bjS/icondefault.png", stream=True, timeout=10)
                        img_resp.raise_for_status()
                        album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                        album_cover = album_cover.resize((350, 350), Image.LANCZOS)
                    except Exception as e:
                        print(f"Falha ao obter capa fallback do YouTube: {e}")
                        album_cover = Image.new("RGBA", (350, 350), color=(30, 30, 30))

            elif image_url:
                try:
                    img_resp = requests.get(image_url, stream=True, timeout=10)
                    img_resp.raise_for_status()
                    album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                    album_cover = album_cover.resize((350, 350), Image.LANCZOS)
                except Exception as e:
                    print(f"Erro ao carregar cover_url: {e}")
                    try:
                        img_resp = requests.get("https://i.postimg.cc/K8QK8bjS/icondefault.png", stream=True, timeout=10)
                        img_resp.raise_for_status()
                        album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                        album_cover = album_cover.resize((350, 350), Image.LANCZOS)
                    except Exception as e:
                        print(f"Falha ao obter capa fallback cover_url: {e}")
                        album_cover = Image.new("RGBA", (350, 350), color=(30, 30, 30))

            else:
                try:
                    img_resp = requests.get("https://i.postimg.cc/K8QK8bjS/icondefault.png", stream=True, timeout=10)
                    img_resp.raise_for_status()
                    album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                    album_cover = album_cover.resize((350, 350), Image.LANCZOS)
                except Exception as e:
                    print(f"Falha ao obter capa fallback geral: {e}")
                    album_cover = Image.new("RGBA", (350, 350), color=(30, 30, 30))

            bg_cover = album_cover.copy()
            bg_ratio = max(card_width / bg_cover.width, card_height / bg_cover.height)
            new_size = (int(bg_cover.width * bg_ratio), int(bg_cover.height * bg_ratio))
            bg_cover = bg_cover.resize(new_size, Image.LANCZOS)
            background = Image.new("RGBA", (card_width, card_height))
            blur_bg = bg_cover.filter(ImageFilter.GaussianBlur(30))
            background.paste(blur_bg, (0, 0))
            overlay = Image.new("RGBA", (card_width, card_height), (0, 0, 0, 150))
            background = Image.alpha_composite(background.convert("RGBA"), overlay)
            mask = Image.new("L", (350, 350), 0)
            draw_mask = ImageDraw.Draw(mask)
            draw_mask.rounded_rectangle((0, 0, 350, 350), radius=23, fill=255)
            album_cover.putalpha(mask)
            background.paste(album_cover, (padding_left, padding_top), album_cover)
            font_scale = 1.2
            base_title_size = 33
            base_artist_size = 27

            try:
                font_family = self.get_setting("font_family", 0)
                custom_fonts = self._list_custom_fonts()

                if font_family > 4 and custom_fonts:
                    custom_font_index = font_family - 5
                    if custom_font_index < len(custom_fonts):
                        font_path = os.path.join(self._get_custom_fonts_path(), custom_fonts[custom_font_index])
                    else:
                        font_path = "/system/fonts/NotoSansCJK-Regular.ttc"
                else:
                    font_paths = {
                        0: "/system/fonts/NotoSansCJK-Regular.ttc",
                        1: "/system/fonts/ComingSoon.ttf",
                        2: "/system/fonts/MiClockTibetan-Thin.ttf",
                        3: "/system/fonts/NotoNaskhArabic-Bold.ttf",
                        4: "/system/fonts/SourceSansPro-Bold.ttf"
                    }
                    font_path = font_paths.get(font_family, font_paths[0])

                font_title = ImageFont.truetype(font_path, int(base_title_size * font_scale))
                font_artist = ImageFont.truetype(font_path, int(base_artist_size * font_scale))
            except Exception as e:
                print(f"Error loading font: {e}")
                font_title = font_artist = ImageFont.load_default()

            def limit_text_by_chars(text, max_chars):
                if len(text) > max_chars:
                    return text[:max_chars].rstrip() + "..."
                return text

            draw = ImageDraw.Draw(background)
            text_x = padding_left + 350 + 40
            text_y = padding_top + 50

            draw.text((text_x, text_y), "Listening to", font=font_artist, fill=(210, 210, 210))
            text_y += font_artist.getbbox("Listening to")[3] + 8

            title_display = limit_text_by_chars(title, max_chars=21)
            draw.text((text_x, text_y), title_display, font=font_title, fill="white")
            text_y += font_title.getbbox(title_display)[3] + 6

            artist_display = limit_text_by_chars(artist, max_chars=25)
            draw.text((text_x, text_y), f"by {artist_display}", font=font_artist, fill=(180, 180, 180))
            text_y += font_artist.getbbox(f"by {artist_display}")[3] + 12

            if not album.strip():
                album_display = "scrobbling"
            else:
                album_display = limit_text_by_chars(album, max_chars=20)
            draw.text((text_x, text_y), f"• {album_display}", font=font_artist, fill=(200, 200, 200))
            text_y += font_artist.getbbox(f"• {album_display}")[3] + 20

            icon_size = 40
            icon_x = text_x
            icon_y = text_y

            icon_url = self._get_service_icon(player_name)
            if icon_url:
                try:
                    response = requests.get(icon_url, stream=True)
                    if response.status_code == 200:
                        icon_image = Image.open(BytesIO(response.content)).convert('RGBA')
                        icon_image = icon_image.resize((icon_size, icon_size), Image.LANCZOS)
                        background.paste(icon_image, (icon_x, icon_y), icon_image)
                except Exception as e:
                    print(f"Last.fm icon error: {e}")

            corner_radius = 50
            rounded_mask = Image.new("L", (card_width, card_height), 0)
            draw_round = ImageDraw.Draw(rounded_mask)
            draw_round.rounded_rectangle((0, 0, card_width, card_height), corner_radius, fill=255)
            background.putalpha(rounded_mask)

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(90))
            background.convert("RGB").save(temp_path)

            footer = self.get_setting("custom_footer_text", "♪ ıllıllı - I'm using nowFy!\n╰ by @exteraDevPlugins")
            show_profile = self.get_setting("show_profile_link", True)

            caption = footer

            if player_name == "YouTube":
                search_query = f"{artist} {title} official music video"
                song_link = f"https://www.youtube.com/results?search_query={requests.utils.quote(search_query)}"
                caption = f"{footer} [YouTube]({song_link})"
            elif player_name == "YouTube Music":
                search_query = f"{artist} {title}"
                song_link = f"https://music.youtube.com/search?q={requests.utils.quote(search_query)}"
                caption = f"{footer} [YouTube Music]({song_link})"
            elif player_name == "Spotify":
                song_link = f"https://open.spotify.com/search/{artist.replace(' ', '+')}%20{title.replace(' ', '+')}"
                caption = f"{footer} [Spotify]({song_link})"

            if show_profile:
                user = self.get_setting("lastfm_user", "")
                if user:
                    profile_link = f"https://www.last.fm/user/{user}"
                    caption = f"{caption} • [{self.tr('lastfm_profile')}]({profile_link})"

            caption_md = parse_markdown(caption)
            params.caption = caption_md.text
            params.entities = ArrayList()
            for entity in caption_md.entities:
                params.entities.add(entity.to_tlrpc_object())

            photo = send_helper.generatePhotoSizes(temp_path, None)
            params.photo = photo
            params.path = temp_path
            params.message = None

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(100))
            run_on_ui_thread(lambda: send_helper.sendMessage(params))
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.dismiss())

        except Exception as e:
            if progress_dialog:
                try: progress_dialog.dismiss()
                except Exception: pass
            self._send_markdown_msg(params, f"Error: {str(e)}")


    def _get_current_track_lastfm_customfm(self, title, artist, album, image_url, params, progress_dialog=None):
        try:
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(50))

            send_helper = get_send_messages_helper()
            base_dir = ApplicationLoader.getFilesDirFixed()
            temp_dir = File(base_dir, "nowFy")
            if not temp_dir.exists():
                temp_dir.mkdirs()
            filename = f"{uuid.uuid4()}.png"
            temp_path = File(temp_dir, filename).getAbsolutePath()

            card_width, card_height = 1012, 512
            
            player_name = self._detect_current_player()

            if player_name in ["YouTube", "YouTube Music"]:
                youtube_cover = self._get_youtube_cover(title, artist)
                if youtube_cover:
                    try:
                        img_resp = requests.get(youtube_cover, stream=True, timeout=10)
                        img_resp.raise_for_status()
                        album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                        album_cover = ImageOps.fit(album_cover, (350, 350), method=Image.LANCZOS, centering=(0.5, 0.5))
                    except Exception as e:
                        print(f"Erro ao carregar capa do YouTube: {e}")
                        try:
                            img_resp = requests.get("https://i.postimg.cc/K8QK8bjS/icondefault.png", stream=True, timeout=10)
                            img_resp.raise_for_status()
                            album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                            album_cover = album_cover.resize((350, 350), Image.LANCZOS)
                        except Exception as e:
                            print(f"Falha ao obter capa fallback do YouTube: {e}")
                            album_cover = Image.new("RGBA", (350, 350), color=(30, 30, 30))
                else:
                    try:
                        img_resp = requests.get("https://i.postimg.cc/K8QK8bjS/icondefault.png", stream=True, timeout=10)
                        img_resp.raise_for_status()
                        album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                        album_cover = album_cover.resize((350, 350), Image.LANCZOS)
                    except Exception as e:
                        print(f"Falha ao obter capa fallback do YouTube: {e}")
                        album_cover = Image.new("RGBA", (350, 350), color=(30, 30, 30))

            elif image_url:
                try:
                    img_resp = requests.get(image_url, stream=True, timeout=10)
                    img_resp.raise_for_status()
                    album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                    album_cover = album_cover.resize((350, 350), Image.LANCZOS)
                except Exception as e:
                    print(f"Erro ao carregar cover_url: {e}")
                    try:
                        img_resp = requests.get("https://i.postimg.cc/K8QK8bjS/icondefault.png", stream=True, timeout=10)
                        img_resp.raise_for_status()
                        album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                        album_cover = album_cover.resize((350, 350), Image.LANCZOS)
                    except Exception as e:
                        print(f"Falha ao obter capa fallback cover_url: {e}")
                        album_cover = Image.new("RGBA", (350, 350), color=(30, 30, 30))

            else:
                try:
                    img_resp = requests.get("https://i.postimg.cc/K8QK8bjS/icondefault.png", stream=True, timeout=10)
                    img_resp.raise_for_status()
                    album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                    album_cover = album_cover.resize((350, 350), Image.LANCZOS)
                except Exception as e:
                    print(f"Falha ao obter capa fallback geral: {e}")
                    album_cover = Image.new("RGBA", (350, 350), color=(30, 30, 30))

            use_background = self.get_setting("customfm_use_background", True)
            background_color = self.get_setting("customfm_background_color", "#d2d2d2")
            text_color = self.get_setting("customfm_text_color", "#000000")

            if use_background:
                bg_cover = album_cover.copy()
                bg_ratio = max(card_width / bg_cover.width, card_height / bg_cover.height)
                new_size = (int(bg_cover.width * bg_ratio), int(bg_cover.height * bg_ratio))
                bg_cover = bg_cover.resize(new_size, Image.LANCZOS)
                background = Image.new("RGBA", (card_width, card_height))
                blur_bg = bg_cover.filter(ImageFilter.GaussianBlur(30))
                background.paste(blur_bg, (0, 0))
                overlay = Image.new("RGBA", (card_width, card_height), (0, 0, 0, 200))
                background = Image.alpha_composite(background.convert("RGBA"), overlay)
            else:
                bg_color = tuple(int(background_color.lstrip('#')[i:i+2], 16) for i in (0, 2, 4))
                background = Image.new("RGBA", (card_width, card_height), bg_color)

            mask = Image.new("L", (350, 350), 0)
            draw_mask = ImageDraw.Draw(mask)
            draw_mask.rounded_rectangle((0, 0, 350, 350), radius=23, fill=255)
            album_cover.putalpha(mask)

            padding_left = 50
            padding_top = (card_height - 350) // 2
            background.paste(album_cover, (padding_left, padding_top), album_cover)

            font_scale = 1.2
            base_title_size = 33
            base_artist_size = 27

            try:
                font_family = self.get_setting("font_family", 0)
                custom_fonts = self._list_custom_fonts()

                if font_family > 4 and custom_fonts:
                    custom_font_index = font_family - 5
                    if custom_font_index < len(custom_fonts):
                        font_path = os.path.join(self._get_custom_fonts_path(), custom_fonts[custom_font_index])
                    else:
                        font_path = "/system/fonts/NotoSansCJK-Regular.ttc"
                else:
                    font_paths = {
                        0: "/system/fonts/NotoSansCJK-Regular.ttc",
                        1: "/system/fonts/ComingSoon.ttf",
                        2: "/system/fonts/MiClockTibetan-Thin.ttf",
                        3: "/system/fonts/NotoNaskhArabic-Bold.ttf",
                        4: "/system/fonts/SourceSansPro-Bold.ttf"
                    }
                    font_path = font_paths.get(font_family, font_paths[0])

                font_title = ImageFont.truetype(font_path, int(base_title_size * font_scale))
                font_artist = ImageFont.truetype(font_path, int(base_artist_size * font_scale))
            except Exception as e:
                print(f"Error loading font: {e}")
                font_title = font_artist = ImageFont.load_default()

            def limit_text_by_chars(text, max_chars):
                if len(text) > max_chars:
                    return text[:max_chars].rstrip() + "..."
                return text

            draw = ImageDraw.Draw(background)
            text_x = padding_left + 350 + 40
            text_y = padding_top + 50
            text_rgb = tuple(int(text_color.lstrip('#')[i:i+2], 16) for i in (0, 2, 4))
            text = "Listening to".encode('utf-8').decode('utf-8')
            draw.text((text_x, text_y), text, font=font_artist, fill=text_rgb)
            text_y += font_artist.getbbox("Listening to")[3] + 8
            title_display = limit_text_by_chars(title, max_chars=21)
            draw.text((text_x, text_y), title_display, font=font_title, fill=text_rgb)
            text_y += font_title.getbbox(title_display)[3] + 6
            artist_display = limit_text_by_chars(artist, max_chars=25)
            draw.text((text_x, text_y), f"by {artist_display}", font=font_artist, fill=text_rgb)
            text_y += font_artist.getbbox(f"by {artist_display}")[3] + 12

            if not album.strip():
                album_display = "scrobbling"
            else:
                album_display = limit_text_by_chars(album, max_chars=20)
            draw.text((text_x, text_y), f"• {album_display}", font=font_artist, fill=text_rgb)
            text_y += font_artist.getbbox(f"• {album_display}")[3] + 20

            icon_size = 40
            icon_x = text_x
            icon_y = text_y

            corner_radius = 50
            rounded_mask = Image.new("L", (card_width, card_height), 0)
            draw_round = ImageDraw.Draw(rounded_mask)
            draw_round.rounded_rectangle((0, 0, card_width, card_height), corner_radius, fill=255)
            background.putalpha(rounded_mask)

            player_name = self._detect_current_player()
            icon_url = self._get_service_icon(player_name)
            if icon_url:
                try:
                    response = requests.get(icon_url, stream=True)
                    if response.status_code == 200:
                        icon_image = Image.open(BytesIO(response.content)).convert('RGBA')
                        icon_image = icon_image.resize((icon_size, icon_size), Image.LANCZOS)
                        background.paste(icon_image, (icon_x, icon_y), icon_image)
                except Exception as e:
                    print(f"Service icon error: {e}")

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(90))
            background.convert("RGB").save(temp_path)

            footer = self.get_setting("custom_footer_text", "♪ ıllıllı - I'm using nowFy!\n╰ by @exteraDevPlugins")
            show_profile = self.get_setting("show_profile_link", True)

            caption = footer

            player_name = self._detect_current_player()
            if player_name == "YouTube":
                search_query = f"{artist} {title} official music video"
                song_link = f"https://www.youtube.com/results?search_query={requests.utils.quote(search_query)}"
                caption = f"{footer} [YouTube]({song_link})"
            elif player_name == "YouTube Music":
                search_query = f"{artist} {title}"
                song_link = f"https://music.youtube.com/search?q={requests.utils.quote(search_query)}"
                caption = f"{footer} [YouTube Music]({song_link})"
            elif player_name == "Spotify":
                song_link = f"https://open.spotify.com/search/{artist.replace(' ', '+')}%20{title.replace(' ', '+')}"
                caption = f"{footer} [Spotify]({song_link})"

            if show_profile:
                user = self.get_setting("lastfm_user", "")
                if user:
                    profile_link = f"https://www.last.fm/user/{user}"
                    caption = f"{caption} • [{self.tr('lastfm_profile')}]({profile_link})"

            caption_md = parse_markdown(caption)
            params.caption = caption_md.text
            params.entities = ArrayList()
            for entity in caption_md.entities:
                params.entities.add(entity.to_tlrpc_object())

            photo = send_helper.generatePhotoSizes(temp_path, None)
            params.photo = photo
            params.path = temp_path
            params.message = None

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(100))
            run_on_ui_thread(lambda: send_helper.sendMessage(params))
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.dismiss())

        except Exception as e:
            if progress_dialog:
                try: progress_dialog.dismiss()
                except Exception: pass
            self._send_markdown_msg(params, f"Error: {str(e)}")

    def _get_current_track_lastfm_dark(self, title, artist, album, image_url, params, progress_dialog=None):
        try:
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(50))

            send_helper = get_send_messages_helper()
            base_dir = ApplicationLoader.getFilesDirFixed()
            temp_dir = File(base_dir, "nowFy")
            if not temp_dir.exists():
                temp_dir.mkdirs()
            filename = f"{uuid.uuid4()}.png"
            temp_path = File(temp_dir, filename).getAbsolutePath()

            player_name = self._detect_current_player()

            if player_name in ["YouTube", "YouTube Music"]:
                youtube_cover = self._get_youtube_cover(title, artist)
                if youtube_cover:
                    try:
                        img_resp = requests.get(youtube_cover, stream=True, timeout=10)
                        img_resp.raise_for_status()
                        album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                        album_cover = ImageOps.fit(album_cover, (350, 350), method=Image.LANCZOS, centering=(0.5, 0.5))
                    except Exception as e:
                        print(f"Erro ao carregar capa do YouTube: {e}")
                        try:
                            fallback_url = "https://i.postimg.cc/K8QK8bjS/icondefault.png"
                            img_resp = requests.get(fallback_url, stream=True, timeout=10)
                            img_resp.raise_for_status()
                            album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                            album_cover = album_cover.resize((350, 350), Image.LANCZOS)
                        except Exception as e:
                            print(f"Falha ao obter capa fallback do YouTube: {e}")
                            album_cover = Image.new("RGBA", (350, 350), color=(30, 30, 30))
                else:
                    try:
                        fallback_url = "https://i.postimg.cc/K8QK8bjS/icondefault.png"
                        img_resp = requests.get(fallback_url, stream=True, timeout=10)
                        img_resp.raise_for_status()
                        album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                        album_cover = album_cover.resize((350, 350), Image.LANCZOS)
                    except Exception as e:
                        print(f"Falha ao obter capa fallback do YouTube: {e}")
                        album_cover = Image.new("RGBA", (350, 350), color=(30, 30, 30))

            elif image_url:
                try:
                    img_resp = requests.get(image_url, stream=True, timeout=10)
                    img_resp.raise_for_status()
                    album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                    album_cover = album_cover.resize((350, 350), Image.LANCZOS)
                except Exception as e:
                    print(f"Erro ao carregar cover_url: {e}")
                    try:
                        fallback_url = "https://i.postimg.cc/K8QK8bjS/icondefault.png"
                        img_resp = requests.get(fallback_url, stream=True, timeout=10)
                        img_resp.raise_for_status()
                        album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                        album_cover = album_cover.resize((350, 350), Image.LANCZOS)
                    except Exception as e:
                        print(f"Falha ao obter capa fallback cover_url: {e}")
                        album_cover = Image.new("RGBA", (350, 350), color=(30, 30, 30))

            else:
                try:
                    fallback_url = "https://i.postimg.cc/K8QK8bjS/icondefault.png"
                    img_resp = requests.get(fallback_url, stream=True, timeout=10)
                    img_resp.raise_for_status()
                    album_cover = Image.open(BytesIO(img_resp.content)).convert("RGBA")
                    album_cover = album_cover.resize((350, 350), Image.LANCZOS)
                except Exception as e:
                    print(f"Falha ao obter capa fallback geral: {e}")
                    album_cover = Image.new("RGBA", (350, 350), color=(30, 30, 30))

            card_width, card_height = 1012, 512
            bg_cover = album_cover.copy()
            bg_ratio = max(card_width / bg_cover.width, card_height / bg_cover.height)
            new_size = (int(bg_cover.width * bg_ratio), int(bg_cover.height * bg_ratio))
            bg_cover = bg_cover.resize(new_size, Image.LANCZOS)
            background = Image.new("RGBA", (card_width, card_height))
            blur_bg = bg_cover.filter(ImageFilter.GaussianBlur(30))
            background.paste(blur_bg, (0, 0))
            overlay = Image.new("RGBA", (card_width, card_height), (0, 0, 0, 200))
            background = Image.alpha_composite(background.convert("RGBA"), overlay)

            mask = Image.new("L", (350, 350), 0)
            draw_mask = ImageDraw.Draw(mask)
            draw_mask.rounded_rectangle((0, 0, 350, 350), radius=23, fill=255)
            album_cover.putalpha(mask)

            padding_left = 50
            padding_top = (card_height - 350) // 2
            background.paste(album_cover, (padding_left, padding_top), album_cover)

            font_scale = 1.2
            base_title_size = 33
            base_artist_size = 27

            try:
                font_family = self.get_setting("font_family", 0)
                custom_fonts = self._list_custom_fonts()

                if font_family > 4 and custom_fonts:
                    custom_font_index = font_family - 5
                    if custom_font_index < len(custom_fonts):
                        font_path = os.path.join(self._get_custom_fonts_path(), custom_fonts[custom_font_index])
                    else:
                        font_path = "/system/fonts/NotoSansCJK-Regular.ttc"
                else:
                    font_paths = {
                        0: "/system/fonts/NotoSansCJK-Regular.ttc",
                        1: "/system/fonts/ComingSoon.ttf",
                        2: "/system/fonts/MiClockTibetan-Thin.ttf",
                        3: "/system/fonts/NotoNaskhArabic-Bold.ttf",
                        4: "/system/fonts/SourceSansPro-Bold.ttf"
                    }
                    font_path = font_paths.get(font_family, font_paths[0])

                font_title = ImageFont.truetype(font_path, int(base_title_size * font_scale))
                font_artist = ImageFont.truetype(font_path, int(base_artist_size * font_scale))
            except Exception as e:
                print(f"Error loading font: {e}")
                font_title = font_artist = ImageFont.load_default()

            def limit_text_by_chars(text, max_chars):
                if len(text) > max_chars:
                    return text[:max_chars].rstrip() + "..."
                return text

            draw = ImageDraw.Draw(background)
            text_x = padding_left + 350 + 40
            text_y = padding_top + 50

            draw.text((text_x, text_y), "Listening to", font=font_artist, fill=(210, 210, 210))
            text_y += font_artist.getbbox("Listening to")[3] + 8

            title_display = limit_text_by_chars(title, max_chars=21)
            draw.text((text_x, text_y), title_display, font=font_title, fill="white")
            text_y += font_title.getbbox(title_display)[3] + 6

            artist_display = limit_text_by_chars(artist, max_chars=25)
            draw.text((text_x, text_y), f"by {artist_display}", font=font_artist, fill=(180, 180, 180))
            text_y += font_artist.getbbox(f"by {artist_display}")[3] + 12

            if not album.strip():
                album_display = "scrobbling"
            else:
                album_display = limit_text_by_chars(album, max_chars=20)
            draw.text((text_x, text_y), f"• {album_display}", font=font_artist, fill=(200, 200, 200))
            text_y += font_artist.getbbox(f"• {album_display}")[3] + 20

            icon_size = 40
            icon_x = text_x
            icon_y = text_y

            corner_radius = 50
            rounded_mask = Image.new("L", (card_width, card_height), 0)
            draw_round = ImageDraw.Draw(rounded_mask)
            draw_round.rounded_rectangle((0, 0, card_width, card_height), corner_radius, fill=255)
            background.putalpha(rounded_mask)

            icon_url = self._get_service_icon(player_name)
            if icon_url:
                try:
                    response = requests.get(icon_url, stream=True)
                    if response.status_code == 200:
                        icon_image = Image.open(BytesIO(response.content)).convert("RGBA")
                        icon_image = icon_image.resize((icon_size, icon_size), Image.LANCZOS)
                        background.paste(icon_image, (icon_x, icon_y), icon_image)
                except Exception as e:
                    print(f"Service icon error: {e}")

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(90))
            background.convert("RGB").save(temp_path)

            footer = self.get_setting("custom_footer_text", "♪ ıllıllı - I'm using nowFy!\n╰ by @exteraDevPlugins")
            show_profile = self.get_setting("show_profile_link", True)

            if player_name == "YouTube":
                search_query = f"{artist} {title} official music video"
                track_link = f"https://www.youtube.com/results?search_query={requests.utils.quote(search_query)}"
                track_link_text = f"[YouTube]({track_link})"
            elif player_name == "YouTube Music":
                search_query = f"{artist} {title}"
                track_link = f"https://music.youtube.com/search?q={requests.utils.quote(search_query)}"
                track_link_text = f"[YouTube Music]({track_link})"
            elif player_name == "Spotify":
                track_link = f"https://open.spotify.com/search/{artist.replace(' ', '+')}%20{title.replace(' ', '+')}"
                track_link_text = f"[Spotify]({track_link})"
            else:
                track_link = f"https://www.last.fm/music/{artist.replace(' ', '+')}/_/{title.replace(' ', '+')}"
                track_link_text = f"[{self.tr('track_link')}]({track_link})"

            caption = f"{footer} • {track_link_text}"

            if show_profile:
                user = self.get_setting("lastfm_user", "")
                if user:
                    profile_link = f"https://www.last.fm/user/{user}"
                    caption = f"{caption} • [{self.tr('lastfm_profile')}]({profile_link})"

            caption_md = parse_markdown(caption)
            params.caption = caption_md.text
            params.entities = ArrayList()
            for entity in caption_md.entities:
                params.entities.add(entity.to_tlrpc_object())

            photo = send_helper.generatePhotoSizes(temp_path, None)
            params.photo = photo
            params.path = temp_path
            params.message = None

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(100))
            run_on_ui_thread(lambda: send_helper.sendMessage(params))
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.dismiss())

        except Exception as e:
            if progress_dialog:
                try:
                    progress_dialog.dismiss()
                except Exception:
                    pass
            self._send_markdown_msg(params, f"Error: {str(e)}")

    def _get_current_track(self, account, params, progress_dialog=None):
        try:
            theme_style = self.get_setting("now_theme_style", 0)
            if theme_style == 0:
                return self._get_current_track_legacy(account, params, progress_dialog)
            elif theme_style == 1: 
                return self._get_current_track_lastfy(account, params, progress_dialog)
            elif theme_style == 2:
                return self._get_current_track_lastfyw(account, params, progress_dialog)
            elif theme_style == 3:
                return self._get_current_track_lastfyblack(account, params, progress_dialog)
            elif theme_style == 4:
                return self._get_current_track_miniplayer(account, params, progress_dialog) 
            elif theme_style == 5:
                return self._get_current_track_discord(account, params, progress_dialog)
            elif theme_style == 6:
                return self._get_current_track_discord_light(account, params, progress_dialog) 
            elif theme_style == 7:
                return self._get_current_track_apple(account, params, progress_dialog)   
        except Exception as e:
            if progress_dialog:
                try: progress_dialog.dismiss()
                except Exception: pass
            self._send_markdown_msg(params, f"❌ Error: {str(e)}")
      
    def _get_recently_played(self, params, progress_dialog=None):
        try:
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(10))
            
            token = self._get_access_token()
            if not token:
                if progress_dialog:
                    progress_dialog.dismiss()
                self._send_markdown_msg(params, "Could not authenticate with Spotify.")
                return

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(30))

            resp = requests.get(
                "https://api.spotify.com/v1/me/player/recently-played?limit=5",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )
            
            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(40))

            if resp.status_code != 200:
                if progress_dialog:
                    progress_dialog.dismiss()
                self._send_markdown_msg(params, "Failed to fetch recently played tracks.")
                return

            data = resp.json()
            tracks = data.get("items", [])
            if not tracks:
                if progress_dialog:
                    progress_dialog.dismiss()
                BulletinHelper.show_info(self.tr("no_recently_played"))
                return

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(50))

            try:
                image_url = self.tr("playblack_image")
                response = requests.get(image_url, timeout=5)
                if response.status_code == 200:
                    img_data = BytesIO(response.content)
                    img = Image.open(img_data)
                    img = img.convert("RGB")
                    img = img.resize((935, 514), Image.LANCZOS)

                    if progress_dialog:
                        run_on_ui_thread(lambda: progress_dialog.set_progress(70))

                    temp_path = os.path.join(ApplicationLoader.getFilesDirFixed(), "list_image.jpg")
                    img.save(temp_path, "JPEG", quality=95)

                    if progress_dialog:
                        run_on_ui_thread(lambda: progress_dialog.set_progress(80))
            except Exception as e:
                print(f"Error processing image: {e}")
                if progress_dialog:
                    progress_dialog.dismiss()
                BulletinHelper.show_info(self.tr("error_processing_image").format(str(e)))
                return

            message = ""
            for item in tracks:
                track = item["track"]
                title = track["name"]
                artists = ", ".join([a["name"] for a in track["artists"]])
                album = track["album"]["name"]
                spotify_link = track["external_urls"]["spotify"]
                link = self.get_universal_song_link(spotify_link) or spotify_link
                safe_link = link.replace("https://", "https://\u200B")

                if not message:
                    message = self.tr("playback_highlights") + "\n\n"
                spotify_link_text = self.get_setting("spotify_link_text", "")
                if not spotify_link_text.strip():
                    spotify_link_text = self.tr("spotify_link_text")
                if self.get_setting("use_spotify_link", False):
                    message += f"˪ {title}\nby {artists} • [{spotify_link_text}]({spotify_link})\n\n"
                else:
                    message += f"˪ {title}\nby {artists}\n\n"   

            caption_md = parse_markdown(message.strip())
            params.message = caption_md.text
            params.entities = ArrayList()
            for entity in caption_md.entities:
                params.entities.add(entity.to_tlrpc_object())

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(90))

            send_helper = get_send_messages_helper()
            photo_sizes = send_helper.generatePhotoSizes(temp_path)
            if photo_sizes:
                params.photo = photo_sizes
                params.path = temp_path

            if progress_dialog:
                run_on_ui_thread(lambda: progress_dialog.set_progress(100))
                progress_dialog.dismiss()

            run_on_ui_thread(lambda: send_helper.sendMessage(params))

        except Exception as e:
            print(f"Error getting recently played tracks: {e}")
            if progress_dialog:
                try:
                    progress_dialog.dismiss()
                except Exception:
                    pass
            BulletinHelper.show_info(f"❌ Error: {str(e)}")
            if not token:
                BulletinHelper.show_info(self.tr("error_auth"))
                return

            resp = requests.get(
                "https://api.spotify.com/v1/me/player/recently-played?limit=5",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if resp.status_code != 200:
                BulletinHelper.show_info(self.tr("error_recently_played"))
                return

            data = resp.json()
            tracks = data.get("items", [])
            if not tracks:
                self._send_markdown_msg(params, "No recently played tracks found.")
                return

            result = []
            for item in tracks:
                track = item["track"]
                title = track["name"]
                artists = ", ".join([a["name"] for a in track["artists"]])
                album = track["album"]["name"]
                spotify_link = track["external_urls"]["spotify"]
                universal_link = self.get_universal_song_link(spotify_link) or spotify_link
                result.append(f"˪ {title} from {album}\nby {artists} • [song.link]({universal_link})\n")

            caption = "\n".join(result)
            caption_md = parse_markdown(caption)
            params.message = caption_md.text
            params.entities = ArrayList()
            for entity in caption_md.entities:
                params.entities.add(entity.to_tlrpc_object())
            run_on_ui_thread(lambda: get_send_messages_helper().sendMessage(params))

        except Exception as e:
            BulletinHelper.show_info(self.tr("error_recently_played_exception").format(str(e)))

    def _send_msg(self, params, text):
        with self._message_lock:
            params.message = text
            params.entities = ArrayList()
            run_on_ui_thread(lambda: get_send_messages_helper().sendMessage(params))

    def _search_tracks(self, account, params, query):
        try:
            token = self._get_access_token()
            if not token:
                self._send_markdown_msg(params, self.tr("error_auth"))
                return

            search_type = "track"
            if query.startswith(".album "):
                search_type = "album"
                query = query[7:].strip()

            resp = requests.get(
                f"https://api.spotify.com/v1/search?q={query}&type={search_type}&limit=5",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if resp.status_code != 200:
                self._send_markdown_msg(params, self.tr("error_search").format(search_type))
                return

            data = resp.json()
            items = data.get(f"{search_type}s", {}).get("items", [])
            if not items:
                self._send_markdown_msg(params, self.tr("no_results").format(search_type))
                return

            self._search_cache[account] = items

            message = f"{self.tr('search_results')}\n\n"
            for i, item in enumerate(items, 1):
                title = item["name"]
                artists = ", ".join([a["name"] for a in item["artists"]])
                if search_type == "track":
                    album = item["album"]["name"]
                    message += f"{i}. {title}\nby {artists} • {album}\n\n"
                else:
                    total_tracks = item["total_tracks"]
                    release_date = item["release_date"][:4]
                    message += f"{i}. {title}\nby {artists} • {total_tracks} tracks • {release_date}\n\n"

            play_type = 'a track' if search_type == 'track' else 'an album'
            message += f"\n{self.tr('use_play_command').format(play_type)}"
            self._send_markdown_msg(params, message)

        except Exception as e:
            self._send_markdown_msg(params, self.tr("error_search_exception").format(str(e)))

    def _set_volume(self, account, params, value):
        try:
            if value < 0 or value > 100:
                BulletinHelper.show_info("Volume must be between 0 and 100.")
                return

            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info("ⓘ Could not authenticate with Spotify.")
                return

            resp = requests.put(
                f"https://api.spotify.com/v1/me/player/volume?volume_percent={value}",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if resp.status_code == 204:
                BulletinHelper.show_info(f"Volume set to {value}%")
            elif resp.status_code == 404:
                BulletinHelper.show_info("No active Spotify device found.")
            else:
                BulletinHelper.show_info(f"Failed to set volume: {resp.status_code}")
        except Exception as e:
            BulletinHelper.show_info(f"Error: {str(e)}")

    def _adjust_volume(self, account, params, offset):
        try:
            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info("ⓘ Could not authenticate with Spotify.")
                return

            resp = requests.get(
                "https://api.spotify.com/v1/me/player",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )
            if resp.status_code != 200:
                BulletinHelper.show_info("Could not fetch current player.")
                return

            data = resp.json()
            current_volume = data.get("device", {}).get("volume_percent", 50)
            new_volume = max(0, min(100, current_volume + offset))

            self._set_volume(account, params, new_volume)

        except Exception as e:
            BulletinHelper.show_info(f"Error: {str(e)}")


    def _play_track(self, account, params, track_number):
        try:
            if not hasattr(self, '_search_cache') or account not in self._search_cache:
                BulletinHelper.show_info(self.tr("no_search_results"))
                return

            try:
                item_index = int(track_number) - 1
                if item_index < 0 or item_index >= len(self._search_cache[account]):
                    BulletinHelper.show_info(self.tr("error_invalid_number"))
                    return
            except ValueError:
                BulletinHelper.show_info(self.tr("error_invalid_number"))
                return

            token = self._get_access_token()
            if not token:
                self._send_markdown_msg(params, "ⓘ Could not authenticate with Spotify.")
                return

            item = self._search_cache[account][item_index]
            is_album = "total_tracks" in item 

            if is_album:
                play_request = {"context_uri": item["uri"]}
            else:
                play_request = {"uris": [item["uri"]]}

            resp = requests.put(
                "https://api.spotify.com/v1/me/player/play",
                headers={"Authorization": f"Bearer {token}"},
                json=play_request,
                timeout=5
            )

            if resp.status_code == 404:
                BulletinHelper.show_info(self.tr("error_no_device"))
            elif resp.status_code != 204:
                BulletinHelper.show_info(self.tr("error_playback_start"))
            else:
                title = item["name"]
                artists = ", ".join([a["name"] for a in item["artists"]])
                BulletinHelper.show_info(self.tr("bulletin_play_start"))

        except Exception as e:
            BulletinHelper.show_info(self.tr("error_play_exception").format(str(e)))


    def _like_track(self, account, params, track_number=None):
        try:
            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info(self.tr("error_authentication_failed"))
                return

            if track_number is None:
                resp = requests.get(
                    "https://api.spotify.com/v1/me/player/currently-playing",
                    headers={"Authorization": f"Bearer {token}"},
                    timeout=5
                )

                if resp.status_code == 204:
                    BulletinHelper.show_info(self.tr("no_track_playing"))
                    return
                elif resp.status_code != 200:
                    BulletinHelper.show_info(self.tr("error_playback_start"))
                    return

                data = resp.json()
                if not data.get("item"):
                    BulletinHelper.show_info(self.tr("no_track_playing"))
                    return

                track_id = data["item"]["id"]
                check_resp = requests.get(
                    f"https://api.spotify.com/v1/me/tracks/contains?ids={track_id}",
                    headers={"Authorization": f"Bearer {token}"},
                    timeout=5
                )
                
                if check_resp.status_code == 200:
                    is_liked = check_resp.json()[0]
                    method = "DELETE" if is_liked else "PUT"
                else:
                    method = "PUT"
                
                resp = requests.request(
                    method,
                    f"https://api.spotify.com/v1/me/tracks?ids={track_id}",
                    headers={"Authorization": f"Bearer {token}"},
                    timeout=5
                )

                if resp.status_code == 401:
                    BulletinHelper.show_info(self.tr("error_unauthorized"))
                elif resp.status_code == 403:
                    BulletinHelper.show_info(self.tr("error_premium_required"))
                elif resp.status_code not in [200, 204]:
                    BulletinHelper.show_info(self.tr("error_like_failed"))
                else:
                    if method == "PUT":
                        BulletinHelper.show_info(self.tr("bulletin_like_success"))
                    else:
                        BulletinHelper.show_info(self.tr("track_unlike_success"))

            else:
                if not hasattr(self, '_search_cache') or account not in self._search_cache:
                    BulletinHelper.show_info(self.tr("no_search_results"))
                    return

                try:
                    item_index = int(track_number) - 1
                    if item_index < 0 or item_index >= len(self._search_cache[account]):
                        BulletinHelper.show_info(self.tr("error_invalid_number"))
                        return
                except ValueError:
                    BulletinHelper.show_info(self.tr("error_invalid_number"))
                    return

                item = self._search_cache[account][item_index]
                if "uri" not in item:
                    BulletinHelper.show_info(self.tr("error_invalid_item"))
                    return

                track_id = item["uri"].split(":")[-1]
                resp = requests.put(
                    f"https://api.spotify.com/v1/me/tracks?ids={track_id}",
                    headers={"Authorization": f"Bearer {token}"},
                    timeout=5
                )

                if resp.status_code == 401:
                    BulletinHelper.show_info(self.tr("error_unauthorized"))
                elif resp.status_code == 403:
                    BulletinHelper.show_info(self.tr("error_premium_required"))
                elif resp.status_code not in [200, 204]:
                    BulletinHelper.show_info(self.tr("error_like_failed"))
                else:
                    BulletinHelper.show_info(self.tr("bulletin_like_success"))

        except Exception as e:
            BulletinHelper.show_info(self.tr("error_like_exception").format(str(e)))
            
    def _shuffle(self, account, params):
        try:
            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info(self.tr("error_authentication_failed"))
                return
                
            BulletinHelper.show_info(self.tr("shuffle_success"))
        except Exception as e:
            BulletinHelper.show_info(self.tr("error_shuffle_failed").format(str(e)))
            
    def _repeat(self, account, params):
        try:
            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info(self.tr("error_authentication_failed"))
                return
                
            BulletinHelper.show_info(self.tr("repeat_success"))
        except Exception as e:
            BulletinHelper.show_info(self.tr("error_repeat_failed").format(str(e)))
                      
    def _queue(self, account, params):
        try:
            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info(self.tr("error_authentication_failed"))
                return
                
            BulletinHelper.show_info(self.tr("queue_success"))
        except Exception as e:
            BulletinHelper.show_info(self.tr("error_queue_failed").format(str(e)))
            
    def _toggle_shuffle(self, account, params):
        try:
            token = self._get_access_token()
            if not token:
                self._send_markdown_msg(params, "ⓘ Could not authenticate with Spotify.")
                return

            resp = requests.get(
                "https://api.spotify.com/v1/me/player",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if resp.status_code == 401:
                BulletinHelper.show_info(self.tr("error_unauthorized"))
                return
            elif resp.status_code == 403:
                BulletinHelper.show_info(self.tr("error_premium_required"))
                return
            elif resp.status_code == 404:
                BulletinHelper.show_info(self.tr("error_no_device"))
                return

            data = resp.json()
            current_shuffle = data.get("shuffle_state", False)
            new_shuffle = not current_shuffle

            resp = requests.put(
                f"https://api.spotify.com/v1/me/player/shuffle?state={str(new_shuffle).lower()}",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if resp.status_code == 204:
                state_key = "shuffle_enabled" if new_shuffle else "shuffle_disabled"
                BulletinHelper.show_info(self.tr(state_key))
            else:
                BulletinHelper.show_info(self.tr("shuffle_is_on"))

        except Exception as e:
            BulletinHelper.show_info(self.tr("shuffle_error").format(str(e)))

    def _toggle_repeat(self, account, params):
        try:
            token = self._get_access_token()
            if not token:
                self._send_markdown_msg(params, "ⓘ Could not authenticate with Spotify.")
                return

            resp = requests.get(
                "https://api.spotify.com/v1/me/player",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if resp.status_code == 401:
                BulletinHelper.show_info(self.tr("error_unauthorized"))
                return
            elif resp.status_code == 403:
                BulletinHelper.show_info(self.tr("error_premium_required"))
                return
            elif resp.status_code == 404:
                BulletinHelper.show_info(self.tr("error_no_device"))
                return

            data = resp.json()
            current_repeat = data.get("repeat_state", "off")
            
            states = {"off": "context", "context": "track", "track": "off"}
            new_repeat = states.get(current_repeat, "off")

            resp = requests.put(
                f"https://api.spotify.com/v1/me/player/repeat?state={new_repeat}",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if resp.status_code == 204:
                state_key = f"repeat_{new_repeat}"
                BulletinHelper.show_info(self.tr(state_key))
            else:
                BulletinHelper.show_info(self.tr("error_playback_start"))

        except Exception as e:
            BulletinHelper.show_info(self.tr("error_playback_exception").format(str(e)))

    def _add_to_queue(self, account, params, track_number):
        try:
            if not hasattr(self, '_search_cache') or account not in self._search_cache:
                BulletinHelper.show_info(self.tr("no_search_results"))
                return

            try:
                item_index = int(track_number) - 1
                if item_index < 0 or item_index >= len(self._search_cache[account]):
                    BulletinHelper.show_info(self.tr("error_invalid_number"))
                    return
            except ValueError:
                BulletinHelper.show_info(self.tr("error_invalid_number"))
                return

            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info("ⓘ Could not authenticate with Spotify.")
                return

            item = self._search_cache[account][item_index]
            if "uri" not in item:
                BulletinHelper.show_info(self.tr("error_invalid_item"))
                return

            resp = requests.post(
                f"https://api.spotify.com/v1/me/player/queue?uri={item['uri']}",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if resp.status_code in [200, 204]:
                title = item["name"]
                artists = ", ".join([a["name"] for a in item["artists"]])
                BulletinHelper.show_info(self.tr("track_added_to_queue").format(title=title, artists=artists))
            elif resp.status_code == 404:
                BulletinHelper.show_info(self.tr("error_no_device"))
            else:
                BulletinHelper.show_info(self.tr("error_queue_failed"))

        except Exception as e:
            BulletinHelper.show_info(f"Erro: {str(e)}")


    def _create_mix(self, account, params, query):
        try:
            token = self._get_access_token()
            if not token:
                BulletinHelper.show_info(self.tr("error_auth_failed"))
                return

            search_resp = requests.get(
                f"https://api.spotify.com/v1/search?q={query}&type=track&limit=20",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if search_resp.status_code != 200:
                BulletinHelper.show_info(self.tr("error_search_failed"))
                return

            search_data = search_resp.json()
            tracks = search_data.get("tracks", {}).get("items", [])

            if not tracks:
                BulletinHelper.show_info(self.tr("no_search_results"))
                return

            user_resp = requests.get(
                "https://api.spotify.com/v1/me",
                headers={"Authorization": f"Bearer {token}"},
                timeout=5
            )

            if user_resp.status_code != 200:
                BulletinHelper.show_info(self.tr("error_user_info"))
                return

            user_id = user_resp.json()["id"]

            playlist_name = f"Mix: {query}"
            playlist_resp = requests.post(
                f"https://api.spotify.com/v1/users/{user_id}/playlists",
                headers={"Authorization": f"Bearer {token}"},
                json={
                    "name": playlist_name,
                    "description": self.tr("mix_description").format(query=query),
                    "public": False
                },
                timeout=5
            )

            if playlist_resp.status_code != 201:
                BulletinHelper.show_info(self.tr("error_create_playlist"))
                return

            playlist_data = playlist_resp.json()
            playlist_id = playlist_data["id"]

            track_uris = [track["uri"] for track in tracks]
            add_tracks_resp = requests.post(
                f"https://api.spotify.com/v1/playlists/{playlist_id}/tracks",
                headers={"Authorization": f"Bearer {token}"},
                json={"uris": track_uris},
                timeout=5
            )

            if add_tracks_resp.status_code != 201:
                BulletinHelper.show_info(self.tr("error_add_tracks"))
                return

            fragment = get_last_fragment()
            ctx = fragment.getParentActivity() if fragment and fragment.getParentActivity() else ApplicationLoader.applicationContext
            
            message = self.tr("mix_tracks_list").format(query=query) + "\n\n"
            for i, track in enumerate(tracks, 1):
                title = track["name"]
                artists = ", ".join([a["name"] for a in track["artists"]])
                message += f"{i}. {title} – {artists}\n"

            dialog = AlertDialog.Builder(ctx)
            dialog.setTitle(self.tr("mix_created_title"))
            dialog.setMessage(message)
            dialog.setPositiveButton("OK", None)
            run_on_ui_thread(lambda: dialog.show())

        except Exception as e:
            BulletinHelper.show_info(self.tr("error_mix_exception").format(str(e)))

    def get_universal_song_link(self, spotify_url):
        try:
            api_url = f"https://api.song.link/v1-alpha.1/links?url={spotify_url}"
            resp = requests.get(api_url, timeout=10)
            data = resp.json()
            return data.get("pageUrl")
        except Exception as e:
            print(f"[song.link] {e}"),