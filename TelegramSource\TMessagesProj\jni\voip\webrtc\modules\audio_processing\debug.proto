syntax = "proto2";
option optimize_for = LITE_RUNTIME;
package webrtc.audioproc;

// Contains the format of input/output/reverse audio. An Init message is added
// when any of the fields are changed.
message Init {
  optional int32 sample_rate = 1;
  optional int32 device_sample_rate = 2 [deprecated=true];
  optional int32 num_input_channels = 3;
  optional int32 num_output_channels = 4;
  optional int32 num_reverse_channels = 5;
  optional int32 reverse_sample_rate = 6;
  optional int32 output_sample_rate = 7;
  optional int32 reverse_output_sample_rate = 8;
  optional int32 num_reverse_output_channels = 9;
  optional int64 timestamp_ms = 10;
}

// May contain interleaved or deinterleaved data, but don't store both formats.
message ReverseStream {
  // int16 interleaved data.
  optional bytes data = 1;

  // float deinterleaved data, where each repeated element points to a single
  // channel buffer of data.
  repeated bytes channel = 2;
}

// May contain interleaved or deinterleaved data, but don't store both formats.
message Stream {
  // int16 interleaved data.
  optional bytes input_data = 1;
  optional bytes output_data = 2;

  optional int32 delay = 3;
  optional sint32 drift = 4;
  optional int32 applied_input_volume = 5;
  optional bool keypress = 6;

  // float deinterleaved data, where each repeated element points to a single
  // channel buffer of data.
  repeated bytes input_channel = 7;
  repeated bytes output_channel = 8;
}

// Contains the configurations of various APM component. A Config message is
// added when any of the fields are changed.
message Config {
  // Acoustic echo canceler.
  optional bool aec_enabled = 1;
  optional bool aec_delay_agnostic_enabled = 2;
  optional bool aec_drift_compensation_enabled = 3;
  optional bool aec_extended_filter_enabled = 4;
  optional int32 aec_suppression_level = 5;
  // Mobile AEC.
  optional bool aecm_enabled = 6;
  optional bool aecm_comfort_noise_enabled = 7 [deprecated = true];
  optional int32 aecm_routing_mode = 8 [deprecated = true];
  // Automatic gain controller.
  optional bool agc_enabled = 9;
  optional int32 agc_mode = 10;
  optional bool agc_limiter_enabled = 11;
  optional bool noise_robust_agc_enabled = 12;
  // High pass filter.
  optional bool hpf_enabled = 13;
  // Noise suppression.
  optional bool ns_enabled = 14;
  optional int32 ns_level = 15;
  // Transient suppression.
  optional bool transient_suppression_enabled = 16;
  // Semicolon-separated string containing experimental feature
  // descriptions.
  optional string experiments_description = 17;
  reserved 18;  // Intelligibility enhancer enabled (deprecated).
  // Pre amplifier.
  optional bool pre_amplifier_enabled = 19;
  optional float pre_amplifier_fixed_gain_factor = 20;

  // Next field number 21.
}

message PlayoutAudioDeviceInfo {
  optional int32 id = 1;
  optional int32 max_volume = 2;
}

message RuntimeSetting {
  optional float capture_pre_gain = 1;
  optional float custom_render_processing_setting = 2;
  optional float capture_fixed_post_gain = 3;
  optional int32 playout_volume_change = 4;
  optional PlayoutAudioDeviceInfo playout_audio_device_change = 5;
  optional bool capture_output_used = 6;
  optional float capture_post_gain = 7;
}

message Event {
  enum Type {
    INIT = 0;
    REVERSE_STREAM = 1;
    STREAM = 2;
    CONFIG = 3;
    UNKNOWN_EVENT = 4;
    RUNTIME_SETTING = 5;
  }

  required Type type = 1;

  optional Init init = 2;
  optional ReverseStream reverse_stream = 3;
  optional Stream stream = 4;
  optional Config config = 5;
  optional RuntimeSetting runtime_setting = 6;
}
