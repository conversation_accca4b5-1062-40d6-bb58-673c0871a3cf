/* src/config.h.in.  Generated from configure.ac by autoheader.  */

/* Namespace for Google classes */
#undef GOOGLE_NAMESPACE

/* Define if you have the `dladdr' function */
#undef HAVE_DLADDR

/* Define to 1 if you have the <dlfcn.h> header file. */
#undef HAVE_DLFCN_H

/* Define to 1 if you have the <execinfo.h> header file. */
#undef HAVE_EXECINFO_H

/* Define if you have the `fcntl' function */
#undef HAVE_FCNTL

/* Define to 1 if you have the <glob.h> header file. */
#undef HAVE_GLOB_H

/* Define to 1 if you have the <inttypes.h> header file. */
#undef HAVE_INTTYPES_H

/* Define to 1 if you have the `pthread' library (-lpthread). */
#undef HAVE_LIBPTHREAD

/* Define to 1 if you have the <libunwind.h> header file. */
#undef HAVE_LIBUNWIND_H

/* define if you have google gflags library */
#undef HAVE_LIB_GFLAGS

/* define if you have google gmock library */
#undef HAVE_LIB_GMOCK

/* define if you have google gtest library */
#undef HAVE_LIB_GTEST

/* define if you have libunwind */
#undef HAVE_LIB_UNWIND

/* Define to 1 if you have the <memory.h> header file. */
#undef HAVE_MEMORY_H

/* define if the compiler implements namespaces */
#undef HAVE_NAMESPACES

/* Define if you have POSIX threads libraries and header files. */
#undef HAVE_PTHREAD

/* Define to 1 if you have the <pwd.h> header file. */
#undef HAVE_PWD_H

/* define if the compiler implements pthread_rwlock_* */
#undef HAVE_RWLOCK

/* Define if you have the `sigaltstack' function */
#undef HAVE_SIGALTSTACK

/* Define to 1 if you have the <stdint.h> header file. */
#undef HAVE_STDINT_H

/* Define to 1 if you have the <stdlib.h> header file. */
#undef HAVE_STDLIB_H

/* Define to 1 if you have the <strings.h> header file. */
#undef HAVE_STRINGS_H

/* Define to 1 if you have the <string.h> header file. */
#undef HAVE_STRING_H

/* Define to 1 if you have the <syscall.h> header file. */
#undef HAVE_SYSCALL_H

/* Define to 1 if you have the <syslog.h> header file. */
#undef HAVE_SYSLOG_H

/* Define to 1 if you have the <sys/stat.h> header file. */
#undef HAVE_SYS_STAT_H

/* Define to 1 if you have the <sys/syscall.h> header file. */
#undef HAVE_SYS_SYSCALL_H

/* Define to 1 if you have the <sys/time.h> header file. */
#undef HAVE_SYS_TIME_H

/* Define to 1 if you have the <sys/types.h> header file. */
#undef HAVE_SYS_TYPES_H

/* Define to 1 if you have the <sys/ucontext.h> header file. */
#undef HAVE_SYS_UCONTEXT_H

/* Define to 1 if you have the <sys/utsname.h> header file. */
#undef HAVE_SYS_UTSNAME_H

/* Define to 1 if you have the <ucontext.h> header file. */
#undef HAVE_UCONTEXT_H

/* Define to 1 if you have the <unistd.h> header file. */
#undef HAVE_UNISTD_H

/* define if the compiler supports using expression for operator */
#undef HAVE_USING_OPERATOR

/* define if your compiler has __attribute__ */
#undef HAVE___ATTRIBUTE__

/* define if your compiler has __builtin_expect */
#undef HAVE___BUILTIN_EXPECT

/* define if your compiler has __sync_val_compare_and_swap */
#undef HAVE___SYNC_VAL_COMPARE_AND_SWAP

/* Define to the sub-directory in which libtool stores uninstalled libraries.
   */
#undef LT_OBJDIR

/* Name of package */
#undef PACKAGE

/* Define to the address where bug reports for this package should be sent. */
#undef PACKAGE_BUGREPORT

/* Define to the full name of this package. */
#undef PACKAGE_NAME

/* Define to the full name and version of this package. */
#undef PACKAGE_STRING

/* Define to the one symbol short name of this package. */
#undef PACKAGE_TARNAME

/* Define to the home page for this package. */
#undef PACKAGE_URL

/* Define to the version of this package. */
#undef PACKAGE_VERSION

/* How to access the PC from a struct ucontext */
#undef PC_FROM_UCONTEXT

/* Define to necessary symbol if this constant uses a non-standard name on
   your system. */
#undef PTHREAD_CREATE_JOINABLE

/* The size of `void *', as computed by sizeof. */
#undef SIZEOF_VOID_P

/* Define to 1 if you have the ANSI C header files. */
#undef STDC_HEADERS

/* the namespace where STL code like vector<> is defined */
#undef STL_NAMESPACE

/* location of source code */
#undef TEST_SRC_DIR

/* Version number of package */
#undef VERSION

/* Stops putting the code inside the Google namespace */
#undef _END_GOOGLE_NAMESPACE_

/* Puts following code inside the Google namespace */
#undef _START_GOOGLE_NAMESPACE_
