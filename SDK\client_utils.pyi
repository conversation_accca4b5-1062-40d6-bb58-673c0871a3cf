from typing import Any, Optional, Callable, Dict

STAGE_QUEUE: str
GLOBAL_QUEUE: str
CACHE_CLEAR_QUEUE: str
SEARCH_QUEUE: str
PHONE_BOOK_QUEUE: str
THEME_QUEUE: str
EXTERNAL_NETWORK_QUEUE: str
PLUGINS_QUEUE: str

def get_queue_by_name(queue_name: str) -> Optional[Any]: ...
def run_on_queue(fn: Callable[..., Any], queue_name: str = PLUGINS_QUEUE, delay: int = 0) -> None: ...

class RequestCallback(object):
    def __init__(self, fn: Callable[[Any, Any], None]) -> None: ...
    def run(self, response: Any, error: Any) -> None: ...

def send_request(request: Any, fn: Callable[[Any, Any], None]) -> int: ...
def get_last_fragment() -> Any: ...
def get_account_instance() -> Any: ...
def get_messages_controller() -> Any: ...
def get_contacts_controller() -> Any: ...
def get_media_data_controller() -> Any: ...
def get_connections_manager() -> Any: ...
def get_location_controller() -> Any: ...
def get_notifications_controller() -> Any: ...
def get_messages_storage() -> Any: ...
def get_send_messages_helper() -> Any: ...
def get_file_loader() -> Any: ...
def get_secret_chat_helper() -> Any: ...
def get_download_controller() -> Any: ...
def get_notifications_settings() -> Any: ...
def get_notification_center() -> Any: ...
def get_media_controller() -> Any: ...
def get_user_config() -> Any: ...
def send_message(params: Dict[str, Any]) -> None: ...

class NotificationCenterDelegate(object):
    def __init__(self) -> None: ...
    def didReceivedNotification(self, id: int, account: int, args: Any) -> None: ...