import re
import traceback
import unicodedata

from android_utils import log
from base_plugin import HookResult, HookStrategy, BasePlugin
from ui.bulletin import BulletinHelper
import ui.settings

import java
from java.util import Locale

__id__ = "textfixer"
__name__ = "Text Fixer"
__description__ = "Plugin for text correction (dots, capital letters at the beginning of a sentence) in your messages"
__icon__ = "etgPlugins/2"
__version__ = "1.0.3 (20250604)"
__author__ = "@etgPlugins"
__min_version__ = "11.9.1"


class TextFixer(BasePlugin):
    raw_strings = {
        "en": {
            "settings": {
                "fixing_settings": "Fixing settings",
                "ignoring_settings": "Chats to ignoring",
                "add_dots": {
                    "desc": "Dots",
                    "full_desc": "Add dots at the end of a message"
                },
                "fix_text": {
                    "desc": "Capital Letters",
                    "full_desc": "Capitalize the letters at the beginning of the message and after the necessary punctuation marks"
                },
                "ignore_chats": {
                    "desc": "Ignore Chats",
                    "full_desc": "Chats in which the plugin will be ignored (separated by commas)"
                }
            }
        },
        "ru": {
            "settings": {
                "fixing_settings": "Настройки исправления",
                "ignoring_settings": "Чаты для игнорирования",
                "add_dots": {
                    "desc": "Точки",
                    "full_desc": "Ставить точки в конце сообщения"
                },
                "fix_text": {
                    "desc": "Заглавные буквы",
                    "full_desc": "Делать заглавные буквы в начале сообщения и после необходимых знаков препинания"
                },
                "ignore_chats": {
                    "desc": "Игнор-чаты",
                    "full_desc": "Чаты, в которых работа плагина будет игнорироваться (через запятую)"
                }
            }
        }
    }


    def __init__(self):
        super().__init__()

    def on_plugin_load(self):
        self.add_on_send_message_hook(100)
        log(f"{__name__} plugin loaded successfully") 

    def on_plugin_unload(self):
        log(f"{__name__} plugin unloaded")


    def __add_dot(self, text: str):
        res = []
        for t in text.split("\n"):
            if (len(t) > 0) and (unicodedata.category(t[-1]).startswith('L')):
                t += "."
            res.append(t)
        return '\n'.join(res)

    def __fix(self, text: str):
        lines = text.split("\n")
        new_lines = []
        for line in lines:
            parts = re.findall(r'.*?[.!?]+(?=\s|$)|.+', line)
            fixed_parts = []
            for part in parts:
                part = part
                if part:
                    fixed_parts.append(part[0].upper() + part[1:])
            new_lines.append(' '.join(fixed_parts))
        return '\n'.join(new_lines)


    def create_settings(self):
        self.strings: dict = self.raw_strings[
            'ru' if Locale.getDefault().getLanguage().startswith("ru") else 'en'
        ]

        return [
            ui.settings.Header(text=self.strings['settings']['fixing_settings']),
            ui.settings.Switch(
                "add_dots", default=True,
                text=self.strings['settings']['add_dots']['desc'],
                subtext=self.strings['settings']['add_dots']['full_desc']
            ),
            ui.settings.Switch(
                "fix_text", default=True,
                text=self.strings['settings']['fix_text']['desc'],
                subtext=self.strings['settings']['fix_text']['full_desc']
            ),

            ui.settings.Divider(),

            ui.settings.Header(text=self.strings['settings']['ignoring_settings']),
            ui.settings.Input(
                "ignore_chats", default="",
                text=self.strings['settings']['ignore_chats']['desc'],
                subtext=self.strings['settings']['ignore_chats']['full_desc']
            ),
        ]


    def on_send_message_hook(self, account, params) -> HookStrategy:
        text = (params.message or params.caption)
        if (not text) or (
            (not self.get_setting("add_dots", True)) and (not self.get_setting("fix_text", True))
        ):
            return HookResult()
        igc = [] if not self.get_setting("ignore_chats", "") else [
            int(x) for x in self.get_setting("ignore_chats", "").split(",")
        ]
        if params.peer in igc:
            return HookResult()

        try:
            if self.get_setting("add_dots", True):
                text = self.__add_dot(text)
            if self.get_setting("fix_text", True):
                text = self.__fix(text)

            if params.message:
                params.message = text
            elif params.caption:
                params.caption = text
            else:
                return HookResult()
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
        except Exception as e:
            log(f"TextFixer plugin error:\n{traceback.format_exc()}")
            BulletinHelper.show_error(str(e))
            return HookResult(strategy=HookStrategy.CANCEL)