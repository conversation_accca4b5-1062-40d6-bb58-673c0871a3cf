<PERSON><PERSON>,100,B<PERSON><PERSON><PERSON>_TOO_<PERSON>ALL
<PERSON>,101,COMMAND_NOT_SUPPORTED
<PERSON><PERSON>,102,DECODE_ERROR
EVP,103,DIFFERENT_KEY_TYPES
EVP,104,D<PERSON><PERSON><PERSON>ENT_PARAMETERS
EVP,136,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,105,E<PERSON><PERSON>E_ERROR
EVP,106,EXPECTING_AN_EC_KEY_KEY
<PERSON>,107,EXPECTING_AN_RSA_KEY
<PERSON>,138,EXPECTING_A_DH_KEY
<PERSON>,108,EXPECTING_A_DSA_KEY
EVP,109,ILLEGAL_OR_UNSUPPORTED_PADDING_MODE
E<PERSON>,137,INVALID_BUFFER_SIZE
EVP,110,INVALID_DIGEST_LENGTH
EVP,111,INVALID_DIGEST_TYPE
EVP,112,INVALID_KEYBITS
<PERSON>VP,113,INVALID_MGF1_<PERSON>
E<PERSON>,114,INVALID_OPERATION
EVP,115,INVALID_PADDING_MOD<PERSON>,133,INVALID_PARAMETERS
EVP,134,INVALID_PEER_KEY
<PERSON>,116,INVALID_PSS_SALTLEN
EVP,131,INVALID_SIGNATURE
EVP,117,KEYS_NOT_SET
EVP,132,MEMORY_LIMIT_EXCEEDED
EVP,118,MISSING_PARAMETERS
EVP,130,NOT_A_PRIVATE_KEY
EVP,135,NOT_XOF_OR_INVALID_LENGTH
EVP,119,NO_DEFAULT_DIGEST
EVP,120,NO_KEY_SET
EVP,121,NO_MDC2_SUPPORT
EVP,122,NO_NID_FOR_CURVE
EVP,123,NO_OPERATION_SET
EVP,124,NO_PARAMETERS_SET
EVP,125,OPERATION_NOT_SUPPORTED_FOR_THIS_KEYTYPE
EVP,126,OPERATON_NOT_INITIALIZED
EVP,127,UNKNOWN_PUBLIC_KEY_TYPE
EVP,128,UNSUPPORTED_ALGORITHM
EVP,129,UNSUPPORTED_PUBLIC_KEY_TYPE
