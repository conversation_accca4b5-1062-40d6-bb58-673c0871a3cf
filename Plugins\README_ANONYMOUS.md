# Плагины анонимизации имен пользователей для Telegram

Коллекция плагинов для замены всех имен пользователей на "Анонимный режим" в Telegram.

## 📋 Список плагинов

### 1. `anonymous_final_working.plugin` 🎯 **РЕКОМЕНДУЕТСЯ**
- **Описание**: Финальная рабочая версия, протестированная на реальных данных
- **Подход**: Проверенные хуки, которые находят реальных пользователей
- **Особенности**: Статистика, фильтрация ботов, 122+ найденных имени в тестах
- **Статус**: ✅ **РАБОТАЕТ** - протестировано и подтверждено

### 2. `anonymous_smart.plugin` 🧠 **ПРОДВИНУТЫЙ**
- **Описание**: Умный плагин, анонимизирует только реальных пользователей
- **Подход**: Интеллектуальное определение типа пользователя
- **Особенности**: Статистика, кэширование, фильтрация ботов и системных аккаунтов
- **Статус**: ✅ Самый продвинутый и точный

### 2. `anonymous_universal.plugin` ⭐ **СТАБИЛЬНЫЙ**
- **Описание**: Универсальный плагин с правильной обработкой Java объектов
- **Подход**: API хуки с безопасной итерацией ArrayList
- **Особенности**: Исправлены все проблемы с Java ArrayList
- **Статус**: ✅ Полностью исправлен и протестирован

### 2. `simple_anonymous.plugin` ✅ **ИСПРАВЛЕН**
- **Описание**: Максимально простой и надежный плагин
- **Подход**: API хуки (перехват ответов сервера)
- **Особенности**: Минимум кода, максимум надежности
- **Статус**: ✅ Исправлены проблемы с ArrayList

### 3. `anonymous_updates.plugin` 🐛 **С ОТЛАДКОЙ**
- **Описание**: Плагин с подробным логированием
- **Подход**: API хуки с режимом отладки
- **Особенности**: Показывает подробные логи работы
- **Статус**: ✅ Исправлены ошибки с ArrayList

### 3. `anonymous_names.plugin` 🎯 **ПРЯМОЙ ХУК**
- **Описание**: Хукает метод `UserObject.getUserName()` напрямую
- **Подход**: Прямой хук методов форматирования
- **Особенности**: Перехватывает на уровне методов
- **Статус**: ⚠️ Требует тестирования

### 4. `anonymous_hook_alt.plugin` 🔄 **АЛЬТЕРНАТИВНЫЙ**
- **Описание**: Альтернативный способ хукинга с fallback
- **Подход**: Комбинированный (методы + API)
- **Особенности**: Автоматически переключается между подходами
- **Статус**: 🧪 Экспериментальный

### 5. `anonymous_mode_final.plugin` 🚀 **ПОЛНОФУНКЦИОНАЛЬНЫЙ**
- **Описание**: Самый продвинутый плагин с множеством функций
- **Подход**: Комбинированный подход
- **Особенности**: Кэширование, сохранение первой буквы, анонимизация чатов
- **Статус**: ✅ Полностью функциональный

## 🎯 Рекомендации по использованию

### Для всех пользователей:
**Используйте `anonymous_final_working.plugin`** 🎯 **ЛУЧШИЙ ВЫБОР**
- ✅ **Протестировано** - находит 122+ реальных имени пользователей
- ✅ **Работает** - подтверждено на реальных данных
- ✅ **Простой** - минимум настроек, максимум результата
- ✅ **Статистика** - показывает сколько имен найдено и заменено

### Для продвинутых пользователей:
**Используйте `anonymous_smart.plugin`** 🧠
- Умно определяет реальных пользователей
- Не трогает ботов и системные аккаунты
- Показывает статистику работы
- Максимально точная анонимизация

### Для стабильности:
**Используйте `anonymous_universal.plugin`** ⭐
- Исправлены все проблемы с Java ArrayList
- Безопасная обработка всех типов данных
- Режим отладки для диагностики
- Простой в настройке

### Для отладки проблем:
**Используйте `anonymous_updates.plugin`**
- Включите "Режим отладки" в настройках
- Смотрите логи в консоли
- Помогает понять, что происходит

### Для продвинутых пользователей:
**Используйте `anonymous_mode_final.plugin`**
- Максимум функций
- Гибкие настройки
- Лучшее покрытие всех случаев

## ⚙️ Общие настройки

Все плагины поддерживают:
- ✅ **Включение/выключение** анонимизации
- ✅ **Настройка текста** для замены имен
- ✅ **Мгновенное применение** изменений

Дополнительные настройки в продвинутых версиях:
- 🔤 **Сохранение первой буквы** имени
- 💬 **Анонимизация названий чатов**
- 🐛 **Режим отладки**
- 🗑️ **Очистка кэша**

## 🔧 Принцип работы

### API хуки (рекомендуется):
1. Перехватывают ответы от сервера Telegram
2. Заменяют имена пользователей в данных
3. Возвращают модифицированные данные в приложение

### Хуки методов:
1. Перехватывают вызовы методов форматирования имен
2. Заменяют результат на анонимный текст
3. Работают на уровне кода приложения

## 🚀 Быстрый старт

1. **Установите** `anonymous_final_working.plugin` 🎯 (**ЛУЧШИЙ ВЫБОР**)
2. **Включите** плагин в настройках (по умолчанию уже включен)
3. **Настройте** анонимный текст (по умолчанию "Анонимный пользователь")
4. **Нажмите "Показать статистику"** чтобы увидеть результаты
5. **Готово!** Плагин уже работает и анонимизирует имена

### 📊 Что вы увидите:
- **Найдено уникальных имен**: количество обнаруженных пользователей
- **Анонимизировано**: количество замененных имен
- **Примеры найденных имен**: список реальных имен, которые были найдены

## 🐛 Решение проблем

### Плагин не работает:
1. Проверьте, что плагин включен
2. Перезапустите Telegram
3. Попробуйте другой плагин из списка
4. Включите режим отладки в `anonymous_updates.plugin`

### Имена не заменяются:
1. Убедитесь, что анонимный текст не пустой
2. Проверьте логи плагина
3. Попробуйте `anonymous_mode_final.plugin` для лучшего покрытия

### Ошибки в логах:
1. Обновите Telegram до последней версии
2. Проверьте совместимость с версией плагина
3. Используйте `simple_anonymous.plugin` как наиболее стабильный

## 📝 Примечания

- Все плагины работают **локально** и не отправляют данные никуда
- Анонимизация применяется только к **отображению** имен
- **Оригинальные данные** остаются неизменными на сервере
- Плагины **не влияют** на функциональность Telegram

---

**Автор**: @exteraDev  
**Версия документации**: 1.0.0  
**Дата**: 2025-01-19