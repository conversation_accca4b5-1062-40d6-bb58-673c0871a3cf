from base_plugin import <PERSON><PERSON><PERSON><PERSON>, HookStrategy, BasePlugin
from java.util import Locale
import re

__id__ = "dotdeleter"
__name__ = "Dot Deleter"
__description__ = "Removes dots at the end of messages automatically"
__author__ = "@misha<PERSON><PERSON> & @mi<PERSON><PERSON><PERSON><PERSON><PERSON>"
__min_version__ = "11.9.0"
__icon__ = "HotCherry/8"
__version__ = "1.0"

class LocalizationManager:
    def __init__(self):
        self.language = Locale.getDefault().getLanguage()
        self.language = self.language if self.language in self._get_supported_languages() else "en"
        
    def get_string(self, string):
        return self.strings[self.language][string]

    def _get_supported_languages(self):
        return self.strings.keys()
    
    strings = {
        "ru": {
            "ENABLED": "✅ Удаление точек активировано",
            "DISABLED": "❌ Удаление точек деактивировано"
        },
        "en": {
            "ENABLED": "✅ Dot removal enabled",
            "DISABLED": "❌ Dot removal disabled"
        }
    }
    
locali = LocalizationManager()

class DotDeleterPlugin(BasePlugin):
    def create_settings(self):
        from ui.settings import Header, Divider, Switch
        
        lang = Locale.getDefault().getLanguage()
        if lang.startswith("ru"):
            header = "Настройки удаления точек"
            enabled_text = "Включить удаление точек"
            enabled_subtext = "Автоматически удаляет точки в конце сообщений"
            info_text = "Удаляет точки в конце каждого сообщения"
        else:
            header = "Dot Deletion Settings"
            enabled_text = "Enable dot deletion"
            enabled_subtext = "Automatically removes dots at the end of messages"
            info_text = "Removes dots at the end of each message"
            
        settings = [
            Header(text=header),
            Switch(key="enabled", text=enabled_text, icon="msg_delete", default=True, subtext=enabled_subtext),
            Divider(text=info_text)
        ]
        
        return settings
        
    def on_plugin_load(self):
        self.add_on_send_message_hook()
    
    def on_send_message_hook(self, account, params) -> HookResult:
        if not self.get_setting("enabled", True):
            return HookResult()
            
        if not hasattr(params, "message") or not isinstance(params.message, str):
            return HookResult()
            
        message = params.message
        
        if message.endswith("."):
            params.message = message.rstrip(".")
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
            
        return HookResult()
