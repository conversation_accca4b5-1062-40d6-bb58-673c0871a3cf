// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     java/lang/Double

#ifndef java_lang_Double_JNI
#define java_lang_Double_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_java_lang_Double[];
const char kClassPath_java_lang_Double[] = "java/lang/Double";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_java_lang_Double_clazz(nullptr);
#ifndef java_lang_Double_clazz_defined
#define java_lang_Double_clazz_defined
inline jclass java_lang_Double_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_java_lang_Double, &g_java_lang_Double_clazz);
}
#endif


// Step 2: Constants (optional).

namespace JNI_Double {

enum Java_Double_constant_fields {
  BYTES = 8,
  MAX_EXPONENT = 1023,
  SIZE = 64,
};


}  // namespace JNI_Double
// Step 3: Method stubs.
namespace JNI_Double {


static std::atomic<jmethodID> g_java_lang_Double_Constructor__String1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_Double_Constructor__String(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Double_Constructor__String(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Ljava/lang/String;)V",
          &g_java_lang_Double_Constructor__String1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Double_Constructor__double1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_Double_Constructor__double(JNIEnv* env, jdouble p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Double_Constructor__double(JNIEnv* env, jdouble
    p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(D)V",
          &g_java_lang_Double_Constructor__double1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, p0);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Double_byteValue0(nullptr);
[[maybe_unused]] static jbyte Java_Double_byteValue(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jbyte Java_Double_byteValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "byteValue",
          "()B",
          &g_java_lang_Double_byteValue0);

  jbyte ret =
      env->CallByteMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_compare2(nullptr);
[[maybe_unused]] static jint Java_Double_compare(JNIEnv* env, jdouble p0,
    jdouble p1);
static jint Java_Double_compare(JNIEnv* env, jdouble p0,
    jdouble p1) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "compare",
          "(DD)I",
          &g_java_lang_Double_compare2);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0, p1);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_compareTo__Double1(nullptr);
[[maybe_unused]] static jint Java_Double_compareTo__Double(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jint Java_Double_compareTo__Double(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "compareTo",
          "(Ljava/lang/Double;)I",
          &g_java_lang_Double_compareTo__Double1);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_compareTo__Object1(nullptr);
[[maybe_unused]] static jint Java_Double_compareTo__Object(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jint Java_Double_compareTo__Object(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "compareTo",
          "(Ljava/lang/Object;)I",
          &g_java_lang_Double_compareTo__Object1);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_doubleToLongBits1(nullptr);
[[maybe_unused]] static jlong Java_Double_doubleToLongBits(JNIEnv* env, jdouble p0);
static jlong Java_Double_doubleToLongBits(JNIEnv* env, jdouble p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "doubleToLongBits",
          "(D)J",
          &g_java_lang_Double_doubleToLongBits1);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_doubleToRawLongBits1(nullptr);
[[maybe_unused]] static jlong Java_Double_doubleToRawLongBits(JNIEnv* env, jdouble p0);
static jlong Java_Double_doubleToRawLongBits(JNIEnv* env, jdouble p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "doubleToRawLongBits",
          "(D)J",
          &g_java_lang_Double_doubleToRawLongBits1);

  jlong ret =
      env->CallStaticLongMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_doubleValue0(nullptr);
[[maybe_unused]] static jdouble Java_Double_doubleValue(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jdouble Java_Double_doubleValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "doubleValue",
          "()D",
          &g_java_lang_Double_doubleValue0);

  jdouble ret =
      env->CallDoubleMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_equals1(nullptr);
[[maybe_unused]] static jboolean Java_Double_equals(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_Double_equals(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Double_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "equals",
          "(Ljava/lang/Object;)Z",
          &g_java_lang_Double_equals1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_floatValue0(nullptr);
[[maybe_unused]] static jfloat Java_Double_floatValue(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jfloat Java_Double_floatValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "floatValue",
          "()F",
          &g_java_lang_Double_floatValue0);

  jfloat ret =
      env->CallFloatMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_hashCode0(nullptr);
[[maybe_unused]] static jint Java_Double_hashCode(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jint Java_Double_hashCode(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "hashCode",
          "()I",
          &g_java_lang_Double_hashCode0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_hashCode1(nullptr);
[[maybe_unused]] static jint Java_Double_hashCode(JNIEnv* env, jdouble p0);
static jint Java_Double_hashCode(JNIEnv* env, jdouble p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "hashCode",
          "(D)I",
          &g_java_lang_Double_hashCode1);

  jint ret =
      env->CallStaticIntMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_intValue0(nullptr);
[[maybe_unused]] static jint Java_Double_intValue(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jint Java_Double_intValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "intValue",
          "()I",
          &g_java_lang_Double_intValue0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_isFinite1(nullptr);
[[maybe_unused]] static jboolean Java_Double_isFinite(JNIEnv* env, jdouble p0);
static jboolean Java_Double_isFinite(JNIEnv* env, jdouble p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "isFinite",
          "(D)Z",
          &g_java_lang_Double_isFinite1);

  jboolean ret =
      env->CallStaticBooleanMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_isInfinite0(nullptr);
[[maybe_unused]] static jboolean Java_Double_isInfinite(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj);
static jboolean Java_Double_isInfinite(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Double_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "isInfinite",
          "()Z",
          &g_java_lang_Double_isInfinite0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_isInfinite1(nullptr);
[[maybe_unused]] static jboolean Java_Double_isInfinite(JNIEnv* env, jdouble p0);
static jboolean Java_Double_isInfinite(JNIEnv* env, jdouble p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "isInfinite",
          "(D)Z",
          &g_java_lang_Double_isInfinite1);

  jboolean ret =
      env->CallStaticBooleanMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_isNaN0(nullptr);
[[maybe_unused]] static jboolean Java_Double_isNaN(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jboolean Java_Double_isNaN(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Double_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "isNaN",
          "()Z",
          &g_java_lang_Double_isNaN0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_isNaN1(nullptr);
[[maybe_unused]] static jboolean Java_Double_isNaN(JNIEnv* env, jdouble p0);
static jboolean Java_Double_isNaN(JNIEnv* env, jdouble p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "isNaN",
          "(D)Z",
          &g_java_lang_Double_isNaN1);

  jboolean ret =
      env->CallStaticBooleanMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_longBitsToDouble1(nullptr);
[[maybe_unused]] static jdouble Java_Double_longBitsToDouble(JNIEnv* env, jlong p0);
static jdouble Java_Double_longBitsToDouble(JNIEnv* env, jlong p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "longBitsToDouble",
          "(J)D",
          &g_java_lang_Double_longBitsToDouble1);

  jdouble ret =
      env->CallStaticDoubleMethod(clazz,
          call_context.base.method_id, p0);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_longValue0(nullptr);
[[maybe_unused]] static jlong Java_Double_longValue(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jlong Java_Double_longValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "longValue",
          "()J",
          &g_java_lang_Double_longValue0);

  jlong ret =
      env->CallLongMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_max2(nullptr);
[[maybe_unused]] static jdouble Java_Double_max(JNIEnv* env, jdouble p0,
    jdouble p1);
static jdouble Java_Double_max(JNIEnv* env, jdouble p0,
    jdouble p1) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "max",
          "(DD)D",
          &g_java_lang_Double_max2);

  jdouble ret =
      env->CallStaticDoubleMethod(clazz,
          call_context.base.method_id, p0, p1);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_min2(nullptr);
[[maybe_unused]] static jdouble Java_Double_min(JNIEnv* env, jdouble p0,
    jdouble p1);
static jdouble Java_Double_min(JNIEnv* env, jdouble p0,
    jdouble p1) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "min",
          "(DD)D",
          &g_java_lang_Double_min2);

  jdouble ret =
      env->CallStaticDoubleMethod(clazz,
          call_context.base.method_id, p0, p1);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_parseDouble1(nullptr);
[[maybe_unused]] static jdouble Java_Double_parseDouble(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0);
static jdouble Java_Double_parseDouble(JNIEnv* env, const jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "parseDouble",
          "(Ljava/lang/String;)D",
          &g_java_lang_Double_parseDouble1);

  jdouble ret =
      env->CallStaticDoubleMethod(clazz,
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_shortValue0(nullptr);
[[maybe_unused]] static jshort Java_Double_shortValue(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static jshort Java_Double_shortValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "shortValue",
          "()S",
          &g_java_lang_Double_shortValue0);

  jshort ret =
      env->CallShortMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_sum2(nullptr);
[[maybe_unused]] static jdouble Java_Double_sum(JNIEnv* env, jdouble p0,
    jdouble p1);
static jdouble Java_Double_sum(JNIEnv* env, jdouble p0,
    jdouble p1) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "sum",
          "(DD)D",
          &g_java_lang_Double_sum2);

  jdouble ret =
      env->CallStaticDoubleMethod(clazz,
          call_context.base.method_id, p0, p1);
  return ret;
}

static std::atomic<jmethodID> g_java_lang_Double_toHexString1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Double_toHexString(JNIEnv* env,
    jdouble p0);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Double_toHexString(JNIEnv* env, jdouble p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toHexString",
          "(D)Ljava/lang/String;",
          &g_java_lang_Double_toHexString1);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Double_toString0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Double_toString(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Double_toString(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_lang_Double_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "toString",
          "()Ljava/lang/String;",
          &g_java_lang_Double_toString0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Double_toString1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jstring> Java_Double_toString(JNIEnv* env,
    jdouble p0);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Double_toString(JNIEnv* env, jdouble p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "toString",
          "(D)Ljava/lang/String;",
          &g_java_lang_Double_toString1);

  jstring ret =
      static_cast<jstring>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Double_valueOf__double1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Double_valueOf__double(JNIEnv*
    env, jdouble p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Double_valueOf__double(JNIEnv* env, jdouble p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "valueOf",
          "(D)Ljava/lang/Double;",
          &g_java_lang_Double_valueOf__double1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_lang_Double_valueOf__String1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_Double_valueOf__String(JNIEnv*
    env, const jni_zero::JavaRef<jstring>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Double_valueOf__String(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& p0) {
  jclass clazz = java_lang_Double_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_lang_Double_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "valueOf",
          "(Ljava/lang/String;)Ljava/lang/Double;",
          &g_java_lang_Double_valueOf__String1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace JNI_Double

#endif  // java_lang_Double_JNI
