# Negation tests.
#
# The following tests satisfy A = -B (mod P).

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000000000
B = 0000000000000000000000000000000000000000000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000000001
B = ffffffff00000001000000000000000000000000fffffffffffffffffffffffe

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000000003
B = ffffffff00000001000000000000000000000000fffffffffffffffffffffffc

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000000007
B = ffffffff00000001000000000000000000000000fffffffffffffffffffffff8

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000000000f
B = ffffffff00000001000000000000000000000000fffffffffffffffffffffff0

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000000001f
B = ffffffff00000001000000000000000000000000ffffffffffffffffffffffe0

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000000003f
B = ffffffff00000001000000000000000000000000ffffffffffffffffffffffc0

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000000007f
B = ffffffff00000001000000000000000000000000ffffffffffffffffffffff80

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000000000ff
B = ffffffff00000001000000000000000000000000ffffffffffffffffffffff00

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000000001ff
B = ffffffff00000001000000000000000000000000fffffffffffffffffffffe00

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000000003ff
B = ffffffff00000001000000000000000000000000fffffffffffffffffffffc00

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000000007ff
B = ffffffff00000001000000000000000000000000fffffffffffffffffffff800

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000000fff
B = ffffffff00000001000000000000000000000000fffffffffffffffffffff000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000001fff
B = ffffffff00000001000000000000000000000000ffffffffffffffffffffe000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000003fff
B = ffffffff00000001000000000000000000000000ffffffffffffffffffffc000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000007fff
B = ffffffff00000001000000000000000000000000ffffffffffffffffffff8000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000000ffff
B = ffffffff00000001000000000000000000000000ffffffffffffffffffff0000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000001ffff
B = ffffffff00000001000000000000000000000000fffffffffffffffffffe0000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000003ffff
B = ffffffff00000001000000000000000000000000fffffffffffffffffffc0000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000007ffff
B = ffffffff00000001000000000000000000000000fffffffffffffffffff80000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000000fffff
B = ffffffff00000001000000000000000000000000fffffffffffffffffff00000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000001fffff
B = ffffffff00000001000000000000000000000000ffffffffffffffffffe00000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000003fffff
B = ffffffff00000001000000000000000000000000ffffffffffffffffffc00000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000007fffff
B = ffffffff00000001000000000000000000000000ffffffffffffffffff800000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000000ffffff
B = ffffffff00000001000000000000000000000000ffffffffffffffffff000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000001ffffff
B = ffffffff00000001000000000000000000000000fffffffffffffffffe000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000003ffffff
B = ffffffff00000001000000000000000000000000fffffffffffffffffc000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000007ffffff
B = ffffffff00000001000000000000000000000000fffffffffffffffff8000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000000fffffff
B = ffffffff00000001000000000000000000000000fffffffffffffffff0000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000001fffffff
B = ffffffff00000001000000000000000000000000ffffffffffffffffe0000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000003fffffff
B = ffffffff00000001000000000000000000000000ffffffffffffffffc0000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000007fffffff
B = ffffffff00000001000000000000000000000000ffffffffffffffff80000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000000ffffffff
B = ffffffff00000001000000000000000000000000ffffffffffffffff00000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000001ffffffff
B = ffffffff00000001000000000000000000000000fffffffffffffffe00000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000003ffffffff
B = ffffffff00000001000000000000000000000000fffffffffffffffc00000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000007ffffffff
B = ffffffff00000001000000000000000000000000fffffffffffffff800000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000000fffffffff
B = ffffffff00000001000000000000000000000000fffffffffffffff000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000001fffffffff
B = ffffffff00000001000000000000000000000000ffffffffffffffe000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000003fffffffff
B = ffffffff00000001000000000000000000000000ffffffffffffffc000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000007fffffffff
B = ffffffff00000001000000000000000000000000ffffffffffffff8000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000000ffffffffff
B = ffffffff00000001000000000000000000000000ffffffffffffff0000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000001ffffffffff
B = ffffffff00000001000000000000000000000000fffffffffffffe0000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000003ffffffffff
B = ffffffff00000001000000000000000000000000fffffffffffffc0000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000007ffffffffff
B = ffffffff00000001000000000000000000000000fffffffffffff80000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000000fffffffffff
B = ffffffff00000001000000000000000000000000fffffffffffff00000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000001fffffffffff
B = ffffffff00000001000000000000000000000000ffffffffffffe00000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000003fffffffffff
B = ffffffff00000001000000000000000000000000ffffffffffffc00000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000007fffffffffff
B = ffffffff00000001000000000000000000000000ffffffffffff800000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000000ffffffffffff
B = ffffffff00000001000000000000000000000000ffffffffffff000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000001ffffffffffff
B = ffffffff00000001000000000000000000000000fffffffffffe000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000003ffffffffffff
B = ffffffff00000001000000000000000000000000fffffffffffc000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000007ffffffffffff
B = ffffffff00000001000000000000000000000000fffffffffff8000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000000fffffffffffff
B = ffffffff00000001000000000000000000000000fffffffffff0000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000001fffffffffffff
B = ffffffff00000001000000000000000000000000ffffffffffe0000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000003fffffffffffff
B = ffffffff00000001000000000000000000000000ffffffffffc0000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000007fffffffffffff
B = ffffffff00000001000000000000000000000000ffffffffff80000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000000ffffffffffffff
B = ffffffff00000001000000000000000000000000ffffffffff00000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000001ffffffffffffff
B = ffffffff00000001000000000000000000000000fffffffffe00000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000003ffffffffffffff
B = ffffffff00000001000000000000000000000000fffffffffc00000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000007ffffffffffffff
B = ffffffff00000001000000000000000000000000fffffffff800000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000000fffffffffffffff
B = ffffffff00000001000000000000000000000000fffffffff000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000001fffffffffffffff
B = ffffffff00000001000000000000000000000000ffffffffe000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000003fffffffffffffff
B = ffffffff00000001000000000000000000000000ffffffffc000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000007fffffffffffffff
B = ffffffff00000001000000000000000000000000ffffffff8000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000000ffffffffffffffff
B = ffffffff00000001000000000000000000000000ffffffff0000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000001ffffffffffffffff
B = ffffffff00000001000000000000000000000000fffffffe0000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000003ffffffffffffffff
B = ffffffff00000001000000000000000000000000fffffffc0000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000007ffffffffffffffff
B = ffffffff00000001000000000000000000000000fffffff80000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000000fffffffffffffffff
B = ffffffff00000001000000000000000000000000fffffff00000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000001fffffffffffffffff
B = ffffffff00000001000000000000000000000000ffffffe00000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000003fffffffffffffffff
B = ffffffff00000001000000000000000000000000ffffffc00000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000007fffffffffffffffff
B = ffffffff00000001000000000000000000000000ffffff800000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000000ffffffffffffffffff
B = ffffffff00000001000000000000000000000000ffffff000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000001ffffffffffffffffff
B = ffffffff00000001000000000000000000000000fffffe000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000003ffffffffffffffffff
B = ffffffff00000001000000000000000000000000fffffc000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000007ffffffffffffffffff
B = ffffffff00000001000000000000000000000000fffff8000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000000fffffffffffffffffff
B = ffffffff00000001000000000000000000000000fffff0000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000001fffffffffffffffffff
B = ffffffff00000001000000000000000000000000ffffe0000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000003fffffffffffffffffff
B = ffffffff00000001000000000000000000000000ffffc0000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000007fffffffffffffffffff
B = ffffffff00000001000000000000000000000000ffff80000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000000ffffffffffffffffffff
B = ffffffff00000001000000000000000000000000ffff00000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000001ffffffffffffffffffff
B = ffffffff00000001000000000000000000000000fffe00000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000003ffffffffffffffffffff
B = ffffffff00000001000000000000000000000000fffc00000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000007ffffffffffffffffffff
B = ffffffff00000001000000000000000000000000fff800000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000000fffffffffffffffffffff
B = ffffffff00000001000000000000000000000000fff000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000001fffffffffffffffffffff
B = ffffffff00000001000000000000000000000000ffe000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000003fffffffffffffffffffff
B = ffffffff00000001000000000000000000000000ffc000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000007fffffffffffffffffffff
B = ffffffff00000001000000000000000000000000ff8000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000000ffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000ff0000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000001ffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000fe0000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000003ffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000fc0000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000007ffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000f80000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000000fffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000f00000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000001fffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000e00000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000003fffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000c00000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000007fffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000800000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000000ffffffffffffffffffffffff
B = ffffffff00000001000000000000000000000000000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000001ffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffffffff000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000003ffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffffffd000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000007ffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffffff9000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000000fffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffffff1000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000001fffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffffffe1000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000003fffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffffffc1000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000007fffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffffff81000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000000ffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffffff01000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000001ffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffffe01000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000003ffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffffc01000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000007ffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffff801000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000000fffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffff001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000001fffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffffe001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000003fffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffffc001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000007fffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffff8001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000000ffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffff0001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000001ffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffe0001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000003ffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffffc0001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000007ffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffff80001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000000fffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffff00001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000001fffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffe00001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000003fffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffffc00001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000007fffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffff800001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000000ffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffff000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000001ffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffe000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000003ffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffffc000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000007ffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffff8000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000000fffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffff0000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000001fffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffe0000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000003fffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffffc0000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000007fffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffff80000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000000ffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffff00000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000001ffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffe00000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000003ffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffffc00000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000007ffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffff800000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000000fffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffff000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000001fffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffe000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000003fffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffffc000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000007fffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffff8000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000000ffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffff0000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000001ffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffe0000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000003ffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffffc0000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000007ffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffff80000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000000fffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffff00000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000001fffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffe00000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000003fffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffffc00000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000007fffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffff800000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000000ffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffff000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000001ffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffe000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000003ffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffffc000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000007ffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffff8000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000000fffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffff0000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000001fffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffe0000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000003fffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffffc0000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000007fffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffff80000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000000ffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffff00000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000001ffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffe00000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000003ffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffffc00000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000007ffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffff800000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000000fffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffff000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000001fffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffe000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000003fffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffffc000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000007fffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffff8000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000000ffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffff0000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000001ffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffe0000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000003ffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffffc0000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000007ffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffff80000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000000fffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffff00000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000001fffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffe00000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000003fffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffffc00000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000007fffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffff800000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000000ffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffff000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000001ffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffe000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000003ffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffffc000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000007ffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffff8000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000000fffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffff0000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000001fffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffe0000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000003fffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffffc0000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000007fffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffff80000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000000ffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffff00000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000001ffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffe00000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000003ffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fffc00000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000007ffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fff800000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000000fffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fff000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000001fffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffe000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000003fffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ffc000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000007fffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ff8000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000000ffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000ff0000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000001ffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fe0000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000003ffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000fc0000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000007ffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000f80000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000000fffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000f00000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000001fffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000e00000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000003fffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000c00000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000007fffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000800000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000000ffffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffff00000000000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000001ffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffffffff000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000003ffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffffffd000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000007ffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffffff9000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000000fffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffffff1000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000001fffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffffffe1000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000003fffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffffffc1000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000007fffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffffff81000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000000ffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffffff01000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000001ffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffffe01000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000003ffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffffc01000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000007ffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffff801000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000000fffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffff001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000001fffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffffe001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000003fffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffffc001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000007fffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffff8001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000000ffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffff0001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000001ffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffe0001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000003ffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefffc0001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000007ffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefff80001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000000fffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefff00001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000001fffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffe00001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000003fffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeffc00001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000007fffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeff800001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000000ffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffeff000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000001ffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefe000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000003ffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffefc000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000007ffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffef8000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000000fffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffef0000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000001fffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffee0000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000003fffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffec0000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000007fffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffe80000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffe00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000001ffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffd00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000003ffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffffb00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000007ffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffff700000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000000fffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffef00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000001fffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffdf00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000003fffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffffffbf00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000007fffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffffff7f00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffeff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000001ffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffdff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000003ffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffffbff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000007ffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffff7ff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00000fffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffffefff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00001fffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffffdfff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00003fffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffffbfff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00007fffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffff7fff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffeffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0001ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffdffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0003ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fffbffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0007ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fff7ffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 000fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffefffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 001fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffdfffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 003fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ffbfffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 007fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = ff7fffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 00ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = feffffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 01ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fdffffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 03ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = fbffffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 07ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = f7ffffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 0fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = efffffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 1fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = dfffffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 3fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = bfffffff00000001000000000000000000000001000000000000000000000000

Test = Negate
A = 7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff
B = 7fffffff00000001000000000000000000000001000000000000000000000000


# Montgomery multiplication tests.
#
# The following tests satisfy A * B * 2^-256 = Result (mod P).

Test = MulMont
A = e762f095431b732ce33c4f4a6f41068ff7f78e37aad940166667d193bfc58039
B = a43df383dd5df14d7c16737b781261473f9ffb76ee29562fbb5e5d390b882fb5
Result = cf637a47dc5fb82aed80ed4c66b682a94bf0b76a2878acf483aad86c0db7cc19

Test = MulMont
A = 2e519e860cb3f8f32fc351861b022e9fc7bb073ca8767efb3d1027dd32a38bcb
B = 466d035e4238d6a30613dd227b0daeacd6a8634fa60f5150d42dd20601794be4
Result = 486e1abe0f79e107f8beca6e4653872f63a24dedb005def6aae75a2a51e73c76

Test = MulMont
A = 1763859541b5e2edee019c66699d0e12e349e6ee586d618ac20c679d2fa8cadd
B = 56125872de888c5656dec17fbf9678d915ff9815da897df81f03fd9aa4f93654
Result = 71ce584135a0aba8222ca0021bcefac5811d19100627f96726cf195ff2ac4aad

Test = MulMont
A = ea3be6295098e4995b93267dbd58f24fd9c18f7e89e9e5aeafdc34ca54e8ec4e
B = 2735658978d620a4f76bffe94d9cd7d683b3bfd533aa6bb2b94f52122e83f7fc
Result = 362f7ab3a12effe9bad069b84a7df5f108503c2457f83ddb05b57f19e6457989

Test = MulMont
A = f607f087ec5015b533df8802771dc60ef1487d86ce405e5bb18f8f06ca483f13
B = 73ac532eb3f2356a96e668f167a1626a0f7b1fd2cd84ba6deeebd01af1d3897d
Result = ce7045e69da157e62fb42508880f5734531c36948c704aedec42afa75cb9c2eb

Test = MulMont
A = 80ce8eb07601fd8e19ba08a9d21081b0324fd459f9c489ac7c871d406133c813
B = 7ad28cef45b137ecc5426a44b6bce6d4329f5bd2b5e55d46edd5fbb295678a1b
Result = 04068f8461d17b34c8d9c3eecf61dbaef9cd5a952bbcd9f84bb2044f2439da60

Test = MulMont
A = 17429caf63689e143c8ca77df69a11cbc02c272daadd75a66f3fa5f88828367e
B = 5725bedc56a4b16e0f0ae55fa0beb1fdf3ff132ccb9803bab678d4ac7915d88c
Result = a1da0fa68947e906287ea164b213bc7e80649b2ac3f97f203549d3b270de05a1

Test = MulMont
A = e7da43c0e0fa7adeb972901bef3160c848e9651bfc810968afdb0cd598830370
B = 08f03060cac1d3c15eea69623d5fb01da465b209e3e5e90fbb51053a1c5700eb
Result = cda4ffaf8b1c3ac0d44bae6ea5154de11e14931747a65396531302c0cb1ed537

Test = MulMont
A = c7375c2b6666713cb33cfb741268fd3ccf703bcaa0b9b27f84a8cb970655da9c
B = b0796ee4bb88b9bad895d9c25c34f43a3941e9585bda8e86ff4fa0bbb391ac61
Result = fd1d557a9fb0031e462121bf7ca31804acfcfce822bb6ee6631b54c575380617

Test = MulMont
A = 72a87b13eb4a2e248214aa591c586df65790f9f750a1641b47581a4ee09be7e9
B = 38e602844b9aaf737e8b1261110b86ba22806ccbbbfdc5305075429d7ce4f002
Result = cb2d63ee829de8801759f0229d4c07139bacd804f0c815d35004747c65bffdf2

# Test cases where A == B to test squaring.

Test = MulMont
A = 0000000000000000000000000000000000000000000000000000000000000000
B = 0000000000000000000000000000000000000000000000000000000000000000
Result = 0000000000000000000000000000000000000000000000000000000000000000

Test = MulMont
A = 579e9ce1ad00639b8b64d49546ff4f9c30ad12eaebe9e2ed91e97d55c3c5d847
B = 579e9ce1ad00639b8b64d49546ff4f9c30ad12eaebe9e2ed91e97d55c3c5d847
Result = 10c5e60c2d480d5d53f50c24fb771fd2dec208db04624dfd05d2847ca173a9aa

Test = MulMont
A = 501947209b121bcdedce8c895ee2ba310f2e561e97998eb8f3b99d1f924f36c1
B = 501947209b121bcdedce8c895ee2ba310f2e561e97998eb8f3b99d1f924f36c1
Result = 54d6d64566619b215910f1b9e467b22ef205ca3aaad37a00fcbd906357f9c179

Test = MulMont
A = e84ab9202722498baa2c9158f40d47b1f03df4d13976b0aec916a937e99f3a89
B = e84ab9202722498baa2c9158f40d47b1f03df4d13976b0aec916a937e99f3a89
Result = 9af01fa6947a60679b6f87efe9b6fba97baf5d55a19d5e91dd5da1da10caeebf

Test = MulMont
A = add67c61d8479570f45a59e9b04974f970b0c4c6c046056fea1bdf3f0e7d3152
B = add67c61d8479570f45a59e9b04974f970b0c4c6c046056fea1bdf3f0e7d3152
Result = c0c68b4327e3fe7e0522167a54b25aaa6f76085ce4f6550479c89f3f1c39dd18

Test = MulMont
A = 434ef0db5640a3ea63125f815bc3cb3c92d06dbc3b5cb484e01b5247b3b4bfe5
B = 434ef0db5640a3ea63125f815bc3cb3c92d06dbc3b5cb484e01b5247b3b4bfe5
Result = b5105d16b858279247ed31362a90260978d64e0492e84bffa7a0e13ee1541544

Test = MulMont
A = b1db42aa4b259d9c6104599aff622114f10c327d02c5640b74cf1742adff332d
B = b1db42aa4b259d9c6104599aff622114f10c327d02c5640b74cf1742adff332d
Result = 0c175e7f96fc62059864c561d99a8d90978c72757ba305cd8862ed6a5fadad59

Test = MulMont
A = 7610271796be25416b652badd3119938974b20d4fc92244aea76d23b80d178f0
B = 7610271796be25416b652badd3119938974b20d4fc92244aea76d23b80d178f0
Result = 67d76e4a7c8355bb362481a76a63b365ad79767cc672b174130e833d41ca5709

Test = MulMont
A = 3480d60b0ccafca89c86f22f78380cead81310241f27a815e6fd21c2060caed8
B = 3480d60b0ccafca89c86f22f78380cead81310241f27a815e6fd21c2060caed8
Result = 68bfb2652d3bf03d17b20b2c52c68e847b0006047ba4ea81d4b85af2e0a21f72

Test = MulMont
A = 8ad6fa8bf3fe56ece1d0970636c1429ed5dfc2441c3194928a6348b69490b537
B = 8ad6fa8bf3fe56ece1d0970636c1429ed5dfc2441c3194928a6348b69490b537
Result = f5cdccf29e09928722137fb5a5ec035d7f39580838e19b892a7a972866330318

Test = MulMont
A = 71c328ce472ae74b5028b21f9d1997e0f7dbcee979a8f9fdecfa5d37d359c835
B = 71c328ce472ae74b5028b21f9d1997e0f7dbcee979a8f9fdecfa5d37d359c835
Result = c3472fafd01fc3ed93a91ab65411cb852bd5839603a02ca6cdfbadcb9ac474a0

Test = MulMont
A = 0585a3dada9bb283fd8db4fc46c106d28f95b8cf159a405891196dbb9ce0b5cf
B = 0000000000000000000000000000000000000000000000000000000000000001
Result = d198d054d25a069c40cdeeb968a5562a67c3ef659297169e4be872f234897dc0

Test = MulMont
A = 9ff49a4a3f810fd34ca6f37fb1b3c40e61bc0492227e91e41cbe06bd58ba65b8
B = 0000000000000000000000000000000000000000000000000000000000000001
Result = 326a061b2047d9ba4eddaba9b1fe253d5b2a24e268e3f8810767bef8cda07643

Test = MulMont
A = 05a69f8f646494be65affbd44d0536ca098d6f3640e80b5e48764ab78928cf58
B = 0000000000000000000000000000000000000000000000000000000000000001
Result = 5a6f9c7025d4063480c400fe6f271cf3a3d2c43f9e1ceac21a88208c28329731

Test = MulMont
A = 256481a9e52d692719330a6f1208d9eca4ddd919aee06e234cbbde77d245501b
B = 0000000000000000000000000000000000000000000000000000000000000001
Result = fe9fc86a2ff61a0c981d5e86c5472248e071e9639521c5be43947bfffc7d5858

Test = MulMont
A = 2062ef333cadefc36ced52a2ea7e4215b1fca29283baa1e3be76e321f1b213f0
B = 0000000000000000000000000000000000000000000000000000000000000001
Result = 961ce39c3bf1d699b4b61ded8a5beae6eb6185d21f1df435b079b1f6a79dc738

Test = MulMont
A = 97241c3651a8f9d2fc02730f15c3e09e48d2e645cfe927385cb81d3f454414fb
B = 0000000000000000000000000000000000000000000000000000000000000001
Result = 2114225803efe7b6c7fbb290cb946da4e78697aad5624c2d3fe9fb568460b93c

Test = MulMont
A = 1aae0ad2c8ac988e11beda32ca7257f4d4de41f4b74452fa46f0a3bafb39262a
B = 0000000000000000000000000000000000000000000000000000000000000001
Result = 77c884131c34a2c3acce8a69dc5cf55987b7999c70586a9ef3c0dfb634900296

Test = MulMont
A = 034de033e2d38cf8bec8a994414b64a2fce7c83c5d81efc3d21448225071e85d
B = 0000000000000000000000000000000000000000000000000000000000000001
Result = 984fecbde84f393133fb602777b4395c56449d2cbbd7d8ae428b2ee6f82a2956

Test = MulMont
A = d2b296c2004b2761b6781311c924cbf5ff56dcc0900ed5cd24f5dd2e07f32633
B = 0000000000000000000000000000000000000000000000000000000000000001
Result = ddcff6e031b859a814ce8f37b71c10cd5fb642af54af72deabb95adcb99307b1

Test = MulMont
A = 8f525e6af50a62fc176dec75bdf48f70ba8ab97323ba78c643ef07f6457ba070
B = 0000000000000000000000000000000000000000000000000000000000000001
Result = 8fa95d57aae2fff79045654501478f7a394b27b8b54113a25ac74662606f767c


# Point adding tests.
#
# The following tests satisfy Result = A + B. Result is in affine coordinates,
# with infinity represented as (0, 0). A and B are in Jacobian coordinates. All
# field elements are fully reduced and in the Montgomery domain.

# ∞ + ∞ = ∞.
Test = PointAdd
A.X = 0000000000000000000000000000000000000000000000000000000000000000
A.Y = 0000000000000000000000000000000000000000000000000000000000000000
A.Z = 0000000000000000000000000000000000000000000000000000000000000000
B.X = 0000000000000000000000000000000000000000000000000000000000000000
B.Y = 0000000000000000000000000000000000000000000000000000000000000000
B.Z = 0000000000000000000000000000000000000000000000000000000000000000
Result.X = 0000000000000000000000000000000000000000000000000000000000000000
Result.Y = 0000000000000000000000000000000000000000000000000000000000000000

# ∞ + ∞ = ∞, with an alternate representation of ∞.
Test = PointAdd
A.X = 33c0d6224957b40403366bcf638f29928c2b9bcc74a0bac58808b02040781420
A.Y = f9698d488fd517306a66a397e92542d435a7ee54e978c2b4782da38fcf613c6e
A.Z = 0000000000000000000000000000000000000000000000000000000000000000
B.X = 5dd08e3206a651cf2320dc98a3a173baef07fbd04bdc6eee2d79ddb13c63ac2e
B.Y = 2270fd1d5dde52ec8f1e53816c2a0fcc9836d990894106894d8f99e89edbc335
B.Z = 0000000000000000000000000000000000000000000000000000000000000000
Result.X = 0000000000000000000000000000000000000000000000000000000000000000
Result.Y = 0000000000000000000000000000000000000000000000000000000000000000

# g + ∞ = g.
Test = PointAdd
A.X = 18905f76a53755c679fb732b7762251075ba95fc5fedb60179e730d418a9143c
A.Y = 8571ff1825885d85d2e88688dd21f3258b4ab8e4ba19e45cddf25357ce95560a
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = 0000000000000000000000000000000000000000000000000000000000000000
B.Y = 0000000000000000000000000000000000000000000000000000000000000000
B.Z = 0000000000000000000000000000000000000000000000000000000000000000
Result.X = 18905f76a53755c679fb732b7762251075ba95fc5fedb60179e730d418a9143c
Result.Y = 8571ff1825885d85d2e88688dd21f3258b4ab8e4ba19e45cddf25357ce95560a

# g + ∞ = g, with an alternate representation of ∞.
Test = PointAdd
A.X = 18905f76a53755c679fb732b7762251075ba95fc5fedb60179e730d418a9143c
A.Y = 8571ff1825885d85d2e88688dd21f3258b4ab8e4ba19e45cddf25357ce95560a
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = edc21713cc3b51b9632b37925b5369a13aa3eab989f2d9a720154c1786eca076
B.Y = 8da43525eb9de2a56c5a3fd7447258d96ccb60337e474b830d1fa37c0da1da8f
B.Z = 0000000000000000000000000000000000000000000000000000000000000000
Result.X = 18905f76a53755c679fb732b7762251075ba95fc5fedb60179e730d418a9143c
Result.Y = 8571ff1825885d85d2e88688dd21f3258b4ab8e4ba19e45cddf25357ce95560a

# g + -g = ∞.
Test = PointAdd
A.X = 18905f76a53755c679fb732b7762251075ba95fc5fedb60179e730d418a9143c
A.Y = 8571ff1825885d85d2e88688dd21f3258b4ab8e4ba19e45cddf25357ce95560a
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = 18905f76a53755c679fb732b7762251075ba95fc5fedb60179e730d418a9143c
B.Y = 7a8e00e6da77a27b2d17797722de0cda74b5471c45e61ba3220daca8316aa9f5
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = 0000000000000000000000000000000000000000000000000000000000000000
Result.Y = 0000000000000000000000000000000000000000000000000000000000000000

# Test some random Jacobian sums.
Test = PointAdd
A.X = cb8dea3327057fe69b5159e0323e60486cda3400545f7e2c60559ac7c8d0d89d
A.Y = 553de89b31719830c3c3300aa8ad50ea81f40762a4f33ccf81a2d3bcc93a2d53
A.Z = 4589e40df2efc546b2572c1f45eda26fc191b8d56376f2063fd9470fb277d181
B.X = 32ad56497c6c6e8399de6814efd21b3eb949bb80dab578073cf0b0aa92054341
B.Y = 57b33b7acfeee75ef6a31eb7ca0244b375f2d0962a3ce65c06afaa02688399e0
B.Z = 337d5e1ec2fc711b12fd6c7a51a2f474a922cb107f592b657617d2e0b4f1d35f
Result.X = 120c6ddd6f8ebc798c5740005bad5a2586575202df9cc3dd07401fe84d8cfdd4
Result.Y = 966bc89126349ce41738be691f32c1a068e54a654ab2cb0eac39ef15ee17f0df

Test = PointAdd
A.X = a858b5249026ccc4d25fbd85db17826afa3c5963c26815cbf8511d84dce62a25
A.Y = bd753e125579388da968036d50067fe0e8eccb531c4d6f1a69c61bc8259c6d76
A.Z = 82f9cdd9abf991ac27f0caa494b7b7b2851cc6591c6362ef02d1bd2c33fd116c
B.X = 3e5790fd7ff28a376586c8ef5625b906f2d5d7e6656191f5a530154eecd4c988
B.Y = 6e6c91011cc5996a7db4e5539eee635ce84780a85a17778da06353048fdf6bd3
B.Z = a9ef3402e9f15e7a91aef4a53431b2b2068914e4a09ebdafc8aa654351f32331
Result.X = de9d6bb4dfdee64193d3eaebb9208a86e764b80e1459fd10a2e01c202e33c5e2
Result.Y = 370e67dbb7cfa6b79adaeec48b1535f0c329856401102d546c695d0dfe1d0db5

Test = PointAdd
A.X = a2d85e21bf520691b397ac9e0c1360218cef96a8a6f4c2b24d21791360ce4d9e
A.Y = 0cc1c5493edf586cd24f7a9f40185c1ceefa727369ed159a9fc09b700ba64f78
A.Z = ad3083a5bd23ee1fdbd3a25abdee815052209bb1a8b22d3f7d8600442b760a61
B.X = 7d8850dafe2c48d86b6c3f3f44453670aa7169712238d024dbd08cb4e95b9cc1
B.Y = 6a2698c143609306fe2c402acdf26e3b42874f5ae3ea2e95898c305e791984b8
B.Z = c81bc8988c6edabf4a03fcc456ce0c445e225c33b76a79552af0b818350ad6b0
Result.X = 67c5f8af069b5a5636647eee50da847dff8f5f6ef71780a5d1330453db5c8a04
Result.Y = cec9200fa541b602d94c694f1289d1d073e64f47054baa40a9921c20ca090643

Test = PointAdd
A.X = 4f9a035ffeddcc36846906cacc812ffae7f3110fe46bf7da12d0b19ec54c3873
A.Y = 73539ed620938543f94c358dba87319dca40ae4d13d0a888527f007d26d73d74
A.Z = 922e97056fbf12d89984346368087375560990c3fb2f337d9f46429f2022d634
B.X = de6fa333804b1da9f046896634e498d5f456288f8f03cc41fc7ba4b1e978429a
B.Y = fd45f1d5e905c448b947fd65bc2897928d6014425c8c502a1b2838ba882f5813
B.Z = 50bb4c98bce36b8aad5662b8db35428bb5c1f298e17347caa5d4f542f278a1d9
Result.X = 5c3cb05b52ec59f3cbb666b0059163afae885676cf81d64cadc943a1c0bb3a86
Result.Y = 2871d088271faa9258e60ff28115f72294b938ef3d7b927e59177f9b41d5747e

# Test some random Jacobian doublings.
Test = PointAdd
A.X = 75da62f76d1887e18a06483bb6b53c3ec42879ed73b7851ed4748e307653714c
A.Y = a6f0d0d3bb492bf488d99d549aff3f0c3a48f0c35a5931578fe697b8c5f486f7
A.Z = 6d6a50229164869f24865148a19a24d57d94ebd09dc06b5e4fc3946a95f9124f
B.X = 3b225af8c7b6155d66061c75a03d23d94e01a2167fa7f44c5bd1e9d9c48c7421
B.Y = af58b0e38531d1e08187c61a36b33693ef534ecae23dca4542667d93f1844d75
B.Z = 86ed2be859c4af1d5cf99041840f3bcb7c9b8e8986811393c96e8bf57fcad872
Result.X = ab0f931fb86a621102e67336eadcf01afe3127aeaf5b4f89e8f34628c8e1afd9
Result.Y = 52c50e2783d69dde29d6bc75fa359ffe72e7115c2fc89a9699a499cac25e3383

Test = PointAdd
A.X = f0d499f2e3775de88ed997feeb4589506f061f93766abb0c1251d25630c4c24d
A.Y = e8de27c3369ba718adbab5597fbaad9581f5b59ae3b758e7d664bae81d895be4
A.Z = c62dc820a597f75518734f12b2d3c076e5b872303e37b3663636912ade79c058
B.X = c2845b3faaa995eb88e13a44b08d8a6fdb37103f7bbcc585302c9d090be3fc5b
B.Y = 733e5ef1b0314754b70b5b98da63cbb7475918ddb85a715e21aade0c2f2e5611
B.Z = b8505e4a057d108b49f5d0b20884674be18bba48bbc37f765c2c32c5cc4aba5d
Result.X = 266f2961b9352b44e61902a235b33f766f82f8199c176920dae25ad2cbad5cc9
Result.Y = 8560e62047908b13c4247b7e4d2657f2bdecab41e73c846ba18523e5f2918a9b

Test = PointAdd
A.X = f0ca07297738d8687bffcd3f1433966241f437fa1c3381cf2d13f0fc714bc83a
A.Y = 1181b9d61c6982e743f1c32de5d14da745290ecaf27297c36ff3ef20a7526a55
A.Z = 93159618ca5a9f9240df7016ddc983e046126d1290e14478dfcc6a4bae9090bd
B.X = 3a2d75803ccad665f6b785c828eaa96821cb0e81979641b9e59b5fd488fcc755
B.Y = 99e7f820abdbcdda23d90a88788e30d67303dac86987816dbbed447431e33f3f
B.Z = a870186c8137cdbd247d16f3aa18782de1e4c5848f49da3437223eb13d7a9ae2
Result.X = 36a104368d39214d5a3a1a348a1de1389d1aa23009aee37464b5b3256ed4b28c
Result.Y = da5b14dbd75f6c333929bdff88e53af7253c27e166e15ead6d778631036b7d38

Test = PointAdd
A.X = a6256508926caca56a31414aba2d5f5b04dcabdb065352a572e215b043df6e01
A.Y = e6567d330ffb11a86ec29b406b8e3d5cce8ca46f55f38515d842dd856d6852dd
A.Z = ec172618c8cdbfc0f4fd6dffb77858bb292f229e6d10b5c70d0d9ba75fa3ab44
B.X = 0251f6715dbba02e6072c12ab1f89a562d35ed0ff68e021b3b5276b9faf57c52
B.Y = d2d74ff4740ad968fa7e816bc2178458efee797669bef2e634e2857de1658e62
B.Z = abbecea633d31f569297a4a9ec28f708c7a097cb2423ebaca66ac0e42b1c8ee4
Result.X = d2a071d4dd72ad7e63834b58b23c4a034ed7950f5c80fad51bf96959b535d55b
Result.Y = 3cb8dcbe25f49099b9d8dd1a9cb6073368bf6556130f2aa02637dfcff430e869

# Test some random affine sums.
Test = PointAdd
A.X = fabada657e477f088883b2987042e595559d669de3a047b27e3ad339fb3fa5f0
A.Y = 0551992531a68d55a8409d8466034f02808637610ce6d6bcd9cfceb8da1c3e85
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = 3a831cf2b316ce371994a5622e31749407fdf59660dc88322d14c37ebb2d68d2
B.Y = 849c511908abdfa2bcadc43f9beae88052fdb00573c783fbb1b34b99687b9a6b
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = d5dc9241b457d33b9bda849fb7aba8baaff8b6eea92974a8adf4b95fbfa849f0
Result.Y = 089a66780811a8ce455c139c4bea6c5c16234c095a41b9e31c617689bdc6bd0f

Test = PointAdd
A.X = 9dfe6299e62453bb943356b6f7d90c8b6c646728ba3550bb7c1548f2ba5920cb
A.Y = 60a4e342a89837c0e7d61c0e3e88a943633028f5260eff6af5ae8a6063f7a5da
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = 924d7305f867afecd3cc550f4c05c83a2b4c981ba0e7ff20fd2035fabe2ccc92
B.Y = 73934620746c23be03a40edb0662c09ef1776506bd50d6397c2654d340629bf5
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = 7384f658ccbe08afcf6b423bfdd092a8a95b03d81254a519b31517b9b9670155
Result.Y = e922a56146b94776f805a0fbdee9084dd87be1df54f76145bf83e07cd31a083a

Test = PointAdd
A.X = 9b6642b661f06c5b3ef2a0950b3c03d35f42d3d0dcbe105a895f40132c40bd9e
A.Y = 90cbe0ed40e47923257f064886f1e309a310cb82fc21282f8e8fa4f6c975aed6
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = 587f6b4c4bb3ab3d59ba8d31457615b3df9f9f9466df3563f4419db731f494ea
B.Y = 38135b314572346439c8d4535b892a26e5da650ae1dc9ac2d5aeb85ade24174f
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = 97e94b6d485f8de6779e4ad19cc7bede6d70ff4853a56eb6d5fd4e5caac60858
Result.Y = 303bf4d62cf569370ae5393fac46b64efe98ee8222b9982bc3dc61b8e32411c5

Test = PointAdd
A.X = da49658b6c64fc7a7441b177987abbbdbfcfc3c2c569ed97696d706f7af91ca0
A.Y = 9a66906a6e313603e9d78f99fbbda837e521e75bbbad9455ffd43f51f5e30ee5
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = fe32e5885d0005fa1962166142d2aea201af9c4ca41cdddc5446dc2472f71f42
B.Y = a2f9b4d35ea19303a101034e96870a7caed371a980965bf86291b03b5c85af60
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = 5375c5ea3e33c1862ca5f09322ce2012c2b4fbee9a299b66e4882e016908cc2a
Result.Y = 936e4f12ed144cf6fcd0ab085a4929e5e3e7c28641692b1fc2ad9a3b3d447b31

# Test some random affine doublings.
Test = PointAdd
A.X = b148cad109d4b24342eb3a03ccaa10dfd6101edf9548b1d1442b61982a4e332c
A.Y = 7daac293162a8ee2592529630f5bd1eae96659d27c045898d33833999cd076ba
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = b148cad109d4b24342eb3a03ccaa10dfd6101edf9548b1d1442b61982a4e332c
B.Y = 7daac293162a8ee2592529630f5bd1eae96659d27c045898d33833999cd076ba
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = ad00fae6ab0898f7d5eeeffe8c94b302060fba2b191a2d342a8a302998ebe566
Result.Y = 9ee46ba864901cad75169cdea023d7e64da39315e2fec1703fad6b613eb24006

Test = PointAdd
A.X = f21318618205f4967c4f47c9bc3cea41e144dc01830d087414da8dcb16d37cb3
A.Y = 76cebf81ecc696024fe949191dc49b245ef8cc0d55ada88abf481ddad9eb6129
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = f21318618205f4967c4f47c9bc3cea41e144dc01830d087414da8dcb16d37cb3
B.Y = 76cebf81ecc696024fe949191dc49b245ef8cc0d55ada88abf481ddad9eb6129
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = ad8e13b721bcbfc0fe629465cda5fee3494785d51dbe65f1e13429f52c83f03e
Result.Y = 85722e168d89543dce293428e75d52765d0935bde2ef5c45a088222db0dbbeb5

Test = PointAdd
A.X = 8797ff95334b238dadf0cb3d4dc9350678f4c7fc520089ecb70ab419510f2331
A.Y = 326c7583d54dde377fa9193c8588912c4db2219e1bb383ab13902187e5ef76ce
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = 8797ff95334b238dadf0cb3d4dc9350678f4c7fc520089ecb70ab419510f2331
B.Y = 326c7583d54dde377fa9193c8588912c4db2219e1bb383ab13902187e5ef76ce
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = e91c8ec9611de8e44e0d882df59f4fae8d15e3867858fb155256a4a2f154bbc4
Result.Y = c12be21033c6dcea7e7d7262c47876d099aead75d8b025e45ce7986193fc6f8a

Test = PointAdd
A.X = 2f4cba9543c9537e393f126e31bedb521dc0a74a940e731800e5e39cdece355d
A.Y = 1a0957898b746b7dbc9245acd0c6df9e6adca4d8537454c9f318a8ce7c3875c4
A.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
B.X = 2f4cba9543c9537e393f126e31bedb521dc0a74a940e731800e5e39cdece355d
B.Y = 1a0957898b746b7dbc9245acd0c6df9e6adca4d8537454c9f318a8ce7c3875c4
B.Z = 00000000fffffffeffffffffffffffffffffffff000000000000000000000001
Result.X = 5cdc40808120b68e3131bd6ed70a5ce6618f960e4d540baa582afc71be97c65d
Result.Y = 1926a2c9f5b2d3d1dff784623fe6efe2ac629395101d38db0eff5e540bfeacb0


# Scalar montgomery multiplication tests.
#
# The following tests satisfy A * B * 2^-256 = Result (mod N).

Test = OrdMulMont
A = 0000000000000000000000000000000000000000000000000000000000000000
B = b4e9b0aea84aa5ed86964a22881a4d0e58f88e9225f30990c18751e7d4b9ec95
Result = 0000000000000000000000000000000000000000000000000000000000000000

Test = OrdMulMont
A = 00000000ffffffff00000000000000004319055258e8617b0c46353d039cdaaf
B = 5d24e62244973fbd829573d5a579b4e89a6512933a2c3d255bbdbc1c89028323
Result = 5d24e62244973fbd829573d5a579b4e89a6512933a2c3d255bbdbc1c89028323

Test = OrdMulMont
A = ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550
B = abafdc695e4c2c850f8fc60f1efdbf7406a3cd2c6c59bb7e608985723896c187
Result = 917b1214c7b31a7ee7e53be0b41a139e435ff576b51ec6af1e1a944412bea38b

Test = OrdMulMont
A = cf0f01b83670a1c79154ea16f3574ca2d4c688a3c3b6017795cbe54854418904
B = c5ec4d3b00fb2e11fb3b1aa09e60f7d187f7c515977d1343dab9745961fcbb43
Result = 7aaddcee32e3b340af5ad06f854284cbbce5a1ab919e9b7771c3b0e937093438

Test = OrdMulMont
A = 50023f9913879ac4020bc45a89a0ea89082db6265b96b851af29969dd8a9661c
B = 7c165b1cba80808db114441563aa0fbfba41b9e8acff77312a2dd2138b74ef89
Result = 3d2ca1705d8d38cbc76a5409c6535044733cafcb95d12654af1d14de177978b5

Test = OrdMulMont
A = 4d5341ea735e53d2e4f2934755642adee209bd0e5a1506206513227f3c48b270
B = 6e48f2b60eb8fb86760134abaf3d61692557862924069c599ceb31309ea18704
Result = 37cde3e35c814d4287bd345b910d687983929907b7a08afa2acd8596832ea86c

Test = OrdMulMont
A = 33d06c3f5a595a41a6f9c4356f8ab2b8c550d4c64b806eab5560af247c5fa9ed
B = 0e52f34adf5754343bcf3529d652620da3c05b5dd9cdcddfb08b674a1ad21a09
Result = 9dc64d7b4c1bc33b930e0daee2a24fc41f770378659ee71b846d2239b0fea8ea

Test = OrdMulMont
A = 8f211780cce4f93b7193b9378e6f83e1147fb3602b052eef782de8cc833e54ab
B = e1e4f7f1feb15be64292cff86b47cd9730bcb15b133340022b824d591a660cdf
Result = dfa2b683b1ae23027c7c109e0abb40a1366eda027ad2cad1a09061a57bee391f

Test = OrdMulMont
A = 803c279c7e4c11a5568290c0a5789ceab6860f51a942bf646501a45e1ec0a6bf
B = c0a1145a12037129c571f5f939bf16ea0b8b480f08ec774c045d059841f7d5ed
Result = ab48fa3b4aa692a7c077cc55ee3c3fff895118a23728c2fa5f361b30730d955a

Test = OrdMulMont
A = 0e5c95158297d75dbf0b02c3090730f65bf14704495b14837dd907af569407f1
B = 5a03e3787c8772b2fb7ab07d7fe7fe653a58bdae7fde3174c6ed305e524f5728
Result = 71296d305dcf9ce39010ea4f4bbf9f7c1064a413597bdc7574c13dea3fa514dc

Test = OrdMulMont
A = 366299be07886f7846fc74231db624b169360e3c8f60196a1afc9f2101e03922
B = d6d7c830a6edb6861868b964519a6b68f6f24f7c09d66003f3f88eadd1e00158
Result = 0b89596bf5054ebe95a39dab6e975b58190160610b09b2a4f93331ecc0e79fd3

Test = OrdMulMont
A = 8f36f0ef275a72192c3b7388e84df2b8acf66fc53aaf556e3be05c76b3f782c0
B = 704e519363d44e8df8d91f5f347eb61e8d3e85c8fc1b82980c370a379b2bc81c
Result = b70a392e3ce5e85b5efbbded9b8c16a3068ba9b93b4cbed9a9a71dffaad6b58a

Test = OrdMulMont
A = bf4466ef4dea9f06f0f3b4f14e01140a774262c7e0706584f4d7dac19be46d58
B = 4af12d528b2cef0f6714961bca2ab682f8abaa97600ea8181f71563d56f8a9f5
Result = 7b6827c0881b9846e32499e13277efb07917cf4b8c8c72bfb3daa8c1786a8e15


# Test cases where A == B to test squaring.

Test = OrdMulMont
A = 0000000000000000000000000000000000000000000000000000000000000000
B = 0000000000000000000000000000000000000000000000000000000000000000
Result = 0000000000000000000000000000000000000000000000000000000000000000

Test = OrdMulMont
A = 00000000ffffffff00000000000000004319055258e8617b0c46353d039cdaaf
B = 00000000ffffffff00000000000000004319055258e8617b0c46353d039cdaaf
Result = 00000000ffffffff00000000000000004319055258e8617b0c46353d039cdaaf

Test = OrdMulMont
A = ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550
B = ffffffff00000000ffffffffffffffffbce6faada7179e84f3b9cac2fc632550
Result = 60d066334905c1e907f8b6041e607725badef3e243566fafce1bc8f79c197c79

Test = OrdMulMont
A = da43b8dd7fe8830a4fe8980ec585ccbe903a2965a695cdff398200b74b2ede41
B = da43b8dd7fe8830a4fe8980ec585ccbe903a2965a695cdff398200b74b2ede41
Result = 5ec68604412205b380e26ee4e4081eccc10ac7d1417b09cd534f8517b0de81ec

Test = OrdMulMont
A = a82a2b8bdbf8a37dc7cb5799691494a8c9fbf649686a4d250dc30697feb0fa47
B = a82a2b8bdbf8a37dc7cb5799691494a8c9fbf649686a4d250dc30697feb0fa47
Result = 552c094a8841621d6cc26b3b54ce5da5664283888445196a6433d3cfdcad3aee

Test = OrdMulMont
A = d785006e250410d9dcc6d7740795a7374c25b00b9c9a37b8285694a07307eacd
B = d785006e250410d9dcc6d7740795a7374c25b00b9c9a37b8285694a07307eacd
Result = 971aaa9e70ad082cf43725f2e65bc73f4bf762459cee13167545072ec7bdcaf8

Test = OrdMulMont
A = 69d6d9f5417e87d603a3fb6acafa0d1f974abf94ca57ce58d718a0ad5d02a496
B = 69d6d9f5417e87d603a3fb6acafa0d1f974abf94ca57ce58d718a0ad5d02a496
Result = eb3284e5799fbe93171f08e6de9f792cd17f036b3a17671b0310e49b48e589b3

Test = OrdMulMont
A = 1c28f742c3e26e74901d0425f2eb4d5272524668d2405875b32cf6433f212900
B = 1c28f742c3e26e74901d0425f2eb4d5272524668d2405875b32cf6433f212900
Result = 74f70a95399b7ad061a2200fa50528d68eee4654341c8158101e1e3f8f16e642

Test = OrdMulMont
A = 026b2f69f0259d221920b2f358b378a79826f0332ee36afa257765043e3d6732
B = 026b2f69f0259d221920b2f358b378a79826f0332ee36afa257765043e3d6732
Result = e1e9cfa4724995bb50971ca22f3c028cd31cb51fbef8a37c31f10fd1d468f13b

Test = OrdMulMont
A = 376ed4fadcc1c6c4160a0c9c2ab7c62260367968b08d304d47c65f25625d7d60
B = 376ed4fadcc1c6c4160a0c9c2ab7c62260367968b08d304d47c65f25625d7d60
Result = b9ccb67f377e1278f1d2eeda26e5eed76f32406c9deed9764fc0aa346d91e02b

Test = OrdMulMont
A = 50f66867d0a4ef389678d760d2a4db886583b4c068d0e240f7ddf3472c871304
B = 50f66867d0a4ef389678d760d2a4db886583b4c068d0e240f7ddf3472c871304
Result = 82c3467bc5f7ca8b45f4ee61546745e2f53755a02e87f65f572418d60e471c8b

Test = OrdMulMont
A = 5b8bd82b37206d2b727f19ad2d02f63773470074dde7d43d2a77c448ddf2f978
B = 5b8bd82b37206d2b727f19ad2d02f63773470074dde7d43d2a77c448ddf2f978
Result = dbf3c2fc67a0688c3b5ff12cab1739d50b6093c5d98943d388652b1207e4a0f2

Test = OrdMulMont
A = bed7b3a4dada0e16984eb59ee239005ab212e5b1772cdd5d240c8ee268f65c81
B = bed7b3a4dada0e16984eb59ee239005ab212e5b1772cdd5d240c8ee268f65c81
Result = 9232aa2759ca9c5efbaefb0cf45cc6bc9c89def8c25e5c169fe623f30787df36
