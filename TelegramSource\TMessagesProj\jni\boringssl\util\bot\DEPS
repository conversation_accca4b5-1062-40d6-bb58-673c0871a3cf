# Copyright 2015 The BoringSSL Authors
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

vars = {
  'chromium_git': 'https://chromium.googlesource.com',

  'checkout_bazel': False,
  'checkout_clang': False,
  'checkout_cmake': True,
  'checkout_go': True,
  'checkout_fuzzer': False,
  'checkout_perl': True,
  'checkout_sde': False,
  'checkout_nasm': False,
  'checkout_libcxx': False,
  'checkout_riscv64': False,
  'vs_version': '2022',

  # Run the following command to see the latest builds in CIPD:
  #  cipd describe PACKAGE_NAME -version latest

  # infra/3pp/tools/bazel_bootstrap/linux-amd64
  # Keep this in sync with .bazelversion.
  'bazel_version': 'version:<EMAIL>.1',
  # infra/3pp/tools/cmake/linux-amd64
  'cmake_version': 'version:<EMAIL>.8',
  # infra/3pp/tools/go/linux-amd64
  'go_version': 'version:3@1.24.1',
  # infra/3pp/tools/perl/windows-amd64
  'perl_version': 'version:2@********',

  # Update the following from
  # https://chromium.googlesource.com/chromium/src/+/main/DEPS
  'android_sdk_platform-tools_version': 'WihaseZR6cojZbkzIqwGhpTp92ztaGfqq8njBU8eTXYC',
  'libfuzzer_revision': 'e31b99917861f891308269c36a32363b120126bb',
  'libcxx_revision': '7c3087b8a7563d8218b27b7183ba41cf52d41508',
  'libcxxabi_revision': '94c5d7a8edc09f0680aee57548c0b5d400c2840d',
  'llvm_libc_revision': 'b86167407ee6dbb81421aa5d139f056aef1c86d7',
  'ninja_version': 'version:<EMAIL>.4',

  # Update to the latest revision of
  # https://chromium.googlesource.com/chromium/src/tools/clang
  'tools_clang_revision': '19193de25965c139d78bcc8bb0d0c96ee1c46faf',

  # The Android NDK cannot be updated until https://crbug.com/boringssl/454 is fixed.
  # We rely on an older NDK to test building without NEON instructions as the baseline.
  'android_ndk_revision': 'U0e8L6l52ySjBrUBB82Vdyhsg60vVMqH0ItTW3TRHAQC',
  'qemu_static_version': 'gYNEUqCr-Tu1C_vBu68YFtN31hjjLe61dnI2AtuyEsYC',
}

deps = {
  'boringssl/util/bot/android_ndk': {
    'packages': [{
      'package': 'infra/3pp/tools/android_ndk/linux-amd64',
      'version': Var('android_ndk_revision'),
    }],
    'condition': 'checkout_android',
    'dep_type': 'cipd',
  },

  'boringssl/util/bot/android_sdk/public': {
    'packages': [{
      'package': 'chromium/third_party/android_sdk/public/platform-tools',
      'version': Var('android_sdk_platform-tools_version'),
    }],
    'condition': 'checkout_android',
    'dep_type': 'cipd',
  },

  'boringssl/util/bot/bazel': {
    'packages': [{
      'package': 'infra/3pp/tools/bazel_bootstrap/${{platform}}',
      'version': Var('bazel_version'),
    }],
    'condition': 'checkout_bazel',
    'dep_type': 'cipd',
  },

  'boringssl/util/bot/cmake': {
    'packages': [{
      'package': 'infra/3pp/tools/cmake/${{platform}}',
      'version': Var('cmake_version'),
    }],
    'condition': 'checkout_cmake',
    'dep_type': 'cipd',
  },

  'boringssl/util/bot/golang': {
    'packages': [{
      'package': 'infra/3pp/tools/go/${{platform}}',
      'version': Var('go_version'),
    }],
    'condition': 'checkout_go',
    'dep_type': 'cipd',
  },

  'boringssl/util/bot/libFuzzer': {
    'url': Var('chromium_git') + '/external/github.com/llvm/llvm-project/compiler-rt/lib/fuzzer.git' +'@' + Var('libfuzzer_revision'),
    'condition': 'checkout_fuzzer',
  },

  'boringssl/util/bot/libcxx': {
    'url': Var('chromium_git') + '/external/github.com/llvm/llvm-project/libcxx.git' + '@' + Var('libcxx_revision'),
    'condition': 'checkout_libcxx',
  },
  'boringssl/util/bot/libcxxabi': {
    'url': Var('chromium_git') + '/external/github.com/llvm/llvm-project/libcxxabi.git' + '@' + Var('libcxxabi_revision'),
    'condition': 'checkout_libcxx',
  },
  'boringssl/util/bot/llvm-libc': {
    'url': Var('chromium_git') + '/external/github.com/llvm/llvm-project/libc.git' + '@' + Var('llvm_libc_revision'),
    'condition': 'checkout_libcxx',
  },

  'boringssl/util/bot/ninja': {
    'packages': [{
      'package': 'infra/3pp/tools/ninja/${{platform}}',
      'version': Var('ninja_version'),
    }],
    'condition': 'checkout_cmake',
    'dep_type': 'cipd',
  },

  'boringssl/util/bot/perl-win32': {
    'packages': [{
      'package': 'infra/3pp/tools/perl/${{platform}}',
      'version': Var('perl_version'),
    }],
    'condition': 'host_os == "win" and checkout_perl',
    'dep_type': 'cipd',
  },

  'boringssl/util/bot/qemu-static': {
      'packages': [{
          'package': 'infra/3pp/tools/qemu_static/linux-amd64',
          'version': Var('qemu_static_version')
      }],
      'condition': 'checkout_riscv64',
      'dep_type': 'cipd',
  },

  'boringssl/util/bot/tools/clang': {
    'url': Var('chromium_git') + '/chromium/src/tools/clang.git' + '@' + Var('tools_clang_revision'),
    'condition': 'checkout_clang',
  },
}

recursedeps = [
  # android_tools pulls in the NDK from a separate repository.
  'boringssl/util/bot/android_tools',
]

hooks = [
  {
    'name': 'nasm_win32',
    'pattern': '.',
    'condition': 'host_os == "win" and checkout_nasm',
    'action': [ 'download_from_google_storage',
                '--no_resume',
                '--platform=win32',
                '--no_auth',
                '--bucket', 'chromium-tools',
                '-s', 'boringssl/util/bot/nasm-win32.exe.sha1',
    ],
  },
  {
    'name': 'win_toolchain',
    'pattern': '.',
    'condition': 'host_os == "win"',
    'action': [ 'python3',
                'boringssl/util/bot/vs_toolchain.py',
                'update',
                Var('vs_version'),
    ],
  },
  {
    'name': 'clang',
    'pattern': '.',
    'condition': 'checkout_clang',
    'action': [ 'python3',
                'boringssl/util/bot/tools/clang/scripts/update.py',
                '--output-dir', 'boringssl/util/bot/llvm-build',
    ],
  },
  {
    'name': 'sde_linux64',
    'pattern': '.',
    'condition': 'checkout_sde and host_os == "linux"',
    'action': [ 'download_from_google_storage',
                '--no_resume',
                '--bucket', 'chrome-boringssl-sde',
                '-s', 'boringssl/util/bot/sde-linux64.tar.xz.sha1'
    ],
  },
  {
    'name': 'sde_linux64_extract',
    'pattern': '.',
    'condition': 'checkout_sde and host_os == "linux"',
    'action': [ 'python3',
                'boringssl/util/bot/extract.py',
                'boringssl/util/bot/sde-linux64.tar.xz',
                'boringssl/util/bot/sde-linux64/',
    ],
  },
  {
    'name': 'sde_win32',
    'pattern': '.',
    'condition': 'checkout_sde and host_os == "win"',
    'action': [ 'download_from_google_storage',
                '--no_resume',
                '--bucket', 'chrome-boringssl-sde',
                '-s', 'boringssl/util/bot/sde-win32.tar.xz.sha1'
    ],
  },
  {
    'name': 'sde_win32_extract',
    'pattern': '.',
    'condition': 'checkout_sde and host_os == "win"',
    'action': [ 'python3',
                'boringssl/util/bot/extract.py',
                'boringssl/util/bot/sde-win32.tar.xz',
                'boringssl/util/bot/sde-win32/',
    ],
  },
]
