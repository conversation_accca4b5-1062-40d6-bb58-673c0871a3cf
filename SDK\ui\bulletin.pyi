from typing import Callable, Optional, Tuple, Any

class BulletinHelper:
    DURATION_SHORT: int
    DURATION_LONG: int
    DURATION_PROLONG: int

    @classmethod
    def _get_factory_and_provider(cls, fragment: Optional[Any] = None) -> Tuple[Any, Optional[Any]]: ...
    @classmethod
    def show_info(cls, message: str, fragment: Optional[Any] = None) -> None: ...
    @classmethod
    def show_error(cls, message: str, fragment: Optional[Any] = None) -> None: ...
    @classmethod
    def show_success(cls, message: str, fragment: Optional[Any] = None) -> None: ...
    @classmethod
    def show_simple(cls, text: str, icon_res_id: int, fragment: Optional[Any] = None) -> None: ...
    @classmethod
    def show_two_line(cls, title: str, subtitle: str, icon_res_id: int, fragment: Optional[Any] = None) -> None: ...
    @classmethod
    def show_with_button(cls,
                         text: str,
                         icon_res_id: int,
                         button_text: str,
                         on_click: Optional[Callable[[], None]],
                         fragment: Optional[Any] = None,
                         duration: int = ...) -> None: ...
    @classmethod
    def show_undo(cls,
                  text: str,
                  on_undo: Callable[[], None],
                  on_action: Optional[Callable[[], None]] = None,
                  subtitle: Optional[str] = None,
                  fragment: Optional[Any] = None) -> None: ...
    @classmethod
    def show_copied_to_clipboard(cls, message: Optional[str] = None, fragment: Optional[Any] = None) -> None: ...
    @classmethod
    def show_link_copied(cls, is_private_link_info: bool = False, fragment: Optional[Any] = None) -> None: ...
    @classmethod
    def show_file_saved_to_gallery(cls, is_video: bool = False, amount: int = 1, fragment: Optional[Any] = None) -> None: ...
    @classmethod
    def show_file_saved_to_downloads(cls, file_type_enum_name: str = "UNKNOWN", amount: int = 1, fragment: Optional[Any] = None) -> None: ...