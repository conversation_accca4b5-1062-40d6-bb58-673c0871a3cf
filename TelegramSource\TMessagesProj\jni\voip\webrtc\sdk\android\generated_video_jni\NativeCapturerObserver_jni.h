// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/NativeCapturerObserver

#ifndef org_webrtc_NativeCapturerObserver_JNI
#define org_webrtc_NativeCapturerObserver_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_NativeCapturerObserver[];
const char kClassPath_org_webrtc_NativeCapturerObserver[] = "org/webrtc/NativeCapturerObserver";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_NativeCapturerObserver_clazz(nullptr);
#ifndef org_webrtc_NativeCapturerObserver_clazz_defined
#define org_webrtc_NativeCapturerObserver_clazz_defined
inline jclass org_webrtc_NativeCapturerObserver_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_NativeCapturerObserver,
      &g_org_webrtc_NativeCapturerObserver_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_NativeCapturerObserver_Constructor1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_NativeCapturerObserver_Constructor(JNIEnv* env,
    jlong nativeSource) {
  jclass clazz = org_webrtc_NativeCapturerObserver_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_NativeCapturerObserver_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(J)V",
          &g_org_webrtc_NativeCapturerObserver_Constructor1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, nativeSource);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_NativeCapturerObserver_JNI
