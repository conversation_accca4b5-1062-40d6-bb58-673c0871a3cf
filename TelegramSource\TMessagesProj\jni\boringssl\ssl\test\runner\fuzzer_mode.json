{"DisabledTests": {"BadCBCPadding*": "Fuzzer mode has no CBC padding.", "*BadFinished*": "Fuzzer mode ignores Finished checks.", "TrailingMessageData-*Finished*": "Fuzzer mode ignores Finished checks.", "DTLSIgnoreBadPackets*": "Fuzzer mode has no bad packets.", "TLSFatalBadPackets": "Fuzzer mode has no bad packets.", "*-BadRecord*": "Fuzzer mode has no bad packets.", "BadRSAClientKeyExchange*": "Fuzzer mode does not notice a bad premaster secret.", "TrailingMessageData-TLS13-ServerHello-*": "Fuzzer mode will not read the peer's alert as a MAC error", "UnexpectedUnencryptedExtension-Client-TLS13": "Fuzzer mode will not read the peer's alert as a MAC error", "UnknownUnencryptedExtension-Client-TLS13": "Fuzzer mode will not read the peer's alert as a MAC error", "WrongMessageType-TLS13-ServerHello-*": "Fuzzer mode will not read the peer's alert as a MAC error", "CurveTest-Invalid-*-Client-*-TLS13": "Fuzzer mode will not read the peer's alert as a MAC error", "BadECDSA-*": "Fuzzer mode always accepts a signature.", "*-InvalidSignature-*": "Fuzzer mode always accepts a signature.", "Verify-*Auth-SignatureType*": "Fuzzer mode always accepts a signature.", "ECDSACurveMismatch-Verify-TLS13*": "Fuzzer mode always accepts a signature.", "InvalidChannelIDSignature-*": "Fuzzer mode always accepts a signature.", "Resume-Server-CipherNotPreferred*": "Fuzzer mode does not encrypt tickets.", "Resume-Server-DeclineBadCipher*": "Fuzzer mode does not encrypt tickets.", "Resume-Server-DeclineCrossVersion*": "Fuzzer mode does not encrypt tickets.", "TicketCallback-SingleCall-*": "Fuzzer mode does not encrypt tickets.", "CorruptTicket-*": "Fuzzer mode does not encrypt tickets.", "*RejectTicket-Server-*": "Fuzzer mode does not encrypt tickets.", "ShimTicketRewritable*": "Fuzzer mode does not encrypt tickets.", "Resume-Server-*Binder*": "Fuzzer mode does not check binders.", "SkipEarlyData*": "Trial decryption does not work with the NULL cipher.", "EarlyDataChannelID-OfferBoth-Server-*": "Trial decryption does not work with the NULL cipher.", "EarlyDataChannelID-AcceptChannelID-Client-*": "Trial decryption does not work with the NULL cipher.", "EarlyData-NonZeroRTTSession-Server-*": "Trial decryption does not work with the NULL cipher.", "EarlyData-SkipEndOfEarlyData-*": "Trial decryption does not work with the NULL cipher.", "EarlyData-ALPNMismatch-*": "Trial decryption does not work with the NULL cipher.", "EarlyData-ALPNOmitted1-Client-*": "Trial decryption does not work with the NULL cipher.", "EarlyData-ALPNOmitted2-Client-*": "Trial decryption does not work with the NULL cipher.", "EarlyData-ALPNOmitted1-Server-*": "Trial decryption does not work with the NULL cipher.", "EarlyData-ALPNOmitted2-Server-*": "Trial decryption does not work with the NULL cipher.", "*-EarlyData-RejectUnfinishedWrite-Client-*": "Trial decryption does not work with the NULL cipher.", "EarlyData-Reject*-Client*": "Trial decryption does not work with the NULL cipher.", "CustomExtensions-Server-EarlyDataOffered": "Trial decryption does not work with the NULL cipher.", "*-TicketAgeSkew-*-Reject*": "Trial decryption does not work with the NULL cipher.", "*EarlyDataRejected*": "Trial decryption does not work with the NULL cipher.", "ALPS-EarlyData-Mismatch-*": "Trial decryption does not work with the NULL cipher.", "UnencryptedEncryptedExtensions": "The NULL cipher will not notice that the peer didn't change keys.", "AppDataBeforeTLS13KeyChange*": "The NULL cipher will not notice that the peer didn't change keys.", "Renegotiate-Client-BadExt*": "Fuzzer mode does not check renegotiation_info.", "CBCRecordSplitting*": "Fuzzer mode does not implement record-splitting.", "*-ECH-Server-Decline*": "Encryption with wrong ECHConfig will not fail because fuzzer mode skips HPKE decryption."}}