/*
 *  Copyright (c) 2011 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

/*
 * lpc_tables.c
 *
 * Coding tables for the KLT coefficients
 *
 */


#include "modules/audio_coding/codecs/isac/fix/source/settings.h"
#include "modules/audio_coding/codecs/isac/fix/source/lpc_tables.h"

/* indices of KLT coefficients used */
const uint16_t WebRtcIsacfix_kSelIndGain[12] = {
  0,  1,  2,  3,  4,  5,  6,  7,  8,  9,
  10,  11};

const uint16_t WebRtcIsacfix_kSelIndShape[108] = {
  0,  1,  2,  3,  4,  5,  6,  7,  8,  9,
  10,  11,  12,  13,  14,  15,  16,  17,  18,  19,
  20,  21,  22,  23,  24,  25,  26,  27,  28,  29,
  30,  31,  32,  33,  34,  35,  36,  37,  38,  39,
  40,  41,  42,  43,  44,  45,  46,  47,  48,  49,
  50,  51,  52,  53,  54,  55,  56,  57,  58,  59,
  60,  61,  62,  63,  64,  65,  66,  67,  68,  69,
  70,  71,  72,  73,  74,  75,  76,  77,  78,  79,
  80,  81,  82,  83,  84,  85,  86,  87,  88,  89,
  90,  91,  92,  93,  94,  95,  96,  97,  98,  99,
  100,  101,  102,  103,  104,  105,  106,  107
};

/* cdf array for model indicator */
const uint16_t WebRtcIsacfix_kModelCdf[4] = {
  0,  15434,  37548,  65535
};

/* pointer to cdf array for model indicator */
const uint16_t *WebRtcIsacfix_kModelCdfPtr[1] = {
  WebRtcIsacfix_kModelCdf
};

/* initial cdf index for decoder of model indicator */
const uint16_t WebRtcIsacfix_kModelInitIndex[1] = {
  1
};

/* offset to go from rounded value to quantization index */
const int16_t WebRtcIsacfix_kQuantMinGain[12] ={
  3,  6,  4,  6,  6,  9,  5,  16,  11,  34,  32,  47
};

const int16_t WebRtcIsacfix_kQuantMinShape[108] = {
  0,  0,  0,  0,  0,  0,  0,  0,  0,  1,
  1,  1,  1,  1,  2,  2,  2,  3,  0,  0,
  0,  0,  1,  0,  0,  0,  0,  1,  1,  1,
  1,  1,  1,  2,  2,  3,  0,  0,  0,  0,
  1,  0,  1,  1,  1,  1,  1,  1,  1,  2,
  2,  4,  3,  5,  0,  0,  0,  0,  1,  1,
  1,  1,  1,  1,  2,  1,  2,  2,  3,  4,
  4,  7,  0,  0,  1,  1,  1,  1,  1,  1,
  1,  2,  3,  2,  3,  4,  4,  5,  7,  13,
  0,  1,  1,  2,  3,  2,  2,  2,  4,  4,
  5,  6,  7,  11, 9, 13, 12, 26
};

/* maximum quantization index */
const uint16_t WebRtcIsacfix_kMaxIndGain[12] = {
  6,  12,  8,  14,  10,  19,  12,  31,  22,  56,  52,  138
};

const uint16_t WebRtcIsacfix_kMaxIndShape[108] = {
  0,  0,  0,  0,  0,  0,  0,  0,  0,  1,
  2,  2,  2,  2,  4,  4,  5,  6,  0,  0,
  0,  0,  1,  0,  0,  0,  0,  1,  2,  2,
  2,  2,  3,  4,  5,  7,  0,  0,  0,  0,
  2,  0,  2,  2,  2,  2,  3,  2,  2,  4,
  4,  6,  6,  9,  0,  0,  0,  0,  2,  2,
  2,  2,  2,  2,  3,  2,  4,  4,  7,  7,
  9,  13, 0,  0,  2,  2,  2,  2,  2,  2,
  3,  4,  5,  4,  6,  8,  8, 10, 16, 25,
  0,  2,  2,  4,  5,  4,  4,  4,  7,  8,
  9, 10, 13, 19, 17, 23, 25, 49
};

/* index offset */
const uint16_t WebRtcIsacfix_kOffsetGain[3][12] = {
  { 0,  7,  20,  29,  44,  55,  75,  88,  120,  143,  200,  253},
  { 0,  7,  19,  27,  42,  53,  73,  86,  117,  140,  197,  249},
  { 0,  7,  20,  28,  44,  55,  75,  89,  121,  145,  202,  257}
};

const uint16_t WebRtcIsacfix_kOffsetShape[3][108] = {
  {
    0,  1,  2,  3,  4,  5,  6,  7,  8,  9,
    11,  14,  17,  20,  23,  28,  33,  39,  46,  47,
    48,  49,  50,  52,  53,  54,  55,  56,  58,  61,
    64,  67,  70,  74,  79,  85,  93,  94,  95,  96,
    97,  100,  101,  104,  107,  110,  113,  117,  120,  123,
    128,  133,  140,  147,  157,  158,  159,  160,  161,  164,
    167,  170,  173,  176,  179,  183,  186,  191,  196,  204,
    212,  222,  236,  237,  238,  241,  244,  247,  250,  253,
    256,  260,  265,  271,  276,  283,  292,  301,  312,  329,
    355,  356,  359,  362,  367,  373,  378,  383,  388,  396,
    405,  415,  426,  440,  460,  478,  502,  528
  },
  {
    0,  1,  2,  3,  4,  6,  7,  8,  9,  11,
    13,  16,  19,  22,  26,  29,  34,  39,  45,  46,
    47,  48,  49,  50,  51,  52,  53,  55,  57,  60,
    63,  66,  70,  73,  78,  84,  91,  92,  93,  94,
    95,  96,  97,  99,  102,  105,  108,  111,  114,  118,
    123,  128,  134,  141,  151,  152,  153,  154,  156,  159,
    162,  165,  168,  171,  174,  177,  181,  186,  194,  200,
    208,  218,  233,  234,  235,  236,  239,  242,  245,  248,
    251,  254,  258,  263,  270,  277,  288,  297,  308,  324,
    349,  351,  354,  357,  361,  366,  372,  378,  383,  390,
    398,  407,  420,  431,  450,  472,  496,  524
  },
  {
    0,  1,  2,  3,  4,  5,  6,  7,  8,  11,
    14,  17,  20,  23,  26,  29,  34,  40,  47,  48,
    49,  50,  51,  52,  53,  54,  55,  58,  61,  64,
    67,  70,  73,  77,  82,  88,  96,  97,  98,  99,
    101,  102,  104,  107,  110,  113,  116,  119,  122,  125,
    129,  134,  141,  150,  160,  161,  162,  163,  166,  168,
    171,  174,  177,  180,  183,  186,  190,  195,  201,  208,
    216,  226,  243,  244,  245,  248,  251,  254,  257,  260,
    263,  268,  273,  278,  284,  291,  299,  310,  323,  340,
    366,  368,  371,  374,  379,  383,  389,  394,  399,  406,
    414,  422,  433,  445,  461,  480,  505,  533
  }
};

/* initial cdf index for KLT coefficients */
const uint16_t WebRtcIsacfix_kInitIndexGain[3][12] = {
  { 3,  6,  4,  7,  5,  10,  6,  16,  11,  28,  26,  69},
  { 3,  6,  4,  7,  5,  10,  6,  15,  11,  28,  26,  69},
  { 3,  6,  4,  8,  5,  10,  7,  16,  12,  28,  27,  70}
};

const uint16_t WebRtcIsacfix_kInitIndexShape[3][108] = {
  {
    0,  0,  0,  0,  0,  0,  0,  0,  0,  1,
    1,  1,  1,  1,  2,  2,  3,  3,  0,  0,
    0,  0,  1,  0,  0,  0,  0,  1,  1,  1,
    1,  1,  2,  2,  3,  4,  0,  0,  0,  0,
    1,  0,  1,  1,  1,  1,  2,  1,  1,  2,
    2,  3,  3,  5,  0,  0,  0,  0,  1,  1,
    1,  1,  1,  1,  2,  1,  2,  2,  4,  4,
    5,  7,  0,  0,  1,  1,  1,  1,  1,  1,
    2,  2,  3,  2,  3,  4,  4,  5,  8,  13,
    0,  1,  1,  2,  3,  2,  2,  2,  4,  4,
    5,  5,  7,  10,  9,  12,  13,  25
  },
  {
    0,  0,  0,  0,  1,  0,  0,  0,  1,  1,
    1,  1,  1,  2,  1,  2,  2,  3,  0,  0,
    0,  0,  0,  0,  0,  0,  1,  1,  1,  1,
    1,  2,  1,  2,  3,  3,  0,  0,  0,  0,
    0,  0,  1,  1,  1,  1,  1,  1,  2,  2,
    2,  3,  3,  5,  0,  0,  0,  1,  1,  1,
    1,  1,  1,  1,  1,  2,  2,  4,  3,  4,
    5,  7,  0,  0,  0,  1,  1,  1,  1,  1,
    1,  2,  2,  3,  3,  5,  4,  5,  8,  12,
    1,  1,  1,  2,  2,  3,  3,  2,  3,  4,
    4,  6,  5,  9,  11,  12,  14,  25
  },
  {
    0,  0,  0,  0,  0,  0,  0,  0,  1,  1,
    1,  1,  1,  1,  1,  2,  3,  3,  0,  0,
    0,  0,  0,  0,  0,  0,  1,  1,  1,  1,
    1,  1,  2,  2,  3,  4,  0,  0,  0,  1,
    0,  1,  1,  1,  1,  1,  1,  1,  1,  2,
    2,  3,  4,  5,  0,  0,  0,  1,  1,  1,
    1,  1,  1,  1,  1,  2,  2,  3,  3,  4,
    5,  8,  0,  0,  1,  1,  1,  1,  1,  1,
    2,  2,  2,  3,  3,  4,  5,  6,  8,  13,
    1,  1,  1,  2,  2,  3,  2,  2,  3,  4,
    4,  5,  6,  8,  9,  12,  14,  25
  }
};

/* offsets for quantizer representation levels*/
const uint16_t WebRtcIsacfix_kOfLevelsGain[3] = {
  0,  392,  779
};

const uint16_t WebRtcIsacfix_kOfLevelsShape[3] = {
  0,  578,  1152
};

/* quantizer representation levels */



const int32_t WebRtcIsacfix_kLevelsGainQ17[1176] = {
  -364547,-231664,-102123,-573,104807,238257,368823,-758583,-640135,-510291
  ,-377662,-252785,-113177,2627,112906,248601,389461,522691,644517,763974
  ,-538963,-368179,-245823,-106095,-890,104299,241111,350730,493190,-800763
  ,-646230,-510239,-382115,-248623,-111829,-2983,113852,251105,388114,519757
  ,644048,774712,896334,1057931,-770009,-635310,-503690,-375087,-248106,-108525
  ,-105,108259,243472,377948,519271,-1160885,-1032044,-914636,-777593,-647891
  ,-518408,-388028,-254321,-115293,-598,117849,251296,385367,515510,652727
  ,777432,920363,1038625,1153637,1316836,-632287,-505430,-379430,-248458,-118013
  ,-888,118762,250266,381650,513327,652169,766471,932113,-2107480,-1971030
  ,-1827020,-1698263,-1558670,-1436314,-1305377,-1172252,-1047355,-914202,-779651,-651001
  ,-520999,-390394,-255761,-123490,-1893,126839,256703,385493,518607,651760
  ,782750,908693,1044131,1163865,1311066,1424177,1582628,1709823,1831740,1955391
  ,-1423044,-1288917,-1181281,-1043222,-911770,-780354,-646799,-522664,-386721,-258266
  ,-128060,-1101,128233,259996,390336,519590,649290,778701,908010,1040796
  ,1161235,1306889,1441882,-4446002,-4301031,-4194304,-4080591,-3947740,-3808975,-3686530
  ,-3567839,-3383251,-3287089,-3136577,-3017405,-2869860,-2751321,-2619984,-2482932,-2354790
  ,-2223147,-2090669,-1964135,-1831208,-1706697,-1570817,-1446008,-1305386,-1175773,-1046066
  ,-915356,-785120,-653614,-524331,-393767,-260442,-130187,-799,128841,261466
  ,393616,520542,652117,784613,914159,1045399,1181072,1308971,1442502,1570346
  ,1693912,1843986,1966014,2090474,2224869,2364593,2475934,2628403,2752512,2856640
  ,-4192441,-4063232,-3917821,-3799195,-3666233,-3519199,-3411021,-3269192,-3135684,-3008826
  ,-2880875,-2747342,-2620981,-2494872,-2354979,-2229718,-2098939,-1964971,-1835399,-1703452
  ,-1572806,-1440482,-1311794,-1179338,-1046521,-919823,-785914,-655335,-523416,-395507
  ,-264833,-132184,-2546,131698,256217,391372,522688,651248,789964,909618
  ,1035305,1179145,1313824,1436934,1552353,1693722,1815508,1972826,2096328,2228224
  ,2359296,2490368,2598848,-6160384,-6029312,-5881382,-5767168,-5636096,-5505024,-5373952
  ,-5228418,-5110384,-4954923,-4880576,-4710990,-4587364,-4471340,-4333905,-4211513,-4051293
  ,-3907927,-3800105,-3675961,-3538640,-3413663,-3271148,-3152105,-3019103,-2869647,-2744015
  ,-2620639,-2479385,-2364211,-2227611,-2095427,-1974497,-1834168,-1703561,-1568961,-1439826
  ,-1309192,-1174050,-1050191,-917836,-786015,-656943,-518934,-394831,-257708,-128041
  ,1610,128991,264442,393977,521383,653849,788164,918641,1049122,1181971
  ,1308934,1439505,1571808,1706305,1836318,1966235,2097269,2228990,2357005,2490292
  ,2617400,2749680,2881234,3014880,3145637,3276467,3409099,3536637,3671493,3802918
  ,3929740,4065036,4194143,4325999,4456126,4586857,4717194,4843923,4978676,5110913
  ,5245281,5371394,5499780,5633779,5762611,5897682,6028688,6167546,6296465,6421682
  ,6548882,6682074,6809432,6941956,7078143,7204509,7334296,7475137,7609896,7732044
  ,7861604,8002039,8131670,8259222,8390299,8522399,8650037,8782348,8908402,9037815
  ,9164594,9300338,9434679,9574500,9699702,9833934,9948152,10083972,10244937,10332822
  ,10485760,10600122,10760754,10892964,11010048,11111004,11272192,11403264,11525091,11624984
  ,11796480,11915146,-393216,-262144,-101702,-740,100568,262144,393216,-786432
  ,-655360,-524288,-383907,-243301,-94956,-156,95547,269629,416691,524288
  ,655360,-393216,-262144,-88448,-37,87318,262144,393216,524288,-917504
  ,-786432,-655360,-495894,-373308,-267503,-93211,4119,91308,250895,393216
  ,526138,655360,786432,917504,-786432,-655360,-524288,-393216,-262144,-83497
  ,222,86893,240922,393216,524288,-1048576,-917504,-790472,-655360,-508639
  ,-383609,-262016,-95550,-3775,96692,256797,364847,534906,655360,786432
  ,889679,1048576,1179648,1310720,1441792,-655360,-524288,-377684,-248408,-93690
  ,1261,95441,227519,393216,524288,655360,786432,917504,-2097152,-1966080
  ,-1809470,-1703936,-1572864,-1441792,-1314289,-1195149,-1056205,-917504,-809951,-657769
  ,-521072,-383788,-248747,-106350,-2944,105550,243408,388548,521064,628732
  ,786432,885456,1064548,1179648,1310720,1441792,1572864,1703936,1835008,-1441792
  ,-1310720,-1179648,-1037570,-888492,-767774,-646634,-519935,-373458,-248029,-111915
  ,760,111232,247735,379432,507672,672699,786432,917504,1048576,1179648
  ,1310720,1441792,-4456448,-4325376,-4194304,-4063232,-3932160,-3801088,-3670016,-3538944
  ,-3407872,-3276800,-3145728,-3014656,-2883584,-2752512,-2647002,-2490368,-2359296,-2228224
  ,-2097152,-1951753,-1835008,-1703936,-1594177,-1462001,-1289150,-1160774,-1025917,-924928
  ,-782509,-641294,-516191,-386630,-251910,-118886,5210,121226,253949,386008
  ,517973,649374,780064,917783,1052462,1183856,1290593,1419389,1556641,1699884
  ,1835008,1988314,2090470,2228224,2359296,2490368,2621440,2752512,2883584,-3801088
  ,-3643514,-3539937,-3409931,-3263294,-3145658,-3012952,-2879230,-2752359,-2622556,-2483471
  ,-2357556,-2226500,-2093112,-1965892,-1833664,-1701035,-1567767,-1440320,-1310556,-1178339
  ,-1049625,-916812,-786477,-655277,-525050,-393773,-264828,-130696,-480,132126
  ,260116,394197,527846,652294,785563,917183,1049511,1175958,1308161,1438759
  ,1572253,1698835,1828535,1967072,2089391,2212798,2348901,2461547,2621440,2752512
  ,2883584,-7309870,-7203780,-7062699,-6939106,-6790819,-6672036,-6553600,-6422317,-6288422
  ,-6164694,-6026456,-5901410,-5754168,-5621459,-5502710,-5369686,-5240454,-5120712,-4976140
  ,-4847970,-4723070,-4589083,-4450923,-4324680,-4189892,-4065551,-3931803,-3800209,-3668539
  ,-3539395,-3404801,-3277470,-3141389,-3016710,-2885724,-2752612,-2618541,-2486762,-2354153
  ,-2225059,-2094984,-1968194,-1830895,-1699508,-1575743,-1444516,-1308683,-1179714,-1053088
  ,-917981,-783707,-653900,-524980,-395409,-260309,-131948,-3452,132113,263241
  ,392185,522597,654134,788288,919810,1045795,1179210,1314201,1444235,1574447
  ,1705193,1834009,1967332,2098102,2229019,2359147,2489859,2619878,2754966,2879671
  ,3014438,3146143,3276733,3405958,3542196,3667493,3798815,3932961,4062458,4187125
  ,4322346,4454875,4587752,4716809,4848274,4975027,5111957,5242215,5373085,5501158
  ,5640140,5762918,5895358,6024008,6157906,6290628,6422713,6546339,6675888,6815606
  ,6955288,7077501,7211630,7337893,7473635,7607175,7728310,7866475,7999658,8127888
  ,8241758,8386483,8522550,8641582,8771915,8922139,9038632,9179385,9313426,9437184
  ,9568256,9699328,9830400,9952933,10120004,10223616,10354688,10474645,10616832,-393216
  ,-262144,-85425,-121,82533,262144,393216,-786432,-655360,-524288,-379928
  ,-222821,-95200,287,95541,227093,393216,493567,655360,786432,-393216
  ,-262144,-86805,510,86722,262144,393216,524288,-1048576,-917504,-786432
  ,-624456,-529951,-395071,-241627,-101168,81,99975,241605,393216,524288
  ,655360,786432,917504,-786432,-655360,-524288,-393216,-230359,-95619,-137
  ,94425,226222,393216,524288,-1179648,-1048576,-917504,-773841,-655360,-492258
  ,-379715,-244707,-103621,-434,104523,242680,381575,523659,650565,786432
  ,917504,1048576,1179648,1310720,-786432,-629344,-524288,-376757,-242858,-101932
  ,-2715,107155,239212,366480,514943,655360,786432,917504,-2228224,-2097152
  ,-1966080,-1835008,-1703936,-1572864,-1441792,-1284584,-1179648,-1048819,-934658,-777181
  ,-626371,-515660,-377493,-248975,-113036,436,113584,248354,379718,512475
  ,653932,796494,917504,1048576,1179648,1310720,1441792,1572864,1703936,1835008
  ,-1572864,-1441792,-1297608,-1161159,-1032316,-917092,-779770,-647384,-515529,-384269
  ,-250003,-119252,1053,118111,249512,380545,512039,648101,770656,907003
  ,1021725,1178082,1310720,1441792,-4587520,-4456448,-4325376,-4194304,-4063232,-3932160
  ,-3801088,-3670016,-3538944,-3407872,-3276800,-3145728,-2999335,-2883584,-2752512,-2621440
  ,-2490368,-2359296,-2228224,-2112691,-1966080,-1848781,-1709830,-1566109,-1438427,-1303530
  ,-1176124,-1040936,-913876,-784585,-652025,-518361,-385267,-256342,-127297,-2733
  ,125422,257792,389363,519911,651106,783805,909407,1044143,1174156,1309267
  ,1436173,1553771,1708958,1814083,1967036,2095386,2255169,2359296,2478303,2621440
  ,2752512,-4456448,-4325376,-4194304,-4063232,-3932160,-3797524,-3670016,-3560250,-3413217
  ,-3257719,-3166416,-2986626,-2878000,-2781144,-2625383,-2495465,-2346792,-2230930,-2077063
  ,-1949225,-1819274,-1697261,-1568664,-1443074,-1304302,-1175289,-1043794,-913423,-785561
  ,-652104,-522835,-392667,-260517,-130088,-2,129509,260990,391931,522470
  ,655770,784902,917093,1046445,1176951,1303121,1441362,1565401,1702022,1822856
  ,1952852,2090384,2214607,2338436,2457483,2621440,-8781824,-8650752,-8519680,-8388608
  ,-8260828,-8126464,-8003337,-7859030,-7750057,-7602176,-7471104,-7340032,-7193045,-7090588
  ,-6946816,-6843344,-6676635,-6557575,-6447804,-6277614,-6159736,-6035729,-5884723,-5739567
  ,-5634818,-5489867,-5372864,-5243300,-5098939,-4988639,-4856258,-4728494,-4591717,-4447428
  ,-4322409,-4192918,-4062638,-3934141,-3797545,-3673373,-3531587,-3407391,-3277404,-3147797
  ,-3013578,-2886548,-2749811,-2616428,-2490949,-2361301,-2228482,-2096883,-1964343,-1831754
  ,-1702201,-1572495,-1442012,-1309242,-1182451,-1048996,-916905,-786510,-657079,-524730
  ,-393672,-261313,-128743,166,130678,261334,393287,524155,655570,786839
  ,917353,1052167,1179013,1309360,1442634,1571153,1703961,1832027,1965014,2097912
  ,2224861,2355341,2490455,2623051,2753484,2877015,3015783,3144157,3273705,3405255
  ,3542006,3669580,3802417,3935413,4065088,4190896,4333521,4456355,4579781,4713832
  ,4845707,4978625,5113278,5243817,5382318,5500592,5638135,5761179,5900822,6029270
  ,6186398,6297816,6436435,6559163,6666389,6806548,6950461,7086078,7195777,7350973
  ,7480132,7614852,7743514,7847288,8014762,8126464,8257536,8388608,8519680,8650752
  ,8781824,8912896,9043968,9175040,9306112,9437184
};



const int16_t WebRtcIsacfix_kLevelsShapeQ10[1735] = {
  0,     0,    -1,     0,     0,     1,     0,     1,     0,  -821
  ,     1,  -763,    -1,   656,  -620,     0,   633,  -636,     4,   615
  ,  -630,     1,   649, -1773,  -670,     5,   678,  1810, -1876,  -676
  ,     0,   691,  1843, -1806,  -743,    -1,   749,  1795,  2920, -2872
  , -1761,  -772,    -3,   790,  1763,  2942,     0,     0,     0,     0
  ,  -792,     2,     0,     0,     1,     0,  -854,     0,  -702,    -1
  ,   662,  -624,    -5,   638,  -611,    -6,   638,  -647,     0,   651
  ,  -685,    -4,   679,  2123, -1814,  -693,     0,   664,  1791, -1735
  ,  -737,     0,   771,  1854,  2873, -2867, -1842,  -793,    -1,   821
  ,  1826,  2805,  3922,     0,     0,     0,    -1,  -779,     1,   786
  ,     1,  -708,     0,   789,  -799,     1,   797,  -663,     2,   646
  ,  -600,     3,   609,  -600,     1,   658,  1807,  -627,    -3,   612
  ,  -625,     3,   632, -1732,  -674,     1,   672,  2048, -1768,  -715
  ,     0,   724,  1784, -3881, -3072, -1774,  -719,    -1,   730,  1811
  , -2963, -1829,  -806,    -1,   816,  1795,  3050, -5389, -3784, -2942
  , -1893,  -865,   -12,   867,  1885,  2945,  3928,    -2,     1,     4
  ,     0,  -694,     2,   665,  -598,     5,   587,  -599,    -1,   661
  ,  -656,    -7,   611,  -607,     5,   603,  -618,    -4,   620, -1794
  ,  -645,    -2,   654,  -655,    -1,   658, -1801,  -700,     5,   707
  ,  1927, -1752,  -745,    -8,   752,  1843, -2838, -1781,  -801,    11
  ,   796,  1811,  2942,  3866, -3849, -3026, -1848,  -819,     2,   827
  ,  1825,  2963, -3873, -2904, -1869,  -910,    -6,   903,  1902,  2885
  ,  3978,  5286, -7168, -6081, -4989, -3968, -2963, -1970,  -943,    -2
  ,   953,  1951,  2968,  3974,  5009,  6032,    -2,     3, -1024,     2
  ,  1024,  -637,     1,   669,  -613,    -7,   630,  -603,     4,   612
  ,  -612,     0,   590,  -645,   -11,   627,  -657,    -2,   671,  1849
  , -1853,  -694,     2,   702,  1838, -3304, -1780,  -736,    -8,   732
  ,  1772, -1709,  -755,    -6,   760,  1780, -2994, -1780,  -800,     8
  ,   819,  1830,  2816, -4096, -2822, -1881,  -851,    -4,   855,  1872
  ,  2840,  3899, -3908, -2904, -1878,  -887,     6,   897,  1872,  2942
  ,  4008, -4992, -3881, -2933, -1915,  -928,     1,   937,  1919,  2900
  ,  4009,  4881, -6848, -6157, -5065, -3981, -2983, -1972,  -978,    -1
  ,   968,  1979,  2988,  4008,  5007,  6108,  7003,  8051,  9027,-13272
  ,-12012,-11228,-10213, -9261, -8084, -7133, -6075, -5052, -4050, -3036
  , -2014,  -996,    -4,  1007,  2031,  3038,  4049,  5074,  6134,  7069
  ,  8094,  9069, 10212, 11049, 12104,    51, -1024,   -13,  1024,  -609
  ,  -107,   613, -2048,  -687,   -95,   667,  2048, -3072, -1724,  -785
  ,   -34,   732,  1819, -2048,  -703,   -26,   681,  2048, -2048,  -686
  ,    -9,   665,  2048, -2048,  -702,    37,   748,  1723, -4096, -2786
  , -1844,  -837,    37,   811,  1742,  3072, -4096, -2783, -1848,  -881
  ,    39,   898,  1843,  2792,  3764, -5120, -4096, -2923, -1833,  -852
  ,   -14,   862,  1824,  2834,  4096, -6144, -5120, -3914, -2842, -1870
  ,  -886,   -27,   888,  1929,  2931,  4051, -7168, -6144, -5120, -3866
  , -2933, -1915,  -927,    64,   933,  1902,  2929,  3912,  5063,  6144
  ,-11264,-10240, -9216, -8192, -7086, -6144, -5039, -3972, -2943, -1929
  ,  -941,     3,   938,  1942,  2959,  3933,  4905,  6088,  6983,  8192
  , -9216, -8192, -7202, -6088, -4983, -4019, -2955, -1975,  -966,    17
  ,   997,  1981,  2967,  3990,  4948,  6022,  6967,  8192,-13312,-12288
  ,-11264,-10240, -9216, -8049, -6997, -6040, -5026, -4043, -3029, -2034
  , -1015,   -23,   984,  1997,  3010,  4038,  5002,  6015,  6946,  8061
  ,  9216, 10240,-12381,-11264,-10240, -9060, -8058, -7153, -6085, -5075
  , -4051, -3042, -2037, -1017,    -5,  1007,  2028,  3035,  4050,  5088
  ,  6111,  7160,  8156,  9215, 10095, 11229, 12202, 13016,-26624,-25600
  ,-24582,-23671,-22674,-21400,-20355,-19508,-18315,-17269,-16361,-15299
  ,-14363,-13294,-12262,-11237,-10203, -9227, -8165, -7156, -6116, -5122
  , -4076, -3056, -2043, -1020,    -8,  1027,  2047,  3065,  4110,  5130
  ,  6125,  7168,  8195,  9206, 10230, 11227, 12256, 13304, 14281, 15316
  , 16374, 17382, 18428, 19388, 20361, 21468, 22448, 23781,     0,     0
  ,    -1,     0,    -2,  1024,     0,     0,     0,    -1,  1024, -1024
  ,     1, -1024,     4,  1024, -1024,     2,  1024, -1024,     2,  1024
  , -2048, -1024,    -4,  1024, -1024,     2,  1024, -2048, -1024,    -3
  ,  1024,  2048, -2048, -1024,     4,  1024,  2048, -3072, -2048, -1024
  ,    -1,   662,  2048,     0,     1,     0,     0,     1,    -2,    -2
  ,     0,     2,  1024,    -1,  1024, -1024,     4,  1024, -1024,     1
  ,  1024, -1024,     1,  1024, -2048,  -781,    -4,   844,  -807,    -5
  ,   866, -2048,  -726,   -13,   777,  2048, -2048,  -643,    -4,   617
  ,  2048,  3072, -3072, -2048,  -629,     1,   630,  2048,  3072,     0
  ,    -1,     1,    -2,     2,     1, -1024,     5, -1024,     6,  1024
  , -1024,     4,  1024, -1024,     1,  1024, -1024,    -9,  1024,  -673
  ,    -7,   655, -2048,  -665,   -15,   716, -2048,  -647,     4,   640
  ,  2048, -2048,  -615,    -1,   635,  2048, -2048,  -613,    10,   637
  ,  2048,  3072, -3072, -2048,  -647,    -3,   641,  2048,  3072, -5120
  , -4096, -3072, -2048,  -681,     6,   685,  2048,  3072,  4096,     1
  ,     1,     0,    -1,  1024, -1024,    -3,  1024, -1024,     6,  1024
  , -1024,    -1,   769,  -733,     0,  1024,  -876,    -2,   653, -1024
  ,    -4,   786,  -596,   -13,   595,  -634,    -2,   638,  2048, -2048
  ,  -620,    -5,   620,  2048, -4096, -3072, -2048,  -639,    11,   655
  ,  2048,  3072, -3072, -2048,  -659,     5,   663,  2048, -3072, -1823
  ,  -687,    22,   695,  2048,  3072,  4096, -4096, -3072, -1848,  -715
  ,    -3,   727,  1816,  3072,  4096,  5120, -8192, -7168, -6144, -5120
  , -4096, -2884, -1771,  -756,   -14,   775,  1844,  3072,  4096,  5120
  ,  6144,    -1,     1,     0, -1024,     2,   815,  -768,     2,   708
  , -1024,    -3,   693,  -661,    -7,   607,  -643,    -5,   609,  -624
  ,     3,   631,  -682,    -3,   691,  2048, -2048,  -640,     5,   650
  ,  2048, -3072, -2048,  -701,     9,   704,  2048,  3072, -3072, -2048
  ,  -670,    10,   674,  2048,  3072, -5120, -4096, -3072, -1749,  -738
  ,     0,   733,  1811,  3072,  4096,  5120, -4096, -3072, -1873,  -753
  ,     0,   756,  1874,  3072,  4096, -5120, -4096, -2900, -1838,  -793
  ,    -6,   793,  1868,  2837,  4096,  5120, -7168, -6144, -5120, -4096
  , -2832, -1891,  -828,     1,   828,  1901,  2823,  3912,  5120,  6144
  ,  7168,  8192,-13312,-12288,-11264,-10240, -9216, -8192, -7168, -6144
  , -5120, -3976, -3004, -1911,  -869,     7,   869,  1932,  3024,  3992
  ,  5009,  6144,  7168,  8192,  9216, 10240, 11264,    -4,  1024,  -629
  ,   -22,   609,  -623,     9,   640, -2048,  -768,     1,   682, -2048
  ,  -741,    49,   722,  2048, -3072, -1706,  -808,   -20,   768,  1750
  , -1684,  -727,   -29,   788,  1840,  3033, -1758,  -784,     0,   801
  ,  1702, -3072, -1813,  -814,    38,   820,  1884,  2927, -4096, -3241
  , -1839,  -922,    25,   882,  1886,  2812, -4096, -2982, -1923,  -894
  ,    84,   912,  1869,  2778,  4096, -4928, -3965, -2902, -1920,  -883
  ,     3,   917,  1953,  2921,  3957,  4922,  6144,  7168, -5120, -3916
  , -2897, -1949,  -930,    31,   959,  1934,  2901,  3851,  5120, -9216
  , -8192, -7046, -6029, -5030, -4034, -2980, -1969, -1013,   -76,   963
  ,  1963,  2901,  3929,  4893,  6270,  7168,  8192,  9216,-12288,-11264
  ,-10240, -9216, -8192, -6846, -6123, -5108, -4008, -3000, -1963,  -954
  ,    -6,   958,  1992,  3009,  4020,  5085,  6097,  7168,  8192,  9216
  ,-11264,-10139, -9194, -8127, -7156, -6102, -5053, -4049, -3036, -2025
  , -1009,   -34,   974,  1984,  3034,  4028,  5138,  6000,  7057,  8166
  ,  9070, 10033, 11360, 12288,-13312,-12288,-10932,-10190, -9120, -8123
  , -7128, -6103, -5074, -4081, -3053, -2029,  -989,    -4,  1010,  2028
  ,  3051,  4073,  5071,  6099,  7132,  8147,  9295, 10159, 11023, 12263
  , 13312, 14336,-25600,-24576,-23552,-22529,-21504,-20480,-19456,-18637
  ,-17425,-16165,-15316,-14327,-13606,-12135,-11182,-10107, -9153, -8144
  , -7146, -6160, -5129, -4095, -3064, -2038, -1025,     1,  1031,  2072
  ,  3074,  4088,  5123,  6149,  7157,  8173,  9198, 10244, 11250, 12268
  , 13263, 14289, 15351, 16370, 17402, 18413, 19474, 20337, 21386, 22521
  , 23367, 24350,     0,     0,     0,     0,     0,     0,     0,     0
  , -1024,     0,  1024, -1024,     0,  1024, -1024,     0,  1024, -1024
  ,     0,  1024, -1024,     0,  1024,  -773,     0,  1024,  -674,     0
  ,   645, -2048,  -745,     0,   628,  2048, -2048,  -712,     0,   681
  ,  2048,  3072, -3072, -2048,  -673,     0,   682,  1964,  3257,     0
  ,     0,     0,     0,     0,     0,     0,     0, -1024,     0,  1024
  , -1024,     0,  1024, -1024,     0,  1024,  -705,     0,   623,  -771
  ,     0,  1024,  -786,     0,   688,  -631,     0,   652,  2048, -2048
  ,  -627,    -1,   666,  2048, -3072, -1756,  -694,     0,   674,  2048
  , -3098, -1879,  -720,     5,   694,  1886,  2958,  4096,     0,     0
  ,     0,     0,  1024,     0,     0,  1024,  -769,     0,  1024, -1024
  ,     0,  1024, -1024,     0,  1024,  -817,     0,   734,  -786,     0
  ,   651,  -638,     0,   637,  -623,     0,   671,  -652,     0,   619
  ,  2048, -2048,  -670,    -1,   663,  2048, -1908,  -680,     1,   686
  ,  2048,  3072,  4096, -4096, -3072, -1833,  -711,     0,   727,  1747
  ,  3072,  4096, -4096, -2971, -1826,  -762,     2,   766,  1832,  2852
  ,  3928,  5079,     0,     0,     0, -1024,     0,  1024, -1024,     0
  ,  -656,     0,  1024,  -599,     0,   620, -1024,     0,  1024,  -603
  ,     0,   622,  -643,     0,   660,  -599,     0,   611,  -641,    -1
  ,   651,  2048, -2048,  -648,    -2,   647,  1798, -3072, -2048,  -672
  ,     2,   670,  2048, -3072, -1780,  -694,    -1,   706,  1751,  3072
  , -3072, -1862,  -757,     7,   739,  1798,  3072,  4096, -5120, -4096
  , -3253, -1811,  -787,     3,   782,  1887,  3123,  4096, -7252, -6144
  , -5354, -4060, -2864, -1863,  -820,   -11,   847,  1903,  2970,  3851
  ,  4921,  5957,  7168,  8192,  9306,     0,     0, -1024,     0,  1024
  ,  -726,     0,   706,  -692,     0,   593,  -598,     0,   616,  -624
  ,     0,   616,  -605,     0,   613, -2048,  -652,     1,   635,  2048
  , -2048,  -647,    -1,   660,  2048, -1811,  -668,    -2,   685,  2048
  , -1796,  -731,    -2,   730,  1702,  3072, -3072, -1766,  -747,    -4
  ,   756,  1770,  3072, -4096, -3024, -1762,  -783,     4,   771,  1781
  ,  3072, -5120, -4057, -2807, -1832,  -822,     0,   816,  1804,  2851
  ,  3949,  5120, -6144, -4899, -3927, -2920, -1893,  -874,    -2,   868
  ,  1881,  2905,  3960,  4912,  6144, -9216, -8192, -7168, -6225, -4963
  , -3943, -2956, -1890,  -902,     0,   897,  1914,  2916,  3984,  4990
  ,  6050,  7168,-11264,-10217, -9114, -8132, -7035, -5988, -4984, -4000
  , -2980, -1962,  -927,     7,   931,  1956,  2981,  4031,  4972,  6213
  ,  7227,  8192,  9216, 10240, 11170, 12288, 13312, 14336,     0,  1024
  ,  -557,     1,   571,  -606,    -4,   612, -1676,  -707,    10,   673
  ,  2048, -2048,  -727,     5,   686, -3072, -1772,  -755,    12,   716
  ,  1877, -1856,  -786,     2,   786,  1712, -1685,  -818,   -16,   863
  ,  1729, -3072, -1762,  -857,     3,   866,  1838,  2841, -3862, -2816
  , -1864,  -925,    -2,   923,  1897,  2779, -2782, -1838,  -920,   -28
  ,   931,  1951,  2835,  3804, -4815, -4001, -2940, -1934,  -959,   -22
  ,   975,  1957,  2904,  3971,  4835, -5148, -3892, -2944, -1953,  -986
  ,   -11,   989,  1968,  2939,  3949,  4947,  5902, -9216, -8192, -6915
  , -6004, -4965, -4013, -3009, -1977,  -987,    -1,   982,  1972,  3000
  ,  3960,  4939,  5814, -8976, -7888, -7084, -5955, -5043, -4009, -2991
  , -2002, -1000,    -8,   993,  2011,  3023,  4026,  5028,  6023,  7052
  ,  8014,  9216,-11240,-10036, -9125, -8118, -7105, -6062, -5048, -4047
  , -3044, -2025, -1009,    -1,  1011,  2023,  3042,  4074,  5085,  6108
  ,  7119,  8142,  9152, 10114, 11141, 12250, 13307,-15360,-14099,-13284
  ,-12291,-11223,-10221, -9152, -8147, -7128, -6104, -5077, -4072, -3062
  , -2033, -1020,     7,  1018,  2038,  3059,  4081,  5084,  6109,  7102
  ,  8128,  9134, 10125, 11239, 12080,-23552,-22528,-21504,-20480,-19456
  ,-18159,-17240,-16291,-15364,-14285,-13305,-12271,-11233,-10217, -9198
  , -8175, -7157, -6134, -5122, -4089, -3071, -2047, -1018,     3,  1026
  ,  2041,  3077,  4090,  5108,  6131,  7150,  8172,  9175, 10196, 11272
  , 12303, 13273, 14328, 15332, 16334, 17381, 18409, 19423, 20423, 21451
  , 22679, 23391, 24568, 25600, 26589
};

/* cdf tables for quantizer indices */
const uint16_t WebRtcIsacfix_kCdfGain[1212] = {
  0,  13,  301,  3730,  61784,  65167,  65489,  65535,  0,  17,
  142,  314,  929,  2466,  7678,  56450,  63463,  64740,  65204,  65426,
  65527,  65535,  0,  8,  100,  724,  6301,  60105,  65125,  65510,
  65531,  65535,  0,  13,  117,  368,  1068,  3010,  11928,  53603,
  61177,  63404,  64505,  65108,  65422,  65502,  65531,  65535,  0,  4,
  17,  96,  410,  1859,  12125,  54361,  64103,  65305,  65497,  65535,
  0,  4,  88,  230,  469,  950,  1746,  3228,  6092,  16592,
  44756,  56848,  61256,  63308,  64325,  64920,  65309,  65460,  65502,  65522,
  65535,  0,  88,  352,  1675,  6339,  20749,  46686,  59284,  63525,
  64949,  65359,  65502,  65527,  65535,  0,  13,  38,  63,  117,
  234,  381,  641,  929,  1407,  2043,  2809,  4032,  5753,  8792,
  14407,  24308,  38941,  48947,  55403,  59293,  61411,  62688,  63630,  64329,
  64840,  65188,  65376,  65472,  65506,  65527,  65531,  65535,  0,  8,
  29,  75,  222,  615,  1327,  2801,  5623,  9931,  16094,  24966,
  34419,  43458,  50676,  56186,  60055,  62500,  63936,  64765,  65225,  65435,
  65514,  65535,  0,  8,  13,  15,  17,  21,  33,  59,
  71,  92,  151,  243,  360,  456,  674,  934,  1223,  1583,
  1989,  2504,  3031,  3617,  4354,  5154,  6163,  7411,  8780,  10747,
  12874,  15591,  18974,  23027,  27436,  32020,  36948,  41830,  46205,  49797,
  53042,  56094,  58418,  60360,  61763,  62818,  63559,  64103,  64509,  64798,
  65045,  65162,  65288,  65363,  65447,  65506,  65522,  65531,  65533,  65535,
  0,  4,  6,  25,  38,  71,  138,  264,  519,  808,
  1227,  1825,  2516,  3408,  4279,  5560,  7092,  9197,  11420,  14108,
  16947,  20300,  23926,  27459,  31164,  34827,  38575,  42178,  45540,  48747,
  51444,  54090,  56426,  58460,  60080,  61595,  62734,  63668,  64275,  64673,
  64936,  65112,  65217,  65334,  65426,  65464,  65477,  65489,  65518,  65527,
  65529,  65531,  65533,  65535,  0,  2,  4,  8,  10,  12,
  14,  16,  21,  33,  50,  71,  84,  92,  105,  138,
  180,  255,  318,  377,  435,  473,  511,  590,  682,  758,
  913,  1097,  1256,  1449,  1671,  1884,  2169,  2445,  2772,  3157,
  3563,  3944,  4375,  4848,  5334,  5820,  6448,  7101,  7716,  8378,
  9102,  9956,  10752,  11648,  12707,  13670,  14758,  15910,  17187,  18472,
  19627,  20649,  21951,  23169,  24283,  25552,  26862,  28227,  29391,  30764,
  31882,  33213,  34432,  35600,  36910,  38116,  39464,  40729,  41872,  43144,
  44371,  45514,  46762,  47813,  48968,  50069,  51032,  51974,  52908,  53737,
  54603,  55445,  56282,  56990,  57572,  58191,  58840,  59410,  59887,  60264,
  60607,  60946,  61269,  61516,  61771,  61960,  62198,  62408,  62558,  62776,
  62985,  63207,  63408,  63546,  63739,  63906,  64070,  64237,  64371,  64551,
  64677,  64836,  64999,  65095,  65213,  65284,  65338,  65380,  65426,  65447,
  65472,  65485,  65487,  65489,  65502,  65510,  65512,  65514,  65516,  65518,
  65522,  65531,  65533,  65535,  0,  2,  4,  6,  65528,  65531,
  65533,  65535,  0,  2,  4,  6,  8,  10,  222,  65321,
  65513,  65528,  65531,  65533,  65535,  0,  2,  4,  50,  65476,
  65529,  65531,  65533,  65535,  0,  2,  4,  6,  8,  12,
  38,  544,  64936,  65509,  65523,  65525,  65529,  65531,  65533,  65535,
  0,  2,  4,  6,  8,  10,  1055,  64508,  65528,  65531,
  65533,  65535,  0,  2,  4,  6,  8,  10,  12,  123,
  3956,  62999,  65372,  65495,  65515,  65521,  65523,  65525,  65527,  65529,
  65531,  65533,  65535,  0,  2,  4,  12,  53,  4707,  59445,
  65467,  65525,  65527,  65529,  65531,  65533,  65535,  0,  2,  4,
  6,  8,  10,  12,  14,  16,  38,  40,  50,  67,
  96,  234,  929,  14345,  55750,  64866,  65389,  65462,  65514,  65517,
  65519,  65521,  65523,  65525,  65527,  65529,  65531,  65533,  65535,  0,
  2,  4,  6,  8,  10,  15,  35,  91,  377,  1946,
  13618,  52565,  63714,  65184,  65465,  65520,  65523,  65525,  65527,  65529,
  65531,  65533,  65535,  0,  2,  4,  6,  8,  10,  12,
  14,  16,  18,  20,  22,  24,  26,  28,  30,  32,
  34,  36,  38,  40,  42,  44,  46,  48,  50,  52,
  54,  82,  149,  362,  751,  1701,  4239,  12893,  38627,  55072,
  60875,  63071,  64158,  64702,  65096,  65283,  65412,  65473,  65494,  65505,
  65508,  65517,  65519,  65521,  65523,  65525,  65527,  65529,  65531,  65533,
  65535,  0,  2,  15,  23,  53,  143,  260,  418,  698,
  988,  1353,  1812,  2411,  3144,  4015,  5143,  6401,  7611,  8999,
  10653,  12512,  14636,  16865,  19404,  22154,  24798,  27521,  30326,  33102,
  35790,  38603,  41415,  43968,  46771,  49435,  52152,  54715,  57143,  59481,
  61178,  62507,  63603,  64489,  64997,  65257,  65427,  65473,  65503,  65520,
  65529,  65531,  65533,  65535,  0,  3,  6,  9,  26,  32,
  44,  46,  64,  94,  111,  164,  205,  254,  327,  409,
  506,  608,  733,  885,  1093,  1292,  1482,  1742,  1993,  2329,
  2615,  3029,  3374,  3798,  4257,  4870,  5405,  5992,  6618,  7225,
  7816,  8418,  9051,  9761,  10532,  11380,  12113,  13010,  13788,  14594,
  15455,  16361,  17182,  18088,  18997,  20046,  20951,  21968,  22947,  24124,
  25296,  26547,  27712,  28775,  29807,  30835,  31709,  32469,  33201,  34014,
  34876,  35773,  36696,  37620,  38558,  39547,  40406,  41277,  42367,  43290,
  44445,  45443,  46510,  47684,  48973,  50157,  51187,  52242,  53209,  54083,
  55006,  55871,  56618,  57293,  57965,  58556,  59222,  59722,  60180,  60554,
  60902,  61250,  61554,  61837,  62100,  62372,  62631,  62856,  63078,  63324,
  63557,  63768,  63961,  64089,  64235,  64352,  64501,  64633,  64770,  64887,
  65001,  65059,  65121,  65188,  65246,  65302,  65346,  65390,  65428,  65463,
  65477,  65506,  65515,  65517,  65519,  65521,  65523,  65525,  65527,  65529,
  65531,  65533,  65535,  0,  2,  4,  109,  65332,  65531,  65533,
  65535,  0,  2,  4,  6,  8,  25,  1817,  63874,  65511,
  65527,  65529,  65531,  65533,  65535,  0,  2,  4,  907,  65014,
  65529,  65531,  65533,  65535,  0,  2,  4,  6,  8,  10,
  12,  132,  2743,  62708,  65430,  65525,  65527,  65529,  65531,  65533,
  65535,  0,  2,  4,  6,  8,  35,  3743,  61666,  65485,
  65531,  65533,  65535,  0,  2,  4,  6,  8,  10,  23,
  109,  683,  6905,  58417,  64911,  65398,  65497,  65518,  65525,  65527,
  65529,  65531,  65533,  65535,  0,  2,  4,  6,  53,  510,
  10209,  55212,  64573,  65441,  65522,  65529,  65531,  65533,  65535,  0,
  2,  4,  6,  8,  10,  12,  14,  16,  18,  20,
  22,  32,  90,  266,  1037,  3349,  14468,  50488,  62394,  64685,
  65341,  65480,  65514,  65519,  65521,  65523,  65525,  65527,  65529,  65531,
  65533,  65535,  0,  2,  4,  6,  9,  16,  37,  106,
  296,  748,  1868,  5733,  18897,  45553,  60165,  63949,  64926,  65314,
  65441,  65508,  65524,  65529,  65531,  65533,  65535,  0,  2,  4,
  6,  8,  10,  12,  14,  16,  18,  20,  22,  24,
  26,  28,  30,  32,  34,  36,  38,  40,  42,  44,
  46,  48,  50,  83,  175,  344,  667,  1293,  2337,  4357,
  8033,  14988,  28600,  43244,  52011,  57042,  59980,  61779,  63065,  63869,
  64390,  64753,  64988,  65164,  65326,  65422,  65462,  65492,  65506,  65522,
  65524,  65526,  65531,  65533,  65535,  0,  2,  4,  6,  8,
  10,  12,  14,  16,  25,  39,  48,  55,  62,  65,
  85,  106,  139,  169,  194,  252,  323,  485,  688,  1074,
  1600,  2544,  3863,  5733,  8303,  11397,  15529,  20273,  25734,  31455,
  36853,  41891,  46410,  50306,  53702,  56503,  58673,  60479,  61880,  62989,
  63748,  64404,  64852,  65124,  65309,  65424,  65480,  65524,  65528,  65533,
  65535,  0,  2,  4,  6,  8,  10,  12,  14,  21,
  23,  25,  27,  29,  31,  39,  41,  43,  48,  60,
  72,  79,  106,  136,  166,  187,  224,  252,  323,  381,
  427,  478,  568,  660,  783,  912,  1046,  1175,  1365,  1567,
  1768,  2024,  2347,  2659,  3049,  3529,  4033,  4623,  5281,  5925,
  6726,  7526,  8417,  9468,  10783,  12141,  13571,  15222,  16916,  18659,
  20350,  22020,  23725,  25497,  27201,  29026,  30867,  32632,  34323,  36062,
  37829,  39466,  41144,  42654,  43981,  45343,  46579,  47759,  49013,  50171,
  51249,  52283,  53245,  54148,  54938,  55669,  56421,  57109,  57791,  58464,
  59092,  59674,  60105,  60653,  61083,  61407,  61757,  62095,  62388,  62649,
  62873,  63157,  63358,  63540,  63725,  63884,  64046,  64155,  64278,  64426,
  64548,  64654,  64806,  64906,  64994,  65077,  65137,  65215,  65277,  65324,
  65354,  65409,  65437,  65455,  65462,  65490,  65495,  65499,  65508,  65511,
  65513,  65515,  65517,  65519,  65521,  65523,  65525,  65527,  65529,  65531,
  65533,  65535
};

const uint16_t WebRtcIsacfix_kCdfShape[2059] = {
  0,  65535,  0,  65535,  0,  65535,  0,  65535,  0,  65535,
  0,  65535,  0,  65535,  0,  65535,  0,  65535,  0,  4,
  65535,  0,  8,  65514,  65535,  0,  29,  65481,  65535,  0,
  121,  65439,  65535,  0,  239,  65284,  65535,  0,  8,  779,
  64999,  65527,  65535,  0,  8,  888,  64693,  65522,  65535,  0,
  29,  2604,  62843,  65497,  65531,  65535,  0,  25,  176,  4576,
  61164,  65275,  65527,  65535,  0,  65535,  0,  65535,  0,  65535,
  0,  65535,  0,  4,  65535,  0,  65535,  0,  65535,  0,
  65535,  0,  65535,  0,  4,  65535,  0,  33,  65502,  65535,
  0,  54,  65481,  65535,  0,  251,  65309,  65535,  0,  611,
  65074,  65535,  0,  1273,  64292,  65527,  65535,  0,  4,  1809,
  63940,  65518,  65535,  0,  88,  4392,  60603,  65426,  65531,  65535,
  0,  25,  419,  7046,  57756,  64961,  65514,  65531,  65535,  0,
  65535,  0,  65535,  0,  65535,  0,  65535,  0,  4,  65531,
  65535,  0,  65535,  0,  8,  65531,  65535,  0,  4,  65527,
  65535,  0,  17,  65510,  65535,  0,  42,  65481,  65535,  0,
  197,  65342,  65531,  65535,  0,  385,  65154,  65535,  0,  1005,
  64522,  65535,  0,  8,  1985,  63469,  65533,  65535,  0,  38,
  3119,  61884,  65514,  65535,  0,  4,  6,  67,  4961,  60804,
  65472,  65535,  0,  17,  565,  9182,  56538,  65087,  65514,  65535,
  0,  8,  63,  327,  2118,  14490,  52774,  63839,  65376,  65522,
  65535,  0,  65535,  0,  65535,  0,  65535,  0,  65535,  0,
  17,  65522,  65535,  0,  59,  65489,  65535,  0,  50,  65522,
  65535,  0,  54,  65489,  65535,  0,  310,  65179,  65535,  0,
  615,  64836,  65535,  0,  4,  1503,  63965,  65535,  0,  2780,
  63383,  65535,  0,  21,  3919,  61051,  65527,  65535,  0,  84,
  6674,  59929,  65435,  65535,  0,  4,  255,  7976,  55784,  65150,
  65518,  65531,  65535,  0,  4,  8,  582,  10726,  53465,  64949,
  65518,  65535,  0,  29,  339,  3006,  17555,  49517,  62956,  65200,
  65497,  65531,  65535,  0,  2,  33,  138,  565,  2324,  7670,
  22089,  45966,  58949,  63479,  64966,  65380,  65518,  65535,  0,  65535,
  0,  65535,  0,  2,  65533,  65535,  0,  46,  65514,  65535,
  0,  414,  65091,  65535,  0,  540,  64911,  65535,  0,  419,
  65162,  65535,  0,  976,  64790,  65535,  0,  2977,  62495,  65531,
  65535,  0,  4,  3852,  61034,  65527,  65535,  0,  4,  29,
  6021,  60243,  65468,  65535,  0,  84,  6711,  58066,  65418,  65535,
  0,  13,  281,  9550,  54917,  65125,  65506,  65535,  0,  2,
  63,  984,  12108,  52644,  64342,  65435,  65527,  65535,  0,  29,
  251,  2014,  14871,  47553,  62881,  65229,  65518,  65535,  0,  13,
  142,  749,  4220,  18497,  45200,  60913,  64823,  65426,  65527,  65535,
  0,  13,  71,  264,  1176,  3789,  10500,  24480,  43488,  56324,
  62315,  64493,  65242,  65464,  65514,  65522,  65531,  65535,  0,  4,
  13,  38,  109,  205,  448,  850,  1708,  3429,  6276,  11371,
  19221,  29734,  40955,  49391,  55411,  59460,  62102,  63793,  64656,  65150,
  65401,  65485,  65522,  65531,  65535,  0,  65535,  0,  2,  65533,
  65535,  0,  1160,  65476,  65535,  0,  2,  6640,  64763,  65533,
  65535,  0,  2,  38,  9923,  61009,  65527,  65535,  0,  2,
  4949,  63092,  65533,  65535,  0,  2,  3090,  63398,  65533,  65535,
  0,  2,  2520,  58744,  65510,  65535,  0,  2,  13,  544,
  8784,  51403,  65148,  65533,  65535,  0,  2,  25,  1017,  10412,
  43550,  63651,  65489,  65527,  65535,  0,  2,  4,  29,  783,
  13377,  52462,  64524,  65495,  65533,  65535,  0,  2,  4,  6,
  100,  1817,  18451,  52590,  63559,  65376,  65531,  65535,  0,  2,
  4,  6,  46,  385,  2562,  11225,  37416,  60488,  65026,  65487,
  65529,  65533,  65535,  0,  2,  4,  6,  8,  10,  12,
  42,  222,  971,  5221,  19811,  45048,  60312,  64486,  65294,  65474,
  65525,  65529,  65533,  65535,  0,  2,  4,  8,  71,  167,
  666,  2533,  7875,  19622,  38082,  54359,  62108,  64633,  65290,  65495,
  65529,  65533,  65535,  0,  2,  4,  6,  8,  10,  13,
  109,  586,  1930,  4949,  11600,  22641,  36125,  48312,  56899,  61495,
  63927,  64932,  65389,  65489,  65518,  65531,  65533,  65535,  0,  4,
  6,  8,  67,  209,  712,  1838,  4195,  8432,  14432,  22834,
  31723,  40523,  48139,  53929,  57865,  60657,  62403,  63584,  64363,  64907,
  65167,  65372,  65472,  65514,  65535,  0,  2,  4,  13,  25,
  42,  46,  50,  75,  113,  147,  281,  448,  657,  909,
  1185,  1591,  1976,  2600,  3676,  5317,  7398,  9914,  12941,  16169,
  19477,  22885,  26464,  29851,  33360,  37228,  41139,  44802,  48654,  52058,
  55181,  57676,  59581,  61022,  62190,  63107,  63676,  64199,  64547,  64924,
  65158,  65313,  65430,  65481,  65518,  65535,  0,  65535,  0,  65535,
  0,  65535,  0,  65535,  0,  65533,  65535,  0,  65535,  0,
  65535,  0,  65535,  0,  65533,  65535,  0,  2,  65535,  0,
  2,  65533,  65535,  0,  2,  65533,  65535,  0,  2,  65533,
  65535,  0,  2,  4,  65533,  65535,  0,  2,  65533,  65535,
  0,  2,  4,  65531,  65533,  65535,  0,  2,  4,  65531,
  65533,  65535,  0,  2,  4,  6,  65524,  65533,  65535,  0,
  65535,  0,  65535,  0,  65535,  0,  65535,  0,  65535,  0,
  65535,  0,  65535,  0,  65535,  0,  65533,  65535,  0,  65533,
  65535,  0,  2,  65533,  65535,  0,  2,  65533,  65535,  0,
  2,  65533,  65535,  0,  2,  4,  65532,  65535,  0,  6,
  65523,  65535,  0,  2,  15,  65530,  65533,  65535,  0,  2,
  35,  65493,  65531,  65533,  65535,  0,  2,  4,  158,  65382,
  65531,  65533,  65535,  0,  65535,  0,  65535,  0,  65535,  0,
  65535,  0,  65535,  0,  65535,  0,  2,  65535,  0,  2,
  65533,  65535,  0,  2,  65533,  65535,  0,  2,  65533,  65535,
  0,  2,  65533,  65535,  0,  9,  65512,  65535,  0,  2,
  12,  65529,  65535,  0,  2,  73,  65434,  65533,  65535,  0,
  2,  240,  65343,  65533,  65535,  0,  2,  476,  65017,  65531,
  65533,  65535,  0,  2,  4,  1046,  64686,  65531,  65533,  65535,
  0,  2,  4,  6,  8,  1870,  63898,  65529,  65531,  65533,
  65535,  0,  65535,  0,  65535,  0,  65535,  0,  65533,  65535,
  0,  2,  65533,  65535,  0,  2,  65533,  65535,  0,  2,
  65532,  65535,  0,  6,  65533,  65535,  0,  6,  65523,  65535,
  0,  2,  65532,  65535,  0,  137,  65439,  65535,  0,  576,
  64899,  65533,  65535,  0,  2,  289,  65299,  65533,  65535,  0,
  2,  4,  6,  880,  64134,  65531,  65533,  65535,  0,  2,
  4,  1853,  63347,  65533,  65535,  0,  2,  6,  2516,  61762,
  65529,  65531,  65533,  65535,  0,  2,  4,  9,  3980,  61380,
  65503,  65529,  65531,  65533,  65535,  0,  2,  4,  6,  8,
  10,  12,  61,  6393,  59859,  65466,  65527,  65529,  65531,  65533,
  65535,  0,  65535,  0,  65535,  0,  65535,  0,  2,  65532,
  65535,  0,  3,  65529,  65535,  0,  2,  65529,  65535,  0,
  61,  65453,  65535,  0,  234,  65313,  65535,  0,  503,  65138,
  65535,  0,  155,  65402,  65533,  65535,  0,  2,  1058,  64554,
  65533,  65535,  0,  2,  4,  3138,  62109,  65531,  65533,  65535,
  0,  2,  4,  2031,  63339,  65531,  65533,  65535,  0,  2,
  4,  6,  9,  4155,  60778,  65523,  65529,  65531,  65533,  65535,
  0,  2,  4,  41,  6189,  59269,  65490,  65531,  65533,  65535,
  0,  2,  4,  6,  210,  8789,  57043,  65400,  65528,  65531,
  65533,  65535,  0,  2,  4,  6,  8,  26,  453,  10086,
  55499,  64948,  65483,  65524,  65527,  65529,  65531,  65533,  65535,  0,
  2,  4,  6,  8,  10,  12,  14,  16,  18,  20,
  114,  1014,  11202,  52670,  64226,  65356,  65503,  65514,  65523,  65525,
  65527,  65529,  65531,  65533,  65535,  0,  65533,  65535,  0,  15,
  65301,  65535,  0,  152,  64807,  65535,  0,  2,  3328,  63308,
  65535,  0,  2,  4050,  59730,  65533,  65535,  0,  2,  164,
  10564,  61894,  65529,  65535,  0,  15,  6712,  59831,  65076,  65532,
  65535,  0,  32,  7712,  57449,  65459,  65535,  0,  2,  210,
  7849,  53110,  65021,  65523,  65535,  0,  2,  12,  1081,  13883,
  48262,  62870,  65477,  65535,  0,  2,  88,  847,  6145,  37852,
  62012,  65454,  65533,  65535,  0,  9,  47,  207,  1823,  14522,
  45521,  61069,  64891,  65481,  65528,  65531,  65533,  65535,  0,  2,
  9,  488,  2881,  12758,  38703,  58412,  64420,  65410,  65533,  65535,
  0,  2,  4,  6,  61,  333,  1891,  6486,  19720,  43188,
  57547,  62472,  64796,  65421,  65497,  65523,  65529,  65531,  65533,  65535,
  0,  2,  4,  6,  8,  10,  12,  29,  117,  447,
  1528,  6138,  21242,  43133,  56495,  62432,  64746,  65362,  65500,  65529,
  65531,  65533,  65535,  0,  2,  18,  105,  301,  760,  1490,
  3472,  7568,  15002,  26424,  40330,  53029,  60048,  62964,  64274,  64890,
  65337,  65445,  65489,  65513,  65527,  65530,  65533,  65535,  0,  2,
  4,  6,  41,  102,  409,  853,  2031,  4316,  7302,  11328,
  16869,  24825,  34926,  43481,  50877,  56126,  59874,  62103,  63281,  63857,
  64166,  64675,  65382,  65522,  65531,  65533,  65535,  0,  2,  4,
  6,  8,  10,  12,  14,  16,  18,  29,  38,  53,
  58,  96,  181,  503,  1183,  2849,  5590,  8600,  11379,  13942,
  16478,  19453,  22638,  26039,  29411,  32921,  37596,  41433,  44998,  48560,
  51979,  55106,  57666,  59892,  61485,  62616,  63484,  64018,  64375,  64685,
  64924,  65076,  65278,  65395,  65471,  65509,  65529,  65535,  0,  65535,
  0,  65535,  0,  65535,  0,  65535,  0,  65535,  0,  65535,
  0,  65535,  0,  65535,  0,  2,  65533,  65535,  0,  2,
  65533,  65535,  0,  2,  65533,  65535,  0,  2,  65533,  65535,
  0,  2,  65533,  65535,  0,  2,  65533,  65535,  0,  7,
  65519,  65535,  0,  2,  14,  65491,  65533,  65535,  0,  2,
  81,  65427,  65531,  65533,  65535,  0,  2,  4,  312,  65293,
  65528,  65533,  65535,  0,  65535,  0,  65535,  0,  65535,  0,
  65535,  0,  65535,  0,  65535,  0,  65535,  0,  65535,  0,
  2,  65533,  65535,  0,  2,  65533,  65535,  0,  2,  65533,
  65535,  0,  5,  65523,  65535,  0,  2,  65533,  65535,  0,
  7,  65526,  65535,  0,  46,  65464,  65533,  65535,  0,  2,
  120,  65309,  65533,  65535,  0,  2,  5,  362,  65097,  65533,
  65535,  0,  2,  18,  1164,  64785,  65528,  65531,  65533,  65535,
  0,  65535,  0,  65535,  0,  65535,  0,  65533,  65535,  0,
  65535,  0,  65533,  65535,  0,  2,  65533,  65535,  0,  2,
  65533,  65535,  0,  2,  65533,  65535,  0,  2,  65530,  65535,
  0,  2,  65523,  65535,  0,  69,  65477,  65535,  0,  141,
  65459,  65535,  0,  194,  65325,  65533,  65535,  0,  2,  543,
  64912,  65533,  65535,  0,  5,  1270,  64301,  65529,  65531,  65533,
  65535,  0,  2,  4,  12,  2055,  63538,  65508,  65531,  65533,
  65535,  0,  2,  7,  102,  3775,  61970,  65429,  65526,  65528,
  65533,  65535,  0,  65535,  0,  65535,  0,  65535,  0,  2,
  65533,  65535,  0,  2,  65535,  0,  9,  65533,  65535,  0,
  25,  65512,  65535,  0,  2,  65533,  65535,  0,  44,  65480,
  65535,  0,  48,  65475,  65535,  0,  162,  65373,  65535,  0,
  637,  64806,  65533,  65535,  0,  2,  935,  64445,  65533,  65535,
  0,  2,  4,  1662,  64083,  65533,  65535,  0,  2,  12,
  3036,  62469,  65521,  65533,  65535,  0,  2,  120,  5405,  60468,
  65469,  65531,  65533,  65535,  0,  2,  4,  18,  254,  6663,
  58999,  65272,  65528,  65533,  65535,  0,  2,  4,  9,  12,
  67,  591,  8981,  56781,  64564,  65365,  65508,  65524,  65526,  65529,
  65531,  65533,  65535,  0,  65535,  0,  65535,  0,  2,  65533,
  65535,  0,  9,  65526,  65535,  0,  14,  65503,  65535,  0,
  127,  65390,  65535,  0,  517,  64990,  65535,  0,  178,  65330,
  65535,  0,  2,  1055,  64533,  65533,  65535,  0,  2,  1558,
  63942,  65533,  65535,  0,  2,  2205,  63173,  65533,  65535,  0,
  25,  4493,  60862,  65505,  65533,  65535,  0,  2,  48,  5890,
  59442,  65482,  65533,  65535,  0,  2,  4,  127,  7532,  58191,
  65394,  65533,  65535,  0,  2,  5,  32,  550,  10388,  54924,
  65046,  65510,  65531,  65533,  65535,  0,  2,  4,  30,  150,
  1685,  14340,  51375,  63619,  65288,  65503,  65528,  65533,  65535,  0,
  2,  4,  6,  8,  28,  97,  473,  2692,  15407,  50020,
  62880,  65064,  65445,  65508,  65531,  65533,  65535,  0,  2,  4,
  12,  32,  79,  150,  372,  907,  2184,  5868,  18207,  45431,
  59856,  64031,  65096,  65401,  65481,  65507,  65521,  65523,  65525,  65527,
  65529,  65531,  65533,  65535,  0,  65533,  65535,  0,  182,  65491,
  65535,  0,  877,  64286,  65535,  0,  9,  2708,  63612,  65533,
  65535,  0,  2,  6038,  59532,  65535,  0,  2,  92,  5500,
  60539,  65533,  65535,  0,  268,  8908,  56512,  65385,  65535,  0,
  129,  13110,  52742,  65036,  65535,  0,  2,  806,  14003,  51929,
  64732,  65523,  65535,  0,  7,  92,  2667,  18159,  47678,  62610,
  65355,  65535,  0,  32,  1836,  19676,  48237,  61677,  64960,  65526,
  65535,  0,  21,  159,  967,  5668,  22782,  44709,  58317,  64020,
  65406,  65528,  65535,  0,  7,  162,  1838,  8328,  23929,  43014,
  56394,  63374,  65216,  65484,  65521,  65535,  0,  2,  4,  6,
  28,  268,  1120,  3613,  10688,  24185,  40989,  54917,  61684,  64510,
  65403,  65530,  65535,  0,  2,  16,  44,  139,  492,  1739,
  5313,  13558,  26766,  41566,  52446,  58937,  62815,  64480,  65201,  65454,
  65524,  65533,  65535,  0,  7,  25,  76,  263,  612,  1466,
  3325,  6832,  12366,  20152,  29466,  39255,  47360,  53506,  57740,  60726,
  62845,  64131,  64882,  65260,  65459,  65521,  65528,  65530,  65535,  0,
  2,  4,  14,  48,  136,  312,  653,  1240,  2369,  4327,
  7028,  10759,  15449,  21235,  28027,  35386,  42938,  49562,  54990,  59119,
  62086,  63916,  64863,  65249,  65445,  65493,  65523,  65535,  0,  2,
  4,  6,  8,  10,  12,  21,  83,  208,  409,  723,
  1152,  1868,  2951,  4463,  6460,  8979,  11831,  15195,  18863,  22657,
  26762,  30881,  34963,  39098,  43054,  47069,  50620,  53871,  56821,  59386,
  61340,  62670,  63512,  64023,  64429,  64750,  64944,  65126,  65279,  65366,
  65413,  65445,  65473,  65505,  65510,  65521,  65528,  65530,  65535
};

/* pointers to cdf tables for quantizer indices */
const uint16_t *WebRtcIsacfix_kCdfGainPtr[3][12] = {
  { WebRtcIsacfix_kCdfGain +0 +0,   WebRtcIsacfix_kCdfGain +0 +8,   WebRtcIsacfix_kCdfGain +0 +22,
    WebRtcIsacfix_kCdfGain +0 +32,  WebRtcIsacfix_kCdfGain +0 +48,  WebRtcIsacfix_kCdfGain +0 +60,
    WebRtcIsacfix_kCdfGain +0 +81,  WebRtcIsacfix_kCdfGain +0 +95,  WebRtcIsacfix_kCdfGain +0 +128,
    WebRtcIsacfix_kCdfGain +0 +152, WebRtcIsacfix_kCdfGain +0 +210, WebRtcIsacfix_kCdfGain +0 +264
  },
  { WebRtcIsacfix_kCdfGain +404 +0,   WebRtcIsacfix_kCdfGain +404 +8,   WebRtcIsacfix_kCdfGain +404 +21,
    WebRtcIsacfix_kCdfGain +404 +30,  WebRtcIsacfix_kCdfGain +404 +46,  WebRtcIsacfix_kCdfGain +404 +58,
    WebRtcIsacfix_kCdfGain +404 +79,  WebRtcIsacfix_kCdfGain +404 +93,  WebRtcIsacfix_kCdfGain +404 +125,
    WebRtcIsacfix_kCdfGain +404 +149, WebRtcIsacfix_kCdfGain +404 +207, WebRtcIsacfix_kCdfGain +404 +260
  },
  { WebRtcIsacfix_kCdfGain +803 +0,   WebRtcIsacfix_kCdfGain +803 +8,   WebRtcIsacfix_kCdfGain +803 +22,
    WebRtcIsacfix_kCdfGain +803 +31,  WebRtcIsacfix_kCdfGain +803 +48,  WebRtcIsacfix_kCdfGain +803 +60,
    WebRtcIsacfix_kCdfGain +803 +81,  WebRtcIsacfix_kCdfGain +803 +96,  WebRtcIsacfix_kCdfGain +803 +129,
    WebRtcIsacfix_kCdfGain +803 +154, WebRtcIsacfix_kCdfGain +803 +212, WebRtcIsacfix_kCdfGain +803 +268
  }
};

const uint16_t *WebRtcIsacfix_kCdfShapePtr[3][108] = {
  { WebRtcIsacfix_kCdfShape +0 +0,   WebRtcIsacfix_kCdfShape +0 +2,   WebRtcIsacfix_kCdfShape +0 +4,
    WebRtcIsacfix_kCdfShape +0 +6,   WebRtcIsacfix_kCdfShape +0 +8,   WebRtcIsacfix_kCdfShape +0 +10,
    WebRtcIsacfix_kCdfShape +0 +12,  WebRtcIsacfix_kCdfShape +0 +14,  WebRtcIsacfix_kCdfShape +0 +16,
    WebRtcIsacfix_kCdfShape +0 +18,  WebRtcIsacfix_kCdfShape +0 +21,  WebRtcIsacfix_kCdfShape +0 +25,
    WebRtcIsacfix_kCdfShape +0 +29,  WebRtcIsacfix_kCdfShape +0 +33,  WebRtcIsacfix_kCdfShape +0 +37,
    WebRtcIsacfix_kCdfShape +0 +43,  WebRtcIsacfix_kCdfShape +0 +49,  WebRtcIsacfix_kCdfShape +0 +56,
    WebRtcIsacfix_kCdfShape +0 +64,  WebRtcIsacfix_kCdfShape +0 +66,  WebRtcIsacfix_kCdfShape +0 +68,
    WebRtcIsacfix_kCdfShape +0 +70,  WebRtcIsacfix_kCdfShape +0 +72,  WebRtcIsacfix_kCdfShape +0 +75,
    WebRtcIsacfix_kCdfShape +0 +77,  WebRtcIsacfix_kCdfShape +0 +79,  WebRtcIsacfix_kCdfShape +0 +81,
    WebRtcIsacfix_kCdfShape +0 +83,  WebRtcIsacfix_kCdfShape +0 +86,  WebRtcIsacfix_kCdfShape +0 +90,
    WebRtcIsacfix_kCdfShape +0 +94,  WebRtcIsacfix_kCdfShape +0 +98,  WebRtcIsacfix_kCdfShape +0 +102,
    WebRtcIsacfix_kCdfShape +0 +107, WebRtcIsacfix_kCdfShape +0 +113, WebRtcIsacfix_kCdfShape +0 +120,
    WebRtcIsacfix_kCdfShape +0 +129, WebRtcIsacfix_kCdfShape +0 +131, WebRtcIsacfix_kCdfShape +0 +133,
    WebRtcIsacfix_kCdfShape +0 +135, WebRtcIsacfix_kCdfShape +0 +137, WebRtcIsacfix_kCdfShape +0 +141,
    WebRtcIsacfix_kCdfShape +0 +143, WebRtcIsacfix_kCdfShape +0 +147, WebRtcIsacfix_kCdfShape +0 +151,
    WebRtcIsacfix_kCdfShape +0 +155, WebRtcIsacfix_kCdfShape +0 +159, WebRtcIsacfix_kCdfShape +0 +164,
    WebRtcIsacfix_kCdfShape +0 +168, WebRtcIsacfix_kCdfShape +0 +172, WebRtcIsacfix_kCdfShape +0 +178,
    WebRtcIsacfix_kCdfShape +0 +184, WebRtcIsacfix_kCdfShape +0 +192, WebRtcIsacfix_kCdfShape +0 +200,
    WebRtcIsacfix_kCdfShape +0 +211, WebRtcIsacfix_kCdfShape +0 +213, WebRtcIsacfix_kCdfShape +0 +215,
    WebRtcIsacfix_kCdfShape +0 +217, WebRtcIsacfix_kCdfShape +0 +219, WebRtcIsacfix_kCdfShape +0 +223,
    WebRtcIsacfix_kCdfShape +0 +227, WebRtcIsacfix_kCdfShape +0 +231, WebRtcIsacfix_kCdfShape +0 +235,
    WebRtcIsacfix_kCdfShape +0 +239, WebRtcIsacfix_kCdfShape +0 +243, WebRtcIsacfix_kCdfShape +0 +248,
    WebRtcIsacfix_kCdfShape +0 +252, WebRtcIsacfix_kCdfShape +0 +258, WebRtcIsacfix_kCdfShape +0 +264,
    WebRtcIsacfix_kCdfShape +0 +273, WebRtcIsacfix_kCdfShape +0 +282, WebRtcIsacfix_kCdfShape +0 +293,
    WebRtcIsacfix_kCdfShape +0 +308, WebRtcIsacfix_kCdfShape +0 +310, WebRtcIsacfix_kCdfShape +0 +312,
    WebRtcIsacfix_kCdfShape +0 +316, WebRtcIsacfix_kCdfShape +0 +320, WebRtcIsacfix_kCdfShape +0 +324,
    WebRtcIsacfix_kCdfShape +0 +328, WebRtcIsacfix_kCdfShape +0 +332, WebRtcIsacfix_kCdfShape +0 +336,
    WebRtcIsacfix_kCdfShape +0 +341, WebRtcIsacfix_kCdfShape +0 +347, WebRtcIsacfix_kCdfShape +0 +354,
    WebRtcIsacfix_kCdfShape +0 +360, WebRtcIsacfix_kCdfShape +0 +368, WebRtcIsacfix_kCdfShape +0 +378,
    WebRtcIsacfix_kCdfShape +0 +388, WebRtcIsacfix_kCdfShape +0 +400, WebRtcIsacfix_kCdfShape +0 +418,
    WebRtcIsacfix_kCdfShape +0 +445, WebRtcIsacfix_kCdfShape +0 +447, WebRtcIsacfix_kCdfShape +0 +451,
    WebRtcIsacfix_kCdfShape +0 +455, WebRtcIsacfix_kCdfShape +0 +461, WebRtcIsacfix_kCdfShape +0 +468,
    WebRtcIsacfix_kCdfShape +0 +474, WebRtcIsacfix_kCdfShape +0 +480, WebRtcIsacfix_kCdfShape +0 +486,
    WebRtcIsacfix_kCdfShape +0 +495, WebRtcIsacfix_kCdfShape +0 +505, WebRtcIsacfix_kCdfShape +0 +516,
    WebRtcIsacfix_kCdfShape +0 +528, WebRtcIsacfix_kCdfShape +0 +543, WebRtcIsacfix_kCdfShape +0 +564,
    WebRtcIsacfix_kCdfShape +0 +583, WebRtcIsacfix_kCdfShape +0 +608, WebRtcIsacfix_kCdfShape +0 +635
  },
  { WebRtcIsacfix_kCdfShape +686 +0,   WebRtcIsacfix_kCdfShape +686 +2,   WebRtcIsacfix_kCdfShape +686 +4,
    WebRtcIsacfix_kCdfShape +686 +6,   WebRtcIsacfix_kCdfShape +686 +8,   WebRtcIsacfix_kCdfShape +686 +11,
    WebRtcIsacfix_kCdfShape +686 +13,  WebRtcIsacfix_kCdfShape +686 +15,  WebRtcIsacfix_kCdfShape +686 +17,
    WebRtcIsacfix_kCdfShape +686 +20,  WebRtcIsacfix_kCdfShape +686 +23,  WebRtcIsacfix_kCdfShape +686 +27,
    WebRtcIsacfix_kCdfShape +686 +31,  WebRtcIsacfix_kCdfShape +686 +35,  WebRtcIsacfix_kCdfShape +686 +40,
    WebRtcIsacfix_kCdfShape +686 +44,  WebRtcIsacfix_kCdfShape +686 +50,  WebRtcIsacfix_kCdfShape +686 +56,
    WebRtcIsacfix_kCdfShape +686 +63,  WebRtcIsacfix_kCdfShape +686 +65,  WebRtcIsacfix_kCdfShape +686 +67,
    WebRtcIsacfix_kCdfShape +686 +69,  WebRtcIsacfix_kCdfShape +686 +71,  WebRtcIsacfix_kCdfShape +686 +73,
    WebRtcIsacfix_kCdfShape +686 +75,  WebRtcIsacfix_kCdfShape +686 +77,  WebRtcIsacfix_kCdfShape +686 +79,
    WebRtcIsacfix_kCdfShape +686 +82,  WebRtcIsacfix_kCdfShape +686 +85,  WebRtcIsacfix_kCdfShape +686 +89,
    WebRtcIsacfix_kCdfShape +686 +93,  WebRtcIsacfix_kCdfShape +686 +97,  WebRtcIsacfix_kCdfShape +686 +102,
    WebRtcIsacfix_kCdfShape +686 +106, WebRtcIsacfix_kCdfShape +686 +112, WebRtcIsacfix_kCdfShape +686 +119,
    WebRtcIsacfix_kCdfShape +686 +127, WebRtcIsacfix_kCdfShape +686 +129, WebRtcIsacfix_kCdfShape +686 +131,
    WebRtcIsacfix_kCdfShape +686 +133, WebRtcIsacfix_kCdfShape +686 +135, WebRtcIsacfix_kCdfShape +686 +137,
    WebRtcIsacfix_kCdfShape +686 +139, WebRtcIsacfix_kCdfShape +686 +142, WebRtcIsacfix_kCdfShape +686 +146,
    WebRtcIsacfix_kCdfShape +686 +150, WebRtcIsacfix_kCdfShape +686 +154, WebRtcIsacfix_kCdfShape +686 +158,
    WebRtcIsacfix_kCdfShape +686 +162, WebRtcIsacfix_kCdfShape +686 +167, WebRtcIsacfix_kCdfShape +686 +173,
    WebRtcIsacfix_kCdfShape +686 +179, WebRtcIsacfix_kCdfShape +686 +186, WebRtcIsacfix_kCdfShape +686 +194,
    WebRtcIsacfix_kCdfShape +686 +205, WebRtcIsacfix_kCdfShape +686 +207, WebRtcIsacfix_kCdfShape +686 +209,
    WebRtcIsacfix_kCdfShape +686 +211, WebRtcIsacfix_kCdfShape +686 +214, WebRtcIsacfix_kCdfShape +686 +218,
    WebRtcIsacfix_kCdfShape +686 +222, WebRtcIsacfix_kCdfShape +686 +226, WebRtcIsacfix_kCdfShape +686 +230,
    WebRtcIsacfix_kCdfShape +686 +234, WebRtcIsacfix_kCdfShape +686 +238, WebRtcIsacfix_kCdfShape +686 +242,
    WebRtcIsacfix_kCdfShape +686 +247, WebRtcIsacfix_kCdfShape +686 +253, WebRtcIsacfix_kCdfShape +686 +262,
    WebRtcIsacfix_kCdfShape +686 +269, WebRtcIsacfix_kCdfShape +686 +278, WebRtcIsacfix_kCdfShape +686 +289,
    WebRtcIsacfix_kCdfShape +686 +305, WebRtcIsacfix_kCdfShape +686 +307, WebRtcIsacfix_kCdfShape +686 +309,
    WebRtcIsacfix_kCdfShape +686 +311, WebRtcIsacfix_kCdfShape +686 +315, WebRtcIsacfix_kCdfShape +686 +319,
    WebRtcIsacfix_kCdfShape +686 +323, WebRtcIsacfix_kCdfShape +686 +327, WebRtcIsacfix_kCdfShape +686 +331,
    WebRtcIsacfix_kCdfShape +686 +335, WebRtcIsacfix_kCdfShape +686 +340, WebRtcIsacfix_kCdfShape +686 +346,
    WebRtcIsacfix_kCdfShape +686 +354, WebRtcIsacfix_kCdfShape +686 +362, WebRtcIsacfix_kCdfShape +686 +374,
    WebRtcIsacfix_kCdfShape +686 +384, WebRtcIsacfix_kCdfShape +686 +396, WebRtcIsacfix_kCdfShape +686 +413,
    WebRtcIsacfix_kCdfShape +686 +439, WebRtcIsacfix_kCdfShape +686 +442, WebRtcIsacfix_kCdfShape +686 +446,
    WebRtcIsacfix_kCdfShape +686 +450, WebRtcIsacfix_kCdfShape +686 +455, WebRtcIsacfix_kCdfShape +686 +461,
    WebRtcIsacfix_kCdfShape +686 +468, WebRtcIsacfix_kCdfShape +686 +475, WebRtcIsacfix_kCdfShape +686 +481,
    WebRtcIsacfix_kCdfShape +686 +489, WebRtcIsacfix_kCdfShape +686 +498, WebRtcIsacfix_kCdfShape +686 +508,
    WebRtcIsacfix_kCdfShape +686 +522, WebRtcIsacfix_kCdfShape +686 +534, WebRtcIsacfix_kCdfShape +686 +554,
    WebRtcIsacfix_kCdfShape +686 +577, WebRtcIsacfix_kCdfShape +686 +602, WebRtcIsacfix_kCdfShape +686 +631
  },
  { WebRtcIsacfix_kCdfShape +1368 +0,   WebRtcIsacfix_kCdfShape +1368 +2,   WebRtcIsacfix_kCdfShape +1368 +4,
    WebRtcIsacfix_kCdfShape +1368 +6,   WebRtcIsacfix_kCdfShape +1368 +8,   WebRtcIsacfix_kCdfShape +1368 +10,
    WebRtcIsacfix_kCdfShape +1368 +12,  WebRtcIsacfix_kCdfShape +1368 +14,  WebRtcIsacfix_kCdfShape +1368 +16,
    WebRtcIsacfix_kCdfShape +1368 +20,  WebRtcIsacfix_kCdfShape +1368 +24,  WebRtcIsacfix_kCdfShape +1368 +28,
    WebRtcIsacfix_kCdfShape +1368 +32,  WebRtcIsacfix_kCdfShape +1368 +36,  WebRtcIsacfix_kCdfShape +1368 +40,
    WebRtcIsacfix_kCdfShape +1368 +44,  WebRtcIsacfix_kCdfShape +1368 +50,  WebRtcIsacfix_kCdfShape +1368 +57,
    WebRtcIsacfix_kCdfShape +1368 +65,  WebRtcIsacfix_kCdfShape +1368 +67,  WebRtcIsacfix_kCdfShape +1368 +69,
    WebRtcIsacfix_kCdfShape +1368 +71,  WebRtcIsacfix_kCdfShape +1368 +73,  WebRtcIsacfix_kCdfShape +1368 +75,
    WebRtcIsacfix_kCdfShape +1368 +77,  WebRtcIsacfix_kCdfShape +1368 +79,  WebRtcIsacfix_kCdfShape +1368 +81,
    WebRtcIsacfix_kCdfShape +1368 +85,  WebRtcIsacfix_kCdfShape +1368 +89,  WebRtcIsacfix_kCdfShape +1368 +93,
    WebRtcIsacfix_kCdfShape +1368 +97,  WebRtcIsacfix_kCdfShape +1368 +101, WebRtcIsacfix_kCdfShape +1368 +105,
    WebRtcIsacfix_kCdfShape +1368 +110, WebRtcIsacfix_kCdfShape +1368 +116, WebRtcIsacfix_kCdfShape +1368 +123,
    WebRtcIsacfix_kCdfShape +1368 +132, WebRtcIsacfix_kCdfShape +1368 +134, WebRtcIsacfix_kCdfShape +1368 +136,
    WebRtcIsacfix_kCdfShape +1368 +138, WebRtcIsacfix_kCdfShape +1368 +141, WebRtcIsacfix_kCdfShape +1368 +143,
    WebRtcIsacfix_kCdfShape +1368 +146, WebRtcIsacfix_kCdfShape +1368 +150, WebRtcIsacfix_kCdfShape +1368 +154,
    WebRtcIsacfix_kCdfShape +1368 +158, WebRtcIsacfix_kCdfShape +1368 +162, WebRtcIsacfix_kCdfShape +1368 +166,
    WebRtcIsacfix_kCdfShape +1368 +170, WebRtcIsacfix_kCdfShape +1368 +174, WebRtcIsacfix_kCdfShape +1368 +179,
    WebRtcIsacfix_kCdfShape +1368 +185, WebRtcIsacfix_kCdfShape +1368 +193, WebRtcIsacfix_kCdfShape +1368 +203,
    WebRtcIsacfix_kCdfShape +1368 +214, WebRtcIsacfix_kCdfShape +1368 +216, WebRtcIsacfix_kCdfShape +1368 +218,
    WebRtcIsacfix_kCdfShape +1368 +220, WebRtcIsacfix_kCdfShape +1368 +224, WebRtcIsacfix_kCdfShape +1368 +227,
    WebRtcIsacfix_kCdfShape +1368 +231, WebRtcIsacfix_kCdfShape +1368 +235, WebRtcIsacfix_kCdfShape +1368 +239,
    WebRtcIsacfix_kCdfShape +1368 +243, WebRtcIsacfix_kCdfShape +1368 +247, WebRtcIsacfix_kCdfShape +1368 +251,
    WebRtcIsacfix_kCdfShape +1368 +256, WebRtcIsacfix_kCdfShape +1368 +262, WebRtcIsacfix_kCdfShape +1368 +269,
    WebRtcIsacfix_kCdfShape +1368 +277, WebRtcIsacfix_kCdfShape +1368 +286, WebRtcIsacfix_kCdfShape +1368 +297,
    WebRtcIsacfix_kCdfShape +1368 +315, WebRtcIsacfix_kCdfShape +1368 +317, WebRtcIsacfix_kCdfShape +1368 +319,
    WebRtcIsacfix_kCdfShape +1368 +323, WebRtcIsacfix_kCdfShape +1368 +327, WebRtcIsacfix_kCdfShape +1368 +331,
    WebRtcIsacfix_kCdfShape +1368 +335, WebRtcIsacfix_kCdfShape +1368 +339, WebRtcIsacfix_kCdfShape +1368 +343,
    WebRtcIsacfix_kCdfShape +1368 +349, WebRtcIsacfix_kCdfShape +1368 +355, WebRtcIsacfix_kCdfShape +1368 +361,
    WebRtcIsacfix_kCdfShape +1368 +368, WebRtcIsacfix_kCdfShape +1368 +376, WebRtcIsacfix_kCdfShape +1368 +385,
    WebRtcIsacfix_kCdfShape +1368 +397, WebRtcIsacfix_kCdfShape +1368 +411, WebRtcIsacfix_kCdfShape +1368 +429,
    WebRtcIsacfix_kCdfShape +1368 +456, WebRtcIsacfix_kCdfShape +1368 +459, WebRtcIsacfix_kCdfShape +1368 +463,
    WebRtcIsacfix_kCdfShape +1368 +467, WebRtcIsacfix_kCdfShape +1368 +473, WebRtcIsacfix_kCdfShape +1368 +478,
    WebRtcIsacfix_kCdfShape +1368 +485, WebRtcIsacfix_kCdfShape +1368 +491, WebRtcIsacfix_kCdfShape +1368 +497,
    WebRtcIsacfix_kCdfShape +1368 +505, WebRtcIsacfix_kCdfShape +1368 +514, WebRtcIsacfix_kCdfShape +1368 +523,
    WebRtcIsacfix_kCdfShape +1368 +535, WebRtcIsacfix_kCdfShape +1368 +548, WebRtcIsacfix_kCdfShape +1368 +565,
    WebRtcIsacfix_kCdfShape +1368 +585, WebRtcIsacfix_kCdfShape +1368 +611, WebRtcIsacfix_kCdfShape +1368 +640
  }
};

/* code length for all coefficients using different models */

const int16_t WebRtcIsacfix_kCodeLenGainQ11[392] = {
  25189, 16036,  8717,   358,  8757, 15706, 21456, 24397, 18502, 17559
  , 13794, 11088,  7480,   873,  6603, 11636, 14627, 16805, 19132, 26624
  , 26624, 19408, 13751,  7280,   583,  7591, 15178, 23773, 28672, 25189
  , 19045, 16442, 13412, 10397,  5893,  1338,  6376,  9992, 12074, 13853
  , 15781, 19821, 22819, 28672, 28672, 25189, 19858, 15781, 11262,  5477
  ,  1298,  5632, 11814, 17234, 22020, 28672, 19677, 18125, 16587, 14521
  , 13032, 11196,  9249,  5411,  2495,  4994,  7975, 10234, 12308, 13892
  , 15148, 17944, 21725, 23917, 25189, 19539, 16293, 11531,  7808,  4475
  ,  2739,  4872,  8089, 11314, 14992, 18105, 23257, 26624, 25189, 23257
  , 23257, 20982, 18697, 18023, 16338, 16036, 14539, 13695, 13146, 11763
  , 10754,  9074,  7260,  5584,  4430,  5553,  6848,  8344, 10141, 11636
  , 12535, 13416, 14342, 15477, 17296, 19282, 22349, 23773, 28672, 28672
  , 26624, 23773, 21456, 18023, 15118, 13362, 11212,  9293,  8043,  6985
  ,  5908,  5721,  5853,  6518,  7316,  8360,  9716, 11289, 12912, 14652
  , 16969, 19858, 23773, 26624, 28013, 30720, 30720, 28672, 25426, 23141
  , 25426, 23773, 20720, 19408, 18697, 19282, 16859, 16338, 16026, 15377
  , 15021, 14319, 14251, 13937, 13260, 13017, 12332, 11703, 11430, 10359
  , 10128,  9405,  8757,  8223,  7974,  7859,  7646,  7673,  7997,  8580
  ,  8880,  9061,  9866, 10397, 11358, 12200, 13244, 14157, 15021, 16026
  , 16490, 18697, 18479, 20011, 19677, 20720, 24576, 26276, 30720, 30720
  , 28672, 30720, 24068, 25189, 22437, 20345, 18479, 16396, 16026, 14928
  , 13877, 13450, 12696, 12766, 11626, 11098, 10159,  9998,  9437,  9275
  ,  8783,  8552,  8629,  8488,  8522,  8454,  8571,  8775,  8915,  9427
  ,  9483,  9851, 10260, 10933, 11131, 11974, 12560, 13833, 15080, 16304
  , 17491, 19017, 18697, 19408, 22020, 25189, 25426, 22819, 26276, 30720
  , 30720, 30720, 30720, 30720, 30720, 28672, 30720, 30720, 30720, 30720
  , 28013, 25426, 24397, 23773, 25189, 26624, 25189, 22437, 21725, 20011
  , 20527, 20720, 20771, 22020, 22020, 19858, 19408, 19972, 17866, 17360
  , 17791, 17219, 16805, 16927, 16067, 16162, 15661, 15178, 15021, 15209
  , 14845, 14570, 14490, 14490, 13733, 13617, 13794, 13577, 13312, 12824
  , 13032, 12683, 12189, 12469, 12109, 11940, 11636, 11617, 11932, 12294
  , 11578, 11775, 12039, 11654, 11560, 11439, 11909, 11421, 12029, 11513
  , 11773, 11899, 11560, 11805, 11476, 11664, 11963, 11647, 11754, 11963
  , 11703, 12211, 11932, 12074, 12469, 12535, 12560, 12912, 12783, 12866
  , 12884, 13378, 13957, 13775, 13635, 14019, 14545, 15240, 15520, 15554
  , 15697, 16490, 16396, 17281, 16599, 16969, 17963, 16859, 16983, 16805
  , 17099, 18210, 17219, 17646, 17700, 17646, 18297, 17425, 18479, 17791
  , 17718, 19282, 18672, 20173, 20982, 21725, 21456, 23773, 23257, 25189
  , 30720, 30720, 25189, 26624, 30720, 30720, 30720, 30720, 28672, 26276
  , 30720, 30720
};

const int16_t WebRtcIsacfix_kCodeLenShapeQ11[578] = {
  0,     0,     0,     0,     0,     0,     0,     0,     0, 28672
  ,     0, 26624,     1, 23773, 22819,     4, 20982, 18598,    10, 19282
  , 16587,    22, 16442, 26624, 13126,    60, 14245, 26624, 26624, 12736
  ,    79, 12912, 25189, 22819,  9563,   249,  9474, 22349, 28672, 23257
  , 17944,  7980,   434,  8181, 16431, 26624,     0,     0,     0,     0
  , 28672,     0,     0,     0,     0,     0, 28672,     0, 22437,     3
  , 22437, 20982,     5, 20982, 16442,    22, 16752, 13814,    49, 14646
  , 11645,   116, 11734, 26624, 28672, 10613,   158, 11010, 24397, 19539
  ,  8046,   453,  7709, 19017, 28672, 23257, 15110,  6770,   758,  6523
  , 14108, 24397, 28672,     0,     0,     0,     0, 28672,     0, 28672
  ,     0, 26624,     1, 28672, 28672,     1, 26624, 24397,     2, 23257
  , 21725,     4, 20982, 17158,    18, 17281, 28672, 15178,    35, 15209
  , 12343,    92, 12320, 26624, 10344,   189, 10217, 30720, 22020,  9033
  ,   322,  8549, 23773, 28672, 30720, 20622,  7666,   473,  7806, 20527
  , 24397, 14135,  5995,   960,  6018, 14872, 23773, 26624, 20928, 16293
  , 10636,  4926,  1588,  5256, 11088, 18043, 25189,     0,     0,     0
  ,     0, 24397,     1, 25189, 20720,     5, 21456, 21209,     3, 25189
  , 20982,     5, 21456, 15818,    30, 15410, 13794,    60, 13416, 28672
  , 11162,   142, 11025,  9337,   231, 10094, 23773,  8338,   405,  7930
  , 26624, 19677,  6787,   613,  7318, 19161, 28672, 16442,  6319,   932
  ,  5748, 15312, 25189, 28672, 28672, 28672, 13998,  5513,  1263,  5146
  , 14024, 24397, 22819, 15818,  9460,  4447,  2122,  4681,  9970, 15945
  , 22349, 28672, 30720, 22622, 19017, 14872, 10689,  7405,  4473,  2983
  ,  4783,  7894, 11186, 14964, 18210, 24397,     0,     0, 30720,     0
  , 30720, 21456,     3, 23773, 14964,    39, 14757, 14179,    53, 13751
  , 14928,    36, 15272, 12430,    79, 13228,  9135,   285,  9077, 28672
  , 28672,  8377,   403,  7919, 26624, 28672, 23257,  7068,   560,  7473
  , 20345, 19677,  6770,   720,  6464, 18697, 25189, 16249,  5779,  1087
  ,  5494, 15209, 22819, 30720, 20622, 12601,  5240,  1419,  5091, 12095
  , 19408, 26624, 22819, 16805, 10683,  4812,  2056,  4293,  9836, 16026
  , 24397, 25189, 18409, 13833,  8681,  4503,  2653,  4220,  8329, 13853
  , 19132, 26624, 25189, 20771, 17219, 12630,  9520,  6733,  4565,  3657
  ,  4817,  7069, 10058, 13212, 16805, 21209, 26624, 26276, 28672, 28672
  , 26276, 23257, 20173, 19282, 16538, 15051, 12811, 10754,  9267,  7547
  ,  6270,  5407,  5214,  6057,  7054,  8226,  9488, 10806, 12793, 14442
  , 16442, 19677, 22099, 26276, 28672,     0, 30720,     0, 30720, 11920
  ,    56, 20720, 30720,  6766,   355, 13130, 30720, 30720, 22180,  5589
  ,   736,  7902, 26624, 30720,  7634,   354,  9721, 30720, 30720,  9027
  ,   246, 10117, 30720, 30720,  9630,   453,  6709, 23257, 30720, 25683
  , 14228,  6127,  1271,  4615, 15178, 30720, 30720, 23504, 12382,  5739
  ,  2015,  3492, 10560, 22020, 26624, 30720, 30720, 23257, 13192,  4873
  ,  1527,  5001, 12445, 22020, 30720, 30720, 30720, 30720, 19344, 10761
  ,  4051,  1927,  5281, 10594, 17866, 28672, 30720, 30720, 30720, 21869
  , 15554, 10060,  5979,  2710,  3085,  7889, 14646, 21725, 28672, 30720
  , 30720, 30720, 30720, 30720, 30720, 30720, 22719, 17425, 13212,  8083
  ,  4439,  2820,  4305,  8136, 12988, 17425, 21151, 28672, 28672, 30720
  , 30720, 30720, 28672, 20527, 19282, 14412, 10513,  7407,  5079,  3744
  ,  4115,  6308,  9621, 13599, 17040, 22349, 28672, 30720, 30720, 30720
  , 30720, 30720, 30720, 29522, 19282, 14545, 11485,  9093,  6760,  5262
  ,  4672,  4970,  6005,  7852,  9732, 12343, 14672, 19161, 22819, 25189
  , 30720, 30720, 28672, 30720, 30720, 20720, 18125, 14388, 12007,  9825
  ,  8092,  7064,  6069,  5903,  5932,  6359,  7169,  8310,  9324, 10711
  , 11867, 13096, 14157, 16338, 17040, 19161, 21725, 23773, 30720, 30720
  , 26276, 25426, 24397, 28672, 28672, 23257, 22020, 22349, 18297, 17646
  , 16983, 16431, 16162, 15021, 15178, 13751, 12142, 10895, 10193,  9632
  ,  9086,  8896,  8823,  8735,  8591,  8754,  8649,  8361,  8329,  8522
  ,  8373,  8739,  8993,  9657, 10454, 11279, 11899, 12614, 14024, 14273
  , 15477, 15240, 16649, 17866, 18697, 21151, 22099, 0
  // The final 0 was added due to http://bugs.webrtc.org/10584.
};

/* left KLT transforms */
const int16_t WebRtcIsacfix_kT1GainQ15[3][4] = {
  { -26130, 19773, 19773, 26130 },
  { -26664, 19046, 19046, 26664 },
  { -23538, 22797, 22797, 23538 }
};



const int16_t WebRtcIsacfix_kT1ShapeQ15[3][324] = {
  { 52,16,168,7,439,-138,-89,306,671,882,
    157,1301,291,1598,-3571,-1943,-1119,32404,96,-12,
    379,-64,-307,345,-836,539,1045,2541,-2865,-992,
    1683,-4717,5808,7427,30599,2319,183,-73,451,481,
    933,-198,781,-397,1244,-777,3690,-2414,149,-1356,
    -2593,-31140,8289,-1737,-202,-14,-214,360,501,450,
    -245,-7,797,3638,-2804,3042,-337,22137,-22103,2264,
    6838,-3381,305,172,263,-195,-355,351,179,513,
    2234,3343,5509,7531,19075,-17740,-16836,2244,-629,-1505,
    -153,108,124,-324,2694,-124,1492,-850,5347,4285,
    7439,-10229,-22822,-12467,-12891,3645,822,-232,131,13,
    374,565,536,4681,1294,-1935,1926,-5734,-10643,26462,
    -12480,-5589,-1038,-2468,964,-704,-247,-106,186,-558,
    -4050,3760,2972,2141,-7393,6294,26740,11991,-3251,5461,
    5341,1574,2208,-51,-552,-297,-753,-154,2068,-5371,
    3578,4106,28043,-10533,8041,2353,2389,4609,3410,1906,
    351,-249,18,-15,1117,539,2870,9084,17585,-24528,
    -366,-6490,2009,-3170,2942,1116,-232,1672,1065,606,
    -399,-388,-518,38,3728,28948,-11936,4543,4104,-4441,
    1545,-4044,1485,622,-68,186,-473,135,-280,125,
    -546,-1813,6989,6606,23711,19376,-2636,2870,-4553,-1687,
    878,-375,205,-208,-409,-108,-200,-45,-1670,-337,
    8213,-5524,-2334,5240,-12939,-26205,5937,-1582,-592,-959,
    -5374,2449,3400,559,349,-492,668,12379,-27684,3419,
    5117,4415,-297,-8270,-1252,-3490,-1272,-1199,-3159,191,
    630,488,-797,-3071,12912,-27783,-10249,1047,647,619,
    111,-3722,-915,-1055,-502,5,-1384,-306,221,68,
    5219,13173,-26474,-11663,-5626,927,806,-1127,236,-589,
    -522,-230,-312,-315,-428,-573,426,192,-11830,-26883,
    -14121,-2785,-1429,-109,410,-832,-302,539,-459,104,
    1,-530,-202,-289,153,116,30082,-12944,-671,20,
    649,98,103,215,234,0,280,-51,-169,298,
    31,230,-73,-51
  },
  { -154,-7,-192,61,-739,-389,-947,-162,-60,94,
    511,-716,1520,-1428,4168,-2214,1816,32270,-123,-77,
    -199,-99,-42,-588,203,-240,-930,-35,1580,234,
    3206,-5507,-1495,-10946,30000,-2667,-136,-176,-240,-175,
    -204,-661,-1796,-1039,-1271,498,3143,734,2663,2699,
    -8127,29333,10495,2356,-72,113,-91,118,-2840,-723,
    -1733,-1158,-389,-2116,-3054,-3,-5179,8071,29546,6308,
    5657,-3178,-186,-294,-473,-635,1213,-983,-1437,-1715,
    -1094,1280,-92,-9573,948,29576,-7060,-5921,2954,1349,
    -337,-108,-1099,962,418,-413,-1149,-334,1241,3975,
    -6825,26725,-14377,7051,-4772,-1707,2335,2008,-150,570,
    1371,42,-1649,-619,2039,3369,-1225,1583,-2755,-15207,
    -27504,-4855,-4304,1495,2733,1324,15,-448,403,353,
    3016,-1242,2338,2673,2064,-7496,-30447,-3686,5833,-1301,
    -2455,2122,1519,608,43,-653,773,-3072,912,-1537,
    4505,10284,30237,1549,3200,-691,205,1702,658,1014,
    1499,148,79,-322,-1162,-4639,-813,7536,3204,29109,
    -10747,-26,1611,2286,2114,2561,1022,372,348,207,
    1062,-1088,-443,-9849,2381,5671,29097,-7612,-2927,3853,
    194,1155,275,1438,1438,1312,581,888,-784,906,
    112,-11103,25104,14438,-9311,-3068,1210,368,370,-940,
    -2434,-1148,1925,392,657,258,-526,1475,-2281,-4265,
    -1880,1534,2185,-1472,959,-30934,6306,3114,-4109,1768,
    -2612,-703,45,644,2185,2033,5670,7211,19114,-22427,
    6432,5150,-4090,-2694,3860,1245,-596,293,1829,369,
    -319,229,-3256,2170,-6374,-26216,-4570,-16053,-5766,-262,
    -2006,2873,-1477,147,378,-1544,-344,-544,-985,-481,
    4210,4542,30757,-7291,-4863,1529,-2079,-628,-603,-783,
    -408,1646,697,808,-620,-292,181,158,-13313,-29173,
    5984,-1262,859,-1776,-558,-24,-883,-1421,739,210,
    -531,-285,131,-160,-246,-56,29345,-13706,-2859,-2966,
    -300,-970,-2382,-268,-103,-636,-12,-62,-691,-253,
    -147,-127,27,66
  },
  { 55,-212,-198,489,-274,81,682,399,328,-934,
    -389,-37,1357,-3632,5276,6581,-9493,-29921,29,-45,
    2,190,172,-15,311,-130,-1085,-25,324,-684,
    3223,-6580,4485,-5280,-29521,9933,82,-320,-530,229,
    -705,-533,-414,848,-1842,-4473,1390,-857,6717,-6692,
    4648,29397,576,8339,-68,-85,238,-330,264,-1012,
    -381,-203,-3384,-3329,3906,6810,3790,-6250,28312,-8078,
    8089,1565,160,-569,-612,-613,-1063,-1928,-1125,3421,
    -7481,-7484,4942,-6984,4330,-25591,-10574,-6982,5682,-1781,
    -308,89,178,-1715,-420,-3530,-5776,1219,-8617,-7137,
    7015,4981,24875,12657,-5408,-3356,-785,-1972,326,-858,
    -506,-3382,-986,-6258,-2259,4015,-8374,-10482,3127,23826,
    -14126,-514,-5417,2178,-2912,-17,-587,80,67,-5881,
    -1702,-5351,-4481,398,-10156,-225,20727,-15460,-11603,7752,
    3660,1714,-2001,-359,499,-527,-1225,-7820,-1297,-6326,
    -8526,7900,-18328,13311,-17488,-2926,-196,-17,2281,873,
    480,-160,-624,471,780,-8729,1707,-14262,-20647,1721,
    18590,-2206,-1214,-1066,312,-2602,783,-412,-113,49,
    -119,1305,-2371,-15132,-1833,-18252,20295,-8316,2227,341,
    -2074,-702,3082,-262,-465,-198,430,30,-70,-788,
    2342,-25132,-4863,19783,-484,2137,2811,-1906,799,1586,
    962,-734,-191,-30,-129,-93,-1126,1729,5860,-2030,
    8953,603,-3338,-10869,-1144,22070,12130,10513,3191,-6881,
    -3514,2090,711,-666,1843,-5997,-5681,2921,-17641,-2801,
    4969,18590,7169,12214,8587,4405,3008,-1074,-371,-77,
    253,331,-5611,5014,13152,-1985,18483,-1696,8043,20463,
    2381,-393,1688,-1205,618,1220,457,248,-83,176,
    7920,-13676,-22139,-3038,17402,2036,844,3258,994,719,
    2087,-44,426,494,12,-91,46,5,-14204,22912,
    -18156,-361,442,2298,-829,2229,386,1433,1335,1323,
    55,-592,-139,49,-12,-57,27783,17134,350,-282,
    552,158,142,2488,465,329,1087,118,143,10,
    56,65,-15,-31
  }
};

/* right KLT transforms */
const int16_t WebRtcIsacfix_kT2GainQ15[3][36] = {
  {   4775, -14892,  20313, -17104,  10533,  -3613,  -6782,  16044,  -8889,
      -11019,  21330, -10720,  13193, -15678, -11101,  14461,  12250, -13096,
      -16951,   2167,  16066,  15569,   -702, -16754, -19195, -12823,  -4321,
      5128,    13348,  17825,  13232,  13404,  13494,  13490,  13383,  13261
  },
  {  -3725,  11408, -18493,  20031, -13097,   3865,   9344, -19294,  10740,
     8856, -18432,   8982,  13975, -14444, -11930,  11774,  14285, -13594,
     -16323,     -4,  16340,  15609,    359, -17220, -18401, -13471,  -4643,
     5225,  13375,  18053,  13124,  13463,  13621,  13583,  13393,  13072
  },
  {  -3513,  11402, -17883,  19504, -14399,   4885,   8702, -19513,  12046,
     8533, -18110,   8447,  12778, -14838, -12444,  13177,  14107, -12759,
     -17268,    914,  15822,  15661,    838, -16686, -18907, -12936,  -4820,
     4175,  12398,  18830,  12913,  13215,  13433,  13572,  13601,  13518
  }
};

const int16_t WebRtcIsacfix_kT2ShapeQ15[3][36] = {
  {   4400, -11512,  17205, -19470,  14770,  -5345,   9784, -19222,  11228,
      6842, -18371,   9909,  14191, -13496, -11563,  14015,  11827, -14839,
      -15439,    948,  17802,  14827,  -2053, -17132,  18723,  14516,   4135,
      -6822, -13869, -16016,  12975,  13341,  13563,  13603,  13478,  13296
  },
  {   5420, -14215,  19060, -18073,  11709,  -3911,   9645, -18335,   7717,
      10842, -19283,   9777,  14898, -12555, -13661,  11668,  13520, -13733,
      -15936,  -1358,  15671,  16728,    328, -17100,  17527,  13973,   5587,
      -5194, -14165, -17677,  12970,  13446,  13693,  13660,  13462,  13015
  },
  {   4386, -12426,  18019, -18895,  13894,  -5034,   9713, -19270,  10283,
      8692, -18439,   9317,  13992, -13454, -13241,  12850,  13366, -13336,
      -16334,   -498,  15976,  16213,   -114, -16987,  18191,  13659,   4958,
      -5116, -13444, -18021,  12911,  13424,  13718,  13674,  13464,  13054
  }
};

/* means of log gains and LAR coefficients*/
const int16_t WebRtcIsacfix_kMeansGainQ8[3][12] = {
  { -1758, -1370, -1758, -1373, -1757, -1375,
    -1758, -1374, -1758, -1373, -1755, -1370
  },
  { -1569, -1224, -1569, -1225, -1569, -1227,
    -1569, -1226, -1567, -1225, -1565, -1224
  },
  { -1452,  -957, -1447,  -951, -1438,  -944,
    -1431,  -938, -1419,  -931, -1406,  -926
  }
};


const int32_t WebRtcIsacfix_kMeansShapeQ17[3][108] = {
  { -119581, 34418, -44193, 11112, -4428, 18906, 9222, 8068, 1953, 5425,
    1871, 1689, 109933, 33751, 10471, -2566, 1090, 2320, -119219, 33728,
    -43759, 11450, -4870, 19117, 9174, 8037, 1972, 5331, 1872, 1843,
    109899, 34301, 10629, -2316, 1272, 2562, -118608, 32318, -44012, 11591,
    -4914, 18932, 9456, 8088, 1900, 5419, 1723, 1853, 109963, 35059,
    10745, -2335, 1161, 2520, -119174, 32107, -44462, 11635, -4694, 18611,
    9757, 8108, 1969, 5486, 1673, 1777, 109636, 34907, 10643, -2406,
    1034, 2420, -118597, 32320, -44590, 10854, -4569, 18821, 9701, 7866,
    2003, 5577, 1732, 1626, 109913, 34448, 10714, -2752, 990, 2228,
    -118138, 32996, -44352, 10334, -3772, 18488, 9464, 7865, 2208, 5540,
    1745, 1664, 109880, 33381, 10640, -2779, 980, 2054
  },
  { -146328, 46370, 1047, 26431, 10035, 13933, 6415, 14359, -2368, 6661,
    2269, 1764, 96623, 7802, 4163, 10742, 1643, 2954, -146871, 46561, 1127,
    26225, 10113, 14096, 6771, 14323, -2037, 6788, 2297, 1761, 96324, 8382,
    4309, 10450, 1695, 3016, -146502, 46475, 1580, 26118, 10487, 14179, 6622,
    14439, -2034, 6757, 2342, 1761, 95869, 8966, 4347, 10358, 1999, 2855,
    -146958, 47717, 826, 25952, 10263, 14061, 5266, 13681, -2417, 6582, 2047,
    1608, 96257, 9107, 4452, 10301, 1792, 2676, -146992, 47123, 446, 25822,
    10405, 14292, 5140, 13804, -2403, 6496, 1834, 1735, 97489, 9253, 4414,
    10684, 1549, 2721, -145811, 46182, 901, 26482, 10241, 14524, 6075, 14514,
    -2147, 6691, 2196, 1899, 97011, 8178, 4102, 10758, 1638, 2869
  },
  { -166617, 46969, -43908, 17726, 6330, 25615, 6913, 5450, -2301, 1984,
    507, 2883, 149998, 28709, 19333, 16703, 11093, 8965, -168254, 46604,
    -44315, 17862, 6474, 25746, 7018, 5373, -2343, 1930, 513, 2819, 150391,
    28627, 19194, 16678, 10998, 8929, -169093, 46084, -44767, 17427, 6401,
    25674, 7147, 5472, -2336, 1820, 491, 2802, 149860, 28430, 19064, 16524,
    10898, 8875, -170205, 46189, -44877, 17403, 6190, 25209, 7035, 5673, -2173,
    1894, 574, 2756, 148830, 28230, 18819, 16418, 10789, 8811, -171263, 45045,
    -44834, 16858, 6103, 24726, 7014, 5713, -2103, 1877, 518, 2729, 147073,
    27744, 18629, 16277, 10690, 8703, -171720, 44153, -45062, 15951, 5872,
    24429, 7044, 5585, -2082, 1807, 519, 2769, 144791, 27402, 18490, 16126,
    10548, 8635
  }
};
