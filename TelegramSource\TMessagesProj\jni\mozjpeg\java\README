TurboJPEG Java Wrapper
======================

The TurboJPEG shared library can optionally be built with a Java Native
Interface wrapper, which allows the library to be loaded and used directly from
Java applications.  The Java front end for this is defined in several classes
located under org/libjpegturbo/turbojpeg.  The source code for these Java
classes is licensed under a BSD-style license, so the files can be incorporated
directly into both open source and proprietary projects without restriction.  A
Java archive (JAR) file containing these classes is also shipped with the
"official" distribution packages of libjpeg-turbo.

TJExample.java, which should also be located in the same directory as this
README file, demonstrates how to use the TurboJPEG Java API to compress and
decompress JPEG images in memory.


Performance Pitfalls
--------------------

The TurboJPEG Java API defines several convenience methods that can allocate
image buffers or instantiate classes to hold the result of compress,
decompress, or transform operations.  However, if you use these methods, then
be mindful of the amount of new data you are creating on the heap.  It may be
necessary to manually invoke the garbage collector to prevent heap exhaustion
or to prevent performance degradation.  Background garbage collection can kill
performance, particularly in a multi-threaded environment (Java pauses all
threads when the GC runs.)

The TurboJPEG Java API always gives you the option of pre-allocating your own
source and destination buffers, which allows you to re-use those buffers for
compressing/decompressing multiple images.  If the image sequence you are
compressing or decompressing consists of images of the same size, then
pre-allocating the buffers is recommended.


Installation Directory
----------------------

The TurboJPEG Java Wrapper will look for the TurboJPEG JNI library
(libturbojpeg.so, libturbojpeg.jnilib, or turbojpeg.dll) in the system library
paths or in any paths specified in LD_LIBRARY_PATH (Un*x), DYLD_LIBRARY_PATH
(Mac), or PATH (Windows.)  Failing this, on Un*x and Mac systems, the wrapper
will look for the JNI library under the library directory configured when
libjpeg-turbo was built.  If that library directory is
/opt/libjpeg-turbo/lib32, then /opt/libjpeg-turbo/lib64 is also searched, and
vice versa.

If you installed the JNI library into another directory, then you will need
to pass an argument of -Djava.library.path={path_to_JNI_library} to java, or
manipulate LD_LIBRARY_PATH, DYLD_LIBRARY_PATH, or PATH to include the directory
containing the JNI library.
