// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: debug.proto

#include "debug.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/io/zero_copy_stream_impl_lite.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace webrtc {
namespace audioproc {
PROTOBUF_CONSTEXPR Init::Init(
    ::_pbi::ConstantInitialized)
  : sample_rate_(0)
  , device_sample_rate_(0)
  , num_input_channels_(0)
  , num_output_channels_(0)
  , num_reverse_channels_(0)
  , reverse_sample_rate_(0)
  , output_sample_rate_(0)
  , reverse_output_sample_rate_(0)
  , timestamp_ms_(int64_t{0})
  , num_reverse_output_channels_(0){}
struct InitDefaultTypeInternal {
  PROTOBUF_CONSTEXPR InitDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~InitDefaultTypeInternal() {}
  union {
    Init _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 InitDefaultTypeInternal _Init_default_instance_;
PROTOBUF_CONSTEXPR ReverseStream::ReverseStream(
    ::_pbi::ConstantInitialized)
  : channel_()
  , data_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}){}
struct ReverseStreamDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ReverseStreamDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ReverseStreamDefaultTypeInternal() {}
  union {
    ReverseStream _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ReverseStreamDefaultTypeInternal _ReverseStream_default_instance_;
PROTOBUF_CONSTEXPR Stream::Stream(
    ::_pbi::ConstantInitialized)
  : input_channel_()
  , output_channel_()
  , input_data_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , output_data_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , delay_(0)
  , drift_(0)
  , applied_input_volume_(0)
  , keypress_(false){}
struct StreamDefaultTypeInternal {
  PROTOBUF_CONSTEXPR StreamDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~StreamDefaultTypeInternal() {}
  union {
    Stream _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 StreamDefaultTypeInternal _Stream_default_instance_;
PROTOBUF_CONSTEXPR Config::Config(
    ::_pbi::ConstantInitialized)
  : experiments_description_(&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{})
  , aec_enabled_(false)
  , aec_delay_agnostic_enabled_(false)
  , aec_drift_compensation_enabled_(false)
  , aec_extended_filter_enabled_(false)
  , aec_suppression_level_(0)
  , aecm_routing_mode_(0)
  , aecm_enabled_(false)
  , aecm_comfort_noise_enabled_(false)
  , agc_enabled_(false)
  , agc_limiter_enabled_(false)
  , agc_mode_(0)
  , noise_robust_agc_enabled_(false)
  , hpf_enabled_(false)
  , ns_enabled_(false)
  , transient_suppression_enabled_(false)
  , ns_level_(0)
  , pre_amplifier_enabled_(false)
  , pre_amplifier_fixed_gain_factor_(0){}
struct ConfigDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ConfigDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ConfigDefaultTypeInternal() {}
  union {
    Config _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ConfigDefaultTypeInternal _Config_default_instance_;
PROTOBUF_CONSTEXPR PlayoutAudioDeviceInfo::PlayoutAudioDeviceInfo(
    ::_pbi::ConstantInitialized)
  : id_(0)
  , max_volume_(0){}
struct PlayoutAudioDeviceInfoDefaultTypeInternal {
  PROTOBUF_CONSTEXPR PlayoutAudioDeviceInfoDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~PlayoutAudioDeviceInfoDefaultTypeInternal() {}
  union {
    PlayoutAudioDeviceInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 PlayoutAudioDeviceInfoDefaultTypeInternal _PlayoutAudioDeviceInfo_default_instance_;
PROTOBUF_CONSTEXPR RuntimeSetting::RuntimeSetting(
    ::_pbi::ConstantInitialized)
  : playout_audio_device_change_(nullptr)
  , capture_pre_gain_(0)
  , custom_render_processing_setting_(0)
  , capture_fixed_post_gain_(0)
  , playout_volume_change_(0)
  , capture_output_used_(false)
  , capture_post_gain_(0){}
struct RuntimeSettingDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RuntimeSettingDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RuntimeSettingDefaultTypeInternal() {}
  union {
    RuntimeSetting _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RuntimeSettingDefaultTypeInternal _RuntimeSetting_default_instance_;
PROTOBUF_CONSTEXPR Event::Event(
    ::_pbi::ConstantInitialized)
  : init_(nullptr)
  , reverse_stream_(nullptr)
  , stream_(nullptr)
  , config_(nullptr)
  , runtime_setting_(nullptr)
  , type_(0)
{}
struct EventDefaultTypeInternal {
  PROTOBUF_CONSTEXPR EventDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~EventDefaultTypeInternal() {}
  union {
    Event _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT_WITH_PTR PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 EventDefaultTypeInternal _Event_default_instance_;
}  // namespace audioproc
}  // namespace webrtc
namespace webrtc {
namespace audioproc {
bool Event_Type_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}

static ::PROTOBUF_NAMESPACE_ID::internal::ExplicitlyConstructed<std::string> Event_Type_strings[6] = {};

static const char Event_Type_names[] =
  "CONFIG"
  "INIT"
  "REVERSE_STREAM"
  "RUNTIME_SETTING"
  "STREAM"
  "UNKNOWN_EVENT";

static const ::PROTOBUF_NAMESPACE_ID::internal::EnumEntry Event_Type_entries[] = {
  { {Event_Type_names + 0, 6}, 3 },
  { {Event_Type_names + 6, 4}, 0 },
  { {Event_Type_names + 10, 14}, 1 },
  { {Event_Type_names + 24, 15}, 5 },
  { {Event_Type_names + 39, 6}, 2 },
  { {Event_Type_names + 45, 13}, 4 },
};

static const int Event_Type_entries_by_number[] = {
  1, // 0 -> INIT
  2, // 1 -> REVERSE_STREAM
  4, // 2 -> STREAM
  0, // 3 -> CONFIG
  5, // 4 -> UNKNOWN_EVENT
  3, // 5 -> RUNTIME_SETTING
};

const std::string& Event_Type_Name(
    Event_Type value) {
  static const bool dummy =
      ::PROTOBUF_NAMESPACE_ID::internal::InitializeEnumStrings(
          Event_Type_entries,
          Event_Type_entries_by_number,
          6, Event_Type_strings);
  (void) dummy;
  int idx = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumName(
      Event_Type_entries,
      Event_Type_entries_by_number,
      6, value);
  return idx == -1 ? ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString() :
                     Event_Type_strings[idx].get();
}
bool Event_Type_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Event_Type* value) {
  int int_value;
  bool success = ::PROTOBUF_NAMESPACE_ID::internal::LookUpEnumValue(
      Event_Type_entries, 6, name, &int_value);
  if (success) {
    *value = static_cast<Event_Type>(int_value);
  }
  return success;
}
#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr Event_Type Event::INIT;
constexpr Event_Type Event::REVERSE_STREAM;
constexpr Event_Type Event::STREAM;
constexpr Event_Type Event::CONFIG;
constexpr Event_Type Event::UNKNOWN_EVENT;
constexpr Event_Type Event::RUNTIME_SETTING;
constexpr Event_Type Event::Type_MIN;
constexpr Event_Type Event::Type_MAX;
constexpr int Event::Type_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))

// ===================================================================

class Init::_Internal {
 public:
  using HasBits = decltype(std::declval<Init>()._has_bits_);
  static void set_has_sample_rate(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_device_sample_rate(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_num_input_channels(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_num_output_channels(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_num_reverse_channels(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_reverse_sample_rate(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_output_sample_rate(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static void set_has_reverse_output_sample_rate(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static void set_has_num_reverse_output_channels(HasBits* has_bits) {
    (*has_bits)[0] |= 512u;
  }
  static void set_has_timestamp_ms(HasBits* has_bits) {
    (*has_bits)[0] |= 256u;
  }
};

Init::Init(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.audioproc.Init)
}
Init::Init(const Init& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ::memcpy(&sample_rate_, &from.sample_rate_,
    static_cast<size_t>(reinterpret_cast<char*>(&num_reverse_output_channels_) -
    reinterpret_cast<char*>(&sample_rate_)) + sizeof(num_reverse_output_channels_));
  // @@protoc_insertion_point(copy_constructor:webrtc.audioproc.Init)
}

inline void Init::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&sample_rate_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&num_reverse_output_channels_) -
    reinterpret_cast<char*>(&sample_rate_)) + sizeof(num_reverse_output_channels_));
}

Init::~Init() {
  // @@protoc_insertion_point(destructor:webrtc.audioproc.Init)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Init::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Init::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Init::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.audioproc.Init)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    ::memset(&sample_rate_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&reverse_output_sample_rate_) -
        reinterpret_cast<char*>(&sample_rate_)) + sizeof(reverse_output_sample_rate_));
  }
  if (cached_has_bits & 0x00000300u) {
    ::memset(&timestamp_ms_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&num_reverse_output_channels_) -
        reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(num_reverse_output_channels_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* Init::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional int32 sample_rate = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_sample_rate(&has_bits);
          sample_rate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 device_sample_rate = 2 [deprecated = true];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_device_sample_rate(&has_bits);
          device_sample_rate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 num_input_channels = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _Internal::set_has_num_input_channels(&has_bits);
          num_input_channels_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 num_output_channels = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _Internal::set_has_num_output_channels(&has_bits);
          num_output_channels_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 num_reverse_channels = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _Internal::set_has_num_reverse_channels(&has_bits);
          num_reverse_channels_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 reverse_sample_rate = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _Internal::set_has_reverse_sample_rate(&has_bits);
          reverse_sample_rate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 output_sample_rate = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          _Internal::set_has_output_sample_rate(&has_bits);
          output_sample_rate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 reverse_output_sample_rate = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          _Internal::set_has_reverse_output_sample_rate(&has_bits);
          reverse_output_sample_rate_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 num_reverse_output_channels = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          _Internal::set_has_num_reverse_output_channels(&has_bits);
          num_reverse_output_channels_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 timestamp_ms = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          _Internal::set_has_timestamp_ms(&has_bits);
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Init::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.audioproc.Init)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 sample_rate = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_sample_rate(), target);
  }

  // optional int32 device_sample_rate = 2 [deprecated = true];
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_device_sample_rate(), target);
  }

  // optional int32 num_input_channels = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_num_input_channels(), target);
  }

  // optional int32 num_output_channels = 4;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(4, this->_internal_num_output_channels(), target);
  }

  // optional int32 num_reverse_channels = 5;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(5, this->_internal_num_reverse_channels(), target);
  }

  // optional int32 reverse_sample_rate = 6;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(6, this->_internal_reverse_sample_rate(), target);
  }

  // optional int32 output_sample_rate = 7;
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(7, this->_internal_output_sample_rate(), target);
  }

  // optional int32 reverse_output_sample_rate = 8;
  if (cached_has_bits & 0x00000080u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(8, this->_internal_reverse_output_sample_rate(), target);
  }

  // optional int32 num_reverse_output_channels = 9;
  if (cached_has_bits & 0x00000200u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(9, this->_internal_num_reverse_output_channels(), target);
  }

  // optional int64 timestamp_ms = 10;
  if (cached_has_bits & 0x00000100u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(10, this->_internal_timestamp_ms(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.audioproc.Init)
  return target;
}

size_t Init::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.audioproc.Init)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    // optional int32 sample_rate = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_sample_rate());
    }

    // optional int32 device_sample_rate = 2 [deprecated = true];
    if (cached_has_bits & 0x00000002u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_device_sample_rate());
    }

    // optional int32 num_input_channels = 3;
    if (cached_has_bits & 0x00000004u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_num_input_channels());
    }

    // optional int32 num_output_channels = 4;
    if (cached_has_bits & 0x00000008u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_num_output_channels());
    }

    // optional int32 num_reverse_channels = 5;
    if (cached_has_bits & 0x00000010u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_num_reverse_channels());
    }

    // optional int32 reverse_sample_rate = 6;
    if (cached_has_bits & 0x00000020u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_reverse_sample_rate());
    }

    // optional int32 output_sample_rate = 7;
    if (cached_has_bits & 0x00000040u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_output_sample_rate());
    }

    // optional int32 reverse_output_sample_rate = 8;
    if (cached_has_bits & 0x00000080u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_reverse_output_sample_rate());
    }

  }
  if (cached_has_bits & 0x00000300u) {
    // optional int64 timestamp_ms = 10;
    if (cached_has_bits & 0x00000100u) {
      total_size += ::_pbi::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
    }

    // optional int32 num_reverse_output_channels = 9;
    if (cached_has_bits & 0x00000200u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_num_reverse_output_channels());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Init::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const Init*>(
      &from));
}

void Init::MergeFrom(const Init& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.audioproc.Init)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      sample_rate_ = from.sample_rate_;
    }
    if (cached_has_bits & 0x00000002u) {
      device_sample_rate_ = from.device_sample_rate_;
    }
    if (cached_has_bits & 0x00000004u) {
      num_input_channels_ = from.num_input_channels_;
    }
    if (cached_has_bits & 0x00000008u) {
      num_output_channels_ = from.num_output_channels_;
    }
    if (cached_has_bits & 0x00000010u) {
      num_reverse_channels_ = from.num_reverse_channels_;
    }
    if (cached_has_bits & 0x00000020u) {
      reverse_sample_rate_ = from.reverse_sample_rate_;
    }
    if (cached_has_bits & 0x00000040u) {
      output_sample_rate_ = from.output_sample_rate_;
    }
    if (cached_has_bits & 0x00000080u) {
      reverse_output_sample_rate_ = from.reverse_output_sample_rate_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 0x00000300u) {
    if (cached_has_bits & 0x00000100u) {
      timestamp_ms_ = from.timestamp_ms_;
    }
    if (cached_has_bits & 0x00000200u) {
      num_reverse_output_channels_ = from.num_reverse_output_channels_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void Init::CopyFrom(const Init& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.audioproc.Init)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Init::IsInitialized() const {
  return true;
}

void Init::InternalSwap(Init* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Init, num_reverse_output_channels_)
      + sizeof(Init::num_reverse_output_channels_)
      - PROTOBUF_FIELD_OFFSET(Init, sample_rate_)>(
          reinterpret_cast<char*>(&sample_rate_),
          reinterpret_cast<char*>(&other->sample_rate_));
}

std::string Init::GetTypeName() const {
  return "webrtc.audioproc.Init";
}


// ===================================================================

class ReverseStream::_Internal {
 public:
  using HasBits = decltype(std::declval<ReverseStream>()._has_bits_);
  static void set_has_data(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

ReverseStream::ReverseStream(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned),
  channel_(arena) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.audioproc.ReverseStream)
}
ReverseStream::ReverseStream(const ReverseStream& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_),
      channel_(from.channel_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  data_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    data_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_data()) {
    data_.Set(from._internal_data(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:webrtc.audioproc.ReverseStream)
}

inline void ReverseStream::SharedCtor() {
data_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  data_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ReverseStream::~ReverseStream() {
  // @@protoc_insertion_point(destructor:webrtc.audioproc.ReverseStream)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void ReverseStream::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  data_.Destroy();
}

void ReverseStream::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ReverseStream::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.audioproc.ReverseStream)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  channel_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    data_.ClearNonDefaultToEmpty();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* ReverseStream::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional bytes data = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_data();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated bytes channel = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_channel();
            ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ReverseStream::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.audioproc.ReverseStream)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bytes data = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_data(), target);
  }

  // repeated bytes channel = 2;
  for (int i = 0, n = this->_internal_channel_size(); i < n; i++) {
    const auto& s = this->_internal_channel(i);
    target = stream->WriteBytes(2, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.audioproc.ReverseStream)
  return target;
}

size_t ReverseStream::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.audioproc.ReverseStream)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated bytes channel = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(channel_.size());
  for (int i = 0, n = channel_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
      channel_.Get(i));
  }

  // optional bytes data = 1;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_data());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void ReverseStream::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const ReverseStream*>(
      &from));
}

void ReverseStream::MergeFrom(const ReverseStream& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.audioproc.ReverseStream)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  channel_.MergeFrom(from.channel_);
  if (from._internal_has_data()) {
    _internal_set_data(from._internal_data());
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void ReverseStream::CopyFrom(const ReverseStream& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.audioproc.ReverseStream)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ReverseStream::IsInitialized() const {
  return true;
}

void ReverseStream::InternalSwap(ReverseStream* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  channel_.InternalSwap(&other->channel_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &data_, lhs_arena,
      &other->data_, rhs_arena
  );
}

std::string ReverseStream::GetTypeName() const {
  return "webrtc.audioproc.ReverseStream";
}


// ===================================================================

class Stream::_Internal {
 public:
  using HasBits = decltype(std::declval<Stream>()._has_bits_);
  static void set_has_input_data(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_output_data(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_delay(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_drift(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_applied_input_volume(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_keypress(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
};

Stream::Stream(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned),
  input_channel_(arena),
  output_channel_(arena) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.audioproc.Stream)
}
Stream::Stream(const Stream& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_),
      input_channel_(from.input_channel_),
      output_channel_(from.output_channel_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  input_data_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    input_data_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_input_data()) {
    input_data_.Set(from._internal_input_data(), 
      GetArenaForAllocation());
  }
  output_data_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    output_data_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_output_data()) {
    output_data_.Set(from._internal_output_data(), 
      GetArenaForAllocation());
  }
  ::memcpy(&delay_, &from.delay_,
    static_cast<size_t>(reinterpret_cast<char*>(&keypress_) -
    reinterpret_cast<char*>(&delay_)) + sizeof(keypress_));
  // @@protoc_insertion_point(copy_constructor:webrtc.audioproc.Stream)
}

inline void Stream::SharedCtor() {
input_data_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  input_data_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
output_data_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  output_data_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&delay_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&keypress_) -
    reinterpret_cast<char*>(&delay_)) + sizeof(keypress_));
}

Stream::~Stream() {
  // @@protoc_insertion_point(destructor:webrtc.audioproc.Stream)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Stream::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  input_data_.Destroy();
  output_data_.Destroy();
}

void Stream::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Stream::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.audioproc.Stream)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  input_channel_.Clear();
  output_channel_.Clear();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      input_data_.ClearNonDefaultToEmpty();
    }
    if (cached_has_bits & 0x00000002u) {
      output_data_.ClearNonDefaultToEmpty();
    }
  }
  if (cached_has_bits & 0x0000003cu) {
    ::memset(&delay_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&keypress_) -
        reinterpret_cast<char*>(&delay_)) + sizeof(keypress_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* Stream::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional bytes input_data = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_input_data();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bytes output_data = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_output_data();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 delay = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _Internal::set_has_delay(&has_bits);
          delay_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional sint32 drift = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _Internal::set_has_drift(&has_bits);
          drift_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarintZigZag32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 applied_input_volume = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _Internal::set_has_applied_input_volume(&has_bits);
          applied_input_volume_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool keypress = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _Internal::set_has_keypress(&has_bits);
          keypress_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated bytes input_channel = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_input_channel();
            ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated bytes output_channel = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_output_channel();
            ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<66>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Stream::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.audioproc.Stream)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bytes input_data = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_input_data(), target);
  }

  // optional bytes output_data = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->WriteBytesMaybeAliased(
        2, this->_internal_output_data(), target);
  }

  // optional int32 delay = 3;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_delay(), target);
  }

  // optional sint32 drift = 4;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteSInt32ToArray(4, this->_internal_drift(), target);
  }

  // optional int32 applied_input_volume = 5;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(5, this->_internal_applied_input_volume(), target);
  }

  // optional bool keypress = 6;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(6, this->_internal_keypress(), target);
  }

  // repeated bytes input_channel = 7;
  for (int i = 0, n = this->_internal_input_channel_size(); i < n; i++) {
    const auto& s = this->_internal_input_channel(i);
    target = stream->WriteBytes(7, s, target);
  }

  // repeated bytes output_channel = 8;
  for (int i = 0, n = this->_internal_output_channel_size(); i < n; i++) {
    const auto& s = this->_internal_output_channel(i);
    target = stream->WriteBytes(8, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.audioproc.Stream)
  return target;
}

size_t Stream::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.audioproc.Stream)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated bytes input_channel = 7;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(input_channel_.size());
  for (int i = 0, n = input_channel_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
      input_channel_.Get(i));
  }

  // repeated bytes output_channel = 8;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(output_channel_.size());
  for (int i = 0, n = output_channel_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
      output_channel_.Get(i));
  }

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    // optional bytes input_data = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
          this->_internal_input_data());
    }

    // optional bytes output_data = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
          this->_internal_output_data());
    }

    // optional int32 delay = 3;
    if (cached_has_bits & 0x00000004u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_delay());
    }

    // optional sint32 drift = 4;
    if (cached_has_bits & 0x00000008u) {
      total_size += ::_pbi::WireFormatLite::SInt32SizePlusOne(this->_internal_drift());
    }

    // optional int32 applied_input_volume = 5;
    if (cached_has_bits & 0x00000010u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_applied_input_volume());
    }

    // optional bool keypress = 6;
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 + 1;
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Stream::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const Stream*>(
      &from));
}

void Stream::MergeFrom(const Stream& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.audioproc.Stream)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  input_channel_.MergeFrom(from.input_channel_);
  output_channel_.MergeFrom(from.output_channel_);
  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_input_data(from._internal_input_data());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_set_output_data(from._internal_output_data());
    }
    if (cached_has_bits & 0x00000004u) {
      delay_ = from.delay_;
    }
    if (cached_has_bits & 0x00000008u) {
      drift_ = from.drift_;
    }
    if (cached_has_bits & 0x00000010u) {
      applied_input_volume_ = from.applied_input_volume_;
    }
    if (cached_has_bits & 0x00000020u) {
      keypress_ = from.keypress_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void Stream::CopyFrom(const Stream& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.audioproc.Stream)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Stream::IsInitialized() const {
  return true;
}

void Stream::InternalSwap(Stream* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  input_channel_.InternalSwap(&other->input_channel_);
  output_channel_.InternalSwap(&other->output_channel_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &input_data_, lhs_arena,
      &other->input_data_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &output_data_, lhs_arena,
      &other->output_data_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Stream, keypress_)
      + sizeof(Stream::keypress_)
      - PROTOBUF_FIELD_OFFSET(Stream, delay_)>(
          reinterpret_cast<char*>(&delay_),
          reinterpret_cast<char*>(&other->delay_));
}

std::string Stream::GetTypeName() const {
  return "webrtc.audioproc.Stream";
}


// ===================================================================

class Config::_Internal {
 public:
  using HasBits = decltype(std::declval<Config>()._has_bits_);
  static void set_has_aec_enabled(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_aec_delay_agnostic_enabled(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_aec_drift_compensation_enabled(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_aec_extended_filter_enabled(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static void set_has_aec_suppression_level(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_aecm_enabled(HasBits* has_bits) {
    (*has_bits)[0] |= 128u;
  }
  static void set_has_aecm_comfort_noise_enabled(HasBits* has_bits) {
    (*has_bits)[0] |= 256u;
  }
  static void set_has_aecm_routing_mode(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
  static void set_has_agc_enabled(HasBits* has_bits) {
    (*has_bits)[0] |= 512u;
  }
  static void set_has_agc_mode(HasBits* has_bits) {
    (*has_bits)[0] |= 2048u;
  }
  static void set_has_agc_limiter_enabled(HasBits* has_bits) {
    (*has_bits)[0] |= 1024u;
  }
  static void set_has_noise_robust_agc_enabled(HasBits* has_bits) {
    (*has_bits)[0] |= 4096u;
  }
  static void set_has_hpf_enabled(HasBits* has_bits) {
    (*has_bits)[0] |= 8192u;
  }
  static void set_has_ns_enabled(HasBits* has_bits) {
    (*has_bits)[0] |= 16384u;
  }
  static void set_has_ns_level(HasBits* has_bits) {
    (*has_bits)[0] |= 65536u;
  }
  static void set_has_transient_suppression_enabled(HasBits* has_bits) {
    (*has_bits)[0] |= 32768u;
  }
  static void set_has_experiments_description(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_pre_amplifier_enabled(HasBits* has_bits) {
    (*has_bits)[0] |= 131072u;
  }
  static void set_has_pre_amplifier_fixed_gain_factor(HasBits* has_bits) {
    (*has_bits)[0] |= 262144u;
  }
};

Config::Config(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.audioproc.Config)
}
Config::Config(const Config& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  experiments_description_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    experiments_description_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_experiments_description()) {
    experiments_description_.Set(from._internal_experiments_description(), 
      GetArenaForAllocation());
  }
  ::memcpy(&aec_enabled_, &from.aec_enabled_,
    static_cast<size_t>(reinterpret_cast<char*>(&pre_amplifier_fixed_gain_factor_) -
    reinterpret_cast<char*>(&aec_enabled_)) + sizeof(pre_amplifier_fixed_gain_factor_));
  // @@protoc_insertion_point(copy_constructor:webrtc.audioproc.Config)
}

inline void Config::SharedCtor() {
experiments_description_.InitDefault();
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  experiments_description_.Set("", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&aec_enabled_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&pre_amplifier_fixed_gain_factor_) -
    reinterpret_cast<char*>(&aec_enabled_)) + sizeof(pre_amplifier_fixed_gain_factor_));
}

Config::~Config() {
  // @@protoc_insertion_point(destructor:webrtc.audioproc.Config)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Config::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  experiments_description_.Destroy();
}

void Config::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Config::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.audioproc.Config)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    experiments_description_.ClearNonDefaultToEmpty();
  }
  if (cached_has_bits & 0x000000feu) {
    ::memset(&aec_enabled_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&aecm_enabled_) -
        reinterpret_cast<char*>(&aec_enabled_)) + sizeof(aecm_enabled_));
  }
  if (cached_has_bits & 0x0000ff00u) {
    ::memset(&aecm_comfort_noise_enabled_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&transient_suppression_enabled_) -
        reinterpret_cast<char*>(&aecm_comfort_noise_enabled_)) + sizeof(transient_suppression_enabled_));
  }
  if (cached_has_bits & 0x00070000u) {
    ::memset(&ns_level_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&pre_amplifier_fixed_gain_factor_) -
        reinterpret_cast<char*>(&ns_level_)) + sizeof(pre_amplifier_fixed_gain_factor_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* Config::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional bool aec_enabled = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_aec_enabled(&has_bits);
          aec_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool aec_delay_agnostic_enabled = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_aec_delay_agnostic_enabled(&has_bits);
          aec_delay_agnostic_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool aec_drift_compensation_enabled = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _Internal::set_has_aec_drift_compensation_enabled(&has_bits);
          aec_drift_compensation_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool aec_extended_filter_enabled = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _Internal::set_has_aec_extended_filter_enabled(&has_bits);
          aec_extended_filter_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 aec_suppression_level = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          _Internal::set_has_aec_suppression_level(&has_bits);
          aec_suppression_level_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool aecm_enabled = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _Internal::set_has_aecm_enabled(&has_bits);
          aecm_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool aecm_comfort_noise_enabled = 7 [deprecated = true];
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          _Internal::set_has_aecm_comfort_noise_enabled(&has_bits);
          aecm_comfort_noise_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 aecm_routing_mode = 8 [deprecated = true];
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          _Internal::set_has_aecm_routing_mode(&has_bits);
          aecm_routing_mode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool agc_enabled = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          _Internal::set_has_agc_enabled(&has_bits);
          agc_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 agc_mode = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          _Internal::set_has_agc_mode(&has_bits);
          agc_mode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool agc_limiter_enabled = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 88)) {
          _Internal::set_has_agc_limiter_enabled(&has_bits);
          agc_limiter_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool noise_robust_agc_enabled = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 96)) {
          _Internal::set_has_noise_robust_agc_enabled(&has_bits);
          noise_robust_agc_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool hpf_enabled = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 104)) {
          _Internal::set_has_hpf_enabled(&has_bits);
          hpf_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool ns_enabled = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 112)) {
          _Internal::set_has_ns_enabled(&has_bits);
          ns_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 ns_level = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 120)) {
          _Internal::set_has_ns_level(&has_bits);
          ns_level_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool transient_suppression_enabled = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 128)) {
          _Internal::set_has_transient_suppression_enabled(&has_bits);
          transient_suppression_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional string experiments_description = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 138)) {
          auto str = _internal_mutable_experiments_description();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool pre_amplifier_enabled = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 152)) {
          _Internal::set_has_pre_amplifier_enabled(&has_bits);
          pre_amplifier_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional float pre_amplifier_fixed_gain_factor = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 165)) {
          _Internal::set_has_pre_amplifier_fixed_gain_factor(&has_bits);
          pre_amplifier_fixed_gain_factor_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Config::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.audioproc.Config)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional bool aec_enabled = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(1, this->_internal_aec_enabled(), target);
  }

  // optional bool aec_delay_agnostic_enabled = 2;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(2, this->_internal_aec_delay_agnostic_enabled(), target);
  }

  // optional bool aec_drift_compensation_enabled = 3;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(3, this->_internal_aec_drift_compensation_enabled(), target);
  }

  // optional bool aec_extended_filter_enabled = 4;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(4, this->_internal_aec_extended_filter_enabled(), target);
  }

  // optional int32 aec_suppression_level = 5;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(5, this->_internal_aec_suppression_level(), target);
  }

  // optional bool aecm_enabled = 6;
  if (cached_has_bits & 0x00000080u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(6, this->_internal_aecm_enabled(), target);
  }

  // optional bool aecm_comfort_noise_enabled = 7 [deprecated = true];
  if (cached_has_bits & 0x00000100u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(7, this->_internal_aecm_comfort_noise_enabled(), target);
  }

  // optional int32 aecm_routing_mode = 8 [deprecated = true];
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(8, this->_internal_aecm_routing_mode(), target);
  }

  // optional bool agc_enabled = 9;
  if (cached_has_bits & 0x00000200u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(9, this->_internal_agc_enabled(), target);
  }

  // optional int32 agc_mode = 10;
  if (cached_has_bits & 0x00000800u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(10, this->_internal_agc_mode(), target);
  }

  // optional bool agc_limiter_enabled = 11;
  if (cached_has_bits & 0x00000400u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(11, this->_internal_agc_limiter_enabled(), target);
  }

  // optional bool noise_robust_agc_enabled = 12;
  if (cached_has_bits & 0x00001000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(12, this->_internal_noise_robust_agc_enabled(), target);
  }

  // optional bool hpf_enabled = 13;
  if (cached_has_bits & 0x00002000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(13, this->_internal_hpf_enabled(), target);
  }

  // optional bool ns_enabled = 14;
  if (cached_has_bits & 0x00004000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(14, this->_internal_ns_enabled(), target);
  }

  // optional int32 ns_level = 15;
  if (cached_has_bits & 0x00010000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(15, this->_internal_ns_level(), target);
  }

  // optional bool transient_suppression_enabled = 16;
  if (cached_has_bits & 0x00008000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(16, this->_internal_transient_suppression_enabled(), target);
  }

  // optional string experiments_description = 17;
  if (cached_has_bits & 0x00000001u) {
    target = stream->WriteStringMaybeAliased(
        17, this->_internal_experiments_description(), target);
  }

  // optional bool pre_amplifier_enabled = 19;
  if (cached_has_bits & 0x00020000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(19, this->_internal_pre_amplifier_enabled(), target);
  }

  // optional float pre_amplifier_fixed_gain_factor = 20;
  if (cached_has_bits & 0x00040000u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(20, this->_internal_pre_amplifier_fixed_gain_factor(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.audioproc.Config)
  return target;
}

size_t Config::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.audioproc.Config)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    // optional string experiments_description = 17;
    if (cached_has_bits & 0x00000001u) {
      total_size += 2 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_experiments_description());
    }

    // optional bool aec_enabled = 1;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 + 1;
    }

    // optional bool aec_delay_agnostic_enabled = 2;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 + 1;
    }

    // optional bool aec_drift_compensation_enabled = 3;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 + 1;
    }

    // optional bool aec_extended_filter_enabled = 4;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 + 1;
    }

    // optional int32 aec_suppression_level = 5;
    if (cached_has_bits & 0x00000020u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_aec_suppression_level());
    }

    // optional int32 aecm_routing_mode = 8 [deprecated = true];
    if (cached_has_bits & 0x00000040u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_aecm_routing_mode());
    }

    // optional bool aecm_enabled = 6;
    if (cached_has_bits & 0x00000080u) {
      total_size += 1 + 1;
    }

  }
  if (cached_has_bits & 0x0000ff00u) {
    // optional bool aecm_comfort_noise_enabled = 7 [deprecated = true];
    if (cached_has_bits & 0x00000100u) {
      total_size += 1 + 1;
    }

    // optional bool agc_enabled = 9;
    if (cached_has_bits & 0x00000200u) {
      total_size += 1 + 1;
    }

    // optional bool agc_limiter_enabled = 11;
    if (cached_has_bits & 0x00000400u) {
      total_size += 1 + 1;
    }

    // optional int32 agc_mode = 10;
    if (cached_has_bits & 0x00000800u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_agc_mode());
    }

    // optional bool noise_robust_agc_enabled = 12;
    if (cached_has_bits & 0x00001000u) {
      total_size += 1 + 1;
    }

    // optional bool hpf_enabled = 13;
    if (cached_has_bits & 0x00002000u) {
      total_size += 1 + 1;
    }

    // optional bool ns_enabled = 14;
    if (cached_has_bits & 0x00004000u) {
      total_size += 1 + 1;
    }

    // optional bool transient_suppression_enabled = 16;
    if (cached_has_bits & 0x00008000u) {
      total_size += 2 + 1;
    }

  }
  if (cached_has_bits & 0x00070000u) {
    // optional int32 ns_level = 15;
    if (cached_has_bits & 0x00010000u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_ns_level());
    }

    // optional bool pre_amplifier_enabled = 19;
    if (cached_has_bits & 0x00020000u) {
      total_size += 2 + 1;
    }

    // optional float pre_amplifier_fixed_gain_factor = 20;
    if (cached_has_bits & 0x00040000u) {
      total_size += 2 + 4;
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Config::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const Config*>(
      &from));
}

void Config::MergeFrom(const Config& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.audioproc.Config)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x000000ffu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_set_experiments_description(from._internal_experiments_description());
    }
    if (cached_has_bits & 0x00000002u) {
      aec_enabled_ = from.aec_enabled_;
    }
    if (cached_has_bits & 0x00000004u) {
      aec_delay_agnostic_enabled_ = from.aec_delay_agnostic_enabled_;
    }
    if (cached_has_bits & 0x00000008u) {
      aec_drift_compensation_enabled_ = from.aec_drift_compensation_enabled_;
    }
    if (cached_has_bits & 0x00000010u) {
      aec_extended_filter_enabled_ = from.aec_extended_filter_enabled_;
    }
    if (cached_has_bits & 0x00000020u) {
      aec_suppression_level_ = from.aec_suppression_level_;
    }
    if (cached_has_bits & 0x00000040u) {
      aecm_routing_mode_ = from.aecm_routing_mode_;
    }
    if (cached_has_bits & 0x00000080u) {
      aecm_enabled_ = from.aecm_enabled_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 0x0000ff00u) {
    if (cached_has_bits & 0x00000100u) {
      aecm_comfort_noise_enabled_ = from.aecm_comfort_noise_enabled_;
    }
    if (cached_has_bits & 0x00000200u) {
      agc_enabled_ = from.agc_enabled_;
    }
    if (cached_has_bits & 0x00000400u) {
      agc_limiter_enabled_ = from.agc_limiter_enabled_;
    }
    if (cached_has_bits & 0x00000800u) {
      agc_mode_ = from.agc_mode_;
    }
    if (cached_has_bits & 0x00001000u) {
      noise_robust_agc_enabled_ = from.noise_robust_agc_enabled_;
    }
    if (cached_has_bits & 0x00002000u) {
      hpf_enabled_ = from.hpf_enabled_;
    }
    if (cached_has_bits & 0x00004000u) {
      ns_enabled_ = from.ns_enabled_;
    }
    if (cached_has_bits & 0x00008000u) {
      transient_suppression_enabled_ = from.transient_suppression_enabled_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 0x00070000u) {
    if (cached_has_bits & 0x00010000u) {
      ns_level_ = from.ns_level_;
    }
    if (cached_has_bits & 0x00020000u) {
      pre_amplifier_enabled_ = from.pre_amplifier_enabled_;
    }
    if (cached_has_bits & 0x00040000u) {
      pre_amplifier_fixed_gain_factor_ = from.pre_amplifier_fixed_gain_factor_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void Config::CopyFrom(const Config& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.audioproc.Config)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Config::IsInitialized() const {
  return true;
}

void Config::InternalSwap(Config* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &experiments_description_, lhs_arena,
      &other->experiments_description_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Config, pre_amplifier_fixed_gain_factor_)
      + sizeof(Config::pre_amplifier_fixed_gain_factor_)
      - PROTOBUF_FIELD_OFFSET(Config, aec_enabled_)>(
          reinterpret_cast<char*>(&aec_enabled_),
          reinterpret_cast<char*>(&other->aec_enabled_));
}

std::string Config::GetTypeName() const {
  return "webrtc.audioproc.Config";
}


// ===================================================================

class PlayoutAudioDeviceInfo::_Internal {
 public:
  using HasBits = decltype(std::declval<PlayoutAudioDeviceInfo>()._has_bits_);
  static void set_has_id(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_max_volume(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

PlayoutAudioDeviceInfo::PlayoutAudioDeviceInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.audioproc.PlayoutAudioDeviceInfo)
}
PlayoutAudioDeviceInfo::PlayoutAudioDeviceInfo(const PlayoutAudioDeviceInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&max_volume_) -
    reinterpret_cast<char*>(&id_)) + sizeof(max_volume_));
  // @@protoc_insertion_point(copy_constructor:webrtc.audioproc.PlayoutAudioDeviceInfo)
}

inline void PlayoutAudioDeviceInfo::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&max_volume_) -
    reinterpret_cast<char*>(&id_)) + sizeof(max_volume_));
}

PlayoutAudioDeviceInfo::~PlayoutAudioDeviceInfo() {
  // @@protoc_insertion_point(destructor:webrtc.audioproc.PlayoutAudioDeviceInfo)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void PlayoutAudioDeviceInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PlayoutAudioDeviceInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PlayoutAudioDeviceInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.audioproc.PlayoutAudioDeviceInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    ::memset(&id_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&max_volume_) -
        reinterpret_cast<char*>(&id_)) + sizeof(max_volume_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* PlayoutAudioDeviceInfo::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional int32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _Internal::set_has_id(&has_bits);
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int32 max_volume = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _Internal::set_has_max_volume(&has_bits);
          max_volume_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PlayoutAudioDeviceInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.audioproc.PlayoutAudioDeviceInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional int32 id = 1;
  if (cached_has_bits & 0x00000001u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_id(), target);
  }

  // optional int32 max_volume = 2;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_max_volume(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.audioproc.PlayoutAudioDeviceInfo)
  return target;
}

size_t PlayoutAudioDeviceInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.audioproc.PlayoutAudioDeviceInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    // optional int32 id = 1;
    if (cached_has_bits & 0x00000001u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_id());
    }

    // optional int32 max_volume = 2;
    if (cached_has_bits & 0x00000002u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_max_volume());
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void PlayoutAudioDeviceInfo::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const PlayoutAudioDeviceInfo*>(
      &from));
}

void PlayoutAudioDeviceInfo::MergeFrom(const PlayoutAudioDeviceInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.audioproc.PlayoutAudioDeviceInfo)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x00000003u) {
    if (cached_has_bits & 0x00000001u) {
      id_ = from.id_;
    }
    if (cached_has_bits & 0x00000002u) {
      max_volume_ = from.max_volume_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void PlayoutAudioDeviceInfo::CopyFrom(const PlayoutAudioDeviceInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.audioproc.PlayoutAudioDeviceInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PlayoutAudioDeviceInfo::IsInitialized() const {
  return true;
}

void PlayoutAudioDeviceInfo::InternalSwap(PlayoutAudioDeviceInfo* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PlayoutAudioDeviceInfo, max_volume_)
      + sizeof(PlayoutAudioDeviceInfo::max_volume_)
      - PROTOBUF_FIELD_OFFSET(PlayoutAudioDeviceInfo, id_)>(
          reinterpret_cast<char*>(&id_),
          reinterpret_cast<char*>(&other->id_));
}

std::string PlayoutAudioDeviceInfo::GetTypeName() const {
  return "webrtc.audioproc.PlayoutAudioDeviceInfo";
}


// ===================================================================

class RuntimeSetting::_Internal {
 public:
  using HasBits = decltype(std::declval<RuntimeSetting>()._has_bits_);
  static void set_has_capture_pre_gain(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static void set_has_custom_render_processing_setting(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static void set_has_capture_fixed_post_gain(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static void set_has_playout_volume_change(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static const ::webrtc::audioproc::PlayoutAudioDeviceInfo& playout_audio_device_change(const RuntimeSetting* msg);
  static void set_has_playout_audio_device_change(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_capture_output_used(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static void set_has_capture_post_gain(HasBits* has_bits) {
    (*has_bits)[0] |= 64u;
  }
};

const ::webrtc::audioproc::PlayoutAudioDeviceInfo&
RuntimeSetting::_Internal::playout_audio_device_change(const RuntimeSetting* msg) {
  return *msg->playout_audio_device_change_;
}
RuntimeSetting::RuntimeSetting(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.audioproc.RuntimeSetting)
}
RuntimeSetting::RuntimeSetting(const RuntimeSetting& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  if (from._internal_has_playout_audio_device_change()) {
    playout_audio_device_change_ = new ::webrtc::audioproc::PlayoutAudioDeviceInfo(*from.playout_audio_device_change_);
  } else {
    playout_audio_device_change_ = nullptr;
  }
  ::memcpy(&capture_pre_gain_, &from.capture_pre_gain_,
    static_cast<size_t>(reinterpret_cast<char*>(&capture_post_gain_) -
    reinterpret_cast<char*>(&capture_pre_gain_)) + sizeof(capture_post_gain_));
  // @@protoc_insertion_point(copy_constructor:webrtc.audioproc.RuntimeSetting)
}

inline void RuntimeSetting::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&playout_audio_device_change_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&capture_post_gain_) -
    reinterpret_cast<char*>(&playout_audio_device_change_)) + sizeof(capture_post_gain_));
}

RuntimeSetting::~RuntimeSetting() {
  // @@protoc_insertion_point(destructor:webrtc.audioproc.RuntimeSetting)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void RuntimeSetting::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete playout_audio_device_change_;
}

void RuntimeSetting::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RuntimeSetting::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.audioproc.RuntimeSetting)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(playout_audio_device_change_ != nullptr);
    playout_audio_device_change_->Clear();
  }
  if (cached_has_bits & 0x0000007eu) {
    ::memset(&capture_pre_gain_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&capture_post_gain_) -
        reinterpret_cast<char*>(&capture_pre_gain_)) + sizeof(capture_post_gain_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* RuntimeSetting::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional float capture_pre_gain = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          _Internal::set_has_capture_pre_gain(&has_bits);
          capture_pre_gain_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // optional float custom_render_processing_setting = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _Internal::set_has_custom_render_processing_setting(&has_bits);
          custom_render_processing_setting_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // optional float capture_fixed_post_gain = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _Internal::set_has_capture_fixed_post_gain(&has_bits);
          capture_fixed_post_gain_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // optional int32 playout_volume_change = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _Internal::set_has_playout_volume_change(&has_bits);
          playout_volume_change_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.audioproc.PlayoutAudioDeviceInfo playout_audio_device_change = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_playout_audio_device_change(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional bool capture_output_used = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          _Internal::set_has_capture_output_used(&has_bits);
          capture_output_used_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional float capture_post_gain = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 61)) {
          _Internal::set_has_capture_post_gain(&has_bits);
          capture_post_gain_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RuntimeSetting::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.audioproc.RuntimeSetting)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional float capture_pre_gain = 1;
  if (cached_has_bits & 0x00000002u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(1, this->_internal_capture_pre_gain(), target);
  }

  // optional float custom_render_processing_setting = 2;
  if (cached_has_bits & 0x00000004u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_custom_render_processing_setting(), target);
  }

  // optional float capture_fixed_post_gain = 3;
  if (cached_has_bits & 0x00000008u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_capture_fixed_post_gain(), target);
  }

  // optional int32 playout_volume_change = 4;
  if (cached_has_bits & 0x00000010u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(4, this->_internal_playout_volume_change(), target);
  }

  // optional .webrtc.audioproc.PlayoutAudioDeviceInfo playout_audio_device_change = 5;
  if (cached_has_bits & 0x00000001u) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, _Internal::playout_audio_device_change(this),
        _Internal::playout_audio_device_change(this).GetCachedSize(), target, stream);
  }

  // optional bool capture_output_used = 6;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(6, this->_internal_capture_output_used(), target);
  }

  // optional float capture_post_gain = 7;
  if (cached_has_bits & 0x00000040u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(7, this->_internal_capture_post_gain(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.audioproc.RuntimeSetting)
  return target;
}

size_t RuntimeSetting::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.audioproc.RuntimeSetting)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000007fu) {
    // optional .webrtc.audioproc.PlayoutAudioDeviceInfo playout_audio_device_change = 5;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *playout_audio_device_change_);
    }

    // optional float capture_pre_gain = 1;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 + 4;
    }

    // optional float custom_render_processing_setting = 2;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 + 4;
    }

    // optional float capture_fixed_post_gain = 3;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 + 4;
    }

    // optional int32 playout_volume_change = 4;
    if (cached_has_bits & 0x00000010u) {
      total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_playout_volume_change());
    }

    // optional bool capture_output_used = 6;
    if (cached_has_bits & 0x00000020u) {
      total_size += 1 + 1;
    }

    // optional float capture_post_gain = 7;
    if (cached_has_bits & 0x00000040u) {
      total_size += 1 + 4;
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RuntimeSetting::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const RuntimeSetting*>(
      &from));
}

void RuntimeSetting::MergeFrom(const RuntimeSetting& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.audioproc.RuntimeSetting)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000007fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_playout_audio_device_change()->::webrtc::audioproc::PlayoutAudioDeviceInfo::MergeFrom(from._internal_playout_audio_device_change());
    }
    if (cached_has_bits & 0x00000002u) {
      capture_pre_gain_ = from.capture_pre_gain_;
    }
    if (cached_has_bits & 0x00000004u) {
      custom_render_processing_setting_ = from.custom_render_processing_setting_;
    }
    if (cached_has_bits & 0x00000008u) {
      capture_fixed_post_gain_ = from.capture_fixed_post_gain_;
    }
    if (cached_has_bits & 0x00000010u) {
      playout_volume_change_ = from.playout_volume_change_;
    }
    if (cached_has_bits & 0x00000020u) {
      capture_output_used_ = from.capture_output_used_;
    }
    if (cached_has_bits & 0x00000040u) {
      capture_post_gain_ = from.capture_post_gain_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void RuntimeSetting::CopyFrom(const RuntimeSetting& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.audioproc.RuntimeSetting)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RuntimeSetting::IsInitialized() const {
  return true;
}

void RuntimeSetting::InternalSwap(RuntimeSetting* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RuntimeSetting, capture_post_gain_)
      + sizeof(RuntimeSetting::capture_post_gain_)
      - PROTOBUF_FIELD_OFFSET(RuntimeSetting, playout_audio_device_change_)>(
          reinterpret_cast<char*>(&playout_audio_device_change_),
          reinterpret_cast<char*>(&other->playout_audio_device_change_));
}

std::string RuntimeSetting::GetTypeName() const {
  return "webrtc.audioproc.RuntimeSetting";
}


// ===================================================================

class Event::_Internal {
 public:
  using HasBits = decltype(std::declval<Event>()._has_bits_);
  static void set_has_type(HasBits* has_bits) {
    (*has_bits)[0] |= 32u;
  }
  static const ::webrtc::audioproc::Init& init(const Event* msg);
  static void set_has_init(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static const ::webrtc::audioproc::ReverseStream& reverse_stream(const Event* msg);
  static void set_has_reverse_stream(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
  static const ::webrtc::audioproc::Stream& stream(const Event* msg);
  static void set_has_stream(HasBits* has_bits) {
    (*has_bits)[0] |= 4u;
  }
  static const ::webrtc::audioproc::Config& config(const Event* msg);
  static void set_has_config(HasBits* has_bits) {
    (*has_bits)[0] |= 8u;
  }
  static const ::webrtc::audioproc::RuntimeSetting& runtime_setting(const Event* msg);
  static void set_has_runtime_setting(HasBits* has_bits) {
    (*has_bits)[0] |= 16u;
  }
  static bool MissingRequiredFields(const HasBits& has_bits) {
    return ((has_bits[0] & 0x00000020) ^ 0x00000020) != 0;
  }
};

const ::webrtc::audioproc::Init&
Event::_Internal::init(const Event* msg) {
  return *msg->init_;
}
const ::webrtc::audioproc::ReverseStream&
Event::_Internal::reverse_stream(const Event* msg) {
  return *msg->reverse_stream_;
}
const ::webrtc::audioproc::Stream&
Event::_Internal::stream(const Event* msg) {
  return *msg->stream_;
}
const ::webrtc::audioproc::Config&
Event::_Internal::config(const Event* msg) {
  return *msg->config_;
}
const ::webrtc::audioproc::RuntimeSetting&
Event::_Internal::runtime_setting(const Event* msg) {
  return *msg->runtime_setting_;
}
Event::Event(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  // @@protoc_insertion_point(arena_constructor:webrtc.audioproc.Event)
}
Event::Event(const Event& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  if (from._internal_has_init()) {
    init_ = new ::webrtc::audioproc::Init(*from.init_);
  } else {
    init_ = nullptr;
  }
  if (from._internal_has_reverse_stream()) {
    reverse_stream_ = new ::webrtc::audioproc::ReverseStream(*from.reverse_stream_);
  } else {
    reverse_stream_ = nullptr;
  }
  if (from._internal_has_stream()) {
    stream_ = new ::webrtc::audioproc::Stream(*from.stream_);
  } else {
    stream_ = nullptr;
  }
  if (from._internal_has_config()) {
    config_ = new ::webrtc::audioproc::Config(*from.config_);
  } else {
    config_ = nullptr;
  }
  if (from._internal_has_runtime_setting()) {
    runtime_setting_ = new ::webrtc::audioproc::RuntimeSetting(*from.runtime_setting_);
  } else {
    runtime_setting_ = nullptr;
  }
  type_ = from.type_;
  // @@protoc_insertion_point(copy_constructor:webrtc.audioproc.Event)
}

inline void Event::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&init_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&type_) -
    reinterpret_cast<char*>(&init_)) + sizeof(type_));
}

Event::~Event() {
  // @@protoc_insertion_point(destructor:webrtc.audioproc.Event)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<std::string>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Event::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete init_;
  if (this != internal_default_instance()) delete reverse_stream_;
  if (this != internal_default_instance()) delete stream_;
  if (this != internal_default_instance()) delete config_;
  if (this != internal_default_instance()) delete runtime_setting_;
}

void Event::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Event::Clear() {
// @@protoc_insertion_point(message_clear_start:webrtc.audioproc.Event)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    if (cached_has_bits & 0x00000001u) {
      GOOGLE_DCHECK(init_ != nullptr);
      init_->Clear();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(reverse_stream_ != nullptr);
      reverse_stream_->Clear();
    }
    if (cached_has_bits & 0x00000004u) {
      GOOGLE_DCHECK(stream_ != nullptr);
      stream_->Clear();
    }
    if (cached_has_bits & 0x00000008u) {
      GOOGLE_DCHECK(config_ != nullptr);
      config_->Clear();
    }
    if (cached_has_bits & 0x00000010u) {
      GOOGLE_DCHECK(runtime_setting_ != nullptr);
      runtime_setting_->Clear();
    }
  }
  type_ = 0;
  _has_bits_.Clear();
  _internal_metadata_.Clear<std::string>();
}

const char* Event::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // required .webrtc.audioproc.Event.Type type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          if (PROTOBUF_PREDICT_TRUE(::webrtc::audioproc::Event_Type_IsValid(val))) {
            _internal_set_type(static_cast<::webrtc::audioproc::Event_Type>(val));
          } else {
            ::PROTOBUF_NAMESPACE_ID::internal::WriteVarint(1, val, mutable_unknown_fields());
          }
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.audioproc.Init init = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_init(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.audioproc.ReverseStream reverse_stream = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_reverse_stream(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.audioproc.Stream stream = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_stream(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.audioproc.Config config = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_config(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional .webrtc.audioproc.RuntimeSetting runtime_setting = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_runtime_setting(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Event::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:webrtc.audioproc.Event)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // required .webrtc.audioproc.Event.Type type = 1;
  if (cached_has_bits & 0x00000020u) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      1, this->_internal_type(), target);
  }

  // optional .webrtc.audioproc.Init init = 2;
  if (cached_has_bits & 0x00000001u) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::init(this),
        _Internal::init(this).GetCachedSize(), target, stream);
  }

  // optional .webrtc.audioproc.ReverseStream reverse_stream = 3;
  if (cached_has_bits & 0x00000002u) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, _Internal::reverse_stream(this),
        _Internal::reverse_stream(this).GetCachedSize(), target, stream);
  }

  // optional .webrtc.audioproc.Stream stream = 4;
  if (cached_has_bits & 0x00000004u) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, _Internal::stream(this),
        _Internal::stream(this).GetCachedSize(), target, stream);
  }

  // optional .webrtc.audioproc.Config config = 5;
  if (cached_has_bits & 0x00000008u) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, _Internal::config(this),
        _Internal::config(this).GetCachedSize(), target, stream);
  }

  // optional .webrtc.audioproc.RuntimeSetting runtime_setting = 6;
  if (cached_has_bits & 0x00000010u) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, _Internal::runtime_setting(this),
        _Internal::runtime_setting(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:webrtc.audioproc.Event)
  return target;
}

size_t Event::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:webrtc.audioproc.Event)
  size_t total_size = 0;

  // required .webrtc.audioproc.Event.Type type = 1;
  if (_internal_has_type()) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_type());
  }
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x0000001fu) {
    // optional .webrtc.audioproc.Init init = 2;
    if (cached_has_bits & 0x00000001u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *init_);
    }

    // optional .webrtc.audioproc.ReverseStream reverse_stream = 3;
    if (cached_has_bits & 0x00000002u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *reverse_stream_);
    }

    // optional .webrtc.audioproc.Stream stream = 4;
    if (cached_has_bits & 0x00000004u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *stream_);
    }

    // optional .webrtc.audioproc.Config config = 5;
    if (cached_has_bits & 0x00000008u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *config_);
    }

    // optional .webrtc.audioproc.RuntimeSetting runtime_setting = 6;
    if (cached_has_bits & 0x00000010u) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *runtime_setting_);
    }

  }
  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::_pbi::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void Event::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::_pbi::DownCast<const Event*>(
      &from));
}

void Event::MergeFrom(const Event& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:webrtc.audioproc.Event)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 0x0000003fu) {
    if (cached_has_bits & 0x00000001u) {
      _internal_mutable_init()->::webrtc::audioproc::Init::MergeFrom(from._internal_init());
    }
    if (cached_has_bits & 0x00000002u) {
      _internal_mutable_reverse_stream()->::webrtc::audioproc::ReverseStream::MergeFrom(from._internal_reverse_stream());
    }
    if (cached_has_bits & 0x00000004u) {
      _internal_mutable_stream()->::webrtc::audioproc::Stream::MergeFrom(from._internal_stream());
    }
    if (cached_has_bits & 0x00000008u) {
      _internal_mutable_config()->::webrtc::audioproc::Config::MergeFrom(from._internal_config());
    }
    if (cached_has_bits & 0x00000010u) {
      _internal_mutable_runtime_setting()->::webrtc::audioproc::RuntimeSetting::MergeFrom(from._internal_runtime_setting());
    }
    if (cached_has_bits & 0x00000020u) {
      type_ = from.type_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void Event::CopyFrom(const Event& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:webrtc.audioproc.Event)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Event::IsInitialized() const {
  if (_Internal::MissingRequiredFields(_has_bits_)) return false;
  return true;
}

void Event::InternalSwap(Event* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Event, type_)
      + sizeof(Event::type_)
      - PROTOBUF_FIELD_OFFSET(Event, init_)>(
          reinterpret_cast<char*>(&init_),
          reinterpret_cast<char*>(&other->init_));
}

std::string Event::GetTypeName() const {
  return "webrtc.audioproc.Event";
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace audioproc
}  // namespace webrtc
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::webrtc::audioproc::Init*
Arena::CreateMaybeMessage< ::webrtc::audioproc::Init >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::audioproc::Init >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::audioproc::ReverseStream*
Arena::CreateMaybeMessage< ::webrtc::audioproc::ReverseStream >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::audioproc::ReverseStream >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::audioproc::Stream*
Arena::CreateMaybeMessage< ::webrtc::audioproc::Stream >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::audioproc::Stream >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::audioproc::Config*
Arena::CreateMaybeMessage< ::webrtc::audioproc::Config >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::audioproc::Config >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::audioproc::PlayoutAudioDeviceInfo*
Arena::CreateMaybeMessage< ::webrtc::audioproc::PlayoutAudioDeviceInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::audioproc::PlayoutAudioDeviceInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::audioproc::RuntimeSetting*
Arena::CreateMaybeMessage< ::webrtc::audioproc::RuntimeSetting >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::audioproc::RuntimeSetting >(arena);
}
template<> PROTOBUF_NOINLINE ::webrtc::audioproc::Event*
Arena::CreateMaybeMessage< ::webrtc::audioproc::Event >(Arena* arena) {
  return Arena::CreateMessageInternal< ::webrtc::audioproc::Event >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
