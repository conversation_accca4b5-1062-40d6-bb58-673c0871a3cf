from base_plugin import <PERSON><PERSON><PERSON><PERSON>, HookStrategy, BasePlugin
import base64
import hashlib
from java.util import Locale
import re
import random
import json
import time
from org.telegram.tgnet import TLRPC
# Assuming client_utils and android_utils are available in the execution environment
# from client_utils import send_message, get_user_config, send_request, RequestCallback, get_messages_controller
# from android_utils import log

# Mocked imports for local testing if client_utils/android_utils are not available
try:
    from client_utils import send_message, get_user_config, send_request, RequestCallback, get_messages_controller
except ImportError:
    log_print_stub = lambda x: print(f"LOG: {x}")
    class MockBulletinHelper:
        @staticmethod
        def show_info(message): log_print_stub(f"Bulletin Info: {message}")
    class ui: # Mock ui module
        class bulletin: BulletinHelper = MockBulletinHelper
    # Mock other necessary client_utils if needed
    pass

try:
    from android_utils import log
except ImportError:
    log_print_stub = lambda x: print(f"LOG: {x}")
    log = log_print_stub
    # Mock other necessary android_utils if needed
    pass


__id__ = "privacy_guard"
__name__ = "Privacy Guard"
__description__ = "Simple plugin for encrypting messages in any chat with key sharing capabilities"
__author__ = "@mihailkotovski & @mishabotov & AI Assistant" # Добавил себя :)
__min_version__ = "11.9.0"
__icon__ = "UtyaDuck/27"
__version__ = "1.5.0" # Версия обновлена

class LocalizationManager:
    def __init__(self):
        try:
            self.language = Locale.getDefault().getLanguage()
        except Exception: # Fallback for environments where Locale might not be available
            self.language = "en"
            
        self.language = self.language if self.language in self._get_supported_languages() else "en"
        
    def get_string(self, string_key): # Renamed 'string' to 'string_key' for clarity
        return self.strings[self.language].get(string_key, f"MISSING_STRING_{string_key}")


    def _get_supported_languages(self):
        return self.strings.keys()
    
    strings = {
        "ru": {
            "ENCRYPTION_ENABLED": "✅ Шифрование сообщений включено",
            "ENCRYPTION_DISABLED": "❌ Шифрование сообщений отключено",
            "DECRYPTION_SUCCESS": "✅ Сообщение расшифровано",
            "DECRYPTION_FAILED": "❌ Не удалось расшифровать сообщение",
            "INVALID_KEY": "❌ Неверный ключ шифрования",
            "COMPLEXITY_LOW": "Сложность: Низкая",
            "COMPLEXITY_MEDIUM": "Сложность: Средняя",
            "COMPLEXITY_HIGH": "Сложность: Высокая",
            "COMPLEXITY_VERY_HIGH": "Сложность: Очень высокая",
            "COMPLEXITY_CHAOS": "Сложность: Хаос (Экстремальная)", # Для нового метода
            "SHOW_LOCK_EMOJI": "Показывать замок 🔒",
            "SHOW_LOCK_EMOJI_SUBTEXT": "Добавлять эмодзи замка в начале зашифрованных сообщений",
            "SHOW_METHOD_INFO": "Показывать информацию о методе шифрования",
            "SHOW_METHOD_INFO_SUBTEXT": "Добавлять название метода шифрования в конце сообщения",
            # Новые строки для системы ключей
            "KEY_MANAGEMENT": "Управление ключами",
            "ENABLE_KEY_SYNC": "Включить синхронизацию ключей",
            "ENABLE_KEY_SYNC_SUBTEXT": "Автоматически использовать разные ключи для разных пользователей",
            "KEY_SHARED_SUCCESS": "✅ Ключ успешно отправлен пользователю",
            "KEY_RECEIVED": "✅ Получен новый ключ шифрования от пользователя",
            "SHARE_KEY_COMMAND": ".pgshare - отправить ключ шифрования собеседнику",
            "SHARE_KEY_FORMAT": "📩 Я отправляю вам ключ шифрования для Privacy Guard.\n\nДля автоматического импорта ключа, у вас должен быть установлен плагин Privacy Guard v1.5.0 или выше.\n\n`PG_KEY:{0}:{1}:{2}:{3}`",
            "SHARE_KEY_ACCEPT": ".pgaccept [ключ] - принять ключ шифрования",
            "KEY_ACCEPTED": "✅ Ключ шифрования принят и сохранен",
            "DEFAULT_KEY_NAME": "Ключ по умолчанию",
            "ADD_NEW_KEY": "➕ Добавить новый ключ",
            "EDIT_KEY": "✏️ Редактировать ключ",
            "DELETE_KEY": "🗑️ Удалить ключ",
            "KEY_NAME": "Название ключа",
            "KEY_VALUE": "Значение ключа",
            "USER_ID": "ID пользователя",
            "METHOD": "Метод шифрования",
            "SAVE": "Сохранить",
            "CANCEL": "Отмена",
            "NO_KEYS_FOUND": "Ключи не найдены. Добавьте новый ключ, нажав на кнопку выше.",
            "KEY_LIST": "Список ключей",
            "KEY_DETAILS": "Детали ключа"
        },
        "en": {
            "ENCRYPTION_ENABLED": "✅ Message encryption enabled",
            "ENCRYPTION_DISABLED": "❌ Message encryption disabled",
            "DECRYPTION_SUCCESS": "✅ Message decrypted",
            "DECRYPTION_FAILED": "❌ Failed to decrypt message",
            "INVALID_KEY": "❌ Invalid encryption key",
            "COMPLEXITY_LOW": "Complexity: Low",
            "COMPLEXITY_MEDIUM": "Complexity: Medium",
            "COMPLEXITY_HIGH": "Complexity: High",
            "COMPLEXITY_VERY_HIGH": "Complexity: Very High",
            "COMPLEXITY_CHAOS": "Complexity: Chaos (Extreme)", # For the new method
            "SHOW_LOCK_EMOJI": "Show lock emoji 🔒",
            "SHOW_LOCK_EMOJI_SUBTEXT": "Add lock emoji at the beginning of encrypted messages",
            "SHOW_METHOD_INFO": "Show encryption method info",
            "SHOW_METHOD_INFO_SUBTEXT": "Add encryption method name at the end of the message",
            # New strings for key system
            "KEY_MANAGEMENT": "Key Management",
            "ENABLE_KEY_SYNC": "Enable key synchronization",
            "ENABLE_KEY_SYNC_SUBTEXT": "Automatically use different keys for different users",
            "KEY_SHARED_SUCCESS": "✅ Key successfully sent to user",
            "KEY_RECEIVED": "✅ Received new encryption key from user",
            "SHARE_KEY_COMMAND": ".pgshare - send encryption key to your chat partner",
            "SHARE_KEY_FORMAT": "📩 I'm sending you an encryption key for Privacy Guard.\n\nFor automatic key import, you should have Privacy Guard plugin v1.5.0 or higher installed.\n\n`PG_KEY:{0}:{1}:{2}:{3}`",
            "SHARE_KEY_ACCEPT": ".pgaccept [key] - accept encryption key",
            "KEY_ACCEPTED": "✅ Encryption key accepted and saved",
            "DEFAULT_KEY_NAME": "Default Key",
            "ADD_NEW_KEY": "➕ Add new key",
            "EDIT_KEY": "✏️ Edit key",
            "DELETE_KEY": "🗑️ Delete key",
            "KEY_NAME": "Key name",
            "KEY_VALUE": "Key value",
            "USER_ID": "User ID",
            "METHOD": "Encryption method",
            "SAVE": "Save",
            "CANCEL": "Cancel",
            "NO_KEYS_FOUND": "No keys found. Add a new key by clicking the button above.",
            "KEY_LIST": "Key List",
            "KEY_DETAILS": "Key Details"
        }
    }
    
localization = LocalizationManager()

# --- Класс для управления ключами шифрования ---
class KeyManager:
    def __init__(self, plugin):
        self.plugin = plugin
        self.keys = self._load_keys()
        
    def _load_keys(self):
        keys_json = self.plugin.get_setting("encryption_keys", "{}")
        try:
            keys = json.loads(keys_json)
            if not keys: # If keys_json was "{}" or invalid, resulting in empty dict
                raise ValueError("Empty or invalid keys JSON")
            # Ensure default key exists if no keys are loaded or if default is missing
            if "default" not in keys or not any(k_data.get("user_id") == 0 for k_data in keys.values()):
                 # Check if there's any key that could act as default
                has_potential_default = any(k_data.get("user_id") == 0 for k_data in keys.values())
                if not has_potential_default:
                    return self._create_default_key_structure() # Create fresh default
            return keys
        except Exception:
            return self._create_default_key_structure()

    def _create_default_key_structure(self):
        default_key = {
            "id": "default",
            "name": localization.get_string("DEFAULT_KEY_NAME"),
            "key": self.plugin.get_setting("key", "mySecretKey"), # Get from old settings
            "method": self.plugin.get_setting("method", 0),     # Get from old settings
            "user_id": 0,
            "timestamp": int(time.time())
        }
        return {"default": default_key}
            
    def save_keys(self):
        self.plugin.set_setting("encryption_keys", json.dumps(self.keys))
        
    def add_key(self, name, key_value, method, user_id=0):
        key_id = hashlib.md5((str(user_id) + str(time.time()) + name + key_value).encode()).hexdigest()[:10]
        # If adding a default key (user_id=0), ensure it gets the 'default' id if no other default exists
        # or replace existing 'default' key.
        is_explicit_default = (user_id == 0 and name == localization.get_string("DEFAULT_KEY_NAME"))

        if user_id == 0:
            # Check if a key with id 'default' already exists. If so, update it.
            # Or, if multiple user_id=0 keys exist, this logic might need refinement to pick one 'true' default.
            # For now, new user_id=0 keys will get a new ID unless they are the 'canonical' default.
            existing_default_id = None
            for k_id, k_data in self.keys.items():
                if k_data.get("user_id") == 0 and k_id == "default": # Canonical default
                    existing_default_id = k_id
                    break
                elif k_data.get("user_id") == 0 and name == k_data.get("name"): # A named default
                    existing_default_id = k_id # update this one
                    break


            if is_explicit_default or (existing_default_id and self.keys[existing_default_id]["name"] == name):
                 key_id = "default" # Overwrite/set the canonical default key
            elif user_id == 0 and "default" not in self.keys : # if no key with id 'default' exists make this one
                 key_id = "default"


        new_key = {
            "id": key_id, "name": name, "key": key_value, "method": method,
            "user_id": user_id, "timestamp": int(time.time())
        }
        self.keys[key_id] = new_key
        self.save_keys()
        return key_id
        
    def update_key(self, key_id, name=None, key_value=None, method=None, user_id=None):
        if key_id in self.keys:
            if name is not None: self.keys[key_id]["name"] = name
            if key_value is not None: self.keys[key_id]["key"] = key_value
            if method is not None: self.keys[key_id]["method"] = method
            if user_id is not None: self.keys[key_id]["user_id"] = user_id
            self.keys[key_id]["timestamp"] = int(time.time())
            
            # If this key is becoming the new default (user_id=0) and its ID is not 'default',
            # And if the old 'default' key is different or being changed away from default.
            if user_id == 0 and key_id != "default":
                if "default" in self.keys and self.keys["default"]["id"] != key_id : # if there is another default
                    # Potentially demote old 'default' if it's no longer user_id=0 or different name
                    pass # This case can be complex; for now, we allow multiple user_id=0 keys.
                       # The `get_key_for_user` will pick one.
                # If this key is now the one named "Default Key" and user_id is 0, make its ID 'default'
                if name == localization.get_string("DEFAULT_KEY_NAME"):
                    old_key_data = self.keys.pop(key_id) # remove by old id
                    old_key_data["id"] = "default"       # assign new id
                    self.keys["default"] = old_key_data  # re-insert with new id

            self.save_keys()
            return True
        return False
        
    def delete_key(self, key_id):
        if key_id in self.keys:
            # Prevent deleting the last default key if it's the only key left
            is_default_key = (self.keys[key_id].get("user_id") == 0 and key_id == "default")
            num_keys = len(self.keys)
            if is_default_key and num_keys == 1:
                # log("Cannot delete the last default key.") # Or show a message to user
                return False 
            del self.keys[key_id]
            # If we deleted the 'default' key, try to promote another user_id=0 key to 'default'
            if key_id == "default" and "default" not in self.keys:
                found_new_default = False
                for k_id_new, k_data_new in self.keys.items():
                    if k_data_new.get("user_id") == 0:
                        promoted_key = self.keys.pop(k_id_new)
                        promoted_key["id"] = "default"
                        self.keys["default"] = promoted_key
                        found_new_default = True
                        break
                if not found_new_default and not self.keys: # No keys left
                     self.keys = self._create_default_key_structure() # re-create a default one

            self.save_keys()
            return True
        return False
        
    def get_key(self, key_id):
        return self.keys.get(key_id)
        
    def get_key_for_user(self, user_id):
        # 1. Exact match for user_id
        for key_data in self.keys.values():
            if key_data.get("user_id") == user_id:
                return key_data
        
        # 2. Key with ID 'default' if user_id specific not found and user_id is not 0
        if user_id != 0 and "default" in self.keys and self.keys["default"].get("user_id") == 0:
            return self.keys["default"]

        # 3. Any other key marked with user_id = 0 (fallback default)
        for key_data in self.keys.values():
            if key_data.get("user_id") == 0:
                return key_data
                
        # 4. If absolutely no default-like key, return the first key available
        if self.keys:
            return list(self.keys.values())[0]
            
        # 5. If no keys at all, create and return a new default structure
        # This case should ideally be handled by _load_keys ensuring a default exists
        default_key_struct = self._create_default_key_structure()
        self.keys.update(default_key_struct)
        self.save_keys()
        return default_key_struct["default"]

    
    def get_all_keys(self):
        return sorted(list(self.keys.values()), key=lambda k: (k.get("user_id", 0) != 0, k.get("name", "")))

    
    def export_key_data(self, key_id):
        if key_id in self.keys:
            key_data = self.keys[key_id]
            # Format: name:key_value:method:timestamp (PG_KEY:name:key_value:method:timestamp)
            return f"PG_KEY:{key_data['name']}:{key_data['key']}:{key_data['method']}:{key_data['timestamp']}"
        return None
    
    def import_key_data(self, key_str, user_id_for_association):
        # Expects key_str in format "PG_KEY:name:key_value:method:timestamp"
        try:
            if key_str.startswith("PG_KEY:"):
                parts = key_str[len("PG_KEY:"):].split(":", 3) # Max 3 splits for 4 parts
                if len(parts) == 4:
                    name, key_value, method_str, timestamp_str = parts
                    method = int(method_str)
                    _ = int(timestamp_str) # Validate timestamp
                    
                    # Check if a key for this user_id with the same name/value already exists to avoid dupes
                    # Or decide on an update policy (e.g., if timestamp is newer)
                    for existing_key in self.keys.values():
                        if existing_key.get("user_id") == user_id_for_association and \
                           existing_key.get("name") == name and \
                           existing_key.get("key") == key_value and \
                           existing_key.get("method") == method:
                            # log(f"Key '{name}' already exists for user {user_id_for_association}. Not re-adding.")
                            return existing_key["id"] # Return existing ID

                    return self.add_key(name, key_value, method, user_id_for_association)
            return None
        except Exception as e:
            # log(f"Error importing key data: {e}")
            return None

def encrypt_caesar(text, shift):
    result = ""
    for char in text:
        if char.isalpha():
            is_upper = char.isupper()
            start_ord = ord('A') if is_upper else ord('a')
            # Ensure shift is within 0-25 for modular arithmetic if it came from sum of ords
            final_shift = shift % 26 if shift >= 0 else (shift % 26 + 26) % 26
            shifted_char_ord = (ord(char) - start_ord + final_shift) % 26
            result += chr(start_ord + shifted_char_ord)
        else:
            result += char
    return result

def decrypt_caesar(text, shift):
    # Ensure shift is within 0-25 for modular arithmetic
    final_shift = shift % 26 if shift >= 0 else (shift % 26 + 26) % 26
    return encrypt_caesar(text, (26 - final_shift) % 26)


def encrypt_xor(text, key):
    if not key: return text # Or handle as an error/no-op
    key_bytes = key.encode('utf-8')
    key_len = len(key_bytes)
    if key_len == 0: return text # Avoid division by zero if key becomes empty string
    text_bytes = text.encode('utf-8')
    encrypted = bytearray()
    for i in range(len(text_bytes)):
        encrypted.append(text_bytes[i] ^ key_bytes[i % key_len])
    return base64.b64encode(encrypted).decode('utf-8')

def decrypt_xor(text, key):
    try:
        if not key: return None
        encrypted = base64.b64decode(text.encode('utf-8'))
        key_bytes = key.encode('utf-8')
        key_len = len(key_bytes)
        if key_len == 0: return None
        decrypted = bytearray()
        for i in range(len(encrypted)):
            decrypted.append(encrypted[i] ^ key_bytes[i % key_len])
        return decrypted.decode('utf-8')
    except: return None

def encrypt_aes(text, key): # Note: This is a stream cipher XORing with SHA256 hash, not true AES block cipher
    if not key: return text
    key_hash = hashlib.sha256(key.encode('utf-8')).digest()
    text_bytes = text.encode('utf-8')
    encrypted = bytearray()
    for i in range(len(text_bytes)):
        encrypted.append(text_bytes[i] ^ key_hash[i % len(key_hash)])
    return base64.b64encode(encrypted).decode('utf-8')

def decrypt_aes(text, key): # Note: This is a stream cipher XORing with SHA256 hash, not true AES block cipher
    try:
        if not key: return None
        key_hash = hashlib.sha256(key.encode('utf-8')).digest()
        encrypted = base64.b64decode(text.encode('utf-8'))
        decrypted = bytearray()
        for i in range(len(encrypted)):
            decrypted.append(encrypted[i] ^ key_hash[i % len(key_hash)])
        return decrypted.decode('utf-8')
    except: return None

def encrypt_rot13(text, _): # Key is ignored
    result = ""
    for char in text:
        if char.isalpha():
            start_ord = ord('a') if char.islower() else ord('A')
            rotated_char_ord = (ord(char) - start_ord + 13) % 26
            result += chr(start_ord + rotated_char_ord)
        else: result += char
    return result

def decrypt_rot13(text, _): return encrypt_rot13(text, None)

def encrypt_atbash(text, _): # Key is ignored
    result = ""
    for char in text:
        if char.isalpha():
            if char.islower(): result += chr(ord('a') + (ord('z') - ord(char)))
            else: result += chr(ord('A') + (ord('Z') - ord(char)))
        else: result += char
    return result

def decrypt_atbash(text, _): return encrypt_atbash(text, None)

def encrypt_vigenere(text, key):
    if not key: return text
    key_only_alpha = ''.join(c for c in key if c.isalpha()).lower()
    if not key_only_alpha: return text # If key has no alphabetic chars, treat as no-op
    result = ""; key_idx = 0
    for char_in_text in text:
        if char_in_text.isalpha():
            key_c = key_only_alpha[key_idx % len(key_only_alpha)]
            key_s = ord(key_c) - ord('a')
            start_o = ord('A') if char_in_text.isupper() else ord('a')
            enc_o = (ord(char_in_text) - start_o + key_s) % 26
            result += chr(start_o + enc_o)
            key_idx +=1
        else: result += char_in_text
    return result

def decrypt_vigenere(text, key):
    if not key: return text
    key_only_alpha = ''.join(c for c in key if c.isalpha()).lower()
    if not key_only_alpha: return text
    result = ""; key_idx = 0
    for char_in_text in text:
        if char_in_text.isalpha():
            key_c = key_only_alpha[key_idx % len(key_only_alpha)]
            key_s = ord(key_c) - ord('a')
            start_o = ord('A') if char_in_text.isupper() else ord('a')
            dec_o = (ord(char_in_text) - start_o - key_s + 26) % 26 # Ensure positive before modulo
            result += chr(start_o + dec_o)
            key_idx += 1
        else: result += char_in_text
    return result

def encrypt_base64(text, _): # Key is ignored
    try: return base64.b64encode(text.encode('utf-8')).decode('utf-8')
    except: return text

def decrypt_base64(text, _): # Key is ignored
    try: return base64.b64decode(text.encode('utf-8')).decode('utf-8')
    except: return None

MORSE_DICT_ENCRYPT = {
    'A':'.-','B':'-...','C':'-.-.','D':'-..','E':'.','F':'..-.','G':'--.','H':'....','I':'..','J':'.---','K':'-.-','L':'.-..',
    'M':'--','N':'-.','O':'---','P':'.--.','Q':'--.-','R':'.-.','S':'...','T':'-','U':'..-','V':'...-','W':'.--','X':'-..-',
    'Y':'-.--','Z':'--..','0':'-----','1':'.----','2':'..---','3':'...--','4':'....-','5':'.....','6':'-....','7':'--...',
    '8':'---..','9':'----.','А':'.-','Б':'-...','В':'.--','Г':'--.','Д':'-..','Е':'.','Ё':'.','Ж':'...-.','З':'--..',
    'И':'..','Й':'.---','К':'-.-','Л':'.-..','М':'--','Н':'-.','О':'---','П':'.--.','Р':'.-.','С':'...','Т':'-',
    'У':'..-','Ф':'..-.','Х':'....','Ц':'-.-.','Ч':'---.','Ш':'----','Щ':'--.-','Ъ':'--.--','Ы':'-.--','Ь':'-..-',
    'Э':'..-..','Ю':'..--','Я':'.-.-',' ':'/'
}
def encrypt_morse(text, _): # Key is ignored
    res = []
    for char_in_text in text.upper(): # Convert to upper for consistent lookup
        if char_in_text in MORSE_DICT_ENCRYPT: res.append(MORSE_DICT_ENCRYPT[char_in_text])
        # Morse code usually doesn't distinguish between space and word separator /
        # The current code adds MORSE_DICT_ENCRYPT[' '] for spaces. This is fine.
        elif char_in_text.isspace() and (not res or res[-1] != MORSE_DICT_ENCRYPT[' ']): # Add / for space if not already last
             res.append(MORSE_DICT_ENCRYPT[' '])
        # else: skip characters not in dict (or handle them, e.g. with '?')
    return " ".join(filter(None, res)) # Filter None in case of complex logic, join with space

REVERSE_MORSE_DUAL_LOOKUP = { # Based on your original structure
    '.-':{'en':'A','ru':'А'},'-...':{'en':'B','ru':'Б'},'.--':{'en':'W','ru':'В'},'--.':{'en':'G','ru':'Г'},
    '-..':{'en':'D','ru':'Д'},'.':{'en':'E','ru':'Е'},'.---':{'en':'J','ru':'Й'},'-.-':{'en':'K','ru':'К'},
    '.-..':{'en':'L','ru':'Л'},'--':{'en':'M','ru':'М'},'-.':{'en':'N','ru':'Н'},'---':{'en':'O','ru':'О'},
    '.--.':{'en':'P','ru':'П'},'.-.':{'en':'R','ru':'Р'},'...':{'en':'S','ru':'С'},'-':{'en':'T','ru':'Т'},
    '..-':{'en':'U','ru':'У'},'..-.':{'en':'F','ru':'Ф'},'....':{'en':'H','ru':'Х'},'--..':{'en':'Z','ru':'З'},
    '..':{'en':'I','ru':'И'},'-.--':{'en':'Y','ru':'Ы'},'-..-':{'en':'X','ru':'Ь'},'-.-.':{'en':'C','ru':'Ц'},
    '--.-':{'en':'Q','ru':'Щ'}
}
REVERSE_MORSE_UNIQUE_LOOKUP = { # Characters with unique Morse codes across EN/RU used here
    '...-':'V','...-.':'Ж','---.':'Ч','----':'Ш','--.--':'Ъ','..-..':'Э','..--':'Ю','.-.-':'Я',
    '-----':'0','.----':'1','..---':'2','...--':'3','....-':'4','.....':'5','-....':'6','--...':'7',
    '---..':'8','----.':'9','/':' ' # '/' is space
}
def decrypt_morse(text, _): # Key is ignored
    try:
        res = []; morse_chars = text.strip().split(' '); pref_lang = localization.language
        for mc in morse_chars:
            if not mc: continue # Skip empty strings from multiple spaces
            found_char = False
            if mc in REVERSE_MORSE_DUAL_LOOKUP:
                options = REVERSE_MORSE_DUAL_LOOKUP[mc]
                if pref_lang in options: res.append(options[pref_lang]); found_char = True
                elif 'en' in options: res.append(options['en']); found_char = True # Fallback to EN
                elif 'ru' in options: res.append(options['ru']); found_char = True # Fallback to RU
                else: res.append('?'); found_char = True # Should not happen with current dict
            
            if not found_char and mc in REVERSE_MORSE_UNIQUE_LOOKUP:
                res.append(REVERSE_MORSE_UNIQUE_LOOKUP[mc]); found_char = True
            
            if not found_char: res.append(mc) # Keep unknown sequences as is
        return "".join(res)
    except: return None

def encrypt_rc4(text, key): # Simplified RC4-like stream cipher
    if not key: return text
    S = list(range(256)); j = 0; key_b = key.encode('utf-8')
    key_len_rc4 = len(key_b)
    if key_len_rc4 == 0: return text

    for i in range(256): j = (j + S[i] + key_b[i % key_len_rc4]) % 256; S[i], S[j] = S[j], S[i]
    
    i = j = 0; res_b = bytearray(); text_b = text.encode('utf-8')
    for char_b in text_b:
        i = (i + 1) % 256; j = (j + S[i]) % 256; S[i], S[j] = S[j], S[i]
        k_b = S[(S[i] + S[j]) % 256]; res_b.append(char_b ^ k_b)
    return base64.b64encode(res_b).decode('utf-8')

def decrypt_rc4(text, key): # Simplified RC4-like stream cipher
    try:
        if not key: return None
        decoded_b = base64.b64decode(text.encode('utf-8'))
        
        S = list(range(256)); j_rc4 = 0; key_b_rc4 = key.encode('utf-8')
        key_len_rc4_dec = len(key_b_rc4)
        if key_len_rc4_dec == 0: return None
        
        for i_rc4_ksa in range(256): 
            j_rc4 = (j_rc4 + S[i_rc4_ksa] + key_b_rc4[i_rc4_ksa % key_len_rc4_dec]) % 256
            S[i_rc4_ksa], S[j_rc4] = S[j_rc4], S[i_rc4_ksa]
        
        i_rc4_prga = 0; j_rc4_prga = 0 # Separate j for PRGA from KSA's j
        res_b_rc4 = bytearray()
        for char_b_rc4 in decoded_b:
            i_rc4_prga = (i_rc4_prga + 1) % 256
            j_rc4_prga = (j_rc4_prga + S[i_rc4_prga]) % 256
            S[i_rc4_prga], S[j_rc4_prga] = S[j_rc4_prga], S[i_rc4_prga]
            k_b_rc4 = S[(S[i_rc4_prga] + S[j_rc4_prga]) % 256]
            res_b_rc4.append(char_b_rc4 ^ k_b_rc4)
        return res_b_rc4.decode('utf-8')
    except: return None

def encrypt_blowfish(text, key): # Simplified Blowfish-like cipher (additive mixing with PRNG)
    if not key: return text
    # Ensure seed is an int. SHA512 produces 64 bytes. Take first 8 for a 64-bit int, or hash of key for simplicity.
    seed_material = hashlib.sha256(key.encode('utf-8')).digest() # Use SHA256 for seed
    seed = int.from_bytes(seed_material[:8], 'big') # Use first 8 bytes of hash as seed
    
    _rand = random.Random(seed); text_b = text.encode('utf-8'); enc = bytearray()
    for byte_val in text_b: mix_b = _rand.randint(0,255); enc.append((byte_val+mix_b)%256)
    return base64.b64encode(enc).decode('utf-8')

def decrypt_blowfish(text, key): # Simplified Blowfish-like cipher
    try:
        if not key: return None
        seed_material = hashlib.sha256(key.encode('utf-8')).digest()
        seed = int.from_bytes(seed_material[:8], 'big')

        _rand = random.Random(seed); enc_b = base64.b64decode(text.encode('utf-8')); dec = bytearray()
        for byte_val in enc_b: mix_b = _rand.randint(0,255); dec.append((byte_val-mix_b+256)%256)
        return dec.decode('utf-8')
    except: return None

def _apply_custom_des_round(data_b, key_b, is_enc_r): # Simplified DES-like round
    val = data_b
    # Reduced rounds for simplicity, actual DES has 16. Let's use a few.
    num_rounds = 4 # Example: 4 rounds instead of 8 for more distinction from just XOR
    for _ in range(num_rounds): 
        if is_enc_r: val = ((val<<1)|(val>>7))&0xFF; val = (val^key_b)&0xFF # Rotate left, XOR
        else: val = (val^key_b)&0xFF; val = ((val>>1)|(val<<7))&0xFF # XOR, Rotate right
    return val

def _custom_des_process(data_bs, key_material, is_enc_op): # Simplified DES-like processing
    proc_bs = bytearray()
    # Key material should be long enough, e.g. MD5 digest (16 bytes)
    key_len = len(key_material)
    if key_len == 0: return data_bs # Should not happen with MD5

    for i, byte_val in enumerate(data_bs):
        key_b_fr = key_material[i % key_len] # Use a byte from key_material for each data byte
        proc_bs.append(_apply_custom_des_round(byte_val, key_b_fr, is_enc_op))
    return proc_bs

def encrypt_des3(text, key): # Simplified 3DES-like structure EDE
    if not key: return text
    text_b = text.encode('utf-8')
    # Derive 3 distinct subkeys from the main key using hashing
    k_h1=hashlib.md5((key+"_sDES_k1").encode('utf-8')).digest()
    k_h2=hashlib.md5((key+"_sDES_k2").encode('utf-8')).digest()
    k_h3=hashlib.md5((key+"_sDES_k3").encode('utf-8')).digest()
    
    intermediate1 = _custom_des_process(text_b, k_h1, True)      # Encrypt with k1
    intermediate2 = _custom_des_process(intermediate1, k_h2, False) # Decrypt with k2
    final_encrypted = _custom_des_process(intermediate2, k_h3, True) # Encrypt with k3
    return base64.b64encode(final_encrypted).decode('utf-8')

def decrypt_des3(text, key): # Simplified 3DES-like structure DED
    try:
        if not key: return None
        enc_b = base64.b64decode(text.encode('utf-8'))
        k_h1=hashlib.md5((key+"_sDES_k1").encode('utf-8')).digest()
        k_h2=hashlib.md5((key+"_sDES_k2").encode('utf-8')).digest()
        k_h3=hashlib.md5((key+"_sDES_k3").encode('utf-8')).digest()

        intermediate1 = _custom_des_process(enc_b, k_h3, False)      # Decrypt with k3
        intermediate2 = _custom_des_process(intermediate1, k_h2, True)  # Encrypt with k2
        final_decrypted = _custom_des_process(intermediate2, k_h1, False) # Decrypt with k1
        return final_decrypted.decode('utf-8')
    except: return None

# --- RSA and ElGamal Helpers ---
def modInverse(e, phi_n):
    # Using pow(e, -1, phi_n) requires Python 3.8+
    # For compatibility or older Pythons, an extended Euclidean algorithm would be needed.
    # Assuming Python 3.8+ based on plugin environment.
    if phi_n == 0: return None # Avoid division by zero or issues with phi_n=0
    try:
        inv = pow(e, -1, phi_n)
        return inv
    except ValueError: # Inverse does not exist
        return None

_miller_rabin_random_source = random.Random() # Independent PRNG for Miller-Rabin
def is_prime_miller_rabin(n, k=10): # Miller-Rabin primality test
    if n < 2: return False
    if n == 2 or n == 3: return True
    if n % 2 == 0 or n % 3 == 0: return False # Small prime divisors
    d, s = n - 1, 0
    while d % 2 == 0: d //= 2; s += 1
    
    for _ in range(k): # k iterations for confidence
        a = _miller_rabin_random_source.randrange(2, n - 2) # a in [2, n-2]
        x = pow(a, d, n)
        if x == 1 or x == n - 1: continue
        for _ in range(s - 1):
            x = pow(x, 2, n)
            if x == n - 1: break
        else: return False # n is composite
    return True # n is probably prime

ALL_DEFAULT_FALLBACK_PRIMES = [
    2003, 2011, 2017, 2027, 2029, 2039, 2053, 2063, 2069, 2081, 2083, 2087, 2089, 2099,
    3001, 3011, 3019, 3023, 3037, 3041, 3049, 3061, 3067, 3079, 3083, 3089,
    4001, 4003, 4007, 4013, 4019, 4021, 4027, 4049, 4051, 4057, 
    # Larger primes for e=65537 compatibility
    65539, 65543, 65551, 65557, 65563, 65579, 65581, 65587, 65599, 65609, 
    65617, 65621, 65629, 65633, 65641, 65651
]
def get_random_prime_in_range(min_val, max_val, prng_instance, e_coprime_val=None):
    if min_val > max_val: min_val, max_val = max_val, min_val # Swap if inverted
    if min_val < 2: min_val = 2
    
    # Start with a random odd number in range
    n_candidate = prng_instance.randint(min_val, max_val)
    if n_candidate % 2 == 0: n_candidate += 1
    if n_candidate < min_val: n_candidate = min_val # if min_val was even and n_candidate became min_val-1
    if n_candidate % 2 == 0 and n_candidate > 2: n_candidate = min_val + 1 if min_val % 2 == 0 else min_val
    if n_candidate < min_val: n_candidate = min_val # final check
    if n_candidate % 2 == 0: n_candidate +=1 # ensure odd starting point if min_val is even


    max_attempts = max(100, (max_val - min_val) // 2 + 20) # More attempts for larger ranges
    initial_candidate = n_candidate

    for attempt in range(max_attempts):
        if n_candidate > max_val: # Wrap around search within the range
            n_candidate = min_val 
            if n_candidate % 2 == 0: n_candidate += 1
        
        is_p = is_prime_miller_rabin(n_candidate)
        passes_e_cond = True
        if e_coprime_val and is_p:
            # Check gcd(phi(p), e_coprime_val) == 1 by checking (p-1) % factor_of_e != 0
            # If e_coprime_val is prime (like 65537), then (p-1) % e_coprime_val != 0
            if (n_candidate - 1) % e_coprime_val == 0:
                passes_e_cond = False
        
        if is_p and passes_e_cond:
            return n_candidate
        
        n_candidate += 2 # Check next odd number
        if attempt == max_attempts // 2 and n_candidate > initial_candidate + (max_val-min_val): # Avoid overly long sequences if stuck
             n_candidate = prng_instance.randint(min_val, max_val) # Resample
             if n_candidate % 2 == 0: n_candidate += 1


    # Fallback if no prime found after attempts
    fallback_options = [p for p in ALL_DEFAULT_FALLBACK_PRIMES 
                        if min_val <= p <= max_val and \
                           (True if not e_coprime_val else ((p-1) % e_coprime_val != 0))]
    return prng_instance.choice(fallback_options) if fallback_options else None


def encrypt_rsa(text, key): # Simplified RSA (textbook RSA, vulnerable)
    if not key: return text
    if not text: return f"|{65537+2}" # Handle empty text, return format with a dummy 'n'

    e = 65537 # Common public exponent
    
    # Seed PRNG with key's hash for deterministic n generation
    key_hash_bytes = hashlib.sha256(key.encode('utf-8')).digest()
    seed_val = int.from_bytes(key_hash_bytes[:8], 'big') # Use first 8 bytes of hash for seed
    _random_gen = random.Random(seed_val)
    
    # n must be > max possible byte value (255) and phi(n) must be coprime to e
    n_min_val = max(256, e + 2) # Ensure n > 255 and e < n-1
    n_max_val = n_min_val + 10000 + _random_gen.randint(0, 5000) # Range for prime search, slightly randomized
                                  
    n = get_random_prime_in_range(n_min_val, n_max_val, _random_gen, e_coprime_val=e)
    
    if n is None: # Fallback if prime generation fails
        # log("RSA: Prime generation failed, returning original text.")
        return text 

    # phi_n = n - 1 (since n is prime in this simplified model)
    # d is not needed for encryption part if we only use public (e,n)

    result_parts = []
    for char_byte in text.encode('utf-8'):
        m = char_byte 
        c = pow(m, e, n) 
        result_parts.append(str(c))
    
    final_payload = ",".join(result_parts)
    return base64.b64encode(final_payload.encode('utf-8')).decode('utf-8') + f"|{n}"

def decrypt_rsa(text_with_n, key): # Simplified RSA decryption
    try:
        if not key: return None
        
        parts = text_with_n.split('|')
        if len(parts) != 2: return None # Expecting "b64_payload|n"
        b64_payload, n_str = parts
        
        if not n_str: return None # n is mandatory
        n = int(n_str)

        if not b64_payload: # Original text was empty
             # Validate n by re-deriving it if needed, or just accept it.
             # For now, if b64_payload is empty, assume original was empty.
             return ""

        e = 65537
        
        # Re-derive 'n' using the key to ensure consistency if needed,
        # but here 'n' is taken from ciphertext. Consistency of 'd' depends on this 'n'.
        # For this model, phi_n = n-1.
        phi_n = n - 1 
        if phi_n <= 0: return None 

        d = modInverse(e, phi_n)
        if d is None: 
            # log(f"RSA Decrypt: Modular inverse for d does not exist (e={e}, phi_n={phi_n}). Key or n mismatch?")
            return None

        encrypted_values_str = base64.b64decode(b64_payload.encode('utf-8')).decode('utf-8')
        if not encrypted_values_str: # Possible if original string was empty and b64 of "" is ""
            return ""

        encrypted_values = encrypted_values_str.split(',')
        
        decrypted_bytes = bytearray()
        for val_str in encrypted_values:
            if not val_str: continue # Skip empty parts from "val1,,val2"
            c = int(val_str)
            m = pow(c, d, n) 
            decrypted_bytes.append(m)
        return decrypted_bytes.decode('utf-8', errors='replace')
    except Exception as ex:
        # log(f"RSA Decryption failed: {ex}")
        return None

def encrypt_elgamal(text, key): # Simplified ElGamal
    if not key: return text
    key_hash_full = hashlib.sha512(key.encode('utf-8')).digest() # 64 bytes

    if not text: # Handle empty text case, need to return p and g
        # Generate p and g even for empty text so format is consistent
        prime_gen_seed = int.from_bytes(key_hash_full[:8], 'big')
        _elgamal_prng_setup = random.Random(prime_gen_seed)
        p_min_val_empty = max(256 * 2, 3000) 
        p_max_val_empty = p_min_val_empty + 5000 + _elgamal_prng_setup.randint(0,1000)
        p_empty = get_random_prime_in_range(p_min_val_empty, p_max_val_empty, _elgamal_prng_setup)
        if p_empty is None: p_empty = ALL_DEFAULT_FALLBACK_PRIMES[-1] # Failsafe p
        g_empty = _elgamal_prng_setup.randint(2, p_empty - 2) if p_empty > 3 else 2
        return f"|{p_empty}|{g_empty}"


    # Seed for PRNG for p and g generation
    prime_gen_seed = int.from_bytes(key_hash_full[:8], 'big')
    _elgamal_prng = random.Random(prime_gen_seed)

    p_min_val = max(256 * 2, 3000) # m*s could be up to (p-1)^2, ensure p is large enough
    p_max_val = p_min_val + 5000 + _elgamal_prng.randint(0,2000) # Randomized range
    p = get_random_prime_in_range(p_min_val, p_max_val, _elgamal_prng)
    if p is None: 
        # log("ElGamal: Prime p generation failed.")
        return text 

    g = _elgamal_prng.randint(2, p - 2) if p > 3 else 2 # Generator g

    # Private key 'a' (1 <= a < p-1) from key hash
    a = (int.from_bytes(key_hash_full[8:16], 'big') % (p - 2)) + 1 
    # Public key h = g^a mod p
    h = pow(g, a, p)

    # PRNG for ephemeral key k, seeded from another part of key hash
    k_prng_seed = int.from_bytes(key_hash_full[16:24], 'big')
    _k_prng = random.Random(k_prng_seed)

    result_pairs = []
    for char_byte in text.encode('utf-8'):
        m = char_byte
        if m >= p : # Should not happen if p is large enough (e.g. >255)
             # log(f"Warning: ElGamal message byte {m} >= prime {p}. Skipping encryption for this byte.")
             # Store as 0,m (c1=0 indicates unencrypted byte)
             result_pairs.append(f"0,{m}"); continue

        k = _k_prng.randint(1, p - 2) # Ephemeral key k (1 <= k < p-1)
        c1 = pow(g, k, p)
        s_shared = pow(h, k, p) 
        c2 = (m * s_shared) % p 
        result_pairs.append(f"{c1},{c2}")
    
    final_payload = ",".join(result_pairs)
    return base64.b64encode(final_payload.encode('utf-8')).decode('utf-8') + f"|{p}|{g}"

def decrypt_elgamal(text_with_params, key): # Simplified ElGamal decryption
    try:
        if not key: return None
        parts = text_with_params.split('|')
        if len(parts) != 3: return None # "b64_payload|p|g"
        b64_payload, p_str, g_str = parts
        
        if not p_str or not g_str: return None # p and g are mandatory
        p = int(p_str)
        # g = int(g_str) # g is not strictly needed for decryption if 'a' is re-derived

        if not b64_payload: # Original text was empty
            return ""

        key_hash_full = hashlib.sha512(key.encode('utf-8')).digest()
        # Recalculate private key 'a' using the same method as encryption
        a = (int.from_bytes(key_hash_full[8:16], 'big') % (p - 2)) + 1

        ciphertext_b64_decoded_str = base64.b64decode(b64_payload.encode('utf-8')).decode('utf-8')
        if not ciphertext_b64_decoded_str: # Empty payload after decode means empty original message
            return ""

        pairs_str = ciphertext_b64_decoded_str.split(',')
        if len(pairs_str) % 2 != 0 and pairs_str != ['']: # Each original byte produces two numbers (c1,c2)
            # log("ElGamal Decrypt: Malformed ciphertext, odd number of pair components.")
            return None


        decrypted_bytes = bytearray()
        i = 0
        while i < len(pairs_str) -1 : 
            c1_str, c2_str = pairs_str[i], pairs_str[i+1]
            if not c1_str or not c2_str: # Should not happen if split correctly
                i += 2; continue 
            
            c1 = int(c1_str); c2 = int(c2_str)

            if c1 == 0: # Special case: unencrypted byte (m >= p during encryption)
                decrypted_bytes.append(c2)
                i += 2
                continue

            s_dec = pow(c1, a, p) # s = c1^a mod p
            s_inv = modInverse(s_dec, p) 
            if s_inv is None: 
                # log(f"ElGamal Decrypt: Modular inverse for s_dec (s_dec={s_dec}, p={p}) failed.")
                return None 
            
            m = (c2 * s_inv) % p 
            decrypted_bytes.append(m)
            i += 2
        return decrypted_bytes.decode('utf-8', errors='replace')
    except Exception as ex:
        # log(f"ElGamal Decryption failed: {ex}")
        return None


# --- Start of new "Chaos Cipher" ---
def encrypt_chaos(text, key):
    if not key: return text
    if not text: return "" # Empty input yields empty output for this cipher

    try:
        dk = hashlib.sha512(key.encode('utf-8')).digest() # 64 bytes
        dk_xor1, dk_prng_seed, dk_rot_params, dk_xor2, dk_chaff_control = \
            dk[0:16], dk[16:24], dk[24:40], dk[40:56], dk[56:64]

        prng_seed_int = int.from_bytes(dk_prng_seed, 'big')
        prng = random.Random(prng_seed_int)

        s_box = list(range(256)); prng.shuffle(s_box)

        chaff_prob_numerator = (dk_chaff_control[0] % 41) + 10 
        chaff_insertion_probability = chaff_prob_numerator / 100.0

        input_bytes = text.encode('utf-8')
        original_length = len(input_bytes)
        processed_bytes = bytearray()

        for i in range(original_length):
            byte_val = input_bytes[i]
            byte_val ^= dk_xor1[i % len(dk_xor1)]
            byte_val = s_box[byte_val]
            
            rotation_amount = dk_rot_params[i % len(dk_rot_params)] % 8 
            if rotation_amount > 0:
                direction_byte_idx = (i + len(dk_rot_params)//2) % len(dk_rot_params)
                rotation_direction = dk_rot_params[direction_byte_idx] % 2
                if rotation_direction == 0: # ROL
                    byte_val = ((byte_val << rotation_amount) | (byte_val >> (8 - rotation_amount))) & 0xFF
                else: # ROR
                    byte_val = ((byte_val >> rotation_amount) | (byte_val << (8 - rotation_amount))) & 0xFF
            
            byte_val ^= dk_xor2[i % len(dk_xor2)]
            processed_bytes.append(byte_val)
            
            if prng.random() < chaff_insertion_probability: # PRNG Call 1 (Decision)
                chaff_byte = prng.randint(0, 255)          # PRNG Call 2 (Value)
                processed_bytes.append(chaff_byte)
        
        length_bytes = original_length.to_bytes(4, 'big')
        final_encrypted_payload = length_bytes + processed_bytes
        return base64.b64encode(final_encrypted_payload).decode('utf-8')
    except Exception as e:
        # log(f"Chaos Encryption Error: {e}")
        return text # Fallback

def decrypt_chaos(b64_encrypted_text, key):
    if not key: return None 
    if not b64_encrypted_text: return ""

    try:
        dk = hashlib.sha512(key.encode('utf-8')).digest()
        dk_xor1, dk_prng_seed, dk_rot_params, dk_xor2, dk_chaff_control = \
            dk[0:16], dk[16:24], dk[24:40], dk[40:56], dk[56:64]

        prng_seed_int = int.from_bytes(dk_prng_seed, 'big')
        prng = random.Random(prng_seed_int)

        s_box = list(range(256)); prng.shuffle(s_box) # Regenerate S-Box
        rev_s_box = [0] * 256
        for i, val in enumerate(s_box): rev_s_box[val] = i

        chaff_prob_numerator = (dk_chaff_control[0] % 41) + 10
        chaff_insertion_probability = chaff_prob_numerator / 100.0

        encrypted_payload_with_len = base64.b64decode(b64_encrypted_text.encode('utf-8'))
        if len(encrypted_payload_with_len) < 4: return None 

        original_length = int.from_bytes(encrypted_payload_with_len[:4], 'big')
        data_stream_with_chaff = encrypted_payload_with_len[4:]
        decrypted_original_bytes = bytearray()
        current_data_stream_idx = 0

        for i in range(original_length):
            if current_data_stream_idx >= len(data_stream_with_chaff): return None
            byte_val = data_stream_with_chaff[current_data_stream_idx]; current_data_stream_idx += 1
            
            byte_val ^= dk_xor2[i % len(dk_xor2)]
            rotation_amount = dk_rot_params[i % len(dk_rot_params)] % 8
            if rotation_amount > 0:
                direction_byte_idx = (i + len(dk_rot_params)//2) % len(dk_rot_params)
                original_rotation_direction = dk_rot_params[direction_byte_idx] % 2
                if original_rotation_direction == 0: # Original ROL, decrypt ROR
                    byte_val = ((byte_val >> rotation_amount) | (byte_val << (8 - rotation_amount))) & 0xFF
                else: # Original ROR, decrypt ROL
                    byte_val = ((byte_val << rotation_amount) | (byte_val >> (8 - rotation_amount))) & 0xFF
            
            byte_val = rev_s_box[byte_val]
            byte_val ^= dk_xor1[i % len(dk_xor1)]
            decrypted_original_bytes.append(byte_val)

            if prng.random() < chaff_insertion_probability:    # Mirror PRNG Call 1
                _ = prng.randint(0, 255)                       # Mirror PRNG Call 2
                if current_data_stream_idx >= len(data_stream_with_chaff): return None
                current_data_stream_idx += 1
        
        if current_data_stream_idx != len(data_stream_with_chaff): return None # Leftover data
        return decrypted_original_bytes.decode('utf-8')
    except UnicodeDecodeError: return None 
    except Exception as e:
        # log(f"Chaos Decryption Error: {e}")
        return None
# --- End of new "Chaos Cipher" ---


class PrivacyGuardPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.key_manager = None
        
    def on_plugin_load(self):
        self.key_manager = KeyManager(self)
        self.add_on_send_message_hook()
        self.add_hook("updateNewMessage", self.on_update_hook) # Corrected hook registration

    def create_settings(self):
        # Assuming ui.settings components are available
        # Mock them if running in an environment without them
        try:
            from ui.settings import Header, Divider, Switch, Selector, Input, Text
        except ImportError:
            # Basic mock for settings components
            class MockSettingComponent:
                def __init__(self, **kwargs): self.kwargs = kwargs
            Header, Divider, Switch, Selector, Input, Text = [MockSettingComponent]*6


        methods = [
            "Caesar cipher (Low)", "XOR encryption (Low)", "AES-like stream (Medium)",
            "ROT13 (Low)", "Atbash cipher (Low)", "Vigenère cipher (Medium)", 
            "Base64 encoding (Low)", "Morse code (Low)", "RC4-like stream (Medium)",
            "Blowfish-like stream (High)", "3DES-like (High)", 
            "RSA simplified (Very High)", "ElGamal simplified (Very High)", 
            "Chaos Cipher (Extreme)" 
        ]
        
        settings = [
            Header(text=localization.get_string("ENCRYPTION_SETTINGS_HEADER") if localization.get_string("ENCRYPTION_SETTINGS_HEADER") != "MISSING_STRING_ENCRYPTION_SETTINGS_HEADER" else "Encryption Settings"),
            Switch(key="enabled", text=localization.get_string("ENABLE_MESSAGE_ENCRYPTION") if localization.get_string("ENABLE_MESSAGE_ENCRYPTION") != "MISSING_STRING_ENABLE_MESSAGE_ENCRYPTION" else "Enable message encryption", icon="ic_lock_24dp", default=False, subtext=localization.get_string("AUTO_ENCRYPT_OUTGOING_SUBTEXT") if localization.get_string("AUTO_ENCRYPT_OUTGOING_SUBTEXT") != "MISSING_STRING_AUTO_ENCRYPT_OUTGOING_SUBTEXT" else "Automatically encrypt all outgoing messages"),
            Selector(key="method", text=localization.get_string("ENCRYPTION_METHOD") if localization.get_string("ENCRYPTION_METHOD") != "MISSING_STRING_ENCRYPTION_METHOD" else "Encryption method", icon="ic_keyboard_24dp", default=0, items=methods),
            Input(key="key", text=localization.get_string("ENCRYPTION_KEY") if localization.get_string("ENCRYPTION_KEY") != "MISSING_STRING_ENCRYPTION_KEY" else "Encryption key", default="mySecretKey", subtext=localization.get_string("KEY_USED_FOR_ENCRYPTION_SUBTEXT") if localization.get_string("KEY_USED_FOR_ENCRYPTION_SUBTEXT") != "MISSING_STRING_KEY_USED_FOR_ENCRYPTION_SUBTEXT" else "Key used for encrypting and decrypting messages"),
            Divider(text=localization.get_string("INFO_USE_DECRYPT") if localization.get_string("INFO_USE_DECRYPT") != "MISSING_STRING_INFO_USE_DECRYPT" else "Use .decrypt to decrypt messages"), 
            Header(text=localization.get_string("APPEARANCE_HEADER") if localization.get_string("APPEARANCE_HEADER") != "MISSING_STRING_APPEARANCE_HEADER" else "Appearance"),
            Switch(key="show_lock", text=localization.get_string("SHOW_LOCK_EMOJI"), default=True, subtext=localization.get_string("SHOW_LOCK_EMOJI_SUBTEXT")),
            Switch(key="show_method", text=localization.get_string("SHOW_METHOD_INFO"), default=False, subtext=localization.get_string("SHOW_METHOD_INFO_SUBTEXT")),
            Header(text=localization.get_string("KEY_MANAGEMENT")),
            Switch(key="enable_key_sync", text=localization.get_string("ENABLE_KEY_SYNC"), default=False, subtext=localization.get_string("ENABLE_KEY_SYNC_SUBTEXT")),
            Text(text=localization.get_string("SHARE_KEY_COMMAND"), icon="ic_share_24dp"),
            Text(text=localization.get_string("SHARE_KEY_ACCEPT"), icon="ic_download_24dp"),
            Text(text=localization.get_string("KEY_LIST"), icon="ic_key_24dp", on_click=self._show_key_list, accent=True),
            Header(text=localization.get_string("COMPLEXITY_LEVELS_HEADER") if localization.get_string("COMPLEXITY_LEVELS_HEADER") != "MISSING_STRING_COMPLEXITY_LEVELS_HEADER" else "Encryption Complexity Levels"), 
            Text(text=localization.get_string("COMPLEXITY_LOW")), 
            Text(text=localization.get_string("COMPLEXITY_MEDIUM")),
            Text(text=localization.get_string("COMPLEXITY_HIGH")), 
            Text(text=localization.get_string("COMPLEXITY_VERY_HIGH")),
            Text(text=localization.get_string("COMPLEXITY_CHAOS"))
        ]
        
        return settings
    
    def _show_key_list(self, view=None): # view can be None if called programmatically
        try:
            from ui.alert import AlertDialogBuilder #, DialogButton (not directly used here)
        except ImportError:
            log("UI components (AlertDialogBuilder) not available for _show_key_list.")
            return

        keys = self.key_manager.get_all_keys()
        builder = AlertDialogBuilder().setTitle(localization.get_string("KEY_LIST"))

        if not keys:
            builder.setMessage(localization.get_string("NO_KEYS_FOUND"))\
                   .setPositiveButton(localization.get_string("ADD_NEW_KEY"), lambda: self._show_add_key_dialog())\
                   .setNegativeButton(localization.get_string("CANCEL"), None)
        else:
            key_display_items = []
            key_ids_for_selection = [] # Store actual IDs corresponding to display items
            for key in keys:
                user_desc = f"User ID: {key['user_id']}" if key['user_id'] != 0 else "Default"
                key_display_items.append(f"{key['name']} ({user_desc})")
                key_ids_for_selection.append(key['id'])
            
            # Add "Add new key" option to the list itself for unified handling
            add_new_key_text = localization.get_string("ADD_NEW_KEY")
            
            builder.setItems(key_display_items + [add_new_key_text], 
                             lambda idx, items_array: self._on_key_selected_from_list(idx, items_array, key_ids_for_selection, add_new_key_text))
        builder.show()
    
    def _on_key_selected_from_list(self, item_index, items_array, key_ids, add_new_text):
        # items_array includes the "Add new key" text. key_ids does not.
        if items_array[item_index] == add_new_text: # Check by text content
            self._show_add_key_dialog()
        elif item_index < len(key_ids): # Ensure index is valid for key_ids
            selected_key_id = key_ids[item_index]
            self._show_key_details(selected_key_id)
    
    def _show_key_details(self, key_id):
        try:
            from ui.alert import AlertDialogBuilder #, DialogButton
        except ImportError: log("UI components not available for _show_key_details."); return

        key_data = self.key_manager.get_key(key_id)
        if not key_data: return
        
        methods_list_display = [ # Keep this list consistent with create_settings
            "Caesar cipher (Low)", "XOR encryption (Low)", "AES-like stream (Medium)",
            "ROT13 (Low)", "Atbash cipher (Low)", "Vigenère cipher (Medium)", 
            "Base64 encoding (Low)", "Morse code (Low)", "RC4-like stream (Medium)",
            "Blowfish-like stream (High)", "3DES-like (High)", 
            "RSA simplified (Very High)", "ElGamal simplified (Very High)", 
            "Chaos Cipher (Extreme)" 
        ]
        method_display_name = methods_list_display[key_data['method']] if 0 <= key_data['method'] < len(methods_list_display) else "Unknown"

        details_msg = f"{localization.get_string('KEY_NAME')}: {key_data['name']}\n" \
                      f"{localization.get_string('KEY_VALUE')}: {key_data['key']}\n" \
                      f"{localization.get_string('USER_ID')}: {key_data['user_id'] if key_data['user_id'] != 0 else localization.get_string('DEFAULT_KEY_NAME')}\n" \
                      f"{localization.get_string('METHOD')}: {method_display_name}"
        
        AlertDialogBuilder()\
            .setTitle(localization.get_string("KEY_DETAILS"))\
            .setMessage(details_msg)\
            .setPositiveButton(localization.get_string("EDIT_KEY"), lambda: self._show_edit_key_dialog(key_id))\
            .setNegativeButton(localization.get_string("DELETE_KEY"), lambda: self._confirm_delete_key(key_id))\
            .setNeutralButton(localization.get_string("CANCEL"), None)\
            .show()

    def _get_methods_for_selector(self):
         return [ # Keep this list consistent
            "Caesar cipher (Low)", "XOR encryption (Low)", "AES-like stream (Medium)",
            "ROT13 (Low)", "Atbash cipher (Low)", "Vigenère cipher (Medium)", 
            "Base64 encoding (Low)", "Morse code (Low)", "RC4-like stream (Medium)",
            "Blowfish-like stream (High)", "3DES-like (High)", 
            "RSA simplified (Very High)", "ElGamal simplified (Very High)", 
            "Chaos Cipher (Extreme)" 
        ]

    def _show_add_key_dialog(self):
        try:
            from ui.alert import AlertDialogBuilder, InputField, SelectorField
        except ImportError: log("UI components not available for _show_add_key_dialog."); return
        
        name_field = InputField(localization.get_string("KEY_NAME"), "")
        key_field = InputField(localization.get_string("KEY_VALUE"), "")
        user_id_field = InputField(localization.get_string("USER_ID"), "0", inputType="number")
        method_field = SelectorField(localization.get_string("METHOD"), self._get_methods_for_selector(), 0)
        
        AlertDialogBuilder()\
            .setTitle(localization.get_string("ADD_NEW_KEY"))\
            .addField(name_field).addField(key_field).addField(user_id_field).addField(method_field)\
            .setPositiveButton(localization.get_string("SAVE"), lambda: self._save_new_key(
                name_field.getText(), key_field.getText(),
                method_field.getSelectedIndex(),
                int(user_id_field.getText() or "0") # Ensure valid int, default to 0
            ))\
            .setNegativeButton(localization.get_string("CANCEL"), None).show()
    
    def _save_new_key(self, name, key_value, method_idx, user_id):
        if not name or not key_value: 
            try: # Try to show bulletin even if full UI not there
                from ui.bulletin import BulletinHelper
                BulletinHelper.show_error("Name and Key Value cannot be empty.")
            except: log("Name and Key Value cannot be empty.")
            return
        
        self.key_manager.add_key(name, key_value, method_idx, user_id)
        # If this was the "default" key, update main settings
        if user_id == 0 and name == localization.get_string("DEFAULT_KEY_NAME"):
            self.set_setting("key", key_value)
            self.set_setting("method", method_idx)
        self._show_key_list() # Refresh list

    def _show_edit_key_dialog(self, key_id):
        try:
            from ui.alert import AlertDialogBuilder, InputField, SelectorField
        except ImportError: log("UI components not available for _show_edit_key_dialog."); return
            
        key_data = self.key_manager.get_key(key_id)
        if not key_data: return
        
        name_field = InputField(localization.get_string("KEY_NAME"), key_data["name"])
        key_field = InputField(localization.get_string("KEY_VALUE"), key_data["key"])
        user_id_field = InputField(localization.get_string("USER_ID"), str(key_data["user_id"]), inputType="number")
        method_field = SelectorField(localization.get_string("METHOD"), self._get_methods_for_selector(), key_data["method"])
        
        AlertDialogBuilder()\
            .setTitle(localization.get_string("EDIT_KEY"))\
            .addField(name_field).addField(key_field).addField(user_id_field).addField(method_field)\
            .setPositiveButton(localization.get_string("SAVE"), lambda: self._update_key(
                key_id, name_field.getText(), key_field.getText(),
                method_field.getSelectedIndex(), int(user_id_field.getText() or "0")
            ))\
            .setNegativeButton(localization.get_string("CANCEL"), None).show()
    
    def _update_key(self, key_id, name, key_value, method_idx, user_id):
        if not name or not key_value:
            try:
                from ui.bulletin import BulletinHelper
                BulletinHelper.show_error("Name and Key Value cannot be empty.")
            except: log("Name and Key Value cannot be empty.")
            return
        
        self.key_manager.update_key(key_id, name, key_value, method_idx, user_id)
        # If this key becomes the main default key, update plugin's global settings
        if user_id == 0 and name == localization.get_string("DEFAULT_KEY_NAME"):
            self.set_setting("key", key_value)
            self.set_setting("method", method_idx)
        self._show_key_list() # Refresh list

    def _confirm_delete_key(self, key_id):
        try:
            from ui.alert import AlertDialogBuilder
        except ImportError: log("UI components not available for _confirm_delete_key."); return

        key_to_delete = self.key_manager.get_key(key_id)
        if not key_to_delete: return

        # Prevent deleting the absolute last key if it's the default one
        if key_id == "default" and len(self.key_manager.get_all_keys()) == 1:
            msg = "This is the last key and cannot be deleted. Add another key first or edit this one."
            if localization.language == "ru":
                msg = "Это последний ключ, и его нельзя удалить. Сначала добавьте другой ключ или отредактируйте этот."
            AlertDialogBuilder().setTitle(localization.get_string("DELETE_KEY")).setMessage(msg)\
                               .setPositiveButton("OK", None).show()
            return

        confirm_msg = "Вы действительно хотите удалить этот ключ?" if localization.language == "ru" else "Are you sure you want to delete this key?"
        
        def on_confirm_delete():
            deleted = self.key_manager.delete_key(key_id)
            if deleted:
                self._show_key_list() # Refresh
            else: # Deletion might fail if it's the last default key (though checked above, double safety)
                try:
                    from ui.bulletin import BulletinHelper
                    BulletinHelper.show_error("Failed to delete key. It might be the last default key.")
                except: log("Failed to delete key.")


        AlertDialogBuilder()\
            .setTitle(localization.get_string("DELETE_KEY"))\
            .setMessage(confirm_msg)\
            .setPositiveButton("OK", on_confirm_delete)\
            .setNegativeButton(localization.get_string("CANCEL"), None).show()
    
    def _extract_user_id_from_message_params(self, params):
        try:
            # For direct user chats, params.peer might be a TLRPC.PeerUser
            if hasattr(params, 'peer') and isinstance(params.peer, TLRPC.PeerUser):
                return params.peer.user_id
            # For group chats, params.peer might be TLRPC.PeerChat or TLRPC.PeerChannel
            # For messages sent by user in groups, from_id might be relevant if available directly on params
            # However, message sending hooks usually provide `params.peer` as the destination.
            # If the intent is to get the *recipient's* ID for key selection:
            if hasattr(params, 'peer'):
                 # For outgoing messages, params.peer is the chat ID.
                 # If peer ID is positive, it's a user. If negative, it's a chat/channel.
                 if params.peer > 0: return params.peer # User ID
                 # For chats/channels, or if we can't determine a specific user, use 0 (default key)
            return 0 
        except: return 0 
    
    def _parse_and_process_key_message(self, text_content, from_user_id):
        # New robust format: PG_KEY:name:key_value:method:timestamp
        key_pattern_new = r"`PG_KEY:([^:]+):([^:]+):(\d+):(\d+)`" # With backticks from share format
        match_new = re.search(key_pattern_new, text_content)
        if not match_new: # Try without backticks for direct .pgaccept or older shares
            key_pattern_new_no_ticks = r"PG_KEY:([^:]+):([^:]+):(\d+):(\d+)"
            match_new = re.search(key_pattern_new_no_ticks, text_content)

        if match_new:
            try:
                name, key_value, method_str, timestamp_str = match_new.groups()
                method = int(method_str)
                _ = int(timestamp_str) # Validate timestamp

                key_id = self.key_manager.import_key_data(f"PG_KEY:{name}:{key_value}:{method}:{timestamp_str}", from_user_id)
                if key_id:
                    try:
                        from ui.bulletin import BulletinHelper
                        BulletinHelper.show_info(localization.get_string("KEY_RECEIVED") + f" ({name})")
                    except: log(localization.get_string("KEY_RECEIVED") + f" ({name})")
                    return True
            except Exception as e:
                log(f"Error processing new key format: {e}")
                return False
        return False # No key found or processed

    def _send_share_key_message(self, params):
        # Determine which key to share. If enable_key_sync is on, share user-specific key.
        # Otherwise, share the current global/default key.
        target_user_id_for_key = 0 # Default to sharing the global default key
        
        if self.get_setting("enable_key_sync", False):
            # If key sync is on, we want to share the key *intended for this recipient*
            # This is tricky because .pgshare is sent *by us*.
            # It should probably share the key *we* use for *this chat/user*.
            recipient_user_id = self._extract_user_id_from_message_params(params)
            if recipient_user_id != 0 : # If it's a 1-to-1 chat
                target_user_id_for_key = recipient_user_id
        
        key_data_to_share = self.key_manager.get_key_for_user(target_user_id_for_key)
        
        exported_key_str_data_only = f"{key_data_to_share['name']}:{key_data_to_share['key']}:{key_data_to_share['method']}:{key_data_to_share['timestamp']}"
        
        # Use the SHARE_KEY_FORMAT string
        formatted_message = localization.get_string("SHARE_KEY_FORMAT").format(
            key_data_to_share['name'], 
            key_data_to_share['key'], 
            key_data_to_share['method'], 
            key_data_to_share['timestamp']
        )
        
        params.message = formatted_message
        try:
            from ui.bulletin import BulletinHelper
            BulletinHelper.show_info(localization.get_string("KEY_SHARED_SUCCESS"))
        except: log(localization.get_string("KEY_SHARED_SUCCESS"))
        return HookResult(strategy=HookStrategy.MODIFY, params=params)
    
    def _process_pgaccept_command(self, params, key_data_str_from_user_input):
        # key_data_str_from_user_input is the part after ".pgaccept "
        # This key is from *another* user, so we associate it with *their* ID if sync is on.
        # The `from_id` of the message containing ".pgaccept" is *our own ID*.
        # We need the ID of the user *who sent us the key we are now accepting*.
        # This usually means the key was copied from a message *they* sent.
        # For .pgaccept, the association should be with the chat partner if it's a 1-to-1 chat.

        user_id_to_associate_key_with = 0 # Default (global key)
        if self.get_setting("enable_key_sync", False):
            # Associate with the user in the current chat, if it's a 1-to-1 chat
            # params.peer is the chat where ".pgaccept" is typed.
            chat_peer_id = self._extract_user_id_from_message_params(params)
            if chat_peer_id != 0: # If it's a specific user chat
                user_id_to_associate_key_with = chat_peer_id
        
        # Try parsing with the full "PG_KEY:..." format first
        if self._parse_and_process_key_message(key_data_str_from_user_input, user_id_to_associate_key_with):
            params.message = localization.get_string("KEY_ACCEPTED")
        else:
            # Fallback for older/simpler formats if needed, or just error
            params.message = "❌ Invalid key format for .pgaccept. Expected `PG_KEY:...`"
            if localization.language == "ru":
                 params.message = "❌ Неверный формат ключа для .pgaccept. Ожидается `PG_KEY:...`"

        return HookResult(strategy=HookStrategy.MODIFY, params=params)

    def on_send_message_hook(self, account, params) -> HookResult:
        if not hasattr(params, "message") or not isinstance(params.message, str): 
            return HookResult()
        
        msg_content = params.message.strip()
        
        if msg_content == ".fpg":
            curr_enabled = self.get_setting("enabled", False)
            self.set_setting("enabled", not curr_enabled)
            params.message = localization.get_string("ENCRYPTION_ENABLED") if not curr_enabled else localization.get_string("ENCRYPTION_DISABLED")
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
        
        if msg_content == ".pgshare":
            return self._send_share_key_message(params)
        
        if msg_content.startswith(".pgaccept "): # Note space
            key_text_to_accept = msg_content[len(".pgaccept "):].strip()
            if key_text_to_accept:
                return self._process_pgaccept_command(params, key_text_to_accept)
            else:
                params.message = "❌ " + (localization.get_string("SHARE_KEY_ACCEPT_USAGE") if localization.get_string("SHARE_KEY_ACCEPT_USAGE") != "MISSING_STRING_SHARE_KEY_ACCEPT_USAGE" else "Usage: .pgaccept [key_string]")
                return HookResult(strategy=HookStrategy.MODIFY, params=params)
        
        # Decryption command
        if msg_content.startswith(".decrypt "): # Note space
            enc_text_to_dec = msg_content[len(".decrypt "):].strip()
            
            if not enc_text_to_dec:
                params.message = "Укажите текст после .decrypt" if localization.language == "ru" else "Specify text after .decrypt"
                return HookResult(strategy=HookStrategy.MODIFY, params=params)

            # Stripping logic (lock emoji and method name)
            if self.get_setting("show_lock", True) and enc_text_to_dec.startswith("🔒 "):
                enc_text_to_dec = enc_text_to_dec[2:]
            
            method_names_strip = ["Caesar","XOR","AES-like","ROT13","Atbash","Vigenère","Base64","Morse","RC4-like","Blowfish-like","3DES-like","RSA","ElGamal", "Chaos"]
            if self.get_setting("show_method", False):
                for m_name in method_names_strip:
                    suffix = f" [{m_name}]"
                    if enc_text_to_dec.endswith(suffix): 
                        enc_text_to_dec = enc_text_to_dec[:-len(suffix)].strip()
                        break
            
            # Key selection for decryption
            # The key used for decryption should be the one associated with the *sender* of the encrypted message.
            # This is complex with just ".decrypt text". It assumes the text was encrypted with *our key for them*,
            # or with a global key. For simplicity, .decrypt usually uses the current active key for the chat.
            current_chat_user_id = self._extract_user_id_from_message_params(params)
            key_data = self.key_manager.get_key_for_user(current_chat_user_id if self.get_setting("enable_key_sync", False) else 0)
            
            key_val = key_data["key"]
            method_idx = key_data["method"]
            
            dec_funcs = [
                decrypt_caesar, decrypt_xor, decrypt_aes, decrypt_rot13, decrypt_atbash, decrypt_vigenere,
                decrypt_base64, decrypt_morse, decrypt_rc4, decrypt_blowfish, decrypt_des3,
                decrypt_rsa, decrypt_elgamal, decrypt_chaos 
            ]
            dec_func = dec_funcs[method_idx] if 0 <= method_idx < len(dec_funcs) else None
            dec_text = None

            if dec_func:
                if method_idx == 0: # Caesar
                    shift_val = sum(ord(c) for c in key_val) if key_val else 13 # Default to 13 if key empty
                    if shift_val % 26 == 0 and key_val: shift_val = 1 # Avoid 0 shift if key existed
                    dec_text = dec_func(enc_text_to_dec, shift_val)
                else: 
                    dec_text = dec_func(enc_text_to_dec, key_val)
            
            params.message = f"🔓 {dec_text}" if dec_text is not None else localization.get_string("DECRYPTION_FAILED")
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
            
        # Encryption logic if enabled
        if self.get_setting("enabled", False):
            current_chat_user_id = self._extract_user_id_from_message_params(params)
            
            if self.get_setting("enable_key_sync", False):
                key_data = self.key_manager.get_key_for_user(current_chat_user_id)
            else: # Use global settings
                key_data = self.key_manager.get_key_for_user(0) # Gets default key
            
            key_val = key_data["key"]
            method_idx = key_data["method"]
            
            enc_funcs = [
                encrypt_caesar, encrypt_xor, encrypt_aes, encrypt_rot13, encrypt_atbash, encrypt_vigenere,
                encrypt_base64, encrypt_morse, encrypt_rc4, encrypt_blowfish, encrypt_des3,
                encrypt_rsa, encrypt_elgamal, encrypt_chaos
            ]
            enc_func = enc_funcs[method_idx] if 0 <= method_idx < len(enc_funcs) else None
            enc_msg_body = msg_content 

            if enc_func:
                if method_idx == 0: # Caesar
                    shift_val = sum(ord(c) for c in key_val) if key_val else 13
                    if shift_val % 26 == 0 and key_val: shift_val = 1
                    enc_msg_body = enc_func(msg_content, shift_val)
                else: 
                    enc_msg_body = enc_func(msg_content, key_val)
            
            if enc_msg_body is None or enc_msg_body == msg_content:
                 final_msg = msg_content 
            else:
                method_names_display = ["Caesar","XOR","AES-like","ROT13","Atbash","Vigenère","Base64","Morse","RC4-like","Blowfish-like","3DES-like","RSA","ElGamal", "Chaos"]
                final_msg = ""
                if self.get_setting("show_lock", True): final_msg += "🔒 "
                final_msg += enc_msg_body
                if self.get_setting("show_method", False) and 0 <= method_idx < len(method_names_display): 
                    final_msg += f" [{method_names_display[method_idx]}]"
                params.message = final_msg
            return HookResult(strategy=HookStrategy.MODIFY, params=params)
            
        return HookResult()
    
    def on_update_hook(self, update_name, account, update_obj) -> HookResult: # Renamed 'update' to 'update_obj'
        if update_name == "updateNewMessage" and hasattr(update_obj, "message"):
            try:
                message_obj = update_obj.message # Renamed 'message' to 'message_obj'
                if hasattr(message_obj, "message") and message_obj.message and isinstance(message_obj.message, str):
                    text_content = message_obj.message # Renamed 'text' to 'text_content'
                    
                    if "PG_KEY:" in text_content: # Check for key sharing format
                        from_id_val = 0
                        if hasattr(message_obj, "from_id") and message_obj.from_id:
                             # from_id can be TLRPC.PeerUser, TLRPC.PeerChat, etc.
                             if hasattr(message_obj.from_id, 'user_id'):
                                from_id_val = message_obj.from_id.user_id
                             elif hasattr(message_obj.from_id, 'channel_id'): # Message from channel
                                 pass # Key sharing from channels usually not directly to a user key
                             elif hasattr(message_obj.from_id, 'chat_id'): # Message from basic group
                                 pass # Key sharing from groups needs context
                        
                        # Only process if from a specific user (not bot, not channel posting as self)
                        if from_id_val > 0:
                            if self._parse_and_process_key_message(text_content, from_id_val):
                                # Optionally, consume the update if it was *only* a key message
                                # For now, let it pass through.
                                pass 
            except Exception as e:
                log(f"Error in on_update_hook for key processing: {e}")
                
        return HookResult() # Default: let update proceed

# Create an instance of the plugin (standard practice for such plugin systems)
plugin = PrivacyGuardPlugin()