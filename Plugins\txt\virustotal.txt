import os
import time
import hashlib
import re
from collections import deque
from typing import Any, Dict, Optional, Tu<PERSON>, List

import requests

# --- Утилиты плагина ---
from android_utils import log, run_on_ui_thread
from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from client_utils import run_on_queue, get_file_loader, send_message, invoke
from markdown_utils import parse_markdown
from ui.bulletin import BulletinHelper

# --- Объекты Telegram API ---
from org.telegram.messenger import MessageObject, FileLoader
from org.telegram.tgnet import TLRPC
from java.util import ArrayList

# --- Метаданные плагина ---
__id__ = "virus_total_scanner_pro"
__name__ = "VirusTotal Scanner Pro"
__description__ = "Scan files, URLs, and hashes with VirusTotal. Features queue, cache, and rate limiting."
__author__ = "@extera_plugin (Enhanced by AI)"
__version__ = "2.1.0"
__icon__ = "yakeygpt_by_fStikBot/2"
__min_version__ = "11.9.0"

# --- Класс для работы с API VirusTotal (без изменений) ---
class VirusTotalScanner:
    """Handles all communication with the VirusTotal API v3."""
    BASE_URL = "https://www.virustotal.com/api/v3"
    MAX_FILE_SIZE = 32 * 1024 * 1024
    RETRY_DELAY = 15
    MAX_ATTEMPTS = 12
    QUOTA_REQUESTS = 4
    QUOTA_PERIOD = 60

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.session = requests.Session()
        self.session.headers.update({
            "x-apikey": self.api_key,
            "accept": "application/json",
            "User-Agent": f"ExteraPlugin/VirusTotalScannerPro/{__version__}"
        })
        self._request_timestamps = deque()

    def _wait_for_quota(self):
        now = time.time()
        while self._request_timestamps and self._request_timestamps[0] < now - self.QUOTA_PERIOD:
            self._request_timestamps.popleft()
        if len(self._request_timestamps) >= self.QUOTA_REQUESTS:
            wait_time = self._request_timestamps[0] + self.QUOTA_PERIOD - now
            if wait_time > 0:
                log(f"VirusTotal quota limit reached. Waiting for {wait_time:.2f} seconds.")
                time.sleep(wait_time)
        self._request_timestamps.append(time.time())

    def _request(self, method: str, url: str, **kwargs) -> requests.Response:
        self._wait_for_quota()
        try:
            response = self.session.request(method, url, **kwargs)
            response.raise_for_status()
            return response
        except requests.exceptions.RequestException as e:
            log(f"VirusTotal API request failed: {e}")
            raise

    def get_upload_url(self) -> Optional[str]:
        try:
            response = self._request("GET", f"{self.BASE_URL}/files/upload_url")
            return response.json().get("data")
        except Exception as e:
            log(f"Failed to get upload URL: {e}")
            return None

    def upload_file(self, file_path: str) -> Optional[Dict[str, Any]]:
        file_size = os.path.getsize(file_path)
        url = f"{self.BASE_URL}/files"
        if file_size > self.MAX_FILE_SIZE:
            upload_url = self.get_upload_url()
            if not upload_url: raise Exception("Could not get URL for large file upload.")
            url = upload_url
        with open(file_path, "rb") as f:
            files = {"file": (os.path.basename(file_path), f)}
            try:
                response = self._request("POST", url, files=files)
                return response.json()
            except Exception as e:
                log(f"File upload failed: {e}")
                raise

    def get_analysis_report(self, analysis_id: str, report_type: str = "files") -> Optional[Dict[str, Any]]:
        endpoint = "analyses" if report_type == "files" else "url/analyses"
        url = f"{self.BASE_URL}/{endpoint}/{analysis_id}"
        for attempt in range(self.MAX_ATTEMPTS):
            try:
                response = self._request("GET", url)
                data = response.json()
                status = data.get("data", {}).get("attributes", {}).get("status")
                if status == "completed":
                    if report_type == "files": return data
                    else:
                        item_id = data.get("meta", {}).get("url_info", {}).get("id")
                        return self.get_report_by_hash(item_id, "urls")
                elif status in ("queued", "in-progress"):
                    log(f"Analysis '{status}', attempt {attempt + 1}/{self.MAX_ATTEMPTS}. Retrying...")
                    time.sleep(self.RETRY_DELAY)
                    continue
                else: return None
            except Exception as e:
                log(f"Error getting analysis report: {e}")
                return None
        return None

    def get_report_by_hash(self, item_hash: str, report_type: str = "files") -> Optional[Dict[str, Any]]:
        url = f"{self.BASE_URL}/{report_type}/{item_hash}"
        try:
            response = self._request("GET", url)
            return response.json()
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 404:
                return {"error": {"code": "NotFound", "message": f"Hash not found on VirusTotal."}}
            log(f"Error getting report by hash: {e}")
        except Exception as e:
            log(f"An unexpected error occurred: {e}")
        return None
        
    def scan_url(self, url_to_scan: str) -> Optional[Dict[str, Any]]:
        try:
            response = self._request("POST", f"{self.BASE_URL}/urls", data={"url": url_to_scan})
            return response.json()
        except Exception as e:
            log(f"URL scan submission failed: {e}")
            raise

    @staticmethod
    def _format_report(report_data: Dict[str, Any], title: str) -> Tuple[str, Optional[str]]:
        if not report_data or "data" not in report_data:
            error_msg = report_data.get("error", {}).get("message", "No results from VirusTotal.")
            return f"❌ *Error*: {error_msg}", None
        attributes = report_data["data"]["attributes"]
        stats = attributes.get("last_analysis_stats", {})
        report_id = report_data["data"]["id"]
        item_type = report_data["data"]["type"]
        if item_type == 'file':
            gui_link = f"https://www.virustotal.com/gui/file/{report_id}"
            sha256 = attributes.get('sha256')
            title_line = f"🛡️ *VirusTotal Scan: {title}*\n`{sha256}`" if sha256 else f"🛡️ *VirusTotal Scan: {title}*"
        elif item_type == 'url':
            gui_link = f"https://www.virustotal.com/gui/url/{report_id}"
            title_line = f"🛡️ *VirusTotal URL Scan*\n`{attributes.get('url', title)}`"
        else:
            gui_link = None
            title_line = f"🛡️ *VirusTotal Scan: {title}*"
        malicious, suspicious = stats.get("malicious", 0), stats.get("suspicious", 0)
        total_threats = malicious + suspicious
        text = f"{title_line}\n\n"
        text += f"⚠️ *Threats detected:* {total_threats} ({malicious} malicious, {suspicious} suspicious)\n"
        text += f"🟢 *Harmless:* {stats.get('harmless', 0)}\n"
        text += f"⚪ *Undetected:* {stats.get('undetected', 0)}\n"
        if total_threats > 0:
            text += "\n*Detection Engines:*\n"
            results = attributes.get("last_analysis_results", {})
            detections = []
            for engine, result in results.items():
                if result.get("category") in ("malicious", "suspicious"):
                    engine_name = re.sub(r'([*_`\[\]\(\)])', r'\\\1', engine.replace("_", " ").title())
                    result_text = re.sub(r'([*_`\[\]\(\)])', r'\\\1', result.get("result", "Unknown"))
                    detections.append(f"• *{engine_name}*: {result_text}")
            text += "\n".join(detections[:10])
            if len(detections) > 10: text += f"\n... and {len(detections) - 10} more."
        text += "\n\n⚠️ _Note: Scan results may contain false positives. Always use caution._"
        return text, gui_link

# --- Основной класс плагина ---
class VirusTotalPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.vt_scanner: Optional[VirusTotalScanner] = None
        self.scan_queue = deque()
        self.is_processing = False
        self.cache: Dict[str, Dict] = {}
        self.CACHE_DURATION = 3600

    def on_plugin_load(self):
        self.load_config()
        self.add_on_send_message_hook()
        log("VirusTotal Scanner Pro loaded!")

    def load_config(self):
        api_key = self.get_setting("vt_api_key", "").strip()
        self.vt_scanner = VirusTotalScanner(api_key) if api_key else None
        self.allowed_extensions = [
            ext.strip().lower() for ext in self.get_setting("allowed_exts", ".exe, .apk, .dll, .msi").split(",") if ext.strip()
        ]

    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        if not self.vt_scanner: return HookResult()
        text: str = params.message.strip()
        command, *args = text.split(" ", 1)
        arg = args[0] if args else ""
        try:
            if command == ".vtscan" and params.replyToMsg:
                return self.handle_file_scan_command(params)
            elif command == ".vthash" and arg:
                return self.handle_hash_scan_command(arg, params)
            elif command == ".vturl" and arg:
                return self.handle_url_scan_command(arg, params)
        except Exception as e:
            log(f"Command processing error: {e}")
        return HookResult()

    def handle_file_scan_command(self, params: Any) -> HookResult:
        document = MessageObject.getDocument(params.replyToMsg)
        if not document:
            self._send_temp_message(params.peer, "❌ No file found in replied message!")
            return HookResult(strategy=HookStrategy.CANCEL)
        file_name = document.file_name_fixed
        if not any(file_name.lower().endswith(ext) for ext in self.allowed_extensions):
            self._send_temp_message(params.peer, f"❌ Unsupported file type. Allowed: {', '.join(self.allowed_extensions)}")
            return HookResult(strategy=HookStrategy.CANCEL)
        
        peer_obj = self.get_peer_by_id(params.peer)
        msg_id = self._send_temp_message(peer_obj, f"Queuing {file_name} for scan...")
        if not msg_id: return HookResult(strategy=HookStrategy.CANCEL)
        
        task = { "type": "file", "document": document, "peer": peer_obj, "msg_id": msg_id, "file_name": file_name }
        self.scan_queue.append(task)
        self._process_queue()
        return HookResult(strategy=HookStrategy.CANCEL)
        
    def handle_hash_scan_command(self, file_hash: str, params: Any) -> HookResult:
        peer_obj = self.get_peer_by_id(params.peer)
        msg_id = self._send_temp_message(peer_obj, f"🔍 Checking hash `{file_hash}`...")
        if not msg_id: return HookResult(strategy=HookStrategy.CANCEL)
        task = { "type": "hash", "hash": file_hash, "peer": peer_obj, "msg_id": msg_id }
        self.scan_queue.append(task)
        self._process_queue()
        return HookResult(strategy=HookStrategy.CANCEL)

    def handle_url_scan_command(self, url: str, params: Any) -> HookResult:
        if not (url.startswith("http://") or url.startswith("https://")): url = "http://" + url
        peer_obj = self.get_peer_by_id(params.peer)
        msg_id = self._send_temp_message(peer_obj, f"🔍 Scanning URL `{url}`...")
        if not msg_id: return HookResult(strategy=HookStrategy.CANCEL)
        task = { "type": "url", "url": url, "peer": peer_obj, "msg_id": msg_id }
        self.scan_queue.append(task)
        self._process_queue()
        return HookResult(strategy=HookStrategy.CANCEL)

    def _process_queue(self):
        if self.is_processing or not self.scan_queue: return
        self.is_processing = True
        task = self.scan_queue.popleft()
        run_on_queue(lambda: self._execute_task(task))

    def _execute_task(self, task: Dict):
        try:
            if task["type"] == "file": self._run_file_scan(task)
            elif task["type"] == "hash": self._run_hash_scan(task)
            elif task["type"] == "url": self._run_url_scan(task)
        except Exception as e:
            error_msg = f"❌ An unexpected error occurred: {e}"
            log(error_msg)
            self._update_message(task["peer"], task["msg_id"], error_msg)
        finally:
            self.is_processing = False
            self._process_queue()

    def _run_file_scan(self, task: Dict):
        peer, msg_id, file_name, doc = task["peer"], task["msg_id"], task["file_name"], task["document"]
        file_path = get_file_loader().getPathToAttach(doc, True).getAbsolutePath()

        if not self._wait_for_file(file_path, doc, peer, msg_id): return
        
        try:
            file_hash = self._calculate_sha256(file_path)
            if file_hash in self.cache and time.time() - self.cache[file_hash]["timestamp"] < self.CACHE_DURATION:
                cached = self.cache[file_hash]
                self._update_message(peer, msg_id, f"_(Cached)_\n{cached['text']}", cached['url'])
                return
        except Exception as e:
            log(f"Cache check failed: {e}"); file_hash = None

        self._update_message(peer, msg_id, f"🔄 Uploading {file_name}...")
        analysis_obj = self.vt_scanner.upload_file(file_path)
        analysis_id = analysis_obj.get("data", {}).get("id")
        if not analysis_id: raise Exception("Failed to get analysis ID.")

        self._update_message(peer, msg_id, f"⏳ Waiting for analysis of {file_name}...")
        report = self.vt_scanner.get_analysis_report(analysis_id, "files")
        
        text, url = self.vt_scanner._format_report(report, file_name)
        if file_hash and url: self.cache[file_hash] = {"timestamp": time.time(), "text": text, "url": url}
        self._update_message(peer, msg_id, text, url)

    def _run_hash_scan(self, task: Dict):
        report = self.vt_scanner.get_report_by_hash(task["hash"])
        text, url = self.vt_scanner._format_report(report, f"Hash: {task['hash'][:12]}...")
        self._update_message(task["peer"], task["msg_id"], text, url)

    def _run_url_scan(self, task: Dict):
        url_to_scan, peer, msg_id = task["url"], task["peer"], task["msg_id"]
        url_id = hashlib.sha256(url_to_scan.encode()).hexdigest()
        report = self.vt_scanner.get_report_by_hash(url_id, report_type="urls")
        
        if report.get("error", {}).get("code") == "NotFound" or self._is_report_old(report):
             self._update_message(peer, msg_id, "🔄 Submitting URL for new analysis...")
             analysis_obj = self.vt_scanner.scan_url(url_to_scan)
             analysis_id = analysis_obj.get("data", {}).get("id")
             if not analysis_id: raise Exception("Failed to get URL analysis ID.")
             self._update_message(peer, msg_id, "⏳ Waiting for URL analysis...")
             report = self.vt_scanner.get_analysis_report(analysis_id, report_type="urls")
        
        text, url = self.vt_scanner._format_report(report, url_to_scan)
        self._update_message(peer, msg_id, text, url)

    def _wait_for_file(self, file_path: str, doc: Any, peer: Any, msg_id: int) -> bool:
        if os.path.exists(file_path): return True
        self._update_message(peer, msg_id, "⬇️ Downloading file...")
        get_file_loader().loadFile(doc, "virus_scan", FileLoader.PRIORITY_HIGH, 1)
        for _ in range(30):
            if os.path.exists(file_path): return True
            time.sleep(1)
        self._update_message(peer, msg_id, "❌ File download timed out.")
        return False

    def _calculate_sha256(self, file_path: str) -> str:
        sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for block in iter(lambda: f.read(4096), b""): sha256.update(block)
        return sha256.hexdigest()

    def _is_report_old(self, report: Dict, days: int = 7) -> bool:
        if not report or "data" not in report: return True
        last_date = report["data"]["attributes"].get("last_analysis_date")
        if not last_date: return True
        return time.time() - last_date > (days * 86400)

    def _send_temp_message(self, peer: Any, text: str) -> Optional[int]:
        """Sends a message using invoke and returns its ID."""
        req = TLRPC.TL_messages_sendMessage()
        req.peer = peer
        req.random_id = int.from_bytes(os.urandom(8), 'big')
        
        parsed = parse_markdown(text)
        req.message = parsed.text
        if parsed.entities:
            req.entities = ArrayList([e.to_tlrpc_object() for e in parsed.entities])
            req.flags |= 8 # has entities
        
        try:
            updates = invoke(req, timeout=5)
            if isinstance(updates, TLRPC.TL_updateShortSentMessage): return updates.id
            for update in updates.updates:
                if isinstance(update, TLRPC.TL_updateMessageID): return update.id
                if hasattr(update, 'message') and hasattr(update.message, 'id'): return update.message.id
        except Exception as e:
            log(f"Could not send temp message via invoke: {e}")
        return None

    def _update_message(self, peer: Any, msg_id: Optional[int], text: str, url: Optional[str] = None):
        """Edits an existing message using invoke."""
        if not msg_id:
            log("No message ID to edit, sending new message.")
            send_message({"peer": peer.id, "message": text.replace("*","").replace("`","")})
            return
        run_on_ui_thread(lambda: self._do_update_message(peer, msg_id, text, url))
        
    def _do_update_message(self, peer: Any, msg_id: int, text: str, url: Optional[str]):
        req = TLRPC.TL_messages_editMessage()
        req.peer = peer
        req.id = msg_id

        parsed = parse_markdown(text)
        req.message = parsed.text
        if parsed.entities:
            java_entities = ArrayList()
            for entity in parsed.entities: java_entities.add(entity.to_tlrpc_object())
            req.entities = java_entities
            req.flags |= 2048 # has entities

        if url:
            markup = TLRPC.TL_replyInlineMarkup()
            row = TLRPC.TL_keyboardButtonRow()
            button = TLRPC.TL_keyboardButtonUrl()
            button.text = "🔗 View Full Report"
            button.url = url
            row.buttons.add(button)
            markup.rows.add(row)
            req.reply_markup = markup
            req.flags |= 64 # has reply_markup
        try:
            invoke(req, timeout=10)
        except Exception as e:
            log(f"Failed to edit message {msg_id}: {e}")
            # Fallback to plain text send if edit fails
            send_message({"peer": peer.id, "message": text.replace("*","").replace("`","")})

    def create_settings(self):
        from ui.settings import Header, Input, Divider, Text
        return [
            Header(text="VirusTotal Scanner Pro Settings"),
            Input(key="vt_api_key", text="VirusTotal API Key", default=self.get_setting("vt_api_key", ""), icon="msg_pin_code", subtext="Get from your virustotal.com account", on_change=self.on_config_change),
            Input(key="allowed_exts", text="Allowed File Extensions", default=self.get_setting("allowed_exts", ".exe, .apk, .dll, .msi"), icon="msg_file", subtext="Comma-separated list (e.g. .exe,.zip)", on_change=self.on_config_change),
            Divider(text="Usage"),
            Text("• Reply to a file with `.vtscan`\n• Check a hash with `.vthash <hash>`\n• Scan a URL with `.vturl <link>`"),
        ]

    def on_config_change(self, new_value: str):
        run_on_queue(self.load_config)