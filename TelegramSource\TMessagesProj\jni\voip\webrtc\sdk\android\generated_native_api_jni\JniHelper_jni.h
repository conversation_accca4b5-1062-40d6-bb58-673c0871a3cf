// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/JniHelper

#ifndef org_webrtc_JniHelper_JNI
#define org_webrtc_JniHelper_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_JniHelper[];
const char kClassPath_org_webrtc_JniHelper[] = "org/webrtc/JniHelper";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_JniHelper_clazz(nullptr);
#ifndef org_webrtc_JniHelper_clazz_defined
#define org_webrtc_JniHelper_clazz_defined
inline jclass org_webrtc_JniHelper_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_JniHelper,
      &g_org_webrtc_JniHelper_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_JniHelper_getKey1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_JniHelper_getKey(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& entry) {
  jclass clazz = org_webrtc_JniHelper_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_JniHelper_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getKey",
          "(Ljava/util/Map$Entry;)Ljava/lang/Object;",
          &g_org_webrtc_JniHelper_getKey1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, entry.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_JniHelper_getStringBytes1(nullptr);
static jni_zero::ScopedJavaLocalRef<jbyteArray> Java_JniHelper_getStringBytes(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& s) {
  jclass clazz = org_webrtc_JniHelper_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_JniHelper_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getStringBytes",
          "(Ljava/lang/String;)[B",
          &g_org_webrtc_JniHelper_getStringBytes1);

  jbyteArray ret =
      static_cast<jbyteArray>(env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, s.obj()));
  return jni_zero::ScopedJavaLocalRef<jbyteArray>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_JniHelper_getStringClass0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_JniHelper_getStringClass(JNIEnv* env) {
  jclass clazz = org_webrtc_JniHelper_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_JniHelper_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getStringClass",
          "()Ljava/lang/Object;",
          &g_org_webrtc_JniHelper_getStringClass0);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_JniHelper_getValue1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_JniHelper_getValue(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& entry) {
  jclass clazz = org_webrtc_JniHelper_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_JniHelper_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "getValue",
          "(Ljava/util/Map$Entry;)Ljava/lang/Object;",
          &g_org_webrtc_JniHelper_getValue1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, entry.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_JniHelper_JNI
