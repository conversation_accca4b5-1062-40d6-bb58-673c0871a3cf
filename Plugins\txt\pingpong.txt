#
#       Plugin for @exteraGram
#     Made by @KomaruPlugins team
#
#       https://t.me/y9hack337
#                 x
#      https://t.me/i9_12900kf
#
#          Copyright 2025
#

from base_plugin import BasePlugin, MenuItemData, MenuItemType
from ui.alert import AlertDialog<PERSON>uilder
from client_utils import get_last_fragment
from ui.settings import Header, Text
import traceback
import time
import random
import math

from hook_utils import find_class
from android.content import Context
from android_utils import run_on_ui_thread
from java import dynamic_proxy
from threading import Thread

__id__ = "ping_pong"
__name__ = "Ping-Pong"
__description__ = "Ping-Pong game. Launch the game in the side menu on the left in the chat list."
__author__ = "@i9_12900kf"
__version__ = "1.0.0"
__min_version__ = "11.12.0"
__icon__ = "xelivet_by_fStikBot/67"

KEY_HIGH_SCORE = "pong_high_score"

class PongGamePlugin(BasePlugin):
    WindowManager, LayoutParams, PixelFormat, TextView, Point, ViewGroup, GradientDrawable = (None,) * 7
    OnTouchListenerInterface, MotionEvent, ApplicationLoader, View = (None,) * 4

    window_manager = None
    racket_top, racket_bottom, ball, score_view, touch_handler_bottom = (None,) * 5
    game_loop_thread = None

    is_game_running = False
    is_starting_or_stopping = False

    screen_width, screen_height = 0, 0
    ball_pos = {'x': 0.0, 'y': 0.0}
    prev_ball_pos = {'x': 0.0, 'y': 0.0}
    ball_velocity = {'x': 0.0, 'y': 0.0}

    base_speed = 16.0
    score_speed_factor = 1.025
    max_speed = base_speed * 4.0
    MAX_DEFLECTION_X_SPEED = base_speed * 2.0

    racket_width, racket_height = 0, 20
    ball_size = 60

    racket_top_pos_x, racket_bottom_pos_x = 0, 0
    score = 0

    @classmethod
    def load_android_classes(cls):
        if cls.WindowManager: return
        cls.WindowManager = find_class("android.view.WindowManager")
        cls.LayoutParams = find_class("android.view.WindowManager$LayoutParams")
        cls.PixelFormat = find_class("android.graphics.PixelFormat")
        cls.TextView = find_class("android.widget.TextView")
        cls.Point = find_class("android.graphics.Point")
        cls.ViewGroup = find_class("android.view.ViewGroup")
        cls.GradientDrawable = find_class("android.graphics.drawable.GradientDrawable")
        cls.OnTouchListenerInterface = find_class("android.view.View$OnTouchListener")
        cls.MotionEvent = find_class("android.view.MotionEvent")
        cls.ApplicationLoader = find_class("org.telegram.messenger.ApplicationLoader")
        cls.View = find_class("android.view.View")

    def on_plugin_load(self):
        self.load_android_classes()
        self.add_menu_item(MenuItemData(menu_type=MenuItemType.DRAWER_MENU, text="Ping-Pong", icon="msg_fave", on_click=self.toggle_game))

    def on_plugin_unload(self):
        run_on_ui_thread(lambda: self.stop_game(self))

    def create_settings(self):
        high_score = self.get_setting(KEY_HIGH_SCORE, 0)
        return [Header(text="Ping-Pong"), Text(text=f"Your record: {high_score}")]

    def toggle_game(self, context: dict):
        drawer_layout = context.get("drawer_layout")
        if drawer_layout: drawer_layout.closeDrawer(False)
        if self.is_game_running: run_on_ui_thread(lambda: self.stop_game(self))
        else: run_on_ui_thread(lambda: self.start_game(self))

    @classmethod
    def start_game(cls, plugin_instance):
        if cls.is_game_running or cls.is_starting_or_stopping: return
        cls.is_starting_or_stopping = True
        cls._remove_views()
        try:
            app_context = cls.ApplicationLoader.applicationContext
            if not app_context: cls.is_starting_or_stopping = False; return
            cls.window_manager = app_context.getSystemService(Context.WINDOW_SERVICE)
            display = cls.window_manager.getDefaultDisplay()
            size = cls.Point()
            display.getSize(size)
            cls.screen_width, cls.screen_height = size.x, size.y
            cls.racket_width = cls.screen_width / 3.5 

            cls.reset_game_state()
            cls.racket_top = cls.create_game_object(is_ball=False)
            cls.racket_bottom = cls.create_game_object(is_ball=False)
            cls.ball = cls.create_game_object(is_ball=True)
            cls.score_view = cls.create_score_view()
            cls.touch_handler_bottom = cls.View(app_context)

            class RacketDragListener(dynamic_proxy(cls.OnTouchListenerInterface)):
                def __init__(self):
                    super().__init__()
                    self.initial_racket_x_bottom = 0
                    self.initial_touch_x = 0
                def onTouch(self, view, event):
                    action = event.getAction()
                    if action == cls.MotionEvent.ACTION_DOWN:
                        self.initial_racket_x_bottom = cls.racket_bottom_pos_x
                        self.initial_touch_x = event.getRawX()
                        return True
                    if action == cls.MotionEvent.ACTION_MOVE:
                        delta_x = event.getRawX() - self.initial_touch_x
                        new_racket_bottom_x = self.initial_racket_x_bottom + delta_x
                        half_racket = cls.racket_width / 2
                        cls.racket_bottom_pos_x = max(half_racket, min(cls.screen_width - half_racket, new_racket_bottom_x))
                        cls.racket_top_pos_x = cls.screen_width - cls.racket_bottom_pos_x
                        return True
                    return False

            cls.touch_handler_bottom.setOnTouchListener(RacketDragListener())
            top_y, bottom_y = 80, cls.screen_height - cls.racket_height - 180
            touch_handler_height = 200
            params_touch = cls.create_layout_params(cls.racket_bottom_pos_x, bottom_y - (touch_handler_height - cls.racket_height) / 2, cls.racket_width, touch_handler_height, is_racket=True, is_touch_handler=True)
            cls.window_manager.addView(cls.touch_handler_bottom, params_touch)

            params_top = cls.create_layout_params(cls.racket_top_pos_x, top_y, cls.racket_width, cls.racket_height)
            params_bottom = cls.create_layout_params(cls.racket_bottom_pos_x, bottom_y, cls.racket_width, cls.racket_height)
            params_ball = cls.create_layout_params(cls.ball_pos['x'], cls.ball_pos['y'], cls.ball_size, cls.ball_size)
            params_score = cls.create_layout_params(0, top_y + 80, cls.screen_width, 200)

            cls.window_manager.addView(cls.racket_top, params_top)
            cls.window_manager.addView(cls.racket_bottom, params_bottom)
            cls.window_manager.addView(cls.ball, params_ball)
            cls.window_manager.addView(cls.score_view, params_score)

            cls.is_game_running = True
            cls.game_loop_thread = Thread(target=lambda: cls.game_loop(plugin_instance), daemon=True)
            cls.game_loop_thread.start()
        except Exception:
            traceback.print_exc()
            run_on_ui_thread(lambda: cls.stop_game(plugin_instance))
        finally:
            cls.is_starting_or_stopping = False

    @classmethod
    def reset_game_state(cls):
        cls.score = 0
        cls.racket_top_pos_x = cls.screen_width / 2
        cls.racket_bottom_pos_x = cls.screen_width / 2
        cls.ball_pos = {'x': cls.screen_width / 2, 'y': cls.screen_height / 3}
        cls.prev_ball_pos = cls.ball_pos.copy()
        angle = random.uniform(math.pi * 0.85, math.pi * 1.15)
        cls.ball_velocity = {'x': math.sin(angle) * cls.base_speed, 'y': abs(math.cos(angle) * cls.base_speed)}

    @classmethod
    def create_game_object(cls, is_ball=False):
        app_context = cls.ApplicationLoader.applicationContext
        view = cls.TextView(app_context)
        drawable = cls.GradientDrawable()
        if is_ball:
            drawable.setShape(cls.GradientDrawable.OVAL)
        else:
            drawable.setShape(cls.GradientDrawable.RECTANGLE)
            drawable.setCornerRadius(20.0)
        drawable.setColor(-1)
        view.setBackground(drawable)
        return view

    @classmethod
    def create_score_view(cls):
        app_context = cls.ApplicationLoader.applicationContext
        view = cls.TextView(app_context)
        view.setText("0")
        view.setTextSize(60)
        view.setTextColor(0x40FFFFFF)
        view.setGravity(find_class("android.view.Gravity").CENTER_HORIZONTAL)
        return view

    @classmethod
    def create_layout_params(cls, x, y, width, height, is_racket=False, is_touch_handler=False):
        params = cls.LayoutParams()
        Build_VERSION = find_class("android.os.Build$VERSION")
        params.type = cls.LayoutParams.TYPE_APPLICATION_OVERLAY if Build_VERSION.SDK_INT >= 26 else cls.LayoutParams.TYPE_PHONE
        flags = cls.LayoutParams.FLAG_NOT_FOCUSABLE | cls.LayoutParams.FLAG_LAYOUT_NO_LIMITS
        if not is_racket and not is_touch_handler:
            flags |= cls.LayoutParams.FLAG_NOT_TOUCHABLE
        params.flags = flags
        params.format = cls.PixelFormat.TRANSLUCENT
        params.gravity = find_class("android.view.Gravity").TOP | find_class("android.view.Gravity").LEFT
        params.width = int(width)
        params.height = int(height)
        params.x = int(x - width/2) if width != cls.screen_width else int(x)
        params.y = int(y)
        return params

    @classmethod
    def game_loop(cls, plugin_instance):
        while cls.is_game_running:
            cls.update_game_state()
            run_on_ui_thread(cls.update_ui)
            time.sleep(1.0 / 60.0)

        run_on_ui_thread(lambda: cls.game_over_action(plugin_instance))

    @classmethod
    def update_game_state(cls):
        cls.prev_ball_pos = cls.ball_pos.copy()
        cls.ball_pos['x'] += cls.ball_velocity['x']
        cls.ball_pos['y'] += cls.ball_velocity['y']

        if cls.ball_pos['x'] <= 0:
            cls.ball_pos['x'] = 0
            cls.ball_velocity['x'] *= -1
        elif cls.ball_pos['x'] >= cls.screen_width - cls.ball_size:
            cls.ball_pos['x'] = cls.screen_width - cls.ball_size
            cls.ball_velocity['x'] *= -1

        racket_top_y = 80
        racket_bottom_y = cls.screen_height - cls.racket_height - 180
        ball_x_start, ball_x_end = cls.ball_pos['x'], cls.ball_pos['x'] + cls.ball_size
        racket_top_x_start, racket_top_x_end = cls.racket_top_pos_x - cls.racket_width / 2, cls.racket_top_pos_x + cls.racket_width / 2
        racket_bottom_x_start, racket_bottom_x_end = cls.racket_bottom_pos_x - cls.racket_width / 2, cls.racket_bottom_pos_x + cls.racket_width / 2

        if cls.ball_velocity['y'] < 0 and cls.prev_ball_pos['y'] >= racket_top_y + cls.racket_height and cls.ball_pos['y'] <= racket_top_y + cls.racket_height:
            if ball_x_end > racket_top_x_start and ball_x_start < racket_top_x_end:
                cls.ball_pos['y'] = racket_top_y + cls.racket_height
                cls.handle_bounce(cls.racket_top_pos_x)

        ball_bottom_edge, prev_ball_bottom_edge = cls.ball_pos['y'] + cls.ball_size, cls.prev_ball_pos['y'] + cls.ball_size
        if cls.ball_velocity['y'] > 0 and prev_ball_bottom_edge <= racket_bottom_y and ball_bottom_edge >= racket_bottom_y:
            if ball_x_end > racket_bottom_x_start and ball_x_start < racket_bottom_x_end:
                cls.ball_pos['y'] = racket_bottom_y - cls.ball_size
                cls.handle_bounce(cls.racket_bottom_pos_x)

        if cls.ball_pos['y'] < -cls.ball_size or cls.ball_pos['y'] > cls.screen_height:
            cls.is_game_running = False

    @classmethod
    def handle_bounce(cls, racket_x):
        cls.score += 1

        ball_center_x = cls.ball_pos['x'] + cls.ball_size / 2
        relative_impact = (ball_center_x - racket_x) / (cls.racket_width / 2)
        relative_impact = max(-0.95, min(0.95, relative_impact)) 

        new_vx = relative_impact * cls.MAX_DEFLECTION_X_SPEED
        new_vy = -cls.ball_velocity['y']

        current_total_speed = math.sqrt(cls.ball_velocity['x']**2 + cls.ball_velocity['y']**2)

        new_total_speed = min(current_total_speed * cls.score_speed_factor, cls.max_speed)

        new_length = math.sqrt(new_vx**2 + new_vy**2)
        if new_length > 0:
            cls.ball_velocity['x'] = (new_vx / new_length) * new_total_speed
            cls.ball_velocity['y'] = (new_vy / new_length) * new_total_speed
        else:
            cls.ball_velocity['y'] *= -1

    @classmethod
    def game_over_action(cls, plugin_instance):
        cls._remove_views()
        high_score = plugin_instance.get_setting(KEY_HIGH_SCORE, 0)
        new_high_score = False
        if cls.score > high_score:
            high_score = cls.score
            plugin_instance.set_setting(KEY_HIGH_SCORE, high_score)
            new_high_score = True

        fragment = get_last_fragment()
        activity = fragment.getParentActivity() if fragment else None
        if not activity: return

        builder = AlertDialogBuilder(activity)
        builder.set_title("Game over!")

        message = f"🏆 You earned: {cls.score}\n🥇 Your record: {high_score}"
        if new_high_score:
            message += "\n\nNew record!"
        builder.set_message(message)

        def start_new(b, w):
            b.dismiss()
            run_on_ui_thread(lambda: cls.start_game(plugin_instance))
        def close_game(b, w):
            b.dismiss()
            run_on_ui_thread(lambda: cls.stop_game(plugin_instance))

        builder.set_positive_button("New game", start_new)
        builder.set_negative_button("Close", close_game)
        builder.set_cancelable(False)
        builder.show()

    @classmethod
    def update_ui(cls):
        if not cls.window_manager: return
        try:
            if cls.racket_top and cls.racket_top.isAttachedToWindow():
                params = cls.racket_top.getLayoutParams()
                params.x = int(cls.racket_top_pos_x - cls.racket_width / 2)
                cls.window_manager.updateViewLayout(cls.racket_top, params)
            if cls.racket_bottom and cls.racket_bottom.isAttachedToWindow():
                params = cls.racket_bottom.getLayoutParams()
                params.x = int(cls.racket_bottom_pos_x - cls.racket_width / 2)
                cls.window_manager.updateViewLayout(cls.racket_bottom, params)
            if cls.ball and cls.ball.isAttachedToWindow():
                params = cls.ball.getLayoutParams()
                params.x = int(cls.ball_pos['x'])
                params.y = int(cls.ball_pos['y'])
                cls.window_manager.updateViewLayout(cls.ball, params)
            if cls.score_view and cls.score_view.isAttachedToWindow() and str(cls.score_view.getText()) != str(cls.score):
                cls.score_view.setText(str(cls.score))
            if cls.touch_handler_bottom and cls.touch_handler_bottom.isAttachedToWindow():
                params = cls.touch_handler_bottom.getLayoutParams()
                params.x = int(cls.racket_bottom_pos_x - cls.racket_width / 2)
                cls.window_manager.updateViewLayout(cls.touch_handler_bottom, params)
        except Exception:
            pass

    @classmethod
    def stop_game(cls, plugin_instance):
        if cls.is_starting_or_stopping and not cls.is_game_running: return
        if not cls.is_starting_or_stopping: cls.is_starting_or_stopping = True

        cls.is_game_running = False
        if cls.game_loop_thread and cls.game_loop_thread.is_alive():
            try:
                cls.game_loop_thread.join(timeout=0.1)
            except Exception:
                pass

        run_on_ui_thread(cls._remove_views)
        cls.is_starting_or_stopping = False

    @classmethod
    def _remove_views(cls):
        if not cls.window_manager: return
        views_to_remove = [cls.racket_top, cls.racket_bottom, cls.ball, cls.score_view, cls.touch_handler_bottom]
        for view in views_to_remove:
            try:
                if view and view.isAttachedToWindow():
                    cls.window_manager.removeView(view)
            except Exception:
                pass
        cls.racket_top, cls.racket_bottom, cls.ball, cls.score_view, cls.touch_handler_bottom = (None,) * 5
