from base_plugin import Base<PERSON>lug<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>gy, <PERSON><PERSON><PERSON>
from android_utils import log as _log, run_on_ui_thread
from ui.settings import Header, Switch, Divider, Text
from ui.bulletin import BulletinHelper
from hook_utils import find_class
from org.telegram.ui.Components import TextStyleSpan
from org.telegram.messenger import MessageObject
from org.telegram.tgnet import TLRPC
from android.text import SpannableStringBuilder, Spanned
import traceback

__id__ = "spoiler_remover"
__name__ = "Spoiler Remover"
__description__ = "🔓 Автоматически убирает спойлеры из сообщений и фотографий. Поддерживает текстовые спойлеры и медиа спойлеры."
__author__ = "@exteraDev"
__version__ = "1.0.1"
__icon__ = "exteraPlugins/1"
__min_version__ = "11.12.0"

class SpoilerRemoverPlugin(BasePlugin):
    def __init__(self):
        super().__init__()
        self.processed_messages = set()
        self.hooked_methods = []
        self.plugin_active = False
        
    def on_plugin_load(self):
        """Called when plugin is loaded"""
        self.log(f"🚀 Spoiler Remover plugin v{__version__} loading...")

        # Check if plugin is enabled
        if not self.get_setting("enabled", True):
            self.log("⏸️ Plugin is disabled in settings")
            return
            
        try:
            # Register hooks for processing updates
            self.add_hook("TL_updateNewMessage")
            self.add_hook("TL_updateNewChannelMessage")
            self.add_hook("TL_updateEditMessage")
            self.add_hook("TL_updateEditChannelMessage")

            # Also hook message processing for better coverage
            self.add_hook("TL_messages_getHistory")
            self.add_hook("TL_messages_getDialogs")

            # Setup method hooks for media spoilers
            self.setup_media_spoiler_hooks()

            self.plugin_active = True
            self.log("✅ Spoiler Remover plugin loaded successfully")
            BulletinHelper.show_success("Spoiler Remover: Plugin activated")
        except Exception as e:
            self.log(f"❌ Failed to load plugin: {e}")
            BulletinHelper.show_error(f"Spoiler Remover: Failed to load - {e}")

    def on_plugin_unload(self):
        """Called when plugin is unloaded"""
        self.log("Spoiler Remover plugin unloaded")
        self.plugin_active = False
        self.processed_messages.clear()

        # Clean up hooked methods
        for hook_obj in self.hooked_methods:
            try:
                # Hooks are automatically removed by the system
                pass
            except Exception as e:
                self.log(f"Error cleaning up hook: {e}")
        self.hooked_methods.clear()

    def on_update_hook(self, update_name: str, account: int, update) -> HookResult:
        """Called when the app receives an individual update"""
        result = HookResult()
        
        try:
            if not self.get_setting("enabled", True) or not self.plugin_active:
                return result
                
            # Process new messages
            if update_name in ["TL_updateNewMessage", "TL_updateNewChannelMessage"]:
                if hasattr(update, 'message') and update.message:
                    self.process_message_spoilers(update.message, account)
                    
            # Process edited messages  
            elif update_name in ["TL_updateEditMessage", "TL_updateEditChannelMessage"]:
                if hasattr(update, 'message') and update.message:
                    self.process_message_spoilers(update.message, account)
                    
        except Exception as e:
            self.log(f"❌ Error in on_update_hook: {e}")
            self.log(f"Traceback: {traceback.format_exc()}")
            
        return result

    def post_request_hook(self, request_name: str, account: int, response, error) -> HookResult:
        """Called after a response is received from the server"""
        result = HookResult()

        try:
            if not self.get_setting("enabled", True) or error:
                return result

            # Process messages from history requests
            if request_name == "TL_messages_getHistory" and response:
                if hasattr(response, 'messages') and response.messages:
                    for message in response.messages:
                        self.process_message_spoilers(message, account)

            # Process messages from dialogs
            elif request_name == "TL_messages_getDialogs" and response:
                if hasattr(response, 'messages') and response.messages:
                    for message in response.messages:
                        self.process_message_spoilers(message, account)

        except Exception as e:
            self.log(f"❌ Error in post_request_hook: {e}")

        return result

    def setup_media_spoiler_hooks(self):
        """Setup method hooks for automatic media spoiler removal"""
        try:
            if not self.get_setting("remove_media_spoilers", True) or not self.get_setting("hook_method_calls", True):
                return

            # Hook MessageObject.hasMediaSpoilers method
            message_object_class = find_class("org.telegram.messenger.MessageObject")
            if message_object_class:
                self.hook_has_media_spoilers(message_object_class)

            # Hook SharedPhotoVideoCell2 for shared media
            shared_cell_class = find_class("org.telegram.ui.Cells.SharedPhotoVideoCell2")
            if shared_cell_class:
                self.hook_shared_media_spoilers(shared_cell_class)

            self.log("✅ Media spoiler hooks setup completed")

        except Exception as e:
            self.log(f"❌ Error setting up media spoiler hooks: {e}")

    def hook_has_media_spoilers(self, message_object_class):
        """Hook hasMediaSpoilers method to always return false"""
        try:
            java_class = message_object_class.getClass()
            methods = java_class.getDeclaredMethods()

            for method in methods:
                if method.getName() == "hasMediaSpoilers" and method.getParameterCount() == 0:
                    method.setAccessible(True)
                    hook_instance = HasMediaSpoilersHook(self)
                    hook_obj = self.hook_method(method, hook_instance)

                    if hook_obj:
                        self.hooked_methods.append(hook_obj)
                        self.log("✅ Successfully hooked hasMediaSpoilers method")
                        return True
                    break

        except Exception as e:
            self.log(f"❌ Error hooking hasMediaSpoilers: {e}")
        return False

    def hook_shared_media_spoilers(self, shared_cell_class):
        """Hook shared media spoiler methods"""
        try:
            java_class = shared_cell_class.getClass()
            methods = java_class.getDeclaredMethods()

            for method in methods:
                if method.getName() == "canRevealSpoiler" and method.getParameterCount() == 0:
                    method.setAccessible(True)
                    hook_instance = CanRevealSpoilerHook(self)
                    hook_obj = self.hook_method(method, hook_instance)

                    if hook_obj:
                        self.hooked_methods.append(hook_obj)
                        self.log("✅ Successfully hooked canRevealSpoiler method")
                        break

        except Exception as e:
            self.log(f"❌ Error hooking shared media spoilers: {e}")

    def process_message_spoilers(self, message, account):
        """Process spoilers in a message"""
        try:
            if not message or not hasattr(message, 'id'):
                return

            # Check if we should process this message based on settings
            if hasattr(message, 'out') and message.out:
                if not self.get_setting("process_outgoing", False):
                    if self.get_setting("detailed_logs", False):
                        self.log(f"Skipping outgoing message {message.id}")
                    return
            elif self.get_setting("process_incoming_only", True):
                # This is an incoming message, process it
                if self.get_setting("detailed_logs", False):
                    self.log(f"Processing incoming message {message.id}")

            message_id = f"{message.id}_{account}"

            # Skip if already processed
            if message_id in self.processed_messages:
                if self.get_setting("detailed_logs", False):
                    self.log(f"Message {message.id} already processed, skipping")
                return

            self.processed_messages.add(message_id)

            if self.get_setting("detailed_logs", False):
                self.log(f"Processing message {message.id} - has entities: {hasattr(message, 'entities') and message.entities is not None}")

            # Process text spoilers
            if self.get_setting("remove_text_spoilers", True):
                self.remove_text_spoilers(message)

            # Process media spoilers (only if not using method hooks)
            if self.get_setting("remove_media_spoilers", True) and not self.get_setting("hook_method_calls", True):
                self.remove_media_spoilers(message)

        except Exception as e:
            self.log(f"❌ Error processing message spoilers: {e}")
            if self.get_setting("detailed_logs", False):
                self.log(f"Traceback: {traceback.format_exc()}")

    def remove_text_spoilers(self, message):
        """Remove text spoilers from message"""
        try:
            if not hasattr(message, 'entities') or not message.entities:
                return

            # Convert Java ArrayList to Python list for iteration
            entities_list = []
            try:
                # Handle Java ArrayList
                for i in range(message.entities.size()):
                    entities_list.append(message.entities.get(i))
            except:
                # Fallback: try direct iteration
                for entity in message.entities:
                    entities_list.append(entity)

            # Find spoiler entities
            spoiler_entities = []
            for entity in entities_list:
                try:
                    # Check for different types of spoiler entities
                    entity_class_name = entity.__class__.__name__.lower()
                    if ('spoiler' in entity_class_name or
                        'messageentityspoiler' in entity_class_name or
                        (hasattr(entity, 'flags') and hasattr(entity, 'spoiler') and entity.spoiler)):
                        spoiler_entities.append(entity)
                except Exception as e:
                    if self.get_setting("detailed_logs", False):
                        self.log(f"Error checking entity: {e}")

            if not spoiler_entities:
                return

            self.log(f"🔓 Found {len(spoiler_entities)} text spoiler(s) in message {message.id}")

            # Remove spoiler entities
            removed_count = 0
            for spoiler_entity in spoiler_entities:
                try:
                    message.entities.remove(spoiler_entity)
                    removed_count += 1
                    if self.get_setting("detailed_logs", False):
                        self.log(f"✅ Removed text spoiler entity: {spoiler_entity.__class__.__name__}")
                except Exception as e:
                    if self.get_setting("detailed_logs", False):
                        self.log(f"❌ Failed to remove spoiler entity: {e}")

            if removed_count > 0:
                # Update statistics
                current_count = self.get_setting("text_spoilers_removed", 0)
                self.set_setting("text_spoilers_removed", current_count + removed_count)

                if self.get_setting("show_notifications", False):
                    BulletinHelper.show_success(f"Removed {removed_count} text spoiler(s)")

        except Exception as e:
            self.log(f"❌ Error removing text spoilers: {e}")
            if self.get_setting("detailed_logs", False):
                self.log(f"Traceback: {traceback.format_exc()}")

    def remove_media_spoilers(self, message):
        """Remove media spoilers from message"""
        try:
            if not hasattr(message, 'media') or not message.media:
                if self.get_setting("detailed_logs", False):
                    self.log(f"Message {message.id} has no media")
                return

            removed_count = 0

            if self.get_setting("detailed_logs", False):
                media_type = message.media.__class__.__name__ if message.media else "None"
                has_spoiler = hasattr(message.media, 'spoiler') and message.media.spoiler
                self.log(f"Message {message.id} media type: {media_type}, has spoiler: {has_spoiler}")

            # Check if media has spoiler flag
            if hasattr(message.media, 'spoiler') and message.media.spoiler:
                self.log(f"🔓 Found media spoiler in message {message.id}")

                # Remove spoiler flag
                message.media.spoiler = False
                removed_count += 1

                if self.get_setting("detailed_logs", False):
                    self.log("✅ Removed media spoiler flag")

            # Also check for grouped media (albums)
            if hasattr(message, 'grouped_id') and message.grouped_id:
                if self.get_setting("detailed_logs", False):
                    self.log(f"Processing grouped media with ID: {message.grouped_id}")

            if removed_count > 0:
                # Update statistics
                current_count = self.get_setting("media_spoilers_removed", 0)
                self.set_setting("media_spoilers_removed", current_count + removed_count)

                if self.get_setting("show_notifications", False):
                    BulletinHelper.show_success(f"Removed {removed_count} media spoiler(s)")

        except Exception as e:
            self.log(f"❌ Error removing media spoilers: {e}")
            if self.get_setting("detailed_logs", False):
                self.log(f"Traceback: {traceback.format_exc()}")

    def log(self, message):
        """Log with plugin prefix"""
        _log(f"[SpoilerRemover] {message}")

    def create_settings(self):
        """Create plugin settings UI"""
        return [
            Header(text="Spoiler Remover Settings"),
            Switch(
                key="enabled",
                text="Enable Spoiler Remover",
                default=True,
                subtext="Automatically remove spoilers from messages and media",
                on_change=self.on_enabled_change
            ),
            Divider(),
            Header(text="Spoiler Types"),
            Switch(
                key="remove_text_spoilers",
                text="Remove Text Spoilers",
                default=True,
                subtext="Automatically reveal spoilers in text messages",
                icon="msg_spoiler"
            ),
            Switch(
                key="remove_media_spoilers", 
                text="Remove Media Spoilers",
                default=True,
                subtext="Automatically reveal spoilers in photos and videos",
                icon="msg_photo"
            ),
            Divider(),
            Header(text="Processing Options"),
            Switch(
                key="process_incoming_only",
                text="Process Incoming Messages Only",
                default=True,
                subtext="Only process new incoming messages, not message history",
                icon="msg_incoming"
            ),
            Switch(
                key="process_outgoing",
                text="Process Outgoing Messages",
                default=False,
                subtext="Also process messages you send (for testing)",
                icon="msg_outgoing"
            ),
            Divider(),
            Header(text="Advanced Settings"),
            Switch(
                key="show_notifications",
                text="Show Notifications",
                default=False,
                subtext="Show notification when spoilers are removed",
                icon="msg_info"
            ),
            Switch(
                key="detailed_logs",
                text="Detailed Logging",
                default=True,
                subtext="Enable detailed logging for debugging (temporarily enabled)",
                icon="msg_log"
            ),
            Switch(
                key="hook_method_calls",
                text="Hook Method Calls",
                default=True,
                subtext="Use advanced method hooking for better spoiler removal",
                icon="msg_settings"
            ),
            Divider(),
            Text(
                text="ℹ️ How it works:",
                accent=True,
                icon="msg_info"
            ),
            Text(
                text="• Text spoilers: Removes spoiler entities from message text\n• Media spoilers: Removes spoiler flag from photos and videos\n• Works automatically on all incoming messages\n• Changes take effect immediately"
            ),
            Text(
                text="⚠️ Note: This plugin modifies incoming messages before they are displayed. The original spoilers are preserved on the server.",
                accent=True
            ),
            Divider(),
            Text(
                text="📊 Plugin Statistics",
                accent=True,
                on_click=self.show_stats
            ),
            Text(
                text="🗑️ Reset Statistics",
                red=True,
                on_click=self.reset_stats
            )
        ]

    def on_enabled_change(self, new_value):
        """Called when enabled setting changes"""
        self.log(f"Plugin enabled changed to: {new_value}")
        if new_value:
            BulletinHelper.show_info("Spoiler Remover: Enabled")
        else:
            BulletinHelper.show_info("Spoiler Remover: Disabled")

    def show_stats(self, view):
        """Show plugin statistics"""
        try:
            processed_count = len(self.processed_messages)
            text_spoilers_removed = self.get_setting("text_spoilers_removed", 0)
            media_spoilers_removed = self.get_setting("media_spoilers_removed", 0)
            
            stats_text = f"📊 Spoiler Remover Statistics\n\n✅ Messages processed: {processed_count}\n🔓 Text spoilers removed: {text_spoilers_removed}\n📷 Media spoilers removed: {media_spoilers_removed}\n\nTotal spoilers removed: {text_spoilers_removed + media_spoilers_removed}"
            
            from ui.alert import AlertDialog
            AlertDialog.show_info(stats_text)
            
        except Exception as e:
            self.log(f"Error showing stats: {e}")
            BulletinHelper.show_error("Failed to load statistics")

    def reset_stats(self, view):
        """Reset plugin statistics"""
        try:
            self.processed_messages.clear()
            self.set_setting("text_spoilers_removed", 0)
            self.set_setting("media_spoilers_removed", 0)
            
            self.log("📊 All statistics reset")
            BulletinHelper.show_success("Statistics have been reset")
        except Exception as e:
            self.log(f"Error resetting stats: {e}")
            BulletinHelper.show_error("Failed to reset statistics")


class HasMediaSpoilersHook(MethodHook):
    """Hook for hasMediaSpoilers method to always return false"""

    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin

    def before_hooked_method(self, param):
        """Called before hasMediaSpoilers method"""
        try:
            if not self.plugin.get_setting("remove_media_spoilers", True) or not self.plugin.plugin_active:
                return

            # Always return false to disable media spoilers
            from java.lang import Boolean
            param.setResult(Boolean(False))

            # Update statistics
            current_count = self.plugin.get_setting("media_spoilers_removed", 0)
            self.plugin.set_setting("media_spoilers_removed", current_count + 1)

            if self.plugin.get_setting("detailed_logs", False):
                self.plugin.log("🔓 Intercepted hasMediaSpoilers - returning false")

        except Exception as e:
            self.plugin.log(f"❌ Error in HasMediaSpoilersHook: {e}")


class CanRevealSpoilerHook(MethodHook):
    """Hook for canRevealSpoiler method to always return false"""

    def __init__(self, plugin):
        super().__init__()
        self.plugin = plugin

    def before_hooked_method(self, param):
        """Called before canRevealSpoiler method"""
        try:
            if not self.plugin.get_setting("remove_media_spoilers", True) or not self.plugin.plugin_active:
                return

            # Always return false to prevent spoiler reveal UI
            from java.lang import Boolean
            param.setResult(Boolean(False))

            if self.plugin.get_setting("detailed_logs", False):
                self.plugin.log("🔓 Intercepted canRevealSpoiler - returning false")

        except Exception as e:
            self.plugin.log(f"❌ Error in CanRevealSpoilerHook: {e}")
