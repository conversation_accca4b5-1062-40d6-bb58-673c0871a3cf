import datetime
import math
import os
import random
import re
import requests
import textwrap
import threading
import time

from PIL import Image, ImageDraw, ImageFont, ImageFilter, ImageEnhance
from android.content import Intent
from android.net import <PERSON><PERSON>
from java.io import File
from java.util import ArrayList
from java.util import Locale
from org.telegram.messenger import <PERSON><PERSON><PERSON><PERSON>, SendMessagesHelper, AndroidUtilities, R
from org.telegram.tgnet.tl import TL_account
from org.telegram.ui.ActionBar import AlertDialog

from android_utils import log, run_on_ui_thread
from base_plugin import BasePlugin, HookResult, HookStrategy
from client_utils import get_send_messages_helper, get_last_fragment, get_account_instance, \
    send_request, get_user_config, get_messages_controller, send_message
from markdown_utils import parse_markdown
from ui.alert import AlertDialogBuilder
from ui.bulletin import BulletinHelper
from ui.settings import Header, Switch, Divider, Input, Selector, Text

__id__ = "rehuyandex"
__name__ = "reMusic"
__description__ = "Now play plugin for Yandex Music.\n\nUse:\n   .now — send now play card\n   .nowt — send now play track\n\nThanks @mipohbopohih & @exteradev for the help"
__icon__ = "remusic/0"
__version__ = "1.2.5"
__author__ = "@reNightly, @itsv1eds, @qmrrchh"
__min_version__ = "11.9.1"

API_BASE_URL = "https://api.mipoh.ru"
TEMP_DIR_NAME = "yandexmusic_temp"
DEFAULT_STREAM_STRING = "🎵 {title} — {artists}"
DEFAULT_STREAM_TEXT = "Hi, I use exteraGram"
FONTS = {
    0: "Onest",
    1: "Circular",
    2: "NotoSansJP"
}
DEFAULT_COLOR = {
    "background_color": "#000000",
    "title_text_color": "#FFFFFF",
    "subtext_color": "#A0A0A0"
}
DEFAULT_COMMANDS = {
    "pic": [".now", ". now"],
    "track": [".nowt", ". nowt"],
}
SPECIAL_COMMANDS = {
    "pic": [".ynow", ". ynow"],
    "track": [".ynowt", ". ynowt"],
}
INSTANT_SUBTEXT = "Powered by"
INSTANT_MAIN_TEXT = "reMusic"


class Language_Controller:
    def __init__(self):
        self.lang_code = Locale.getDefault().getLanguage()

    def get_controller(self):
        if self.lang_code == "ru":
            return self.lang_ru()
        elif self.lang_code == "pt":
            return self.lang_ptbr()
        else:
            return self.lang_en()

    class lang_en:
        _lang = 'en'

        Settings_ZwyLibNeed = "To auto-update the plugin, it is recommended to install the auxiliary plugin ZwyLib. You can find it in the @ZwyPlugins channel."
        Settings_reFamily = "A neighboring plugin from the re[] family was detected. A prefix s (.ynow/.ynowt) was added to reYandex commands to avoid conflicts between plugins."

        Settings_AuthToken_Header = "Authorization"

        Settings_LoginGuide_Text = "Help"
        Settings_LoginGuide_Title = "Authorization in reYandex"
        Settings_LoginGuide_Go = "guide"

        Settings_LoginGuide = "Go to token guide, log in to your Yandex account, and paste your token below."
        Settings_Username_Text = "Yandex.Music token"
        Settings_Username_Subtext = "Paste your token from login guide here."

        Settings_CardSettings_Header = "Customization"

        Settings_BackgroundMode_Text = "Background"
        Settings_BackgroundMode_Item_1 = "Track Cover"
        Settings_BackgroundMode_Item_2 = "Cover Color"

        Settings_Font_Text = "Font"
        Settings_Font_Item1 = "Onest"
        Settings_Font_Item2 = "Spotify"
        Settings_Font_Item3 = "NotoSansJP"

        Settings_YandexLink_Text = "Insert link to ..."
        Settings_YandexLink_Item_1 = "—"
        Settings_YandexLink_Item_2 = "Track"
        Settings_YandexLink_Item_3 = "Album"

        Settings_SongLinkInclude_Text = "Link to other platforms"
        Settings_SongLinkInclude_Subtext = "Adds a link to the song page on song.link"
        Settings_FastCardRender_Text = "Pre-render cards"
        Settings_FastCardRender_Subtext = "May increase battery consumption"

        Setting_AdvancedMode_Text = "Advanced Settings"
        Setting_AdvancedMode_Subtext = "Additional customization settings"
        Setting_AdvancedMode_Title = "Customization"

        Settings_BackgroundColor_Text = "Background Color"
        Settings_BackgroundColor_Subtext = "Background color if 'Cover Color' is selected in 'Background'"

        Settings_AccentColor_Text = "Accent Color"
        Settings_AccentColor_Subtext = "Text color used in track title and active progress bar"

        Settings_SecondaryColor_Text = "Secondary Color"
        Settings_SecondaryColor_Subtext = "Text color used for artist line, inactive progress bar, and timers"

        Settings_InstantCardSubtext_Text = "Secondary Text"
        Settings_InstantCardSubtext_Subtext = "Text displayed at the top of the bottom block of pre-rendered cards"

        Settings_InstantCardMainText_Text = "Main Text"
        Settings_InstantCardMainText_Subtext = "Text displayed at the bottom of the bottom block of pre-rendered cards"

        Settings_Stream_Header = "Profile Streaming"

        Setting_Stream_Title = "Stream settings"
        Settings_Stream_Text = "Stream track to profile"
        Settings_Stream_Subtext = "Updates your bio/location with the currently playing track"

        Settings_StreamAlert_Title = "⚠️⚠️WARNING⚠️⚠️"
        Settings_StreamAlert_Text = "This feature may work inconsistently due to Telegram's profile change limits. As a result, your profile information may not update immediately. Use at your own risk."

        Setting_TrackStream_Text = "Stream to..."
        Setting_TrackStream_Item1 = "Bio"
        Setting_TrackStream_Item2 = "Location (Recommended)"

        Settings_InStream_Text = "Default Text"
        Settings_InStream_Subtext = "Text displayed when the player is unavailable or no track is playing"

        Settings_FormatInStream_Text = "Format"
        Settings_FormatInStream_Subtext = "Customize track display. {title} — track name, {artists} — artist(s)"

        Settings_Other_Header = "Other"

        Setting_Other_SourceCheck = "File Integrity Check"
        Setting_Other_ForceDownload = "Download Full Resource Package"
        Setting_Other_Donate = "Support Development"

        Alert_HEX_Title = "HEX Error"
        Alert_HEX_Text = "Invalid HEX color code"

        Alert_SourceError_Title = "Integrity Check Error"
        Alert_SourceError_FontsNotFound = "Font files not found"
        Alert_SourceError_FontNotFound = "Font {0} not found"
        Alert_SourceError_FontApplyError = "Failed to apply font {0}: {1}"

        Alert_SourceSuccess_Title = "Success"
        Alert_SourceSuccess_Text = "No issues detected during resource check"

        Alert_SourceDownload_Title = "Downloading Resources"
        Alert_SourceCheck_Title = "Checking Resources"

        Alert_Donate_Title = "Support Development"
        Alert_Donate_Text = "Below you can copy the TON address of the reYandex developers and support the development with your donation."
        Alert_Donate_Button = "Copy"

        Card_PlayerInactive = "Player not started"
        Card_PlayingIn = "Playing on"

        Message_CaptionLink_Text = "[Yandex]({0})"
        Message_CaptionSongLink_Text = "[song.link]({0})"
        Message_CaptionDivider = " • "
        Message_PlayerNotActive = "Yandex player unavailable or not started"
        Message_Trigger_Text = "⚠️ *rePlugins Family Error* \nCommand *{0}* is ambiguous for reYandex & reMusic plugins.\n\nTo get information from the service you need, use the commands:\n   .snow/.snowt - for *reYandex* \n   .ynow/.ynowt - for *reMusic*"

        Bulletin_NowPlay = "Now playing: {0}"

        Bulletin_ErrorMessage = "[reYandex] Error: {0}"
        Bulletin_ErrorMessageCopy = "[reYandex] An error occurred"
        Bulletin_FailedProcessImage = "[reYandex] Image processing error"
        Bulletin_FailedGetYouTubeLink = "[reYandex] Failed to get YouTube/SoundCloud link"
        Bulletin_ErrorSendingAudio = "[reYandex] Audio sending error"
        Bulletin_InvalidCobaltResponse = "Invalid response from Cobalt API"
        Bulletin_NoItemsToDownload = "No items to download"
        Bulletin_CobaltErrorCode = "[reYandex]: Cobalt Error Code — {0}"

    class lang_ru:
        _lang = 'ru'

        Settings_ZwyLibNeed = "Для автоматического обновления плагина рекомендуется установить вспомогательный плагин ZwyLib. Вы можете найти его в канале @ZwyPlugins."
        Settings_reFamily = "Обнаружен соседний плагин из семейства re[]. К командам reYandex был добавлен префикс s (.ynow/.ynowt) для предотвращения конфликтов между плагинами."

        Settings_AuthToken_Header = "Авторизация"

        Settings_LoginGuide_Text = "Помощь"
        Settings_LoginGuide_Title = "Авторизация в reYandex"
        Settings_LoginGuide_Go = "инструкция"

        Settings_LoginGuide = "Перейдите к инструкции по получению токена, войдите в аккаунт Яндекса и вставьте токен ниже."
        Settings_Username_Text = "Токен Яндекс.Музыки"
        Settings_Username_Subtext = "Вставьте сюда токен из инструкции по авторизации."

        Settings_CardSettings_Header = "Внешний вид"

        Settings_BackgroundMode_Text = "Фон"
        Settings_BackgroundMode_Item_1 = "Обложка трека"
        Settings_BackgroundMode_Item_2 = "Цвет обложки"

        Settings_Font_Text = "Шрифт"
        Settings_Font_Item1 = "Onest"
        Settings_Font_Item2 = "Spotify"
        Settings_Font_Item3 = "NotoSansJP"

        Settings_YandexLink_Text = "Вставьте ссылку на ..."
        Settings_YandexLink_Item_1 = "—"
        Settings_YandexLink_Item_2 = "Трек"
        Settings_YandexLink_Item_3 = "Альбом"

        Settings_SongLinkInclude_Text = "Ссылка на другие платформы"
        Settings_SongLinkInclude_Subtext = "Добавляет ссылку на страницу песни на song.link"
        Settings_FastCardRender_Text = "Предзагрузка карточек"
        Settings_FastCardRender_Subtext = "Может увеличить расход батареи"

        Setting_AdvancedMode_Text = "Расширенные настройки"
        Setting_AdvancedMode_Subtext = "Дополнительные параметры кастомизации"
        Setting_AdvancedMode_Title = "Настройка внешнего вида"

        Settings_BackgroundColor_Text = "Цвет фона"
        Settings_BackgroundColor_Subtext = "Цвет фона при выборе 'Цвет обложки' в параметре 'Фон'"

        Settings_AccentColor_Text = "Акцентный цвет"
        Settings_AccentColor_Subtext = "Цвет текста в названии трека и активном индикаторе прогресса"

        Settings_SecondaryColor_Text = "Вторичный цвет"
        Settings_SecondaryColor_Subtext = "Цвет текста для строки с артистом, неактивного прогресс-бара и таймеров"

        Settings_InstantCardSubtext_Text = "Вторичный текст"
        Settings_InstantCardSubtext_Subtext = "Текст в верхней части нижнего блока предзагруженных карточек"

        Settings_InstantCardMainText_Text = "Основной текст"
        Settings_InstantCardMainText_Subtext = "Текст в нижней части нижнего блока предзагруженных карточек"

        Settings_Stream_Header = "Поток в профиль"

        Setting_Stream_Title = "Настройки стрима"
        Settings_Stream_Text = "Стрим трека в профиль"
        Settings_Stream_Subtext = "Обновляет био/локацию текущим треком"

        Settings_StreamAlert_Title = "⚠️⚠️ВНИМАНИЕ⚠️⚠️"
        Settings_StreamAlert_Text = "Эта функция может работать нестабильно из-за ограничений Telegram на частую смену профиля. Ваши данные могут обновляться с задержкой. Используйте на свой страх и риск."

        Setting_TrackStream_Text = "Стримить в..."
        Setting_TrackStream_Item1 = "Био"
        Setting_TrackStream_Item2 = "Локацию (рекомендуется)"

        Settings_InStream_Text = "Текст по умолчанию"
        Settings_InStream_Subtext = "Отображается, если плеер недоступен или ничего не воспроизводится"

        Settings_FormatInStream_Text = "Формат"
        Settings_FormatInStream_Subtext = "Формат отображения трека. {title} — название трека, {artists} — артист(ы)"

        Settings_Other_Header = "Прочее"

        Setting_Other_SourceCheck = "Проверка целостности файлов"
        Setting_Other_ForceDownload = "Скачать все ресурсы заново"
        Setting_Other_Donate = "Поддержать разработку"

        Alert_HEX_Title = "Ошибка HEX"
        Alert_HEX_Text = "Недопустимый HEX-код цвета"

        Alert_SourceError_Title = "Ошибка проверки"
        Alert_SourceError_FontsNotFound = "Файлы шрифтов не найдены"
        Alert_SourceError_FontNotFound = "Шрифт {0} не найден"
        Alert_SourceError_FontApplyError = "Не удалось применить шрифт {0}: {1}"

        Alert_SourceSuccess_Title = "Успешно"
        Alert_SourceSuccess_Text = "Ошибок при проверке ресурсов не найдено"

        Alert_SourceDownload_Title = "Скачивание ресурсов"
        Alert_SourceCheck_Title = "Проверка ресурсов"

        Alert_Donate_Title = "Поддержка разработки"
        Alert_Donate_Text = "Ниже вы можете скопировать TON-адрес разработчиков reYandex и поддержать проект донатом."
        Alert_Donate_Button = "Скопировать"

        Card_PlayerInactive = "Плеер не запущен"
        Card_PlayingIn = "Играет на"

        Message_CaptionLink_Text = "[Яндекс]({0})"
        Message_CaptionSongLink_Text = "[song.link]({0})"
        Message_CaptionDivider = " • "
        Message_PlayerNotActive = "Плеер Яндекс.Музыки недоступен или не запущен"
        Message_Trigger_Text = "⚠️ *Ошибка семейства rePlugins* \nКоманда *{0}* неоднозначна для плагинов reYandex и reMusic.\n\nЧтобы получить информацию от нужного сервиса, используйте команды:\n   .snow/.snowt — для *reYandex* \n   .ynow/.ynowt — для *reMusic*"

        Bulletin_NowPlay = "Сейчас играет: {0}"

        Bulletin_ErrorMessage = "[reYandex] Ошибка: {0}"
        Bulletin_ErrorMessageCopy = "[reYandex] Произошла ошибка"
        Bulletin_FailedProcessImage = "[reYandex] Ошибка обработки изображения"
        Bulletin_FailedGetYouTubeLink = "[reYandex] Не удалось получить ссылку на YouTube/SoundCloud"
        Bulletin_ErrorSendingAudio = "[reYandex] Ошибка отправки аудио"
        Bulletin_InvalidCobaltResponse = "Некорректный ответ от Cobalt API"
        Bulletin_NoItemsToDownload = "Нет элементов для загрузки"
        Bulletin_CobaltErrorCode = "[reYandex]: Код ошибки Cobalt — {0}"

    class lang_ptbr:
        _lang = 'pt'

        Settings_ZwyLibNeed = "Para atualizar automaticamente o plugin, é recomendável instalar o plugin auxiliar ZwyLib. Você pode encontrá-lo no canal @ZwyPlugins."
        Settings_reFamily = "Foi detectado um plugin vizinho da família re[]. Um prefixo s (.ynow/.ynowt) foi adicionado aos comandos do reYandex para evitar conflitos entre plugins."

        Settings_AuthToken_Header = "Autorização"

        Settings_LoginGuide_Text = "Ajuda"
        Settings_LoginGuide_Title = "Autorização no reYandex"
        Settings_LoginGuide_Go = "guia"

        Settings_LoginGuide = "Acesse o guia do token, faça login na sua conta Yandex e cole seu token abaixo."
        Settings_Username_Text = "Token do Yandex.Music"
        Settings_Username_Subtext = "Cole aqui seu token do guia de login."

        Settings_CardSettings_Header = "Personalização"

        Settings_BackgroundMode_Text = "Plano de fundo"
        Settings_BackgroundMode_Item_1 = "Capa da faixa"
        Settings_BackgroundMode_Item_2 = "Cor da capa"

        Settings_Font_Text = "Fonte"
        Settings_Font_Item1 = "Onest"
        Settings_Font_Item2 = "Spotify"
        Settings_Font_Item3 = "NotoSansJP"

        Settings_YandexLink_Text = "Inserir link para ..."
        Settings_YandexLink_Item_1 = "—"
        Settings_YandexLink_Item_2 = "Faixa"
        Settings_YandexLink_Item_3 = "Álbum"

        Settings_SongLinkInclude_Text = "Link para outras plataformas"
        Settings_SongLinkInclude_Subtext = "Adiciona um link da música para a página no song.link"
        Settings_FastCardRender_Text = "Pré-carregar cartões"
        Settings_FastCardRender_Subtext = "Pode aumentar o consumo de bateria"

        Setting_AdvancedMode_Text = "Modo avançado"
        Setting_AdvancedMode_Subtext = "Configurações adicionais de personalização"
        Setting_AdvancedMode_Title = "Personalização"

        Settings_BackgroundColor_Text = "Cor de fundo"
        Settings_BackgroundColor_Subtext = "Cor de fundo usada quando 'Cor da capa' está selecionada em 'Plano de fundo'"

        Settings_AccentColor_Text = "Cor de destaque"
        Settings_AccentColor_Subtext = "Cor do texto no título da faixa e na barra de progresso ativa"

        Settings_SecondaryColor_Text = "Cor secundária"
        Settings_SecondaryColor_Subtext = "Cor do texto para o artista, barra de progresso inativa e cronômetros"

        Settings_InstantCardSubtext_Text = "Texto secundário"
        Settings_InstantCardSubtext_Subtext = "Texto exibido no topo do bloco inferior dos cartões pré-carregados"

        Settings_InstantCardMainText_Text = "Texto principal"
        Settings_InstantCardMainText_Subtext = "Texto exibido na parte inferior do bloco inferior dos cartões pré-carregados"

        Settings_Stream_Header = "Streaming no perfil"

        Setting_Stream_Title = "Configurações de stream"
        Settings_Stream_Text = "Transmitir faixa no perfil"
        Settings_Stream_Subtext = "Atualiza sua biografia/localização com a faixa atual"

        Settings_StreamAlert_Title = "⚠️⚠️AVISO⚠️⚠️"
        Settings_StreamAlert_Text = "Esta função pode funcionar de forma inconsistente devido às limitações do Telegram para alterar informações de perfil. Sua biografia pode não ser atualizada imediatamente. Use por sua conta e risco."

        Setting_TrackStream_Text = "Transmitir para..."
        Setting_TrackStream_Item1 = "Biografia"
        Setting_TrackStream_Item2 = "Localização (Recomendado)"

        Settings_InStream_Text = "Texto padrão"
        Settings_InStream_Subtext = "Texto exibido quando o player está indisponível ou nada está sendo reproduzido"

        Settings_FormatInStream_Text = "Formato"
        Settings_FormatInStream_Subtext = "Personalize como a faixa será exibida. {title} — nome da faixa, {artists} — artista(s)"

        Settings_Other_Header = "Outros"

        Setting_Other_SourceCheck = "Verificar integridade dos arquivos"
        Setting_Other_ForceDownload = "Baixar todos os recursos novamente"
        Setting_Other_Donate = "Apoiar o desenvolvimento"

        Alert_HEX_Title = "Erro HEX"
        Alert_HEX_Text = "Código de cor HEX inválido"

        Alert_SourceError_Title = "Erro de verificação"
        Alert_SourceError_FontsNotFound = "Arquivos de fontes não encontrados"
        Alert_SourceError_FontNotFound = "Fonte {0} não encontrada"
        Alert_SourceError_FontApplyError = "Erro ao aplicar fonte {0}: {1}"

        Alert_SourceSuccess_Title = "Sucesso"
        Alert_SourceSuccess_Text = "Nenhum problema encontrado durante a verificação dos recursos"

        Alert_SourceDownload_Title = "Baixando recursos"
        Alert_SourceCheck_Title = "Verificando recursos"

        Alert_Donate_Title = "Apoiar o desenvolvimento"
        Alert_Donate_Text = "Abaixo você pode copiar o endereço TON dos desenvolvedores do reYandex e apoiar o projeto com sua doação."
        Alert_Donate_Button = "Copiar"

        Card_PlayerInactive = "Player não iniciado"
        Card_PlayingIn = "Tocando em"

        Message_CaptionLink_Text = "[Yandex]({0})"
        Message_CaptionSongLink_Text = "[song.link]({0})"
        Message_CaptionDivider = " • "
        Message_PlayerNotActive = "Player do Yandex indisponível ou não iniciado"
        Message_Trigger_Text = "⚠️ *Erro da família rePlugins* \nO comando *{0}* é ambíguo para os plugins reYandex e reMusic.\n\nPara acessar o serviço correto, use os comandos:\n   .snow/.snowt – para *reYandex* \n   .ynow/.ynowt – para *reMusic*"

        Bulletin_NowPlay = "Tocando agora: {0}"

        Bulletin_ErrorMessage = "[reYandex] Erro: {0}"
        Bulletin_ErrorMessageCopy = "[reYandex] Ocorreu um erro"
        Bulletin_FailedProcessImage = "[reYandex] Erro ao processar a imagem"
        Bulletin_FailedGetYouTubeLink = "[reYandex] Falha ao obter link do YouTube/SoundCloud"
        Bulletin_ErrorSendingAudio = "[reYandex] Erro ao enviar áudio"
        Bulletin_InvalidCobaltResponse = "Resposta inválida da API Cobalt"
        Bulletin_NoItemsToDownload = "Nenhum item para baixar"
        Bulletin_CobaltErrorCode = "[reYandex]: Código de erro Cobalt — {0}"


string = Language_Controller().get_controller()


class Track:
    def __init__(self, active, id=None, title=None, artist=None, album=None,
                 thumb=None, duration=None, progress=None, link=None):
        self.active = active
        self.id = id
        self.title = title
        self.artist = artist or []
        self.album = album
        self.thumb = thumb
        self.duration = duration
        self.progress = progress
        self.link = link


class YandexMusic:
    def __init__(self, token):
        self.token = token.strip()
        self.headers = {"User-Agent": "Mozilla/5.0", "Accept": "application/json", "ya-token": self.token}
        self.now_track = Track(active=False)
        self.memory_id = "Default"
        threading.Thread(target=self._poller, daemon=True).start()

    def get_current_track(self):
        if not self.token:
            return Track(active=False)
        try:
            r = requests.get(f"{API_BASE_URL}/get_current_track_beta", headers=self.headers, timeout=10)
            log(f"[reMusic] get_current_track status={r.status_code}, body={r.text[:200]}")
            data = r.json()
            log(f"[reMusic] get_current_track data keys={list(data.keys())}")
            if r.status_code != 200 or 'track' not in data:
                log("[reMusic] no 'track' key or bad status")
                return Track(active=False)
            t = data['track']
            log(f"[reMusic] raw track: {t}")
            tid = t.get('track_id')
            # Build Track from beta response 't'
            raw_artist = t.get('artist', '')
            if isinstance(raw_artist, str):
                artists = [x.strip() for x in raw_artist.split(',') if x.strip()]
            elif isinstance(raw_artist, list):
                artists = raw_artist
            else:
                artists = []
            album = t.get('album')
            thumb = t.get('img')
            duration = int(t.get('duration', 0))
            progress_ms_raw = data.get('progress_ms', 0)
            try:
                progress_ms = int(progress_ms_raw)
            except:
                progress_ms = 0
            # Convert milliseconds to seconds
            progress = progress_ms // 1000
            track = Track(
                active=True,
                id=tid,
                title=t.get('title'),
                artist=artists,
                album=album,
                thumb=thumb,
                duration=duration,
                progress=progress,
                link=f"https://music.yandex.ru/track/{tid}"
            )
            track.download_url = t.get('download_link')
            log(f"[reMusic] built track: {track.title} — {track.artist}, album={track.album}, duration={track.duration}, progress={track.progress}")
            return track
        except Exception as e:
            log(f"[reMusic] get_current_track exception: {e}")
            return Track(active=False)

    def _poller(self):
        while True:
            try:
                data = self.get_current_track()
                self.now_track = data

                if not data.active:
                    time.sleep(2.5)
            except Exception as e:
                try:
                    self.now_track.active
                except:
                    self.now_track = Track(active=False)
                if "retries exceeded" not in str(e):
                    log(f"[exteraGram reMusic]: Internal Error {e}")
            time.sleep(2.5)


def show_with_copy(message, submsg):
    def copy():
        if AndroidUtilities.addToClipboard(submsg):
            BulletinHelper.show_copied_to_clipboard()

    BulletinHelper.show_with_button(message, R.raw.error, "Copy", lambda: copy())


class ReMusicPlugin(BasePlugin):

    def __init__(self):
        super().__init__()
        self.progress = None
        self.re_family = None
        self.for_metadata = None
        self._temp_dir = None
        self.alert_builder_instance: AlertDialogBuilder = None

    def on_plugin_load(self):
        self.add_on_send_message_hook(priority=42)
        self._temp_dir = self._get_temp_dir()
        if self._temp_dir:
            log("reMusic plugin loaded successfully")
        else:
            log("Failed to initialize temp directory for reMusic")
        self.yandex = YandexMusic(self.get_setting('yandex_api_token', "<your token>"))
        self.progress = self.Progress()
        threading.Thread(target=self._streamer, daemon=True).start()
        try:
            import reyandex_renightly
            self.re_family = True
        except:
            self.re_family = False
        self.autocard_render(self.get_setting('fast_card_render', False))

    def on_plugin_unload(self):
        self._unactive()

    def create_settings(self):
        advanced = self.get_setting('advanced_mode', False)
        update_bio = self.get_setting("update_bio", False)

        settings = [
            Divider(
                text=string.Settings_reFamily
            ) if self.re_family else None,

            Header(text=string.Settings_AuthToken_Header),
            Input(
                key="yandex_api_token",
                text=string.Settings_Username_Text,
                default="<your token>",
                subtext=string.Settings_Username_Subtext,
                on_change=lambda new_value: self.update_yandex_object(new_value),
                icon="msg_pin_code"
            ),
            Text(
                text=string.Settings_LoginGuide_Text,
                icon="msg_info",
                on_click=lambda view: self.show_my_info_alert(
                    title=string.Settings_LoginGuide_Title,
                    message=string.Settings_LoginGuide,
                    positive_button="OK",
                    neutral_button=string.Settings_LoginGuide_Go,
                    neutral_link="https://yandex-music.readthedocs.io/en/main/token.html",
                    neutral_type="link"
                )
            ),

            Divider(),

            Header(text=string.Settings_CardSettings_Header),
            Selector(
                key="background",
                text=string.Settings_BackgroundMode_Text,
                default=1,
                items=[
                    string.Settings_BackgroundMode_Item_1,
                    string.Settings_BackgroundMode_Item_2
                ],
                icon="msg_photos"
            ),
            Selector(
                key="font",
                text=string.Settings_Font_Text,
                default=0,
                items=[
                    string.Settings_Font_Item1,
                    string.Settings_Font_Item2,
                    string.Settings_Font_Item3
                ],
                icon="msg_photo_text_regular"
            ),
            Selector(
                key="yandex_link",
                text=string.Settings_YandexLink_Text,
                default=1,
                items=[
                    string.Settings_YandexLink_Item_1,
                    string.Settings_YandexLink_Item_2,
                    string.Settings_YandexLink_Item_3
                ],
                icon="msg_link2"
            ),
            Switch(
                key="songlink_link_include",
                text=string.Settings_SongLinkInclude_Text,
                default=True,
                subtext=string.Settings_SongLinkInclude_Subtext,
                on_change=lambda new_value: self.LogCBO(),
                icon="msg_language"
            ),
            Switch(
                key="fast_card_render",
                text=string.Settings_FastCardRender_Text,
                default=False,
                subtext=string.Settings_FastCardRender_Subtext,
                on_change=lambda new_value: self.autocard_render(new_value),
                icon="boosts_solar"
            ),

            Divider(),

            Switch(
                key="advanced_mode",
                text=string.Setting_AdvancedMode_Text,
                default=False,
                subtext=string.Setting_AdvancedMode_Subtext,
                on_change=None,
                icon="msg_palette"
            ),
            Text(
                text=string.Setting_AdvancedMode_Title,
                icon="msg_download_settings",
                create_sub_fragment=self.create_customization_settings
            ) if advanced else None,

            Divider(),

            Header(text=string.Settings_Stream_Header),
            Switch(
                key="update_bio",
                text=string.Settings_Stream_Text,
                default=False,
                subtext=string.Settings_Stream_Subtext,
                on_change=lambda new_value: self._unactive(new_value),
                icon="msg_online"
            ),
            Text(
                text=string.Setting_Stream_Title,
                icon="msg_download_settings",
                create_sub_fragment=self.create_stream_settings
            ) if update_bio else None,

            Header(text=string.Settings_Other_Header),
            Text(
                text=string.Setting_Other_SourceCheck,
                icon="msg_noise_on",
                on_click=lambda view: self.source_check()
            ),
            Text(
                text=string.Setting_Other_ForceDownload,
                icon="msg_download",
                on_click=lambda view: self.force_source_downloader()
            ),
            Text(
                text=string.Setting_Other_Donate,
                icon="msg_ton",
                accent=True,
                on_click=lambda view: self.show_my_info_alert(
                    title=string.Alert_Donate_Title,
                    message=string.Alert_Donate_Text,
                    neutral_button=string.Alert_Donate_Button,
                    neutral_link="UQBVxjueXqAEpALX_b0yr-ytXN26LOTpSBn26b9VRHKrmm5F",
                    neutral_type="copy"
                )
            )
        ]
        return settings

    def create_customization_settings(self):
        instant_card = self.get_setting("fast_card_render", False)
        return [
            Input(
                key="background_color",
                text=string.Settings_BackgroundColor_Text,
                default=DEFAULT_COLOR["background_color"],
                subtext=string.Settings_BackgroundColor_Subtext,
                icon="menu_feature_custombg",
                on_change=lambda new_value: self.HEX_check(new_value, "background_color")
            ) if self.get_setting("background", 1) == 1 else None,
            Input(
                key="title_text_color",
                text=string.Settings_AccentColor_Text,
                default=DEFAULT_COLOR["title_text_color"],
                subtext=string.Settings_AccentColor_Subtext,
                icon="msg_photo_text_framed",
                on_change=lambda new_value: self.HEX_check(new_value, "title_text_color")
            ),
            Input(
                key="subtext_color",
                text=string.Settings_SecondaryColor_Text,
                default=DEFAULT_COLOR["subtext_color"],
                subtext=string.Settings_SecondaryColor_Subtext,
                icon="msg_photo_text_framed2",
                on_change=lambda new_value: self.HEX_check(new_value, "subtext_color")
            ),
            Input(
                key="instant_subtext",
                text=string.Settings_InstantCardSubtext_Text,
                default=INSTANT_SUBTEXT,
                subtext=string.Settings_InstantCardSubtext_Subtext,
                icon="menu_feature_intro"
            ) if instant_card else None,
            Input(
                key="instant_main_text",
                text=string.Settings_InstantCardMainText_Text,
                default=INSTANT_MAIN_TEXT,
                subtext=string.Settings_InstantCardMainText_Subtext,
                icon="menu_feature_cover"
            ) if instant_card else None,
        ]

    def create_stream_settings(self):
        update_bio = self.get_setting("update_bio", False)
        return [
            Selector(
                key="stream_place",
                text=string.Setting_TrackStream_Text,
                default=0,
                items=[
                    string.Setting_TrackStream_Item1,
                    string.Setting_TrackStream_Item2,
                ],
                icon="menu_premium_location" if self.get_setting("stream_place", 0) else "msg_openprofile"
            ) if get_user_config().isPremium() and update_bio else None,
            Input(
                key="default_stream_text",
                text=string.Settings_InStream_Text,
                default=DEFAULT_STREAM_TEXT,
                subtext=string.Settings_InStream_Subtext,
                icon="msg_photo_text_framed3"
            ) if update_bio else None,
            Input(
                key="track_display_format",
                text=string.Settings_FormatInStream_Text,
                default=DEFAULT_STREAM_STRING,
                subtext=string.Settings_FormatInStream_Subtext,
                icon="msg_view_file"
            ) if update_bio else None,
        ]

    class Progress():
        def __init__(self):
            self.progress_dialog = AlertDialog(get_last_fragment().getParentActivity(), 3)

        def show(self):
            if self.progress_dialog is None:
                self.progress_dialog = AlertDialog(get_last_fragment().getParentActivity(), 3)
            self.progress_dialog.show()

        def dismiss(self):
            if self.progress_dialog is not None and self.progress_dialog.isShowing():
                try:
                    self.progress_dialog.dismiss()
                except Exception:
                    pass
                finally:
                    self.progress_dialog = None

    def copy(self, value):
        if AndroidUtilities.addToClipboard(value):
            BulletinHelper.show_copied_to_clipboard()

    def show_my_info_alert(self, title="TITLE", message="MESSAGE", positive_button="OK", neutral_button=None,
                           neutral_link=None, neutral_type=None):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        builder = AlertDialogBuilder(ctx, AlertDialogBuilder.ALERT_TYPE_MESSAGE)
        builder.set_title(title)
        builder.set_message(message)
        builder.set_positive_button(positive_button, self._dismiss_dialog(self.alert_builder_instance))
        if neutral_button:
            builder.set_neutral_button(neutral_button, lambda builder, which: self._open_link(
                neutral_link) if neutral_type == "link" else self.copy(neutral_link))
        self.alert_builder_instance = builder.show()

    def show_my_loading_alert(self, title="Дайте денег пожалуйста"):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        builder = AlertDialog(ctx, AlertDialogBuilder.ALERT_TYPE_LOADING)
        builder.setTitle(title)
        builder.setCancelable(False)
        builder.setCanceledOnTouchOutside(False)
        self.alert_builder_instance = builder
        self.alert_builder_instance.show()
        self.alert_builder_instance.setProgress(0)

    def _update_dialog_progress(self, builder_instance: AlertDialogBuilder, progress: int):
        if builder_instance and builder_instance.isShowing():
            builder_instance.setProgress(progress)

    def _dismiss_dialog(self, builder_instance: AlertDialogBuilder):
        def action():
            if builder_instance is not None:
                try:
                    dlg = builder_instance.getDialog() if hasattr(builder_instance, 'getDialog') else builder_instance
                    if dlg and dlg.isShowing():
                        dlg.dismiss()
                except Exception:
                    pass
                finally:
                    self.alert_builder_instance = None

        run_on_ui_thread(action)

    def _open_link(self, url):
        fragment = get_last_fragment()
        ctx = fragment.getContext() if fragment else ApplicationLoader.applicationContext
        intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
        run_on_ui_thread(lambda: ctx.startActivity(intent))

    def LogCBO(self):
        log("[reMusic] Test CBO log")

    def HEX_check(self, new, variable):
        if not re.match("^#[A-Fa-f0-9]{6}$", new):
            run_on_ui_thread(lambda: self.show_my_info_alert(
                title=string.Alert_HEX_Title,
                message=string.Alert_HEX_Text
            ))
            self.set_setting(variable, DEFAULT_COLOR[variable])

    def update_yandex_object(self, token):
        self.yandex = YandexMusic(token)

    def source_check(self):
        self.show_my_loading_alert(
            title=string.Alert_SourceCheck_Title
        )
        plugin_dir = [file.getName() for file in self._temp_dir.listFiles()]
        if len(plugin_dir) == 0:
            self.show_my_info_alert(title=string.Alert_SourceError_Title,
                                    message=string.Alert_SourceError_FontsNotFound)
            return
        for font in FONTS.values():
            for font_type in ["Regular", "Bold"]:
                nigger = 100 // (len(FONTS.values()) * 2)
                if f"{font}-{font_type}.ttf" not in plugin_dir:
                    self._dismiss_dialog(self.alert_builder_instance)
                    self.show_my_info_alert(title=string.Alert_SourceError_Title,
                                            message=string.Alert_SourceError_FontNotFound.format(
                                                f"{font}-{font_type}.ttf"))
                    return
                try:
                    test_img = Image.new("RGBA", (100, 100), (0, 0, 0, 0))
                    img_font = ImageFont.truetype(File(self._temp_dir, f"{font}-{font_type}.ttf").getAbsolutePath(), 42)
                    draw = ImageDraw.Draw(test_img)
                    draw.text((0, 0), "reyandex", font=img_font, fill=(255, 255, 255))
                    test_img = None
                except Exception as e:
                    self._dismiss_dialog(self.alert_builder_instance)
                    self.show_my_info_alert(title=string.Alert_SourceError_Title,
                                            message=string.Alert_SourceError_FontApplyError.format(
                                                f"{font}-{font_type}.ttf", e))
                    return
                niggers_counter = nigger + (nigger * (list(FONTS.values()).index(font))) * 2 + (
                    nigger if font_type == "Bold" else 0)
                run_on_ui_thread(lambda: self._update_dialog_progress(self.alert_builder_instance, niggers_counter))
        self.show_my_info_alert(title=string.Alert_SourceSuccess_Title, message=string.Alert_SourceSuccess_Text)

    def force_source_downloader(self):
        threading.Thread(target=self._source_downloader, args=(self._temp_dir, True,)).start()

    def _unactive(self, value=None):
        setting_value = False
        if value:
            run_on_ui_thread(lambda: self.show_my_info_alert(
                title=string.Settings_StreamAlert_Title,
                message=string.Settings_StreamAlert_Text,
            ))
            setting_value = value
        elif value is None:
            setting_value = self.get_setting("update_bio", False)
        if setting_value:
            userFull = get_messages_controller().getUserFull(get_user_config().getClientUserId())
            stream_place = self.get_setting("stream_place", 0)
            default_bio = self.get_setting("default_stream_text", DEFAULT_STREAM_TEXT)
            if default_bio == DEFAULT_STREAM_TEXT:
                user_bio = userFull.about
                if user_bio:
                    default_bio = user_bio
                    self.set_setting("default_stream_text", default_bio)
            if stream_place == 0:
                max_len = 140 if get_user_config().isPremium() else 70
                if userFull.about != default_bio[:max_len]:
                    req = TL_account.updateProfile()
                    req.flags = 4
                    req.about = default_bio[:max_len]

                    send_request(req, ())
            elif stream_place == 1:
                if userFull.business_location.address != default_bio[:96]:
                    req = TL_account.updateBusinessLocation()
                    req.address = default_bio[:96]
                    req.flags = 1

                    send_request(req, ())

    def autocard_render(self, value):
        if value:
            threading.Thread(target=self._autocard_render).start()

    def autocard_render_after_change(self):
        if not self.get_setting("fast_card_render", False): return
        self.yandex.memory_id = "Default"
        threading.Thread(target=self._autocard_render).start()

    def _autocard_render(self):
        while True:
            if self.yandex.memory_id != self.yandex.now_track.id:
                try:
                    self.yandex.memory_id = self.yandex.now_track.id
                    self._make_card()
                except Exception as e:
                    show_with_copy("An error has occurred", e)
            time.sleep(1)

    def _streamer(self):
        while True:
            try:
                if "now_track" not in dir(self.yandex):
                    time.sleep(1)
                if self.get_setting("update_bio", False):
                    userFull = get_messages_controller().getUserFull(get_user_config().getClientUserId())
                    track = self.yandex.now_track
                    stream_place = self.get_setting("stream_place", 0)

                    if userFull:
                        max_len = 140 if get_user_config().isPremium() else 70

                        if track.active:
                            new_about_text = self.get_setting("track_display_format", DEFAULT_STREAM_STRING)
                            new_about_text = new_about_text.replace("{title}", track.title)
                            new_about_text = new_about_text.replace("{artists}", ", ".join(track.artist))
                            if stream_place == 0:
                                if userFull.about != new_about_text[:max_len]:
                                    try:
                                        req = TL_account.updateProfile()
                                        req.flags = 4
                                        req.about = new_about_text[:max_len]
                                        send_request(req, ())
                                    except:
                                        time.sleep(5)
                            elif stream_place == 1:
                                if userFull.business_location.address != new_about_text[:96]:
                                    try:
                                        req = TL_account.updateBusinessLocation()
                                        req.address = new_about_text[:96]
                                        req.flags = 1
                                        send_request(req, ())
                                    except:
                                        time.sleep(5)
                            else:
                                pass

                        else:
                            default_bio = self.get_setting("default_stream_text", DEFAULT_STREAM_TEXT)

                            if stream_place == 0:
                                if userFull.about != default_bio[:max_len]:
                                    try:
                                        req = TL_account.updateProfile()
                                        req.flags = 4
                                        req.about = default_bio[:max_len]
                                        send_request(req, ())
                                    except:
                                        time.sleep(5)
                            elif stream_place == 1:
                                if userFull.business_location.address != default_bio[:96]:
                                    try:
                                        req = TL_account.updateBusinessLocation()
                                        req.address = default_bio[:96]
                                        req.flags = 1
                                        send_request(req, ())
                                    except:
                                        time.sleep(5)

                    time.sleep(5 if stream_place else 30)
            except Exception as e:
                log(f"reMusic Bio Error: {e}")
                time.sleep(10)

    def _source_downloader(self, temp_dir, force_load=False):

        try:
            plugin_dir = [file.getName() for file in temp_dir.listFiles()]
            not_need_download = any([font.endswith(".ttf") for font in plugin_dir])
            if not_need_download and not force_load:
                return
            log("[reMusic] Downloading fonts...")
            run_on_ui_thread(lambda: self.show_my_loading_alert(
                string.Alert_SourceDownload_Title
            ))
            nigger = 100 // (len(FONTS.values()) * 2)

            for font in FONTS.values():
                for font_type in ["Regular", "Bold"]:
                    if f"{font}-{font_type}.ttf" not in plugin_dir or force_load:
                        with open(File(temp_dir, f"{font}-{font_type}.ttf").getAbsolutePath(), "wb") as f:
                            f.write(requests.get(
                                f"https://github.com/itsNightly/font_link/raw/refs/heads/main/{font}-{font_type}.ttf")
                                    .content)

                        niggers_counter = nigger + (nigger * (list(FONTS.values()).index(font))) * 2 + (
                            nigger if font_type == "Bold" else 0)
                        run_on_ui_thread(
                            lambda: self._update_dialog_progress(self.alert_builder_instance, niggers_counter))

            run_on_ui_thread(lambda: self._dismiss_dialog(self.alert_builder_instance))

        except Exception as e:

            run_on_ui_thread(lambda: self._dismiss_dialog(self.alert_builder_instance))
            show_with_copy(string.Bulletin_ErrorMessageCopy, e)
            log(f"Error downloading font: {e}")

    def _get_temp_dir(self):
        base_dir = ApplicationLoader.getFilesDirFixed()
        File(base_dir, "reMusic")
        try:
            base_dir = ApplicationLoader.getFilesDirFixed()
            if not base_dir:
                return None
            temp_dir = File(base_dir, "reMusic")
            if not temp_dir.exists() and not temp_dir.mkdirs():
                return None
            threading.Thread(target=self._source_downloader, args=(temp_dir,)).start()
            return temp_dir
        except Exception as e:
            show_with_copy(string.Bulletin_ErrorMessageCopy, e)
            log(f"Error getting/creating temp directory: {e}")
            return None

    def _make_card(self):
        track = self.yandex.now_track
        font_family = self.get_setting("font", 0)
        font_family = FONTS[font_family]
        width, height = 1440, 600

        advanced_mode = self.get_setting("advanced_mode", False)
        if not advanced_mode:
            background_color = DEFAULT_COLOR["background_color"]
            title_text_color = DEFAULT_COLOR["title_text_color"]
            subtext_color = DEFAULT_COLOR["subtext_color"]
        else:
            background_color = self.get_setting("background_color", DEFAULT_COLOR["background_color"])
            title_text_color = self.get_setting("title_text_color", DEFAULT_COLOR["title_text_color"])
            subtext_color = self.get_setting("subtext_color", DEFAULT_COLOR["subtext_color"])

        if not track.active:
            if string._lang == 'ru' and font_family == "Circular":
                font_family = "Onest"
            card = Image.new('RGB', (width, height), background_color)
            draw = ImageDraw.Draw(card)
            reMusicFont = ImageFont.truetype(File(self._temp_dir, f"{font_family}-Regular.ttf").getAbsolutePath(), 40)
            notActiveFont = ImageFont.truetype(File(self._temp_dir, f"{font_family}-Bold.ttf").getAbsolutePath(), 80)
            draw.text((width // 2, 45), "reMusic", font=reMusicFont, fill=title_text_color, align="center",
                      anchor="mm")
            draw.text((width // 2, height // 2), string.Card_PlayerInactive, font=notActiveFont, fill=title_text_color,
                      align="center", anchor="mm")
            filename = f"now_reMusic.png"
            temp_photo_path = File(self._temp_dir, filename).getAbsolutePath()
            card.save(temp_photo_path)
            return temp_photo_path
        background_setting = self.get_setting("background", 1)
        thumb = requests.get(track.thumb, stream=True).raw
        background = Image.open(thumb)
        thumbnail = background.copy()
        if background_setting == 0:
            background = background.resize((width, width)).crop((0, (width - height) // 2, width, width)).filter(
                ImageFilter.GaussianBlur(radius=14))
            background = ImageEnhance.Brightness(background).enhance(0.3)
            card = Image.new('RGB', (width, height), background_color)
            card.paste(background, (0, 0))
        elif not advanced_mode:
            img = background.resize((16, 16), Image.LANCZOS)
            pixels = img.load()
            lWidth, lHeight = img.size
            for y in range(lHeight):
                for x in range(lWidth):
                    if img.mode == 'L':
                        r = pixels[x, y]
                        r = math.pow(r / 255.0, 1 / 2.2) * 255.0
                        pixels[x, y] = int(r)
                    else:
                        r, g, b = pixels[x, y][:3]
                        r = math.pow(r / 255.0, 1 / 2.2) * 255.0
                        g = math.pow(g / 255.0, 1 / 2.2) * 255.0
                        b = math.pow(b / 255.0, 1 / 2.2) * 255.0
                        if img.mode == 'RGB':
                            pixels[x, y] = (int(r), int(g), int(b))
                        elif img.mode == 'RGBA':
                            a = pixels[x, y][3]
                            pixels[x, y] = (int(r), int(g), int(b), a)
            if img.mode == 'L':
                img = img.convert('RGB')
            elif img.mode == 'RGBA':
                rgb_img = Image.new('RGB', img.size)
                rgb_img.paste(img, mask=img.split()[3])
                img = rgb_img

            pixels = list(img.getdata())
            iWidth, iHeight = img.size

            if img.mode == 'RGB':
                total_r, total_g, total_b = 0, 0, 0
                darkness_index = 1.9
                for r, g, b in pixels:
                    total_r += int(r // darkness_index)
                    total_g += int(g // darkness_index)
                    total_b += int(b // darkness_index)
                count = iWidth * iHeight
                average = (total_r // count, total_g // count, total_b // count)
            else:
                total = sum(pixels)
                average = (total // (iWidth * iHeight),) * 3
            card = Image.new('RGB', (width, height), average)
        else:
            card = Image.new('RGB', (width, height), background_color)

        thumbnail = thumbnail.resize((450, 450))
        mask = Image.new('L', thumbnail.size, 0)
        draw = ImageDraw.Draw(mask)
        draw.rounded_rectangle((0, 0, *thumbnail.size), 30, fill=255)
        thumbnail = thumbnail.copy()
        thumbnail.putalpha(mask)
        card.paste(thumbnail, (75, 75), thumbnail)
        draw = ImageDraw.Draw(card)
        local_font_family = None
        if re.findall(r"[А-Яа-яЁё]", track.title) and font_family == "Circular":
            local_font_family = "Onest"
        titleFont = ImageFont.truetype(File(self._temp_dir,
                                            f"{local_font_family if local_font_family else font_family}-Bold.ttf").getAbsolutePath(),
                                       60)
        x, y = 590, 85
        artistsPlusY = 0
        lines = textwrap.wrap(track.title, width=21)
        if len(lines) > 1:
            lines[1] = lines[1] + "..." if len(lines) > 2 else lines[1]
            artistsPlusY = 70
        else:
            pass
        lines = lines[:2]
        for line in lines:
            draw.text((x, y), line, font=titleFont, fill=title_text_color)
            y += 70
        local_font_family = None
        if re.findall(r"[А-Яа-яЁё]", "".join(track.title)) and font_family == "Circular":
            local_font_family = "Onest"
        artistFont = ImageFont.truetype(File(self._temp_dir,
                                             f"{local_font_family if local_font_family else font_family}-Regular.ttf").getAbsolutePath(),
                                        40)
        artists = textwrap.wrap(" • ".join(track.artist), width=32)
        if len(artists) > 1:
            if "•" in artists[0][-2:]:
                artists[0] = artists[0][:artists[0].rfind("•") - 1]
            artists[0] = artists[0]
        artists = artists[0]
        draw.text((590, 170 + artistsPlusY), artists, subtext_color, font=artistFont)
        if not (self.get_setting("fast_card_render", False)):
            progressBarEmpty = Image.new('RGBA', (width - 665, 10), (0, 0, 0, 0))
            progressDraw = ImageDraw.Draw(progressBarEmpty)
            progressDraw.rounded_rectangle((0, 0, *progressBarEmpty.size), 7, fill=subtext_color)
            progressDraw.rounded_rectangle((0, 0, progressBarEmpty.width * (track.progress / track.duration), 10), 7,
                                           fill=title_text_color)
            card.paste(progressBarEmpty, (590, 460), progressBarEmpty)
            timersFont = ImageFont.truetype(File(self._temp_dir, f"{font_family}-Regular.ttf").getAbsolutePath(), 30)
            draw.text((590, 490), f"{datetime.datetime.fromtimestamp(track.progress).strftime('%M:%S')}", subtext_color,
                      font=timersFont, anchor="la")
            draw.text((1365, 490), f"{datetime.datetime.fromtimestamp(track.duration).strftime('%M:%S')}",
                      subtext_color, font=timersFont, anchor="ra")
        else:
            local_font_family = None

            if advanced_mode:
                subtext = self.get_setting("instant_subtext", "powered by")
                maintext = self.get_setting("instant_main_text", "reMusic")
            else:
                subtext = "powered by"
                maintext = "reMusic"

            subtext = subtext[:26] + "..." if len(subtext) > 26 else subtext
            maintext = maintext[:21] + "..." if len(maintext) > 21 else maintext

            ru_flag_subtext = True if re.findall(r"[А-Яа-яЁё]", subtext) else False
            ru_flag_maintext = True if re.findall(r"[А-Яа-яЁё]", maintext) else False

            if ru_flag_subtext and font_family == "Circular":
                local_font_family = "Onest"

            infoFont = ImageFont.truetype(File(self._temp_dir,
                                               f"{local_font_family if local_font_family else font_family}-Regular.ttf").getAbsolutePath(),
                                          42)
            local_font_family = None

            if ru_flag_maintext and font_family == "Circular":
                local_font_family = "Onest"

            deviceFont = ImageFont.truetype(File(self._temp_dir,
                                                 f"{local_font_family if local_font_family else font_family}-Bold.ttf").getAbsolutePath(),
                                            52)
            draw.text((590, 415), subtext, subtext_color, font=infoFont, anchor="ls")
            draw.text((590, 485), maintext, title_text_color, font=deviceFont, anchor="ls")

        filename = f"now_reMusic.png"
        temp_photo_path = File(self._temp_dir, filename).getAbsolutePath()
        card.save(temp_photo_path)
        return temp_photo_path

    def _delete_file_delayed(self, path, delay=60):
        def action():
            try:
                time.sleep(delay)
                if os.path.exists(path):
                    os.remove(path)
                    log(f"Deleted temp file: {path}")
            except Exception as e:
                log(f"Delayed delete error: {e}")

        threading.Thread(target=action, daemon=True).start()

    def image_processor(self, msg_params):
        params = {
            "message": None,
            "peer": msg_params.peer
        }
        track = self.yandex.now_track
        try:
            answer = self._make_card() if not (self.get_setting("fast_card_render", False)) else True
            if answer:
                temp_file_path = File(self._temp_dir, "now_reMusic.png").getAbsolutePath()

            send_helper = get_send_messages_helper()
            generated_photo = send_helper.generatePhotoSizes(temp_file_path, None)

            if not generated_photo:
                self.progress.dismiss()
                show_with_copy(string.Bulletin_ErrorMessageCopy, string.Bulletin_FailedProcessImage)
                return HookResult(strategy=HookStrategy.CANCEL)

            params["photo"] = generated_photo
            params["path"] = temp_file_path
            params["replyToMsg"] = msg_params.replyToMsg
            params["replyToTopMsg"] = msg_params.replyToTopMsg
            if track.active:
                yandex_link = self.get_setting("yandex_link", 1)
                songlink = self.get_setting("songlink_link_include", True)
                caption = None
                if any([yandex_link != 0, songlink]):
                    caption = random.choice(["[🎵](5188621441926438751) | ", "[🎶](5188705588925702510) | "])
                    if all([yandex_link != 0, songlink]):
                        link = track.link if (yandex_link == 1 or track == None) else track.album
                        caption += string.Message_CaptionLink_Text.format(
                            link) + string.Message_CaptionDivider + string.Message_CaptionSongLink_Text.format(
                            f"https://song.link/ya/{track.id}")
                    else:
                        if yandex_link:
                            link = track.link if (yandex_link == 1 or track == None) else track.album
                            caption += string.Message_CaptionLink_Text.format(link)
                        elif songlink != 0:
                            caption += string.Message_CaptionSongLink_Text.format(f"https://song.link/ya/{track.id}")
                    caption = parse_markdown(caption)
                    params["caption"] = caption.text
                    params["entities"] = set()
                    for i in caption.entities:
                        params["entities"].add(i.to_tlrpc_object())
            else:
                params["caption"] = string.Message_PlayerNotActive

        except Exception as e:
            params["message"] = string.Bulletin_ErrorMessage.format(e)
            log(f"Internal reMusic error: {e}")
            show_with_copy(string.Bulletin_ErrorMessageCopy, e)

        self.progress.dismiss()
        send_message(params)

    def _process_audio(self, dialog_id, replyToMsg, replyToTopMsg):
        track = getattr(self.yandex, 'now_track', None)
        if not track or not track.active:
            self.progress.dismiss()
            BulletinHelper.show_error(
                "Сейчас трек не играет" if Locale.getDefault().getLanguage() == 'ru' else "No track playing"
            )
            return

        url = track.download_url
        if not url:
            self.progress.dismiss()
            BulletinHelper.show_error("No download link")
            return

        try:
            ext = os.path.splitext(url)[1] or ".mp3"
            filename = f"{track.title}{ext}"
            file_path = File(self._temp_dir, filename).getAbsolutePath()
            resp = requests.get(url, stream=True, timeout=60)
            resp.raise_for_status()

            with open(file_path, 'wb') as f:
                for chunk in resp.iter_content(8192): f.write(chunk)

            ext_root = ApplicationLoader.applicationContext.getExternalCacheDir()
            plugin_dir = File(ext_root, TEMP_DIR_NAME)

            if not plugin_dir.exists() and not plugin_dir.mkdirs():
                pass

            ext_path = File(plugin_dir, File(file_path).getName()).getAbsolutePath()

            with open(file_path, 'rb') as fin, open(ext_path, 'wb') as fout:
                fout.write(fin.read())

            account = get_account_instance()
            mime = "audio/mpeg"
            SendMessagesHelper.prepareSendingDocument(account, ext_path, ext_path, None,
                                                      f"{track.title} — {', '.join(track.artist)}", mime, dialog_id,
                                                      replyToMsg, replyToTopMsg, None, None, None, True, 0, None, None,
                                                      0, False)

            self.progress.dismiss()
            self._delete_file_delayed(file_path)
            self._delete_file_delayed(ext_path)
        except Exception as e:
            self.progress.dismiss()
            show_with_copy("An error has occurred", e)

    def on_send_message_hook(self, account, params):

        commands = SPECIAL_COMMANDS if self.re_family else DEFAULT_COMMANDS
        if hasattr(params, 'message') and isinstance(params.message, str):
            if params.message in commands["pic"]:
                self.progress.show()
                threading.Thread(target=self.image_processor, args=(params,)).start()
                return HookResult(strategy=HookStrategy.CANCEL)

            if params.message in commands["track"]:
                self.progress.show()
                try:
                    if not self.yandex.now_track.active:
                        self.progress.dismiss()
                        params.message = string.Message_PlayerNotActive
                        return HookResult(strategy=HookStrategy.MODIFY, params=params)
                    thread = threading.Thread(target=self._process_audio,
                                              args=(params.peer, params.replyToMsg, params.replyToTopMsg), daemon=True)
                    thread.start()
                    return HookResult(strategy=HookStrategy.CANCEL)
                except Exception as e:
                    self.progress.dismiss()
                    params.message = string.Bulletin_ErrorMessage.format(e)
                    log(f"Internal reMusic error: {e}")
                    show_with_copy(string.Bulletin_ErrorMessageCopy, e)

        elif hasattr(params, 'caption') and isinstance(params.caption, str):

            if "reMusic_flag_metadata" in params.caption:

                if self.for_metadata:
                    track = self.for_metadata
                    self.for_metadata = None
                else:
                    track = self.yandex.now_track

                for i in range(params.document.attributes.__str__().split(",").__len__()):
                    if "title" in dir(params.document.attributes.get(i)):
                        params.document.attributes.get(i).title = track.title if track.active else "[reMusic] ERROR"
                        params.document.attributes.get(i).performer = ", ".join(
                            track.artist) if track.active else string.Message_PlayerNotActive
                        params.document.attributes.get(i).duration = track.duration if track.active else 0
                markdown = "reMusic_flag_markdown" in params.caption
                if "reMusic_flag_metadata" in params.caption:
                    params.caption = params.caption.replace(" reMusic_flag_metadata", "")
                if markdown:
                    params.caption = params.caption.replace(" reMusic_flag_markdown", "")
                    caption = parse_markdown(params.caption)
                    params.caption = caption.text
                    params.entities = ArrayList()
                    for i in caption.entities:
                        params.entities.add(i.to_tlrpc_object())
                return HookResult(HookStrategy.MODIFY, params=params)
            return HookResult()
