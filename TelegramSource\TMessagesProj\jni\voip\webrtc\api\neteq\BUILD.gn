# Copyright (c) 2019 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../webrtc.gni")

rtc_source_set("neteq_api") {
  visibility = [ "*" ]
  sources = [
    "neteq.cc",
    "neteq.h",
    "neteq_factory.h",
  ]

  deps = [
    "..:rtp_headers",
    "..:rtp_packet_info",
    "..:scoped_refptr",
    "../../rtc_base:stringutils",
    "../../system_wrappers:system_wrappers",
    "../audio_codecs:audio_codecs_api",
  ]
  absl_deps = [ "//third_party/abseil-cpp/absl/types:optional" ]
}

rtc_source_set("custom_neteq_factory") {
  visibility = [ "*" ]
  sources = [
    "custom_neteq_factory.cc",
    "custom_neteq_factory.h",
  ]

  deps = [
    ":neteq_api",
    ":neteq_controller_api",
    "..:scoped_refptr",
    "../../modules/audio_coding:neteq",
    "../../system_wrappers:system_wrappers",
    "../audio_codecs:audio_codecs_api",
  ]
}

rtc_source_set("neteq_controller_api") {
  visibility = [ "*" ]
  sources = [
    "neteq_controller.h",
    "neteq_controller_factory.h",
  ]

  deps = [
    ":neteq_api",
    ":tick_timer",
    "../../system_wrappers:system_wrappers",
  ]
  absl_deps = [ "//third_party/abseil-cpp/absl/types:optional" ]
}

rtc_source_set("default_neteq_controller_factory") {
  visibility = [ "*" ]
  sources = [
    "default_neteq_controller_factory.cc",
    "default_neteq_controller_factory.h",
  ]

  deps = [
    ":neteq_controller_api",
    "../../modules/audio_coding:neteq",
  ]
}

rtc_source_set("tick_timer") {
  visibility = [ "*" ]
  sources = [
    "tick_timer.cc",
    "tick_timer.h",
  ]
  deps = [
    "../../rtc_base:checks",
  ]
}

rtc_source_set("tick_timer_unittest") {
  visibility = [ "*" ]
  testonly = true
  sources = [ "tick_timer_unittest.cc" ]
  deps = [
    ":tick_timer",
    "../../test:test_support",
    "//testing/gtest",
  ]
}
