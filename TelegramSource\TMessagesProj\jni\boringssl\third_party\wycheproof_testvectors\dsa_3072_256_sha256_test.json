{"algorithm": "DSA", "generatorVersion": "0.8rc18", "numberOfTests": 358, "header": ["Test vectors of test DsaVerify are intended for checking the signature", "verification of DSA signatures."], "notes": {"EdgeCase": "Some implementations of DSA do not properly check for boundaries. In some cases the modular inverse of 0 is simply 0. As a result there are implementations where values such as r=1, s=0 lead to forgeries.", "NoLeadingZero": "ASN encoded integers with a leading hex-digit in the range 8 .. F are negative. If the first hex-digit of a positive integer is 8 .. F then a leading 0 must be added. Some libraries forgot to do this an therefore generated invalid DSA signatures. Some providers, accept such legacy signatures for compatibility."}, "schema": "dsa_verify_schema.json", "testGroups": [{"key": {"g": "6dea4b8c3fe3ab91e3229fb14c1cfa822915769af161405f48b7fadfe1ec5d9fec4ef0cfbb2233ffddfa5a554cfc68c6bc6a0ba30cef6f51309294e622b58d4face00ae9669d9172b15696839ed332afd906e3f427d85a9af73562b845be53a3713c0219402a4c208e9b6a6873235e0bc20442e70ab69edd46e8f3f7d58cb35ea3690c673f54cd37377725739f00ebe2b3b53bdaf89ddac74012f8486bd3f5217579b4a303f61bccc98931faba969c8c2a27acb04bc21201edf9a7f6b42e10f75dd23c3ab073d7290d173ebe6cb1919607bfe2bf0d829a609d8d3cda7044ff8dfbbd463e68c9403a45834ec547a7d4fd5abc68c5997cdc397120698f879356e0e74b62fe1a2938a5d1b486b53a5e0cb875e23a2e834ea563a4a9d4be44045877df020c30e22e55603f63d74ed2cafde18180ec294a7ce263d56eb280562687f61f898f3c7d2b37d7f00250a43ca989de16fa1aab7d83e0dbf6aa66edc36ad79eecfe2f91cfab6285ba10ae713126f69326540c461e44e45bdf076e4ed8d3e924", "keySize": 3072, "p": "00dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1", "q": "00f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037", "type": "DsaPublicKey", "y": "7e0062e6f32ecab1effa38391b71a523221d6e97a61f55238f0a623cc42980b987fb22ec6138e8d1c0d36c05d059ca0ca3f1a526a5f67b216341abcd04105bb8efc9e479c2532f9dea6cddfb4f57de5b9d6964e3d314eb89693a3d57f82b9ff93e0e0d11d72fa4f1bd82bb2b20f1b59547aff7711db319d7d06e6964beb294e44d34c2a21c7ac7cdac5e91f2f6d183042afc3644b09837fa2225a074ceb65d499f73cee04c705c82bb912f97d765d5f9c8cb442019e7dac1e1ccccee990335ea3b8c837583595cd4f83169d4787fe4675386d604e8e205b977c7ab236950428254e3b836bd00296257238d22bda16a722e405df82029e3384931fb0e4903c3f8771fb15708d4cb3238e7b2a68131be518a08d6efd483a01537a432046dcbd1ffa5ff831e0257b292012d5e1a44c6e32019a6b3ae176a67edaf12eb27e68fa60a05af4e5448d606c392b4a672b44298b1775a16b9440b131eb0d91ca3fde1a1e528b5fffc31ffdf1449169c2f4abd96809a75fb6c85ae845940c45d5af8334057"}, "keyDer": "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", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIIExjCCAzkGByqGSM44BAEwggMsAoIBgQDc0vcfunrrRq7qhYq3byEC+pepU6vO\nnXkaomnwFhczrD3yX1yds0SPgoRuNV4jCJYUBG1CsDApjZT1Nl2ULLtUkOQKHV5u\nV3zGRqgH8Emh+0K5ep5k76Gqnvk7s8cSDdv5xAPlgEMfF4kSfwpk6nsDbvEtB/Ah\nA2VdY93aPEStMo9yfB0GD8kuNhaXbPEb8f7v/wM0kNmJKSUrWFzZLAga/McdrmNB\nro3QXmKuKXrSsAVg7JTx9kSCgW468FL8Ha/wqb9SA0ASWU1CRgNtBA+l50Hmk+Nr\nBkvtsiTqH3xshhccqPz6yYxdtuNNrTB8W/3s5OV48OGPyq7p1bMw7WmnLY/d+Hil\nileRQkeCWubtHLimskHqaUt3+EPu5A8b6Q8msmFUgTZH0eGvASVM8hz90unrp+Qx\nvY22Fk0Fo9Ouk3GvXQ05o6m58HumEjPHemv8JzUV+4RNuPr9abVZzoRMej1obqSZ\nHZ/nTK1WBInzwdu0/RceqK54dOMCIHwCp7ECIQD4cNNcqfhOasrYCNasNbE+5Ac/\nJuqE/v4IxNmlZXVANwKCAYBt6kuMP+OrkeMin7FMHPqCKRV2mvFhQF9It/rf4exd\nn+xO8M+7IjP/3fpaVUz8aMa8agujDO9vUTCSlOYitY1PrOAK6WadkXKxVpaDntMy\nr9kG4/Qn2Fqa9zViuEW+U6NxPAIZQCpMII6bamhzI14LwgRC5wq2nt1G6PP31Yyz\nXqNpDGc/VM03N3clc58A6+KztTva+J3ax0AS+Ehr0/UhdXm0owP2G8zJiTH6upac\njConrLBLwhIB7fmn9rQuEPdd0jw6sHPXKQ0XPr5ssZGWB7/ivw2CmmCdjTzacET/\njfu9Rj5oyUA6RYNOxUen1P1avGjFmXzcOXEgaY+Hk1bg50ti/hopOKXRtIa1Ol4M\nuHXiOi6DTqVjpKnUvkQEWHffAgww4i5VYD9j107Syv3hgYDsKUp84mPVbrKAViaH\n9h+Jjzx9KzfX8AJQpDypid4W+hqrfYPg2/aqZu3Datee7P4vkc+rYoW6EK5xMSb2\nkyZUDEYeRORb3wduTtjT6SQDggGFAAKCAYB+AGLm8y7Kse/6ODkbcaUjIh1ul6Yf\nVSOPCmI8xCmAuYf7IuxhOOjRwNNsBdBZygyj8aUmpfZ7IWNBq80EEFu478nkecJT\nL53qbN37T1feW51pZOPTFOuJaTo9V/grn/k+Dg0R1y+k8b2Cuysg8bWVR6/3cR2z\nGdfQbmlkvrKU5E00wqIcesfNrF6R8vbRgwQq/DZEsJg3+iIloHTOtl1Jn3PO4Exw\nXIK7kS+X12XV+cjLRCAZ59rB4czM7pkDNeo7jIN1g1lc1PgxadR4f+RnU4bWBOji\nBbl3x6sjaVBCglTjuDa9ACliVyONIr2hanIuQF34ICnjOEkx+w5JA8P4dx+xVwjU\nyzI457KmgTG+UYoI1u/Ug6AVN6QyBG3L0f+l/4MeAleykgEtXhpExuMgGaazrhdq\nZ+2vEusn5o+mCgWvTlRI1gbDkrSmcrRCmLF3Wha5RAsTHrDZHKP94aHlKLX//DH/\n3xRJFpwvSr2WgJp1+2yFroRZQMRdWvgzQFc=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "DsaVerify", "tests": [{"tcId": 1, "comment": "Legacy:ASN encoding of r misses leading 0", "msg": "313233343030", "sig": "304402208c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "acceptable", "flags": ["NoLeadingZero"]}, {"tcId": 2, "comment": "valid", "msg": "313233343030", "sig": "30450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "valid", "flags": []}, {"tcId": 3, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "3081450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 4, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "308200450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 5, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "30460221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 6, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "30440221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "308501000000450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "30890100000000000000450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff0221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff0221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff0221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff0221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff0221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "30800221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "30450280008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "30450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4028033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "30470221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba0000", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "304700000221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "30450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba0000", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "30470221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba0500", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "including garbage", "msg": "313233343030", "sig": "304a49817730450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "including garbage", "msg": "313233343030", "sig": "3049250030450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "304730450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba0004deadbeef", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "304a22264981770221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "3049222525000221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "304d22230221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40004deadbeef022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "304a0221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b42225498177022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "30490221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b422242500022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "304d0221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b42222022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba0004deadbeef", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including undefined tags", "msg": "313233343030", "sig": "304daa00bb00cd0030450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including undefined tags", "msg": "313233343030", "sig": "304baa02aabb30450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "304d2229aa00bb00cd000221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "304b2227aa02aabb0221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "304d0221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b42228aa00bb00cd00022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "304b0221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b42226aa02aabb022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "308030450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba0000", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "304922800221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40000022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30490221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b42280022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba0000", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "308031450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba0000", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "304922800321008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40000022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30490221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b42280032033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba0000", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "31450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "32450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "using composition for sequence", "msg": "313233343030", "sig": "3049300102304421008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "truncated sequence", "msg": "313233343030", "sig": "30440221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783c", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "truncated sequence", "msg": "313233343030", "sig": "304421008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "indefinite length", "msg": "313233343030", "sig": "30800221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba0000", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "30800221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba00", "result": "invalid", "flags": []}, {"tcId": 57, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "30800221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba05000000", "result": "invalid", "flags": []}, {"tcId": 58, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "30800221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba060811220000", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "30800221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba0000fe02beef", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "30800221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba0002beef", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "304730000221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "append empty sequence", "msg": "313233343030", "sig": "30470221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba3000", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "30480221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cbabf7f00", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "sequence of sequence", "msg": "313233343030", "sig": "304730450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "30230221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "30670221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "3046028121008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "30460221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b402812033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 69, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "304702820021008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 70, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "30470221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40282002033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 71, "comment": "wrong length of integer", "msg": "313233343030", "sig": "30450222008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 72, "comment": "wrong length of integer", "msg": "313233343030", "sig": "30450220008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "30450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022133708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "30450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4021f33708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "304a02850100000021008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "304a0221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40285010000002033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "304e0289010000000000000021008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "304e0221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4028901000000000000002033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304902847fffffff008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "30490221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b402847fffffff33708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30490284ffffffff008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30490221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40284ffffffff33708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "304a0285ffffffffff008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "304a0221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40285ffffffffff33708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "304d0288ffffffffffffffff008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "304d0221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40288ffffffffffffffff33708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "304502ff008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "30450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b402ff33708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "removing integer", "msg": "313233343030", "sig": "3022022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "lonely integer tag", "msg": "313233343030", "sig": "302302022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "lonely integer tag", "msg": "313233343030", "sig": "30240221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b402", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "30470223008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40000022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "30470221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022233708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba0000", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "304702230000008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "30470221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40222000033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 96, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "30470221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40000022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 97, "comment": "appending null value to integer", "msg": "313233343030", "sig": "30470223008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40500022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 98, "comment": "appending null value to integer", "msg": "313233343030", "sig": "30470221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022233708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba0500", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "truncated length of integer", "msg": "313233343030", "sig": "30240281022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "truncated length of integer", "msg": "313233343030", "sig": "30250221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40281", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "30240500022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "30250221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40500", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450021008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450121008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450321008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450421008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "3045ff21008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4002033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4012033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4032033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4042033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "30450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4ff2033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "dropping value of integer", "msg": "313233343030", "sig": "30240200022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "dropping value of integer", "msg": "313233343030", "sig": "30250221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40200", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "using composition for integer", "msg": "313233343030", "sig": "3049222502010002208c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "using composition for integer", "msg": "313233343030", "sig": "30490221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b42224020133021f708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "30450221028c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "30450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022031708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "30450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b59834022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "30450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783c3a", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "truncated integer", "msg": "313233343030", "sig": "30440220008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "truncated integer", "msg": "313233343030", "sig": "30440221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4021f33708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783c", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "30440221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4021f708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "leading ff in integer", "msg": "313233343030", "sig": "30460222ff008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "30460221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40221ff33708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3025090180022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "30260221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4090180", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3025020100022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "30260221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4020100", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "304502210184f06edc074bf33e52498ea99b9f63f28f1d714483ee3fc0f890c8a7262ad8eb022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "30440220940ec822b35b5668bc997cfc43340174c70ef2f6aee441c4e707155c5b40587d022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "30450221ff73806480a2ac5b2c788e7a2d10964d4c54e9cde26696bf3d103410fe3f4a674c022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "304402206bf137dd4ca4a99743668303bccbfe8b38f10d09511bbe3b18f8eaa3a4bfa783022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "30450221fe7b0f9123f8b40cc1adb6715664609c0d70e28ebb7c11c03f076f3758d9d52715022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "30450221018c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "3044022073806480a2ac5b2c788e7a2d10964d4c54e9cde26696bf3d103410fe3f4a674c022033708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "30460221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40221012be15e6c7e08e6de1fe50b0ab54e9dd2c93f7c6e07220930336c45cdd7ed7cf1", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "30460221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40221ff3affb7b32a184a088a34f95d5ce33b550130fe2032180b3421e292830d02fc83", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "30450221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40220cc8f74f02bef678caaf2fdcbf6e7136c1ac7c2b8e362f5cdd55893d78d87c346", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "30460221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022100c500484cd5e7b5f775cb06a2a31cc4aafecf01dfcde7f4cbde1d6d7cf2fd037d", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "30460221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b40221fed41ea19381f71921e01af4f54ab1622d36c08391f8ddf6cfcc93ba322812830f", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "30460221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b402210133708b0fd4109873550d02340918ec93e5383d471c9d0a322aa76c2872783cba", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "30460221008c7f9b7f5d53a4d3877185d2ef69b2b3ab16321d996940c2efcbef01c0b598b4022100cc8f74f02bef678caaf2fdcbf6e7136c1ac7c2b8e362f5cdd55893d78d87c346", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30260201000221ff078f2ca35607b1953527f72953ca4ec11bf8c0d9157b0101f73b265a9a8abfc9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302502010002207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302502010002207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026020100022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026020100022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026020100022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30260201000221010000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201880201000282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30260201010221ff078f2ca35607b1953527f72953ca4ec11bf8c0d9157b0101f73b265a9a8abfc9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302502010102207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302502010102207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026020101022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026020101022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026020101022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30260201010221010000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201880201010282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30260201ff0221ff078f2ca35607b1953527f72953ca4ec11bf8c0d9157b0101f73b265a9a8abfc9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30250201ff02207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30250201ff02207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30260201ff022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30260201ff022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30260201ff022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30260201ff0221010000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201880201ff0282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "304502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b0221ff078f2ca35607b1953527f72953ca4ec11bf8c0d9157b0101f73b265a9a8abfc9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "304402207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b02207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "304402207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b02207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "304502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "304502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "304502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "304502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b0221010000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201a702207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b0282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302702207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "304502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c0221ff078f2ca35607b1953527f72953ca4ec11bf8c0d9157b0101f73b265a9a8abfc9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "304402207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c02207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "304402207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c02207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "304502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "304502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "304502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "304502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c0221010000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201a702207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c0282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302702207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302502207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3046022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a5657540360221ff078f2ca35607b1953527f72953ca4ec11bf8c0d9157b0101f73b265a9a8abfc9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a5657540360201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3045022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a56575403602207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3045022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a56575403602207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3046022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3046022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3046022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3046022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a5657540360221010000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201a8022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a5657540360282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3028022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3046022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a5657540370221ff078f2ca35607b1953527f72953ca4ec11bf8c0d9157b0101f73b265a9a8abfc9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 225, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a5657540370201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 226, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3045022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a56575403702207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 227, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3045022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a56575403702207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 228, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3046022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 229, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3046022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 230, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3046022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 231, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3046022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a5657540370221010000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 232, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201a8022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a5657540370282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 233, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3028022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 234, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 235, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3046022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a5657540380221ff078f2ca35607b1953527f72953ca4ec11bf8c0d9157b0101f73b265a9a8abfc9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 236, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 237, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 238, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a5657540380201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 239, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3045022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a56575403802207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 240, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3045022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a56575403802207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 241, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3046022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 242, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3046022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 243, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3046022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 244, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3046022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a5657540380221010000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 245, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201a8022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a5657540380282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 246, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3028022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 247, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3026022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 248, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "304602210100000000000000000000000000000000000000000000000000000000000000000221ff078f2ca35607b1953527f72953ca4ec11bf8c0d9157b0101f73b265a9a8abfc9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 249, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30260221010000000000000000000000000000000000000000000000000000000000000000020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 250, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30260221010000000000000000000000000000000000000000000000000000000000000000020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 251, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "302602210100000000000000000000000000000000000000000000000000000000000000000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 252, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3045022101000000000000000000000000000000000000000000000000000000000000000002207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 253, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3045022101000000000000000000000000000000000000000000000000000000000000000002207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 254, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30460221010000000000000000000000000000000000000000000000000000000000000000022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 255, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30460221010000000000000000000000000000000000000000000000000000000000000000022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 256, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30460221010000000000000000000000000000000000000000000000000000000000000000022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 257, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "304602210100000000000000000000000000000000000000000000000000000000000000000221010000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 258, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201a802210100000000000000000000000000000000000000000000000000000000000000000282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 259, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30280221010000000000000000000000000000000000000000000000000000000000000000090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 260, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30260221010000000000000000000000000000000000000000000000000000000000000000090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 261, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201a80282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b10221ff078f2ca35607b1953527f72953ca4ec11bf8c0d9157b0101f73b265a9a8abfc9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 262, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201880282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 263, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201880282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 264, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201880282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b10201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 265, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201a70282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b102207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 266, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201a70282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b102207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 267, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201a80282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 268, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201a80282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 269, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201a80282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 270, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201a80282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b10221010000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 271, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082030a0282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b10282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 272, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082018a0282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 273, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201880282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 274, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3028090380fe010221ff078f2ca35607b1953527f72953ca4ec11bf8c0d9157b0101f73b265a9a8abfc9", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 275, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008090380fe01020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 276, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008090380fe01020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 277, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008090380fe010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 278, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3027090380fe0102207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01b", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 279, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3027090380fe0102207c3869ae54fc2735656c046b561ad89f72039f9375427f7f04626cd2b2baa01c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 280, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3028090380fe01022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 281, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3028090380fe01022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 282, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3028090380fe01022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754038", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 283, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3028090380fe010221010000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 284, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082018a090380fe010282018100dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 285, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "300a090380fe01090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 286, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008090380fe01090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 287, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 288, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 289, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 290, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 291, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 292, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 293, "comment": "random signatures", "msg": "313233343030", "sig": "3044022075372ea72fa5e6cbfc7a9f521a7dc997110d4733142c8e9f87295cdd4388e88a022007db3d8d4e1e5aaafe3de950d7f92fe2bc11e10b486b7280b2518312b651b982", "result": "valid", "flags": []}, {"tcId": 294, "comment": "random signatures", "msg": "313233343030", "sig": "304402206f143bbab6091e03c826a5735285dd54ccff1dfca8f7ad58abda32a1d448117802202f5e7d1e5978f32d36d82c0a726eb8947ddb3a89a7c6a474df381ea1cc4a86e3", "result": "valid", "flags": []}, {"tcId": 295, "comment": "random signatures", "msg": "313233343030", "sig": "304502203b2573d4d3d21ae7a36823fcbbce05bed850083cdef45e7e25b9c2e062a48309022100e628902f357ed9b47c521a6ccf93464989b5536277f5e9048e4f69537a2d8c05", "result": "valid", "flags": []}, {"tcId": 296, "comment": "random signatures", "msg": "313233343030", "sig": "30440220119bd5685c0085732a3e93023254ffa3781d7a7ab8b3f7d46de9a07fa344284b02206419b92689c084d247324c8c17df1d9e304824e8a21b8daa1125f2c439764663", "result": "valid", "flags": []}, {"tcId": 297, "comment": "random signatures", "msg": "313233343030", "sig": "30450221009b9385d07a20cf8298440c05caaaf990f16ba2fab259f32e191921544d94774a0220790e92ed4c924e5302ef59341089f6c5f810e454ffcded1b55c0d774fabcc047", "result": "valid", "flags": []}, {"tcId": 298, "comment": "special case hash", "msg": "343236343739373234", "sig": "3046022100d4062f0317e2ac0a88ddd5c358b237966daa48fe2aea086df532aa834806e415022100e1286fde40f009a5d17720a785f90866a2fa866c899230dbbb9b8c16024c47ad", "result": "valid", "flags": []}, {"tcId": 299, "comment": "special case hash", "msg": "37313338363834383931", "sig": "3046022100f4548556b4132464c0a62684dd086bb78a1a100dd8a338b6fc6267bd35b4a532022100bb2db0562893e142a1eedbd0e609fdf5f0594b11b8ee5788bc44b55ee6299e4d", "result": "valid", "flags": []}, {"tcId": 300, "comment": "special case hash", "msg": "3130333539333331363638", "sig": "304502200ad7a013ba6dfd6bf2a24c8486de1bc33d61bd4ab6cd641e96d7db3d6c55e9de022100a48e66b4c2fbe5a0caefd70941a19a3db540587ca07c8ea470d5c92f12653d37", "result": "valid", "flags": []}, {"tcId": 301, "comment": "special case hash", "msg": "33393439343031323135", "sig": "30440220621aa059e535c61df7cc76ec85ec7a83fdb672e744e5f395740578ee216d2053022074fe4685e0b636fdfb64bbdfd9f0eb1b7bdee523c10846e9fca17998c3781816", "result": "valid", "flags": []}, {"tcId": 302, "comment": "special case hash", "msg": "31333434323933303739", "sig": "3045022076340e9329e00faff1e9b9c607155fca80891ce6bddd0b043f31e85260e0e1ff022100d2d4f8223494939a6af1cc76530772073a431974c1306130e0c4cf7500bc3b5d", "result": "valid", "flags": []}, {"tcId": 303, "comment": "special case hash", "msg": "33373036323131373132", "sig": "3045022100f6fb56190c2ed5efbb45653ff2ec451789bc319b03825e1ef2b6eb8372af7c7302206c2bf34582d1659c1fa10a2f8355913d32e72183692a59702390d2db78314749", "result": "valid", "flags": []}, {"tcId": 304, "comment": "special case hash", "msg": "333433363838373132", "sig": "304602210099cc7941cf539a42dda08c17956a3f5f7c8cd55f4bf8886a3ad66269e81f850d022100adbed99646f1dac7abed06a880570435482d5978d243e7febba388f9f839b2eb", "result": "valid", "flags": []}, {"tcId": 305, "comment": "special case hash", "msg": "31333531353330333730", "sig": "304502201184997197881b07dd9e4a18962057c419dfc20693fbd7de5edc0b302ace4581022100a26fce9c3bddcf47b68d2cfadd1ba5680fb3862bf4fc269eadd3cfea451fbc01", "result": "valid", "flags": []}, {"tcId": 306, "comment": "special case hash", "msg": "36353533323033313236", "sig": "304402200544c499767d0374643503779f4aa2435b1ecd73b7094373991f636c29f2714102204c6feaa33563aeddf7cdcdd86c88df92a065e7acb5c7668fa3fd135c9e34d434", "result": "valid", "flags": []}, {"tcId": 307, "comment": "special case hash", "msg": "31353634333436363033", "sig": "304402206fb407811c9d4c9cb6949bc2ff7158c38eb1b6b2b27441a8dc848cf08eee3c7e022058707134ba340f02b07ef007c5a5f3b664de7a1ea9ca50c00c1ff08b6a8e3243", "result": "valid", "flags": []}, {"tcId": 308, "comment": "special case hash", "msg": "34343239353339313137", "sig": "304502210081ab0ab1e40e06ad80d53fb9519c1a8e9d5aef739751ce97f1a4d1548c936a2402202fd18e842ba1fbe6bcfd67142394207e58fb890b67e62d133436f561eb8038e5", "result": "valid", "flags": []}, {"tcId": 309, "comment": "special case hash", "msg": "3130393533323631333531", "sig": "3046022100ba82e95f2e450ecc8cf3583a9f184e6717b413a48e34143e89922d696720611a022100f85c4e88aa4a6c7e6200b75a4076bf93be24d52f5f72b4b5234e31debf19f17e", "result": "valid", "flags": []}, {"tcId": 310, "comment": "special case hash", "msg": "35393837333530303431", "sig": "30460221008b9b82cb97e2780f0c81a085ad367fc95da9f8843fab57ddce33a52fd27169eb0221008dcf5e3856ece8d17addd0e6ee514ec6720521c2d50d42650405d8635eba20ad", "result": "valid", "flags": []}, {"tcId": 311, "comment": "special case hash", "msg": "33343633303036383738", "sig": "304502203ba6b2bdf397aafc9692f7c94f84b321f00252aeec35d74788fd5df1d952b351022100ea25ac40807b7addc6e4f06b68c07bebf7b03c8b044ba73d8e9cd08a4993eeed", "result": "valid", "flags": []}, {"tcId": 312, "comment": "special case hash", "msg": "39383137333230323837", "sig": "3045022100ddc0f486494364e050b70ba83c06fc5b3a1d97fee7bc13f8a5d1b462f7f2ce260220390c1bf241c42f4613eb679f491fc649eed849e476b2e334e22d6b208eab241f", "result": "valid", "flags": []}, {"tcId": 313, "comment": "special case hash", "msg": "33323232303431303436", "sig": "304402200c5a7dbaa92428ca5eda38e448da0e775e87293a777d42b48469512f7324650b0220793c1cc9008343bb642e99bb64f84d0d0b6ec9dc94363fe286b644946e0ff8a5", "result": "valid", "flags": []}, {"tcId": 314, "comment": "special case hash", "msg": "36363636333037313034", "sig": "304402206a1e03b2957a0746034796e1af1b58515afec3cf39db1ea9476d441622a7f0a202200effd344a79068b2c201ee6fef9bcbd6479005097e561e1a1ae6eb1fba12ae3a", "result": "valid", "flags": []}, {"tcId": 315, "comment": "special case hash", "msg": "31303335393531383938", "sig": "304502201e731f4650444d2e9426a7aa90e4934349aeb284fdffb93f5cf560558c5b9bd9022100948dc915d655d7d0b6d9f881acf3edc1d493434556a934cf7c7e96583f737b56", "result": "valid", "flags": []}, {"tcId": 316, "comment": "special case hash", "msg": "31383436353937313935", "sig": "30460221009c50feb59e2d9956fd4571b2331e9e4e87ce4978281f00a18c1f1c47a6ddb51b022100db21411c7b7a75782c4c9f6103e65730ba10c1e440b21c7c7c446ce0240ff284", "result": "valid", "flags": []}, {"tcId": 317, "comment": "special case hash", "msg": "33313336303436313839", "sig": "3045022100f16ab60037bf56e4f5286ae3141eebed7ceee0c04cbfbb669cea8bc5e07d54b702203e437e0334810de42eca1c5c60b79cb72c0aa39f25a422c55fc8c3741da6cf91", "result": "valid", "flags": []}, {"tcId": 318, "comment": "special case hash", "msg": "32363633373834323534", "sig": "3045022079796fe13dd5d95f9fcd1df30604a26fa9c64e431b1889eb1e20f051e55280c5022100afda38bfb5aa3f297f3b6d71309f1838c575b332f9d908354a52c7476c4213f6", "result": "valid", "flags": []}, {"tcId": 319, "comment": "special case hash", "msg": "31363532313030353234", "sig": "3046022100ac66710eaa5a74a958ade0826b2d1a03ad5ec4e0caff2ab2efb3685eb5536a080221008167486e3ee010df3a8c539911394df69c6c6e06205046beaac57f02bf20dc88", "result": "valid", "flags": []}, {"tcId": 320, "comment": "special case hash", "msg": "35373438303831363936", "sig": "304402200ae252fa295ace544bc7ae0b8a8b03dedb378a596bbd0d6c588c92848a0a2f63022019a20860d415bc6326964efe78ba1665ce59e0fd7973da4354c94c5496009631", "result": "valid", "flags": []}, {"tcId": 321, "comment": "special case hash", "msg": "36333433393133343638", "sig": "3045022100ec0c0002ae8a3c20ebbca4c996097802d704edb0fd8550b250754ed81c88da2202203abe4fdfe6e249873123b9164bb6fddeec88b78e39d34404bf1decb8b9065934", "result": "valid", "flags": []}, {"tcId": 322, "comment": "special case hash", "msg": "31353431313033353938", "sig": "304502210082d741de565d173f15e4edb688983ccbfcd25b59caf9db34b03ac8f9e2a440d30220685eaa9e9fe516b89441c250bb01cedd39937335366da052bdafb6188710f665", "result": "valid", "flags": []}, {"tcId": 323, "comment": "special case hash", "msg": "3130343738353830313238", "sig": "3044022074480e03561f8c20523a4dfb6b220178f6e4b3ec18c507b7cfd0204dc3831260022047be7bef3fa121d488098ea192a87373412c4632211c40bbf2122b6ebbc56c13", "result": "valid", "flags": []}, {"tcId": 324, "comment": "special case hash", "msg": "3130353336323835353638", "sig": "3044022028ce130dfd2d469a280e06be04383465d195c485828fd1376e407b769be6ddb602200a842cca5a4a5322a49eb2cc1bcc8cc6ba9b233084669044af2214550101f0b3", "result": "valid", "flags": []}, {"tcId": 325, "comment": "special case hash", "msg": "393533393034313035", "sig": "3045022100afac7a51b37f2fff9acefbd2c6a54fe45abcedb118eb63e99a84e08c935df25302204ae2604d5cb1d4941a85237a9db6fb63fb1fb4dc534facd983cc91d5b6dd43d7", "result": "valid", "flags": []}, {"tcId": 326, "comment": "special case hash", "msg": "393738383438303339", "sig": "304502200739e95552e8faf3c00f20209399a7911c485f844617355d7ab1a23d1d807d9d022100b24105c928ec5e1257d4f7c47a4facfe810453065124b05e21b521f15442ab0f", "result": "valid", "flags": []}, {"tcId": 327, "comment": "special case hash", "msg": "33363130363732343432", "sig": "304602210094983a09df13553e9254468d684dcef3369706c7380264fe2feaa96d397133f20221008c0fee1f598c5581e5f9bdd2d073adca375962d021d1002db48904c8ab998393", "result": "valid", "flags": []}, {"tcId": 328, "comment": "special case hash", "msg": "31303534323430373035", "sig": "3045022100ca454225647d2666c8d43fb2ef3496ea8456627c1be300a4d2be264b8b08ed4b02204ed2c7d61e76612ef7d4d9331893a652bc38877b0a9acb86101ff8e2b6c82a22", "result": "valid", "flags": []}, {"tcId": 329, "comment": "special case hash", "msg": "35313734343438313937", "sig": "304402206b3e9c3d2ffbd3274024afcd0c73429cca6d61b3149b17a721b227679ce832ec022053bf387a3ade5ad0966a962a298fd791d31b5fa172ea73a3401de9f1f2eb7cb0", "result": "valid", "flags": []}, {"tcId": 330, "comment": "special case hash", "msg": "31393637353631323531", "sig": "30440220442c81cf0b2d782b91f0c6f7b1d0e29845e677f50936046ce160d69c9977d975022073d903b2536563c4f6a2e7c04126d66a603187635484d3d2db57cb85cbe669b1", "result": "valid", "flags": []}, {"tcId": 331, "comment": "special case hash", "msg": "33343437323533333433", "sig": "3045022044bbe094d08589a9c95354f15338dac4b217f1ed1f8565ae9baa4212bd3f1d15022100cc69dc332ded63569fa975f7219a188d7b8a0e79fe1fe84b0d8587d3dcb2d189", "result": "valid", "flags": []}, {"tcId": 332, "comment": "special case hash", "msg": "333638323634333138", "sig": "3046022100adeeaee62b783efacef60ab1e2d8d86c69466d5c3f06cfd1b49a3c569ed2ce7f0221008f16c45c21c06cb671939dc4c7e424ec6099c75bbef2b1fc75fc21f09a58ccd5", "result": "valid", "flags": []}, {"tcId": 333, "comment": "special case hash", "msg": "33323631313938363038", "sig": "3046022100b583118fe17642e4c8fbd9a1260cc3e8e88be8647f584d25dfdcecdd2ff4fb8f022100a90a3290057fba3cb23ab281f866f33c75409293016c7bf84e2b72421fdba62d", "result": "valid", "flags": []}, {"tcId": 334, "comment": "special case hash", "msg": "39363738373831303934", "sig": "304502206ec6c4d2d57bbb7da66a500a8537ee7ed79d9dc75e40b92155eed931a4254865022100b7924b5b645146742582fb6ad0c1775787566b9e3cde6622ea506dc44ebd9702", "result": "valid", "flags": []}, {"tcId": 335, "comment": "special case hash", "msg": "34393538383233383233", "sig": "3045022037d0fa1d4a55ca4a71a3050e6523294132def6f3fadee9fb218bb03027be7346022100df13ec1d9c3c1319cc6d09d6fd4f93c6f777c377823c86badfe0e0934627c463", "result": "valid", "flags": []}, {"tcId": 336, "comment": "special case hash", "msg": "383234363337383337", "sig": "3046022100bf7527a5a8390c6a43ea2a406eb6291c04ecef0b71839b1b6e74e518e68f75c8022100a36ecc6d304fb7e3f0c8497059508ac0ad1f22f4cf299a5b583cb96abb7fb501", "result": "valid", "flags": []}, {"tcId": 337, "comment": "special case hash", "msg": "3131303230383333373736", "sig": "3045022008dba73101f8b7051bfee1963891051740406470a0b00feb1cfed9efe85f5513022100bd35d2e88e11b75b348927c930974b7bc64d46bd6d3754b70210abe4b2aab4c6", "result": "valid", "flags": []}, {"tcId": 338, "comment": "special case hash", "msg": "313333383731363438", "sig": "304402200b2332e792473a1554144cf2b3fe0f4fdd35eb5a1bd97f777240a429a08b980602202355a6c2e0629d948c631c68058ce55d25c5ae875090ea5b3d20e90336acad3c", "result": "valid", "flags": []}, {"tcId": 339, "comment": "special case hash", "msg": "333232313434313632", "sig": "304502200f5dfa8a6a1061f3819eaf0471acb65b1b07712405f4f9d2f8a04694996e26e8022100d2c4ad577a48b104a35e2bdd8892b0e87cd69f4df780a2acd2591e2bcea8c864", "result": "valid", "flags": []}, {"tcId": 340, "comment": "special case hash", "msg": "3130363836363535353436", "sig": "304402201f516ba16570812dd2f8b530402c236e78ad128ebe769af0e15eddbb6e41367c02203fa165487052e0932f01a853305e358c35a531c8b95570d6382b90f088065eb4", "result": "valid", "flags": []}, {"tcId": 341, "comment": "special case hash", "msg": "3632313535323436", "sig": "304402205359b3048d252766571d81f764f3eeea65bed72ec2c435b85eda413ebda98a6802206f2ba381008798b57e9107471d9e2f33ddaf26707615333a2d94b2c60cc9518b", "result": "valid", "flags": []}, {"tcId": 342, "comment": "special case hash", "msg": "37303330383138373734", "sig": "304502205b4587dceb3adfb5c5d5b12be1bda39422e96baa4a1e9e62a9f2ffbff3c3135c022100d5573abf5ebe5207af7e45e5e535b9eae2f1db124ac15b5dbd230d63bf96b00c", "result": "valid", "flags": []}, {"tcId": 343, "comment": "special case hash", "msg": "35393234353233373434", "sig": "3044022046b88098ea1bb00272962dabc887342f9d3079b64eb3754f1f3edd5289a5a0cc02206a5108935469a4d55448ec9a8230f8a1af17e66d4da6631c2de1b254e3a8f887", "result": "valid", "flags": []}, {"tcId": 344, "comment": "special case hash", "msg": "31343935353836363231", "sig": "3044022100e17a0b9c8fb3857fd314a20b9c208d2882548c2bddef8181a790caf80f93a3cf021f77ca72436f2ef67d720153941fc38e72b45b6608e2eb4231a1050ec7feb59a", "result": "valid", "flags": []}, {"tcId": 345, "comment": "special case hash", "msg": "34303035333134343036", "sig": "30450221008bac66a98dd9d87e19e66ca8083f79b0f17067b1f4d23aeb1023ce8e3498b658022016c979b46925fba22f49d17e0800e79d77eae69162b62f96a30a1bcd254655ee", "result": "valid", "flags": []}, {"tcId": 346, "comment": "special case hash", "msg": "33303936343537353132", "sig": "304402206690642756a4d30c72a319efe294397bada5b2d5b07c09015445e756c7930d7802205d845d7acde5a7e7379d190ae60a96b4042cbfe74c7a054bd64d6665b0df1d5d", "result": "valid", "flags": []}, {"tcId": 347, "comment": "special case hash", "msg": "32373834303235363230", "sig": "304502207c587b104978987fef8b02bdcde431e46e64f071feebef0f5d3e5a1daa7f820b0221008c72848ab64d909ba2722beb89fbec420e518bbdf208dd880fb53d12f5fe1432", "result": "valid", "flags": []}, {"tcId": 348, "comment": "special case hash", "msg": "32363138373837343138", "sig": "30460221008d92d7774b2459889aa898c12661861481bf40018da8a80c21625d53ac3a8474022100db5a588429c571740cfe37ee8575b2e5182a7060a4cbfb29cdb3fd33dfa1fb02", "result": "valid", "flags": []}, {"tcId": 349, "comment": "special case hash", "msg": "31363432363235323632", "sig": "30450220577582612e9cba25a12b44e770f7ac4564d369b99d6c3647d3e35f2504b53dac0221009a7a7708c3c59b487ddbde5747fc58e6d653b0a14c8b954758ad5c677afb6307", "result": "valid", "flags": []}, {"tcId": 350, "comment": "special case hash", "msg": "36383234313839343336", "sig": "3046022100843b1bcb4a8f0784a31f67ce4d2e001ecc21bbfcf3ab9e1f69ea269579529d150221008ee5cefd7180787c005d5649aef4211d8f3a5f1e131bfb798132709d1d624357", "result": "valid", "flags": []}, {"tcId": 351, "comment": "special case hash", "msg": "343834323435343235", "sig": "304602210088af7c66e7e776199ee836411ebeaaa25a38f698848354b26b5cc578fe281af7022100a2d1701a71143790dc3503ea4284eed3eb62b77e02a54a1eaf33114ee35b44e2", "result": "valid", "flags": []}]}, {"key": {"g": "6dea4b8c3fe3ab91e3229fb14c1cfa822915769af161405f48b7fadfe1ec5d9fec4ef0cfbb2233ffddfa5a554cfc68c6bc6a0ba30cef6f51309294e622b58d4face00ae9669d9172b15696839ed332afd906e3f427d85a9af73562b845be53a3713c0219402a4c208e9b6a6873235e0bc20442e70ab69edd46e8f3f7d58cb35ea3690c673f54cd37377725739f00ebe2b3b53bdaf89ddac74012f8486bd3f5217579b4a303f61bccc98931faba969c8c2a27acb04bc21201edf9a7f6b42e10f75dd23c3ab073d7290d173ebe6cb1919607bfe2bf0d829a609d8d3cda7044ff8dfbbd463e68c9403a45834ec547a7d4fd5abc68c5997cdc397120698f879356e0e74b62fe1a2938a5d1b486b53a5e0cb875e23a2e834ea563a4a9d4be44045877df020c30e22e55603f63d74ed2cafde18180ec294a7ce263d56eb280562687f61f898f3c7d2b37d7f00250a43ca989de16fa1aab7d83e0dbf6aa66edc36ad79eecfe2f91cfab6285ba10ae713126f69326540c461e44e45bdf076e4ed8d3e924", "keySize": 3072, "p": "00dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1", "q": "00f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037", "type": "DsaPublicKey", "y": "40f5d5e61931f77d7887325e6949df8fcbd229d1a0ca001160408a66d719fab5d0af83edaf235bfdca7dbcbf17d66b7834d20e190937a7783d9d74458b94b1b2741ba8a67e2abd64b46bbcac68dd63be7e08ee6b91f1dba967de5412bc4bf0f4fa4fd84f186f42ffa23d4c8d01d6e1a750100105d563bab2f1475fe83afba240853525037a3a7d50a4101fdf3daeb43fc16802cef1fbfed78c48fdb32d7de98643fa52c23630e12987f1215a3330e4b246cc1d35937f5d54a481363870cf385bb495c0892c34403bd97b47bf1f38882d68e0a5b8c712b618113afe188f0d8ef2f3f4cd264ec90af7187a88dc64abb49b9bd239fa13c8e72dc8baf42054923025f3d7d7082e24fe5d184515d4b78390899af933dec36e5ff495b781bfc5d2800b324c8606d707b5d207cf177a513701965706e89bc63671f7b42809892744ed963d0dbd8644bdd67532b778700176f00d123b41353eb5bcfa5cef38f2e2e02c1f44b256a981a2232a4a6e8e98b9adec82b8e5f6e97ad744762fa12836fad60cc8"}, "keyDer": "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", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIIExjCCAzkGByqGSM44BAEwggMsAoIBgQDc0vcfunrrRq7qhYq3byEC+pepU6vO\nnXkaomnwFhczrD3yX1yds0SPgoRuNV4jCJYUBG1CsDApjZT1Nl2ULLtUkOQKHV5u\nV3zGRqgH8Emh+0K5ep5k76Gqnvk7s8cSDdv5xAPlgEMfF4kSfwpk6nsDbvEtB/Ah\nA2VdY93aPEStMo9yfB0GD8kuNhaXbPEb8f7v/wM0kNmJKSUrWFzZLAga/McdrmNB\nro3QXmKuKXrSsAVg7JTx9kSCgW468FL8Ha/wqb9SA0ASWU1CRgNtBA+l50Hmk+Nr\nBkvtsiTqH3xshhccqPz6yYxdtuNNrTB8W/3s5OV48OGPyq7p1bMw7WmnLY/d+Hil\nileRQkeCWubtHLimskHqaUt3+EPu5A8b6Q8msmFUgTZH0eGvASVM8hz90unrp+Qx\nvY22Fk0Fo9Ouk3GvXQ05o6m58HumEjPHemv8JzUV+4RNuPr9abVZzoRMej1obqSZ\nHZ/nTK1WBInzwdu0/RceqK54dOMCIHwCp7ECIQD4cNNcqfhOasrYCNasNbE+5Ac/\nJuqE/v4IxNmlZXVANwKCAYBt6kuMP+OrkeMin7FMHPqCKRV2mvFhQF9It/rf4exd\nn+xO8M+7IjP/3fpaVUz8aMa8agujDO9vUTCSlOYitY1PrOAK6WadkXKxVpaDntMy\nr9kG4/Qn2Fqa9zViuEW+U6NxPAIZQCpMII6bamhzI14LwgRC5wq2nt1G6PP31Yyz\nXqNpDGc/VM03N3clc58A6+KztTva+J3ax0AS+Ehr0/UhdXm0owP2G8zJiTH6upac\njConrLBLwhIB7fmn9rQuEPdd0jw6sHPXKQ0XPr5ssZGWB7/ivw2CmmCdjTzacET/\njfu9Rj5oyUA6RYNOxUen1P1avGjFmXzcOXEgaY+Hk1bg50ti/hopOKXRtIa1Ol4M\nuHXiOi6DTqVjpKnUvkQEWHffAgww4i5VYD9j107Syv3hgYDsKUp84mPVbrKAViaH\n9h+Jjzx9KzfX8AJQpDypid4W+hqrfYPg2/aqZu3Datee7P4vkc+rYoW6EK5xMSb2\nkyZUDEYeRORb3wduTtjT6SQDggGFAAKCAYBA9dXmGTH3fXiHMl5pSd+Py9Ip0aDK\nABFgQIpm1xn6tdCvg+2vI1v9yn28vxfWa3g00g4ZCTeneD2ddEWLlLGydBuopn4q\nvWS0a7ysaN1jvn4I7muR8dupZ95UErxL8PT6T9hPGG9C/6I9TI0B1uGnUBABBdVj\nurLxR1/oOvuiQIU1JQN6On1QpBAf3z2utD/BaALO8fv+14xI/bMtfemGQ/pSwjYw\n4SmH8SFaMzDkskbMHTWTf11UpIE2OHDPOFu0lcCJLDRAO9l7R78fOIgtaOCluMcS\nthgROv4Yjw2O8vP0zSZOyQr3GHqI3GSrtJub0jn6E8jnLci69CBUkjAl89fXCC4k\n/l0YRRXUt4OQiZr5M97Dbl/0lbeBv8XSgAsyTIYG1we10gfPF3pRNwGWVwbom8Y2\ncfe0KAmJJ0Ttlj0NvYZEvdZ1Mrd4cAF28A0SO0E1PrW8+lzvOPLi4CwfRLJWqYGi\nIypKbo6Yua3sgrjl9ul610R2L6EoNvrWDMg=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "DsaVerify", "tests": [{"tcId": 352, "comment": "r,s = 1,1", "msg": "54657374", "sig": "3006020101020101", "result": "valid", "flags": []}, {"tcId": 353, "comment": "r,s = 1,5", "msg": "54657374", "sig": "3006020101020105", "result": "valid", "flags": []}, {"tcId": 354, "comment": "r = 1, u2 small", "msg": "54657374", "sig": "3026020101022100c6c0a916ee603ebbd579a0abbcf7c0ff1cd298ebeed0cbfe6d6a47b7845dccf9", "result": "valid", "flags": []}, {"tcId": 355, "comment": "r = 1, s = q-1", "msg": "54657374", "sig": "3026020101022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036", "result": "valid", "flags": []}]}, {"key": {"g": "6dea4b8c3fe3ab91e3229fb14c1cfa822915769af161405f48b7fadfe1ec5d9fec4ef0cfbb2233ffddfa5a554cfc68c6bc6a0ba30cef6f51309294e622b58d4face00ae9669d9172b15696839ed332afd906e3f427d85a9af73562b845be53a3713c0219402a4c208e9b6a6873235e0bc20442e70ab69edd46e8f3f7d58cb35ea3690c673f54cd37377725739f00ebe2b3b53bdaf89ddac74012f8486bd3f5217579b4a303f61bccc98931faba969c8c2a27acb04bc21201edf9a7f6b42e10f75dd23c3ab073d7290d173ebe6cb1919607bfe2bf0d829a609d8d3cda7044ff8dfbbd463e68c9403a45834ec547a7d4fd5abc68c5997cdc397120698f879356e0e74b62fe1a2938a5d1b486b53a5e0cb875e23a2e834ea563a4a9d4be44045877df020c30e22e55603f63d74ed2cafde18180ec294a7ce263d56eb280562687f61f898f3c7d2b37d7f00250a43ca989de16fa1aab7d83e0dbf6aa66edc36ad79eecfe2f91cfab6285ba10ae713126f69326540c461e44e45bdf076e4ed8d3e924", "keySize": 3072, "p": "00dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1", "q": "00f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037", "type": "DsaPublicKey", "y": "0080e1fa22c271cc861740bfd76ea27b8f81f2cdff5fc6371d7f1095790a59c96f55ecc3b6657557f071012aeae07ba4001c7195563bc837b8a330a7c77bee5cd50deb9edf121eb49d8aa4267c5f1e144d2a3fbe0d368a809f3199321adf3d68925e024bfda8a63f0dd15025887385a436d7f68f2f07c695a6a69ca09fc9cdf7da6ff0a8d5615aa7da95e46f429db95fde5e051e4c3da6e65c3666a54b936c961cfb7af99b614dabba224dbc8c3cf4e6818a1b83881dc1b8007dfecb497d55519941f763e015fda40fc314ff116cbb6b318abfa21d70cd325faac8ebe0c8ce9718fb1f9345d2284eb17b3003aaa93d4d4a25d27733a1cd804007ce46bc35cf0e7691f419a86cea1dfcf7786dcc3effca55e2136e593d9ca77632471098e79b1de0526bf8b0f69185d72d26a7aac702a37d8743c844ab495b10cf1b6c231d310bd9c5711e5b33ac37bcf679cebd416863563df4c8ea4e92bd177f27d508a8fd7dda90e62a4fa9012fec0a40c1fec8417052321518e50d8d70500b58b47bf37aed5e"}, "keyDer": "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", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIIExzCCAzkGByqGSM44BAEwggMsAoIBgQDc0vcfunrrRq7qhYq3byEC+pepU6vO\nnXkaomnwFhczrD3yX1yds0SPgoRuNV4jCJYUBG1CsDApjZT1Nl2ULLtUkOQKHV5u\nV3zGRqgH8Emh+0K5ep5k76Gqnvk7s8cSDdv5xAPlgEMfF4kSfwpk6nsDbvEtB/Ah\nA2VdY93aPEStMo9yfB0GD8kuNhaXbPEb8f7v/wM0kNmJKSUrWFzZLAga/McdrmNB\nro3QXmKuKXrSsAVg7JTx9kSCgW468FL8Ha/wqb9SA0ASWU1CRgNtBA+l50Hmk+Nr\nBkvtsiTqH3xshhccqPz6yYxdtuNNrTB8W/3s5OV48OGPyq7p1bMw7WmnLY/d+Hil\nileRQkeCWubtHLimskHqaUt3+EPu5A8b6Q8msmFUgTZH0eGvASVM8hz90unrp+Qx\nvY22Fk0Fo9Ouk3GvXQ05o6m58HumEjPHemv8JzUV+4RNuPr9abVZzoRMej1obqSZ\nHZ/nTK1WBInzwdu0/RceqK54dOMCIHwCp7ECIQD4cNNcqfhOasrYCNasNbE+5Ac/\nJuqE/v4IxNmlZXVANwKCAYBt6kuMP+OrkeMin7FMHPqCKRV2mvFhQF9It/rf4exd\nn+xO8M+7IjP/3fpaVUz8aMa8agujDO9vUTCSlOYitY1PrOAK6WadkXKxVpaDntMy\nr9kG4/Qn2Fqa9zViuEW+U6NxPAIZQCpMII6bamhzI14LwgRC5wq2nt1G6PP31Yyz\nXqNpDGc/VM03N3clc58A6+KztTva+J3ax0AS+Ehr0/UhdXm0owP2G8zJiTH6upac\njConrLBLwhIB7fmn9rQuEPdd0jw6sHPXKQ0XPr5ssZGWB7/ivw2CmmCdjTzacET/\njfu9Rj5oyUA6RYNOxUen1P1avGjFmXzcOXEgaY+Hk1bg50ti/hopOKXRtIa1Ol4M\nuHXiOi6DTqVjpKnUvkQEWHffAgww4i5VYD9j107Syv3hgYDsKUp84mPVbrKAViaH\n9h+Jjzx9KzfX8AJQpDypid4W+hqrfYPg2/aqZu3Datee7P4vkc+rYoW6EK5xMSb2\nkyZUDEYeRORb3wduTtjT6SQDggGGAAKCAYEAgOH6IsJxzIYXQL/XbqJ7j4Hyzf9f\nxjcdfxCVeQpZyW9V7MO2ZXVX8HEBKurge6QAHHGVVjvIN7ijMKfHe+5c1Q3rnt8S\nHrSdiqQmfF8eFE0qP74NNoqAnzGZMhrfPWiSXgJL/aimPw3RUCWIc4WkNtf2jy8H\nxpWmppygn8nN99pv8KjVYVqn2pXkb0KduV/eXgUeTD2m5lw2ZqVLk2yWHPt6+Zth\nTau6Ik28jDz05oGKG4OIHcG4AH3+y0l9VVGZQfdj4BX9pA/DFP8RbLtrMYq/oh1w\nzTJfqsjr4MjOlxj7H5NF0ihOsXswA6qpPU1KJdJ3M6HNgEAHzka8Nc8OdpH0Gahs\n6h3893htzD7/ylXiE25ZPZyndjJHEJjnmx3gUmv4sPaRhdctJqeqxwKjfYdDyESr\nSVsQzxtsIx0xC9nFcR5bM6w3vPZ5zr1BaGNWPfTI6k6SvRd/J9UIqP192pDmKk+p\nAS/sCkDB/shBcFIyFRjlDY1wUAtYtHvzeu1e\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "DsaVerify", "tests": [{"tcId": 356, "comment": "s = 1", "msg": "54657374", "sig": "3026022100b5618f5ebd9da737efd6cb29f5d56e921fa325cff53d2202b27cc44e642492c0020101", "result": "valid", "flags": []}]}, {"key": {"g": "6dea4b8c3fe3ab91e3229fb14c1cfa822915769af161405f48b7fadfe1ec5d9fec4ef0cfbb2233ffddfa5a554cfc68c6bc6a0ba30cef6f51309294e622b58d4face00ae9669d9172b15696839ed332afd906e3f427d85a9af73562b845be53a3713c0219402a4c208e9b6a6873235e0bc20442e70ab69edd46e8f3f7d58cb35ea3690c673f54cd37377725739f00ebe2b3b53bdaf89ddac74012f8486bd3f5217579b4a303f61bccc98931faba969c8c2a27acb04bc21201edf9a7f6b42e10f75dd23c3ab073d7290d173ebe6cb1919607bfe2bf0d829a609d8d3cda7044ff8dfbbd463e68c9403a45834ec547a7d4fd5abc68c5997cdc397120698f879356e0e74b62fe1a2938a5d1b486b53a5e0cb875e23a2e834ea563a4a9d4be44045877df020c30e22e55603f63d74ed2cafde18180ec294a7ce263d56eb280562687f61f898f3c7d2b37d7f00250a43ca989de16fa1aab7d83e0dbf6aa66edc36ad79eecfe2f91cfab6285ba10ae713126f69326540c461e44e45bdf076e4ed8d3e924", "keySize": 3072, "p": "00dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1", "q": "00f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037", "type": "DsaPublicKey", "y": "00b05e438cb1dd41add9b3e6af7d3d207b01597a51d942fae22b162238d3e6a092903b46452c685715d1b6ba712fcaf51d6ed47bcdda301f44d505883dd01eaf89c36cd365d74e1ce026d68171c4b92a0fd8a57fc4ece713ab92de7e70ebd64c2d061d3834af7955688aa500bf2a820482cc09c9c1a5c482d858395c8a31d92fa6922cf9152f441c6c8bc0327e6ac50dedd11add6f5a64dbcabd511bb44bca5db2fa1b05bd30e4722903952311fa1f7e645d0a57fff5bddba61a8212a1f8e6c5c406d8b8468a591e00cf2233055ac31bd189af2623e976435f486f2310092e90f7de5dfd194ecc1588d7ac856b63ccc1f6b01070a0a49ba4a5fea26b1cbf8c91a25e0210049dcd86be6c87f6b74102bc31e42cc8b41111916161a2b04d60f76c323f9a9aa78393ec7ff0aeece004d4d8a594567bef432868d4c1cea12e5c6623774b913de7d948976ea5dc957ef58cfabe2816159f13cbc1efeebce7aba8c20eac720fd6e3a49ca74f12489a74d808097e70903ddebba1db497f28c153e382fe0f"}, "keyDer": "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", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIIExzCCAzkGByqGSM44BAEwggMsAoIBgQDc0vcfunrrRq7qhYq3byEC+pepU6vO\nnXkaomnwFhczrD3yX1yds0SPgoRuNV4jCJYUBG1CsDApjZT1Nl2ULLtUkOQKHV5u\nV3zGRqgH8Emh+0K5ep5k76Gqnvk7s8cSDdv5xAPlgEMfF4kSfwpk6nsDbvEtB/Ah\nA2VdY93aPEStMo9yfB0GD8kuNhaXbPEb8f7v/wM0kNmJKSUrWFzZLAga/McdrmNB\nro3QXmKuKXrSsAVg7JTx9kSCgW468FL8Ha/wqb9SA0ASWU1CRgNtBA+l50Hmk+Nr\nBkvtsiTqH3xshhccqPz6yYxdtuNNrTB8W/3s5OV48OGPyq7p1bMw7WmnLY/d+Hil\nileRQkeCWubtHLimskHqaUt3+EPu5A8b6Q8msmFUgTZH0eGvASVM8hz90unrp+Qx\nvY22Fk0Fo9Ouk3GvXQ05o6m58HumEjPHemv8JzUV+4RNuPr9abVZzoRMej1obqSZ\nHZ/nTK1WBInzwdu0/RceqK54dOMCIHwCp7ECIQD4cNNcqfhOasrYCNasNbE+5Ac/\nJuqE/v4IxNmlZXVANwKCAYBt6kuMP+OrkeMin7FMHPqCKRV2mvFhQF9It/rf4exd\nn+xO8M+7IjP/3fpaVUz8aMa8agujDO9vUTCSlOYitY1PrOAK6WadkXKxVpaDntMy\nr9kG4/Qn2Fqa9zViuEW+U6NxPAIZQCpMII6bamhzI14LwgRC5wq2nt1G6PP31Yyz\nXqNpDGc/VM03N3clc58A6+KztTva+J3ax0AS+Ehr0/UhdXm0owP2G8zJiTH6upac\njConrLBLwhIB7fmn9rQuEPdd0jw6sHPXKQ0XPr5ssZGWB7/ivw2CmmCdjTzacET/\njfu9Rj5oyUA6RYNOxUen1P1avGjFmXzcOXEgaY+Hk1bg50ti/hopOKXRtIa1Ol4M\nuHXiOi6DTqVjpKnUvkQEWHffAgww4i5VYD9j107Syv3hgYDsKUp84mPVbrKAViaH\n9h+Jjzx9KzfX8AJQpDypid4W+hqrfYPg2/aqZu3Datee7P4vkc+rYoW6EK5xMSb2\nkyZUDEYeRORb3wduTtjT6SQDggGGAAKCAYEAsF5DjLHdQa3Zs+avfT0gewFZelHZ\nQvriKxYiONPmoJKQO0ZFLGhXFdG2unEvyvUdbtR7zdowH0TVBYg90B6vicNs02XX\nThzgJtaBccS5Kg/YpX/E7OcTq5LefnDr1kwtBh04NK95VWiKpQC/KoIEgswJycGl\nxILYWDlcijHZL6aSLPkVL0QcbIvAMn5qxQ3t0Rrdb1pk28q9URu0S8pdsvobBb0w\n5HIpA5UjEfoffmRdClf/9b3bphqCEqH45sXEBti4RopZHgDPIjMFWsMb0YmvJiPp\ndkNfSG8jEAkukPfeXf0ZTswViNeshWtjzMH2sBBwoKSbpKX+omscv4yRol4CEASd\nzYa+bIf2t0ECvDHkLMi0ERGRYWGisE1g92wyP5qap4OT7H/wruzgBNTYpZRWe+9D\nKGjUwc6hLlxmI3dLkT3n2UiXbqXclX71jPq+KBYVnxPLwe/uvOerqMIOrHIP1uOk\nnKdPEkiadNgICX5wkD3eu6HbSX8owVPjgv4P\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "DsaVerify", "tests": [{"tcId": 357, "comment": "u2 small", "msg": "54657374", "sig": "3046022100b8636239da28896d6201f928c6ca3e7737c0014de2e642f25d3f6a913baec760022100c6c0a916ee603ebbd579a0abbcf7c0ff1cd298ebeed0cbfe6d6a47b7845dccf9", "result": "valid", "flags": []}]}, {"key": {"g": "6dea4b8c3fe3ab91e3229fb14c1cfa822915769af161405f48b7fadfe1ec5d9fec4ef0cfbb2233ffddfa5a554cfc68c6bc6a0ba30cef6f51309294e622b58d4face00ae9669d9172b15696839ed332afd906e3f427d85a9af73562b845be53a3713c0219402a4c208e9b6a6873235e0bc20442e70ab69edd46e8f3f7d58cb35ea3690c673f54cd37377725739f00ebe2b3b53bdaf89ddac74012f8486bd3f5217579b4a303f61bccc98931faba969c8c2a27acb04bc21201edf9a7f6b42e10f75dd23c3ab073d7290d173ebe6cb1919607bfe2bf0d829a609d8d3cda7044ff8dfbbd463e68c9403a45834ec547a7d4fd5abc68c5997cdc397120698f879356e0e74b62fe1a2938a5d1b486b53a5e0cb875e23a2e834ea563a4a9d4be44045877df020c30e22e55603f63d74ed2cafde18180ec294a7ce263d56eb280562687f61f898f3c7d2b37d7f00250a43ca989de16fa1aab7d83e0dbf6aa66edc36ad79eecfe2f91cfab6285ba10ae713126f69326540c461e44e45bdf076e4ed8d3e924", "keySize": 3072, "p": "00dcd2f71fba7aeb46aeea858ab76f2102fa97a953abce9d791aa269f0161733ac3df25f5c9db3448f82846e355e23089614046d42b030298d94f5365d942cbb5490e40a1d5e6e577cc646a807f049a1fb42b97a9e64efa1aa9ef93bb3c7120ddbf9c403e580431f1789127f0a64ea7b036ef12d07f02103655d63ddda3c44ad328f727c1d060fc92e3616976cf11bf1feefff033490d98929252b585cd92c081afcc71dae6341ae8dd05e62ae297ad2b00560ec94f1f64482816e3af052fc1daff0a9bf52034012594d4246036d040fa5e741e693e36b064bedb224ea1f7c6c86171ca8fcfac98c5db6e34dad307c5bfdece4e578f0e18fcaaee9d5b330ed69a72d8fddf878a58a57914247825ae6ed1cb8a6b241ea694b77f843eee40f1be90f26b26154813647d1e1af01254cf21cfdd2e9eba7e431bd8db6164d05a3d3ae9371af5d0d39a3a9b9f07ba61233c77a6bfc273515fb844db8fafd69b559ce844c7a3d686ea4991d9fe74cad560489f3c1dbb4fd171ea8ae7874e302207c02a7b1", "q": "00f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754037", "type": "DsaPublicKey", "y": "24b727ad92154849f7517efd590e722c746f465dafc53feda343b0f1e2765f463697f55af913f5142454a9fbe9bba0467c3d89452f96022a4ab0888ba8917e3ba2bd6efdefbc0a61a9fe8eb8d3168503b2951bdfad7463c4448a7dc0137d5f662bdff347c5813af7f12457e3b3481e94bc73c8e98430fd91d82de635dae872d3961c566ad21b43db93964267c0974b83bed462cbb1ebf3e91744a8c3a0ac0e07119bb10d4dfee65a26048017674dcc065e5db702b0a7c96d986703d935d07546a6a488f9ef3f4ec97c7e24a2eb65e1ec632d3447fedd30d915a3ad1862699d44f599b8de3b7c1c7d9230833a8ab3c7810359fb0ffe20d9f1f618d4461bac0ae30778ce78f371832d8a9f50b2defcf8a3402c76395deeb9668d476f26ae065cfbbd3e44ba5913688321801168b8bc30b8ae610e3d45964d6f8ceeb044dcdff529fcfedf6146a38e5f097f5c36b462fa3e004736d860175f910514c890f95243fc36b5dfc4987449bf8b701e89c18c7c2cab21deade8306fcf5473a927df600df7"}, "keyDer": "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", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMIIExjCCAzkGByqGSM44BAEwggMsAoIBgQDc0vcfunrrRq7qhYq3byEC+pepU6vO\nnXkaomnwFhczrD3yX1yds0SPgoRuNV4jCJYUBG1CsDApjZT1Nl2ULLtUkOQKHV5u\nV3zGRqgH8Emh+0K5ep5k76Gqnvk7s8cSDdv5xAPlgEMfF4kSfwpk6nsDbvEtB/Ah\nA2VdY93aPEStMo9yfB0GD8kuNhaXbPEb8f7v/wM0kNmJKSUrWFzZLAga/McdrmNB\nro3QXmKuKXrSsAVg7JTx9kSCgW468FL8Ha/wqb9SA0ASWU1CRgNtBA+l50Hmk+Nr\nBkvtsiTqH3xshhccqPz6yYxdtuNNrTB8W/3s5OV48OGPyq7p1bMw7WmnLY/d+Hil\nileRQkeCWubtHLimskHqaUt3+EPu5A8b6Q8msmFUgTZH0eGvASVM8hz90unrp+Qx\nvY22Fk0Fo9Ouk3GvXQ05o6m58HumEjPHemv8JzUV+4RNuPr9abVZzoRMej1obqSZ\nHZ/nTK1WBInzwdu0/RceqK54dOMCIHwCp7ECIQD4cNNcqfhOasrYCNasNbE+5Ac/\nJuqE/v4IxNmlZXVANwKCAYBt6kuMP+OrkeMin7FMHPqCKRV2mvFhQF9It/rf4exd\nn+xO8M+7IjP/3fpaVUz8aMa8agujDO9vUTCSlOYitY1PrOAK6WadkXKxVpaDntMy\nr9kG4/Qn2Fqa9zViuEW+U6NxPAIZQCpMII6bamhzI14LwgRC5wq2nt1G6PP31Yyz\nXqNpDGc/VM03N3clc58A6+KztTva+J3ax0AS+Ehr0/UhdXm0owP2G8zJiTH6upac\njConrLBLwhIB7fmn9rQuEPdd0jw6sHPXKQ0XPr5ssZGWB7/ivw2CmmCdjTzacET/\njfu9Rj5oyUA6RYNOxUen1P1avGjFmXzcOXEgaY+Hk1bg50ti/hopOKXRtIa1Ol4M\nuHXiOi6DTqVjpKnUvkQEWHffAgww4i5VYD9j107Syv3hgYDsKUp84mPVbrKAViaH\n9h+Jjzx9KzfX8AJQpDypid4W+hqrfYPg2/aqZu3Datee7P4vkc+rYoW6EK5xMSb2\nkyZUDEYeRORb3wduTtjT6SQDggGFAAKCAYAktyetkhVISfdRfv1ZDnIsdG9GXa/F\nP+2jQ7Dx4nZfRjaX9Vr5E/UUJFSp++m7oEZ8PYlFL5YCKkqwiIuokX47or1u/e+8\nCmGp/o640xaFA7KVG9+tdGPERIp9wBN9X2Yr3/NHxYE69/EkV+OzSB6UvHPI6YQw\n/ZHYLeY12uhy05YcVmrSG0Pbk5ZCZ8CXS4O+1GLLsevz6RdEqMOgrA4HEZuxDU3+\n5lomBIAXZ03MBl5dtwKwp8ltmGcD2TXQdUampIj57z9OyXx+JKLrZeHsYy00R/7d\nMNkVo60YYmmdRPWZuN47fBx9kjCDOoqzx4EDWfsP/iDZ8fYY1EYbrArjB3jOePNx\ngy2Kn1Cy3vz4o0Asdjld7rlmjUdvJq4GXPu9PkS6WRNogyGAEWi4vDC4rmEOPUWW\nTW+M7rBE3N/1Kfz+32FGo45fCX9cNrRi+j4ARzbYYBdfkQUUyJD5UkP8NrXfxJh0\nSb+LcB6JwYx8LKsh3q3oMG/PVHOpJ99gDfc=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "DsaVerify", "tests": [{"tcId": 358, "comment": "s = q - 1", "msg": "54657374", "sig": "3046022100b8636239da28896d6201f928c6ca3e7737c0014de2e642f25d3f6a913baec760022100f870d35ca9f84e6acad808d6ac35b13ee4073f26ea84fefe08c4d9a565754036", "result": "valid", "flags": []}]}]}