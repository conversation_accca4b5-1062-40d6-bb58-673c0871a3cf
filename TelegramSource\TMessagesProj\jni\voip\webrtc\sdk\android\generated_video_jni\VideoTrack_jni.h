// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/VideoTrack

#ifndef org_webrtc_VideoTrack_JNI
#define org_webrtc_VideoTrack_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_VideoTrack[];
const char kClassPath_org_webrtc_VideoTrack[] = "org/webrtc/VideoTrack";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_VideoTrack_clazz(nullptr);
#ifndef org_webrtc_VideoTrack_clazz_defined
#define org_webrtc_VideoTrack_clazz_defined
inline jclass org_webrtc_VideoTrack_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_VideoTrack,
      &g_org_webrtc_VideoTrack_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {

static void JNI_VideoTrack_AddSink(JNIEnv* env, jlong track,
    jlong nativeSink);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_VideoTrack_nativeAddSink(
    JNIEnv* env,
    jclass jcaller,
    jlong track,
    jlong nativeSink) {
  return JNI_VideoTrack_AddSink(env, track, nativeSink);
}

static void JNI_VideoTrack_FreeSink(JNIEnv* env, jlong sink);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_VideoTrack_nativeFreeSink(
    JNIEnv* env,
    jclass jcaller,
    jlong sink) {
  return JNI_VideoTrack_FreeSink(env, sink);
}

static void JNI_VideoTrack_RemoveSink(JNIEnv* env, jlong track,
    jlong nativeSink);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_VideoTrack_nativeRemoveSink(
    JNIEnv* env,
    jclass jcaller,
    jlong track,
    jlong nativeSink) {
  return JNI_VideoTrack_RemoveSink(env, track, nativeSink);
}

static jlong JNI_VideoTrack_WrapSink(JNIEnv* env, const jni_zero::JavaParamRef<jobject>& sink);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_VideoTrack_nativeWrapSink(
    JNIEnv* env,
    jclass jcaller,
    jobject sink) {
  return JNI_VideoTrack_WrapSink(env, jni_zero::JavaParamRef<jobject>(env, sink));
}


}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_VideoTrack_JNI
