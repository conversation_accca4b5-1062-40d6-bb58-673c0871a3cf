[{"cmd": ["crypto_test"], "shard": true}, {"cmd": ["crypto_test", "--gtest_also_run_disabled_tests", "--gtest_filter=BNTest.DISABLED_WycheproofPrimality"]}, {"cmd": ["crypto_test", "--gtest_also_run_disabled_tests", "--gtest_filter=RSATest.DISABLED_BlindingCacheConcurrency"], "skip_sde": true}, {"cmd": ["urandom_test"]}, {"comment": "Without RDRAND", "cmd": ["urandom_test"], "env": ["OPENSSL_ia32cap=~0x4000000000000000"]}, {"comment": "Potentially with RDRAND, but not Intel", "cmd": ["urandom_test"], "env": ["OPENSSL_ia32cap=~0x0000000040000000"]}, {"comment": "Potentially with RDRAND, and forced to Intel", "cmd": ["urandom_test"], "env": ["OPENSSL_ia32cap=|0x0000000040000000"]}, {"comment": "No RDRAND and without WIP<PERSON>ONFORK", "cmd": ["urandom_test"], "env": ["OPENSSL_ia32cap=~0x4000000000000000", "BORINGSSL_IGNORE_MADV_WIPEONFORK=1"]}, {"comment": "Potentially with RDRAND, but not Intel, and no WIPEONFORK", "cmd": ["urandom_test"], "env": ["OPENSSL_ia32cap=~0x0000000040000000", "BORINGSSL_IGNORE_MADV_WIPEONFORK=1"]}, {"cmd": ["crypto_test", "--fork_unsafe_buffering", "--gtest_filter=RandTest.*:-RandTest.Fork"]}, {"cmd": ["decrepit_test"], "shard": true}, {"cmd": ["ssl_test"], "shard": true}, {"cmd": ["pki_test"], "shard": true}]