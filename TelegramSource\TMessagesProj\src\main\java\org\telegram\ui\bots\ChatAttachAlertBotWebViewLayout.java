package org.telegram.ui.bots;

import static org.telegram.messenger.AndroidUtilities.dp;
import static org.telegram.ui.Components.Bulletin.DURATION_PROLONG;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ValueAnimator;
import android.content.Context;
import android.content.res.Configuration;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.RenderNode;
import android.os.Build;
import android.os.Bundle;
import android.text.TextPaint;
import android.util.Log;
import android.view.GestureDetector;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.math.MathUtils;
import androidx.core.view.GestureDetectorCompat;
import androidx.dynamicanimation.animation.FloatValueHolder;
import androidx.dynamicanimation.animation.SpringAnimation;
import androidx.dynamicanimation.animation.SpringForce;
import androidx.recyclerview.widget.ChatListItemAnimator;

import org.json.JSONObject;
import org.telegram.messenger.AndroidUtilities;
import org.telegram.messenger.ContactsController;
import org.telegram.messenger.Emoji;
import org.telegram.messenger.GenericProvider;
import org.telegram.messenger.LocaleController;
import org.telegram.messenger.MediaDataController;
import org.telegram.messenger.MessagesController;
import org.telegram.messenger.NotificationCenter;
import org.telegram.messenger.R;
import org.telegram.messenger.SendMessagesHelper;
import org.telegram.messenger.UserObject;
import org.telegram.messenger.browser.Browser;
import org.telegram.tgnet.ConnectionsManager;
import org.telegram.tgnet.TLRPC;
import org.telegram.ui.ActionBar.ActionBar;
import org.telegram.ui.ActionBar.ActionBarMenu;
import org.telegram.ui.ActionBar.ActionBarMenuItem;
import org.telegram.ui.ActionBar.ActionBarMenuSubItem;
import org.telegram.ui.ActionBar.AlertDialog;
import org.telegram.ui.ActionBar.BaseFragment;
import org.telegram.ui.ActionBar.Theme;
import org.telegram.ui.ChatActivity;
import org.telegram.ui.Components.Bulletin;
import org.telegram.ui.Components.BulletinFactory;
import org.telegram.ui.Components.ChatAttachAlert;
import org.telegram.ui.Components.CubicBezierInterpolator;
import org.telegram.ui.Components.LayoutHelper;
import org.telegram.ui.Components.SimpleFloatPropertyCompat;
import org.telegram.ui.web.BotWebViewContainer;

public class ChatAttachAlertBotWebViewLayout extends ChatAttachAlert.AttachAlertLayout implements NotificationCenter.NotificationCenterDelegate {
    private final static int POLL_PERIOD = 60000;

    private BotWebViewContainer webViewContainer;
    private ValueAnimator webViewScrollAnimator;

    private boolean ignoreLayout;

    private long botId;
    private long peerId;
    private long queryId;
    private boolean silent;
    private int replyToMsgId;
    private int currentAccount;
    private String startCommand;

    private boolean needReload;
    private WebProgressView progressView;
    private WebViewSwipeContainer swipeContainer;
    private ActionBarMenuItem otherItem;
    public ActionBarMenuSubItem settingsItem;
    private ActionBarMenuSubItem addToHomeScreenItem;

    private int measureOffsetY;

    private long lastSwipeTime;

    private boolean ignoreMeasure;
    private boolean isBotButtonAvailable;

    private boolean hasCustomBackground;
    private int customBackground;

    private boolean needCloseConfirmation;

    private boolean destroyed;
    private Runnable pollRunnable = () -> {
        if (!destroyed) {
            TLRPC.TL_messages_prolongWebView prolongWebView = new TLRPC.TL_messages_prolongWebView();
            prolongWebView.bot = MessagesController.getInstance(currentAccount).getInputUser(botId);
            prolongWebView.peer = MessagesController.getInstance(currentAccount).getInputPeer(peerId);
            prolongWebView.query_id = queryId;
            prolongWebView.silent = silent;
            if (replyToMsgId != 0) {
                prolongWebView.reply_to = SendMessagesHelper.getInstance(currentAccount).createReplyInput(replyToMsgId);
                prolongWebView.flags |= 1;
            }

            if (peerId < 0) {
                TLRPC.ChatFull chatFull = MessagesController.getInstance(currentAccount).getChatFull(-peerId);
                if (chatFull != null) {
                    TLRPC.Peer peer = chatFull.default_send_as;
                    if (peer != null) {
                        prolongWebView.send_as = MessagesController.getInstance(currentAccount).getInputPeer(peer);
                        prolongWebView.flags |= 8192;
                    }
                }
            }

            ConnectionsManager.getInstance(currentAccount).sendRequest(prolongWebView, (response, error) -> AndroidUtilities.runOnUIThread(() -> {
                if (destroyed) {
                    return;
                }
                if (error != null) {
                    parentAlert.dismiss();
                } else {
                    AndroidUtilities.runOnUIThread(this.pollRunnable, POLL_PERIOD);
                }
            }));
        }
    };
    private boolean hasCustomActionBarBackground;
    private int customActionBarBackground;

    @Override
    public void onMenuItemClick(int id) {
        if (id == -1) {
            if (!webViewContainer.onBackPressed()) {
                onCheckDismissByUser();
            }
        } else if (id == R.id.menu_open_bot) {
            Bundle bundle = new Bundle();
            bundle.putLong("user_id", botId);
            parentAlert.baseFragment.presentFragment(new ChatActivity(bundle));
            parentAlert.dismiss();
        } else if (id == R.id.menu_reload_page) {
            if (webViewContainer.getWebView() != null) {
                webViewContainer.getWebView().animate().cancel();
                webViewContainer.getWebView().animate().alpha(0).start();
            }

            progressView.setLoadProgress(0);
            progressView.setAlpha(1f);
            progressView.setVisibility(VISIBLE);

            webViewContainer.setBotUser(MessagesController.getInstance(currentAccount).getUser(botId));
            webViewContainer.loadFlickerAndSettingsItem(currentAccount, botId, settingsItem);
            webViewContainer.reload();
        } else if (id == R.id.menu_delete_bot) {
            for (TLRPC.TL_attachMenuBot bot : MediaDataController.getInstance(currentAccount).getAttachMenuBots().bots) {
                if (bot.bot_id == botId) {
                    parentAlert.onLongClickBotButton(bot, MessagesController.getInstance(currentAccount).getUser(botId));
                    break;
                }
            }
        } else if (id == R.id.menu_settings) {
            webViewContainer.onSettingsButtonPressed();
        } else if (id == R.id.menu_add_to_home_screen_bot) {
            MediaDataController.getInstance(currentAccount).installShortcut(botId, MediaDataController.SHORTCUT_TYPE_ATTACHED_BOT);
        } else if (id == R.id.menu_tos_bot) {
            Browser.openUrl(getContext(), LocaleController.getString(R.string.BotWebViewToSLink));
        }
    }

    public ChatAttachAlertBotWebViewLayout(ChatAttachAlert alert, Context context, Theme.ResourcesProvider resourcesProvider) {
        super(alert, context, resourcesProvider);

        ActionBarMenu menu = parentAlert.actionBar.createMenu();
        otherItem = menu.addItem(0, R.drawable.ic_ab_other);
        otherItem.addSubItem(R.id.menu_open_bot, R.drawable.msg_bot, LocaleController.getString(R.string.BotWebViewOpenBot));
        settingsItem = otherItem.addSubItem(R.id.menu_settings, R.drawable.msg_settings, LocaleController.getString(R.string.BotWebViewSettings));
        settingsItem.setVisibility(View.GONE);
        otherItem.addSubItem(R.id.menu_reload_page, R.drawable.msg_retry, LocaleController.getString(R.string.BotWebViewReloadPage));
        addToHomeScreenItem = otherItem.addSubItem(R.id.menu_add_to_home_screen_bot, R.drawable.msg_home, LocaleController.getString(R.string.AddShortcut));
        addToHomeScreenItem.setVisibility(View.GONE);
        otherItem.addSubItem(R.id.menu_tos_bot, R.drawable.menu_intro, LocaleController.getString(R.string.BotWebViewToS));
        otherItem.addSubItem(R.id.menu_delete_bot, R.drawable.msg_delete, LocaleController.getString(R.string.BotWebViewDeleteBot));

        webViewContainer = new BotWebViewContainer(context, resourcesProvider, getThemedColor(Theme.key_dialogBackground), true) {
            @Override
            public boolean dispatchTouchEvent(MotionEvent ev) {
                if (ev.getAction() == MotionEvent.ACTION_DOWN) {
                    if (!isBotButtonAvailable) {
                        isBotButtonAvailable = true;
                        webViewContainer.restoreButtonData();
                    }
                }
                return super.dispatchTouchEvent(ev);
            }

            @Override
            public void onWebViewCreated(MyWebView webView) {
                super.onWebViewCreated(webView);
                swipeContainer.setWebView(webView);
            }
        };
        swipeContainer = new WebViewSwipeContainer(context) {
            @Override
            protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
                super.onMeasure(widthMeasureSpec, MeasureSpec.makeMeasureSpec(MeasureSpec.getSize(heightMeasureSpec) - ActionBar.getCurrentActionBarHeight() - dp(84) + measureOffsetY, MeasureSpec.EXACTLY));
            }
        };
        swipeContainer.addView(webViewContainer, LayoutHelper.createFrame(LayoutHelper.MATCH_PARENT, LayoutHelper.MATCH_PARENT));
        swipeContainer.setScrollListener(() -> {
            parentAlert.updateLayout(this, true, 0);
            webViewContainer.invalidateViewPortHeight();
            lastSwipeTime = System.currentTimeMillis();
        });
        swipeContainer.setScrollEndListener(()-> webViewContainer.invalidateViewPortHeight(true));
        swipeContainer.setDelegate(byTap -> {
            if (!onCheckDismissByUser()) {
                swipeContainer.stickTo(0);
            }
        });
        swipeContainer.setIsKeyboardVisible(obj -> parentAlert.sizeNotifierFrameLayout.getKeyboardHeight() >= dp(20));

        addView(swipeContainer, LayoutHelper.createFrame(LayoutHelper.MATCH_PARENT, LayoutHelper.MATCH_PARENT));
        addView(progressView = new WebProgressView(context, resourcesProvider), LayoutHelper.createFrame(LayoutHelper.MATCH_PARENT, LayoutHelper.WRAP_CONTENT, Gravity.BOTTOM, 0, 0, 0, 84));

        webViewContainer.setWebViewProgressListener(progress -> {
            progressView.setLoadProgressAnimated(progress);
            if (progress == 1f) {
                ValueAnimator animator = ValueAnimator.ofFloat(1, 0).setDuration(200);
                animator.setInterpolator(CubicBezierInterpolator.DEFAULT);
                animator.addUpdateListener(animation -> progressView.setAlpha((Float) animation.getAnimatedValue()));
                animator.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        progressView.setVisibility(GONE);
                    }
                });
                animator.start();

                requestEnableKeyboard();
            }
        });

        NotificationCenter.getGlobalInstance().addObserver(this, NotificationCenter.didSetNewTheme);
    }

    public void setNeedCloseConfirmation(boolean needCloseConfirmation) {
        this.needCloseConfirmation = needCloseConfirmation;
    }

    public void setAllowSwipes(boolean allowSwipes) {
        swipeContainer.setAllowSwipes(allowSwipes);
    }

    @Override
    public boolean onDismissWithTouchOutside() {
        onCheckDismissByUser();
        return false;
    }

    public boolean onCheckDismissByUser() {
        if (needCloseConfirmation) {
            String botName = null;
            TLRPC.User user = MessagesController.getInstance(currentAccount).getUser(botId);
            if (user != null) {
                botName = ContactsController.formatName(user.first_name, user.last_name);
            }
            AlertDialog dialog = new AlertDialog.Builder(getContext())
                    .setTitle(botName)
                    .setMessage(LocaleController.getString(R.string.BotWebViewChangesMayNotBeSaved))
                    .setPositiveButton(LocaleController.getString(R.string.BotWebViewCloseAnyway), (dialog2, which) -> parentAlert.dismiss())
                    .setNegativeButton(LocaleController.getString(R.string.Cancel), null)
                    .create();
            dialog.show();
            TextView textView = (TextView) dialog.getButton(AlertDialog.BUTTON_POSITIVE);
            textView.setTextColor(getThemedColor(Theme.key_text_RedBold));
            return false;
        } else {
            parentAlert.dismiss();
            return true;
        }
    }

    public void setCustomBackground(int customBackground) {
        this.customBackground = customBackground;
        hasCustomBackground = true;
    }

    @Override
    public boolean hasCustomBackground() {
        return hasCustomBackground;
    }

    @Override
    public int getCustomBackground() {
        return customBackground;
    }

    @Override
    public boolean hasCustomActionBarBackground() {
        return hasCustomActionBarBackground;
    }

    @Override
    public int getCustomActionBarBackground() {
        return customActionBarBackground;
    }

    public void setCustomActionBarBackground(int customActionBarBackground) {
        hasCustomActionBarBackground = true;
        this.customActionBarBackground = customActionBarBackground;
    }

    public boolean canExpandByRequest() {
        return /* System.currentTimeMillis() - lastSwipeTime > 1000 && */ !swipeContainer.isSwipeInProgress();
    }

    public void setMeasureOffsetY(int measureOffsetY) {
        this.measureOffsetY = measureOffsetY;
        swipeContainer.requestLayout();
    }

    public void disallowSwipeOffsetAnimation() {
        swipeContainer.setSwipeOffsetAnimationDisallowed(true);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        if (ignoreMeasure) {
            setMeasuredDimension(getMeasuredWidth(), getMeasuredHeight());
        } else {
            super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        }
    }

    @Override
    public void onPanTransitionStart(boolean keyboardVisible, int contentHeight) {
        if (!keyboardVisible) {
            return;
        }

        webViewContainer.setViewPortByMeasureSuppressed(true);

        boolean doNotScroll = false;
        float openOffset = -swipeContainer.getOffsetY() + swipeContainer.getTopActionBarOffsetY();
        if (swipeContainer.getSwipeOffsetY() != openOffset) {
            swipeContainer.stickTo(openOffset);
            doNotScroll = true;
        }

        int oldh = contentHeight + parentAlert.sizeNotifierFrameLayout.measureKeyboardHeight();
        setMeasuredDimension(getMeasuredWidth(), contentHeight);
        ignoreMeasure = true;
        swipeContainer.setSwipeOffsetAnimationDisallowed(true);

        if (!doNotScroll) {
            if (webViewScrollAnimator != null) {
                webViewScrollAnimator.cancel();
                webViewScrollAnimator = null;
            }

            if (webViewContainer.getWebView() != null) {
                int fromY = webViewContainer.getWebView().getScrollY();
                int toY = fromY + (oldh - contentHeight);
                webViewScrollAnimator = ValueAnimator.ofInt(fromY, toY).setDuration(250);
                webViewScrollAnimator.setInterpolator(ChatListItemAnimator.DEFAULT_INTERPOLATOR);
                webViewScrollAnimator.addUpdateListener(animation -> {
                    int val = (int) animation.getAnimatedValue();
                    if (webViewContainer.getWebView() != null) {
                        webViewContainer.getWebView().setScrollY(val);
                    }
                });
                webViewScrollAnimator.addListener(new AnimatorListenerAdapter() {
                    @Override
                    public void onAnimationEnd(Animator animation) {
                        if (webViewContainer.getWebView() != null) {
                            webViewContainer.getWebView().setScrollY(toY);
                        }
                        if (animation == webViewScrollAnimator) {
                            webViewScrollAnimator = null;
                        }
                    }
                });
                webViewScrollAnimator.start();
            }
        }
    }

    @Override
    public void onPanTransitionEnd() {
        ignoreMeasure = false;
        swipeContainer.setSwipeOffsetAnimationDisallowed(false);
        webViewContainer.setViewPortByMeasureSuppressed(false);
        requestLayout();
    }

    @Override
    public void onShow(ChatAttachAlert.AttachAlertLayout previousLayout) {
        CharSequence title = UserObject.getUserName(MessagesController.getInstance(currentAccount).getUser(botId));
        try {
            TextPaint tp = new TextPaint();
            tp.setTextSize(dp(20));
            title = Emoji.replaceEmoji(title, tp.getFontMetricsInt(), false);
        } catch (Exception ignore) {}
        parentAlert.actionBar.setTitle(title);
        swipeContainer.setSwipeOffsetY(0);
        if (webViewContainer.getWebView() != null) {
            webViewContainer.getWebView().scrollTo(0, 0);
        }
        if (parentAlert.getBaseFragment() != null) {
            webViewContainer.setParentActivity(parentAlert.getBaseFragment().getParentActivity());
        }
        otherItem.setVisibility(VISIBLE);

        if (!webViewContainer.isBackButtonVisible()) {
            AndroidUtilities.updateImageViewImageAnimated(parentAlert.actionBar.getBackButton(), R.drawable.ic_close_white);
        }
    }

    @Override
    public void onShown() {
        if (webViewContainer.isPageLoaded()) {
            requestEnableKeyboard();
        }

        swipeContainer.setSwipeOffsetAnimationDisallowed(false);
        AndroidUtilities.runOnUIThread(() -> webViewContainer.restoreButtonData());
    }

    private void requestEnableKeyboard() {
        BaseFragment fragment = parentAlert.getBaseFragment();
        if (fragment instanceof ChatActivity && ((ChatActivity) fragment).contentView.measureKeyboardHeight() > dp(20)) {
            AndroidUtilities.hideKeyboard(parentAlert.baseFragment.getFragmentView());
            AndroidUtilities.runOnUIThread(this::requestEnableKeyboard, 250);
            return;
        }

        parentAlert.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE | WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE);
        setFocusable(true);
        parentAlert.setFocusable(true);
    }

    @Override
    public void onHidden() {
        super.onHidden();

        parentAlert.setFocusable(false);
        parentAlert.getWindow().setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_NOTHING);
    }

    @Override
    public int getCurrentItemTop() {
        return (int) (swipeContainer.getSwipeOffsetY() + swipeContainer.getOffsetY());
    }

    @Override
    public void setTranslationY(float translationY) {
        super.setTranslationY(translationY);
        parentAlert.getSheetContainer().invalidate();
    }

    public String getStartCommand() {
        return startCommand;
    }

    public void requestWebView(int currentAccount, long peerId, long botId, boolean silent, int replyToMsgId) {
        requestWebView(currentAccount, peerId, botId, silent, replyToMsgId, null);
    }

    public void requestWebView(int currentAccount, long peerId, long botId, boolean silent, int replyToMsgId, String startCommand) {
        this.currentAccount = currentAccount;
        this.peerId = peerId;
        this.botId = botId;
        this.silent = silent;
        this.replyToMsgId = replyToMsgId;
        this.startCommand = startCommand;
        if (addToHomeScreenItem != null) {
            if (MediaDataController.getInstance(currentAccount).canCreateAttachedMenuBotShortcut(botId)) {
                addToHomeScreenItem.setVisibility(View.VISIBLE);
            } else {
                addToHomeScreenItem.setVisibility(View.GONE);
            }
        }

        webViewContainer.setBotUser(MessagesController.getInstance(currentAccount).getUser(botId));
        webViewContainer.loadFlickerAndSettingsItem(currentAccount, botId, settingsItem);

        TLRPC.TL_messages_requestWebView req = new TLRPC.TL_messages_requestWebView();
        req.peer = MessagesController.getInstance(currentAccount).getInputPeer(peerId);
        req.bot = MessagesController.getInstance(currentAccount).getInputUser(botId);
        req.silent = silent;
        req.platform = "android";

        if (peerId < 0) {
            TLRPC.ChatFull chatFull = MessagesController.getInstance(currentAccount).getChatFull(-peerId);
            if (chatFull != null) {
                TLRPC.Peer peer = chatFull.default_send_as;
                if (peer != null) {
                    req.send_as = MessagesController.getInstance(currentAccount).getInputPeer(peer);
                    req.flags |= 8192;
                }
            }
        }
        if (startCommand != null) {
            req.start_param = startCommand;
            req.flags |= 8;
        }

        if (replyToMsgId != 0) {
            req.reply_to = SendMessagesHelper.getInstance(currentAccount).createReplyInput(replyToMsgId);
            req.flags |= 1;
        }

        JSONObject theme_params = BotWebViewSheet.makeThemeParams(resourcesProvider);
        if (theme_params != null) {
            req.theme_params = new TLRPC.TL_dataJSON();
            req.theme_params.data = theme_params.toString();
            req.flags |= 4;
        }

        ConnectionsManager.getInstance(currentAccount).sendRequest(req, (response, error) -> AndroidUtilities.runOnUIThread(() -> {
            if (response instanceof TLRPC.TL_webViewResultUrl) {
                TLRPC.TL_webViewResultUrl resultUrl = (TLRPC.TL_webViewResultUrl) response;
                queryId = resultUrl.query_id;
                webViewContainer.loadUrl(currentAccount, resultUrl.url);

                AndroidUtilities.runOnUIThread(pollRunnable);
            }
        }));

        NotificationCenter.getInstance(currentAccount).addObserver(this, NotificationCenter.webViewResultSent);
    }

    @Override
    public void onDestroy() {
        NotificationCenter.getInstance(currentAccount).removeObserver(this, NotificationCenter.webViewResultSent);
        NotificationCenter.getGlobalInstance().removeObserver(this, NotificationCenter.didSetNewTheme);

        ActionBarMenu menu = parentAlert.actionBar.createMenu();
        otherItem.removeAllSubItems();
        menu.removeView(otherItem);

        webViewContainer.destroyWebView();
        destroyed = true;

        AndroidUtilities.cancelRunOnUIThread(pollRunnable);
    }

    @Override
    public void onHide() {
        super.onHide();
        otherItem.setVisibility(GONE);
        isBotButtonAvailable = false;
        if (!webViewContainer.isBackButtonVisible()) {
            AndroidUtilities.updateImageViewImageAnimated(parentAlert.actionBar.getBackButton(), R.drawable.ic_ab_back);
        }
        parentAlert.actionBar.setBackgroundColor(getThemedColor(Theme.key_windowBackgroundWhite));

        if (webViewContainer.hasUserPermissions()) {
            webViewContainer.destroyWebView();
            needReload = true;
        }
    }

    public boolean needReload() {
        if (needReload) {
            needReload = false;
            return true;
        }
        return false;
    }

    @Override
    public int getListTopPadding() {
        return (int) swipeContainer.getOffsetY();
    }

    @Override
    public int getFirstOffset() {
        return getListTopPadding() + dp(56);
    }

    @Override
    public void onPreMeasure(int availableWidth, int availableHeight) {
        int padding;
        if (!AndroidUtilities.isTablet() && AndroidUtilities.displaySize.x > AndroidUtilities.displaySize.y) {
            padding = (int) (availableHeight / 3.5f);
        } else {
            padding = (availableHeight / 5 * 2);
        }
        parentAlert.setAllowNestedScroll(true);

        if (padding < 0) {
            padding = 0;
        }
        if (swipeContainer.getOffsetY() != padding) {
            ignoreLayout = true;
            swipeContainer.setOffsetY(padding);
            ignoreLayout = false;
        }
    }

    @Override
    public int getButtonsHideOffset() {
        return (int) swipeContainer.getTopActionBarOffsetY() + dp(12);
    }

    @Override
    public boolean onBackPressed() {
        if (webViewContainer.onBackPressed()) {
            return true;
        }
        onCheckDismissByUser();
        return true;
    }

    @Override
    public void requestLayout() {
        if (ignoreLayout) {
            return;
        }
        super.requestLayout();
    }

    @Override
    public void scrollToTop() {
        swipeContainer.stickTo(-swipeContainer.getOffsetY() + swipeContainer.getTopActionBarOffsetY());
    }

    @Override
    public boolean shouldHideBottomButtons() {
        return false;
    }

    @Override
    public int needsActionBar() {
        return 1;
    }

    public BotWebViewContainer getWebViewContainer() {
        return webViewContainer;
    }

    public void setDelegate(BotWebViewContainer.Delegate delegate) {
        webViewContainer.setDelegate(delegate);
    }

    public boolean isBotButtonAvailable() {
        return isBotButtonAvailable;
    }

    @Override
    public void didReceivedNotification(int id, int account, Object... args) {
        if (id == NotificationCenter.webViewResultSent) {
            long queryId = (long) args[0];

            if (this.queryId == queryId) {
                webViewContainer.destroyWebView();
                needReload = true;
                parentAlert.dismiss();
            }
        } else if (id == NotificationCenter.didSetNewTheme) {
            webViewContainer.updateFlickerBackgroundColor(getThemedColor(Theme.key_dialogBackground));
        }
    }

    public void showJustAddedBulletin() {
        TLRPC.User user = MessagesController.getInstance(currentAccount).getUser(botId);
        TLRPC.TL_attachMenuBot currentBot = null;
        for (TLRPC.TL_attachMenuBot bot : MediaDataController.getInstance(currentAccount).getAttachMenuBots().bots) {
            if (bot.bot_id == botId) {
                currentBot = bot;
                break;
            }
        }
        if (currentBot == null) {
            return;
        }
        String str;
        if (currentBot.show_in_side_menu && currentBot.show_in_attach_menu) {
            str = LocaleController.formatString("BotAttachMenuShortcatAddedAttachAndSide", R.string.BotAttachMenuShortcatAddedAttachAndSide, user.first_name);
        } else if (currentBot.show_in_side_menu) {
            str = LocaleController.formatString("BotAttachMenuShortcatAddedSide", R.string.BotAttachMenuShortcatAddedSide, user.first_name);
        } else {
            str = LocaleController.formatString("BotAttachMenuShortcatAddedAttach", R.string.BotAttachMenuShortcatAddedAttach, user.first_name);
        }
        AndroidUtilities.runOnUIThread(() -> {
        BulletinFactory.of(parentAlert.getContainer(), resourcesProvider)
                .createSimpleBulletin(R.raw.contact_check, AndroidUtilities.replaceTags(str))
                .setDuration(DURATION_PROLONG)
                .show(true);
        }, 200);
    }

    public static class WebViewSwipeContainer extends FrameLayout {
        public final static SimpleFloatPropertyCompat<WebViewSwipeContainer> SWIPE_OFFSET_Y = new SimpleFloatPropertyCompat<>("swipeOffsetY", WebViewSwipeContainer::getSwipeOffsetY, WebViewSwipeContainer::setSwipeOffsetY);

        private Object renderNode;
        public Object getRenderNode() {
            if (renderNode == null) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                    renderNode = new RenderNode("WebViewSwipeContainer");
                }
            }
            return renderNode;
        }

        @Override
        protected void dispatchDraw(@NonNull Canvas canvas) {
            if (canvas.isHardwareAccelerated()) {
                Canvas drawingCanvas = canvas;
                if (renderNode != null) {
                    final RenderNode node = (RenderNode) renderNode;
                    node.setPosition(0, 0, getWidth(), getHeight());
                    drawingCanvas = node.beginRecording();
                }
                super.dispatchDraw(drawingCanvas);
                if (renderNode != null) {
                    final RenderNode node = (RenderNode) renderNode;
                    node.endRecording();
                    canvas.drawRenderNode(node);
                }
            } else {
                super.dispatchDraw(canvas);
            }
        }

        private final GestureDetectorCompat gestureDetector;
        public boolean isScrolling;
        private boolean isSwipeDisallowed;

        public float topActionBarOffsetY = ActionBar.getCurrentActionBarHeight();
        public float offsetY = 0;
        private float pendingOffsetY = -1;
        private float pendingSwipeOffsetY = Integer.MIN_VALUE;
        private float swipeOffsetY;
        private boolean isSwipeOffsetAnimationDisallowed;

        private SpringAnimation offsetYAnimator;

        private boolean flingInProgress;

        private BotWebViewContainer.MyWebView webView;

        private Runnable scrollListener;
        private Runnable scrollEndListener;
        private Delegate delegate;

        private SpringAnimation scrollAnimator;

        private int swipeStickyRange;

        private GenericProvider<Void, Boolean> isKeyboardVisible = obj -> false;

        private boolean fullsize;
        public boolean opened;
        public void setFullSize(boolean fullsize) {
            if (this.fullsize != fullsize) {
                this.fullsize = fullsize;
                if (fullsize) {
                    if (opened) {
                        stickTo(-getOffsetY() + getTopActionBarOffsetY());
                    }
                } else {
                    stickTo(0);
                }
            }
        }

        public boolean isFullSize() {
            return fullsize;
        }

        private boolean allowFullSizeSwipe;
        public void setAllowFullSizeSwipe(boolean value) {
            allowFullSizeSwipe = value;
        }

        private boolean allowSwipes = true;
        public void setAllowSwipes(boolean allowSwipes) {
            if (this.allowSwipes != allowSwipes) {
                this.allowSwipes = allowSwipes;
            }
        }
        public boolean isAllowedSwipes() {
            return allowSwipes;
        }

        public boolean shouldWaitWebViewScroll;
        public boolean allowedScrollX, allowedScrollY;
        public void setShouldWaitWebViewScroll(boolean value) {
            shouldWaitWebViewScroll = value;
        }
        public void allowThisScroll(boolean x, boolean y) {
            allowedScrollX = x;
            allowedScrollY = y;
        }

        public boolean allowingScroll(boolean x) {
            return webView == null || !webView.injectedJS || (x ? allowedScrollX : allowedScrollY);
        }

        public WebViewSwipeContainer(@NonNull Context context) {
            super(context);

            int touchSlop = ViewConfiguration.get(context).getScaledTouchSlop();
            gestureDetector = new GestureDetectorCompat(context, new GestureDetector.SimpleOnGestureListener() {
                @Override
                public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
                    if (isSwipeDisallowed || !allowSwipes || fullsize && !allowFullSizeSwipe || (shouldWaitWebViewScroll && !allowingScroll(false))) {
                        return false;
                    }
                    final float distance = AndroidUtilities.distance(e1.getX(), e1.getY(), e2.getX(), e2.getY());
                    final float time = e2.getEventTime() - e1.getEventTime();
                    if (velocityY >= dp(650) && (distance > dp(200) || (time > 250)) && (webView == null || webView.getScrollY() == 0)) {
                        flingInProgress = true;

                        if (swipeOffsetY >= swipeStickyRange || fullsize) {
                            if (fullsize && allowFullSizeSwipe && (drawnSwipeOffsetY == -offsetY + topActionBarOffsetY || swipeOffsetY <= -swipeStickyRange && velocityY < dp(1200))) {
                                stickTo(-offsetY + topActionBarOffsetY);
                            } else if (delegate != null) {
                                delegate.onDismiss(false);
                            }
                        } else {
                            stickTo(0);
                        }
                        return true;
                    } else if (velocityY <= -700 && swipeOffsetY > -offsetY + topActionBarOffsetY) {
                        flingInProgress = true;
                        stickTo(-offsetY + topActionBarOffsetY);
                        return true;
                    }
                    return false;
                }

                @Override
                public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
                    distanceY = cap(distanceY);
                    if (!isScrolling && !isSwipeDisallowed && allowSwipes && (!shouldWaitWebViewScroll || swipeOffsetY != -offsetY + topActionBarOffsetY || allowingScroll(false))) {
                        if (isKeyboardVisible.provide(null) && swipeOffsetY == -offsetY + topActionBarOffsetY) {
                            isSwipeDisallowed = true;
                        } else if (Math.abs(distanceY) >= touchSlop && Math.abs(distanceY) * 1.5f >= Math.abs(distanceX) && (swipeOffsetY != -offsetY + topActionBarOffsetY || webView == null || distanceY < 0 && webView.getScrollY() == 0)) {
                            isScrolling = true;

                            MotionEvent ev = MotionEvent.obtain(0, 0, MotionEvent.ACTION_CANCEL, 0, 0, 0);
                            for (int i = 0; i < getChildCount(); i++) {
                                getChildAt(i).dispatchTouchEvent(ev);
                            }
                            ev.recycle();

                            return true;
                        } else if (webView != null && webView.canScrollHorizontally(distanceX >= 0 ? 1 : -1) || Math.abs(distanceX) >= touchSlop && Math.abs(distanceX) * 1.5f >= Math.abs(distanceY)) {
                            isSwipeDisallowed = true;
                        }
                    }
                    if (isScrolling) {
                        if (distanceY < 0) {
                            if (swipeOffsetY > -offsetY + topActionBarOffsetY) {
                                swipeOffsetY -= distanceY;
                            } else if (webView != null) {
                                float newWebScrollY = webView.getScrollY() + distanceY;
                                webView.setScrollY((int) MathUtils.clamp(newWebScrollY, 0, Math.max(webView.getContentHeight(), webView.getHeight()) - topActionBarOffsetY));

                                if (newWebScrollY < 0) {
                                    swipeOffsetY -= newWebScrollY;
                                }
                            } else {
                                swipeOffsetY -= distanceY;
                            }
                        } else if (distanceY > 0) {
                            swipeOffsetY -= distanceY;

                            if (webView != null && swipeOffsetY < -offsetY + topActionBarOffsetY) {
                                float newWebScrollY = webView.getScrollY() - (swipeOffsetY + offsetY - topActionBarOffsetY);
                                webView.setScrollY((int) MathUtils.clamp(newWebScrollY, 0, Math.max(webView.getContentHeight(), webView.getHeight()) - topActionBarOffsetY));
                            }
                        }

                        swipeOffsetY = MathUtils.clamp(swipeOffsetY, -offsetY + topActionBarOffsetY, getHeight() - offsetY + topActionBarOffsetY);
                        if (fullsize && !allowFullSizeSwipe) {
                            swipeOffsetY = Math.min(swipeOffsetY, -offsetY + topActionBarOffsetY);
                        }
                        invalidateTranslation();
                        return true;
                    }

                    return true;
                }
            });
            updateStickyRange();
        }

        private float drawnSwipeOffsetY;

        public void setIsKeyboardVisible(GenericProvider<Void, Boolean> isKeyboardVisible) {
            this.isKeyboardVisible = isKeyboardVisible;
        }

        @Override
        protected void onConfigurationChanged(Configuration newConfig) {
            super.onConfigurationChanged(newConfig);
            updateStickyRange();
        }

        private void updateStickyRange() {
            swipeStickyRange = AndroidUtilities.dp(AndroidUtilities.displaySize.x > AndroidUtilities.displaySize.y ? 8 : 64);
        }

        @Override
        public void requestDisallowInterceptTouchEvent(boolean disallowIntercept) {
            super.requestDisallowInterceptTouchEvent(disallowIntercept);

            if (disallowIntercept) {
                isSwipeDisallowed = true;
                isScrolling = false;
            }
        }

        public void setSwipeOffsetAnimationDisallowed(boolean swipeOffsetAnimationDisallowed) {
            isSwipeOffsetAnimationDisallowed = swipeOffsetAnimationDisallowed;
        }

        public void setScrollListener(Runnable scrollListener) {
            this.scrollListener = scrollListener;
        }

        public void setScrollEndListener(Runnable scrollEndListener) {
            this.scrollEndListener = scrollEndListener;
        }

        public void setWebView(BotWebViewContainer.MyWebView webView) {
            this.webView = webView;
        }

        public void setTopActionBarOffsetY(float topActionBarOffsetY) {
            this.topActionBarOffsetY = topActionBarOffsetY;
            invalidateTranslation();
        }

        public void setSwipeOffsetY(float swipeOffsetY) {
            this.swipeOffsetY = swipeOffsetY;
            invalidateTranslation();
        }

        public void setForceOffsetY(float offsetY) {
            this.offsetY = offsetY;
            invalidateTranslation();
        }

        public void setOffsetY(float offsetY) {
            if (pendingSwipeOffsetY != Integer.MIN_VALUE) {
                pendingOffsetY = offsetY;
                return;
            }

            if (offsetYAnimator != null) {
                offsetYAnimator.cancel();
            }

            float wasOffsetY = this.offsetY;
            float deltaOffsetY = offsetY - wasOffsetY;
            boolean wasOnTop = Math.abs(swipeOffsetY + wasOffsetY - topActionBarOffsetY) <= dp(1);
            if (!isSwipeOffsetAnimationDisallowed) {
                if (offsetYAnimator != null) {
                    offsetYAnimator.cancel();
                }
                offsetYAnimator = new SpringAnimation(new FloatValueHolder(wasOffsetY))
                        .setSpring(new SpringForce(offsetY)
                                .setStiffness(1400)
                                .setDampingRatio(SpringForce.DAMPING_RATIO_NO_BOUNCY))
                        .addUpdateListener((animation, value, velocity) -> {
                            this.offsetY = value;

                            float progress = deltaOffsetY == 0 ? 1f : (value - wasOffsetY) / deltaOffsetY;

                            if (wasOnTop) {
                                swipeOffsetY = MathUtils.clamp(
                                    swipeOffsetY - progress * Math.max(0, deltaOffsetY),
                                    -this.offsetY + topActionBarOffsetY,
                                    getHeight() - this.offsetY + topActionBarOffsetY
                                );
                            }
                            if (scrollAnimator != null && scrollAnimator.getSpring().getFinalPosition() == -wasOffsetY + topActionBarOffsetY) {
                                scrollAnimator.getSpring().setFinalPosition(-offsetY + topActionBarOffsetY);
                            }
                            invalidateTranslation();
                        })
                        .addEndListener((animation, canceled, value, velocity) -> {
                            offsetYAnimator = null;

                            if (!canceled) {
                                WebViewSwipeContainer.this.offsetY = offsetY;
                                invalidateTranslation();
                            } else {
                                pendingOffsetY = offsetY;
                            }
                        });
                offsetYAnimator.start();
            } else {
                this.offsetY = offsetY;

                if (wasOnTop) {
                    swipeOffsetY = MathUtils.clamp(
                        swipeOffsetY - Math.max(0, deltaOffsetY),
                        -this.offsetY + topActionBarOffsetY,
                        getHeight() - this.offsetY + topActionBarOffsetY
                    );
                }
                invalidateTranslation();
            }
        }

        private void updateDrawn() {
            drawnSwipeOffsetY = swipeOffsetY;
        }

        public void invalidateTranslation() {
            setTranslationY(Math.max(topActionBarOffsetY, offsetY + swipeOffsetY));
            AndroidUtilities.cancelRunOnUIThread(this::updateDrawn);
            AndroidUtilities.runOnUIThread(this::updateDrawn);
            if (scrollListener != null) {
                scrollListener.run();
            }

            if (Bulletin.getVisibleBulletin() != null) {
                Bulletin bulletin = Bulletin.getVisibleBulletin();
                bulletin.updatePosition();
            }
        }

        @Override
        public void setTranslationY(float translationY) {
            super.setTranslationY(translationY);
        }

        public float getTopActionBarOffsetY() {
            return topActionBarOffsetY;
        }

        public float getOffsetY() {
            return offsetY;
        }

        public float getSwipeOffsetY() {
            return swipeOffsetY;
        }

        public void setDelegate(Delegate delegate) {
            this.delegate = delegate;
        }

        private float sy = 0;
        private boolean scrolledOut = false;
        private final float minscroll = dp(60);
        private float cap(float dy) {
            if (scrolledOut) {
                return dy;
            }
            sy += dy;
            if (Math.abs(sy) > minscroll) {
                scrolledOut = true;
                if (sy > 0) {
                    dy = sy - minscroll;
                } else {
                    dy = sy + minscroll;
                }
            } else {
                dy = 0;
            }
            return dy;
        }

        public boolean stickToEdges = true;

        private long pressDownTime;
        private float pressDownX, pressDownY;
        @Override
        public boolean dispatchTouchEvent(MotionEvent ev) {
            if (isScrolling && ev.getActionIndex() != 0) {
                return false;
            }
            if (ev.getAction() == MotionEvent.ACTION_DOWN) {
                pressDownTime = ev.getEventTime();
                pressDownX = ev.getX();
                pressDownY = ev.getY();
                scrolledOut = false;
                sy = 0;
                if (shouldWaitWebViewScroll) {
                    allowedScrollX = false;
                    allowedScrollY = false;
                }
            }

            MotionEvent rawEvent = MotionEvent.obtain(ev);
            int index = ev.getActionIndex();
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                rawEvent.setLocation(ev.getRawX(index), ev.getRawY(index));
            } else {
                float offsetX = ev.getRawX() - ev.getX(), offsetY = ev.getRawY() - ev.getY();
                rawEvent.setLocation(ev.getX(index) + offsetX, ev.getY(index) + offsetY);
            }
            boolean detector = gestureDetector.onTouchEvent(rawEvent);
            rawEvent.recycle();

            if (ev.getAction() == MotionEvent.ACTION_UP || ev.getAction() == MotionEvent.ACTION_CANCEL) {
                final boolean wasScrolling = isScrolling;
                isSwipeDisallowed = false;
                isScrolling = false;

                if (fullsize && !allowFullSizeSwipe) {

                } else if (flingInProgress) {
                    flingInProgress = false;
                } else if (allowSwipes && (!shouldWaitWebViewScroll || swipeOffsetY != -offsetY + topActionBarOffsetY && allowingScroll(false))) {
                    if (swipeOffsetY <= -swipeStickyRange) {
                        if (stickToEdges) {
                            stickTo(-offsetY + topActionBarOffsetY);
                        }
                    } else if (swipeOffsetY > -swipeStickyRange && swipeOffsetY <= swipeStickyRange) {
                        if (stickToEdges) {
                            stickTo(0);
                        }
                    } else {
                        final float distance = AndroidUtilities.distance(ev.getX(), ev.getY(), pressDownX, pressDownY);
                        final long time = ev.getEventTime() - pressDownTime;
                        if (delegate != null && (time > 250 || distance > dp(200))) {
                            delegate.onDismiss(!wasScrolling);
                        } else if (stickToEdges) {
                            stickTo(-offsetY + topActionBarOffsetY);
                        }
                    }
                }
            }

            boolean superTouch = super.dispatchTouchEvent(ev);
            if (!superTouch && !detector && ev.getAction() == MotionEvent.ACTION_DOWN) {
                return true;
            }

            return superTouch || detector;
        }

        public void stickTo(float offset) {
            stickTo(offset, null);
        }

        public void cancelStickTo() {
            if (offsetYAnimator != null) {
                offsetYAnimator.cancel();
            }
            if (scrollAnimator != null) {
                scrollAnimator.cancel();
            }
        }

        public void stickTo(float offset, Runnable callback) {
            stickTo(offset, false, callback);
        }
        public void stickTo(float offset, boolean force, Runnable callback) {
            if (fullsize && !force) {
                offset = -getOffsetY() + getTopActionBarOffsetY();
            }
            if (swipeOffsetY == offset || scrollAnimator != null && scrollAnimator.getSpring().getFinalPosition() == offset) {
                if (callback != null) {
                    callback.run();
                }
                if (scrollEndListener != null) {
                    scrollEndListener.run();
                }
                return;
            }
            pendingSwipeOffsetY = offset;

            if (offsetYAnimator != null) {
                offsetYAnimator.cancel();
            }
            if (scrollAnimator != null) {
                scrollAnimator.cancel();
            }
            scrollAnimator = new SpringAnimation(this, SWIPE_OFFSET_Y, offset)
                    .setSpring(new SpringForce(offset)
                            .setStiffness(1200)
                            .setDampingRatio(SpringForce.DAMPING_RATIO_NO_BOUNCY))
                    .addEndListener((animation, canceled, value, velocity) -> {
                        if (animation == scrollAnimator) {
                            scrollAnimator = null;

                            if (callback != null) {
                                callback.run();
                            }

                            if (scrollEndListener != null) {
                                scrollEndListener.run();
                            }

                            if (pendingOffsetY != -1) {
                                boolean wasDisallowed = isSwipeOffsetAnimationDisallowed;
                                isSwipeOffsetAnimationDisallowed = true;
                                setOffsetY(pendingOffsetY);
                                pendingOffsetY = -1;
                                isSwipeOffsetAnimationDisallowed = wasDisallowed;
                            }
                            pendingSwipeOffsetY = Integer.MIN_VALUE;
                        }
                    });
            scrollAnimator.start();
        }

        public boolean isSwipeInProgress() {
            return isScrolling;
        }

        public interface Delegate {
            /**
             * Called to dismiss parent layout
             */
            void onDismiss(boolean byTap);
        }
    }

    public static class WebProgressView extends View {
        private final SimpleFloatPropertyCompat<WebProgressView> LOAD_PROGRESS_PROPERTY = new SimpleFloatPropertyCompat<>("loadProgress", obj -> obj.loadProgress, WebProgressView::setLoadProgress).setMultiplier(100f);

        private Paint bluePaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        private float loadProgress;
        private SpringAnimation springAnimation;
        private Theme.ResourcesProvider resourcesProvider;

        public WebProgressView(Context context, Theme.ResourcesProvider resourcesProvider) {
            super(context);
            this.resourcesProvider = resourcesProvider;

            bluePaint.setColor(getThemedColor(Theme.key_featuredStickers_addButton));
            bluePaint.setStyle(Paint.Style.STROKE);
            bluePaint.setStrokeWidth(dp(2));
            bluePaint.setStrokeCap(Paint.Cap.ROUND);
        }

        protected int getThemedColor(int key) {
            return Theme.getColor(key, resourcesProvider);
        }

        @Override
        protected void onAttachedToWindow() {
            super.onAttachedToWindow();

            springAnimation = new SpringAnimation(this, LOAD_PROGRESS_PROPERTY)
                    .setSpring(new SpringForce()
                        .setStiffness(400f)
                        .setDampingRatio(SpringForce.DAMPING_RATIO_NO_BOUNCY));
        }

        @Override
        protected void onDetachedFromWindow() {
            super.onDetachedFromWindow();

            springAnimation.cancel();
            springAnimation = null;
        }

        public void setLoadProgressAnimated(float loadProgress) {
            if (springAnimation == null) {
                setLoadProgress(loadProgress);
                return;
            }
            springAnimation.getSpring().setFinalPosition(loadProgress * 100f);
            springAnimation.start();
        }

        public void setLoadProgress(float loadProgress) {
            this.loadProgress = loadProgress;
            invalidate();
        }

        @Override
        public void draw(Canvas canvas) {
            super.draw(canvas);

            if (loadProgress > 0) {
                float y = getHeight() - bluePaint.getStrokeWidth() / 2f;
                canvas.drawLine(0, y, getWidth() * loadProgress, y, bluePaint);
            }
        }
    }
}
