import requests
import threading

from java.util import Locale
from org.telegram.ui.ActionBar import AlertDialog

from android_utils import log
from base_plugin import BasePlugin, <PERSON><PERSON><PERSON><PERSON>, HookStrategy
from client_utils import get_last_fragment, send_message
from ui.settings import Header, Switch, Divider, Input, Selector


__id__ = "weather"
__name__ = "Weather"
__version__ = "1.0.0"
__description__ = "Получение текущей погоды с помощью wttr.in"
__author__ = "@extera_plugin"
__min_version__ = "11.9.0"
__icon__ = "SpottyAnimated/35"

API_BASE_URL = "https://wttr.in/{city}?format=j1&lang=ru"
progress_dialog = None

WEATHER_EMOJI = {
    "clear": "☀️",
    "sunny": "☀️",
    "partly cloudy": "⛅️",
    "cloudy": "☁️",
    "overcast": "☁️",
    "mist": "😶‍🌫️",
    "fog": "😶‍🌫️",
    "light rain": "🌦",
    "rain": "🌧",
    "heavy rain": "⛈",
    "thunderstorm": "⛈",
    "snow": "🌨",
    "heavy snow": "❄️",
    "sleet": "🌨",
    "wind": "💨",
}

class ForecastDay:
    def __init__(self, date, emoji, condition, temp_min, temp_max):
        self.date = date
        self.emoji = emoji
        self.condition = condition
        self.temp_min = temp_min
        self.temp_max = temp_max


def _get_lang():
    return "ru"

def _tr(msgs):
    return msgs.get("ru", msgs.get("en", ""))


class WeatherPlugin(BasePlugin):
    def create_settings(self):
        return [
            Header(text="Настройки плагина \"Погода\""),
            Input(
                key="default_city",
                text="Город по умолчанию",
                default="Москва",
                subtext="Город, который будет использоваться, если не указан другой в команде"
            ),
            Switch(
                key="show_humidity",
                text="Показывать влажность",
                default=True,
                subtext="Отображать влажность воздуха"
            ),
            Switch(
                key="show_wind",
                text="Показывать ветер",
                default=True,
                subtext="Отображать скорость ветра"
            ),
            Switch(
                key="show_forecast",
                text="Показывать прогноз на 2 дня",
                default=False,
                subtext="Отображать прогноз погоды на следующие 2 дня"
            ),
            Divider(text="Команда: .wt [город]\nПример: .wt Москва"),
        ]

    def _get_weather_emoji(self, condition):
        condition = condition.lower()
        for cond, emoji in WEATHER_EMOJI.items():
            if cond in condition:
                return emoji
        return "🌡"

    def _fetch_weather(self, city):
        try:
            url = API_BASE_URL.format(city=city)
            resp = requests.get(url, timeout=10)
            resp.raise_for_status()
            return resp.json(), None

        except requests.exceptions.RequestException as e:
            log(f"[WeatherPlugin] Ошибка запроса: {e}")
            return None, f"Ошибка получения погоды: {e}"
        except Exception as e:
            log(f"[WeatherPlugin] Ошибка: {e}")
            return None, f"Ошибка получения погоды: {e}"

    def _format_forecast_day(self, day):
        """Format a single day's forecast."""
        date = day.date
        emoji = day.emoji
        condition = day.condition
        temp_min = day.temp_min
        temp_max = day.temp_max

        return f"  • {date}: {condition} {emoji}, {temp_min}°C ... {temp_max}°C\n"


    def _format_forecast(self, forecast_data):
        """Format weather forecast for multiple days."""
        forecast_text = ""
        for day in forecast_data:
            forecast_text += self._format_forecast_day(day)
        return forecast_text


    def _process_weather_data(self, weather_data):
        """Process weather data and format the text."""
        try:
            current = weather_data.get("current_condition", [{}])[0]
            location_data = weather_data.get("nearest_area", [{}])[0]
            location = f"{location_data.get('areaName', [{}])[0].get('value', 'Неизвестно')}, {location_data.get('country', [{}])[0].get('value', 'Неизвестно')}"

            emoji = self._get_weather_emoji(current.get("weatherDesc", [{}])[0].get("value", "Неизвестно"))
            temp = current.get("temp_C", "Неизвестно")
            feels_like = current.get("FeelsLikeC", "Неизвестно")
            humidity = current.get("humidity", "Неизвестно")
            wind_speed = current.get("windspeedKmph", "Неизвестно")
            wind_direction = current.get("winddir16Point", "Неизвестно")
            condition = current.get("lang_ru", [{}])[0].get("value", "Неизвестно")
            pressure = current.get("pressure", "Неизвестно")
            visibility = current.get("visibility", "Неизвестно")
            local_time = current.get("observation_time", "Неизвестно")


            msg = f"🌤️ Погода в {location}:\n"
            msg += f"• {condition} {emoji}\n"
            msg += f"• Температура: {temp}°C (Ощущается как: {feels_like}°C)\n"

            if self.get_setting("show_humidity", True):
                msg += f"• Влажность: {humidity}%\n"
            if self.get_setting("show_wind", True):
                msg += f"• Ветер: {wind_speed} км/ч ({wind_direction})\n"
            msg += f"• Давление: {pressure} мм рт. ст.\n"
            msg += f"• Видимость: {visibility} км\n"
            msg += f"• Местное время: {local_time}\n"

            forecast_text = ""

            if self.get_setting("show_forecast", False):
                forecast_data = []
                weather_data_list = weather_data.get("weather", [])
                for i in range(1, min(4, len(weather_data_list))):
                    forecast = weather_data_list[i]
                    date = forecast.get("date", "Неизвестно")
                    min_temp = forecast.get("mintempC", "Неизвестно")
                    max_temp = forecast.get("maxtempC", "Неизвестно")
                    hourly = forecast.get("hourly", [{}])[0]
                    condition_forecast = hourly.get("lang_ru", [{}])[0].get("value", "Неизвестно")
                    emoji_forecast = self._get_weather_emoji(condition_forecast)

                    forecast_day = ForecastDay(date, emoji_forecast, condition_forecast, min_temp, max_temp)
                    forecast_data.append(forecast_day)

                forecast_text = "\nПрогноз на 2 дня:\n" + self._format_forecast(forecast_data)

            msg += forecast_text

            return msg

        except Exception as e:
            log(f"[WeatherPlugin] Ошибка форматирования: {e}")
            return "Ошибка форматирования информации о погоде."

    def on_send_message_hook(self, account, params):
        if not hasattr(params, 'message') or not isinstance(params.message, str):
            return HookResult()
        msg = params.message.strip()
        usage = "Использование: .wt [город]\nПример: .wt Москва"

        if not msg.startswith(".wt"):
            return HookResult()

        def fetch_and_reply(city_arg):
            global progress_dialog
            try:
                city = city_arg if city_arg else self.get_setting("default_city", "Москва")
                if not city:
                    out_msg = "Пожалуйста, укажите город или задайте город по умолчанию в настройках."
                    self._dismiss_dialog()
                    send_message({"peer": getattr(params, "peer", None), "message": out_msg})
                    return

                data, error = self._fetch_weather(city)
                if error:
                    self._dismiss_dialog()
                    send_message({"peer": getattr(params, "peer", None), "message": error})
                    return
                out_msg = self._process_weather_data(data)
                self._dismiss_dialog()
                send_message({"peer": getattr(params, "peer", None), "message": out_msg})
            except Exception as e:
                log(f"[WeatherPlugin] Ошибка: {e}")
                out_msg = f"Ошибка: {e}"
                self._dismiss_dialog()
                send_message({"peer": getattr(params, "peer", None), "message": out_msg})

        parts = msg.split(" ", 1)
        city_arg = parts[1].strip() if len(parts) > 1 and parts[1].strip() else None

        if not city_arg and not self.get_setting("default_city", "").strip():
            params.message = usage
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        global progress_dialog
        try:
            progress_dialog = AlertDialog(get_last_fragment().getParentActivity(), 3)
            progress_dialog.show()
        except Exception as e:
            log(f"[WeatherPlugin] Ошибка при создании диалога: {e}")

        threading.Thread(target=lambda: fetch_and_reply(city_arg), daemon=True).start()
        params.message = "Получение погоды..."
        return HookResult(strategy=HookStrategy.CANCEL)

    def _dismiss_dialog(self):
        global progress_dialog
        try:
            if progress_dialog is not None and progress_dialog.isShowing():
                progress_dialog.dismiss()
        except Exception:
            pass
        finally:
            progress_dialog = None
