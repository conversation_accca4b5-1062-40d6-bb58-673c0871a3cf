// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/PeerConnection

#ifndef org_webrtc_PeerConnection_JNI
#define org_webrtc_PeerConnection_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_PeerConnection[];
const char kClassPath_org_webrtc_PeerConnection[] = "org/webrtc/PeerConnection";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_PeerConnection_00024AdapterType[];
const char kClassPath_org_webrtc_PeerConnection_00024AdapterType[] =
    "org/webrtc/PeerConnection$AdapterType";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_PeerConnection_00024IceConnectionState[];
const char kClassPath_org_webrtc_PeerConnection_00024IceConnectionState[] =
    "org/webrtc/PeerConnection$IceConnectionState";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_PeerConnection_00024IceGatheringState[];
const char kClassPath_org_webrtc_PeerConnection_00024IceGatheringState[] =
    "org/webrtc/PeerConnection$IceGatheringState";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_PeerConnection_00024IceServer[];
const char kClassPath_org_webrtc_PeerConnection_00024IceServer[] =
    "org/webrtc/PeerConnection$IceServer";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_PeerConnection_00024Observer[];
const char kClassPath_org_webrtc_PeerConnection_00024Observer[] =
    "org/webrtc/PeerConnection$Observer";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_PeerConnection_00024PeerConnectionState[];
const char kClassPath_org_webrtc_PeerConnection_00024PeerConnectionState[] =
    "org/webrtc/PeerConnection$PeerConnectionState";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_PeerConnection_00024RTCConfiguration[];
const char kClassPath_org_webrtc_PeerConnection_00024RTCConfiguration[] =
    "org/webrtc/PeerConnection$RTCConfiguration";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_PeerConnection_00024SignalingState[];
const char kClassPath_org_webrtc_PeerConnection_00024SignalingState[] =
    "org/webrtc/PeerConnection$SignalingState";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_PeerConnection_clazz(nullptr);
#ifndef org_webrtc_PeerConnection_clazz_defined
#define org_webrtc_PeerConnection_clazz_defined
inline jclass org_webrtc_PeerConnection_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_PeerConnection,
      &g_org_webrtc_PeerConnection_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_PeerConnection_00024AdapterType_clazz(nullptr);
#ifndef org_webrtc_PeerConnection_00024AdapterType_clazz_defined
#define org_webrtc_PeerConnection_00024AdapterType_clazz_defined
inline jclass org_webrtc_PeerConnection_00024AdapterType_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_PeerConnection_00024AdapterType,
      &g_org_webrtc_PeerConnection_00024AdapterType_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_PeerConnection_00024IceConnectionState_clazz(nullptr);
#ifndef org_webrtc_PeerConnection_00024IceConnectionState_clazz_defined
#define org_webrtc_PeerConnection_00024IceConnectionState_clazz_defined
inline jclass org_webrtc_PeerConnection_00024IceConnectionState_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_PeerConnection_00024IceConnectionState,
      &g_org_webrtc_PeerConnection_00024IceConnectionState_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_PeerConnection_00024IceGatheringState_clazz(nullptr);
#ifndef org_webrtc_PeerConnection_00024IceGatheringState_clazz_defined
#define org_webrtc_PeerConnection_00024IceGatheringState_clazz_defined
inline jclass org_webrtc_PeerConnection_00024IceGatheringState_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_PeerConnection_00024IceGatheringState,
      &g_org_webrtc_PeerConnection_00024IceGatheringState_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_PeerConnection_00024IceServer_clazz(nullptr);
#ifndef org_webrtc_PeerConnection_00024IceServer_clazz_defined
#define org_webrtc_PeerConnection_00024IceServer_clazz_defined
inline jclass org_webrtc_PeerConnection_00024IceServer_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_PeerConnection_00024IceServer,
      &g_org_webrtc_PeerConnection_00024IceServer_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_PeerConnection_00024Observer_clazz(nullptr);
#ifndef org_webrtc_PeerConnection_00024Observer_clazz_defined
#define org_webrtc_PeerConnection_00024Observer_clazz_defined
inline jclass org_webrtc_PeerConnection_00024Observer_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_PeerConnection_00024Observer,
      &g_org_webrtc_PeerConnection_00024Observer_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_PeerConnection_00024PeerConnectionState_clazz(nullptr);
#ifndef org_webrtc_PeerConnection_00024PeerConnectionState_clazz_defined
#define org_webrtc_PeerConnection_00024PeerConnectionState_clazz_defined
inline jclass org_webrtc_PeerConnection_00024PeerConnectionState_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_PeerConnection_00024PeerConnectionState,
      &g_org_webrtc_PeerConnection_00024PeerConnectionState_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_clazz(nullptr);
#ifndef org_webrtc_PeerConnection_00024RTCConfiguration_clazz_defined
#define org_webrtc_PeerConnection_00024RTCConfiguration_clazz_defined
inline jclass org_webrtc_PeerConnection_00024RTCConfiguration_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_PeerConnection_00024RTCConfiguration,
      &g_org_webrtc_PeerConnection_00024RTCConfiguration_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_PeerConnection_00024SignalingState_clazz(nullptr);
#ifndef org_webrtc_PeerConnection_00024SignalingState_clazz_defined
#define org_webrtc_PeerConnection_00024SignalingState_clazz_defined
inline jclass org_webrtc_PeerConnection_00024SignalingState_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_PeerConnection_00024SignalingState,
      &g_org_webrtc_PeerConnection_00024SignalingState_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {

static jboolean JNI_PeerConnection_AddIceCandidate(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller,
    const jni_zero::JavaParamRef<jstring>& sdpMid,
    jint sdpMLineIndex,
    const jni_zero::JavaParamRef<jstring>& iceCandidateSdp);

JNI_BOUNDARY_EXPORT jboolean Java_org_webrtc_PeerConnection_nativeAddIceCandidate(
    JNIEnv* env,
    jobject jcaller,
    jstring sdpMid,
    jint sdpMLineIndex,
    jstring iceCandidateSdp) {
  return JNI_PeerConnection_AddIceCandidate(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      jni_zero::JavaParamRef<jstring>(env, sdpMid), sdpMLineIndex,
      jni_zero::JavaParamRef<jstring>(env, iceCandidateSdp));
}

static void JNI_PeerConnection_AddIceCandidateWithObserver(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller,
    const jni_zero::JavaParamRef<jstring>& sdpMid,
    jint sdpMLineIndex,
    const jni_zero::JavaParamRef<jstring>& iceCandidateSdp,
    const jni_zero::JavaParamRef<jobject>& observer);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnection_nativeAddIceCandidateWithObserver(
    JNIEnv* env,
    jobject jcaller,
    jstring sdpMid,
    jint sdpMLineIndex,
    jstring iceCandidateSdp,
    jobject observer) {
  return JNI_PeerConnection_AddIceCandidateWithObserver(env, jni_zero::JavaParamRef<jobject>(env,
      jcaller), jni_zero::JavaParamRef<jstring>(env, sdpMid), sdpMLineIndex,
      jni_zero::JavaParamRef<jstring>(env, iceCandidateSdp), jni_zero::JavaParamRef<jobject>(env,
      observer));
}

static jboolean JNI_PeerConnection_AddLocalStream(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller,
    jlong stream);

JNI_BOUNDARY_EXPORT jboolean Java_org_webrtc_PeerConnection_nativeAddLocalStream(
    JNIEnv* env,
    jobject jcaller,
    jlong stream) {
  return JNI_PeerConnection_AddLocalStream(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      stream);
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_PeerConnection_AddTrack(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller,
    jlong track,
    const jni_zero::JavaParamRef<jobject>& streamIds);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnection_nativeAddTrack(
    JNIEnv* env,
    jobject jcaller,
    jlong track,
    jobject streamIds) {
  return JNI_PeerConnection_AddTrack(env, jni_zero::JavaParamRef<jobject>(env, jcaller), track,
      jni_zero::JavaParamRef<jobject>(env, streamIds)).Release();
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_PeerConnection_AddTransceiverOfType(JNIEnv* env,
    const jni_zero::JavaParamRef<jobject>& jcaller,
    const jni_zero::JavaParamRef<jobject>& mediaType,
    const jni_zero::JavaParamRef<jobject>& init);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnection_nativeAddTransceiverOfType(
    JNIEnv* env,
    jobject jcaller,
    jobject mediaType,
    jobject init) {
  return JNI_PeerConnection_AddTransceiverOfType(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      jni_zero::JavaParamRef<jobject>(env, mediaType), jni_zero::JavaParamRef<jobject>(env,
      init)).Release();
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_PeerConnection_AddTransceiverWithTrack(JNIEnv* env,
    const jni_zero::JavaParamRef<jobject>& jcaller,
    jlong track,
    const jni_zero::JavaParamRef<jobject>& init);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnection_nativeAddTransceiverWithTrack(
    JNIEnv* env,
    jobject jcaller,
    jlong track,
    jobject init) {
  return JNI_PeerConnection_AddTransceiverWithTrack(env, jni_zero::JavaParamRef<jobject>(env,
      jcaller), track, jni_zero::JavaParamRef<jobject>(env, init)).Release();
}

static void JNI_PeerConnection_Close(JNIEnv* env, const jni_zero::JavaParamRef<jobject>& jcaller);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnection_nativeClose(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_PeerConnection_Close(env, jni_zero::JavaParamRef<jobject>(env, jcaller));
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_PeerConnection_ConnectionState(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnection_nativeConnectionState(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_PeerConnection_ConnectionState(env, jni_zero::JavaParamRef<jobject>(env,
      jcaller)).Release();
}

static void JNI_PeerConnection_CreateAnswer(JNIEnv* env, const jni_zero::JavaParamRef<jobject>&
    jcaller,
    const jni_zero::JavaParamRef<jobject>& observer,
    const jni_zero::JavaParamRef<jobject>& constraints);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnection_nativeCreateAnswer(
    JNIEnv* env,
    jobject jcaller,
    jobject observer,
    jobject constraints) {
  return JNI_PeerConnection_CreateAnswer(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      jni_zero::JavaParamRef<jobject>(env, observer), jni_zero::JavaParamRef<jobject>(env,
      constraints));
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_PeerConnection_CreateDataChannel(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller,
    const jni_zero::JavaParamRef<jstring>& label,
    const jni_zero::JavaParamRef<jobject>& init);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnection_nativeCreateDataChannel(
    JNIEnv* env,
    jobject jcaller,
    jstring label,
    jobject init) {
  return JNI_PeerConnection_CreateDataChannel(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      jni_zero::JavaParamRef<jstring>(env, label), jni_zero::JavaParamRef<jobject>(env,
      init)).Release();
}

static void JNI_PeerConnection_CreateOffer(JNIEnv* env, const jni_zero::JavaParamRef<jobject>&
    jcaller,
    const jni_zero::JavaParamRef<jobject>& observer,
    const jni_zero::JavaParamRef<jobject>& constraints);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnection_nativeCreateOffer(
    JNIEnv* env,
    jobject jcaller,
    jobject observer,
    jobject constraints) {
  return JNI_PeerConnection_CreateOffer(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      jni_zero::JavaParamRef<jobject>(env, observer), jni_zero::JavaParamRef<jobject>(env,
      constraints));
}

static jlong JNI_PeerConnection_CreatePeerConnectionObserver(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& observer);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_PeerConnection_nativeCreatePeerConnectionObserver(
    JNIEnv* env,
    jclass jcaller,
    jobject observer) {
  return JNI_PeerConnection_CreatePeerConnectionObserver(env, jni_zero::JavaParamRef<jobject>(env,
      observer));
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_PeerConnection_CreateSender(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller,
    const jni_zero::JavaParamRef<jstring>& kind,
    const jni_zero::JavaParamRef<jstring>& stream_id);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnection_nativeCreateSender(
    JNIEnv* env,
    jobject jcaller,
    jstring kind,
    jstring stream_id) {
  return JNI_PeerConnection_CreateSender(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      jni_zero::JavaParamRef<jstring>(env, kind), jni_zero::JavaParamRef<jstring>(env,
      stream_id)).Release();
}

static void JNI_PeerConnection_FreeOwnedPeerConnection(JNIEnv* env, jlong ownedPeerConnection);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnection_nativeFreeOwnedPeerConnection(
    JNIEnv* env,
    jclass jcaller,
    jlong ownedPeerConnection) {
  return JNI_PeerConnection_FreeOwnedPeerConnection(env, ownedPeerConnection);
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_PeerConnection_GetCertificate(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnection_nativeGetCertificate(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_PeerConnection_GetCertificate(env, jni_zero::JavaParamRef<jobject>(env,
      jcaller)).Release();
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_PeerConnection_GetLocalDescription(JNIEnv* env,
    const jni_zero::JavaParamRef<jobject>& jcaller);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnection_nativeGetLocalDescription(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_PeerConnection_GetLocalDescription(env, jni_zero::JavaParamRef<jobject>(env,
      jcaller)).Release();
}

static jlong JNI_PeerConnection_GetNativePeerConnection(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_PeerConnection_nativeGetNativePeerConnection(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_PeerConnection_GetNativePeerConnection(env, jni_zero::JavaParamRef<jobject>(env,
      jcaller));
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_PeerConnection_GetReceivers(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnection_nativeGetReceivers(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_PeerConnection_GetReceivers(env, jni_zero::JavaParamRef<jobject>(env,
      jcaller)).Release();
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_PeerConnection_GetRemoteDescription(JNIEnv* env,
    const jni_zero::JavaParamRef<jobject>& jcaller);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnection_nativeGetRemoteDescription(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_PeerConnection_GetRemoteDescription(env, jni_zero::JavaParamRef<jobject>(env,
      jcaller)).Release();
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_PeerConnection_GetSenders(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnection_nativeGetSenders(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_PeerConnection_GetSenders(env, jni_zero::JavaParamRef<jobject>(env,
      jcaller)).Release();
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_PeerConnection_GetTransceivers(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnection_nativeGetTransceivers(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_PeerConnection_GetTransceivers(env, jni_zero::JavaParamRef<jobject>(env,
      jcaller)).Release();
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_PeerConnection_IceConnectionState(JNIEnv* env,
    const jni_zero::JavaParamRef<jobject>& jcaller);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnection_nativeIceConnectionState(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_PeerConnection_IceConnectionState(env, jni_zero::JavaParamRef<jobject>(env,
      jcaller)).Release();
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_PeerConnection_IceGatheringState(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnection_nativeIceGatheringState(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_PeerConnection_IceGatheringState(env, jni_zero::JavaParamRef<jobject>(env,
      jcaller)).Release();
}

static void JNI_PeerConnection_NewGetStats(JNIEnv* env, const jni_zero::JavaParamRef<jobject>&
    jcaller,
    const jni_zero::JavaParamRef<jobject>& callback);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnection_nativeNewGetStats(
    JNIEnv* env,
    jobject jcaller,
    jobject callback) {
  return JNI_PeerConnection_NewGetStats(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      jni_zero::JavaParamRef<jobject>(env, callback));
}

static void JNI_PeerConnection_NewGetStatsReceiver(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller,
    jlong receiver,
    const jni_zero::JavaParamRef<jobject>& callback);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnection_nativeNewGetStatsReceiver(
    JNIEnv* env,
    jobject jcaller,
    jlong receiver,
    jobject callback) {
  return JNI_PeerConnection_NewGetStatsReceiver(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      receiver, jni_zero::JavaParamRef<jobject>(env, callback));
}

static void JNI_PeerConnection_NewGetStatsSender(JNIEnv* env, const jni_zero::JavaParamRef<jobject>&
    jcaller,
    jlong sender,
    const jni_zero::JavaParamRef<jobject>& callback);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnection_nativeNewGetStatsSender(
    JNIEnv* env,
    jobject jcaller,
    jlong sender,
    jobject callback) {
  return JNI_PeerConnection_NewGetStatsSender(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      sender, jni_zero::JavaParamRef<jobject>(env, callback));
}

static jboolean JNI_PeerConnection_OldGetStats(JNIEnv* env, const jni_zero::JavaParamRef<jobject>&
    jcaller,
    const jni_zero::JavaParamRef<jobject>& observer,
    jlong nativeTrack);

JNI_BOUNDARY_EXPORT jboolean Java_org_webrtc_PeerConnection_nativeOldGetStats(
    JNIEnv* env,
    jobject jcaller,
    jobject observer,
    jlong nativeTrack) {
  return JNI_PeerConnection_OldGetStats(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      jni_zero::JavaParamRef<jobject>(env, observer), nativeTrack);
}

static jboolean JNI_PeerConnection_RemoveIceCandidates(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller,
    const jni_zero::JavaParamRef<jobjectArray>& candidates);

JNI_BOUNDARY_EXPORT jboolean Java_org_webrtc_PeerConnection_nativeRemoveIceCandidates(
    JNIEnv* env,
    jobject jcaller,
    jobjectArray candidates) {
  return JNI_PeerConnection_RemoveIceCandidates(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      jni_zero::JavaParamRef<jobjectArray>(env, candidates));
}

static void JNI_PeerConnection_RemoveLocalStream(JNIEnv* env, const jni_zero::JavaParamRef<jobject>&
    jcaller,
    jlong stream);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnection_nativeRemoveLocalStream(
    JNIEnv* env,
    jobject jcaller,
    jlong stream) {
  return JNI_PeerConnection_RemoveLocalStream(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      stream);
}

static jboolean JNI_PeerConnection_RemoveTrack(JNIEnv* env, const jni_zero::JavaParamRef<jobject>&
    jcaller,
    jlong sender);

JNI_BOUNDARY_EXPORT jboolean Java_org_webrtc_PeerConnection_nativeRemoveTrack(
    JNIEnv* env,
    jobject jcaller,
    jlong sender) {
  return JNI_PeerConnection_RemoveTrack(env, jni_zero::JavaParamRef<jobject>(env, jcaller), sender);
}

static void JNI_PeerConnection_RestartIce(JNIEnv* env, const jni_zero::JavaParamRef<jobject>&
    jcaller);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnection_nativeRestartIce(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_PeerConnection_RestartIce(env, jni_zero::JavaParamRef<jobject>(env, jcaller));
}

static void JNI_PeerConnection_SetAudioPlayout(JNIEnv* env, const jni_zero::JavaParamRef<jobject>&
    jcaller,
    jboolean playout);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnection_nativeSetAudioPlayout(
    JNIEnv* env,
    jobject jcaller,
    jboolean playout) {
  return JNI_PeerConnection_SetAudioPlayout(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      playout);
}

static void JNI_PeerConnection_SetAudioRecording(JNIEnv* env, const jni_zero::JavaParamRef<jobject>&
    jcaller,
    jboolean recording);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnection_nativeSetAudioRecording(
    JNIEnv* env,
    jobject jcaller,
    jboolean recording) {
  return JNI_PeerConnection_SetAudioRecording(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      recording);
}

static jboolean JNI_PeerConnection_SetBitrate(JNIEnv* env, const jni_zero::JavaParamRef<jobject>&
    jcaller,
    const jni_zero::JavaParamRef<jobject>& min,
    const jni_zero::JavaParamRef<jobject>& current,
    const jni_zero::JavaParamRef<jobject>& max);

JNI_BOUNDARY_EXPORT jboolean Java_org_webrtc_PeerConnection_nativeSetBitrate(
    JNIEnv* env,
    jobject jcaller,
    jobject min,
    jobject current,
    jobject max) {
  return JNI_PeerConnection_SetBitrate(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      jni_zero::JavaParamRef<jobject>(env, min), jni_zero::JavaParamRef<jobject>(env, current),
      jni_zero::JavaParamRef<jobject>(env, max));
}

static jboolean JNI_PeerConnection_SetConfiguration(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller,
    const jni_zero::JavaParamRef<jobject>& config);

JNI_BOUNDARY_EXPORT jboolean Java_org_webrtc_PeerConnection_nativeSetConfiguration(
    JNIEnv* env,
    jobject jcaller,
    jobject config) {
  return JNI_PeerConnection_SetConfiguration(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      jni_zero::JavaParamRef<jobject>(env, config));
}

static void JNI_PeerConnection_SetLocalDescription(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller,
    const jni_zero::JavaParamRef<jobject>& observer,
    const jni_zero::JavaParamRef<jobject>& sdp);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnection_nativeSetLocalDescription(
    JNIEnv* env,
    jobject jcaller,
    jobject observer,
    jobject sdp) {
  return JNI_PeerConnection_SetLocalDescription(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      jni_zero::JavaParamRef<jobject>(env, observer), jni_zero::JavaParamRef<jobject>(env, sdp));
}

static void JNI_PeerConnection_SetLocalDescriptionAutomatically(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller,
    const jni_zero::JavaParamRef<jobject>& observer);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnection_nativeSetLocalDescriptionAutomatically(
    JNIEnv* env,
    jobject jcaller,
    jobject observer) {
  return JNI_PeerConnection_SetLocalDescriptionAutomatically(env,
      jni_zero::JavaParamRef<jobject>(env, jcaller), jni_zero::JavaParamRef<jobject>(env,
      observer));
}

static void JNI_PeerConnection_SetRemoteDescription(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller,
    const jni_zero::JavaParamRef<jobject>& observer,
    const jni_zero::JavaParamRef<jobject>& sdp);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnection_nativeSetRemoteDescription(
    JNIEnv* env,
    jobject jcaller,
    jobject observer,
    jobject sdp) {
  return JNI_PeerConnection_SetRemoteDescription(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      jni_zero::JavaParamRef<jobject>(env, observer), jni_zero::JavaParamRef<jobject>(env, sdp));
}

static jni_zero::ScopedJavaLocalRef<jobject> JNI_PeerConnection_SignalingState(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller);

JNI_BOUNDARY_EXPORT jobject Java_org_webrtc_PeerConnection_nativeSignalingState(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_PeerConnection_SignalingState(env, jni_zero::JavaParamRef<jobject>(env,
      jcaller)).Release();
}

static jboolean JNI_PeerConnection_StartRtcEventLog(JNIEnv* env, const
    jni_zero::JavaParamRef<jobject>& jcaller,
    jint file_descriptor,
    jint max_size_bytes);

JNI_BOUNDARY_EXPORT jboolean Java_org_webrtc_PeerConnection_nativeStartRtcEventLog(
    JNIEnv* env,
    jobject jcaller,
    jint file_descriptor,
    jint max_size_bytes) {
  return JNI_PeerConnection_StartRtcEventLog(env, jni_zero::JavaParamRef<jobject>(env, jcaller),
      file_descriptor, max_size_bytes);
}

static void JNI_PeerConnection_StopRtcEventLog(JNIEnv* env, const jni_zero::JavaParamRef<jobject>&
    jcaller);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_PeerConnection_nativeStopRtcEventLog(
    JNIEnv* env,
    jobject jcaller) {
  return JNI_PeerConnection_StopRtcEventLog(env, jni_zero::JavaParamRef<jobject>(env, jcaller));
}


static std::atomic<jmethodID> g_org_webrtc_PeerConnection_getNativeOwnedPeerConnection0(nullptr);
static jlong Java_PeerConnection_getNativeOwnedPeerConnection(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getNativeOwnedPeerConnection",
          "()J",
          &g_org_webrtc_PeerConnection_getNativeOwnedPeerConnection0);

  jlong ret =
      env->CallLongMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024AdapterType_fromNativeIndex1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_AdapterType_fromNativeIndex(JNIEnv* env,
    JniIntWrapper nativeIndex) {
  jclass clazz = org_webrtc_PeerConnection_00024AdapterType_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_PeerConnection_00024AdapterType_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "fromNativeIndex",
          "(I)Lorg/webrtc/PeerConnection$AdapterType;",
          &g_org_webrtc_PeerConnection_00024AdapterType_fromNativeIndex1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(nativeIndex));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024IceConnectionState_fromNativeIndex1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_IceConnectionState_fromNativeIndex(JNIEnv* env,
    JniIntWrapper nativeIndex) {
  jclass clazz = org_webrtc_PeerConnection_00024IceConnectionState_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_PeerConnection_00024IceConnectionState_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "fromNativeIndex",
          "(I)Lorg/webrtc/PeerConnection$IceConnectionState;",
          &g_org_webrtc_PeerConnection_00024IceConnectionState_fromNativeIndex1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(nativeIndex));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024IceGatheringState_fromNativeIndex1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_IceGatheringState_fromNativeIndex(JNIEnv* env,
    JniIntWrapper nativeIndex) {
  jclass clazz = org_webrtc_PeerConnection_00024IceGatheringState_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_PeerConnection_00024IceGatheringState_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "fromNativeIndex",
          "(I)Lorg/webrtc/PeerConnection$IceGatheringState;",
          &g_org_webrtc_PeerConnection_00024IceGatheringState_fromNativeIndex1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(nativeIndex));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_PeerConnection_00024IceServer_getHostname0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_IceServer_getHostname(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024IceServer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024IceServer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHostname",
          "()Ljava/lang/String;",
          &g_org_webrtc_PeerConnection_00024IceServer_getHostname0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_PeerConnection_00024IceServer_getPassword0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_IceServer_getPassword(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024IceServer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024IceServer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getPassword",
          "()Ljava/lang/String;",
          &g_org_webrtc_PeerConnection_00024IceServer_getPassword0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024IceServer_getTlsAlpnProtocols0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_IceServer_getTlsAlpnProtocols(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024IceServer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024IceServer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getTlsAlpnProtocols",
          "()Ljava/util/List;",
          &g_org_webrtc_PeerConnection_00024IceServer_getTlsAlpnProtocols0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_PeerConnection_00024IceServer_getTlsCertPolicy0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_IceServer_getTlsCertPolicy(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024IceServer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024IceServer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getTlsCertPolicy",
          "()Lorg/webrtc/PeerConnection$TlsCertPolicy;",
          &g_org_webrtc_PeerConnection_00024IceServer_getTlsCertPolicy0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024IceServer_getTlsEllipticCurves0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_IceServer_getTlsEllipticCurves(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024IceServer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024IceServer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getTlsEllipticCurves",
          "()Ljava/util/List;",
          &g_org_webrtc_PeerConnection_00024IceServer_getTlsEllipticCurves0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_PeerConnection_00024IceServer_getUrls0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_IceServer_getUrls(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024IceServer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024IceServer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getUrls",
          "()Ljava/util/List;",
          &g_org_webrtc_PeerConnection_00024IceServer_getUrls0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_PeerConnection_00024IceServer_getUsername0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_IceServer_getUsername(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024IceServer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024IceServer_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getUsername",
          "()Ljava/lang/String;",
          &g_org_webrtc_PeerConnection_00024IceServer_getUsername0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_PeerConnection_00024Observer_onAddStream1(nullptr);
static void Java_Observer_onAddStream(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& stream) {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onAddStream",
          "(Lorg/webrtc/MediaStream;)V",
          &g_org_webrtc_PeerConnection_00024Observer_onAddStream1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, stream.obj());
}

static std::atomic<jmethodID> g_org_webrtc_PeerConnection_00024Observer_onAddTrack2(nullptr);
static void Java_Observer_onAddTrack(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& receiver,
    const jni_zero::JavaRef<jobjectArray>& mediaStreams) {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onAddTrack",
          "(Lorg/webrtc/RtpReceiver;[Lorg/webrtc/MediaStream;)V",
          &g_org_webrtc_PeerConnection_00024Observer_onAddTrack2);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, receiver.obj(), mediaStreams.obj());
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024Observer_onConnectionChange1(nullptr);
static void Java_Observer_onConnectionChange(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& newState) {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onConnectionChange",
          "(Lorg/webrtc/PeerConnection$PeerConnectionState;)V",
          &g_org_webrtc_PeerConnection_00024Observer_onConnectionChange1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, newState.obj());
}

static std::atomic<jmethodID> g_org_webrtc_PeerConnection_00024Observer_onDataChannel1(nullptr);
static void Java_Observer_onDataChannel(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& dataChannel) {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onDataChannel",
          "(Lorg/webrtc/DataChannel;)V",
          &g_org_webrtc_PeerConnection_00024Observer_onDataChannel1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, dataChannel.obj());
}

static std::atomic<jmethodID> g_org_webrtc_PeerConnection_00024Observer_onIceCandidate1(nullptr);
static void Java_Observer_onIceCandidate(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& candidate) {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onIceCandidate",
          "(Lorg/webrtc/IceCandidate;)V",
          &g_org_webrtc_PeerConnection_00024Observer_onIceCandidate1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, candidate.obj());
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024Observer_onIceCandidateError1(nullptr);
static void Java_Observer_onIceCandidateError(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& event) {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onIceCandidateError",
          "(Lorg/webrtc/IceCandidateErrorEvent;)V",
          &g_org_webrtc_PeerConnection_00024Observer_onIceCandidateError1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, event.obj());
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024Observer_onIceCandidatesRemoved1(nullptr);
static void Java_Observer_onIceCandidatesRemoved(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobjectArray>& candidates) {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onIceCandidatesRemoved",
          "([Lorg/webrtc/IceCandidate;)V",
          &g_org_webrtc_PeerConnection_00024Observer_onIceCandidatesRemoved1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, candidates.obj());
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024Observer_onIceConnectionChange1(nullptr);
static void Java_Observer_onIceConnectionChange(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& newState) {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onIceConnectionChange",
          "(Lorg/webrtc/PeerConnection$IceConnectionState;)V",
          &g_org_webrtc_PeerConnection_00024Observer_onIceConnectionChange1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, newState.obj());
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024Observer_onIceConnectionReceivingChange1(nullptr);
static void Java_Observer_onIceConnectionReceivingChange(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, jboolean receiving) {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onIceConnectionReceivingChange",
          "(Z)V",
          &g_org_webrtc_PeerConnection_00024Observer_onIceConnectionReceivingChange1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, receiving);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024Observer_onIceGatheringChange1(nullptr);
static void Java_Observer_onIceGatheringChange(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& newState) {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onIceGatheringChange",
          "(Lorg/webrtc/PeerConnection$IceGatheringState;)V",
          &g_org_webrtc_PeerConnection_00024Observer_onIceGatheringChange1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, newState.obj());
}

static std::atomic<jmethodID> g_org_webrtc_PeerConnection_00024Observer_onRemoveStream1(nullptr);
static void Java_Observer_onRemoveStream(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& stream) {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onRemoveStream",
          "(Lorg/webrtc/MediaStream;)V",
          &g_org_webrtc_PeerConnection_00024Observer_onRemoveStream1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, stream.obj());
}

static std::atomic<jmethodID> g_org_webrtc_PeerConnection_00024Observer_onRemoveTrack1(nullptr);
static void Java_Observer_onRemoveTrack(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& receiver) {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onRemoveTrack",
          "(Lorg/webrtc/RtpReceiver;)V",
          &g_org_webrtc_PeerConnection_00024Observer_onRemoveTrack1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, receiver.obj());
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024Observer_onRenegotiationNeeded0(nullptr);
static void Java_Observer_onRenegotiationNeeded(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj)
    {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onRenegotiationNeeded",
          "()V",
          &g_org_webrtc_PeerConnection_00024Observer_onRenegotiationNeeded0);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024Observer_onSelectedCandidatePairChanged1(nullptr);
static void Java_Observer_onSelectedCandidatePairChanged(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& event) {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onSelectedCandidatePairChanged",
          "(Lorg/webrtc/CandidatePairChangeEvent;)V",
          &g_org_webrtc_PeerConnection_00024Observer_onSelectedCandidatePairChanged1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, event.obj());
}

static std::atomic<jmethodID> g_org_webrtc_PeerConnection_00024Observer_onSignalingChange1(nullptr);
static void Java_Observer_onSignalingChange(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& newState) {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onSignalingChange",
          "(Lorg/webrtc/PeerConnection$SignalingState;)V",
          &g_org_webrtc_PeerConnection_00024Observer_onSignalingChange1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, newState.obj());
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024Observer_onStandardizedIceConnectionChange1(nullptr);
static void Java_Observer_onStandardizedIceConnectionChange(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& newState) {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onStandardizedIceConnectionChange",
          "(Lorg/webrtc/PeerConnection$IceConnectionState;)V",
          &g_org_webrtc_PeerConnection_00024Observer_onStandardizedIceConnectionChange1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, newState.obj());
}

static std::atomic<jmethodID> g_org_webrtc_PeerConnection_00024Observer_onTrack1(nullptr);
static void Java_Observer_onTrack(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& transceiver) {
  jclass clazz = org_webrtc_PeerConnection_00024Observer_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024Observer_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onTrack",
          "(Lorg/webrtc/RtpTransceiver;)V",
          &g_org_webrtc_PeerConnection_00024Observer_onTrack1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, transceiver.obj());
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024PeerConnectionState_fromNativeIndex1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_PeerConnectionState_fromNativeIndex(JNIEnv* env,
    JniIntWrapper nativeIndex) {
  jclass clazz = org_webrtc_PeerConnection_00024PeerConnectionState_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_PeerConnection_00024PeerConnectionState_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "fromNativeIndex",
          "(I)Lorg/webrtc/PeerConnection$PeerConnectionState;",
          &g_org_webrtc_PeerConnection_00024PeerConnectionState_fromNativeIndex1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(nativeIndex));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getActiveResetSrtpParams0(nullptr);
static jboolean Java_RTCConfiguration_getActiveResetSrtpParams(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getActiveResetSrtpParams",
          "()Z",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getActiveResetSrtpParams0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getAudioJitterBufferFastAccelerate0(nullptr);
static jboolean Java_RTCConfiguration_getAudioJitterBufferFastAccelerate(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getAudioJitterBufferFastAccelerate",
          "()Z",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getAudioJitterBufferFastAccelerate0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getAudioJitterBufferMaxPackets0(nullptr);
static jint Java_RTCConfiguration_getAudioJitterBufferMaxPackets(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getAudioJitterBufferMaxPackets",
          "()I",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getAudioJitterBufferMaxPackets0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getBundlePolicy0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getBundlePolicy(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getBundlePolicy",
          "()Lorg/webrtc/PeerConnection$BundlePolicy;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getBundlePolicy0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getCandidateNetworkPolicy0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getCandidateNetworkPolicy(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getCandidateNetworkPolicy",
          "()Lorg/webrtc/PeerConnection$CandidateNetworkPolicy;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getCandidateNetworkPolicy0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getCertificate0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getCertificate(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getCertificate",
          "()Lorg/webrtc/RtcCertificatePem;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getCertificate0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getContinualGatheringPolicy0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject>
    Java_RTCConfiguration_getContinualGatheringPolicy(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getContinualGatheringPolicy",
          "()Lorg/webrtc/PeerConnection$ContinualGatheringPolicy;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getContinualGatheringPolicy0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getCryptoOptions0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getCryptoOptions(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getCryptoOptions",
          "()Lorg/webrtc/CryptoOptions;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getCryptoOptions0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getDisableIPv6OnWifi0(nullptr);
static jboolean Java_RTCConfiguration_getDisableIPv6OnWifi(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getDisableIPv6OnWifi",
          "()Z",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getDisableIPv6OnWifi0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getEnableCpuOveruseDetection0(nullptr);
static jboolean Java_RTCConfiguration_getEnableCpuOveruseDetection(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getEnableCpuOveruseDetection",
          "()Z",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getEnableCpuOveruseDetection0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getEnableDscp0(nullptr);
static jboolean Java_RTCConfiguration_getEnableDscp(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getEnableDscp",
          "()Z",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getEnableDscp0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getEnableImplicitRollback0(nullptr);
static jboolean Java_RTCConfiguration_getEnableImplicitRollback(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getEnableImplicitRollback",
          "()Z",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getEnableImplicitRollback0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceBackupCandidatePairPingInterval0(nullptr);
static jint Java_RTCConfiguration_getIceBackupCandidatePairPingInterval(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getIceBackupCandidatePairPingInterval",
          "()I",
&g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceBackupCandidatePairPingInterval0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceCandidatePoolSize0(nullptr);
static jint Java_RTCConfiguration_getIceCandidatePoolSize(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getIceCandidatePoolSize",
          "()I",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceCandidatePoolSize0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceCheckIntervalStrongConnectivity0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject>
    Java_RTCConfiguration_getIceCheckIntervalStrongConnectivity(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getIceCheckIntervalStrongConnectivity",
          "()Ljava/lang/Integer;",
&g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceCheckIntervalStrongConnectivity0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceCheckIntervalWeakConnectivity0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject>
    Java_RTCConfiguration_getIceCheckIntervalWeakConnectivity(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getIceCheckIntervalWeakConnectivity",
          "()Ljava/lang/Integer;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceCheckIntervalWeakConnectivity0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceCheckMinInterval0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getIceCheckMinInterval(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getIceCheckMinInterval",
          "()Ljava/lang/Integer;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceCheckMinInterval0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceConnectionReceivingTimeout0(nullptr);
static jint Java_RTCConfiguration_getIceConnectionReceivingTimeout(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getIceConnectionReceivingTimeout",
          "()I",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceConnectionReceivingTimeout0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceServers0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getIceServers(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getIceServers",
          "()Ljava/util/List;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceServers0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceTransportsType0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getIceTransportsType(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getIceTransportsType",
          "()Lorg/webrtc/PeerConnection$IceTransportsType;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceTransportsType0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceUnwritableMinChecks0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getIceUnwritableMinChecks(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getIceUnwritableMinChecks",
          "()Ljava/lang/Integer;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceUnwritableMinChecks0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceUnwritableTimeout0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getIceUnwritableTimeout(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getIceUnwritableTimeout",
          "()Ljava/lang/Integer;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getIceUnwritableTimeout0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getKeyType0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getKeyType(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getKeyType",
          "()Lorg/webrtc/PeerConnection$KeyType;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getKeyType0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getMaxIPv6Networks0(nullptr);
static jint Java_RTCConfiguration_getMaxIPv6Networks(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getMaxIPv6Networks",
          "()I",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getMaxIPv6Networks0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getNetworkPreference0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getNetworkPreference(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getNetworkPreference",
          "()Lorg/webrtc/PeerConnection$AdapterType;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getNetworkPreference0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getOfferExtmapAllowMixed0(nullptr);
static jboolean Java_RTCConfiguration_getOfferExtmapAllowMixed(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getOfferExtmapAllowMixed",
          "()Z",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getOfferExtmapAllowMixed0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getPresumeWritableWhenFullyRelayed0(nullptr);
static jboolean Java_RTCConfiguration_getPresumeWritableWhenFullyRelayed(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getPresumeWritableWhenFullyRelayed",
          "()Z",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getPresumeWritableWhenFullyRelayed0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getPruneTurnPorts0(nullptr);
static jboolean Java_RTCConfiguration_getPruneTurnPorts(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getPruneTurnPorts",
          "()Z",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getPruneTurnPorts0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getRtcpMuxPolicy0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getRtcpMuxPolicy(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getRtcpMuxPolicy",
          "()Lorg/webrtc/PeerConnection$RtcpMuxPolicy;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getRtcpMuxPolicy0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getScreencastMinBitrate0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getScreencastMinBitrate(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getScreencastMinBitrate",
          "()Ljava/lang/Integer;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getScreencastMinBitrate0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getSdpSemantics0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getSdpSemantics(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getSdpSemantics",
          "()Lorg/webrtc/PeerConnection$SdpSemantics;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getSdpSemantics0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getStableWritableConnectionPingIntervalMs0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject>
    Java_RTCConfiguration_getStableWritableConnectionPingIntervalMs(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getStableWritableConnectionPingIntervalMs",
          "()Ljava/lang/Integer;",
&g_org_webrtc_PeerConnection_00024RTCConfiguration_getStableWritableConnectionPingIntervalMs0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getStunCandidateKeepaliveInterval0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject>
    Java_RTCConfiguration_getStunCandidateKeepaliveInterval(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getStunCandidateKeepaliveInterval",
          "()Ljava/lang/Integer;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getStunCandidateKeepaliveInterval0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getSurfaceIceCandidatesOnIceTransportTypeChanged0(nullptr);
static jboolean Java_RTCConfiguration_getSurfaceIceCandidatesOnIceTransportTypeChanged(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getSurfaceIceCandidatesOnIceTransportTypeChanged",
          "()Z",
&g_org_webrtc_PeerConnection_00024RTCConfiguration_getSurfaceIceCandidatesOnIceTransportTypeChanged0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getSuspendBelowMinBitrate0(nullptr);
static jboolean Java_RTCConfiguration_getSuspendBelowMinBitrate(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getSuspendBelowMinBitrate",
          "()Z",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getSuspendBelowMinBitrate0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getTcpCandidatePolicy0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getTcpCandidatePolicy(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getTcpCandidatePolicy",
          "()Lorg/webrtc/PeerConnection$TcpCandidatePolicy;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getTcpCandidatePolicy0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getTurnCustomizer0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getTurnCustomizer(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getTurnCustomizer",
          "()Lorg/webrtc/TurnCustomizer;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getTurnCustomizer0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getTurnLoggingId0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_RTCConfiguration_getTurnLoggingId(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getTurnLoggingId",
          "()Ljava/lang/String;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getTurnLoggingId0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024RTCConfiguration_getTurnPortPrunePolicy0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RTCConfiguration_getTurnPortPrunePolicy(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_PeerConnection_00024RTCConfiguration_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getTurnPortPrunePolicy",
          "()Lorg/webrtc/PeerConnection$PortPrunePolicy;",
          &g_org_webrtc_PeerConnection_00024RTCConfiguration_getTurnPortPrunePolicy0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_PeerConnection_00024SignalingState_fromNativeIndex1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_SignalingState_fromNativeIndex(JNIEnv* env,
    JniIntWrapper nativeIndex) {
  jclass clazz = org_webrtc_PeerConnection_00024SignalingState_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_PeerConnection_00024SignalingState_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "fromNativeIndex",
          "(I)Lorg/webrtc/PeerConnection$SignalingState;",
          &g_org_webrtc_PeerConnection_00024SignalingState_fromNativeIndex1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(nativeIndex));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_PeerConnection_JNI
