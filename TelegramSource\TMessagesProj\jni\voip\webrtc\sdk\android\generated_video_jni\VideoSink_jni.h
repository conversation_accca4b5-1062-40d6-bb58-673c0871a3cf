// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/VideoSink

#ifndef org_webrtc_VideoSink_JNI
#define org_webrtc_VideoSink_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_VideoSink[];
const char kClassPath_org_webrtc_VideoSink[] = "org/webrtc/VideoSink";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_VideoSink_clazz(nullptr);
#ifndef org_webrtc_VideoSink_clazz_defined
#define org_webrtc_VideoSink_clazz_defined
inline jclass org_webrtc_VideoSink_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_VideoSink,
      &g_org_webrtc_VideoSink_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_VideoSink_onFrame1(nullptr);
static void Java_VideoSink_onFrame(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& frame) {
  jclass clazz = org_webrtc_VideoSink_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_VideoSink_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "onFrame",
          "(Lorg/webrtc/VideoFrame;)V",
          &g_org_webrtc_VideoSink_onFrame1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, frame.obj());
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_VideoSink_JNI
