__id__ = "template_plugin"
__name__ = "Template Plugin"
__version__ = "1.0"
__author__ = "Your name or @username"
__description__ = "Template plugin description"
__min_version__ = "11.9.0"
__icon__ = "exteraPlugins/0"


class TemplatePlugin(BasePlugin):

    def on_plugin_load(self):
        self.log(f"Plugin '{self.name}' loaded successfully.")

    def on_plugin_unload(self):
        self.log(f"Plugin '{self.name}' unloaded successfully.")
