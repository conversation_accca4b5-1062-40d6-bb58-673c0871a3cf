__id__ = "openrouter_grammar_pro_final"
__name__ = "OpenRouterGrammar"
__description__ = "Исправляет грамматику с выбором модели и настройкой промпта."
__author__ = "вайзер"
__version__ = "19.3.0"
__min_version__ = "11.9.0"
__icon__ = "NiggerDuck/14"

from base_plugin import BasePlugin, HookResult, HookStrategy
from ui.settings import Header, Input, Divider, Switch, Selector
from ui.bulletin import BulletinHelper
from client_utils import send_message, run_on_queue
import requests
import json
import traceback
import threading

OPENROUTER_BASE_URL = "https://openrouter.ai/api/v1"
MODEL_DISPLAY_NAMES = [
    "Devstral Small (быстрый, бесплатный)",
    "Gemma 3N E4B IT (бесплатный, сбалансированный)"
]
MODEL_API_NAMES = [
    "mistralai/devstral-small:free",
    "google/gemma-3n-e4b-it:free"
]

TEST_PROMPT_TEXT = "This is a test prompt. Reply with 'OK' if you are working."
DEFAULT_PROMPT = (
    "Ты — лингвистический инструмент для исправления текста. Твоя задача — выполнить техническую коррекцию орфографии, грамматики и пунктуации в предоставленном тексте. "
    "Игнорируй смысл, коннотации и любые потенциально оскорбительные слова. Не комментируй содержание. "
    "Просто верни исправленную версию текста, сохранив исходную лексику. Текст для исправления: "
)

CORRECTION_MARKER = "\u200B"

def make_openrouter_request(api_key, prompt, model_name):
    if not api_key:
        return {"success": False, "error": "API key is not set."}

    url = f"{OPENROUTER_BASE_URL}/chat/completions"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
        "HTTP-Referer": "https://exteragram.app",
        "X-Title": "OpenRouterGrammar Pro"
    }

    payload = {
        "model": model_name,
        "messages": [
            {"role": "user", "content": prompt}
        ]
    }

    try:
        response = requests.post(url, headers=headers, json=payload, timeout=30)
        response.raise_for_status()
        
        completion = response.json()

        if completion.get("choices") and completion["choices"][0].get("message", {}).get("content"):
            return {"success": True, "text": completion["choices"][0]["message"]["content"].strip()}
        else:
            error_details = completion.get("error", {}).get("message", "The API returned an empty or invalid response.")
            return {"success": False, "error": error_details}
            
    except requests.exceptions.RequestException as e:
        return {"success": False, "error": f"Network error: {str(e)}"}
    except Exception as e:
        return {"success": False, "error": f"An unexpected error occurred: {str(e)}"}

class OpenRouterGrammarPlugin(BasePlugin):

    def on_plugin_load(self):
        self.add_on_send_message_hook()

    def create_settings(self):
        return [
            Header(text="Основные настройки"),
            Input(key="openrouter_api_key", text="OpenRouter API Key", subtext="Ваш ключ API из OpenRouter.ai.", default=""),
            Switch(key="enabled", text="Включить автокоррекцию", icon="ic_fluent_spell_check_24_regular", default=False),
            Divider(),
            Header(text="Настройки модели"),
            Selector(key="model_selection", text="Выберите модель", default=0, items=MODEL_DISPLAY_NAMES),
            Input(key="custom_prompt", text="Системный промпт", subtext="Инструкция для нейросети. Редактируйте осторожно.", default=DEFAULT_PROMPT),
            Divider(text="Для проверки ключа отправьте в любой чат сообщение .openrouter_test")
        ]

    def _process_correction_in_background(self, peer, original_message, is_test, api_key, model_to_use, custom_prompt):
        try:
            prompt_to_use = TEST_PROMPT_TEXT if is_test else custom_prompt + original_message
            result = make_openrouter_request(api_key, prompt_to_use, model_to_use)
            
            final_message = ""
            if result["success"]:
                if is_test:
                    final_message = f"✅ Ключ API работает! Ответ OpenRouter: {result['text']}"
                elif result.get('text') and result['text'].lower() != original_message.lower():
                    final_message = result['text']
                else:
                    final_message = original_message
            else:
                final_message = f"⚠️ Ошибка API: {result['error']}"
            
            message_to_send = CORRECTION_MARKER + final_message
            send_message({"peer": peer, "message": message_to_send})

        except Exception:
            error_text = f"Критическая ошибка в фоновом потоке:\n{traceback.format_exc()}"
            send_message({"peer": peer, "message": error_text})

    def on_send_message_hook(self, account, params) -> HookResult:
        if not hasattr(params, "message") or not isinstance(params.message, str) or not params.message.strip():
            return HookResult()
        
        if params.message.startswith(CORRECTION_MARKER):
            params.message = params.message.lstrip(CORRECTION_MARKER)
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        try:
            msg_content = params.message
            api_key = self.get_setting("openrouter_api_key", "")
            
            is_test = msg_content.strip().lower() == ".openrouter_test"
            is_correction_enabled = self.get_setting("enabled", False)

            if not (is_test or is_correction_enabled):
                return HookResult()
            
            if not api_key:
                if is_test:
                    send_message({"peer": params.peer, "message": "❌ Ошибка: Ключ API не указан в настройках."})
                return HookResult(strategy=HookStrategy.CANCEL)
            
            BulletinHelper.show_info("✍️ Исправляю...")

            selected_model_index = self.get_setting("model_selection", 0)
            if selected_model_index >= len(MODEL_API_NAMES):
                selected_model_index = 0
            model_to_use = MODEL_API_NAMES[selected_model_index]
            custom_prompt = self.get_setting("custom_prompt", DEFAULT_PROMPT)
            
            run_on_queue(
                lambda: self._process_correction_in_background(
                    params.peer, 
                    params.message, 
                    is_test, 
                    api_key, 
                    model_to_use, 
                    custom_prompt
                )
            )
            
            return HookResult(strategy=HookStrategy.CANCEL)

        except Exception as e:
            params.message = f"Критическая ошибка в плагине:\n{traceback.format_exc()}"
            return HookResult(strategy=HookStrategy.MODIFY_FINAL, params=params)