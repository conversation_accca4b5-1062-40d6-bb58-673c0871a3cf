package main

// Code generated by peg delocate.peg DO NOT EDIT.

import (
	"fmt"
	"io"
	"os"
	"sort"
	"strconv"
	"strings"
)

const endSymbol rune = 1114112

/* The rule types inferred from the grammar are below. */
type pegRule uint8

const (
	ruleUnknown pegRule = iota
	ruleAsmFile
	ruleStatement
	ruleGlobalDirective
	ruleDirective
	ruleDirectiveName
	ruleLocationDirective
	ruleFileDirective
	ruleLocDirective
	ruleArgs
	ruleArg
	ruleQuotedArg
	ruleQuotedText
	ruleSymbolDefiningDirective
	ruleSymbolDefiningDirectiveName
	ruleLabelContainingDirective
	ruleLabelContainingDirectiveName
	ruleSymbolArgs
	ruleSymbolArg
	ruleSymbolExpr
	ruleSymbolAtom
	ruleSymbolOperator
	ruleOpenParen
	ruleCloseParen
	ruleSymbolType
	ruleDot
	ruleTCMarker
	ruleEscapedChar
	ruleWS
	ruleComment
	ruleLabel
	ruleSymbolName
	ruleLocalSymbol
	ruleLocalLabel
	ruleLocalLabelRef
	ruleInstructionPrefix
	ruleInstruction
	ruleInstructionName
	ruleInstructionArg
	ruleGOTLocation
	ruleGOTAddress
	ruleGOTSymbolOffset
	ruleAVX512Token
	ruleTOCRefHigh
	ruleTOCRefLow
	ruleIndirectionIndicator
	ruleFloat
	ruleRegisterOrConstant
	ruleARMConstantTweak
	ruleARMRegister
	ruleARMVectorRegister
	ruleSVE2PredicateRegister
	ruleSVE2SpecialValue
	ruleMemoryRef
	ruleSymbolRef
	ruleLow12BitsSymbolRef
	ruleARMBaseIndexScale
	ruleARMGOTLow12
	ruleARMPostincrement
	ruleBaseIndexScale
	ruleOperator
	ruleOffset
	ruleSection
	ruleSegmentRegister
)

var rul3s = [...]string{
	"Unknown",
	"AsmFile",
	"Statement",
	"GlobalDirective",
	"Directive",
	"DirectiveName",
	"LocationDirective",
	"FileDirective",
	"LocDirective",
	"Args",
	"Arg",
	"QuotedArg",
	"QuotedText",
	"SymbolDefiningDirective",
	"SymbolDefiningDirectiveName",
	"LabelContainingDirective",
	"LabelContainingDirectiveName",
	"SymbolArgs",
	"SymbolArg",
	"SymbolExpr",
	"SymbolAtom",
	"SymbolOperator",
	"OpenParen",
	"CloseParen",
	"SymbolType",
	"Dot",
	"TCMarker",
	"EscapedChar",
	"WS",
	"Comment",
	"Label",
	"SymbolName",
	"LocalSymbol",
	"LocalLabel",
	"LocalLabelRef",
	"InstructionPrefix",
	"Instruction",
	"InstructionName",
	"InstructionArg",
	"GOTLocation",
	"GOTAddress",
	"GOTSymbolOffset",
	"AVX512Token",
	"TOCRefHigh",
	"TOCRefLow",
	"IndirectionIndicator",
	"Float",
	"RegisterOrConstant",
	"ARMConstantTweak",
	"ARMRegister",
	"ARMVectorRegister",
	"SVE2PredicateRegister",
	"SVE2SpecialValue",
	"MemoryRef",
	"SymbolRef",
	"Low12BitsSymbolRef",
	"ARMBaseIndexScale",
	"ARMGOTLow12",
	"ARMPostincrement",
	"BaseIndexScale",
	"Operator",
	"Offset",
	"Section",
	"SegmentRegister",
}

type token32 struct {
	pegRule
	begin, end uint32
}

func (t *token32) String() string {
	return fmt.Sprintf("\x1B[34m%v\x1B[m %v %v", rul3s[t.pegRule], t.begin, t.end)
}

type node32 struct {
	token32
	up, next *node32
}

func (node *node32) print(w io.Writer, pretty bool, buffer string) {
	var print func(node *node32, depth int)
	print = func(node *node32, depth int) {
		for node != nil {
			for c := 0; c < depth; c++ {
				fmt.Fprintf(w, " ")
			}
			rule := rul3s[node.pegRule]
			quote := strconv.Quote(string(([]rune(buffer)[node.begin:node.end])))
			if !pretty {
				fmt.Fprintf(w, "%v %v\n", rule, quote)
			} else {
				fmt.Fprintf(w, "\x1B[36m%v\x1B[m %v\n", rule, quote)
			}
			if node.up != nil {
				print(node.up, depth+1)
			}
			node = node.next
		}
	}
	print(node, 0)
}

func (node *node32) Print(w io.Writer, buffer string) {
	node.print(w, false, buffer)
}

func (node *node32) PrettyPrint(w io.Writer, buffer string) {
	node.print(w, true, buffer)
}

type tokens32 struct {
	tree []token32
}

func (t *tokens32) Trim(length uint32) {
	t.tree = t.tree[:length]
}

func (t *tokens32) Print() {
	for _, token := range t.tree {
		fmt.Println(token.String())
	}
}

func (t *tokens32) AST() *node32 {
	type element struct {
		node *node32
		down *element
	}
	tokens := t.Tokens()
	var stack *element
	for _, token := range tokens {
		if token.begin == token.end {
			continue
		}
		node := &node32{token32: token}
		for stack != nil && stack.node.begin >= token.begin && stack.node.end <= token.end {
			stack.node.next = node.up
			node.up = stack.node
			stack = stack.down
		}
		stack = &element{node: node, down: stack}
	}
	if stack != nil {
		return stack.node
	}
	return nil
}

func (t *tokens32) PrintSyntaxTree(buffer string) {
	t.AST().Print(os.Stdout, buffer)
}

func (t *tokens32) WriteSyntaxTree(w io.Writer, buffer string) {
	t.AST().Print(w, buffer)
}

func (t *tokens32) PrettyPrintSyntaxTree(buffer string) {
	t.AST().PrettyPrint(os.Stdout, buffer)
}

func (t *tokens32) Add(rule pegRule, begin, end, index uint32) {
	tree, i := t.tree, int(index)
	if i >= len(tree) {
		t.tree = append(tree, token32{pegRule: rule, begin: begin, end: end})
		return
	}
	tree[i] = token32{pegRule: rule, begin: begin, end: end}
}

func (t *tokens32) Tokens() []token32 {
	return t.tree
}

type Asm struct {
	Buffer string
	buffer []rune
	rules  [64]func() bool
	parse  func(rule ...int) error
	reset  func()
	Pretty bool
	tokens32
}

func (p *Asm) Parse(rule ...int) error {
	return p.parse(rule...)
}

func (p *Asm) Reset() {
	p.reset()
}

type textPosition struct {
	line, symbol int
}

type textPositionMap map[int]textPosition

func translatePositions(buffer []rune, positions []int) textPositionMap {
	length, translations, j, line, symbol := len(positions), make(textPositionMap, len(positions)), 0, 1, 0
	sort.Ints(positions)

search:
	for i, c := range buffer {
		if c == '\n' {
			line, symbol = line+1, 0
		} else {
			symbol++
		}
		if i == positions[j] {
			translations[positions[j]] = textPosition{line, symbol}
			for j++; j < length; j++ {
				if i != positions[j] {
					continue search
				}
			}
			break search
		}
	}

	return translations
}

type parseError struct {
	p   *Asm
	max token32
}

func (e *parseError) Error() string {
	tokens, err := []token32{e.max}, "\n"
	positions, p := make([]int, 2*len(tokens)), 0
	for _, token := range tokens {
		positions[p], p = int(token.begin), p+1
		positions[p], p = int(token.end), p+1
	}
	translations := translatePositions(e.p.buffer, positions)
	format := "parse error near %v (line %v symbol %v - line %v symbol %v):\n%v\n"
	if e.p.Pretty {
		format = "parse error near \x1B[34m%v\x1B[m (line %v symbol %v - line %v symbol %v):\n%v\n"
	}
	for _, token := range tokens {
		begin, end := int(token.begin), int(token.end)
		err += fmt.Sprintf(format,
			rul3s[token.pegRule],
			translations[begin].line, translations[begin].symbol,
			translations[end].line, translations[end].symbol,
			strconv.Quote(string(e.p.buffer[begin:end])))
	}

	return err
}

func (p *Asm) PrintSyntaxTree() {
	if p.Pretty {
		p.tokens32.PrettyPrintSyntaxTree(p.Buffer)
	} else {
		p.tokens32.PrintSyntaxTree(p.Buffer)
	}
}

func (p *Asm) WriteSyntaxTree(w io.Writer) {
	p.tokens32.WriteSyntaxTree(w, p.Buffer)
}

func (p *Asm) SprintSyntaxTree() string {
	var bldr strings.Builder
	p.WriteSyntaxTree(&bldr)
	return bldr.String()
}

func Pretty(pretty bool) func(*Asm) error {
	return func(p *Asm) error {
		p.Pretty = pretty
		return nil
	}
}

func Size(size int) func(*Asm) error {
	return func(p *Asm) error {
		p.tokens32 = tokens32{tree: make([]token32, 0, size)}
		return nil
	}
}
func (p *Asm) Init(options ...func(*Asm) error) error {
	var (
		max                  token32
		position, tokenIndex uint32
		buffer               []rune
	)
	for _, option := range options {
		err := option(p)
		if err != nil {
			return err
		}
	}
	p.reset = func() {
		max = token32{}
		position, tokenIndex = 0, 0

		p.buffer = []rune(p.Buffer)
		if len(p.buffer) == 0 || p.buffer[len(p.buffer)-1] != endSymbol {
			p.buffer = append(p.buffer, endSymbol)
		}
		buffer = p.buffer
	}
	p.reset()

	_rules := p.rules
	tree := p.tokens32
	p.parse = func(rule ...int) error {
		r := 1
		if len(rule) > 0 {
			r = rule[0]
		}
		matches := p.rules[r]()
		p.tokens32 = tree
		if matches {
			p.Trim(tokenIndex)
			return nil
		}
		return &parseError{p, max}
	}

	add := func(rule pegRule, begin uint32) {
		tree.Add(rule, begin, position, tokenIndex)
		tokenIndex++
		if begin != position && position > max.end {
			max = token32{rule, begin, position}
		}
	}

	matchDot := func() bool {
		if buffer[position] != endSymbol {
			position++
			return true
		}
		return false
	}

	/*matchChar := func(c byte) bool {
		if buffer[position] == c {
			position++
			return true
		}
		return false
	}*/

	/*matchRange := func(lower byte, upper byte) bool {
		if c := buffer[position]; c >= lower && c <= upper {
			position++
			return true
		}
		return false
	}*/

	_rules = [...]func() bool{
		nil,
		/* 0 AsmFile <- <(Statement* !.)> */
		func() bool {
			position0, tokenIndex0 := position, tokenIndex
			{
				position1 := position
			l2:
				{
					position3, tokenIndex3 := position, tokenIndex
					if !_rules[ruleStatement]() {
						goto l3
					}
					goto l2
				l3:
					position, tokenIndex = position3, tokenIndex3
				}
				{
					position4, tokenIndex4 := position, tokenIndex
					if !matchDot() {
						goto l4
					}
					goto l0
				l4:
					position, tokenIndex = position4, tokenIndex4
				}
				add(ruleAsmFile, position1)
			}
			return true
		l0:
			position, tokenIndex = position0, tokenIndex0
			return false
		},
		/* 1 Statement <- <(WS? (Label / ((GlobalDirective / LocationDirective / SymbolDefiningDirective / LabelContainingDirective / Instruction / Directive / Comment / ) WS? ((Comment? '\n') / ';'))))> */
		func() bool {
			position5, tokenIndex5 := position, tokenIndex
			{
				position6 := position
				{
					position7, tokenIndex7 := position, tokenIndex
					if !_rules[ruleWS]() {
						goto l7
					}
					goto l8
				l7:
					position, tokenIndex = position7, tokenIndex7
				}
			l8:
				{
					position9, tokenIndex9 := position, tokenIndex
					if !_rules[ruleLabel]() {
						goto l10
					}
					goto l9
				l10:
					position, tokenIndex = position9, tokenIndex9
					{
						position11, tokenIndex11 := position, tokenIndex
						if !_rules[ruleGlobalDirective]() {
							goto l12
						}
						goto l11
					l12:
						position, tokenIndex = position11, tokenIndex11
						if !_rules[ruleLocationDirective]() {
							goto l13
						}
						goto l11
					l13:
						position, tokenIndex = position11, tokenIndex11
						if !_rules[ruleSymbolDefiningDirective]() {
							goto l14
						}
						goto l11
					l14:
						position, tokenIndex = position11, tokenIndex11
						if !_rules[ruleLabelContainingDirective]() {
							goto l15
						}
						goto l11
					l15:
						position, tokenIndex = position11, tokenIndex11
						if !_rules[ruleInstruction]() {
							goto l16
						}
						goto l11
					l16:
						position, tokenIndex = position11, tokenIndex11
						if !_rules[ruleDirective]() {
							goto l17
						}
						goto l11
					l17:
						position, tokenIndex = position11, tokenIndex11
						if !_rules[ruleComment]() {
							goto l18
						}
						goto l11
					l18:
						position, tokenIndex = position11, tokenIndex11
					}
				l11:
					{
						position19, tokenIndex19 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l19
						}
						goto l20
					l19:
						position, tokenIndex = position19, tokenIndex19
					}
				l20:
					{
						position21, tokenIndex21 := position, tokenIndex
						{
							position23, tokenIndex23 := position, tokenIndex
							if !_rules[ruleComment]() {
								goto l23
							}
							goto l24
						l23:
							position, tokenIndex = position23, tokenIndex23
						}
					l24:
						if buffer[position] != rune('\n') {
							goto l22
						}
						position++
						goto l21
					l22:
						position, tokenIndex = position21, tokenIndex21
						if buffer[position] != rune(';') {
							goto l5
						}
						position++
					}
				l21:
				}
			l9:
				add(ruleStatement, position6)
			}
			return true
		l5:
			position, tokenIndex = position5, tokenIndex5
			return false
		},
		/* 2 GlobalDirective <- <((('.' ('g' / 'G') ('l' / 'L') ('o' / 'O') ('b' / 'B') ('a' / 'A') ('l' / 'L')) / ('.' ('g' / 'G') ('l' / 'L') ('o' / 'O') ('b' / 'B') ('l' / 'L'))) WS SymbolName)> */
		func() bool {
			position25, tokenIndex25 := position, tokenIndex
			{
				position26 := position
				{
					position27, tokenIndex27 := position, tokenIndex
					if buffer[position] != rune('.') {
						goto l28
					}
					position++
					{
						position29, tokenIndex29 := position, tokenIndex
						if buffer[position] != rune('g') {
							goto l30
						}
						position++
						goto l29
					l30:
						position, tokenIndex = position29, tokenIndex29
						if buffer[position] != rune('G') {
							goto l28
						}
						position++
					}
				l29:
					{
						position31, tokenIndex31 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l32
						}
						position++
						goto l31
					l32:
						position, tokenIndex = position31, tokenIndex31
						if buffer[position] != rune('L') {
							goto l28
						}
						position++
					}
				l31:
					{
						position33, tokenIndex33 := position, tokenIndex
						if buffer[position] != rune('o') {
							goto l34
						}
						position++
						goto l33
					l34:
						position, tokenIndex = position33, tokenIndex33
						if buffer[position] != rune('O') {
							goto l28
						}
						position++
					}
				l33:
					{
						position35, tokenIndex35 := position, tokenIndex
						if buffer[position] != rune('b') {
							goto l36
						}
						position++
						goto l35
					l36:
						position, tokenIndex = position35, tokenIndex35
						if buffer[position] != rune('B') {
							goto l28
						}
						position++
					}
				l35:
					{
						position37, tokenIndex37 := position, tokenIndex
						if buffer[position] != rune('a') {
							goto l38
						}
						position++
						goto l37
					l38:
						position, tokenIndex = position37, tokenIndex37
						if buffer[position] != rune('A') {
							goto l28
						}
						position++
					}
				l37:
					{
						position39, tokenIndex39 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l40
						}
						position++
						goto l39
					l40:
						position, tokenIndex = position39, tokenIndex39
						if buffer[position] != rune('L') {
							goto l28
						}
						position++
					}
				l39:
					goto l27
				l28:
					position, tokenIndex = position27, tokenIndex27
					if buffer[position] != rune('.') {
						goto l25
					}
					position++
					{
						position41, tokenIndex41 := position, tokenIndex
						if buffer[position] != rune('g') {
							goto l42
						}
						position++
						goto l41
					l42:
						position, tokenIndex = position41, tokenIndex41
						if buffer[position] != rune('G') {
							goto l25
						}
						position++
					}
				l41:
					{
						position43, tokenIndex43 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l44
						}
						position++
						goto l43
					l44:
						position, tokenIndex = position43, tokenIndex43
						if buffer[position] != rune('L') {
							goto l25
						}
						position++
					}
				l43:
					{
						position45, tokenIndex45 := position, tokenIndex
						if buffer[position] != rune('o') {
							goto l46
						}
						position++
						goto l45
					l46:
						position, tokenIndex = position45, tokenIndex45
						if buffer[position] != rune('O') {
							goto l25
						}
						position++
					}
				l45:
					{
						position47, tokenIndex47 := position, tokenIndex
						if buffer[position] != rune('b') {
							goto l48
						}
						position++
						goto l47
					l48:
						position, tokenIndex = position47, tokenIndex47
						if buffer[position] != rune('B') {
							goto l25
						}
						position++
					}
				l47:
					{
						position49, tokenIndex49 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l50
						}
						position++
						goto l49
					l50:
						position, tokenIndex = position49, tokenIndex49
						if buffer[position] != rune('L') {
							goto l25
						}
						position++
					}
				l49:
				}
			l27:
				if !_rules[ruleWS]() {
					goto l25
				}
				if !_rules[ruleSymbolName]() {
					goto l25
				}
				add(ruleGlobalDirective, position26)
			}
			return true
		l25:
			position, tokenIndex = position25, tokenIndex25
			return false
		},
		/* 3 Directive <- <('.' DirectiveName (WS Args)?)> */
		func() bool {
			position51, tokenIndex51 := position, tokenIndex
			{
				position52 := position
				if buffer[position] != rune('.') {
					goto l51
				}
				position++
				if !_rules[ruleDirectiveName]() {
					goto l51
				}
				{
					position53, tokenIndex53 := position, tokenIndex
					if !_rules[ruleWS]() {
						goto l53
					}
					if !_rules[ruleArgs]() {
						goto l53
					}
					goto l54
				l53:
					position, tokenIndex = position53, tokenIndex53
				}
			l54:
				add(ruleDirective, position52)
			}
			return true
		l51:
			position, tokenIndex = position51, tokenIndex51
			return false
		},
		/* 4 DirectiveName <- <([a-z] / [A-Z] / ([0-9] / [0-9]) / '_')+> */
		func() bool {
			position55, tokenIndex55 := position, tokenIndex
			{
				position56 := position
				{
					position59, tokenIndex59 := position, tokenIndex
					if c := buffer[position]; c < rune('a') || c > rune('z') {
						goto l60
					}
					position++
					goto l59
				l60:
					position, tokenIndex = position59, tokenIndex59
					if c := buffer[position]; c < rune('A') || c > rune('Z') {
						goto l61
					}
					position++
					goto l59
				l61:
					position, tokenIndex = position59, tokenIndex59
					{
						position63, tokenIndex63 := position, tokenIndex
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l64
						}
						position++
						goto l63
					l64:
						position, tokenIndex = position63, tokenIndex63
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l62
						}
						position++
					}
				l63:
					goto l59
				l62:
					position, tokenIndex = position59, tokenIndex59
					if buffer[position] != rune('_') {
						goto l55
					}
					position++
				}
			l59:
			l57:
				{
					position58, tokenIndex58 := position, tokenIndex
					{
						position65, tokenIndex65 := position, tokenIndex
						if c := buffer[position]; c < rune('a') || c > rune('z') {
							goto l66
						}
						position++
						goto l65
					l66:
						position, tokenIndex = position65, tokenIndex65
						if c := buffer[position]; c < rune('A') || c > rune('Z') {
							goto l67
						}
						position++
						goto l65
					l67:
						position, tokenIndex = position65, tokenIndex65
						{
							position69, tokenIndex69 := position, tokenIndex
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l70
							}
							position++
							goto l69
						l70:
							position, tokenIndex = position69, tokenIndex69
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l68
							}
							position++
						}
					l69:
						goto l65
					l68:
						position, tokenIndex = position65, tokenIndex65
						if buffer[position] != rune('_') {
							goto l58
						}
						position++
					}
				l65:
					goto l57
				l58:
					position, tokenIndex = position58, tokenIndex58
				}
				add(ruleDirectiveName, position56)
			}
			return true
		l55:
			position, tokenIndex = position55, tokenIndex55
			return false
		},
		/* 5 LocationDirective <- <(FileDirective / LocDirective)> */
		func() bool {
			position71, tokenIndex71 := position, tokenIndex
			{
				position72 := position
				{
					position73, tokenIndex73 := position, tokenIndex
					if !_rules[ruleFileDirective]() {
						goto l74
					}
					goto l73
				l74:
					position, tokenIndex = position73, tokenIndex73
					if !_rules[ruleLocDirective]() {
						goto l71
					}
				}
			l73:
				add(ruleLocationDirective, position72)
			}
			return true
		l71:
			position, tokenIndex = position71, tokenIndex71
			return false
		},
		/* 6 FileDirective <- <('.' ('f' / 'F') ('i' / 'I') ('l' / 'L') ('e' / 'E') WS (!('#' / '\n') .)+)> */
		func() bool {
			position75, tokenIndex75 := position, tokenIndex
			{
				position76 := position
				if buffer[position] != rune('.') {
					goto l75
				}
				position++
				{
					position77, tokenIndex77 := position, tokenIndex
					if buffer[position] != rune('f') {
						goto l78
					}
					position++
					goto l77
				l78:
					position, tokenIndex = position77, tokenIndex77
					if buffer[position] != rune('F') {
						goto l75
					}
					position++
				}
			l77:
				{
					position79, tokenIndex79 := position, tokenIndex
					if buffer[position] != rune('i') {
						goto l80
					}
					position++
					goto l79
				l80:
					position, tokenIndex = position79, tokenIndex79
					if buffer[position] != rune('I') {
						goto l75
					}
					position++
				}
			l79:
				{
					position81, tokenIndex81 := position, tokenIndex
					if buffer[position] != rune('l') {
						goto l82
					}
					position++
					goto l81
				l82:
					position, tokenIndex = position81, tokenIndex81
					if buffer[position] != rune('L') {
						goto l75
					}
					position++
				}
			l81:
				{
					position83, tokenIndex83 := position, tokenIndex
					if buffer[position] != rune('e') {
						goto l84
					}
					position++
					goto l83
				l84:
					position, tokenIndex = position83, tokenIndex83
					if buffer[position] != rune('E') {
						goto l75
					}
					position++
				}
			l83:
				if !_rules[ruleWS]() {
					goto l75
				}
				{
					position87, tokenIndex87 := position, tokenIndex
					{
						position88, tokenIndex88 := position, tokenIndex
						if buffer[position] != rune('#') {
							goto l89
						}
						position++
						goto l88
					l89:
						position, tokenIndex = position88, tokenIndex88
						if buffer[position] != rune('\n') {
							goto l87
						}
						position++
					}
				l88:
					goto l75
				l87:
					position, tokenIndex = position87, tokenIndex87
				}
				if !matchDot() {
					goto l75
				}
			l85:
				{
					position86, tokenIndex86 := position, tokenIndex
					{
						position90, tokenIndex90 := position, tokenIndex
						{
							position91, tokenIndex91 := position, tokenIndex
							if buffer[position] != rune('#') {
								goto l92
							}
							position++
							goto l91
						l92:
							position, tokenIndex = position91, tokenIndex91
							if buffer[position] != rune('\n') {
								goto l90
							}
							position++
						}
					l91:
						goto l86
					l90:
						position, tokenIndex = position90, tokenIndex90
					}
					if !matchDot() {
						goto l86
					}
					goto l85
				l86:
					position, tokenIndex = position86, tokenIndex86
				}
				add(ruleFileDirective, position76)
			}
			return true
		l75:
			position, tokenIndex = position75, tokenIndex75
			return false
		},
		/* 7 LocDirective <- <('.' ('l' / 'L') ('o' / 'O') ('c' / 'C') WS (!('#' / '/' / '\n') .)+)> */
		func() bool {
			position93, tokenIndex93 := position, tokenIndex
			{
				position94 := position
				if buffer[position] != rune('.') {
					goto l93
				}
				position++
				{
					position95, tokenIndex95 := position, tokenIndex
					if buffer[position] != rune('l') {
						goto l96
					}
					position++
					goto l95
				l96:
					position, tokenIndex = position95, tokenIndex95
					if buffer[position] != rune('L') {
						goto l93
					}
					position++
				}
			l95:
				{
					position97, tokenIndex97 := position, tokenIndex
					if buffer[position] != rune('o') {
						goto l98
					}
					position++
					goto l97
				l98:
					position, tokenIndex = position97, tokenIndex97
					if buffer[position] != rune('O') {
						goto l93
					}
					position++
				}
			l97:
				{
					position99, tokenIndex99 := position, tokenIndex
					if buffer[position] != rune('c') {
						goto l100
					}
					position++
					goto l99
				l100:
					position, tokenIndex = position99, tokenIndex99
					if buffer[position] != rune('C') {
						goto l93
					}
					position++
				}
			l99:
				if !_rules[ruleWS]() {
					goto l93
				}
				{
					position103, tokenIndex103 := position, tokenIndex
					{
						position104, tokenIndex104 := position, tokenIndex
						if buffer[position] != rune('#') {
							goto l105
						}
						position++
						goto l104
					l105:
						position, tokenIndex = position104, tokenIndex104
						if buffer[position] != rune('/') {
							goto l106
						}
						position++
						goto l104
					l106:
						position, tokenIndex = position104, tokenIndex104
						if buffer[position] != rune('\n') {
							goto l103
						}
						position++
					}
				l104:
					goto l93
				l103:
					position, tokenIndex = position103, tokenIndex103
				}
				if !matchDot() {
					goto l93
				}
			l101:
				{
					position102, tokenIndex102 := position, tokenIndex
					{
						position107, tokenIndex107 := position, tokenIndex
						{
							position108, tokenIndex108 := position, tokenIndex
							if buffer[position] != rune('#') {
								goto l109
							}
							position++
							goto l108
						l109:
							position, tokenIndex = position108, tokenIndex108
							if buffer[position] != rune('/') {
								goto l110
							}
							position++
							goto l108
						l110:
							position, tokenIndex = position108, tokenIndex108
							if buffer[position] != rune('\n') {
								goto l107
							}
							position++
						}
					l108:
						goto l102
					l107:
						position, tokenIndex = position107, tokenIndex107
					}
					if !matchDot() {
						goto l102
					}
					goto l101
				l102:
					position, tokenIndex = position102, tokenIndex102
				}
				add(ruleLocDirective, position94)
			}
			return true
		l93:
			position, tokenIndex = position93, tokenIndex93
			return false
		},
		/* 8 Args <- <(Arg (WS? ',' WS? Arg)*)> */
		func() bool {
			position111, tokenIndex111 := position, tokenIndex
			{
				position112 := position
				if !_rules[ruleArg]() {
					goto l111
				}
			l113:
				{
					position114, tokenIndex114 := position, tokenIndex
					{
						position115, tokenIndex115 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l115
						}
						goto l116
					l115:
						position, tokenIndex = position115, tokenIndex115
					}
				l116:
					if buffer[position] != rune(',') {
						goto l114
					}
					position++
					{
						position117, tokenIndex117 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l117
						}
						goto l118
					l117:
						position, tokenIndex = position117, tokenIndex117
					}
				l118:
					if !_rules[ruleArg]() {
						goto l114
					}
					goto l113
				l114:
					position, tokenIndex = position114, tokenIndex114
				}
				add(ruleArgs, position112)
			}
			return true
		l111:
			position, tokenIndex = position111, tokenIndex111
			return false
		},
		/* 9 Arg <- <(QuotedArg / ([0-9] / [0-9] / ([a-z] / [A-Z]) / '%' / '+' / '-' / '*' / '_' / '@' / '.' / '$')*)> */
		func() bool {
			{
				position120 := position
				{
					position121, tokenIndex121 := position, tokenIndex
					if !_rules[ruleQuotedArg]() {
						goto l122
					}
					goto l121
				l122:
					position, tokenIndex = position121, tokenIndex121
				l123:
					{
						position124, tokenIndex124 := position, tokenIndex
						{
							position125, tokenIndex125 := position, tokenIndex
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l126
							}
							position++
							goto l125
						l126:
							position, tokenIndex = position125, tokenIndex125
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l127
							}
							position++
							goto l125
						l127:
							position, tokenIndex = position125, tokenIndex125
							{
								position129, tokenIndex129 := position, tokenIndex
								if c := buffer[position]; c < rune('a') || c > rune('z') {
									goto l130
								}
								position++
								goto l129
							l130:
								position, tokenIndex = position129, tokenIndex129
								if c := buffer[position]; c < rune('A') || c > rune('Z') {
									goto l128
								}
								position++
							}
						l129:
							goto l125
						l128:
							position, tokenIndex = position125, tokenIndex125
							if buffer[position] != rune('%') {
								goto l131
							}
							position++
							goto l125
						l131:
							position, tokenIndex = position125, tokenIndex125
							if buffer[position] != rune('+') {
								goto l132
							}
							position++
							goto l125
						l132:
							position, tokenIndex = position125, tokenIndex125
							if buffer[position] != rune('-') {
								goto l133
							}
							position++
							goto l125
						l133:
							position, tokenIndex = position125, tokenIndex125
							if buffer[position] != rune('*') {
								goto l134
							}
							position++
							goto l125
						l134:
							position, tokenIndex = position125, tokenIndex125
							if buffer[position] != rune('_') {
								goto l135
							}
							position++
							goto l125
						l135:
							position, tokenIndex = position125, tokenIndex125
							if buffer[position] != rune('@') {
								goto l136
							}
							position++
							goto l125
						l136:
							position, tokenIndex = position125, tokenIndex125
							if buffer[position] != rune('.') {
								goto l137
							}
							position++
							goto l125
						l137:
							position, tokenIndex = position125, tokenIndex125
							if buffer[position] != rune('$') {
								goto l124
							}
							position++
						}
					l125:
						goto l123
					l124:
						position, tokenIndex = position124, tokenIndex124
					}
				}
			l121:
				add(ruleArg, position120)
			}
			return true
		},
		/* 10 QuotedArg <- <('"' QuotedText '"')> */
		func() bool {
			position138, tokenIndex138 := position, tokenIndex
			{
				position139 := position
				if buffer[position] != rune('"') {
					goto l138
				}
				position++
				if !_rules[ruleQuotedText]() {
					goto l138
				}
				if buffer[position] != rune('"') {
					goto l138
				}
				position++
				add(ruleQuotedArg, position139)
			}
			return true
		l138:
			position, tokenIndex = position138, tokenIndex138
			return false
		},
		/* 11 QuotedText <- <(EscapedChar / (!'"' .))*> */
		func() bool {
			{
				position141 := position
			l142:
				{
					position143, tokenIndex143 := position, tokenIndex
					{
						position144, tokenIndex144 := position, tokenIndex
						if !_rules[ruleEscapedChar]() {
							goto l145
						}
						goto l144
					l145:
						position, tokenIndex = position144, tokenIndex144
						{
							position146, tokenIndex146 := position, tokenIndex
							if buffer[position] != rune('"') {
								goto l146
							}
							position++
							goto l143
						l146:
							position, tokenIndex = position146, tokenIndex146
						}
						if !matchDot() {
							goto l143
						}
					}
				l144:
					goto l142
				l143:
					position, tokenIndex = position143, tokenIndex143
				}
				add(ruleQuotedText, position141)
			}
			return true
		},
		/* 12 SymbolDefiningDirective <- <(SymbolDefiningDirectiveName WS (LocalSymbol / SymbolName) WS? ',' WS? SymbolArg)> */
		func() bool {
			position147, tokenIndex147 := position, tokenIndex
			{
				position148 := position
				if !_rules[ruleSymbolDefiningDirectiveName]() {
					goto l147
				}
				if !_rules[ruleWS]() {
					goto l147
				}
				{
					position149, tokenIndex149 := position, tokenIndex
					if !_rules[ruleLocalSymbol]() {
						goto l150
					}
					goto l149
				l150:
					position, tokenIndex = position149, tokenIndex149
					if !_rules[ruleSymbolName]() {
						goto l147
					}
				}
			l149:
				{
					position151, tokenIndex151 := position, tokenIndex
					if !_rules[ruleWS]() {
						goto l151
					}
					goto l152
				l151:
					position, tokenIndex = position151, tokenIndex151
				}
			l152:
				if buffer[position] != rune(',') {
					goto l147
				}
				position++
				{
					position153, tokenIndex153 := position, tokenIndex
					if !_rules[ruleWS]() {
						goto l153
					}
					goto l154
				l153:
					position, tokenIndex = position153, tokenIndex153
				}
			l154:
				if !_rules[ruleSymbolArg]() {
					goto l147
				}
				add(ruleSymbolDefiningDirective, position148)
			}
			return true
		l147:
			position, tokenIndex = position147, tokenIndex147
			return false
		},
		/* 13 SymbolDefiningDirectiveName <- <(('.' ('e' / 'E') ('q' / 'Q') ('u' / 'U') ('i' / 'I') ('v' / 'V')) / ('.' ('e' / 'E') ('q' / 'Q') ('u' / 'U')) / ('.' ('s' / 'S') ('e' / 'E') ('t' / 'T')))> */
		func() bool {
			position155, tokenIndex155 := position, tokenIndex
			{
				position156 := position
				{
					position157, tokenIndex157 := position, tokenIndex
					if buffer[position] != rune('.') {
						goto l158
					}
					position++
					{
						position159, tokenIndex159 := position, tokenIndex
						if buffer[position] != rune('e') {
							goto l160
						}
						position++
						goto l159
					l160:
						position, tokenIndex = position159, tokenIndex159
						if buffer[position] != rune('E') {
							goto l158
						}
						position++
					}
				l159:
					{
						position161, tokenIndex161 := position, tokenIndex
						if buffer[position] != rune('q') {
							goto l162
						}
						position++
						goto l161
					l162:
						position, tokenIndex = position161, tokenIndex161
						if buffer[position] != rune('Q') {
							goto l158
						}
						position++
					}
				l161:
					{
						position163, tokenIndex163 := position, tokenIndex
						if buffer[position] != rune('u') {
							goto l164
						}
						position++
						goto l163
					l164:
						position, tokenIndex = position163, tokenIndex163
						if buffer[position] != rune('U') {
							goto l158
						}
						position++
					}
				l163:
					{
						position165, tokenIndex165 := position, tokenIndex
						if buffer[position] != rune('i') {
							goto l166
						}
						position++
						goto l165
					l166:
						position, tokenIndex = position165, tokenIndex165
						if buffer[position] != rune('I') {
							goto l158
						}
						position++
					}
				l165:
					{
						position167, tokenIndex167 := position, tokenIndex
						if buffer[position] != rune('v') {
							goto l168
						}
						position++
						goto l167
					l168:
						position, tokenIndex = position167, tokenIndex167
						if buffer[position] != rune('V') {
							goto l158
						}
						position++
					}
				l167:
					goto l157
				l158:
					position, tokenIndex = position157, tokenIndex157
					if buffer[position] != rune('.') {
						goto l169
					}
					position++
					{
						position170, tokenIndex170 := position, tokenIndex
						if buffer[position] != rune('e') {
							goto l171
						}
						position++
						goto l170
					l171:
						position, tokenIndex = position170, tokenIndex170
						if buffer[position] != rune('E') {
							goto l169
						}
						position++
					}
				l170:
					{
						position172, tokenIndex172 := position, tokenIndex
						if buffer[position] != rune('q') {
							goto l173
						}
						position++
						goto l172
					l173:
						position, tokenIndex = position172, tokenIndex172
						if buffer[position] != rune('Q') {
							goto l169
						}
						position++
					}
				l172:
					{
						position174, tokenIndex174 := position, tokenIndex
						if buffer[position] != rune('u') {
							goto l175
						}
						position++
						goto l174
					l175:
						position, tokenIndex = position174, tokenIndex174
						if buffer[position] != rune('U') {
							goto l169
						}
						position++
					}
				l174:
					goto l157
				l169:
					position, tokenIndex = position157, tokenIndex157
					if buffer[position] != rune('.') {
						goto l155
					}
					position++
					{
						position176, tokenIndex176 := position, tokenIndex
						if buffer[position] != rune('s') {
							goto l177
						}
						position++
						goto l176
					l177:
						position, tokenIndex = position176, tokenIndex176
						if buffer[position] != rune('S') {
							goto l155
						}
						position++
					}
				l176:
					{
						position178, tokenIndex178 := position, tokenIndex
						if buffer[position] != rune('e') {
							goto l179
						}
						position++
						goto l178
					l179:
						position, tokenIndex = position178, tokenIndex178
						if buffer[position] != rune('E') {
							goto l155
						}
						position++
					}
				l178:
					{
						position180, tokenIndex180 := position, tokenIndex
						if buffer[position] != rune('t') {
							goto l181
						}
						position++
						goto l180
					l181:
						position, tokenIndex = position180, tokenIndex180
						if buffer[position] != rune('T') {
							goto l155
						}
						position++
					}
				l180:
				}
			l157:
				add(ruleSymbolDefiningDirectiveName, position156)
			}
			return true
		l155:
			position, tokenIndex = position155, tokenIndex155
			return false
		},
		/* 14 LabelContainingDirective <- <(LabelContainingDirectiveName WS SymbolArgs)> */
		func() bool {
			position182, tokenIndex182 := position, tokenIndex
			{
				position183 := position
				if !_rules[ruleLabelContainingDirectiveName]() {
					goto l182
				}
				if !_rules[ruleWS]() {
					goto l182
				}
				if !_rules[ruleSymbolArgs]() {
					goto l182
				}
				add(ruleLabelContainingDirective, position183)
			}
			return true
		l182:
			position, tokenIndex = position182, tokenIndex182
			return false
		},
		/* 15 LabelContainingDirectiveName <- <(('.' ('x' / 'X') ('w' / 'W') ('o' / 'O') ('r' / 'R') ('d' / 'D')) / ('.' ('w' / 'W') ('o' / 'O') ('r' / 'R') ('d' / 'D')) / ('.' ('h' / 'H') ('w' / 'W') ('o' / 'O') ('r' / 'R') ('d' / 'D')) / ('.' ('l' / 'L') ('o' / 'O') ('n' / 'N') ('g' / 'G')) / ('.' ('b' / 'B') ('y' / 'Y') ('t' / 'T') ('e' / 'E')) / ('.' '8' ('b' / 'B') ('y' / 'Y') ('t' / 'T') ('e' / 'E')) / ('.' '4' ('b' / 'B') ('y' / 'Y') ('t' / 'T') ('e' / 'E')) / ('.' ('q' / 'Q') ('u' / 'U') ('a' / 'A') ('d' / 'D')) / ('.' ('t' / 'T') ('c' / 'C')) / ('.' ('l' / 'L') ('o' / 'O') ('c' / 'C') ('a' / 'A') ('l' / 'L') ('e' / 'E') ('n' / 'N') ('t' / 'T') ('r' / 'R') ('y' / 'Y')) / ('.' ('s' / 'S') ('i' / 'I') ('z' / 'Z') ('e' / 'E')) / ('.' ('t' / 'T') ('y' / 'Y') ('p' / 'P') ('e' / 'E')) / ('.' ('u' / 'U') ('l' / 'L') ('e' / 'E') ('b' / 'B') '1' '2' '8') / ('.' ('s' / 'S') ('l' / 'L') ('e' / 'E') ('b' / 'B') '1' '2' '8'))> */
		func() bool {
			position184, tokenIndex184 := position, tokenIndex
			{
				position185 := position
				{
					position186, tokenIndex186 := position, tokenIndex
					if buffer[position] != rune('.') {
						goto l187
					}
					position++
					{
						position188, tokenIndex188 := position, tokenIndex
						if buffer[position] != rune('x') {
							goto l189
						}
						position++
						goto l188
					l189:
						position, tokenIndex = position188, tokenIndex188
						if buffer[position] != rune('X') {
							goto l187
						}
						position++
					}
				l188:
					{
						position190, tokenIndex190 := position, tokenIndex
						if buffer[position] != rune('w') {
							goto l191
						}
						position++
						goto l190
					l191:
						position, tokenIndex = position190, tokenIndex190
						if buffer[position] != rune('W') {
							goto l187
						}
						position++
					}
				l190:
					{
						position192, tokenIndex192 := position, tokenIndex
						if buffer[position] != rune('o') {
							goto l193
						}
						position++
						goto l192
					l193:
						position, tokenIndex = position192, tokenIndex192
						if buffer[position] != rune('O') {
							goto l187
						}
						position++
					}
				l192:
					{
						position194, tokenIndex194 := position, tokenIndex
						if buffer[position] != rune('r') {
							goto l195
						}
						position++
						goto l194
					l195:
						position, tokenIndex = position194, tokenIndex194
						if buffer[position] != rune('R') {
							goto l187
						}
						position++
					}
				l194:
					{
						position196, tokenIndex196 := position, tokenIndex
						if buffer[position] != rune('d') {
							goto l197
						}
						position++
						goto l196
					l197:
						position, tokenIndex = position196, tokenIndex196
						if buffer[position] != rune('D') {
							goto l187
						}
						position++
					}
				l196:
					goto l186
				l187:
					position, tokenIndex = position186, tokenIndex186
					if buffer[position] != rune('.') {
						goto l198
					}
					position++
					{
						position199, tokenIndex199 := position, tokenIndex
						if buffer[position] != rune('w') {
							goto l200
						}
						position++
						goto l199
					l200:
						position, tokenIndex = position199, tokenIndex199
						if buffer[position] != rune('W') {
							goto l198
						}
						position++
					}
				l199:
					{
						position201, tokenIndex201 := position, tokenIndex
						if buffer[position] != rune('o') {
							goto l202
						}
						position++
						goto l201
					l202:
						position, tokenIndex = position201, tokenIndex201
						if buffer[position] != rune('O') {
							goto l198
						}
						position++
					}
				l201:
					{
						position203, tokenIndex203 := position, tokenIndex
						if buffer[position] != rune('r') {
							goto l204
						}
						position++
						goto l203
					l204:
						position, tokenIndex = position203, tokenIndex203
						if buffer[position] != rune('R') {
							goto l198
						}
						position++
					}
				l203:
					{
						position205, tokenIndex205 := position, tokenIndex
						if buffer[position] != rune('d') {
							goto l206
						}
						position++
						goto l205
					l206:
						position, tokenIndex = position205, tokenIndex205
						if buffer[position] != rune('D') {
							goto l198
						}
						position++
					}
				l205:
					goto l186
				l198:
					position, tokenIndex = position186, tokenIndex186
					if buffer[position] != rune('.') {
						goto l207
					}
					position++
					{
						position208, tokenIndex208 := position, tokenIndex
						if buffer[position] != rune('h') {
							goto l209
						}
						position++
						goto l208
					l209:
						position, tokenIndex = position208, tokenIndex208
						if buffer[position] != rune('H') {
							goto l207
						}
						position++
					}
				l208:
					{
						position210, tokenIndex210 := position, tokenIndex
						if buffer[position] != rune('w') {
							goto l211
						}
						position++
						goto l210
					l211:
						position, tokenIndex = position210, tokenIndex210
						if buffer[position] != rune('W') {
							goto l207
						}
						position++
					}
				l210:
					{
						position212, tokenIndex212 := position, tokenIndex
						if buffer[position] != rune('o') {
							goto l213
						}
						position++
						goto l212
					l213:
						position, tokenIndex = position212, tokenIndex212
						if buffer[position] != rune('O') {
							goto l207
						}
						position++
					}
				l212:
					{
						position214, tokenIndex214 := position, tokenIndex
						if buffer[position] != rune('r') {
							goto l215
						}
						position++
						goto l214
					l215:
						position, tokenIndex = position214, tokenIndex214
						if buffer[position] != rune('R') {
							goto l207
						}
						position++
					}
				l214:
					{
						position216, tokenIndex216 := position, tokenIndex
						if buffer[position] != rune('d') {
							goto l217
						}
						position++
						goto l216
					l217:
						position, tokenIndex = position216, tokenIndex216
						if buffer[position] != rune('D') {
							goto l207
						}
						position++
					}
				l216:
					goto l186
				l207:
					position, tokenIndex = position186, tokenIndex186
					if buffer[position] != rune('.') {
						goto l218
					}
					position++
					{
						position219, tokenIndex219 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l220
						}
						position++
						goto l219
					l220:
						position, tokenIndex = position219, tokenIndex219
						if buffer[position] != rune('L') {
							goto l218
						}
						position++
					}
				l219:
					{
						position221, tokenIndex221 := position, tokenIndex
						if buffer[position] != rune('o') {
							goto l222
						}
						position++
						goto l221
					l222:
						position, tokenIndex = position221, tokenIndex221
						if buffer[position] != rune('O') {
							goto l218
						}
						position++
					}
				l221:
					{
						position223, tokenIndex223 := position, tokenIndex
						if buffer[position] != rune('n') {
							goto l224
						}
						position++
						goto l223
					l224:
						position, tokenIndex = position223, tokenIndex223
						if buffer[position] != rune('N') {
							goto l218
						}
						position++
					}
				l223:
					{
						position225, tokenIndex225 := position, tokenIndex
						if buffer[position] != rune('g') {
							goto l226
						}
						position++
						goto l225
					l226:
						position, tokenIndex = position225, tokenIndex225
						if buffer[position] != rune('G') {
							goto l218
						}
						position++
					}
				l225:
					goto l186
				l218:
					position, tokenIndex = position186, tokenIndex186
					if buffer[position] != rune('.') {
						goto l227
					}
					position++
					{
						position228, tokenIndex228 := position, tokenIndex
						if buffer[position] != rune('b') {
							goto l229
						}
						position++
						goto l228
					l229:
						position, tokenIndex = position228, tokenIndex228
						if buffer[position] != rune('B') {
							goto l227
						}
						position++
					}
				l228:
					{
						position230, tokenIndex230 := position, tokenIndex
						if buffer[position] != rune('y') {
							goto l231
						}
						position++
						goto l230
					l231:
						position, tokenIndex = position230, tokenIndex230
						if buffer[position] != rune('Y') {
							goto l227
						}
						position++
					}
				l230:
					{
						position232, tokenIndex232 := position, tokenIndex
						if buffer[position] != rune('t') {
							goto l233
						}
						position++
						goto l232
					l233:
						position, tokenIndex = position232, tokenIndex232
						if buffer[position] != rune('T') {
							goto l227
						}
						position++
					}
				l232:
					{
						position234, tokenIndex234 := position, tokenIndex
						if buffer[position] != rune('e') {
							goto l235
						}
						position++
						goto l234
					l235:
						position, tokenIndex = position234, tokenIndex234
						if buffer[position] != rune('E') {
							goto l227
						}
						position++
					}
				l234:
					goto l186
				l227:
					position, tokenIndex = position186, tokenIndex186
					if buffer[position] != rune('.') {
						goto l236
					}
					position++
					if buffer[position] != rune('8') {
						goto l236
					}
					position++
					{
						position237, tokenIndex237 := position, tokenIndex
						if buffer[position] != rune('b') {
							goto l238
						}
						position++
						goto l237
					l238:
						position, tokenIndex = position237, tokenIndex237
						if buffer[position] != rune('B') {
							goto l236
						}
						position++
					}
				l237:
					{
						position239, tokenIndex239 := position, tokenIndex
						if buffer[position] != rune('y') {
							goto l240
						}
						position++
						goto l239
					l240:
						position, tokenIndex = position239, tokenIndex239
						if buffer[position] != rune('Y') {
							goto l236
						}
						position++
					}
				l239:
					{
						position241, tokenIndex241 := position, tokenIndex
						if buffer[position] != rune('t') {
							goto l242
						}
						position++
						goto l241
					l242:
						position, tokenIndex = position241, tokenIndex241
						if buffer[position] != rune('T') {
							goto l236
						}
						position++
					}
				l241:
					{
						position243, tokenIndex243 := position, tokenIndex
						if buffer[position] != rune('e') {
							goto l244
						}
						position++
						goto l243
					l244:
						position, tokenIndex = position243, tokenIndex243
						if buffer[position] != rune('E') {
							goto l236
						}
						position++
					}
				l243:
					goto l186
				l236:
					position, tokenIndex = position186, tokenIndex186
					if buffer[position] != rune('.') {
						goto l245
					}
					position++
					if buffer[position] != rune('4') {
						goto l245
					}
					position++
					{
						position246, tokenIndex246 := position, tokenIndex
						if buffer[position] != rune('b') {
							goto l247
						}
						position++
						goto l246
					l247:
						position, tokenIndex = position246, tokenIndex246
						if buffer[position] != rune('B') {
							goto l245
						}
						position++
					}
				l246:
					{
						position248, tokenIndex248 := position, tokenIndex
						if buffer[position] != rune('y') {
							goto l249
						}
						position++
						goto l248
					l249:
						position, tokenIndex = position248, tokenIndex248
						if buffer[position] != rune('Y') {
							goto l245
						}
						position++
					}
				l248:
					{
						position250, tokenIndex250 := position, tokenIndex
						if buffer[position] != rune('t') {
							goto l251
						}
						position++
						goto l250
					l251:
						position, tokenIndex = position250, tokenIndex250
						if buffer[position] != rune('T') {
							goto l245
						}
						position++
					}
				l250:
					{
						position252, tokenIndex252 := position, tokenIndex
						if buffer[position] != rune('e') {
							goto l253
						}
						position++
						goto l252
					l253:
						position, tokenIndex = position252, tokenIndex252
						if buffer[position] != rune('E') {
							goto l245
						}
						position++
					}
				l252:
					goto l186
				l245:
					position, tokenIndex = position186, tokenIndex186
					if buffer[position] != rune('.') {
						goto l254
					}
					position++
					{
						position255, tokenIndex255 := position, tokenIndex
						if buffer[position] != rune('q') {
							goto l256
						}
						position++
						goto l255
					l256:
						position, tokenIndex = position255, tokenIndex255
						if buffer[position] != rune('Q') {
							goto l254
						}
						position++
					}
				l255:
					{
						position257, tokenIndex257 := position, tokenIndex
						if buffer[position] != rune('u') {
							goto l258
						}
						position++
						goto l257
					l258:
						position, tokenIndex = position257, tokenIndex257
						if buffer[position] != rune('U') {
							goto l254
						}
						position++
					}
				l257:
					{
						position259, tokenIndex259 := position, tokenIndex
						if buffer[position] != rune('a') {
							goto l260
						}
						position++
						goto l259
					l260:
						position, tokenIndex = position259, tokenIndex259
						if buffer[position] != rune('A') {
							goto l254
						}
						position++
					}
				l259:
					{
						position261, tokenIndex261 := position, tokenIndex
						if buffer[position] != rune('d') {
							goto l262
						}
						position++
						goto l261
					l262:
						position, tokenIndex = position261, tokenIndex261
						if buffer[position] != rune('D') {
							goto l254
						}
						position++
					}
				l261:
					goto l186
				l254:
					position, tokenIndex = position186, tokenIndex186
					if buffer[position] != rune('.') {
						goto l263
					}
					position++
					{
						position264, tokenIndex264 := position, tokenIndex
						if buffer[position] != rune('t') {
							goto l265
						}
						position++
						goto l264
					l265:
						position, tokenIndex = position264, tokenIndex264
						if buffer[position] != rune('T') {
							goto l263
						}
						position++
					}
				l264:
					{
						position266, tokenIndex266 := position, tokenIndex
						if buffer[position] != rune('c') {
							goto l267
						}
						position++
						goto l266
					l267:
						position, tokenIndex = position266, tokenIndex266
						if buffer[position] != rune('C') {
							goto l263
						}
						position++
					}
				l266:
					goto l186
				l263:
					position, tokenIndex = position186, tokenIndex186
					if buffer[position] != rune('.') {
						goto l268
					}
					position++
					{
						position269, tokenIndex269 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l270
						}
						position++
						goto l269
					l270:
						position, tokenIndex = position269, tokenIndex269
						if buffer[position] != rune('L') {
							goto l268
						}
						position++
					}
				l269:
					{
						position271, tokenIndex271 := position, tokenIndex
						if buffer[position] != rune('o') {
							goto l272
						}
						position++
						goto l271
					l272:
						position, tokenIndex = position271, tokenIndex271
						if buffer[position] != rune('O') {
							goto l268
						}
						position++
					}
				l271:
					{
						position273, tokenIndex273 := position, tokenIndex
						if buffer[position] != rune('c') {
							goto l274
						}
						position++
						goto l273
					l274:
						position, tokenIndex = position273, tokenIndex273
						if buffer[position] != rune('C') {
							goto l268
						}
						position++
					}
				l273:
					{
						position275, tokenIndex275 := position, tokenIndex
						if buffer[position] != rune('a') {
							goto l276
						}
						position++
						goto l275
					l276:
						position, tokenIndex = position275, tokenIndex275
						if buffer[position] != rune('A') {
							goto l268
						}
						position++
					}
				l275:
					{
						position277, tokenIndex277 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l278
						}
						position++
						goto l277
					l278:
						position, tokenIndex = position277, tokenIndex277
						if buffer[position] != rune('L') {
							goto l268
						}
						position++
					}
				l277:
					{
						position279, tokenIndex279 := position, tokenIndex
						if buffer[position] != rune('e') {
							goto l280
						}
						position++
						goto l279
					l280:
						position, tokenIndex = position279, tokenIndex279
						if buffer[position] != rune('E') {
							goto l268
						}
						position++
					}
				l279:
					{
						position281, tokenIndex281 := position, tokenIndex
						if buffer[position] != rune('n') {
							goto l282
						}
						position++
						goto l281
					l282:
						position, tokenIndex = position281, tokenIndex281
						if buffer[position] != rune('N') {
							goto l268
						}
						position++
					}
				l281:
					{
						position283, tokenIndex283 := position, tokenIndex
						if buffer[position] != rune('t') {
							goto l284
						}
						position++
						goto l283
					l284:
						position, tokenIndex = position283, tokenIndex283
						if buffer[position] != rune('T') {
							goto l268
						}
						position++
					}
				l283:
					{
						position285, tokenIndex285 := position, tokenIndex
						if buffer[position] != rune('r') {
							goto l286
						}
						position++
						goto l285
					l286:
						position, tokenIndex = position285, tokenIndex285
						if buffer[position] != rune('R') {
							goto l268
						}
						position++
					}
				l285:
					{
						position287, tokenIndex287 := position, tokenIndex
						if buffer[position] != rune('y') {
							goto l288
						}
						position++
						goto l287
					l288:
						position, tokenIndex = position287, tokenIndex287
						if buffer[position] != rune('Y') {
							goto l268
						}
						position++
					}
				l287:
					goto l186
				l268:
					position, tokenIndex = position186, tokenIndex186
					if buffer[position] != rune('.') {
						goto l289
					}
					position++
					{
						position290, tokenIndex290 := position, tokenIndex
						if buffer[position] != rune('s') {
							goto l291
						}
						position++
						goto l290
					l291:
						position, tokenIndex = position290, tokenIndex290
						if buffer[position] != rune('S') {
							goto l289
						}
						position++
					}
				l290:
					{
						position292, tokenIndex292 := position, tokenIndex
						if buffer[position] != rune('i') {
							goto l293
						}
						position++
						goto l292
					l293:
						position, tokenIndex = position292, tokenIndex292
						if buffer[position] != rune('I') {
							goto l289
						}
						position++
					}
				l292:
					{
						position294, tokenIndex294 := position, tokenIndex
						if buffer[position] != rune('z') {
							goto l295
						}
						position++
						goto l294
					l295:
						position, tokenIndex = position294, tokenIndex294
						if buffer[position] != rune('Z') {
							goto l289
						}
						position++
					}
				l294:
					{
						position296, tokenIndex296 := position, tokenIndex
						if buffer[position] != rune('e') {
							goto l297
						}
						position++
						goto l296
					l297:
						position, tokenIndex = position296, tokenIndex296
						if buffer[position] != rune('E') {
							goto l289
						}
						position++
					}
				l296:
					goto l186
				l289:
					position, tokenIndex = position186, tokenIndex186
					if buffer[position] != rune('.') {
						goto l298
					}
					position++
					{
						position299, tokenIndex299 := position, tokenIndex
						if buffer[position] != rune('t') {
							goto l300
						}
						position++
						goto l299
					l300:
						position, tokenIndex = position299, tokenIndex299
						if buffer[position] != rune('T') {
							goto l298
						}
						position++
					}
				l299:
					{
						position301, tokenIndex301 := position, tokenIndex
						if buffer[position] != rune('y') {
							goto l302
						}
						position++
						goto l301
					l302:
						position, tokenIndex = position301, tokenIndex301
						if buffer[position] != rune('Y') {
							goto l298
						}
						position++
					}
				l301:
					{
						position303, tokenIndex303 := position, tokenIndex
						if buffer[position] != rune('p') {
							goto l304
						}
						position++
						goto l303
					l304:
						position, tokenIndex = position303, tokenIndex303
						if buffer[position] != rune('P') {
							goto l298
						}
						position++
					}
				l303:
					{
						position305, tokenIndex305 := position, tokenIndex
						if buffer[position] != rune('e') {
							goto l306
						}
						position++
						goto l305
					l306:
						position, tokenIndex = position305, tokenIndex305
						if buffer[position] != rune('E') {
							goto l298
						}
						position++
					}
				l305:
					goto l186
				l298:
					position, tokenIndex = position186, tokenIndex186
					if buffer[position] != rune('.') {
						goto l307
					}
					position++
					{
						position308, tokenIndex308 := position, tokenIndex
						if buffer[position] != rune('u') {
							goto l309
						}
						position++
						goto l308
					l309:
						position, tokenIndex = position308, tokenIndex308
						if buffer[position] != rune('U') {
							goto l307
						}
						position++
					}
				l308:
					{
						position310, tokenIndex310 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l311
						}
						position++
						goto l310
					l311:
						position, tokenIndex = position310, tokenIndex310
						if buffer[position] != rune('L') {
							goto l307
						}
						position++
					}
				l310:
					{
						position312, tokenIndex312 := position, tokenIndex
						if buffer[position] != rune('e') {
							goto l313
						}
						position++
						goto l312
					l313:
						position, tokenIndex = position312, tokenIndex312
						if buffer[position] != rune('E') {
							goto l307
						}
						position++
					}
				l312:
					{
						position314, tokenIndex314 := position, tokenIndex
						if buffer[position] != rune('b') {
							goto l315
						}
						position++
						goto l314
					l315:
						position, tokenIndex = position314, tokenIndex314
						if buffer[position] != rune('B') {
							goto l307
						}
						position++
					}
				l314:
					if buffer[position] != rune('1') {
						goto l307
					}
					position++
					if buffer[position] != rune('2') {
						goto l307
					}
					position++
					if buffer[position] != rune('8') {
						goto l307
					}
					position++
					goto l186
				l307:
					position, tokenIndex = position186, tokenIndex186
					if buffer[position] != rune('.') {
						goto l184
					}
					position++
					{
						position316, tokenIndex316 := position, tokenIndex
						if buffer[position] != rune('s') {
							goto l317
						}
						position++
						goto l316
					l317:
						position, tokenIndex = position316, tokenIndex316
						if buffer[position] != rune('S') {
							goto l184
						}
						position++
					}
				l316:
					{
						position318, tokenIndex318 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l319
						}
						position++
						goto l318
					l319:
						position, tokenIndex = position318, tokenIndex318
						if buffer[position] != rune('L') {
							goto l184
						}
						position++
					}
				l318:
					{
						position320, tokenIndex320 := position, tokenIndex
						if buffer[position] != rune('e') {
							goto l321
						}
						position++
						goto l320
					l321:
						position, tokenIndex = position320, tokenIndex320
						if buffer[position] != rune('E') {
							goto l184
						}
						position++
					}
				l320:
					{
						position322, tokenIndex322 := position, tokenIndex
						if buffer[position] != rune('b') {
							goto l323
						}
						position++
						goto l322
					l323:
						position, tokenIndex = position322, tokenIndex322
						if buffer[position] != rune('B') {
							goto l184
						}
						position++
					}
				l322:
					if buffer[position] != rune('1') {
						goto l184
					}
					position++
					if buffer[position] != rune('2') {
						goto l184
					}
					position++
					if buffer[position] != rune('8') {
						goto l184
					}
					position++
				}
			l186:
				add(ruleLabelContainingDirectiveName, position185)
			}
			return true
		l184:
			position, tokenIndex = position184, tokenIndex184
			return false
		},
		/* 16 SymbolArgs <- <(SymbolArg (WS? ',' WS? SymbolArg)*)> */
		func() bool {
			position324, tokenIndex324 := position, tokenIndex
			{
				position325 := position
				if !_rules[ruleSymbolArg]() {
					goto l324
				}
			l326:
				{
					position327, tokenIndex327 := position, tokenIndex
					{
						position328, tokenIndex328 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l328
						}
						goto l329
					l328:
						position, tokenIndex = position328, tokenIndex328
					}
				l329:
					if buffer[position] != rune(',') {
						goto l327
					}
					position++
					{
						position330, tokenIndex330 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l330
						}
						goto l331
					l330:
						position, tokenIndex = position330, tokenIndex330
					}
				l331:
					if !_rules[ruleSymbolArg]() {
						goto l327
					}
					goto l326
				l327:
					position, tokenIndex = position327, tokenIndex327
				}
				add(ruleSymbolArgs, position325)
			}
			return true
		l324:
			position, tokenIndex = position324, tokenIndex324
			return false
		},
		/* 17 SymbolArg <- <SymbolExpr> */
		func() bool {
			position332, tokenIndex332 := position, tokenIndex
			{
				position333 := position
				if !_rules[ruleSymbolExpr]() {
					goto l332
				}
				add(ruleSymbolArg, position333)
			}
			return true
		l332:
			position, tokenIndex = position332, tokenIndex332
			return false
		},
		/* 18 SymbolExpr <- <(SymbolAtom (WS? SymbolOperator WS? SymbolExpr)?)> */
		func() bool {
			position334, tokenIndex334 := position, tokenIndex
			{
				position335 := position
				if !_rules[ruleSymbolAtom]() {
					goto l334
				}
				{
					position336, tokenIndex336 := position, tokenIndex
					{
						position338, tokenIndex338 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l338
						}
						goto l339
					l338:
						position, tokenIndex = position338, tokenIndex338
					}
				l339:
					if !_rules[ruleSymbolOperator]() {
						goto l336
					}
					{
						position340, tokenIndex340 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l340
						}
						goto l341
					l340:
						position, tokenIndex = position340, tokenIndex340
					}
				l341:
					if !_rules[ruleSymbolExpr]() {
						goto l336
					}
					goto l337
				l336:
					position, tokenIndex = position336, tokenIndex336
				}
			l337:
				add(ruleSymbolExpr, position335)
			}
			return true
		l334:
			position, tokenIndex = position334, tokenIndex334
			return false
		},
		/* 19 SymbolAtom <- <(LocalLabelRef / Offset / SymbolType / (LocalSymbol TCMarker?) / (SymbolName Offset) / (SymbolName TCMarker?) / Dot / (OpenParen WS? SymbolExpr WS? CloseParen))> */
		func() bool {
			position342, tokenIndex342 := position, tokenIndex
			{
				position343 := position
				{
					position344, tokenIndex344 := position, tokenIndex
					if !_rules[ruleLocalLabelRef]() {
						goto l345
					}
					goto l344
				l345:
					position, tokenIndex = position344, tokenIndex344
					if !_rules[ruleOffset]() {
						goto l346
					}
					goto l344
				l346:
					position, tokenIndex = position344, tokenIndex344
					if !_rules[ruleSymbolType]() {
						goto l347
					}
					goto l344
				l347:
					position, tokenIndex = position344, tokenIndex344
					if !_rules[ruleLocalSymbol]() {
						goto l348
					}
					{
						position349, tokenIndex349 := position, tokenIndex
						if !_rules[ruleTCMarker]() {
							goto l349
						}
						goto l350
					l349:
						position, tokenIndex = position349, tokenIndex349
					}
				l350:
					goto l344
				l348:
					position, tokenIndex = position344, tokenIndex344
					if !_rules[ruleSymbolName]() {
						goto l351
					}
					if !_rules[ruleOffset]() {
						goto l351
					}
					goto l344
				l351:
					position, tokenIndex = position344, tokenIndex344
					if !_rules[ruleSymbolName]() {
						goto l352
					}
					{
						position353, tokenIndex353 := position, tokenIndex
						if !_rules[ruleTCMarker]() {
							goto l353
						}
						goto l354
					l353:
						position, tokenIndex = position353, tokenIndex353
					}
				l354:
					goto l344
				l352:
					position, tokenIndex = position344, tokenIndex344
					if !_rules[ruleDot]() {
						goto l355
					}
					goto l344
				l355:
					position, tokenIndex = position344, tokenIndex344
					if !_rules[ruleOpenParen]() {
						goto l342
					}
					{
						position356, tokenIndex356 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l356
						}
						goto l357
					l356:
						position, tokenIndex = position356, tokenIndex356
					}
				l357:
					if !_rules[ruleSymbolExpr]() {
						goto l342
					}
					{
						position358, tokenIndex358 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l358
						}
						goto l359
					l358:
						position, tokenIndex = position358, tokenIndex358
					}
				l359:
					if !_rules[ruleCloseParen]() {
						goto l342
					}
				}
			l344:
				add(ruleSymbolAtom, position343)
			}
			return true
		l342:
			position, tokenIndex = position342, tokenIndex342
			return false
		},
		/* 20 SymbolOperator <- <('+' / '-' / '|' / ('<' '<') / ('>' '>'))> */
		func() bool {
			position360, tokenIndex360 := position, tokenIndex
			{
				position361 := position
				{
					position362, tokenIndex362 := position, tokenIndex
					if buffer[position] != rune('+') {
						goto l363
					}
					position++
					goto l362
				l363:
					position, tokenIndex = position362, tokenIndex362
					if buffer[position] != rune('-') {
						goto l364
					}
					position++
					goto l362
				l364:
					position, tokenIndex = position362, tokenIndex362
					if buffer[position] != rune('|') {
						goto l365
					}
					position++
					goto l362
				l365:
					position, tokenIndex = position362, tokenIndex362
					if buffer[position] != rune('<') {
						goto l366
					}
					position++
					if buffer[position] != rune('<') {
						goto l366
					}
					position++
					goto l362
				l366:
					position, tokenIndex = position362, tokenIndex362
					if buffer[position] != rune('>') {
						goto l360
					}
					position++
					if buffer[position] != rune('>') {
						goto l360
					}
					position++
				}
			l362:
				add(ruleSymbolOperator, position361)
			}
			return true
		l360:
			position, tokenIndex = position360, tokenIndex360
			return false
		},
		/* 21 OpenParen <- <'('> */
		func() bool {
			position367, tokenIndex367 := position, tokenIndex
			{
				position368 := position
				if buffer[position] != rune('(') {
					goto l367
				}
				position++
				add(ruleOpenParen, position368)
			}
			return true
		l367:
			position, tokenIndex = position367, tokenIndex367
			return false
		},
		/* 22 CloseParen <- <')'> */
		func() bool {
			position369, tokenIndex369 := position, tokenIndex
			{
				position370 := position
				if buffer[position] != rune(')') {
					goto l369
				}
				position++
				add(ruleCloseParen, position370)
			}
			return true
		l369:
			position, tokenIndex = position369, tokenIndex369
			return false
		},
		/* 23 SymbolType <- <(('@' / '%') (('f' 'u' 'n' 'c' 't' 'i' 'o' 'n') / ('o' 'b' 'j' 'e' 'c' 't')))> */
		func() bool {
			position371, tokenIndex371 := position, tokenIndex
			{
				position372 := position
				{
					position373, tokenIndex373 := position, tokenIndex
					if buffer[position] != rune('@') {
						goto l374
					}
					position++
					goto l373
				l374:
					position, tokenIndex = position373, tokenIndex373
					if buffer[position] != rune('%') {
						goto l371
					}
					position++
				}
			l373:
				{
					position375, tokenIndex375 := position, tokenIndex
					if buffer[position] != rune('f') {
						goto l376
					}
					position++
					if buffer[position] != rune('u') {
						goto l376
					}
					position++
					if buffer[position] != rune('n') {
						goto l376
					}
					position++
					if buffer[position] != rune('c') {
						goto l376
					}
					position++
					if buffer[position] != rune('t') {
						goto l376
					}
					position++
					if buffer[position] != rune('i') {
						goto l376
					}
					position++
					if buffer[position] != rune('o') {
						goto l376
					}
					position++
					if buffer[position] != rune('n') {
						goto l376
					}
					position++
					goto l375
				l376:
					position, tokenIndex = position375, tokenIndex375
					if buffer[position] != rune('o') {
						goto l371
					}
					position++
					if buffer[position] != rune('b') {
						goto l371
					}
					position++
					if buffer[position] != rune('j') {
						goto l371
					}
					position++
					if buffer[position] != rune('e') {
						goto l371
					}
					position++
					if buffer[position] != rune('c') {
						goto l371
					}
					position++
					if buffer[position] != rune('t') {
						goto l371
					}
					position++
				}
			l375:
				add(ruleSymbolType, position372)
			}
			return true
		l371:
			position, tokenIndex = position371, tokenIndex371
			return false
		},
		/* 24 Dot <- <'.'> */
		func() bool {
			position377, tokenIndex377 := position, tokenIndex
			{
				position378 := position
				if buffer[position] != rune('.') {
					goto l377
				}
				position++
				add(ruleDot, position378)
			}
			return true
		l377:
			position, tokenIndex = position377, tokenIndex377
			return false
		},
		/* 25 TCMarker <- <('[' 'T' 'C' ']')> */
		func() bool {
			position379, tokenIndex379 := position, tokenIndex
			{
				position380 := position
				if buffer[position] != rune('[') {
					goto l379
				}
				position++
				if buffer[position] != rune('T') {
					goto l379
				}
				position++
				if buffer[position] != rune('C') {
					goto l379
				}
				position++
				if buffer[position] != rune(']') {
					goto l379
				}
				position++
				add(ruleTCMarker, position380)
			}
			return true
		l379:
			position, tokenIndex = position379, tokenIndex379
			return false
		},
		/* 26 EscapedChar <- <('\\' .)> */
		func() bool {
			position381, tokenIndex381 := position, tokenIndex
			{
				position382 := position
				if buffer[position] != rune('\\') {
					goto l381
				}
				position++
				if !matchDot() {
					goto l381
				}
				add(ruleEscapedChar, position382)
			}
			return true
		l381:
			position, tokenIndex = position381, tokenIndex381
			return false
		},
		/* 27 WS <- <(' ' / '\t')+> */
		func() bool {
			position383, tokenIndex383 := position, tokenIndex
			{
				position384 := position
				{
					position387, tokenIndex387 := position, tokenIndex
					if buffer[position] != rune(' ') {
						goto l388
					}
					position++
					goto l387
				l388:
					position, tokenIndex = position387, tokenIndex387
					if buffer[position] != rune('\t') {
						goto l383
					}
					position++
				}
			l387:
			l385:
				{
					position386, tokenIndex386 := position, tokenIndex
					{
						position389, tokenIndex389 := position, tokenIndex
						if buffer[position] != rune(' ') {
							goto l390
						}
						position++
						goto l389
					l390:
						position, tokenIndex = position389, tokenIndex389
						if buffer[position] != rune('\t') {
							goto l386
						}
						position++
					}
				l389:
					goto l385
				l386:
					position, tokenIndex = position386, tokenIndex386
				}
				add(ruleWS, position384)
			}
			return true
		l383:
			position, tokenIndex = position383, tokenIndex383
			return false
		},
		/* 28 Comment <- <((('/' '/') / '#') (!'\n' .)*)> */
		func() bool {
			position391, tokenIndex391 := position, tokenIndex
			{
				position392 := position
				{
					position393, tokenIndex393 := position, tokenIndex
					if buffer[position] != rune('/') {
						goto l394
					}
					position++
					if buffer[position] != rune('/') {
						goto l394
					}
					position++
					goto l393
				l394:
					position, tokenIndex = position393, tokenIndex393
					if buffer[position] != rune('#') {
						goto l391
					}
					position++
				}
			l393:
			l395:
				{
					position396, tokenIndex396 := position, tokenIndex
					{
						position397, tokenIndex397 := position, tokenIndex
						if buffer[position] != rune('\n') {
							goto l397
						}
						position++
						goto l396
					l397:
						position, tokenIndex = position397, tokenIndex397
					}
					if !matchDot() {
						goto l396
					}
					goto l395
				l396:
					position, tokenIndex = position396, tokenIndex396
				}
				add(ruleComment, position392)
			}
			return true
		l391:
			position, tokenIndex = position391, tokenIndex391
			return false
		},
		/* 29 Label <- <((LocalSymbol / LocalLabel / SymbolName) ':')> */
		func() bool {
			position398, tokenIndex398 := position, tokenIndex
			{
				position399 := position
				{
					position400, tokenIndex400 := position, tokenIndex
					if !_rules[ruleLocalSymbol]() {
						goto l401
					}
					goto l400
				l401:
					position, tokenIndex = position400, tokenIndex400
					if !_rules[ruleLocalLabel]() {
						goto l402
					}
					goto l400
				l402:
					position, tokenIndex = position400, tokenIndex400
					if !_rules[ruleSymbolName]() {
						goto l398
					}
				}
			l400:
				if buffer[position] != rune(':') {
					goto l398
				}
				position++
				add(ruleLabel, position399)
			}
			return true
		l398:
			position, tokenIndex = position398, tokenIndex398
			return false
		},
		/* 30 SymbolName <- <(([a-z] / [A-Z] / '.' / '_') ([a-z] / [A-Z] / '.' / ([0-9] / [0-9]) / '$' / '_')*)> */
		func() bool {
			position403, tokenIndex403 := position, tokenIndex
			{
				position404 := position
				{
					position405, tokenIndex405 := position, tokenIndex
					if c := buffer[position]; c < rune('a') || c > rune('z') {
						goto l406
					}
					position++
					goto l405
				l406:
					position, tokenIndex = position405, tokenIndex405
					if c := buffer[position]; c < rune('A') || c > rune('Z') {
						goto l407
					}
					position++
					goto l405
				l407:
					position, tokenIndex = position405, tokenIndex405
					if buffer[position] != rune('.') {
						goto l408
					}
					position++
					goto l405
				l408:
					position, tokenIndex = position405, tokenIndex405
					if buffer[position] != rune('_') {
						goto l403
					}
					position++
				}
			l405:
			l409:
				{
					position410, tokenIndex410 := position, tokenIndex
					{
						position411, tokenIndex411 := position, tokenIndex
						if c := buffer[position]; c < rune('a') || c > rune('z') {
							goto l412
						}
						position++
						goto l411
					l412:
						position, tokenIndex = position411, tokenIndex411
						if c := buffer[position]; c < rune('A') || c > rune('Z') {
							goto l413
						}
						position++
						goto l411
					l413:
						position, tokenIndex = position411, tokenIndex411
						if buffer[position] != rune('.') {
							goto l414
						}
						position++
						goto l411
					l414:
						position, tokenIndex = position411, tokenIndex411
						{
							position416, tokenIndex416 := position, tokenIndex
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l417
							}
							position++
							goto l416
						l417:
							position, tokenIndex = position416, tokenIndex416
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l415
							}
							position++
						}
					l416:
						goto l411
					l415:
						position, tokenIndex = position411, tokenIndex411
						if buffer[position] != rune('$') {
							goto l418
						}
						position++
						goto l411
					l418:
						position, tokenIndex = position411, tokenIndex411
						if buffer[position] != rune('_') {
							goto l410
						}
						position++
					}
				l411:
					goto l409
				l410:
					position, tokenIndex = position410, tokenIndex410
				}
				add(ruleSymbolName, position404)
			}
			return true
		l403:
			position, tokenIndex = position403, tokenIndex403
			return false
		},
		/* 31 LocalSymbol <- <('.' 'L' ([a-z] / [A-Z] / ([a-z] / [A-Z]) / '.' / ([0-9] / [0-9]) / '$' / '_')+)> */
		func() bool {
			position419, tokenIndex419 := position, tokenIndex
			{
				position420 := position
				if buffer[position] != rune('.') {
					goto l419
				}
				position++
				if buffer[position] != rune('L') {
					goto l419
				}
				position++
				{
					position423, tokenIndex423 := position, tokenIndex
					if c := buffer[position]; c < rune('a') || c > rune('z') {
						goto l424
					}
					position++
					goto l423
				l424:
					position, tokenIndex = position423, tokenIndex423
					if c := buffer[position]; c < rune('A') || c > rune('Z') {
						goto l425
					}
					position++
					goto l423
				l425:
					position, tokenIndex = position423, tokenIndex423
					{
						position427, tokenIndex427 := position, tokenIndex
						if c := buffer[position]; c < rune('a') || c > rune('z') {
							goto l428
						}
						position++
						goto l427
					l428:
						position, tokenIndex = position427, tokenIndex427
						if c := buffer[position]; c < rune('A') || c > rune('Z') {
							goto l426
						}
						position++
					}
				l427:
					goto l423
				l426:
					position, tokenIndex = position423, tokenIndex423
					if buffer[position] != rune('.') {
						goto l429
					}
					position++
					goto l423
				l429:
					position, tokenIndex = position423, tokenIndex423
					{
						position431, tokenIndex431 := position, tokenIndex
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l432
						}
						position++
						goto l431
					l432:
						position, tokenIndex = position431, tokenIndex431
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l430
						}
						position++
					}
				l431:
					goto l423
				l430:
					position, tokenIndex = position423, tokenIndex423
					if buffer[position] != rune('$') {
						goto l433
					}
					position++
					goto l423
				l433:
					position, tokenIndex = position423, tokenIndex423
					if buffer[position] != rune('_') {
						goto l419
					}
					position++
				}
			l423:
			l421:
				{
					position422, tokenIndex422 := position, tokenIndex
					{
						position434, tokenIndex434 := position, tokenIndex
						if c := buffer[position]; c < rune('a') || c > rune('z') {
							goto l435
						}
						position++
						goto l434
					l435:
						position, tokenIndex = position434, tokenIndex434
						if c := buffer[position]; c < rune('A') || c > rune('Z') {
							goto l436
						}
						position++
						goto l434
					l436:
						position, tokenIndex = position434, tokenIndex434
						{
							position438, tokenIndex438 := position, tokenIndex
							if c := buffer[position]; c < rune('a') || c > rune('z') {
								goto l439
							}
							position++
							goto l438
						l439:
							position, tokenIndex = position438, tokenIndex438
							if c := buffer[position]; c < rune('A') || c > rune('Z') {
								goto l437
							}
							position++
						}
					l438:
						goto l434
					l437:
						position, tokenIndex = position434, tokenIndex434
						if buffer[position] != rune('.') {
							goto l440
						}
						position++
						goto l434
					l440:
						position, tokenIndex = position434, tokenIndex434
						{
							position442, tokenIndex442 := position, tokenIndex
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l443
							}
							position++
							goto l442
						l443:
							position, tokenIndex = position442, tokenIndex442
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l441
							}
							position++
						}
					l442:
						goto l434
					l441:
						position, tokenIndex = position434, tokenIndex434
						if buffer[position] != rune('$') {
							goto l444
						}
						position++
						goto l434
					l444:
						position, tokenIndex = position434, tokenIndex434
						if buffer[position] != rune('_') {
							goto l422
						}
						position++
					}
				l434:
					goto l421
				l422:
					position, tokenIndex = position422, tokenIndex422
				}
				add(ruleLocalSymbol, position420)
			}
			return true
		l419:
			position, tokenIndex = position419, tokenIndex419
			return false
		},
		/* 32 LocalLabel <- <([0-9] ([0-9] / '$')*)> */
		func() bool {
			position445, tokenIndex445 := position, tokenIndex
			{
				position446 := position
				if c := buffer[position]; c < rune('0') || c > rune('9') {
					goto l445
				}
				position++
			l447:
				{
					position448, tokenIndex448 := position, tokenIndex
					{
						position449, tokenIndex449 := position, tokenIndex
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l450
						}
						position++
						goto l449
					l450:
						position, tokenIndex = position449, tokenIndex449
						if buffer[position] != rune('$') {
							goto l448
						}
						position++
					}
				l449:
					goto l447
				l448:
					position, tokenIndex = position448, tokenIndex448
				}
				add(ruleLocalLabel, position446)
			}
			return true
		l445:
			position, tokenIndex = position445, tokenIndex445
			return false
		},
		/* 33 LocalLabelRef <- <([0-9] ([0-9] / '$')* ('b' / 'f'))> */
		func() bool {
			position451, tokenIndex451 := position, tokenIndex
			{
				position452 := position
				if c := buffer[position]; c < rune('0') || c > rune('9') {
					goto l451
				}
				position++
			l453:
				{
					position454, tokenIndex454 := position, tokenIndex
					{
						position455, tokenIndex455 := position, tokenIndex
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l456
						}
						position++
						goto l455
					l456:
						position, tokenIndex = position455, tokenIndex455
						if buffer[position] != rune('$') {
							goto l454
						}
						position++
					}
				l455:
					goto l453
				l454:
					position, tokenIndex = position454, tokenIndex454
				}
				{
					position457, tokenIndex457 := position, tokenIndex
					if buffer[position] != rune('b') {
						goto l458
					}
					position++
					goto l457
				l458:
					position, tokenIndex = position457, tokenIndex457
					if buffer[position] != rune('f') {
						goto l451
					}
					position++
				}
			l457:
				add(ruleLocalLabelRef, position452)
			}
			return true
		l451:
			position, tokenIndex = position451, tokenIndex451
			return false
		},
		/* 34 InstructionPrefix <- <(('n' / 'N') ('o' / 'O') ('t' / 'T') ('r' / 'R') ('a' / 'A') ('c' / 'C') ('k' / 'K'))> */
		func() bool {
			position459, tokenIndex459 := position, tokenIndex
			{
				position460 := position
				{
					position461, tokenIndex461 := position, tokenIndex
					if buffer[position] != rune('n') {
						goto l462
					}
					position++
					goto l461
				l462:
					position, tokenIndex = position461, tokenIndex461
					if buffer[position] != rune('N') {
						goto l459
					}
					position++
				}
			l461:
				{
					position463, tokenIndex463 := position, tokenIndex
					if buffer[position] != rune('o') {
						goto l464
					}
					position++
					goto l463
				l464:
					position, tokenIndex = position463, tokenIndex463
					if buffer[position] != rune('O') {
						goto l459
					}
					position++
				}
			l463:
				{
					position465, tokenIndex465 := position, tokenIndex
					if buffer[position] != rune('t') {
						goto l466
					}
					position++
					goto l465
				l466:
					position, tokenIndex = position465, tokenIndex465
					if buffer[position] != rune('T') {
						goto l459
					}
					position++
				}
			l465:
				{
					position467, tokenIndex467 := position, tokenIndex
					if buffer[position] != rune('r') {
						goto l468
					}
					position++
					goto l467
				l468:
					position, tokenIndex = position467, tokenIndex467
					if buffer[position] != rune('R') {
						goto l459
					}
					position++
				}
			l467:
				{
					position469, tokenIndex469 := position, tokenIndex
					if buffer[position] != rune('a') {
						goto l470
					}
					position++
					goto l469
				l470:
					position, tokenIndex = position469, tokenIndex469
					if buffer[position] != rune('A') {
						goto l459
					}
					position++
				}
			l469:
				{
					position471, tokenIndex471 := position, tokenIndex
					if buffer[position] != rune('c') {
						goto l472
					}
					position++
					goto l471
				l472:
					position, tokenIndex = position471, tokenIndex471
					if buffer[position] != rune('C') {
						goto l459
					}
					position++
				}
			l471:
				{
					position473, tokenIndex473 := position, tokenIndex
					if buffer[position] != rune('k') {
						goto l474
					}
					position++
					goto l473
				l474:
					position, tokenIndex = position473, tokenIndex473
					if buffer[position] != rune('K') {
						goto l459
					}
					position++
				}
			l473:
				add(ruleInstructionPrefix, position460)
			}
			return true
		l459:
			position, tokenIndex = position459, tokenIndex459
			return false
		},
		/* 35 Instruction <- <((InstructionPrefix WS)? InstructionName (WS InstructionArg (WS? ',' WS? InstructionArg)*)?)> */
		func() bool {
			position475, tokenIndex475 := position, tokenIndex
			{
				position476 := position
				{
					position477, tokenIndex477 := position, tokenIndex
					if !_rules[ruleInstructionPrefix]() {
						goto l477
					}
					if !_rules[ruleWS]() {
						goto l477
					}
					goto l478
				l477:
					position, tokenIndex = position477, tokenIndex477
				}
			l478:
				if !_rules[ruleInstructionName]() {
					goto l475
				}
				{
					position479, tokenIndex479 := position, tokenIndex
					if !_rules[ruleWS]() {
						goto l479
					}
					if !_rules[ruleInstructionArg]() {
						goto l479
					}
				l481:
					{
						position482, tokenIndex482 := position, tokenIndex
						{
							position483, tokenIndex483 := position, tokenIndex
							if !_rules[ruleWS]() {
								goto l483
							}
							goto l484
						l483:
							position, tokenIndex = position483, tokenIndex483
						}
					l484:
						if buffer[position] != rune(',') {
							goto l482
						}
						position++
						{
							position485, tokenIndex485 := position, tokenIndex
							if !_rules[ruleWS]() {
								goto l485
							}
							goto l486
						l485:
							position, tokenIndex = position485, tokenIndex485
						}
					l486:
						if !_rules[ruleInstructionArg]() {
							goto l482
						}
						goto l481
					l482:
						position, tokenIndex = position482, tokenIndex482
					}
					goto l480
				l479:
					position, tokenIndex = position479, tokenIndex479
				}
			l480:
				add(ruleInstruction, position476)
			}
			return true
		l475:
			position, tokenIndex = position475, tokenIndex475
			return false
		},
		/* 36 InstructionName <- <(([a-z] / [A-Z]) ([a-z] / [A-Z] / '.' / ([0-9] / [0-9]))* ('.' / '+' / '-')?)> */
		func() bool {
			position487, tokenIndex487 := position, tokenIndex
			{
				position488 := position
				{
					position489, tokenIndex489 := position, tokenIndex
					if c := buffer[position]; c < rune('a') || c > rune('z') {
						goto l490
					}
					position++
					goto l489
				l490:
					position, tokenIndex = position489, tokenIndex489
					if c := buffer[position]; c < rune('A') || c > rune('Z') {
						goto l487
					}
					position++
				}
			l489:
			l491:
				{
					position492, tokenIndex492 := position, tokenIndex
					{
						position493, tokenIndex493 := position, tokenIndex
						if c := buffer[position]; c < rune('a') || c > rune('z') {
							goto l494
						}
						position++
						goto l493
					l494:
						position, tokenIndex = position493, tokenIndex493
						if c := buffer[position]; c < rune('A') || c > rune('Z') {
							goto l495
						}
						position++
						goto l493
					l495:
						position, tokenIndex = position493, tokenIndex493
						if buffer[position] != rune('.') {
							goto l496
						}
						position++
						goto l493
					l496:
						position, tokenIndex = position493, tokenIndex493
						{
							position497, tokenIndex497 := position, tokenIndex
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l498
							}
							position++
							goto l497
						l498:
							position, tokenIndex = position497, tokenIndex497
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l492
							}
							position++
						}
					l497:
					}
				l493:
					goto l491
				l492:
					position, tokenIndex = position492, tokenIndex492
				}
				{
					position499, tokenIndex499 := position, tokenIndex
					{
						position501, tokenIndex501 := position, tokenIndex
						if buffer[position] != rune('.') {
							goto l502
						}
						position++
						goto l501
					l502:
						position, tokenIndex = position501, tokenIndex501
						if buffer[position] != rune('+') {
							goto l503
						}
						position++
						goto l501
					l503:
						position, tokenIndex = position501, tokenIndex501
						if buffer[position] != rune('-') {
							goto l499
						}
						position++
					}
				l501:
					goto l500
				l499:
					position, tokenIndex = position499, tokenIndex499
				}
			l500:
				add(ruleInstructionName, position488)
			}
			return true
		l487:
			position, tokenIndex = position487, tokenIndex487
			return false
		},
		/* 37 InstructionArg <- <(IndirectionIndicator? (ARMConstantTweak / RegisterOrConstant / LocalLabelRef / TOCRefHigh / TOCRefLow / GOTLocation / GOTAddress / GOTSymbolOffset / MemoryRef) AVX512Token*)> */
		func() bool {
			position504, tokenIndex504 := position, tokenIndex
			{
				position505 := position
				{
					position506, tokenIndex506 := position, tokenIndex
					if !_rules[ruleIndirectionIndicator]() {
						goto l506
					}
					goto l507
				l506:
					position, tokenIndex = position506, tokenIndex506
				}
			l507:
				{
					position508, tokenIndex508 := position, tokenIndex
					if !_rules[ruleARMConstantTweak]() {
						goto l509
					}
					goto l508
				l509:
					position, tokenIndex = position508, tokenIndex508
					if !_rules[ruleRegisterOrConstant]() {
						goto l510
					}
					goto l508
				l510:
					position, tokenIndex = position508, tokenIndex508
					if !_rules[ruleLocalLabelRef]() {
						goto l511
					}
					goto l508
				l511:
					position, tokenIndex = position508, tokenIndex508
					if !_rules[ruleTOCRefHigh]() {
						goto l512
					}
					goto l508
				l512:
					position, tokenIndex = position508, tokenIndex508
					if !_rules[ruleTOCRefLow]() {
						goto l513
					}
					goto l508
				l513:
					position, tokenIndex = position508, tokenIndex508
					if !_rules[ruleGOTLocation]() {
						goto l514
					}
					goto l508
				l514:
					position, tokenIndex = position508, tokenIndex508
					if !_rules[ruleGOTAddress]() {
						goto l515
					}
					goto l508
				l515:
					position, tokenIndex = position508, tokenIndex508
					if !_rules[ruleGOTSymbolOffset]() {
						goto l516
					}
					goto l508
				l516:
					position, tokenIndex = position508, tokenIndex508
					if !_rules[ruleMemoryRef]() {
						goto l504
					}
				}
			l508:
			l517:
				{
					position518, tokenIndex518 := position, tokenIndex
					if !_rules[ruleAVX512Token]() {
						goto l518
					}
					goto l517
				l518:
					position, tokenIndex = position518, tokenIndex518
				}
				add(ruleInstructionArg, position505)
			}
			return true
		l504:
			position, tokenIndex = position504, tokenIndex504
			return false
		},
		/* 38 GOTLocation <- <('$' '_' 'G' 'L' 'O' 'B' 'A' 'L' '_' 'O' 'F' 'F' 'S' 'E' 'T' '_' 'T' 'A' 'B' 'L' 'E' '_' '-' LocalSymbol)> */
		func() bool {
			position519, tokenIndex519 := position, tokenIndex
			{
				position520 := position
				if buffer[position] != rune('$') {
					goto l519
				}
				position++
				if buffer[position] != rune('_') {
					goto l519
				}
				position++
				if buffer[position] != rune('G') {
					goto l519
				}
				position++
				if buffer[position] != rune('L') {
					goto l519
				}
				position++
				if buffer[position] != rune('O') {
					goto l519
				}
				position++
				if buffer[position] != rune('B') {
					goto l519
				}
				position++
				if buffer[position] != rune('A') {
					goto l519
				}
				position++
				if buffer[position] != rune('L') {
					goto l519
				}
				position++
				if buffer[position] != rune('_') {
					goto l519
				}
				position++
				if buffer[position] != rune('O') {
					goto l519
				}
				position++
				if buffer[position] != rune('F') {
					goto l519
				}
				position++
				if buffer[position] != rune('F') {
					goto l519
				}
				position++
				if buffer[position] != rune('S') {
					goto l519
				}
				position++
				if buffer[position] != rune('E') {
					goto l519
				}
				position++
				if buffer[position] != rune('T') {
					goto l519
				}
				position++
				if buffer[position] != rune('_') {
					goto l519
				}
				position++
				if buffer[position] != rune('T') {
					goto l519
				}
				position++
				if buffer[position] != rune('A') {
					goto l519
				}
				position++
				if buffer[position] != rune('B') {
					goto l519
				}
				position++
				if buffer[position] != rune('L') {
					goto l519
				}
				position++
				if buffer[position] != rune('E') {
					goto l519
				}
				position++
				if buffer[position] != rune('_') {
					goto l519
				}
				position++
				if buffer[position] != rune('-') {
					goto l519
				}
				position++
				if !_rules[ruleLocalSymbol]() {
					goto l519
				}
				add(ruleGOTLocation, position520)
			}
			return true
		l519:
			position, tokenIndex = position519, tokenIndex519
			return false
		},
		/* 39 GOTAddress <- <('_' 'G' 'L' 'O' 'B' 'A' 'L' '_' 'O' 'F' 'F' 'S' 'E' 'T' '_' 'T' 'A' 'B' 'L' 'E' '_' '(' '%' 'r' 'i' 'p' ')')> */
		func() bool {
			position521, tokenIndex521 := position, tokenIndex
			{
				position522 := position
				if buffer[position] != rune('_') {
					goto l521
				}
				position++
				if buffer[position] != rune('G') {
					goto l521
				}
				position++
				if buffer[position] != rune('L') {
					goto l521
				}
				position++
				if buffer[position] != rune('O') {
					goto l521
				}
				position++
				if buffer[position] != rune('B') {
					goto l521
				}
				position++
				if buffer[position] != rune('A') {
					goto l521
				}
				position++
				if buffer[position] != rune('L') {
					goto l521
				}
				position++
				if buffer[position] != rune('_') {
					goto l521
				}
				position++
				if buffer[position] != rune('O') {
					goto l521
				}
				position++
				if buffer[position] != rune('F') {
					goto l521
				}
				position++
				if buffer[position] != rune('F') {
					goto l521
				}
				position++
				if buffer[position] != rune('S') {
					goto l521
				}
				position++
				if buffer[position] != rune('E') {
					goto l521
				}
				position++
				if buffer[position] != rune('T') {
					goto l521
				}
				position++
				if buffer[position] != rune('_') {
					goto l521
				}
				position++
				if buffer[position] != rune('T') {
					goto l521
				}
				position++
				if buffer[position] != rune('A') {
					goto l521
				}
				position++
				if buffer[position] != rune('B') {
					goto l521
				}
				position++
				if buffer[position] != rune('L') {
					goto l521
				}
				position++
				if buffer[position] != rune('E') {
					goto l521
				}
				position++
				if buffer[position] != rune('_') {
					goto l521
				}
				position++
				if buffer[position] != rune('(') {
					goto l521
				}
				position++
				if buffer[position] != rune('%') {
					goto l521
				}
				position++
				if buffer[position] != rune('r') {
					goto l521
				}
				position++
				if buffer[position] != rune('i') {
					goto l521
				}
				position++
				if buffer[position] != rune('p') {
					goto l521
				}
				position++
				if buffer[position] != rune(')') {
					goto l521
				}
				position++
				add(ruleGOTAddress, position522)
			}
			return true
		l521:
			position, tokenIndex = position521, tokenIndex521
			return false
		},
		/* 40 GOTSymbolOffset <- <(('$' SymbolName ('@' 'G' 'O' 'T') ('O' 'F' 'F')?) / (':' ('g' / 'G') ('o' / 'O') ('t' / 'T') ':' SymbolName))> */
		func() bool {
			position523, tokenIndex523 := position, tokenIndex
			{
				position524 := position
				{
					position525, tokenIndex525 := position, tokenIndex
					if buffer[position] != rune('$') {
						goto l526
					}
					position++
					if !_rules[ruleSymbolName]() {
						goto l526
					}
					if buffer[position] != rune('@') {
						goto l526
					}
					position++
					if buffer[position] != rune('G') {
						goto l526
					}
					position++
					if buffer[position] != rune('O') {
						goto l526
					}
					position++
					if buffer[position] != rune('T') {
						goto l526
					}
					position++
					{
						position527, tokenIndex527 := position, tokenIndex
						if buffer[position] != rune('O') {
							goto l527
						}
						position++
						if buffer[position] != rune('F') {
							goto l527
						}
						position++
						if buffer[position] != rune('F') {
							goto l527
						}
						position++
						goto l528
					l527:
						position, tokenIndex = position527, tokenIndex527
					}
				l528:
					goto l525
				l526:
					position, tokenIndex = position525, tokenIndex525
					if buffer[position] != rune(':') {
						goto l523
					}
					position++
					{
						position529, tokenIndex529 := position, tokenIndex
						if buffer[position] != rune('g') {
							goto l530
						}
						position++
						goto l529
					l530:
						position, tokenIndex = position529, tokenIndex529
						if buffer[position] != rune('G') {
							goto l523
						}
						position++
					}
				l529:
					{
						position531, tokenIndex531 := position, tokenIndex
						if buffer[position] != rune('o') {
							goto l532
						}
						position++
						goto l531
					l532:
						position, tokenIndex = position531, tokenIndex531
						if buffer[position] != rune('O') {
							goto l523
						}
						position++
					}
				l531:
					{
						position533, tokenIndex533 := position, tokenIndex
						if buffer[position] != rune('t') {
							goto l534
						}
						position++
						goto l533
					l534:
						position, tokenIndex = position533, tokenIndex533
						if buffer[position] != rune('T') {
							goto l523
						}
						position++
					}
				l533:
					if buffer[position] != rune(':') {
						goto l523
					}
					position++
					if !_rules[ruleSymbolName]() {
						goto l523
					}
				}
			l525:
				add(ruleGOTSymbolOffset, position524)
			}
			return true
		l523:
			position, tokenIndex = position523, tokenIndex523
			return false
		},
		/* 41 AVX512Token <- <(WS? '{' '%'? ([0-9] / [a-z])* '}')> */
		func() bool {
			position535, tokenIndex535 := position, tokenIndex
			{
				position536 := position
				{
					position537, tokenIndex537 := position, tokenIndex
					if !_rules[ruleWS]() {
						goto l537
					}
					goto l538
				l537:
					position, tokenIndex = position537, tokenIndex537
				}
			l538:
				if buffer[position] != rune('{') {
					goto l535
				}
				position++
				{
					position539, tokenIndex539 := position, tokenIndex
					if buffer[position] != rune('%') {
						goto l539
					}
					position++
					goto l540
				l539:
					position, tokenIndex = position539, tokenIndex539
				}
			l540:
			l541:
				{
					position542, tokenIndex542 := position, tokenIndex
					{
						position543, tokenIndex543 := position, tokenIndex
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l544
						}
						position++
						goto l543
					l544:
						position, tokenIndex = position543, tokenIndex543
						if c := buffer[position]; c < rune('a') || c > rune('z') {
							goto l542
						}
						position++
					}
				l543:
					goto l541
				l542:
					position, tokenIndex = position542, tokenIndex542
				}
				if buffer[position] != rune('}') {
					goto l535
				}
				position++
				add(ruleAVX512Token, position536)
			}
			return true
		l535:
			position, tokenIndex = position535, tokenIndex535
			return false
		},
		/* 42 TOCRefHigh <- <('.' 'T' 'O' 'C' '.' '-' (('0' 'b') / ('.' 'L' ([a-z] / [A-Z] / '_' / [0-9])+)) ('@' ('h' / 'H') ('a' / 'A')))> */
		func() bool {
			position545, tokenIndex545 := position, tokenIndex
			{
				position546 := position
				if buffer[position] != rune('.') {
					goto l545
				}
				position++
				if buffer[position] != rune('T') {
					goto l545
				}
				position++
				if buffer[position] != rune('O') {
					goto l545
				}
				position++
				if buffer[position] != rune('C') {
					goto l545
				}
				position++
				if buffer[position] != rune('.') {
					goto l545
				}
				position++
				if buffer[position] != rune('-') {
					goto l545
				}
				position++
				{
					position547, tokenIndex547 := position, tokenIndex
					if buffer[position] != rune('0') {
						goto l548
					}
					position++
					if buffer[position] != rune('b') {
						goto l548
					}
					position++
					goto l547
				l548:
					position, tokenIndex = position547, tokenIndex547
					if buffer[position] != rune('.') {
						goto l545
					}
					position++
					if buffer[position] != rune('L') {
						goto l545
					}
					position++
					{
						position551, tokenIndex551 := position, tokenIndex
						if c := buffer[position]; c < rune('a') || c > rune('z') {
							goto l552
						}
						position++
						goto l551
					l552:
						position, tokenIndex = position551, tokenIndex551
						if c := buffer[position]; c < rune('A') || c > rune('Z') {
							goto l553
						}
						position++
						goto l551
					l553:
						position, tokenIndex = position551, tokenIndex551
						if buffer[position] != rune('_') {
							goto l554
						}
						position++
						goto l551
					l554:
						position, tokenIndex = position551, tokenIndex551
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l545
						}
						position++
					}
				l551:
				l549:
					{
						position550, tokenIndex550 := position, tokenIndex
						{
							position555, tokenIndex555 := position, tokenIndex
							if c := buffer[position]; c < rune('a') || c > rune('z') {
								goto l556
							}
							position++
							goto l555
						l556:
							position, tokenIndex = position555, tokenIndex555
							if c := buffer[position]; c < rune('A') || c > rune('Z') {
								goto l557
							}
							position++
							goto l555
						l557:
							position, tokenIndex = position555, tokenIndex555
							if buffer[position] != rune('_') {
								goto l558
							}
							position++
							goto l555
						l558:
							position, tokenIndex = position555, tokenIndex555
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l550
							}
							position++
						}
					l555:
						goto l549
					l550:
						position, tokenIndex = position550, tokenIndex550
					}
				}
			l547:
				if buffer[position] != rune('@') {
					goto l545
				}
				position++
				{
					position559, tokenIndex559 := position, tokenIndex
					if buffer[position] != rune('h') {
						goto l560
					}
					position++
					goto l559
				l560:
					position, tokenIndex = position559, tokenIndex559
					if buffer[position] != rune('H') {
						goto l545
					}
					position++
				}
			l559:
				{
					position561, tokenIndex561 := position, tokenIndex
					if buffer[position] != rune('a') {
						goto l562
					}
					position++
					goto l561
				l562:
					position, tokenIndex = position561, tokenIndex561
					if buffer[position] != rune('A') {
						goto l545
					}
					position++
				}
			l561:
				add(ruleTOCRefHigh, position546)
			}
			return true
		l545:
			position, tokenIndex = position545, tokenIndex545
			return false
		},
		/* 43 TOCRefLow <- <('.' 'T' 'O' 'C' '.' '-' (('0' 'b') / ('.' 'L' ([a-z] / [A-Z] / '_' / [0-9])+)) ('@' ('l' / 'L')))> */
		func() bool {
			position563, tokenIndex563 := position, tokenIndex
			{
				position564 := position
				if buffer[position] != rune('.') {
					goto l563
				}
				position++
				if buffer[position] != rune('T') {
					goto l563
				}
				position++
				if buffer[position] != rune('O') {
					goto l563
				}
				position++
				if buffer[position] != rune('C') {
					goto l563
				}
				position++
				if buffer[position] != rune('.') {
					goto l563
				}
				position++
				if buffer[position] != rune('-') {
					goto l563
				}
				position++
				{
					position565, tokenIndex565 := position, tokenIndex
					if buffer[position] != rune('0') {
						goto l566
					}
					position++
					if buffer[position] != rune('b') {
						goto l566
					}
					position++
					goto l565
				l566:
					position, tokenIndex = position565, tokenIndex565
					if buffer[position] != rune('.') {
						goto l563
					}
					position++
					if buffer[position] != rune('L') {
						goto l563
					}
					position++
					{
						position569, tokenIndex569 := position, tokenIndex
						if c := buffer[position]; c < rune('a') || c > rune('z') {
							goto l570
						}
						position++
						goto l569
					l570:
						position, tokenIndex = position569, tokenIndex569
						if c := buffer[position]; c < rune('A') || c > rune('Z') {
							goto l571
						}
						position++
						goto l569
					l571:
						position, tokenIndex = position569, tokenIndex569
						if buffer[position] != rune('_') {
							goto l572
						}
						position++
						goto l569
					l572:
						position, tokenIndex = position569, tokenIndex569
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l563
						}
						position++
					}
				l569:
				l567:
					{
						position568, tokenIndex568 := position, tokenIndex
						{
							position573, tokenIndex573 := position, tokenIndex
							if c := buffer[position]; c < rune('a') || c > rune('z') {
								goto l574
							}
							position++
							goto l573
						l574:
							position, tokenIndex = position573, tokenIndex573
							if c := buffer[position]; c < rune('A') || c > rune('Z') {
								goto l575
							}
							position++
							goto l573
						l575:
							position, tokenIndex = position573, tokenIndex573
							if buffer[position] != rune('_') {
								goto l576
							}
							position++
							goto l573
						l576:
							position, tokenIndex = position573, tokenIndex573
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l568
							}
							position++
						}
					l573:
						goto l567
					l568:
						position, tokenIndex = position568, tokenIndex568
					}
				}
			l565:
				if buffer[position] != rune('@') {
					goto l563
				}
				position++
				{
					position577, tokenIndex577 := position, tokenIndex
					if buffer[position] != rune('l') {
						goto l578
					}
					position++
					goto l577
				l578:
					position, tokenIndex = position577, tokenIndex577
					if buffer[position] != rune('L') {
						goto l563
					}
					position++
				}
			l577:
				add(ruleTOCRefLow, position564)
			}
			return true
		l563:
			position, tokenIndex = position563, tokenIndex563
			return false
		},
		/* 44 IndirectionIndicator <- <'*'> */
		func() bool {
			position579, tokenIndex579 := position, tokenIndex
			{
				position580 := position
				if buffer[position] != rune('*') {
					goto l579
				}
				position++
				add(ruleIndirectionIndicator, position580)
			}
			return true
		l579:
			position, tokenIndex = position579, tokenIndex579
			return false
		},
		/* 45 Float <- <([0-9]+ '.' [0-9]*)> */
		func() bool {
			position581, tokenIndex581 := position, tokenIndex
			{
				position582 := position
				if c := buffer[position]; c < rune('0') || c > rune('9') {
					goto l581
				}
				position++
			l583:
				{
					position584, tokenIndex584 := position, tokenIndex
					if c := buffer[position]; c < rune('0') || c > rune('9') {
						goto l584
					}
					position++
					goto l583
				l584:
					position, tokenIndex = position584, tokenIndex584
				}
				if buffer[position] != rune('.') {
					goto l581
				}
				position++
			l585:
				{
					position586, tokenIndex586 := position, tokenIndex
					if c := buffer[position]; c < rune('0') || c > rune('9') {
						goto l586
					}
					position++
					goto l585
				l586:
					position, tokenIndex = position586, tokenIndex586
				}
				add(ruleFloat, position582)
			}
			return true
		l581:
			position, tokenIndex = position581, tokenIndex581
			return false
		},
		/* 46 RegisterOrConstant <- <((('%' ([a-z] / [A-Z]) ([a-z] / [A-Z] / ([0-9] / [0-9]))*) / ('$'? ((Offset Offset) / Offset)) / ('#' Float) / ('#' Offset ('*' [0-9]+ ('-' [0-9] [0-9]*)?)?) / ('#' '~'? '(' [0-9] WS? ('<' '<') WS? [0-9] ')') / ARMRegister) !('f' / 'b' / ':' / '(' / '+' / '-'))> */
		func() bool {
			position587, tokenIndex587 := position, tokenIndex
			{
				position588 := position
				{
					position589, tokenIndex589 := position, tokenIndex
					if buffer[position] != rune('%') {
						goto l590
					}
					position++
					{
						position591, tokenIndex591 := position, tokenIndex
						if c := buffer[position]; c < rune('a') || c > rune('z') {
							goto l592
						}
						position++
						goto l591
					l592:
						position, tokenIndex = position591, tokenIndex591
						if c := buffer[position]; c < rune('A') || c > rune('Z') {
							goto l590
						}
						position++
					}
				l591:
				l593:
					{
						position594, tokenIndex594 := position, tokenIndex
						{
							position595, tokenIndex595 := position, tokenIndex
							if c := buffer[position]; c < rune('a') || c > rune('z') {
								goto l596
							}
							position++
							goto l595
						l596:
							position, tokenIndex = position595, tokenIndex595
							if c := buffer[position]; c < rune('A') || c > rune('Z') {
								goto l597
							}
							position++
							goto l595
						l597:
							position, tokenIndex = position595, tokenIndex595
							{
								position598, tokenIndex598 := position, tokenIndex
								if c := buffer[position]; c < rune('0') || c > rune('9') {
									goto l599
								}
								position++
								goto l598
							l599:
								position, tokenIndex = position598, tokenIndex598
								if c := buffer[position]; c < rune('0') || c > rune('9') {
									goto l594
								}
								position++
							}
						l598:
						}
					l595:
						goto l593
					l594:
						position, tokenIndex = position594, tokenIndex594
					}
					goto l589
				l590:
					position, tokenIndex = position589, tokenIndex589
					{
						position601, tokenIndex601 := position, tokenIndex
						if buffer[position] != rune('$') {
							goto l601
						}
						position++
						goto l602
					l601:
						position, tokenIndex = position601, tokenIndex601
					}
				l602:
					{
						position603, tokenIndex603 := position, tokenIndex
						if !_rules[ruleOffset]() {
							goto l604
						}
						if !_rules[ruleOffset]() {
							goto l604
						}
						goto l603
					l604:
						position, tokenIndex = position603, tokenIndex603
						if !_rules[ruleOffset]() {
							goto l600
						}
					}
				l603:
					goto l589
				l600:
					position, tokenIndex = position589, tokenIndex589
					if buffer[position] != rune('#') {
						goto l605
					}
					position++
					if !_rules[ruleFloat]() {
						goto l605
					}
					goto l589
				l605:
					position, tokenIndex = position589, tokenIndex589
					if buffer[position] != rune('#') {
						goto l606
					}
					position++
					if !_rules[ruleOffset]() {
						goto l606
					}
					{
						position607, tokenIndex607 := position, tokenIndex
						if buffer[position] != rune('*') {
							goto l607
						}
						position++
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l607
						}
						position++
					l609:
						{
							position610, tokenIndex610 := position, tokenIndex
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l610
							}
							position++
							goto l609
						l610:
							position, tokenIndex = position610, tokenIndex610
						}
						{
							position611, tokenIndex611 := position, tokenIndex
							if buffer[position] != rune('-') {
								goto l611
							}
							position++
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l611
							}
							position++
						l613:
							{
								position614, tokenIndex614 := position, tokenIndex
								if c := buffer[position]; c < rune('0') || c > rune('9') {
									goto l614
								}
								position++
								goto l613
							l614:
								position, tokenIndex = position614, tokenIndex614
							}
							goto l612
						l611:
							position, tokenIndex = position611, tokenIndex611
						}
					l612:
						goto l608
					l607:
						position, tokenIndex = position607, tokenIndex607
					}
				l608:
					goto l589
				l606:
					position, tokenIndex = position589, tokenIndex589
					if buffer[position] != rune('#') {
						goto l615
					}
					position++
					{
						position616, tokenIndex616 := position, tokenIndex
						if buffer[position] != rune('~') {
							goto l616
						}
						position++
						goto l617
					l616:
						position, tokenIndex = position616, tokenIndex616
					}
				l617:
					if buffer[position] != rune('(') {
						goto l615
					}
					position++
					if c := buffer[position]; c < rune('0') || c > rune('9') {
						goto l615
					}
					position++
					{
						position618, tokenIndex618 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l618
						}
						goto l619
					l618:
						position, tokenIndex = position618, tokenIndex618
					}
				l619:
					if buffer[position] != rune('<') {
						goto l615
					}
					position++
					if buffer[position] != rune('<') {
						goto l615
					}
					position++
					{
						position620, tokenIndex620 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l620
						}
						goto l621
					l620:
						position, tokenIndex = position620, tokenIndex620
					}
				l621:
					if c := buffer[position]; c < rune('0') || c > rune('9') {
						goto l615
					}
					position++
					if buffer[position] != rune(')') {
						goto l615
					}
					position++
					goto l589
				l615:
					position, tokenIndex = position589, tokenIndex589
					if !_rules[ruleARMRegister]() {
						goto l587
					}
				}
			l589:
				{
					position622, tokenIndex622 := position, tokenIndex
					{
						position623, tokenIndex623 := position, tokenIndex
						if buffer[position] != rune('f') {
							goto l624
						}
						position++
						goto l623
					l624:
						position, tokenIndex = position623, tokenIndex623
						if buffer[position] != rune('b') {
							goto l625
						}
						position++
						goto l623
					l625:
						position, tokenIndex = position623, tokenIndex623
						if buffer[position] != rune(':') {
							goto l626
						}
						position++
						goto l623
					l626:
						position, tokenIndex = position623, tokenIndex623
						if buffer[position] != rune('(') {
							goto l627
						}
						position++
						goto l623
					l627:
						position, tokenIndex = position623, tokenIndex623
						if buffer[position] != rune('+') {
							goto l628
						}
						position++
						goto l623
					l628:
						position, tokenIndex = position623, tokenIndex623
						if buffer[position] != rune('-') {
							goto l622
						}
						position++
					}
				l623:
					goto l587
				l622:
					position, tokenIndex = position622, tokenIndex622
				}
				add(ruleRegisterOrConstant, position588)
			}
			return true
		l587:
			position, tokenIndex = position587, tokenIndex587
			return false
		},
		/* 47 ARMConstantTweak <- <((((('u' / 's') (('x' / 'X') ('t' / 'T')) ('x' / 'w' / 'h' / 'b')) / (('l' / 'L') ('s' / 'S') ('l' / 'L')) / (('l' / 'L') ('s' / 'S') ('r' / 'R')) / (('r' / 'R') ('o' / 'O') ('r' / 'R')) / (('a' / 'A') ('s' / 'S') ('r' / 'R')) / (('m' / 'M') ('s' / 'S') ('l' / 'L'))) (WS '#' Offset)?) / (('m' / 'M') ('u' / 'U') ('l' / 'L') ' ' ('v' / 'V') ('l' / 'L')) / (('m' / 'M') ('u' / 'U') ('l' / 'L') ' ' '#' [0-9]))> */
		func() bool {
			position629, tokenIndex629 := position, tokenIndex
			{
				position630 := position
				{
					position631, tokenIndex631 := position, tokenIndex
					{
						position633, tokenIndex633 := position, tokenIndex
						{
							position635, tokenIndex635 := position, tokenIndex
							if buffer[position] != rune('u') {
								goto l636
							}
							position++
							goto l635
						l636:
							position, tokenIndex = position635, tokenIndex635
							if buffer[position] != rune('s') {
								goto l634
							}
							position++
						}
					l635:
						{
							position637, tokenIndex637 := position, tokenIndex
							if buffer[position] != rune('x') {
								goto l638
							}
							position++
							goto l637
						l638:
							position, tokenIndex = position637, tokenIndex637
							if buffer[position] != rune('X') {
								goto l634
							}
							position++
						}
					l637:
						{
							position639, tokenIndex639 := position, tokenIndex
							if buffer[position] != rune('t') {
								goto l640
							}
							position++
							goto l639
						l640:
							position, tokenIndex = position639, tokenIndex639
							if buffer[position] != rune('T') {
								goto l634
							}
							position++
						}
					l639:
						{
							position641, tokenIndex641 := position, tokenIndex
							if buffer[position] != rune('x') {
								goto l642
							}
							position++
							goto l641
						l642:
							position, tokenIndex = position641, tokenIndex641
							if buffer[position] != rune('w') {
								goto l643
							}
							position++
							goto l641
						l643:
							position, tokenIndex = position641, tokenIndex641
							if buffer[position] != rune('h') {
								goto l644
							}
							position++
							goto l641
						l644:
							position, tokenIndex = position641, tokenIndex641
							if buffer[position] != rune('b') {
								goto l634
							}
							position++
						}
					l641:
						goto l633
					l634:
						position, tokenIndex = position633, tokenIndex633
						{
							position646, tokenIndex646 := position, tokenIndex
							if buffer[position] != rune('l') {
								goto l647
							}
							position++
							goto l646
						l647:
							position, tokenIndex = position646, tokenIndex646
							if buffer[position] != rune('L') {
								goto l645
							}
							position++
						}
					l646:
						{
							position648, tokenIndex648 := position, tokenIndex
							if buffer[position] != rune('s') {
								goto l649
							}
							position++
							goto l648
						l649:
							position, tokenIndex = position648, tokenIndex648
							if buffer[position] != rune('S') {
								goto l645
							}
							position++
						}
					l648:
						{
							position650, tokenIndex650 := position, tokenIndex
							if buffer[position] != rune('l') {
								goto l651
							}
							position++
							goto l650
						l651:
							position, tokenIndex = position650, tokenIndex650
							if buffer[position] != rune('L') {
								goto l645
							}
							position++
						}
					l650:
						goto l633
					l645:
						position, tokenIndex = position633, tokenIndex633
						{
							position653, tokenIndex653 := position, tokenIndex
							if buffer[position] != rune('l') {
								goto l654
							}
							position++
							goto l653
						l654:
							position, tokenIndex = position653, tokenIndex653
							if buffer[position] != rune('L') {
								goto l652
							}
							position++
						}
					l653:
						{
							position655, tokenIndex655 := position, tokenIndex
							if buffer[position] != rune('s') {
								goto l656
							}
							position++
							goto l655
						l656:
							position, tokenIndex = position655, tokenIndex655
							if buffer[position] != rune('S') {
								goto l652
							}
							position++
						}
					l655:
						{
							position657, tokenIndex657 := position, tokenIndex
							if buffer[position] != rune('r') {
								goto l658
							}
							position++
							goto l657
						l658:
							position, tokenIndex = position657, tokenIndex657
							if buffer[position] != rune('R') {
								goto l652
							}
							position++
						}
					l657:
						goto l633
					l652:
						position, tokenIndex = position633, tokenIndex633
						{
							position660, tokenIndex660 := position, tokenIndex
							if buffer[position] != rune('r') {
								goto l661
							}
							position++
							goto l660
						l661:
							position, tokenIndex = position660, tokenIndex660
							if buffer[position] != rune('R') {
								goto l659
							}
							position++
						}
					l660:
						{
							position662, tokenIndex662 := position, tokenIndex
							if buffer[position] != rune('o') {
								goto l663
							}
							position++
							goto l662
						l663:
							position, tokenIndex = position662, tokenIndex662
							if buffer[position] != rune('O') {
								goto l659
							}
							position++
						}
					l662:
						{
							position664, tokenIndex664 := position, tokenIndex
							if buffer[position] != rune('r') {
								goto l665
							}
							position++
							goto l664
						l665:
							position, tokenIndex = position664, tokenIndex664
							if buffer[position] != rune('R') {
								goto l659
							}
							position++
						}
					l664:
						goto l633
					l659:
						position, tokenIndex = position633, tokenIndex633
						{
							position667, tokenIndex667 := position, tokenIndex
							if buffer[position] != rune('a') {
								goto l668
							}
							position++
							goto l667
						l668:
							position, tokenIndex = position667, tokenIndex667
							if buffer[position] != rune('A') {
								goto l666
							}
							position++
						}
					l667:
						{
							position669, tokenIndex669 := position, tokenIndex
							if buffer[position] != rune('s') {
								goto l670
							}
							position++
							goto l669
						l670:
							position, tokenIndex = position669, tokenIndex669
							if buffer[position] != rune('S') {
								goto l666
							}
							position++
						}
					l669:
						{
							position671, tokenIndex671 := position, tokenIndex
							if buffer[position] != rune('r') {
								goto l672
							}
							position++
							goto l671
						l672:
							position, tokenIndex = position671, tokenIndex671
							if buffer[position] != rune('R') {
								goto l666
							}
							position++
						}
					l671:
						goto l633
					l666:
						position, tokenIndex = position633, tokenIndex633
						{
							position673, tokenIndex673 := position, tokenIndex
							if buffer[position] != rune('m') {
								goto l674
							}
							position++
							goto l673
						l674:
							position, tokenIndex = position673, tokenIndex673
							if buffer[position] != rune('M') {
								goto l632
							}
							position++
						}
					l673:
						{
							position675, tokenIndex675 := position, tokenIndex
							if buffer[position] != rune('s') {
								goto l676
							}
							position++
							goto l675
						l676:
							position, tokenIndex = position675, tokenIndex675
							if buffer[position] != rune('S') {
								goto l632
							}
							position++
						}
					l675:
						{
							position677, tokenIndex677 := position, tokenIndex
							if buffer[position] != rune('l') {
								goto l678
							}
							position++
							goto l677
						l678:
							position, tokenIndex = position677, tokenIndex677
							if buffer[position] != rune('L') {
								goto l632
							}
							position++
						}
					l677:
					}
				l633:
					{
						position679, tokenIndex679 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l679
						}
						if buffer[position] != rune('#') {
							goto l679
						}
						position++
						if !_rules[ruleOffset]() {
							goto l679
						}
						goto l680
					l679:
						position, tokenIndex = position679, tokenIndex679
					}
				l680:
					goto l631
				l632:
					position, tokenIndex = position631, tokenIndex631
					{
						position682, tokenIndex682 := position, tokenIndex
						if buffer[position] != rune('m') {
							goto l683
						}
						position++
						goto l682
					l683:
						position, tokenIndex = position682, tokenIndex682
						if buffer[position] != rune('M') {
							goto l681
						}
						position++
					}
				l682:
					{
						position684, tokenIndex684 := position, tokenIndex
						if buffer[position] != rune('u') {
							goto l685
						}
						position++
						goto l684
					l685:
						position, tokenIndex = position684, tokenIndex684
						if buffer[position] != rune('U') {
							goto l681
						}
						position++
					}
				l684:
					{
						position686, tokenIndex686 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l687
						}
						position++
						goto l686
					l687:
						position, tokenIndex = position686, tokenIndex686
						if buffer[position] != rune('L') {
							goto l681
						}
						position++
					}
				l686:
					if buffer[position] != rune(' ') {
						goto l681
					}
					position++
					{
						position688, tokenIndex688 := position, tokenIndex
						if buffer[position] != rune('v') {
							goto l689
						}
						position++
						goto l688
					l689:
						position, tokenIndex = position688, tokenIndex688
						if buffer[position] != rune('V') {
							goto l681
						}
						position++
					}
				l688:
					{
						position690, tokenIndex690 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l691
						}
						position++
						goto l690
					l691:
						position, tokenIndex = position690, tokenIndex690
						if buffer[position] != rune('L') {
							goto l681
						}
						position++
					}
				l690:
					goto l631
				l681:
					position, tokenIndex = position631, tokenIndex631
					{
						position692, tokenIndex692 := position, tokenIndex
						if buffer[position] != rune('m') {
							goto l693
						}
						position++
						goto l692
					l693:
						position, tokenIndex = position692, tokenIndex692
						if buffer[position] != rune('M') {
							goto l629
						}
						position++
					}
				l692:
					{
						position694, tokenIndex694 := position, tokenIndex
						if buffer[position] != rune('u') {
							goto l695
						}
						position++
						goto l694
					l695:
						position, tokenIndex = position694, tokenIndex694
						if buffer[position] != rune('U') {
							goto l629
						}
						position++
					}
				l694:
					{
						position696, tokenIndex696 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l697
						}
						position++
						goto l696
					l697:
						position, tokenIndex = position696, tokenIndex696
						if buffer[position] != rune('L') {
							goto l629
						}
						position++
					}
				l696:
					if buffer[position] != rune(' ') {
						goto l629
					}
					position++
					if buffer[position] != rune('#') {
						goto l629
					}
					position++
					if c := buffer[position]; c < rune('0') || c > rune('9') {
						goto l629
					}
					position++
				}
			l631:
				add(ruleARMConstantTweak, position630)
			}
			return true
		l629:
			position, tokenIndex = position629, tokenIndex629
			return false
		},
		/* 48 ARMRegister <- <((('s' / 'S') ('p' / 'P')) / (('x' / 'w' / 'd' / 'q' / 's' / 'h' / 'b') [0-9] [0-9]?) / (('x' / 'X') ('z' / 'Z') ('r' / 'R')) / (('w' / 'W') ('z' / 'Z') ('r' / 'R')) / (('n' / 'N') ('z' / 'Z') ('c' / 'C') ('v' / 'V')) / SVE2PredicateRegister / ARMVectorRegister / SVE2SpecialValue / ('{' WS? ARMVectorRegister (',' WS? ARMVectorRegister)* WS? '}' ('[' [0-9] [0-9]? ']')?))> */
		func() bool {
			position698, tokenIndex698 := position, tokenIndex
			{
				position699 := position
				{
					position700, tokenIndex700 := position, tokenIndex
					{
						position702, tokenIndex702 := position, tokenIndex
						if buffer[position] != rune('s') {
							goto l703
						}
						position++
						goto l702
					l703:
						position, tokenIndex = position702, tokenIndex702
						if buffer[position] != rune('S') {
							goto l701
						}
						position++
					}
				l702:
					{
						position704, tokenIndex704 := position, tokenIndex
						if buffer[position] != rune('p') {
							goto l705
						}
						position++
						goto l704
					l705:
						position, tokenIndex = position704, tokenIndex704
						if buffer[position] != rune('P') {
							goto l701
						}
						position++
					}
				l704:
					goto l700
				l701:
					position, tokenIndex = position700, tokenIndex700
					{
						position707, tokenIndex707 := position, tokenIndex
						if buffer[position] != rune('x') {
							goto l708
						}
						position++
						goto l707
					l708:
						position, tokenIndex = position707, tokenIndex707
						if buffer[position] != rune('w') {
							goto l709
						}
						position++
						goto l707
					l709:
						position, tokenIndex = position707, tokenIndex707
						if buffer[position] != rune('d') {
							goto l710
						}
						position++
						goto l707
					l710:
						position, tokenIndex = position707, tokenIndex707
						if buffer[position] != rune('q') {
							goto l711
						}
						position++
						goto l707
					l711:
						position, tokenIndex = position707, tokenIndex707
						if buffer[position] != rune('s') {
							goto l712
						}
						position++
						goto l707
					l712:
						position, tokenIndex = position707, tokenIndex707
						if buffer[position] != rune('h') {
							goto l713
						}
						position++
						goto l707
					l713:
						position, tokenIndex = position707, tokenIndex707
						if buffer[position] != rune('b') {
							goto l706
						}
						position++
					}
				l707:
					if c := buffer[position]; c < rune('0') || c > rune('9') {
						goto l706
					}
					position++
					{
						position714, tokenIndex714 := position, tokenIndex
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l714
						}
						position++
						goto l715
					l714:
						position, tokenIndex = position714, tokenIndex714
					}
				l715:
					goto l700
				l706:
					position, tokenIndex = position700, tokenIndex700
					{
						position717, tokenIndex717 := position, tokenIndex
						if buffer[position] != rune('x') {
							goto l718
						}
						position++
						goto l717
					l718:
						position, tokenIndex = position717, tokenIndex717
						if buffer[position] != rune('X') {
							goto l716
						}
						position++
					}
				l717:
					{
						position719, tokenIndex719 := position, tokenIndex
						if buffer[position] != rune('z') {
							goto l720
						}
						position++
						goto l719
					l720:
						position, tokenIndex = position719, tokenIndex719
						if buffer[position] != rune('Z') {
							goto l716
						}
						position++
					}
				l719:
					{
						position721, tokenIndex721 := position, tokenIndex
						if buffer[position] != rune('r') {
							goto l722
						}
						position++
						goto l721
					l722:
						position, tokenIndex = position721, tokenIndex721
						if buffer[position] != rune('R') {
							goto l716
						}
						position++
					}
				l721:
					goto l700
				l716:
					position, tokenIndex = position700, tokenIndex700
					{
						position724, tokenIndex724 := position, tokenIndex
						if buffer[position] != rune('w') {
							goto l725
						}
						position++
						goto l724
					l725:
						position, tokenIndex = position724, tokenIndex724
						if buffer[position] != rune('W') {
							goto l723
						}
						position++
					}
				l724:
					{
						position726, tokenIndex726 := position, tokenIndex
						if buffer[position] != rune('z') {
							goto l727
						}
						position++
						goto l726
					l727:
						position, tokenIndex = position726, tokenIndex726
						if buffer[position] != rune('Z') {
							goto l723
						}
						position++
					}
				l726:
					{
						position728, tokenIndex728 := position, tokenIndex
						if buffer[position] != rune('r') {
							goto l729
						}
						position++
						goto l728
					l729:
						position, tokenIndex = position728, tokenIndex728
						if buffer[position] != rune('R') {
							goto l723
						}
						position++
					}
				l728:
					goto l700
				l723:
					position, tokenIndex = position700, tokenIndex700
					{
						position731, tokenIndex731 := position, tokenIndex
						if buffer[position] != rune('n') {
							goto l732
						}
						position++
						goto l731
					l732:
						position, tokenIndex = position731, tokenIndex731
						if buffer[position] != rune('N') {
							goto l730
						}
						position++
					}
				l731:
					{
						position733, tokenIndex733 := position, tokenIndex
						if buffer[position] != rune('z') {
							goto l734
						}
						position++
						goto l733
					l734:
						position, tokenIndex = position733, tokenIndex733
						if buffer[position] != rune('Z') {
							goto l730
						}
						position++
					}
				l733:
					{
						position735, tokenIndex735 := position, tokenIndex
						if buffer[position] != rune('c') {
							goto l736
						}
						position++
						goto l735
					l736:
						position, tokenIndex = position735, tokenIndex735
						if buffer[position] != rune('C') {
							goto l730
						}
						position++
					}
				l735:
					{
						position737, tokenIndex737 := position, tokenIndex
						if buffer[position] != rune('v') {
							goto l738
						}
						position++
						goto l737
					l738:
						position, tokenIndex = position737, tokenIndex737
						if buffer[position] != rune('V') {
							goto l730
						}
						position++
					}
				l737:
					goto l700
				l730:
					position, tokenIndex = position700, tokenIndex700
					if !_rules[ruleSVE2PredicateRegister]() {
						goto l739
					}
					goto l700
				l739:
					position, tokenIndex = position700, tokenIndex700
					if !_rules[ruleARMVectorRegister]() {
						goto l740
					}
					goto l700
				l740:
					position, tokenIndex = position700, tokenIndex700
					if !_rules[ruleSVE2SpecialValue]() {
						goto l741
					}
					goto l700
				l741:
					position, tokenIndex = position700, tokenIndex700
					if buffer[position] != rune('{') {
						goto l698
					}
					position++
					{
						position742, tokenIndex742 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l742
						}
						goto l743
					l742:
						position, tokenIndex = position742, tokenIndex742
					}
				l743:
					if !_rules[ruleARMVectorRegister]() {
						goto l698
					}
				l744:
					{
						position745, tokenIndex745 := position, tokenIndex
						if buffer[position] != rune(',') {
							goto l745
						}
						position++
						{
							position746, tokenIndex746 := position, tokenIndex
							if !_rules[ruleWS]() {
								goto l746
							}
							goto l747
						l746:
							position, tokenIndex = position746, tokenIndex746
						}
					l747:
						if !_rules[ruleARMVectorRegister]() {
							goto l745
						}
						goto l744
					l745:
						position, tokenIndex = position745, tokenIndex745
					}
					{
						position748, tokenIndex748 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l748
						}
						goto l749
					l748:
						position, tokenIndex = position748, tokenIndex748
					}
				l749:
					if buffer[position] != rune('}') {
						goto l698
					}
					position++
					{
						position750, tokenIndex750 := position, tokenIndex
						if buffer[position] != rune('[') {
							goto l750
						}
						position++
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l750
						}
						position++
						{
							position752, tokenIndex752 := position, tokenIndex
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l752
							}
							position++
							goto l753
						l752:
							position, tokenIndex = position752, tokenIndex752
						}
					l753:
						if buffer[position] != rune(']') {
							goto l750
						}
						position++
						goto l751
					l750:
						position, tokenIndex = position750, tokenIndex750
					}
				l751:
				}
			l700:
				add(ruleARMRegister, position699)
			}
			return true
		l698:
			position, tokenIndex = position698, tokenIndex698
			return false
		},
		/* 49 ARMVectorRegister <- <(('p' / 'v' / 'z') [0-9] [0-9]? !([0-9] / [0-9] / ([a-z] / [A-Z]) / '_') ('.' [0-9]* ('b' / 's' / 'd' / 'h' / 'q') ('[' [0-9] [0-9]? ']')?)?)> */
		func() bool {
			position754, tokenIndex754 := position, tokenIndex
			{
				position755 := position
				{
					position756, tokenIndex756 := position, tokenIndex
					if buffer[position] != rune('p') {
						goto l757
					}
					position++
					goto l756
				l757:
					position, tokenIndex = position756, tokenIndex756
					if buffer[position] != rune('v') {
						goto l758
					}
					position++
					goto l756
				l758:
					position, tokenIndex = position756, tokenIndex756
					if buffer[position] != rune('z') {
						goto l754
					}
					position++
				}
			l756:
				if c := buffer[position]; c < rune('0') || c > rune('9') {
					goto l754
				}
				position++
				{
					position759, tokenIndex759 := position, tokenIndex
					if c := buffer[position]; c < rune('0') || c > rune('9') {
						goto l759
					}
					position++
					goto l760
				l759:
					position, tokenIndex = position759, tokenIndex759
				}
			l760:
				{
					position761, tokenIndex761 := position, tokenIndex
					{
						position762, tokenIndex762 := position, tokenIndex
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l763
						}
						position++
						goto l762
					l763:
						position, tokenIndex = position762, tokenIndex762
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l764
						}
						position++
						goto l762
					l764:
						position, tokenIndex = position762, tokenIndex762
						{
							position766, tokenIndex766 := position, tokenIndex
							if c := buffer[position]; c < rune('a') || c > rune('z') {
								goto l767
							}
							position++
							goto l766
						l767:
							position, tokenIndex = position766, tokenIndex766
							if c := buffer[position]; c < rune('A') || c > rune('Z') {
								goto l765
							}
							position++
						}
					l766:
						goto l762
					l765:
						position, tokenIndex = position762, tokenIndex762
						if buffer[position] != rune('_') {
							goto l761
						}
						position++
					}
				l762:
					goto l754
				l761:
					position, tokenIndex = position761, tokenIndex761
				}
				{
					position768, tokenIndex768 := position, tokenIndex
					if buffer[position] != rune('.') {
						goto l768
					}
					position++
				l770:
					{
						position771, tokenIndex771 := position, tokenIndex
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l771
						}
						position++
						goto l770
					l771:
						position, tokenIndex = position771, tokenIndex771
					}
					{
						position772, tokenIndex772 := position, tokenIndex
						if buffer[position] != rune('b') {
							goto l773
						}
						position++
						goto l772
					l773:
						position, tokenIndex = position772, tokenIndex772
						if buffer[position] != rune('s') {
							goto l774
						}
						position++
						goto l772
					l774:
						position, tokenIndex = position772, tokenIndex772
						if buffer[position] != rune('d') {
							goto l775
						}
						position++
						goto l772
					l775:
						position, tokenIndex = position772, tokenIndex772
						if buffer[position] != rune('h') {
							goto l776
						}
						position++
						goto l772
					l776:
						position, tokenIndex = position772, tokenIndex772
						if buffer[position] != rune('q') {
							goto l768
						}
						position++
					}
				l772:
					{
						position777, tokenIndex777 := position, tokenIndex
						if buffer[position] != rune('[') {
							goto l777
						}
						position++
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l777
						}
						position++
						{
							position779, tokenIndex779 := position, tokenIndex
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l779
							}
							position++
							goto l780
						l779:
							position, tokenIndex = position779, tokenIndex779
						}
					l780:
						if buffer[position] != rune(']') {
							goto l777
						}
						position++
						goto l778
					l777:
						position, tokenIndex = position777, tokenIndex777
					}
				l778:
					goto l769
				l768:
					position, tokenIndex = position768, tokenIndex768
				}
			l769:
				add(ruleARMVectorRegister, position755)
			}
			return true
		l754:
			position, tokenIndex = position754, tokenIndex754
			return false
		},
		/* 50 SVE2PredicateRegister <- <(('p' / 'P') [0-9] [0-9]? '/' ('m' / 'M' / ('z' / 'Z')))> */
		func() bool {
			position781, tokenIndex781 := position, tokenIndex
			{
				position782 := position
				{
					position783, tokenIndex783 := position, tokenIndex
					if buffer[position] != rune('p') {
						goto l784
					}
					position++
					goto l783
				l784:
					position, tokenIndex = position783, tokenIndex783
					if buffer[position] != rune('P') {
						goto l781
					}
					position++
				}
			l783:
				if c := buffer[position]; c < rune('0') || c > rune('9') {
					goto l781
				}
				position++
				{
					position785, tokenIndex785 := position, tokenIndex
					if c := buffer[position]; c < rune('0') || c > rune('9') {
						goto l785
					}
					position++
					goto l786
				l785:
					position, tokenIndex = position785, tokenIndex785
				}
			l786:
				if buffer[position] != rune('/') {
					goto l781
				}
				position++
				{
					position787, tokenIndex787 := position, tokenIndex
					if buffer[position] != rune('m') {
						goto l788
					}
					position++
					goto l787
				l788:
					position, tokenIndex = position787, tokenIndex787
					if buffer[position] != rune('M') {
						goto l789
					}
					position++
					goto l787
				l789:
					position, tokenIndex = position787, tokenIndex787
					{
						position790, tokenIndex790 := position, tokenIndex
						if buffer[position] != rune('z') {
							goto l791
						}
						position++
						goto l790
					l791:
						position, tokenIndex = position790, tokenIndex790
						if buffer[position] != rune('Z') {
							goto l781
						}
						position++
					}
				l790:
				}
			l787:
				add(ruleSVE2PredicateRegister, position782)
			}
			return true
		l781:
			position, tokenIndex = position781, tokenIndex781
			return false
		},
		/* 51 SVE2SpecialValue <- <(((('p' / 'P') ('o' / 'O') ('w' / 'W') '2') / (('v' / 'V') ('l' / 'L') ('1' / '2' / '3' / '4' / '5' / '6' / '7' / '8') ![0-9]) / (('v' / 'V') ('l' / 'L') '1' '6') / (('v' / 'V') ('l' / 'L') '3' '2') / (('v' / 'V') ('l' / 'L') '6' '4') / (('v' / 'V') ('l' / 'L') '1' '2' '8') / (('v' / 'V') ('l' / 'L') '2' '5' '6') / (('m' / 'M') ('u' / 'U') ('l' / 'L') '3') / (('m' / 'M') ('u' / 'U') ('l' / 'L') '4') / (('a' / 'A') ('l' / 'L') ('l' / 'L'))) !([0-9] / [0-9] / ([a-z] / [A-Z]) / '_'))> */
		func() bool {
			position792, tokenIndex792 := position, tokenIndex
			{
				position793 := position
				{
					position794, tokenIndex794 := position, tokenIndex
					{
						position796, tokenIndex796 := position, tokenIndex
						if buffer[position] != rune('p') {
							goto l797
						}
						position++
						goto l796
					l797:
						position, tokenIndex = position796, tokenIndex796
						if buffer[position] != rune('P') {
							goto l795
						}
						position++
					}
				l796:
					{
						position798, tokenIndex798 := position, tokenIndex
						if buffer[position] != rune('o') {
							goto l799
						}
						position++
						goto l798
					l799:
						position, tokenIndex = position798, tokenIndex798
						if buffer[position] != rune('O') {
							goto l795
						}
						position++
					}
				l798:
					{
						position800, tokenIndex800 := position, tokenIndex
						if buffer[position] != rune('w') {
							goto l801
						}
						position++
						goto l800
					l801:
						position, tokenIndex = position800, tokenIndex800
						if buffer[position] != rune('W') {
							goto l795
						}
						position++
					}
				l800:
					if buffer[position] != rune('2') {
						goto l795
					}
					position++
					goto l794
				l795:
					position, tokenIndex = position794, tokenIndex794
					{
						position803, tokenIndex803 := position, tokenIndex
						if buffer[position] != rune('v') {
							goto l804
						}
						position++
						goto l803
					l804:
						position, tokenIndex = position803, tokenIndex803
						if buffer[position] != rune('V') {
							goto l802
						}
						position++
					}
				l803:
					{
						position805, tokenIndex805 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l806
						}
						position++
						goto l805
					l806:
						position, tokenIndex = position805, tokenIndex805
						if buffer[position] != rune('L') {
							goto l802
						}
						position++
					}
				l805:
					{
						position807, tokenIndex807 := position, tokenIndex
						if buffer[position] != rune('1') {
							goto l808
						}
						position++
						goto l807
					l808:
						position, tokenIndex = position807, tokenIndex807
						if buffer[position] != rune('2') {
							goto l809
						}
						position++
						goto l807
					l809:
						position, tokenIndex = position807, tokenIndex807
						if buffer[position] != rune('3') {
							goto l810
						}
						position++
						goto l807
					l810:
						position, tokenIndex = position807, tokenIndex807
						if buffer[position] != rune('4') {
							goto l811
						}
						position++
						goto l807
					l811:
						position, tokenIndex = position807, tokenIndex807
						if buffer[position] != rune('5') {
							goto l812
						}
						position++
						goto l807
					l812:
						position, tokenIndex = position807, tokenIndex807
						if buffer[position] != rune('6') {
							goto l813
						}
						position++
						goto l807
					l813:
						position, tokenIndex = position807, tokenIndex807
						if buffer[position] != rune('7') {
							goto l814
						}
						position++
						goto l807
					l814:
						position, tokenIndex = position807, tokenIndex807
						if buffer[position] != rune('8') {
							goto l802
						}
						position++
					}
				l807:
					{
						position815, tokenIndex815 := position, tokenIndex
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l815
						}
						position++
						goto l802
					l815:
						position, tokenIndex = position815, tokenIndex815
					}
					goto l794
				l802:
					position, tokenIndex = position794, tokenIndex794
					{
						position817, tokenIndex817 := position, tokenIndex
						if buffer[position] != rune('v') {
							goto l818
						}
						position++
						goto l817
					l818:
						position, tokenIndex = position817, tokenIndex817
						if buffer[position] != rune('V') {
							goto l816
						}
						position++
					}
				l817:
					{
						position819, tokenIndex819 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l820
						}
						position++
						goto l819
					l820:
						position, tokenIndex = position819, tokenIndex819
						if buffer[position] != rune('L') {
							goto l816
						}
						position++
					}
				l819:
					if buffer[position] != rune('1') {
						goto l816
					}
					position++
					if buffer[position] != rune('6') {
						goto l816
					}
					position++
					goto l794
				l816:
					position, tokenIndex = position794, tokenIndex794
					{
						position822, tokenIndex822 := position, tokenIndex
						if buffer[position] != rune('v') {
							goto l823
						}
						position++
						goto l822
					l823:
						position, tokenIndex = position822, tokenIndex822
						if buffer[position] != rune('V') {
							goto l821
						}
						position++
					}
				l822:
					{
						position824, tokenIndex824 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l825
						}
						position++
						goto l824
					l825:
						position, tokenIndex = position824, tokenIndex824
						if buffer[position] != rune('L') {
							goto l821
						}
						position++
					}
				l824:
					if buffer[position] != rune('3') {
						goto l821
					}
					position++
					if buffer[position] != rune('2') {
						goto l821
					}
					position++
					goto l794
				l821:
					position, tokenIndex = position794, tokenIndex794
					{
						position827, tokenIndex827 := position, tokenIndex
						if buffer[position] != rune('v') {
							goto l828
						}
						position++
						goto l827
					l828:
						position, tokenIndex = position827, tokenIndex827
						if buffer[position] != rune('V') {
							goto l826
						}
						position++
					}
				l827:
					{
						position829, tokenIndex829 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l830
						}
						position++
						goto l829
					l830:
						position, tokenIndex = position829, tokenIndex829
						if buffer[position] != rune('L') {
							goto l826
						}
						position++
					}
				l829:
					if buffer[position] != rune('6') {
						goto l826
					}
					position++
					if buffer[position] != rune('4') {
						goto l826
					}
					position++
					goto l794
				l826:
					position, tokenIndex = position794, tokenIndex794
					{
						position832, tokenIndex832 := position, tokenIndex
						if buffer[position] != rune('v') {
							goto l833
						}
						position++
						goto l832
					l833:
						position, tokenIndex = position832, tokenIndex832
						if buffer[position] != rune('V') {
							goto l831
						}
						position++
					}
				l832:
					{
						position834, tokenIndex834 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l835
						}
						position++
						goto l834
					l835:
						position, tokenIndex = position834, tokenIndex834
						if buffer[position] != rune('L') {
							goto l831
						}
						position++
					}
				l834:
					if buffer[position] != rune('1') {
						goto l831
					}
					position++
					if buffer[position] != rune('2') {
						goto l831
					}
					position++
					if buffer[position] != rune('8') {
						goto l831
					}
					position++
					goto l794
				l831:
					position, tokenIndex = position794, tokenIndex794
					{
						position837, tokenIndex837 := position, tokenIndex
						if buffer[position] != rune('v') {
							goto l838
						}
						position++
						goto l837
					l838:
						position, tokenIndex = position837, tokenIndex837
						if buffer[position] != rune('V') {
							goto l836
						}
						position++
					}
				l837:
					{
						position839, tokenIndex839 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l840
						}
						position++
						goto l839
					l840:
						position, tokenIndex = position839, tokenIndex839
						if buffer[position] != rune('L') {
							goto l836
						}
						position++
					}
				l839:
					if buffer[position] != rune('2') {
						goto l836
					}
					position++
					if buffer[position] != rune('5') {
						goto l836
					}
					position++
					if buffer[position] != rune('6') {
						goto l836
					}
					position++
					goto l794
				l836:
					position, tokenIndex = position794, tokenIndex794
					{
						position842, tokenIndex842 := position, tokenIndex
						if buffer[position] != rune('m') {
							goto l843
						}
						position++
						goto l842
					l843:
						position, tokenIndex = position842, tokenIndex842
						if buffer[position] != rune('M') {
							goto l841
						}
						position++
					}
				l842:
					{
						position844, tokenIndex844 := position, tokenIndex
						if buffer[position] != rune('u') {
							goto l845
						}
						position++
						goto l844
					l845:
						position, tokenIndex = position844, tokenIndex844
						if buffer[position] != rune('U') {
							goto l841
						}
						position++
					}
				l844:
					{
						position846, tokenIndex846 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l847
						}
						position++
						goto l846
					l847:
						position, tokenIndex = position846, tokenIndex846
						if buffer[position] != rune('L') {
							goto l841
						}
						position++
					}
				l846:
					if buffer[position] != rune('3') {
						goto l841
					}
					position++
					goto l794
				l841:
					position, tokenIndex = position794, tokenIndex794
					{
						position849, tokenIndex849 := position, tokenIndex
						if buffer[position] != rune('m') {
							goto l850
						}
						position++
						goto l849
					l850:
						position, tokenIndex = position849, tokenIndex849
						if buffer[position] != rune('M') {
							goto l848
						}
						position++
					}
				l849:
					{
						position851, tokenIndex851 := position, tokenIndex
						if buffer[position] != rune('u') {
							goto l852
						}
						position++
						goto l851
					l852:
						position, tokenIndex = position851, tokenIndex851
						if buffer[position] != rune('U') {
							goto l848
						}
						position++
					}
				l851:
					{
						position853, tokenIndex853 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l854
						}
						position++
						goto l853
					l854:
						position, tokenIndex = position853, tokenIndex853
						if buffer[position] != rune('L') {
							goto l848
						}
						position++
					}
				l853:
					if buffer[position] != rune('4') {
						goto l848
					}
					position++
					goto l794
				l848:
					position, tokenIndex = position794, tokenIndex794
					{
						position855, tokenIndex855 := position, tokenIndex
						if buffer[position] != rune('a') {
							goto l856
						}
						position++
						goto l855
					l856:
						position, tokenIndex = position855, tokenIndex855
						if buffer[position] != rune('A') {
							goto l792
						}
						position++
					}
				l855:
					{
						position857, tokenIndex857 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l858
						}
						position++
						goto l857
					l858:
						position, tokenIndex = position857, tokenIndex857
						if buffer[position] != rune('L') {
							goto l792
						}
						position++
					}
				l857:
					{
						position859, tokenIndex859 := position, tokenIndex
						if buffer[position] != rune('l') {
							goto l860
						}
						position++
						goto l859
					l860:
						position, tokenIndex = position859, tokenIndex859
						if buffer[position] != rune('L') {
							goto l792
						}
						position++
					}
				l859:
				}
			l794:
				{
					position861, tokenIndex861 := position, tokenIndex
					{
						position862, tokenIndex862 := position, tokenIndex
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l863
						}
						position++
						goto l862
					l863:
						position, tokenIndex = position862, tokenIndex862
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l864
						}
						position++
						goto l862
					l864:
						position, tokenIndex = position862, tokenIndex862
						{
							position866, tokenIndex866 := position, tokenIndex
							if c := buffer[position]; c < rune('a') || c > rune('z') {
								goto l867
							}
							position++
							goto l866
						l867:
							position, tokenIndex = position866, tokenIndex866
							if c := buffer[position]; c < rune('A') || c > rune('Z') {
								goto l865
							}
							position++
						}
					l866:
						goto l862
					l865:
						position, tokenIndex = position862, tokenIndex862
						if buffer[position] != rune('_') {
							goto l861
						}
						position++
					}
				l862:
					goto l792
				l861:
					position, tokenIndex = position861, tokenIndex861
				}
				add(ruleSVE2SpecialValue, position793)
			}
			return true
		l792:
			position, tokenIndex = position792, tokenIndex792
			return false
		},
		/* 52 MemoryRef <- <((SymbolRef BaseIndexScale) / SymbolRef / Low12BitsSymbolRef / (Offset* BaseIndexScale) / (SegmentRegister Offset BaseIndexScale) / (SegmentRegister BaseIndexScale) / (SegmentRegister Offset) / ARMBaseIndexScale / BaseIndexScale)> */
		func() bool {
			position868, tokenIndex868 := position, tokenIndex
			{
				position869 := position
				{
					position870, tokenIndex870 := position, tokenIndex
					if !_rules[ruleSymbolRef]() {
						goto l871
					}
					if !_rules[ruleBaseIndexScale]() {
						goto l871
					}
					goto l870
				l871:
					position, tokenIndex = position870, tokenIndex870
					if !_rules[ruleSymbolRef]() {
						goto l872
					}
					goto l870
				l872:
					position, tokenIndex = position870, tokenIndex870
					if !_rules[ruleLow12BitsSymbolRef]() {
						goto l873
					}
					goto l870
				l873:
					position, tokenIndex = position870, tokenIndex870
				l875:
					{
						position876, tokenIndex876 := position, tokenIndex
						if !_rules[ruleOffset]() {
							goto l876
						}
						goto l875
					l876:
						position, tokenIndex = position876, tokenIndex876
					}
					if !_rules[ruleBaseIndexScale]() {
						goto l874
					}
					goto l870
				l874:
					position, tokenIndex = position870, tokenIndex870
					if !_rules[ruleSegmentRegister]() {
						goto l877
					}
					if !_rules[ruleOffset]() {
						goto l877
					}
					if !_rules[ruleBaseIndexScale]() {
						goto l877
					}
					goto l870
				l877:
					position, tokenIndex = position870, tokenIndex870
					if !_rules[ruleSegmentRegister]() {
						goto l878
					}
					if !_rules[ruleBaseIndexScale]() {
						goto l878
					}
					goto l870
				l878:
					position, tokenIndex = position870, tokenIndex870
					if !_rules[ruleSegmentRegister]() {
						goto l879
					}
					if !_rules[ruleOffset]() {
						goto l879
					}
					goto l870
				l879:
					position, tokenIndex = position870, tokenIndex870
					if !_rules[ruleARMBaseIndexScale]() {
						goto l880
					}
					goto l870
				l880:
					position, tokenIndex = position870, tokenIndex870
					if !_rules[ruleBaseIndexScale]() {
						goto l868
					}
				}
			l870:
				add(ruleMemoryRef, position869)
			}
			return true
		l868:
			position, tokenIndex = position868, tokenIndex868
			return false
		},
		/* 53 SymbolRef <- <((Offset* '+')? (LocalSymbol / SymbolName) Offset* ('@' Section Offset*)?)> */
		func() bool {
			position881, tokenIndex881 := position, tokenIndex
			{
				position882 := position
				{
					position883, tokenIndex883 := position, tokenIndex
				l885:
					{
						position886, tokenIndex886 := position, tokenIndex
						if !_rules[ruleOffset]() {
							goto l886
						}
						goto l885
					l886:
						position, tokenIndex = position886, tokenIndex886
					}
					if buffer[position] != rune('+') {
						goto l883
					}
					position++
					goto l884
				l883:
					position, tokenIndex = position883, tokenIndex883
				}
			l884:
				{
					position887, tokenIndex887 := position, tokenIndex
					if !_rules[ruleLocalSymbol]() {
						goto l888
					}
					goto l887
				l888:
					position, tokenIndex = position887, tokenIndex887
					if !_rules[ruleSymbolName]() {
						goto l881
					}
				}
			l887:
			l889:
				{
					position890, tokenIndex890 := position, tokenIndex
					if !_rules[ruleOffset]() {
						goto l890
					}
					goto l889
				l890:
					position, tokenIndex = position890, tokenIndex890
				}
				{
					position891, tokenIndex891 := position, tokenIndex
					if buffer[position] != rune('@') {
						goto l891
					}
					position++
					if !_rules[ruleSection]() {
						goto l891
					}
				l893:
					{
						position894, tokenIndex894 := position, tokenIndex
						if !_rules[ruleOffset]() {
							goto l894
						}
						goto l893
					l894:
						position, tokenIndex = position894, tokenIndex894
					}
					goto l892
				l891:
					position, tokenIndex = position891, tokenIndex891
				}
			l892:
				add(ruleSymbolRef, position882)
			}
			return true
		l881:
			position, tokenIndex = position881, tokenIndex881
			return false
		},
		/* 54 Low12BitsSymbolRef <- <(':' ('l' / 'L') ('o' / 'O') '1' '2' ':' (LocalSymbol / SymbolName) Offset?)> */
		func() bool {
			position895, tokenIndex895 := position, tokenIndex
			{
				position896 := position
				if buffer[position] != rune(':') {
					goto l895
				}
				position++
				{
					position897, tokenIndex897 := position, tokenIndex
					if buffer[position] != rune('l') {
						goto l898
					}
					position++
					goto l897
				l898:
					position, tokenIndex = position897, tokenIndex897
					if buffer[position] != rune('L') {
						goto l895
					}
					position++
				}
			l897:
				{
					position899, tokenIndex899 := position, tokenIndex
					if buffer[position] != rune('o') {
						goto l900
					}
					position++
					goto l899
				l900:
					position, tokenIndex = position899, tokenIndex899
					if buffer[position] != rune('O') {
						goto l895
					}
					position++
				}
			l899:
				if buffer[position] != rune('1') {
					goto l895
				}
				position++
				if buffer[position] != rune('2') {
					goto l895
				}
				position++
				if buffer[position] != rune(':') {
					goto l895
				}
				position++
				{
					position901, tokenIndex901 := position, tokenIndex
					if !_rules[ruleLocalSymbol]() {
						goto l902
					}
					goto l901
				l902:
					position, tokenIndex = position901, tokenIndex901
					if !_rules[ruleSymbolName]() {
						goto l895
					}
				}
			l901:
				{
					position903, tokenIndex903 := position, tokenIndex
					if !_rules[ruleOffset]() {
						goto l903
					}
					goto l904
				l903:
					position, tokenIndex = position903, tokenIndex903
				}
			l904:
				add(ruleLow12BitsSymbolRef, position896)
			}
			return true
		l895:
			position, tokenIndex = position895, tokenIndex895
			return false
		},
		/* 55 ARMBaseIndexScale <- <('[' ARMRegister (',' WS? (('#' Offset (('*' [0-9]+) / ('*' '(' [0-9]+ Operator [0-9]+ ')') / ('+' [0-9]+)*)?) / ARMGOTLow12 / Low12BitsSymbolRef / ARMRegister) (',' WS? ARMConstantTweak)?)? ']' ARMPostincrement?)> */
		func() bool {
			position905, tokenIndex905 := position, tokenIndex
			{
				position906 := position
				if buffer[position] != rune('[') {
					goto l905
				}
				position++
				if !_rules[ruleARMRegister]() {
					goto l905
				}
				{
					position907, tokenIndex907 := position, tokenIndex
					if buffer[position] != rune(',') {
						goto l907
					}
					position++
					{
						position909, tokenIndex909 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l909
						}
						goto l910
					l909:
						position, tokenIndex = position909, tokenIndex909
					}
				l910:
					{
						position911, tokenIndex911 := position, tokenIndex
						if buffer[position] != rune('#') {
							goto l912
						}
						position++
						if !_rules[ruleOffset]() {
							goto l912
						}
						{
							position913, tokenIndex913 := position, tokenIndex
							{
								position915, tokenIndex915 := position, tokenIndex
								if buffer[position] != rune('*') {
									goto l916
								}
								position++
								if c := buffer[position]; c < rune('0') || c > rune('9') {
									goto l916
								}
								position++
							l917:
								{
									position918, tokenIndex918 := position, tokenIndex
									if c := buffer[position]; c < rune('0') || c > rune('9') {
										goto l918
									}
									position++
									goto l917
								l918:
									position, tokenIndex = position918, tokenIndex918
								}
								goto l915
							l916:
								position, tokenIndex = position915, tokenIndex915
								if buffer[position] != rune('*') {
									goto l919
								}
								position++
								if buffer[position] != rune('(') {
									goto l919
								}
								position++
								if c := buffer[position]; c < rune('0') || c > rune('9') {
									goto l919
								}
								position++
							l920:
								{
									position921, tokenIndex921 := position, tokenIndex
									if c := buffer[position]; c < rune('0') || c > rune('9') {
										goto l921
									}
									position++
									goto l920
								l921:
									position, tokenIndex = position921, tokenIndex921
								}
								if !_rules[ruleOperator]() {
									goto l919
								}
								if c := buffer[position]; c < rune('0') || c > rune('9') {
									goto l919
								}
								position++
							l922:
								{
									position923, tokenIndex923 := position, tokenIndex
									if c := buffer[position]; c < rune('0') || c > rune('9') {
										goto l923
									}
									position++
									goto l922
								l923:
									position, tokenIndex = position923, tokenIndex923
								}
								if buffer[position] != rune(')') {
									goto l919
								}
								position++
								goto l915
							l919:
								position, tokenIndex = position915, tokenIndex915
							l924:
								{
									position925, tokenIndex925 := position, tokenIndex
									if buffer[position] != rune('+') {
										goto l925
									}
									position++
									if c := buffer[position]; c < rune('0') || c > rune('9') {
										goto l925
									}
									position++
								l926:
									{
										position927, tokenIndex927 := position, tokenIndex
										if c := buffer[position]; c < rune('0') || c > rune('9') {
											goto l927
										}
										position++
										goto l926
									l927:
										position, tokenIndex = position927, tokenIndex927
									}
									goto l924
								l925:
									position, tokenIndex = position925, tokenIndex925
								}
							}
						l915:
							goto l914

							position, tokenIndex = position913, tokenIndex913
						}
					l914:
						goto l911
					l912:
						position, tokenIndex = position911, tokenIndex911
						if !_rules[ruleARMGOTLow12]() {
							goto l928
						}
						goto l911
					l928:
						position, tokenIndex = position911, tokenIndex911
						if !_rules[ruleLow12BitsSymbolRef]() {
							goto l929
						}
						goto l911
					l929:
						position, tokenIndex = position911, tokenIndex911
						if !_rules[ruleARMRegister]() {
							goto l907
						}
					}
				l911:
					{
						position930, tokenIndex930 := position, tokenIndex
						if buffer[position] != rune(',') {
							goto l930
						}
						position++
						{
							position932, tokenIndex932 := position, tokenIndex
							if !_rules[ruleWS]() {
								goto l932
							}
							goto l933
						l932:
							position, tokenIndex = position932, tokenIndex932
						}
					l933:
						if !_rules[ruleARMConstantTweak]() {
							goto l930
						}
						goto l931
					l930:
						position, tokenIndex = position930, tokenIndex930
					}
				l931:
					goto l908
				l907:
					position, tokenIndex = position907, tokenIndex907
				}
			l908:
				if buffer[position] != rune(']') {
					goto l905
				}
				position++
				{
					position934, tokenIndex934 := position, tokenIndex
					if !_rules[ruleARMPostincrement]() {
						goto l934
					}
					goto l935
				l934:
					position, tokenIndex = position934, tokenIndex934
				}
			l935:
				add(ruleARMBaseIndexScale, position906)
			}
			return true
		l905:
			position, tokenIndex = position905, tokenIndex905
			return false
		},
		/* 56 ARMGOTLow12 <- <(':' ('g' / 'G') ('o' / 'O') ('t' / 'T') '_' ('l' / 'L') ('o' / 'O') '1' '2' ':' SymbolName)> */
		func() bool {
			position936, tokenIndex936 := position, tokenIndex
			{
				position937 := position
				if buffer[position] != rune(':') {
					goto l936
				}
				position++
				{
					position938, tokenIndex938 := position, tokenIndex
					if buffer[position] != rune('g') {
						goto l939
					}
					position++
					goto l938
				l939:
					position, tokenIndex = position938, tokenIndex938
					if buffer[position] != rune('G') {
						goto l936
					}
					position++
				}
			l938:
				{
					position940, tokenIndex940 := position, tokenIndex
					if buffer[position] != rune('o') {
						goto l941
					}
					position++
					goto l940
				l941:
					position, tokenIndex = position940, tokenIndex940
					if buffer[position] != rune('O') {
						goto l936
					}
					position++
				}
			l940:
				{
					position942, tokenIndex942 := position, tokenIndex
					if buffer[position] != rune('t') {
						goto l943
					}
					position++
					goto l942
				l943:
					position, tokenIndex = position942, tokenIndex942
					if buffer[position] != rune('T') {
						goto l936
					}
					position++
				}
			l942:
				if buffer[position] != rune('_') {
					goto l936
				}
				position++
				{
					position944, tokenIndex944 := position, tokenIndex
					if buffer[position] != rune('l') {
						goto l945
					}
					position++
					goto l944
				l945:
					position, tokenIndex = position944, tokenIndex944
					if buffer[position] != rune('L') {
						goto l936
					}
					position++
				}
			l944:
				{
					position946, tokenIndex946 := position, tokenIndex
					if buffer[position] != rune('o') {
						goto l947
					}
					position++
					goto l946
				l947:
					position, tokenIndex = position946, tokenIndex946
					if buffer[position] != rune('O') {
						goto l936
					}
					position++
				}
			l946:
				if buffer[position] != rune('1') {
					goto l936
				}
				position++
				if buffer[position] != rune('2') {
					goto l936
				}
				position++
				if buffer[position] != rune(':') {
					goto l936
				}
				position++
				if !_rules[ruleSymbolName]() {
					goto l936
				}
				add(ruleARMGOTLow12, position937)
			}
			return true
		l936:
			position, tokenIndex = position936, tokenIndex936
			return false
		},
		/* 57 ARMPostincrement <- <'!'> */
		func() bool {
			position948, tokenIndex948 := position, tokenIndex
			{
				position949 := position
				if buffer[position] != rune('!') {
					goto l948
				}
				position++
				add(ruleARMPostincrement, position949)
			}
			return true
		l948:
			position, tokenIndex = position948, tokenIndex948
			return false
		},
		/* 58 BaseIndexScale <- <('(' RegisterOrConstant? WS? (',' WS? RegisterOrConstant WS? (',' [0-9]+)?)? ')')> */
		func() bool {
			position950, tokenIndex950 := position, tokenIndex
			{
				position951 := position
				if buffer[position] != rune('(') {
					goto l950
				}
				position++
				{
					position952, tokenIndex952 := position, tokenIndex
					if !_rules[ruleRegisterOrConstant]() {
						goto l952
					}
					goto l953
				l952:
					position, tokenIndex = position952, tokenIndex952
				}
			l953:
				{
					position954, tokenIndex954 := position, tokenIndex
					if !_rules[ruleWS]() {
						goto l954
					}
					goto l955
				l954:
					position, tokenIndex = position954, tokenIndex954
				}
			l955:
				{
					position956, tokenIndex956 := position, tokenIndex
					if buffer[position] != rune(',') {
						goto l956
					}
					position++
					{
						position958, tokenIndex958 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l958
						}
						goto l959
					l958:
						position, tokenIndex = position958, tokenIndex958
					}
				l959:
					if !_rules[ruleRegisterOrConstant]() {
						goto l956
					}
					{
						position960, tokenIndex960 := position, tokenIndex
						if !_rules[ruleWS]() {
							goto l960
						}
						goto l961
					l960:
						position, tokenIndex = position960, tokenIndex960
					}
				l961:
					{
						position962, tokenIndex962 := position, tokenIndex
						if buffer[position] != rune(',') {
							goto l962
						}
						position++
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l962
						}
						position++
					l964:
						{
							position965, tokenIndex965 := position, tokenIndex
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l965
							}
							position++
							goto l964
						l965:
							position, tokenIndex = position965, tokenIndex965
						}
						goto l963
					l962:
						position, tokenIndex = position962, tokenIndex962
					}
				l963:
					goto l957
				l956:
					position, tokenIndex = position956, tokenIndex956
				}
			l957:
				if buffer[position] != rune(')') {
					goto l950
				}
				position++
				add(ruleBaseIndexScale, position951)
			}
			return true
		l950:
			position, tokenIndex = position950, tokenIndex950
			return false
		},
		/* 59 Operator <- <('+' / '-')> */
		func() bool {
			position966, tokenIndex966 := position, tokenIndex
			{
				position967 := position
				{
					position968, tokenIndex968 := position, tokenIndex
					if buffer[position] != rune('+') {
						goto l969
					}
					position++
					goto l968
				l969:
					position, tokenIndex = position968, tokenIndex968
					if buffer[position] != rune('-') {
						goto l966
					}
					position++
				}
			l968:
				add(ruleOperator, position967)
			}
			return true
		l966:
			position, tokenIndex = position966, tokenIndex966
			return false
		},
		/* 60 Offset <- <('+'? '-'? (('0' ('b' / 'B') ('0' / '1')+) / ('0' ('x' / 'X') ([0-9] / [0-9] / ([a-f] / [A-F]))+) / [0-9]+))> */
		func() bool {
			position970, tokenIndex970 := position, tokenIndex
			{
				position971 := position
				{
					position972, tokenIndex972 := position, tokenIndex
					if buffer[position] != rune('+') {
						goto l972
					}
					position++
					goto l973
				l972:
					position, tokenIndex = position972, tokenIndex972
				}
			l973:
				{
					position974, tokenIndex974 := position, tokenIndex
					if buffer[position] != rune('-') {
						goto l974
					}
					position++
					goto l975
				l974:
					position, tokenIndex = position974, tokenIndex974
				}
			l975:
				{
					position976, tokenIndex976 := position, tokenIndex
					if buffer[position] != rune('0') {
						goto l977
					}
					position++
					{
						position978, tokenIndex978 := position, tokenIndex
						if buffer[position] != rune('b') {
							goto l979
						}
						position++
						goto l978
					l979:
						position, tokenIndex = position978, tokenIndex978
						if buffer[position] != rune('B') {
							goto l977
						}
						position++
					}
				l978:
					{
						position982, tokenIndex982 := position, tokenIndex
						if buffer[position] != rune('0') {
							goto l983
						}
						position++
						goto l982
					l983:
						position, tokenIndex = position982, tokenIndex982
						if buffer[position] != rune('1') {
							goto l977
						}
						position++
					}
				l982:
				l980:
					{
						position981, tokenIndex981 := position, tokenIndex
						{
							position984, tokenIndex984 := position, tokenIndex
							if buffer[position] != rune('0') {
								goto l985
							}
							position++
							goto l984
						l985:
							position, tokenIndex = position984, tokenIndex984
							if buffer[position] != rune('1') {
								goto l981
							}
							position++
						}
					l984:
						goto l980
					l981:
						position, tokenIndex = position981, tokenIndex981
					}
					goto l976
				l977:
					position, tokenIndex = position976, tokenIndex976
					if buffer[position] != rune('0') {
						goto l986
					}
					position++
					{
						position987, tokenIndex987 := position, tokenIndex
						if buffer[position] != rune('x') {
							goto l988
						}
						position++
						goto l987
					l988:
						position, tokenIndex = position987, tokenIndex987
						if buffer[position] != rune('X') {
							goto l986
						}
						position++
					}
				l987:
					{
						position991, tokenIndex991 := position, tokenIndex
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l992
						}
						position++
						goto l991
					l992:
						position, tokenIndex = position991, tokenIndex991
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l993
						}
						position++
						goto l991
					l993:
						position, tokenIndex = position991, tokenIndex991
						{
							position994, tokenIndex994 := position, tokenIndex
							if c := buffer[position]; c < rune('a') || c > rune('f') {
								goto l995
							}
							position++
							goto l994
						l995:
							position, tokenIndex = position994, tokenIndex994
							if c := buffer[position]; c < rune('A') || c > rune('F') {
								goto l986
							}
							position++
						}
					l994:
					}
				l991:
				l989:
					{
						position990, tokenIndex990 := position, tokenIndex
						{
							position996, tokenIndex996 := position, tokenIndex
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l997
							}
							position++
							goto l996
						l997:
							position, tokenIndex = position996, tokenIndex996
							if c := buffer[position]; c < rune('0') || c > rune('9') {
								goto l998
							}
							position++
							goto l996
						l998:
							position, tokenIndex = position996, tokenIndex996
							{
								position999, tokenIndex999 := position, tokenIndex
								if c := buffer[position]; c < rune('a') || c > rune('f') {
									goto l1000
								}
								position++
								goto l999
							l1000:
								position, tokenIndex = position999, tokenIndex999
								if c := buffer[position]; c < rune('A') || c > rune('F') {
									goto l990
								}
								position++
							}
						l999:
						}
					l996:
						goto l989
					l990:
						position, tokenIndex = position990, tokenIndex990
					}
					goto l976
				l986:
					position, tokenIndex = position976, tokenIndex976
					if c := buffer[position]; c < rune('0') || c > rune('9') {
						goto l970
					}
					position++
				l1001:
					{
						position1002, tokenIndex1002 := position, tokenIndex
						if c := buffer[position]; c < rune('0') || c > rune('9') {
							goto l1002
						}
						position++
						goto l1001
					l1002:
						position, tokenIndex = position1002, tokenIndex1002
					}
				}
			l976:
				add(ruleOffset, position971)
			}
			return true
		l970:
			position, tokenIndex = position970, tokenIndex970
			return false
		},
		/* 61 Section <- <([a-z] / [A-Z] / '@')+> */
		func() bool {
			position1003, tokenIndex1003 := position, tokenIndex
			{
				position1004 := position
				{
					position1007, tokenIndex1007 := position, tokenIndex
					if c := buffer[position]; c < rune('a') || c > rune('z') {
						goto l1008
					}
					position++
					goto l1007
				l1008:
					position, tokenIndex = position1007, tokenIndex1007
					if c := buffer[position]; c < rune('A') || c > rune('Z') {
						goto l1009
					}
					position++
					goto l1007
				l1009:
					position, tokenIndex = position1007, tokenIndex1007
					if buffer[position] != rune('@') {
						goto l1003
					}
					position++
				}
			l1007:
			l1005:
				{
					position1006, tokenIndex1006 := position, tokenIndex
					{
						position1010, tokenIndex1010 := position, tokenIndex
						if c := buffer[position]; c < rune('a') || c > rune('z') {
							goto l1011
						}
						position++
						goto l1010
					l1011:
						position, tokenIndex = position1010, tokenIndex1010
						if c := buffer[position]; c < rune('A') || c > rune('Z') {
							goto l1012
						}
						position++
						goto l1010
					l1012:
						position, tokenIndex = position1010, tokenIndex1010
						if buffer[position] != rune('@') {
							goto l1006
						}
						position++
					}
				l1010:
					goto l1005
				l1006:
					position, tokenIndex = position1006, tokenIndex1006
				}
				add(ruleSection, position1004)
			}
			return true
		l1003:
			position, tokenIndex = position1003, tokenIndex1003
			return false
		},
		/* 62 SegmentRegister <- <('%' ([c-g] / 's') ('s' ':'))> */
		func() bool {
			position1013, tokenIndex1013 := position, tokenIndex
			{
				position1014 := position
				if buffer[position] != rune('%') {
					goto l1013
				}
				position++
				{
					position1015, tokenIndex1015 := position, tokenIndex
					if c := buffer[position]; c < rune('c') || c > rune('g') {
						goto l1016
					}
					position++
					goto l1015
				l1016:
					position, tokenIndex = position1015, tokenIndex1015
					if buffer[position] != rune('s') {
						goto l1013
					}
					position++
				}
			l1015:
				if buffer[position] != rune('s') {
					goto l1013
				}
				position++
				if buffer[position] != rune(':') {
					goto l1013
				}
				position++
				add(ruleSegmentRegister, position1014)
			}
			return true
		l1013:
			position, tokenIndex = position1013, tokenIndex1013
			return false
		},
	}
	p.rules = _rules
	return nil
}
