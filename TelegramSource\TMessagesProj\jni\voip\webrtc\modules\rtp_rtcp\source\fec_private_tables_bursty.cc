/*
 *  Copyright (c) 2018 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#include "modules/rtp_rtcp/source/fec_private_tables_bursty.h"

namespace {
// clang-format off
#define kMaskBursty1_1 \
  0x80, 0x00

#define kMaskBursty2_1 \
  0xc0, 0x00

#define kMaskBursty2_2 \
  0x80, 0x00, \
  0xc0, 0x00

#define kMaskBursty3_1 \
  0xe0, 0x00

#define kMaskBursty3_2 \
  0xc0, 0x00, \
  0xa0, 0x00

#define kMaskBursty3_3 \
  0x80, 0x00, \
  0xc0, 0x00, \
  0x60, 0x00

#define kMaskBursty4_1 \
  0xf0, 0x00

#define kMaskBursty4_2 \
  0xa0, 0x00, \
  0xd0, 0x00

#define kMaskBursty4_3 \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x90, 0x00

#define kMaskBursty4_4 \
  0x80, 0x00, \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00

#define kMaskBursty5_1 \
  0xf8, 0x00

#define kMaskBursty5_2 \
  0xd0, 0x00, \
  0xa8, 0x00

#define kMaskBursty5_3 \
  0x70, 0x00, \
  0x90, 0x00, \
  0xc8, 0x00

#define kMaskBursty5_4 \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00, \
  0x88, 0x00

#define kMaskBursty5_5 \
  0x80, 0x00, \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00

#define kMaskBursty6_1 \
  0xfc, 0x00

#define kMaskBursty6_2 \
  0xa8, 0x00, \
  0xd4, 0x00

#define kMaskBursty6_3 \
  0x94, 0x00, \
  0xc8, 0x00, \
  0x64, 0x00

#define kMaskBursty6_4 \
  0x60, 0x00, \
  0x38, 0x00, \
  0x88, 0x00, \
  0xc4, 0x00

#define kMaskBursty6_5 \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x84, 0x00

#define kMaskBursty6_6 \
  0x80, 0x00, \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00

#define kMaskBursty7_1 \
  0xfe, 0x00

#define kMaskBursty7_2 \
  0xd4, 0x00, \
  0xaa, 0x00

#define kMaskBursty7_3 \
  0xc8, 0x00, \
  0x74, 0x00, \
  0x92, 0x00

#define kMaskBursty7_4 \
  0x38, 0x00, \
  0x8a, 0x00, \
  0xc4, 0x00, \
  0x62, 0x00

#define kMaskBursty7_5 \
  0x60, 0x00, \
  0x30, 0x00, \
  0x1c, 0x00, \
  0x84, 0x00, \
  0xc2, 0x00

#define kMaskBursty7_6 \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x82, 0x00

#define kMaskBursty7_7 \
  0x80, 0x00, \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00

#define kMaskBursty8_1 \
  0xff, 0x00

#define kMaskBursty8_2 \
  0xaa, 0x00, \
  0xd5, 0x00

#define kMaskBursty8_3 \
  0x74, 0x00, \
  0x92, 0x00, \
  0xc9, 0x00

#define kMaskBursty8_4 \
  0x8a, 0x00, \
  0xc5, 0x00, \
  0x62, 0x00, \
  0x31, 0x00

#define kMaskBursty8_5 \
  0x30, 0x00, \
  0x1c, 0x00, \
  0x85, 0x00, \
  0xc2, 0x00, \
  0x61, 0x00

#define kMaskBursty8_6 \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0e, 0x00, \
  0x82, 0x00, \
  0xc1, 0x00

#define kMaskBursty8_7 \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00, \
  0x81, 0x00

#define kMaskBursty8_8 \
  0x80, 0x00, \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00, \
  0x03, 0x00

#define kMaskBursty9_1 \
  0xff, 0x80

#define kMaskBursty9_2 \
  0xd5, 0x00, \
  0xaa, 0x80

#define kMaskBursty9_3 \
  0x92, 0x00, \
  0xc9, 0x00, \
  0x74, 0x80

#define kMaskBursty9_4 \
  0xc5, 0x00, \
  0x62, 0x00, \
  0x39, 0x00, \
  0x8a, 0x80

#define kMaskBursty9_5 \
  0x1c, 0x00, \
  0x85, 0x00, \
  0xc2, 0x80, \
  0x61, 0x00, \
  0x30, 0x80

#define kMaskBursty9_6 \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0e, 0x00, \
  0x82, 0x80, \
  0xc1, 0x00, \
  0x60, 0x80

#define kMaskBursty9_7 \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x07, 0x00, \
  0x81, 0x00, \
  0xc0, 0x80

#define kMaskBursty9_8 \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00, \
  0x03, 0x00, \
  0x80, 0x80

#define kMaskBursty9_9 \
  0x80, 0x00, \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00, \
  0x03, 0x00, \
  0x01, 0x80

#define kMaskBursty10_1 \
  0xff, 0xc0

#define kMaskBursty10_2 \
  0xaa, 0x80, \
  0xd5, 0x40

#define kMaskBursty10_3 \
  0xc9, 0x00, \
  0x74, 0x80, \
  0x92, 0x40

#define kMaskBursty10_4 \
  0x62, 0x00, \
  0x39, 0x00, \
  0x8a, 0x80, \
  0xc5, 0x40

#define kMaskBursty10_5 \
  0x85, 0x00, \
  0xc2, 0x80, \
  0x61, 0x40, \
  0x30, 0x80, \
  0x18, 0x40

#define kMaskBursty10_6 \
  0x18, 0x00, \
  0x0e, 0x00, \
  0x82, 0x80, \
  0xc1, 0x40, \
  0x60, 0x80, \
  0x30, 0x40

#define kMaskBursty10_7 \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x07, 0x00, \
  0x81, 0x40, \
  0xc0, 0x80, \
  0x60, 0x40

#define kMaskBursty10_8 \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00, \
  0x03, 0x00, \
  0x80, 0x80, \
  0xc0, 0x40

#define kMaskBursty10_9 \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00, \
  0x03, 0x00, \
  0x01, 0x80, \
  0x80, 0x40

#define kMaskBursty10_10 \
  0x80, 0x00, \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00, \
  0x03, 0x00, \
  0x01, 0x80, \
  0x00, 0xc0

#define kMaskBursty11_1 \
  0xff, 0xe0

#define kMaskBursty11_2 \
  0xd5, 0x40, \
  0xaa, 0xa0

#define kMaskBursty11_3 \
  0x74, 0x80, \
  0x92, 0x40, \
  0xc9, 0x20

#define kMaskBursty11_4 \
  0x39, 0x00, \
  0x8a, 0x80, \
  0xc5, 0x40, \
  0x62, 0x20

#define kMaskBursty11_5 \
  0xc2, 0xc0, \
  0x61, 0x00, \
  0x30, 0xa0, \
  0x1c, 0x40, \
  0x85, 0x20

#define kMaskBursty11_6 \
  0x0e, 0x00, \
  0x82, 0x80, \
  0xc1, 0x40, \
  0x60, 0xa0, \
  0x30, 0x40, \
  0x18, 0x20

#define kMaskBursty11_7 \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x07, 0x00, \
  0x81, 0x40, \
  0xc0, 0xa0, \
  0x60, 0x40, \
  0x30, 0x20

#define kMaskBursty11_8 \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00, \
  0x03, 0x40, \
  0x80, 0xa0, \
  0xc0, 0x40, \
  0x60, 0x20

#define kMaskBursty11_9 \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00, \
  0x03, 0x00, \
  0x01, 0x80, \
  0x80, 0x40, \
  0xc0, 0x20

#define kMaskBursty11_10 \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00, \
  0x03, 0x00, \
  0x01, 0x80, \
  0x00, 0xc0, \
  0x80, 0x20

#define kMaskBursty11_11 \
  0x80, 0x00, \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00, \
  0x03, 0x00, \
  0x01, 0x80, \
  0x00, 0xc0, \
  0x00, 0x60

#define kMaskBursty12_1 \
  0xff, 0xf0

#define kMaskBursty12_2 \
  0xaa, 0xa0, \
  0xd5, 0x50

#define kMaskBursty12_3 \
  0x92, 0x40, \
  0xc9, 0x20, \
  0x74, 0x90

#define kMaskBursty12_4 \
  0x8a, 0x80, \
  0xc5, 0x40, \
  0x62, 0x20, \
  0x39, 0x10

#define kMaskBursty12_5 \
  0x61, 0x00, \
  0x30, 0xa0, \
  0x1c, 0x50, \
  0x85, 0x20, \
  0xc2, 0x90

#define kMaskBursty12_6 \
  0x82, 0x90, \
  0xc1, 0x40, \
  0x60, 0xa0, \
  0x30, 0x50, \
  0x18, 0x20, \
  0x0c, 0x10

#define kMaskBursty12_7 \
  0x0c, 0x00, \
  0x07, 0x00, \
  0x81, 0x40, \
  0xc0, 0xa0, \
  0x60, 0x50, \
  0x30, 0x20, \
  0x18, 0x10

#define kMaskBursty12_8 \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00, \
  0x03, 0x00, \
  0x80, 0xa0, \
  0xc0, 0x50, \
  0x60, 0x20, \
  0x30, 0x10

#define kMaskBursty12_9 \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00, \
  0x03, 0x00, \
  0x01, 0x80, \
  0x80, 0x50, \
  0xc0, 0x20, \
  0x60, 0x10

#define kMaskBursty12_10 \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00, \
  0x03, 0x00, \
  0x01, 0x80, \
  0x00, 0xc0, \
  0x80, 0x20, \
  0xc0, 0x10

#define kMaskBursty12_11 \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00, \
  0x03, 0x00, \
  0x01, 0x80, \
  0x00, 0xc0, \
  0x00, 0x60, \
  0x80, 0x10

#define kMaskBursty12_12 \
  0x80, 0x00, \
  0xc0, 0x00, \
  0x60, 0x00, \
  0x30, 0x00, \
  0x18, 0x00, \
  0x0c, 0x00, \
  0x06, 0x00, \
  0x03, 0x00, \
  0x01, 0x80, \
  0x00, 0xc0, \
  0x00, 0x60, \
  0x00, 0x30

#define kPacketMaskBursty1 1, \
  kMaskBursty1_1

#define kPacketMaskBursty2 2, \
  kMaskBursty2_1, \
  kMaskBursty2_2

#define kPacketMaskBursty3 3, \
  kMaskBursty3_1, \
  kMaskBursty3_2, \
  kMaskBursty3_3

#define kPacketMaskBursty4 4, \
  kMaskBursty4_1, \
  kMaskBursty4_2, \
  kMaskBursty4_3, \
  kMaskBursty4_4

#define kPacketMaskBursty5 5, \
  kMaskBursty5_1, \
  kMaskBursty5_2, \
  kMaskBursty5_3, \
  kMaskBursty5_4, \
  kMaskBursty5_5

#define kPacketMaskBursty6 6, \
  kMaskBursty6_1, \
  kMaskBursty6_2, \
  kMaskBursty6_3, \
  kMaskBursty6_4, \
  kMaskBursty6_5, \
  kMaskBursty6_6

#define kPacketMaskBursty7 7, \
  kMaskBursty7_1, \
  kMaskBursty7_2, \
  kMaskBursty7_3, \
  kMaskBursty7_4, \
  kMaskBursty7_5, \
  kMaskBursty7_6, \
  kMaskBursty7_7

#define kPacketMaskBursty8 8, \
  kMaskBursty8_1, \
  kMaskBursty8_2, \
  kMaskBursty8_3, \
  kMaskBursty8_4, \
  kMaskBursty8_5, \
  kMaskBursty8_6, \
  kMaskBursty8_7, \
  kMaskBursty8_8

#define kPacketMaskBursty9 9, \
  kMaskBursty9_1, \
  kMaskBursty9_2, \
  kMaskBursty9_3, \
  kMaskBursty9_4, \
  kMaskBursty9_5, \
  kMaskBursty9_6, \
  kMaskBursty9_7, \
  kMaskBursty9_8, \
  kMaskBursty9_9

#define kPacketMaskBursty10 10, \
  kMaskBursty10_1, \
  kMaskBursty10_2, \
  kMaskBursty10_3, \
  kMaskBursty10_4, \
  kMaskBursty10_5, \
  kMaskBursty10_6, \
  kMaskBursty10_7, \
  kMaskBursty10_8, \
  kMaskBursty10_9, \
  kMaskBursty10_10

#define kPacketMaskBursty11 11, \
  kMaskBursty11_1, \
  kMaskBursty11_2, \
  kMaskBursty11_3, \
  kMaskBursty11_4, \
  kMaskBursty11_5, \
  kMaskBursty11_6, \
  kMaskBursty11_7, \
  kMaskBursty11_8, \
  kMaskBursty11_9, \
  kMaskBursty11_10, \
  kMaskBursty11_11

#define kPacketMaskBursty12 12, \
  kMaskBursty12_1, \
  kMaskBursty12_2, \
  kMaskBursty12_3, \
  kMaskBursty12_4, \
  kMaskBursty12_5, \
  kMaskBursty12_6, \
  kMaskBursty12_7, \
  kMaskBursty12_8, \
  kMaskBursty12_9, \
  kMaskBursty12_10, \
  kMaskBursty12_11, \
  kMaskBursty12_12

// clang-format on
}  // namespace

namespace webrtc {
namespace fec_private_tables {

const uint8_t kPacketMaskBurstyTbl[] = {
    12,
    kPacketMaskBursty1,
    kPacketMaskBursty2,
    kPacketMaskBursty3,
    kPacketMaskBursty4,
    kPacketMaskBursty5,
    kPacketMaskBursty6,
    kPacketMaskBursty7,
    kPacketMaskBursty8,
    kPacketMaskBursty9,
    kPacketMaskBursty10,
    kPacketMaskBursty11,
    kPacketMaskBursty12,
};

}  // namespace fec_private_tables
}  // namespace webrtc
