/*
 * Copyright (C) 2016 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.google.android.exoplayer2.text.webvtt;

import com.google.android.exoplayer2.text.Cue;

/** A representation of a WebVTT cue. */
public final class WebvttCueInfo {

  public final Cue cue;
  public final long startTimeUs;
  public final long endTimeUs;

  public WebvttCueInfo(Cue cue, long startTimeUs, long endTimeUs) {
    this.cue = cue;
    this.startTimeUs = startTimeUs;
    this.endTimeUs = endTimeUs;
  }
}
