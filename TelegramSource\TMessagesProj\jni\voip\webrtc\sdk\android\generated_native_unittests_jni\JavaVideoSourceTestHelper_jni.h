// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/JavaVideoSourceTestHelper

#ifndef org_webrtc_JavaVideoSourceTestHelper_JNI
#define org_webrtc_JavaVideoSourceTestHelper_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_JavaVideoSourceTestHelper[];
const char kClassPath_org_webrtc_JavaVideoSourceTestHelper[] =
    "org/webrtc/JavaVideoSourceTestHelper";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_JavaVideoSourceTestHelper_clazz(nullptr);
#ifndef org_webrtc_JavaVideoSourceTestHelper_clazz_defined
#define org_webrtc_JavaVideoSourceTestHelper_clazz_defined
inline jclass org_webrtc_JavaVideoSourceTestHelper_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_JavaVideoSourceTestHelper,
      &g_org_webrtc_JavaVideoSourceTestHelper_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_JavaVideoSourceTestHelper_deliverFrame5(nullptr);
static void Java_JavaVideoSourceTestHelper_deliverFrame(JNIEnv* env, JniIntWrapper width,
    JniIntWrapper height,
    JniIntWrapper rotation,
    jlong timestampNs,
    const jni_zero::JavaRef<jobject>& observer) {
  jclass clazz = org_webrtc_JavaVideoSourceTestHelper_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_JavaVideoSourceTestHelper_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "deliverFrame",
          "(IIIJLorg/webrtc/CapturerObserver;)V",
          &g_org_webrtc_JavaVideoSourceTestHelper_deliverFrame5);

     env->CallStaticVoidMethod(clazz,
          call_context.base.method_id, as_jint(width), as_jint(height), as_jint(rotation),
              timestampNs, observer.obj());
}

static std::atomic<jmethodID> g_org_webrtc_JavaVideoSourceTestHelper_startCapture2(nullptr);
static void Java_JavaVideoSourceTestHelper_startCapture(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& observer,
    jboolean success) {
  jclass clazz = org_webrtc_JavaVideoSourceTestHelper_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_JavaVideoSourceTestHelper_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "startCapture",
          "(Lorg/webrtc/CapturerObserver;Z)V",
          &g_org_webrtc_JavaVideoSourceTestHelper_startCapture2);

     env->CallStaticVoidMethod(clazz,
          call_context.base.method_id, observer.obj(), success);
}

static std::atomic<jmethodID> g_org_webrtc_JavaVideoSourceTestHelper_stopCapture1(nullptr);
static void Java_JavaVideoSourceTestHelper_stopCapture(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& observer) {
  jclass clazz = org_webrtc_JavaVideoSourceTestHelper_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_JavaVideoSourceTestHelper_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "stopCapture",
          "(Lorg/webrtc/CapturerObserver;)V",
          &g_org_webrtc_JavaVideoSourceTestHelper_stopCapture1);

     env->CallStaticVoidMethod(clazz,
          call_context.base.method_id, observer.obj());
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_JavaVideoSourceTestHelper_JNI
