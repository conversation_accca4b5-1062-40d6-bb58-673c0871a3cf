name: "Project Wycheproof"
description:
  "Project Wycheproof tests crypto libraries against known attacks. "
  "This is a copy of only the test vectors, adapted for BoringSSL."

third_party {
  url {
    type: GIT
    value: "https://github.com/google/wycheproof"
  }
  version: "eb35c25e05d17dc05fdf42f40ff4cbb3a959c096"
  last_upgrade_date { year: 2019 month: 12 day: 13 }

  local_modifications:
    "Only the testvectors directory checked in. txt files "
    "are generated by convert_wycheproof.go script in util."
}
