# [p_test_configured]
module = p_test.dylib
activate = 1
greeting = Hello OpenSSL, greetings from Test Provider

# Comment out the next line to ignore configuration errors
config_diagnostics = 1


openssl_conf = openssl_init

[openssl_init]
providers = providers

[providers]
p_test_configured = p_test_configured

[p_test_configured]
module = p_test.dylib
activate = 1
greeting = Hello OpenSSL, greetings from Test Provider
