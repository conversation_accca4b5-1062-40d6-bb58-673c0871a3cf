// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     java/util/LinkedHashMap

#ifndef java_util_LinkedHashMap_JNI
#define java_util_LinkedHashMap_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_java_util_LinkedHashMap[];
const char kClassPath_java_util_LinkedHashMap[] = "java/util/LinkedHashMap";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_java_util_LinkedHashMap_clazz(nullptr);
#ifndef java_util_LinkedHashMap_clazz_defined
#define java_util_LinkedHashMap_clazz_defined
inline jclass java_util_LinkedHashMap_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_java_util_LinkedHashMap,
      &g_java_util_LinkedHashMap_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace JNI_LinkedHashMap {


static std::atomic<jmethodID> g_java_util_LinkedHashMap_Constructor0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_Constructor(JNIEnv*
    env);
static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_Constructor(JNIEnv* env) {
  jclass clazz = java_util_LinkedHashMap_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_LinkedHashMap_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "()V",
          &g_java_util_LinkedHashMap_Constructor0);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_LinkedHashMap_Constructor__Map1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_LinkedHashMap_Constructor__Map(JNIEnv* env, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_Constructor__Map(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_LinkedHashMap_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_LinkedHashMap_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Ljava/util/Map;)V",
          &g_java_util_LinkedHashMap_Constructor__Map1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_LinkedHashMap_Constructor__int1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_LinkedHashMap_Constructor__int(JNIEnv* env, JniIntWrapper p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_Constructor__int(JNIEnv* env,
    JniIntWrapper p0) {
  jclass clazz = java_util_LinkedHashMap_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_LinkedHashMap_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(I)V",
          &g_java_util_LinkedHashMap_Constructor__int1);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, as_jint(p0));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_LinkedHashMap_Constructor2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_Constructor(JNIEnv*
    env, JniIntWrapper p0,
    jfloat p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_Constructor(JNIEnv* env,
    JniIntWrapper p0,
    jfloat p1) {
  jclass clazz = java_util_LinkedHashMap_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_LinkedHashMap_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(IF)V",
          &g_java_util_LinkedHashMap_Constructor2);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, as_jint(p0), p1);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_LinkedHashMap_Constructor3(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_Constructor(JNIEnv*
    env, JniIntWrapper p0,
    jfloat p1,
    jboolean p2);
static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_Constructor(JNIEnv* env,
    JniIntWrapper p0,
    jfloat p1,
    jboolean p2) {
  jclass clazz = java_util_LinkedHashMap_clazz(env);
  CHECK_CLAZZ(env, clazz,
      java_util_LinkedHashMap_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(IFZ)V",
          &g_java_util_LinkedHashMap_Constructor3);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, as_jint(p0), p1, p2);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_LinkedHashMap_clear0(nullptr);
[[maybe_unused]] static void Java_LinkedHashMap_clear(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj);
static void Java_LinkedHashMap_clear(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_LinkedHashMap_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_LinkedHashMap_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "clear",
          "()V",
          &g_java_util_LinkedHashMap_clear0);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id);
}

static std::atomic<jmethodID> g_java_util_LinkedHashMap_containsValue1(nullptr);
[[maybe_unused]] static jboolean Java_LinkedHashMap_containsValue(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_LinkedHashMap_containsValue(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_LinkedHashMap_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_LinkedHashMap_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "containsValue",
          "(Ljava/lang/Object;)Z",
          &g_java_util_LinkedHashMap_containsValue1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_LinkedHashMap_entrySet0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_entrySet(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_entrySet(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_LinkedHashMap_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_LinkedHashMap_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "entrySet",
          "()Ljava/util/Set;",
          &g_java_util_LinkedHashMap_entrySet0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_LinkedHashMap_forEach1(nullptr);
[[maybe_unused]] static void Java_LinkedHashMap_forEach(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static void Java_LinkedHashMap_forEach(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_LinkedHashMap_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_LinkedHashMap_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "forEach",
          "(Ljava/util/function/BiConsumer;)V",
          &g_java_util_LinkedHashMap_forEach1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
}

static std::atomic<jmethodID> g_java_util_LinkedHashMap_get1(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_get(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_get(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_LinkedHashMap_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_LinkedHashMap_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "get",
          "(Ljava/lang/Object;)Ljava/lang/Object;",
          &g_java_util_LinkedHashMap_get1);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_LinkedHashMap_getOrDefault2(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject>
    Java_LinkedHashMap_getOrDefault(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1);
static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_getOrDefault(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  jclass clazz = java_util_LinkedHashMap_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_LinkedHashMap_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getOrDefault",
          "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/lang/Object;",
          &g_java_util_LinkedHashMap_getOrDefault2);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id, p0.obj(), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_LinkedHashMap_keySet0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_keySet(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_keySet(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_LinkedHashMap_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_LinkedHashMap_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "keySet",
          "()Ljava/util/Set;",
          &g_java_util_LinkedHashMap_keySet0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_java_util_LinkedHashMap_removeEldestEntry1(nullptr);
[[maybe_unused]] static jboolean Java_LinkedHashMap_removeEldestEntry(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static jboolean Java_LinkedHashMap_removeEldestEntry(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj, const jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_LinkedHashMap_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_LinkedHashMap_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "removeEldestEntry",
          "(Ljava/util/Map$Entry;)Z",
          &g_java_util_LinkedHashMap_removeEldestEntry1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
  return ret;
}

static std::atomic<jmethodID> g_java_util_LinkedHashMap_replaceAll1(nullptr);
[[maybe_unused]] static void Java_LinkedHashMap_replaceAll(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj, const jni_zero::JavaRef<jobject>& p0);
static void Java_LinkedHashMap_replaceAll(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj, const
    jni_zero::JavaRef<jobject>& p0) {
  jclass clazz = java_util_LinkedHashMap_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_LinkedHashMap_clazz(env));

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "replaceAll",
          "(Ljava/util/function/BiFunction;)V",
          &g_java_util_LinkedHashMap_replaceAll1);

     env->CallVoidMethod(obj.obj(),
          call_context.base.method_id, p0.obj());
}

static std::atomic<jmethodID> g_java_util_LinkedHashMap_values0(nullptr);
[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_values(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj);
static jni_zero::ScopedJavaLocalRef<jobject> Java_LinkedHashMap_values(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = java_util_LinkedHashMap_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      java_util_LinkedHashMap_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "values",
          "()Ljava/util/Collection;",
          &g_java_util_LinkedHashMap_values0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

}  // namespace JNI_LinkedHashMap

#endif  // java_util_LinkedHashMap_JNI
