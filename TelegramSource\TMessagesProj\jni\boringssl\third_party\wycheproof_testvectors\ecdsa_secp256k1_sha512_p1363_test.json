{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 281, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of IEEE P1363 encoded ECDSA signatures."], "notes": {"EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission.", "SigSize": "The size of the signature should always be twice the number of bytes of the size of the order. But some libraries accept signatures with less bytes."}, "schema": "ecdsa_p1363_verify_schema.json", "testGroups": [{"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "uDj_ROW8F3vyEYnQdmCC_J2EMiaIf8l2A3EQC37iCm8", "y": "8MnXW_unsxpryhl0SW7rVt41cHGVXYPEsbraoLIYMuk"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b838ff44e5bc177bf21189d0766082fc9d843226887fc9760371100b7ee20a6ff0c9d75bfba7b31a6bca1974496eeb56de357071955d83c4b1badaa0b21832e9", "wx": "00b838ff44e5bc177bf21189d0766082fc9d843226887fc9760371100b7ee20a6f", "wy": "00f0c9d75bfba7b31a6bca1974496eeb56de357071955d83c4b1badaa0b21832e9"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004b838ff44e5bc177bf21189d0766082fc9d843226887fc9760371100b7ee20a6ff0c9d75bfba7b31a6bca1974496eeb56de357071955d83c4b1badaa0b21832e9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEuDj/ROW8F3vyEYnQdmCC/J2EMiaIf8l2\nA3EQC37iCm/wyddb+6ezGmvKGXRJbutW3jVwcZVdg8Sxutqgshgy6Q==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "313233343030", "sig": "6cb914246e1c92050a03d9b0b4f05ddf5eebd9d87486236561230f18b407a1e934d2f1a567d7e647b178552dec35875a2cc61df3ce8ae2c1357ea8c5ff505561", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "016cb914246e1c92050a03d9b0b4f05dde199ab6bf23cec3a120f56da5843de32a00cb2d0e5a982819b84e87aad213ca78a48de8bef2e0bdbd7a8a53b5c6d0e5ebe0", "result": "invalid", "flags": []}, {"tcId": 3, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "9346ebdb91e36dfaf5fc264f4b0fa21f5bc3030e3ac27cd65eaf4f741c2e9f58cb2d0e5a982819b84e87aad213ca78a48de8bef2e0bdbd7a8a53b5c6d0e5ebe0", "result": "invalid", "flags": []}, {"tcId": 4, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "016cb914246e1c92050a03d9b0b4f05ddf5eebd9d87486236561230f18b407a1e900cb2d0e5a982819b84e87aad213ca78a48de8bef2e0bdbd7a8a53b5c6d0e5ebe0", "result": "invalid", "flags": []}, {"tcId": 5, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "9346ebdb91e36dfaf5fc264f4b0fa220a11426278b79dc9a9edcf0e74bf85e17cb2d0e5a982819b84e87aad213ca78a48de8bef2e0bdbd7a8a53b5c6d0e5ebe0", "result": "invalid", "flags": []}, {"tcId": 6, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "006cb914246e1c92050a03d9b0b4f05ddf5eebd9d87486236561230f18b407a1e901cb2d0e5a982819b84e87aad213ca78a348979bd990065db64a261453a11c2d21", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "006cb914246e1c92050a03d9b0b4f05ddf5eebd9d87486236561230f18b407a1e901cb2d0e5a982819b84e87aad213ca78a48de8bef2e0bdbd7a8a53b5c6d0e5ebe0", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "313233343030", "sig": "6cb914246e1c92050a03d9b0b4f05ddf5eebd9d87486236561230f18b407a1e934d2f1a567d7e647b178552dec35875b7217410d1f42428575ac4a392f1a1420", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 10, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 11, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000000fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 12, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000000fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 13, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000000fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 14, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000000fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 15, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000000fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 16, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 17, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 18, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 19, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 20, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 21, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 22, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 23, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641410000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 24, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641410000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 25, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 26, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 27, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 28, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 29, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 30, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641400000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 31, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641400000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 32, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 33, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 34, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 35, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 36, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 37, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641420000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 38, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641420000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 39, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 40, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 41, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 42, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 43, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 44, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f0000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 45, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f0000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 46, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2ffffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 47, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2ffffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 48, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2ffffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 49, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2ffffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 50, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2ffffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 51, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc300000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 52, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc300000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 53, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 54, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364140", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 55, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 56, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 57, "comment": "Signature with special case values for r and s", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc30", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 58, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "313236373939", "sig": "dd1b7d09a7bd8218961034a39a87fecf5314f00c4d25eb58a07ac85e85eab5162c8a79b49cae4ec15d293575a5a1af5b4d6efb74ef5c2c1be34e33cdeb7113cc", "result": "valid", "flags": []}, {"tcId": 59, "comment": "special case hash", "msg": "33393439313934313732", "sig": "d743c5d76e1193a57438f1b43b1b0e33d0d1ab15bd3d57a5cf6aebb370d46ce07df27cb730b33dfe01e34a0067e548a98c56846d9a4cd64a930c96bfd917cf08", "result": "valid", "flags": []}, {"tcId": 60, "comment": "special case hash", "msg": "35333637363431383737", "sig": "ba30f4ddf3348f26835e9c50f6a2d5023a9a1f5fe2e9cf14b3270015dac283fe1d1616abb204f615fbe99860d89158c3264182d617ac9f1560fa8291b349d579", "result": "valid", "flags": []}, {"tcId": 61, "comment": "special case hash", "msg": "35363731343831303935", "sig": "551d72e63f7b27283c4107f7d851f387b60f3f4713a5d35c21fa332fbeed449480914cc37a3fe13a74db7fcc5226388d95034a50a89a9b2fe9bf42ea29e5714d", "result": "valid", "flags": []}, {"tcId": 62, "comment": "special case hash", "msg": "3131323037313732393039", "sig": "80cead3d165ce05c7cf8469f1c35c5a3a641696c843bef0f022a6c68133dc49eea8409d743a4ad5e136207736c3ad79c8cfc7b57ebd1bd9b8a596670ad12d41c", "result": "valid", "flags": []}, {"tcId": 63, "comment": "special case hash", "msg": "3131323938303334323336", "sig": "bbc0e8b7721065a51bac9c3aad64168998cc0efa23298340d436867cc86ba847ae3baa131a83153cb31de2f758e45139f62fe6cc9ce3941c6b1789dc1010f3e2", "result": "valid", "flags": []}, {"tcId": 64, "comment": "special case hash", "msg": "39383736303239363833", "sig": "3a5ba93917b954617b40e1d866860d1522b0d310cac2457636e54e2ffdea888e3eac6fe762aee127837c2c65fd9c1f65b404b2c31bb945e75d6166503fb5c8bd", "result": "valid", "flags": []}, {"tcId": 65, "comment": "special case hash", "msg": "3230323034323936353139", "sig": "647f2b4bef6d1ea7908ac5f3dfd705494c2587456557805fe64a703b2b17503c20e164bbb505c6df56455908008cf9626df320f48aa3fc9d0cc8ad8bcf078cb2", "result": "valid", "flags": []}, {"tcId": 66, "comment": "special case hash", "msg": "31343531363639313830", "sig": "8aa653cfa001798c471eea3199dc975a4dea4f7c1ede47453409e606d05ceb51cab20967a056c0ea7fe9cdf8e1980f55b1597a2dad80c9223a0fab15c314fe6d", "result": "valid", "flags": []}, {"tcId": 67, "comment": "special case hash", "msg": "31303933363835393531", "sig": "842e421f33be241d27f12f875355902a25819f210b3685ad536e23594012d9d04fb894ae0e9c24b6ed280e224ab0811469296a9837d1e95b5d9d661d21a1c255", "result": "valid", "flags": []}, {"tcId": 68, "comment": "special case hash", "msg": "36323139353630323031", "sig": "0b703fd75bdd8dce4820fe130a0b0af17aad4e4681b0254864d5d6f8931ff573404521acf84e72ff22c2ee05d14a4bc7b70e69adc78caf81350e01379694c3e8", "result": "valid", "flags": []}, {"tcId": 69, "comment": "special case hash", "msg": "35363832343734333033", "sig": "62f0df1650560a5800fa670377a4317a604d6475c490066ce15638f8d1330b63963edf905197096818368a993fbffe32908a57153e6a1612bae6ee9ee8a8a719", "result": "valid", "flags": []}, {"tcId": 70, "comment": "special case hash", "msg": "33373336353331373836", "sig": "2901ade694d4b9c376b3244018e57bcde7057e8e11dd0f7d07080cdd1a39194bee65a4c2baa70f8e236ceba9eed400d899f75276f94e4b7997b2b01ac008bbbc", "result": "valid", "flags": []}, {"tcId": 71, "comment": "special case hash", "msg": "34373935393033373932", "sig": "aa9c8e5311b232b4ce9db03892f26eb77d655c6ff09a599424abbd4b11e750bec1034c44b02e2fdf05e1ba5eebdf954c5a01794600059e05e5c73d542da3ee38", "result": "valid", "flags": []}, {"tcId": 72, "comment": "special case hash", "msg": "39333939363131303037", "sig": "2febea016e55059e91e157b988f86048db57c37fd122f5cc60169ff4fcb4863ceb19cbc35b3061e1ac4b59b92d1f732cea3212dcbe943ccad82d32740bc22c33", "result": "valid", "flags": []}, {"tcId": 73, "comment": "special case hash", "msg": "31303837343931313835", "sig": "2be463ff06af2096dd62f0326e1af51c585f18ca8f8aa361dedcf55d543e6b7df56afd59dad42530d94f11c59a6408c54826b7a9ef83f4d020f209d71f9b74c5", "result": "valid", "flags": []}, {"tcId": 74, "comment": "special case hash", "msg": "33323336363738353030", "sig": "f61f64defc45abe284b39161b49585f21edef1e88d06389e5b5aacbb394ce4dca5a27e17df10aedace97eb2c48659f69b58cfe76a1f1ac30fea3043655bde515", "result": "valid", "flags": []}, {"tcId": 75, "comment": "special case hash", "msg": "31343438393937373033", "sig": "052134eae13c1dec5ac5aa46186391786f5b60591cb0dd30bfc61e89486abfe209cdaa279c4f0d3d5ae00e0d74e733a260b8b120a1bda7e5a90194ec442e592d", "result": "valid", "flags": []}, {"tcId": 76, "comment": "special case hash", "msg": "35373134363332383037", "sig": "24824614686b80f3b738970a27816f58cf103c4a93c2d6b0f5f6de65a65501e3180e5801a593063e75b83cd7ab8e52575a013a1be5cdeeb05b30e3ac9dc4ed82", "result": "valid", "flags": []}, {"tcId": 77, "comment": "special case hash", "msg": "323236343837343932", "sig": "2ff7a5ab2f1a3323651a0d17c4263672ee4d2c560cda94e7d52ee755138bb045542ce83d8d9d441357e24b618b5695164d4391791cff62eeb01609d1d7cb1c0a", "result": "valid", "flags": []}, {"tcId": 78, "comment": "special case hash", "msg": "35333533343439343739", "sig": "ae446d1a81766d21dd7fc515d0a956605d0cde26d6086a76f8ffc81a6dfbea464fccef9f75e94abc7eb3f2bdcafdc5d97d61b9d950a06010ab4c54e3da7fd4e0", "result": "valid", "flags": []}, {"tcId": 79, "comment": "special case hash", "msg": "34373837333033383830", "sig": "3957cff4a75fc6039c0b0c2e47eb9b07ff6ec5dc8a3c3316590a7ec9a1d7d9934e578ee6594a00cb80c640cb9589d616dbd1cecda2d15dcc0062f30686d6073b", "result": "valid", "flags": []}, {"tcId": 80, "comment": "special case hash", "msg": "32323332313935383233", "sig": "437c36031737a3140dc30eed281adac8e9074187aad41502a3b9a3bfd4ef252cda13f88f633202b9b9517b93a6c08a7b8e6858734e8894b1a64c6ec08f1d0423", "result": "valid", "flags": []}, {"tcId": 81, "comment": "special case hash", "msg": "3130373339333931393137", "sig": "828c12fd9fe31f91bd8f58aac72ee6485e34ceddf91927cf3a09b63363b9d8e90e889664a8c98619cab572687064edb4f0500f8324a5df0bfb5a431a3cb1ca39", "result": "valid", "flags": []}, {"tcId": 82, "comment": "special case hash", "msg": "31383831303237333135", "sig": "807cb34aa6ea48b175f41f3afdf70a109d2b746ae48e08677cdafc33d916b2da41980e6f7ad19944d278851f98e0a6220ae888964ae81a667a63fec21449334d", "result": "valid", "flags": []}, {"tcId": 83, "comment": "special case hash", "msg": "36303631363933393037", "sig": "a998f9f0daf02f717f5292142dca447c722d2394dae0c84910433754669716ac826fc37269539cf8a98997f8a0268bfffe888d6c23bc68ad7c759db47f65a925", "result": "valid", "flags": []}, {"tcId": 84, "comment": "special case hash", "msg": "38383935323237303934", "sig": "f151b614afe5bc9d511d0c34a7eb44283921272e91b3e5d02821cf7a43a92bc5097aa33dc50ebf8fea036cd7e224a4d38aa20773e5a78ddb83a2f3b579b2ef6c", "result": "valid", "flags": []}, {"tcId": 85, "comment": "special case hash", "msg": "31353830323334303934", "sig": "5f21585381f5f42e9f76be3f61f4cfd6476ecc6f06cd4fbcf13e08c27f42614895d5b2deabf19891edd41ac52d9072fadebb2f0145bec9b916f68fd1fbcfb3cf", "result": "valid", "flags": []}, {"tcId": 86, "comment": "special case hash", "msg": "33393635393931353132", "sig": "bdc361e68984482d7b169bc5e6ccf82d2263871be749d67a44f548d32bcaf5f1375614fa4134d5055ac117a6ea948b74269b8063e39259d494a7544afb6291ab", "result": "valid", "flags": []}, {"tcId": 87, "comment": "special case hash", "msg": "32323838373332313938", "sig": "5773b016dffac865ab008abe8a06353d197b4dff32403d7ce98ada4d20ea8a00d60de9c98cf50eff0515b962dffd6aac8a1b72bc9cfaf6bda12b99f63eb976d2", "result": "valid", "flags": []}, {"tcId": 88, "comment": "special case hash", "msg": "32323330383837333139", "sig": "57b747d21fc898472a888b88693a989eabaf143396e4cb2de4af19386fba384f7c99f63904191a4464d0d23ca560d5558895cdcff93af4b00c1c66ca2d974393", "result": "valid", "flags": []}, {"tcId": 89, "comment": "special case hash", "msg": "313239303536393337", "sig": "854be2bf302a2d6db437eb9e78703673c1c7371399e68caa8625bb13c7aa0fec8fd22607e0169eb2e2e00c4af898fd2a609dc57a9fa94a7f93372098fa675649", "result": "valid", "flags": []}, {"tcId": 90, "comment": "special case hash", "msg": "32373438363536343338", "sig": "ebb3359de3b13a518545a86b7fdd92f4793225b8ca4555a6bd4182922b0452be83faa7dff1aa0eed89a7ddcdaa5d716ba6253c5c21f7122c2755eb78b28884c4", "result": "valid", "flags": []}, {"tcId": 91, "comment": "special case hash", "msg": "37353833353032363034", "sig": "8bc91cfcfc85ba8aa171b703a330e398df4460d22602e73e327423ebf98bf632ec7569072aa73ff19f183daf433abff142d7d5edceb25b771d853acf0fbd68b6", "result": "valid", "flags": []}, {"tcId": 92, "comment": "special case hash", "msg": "32333237373534323739", "sig": "895b07c0450ed6f4941633a053c978128c46e5225c00eb009c3c6cee5eb2b842c982818b260f1650e03eba8f9db1a2ca79c3f804dbe7d172233260e1a9c10640", "result": "valid", "flags": []}, {"tcId": 93, "comment": "special case hash", "msg": "373735353038353834", "sig": "d5e152ec304090d764fd7ae61abeeadff2fee8df3dccd8fb44d2af5a8dbee0bc72518dc1ecc993faadffc3426594fe2024c7c84ba101a9274d88009393103ff6", "result": "valid", "flags": []}, {"tcId": 94, "comment": "special case hash", "msg": "3137393832363438333832", "sig": "1298b131ce97a528e5dae05d92b286e2447b17ec002267b9e8f03784d4074bd1edf223ad9c308aef22e1e0c24a20268f966cc2b9ca4d941945bbca057db92d4c", "result": "valid", "flags": []}, {"tcId": 95, "comment": "special case hash", "msg": "32333936373737333635", "sig": "1e79b3921d23d290a57d08958d3ad8305ec444efe1281c98fda44e8af7648f49f4c7610ad1ba9339178c50e7979b5aa9af07d8143e59d13a2e84f98f37101e3b", "result": "valid", "flags": []}, {"tcId": 96, "comment": "special case hash", "msg": "35393938313035383031", "sig": "e455f464e0edff9c959f84f081828896149a330361ff2d16d5a2448c9d683684351cfa2f29a1318ebb3a46f0a36df8954043949b8d7cea94eacf99108b4d3fa0", "result": "valid", "flags": []}, {"tcId": 97, "comment": "special case hash", "msg": "3136363737383237303537", "sig": "a885770c9ffef33f0c11245064936e3dd165ea2633575a6a155368670351f726de31e6a58626a41fd029cf766ef44b8273b88558e2452e893978fbdda1e321d1", "result": "valid", "flags": []}, {"tcId": 98, "comment": "special case hash", "msg": "323036323134333632", "sig": "4b6b451478ba253ae3c75ca5b18b70ccd3cca408ed245cb2af3369548dd2e507fe479b631a3431b42772925cbfe8e789f9c55fb2fd1d7ab51664cc2fa571ad93", "result": "valid", "flags": []}, {"tcId": 99, "comment": "special case hash", "msg": "36383432343936303435", "sig": "7ca70376547ad6d18f8e539f09dc269ebaa06854c1adacd58fdc735ed3cf0c16f47654f4c0ac1b0e65b712300e3bb472983b116db5206520eabd886dc706b266", "result": "valid", "flags": []}, {"tcId": 100, "comment": "special case hash", "msg": "33323639383937333231", "sig": "388514d147664fbb37271cb8693e47459c0627d6b1dd52dff1d3947dfc9cabec99d3d40814aa177be99e4819696996bc75073f4518955587cd56b5ad8bbc2c58", "result": "valid", "flags": []}, {"tcId": 101, "comment": "special case hash", "msg": "31333837333234363932", "sig": "44d3ac50d9b65601d79b47d6c5d98394cef155211ff37d4bac15e0d4890809b83ea03829afb0545e088361a8cf952aec17bab7637fddd6db35f039803523c921", "result": "valid", "flags": []}, {"tcId": 102, "comment": "special case hash", "msg": "34313138383837353336", "sig": "a33004a2cd50a4f70447fd382e7fdc9257c4d9be7b16e686c5082a231ee7b010d87b96ed3beea54652607017702cfce5d4e7fcec1fdd28f41681ab80a5c5b63c", "result": "valid", "flags": []}, {"tcId": 103, "comment": "special case hash", "msg": "393838363036353435", "sig": "668ad18cc22c1d1498cc8e5a11e2bfc4c1e1fcf0a7350a5806c5533ae332f0b1f58b49369771bd20bb08b63d4a9212e2dc71da9257ed3710d9eaef9bee469eb2", "result": "valid", "flags": []}, {"tcId": 104, "comment": "special case hash", "msg": "32343739313135383435", "sig": "f7cdcb0281c70786cc3653820d1756a78395a9eeeab2a4d164e260f64ebfd6a8d966c74499cac97ca8ee67400df01b14793b6d7d07668fc202a9918f3c046e9b", "result": "valid", "flags": []}, {"tcId": 105, "comment": "special case hash", "msg": "35303736383837333637", "sig": "de0e781d9e3e7f73021458fc1201fc021e5c54f1fe40b1b10db8fcf16ef7e54a7d9db92321b5e5bb105990145390979390d32394116f4e78af34b85105dee8e9", "result": "valid", "flags": []}, {"tcId": 106, "comment": "special case hash", "msg": "393838353036393637", "sig": "011dac8ea37f7bc6a530a42d0e3bec8c845694f73bec6950081a6f999ccdfbc6153e57ee45e0a379839f3b8f6faf86de7a626b210f4c1007e431f842e39bf7d5", "result": "valid", "flags": []}, {"tcId": 107, "comment": "special case hash", "msg": "32373231333036313331", "sig": "63f9c43a8cab49f518685a120bd73a4e5956f9f167a78d4661fc795d41be2ae16aaf4f3384f1489ef026cb29e97ea1b5562fe8ceb9978d506fb7064f427b9f31", "result": "valid", "flags": []}, {"tcId": 108, "comment": "special case hash", "msg": "33323034313031363535", "sig": "7f0fd3736166195ba810d5a2dfb5e1f03aece2170510c8aa4cc4a0c974a7c5d6370c8772a75d32e8c9cc103004e75e6d30a8ac8611b84b89c41c65542171bc5b", "result": "valid", "flags": []}, {"tcId": 109, "comment": "special case hash", "msg": "33313530363830393530", "sig": "f975196086d10f683f4aa1a3c2d5fe13fd0f52ee72aa3f785006aa024c7587356a66364156ef21b5dfdcee60cce8fb09c12019bc576848ff73db49856af74681", "result": "valid", "flags": []}, {"tcId": 110, "comment": "special case hash", "msg": "31373237343630313033", "sig": "35fe6d9bf9f7d47612c3f5be6a4e9a0fb0c14854d1a377adfb5485d6e3835c6ff96587fc460e7d07396f9f2d060693dae632721259e77c90b8314002a5235dd0", "result": "valid", "flags": []}, {"tcId": 111, "comment": "special case hash", "msg": "3134353731343631323235", "sig": "210c7c9b231293c8ec09b0f610d31724a045f6a33f84423fdd541ac11ff78962e5a40e6b80da99cfc49ce969f1f59146835183e61001b4513f927b71ec3b2a13", "result": "valid", "flags": []}, {"tcId": 112, "comment": "special case hash", "msg": "34313739353136303930", "sig": "09b7dcfad2c84b89825cf3aaaffed51664faccc0d171a43387a6ff98aa128a04272b00e6e0917afe4fbe782604428e09fd91c38125d51c3ba06ce3198e6bf736", "result": "valid", "flags": []}, {"tcId": 113, "comment": "special case hash", "msg": "35383932373133303534", "sig": "09c7c99681c9159b22c0a467999559a31e279075d37ef872a88ae13565f6149bb0ff953be1940d2cf548663c1b4db7b416521db289467733b9a76629f8ab261f", "result": "valid", "flags": []}, {"tcId": 114, "comment": "special case hash", "msg": "33383936313832323937", "sig": "2bfaae0ea6d8baab3e02ad7fa3dda3ce0725d11533e3666477f54d697e2ca9bc9289d5da443395bca18fe9d1a4afbe04a32b4ecd258eca6c1772acff2d0b9a89", "result": "valid", "flags": []}, {"tcId": 115, "comment": "special case hash", "msg": "38323833333436373332", "sig": "368846edc677ae8fc237069cda719af3d7f17cc136fe443b2af614ccfb4844ab5ebe6c1d3e88bc4e291841ea97c836bdcf67d9eabe926346c5f42105f7b38f67", "result": "valid", "flags": []}, {"tcId": 116, "comment": "special case hash", "msg": "33333636393734383931", "sig": "f336da82bea2a111bddef6a25de4ab87d7c95aa80d21838f3a4efa3d9346555dda5ab612b327aa0fe95d1caf85f3b6698c23a47212006c5667cfa92aa3ef4dad", "result": "valid", "flags": []}, {"tcId": 117, "comment": "special case hash", "msg": "32313939313533323239", "sig": "97c2fb9865f9e76f8d54ce957120b68ccb04cd3183dae7130f73139cd56655cffb63e38176ffac37d0ec1e49c2e2efeff04dffdad5a75f3576f8276cccee9851", "result": "valid", "flags": []}, {"tcId": 118, "comment": "special case hash", "msg": "35363030333136383232", "sig": "7393e0207e07bd73b674d3667dfbc9c30022574d63079a040a23c0cd7e1b6aa62994b3468432fecd0a32134171179d2809244d586bd971129cdba73fd3dc8876", "result": "valid", "flags": []}, {"tcId": 119, "comment": "special case hash", "msg": "383639363531363935", "sig": "21e1943d7d396a8c46658bede4ce155c9a06f929cf6ad292d32c91cf8f49388730783c682cebfffec5787d762bd725bafc9c4075ad8eb1582188f4c05dd5169d", "result": "valid", "flags": []}, {"tcId": 120, "comment": "special case hash", "msg": "36353833393236333732", "sig": "5a269eb44e910bfe8a2656dee47556cb908a417917e2068e20d201721f44f9b1e69d463204dce77c249439f22f77cc4c88134012a286b36a9559f694203766c6", "result": "valid", "flags": []}, {"tcId": 121, "comment": "special case hash", "msg": "3133323035303135373235", "sig": "cb8c146fb3d58846e5748c48742af2f1b77805f6cd1e4eb98d8c66cbdf5d645517ac992e10251e334467f8e57e2e1c269db8b19469321c74b443972a80f38b2d", "result": "valid", "flags": []}, {"tcId": 122, "comment": "special case hash", "msg": "35303835333330373931", "sig": "212d84a153db81cea5212fa7dee31d59bdca1307277a01b5936c3aead31bf1e4520305dbef2bda6526fa2cfca789a1c9aca5c2ad4c0027cc8cf3881813da8a72", "result": "valid", "flags": []}, {"tcId": 123, "comment": "special case hash", "msg": "37383636383133313139", "sig": "310c82892f571134a36725f4a31c5cba8bc46e65002d73b11364084433d8da4a9ca552aca84b96cc9461e2b65a64975118ea78b8b355a0ebcc1a61de37877d13", "result": "valid", "flags": []}, {"tcId": 124, "comment": "special case hash", "msg": "32303832353339343239", "sig": "489deda580c62533783df9fe62de34c2e2cab91d676709beeff13afac8e90db932a85a9c56f308b7a794dcce614a5ed7e0857030b8429fe3b4e07ad533a5a00a", "result": "valid", "flags": []}, {"tcId": 125, "comment": "special case hash", "msg": "3130303635393536363937", "sig": "e8897c1cad1fc870a7d364676a9d7f7cd3ac951f3bc3a9ef1f7231466c3493d7dd2128e876d62da82cfc5fc508d33bf66b71c0a84d0a9b7e47dfc620f5846bc6", "result": "valid", "flags": []}, {"tcId": 126, "comment": "special case hash", "msg": "33303234313831363034", "sig": "b4d771d19fffb1fe5ead25ef5dbf6b53d4d3dad284641108ad84b2541ad435a4843ecdc2641b33a3ae9ae15d559f6229d7304ee5ecabe00db73bf2b6b5c6c21f", "result": "valid", "flags": []}, {"tcId": 127, "comment": "special case hash", "msg": "37373637383532383734", "sig": "5ab5fb3136fabdbd22009642df03685935819895d675fc284e8b8112db522d08d87ec88173e823ed70438fb1088b00689352542fabad5e9fd6d4c3c58f722f86", "result": "valid", "flags": []}, {"tcId": 128, "comment": "special case hash", "msg": "353434313939393734", "sig": "be310120169f8d488c6e5ec5b5e588ab8a65040169d9efd3062e0d05fd7d58df45033f291fa21a85cc08f78fec2dbd94135520de261360728b8743b558ed16f8", "result": "valid", "flags": []}, {"tcId": 129, "comment": "special case hash", "msg": "35383433343830333931", "sig": "cd7fb3f2c25dfab6f9ee83fcbb08698680e9d1f3d47815bc772d717a764f9997287dd85b976d7f56d23ae7837398c118932aadc982f675f94103036729a47c7c", "result": "valid", "flags": []}, {"tcId": 130, "comment": "special case hash", "msg": "373138383932363239", "sig": "69f18c064ad2683cc1b6d8b79020aacd186b6ad1999e6e55bf28bb1dac33f339ef66e66001fcc219c9a927d7f0b84863483bfd1ffa6086c06921905310c793e1", "result": "valid", "flags": []}, {"tcId": 131, "comment": "special case hash", "msg": "31373433323233343433", "sig": "00547c6bb40f52d207fff796a29f6dbe62058e50fb73bde6b9c6ca11346fd8e82bc82bd3efc9febe8578acdbc3148bb46c41a39be9ae1994ad52d8bf13195d09", "result": "valid", "flags": []}, {"tcId": 132, "comment": "special case hash", "msg": "32343036303035393336", "sig": "a80496adce42e7971ebe91300710cf4f535fad266668d76d72c95fffe4d425700d4338ca32857e14e0ea8026bc194227b910b98509c8c9307b0d8d93d47b191b", "result": "valid", "flags": []}, {"tcId": 133, "comment": "special case hash", "msg": "31363134303336393838", "sig": "3de40634d11a7a6b67023b84650420673ce6dbadb1159768cc0fd55f3784ec88a455fb08e51b8493177d88fca43aeff306e1490d7f6d24d6a910970a3d8619de", "result": "valid", "flags": []}, {"tcId": 134, "comment": "special case hash", "msg": "32303935343235363835", "sig": "c1f229c0557d4c47962593781bc96cf745f3bd629ad85434dc2eee456ddb30318638f6c01c15d23db24bb851f6c63c763c1f040976f3f2b32c4bb1b9506c1c12", "result": "valid", "flags": []}, {"tcId": 135, "comment": "special case hash", "msg": "31303038303938393833", "sig": "35dd4957b352e8b1bbc80d1deb21f9b0989188ade3fbe46f75106da1684e1d6d8b508e2ed7a51efea0dfaf377f6bd5d4ae133cc4c93650600be545af5d3acd75", "result": "valid", "flags": []}, {"tcId": 136, "comment": "special case hash", "msg": "31353734313437393237", "sig": "410aa9c943e663082c6f76b84469c9845e0d439ba7ffc7cac0418eea0e20e638c873ab5c21c9f0ce0bf78484028796b77451e1187250ee33535dacfb3cee5f61", "result": "valid", "flags": []}, {"tcId": 137, "comment": "special case hash", "msg": "32383636373731353232", "sig": "8191db069b571cd40f2676348433430d3a65155c233c46a42a4299e6f5be806cf3679ef8af0b1b3a3aeaa7bcee51ce960441622e9ff2dcb22a8ec8de724e0a0c", "result": "valid", "flags": []}, {"tcId": 138, "comment": "special case hash", "msg": "31363934323830373837", "sig": "889c44edbf3825b18d933aecd5ef70d12ebb00bf79550451205fd6f5ba7f372becb67194bed2b8176077622d58c9ab4fe4ca34601decc09f9386b8c4445c7224", "result": "valid", "flags": []}, {"tcId": 139, "comment": "special case hash", "msg": "39393231363932353638", "sig": "aa87113aff2e1ad6461191241f90a23b91242d0066779daaa9506a4188abc42733dbaac5ac443fb4d9529f83247f94c0ad1360d4d0ba8e162a377946c6ab9ae2", "result": "valid", "flags": []}, {"tcId": 140, "comment": "special case hash", "msg": "3131363039343339373938", "sig": "0e13f66a8ffd0da1c4b67f4d805941e90f98ce386540c48019c1ac10540756830cb489e8d5acfca5245d9292f59c6ede52425157af77b8beef38d23b6e6ade13", "result": "valid", "flags": []}, {"tcId": 141, "comment": "special case hash", "msg": "37313836313632313030", "sig": "6c1813f660c78bda956c1685bc924f69d1bbac5fadf3e4b027ab049bc82ad13420de89ee005d7646f070bdac794ccce24d661b390a78851d35fe6fb5b25b3eba", "result": "valid", "flags": []}, {"tcId": 142, "comment": "special case hash", "msg": "33323934333437313737", "sig": "48dc830b6326ec218144391b658d52045ef86ef918a8d41c59131912b1a46fb1a431916cb7cf79129b90f09842b3f2164a6cf603db88f2d99944142c00b42559", "result": "valid", "flags": []}, {"tcId": 143, "comment": "special case hash", "msg": "3138353134343535313230", "sig": "4d45782be145a27ae9ecb6cac1b9e30be87c0d13b7d6ada9f795ff051351ac70cf71d1eb15e88446ddb900f20d1e0739da499de9963fe99ded00a62da6462d62", "result": "valid", "flags": []}, {"tcId": 144, "comment": "special case hash", "msg": "343736303433393330", "sig": "11acd8b8d736e7f00476495803fbd20ad351321e800cfbddbd6a7dd610c5ab8c734027aabcca9487773dc3ab069b802c00f5b6e5520e7761496ac1e7c78ced91", "result": "valid", "flags": []}, {"tcId": 145, "comment": "special case hash", "msg": "32353637333738373431", "sig": "41be8b3bf41a4c507de12f098f7d409a1f941fef84d93794c497f7242a7c382c81f7e7243116f24b84b0321e93eed35e2bdc32b00aa8eb9583be3e9b7a09a4f3", "result": "valid", "flags": []}, {"tcId": 146, "comment": "special case hash", "msg": "35373339393334393935", "sig": "ea032ff41b061e93e456a5f0a9cdef36c0732df4d55ab4d3867484b0fc49d9ebab298dd811826a6a9319c3632a96253c31c14f75baef536a645420442bab4d43", "result": "valid", "flags": []}, {"tcId": 147, "comment": "special case hash", "msg": "33343738333636313339", "sig": "8b1ff140c65adca22e5596ffb95a5121c356d2d4055f14606445249a5725686fef8c16ff228114a7e33b35ad465f957577dea405fbdf3faf077a878754e58bef", "result": "valid", "flags": []}, {"tcId": 148, "comment": "special case hash", "msg": "363439303532363032", "sig": "3a40e8dc3ebe9e19dcd0d4d1b698ab2a4934a146def5427b3a6a8fbfbf34784654f65e36088d2d4543011c94b1e5371697202d488b342dd6f77a69944128223d", "result": "valid", "flags": []}, {"tcId": 149, "comment": "special case hash", "msg": "34373633383837343936", "sig": "15fecd439137df74820727f71218405cbe525d403c574471d8a36fa4b1f592ab18ec290971ed0a227ec47f1e2142f3b8fe5b17336350c5515d4a87eb3382fcb6", "result": "valid", "flags": []}, {"tcId": 150, "comment": "special case hash", "msg": "353739303230303830", "sig": "e676e84a299f481a207cde6a4271c87d73e29d1e49216393292323bcdc238844b8a98c769bf81429644758c8f803ddbedf81634e53099c43ad0ca42f4207ba16", "result": "valid", "flags": []}, {"tcId": 151, "comment": "special case hash", "msg": "35333434373837383438", "sig": "5116f8f0af12b47bd025aa6eaec5007d4e3c5a3a72cb4c331f569581adb01bfb6962251da7ba9ac951cfbd2051bcb7d953005cb9599ae0ad9c5f5139baacb976", "result": "valid", "flags": []}, {"tcId": 152, "comment": "special case hash", "msg": "3139323636343130393230", "sig": "b83f3918b6c5506d648ba3dba36762db593ad4b791456babcc3c1a4966317ae68cd0166047cec89963e9c8ca43b556ac17d0d62177a9bda35e61d0bb16dd471d", "result": "valid", "flags": []}, {"tcId": 153, "comment": "special case hash", "msg": "33373033393135373035", "sig": "077858a840230ca21385c4ab4c36cbd3ffaf85656202fba58f1ea995f52ebc4c543e5e32a6d2f5c08664ed72175adaa25cdb5d6a754b0cb184e6994ede66c5b9", "result": "valid", "flags": []}, {"tcId": 154, "comment": "special case hash", "msg": "3831353435373730", "sig": "538ad8797a397414ac82287c9216e41915c9e3dadbd493a0bbef5cb0dc7935ec2c94cfdae7bf76f90b3cc7d19feea4005b387e312ad4116654d63cfbecf2ae1a", "result": "valid", "flags": []}, {"tcId": 155, "comment": "special case hash", "msg": "313935353330333737", "sig": "ff8bbd1b6441388cb8d562c28ce29fbe51de11502fc825773ded3f0df225b2368eccca0148b82fdfb370cdd073aa0634b39cc70d0d5244a7319e4b13791e2c2a", "result": "valid", "flags": []}, {"tcId": 156, "comment": "special case hash", "msg": "31323637383130393033", "sig": "7c179a010f51d66ec82fe5d5d45bd867b4b236a27be882e627506f7286ed7baa5e38c048fb0fbd81c40df3dc16087d9aabeb51a193107499d29d8cf99c388a21", "result": "valid", "flags": []}, {"tcId": 157, "comment": "special case hash", "msg": "3131313830373230383135", "sig": "7e0810885b405d54ceb2eb18cae08de2062f61b7ed94ab67eb15e87b64e730eff511a7919e6e4d70c8d61b831e383f58dea5878a6c8c5f0436ee058dd80a7668", "result": "valid", "flags": []}, {"tcId": 158, "comment": "special case hash", "msg": "38333831383639323930", "sig": "c665d558dd638ef27a28557c3deb8a2f54abf9bd0bfa032c7ec9a514da9a9e9e65c9efc355981f91778227eefacf1bb2fedb98657e6cd8674fdd42ae00d619ed", "result": "valid", "flags": []}, {"tcId": 159, "comment": "special case hash", "msg": "33313331323837323737", "sig": "4f06b82aa0d070a004a7fd1135bc3a0bc36fcaeeca35e3edf00f5895394d59ab65f71dd7406a17bf19e434a4635479340204dd862a9f2c4653e2fa39b178286c", "result": "valid", "flags": []}, {"tcId": 160, "comment": "special case hash", "msg": "3134333331393236353338", "sig": "539c8fe5715c3dc893815ec2f00e203b4cd4f8fd36cc5742cc81ced266e02e3ba5964b2d5157624cf42b6726ae23a7d5ef83a5d1f1460bd573d5a15316be5bf2", "result": "valid", "flags": []}, {"tcId": 161, "comment": "special case hash", "msg": "333434393038323336", "sig": "25f337273591f276849cd855b03d07cbcb205924cda4f62a079591602cc10a8cd7b82c8fb38bbd503d92e5ae9303e8673c6dd0e9389f5af53366bbab851f0470", "result": "valid", "flags": []}, {"tcId": 162, "comment": "special case hash", "msg": "36383239383335393239", "sig": "f36018945d24c89678ce2c8cf3cb4f93c38bdad3589891a5baa293744d4daa2019ef05878dfc636a4662fd5dd127c908d7948991a324840323c8aef4fc2ff8ac", "result": "valid", "flags": []}, {"tcId": 163, "comment": "special case hash", "msg": "33343435313538303233", "sig": "43203c89ad43a2bb1910e70ea104347e84764599535d46dabbe547395b1463f4ed3d29c7c506ecc988614b368b38dd5b4f1e330c1b861efca8152a704b9146e5", "result": "valid", "flags": []}, {"tcId": 164, "comment": "special case hash", "msg": "3132363937393837363434", "sig": "c2740bfb3f387df1b564e3ff48835b9e380104716f58c5a43e97bb2c2d84d04ae760ee5d0950b512f6c271cd1a87619b830df83fd40d44b9283539b3aa380019", "result": "valid", "flags": []}, {"tcId": 165, "comment": "special case hash", "msg": "333939323432353533", "sig": "ec07ec5378ed131b2dea7ae9776ba536daef2afc38e2556a70b89b9752eb1f71fea25b9e50b1cfa2cf475dbb2245761d5f4585fbbc438d97226c64ff74bff19e", "result": "valid", "flags": []}, {"tcId": 166, "comment": "special case hash", "msg": "31363031393737393737", "sig": "e438303ccbbee359c865997e46112b0afd7a647c593429291398f0c432dfb9f08487e07a53da18793f8b527069e620e44587e420245d6ec827bb35cccfae7a47", "result": "valid", "flags": []}, {"tcId": 167, "comment": "special case hash", "msg": "3130383738373535313435", "sig": "fc09fa30e89a2ba3d0c4d9d9350e717168c21253371359c0f3cb8c8807bdab565d6c4766bca462cf95b4aeb8f5886b52fc3286642ffee8d0bd7ffd4af7badb4a", "result": "valid", "flags": []}, {"tcId": 168, "comment": "special case hash", "msg": "37303034323532393939", "sig": "4f184fba2be39078385290acb4cc4b3f39b099c3300c762df205c605c6b30e1a506481d2018b3a4c0ad558f029c82e0625c833cbbee978bee7b589742ee1e377", "result": "valid", "flags": []}, {"tcId": 169, "comment": "special case hash", "msg": "31353635333235323833", "sig": "e9a27533a50eafb09561dc335d67f8e5e53b4fc16b3013f062e581ad027e110e7e4150def368f969ace0fc28cac7a3312d6b9af538c412048be1763ea81f3f44", "result": "valid", "flags": []}, {"tcId": 170, "comment": "special case hash", "msg": "3233383236333432333530", "sig": "fac24d54387202bff01a91f5504f778c183a0a7930c02af0b618ee64d1b1e438f3a53cb6f96feea45ccadcdf9ac78cd735ec3342163e573d2125caa0d8d507bb", "result": "valid", "flags": []}, {"tcId": 171, "comment": "special case hash", "msg": "31343437383437303635", "sig": "3544590a0f9fa5d43ad4e0a003a8d7db58b8570951657aab3bab732727d1bbc2f257beac10d53e8012ecd236793d280026c5cf1c04aae522019b87e003500ec5", "result": "valid", "flags": []}, {"tcId": 172, "comment": "special case hash", "msg": "3134323630323035353434", "sig": "bc0726386497c85da8f4055a727b1938e96786b009e6847a080a8aae571b075354b1b15fc7886f09b121af6520d0f4336d259d734713fc3e973cf28368830eff", "result": "valid", "flags": []}, {"tcId": 173, "comment": "special case hash", "msg": "31393933383335323835", "sig": "216f8051f9ceed5b5cc1085f83efd871128cb44b260ac12c486c0ea06c71aa55df90346cb028245a72ac7d8094497f0efb83a7c44ba3b258873127355e3b2edf", "result": "valid", "flags": []}, {"tcId": 174, "comment": "special case hash", "msg": "34323932313533353233", "sig": "cb76652e19d6e7a72c9cac35c2ae46178d8c0ff59b06b0cb97c31aad39ec1b095c47b889a29c781540b8783ca24e2acc340178685d7331017e29b4efe92d9fbd", "result": "valid", "flags": []}, {"tcId": 175, "comment": "special case hash", "msg": "34343539393031343936", "sig": "edfc03190c839528ba2aa0ba3a23b596fcfec1bf2bbf4467f1fd88398cab8ad245b41fa49e0fa7f060ac1ba38ab4d2d5ab5b9fa54ca59285aee09ceedd9865a3", "result": "valid", "flags": []}, {"tcId": 176, "comment": "special case hash", "msg": "31333933393731313731", "sig": "e7631f03d9dfddc64cfd2a971523def68cb9f8a64e07eb2235c7250adc36480ba004cbac3e04056c7e65fdb48be051e9a52ab427c826c84e2cb2229252983663", "result": "valid", "flags": []}, {"tcId": 177, "comment": "special case hash", "msg": "32333930363936343935", "sig": "15e36a42515118021f6f5372ecbff90755d8ae77f9dd683972d2f26aa67164518d1cd988ba0a1bd919d2f9b5c8a3517eb59ef776caecdf2b5ac2f7a721858315", "result": "valid", "flags": []}, {"tcId": 178, "comment": "special case hash", "msg": "3131343436303536323634", "sig": "6daacbc1125cb3690e43e16b414077c0dd274b96ed61892bad5a519274f01b23d044965811b4050c7a85021e8827635cf9f46260fc33bb7cb56b1b37180c4220", "result": "valid", "flags": []}, {"tcId": 179, "comment": "special case hash", "msg": "363835303034373530", "sig": "37e50775ee06024d596ed49824b1e6a49efae25c7dce8181de33f93ce34ac3ce616a3e9d1fed086138f6feef6532647c02bd324ba4a8bfea20640d22f5494429", "result": "valid", "flags": []}, {"tcId": 180, "comment": "special case hash", "msg": "3232323035333630363139", "sig": "d5b64cdf82e354ba6a01772f7d38e8d46a729b808aaed73616ed41a9afc83db7b5c456c91254e57013228c9724bb7f97aaf18e1bfd4c99d3ca9eaa8214382a10", "result": "valid", "flags": []}, {"tcId": 181, "comment": "special case hash", "msg": "36323135363635313234", "sig": "915779b90ae6f6c1fb82c198c9f0719ce2ea37be0f261e36585ec89adaedd2b67d05e7794ac57578790808c0ac52ca3a51d1399f1a4c7173a7ed19867732b3d9", "result": "valid", "flags": []}, {"tcId": 182, "comment": "Signature generated without truncating the hash", "msg": "313233343030", "sig": "097a04ee03a13c511d939e8bbe1471c57a71020e168e2689c69a5625686e24ad40d24d52f3701ac8da959560c36ed0750a1cf031b728a9134e2b71ed3ddef889", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "EskKXevYjUJoa4Qifbx1U1G3jnxsuGwLIlNvOUYDZG4", "y": "0D2WWFG8QbsIlJnFGYe4mag1PZl-BA_dNSkKJifwo6s"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0412c90a5debd88d42686b84227dbc755351b78e7c6cb86c0b22536f394603646ed03d965851bc41bb089499c51987b899a8353d997e040fdd35290a2627f0a3ab", "wx": "12c90a5debd88d42686b84227dbc755351b78e7c6cb86c0b22536f394603646e", "wy": "00d03d965851bc41bb089499c51987b899a8353d997e040fdd35290a2627f0a3ab"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000412c90a5debd88d42686b84227dbc755351b78e7c6cb86c0b22536f394603646ed03d965851bc41bb089499c51987b899a8353d997e040fdd35290a2627f0a3ab", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEEskKXevYjUJoa4Qifbx1U1G3jnxsuGwL\nIlNvOUYDZG7QPZZYUbxBuwiUmcUZh7iZqDU9mX4ED901KQomJ/Cjqw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 183, "comment": "k*G has a large x-coordinate", "msg": "313233343030", "sig": "000000000000000000000000000000014551231950b75fc4402da1722fc9baebfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd036413e", "result": "valid", "flags": []}, {"tcId": 184, "comment": "r too large", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2cfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd036413e", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "kT7QQwIu5ZD1nkT1GeXP2dbxuEpQ-0F-mtBmg8avoZQ", "y": "to-4DW7yYbWmO1f4cdLqciQxn1-j7T3XfxAS26GdA5U"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04913ed043022ee590f59e44f519e5cfd9d6f1b84a50fb417e9ad06683c6afa194b68fb80d6ef261b5a63b57f871d2ea7224319f5fa3ed3dd77f1012dba19d0395", "wx": "00913ed043022ee590f59e44f519e5cfd9d6f1b84a50fb417e9ad06683c6afa194", "wy": "00b68fb80d6ef261b5a63b57f871d2ea7224319f5fa3ed3dd77f1012dba19d0395"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004913ed043022ee590f59e44f519e5cfd9d6f1b84a50fb417e9ad06683c6afa194b68fb80d6ef261b5a63b57f871d2ea7224319f5fa3ed3dd77f1012dba19d0395", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEkT7QQwIu5ZD1nkT1GeXP2dbxuEpQ+0F+\nmtBmg8avoZS2j7gNbvJhtaY7V/hx0upyJDGfX6PtPdd/EBLboZ0DlQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 185, "comment": "r,s are large", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd036413ffffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd036413e", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "ZEzFToRGchP6_ipEUdulUPPqduqZcL1iUfx3g6Qg2LU", "y": "HNlDkVXsRdVjRnfCgRVLvfmf5EBR3OwyIFPKaeqIKXw"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04644cc54e84467213fafe2a4451dba550f3ea76ea9970bd6251fc7783a420d8b51cd9439155ec45d5634677c281154bbdf99fe44051dcec322053ca69ea88297c", "wx": "644cc54e84467213fafe2a4451dba550f3ea76ea9970bd6251fc7783a420d8b5", "wy": "1cd9439155ec45d5634677c281154bbdf99fe44051dcec322053ca69ea88297c"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004644cc54e84467213fafe2a4451dba550f3ea76ea9970bd6251fc7783a420d8b51cd9439155ec45d5634677c281154bbdf99fe44051dcec322053ca69ea88297c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEZEzFToRGchP6/ipEUdulUPPqduqZcL1i\nUfx3g6Qg2LUc2UORVexF1WNGd8KBFUu9+Z/kQFHc7DIgU8pp6ogpfA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 186, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc3e9a7582886089c62fb840cf3b83061cd1cff3ae4341808bb5bdee6191174177", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "ChHUIVS9LeEMqSMh-2s-Y47otaf7T7X1AbRFFc9g6Mk", "y": "BsyquHSM047Oc93Jdbwwfn3hcjV-FM2WqUuzRh0y1Q4"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040a11d42154bd2de10ca92321fb6b3e638ee8b5a7fb4fb5f501b44515cf60e8c906ccaab8748cd38ece73ddc975bc307e7de172357e14cd96a94bb3461d32d50e", "wx": "0a11d42154bd2de10ca92321fb6b3e638ee8b5a7fb4fb5f501b44515cf60e8c9", "wy": "06ccaab8748cd38ece73ddc975bc307e7de172357e14cd96a94bb3461d32d50e"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200040a11d42154bd2de10ca92321fb6b3e638ee8b5a7fb4fb5f501b44515cf60e8c906ccaab8748cd38ece73ddc975bc307e7de172357e14cd96a94bb3461d32d50e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEChHUIVS9LeEMqSMh+2s+Y47otaf7T7X1\nAbRFFc9g6MkGzKq4dIzTjs5z3cl1vDB+feFyNX4UzZapS7NGHTLVDg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 187, "comment": "r and s^-1 have a large Hamming weight", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc24238e70b431b1a64efdf9032669939d4b77f249503fc6905feb7540dea3e6d2", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "n6LDK7NJhGrLWvFOHGes_diWPtJRxLV4PK1LzdD9UF0", "y": "b3JJNyF9HlSDkgQFzxsgIAeXUhxGSiNV_d5TBvKp5Eg"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "049fa2c32bb349846acb5af14e1c67acfdd8963ed251c4b5783cad4bcdd0fd505d6f724937217d1e5483920405cf1b20200797521c464a2355fdde5306f2a9e448", "wx": "009fa2c32bb349846acb5af14e1c67acfdd8963ed251c4b5783cad4bcdd0fd505d", "wy": "6f724937217d1e5483920405cf1b20200797521c464a2355fdde5306f2a9e448"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200049fa2c32bb349846acb5af14e1c67acfdd8963ed251c4b5783cad4bcdd0fd505d6f724937217d1e5483920405cf1b20200797521c464a2355fdde5306f2a9e448", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEn6LDK7NJhGrLWvFOHGes/diWPtJRxLV4\nPK1LzdD9UF1vckk3IX0eVIOSBAXPGyAgB5dSHEZKI1X93lMG8qnkSA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 188, "comment": "small r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000001", "result": "valid", "flags": []}, {"tcId": 189, "comment": "incorrect size of signature", "msg": "313233343030", "sig": "0101", "result": "acceptable", "flags": ["SigSize"]}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "YOrOlQASAc9Mg7WA-2mLtqv0RuXFb_lF61dpsaR3tVA", "y": "afU1Snf-LWAVKPEmyaaFje7dueXsQINW0F7VyA1iuOE"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0460eace95001201cf4c83b580fb698bb6abf446e5c56ff945eb5769b1a477b55069f5354a77fe2d601528f126c9a6858deeddb9e5ec408356d05ed5c80d62b8e1", "wx": "60eace95001201cf4c83b580fb698bb6abf446e5c56ff945eb5769b1a477b550", "wy": "69f5354a77fe2d601528f126c9a6858deeddb9e5ec408356d05ed5c80d62b8e1"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000460eace95001201cf4c83b580fb698bb6abf446e5c56ff945eb5769b1a477b55069f5354a77fe2d601528f126c9a6858deeddb9e5ec408356d05ed5c80d62b8e1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEYOrOlQASAc9Mg7WA+2mLtqv0RuXFb/lF\n61dpsaR3tVBp9TVKd/4tYBUo8SbJpoWN7t255exAg1bQXtXIDWK44Q==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 190, "comment": "small r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000002", "result": "valid", "flags": []}, {"tcId": 191, "comment": "incorrect size of signature", "msg": "313233343030", "sig": "0102", "result": "acceptable", "flags": ["SigSize"]}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "8aV9k0aEIxCXXtNWZypIoGpwte-8DCMofJuZUuyVWzM", "y": "AJGu4SJOzWl5GFbFIbEt8XK0WlziR-bcrKc0loQnjyM"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f1a57d9346842310975ed356672a48a06a70b5efbc0c23287c9b9952ec955b330091aee1224ecd69791856c521b12df172b45a5ce247e6dcaca7349684278f23", "wx": "00f1a57d9346842310975ed356672a48a06a70b5efbc0c23287c9b9952ec955b33", "wy": "0091aee1224ecd69791856c521b12df172b45a5ce247e6dcaca7349684278f23"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004f1a57d9346842310975ed356672a48a06a70b5efbc0c23287c9b9952ec955b330091aee1224ecd69791856c521b12df172b45a5ce247e6dcaca7349684278f23", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE8aV9k0aEIxCXXtNWZypIoGpwte+8DCMo\nfJuZUuyVWzMAka7hIk7NaXkYVsUhsS3xcrRaXOJH5tyspzSWhCePIw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 192, "comment": "small r and s", "msg": "313233343030", "sig": "00000000000000000000000000000000000000000000000000000000000000010000000000000000000000000000000000000000000000000000000000000003", "result": "valid", "flags": []}, {"tcId": 193, "comment": "incorrect size of signature", "msg": "313233343030", "sig": "0103", "result": "acceptable", "flags": ["SigSize"]}, {"tcId": 194, "comment": "r is larger than n", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03641420000000000000000000000000000000000000000000000000000000000000003", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "lopJPyecD46p8kRuNh7luXVwOdV6gAPm_XMdTcai0so", "y": "Z4TFSE_nl8gwqkmnLPhTdVIyKDk7cwsgsEoZIDKvTSk"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04968a493f279c0f8ea9f2446e361ee5b9757039d57a8003e6fd731d4dc6a2d2ca6784c5484fe797c830aa49a72cf85375523228393b730b20b04a192032af4d29", "wx": "00968a493f279c0f8ea9f2446e361ee5b9757039d57a8003e6fd731d4dc6a2d2ca", "wy": "6784c5484fe797c830aa49a72cf85375523228393b730b20b04a192032af4d29"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004968a493f279c0f8ea9f2446e361ee5b9757039d57a8003e6fd731d4dc6a2d2ca6784c5484fe797c830aa49a72cf85375523228393b730b20b04a192032af4d29", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAElopJPyecD46p8kRuNh7luXVwOdV6gAPm\n/XMdTcai0spnhMVIT+eXyDCqSacs+FN1UjIoOTtzCyCwShkgMq9NKQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 195, "comment": "s is larger than n", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000001fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd04917c8", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "s8f73x10cve9V4dXdiyOvJIv8GOwrpw6qc2BYAq-p2w", "y": "A47rOFK4NsBkn9gv5dHQLD0NuzD7zX_kGGbrw72SfGk"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b3c7fbdf1d7472f7bd578757762c8ebc922ff063b0ae9c3aa9cd81600abea76c038eeb3852b836c0649fd82fe5d1d02c3d0dbb30fbcd7fe41866ebc3bd927c69", "wx": "00b3c7fbdf1d7472f7bd578757762c8ebc922ff063b0ae9c3aa9cd81600abea76c", "wy": "038eeb3852b836c0649fd82fe5d1d02c3d0dbb30fbcd7fe41866ebc3bd927c69"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004b3c7fbdf1d7472f7bd578757762c8ebc922ff063b0ae9c3aa9cd81600abea76c038eeb3852b836c0649fd82fe5d1d02c3d0dbb30fbcd7fe41866ebc3bd927c69", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEs8f73x10cve9V4dXdiyOvJIv8GOwrpw6\nqc2BYAq+p2wDjus4Urg2wGSf2C/l0dAsPQ27MPvNf+QYZuvDvZJ8aQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 196, "comment": "small r and s^-1", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000101c58b162c58b162c58b162c58b162c58a1b242973853e16db75c8a1a71da4d39d", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "dZ_dGmTAABiLh-sN3SkaUDWPyisKW5LwJ1c4RdxAsno", "y": "EuwbKJLvRnAPE8_464j0AHbMgRR4sAj1qr7kp0tFRvE"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04759fdd1a64c000188b87eb0ddd291a50358fca2b0a5b92f027573845dc40b27a12ec1b2892ef46700f13cff8eb88f40076cc811478b008f5aabee4a74b4546f1", "wx": "759fdd1a64c000188b87eb0ddd291a50358fca2b0a5b92f027573845dc40b27a", "wy": "12ec1b2892ef46700f13cff8eb88f40076cc811478b008f5aabee4a74b4546f1"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004759fdd1a64c000188b87eb0ddd291a50358fca2b0a5b92f027573845dc40b27a12ec1b2892ef46700f13cff8eb88f40076cc811478b008f5aabee4a74b4546f1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEdZ/dGmTAABiLh+sN3SkaUDWPyisKW5Lw\nJ1c4RdxAsnoS7Bsoku9GcA8Tz/jriPQAdsyBFHiwCPWqvuSnS0VG8Q==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 197, "comment": "smallish r and s^-1", "msg": "313233343030", "sig": "000000000000000000000000000000000000000000000000002d9b4d347952ccfcbc5103d0da267477d1791461cf2aa44bf9d43198f79507bd8779d69a13108e", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "SIKCWoktMCZyZOMA6GirXUsP_J7zwstukNYdI42u2FY", "y": "5Mgkihies22DdA9ZKMuAL7nFC1oYyRljRKDCy3RBZCM"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044882825a892d30267264e300e868ab5d4b0ffc9ef3c2cb6e90d61d238daed856e4c8248a189eb36d83740f5928cb802fb9c50b5a18c9196344a0c2cb74416423", "wx": "4882825a892d30267264e300e868ab5d4b0ffc9ef3c2cb6e90d61d238daed856", "wy": "00e4c8248a189eb36d83740f5928cb802fb9c50b5a18c9196344a0c2cb74416423"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200044882825a892d30267264e300e868ab5d4b0ffc9ef3c2cb6e90d61d238daed856e4c8248a189eb36d83740f5928cb802fb9c50b5a18c9196344a0c2cb74416423", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAESIKCWoktMCZyZOMA6GirXUsP/J7zwstu\nkNYdI42u2FbkyCSKGJ6zbYN0D1koy4AvucULWhjJGWNEoMLLdEFkIw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 198, "comment": "100-bit r and small s^-1", "msg": "313233343030", "sig": "000000000000000000000000000000000000001033e67e37b32b445580bf4efc906f906f906f906f906f906f906f906ed8e426f7b1968c35a204236a579723d2", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "xNGx_fJ0z4PzOVpwo2yU98UfGjHplRS07xC6EwR1bK8", "y": "Tq9DWyDddtbvRHhpUD2pso8OoI7fKHQk1EqgSyVMFzY"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04c4d1b1fdf274cf83f3395a70a36c94f7c51f1a31e99514b4ef10ba1304756caf4eaf435b20dd76d6ef447869503da9b28f0ea08edf287424d44aa04b254c1736", "wx": "00c4d1b1fdf274cf83f3395a70a36c94f7c51f1a31e99514b4ef10ba1304756caf", "wy": "4eaf435b20dd76d6ef447869503da9b28f0ea08edf287424d44aa04b254c1736"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004c4d1b1fdf274cf83f3395a70a36c94f7c51f1a31e99514b4ef10ba1304756caf4eaf435b20dd76d6ef447869503da9b28f0ea08edf287424d44aa04b254c1736", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAExNGx/fJ0z4PzOVpwo2yU98UfGjHplRS0\n7xC6EwR1bK9Or0NbIN121u9EeGlQPamyjw6gjt8odCTUSqBLJUwXNg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 199, "comment": "small r and 100 bit s^-1", "msg": "313233343030", "sig": "0000000000000000000000000000000000000000000000000000000000000101783266e90f43dafe5cd9b3b0be86de22f9de83677d0f50713a468ec72fcf5d57", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "M3bfc3bV5lHUW47C5f-diRxv3W27tSsEbmtaxMn6zt8", "y": "ds8n-fy2VAOx9YWi2v4mtD69YiuszeaZ2Byb6Y359N8"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043376df7376d5e651d45b8ec2e5ff9d891c6fdd6dbbb52b046e6b5ac4c9facedf76cf27f9fcb65403b1f585a2dafe26b43ebd622baccde699d81c9be98df9f4df", "wx": "3376df7376d5e651d45b8ec2e5ff9d891c6fdd6dbbb52b046e6b5ac4c9facedf", "wy": "76cf27f9fcb65403b1f585a2dafe26b43ebd622baccde699d81c9be98df9f4df"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200043376df7376d5e651d45b8ec2e5ff9d891c6fdd6dbbb52b046e6b5ac4c9facedf76cf27f9fcb65403b1f585a2dafe26b43ebd622baccde699d81c9be98df9f4df", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEM3bfc3bV5lHUW47C5f+diRxv3W27tSsE\nbmtaxMn6zt92zyf5/LZUA7H1haLa/ia0Pr1iK6zN5pnYHJvpjfn03w==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 200, "comment": "100-bit r and s^-1", "msg": "313233343030", "sig": "00000000000000000000000000000000000000062522bbd3ecbe7c39e93e7c26783266e90f43dafe5cd9b3b0be86de22f9de83677d0f50713a468ec72fcf5d57", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "UHf90gL9tBlLBUkbbAU__4dgaXUx_FInh56cvsMwlYU", "y": "0LXP-z4P37HAbm0RoRgnUnMM_kOfek-KSbnCkk9J7BQ"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "045077fdd202fdb4194b05491b6c053fff8760697531fc5227879e9cbec3309585d0b5cffb3e0fdfb1c06e6d11a1182752730cfe439f7a4f8a49b9c2924f49ec14", "wx": "5077fdd202fdb4194b05491b6c053fff8760697531fc5227879e9cbec3309585", "wy": "00d0b5cffb3e0fdfb1c06e6d11a1182752730cfe439f7a4f8a49b9c2924f49ec14"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200045077fdd202fdb4194b05491b6c053fff8760697531fc5227879e9cbec3309585d0b5cffb3e0fdfb1c06e6d11a1182752730cfe439f7a4f8a49b9c2924f49ec14", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEUHf90gL9tBlLBUkbbAU//4dgaXUx/FIn\nh56cvsMwlYXQtc/7Pg/fscBubRGhGCdScwz+Q596T4pJucKST0nsFA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 201, "comment": "r and s^-1 are close to n", "msg": "313233343030", "sig": "fffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd03640c155555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "Gx93O0ctrF4a35TmnYZbQE0sySz_e7Zs8hl5ePbEXQg", "y": "qXJXkcXzN4eXep3fppKWvpmKloxR7H8cVEd5O8VihrM"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041b1f773b472dac5e1adf94e69d865b404d2cc92cff7bb66cf2197978f6c45d08a9725791c5f33787977a9ddfa69296be998a968c51ec7f1c5447793bc56286b3", "wx": "1b1f773b472dac5e1adf94e69d865b404d2cc92cff7bb66cf2197978f6c45d08", "wy": "00a9725791c5f33787977a9ddfa69296be998a968c51ec7f1c5447793bc56286b3"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200041b1f773b472dac5e1adf94e69d865b404d2cc92cff7bb66cf2197978f6c45d08a9725791c5f33787977a9ddfa69296be998a968c51ec7f1c5447793bc56286b3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEGx93O0ctrF4a35TmnYZbQE0sySz/e7Zs\n8hl5ePbEXQipcleRxfM3h5d6nd+mkpa+mYqWjFHsfxxUR3k7xWKGsw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 202, "comment": "s == 1", "msg": "313233343030", "sig": "55555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c10000000000000000000000000000000000000000000000000000000000000001", "result": "valid", "flags": []}, {"tcId": 203, "comment": "s == 0", "msg": "313233343030", "sig": "55555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c10000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "LyC8IjK0up11_qapK8gn2Rxaj1yIf04wTXZla6FZmeo", "y": "X4MkLvvVfdFtvT3gkVvbLd7CAdL3SbE_wiwiOiZE3Nw"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "042f20bc2232b4ba9d75fea6a92bc827d91c5a8f5c887f4e304d76656ba15999ea5f83242efbd57dd16dbd3de0915bdb2ddec201d2f749b13fc22c223a2644dcdc", "wx": "2f20bc2232b4ba9d75fea6a92bc827d91c5a8f5c887f4e304d76656ba15999ea", "wy": "5f83242efbd57dd16dbd3de0915bdb2ddec201d2f749b13fc22c223a2644dcdc"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200042f20bc2232b4ba9d75fea6a92bc827d91c5a8f5c887f4e304d76656ba15999ea5f83242efbd57dd16dbd3de0915bdb2ddec201d2f749b13fc22c223a2644dcdc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAELyC8IjK0up11/qapK8gn2Rxaj1yIf04w\nTXZla6FZmepfgyQu+9V90W29PeCRW9st3sIB0vdJsT/CLCI6JkTc3A==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 204, "comment": "point at infinity during verify", "msg": "313233343030", "sig": "7fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a055555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "ngCc0KGn0MUXZRacRo5i5W_E8_8C6GZsVUg0GaJWADI", "y": "zTbXE6zVBFmP87T1gEakaQ9VC9YO9MgjxcWBxriZMV4"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "049e009cd0a1a7d0c51765169c468e62e56fc4f3ff02e8666c55483419a2560032cd36d713acd504598ff3b4f58046a4690f550bd60ef4c823c5c581c6b899315e", "wx": "009e009cd0a1a7d0c51765169c468e62e56fc4f3ff02e8666c55483419a2560032", "wy": "00cd36d713acd504598ff3b4f58046a4690f550bd60ef4c823c5c581c6b899315e"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200049e009cd0a1a7d0c51765169c468e62e56fc4f3ff02e8666c55483419a2560032cd36d713acd504598ff3b4f58046a4690f550bd60ef4c823c5c581c6b899315e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEngCc0KGn0MUXZRacRo5i5W/E8/8C6GZs\nVUg0GaJWADLNNtcTrNUEWY/ztPWARqRpD1UL1g70yCPFxYHGuJkxXg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 205, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "7fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a07fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a0", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "r1im7MglS5uDGuBEHBOZCALD1owwHUNjTHHxl0wJ5wQ", "y": "2SBhLYLzL8pDbFxQl1BScUlIdUAnMdA9upQrNVMGx4M"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04af58a6ecc8254b9b831ae0441c13990802c3d68c301d43634c71f1974c09e704d920612d82f32fca436c5c5097505271494875402731d03dba942b355306c783", "wx": "00af58a6ecc8254b9b831ae0441c13990802c3d68c301d43634c71f1974c09e704", "wy": "00d920612d82f32fca436c5c5097505271494875402731d03dba942b355306c783"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004af58a6ecc8254b9b831ae0441c13990802c3d68c301d43634c71f1974c09e704d920612d82f32fca436c5c5097505271494875402731d03dba942b355306c783", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEr1im7MglS5uDGuBEHBOZCALD1owwHUNj\nTHHxl0wJ5wTZIGEtgvMvykNsXFCXUFJxSUh1QCcx0D26lCs1UwbHgw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 206, "comment": "edge case for signature malleability", "msg": "313233343030", "sig": "7fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a07fffffffffffffffffffffffffffffff5d576e7357a4501ddfe92f46681b20a1", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "SnIXyryVtJbz9OEtVOne92UbhmvmnTaVzXetLjo_E9E", "y": "0PpxvyHSwAsf9Mx2tTqcXCqKi2tMLsiLme5TesYmKz0"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044a7217cabc95b496f3f4e12d54e9def7651b866be69d3695cd77ad2e3a3f13d1d0fa71bf21d2c00b1ff4cc76b53a9c5c2a8a8b6b4c2ec88b99ee537ac6262b3d", "wx": "4a7217cabc95b496f3f4e12d54e9def7651b866be69d3695cd77ad2e3a3f13d1", "wy": "00d0fa71bf21d2c00b1ff4cc76b53a9c5c2a8a8b6b4c2ec88b99ee537ac6262b3d"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200044a7217cabc95b496f3f4e12d54e9def7651b866be69d3695cd77ad2e3a3f13d1d0fa71bf21d2c00b1ff4cc76b53a9c5c2a8a8b6b4c2ec88b99ee537ac6262b3d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAESnIXyryVtJbz9OEtVOne92UbhmvmnTaV\nzXetLjo/E9HQ+nG/IdLACx/0zHa1OpxcKoqLa0wuyIuZ7lN6xiYrPQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 207, "comment": "u1 == 1", "msg": "313233343030", "sig": "55555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b843f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b023210281", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "AKQuJ3zmV_s90H4TWjy5sKdaML2LZJEWBu5oNx5WEkQ", "y": "Z88i4mpwCQRbc_8ZzXmFHM6q2a5y7y0EPXU2UkW--gY"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0400a42e277ce657fb3dd07e135a3cb9b0a75a30bd8b64911606ee68371e56124467cf22e26a7009045b73ff19cd79851cceaad9ae72ef2d043d75365245befa06", "wx": "00a42e277ce657fb3dd07e135a3cb9b0a75a30bd8b64911606ee68371e561244", "wy": "67cf22e26a7009045b73ff19cd79851cceaad9ae72ef2d043d75365245befa06"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000400a42e277ce657fb3dd07e135a3cb9b0a75a30bd8b64911606ee68371e56124467cf22e26a7009045b73ff19cd79851cceaad9ae72ef2d043d75365245befa06", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEAKQuJ3zmV/s90H4TWjy5sKdaML2LZJEW\nBu5oNx5WEkRnzyLianAJBFtz/xnNeYUczqrZrnLvLQQ9dTZSRb76Bg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 208, "comment": "u1 == n - 1", "msg": "313233343030", "sig": "55555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8bc07ff041506dc73a75086a43252fb4270e157da75fb6cb92a9f07dcad153ec0", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "hSC5UC-aXtdT8JpSgsrXIfXr-z20FC1mfGJ5hp52vPE", "y": "Z46bvQSlFGCvxAo-DLew-Lit2JspeXWKWh_-tFhO5J4"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048520b9502f9a5ed753f09a5282cad721f5ebfb3db4142d667c6279869e76bcf1678e9bbd04a51460afc40a3e0cb7b0f8b8add89b2979758a5a1ffeb4584ee49e", "wx": "008520b9502f9a5ed753f09a5282cad721f5ebfb3db4142d667c6279869e76bcf1", "wy": "678e9bbd04a51460afc40a3e0cb7b0f8b8add89b2979758a5a1ffeb4584ee49e"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200048520b9502f9a5ed753f09a5282cad721f5ebfb3db4142d667c6279869e76bcf1678e9bbd04a51460afc40a3e0cb7b0f8b8add89b2979758a5a1ffeb4584ee49e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEhSC5UC+aXtdT8JpSgsrXIfXr+z20FC1m\nfGJ5hp52vPFnjpu9BKUUYK/ECj4Mt7D4uK3Ymyl5dYpaH/60WE7kng==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 209, "comment": "u2 == 1", "msg": "313233343030", "sig": "55555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b855555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "td7KD-ApaQWqwn42BKlaCi7L7p_EU9LhFkYylkRU0Mk", "y": "T55OhaFD7md9QJGccQFOjKv02dt0Qv5LlimPmfkMpn8"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b5deca0fe0296905aac27e3604a95a0a2ecbee9fc453d2e1164632964454d0c94f9e4e85a143ee677d40919c71014e8cabf4d9db7442fe4b96298f99f90ca67f", "wx": "00b5deca0fe0296905aac27e3604a95a0a2ecbee9fc453d2e1164632964454d0c9", "wy": "4f9e4e85a143ee677d40919c71014e8cabf4d9db7442fe4b96298f99f90ca67f"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004b5deca0fe0296905aac27e3604a95a0a2ecbee9fc453d2e1164632964454d0c94f9e4e85a143ee677d40919c71014e8cabf4d9db7442fe4b96298f99f90ca67f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEtd7KD+ApaQWqwn42BKlaCi7L7p/EU9Lh\nFkYylkRU0MlPnk6FoUPuZ31AkZxxAU6Mq/TZ23RC/kuWKY+Z+Qymfw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 210, "comment": "u2 == n - 1", "msg": "313233343030", "sig": "55555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215b8aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa9d1c9e899ca306ad27fe1945de0242b89", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "XcsnZ9yFHiCRHte-Od2HuoHHptECVd-4JfJBSG-YrhA", "y": "-Knvc2s-EdfVSg4IaQL7R3JG7IxX3mXTNlcLZfZeDYM"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "045dcb2767dc851e20911ed7be39dd87ba81c7a6d10255dfb825f241486f98ae10f8a9ef736b3e11d7d54a0e086902fb477246ec8c57de65d336570b65f65e0d83", "wx": "5dcb2767dc851e20911ed7be39dd87ba81c7a6d10255dfb825f241486f98ae10", "wy": "00f8a9ef736b3e11d7d54a0e086902fb477246ec8c57de65d336570b65f65e0d83"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200045dcb2767dc851e20911ed7be39dd87ba81c7a6d10255dfb825f241486f98ae10f8a9ef736b3e11d7d54a0e086902fb477246ec8c57de65d336570b65f65e0d83", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEXcsnZ9yFHiCRHte+Od2HuoHHptECVd+4\nJfJBSG+YrhD4qe9zaz4R19VKDghpAvtHckbsjFfeZdM2Vwtl9l4Ngw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 211, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc6bfd55a94e530bd972e52873ef39ac3e56d420a64d874694c701e714511d1696", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "yOFEyFOn4ab1u6vn75HvWxUhEyENRP1Y08thhRhOFoo", "y": "rED7NhiIIZP8bRE3YOR2Rl30kGdICgp8_-aGUVszkag"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04c8e144c853a7e1a6f5bbabe7ef91ef5b152113210d44fd58d3cb6185184e168aac40fb3618882193fc6d113760e476465df49067480a0a7cffe686515b3391a8", "wx": "00c8e144c853a7e1a6f5bbabe7ef91ef5b152113210d44fd58d3cb6185184e168a", "wy": "00ac40fb3618882193fc6d113760e476465df49067480a0a7cffe686515b3391a8"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004c8e144c853a7e1a6f5bbabe7ef91ef5b152113210d44fd58d3cb6185184e168aac40fb3618882193fc6d113760e476465df49067480a0a7cffe686515b3391a8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEyOFEyFOn4ab1u6vn75HvWxUhEyENRP1Y\n08thhRhOFoqsQPs2GIghk/xtETdg5HZGXfSQZ0gKCnz/5oZRWzORqA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 212, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcb494bd67c209a5adb1c9a09337e2629b03f8a924be53c542478e5864ed2622ad", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "f_4YWiPrW3NnBDh-Y1diimWYSYV3O0Rzz571YLP6UFE", "y": "R0DLEhfxrStZENf3SQZgKx-VULPRHP9wWzWMO8v3LD0"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "047ffe185a23eb5b736704387e6357628a65984985773b4473cf9ef560b3fa50514740cb1217f1ad2b5910d7f74906602b1f9550b3d11cff705b358c3bcbf72c3d", "wx": "7ffe185a23eb5b736704387e6357628a65984985773b4473cf9ef560b3fa5051", "wy": "4740cb1217f1ad2b5910d7f74906602b1f9550b3d11cff705b358c3bcbf72c3d"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200047ffe185a23eb5b736704387e6357628a65984985773b4473cf9ef560b3fa50514740cb1217f1ad2b5910d7f74906602b1f9550b3d11cff705b358c3bcbf72c3d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEf/4YWiPrW3NnBDh+Y1diimWYSYV3O0Rz\nz571YLP6UFFHQMsSF/GtK1kQ1/dJBmArH5VQs9Ec/3BbNYw7y/csPQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 213, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcaad4e2b69a9f378dae7873b40f7c15cb4565fcc8cbc0ec55b0bd3fe9d8626b2c", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "ioWCJhVeNNu35drH8TEnyBxs6MnYkZGMZ8hzjX5LRuk", "y": "bBOG6ExhIxLeU-nkrzTZvVf5PZoGuFW24LBq1BN_9Xw"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048a858226155e34dbb7e5dac7f13127c81c6ce8c9d891918c67c8738d7e4b46e96c1386e84c612312de53e9e4af34d9bd57f93d9a06b855b6e0b06ad4137ff57c", "wx": "008a858226155e34dbb7e5dac7f13127c81c6ce8c9d891918c67c8738d7e4b46e9", "wy": "6c1386e84c612312de53e9e4af34d9bd57f93d9a06b855b6e0b06ad4137ff57c"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200048a858226155e34dbb7e5dac7f13127c81c6ce8c9d891918c67c8738d7e4b46e96c1386e84c612312de53e9e4af34d9bd57f93d9a06b855b6e0b06ad4137ff57c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEioWCJhVeNNu35drH8TEnyBxs6MnYkZGM\nZ8hzjX5LRulsE4boTGEjEt5T6eSvNNm9V/k9mga4VbbgsGrUE3/1fA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 214, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc73fec4995e9d3140bc07ff041506dc7313e95389fb599d22f24039392a4014d3", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "rsC-cpsC8mbFQtE5o-BBEMkz6OyhAI6NujjXXn-Pq1M", "y": "LNaI2SS0VoSL1cZRRExnqTmf37W1uWkxYsFyi_rcEEY"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04aec0be729b02f266c542d139a3e04110c933e8eca1008e8dba38d75e7f8fab532cd688d924b456848bd5c651444c67a9399fdfb5b5b9693162c1728bfadc1046", "wx": "00aec0be729b02f266c542d139a3e04110c933e8eca1008e8dba38d75e7f8fab53", "wy": "2cd688d924b456848bd5c651444c67a9399fdfb5b5b9693162c1728bfadc1046"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004aec0be729b02f266c542d139a3e04110c933e8eca1008e8dba38d75e7f8fab532cd688d924b456848bd5c651444c67a9399fdfb5b5b9693162c1728bfadc1046", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAErsC+cpsC8mbFQtE5o+BBEMkz6OyhAI6N\nujjXXn+Pq1Ms1ojZJLRWhIvVxlFETGepOZ/ftbW5aTFiwXKL+twQRg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 215, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcec4995e9d3140bc07ff041506dc73a73dc25f4257a911e310e38744b482a5a01", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "Ae1LX5QfRDsxp-JYPqFlVR0YFbVHQN6xLp_e_zLiMGE", "y": "hDhcpEjMXdcROb2jq0LQtuRNcZ5S__ZNlxh276kQn7I"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0401ed4b5f941f443b31a7e2583ea165551d1815b54740deb12e9fdeff32e2306184385ca448cc5dd71139bda3ab42d0b6e44d719e52fff64d971876efa9109fb2", "wx": "01ed4b5f941f443b31a7e2583ea165551d1815b54740deb12e9fdeff32e23061", "wy": "0084385ca448cc5dd71139bda3ab42d0b6e44d719e52fff64d971876efa9109fb2"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000401ed4b5f941f443b31a7e2583ea165551d1815b54740deb12e9fdeff32e2306184385ca448cc5dd71139bda3ab42d0b6e44d719e52fff64d971876efa9109fb2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEAe1LX5QfRDsxp+JYPqFlVR0YFbVHQN6x\nLp/e/zLiMGGEOFykSMxd1xE5vaOrQtC25E1xnlL/9k2XGHbvqRCfsg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 216, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcd8932bd3a6281780ffe082a0db8e74e8fd9d0b6445d99c265c9e8a09c01e72c1", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "WcQny2Ul6rURoG4D4AzyqrSrxYfCYBU0M4pQvCVwGnA", "y": "Pk6ziLRTy66llNa1wUpRmsP9p3DFNYC-78aPCSANVf8"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0459c427cb6525eab511a06e03e00cf2aab4abc587c2601534338a50bc25701a703e4eb388b453cbaea594d6b5c14a519ac3fda770c53580beefc68f09200d55ff", "wx": "59c427cb6525eab511a06e03e00cf2aab4abc587c2601534338a50bc25701a70", "wy": "3e4eb388b453cbaea594d6b5c14a519ac3fda770c53580beefc68f09200d55ff"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000459c427cb6525eab511a06e03e00cf2aab4abc587c2601534338a50bc25701a703e4eb388b453cbaea594d6b5c14a519ac3fda770c53580beefc68f09200d55ff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEWcQny2Ul6rURoG4D4AzyqrSrxYfCYBU0\nM4pQvCVwGnA+TrOItFPLrqWU1rXBSlGaw/2ncMU1gL7vxo8JIA1V/w==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 217, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc5e9d3140bc07ff041506dc73a75086a3ba176f06c2b6e37363e2ce1c141f3c27", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "BKy7zSPPLsgZ_Sl6sstUB-3mMZUYZRo5HpQcyAA1aDM", "y": "EgbdAN8jvIzguFoBjEs06cO0G071nHFJL6YtE0dy-X4"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0404acbbcd23cf2ec819fd297ab2cb5407ede6319518651a391e941cc8003568331206dd00df23bc8ce0b85a018c4b34e9c3b41b4ef59c71492fa62d134772f97e", "wx": "04acbbcd23cf2ec819fd297ab2cb5407ede6319518651a391e941cc800356833", "wy": "1206dd00df23bc8ce0b85a018c4b34e9c3b41b4ef59c71492fa62d134772f97e"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000404acbbcd23cf2ec819fd297ab2cb5407ede6319518651a391e941cc8003568331206dd00df23bc8ce0b85a018c4b34e9c3b41b4ef59c71492fa62d134772f97e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEBKy7zSPPLsgZ/Sl6sstUB+3mMZUYZRo5\nHpQcyAA1aDMSBt0A3yO8jOC4WgGMSzTpw7QbTvWccUkvpi0TR3L5fg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 218, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcfd6dc71a71f1d50d1bbd976af4357be4dd2fe850707c431fd376e53d176c6b62", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "zKy8Ym_W6jEXWBXP-VjKFjcyOHfTvfCYlrUnv04lXoU", "y": "cfiifmMJvZubFdeNUnABKtLtFaf__gJPwOymP7asL40"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04ccacbc626fd6ea31175815cff958ca1637323877d3bdf09896b527bf4e255e8571f8a27e6309bd9b9b15d78d5270012ad2ed15a7fffe024fc0eca63fb6ac2f8d", "wx": "00ccacbc626fd6ea31175815cff958ca1637323877d3bdf09896b527bf4e255e85", "wy": "71f8a27e6309bd9b9b15d78d5270012ad2ed15a7fffe024fc0eca63fb6ac2f8d"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004ccacbc626fd6ea31175815cff958ca1637323877d3bdf09896b527bf4e255e8571f8a27e6309bd9b9b15d78d5270012ad2ed15a7fffe024fc0eca63fb6ac2f8d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEzKy8Ym/W6jEXWBXP+VjKFjcyOHfTvfCY\nlrUnv04lXoVx+KJ+Ywm9m5sV141ScAEq0u0Vp//+Ak/A7KY/tqwvjQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 219, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc7ee75ad2a5801c54722eb7d95ba67febcfc399b956b7b682fe89638de3690bf1", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "zMMLZcrT3R15O224D1ey4SN5c-QmTD2bvCVR7Gigt74", "y": "df9tH09TWhMapXP24taRLDlxVJM3UEF9KORlJDklkt4"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04ccc30b65cad3dd1d793b6db80f57b2e1237973e4264c3d9bbc2551ec68a0b7be75ff6d1f4f535a131aa573f6e2d6912c397154933750417d28e46524392592de", "wx": "00ccc30b65cad3dd1d793b6db80f57b2e1237973e4264c3d9bbc2551ec68a0b7be", "wy": "75ff6d1f4f535a131aa573f6e2d6912c397154933750417d28e46524392592de"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004ccc30b65cad3dd1d793b6db80f57b2e1237973e4264c3d9bbc2551ec68a0b7be75ff6d1f4f535a131aa573f6e2d6912c397154933750417d28e46524392592de", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEzMMLZcrT3R15O224D1ey4SN5c+QmTD2b\nvCVR7Gigt751/20fT1NaExqlc/bi1pEsOXFUkzdQQX0o5GUkOSWS3g==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 220, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcf533776f11c47ed0a7b5e25ace7a3b921866733c7454b2c678b8943dfb4cf232", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "zJNJrKDL0LLfDe7NiO055tjHw9e0Iv1dkkMbr3Il_MA", "y": "7UlL5pjW84UL4nfCaHkkAPOWAlz6lc9WAYvLwkPlEus"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04cc9349aca0cbd0b2df0deecd88ed39e6d8c7c3d7b422fd5d92431baf7225fcc0ed494be698d6f3850be277c268792400f396025cfa95cf56018bcbc243e512eb", "wx": "00cc9349aca0cbd0b2df0deecd88ed39e6d8c7c3d7b422fd5d92431baf7225fcc0", "wy": "00ed494be698d6f3850be277c268792400f396025cfa95cf56018bcbc243e512eb"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004cc9349aca0cbd0b2df0deecd88ed39e6d8c7c3d7b422fd5d92431baf7225fcc0ed494be698d6f3850be277c268792400f396025cfa95cf56018bcbc243e512eb", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEzJNJrKDL0LLfDe7NiO055tjHw9e0Iv1d\nkkMbr3Il/MDtSUvmmNbzhQvid8JoeSQA85YCXPqVz1YBi8vCQ+US6w==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 221, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffce8dbffee01807d75f9aa52c295e15b15f138439e7a195a40709b1abf511dbc6a", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "AA58MNLyWffBPxlDIOQ5BdDq1yd-KD6JGEN8EPnQUrA", "y": "Kzm2bbuisc9drBtB0t7G8fsIvdFNQg1wOYb2Ou3rXEc"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04000e7c30d2f259f7c13f194320e43905d0ead7277e283e8918437c10f9d052b02b39b66dbba2b1cf5dac1b41d2dec6f1fb08bdd14d420d703986f63aedeb5c47", "wx": "0e7c30d2f259f7c13f194320e43905d0ead7277e283e8918437c10f9d052b0", "wy": "2b39b66dbba2b1cf5dac1b41d2dec6f1fb08bdd14d420d703986f63aedeb5c47"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004000e7c30d2f259f7c13f194320e43905d0ead7277e283e8918437c10f9d052b02b39b66dbba2b1cf5dac1b41d2dec6f1fb08bdd14d420d703986f63aedeb5c47", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEAA58MNLyWffBPxlDIOQ5BdDq1yd+KD6J\nGEN8EPnQUrArObZtu6Kxz12sG0HS3sbx+wi90U1CDXA5hvY67etcRw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 222, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcca01552b58d67a13468d6bc6086329df8f44cc938884fcf15c516b02a7a7b5f6", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "j6KYwArJP3w2iSxSmQBaD2hD-c8Gaf27fW2B4DQYA-0", "y": "TKszzCghstqEn5DvINweuJb8ZxYUQLPFLAseiGJ-UIw"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048fa298c00ac93f7c36892c5299005a0f6843f9cf0669fdbb7d6d81e0341803ed4cab33cc2821b2da849f90ef20dc1eb896fc67161440b3c52c0b1e88627e508c", "wx": "008fa298c00ac93f7c36892c5299005a0f6843f9cf0669fdbb7d6d81e0341803ed", "wy": "4cab33cc2821b2da849f90ef20dc1eb896fc67161440b3c52c0b1e88627e508c"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200048fa298c00ac93f7c36892c5299005a0f6843f9cf0669fdbb7d6d81e0341803ed4cab33cc2821b2da849f90ef20dc1eb896fc67161440b3c52c0b1e88627e508c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEj6KYwArJP3w2iSxSmQBaD2hD+c8Gaf27\nfW2B4DQYA+1MqzPMKCGy2oSfkO8g3B64lvxnFhRAs8UsCx6IYn5QjA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 223, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc9402aa56b1acf4268d1ad78c10c653c063dabc4061c159a6f8d077787f192aab", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "b79gioPjfsJrN9oDPgaYFmgLdwunZvuMRPzgA5YFYvE", "y": "BF8mjMxeCUkhP38vH6V8_q0EYl7DzPycMzWW5IeyBW8"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046fbf608a83e37ec26b37da033e069816680b770ba766fb8c44fce003960562f1045f268ccc5e0949213f7f2f1fa57cfead04625ec3ccfc9c333596e487b2056f", "wx": "6fbf608a83e37ec26b37da033e069816680b770ba766fb8c44fce003960562f1", "wy": "045f268ccc5e0949213f7f2f1fa57cfead04625ec3ccfc9c333596e487b2056f"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046fbf608a83e37ec26b37da033e069816680b770ba766fb8c44fce003960562f1045f268ccc5e0949213f7f2f1fa57cfead04625ec3ccfc9c333596e487b2056f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEb79gioPjfsJrN9oDPgaYFmgLdwunZvuM\nRPzgA5YFYvEEXyaMzF4JSSE/fy8fpXz+rQRiXsPM/JwzNZbkh7IFbw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 224, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc5e03ff820a836e39d3a8435219297da13870abed3afdb65c954f83ee568a9f60", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "xN1UetdQF0F5usi4zidIHFi4E0d3YiChtSraE9ZcgSQ", "y": "-cLvO1tJV89p06E5iRaCNjwEBhDyAPTDGOWapo8pivA"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04c4dd547ad750174179bac8b8ce27481c58b81347776220a1b52ada13d65c8124f9c2ef3b5b4957cf69d3a139891682363c040610f200f4c318e59aa68f298af0", "wx": "00c4dd547ad750174179bac8b8ce27481c58b81347776220a1b52ada13d65c8124", "wy": "00f9c2ef3b5b4957cf69d3a139891682363c040610f200f4c318e59aa68f298af0"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004c4dd547ad750174179bac8b8ce27481c58b81347776220a1b52ada13d65c8124f9c2ef3b5b4957cf69d3a139891682363c040610f200f4c318e59aa68f298af0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAExN1UetdQF0F5usi4zidIHFi4E0d3YiCh\ntSraE9ZcgST5wu87W0lXz2nToTmJFoI2PAQGEPIA9MMY5ZqmjymK8A==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 225, "comment": "edge case for u1", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc556a715b4d4f9bc6d73c39da07be0ae5a2b2fe6465e0762ad85e9ff4ec313596", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "AAVdefsmKGu2KJp5g6KyO_XDDMPXA2O1Wa31VIr5kfg", "y": "yuixsKzjL9dKhu4aZxzDbAUqR5bq4yO-MuAs6aD7Yic"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0400055d79fb26286bb6289a7983a2b23bf5c30cc3d70363b559adf5548af991f8cae8b1b0ace32fd74a86ee1a671cc36c052a4796eae323be32e02ce9a0fb6227", "wx": "055d79fb26286bb6289a7983a2b23bf5c30cc3d70363b559adf5548af991f8", "wy": "00cae8b1b0ace32fd74a86ee1a671cc36c052a4796eae323be32e02ce9a0fb6227"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000400055d79fb26286bb6289a7983a2b23bf5c30cc3d70363b559adf5548af991f8cae8b1b0ace32fd74a86ee1a671cc36c052a4796eae323be32e02ce9a0fb6227", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEAAVdefsmKGu2KJp5g6KyO/XDDMPXA2O1\nWa31VIr5kfjK6LGwrOMv10qG7hpnHMNsBSpHlurjI74y4CzpoPtiJw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 226, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcd55555555555555555555555555555547c74934474db157d2a8c3f088aced62a", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "DbUcdNNOQbq6Z8E6YK9ATugtjxsDhrCWlu4ebqEye4Y", "y": "QTiGxGI_wiKmlQw8Ognz_YZ6Vmv9NF4GsJ7GxcLkoZI"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040db51c74d34e41baba67c13a60af404ee82d8f1b0386b09696ee1e6ea1327b86413886c4623fc222a6950c3c3a09f3fd867a566bfd345e06b09ec6c5c2e4a192", "wx": "0db51c74d34e41baba67c13a60af404ee82d8f1b0386b09696ee1e6ea1327b86", "wy": "413886c4623fc222a6950c3c3a09f3fd867a566bfd345e06b09ec6c5c2e4a192"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200040db51c74d34e41baba67c13a60af404ee82d8f1b0386b09696ee1e6ea1327b86413886c4623fc222a6950c3c3a09f3fd867a566bfd345e06b09ec6c5c2e4a192", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEDbUcdNNOQbq6Z8E6YK9ATugtjxsDhrCW\nlu4ebqEye4ZBOIbEYj/CIqaVDDw6CfP9hnpWa/00XgawnsbFwuShkg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 227, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcc1777c8853938e536213c02464a936000ba1e21c0fc62075d46c624e23b52f31", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "vC97x0yzvH55ewbMPmSb80B9GlW06q3dKNPc-v8sNzc", "y": "ojuzZOFqx5OYwBPOKaIudiwNYGeq79qVhHSq0ZSpLoo"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04bc2f7bc74cb3bc7e797b06cc3e649bf3407d1a55b4eaaddd28d3dcfaff2c3737a23bb364e16ac79398c013ce29a22e762c0d6067aaefda958474aad194a92e8a", "wx": "00bc2f7bc74cb3bc7e797b06cc3e649bf3407d1a55b4eaaddd28d3dcfaff2c3737", "wy": "00a23bb364e16ac79398c013ce29a22e762c0d6067aaefda958474aad194a92e8a"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004bc2f7bc74cb3bc7e797b06cc3e649bf3407d1a55b4eaaddd28d3dcfaff2c3737a23bb364e16ac79398c013ce29a22e762c0d6067aaefda958474aad194a92e8a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEvC97x0yzvH55ewbMPmSb80B9GlW06q3d\nKNPc+v8sNzeiO7Nk4WrHk5jAE84poi52LA1gZ6rv2pWEdKrRlKkuig==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 228, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc30bbb794db588363b40679f6c182a50d3ce9679acdd3ffbe36d7813dacbdc818", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "1-3HxkXv_2r4ghrqW3-Wn1bvbmFYYrCPuj6vARHAb2c", "y": "5H_Q2mFoKtzEBfMpFIvxw1uJy17Fqe0NmKQQ4mGmtBo"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d7edc7c645efff6af8821aea5b7f969f56ef6e615862b08fba3eaf0111c06f67e47fd0da61682adcc405f329148bf1c35b89cb5ec5a9ed0d98a410e261a6b41a", "wx": "00d7edc7c645efff6af8821aea5b7f969f56ef6e615862b08fba3eaf0111c06f67", "wy": "00e47fd0da61682adcc405f329148bf1c35b89cb5ec5a9ed0d98a410e261a6b41a"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004d7edc7c645efff6af8821aea5b7f969f56ef6e615862b08fba3eaf0111c06f67e47fd0da61682adcc405f329148bf1c35b89cb5ec5a9ed0d98a410e261a6b41a", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE1+3HxkXv/2r4ghrqW3+Wn1bvbmFYYrCP\nuj6vARHAb2fkf9DaYWgq3MQF8ykUi/HDW4nLXsWp7Q2YpBDiYaa0Gg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 229, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc2c37fd995622c4fb7fffffffffffffffc7cee745110cb45ab558ed7c90c15a2f", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "a_160Btdz7BN5GQIPTyn71BUUGER35LvAv92kNmm7JM", "y": "BsRp_kxaHgTxFOGTtLsZfeLI41CJA35aICdbz2fZv3M"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046bfd7ad01b5dcfb04de464083d3ca7ef5054506111df92ef02ff7690d9a6ec9306c469fe4c5a1e04f114e193b4bb197de2c8e35089037e5a20275bcf67d9bf73", "wx": "6bfd7ad01b5dcfb04de464083d3ca7ef5054506111df92ef02ff7690d9a6ec93", "wy": "06c469fe4c5a1e04f114e193b4bb197de2c8e35089037e5a20275bcf67d9bf73"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046bfd7ad01b5dcfb04de464083d3ca7ef5054506111df92ef02ff7690d9a6ec9306c469fe4c5a1e04f114e193b4bb197de2c8e35089037e5a20275bcf67d9bf73", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEa/160Btdz7BN5GQIPTyn71BUUGER35Lv\nAv92kNmm7JMGxGn+TFoeBPEU4ZO0uxl94sjjUIkDflogJ1vPZ9m/cw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 230, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc7fd995622c4fb7ffffffffffffffffff5d883ffab5b32652ccdcaa290fccb97d", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "ipB2ySMCHVxe-FiUF267XDp0q6dbOUTJbxfevCFzupk", "y": "5WAdEVvwjTeuEVxNGGvCESe7-yHQYpveJ6Funtcht0A"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "048a9076c923021d5c5ef85894176ebb5c3a74aba75b3944c96f17debc2173ba99e5601d115bf08d37ae115c4d186bc21127bbfb21d0629bde27a16e9ed721b740", "wx": "008a9076c923021d5c5ef85894176ebb5c3a74aba75b3944c96f17debc2173ba99", "wy": "00e5601d115bf08d37ae115c4d186bc21127bbfb21d0629bde27a16e9ed721b740"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200048a9076c923021d5c5ef85894176ebb5c3a74aba75b3944c96f17debc2173ba99e5601d115bf08d37ae115c4d186bc21127bbfb21d0629bde27a16e9ed721b740", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEipB2ySMCHVxe+FiUF267XDp0q6dbOUTJ\nbxfevCFzupnlYB0RW/CNN64RXE0Ya8IRJ7v7IdBim94noW6e1yG3QA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 231, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcffb32ac4589f6ffffffffffffffffffebb107ff56b664ca599b954521f9972fa", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "D-xqheB370JAuYxiqzuT4s68rQrpYX97BHFQTbH0WmU", "y": "JFpf0K16bYVBJe121Hh_d8wZg-yoxrqMAZUjoIjE0PM"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "040fec6a85e077ef4240b98c62ab3b93e2cebcad0ae9617f7b0471504db1f45a65245a5fd0ad7a6d854125ed76d4787f77cc1983eca8c6ba8c019523a088c4d0f3", "wx": "0fec6a85e077ef4240b98c62ab3b93e2cebcad0ae9617f7b0471504db1f45a65", "wy": "245a5fd0ad7a6d854125ed76d4787f77cc1983eca8c6ba8c019523a088c4d0f3"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200040fec6a85e077ef4240b98c62ab3b93e2cebcad0ae9617f7b0471504db1f45a65245a5fd0ad7a6d854125ed76d4787f77cc1983eca8c6ba8c019523a088c4d0f3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAED+xqheB370JAuYxiqzuT4s68rQrpYX97\nBHFQTbH0WmUkWl/QrXpthUEl7XbUeH93zBmD7KjGuowBlSOgiMTQ8w==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 232, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc5622c4fb7fffffffffffffffffffffff928a8f1c7ac7bec1808b9f61c01ec327", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "06uU2HBPtRd03MODitlwMHHghR3pstbKdMzXm4VVgZE", "y": "Tkl5tn83dBnlqdTwMBK351ZWVW8jdW1NvuFFg0yCee8"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d3ab94d8704fb51774dcc3838ad9703071e0851de9b2d6ca74ccd79b855581914e4979b67f377419e5a9d4f03012b7e75656556f23756d4dbee145834c8279ef", "wx": "00d3ab94d8704fb51774dcc3838ad9703071e0851de9b2d6ca74ccd79b85558191", "wy": "4e4979b67f377419e5a9d4f03012b7e75656556f23756d4dbee145834c8279ef"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004d3ab94d8704fb51774dcc3838ad9703071e0851de9b2d6ca74ccd79b855581914e4979b67f377419e5a9d4f03012b7e75656556f23756d4dbee145834c8279ef", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE06uU2HBPtRd03MODitlwMHHghR3pstbK\ndMzXm4VVgZFOSXm2fzd0GeWp1PAwErfnVlZVbyN1bU2+4UWDTIJ57w==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 233, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc44104104104104104104104104104103b87853fd3b7d3f8e175125b4382f25ed", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "SeE81EyLg1Cl6sohgb-W2xILdoveiADzefQ-kZgzPHU", "y": "AwrZ-0sLIzvcEMoNxMITSxi2keRscVHjVzqitiiR5p0"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0449e13cd44c8b8350a5eaca2181bf96db120b768bde8800f379f43e9198333c75030ad9fb4b0b233bdc10ca0dc4c2134b18b691e46c7151e3573aa2b62891e69d", "wx": "49e13cd44c8b8350a5eaca2181bf96db120b768bde8800f379f43e9198333c75", "wy": "030ad9fb4b0b233bdc10ca0dc4c2134b18b691e46c7151e3573aa2b62891e69d"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000449e13cd44c8b8350a5eaca2181bf96db120b768bde8800f379f43e9198333c75030ad9fb4b0b233bdc10ca0dc4c2134b18b691e46c7151e3573aa2b62891e69d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAESeE81EyLg1Cl6sohgb+W2xILdoveiADz\nefQ+kZgzPHUDCtn7SwsjO9wQyg3EwhNLGLaR5GxxUeNXOqK2KJHmnQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 234, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc2739ce739ce739ce739ce739ce739ce705560298d1f2f08dc419ac273a5b54d9", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "RXT9lK0DgoWIywvC1DSELuCT7-Y5AVzBB9HqNxDyES0", "y": "F4bW7x1BHL0a9bXuiEWZPnOPtkUZtDKdBL4h95AqHB0"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044574fd94ad03828588cb0bc2d434842ee093efe639015cc107d1ea3710f2112d1786d6ef1d411cbd1af5b5ee8845993e738fb64519b4329d04be21f7902a1c1d", "wx": "4574fd94ad03828588cb0bc2d434842ee093efe639015cc107d1ea3710f2112d", "wy": "1786d6ef1d411cbd1af5b5ee8845993e738fb64519b4329d04be21f7902a1c1d"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200044574fd94ad03828588cb0bc2d434842ee093efe639015cc107d1ea3710f2112d1786d6ef1d411cbd1af5b5ee8845993e738fb64519b4329d04be21f7902a1c1d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAERXT9lK0DgoWIywvC1DSELuCT7+Y5AVzB\nB9HqNxDyES0XhtbvHUEcvRr1te6IRZk+c4+2RRm0Mp0EviH3kCocHQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 235, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcb777777777777777777777777777777688e6a1fe808a97a348671222ff16b863", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "7oJNgYdo8T-g65COOW6hxWsRd0zmnQHlY6o2u0HWNxw", "y": "mQKRziq8VbtmgtUCrgEp58V-FG6W1EdX2qoflMk-Cxc"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04ee824d818768f13fa0eb908e396ea1c56b11774ce69d01e563aa36bb41d6371c990291ce2abc55bb6682d502ae0129e7c57e146e96d44757daaa1f94c93e0b17", "wx": "00ee824d818768f13fa0eb908e396ea1c56b11774ce69d01e563aa36bb41d6371c", "wy": "00990291ce2abc55bb6682d502ae0129e7c57e146e96d44757daaa1f94c93e0b17"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004ee824d818768f13fa0eb908e396ea1c56b11774ce69d01e563aa36bb41d6371c990291ce2abc55bb6682d502ae0129e7c57e146e96d44757daaa1f94c93e0b17", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE7oJNgYdo8T+g65COOW6hxWsRd0zmnQHl\nY6o2u0HWNxyZApHOKrxVu2aC1QKuASnnxX4UbpbUR1faqh+UyT4LFw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 236, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc6492492492492492492492492492492406dd3a19b8d5fb875235963c593bd2d3", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "SCXuRrLSFWRyajKpIvXj8tpgmPeA4fFca_FkBmnEH-c", "y": "KSwGaiTw9FDCYD8YNyEImPjoD6OEqvB361x-h8ayaXY"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "044825ee46b2d21564726a32a922f5e3f2da6098f780e1f15c6bf1640669c41fe7292c066a24f0f450c2603f1837210898f8e80fa384aaf077eb5c7e87c6b26976", "wx": "4825ee46b2d21564726a32a922f5e3f2da6098f780e1f15c6bf1640669c41fe7", "wy": "292c066a24f0f450c2603f1837210898f8e80fa384aaf077eb5c7e87c6b26976"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200044825ee46b2d21564726a32a922f5e3f2da6098f780e1f15c6bf1640669c41fe7292c066a24f0f450c2603f1837210898f8e80fa384aaf077eb5c7e87c6b26976", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAESCXuRrLSFWRyajKpIvXj8tpgmPeA4fFc\na/FkBmnEH+cpLAZqJPD0UMJgPxg3IQiY+OgPo4Sq8HfrXH6HxrJpdg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 237, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc955555555555555555555555555555547c74934474db157d2a8c3f088aced62c", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "QTSOesGOsfSFKAFGe7Cg42IJMhqK9LQQ_QbwcKgfXeY", "y": "A7VZTxpaedIwieSePjefKmyxT5IwHGmZ5RC4yNw3-0s"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0441348e7ac18eb1f4852801467bb0a0e36209321a8af4b410fd06f070a81f5de603b5594f1a5a79d23089e49e3e379f2a6cb14f92301c6999e510b8c8dc37fb4b", "wx": "41348e7ac18eb1f4852801467bb0a0e36209321a8af4b410fd06f070a81f5de6", "wy": "03b5594f1a5a79d23089e49e3e379f2a6cb14f92301c6999e510b8c8dc37fb4b"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000441348e7ac18eb1f4852801467bb0a0e36209321a8af4b410fd06f070a81f5de603b5594f1a5a79d23089e49e3e379f2a6cb14f92301c6999e510b8c8dc37fb4b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEQTSOesGOsfSFKAFGe7Cg42IJMhqK9LQQ\n/QbwcKgfXeYDtVlPGlp50jCJ5J4+N58qbLFPkjAcaZnlELjI3Df7Sw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 238, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc2aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa3e3a49a23a6d8abe95461f8445676b17", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "dTx05aNuGkthvneHICyY4FhB_qKwOStqtp7i6KdH4rY", "y": "GJcdochYJcHYFBiGEV0nyyrdhlReaXG7g1ovRSzeHlI"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04753c74e5a36e1a4b61be7787202c98e05841fea2b0392b6ab69ee2e8a747e2b618971da1c85825c1d8141886115d27cb2add86545e6971bb835a2f452cde1e52", "wx": "753c74e5a36e1a4b61be7787202c98e05841fea2b0392b6ab69ee2e8a747e2b6", "wy": "18971da1c85825c1d8141886115d27cb2add86545e6971bb835a2f452cde1e52"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004753c74e5a36e1a4b61be7787202c98e05841fea2b0392b6ab69ee2e8a747e2b618971da1c85825c1d8141886115d27cb2add86545e6971bb835a2f452cde1e52", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEdTx05aNuGkthvneHICyY4FhB/qKwOStq\ntp7i6KdH4rYYlx2hyFglwdgUGIYRXSfLKt2GVF5pcbuDWi9FLN4eUg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 239, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffcbffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364143", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "ScAlRyRXawlJgnzkYkDZDLQHXNGXikFklaRV8GqJVQQ", "y": "331kw1hTNTvU2QXaatuI8m5ipfILPNY4Kt8sWkLYUFM"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0449c0254724576b0949827ce46240d90cb4075cd1978a416495a455f06a895504df7d64c35853353bd4d905da6adb88f26e62a5f20b3cd6382adf2c5a42d85053", "wx": "49c0254724576b0949827ce46240d90cb4075cd1978a416495a455f06a895504", "wy": "00df7d64c35853353bd4d905da6adb88f26e62a5f20b3cd6382adf2c5a42d85053"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000449c0254724576b0949827ce46240d90cb4075cd1978a416495a455f06a895504df7d64c35853353bd4d905da6adb88f26e62a5f20b3cd6382adf2c5a42d85053", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEScAlRyRXawlJgnzkYkDZDLQHXNGXikFk\nlaRV8GqJVQTffWTDWFM1O9TZBdpq24jybmKl8gs81jgq3yxaQthQUw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 240, "comment": "edge case for u2", "msg": "313233343030", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffc185ddbca6dac41b1da033cfb60c152869e74b3cd66e9ffdf1b6bc09ed65ee40c", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "sF6Y6E4sGXQ8Hc9ODd8LsfMoVAM95j_PPmBfuy7ZTLE", "y": "hx10FdX2xXyEBnj34aHB4yNRmkZH-z9vUqu0ZHubbXA"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b05e98e84e2c19743c1dcf4e0ddf0bb1f32854033de63fcf3e605fbb2ed94cb1871d7415d5f6c57c840678f7e1a1c1e323519a4647fb3f6f52abb4647b9b6d70", "wx": "00b05e98e84e2c19743c1dcf4e0ddf0bb1f32854033de63fcf3e605fbb2ed94cb1", "wy": "00871d7415d5f6c57c840678f7e1a1c1e323519a4647fb3f6f52abb4647b9b6d70"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004b05e98e84e2c19743c1dcf4e0ddf0bb1f32854033de63fcf3e605fbb2ed94cb1871d7415d5f6c57c840678f7e1a1c1e323519a4647fb3f6f52abb4647b9b6d70", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEsF6Y6E4sGXQ8Hc9ODd8LsfMoVAM95j/P\nPmBfuy7ZTLGHHXQV1fbFfIQGePfhocHjI1GaRkf7P29Sq7Rke5ttcA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 241, "comment": "point duplication during verification", "msg": "313233343030", "sig": "32b0d10d8d0e04bc8d4d064d270699e87cffc9b49c5c20730e1c26f6105ddcda6fd848306e968e3ac1f6e443577c47a3c20bf0d01a5dc39c78c2c69d681850f4", "result": "valid", "flags": ["PointDuplication"]}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "sF6Y6E4sGXQ8Hc9ODd8LsfMoVAM95j_PPmBfuy7ZTLE", "y": "eOKL6ioJOoN7-YcIHl4-HNyuZbm4BMCQrVRLmoRkjr8"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04b05e98e84e2c19743c1dcf4e0ddf0bb1f32854033de63fcf3e605fbb2ed94cb178e28bea2a093a837bf987081e5e3e1cdcae65b9b804c090ad544b9a84648ebf", "wx": "00b05e98e84e2c19743c1dcf4e0ddf0bb1f32854033de63fcf3e605fbb2ed94cb1", "wy": "78e28bea2a093a837bf987081e5e3e1cdcae65b9b804c090ad544b9a84648ebf"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004b05e98e84e2c19743c1dcf4e0ddf0bb1f32854033de63fcf3e605fbb2ed94cb178e28bea2a093a837bf987081e5e3e1cdcae65b9b804c090ad544b9a84648ebf", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEsF6Y6E4sGXQ8Hc9ODd8LsfMoVAM95j/P\nPmBfuy7ZTLF44ovqKgk6g3v5hwgeXj4c3K5lubgEwJCtVEuahGSOvw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 242, "comment": "duplication bug", "msg": "313233343030", "sig": "32b0d10d8d0e04bc8d4d064d270699e87cffc9b49c5c20730e1c26f6105ddcda6fd848306e968e3ac1f6e443577c47a3c20bf0d01a5dc39c78c2c69d681850f4", "result": "invalid", "flags": ["PointDuplication"]}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "pJ-evAgsBk1hwOq1-L8jIHsG46aJ38T6KJbtEU0aiKs", "y": "VXg6a6-UAZd9EXzLdIwNXCSl070hM9YsdN4r58x9nUA"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04a49f9ebc082c064d61c0eab5f8bf23207b06e3a689dfc4fa2896ed114d1a88ab55783a6baf9401977d117ccb748c0d5c24a5d3bd2133d62c74de2be7cc7d9d40", "wx": "00a49f9ebc082c064d61c0eab5f8bf23207b06e3a689dfc4fa2896ed114d1a88ab", "wy": "55783a6baf9401977d117ccb748c0d5c24a5d3bd2133d62c74de2be7cc7d9d40"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004a49f9ebc082c064d61c0eab5f8bf23207b06e3a689dfc4fa2896ed114d1a88ab55783a6baf9401977d117ccb748c0d5c24a5d3bd2133d62c74de2be7cc7d9d40", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEpJ+evAgsBk1hwOq1+L8jIHsG46aJ38T6\nKJbtEU0aiKtVeDprr5QBl30RfMt0jA1cJKXTvSEz1ix03ivnzH2dQA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 243, "comment": "comparison with point at infinity ", "msg": "313233343030", "sig": "55555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c033333333333333333333333333333332f222f8faefdb533f265d461c29a47373", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "-VZ6QxtxY4hChRA5Ozf-79Ovz8bcOIH2I8CgmV5GHsM", "y": "-6L5EM7Rn454mxWDkKKV5jbFiMYi1U-P7_vShS4pEak"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f9567a431b716388428510393b37feefd3afcfc6dc3881f623c0a0995e461ec3fba2f910ced19f8e789b158390a295e636c588c622d54f8feffbd2852e2911a9", "wx": "00f9567a431b716388428510393b37feefd3afcfc6dc3881f623c0a0995e461ec3", "wy": "00fba2f910ced19f8e789b158390a295e636c588c622d54f8feffbd2852e2911a9"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004f9567a431b716388428510393b37feefd3afcfc6dc3881f623c0a0995e461ec3fba2f910ced19f8e789b158390a295e636c588c622d54f8feffbd2852e2911a9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE+VZ6QxtxY4hChRA5Ozf+79Ovz8bcOIH2\nI8CgmV5GHsP7ovkQztGfjnibFYOQopXmNsWIxiLVT4/v+9KFLikRqQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 244, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee555555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "Jgle-TsQv1D-KD9MmRNvuB-il4FPCZd-jjijv7g39hs", "y": "r418_EbBkoYk8gHtFKcHAbxVMb_04uV41cktq928dYA"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0426095ef93b10bf50fe283f4c99136fb81fa297814f09977e8e38a3bfb837f61baf8d7cfc46c1928624f201ed14a70701bc5531bff4e2e578d5c92dabddbc7580", "wx": "26095ef93b10bf50fe283f4c99136fb81fa297814f09977e8e38a3bfb837f61b", "wy": "00af8d7cfc46c1928624f201ed14a70701bc5531bff4e2e578d5c92dabddbc7580"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000426095ef93b10bf50fe283f4c99136fb81fa297814f09977e8e38a3bfb837f61baf8d7cfc46c1928624f201ed14a70701bc5531bff4e2e578d5c92dabddbc7580", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEJgle+TsQv1D+KD9MmRNvuB+il4FPCZd+\njjijv7g39huvjXz8RsGShiTyAe0UpwcBvFUxv/Ti5XjVyS2r3bx1gA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 245, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee5b6db6db6db6db6db6db6db6db6db6db5f30f30127d33e02aad96438927022e9c", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "ektY7nbUYaHDzeaEAKC77qs0buaTFb7WPxcAxmz15sw", "y": "pkKuQHi7a7u3YCiXeILpyDdPJnos7RMQKa6JVgzimCU"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "047a4b58ee76d461a1c3cde68400a0bbeeab346ee69315bed63f1700c66cf5e6cca642ae4078bb6bbbb76028977882e9c8374f267a2ced131029ae89560ce29825", "wx": "7a4b58ee76d461a1c3cde68400a0bbeeab346ee69315bed63f1700c66cf5e6cc", "wy": "00a642ae4078bb6bbbb76028977882e9c8374f267a2ced131029ae89560ce29825"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200047a4b58ee76d461a1c3cde68400a0bbeeab346ee69315bed63f1700c66cf5e6cca642ae4078bb6bbbb76028977882e9c8374f267a2ced131029ae89560ce29825", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEektY7nbUYaHDzeaEAKC77qs0buaTFb7W\nPxcAxmz15symQq5AeLtru7dgKJd4gunIN08meiztExAprolWDOKYJQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 246, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee599999999999999999999999999999998d668eaf0cf91f9bd7317d2547ced5a5a", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "8qER6yTJ0oDZpm5P8YaB0iLdahgo68RSjyvr4-JSKKE", "y": "oGmbzsUH_Q7IPaVBpaYUPi5o5K9y_NzIoq6isXR4zIo"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04f2a111eb24c9d280d9a66e4ff18681d222dd6a1828ebc4528f2bebe3e25228a1a0699bcec507fd0ec83da541a5a6143e2e68e4af72fcdcc8a2aea2b17478cc8a", "wx": "00f2a111eb24c9d280d9a66e4ff18681d222dd6a1828ebc4528f2bebe3e25228a1", "wy": "00a0699bcec507fd0ec83da541a5a6143e2e68e4af72fcdcc8a2aea2b17478cc8a"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004f2a111eb24c9d280d9a66e4ff18681d222dd6a1828ebc4528f2bebe3e25228a1a0699bcec507fd0ec83da541a5a6143e2e68e4af72fcdcc8a2aea2b17478cc8a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE8qER6yTJ0oDZpm5P8YaB0iLdahgo68RS\njyvr4+JSKKGgaZvOxQf9Dsg9pUGlphQ+Lmjkr3L83MiirqKxdHjMig==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 247, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee566666666666666666666666666666665e445f1f5dfb6a67e4cba8c385348e6e7", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "5QBUs-Sk0f75iOWl6DAVWrwpP-o1mK9MXdqhCs0REnQ", "y": "63ENGDRWjLN5odHz1pGowNwZ-QH-MiXCtmkd9e9TM_4"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04e50054b3e4a4d1fef988e5a5e830155abc293fea3598af4c5ddaa10acd111274eb710d1834568cb379a1d1f3d691a8c0dc19f901fe3225c2b6691df5ef5333fe", "wx": "00e50054b3e4a4d1fef988e5a5e830155abc293fea3598af4c5ddaa10acd111274", "wy": "00eb710d1834568cb379a1d1f3d691a8c0dc19f901fe3225c2b6691df5ef5333fe"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004e50054b3e4a4d1fef988e5a5e830155abc293fea3598af4c5ddaa10acd111274eb710d1834568cb379a1d1f3d691a8c0dc19f901fe3225c2b6691df5ef5333fe", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE5QBUs+Sk0f75iOWl6DAVWrwpP+o1mK9M\nXdqhCs0REnTrcQ0YNFaMs3mh0fPWkajA3Bn5Af4yJcK2aR3171Mz/g==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 248, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee549249249249249249249249249249248c79facd43214c011123c1b03a93412a5", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "7cF81MpvmYj9pa9AQuP560LQ97ahwBVuGir1ZreBA1Q", "y": "il01d3ezBulkBfEuJhfBsp6NV05fbWbRvI_36nxLaDw"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04edc17cd4ca6f9988fda5af4042e3f9eb42d0f7b6a1c0156e1a2af566b78103548a5d357777b306e96405f12e2617c1b29e8d574e5f6d66d1bc8ff7ea7c4b683c", "wx": "00edc17cd4ca6f9988fda5af4042e3f9eb42d0f7b6a1c0156e1a2af566b7810354", "wy": "008a5d357777b306e96405f12e2617c1b29e8d574e5f6d66d1bc8ff7ea7c4b683c"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004edc17cd4ca6f9988fda5af4042e3f9eb42d0f7b6a1c0156e1a2af566b78103548a5d357777b306e96405f12e2617c1b29e8d574e5f6d66d1bc8ff7ea7c4b683c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE7cF81MpvmYj9pa9AQuP560LQ97ahwBVu\nGir1ZreBA1SKXTV3d7MG6WQF8S4mF8Gyno1XTl9tZtG8j/fqfEtoPA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 249, "comment": "extreme value for k", "msg": "313233343030", "sig": "c6047f9441ed7d6d3045406e95c07cd85c778e4b8cef3ca7abac09b95c709ee50eb10e5ab95f2f275348d82ad2e4d7949c8193800d8c9c75df58e343f0ebba7b", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "bSZ8ENIxW0LbrzTJfDwNMx-rrK9gId9NyFs-nmPcB5g", "y": "7RVLEfo6XtlSwU2KLdJC3itszjwi30LNl94wBUoZVV4"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046d267c10d2315b42dbaf34c97c3c0d331fabacaf6021df4dc85b3e9e63dc0798ed154b11fa3a5ed952c14d8a2dd242de2b6cce3c22df42cd97de30054a19555e", "wx": "6d267c10d2315b42dbaf34c97c3c0d331fabacaf6021df4dc85b3e9e63dc0798", "wy": "00ed154b11fa3a5ed952c14d8a2dd242de2b6cce3c22df42cd97de30054a19555e"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046d267c10d2315b42dbaf34c97c3c0d331fabacaf6021df4dc85b3e9e63dc0798ed154b11fa3a5ed952c14d8a2dd242de2b6cce3c22df42cd97de30054a19555e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEbSZ8ENIxW0LbrzTJfDwNMx+rrK9gId9N\nyFs+nmPcB5jtFUsR+jpe2VLBTYot0kLeK2zOPCLfQs2X3jAFShlVXg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 250, "comment": "extreme value for k and edgecase s", "msg": "313233343030", "sig": "79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f8179855555555555555555555555555555554e8e4f44ce51835693ff0ca2ef01215c0", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "wkv3qYTJbs4QB3qd7zjL0NiYq9VV8WaOBsJ8q8APb2c", "y": "n2myOOH5XpnltVjgA2Jz69bDbRK0UVNIuFoh9ig_UBY"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04c24bf7a984c96ece10077a9def38cbd0d898abd555f1668e06c27cabc00f6f679f69b238e1f95e99e5b558e0036273ebd6c36d12b4515348b85a21f6283f5016", "wx": "00c24bf7a984c96ece10077a9def38cbd0d898abd555f1668e06c27cabc00f6f67", "wy": "009f69b238e1f95e99e5b558e0036273ebd6c36d12b4515348b85a21f6283f5016"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004c24bf7a984c96ece10077a9def38cbd0d898abd555f1668e06c27cabc00f6f679f69b238e1f95e99e5b558e0036273ebd6c36d12b4515348b85a21f6283f5016", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEwkv3qYTJbs4QB3qd7zjL0NiYq9VV8WaO\nBsJ8q8APb2efabI44flemeW1WOADYnPr1sNtErRRU0i4WiH2KD9QFg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 251, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798b6db6db6db6db6db6db6db6db6db6db5f30f30127d33e02aad96438927022e9c", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "HNJtZogHyBXtP1MsHbgaxHP7No8PfvGv8lkupvpsRiQ", "y": "oim5q1dGz7xHKAwBmkJIVFNUyiCID_QcrC4lK8m0lwQ"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "041cd26d668807c815ed3f532c1db81ac473fb368f0f7ef1aff2592ea6fa6c4624a229b9ab5746cfbc47280c019a4248545354ca20880ff41cac2e252bc9b49704", "wx": "1cd26d668807c815ed3f532c1db81ac473fb368f0f7ef1aff2592ea6fa6c4624", "wy": "00a229b9ab5746cfbc47280c019a4248545354ca20880ff41cac2e252bc9b49704"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200041cd26d668807c815ed3f532c1db81ac473fb368f0f7ef1aff2592ea6fa6c4624a229b9ab5746cfbc47280c019a4248545354ca20880ff41cac2e252bc9b49704", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEHNJtZogHyBXtP1MsHbgaxHP7No8PfvGv\n8lkupvpsRiSiKbmrV0bPvEcoDAGaQkhUU1TKIIgP9BysLiUrybSXBA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 252, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f8179899999999999999999999999999999998d668eaf0cf91f9bd7317d2547ced5a5a", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "_HgHd6MomvZj-gKxwmKoNzuEYU5lnBq0aULx4FiSb_g", "y": "IZbGvK4LJ5gpjUY75ch5JDQ9fxA6JxMeDH9NYNK12ow"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04fc780777a3289af663fa02b1c262a8373b84614e659c1ab46942f1e058926ff82196c6bcae0b2798298d463be5c87924343d7f103a27131e0c7f4d60d2b5da8c", "wx": "00fc780777a3289af663fa02b1c262a8373b84614e659c1ab46942f1e058926ff8", "wy": "2196c6bcae0b2798298d463be5c87924343d7f103a27131e0c7f4d60d2b5da8c"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004fc780777a3289af663fa02b1c262a8373b84614e659c1ab46942f1e058926ff82196c6bcae0b2798298d463be5c87924343d7f103a27131e0c7f4d60d2b5da8c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAE/HgHd6MomvZj+gKxwmKoNzuEYU5lnBq0\naULx4FiSb/ghlsa8rgsnmCmNRjvlyHkkND1/EDonEx4Mf01g0rXajA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 253, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f8179866666666666666666666666666666665e445f1f5dfb6a67e4cba8c385348e6e7", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "XiXi7or174o-CQg0H5iEUB-1ii_SNLHbbyLVYQJVJPQ", "y": "SR2Xp3k8nZofNbs18SEhudvgddhQHL1Ntml-PgrZi8A"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "045e25e2ee8af5ef8a3e0908341f9884501fb58a2fd234b1db6f22d561025524f4491d97a7793c9d9a1f35bb35f12121b9dbe075d8501cbd4db6697e3e0ad98bc0", "wx": "5e25e2ee8af5ef8a3e0908341f9884501fb58a2fd234b1db6f22d561025524f4", "wy": "491d97a7793c9d9a1f35bb35f12121b9dbe075d8501cbd4db6697e3e0ad98bc0"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200045e25e2ee8af5ef8a3e0908341f9884501fb58a2fd234b1db6f22d561025524f4491d97a7793c9d9a1f35bb35f12121b9dbe075d8501cbd4db6697e3e0ad98bc0", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEXiXi7or174o+CQg0H5iEUB+1ii/SNLHb\nbyLVYQJVJPRJHZeneTydmh81uzXxISG52+B12FAcvU22aX4+CtmLwA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 254, "comment": "extreme value for k and s^-1", "msg": "313233343030", "sig": "79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f8179849249249249249249249249249249248c79facd43214c011123c1b03a93412a5", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "Pd8pIGB99ZbakBI-pWdJWAVMjtd1hmG4E_GqMPGXeLA", "y": "cHJD4ae8wmS1QomDLpUMJ1Y4ViQbecJD0PxU960kvCU"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "043ddf2920607df596da90123ea5674958054c8ed7758661b813f1aa30f19778b0707243e1a7bcc264b54289832e950c27563856241b79c243d0fc54f7ad24bc25", "wx": "3ddf2920607df596da90123ea5674958054c8ed7758661b813f1aa30f19778b0", "wy": "707243e1a7bcc264b54289832e950c27563856241b79c243d0fc54f7ad24bc25"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200043ddf2920607df596da90123ea5674958054c8ed7758661b813f1aa30f19778b0707243e1a7bcc264b54289832e950c27563856241b79c243d0fc54f7ad24bc25", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEPd8pIGB99ZbakBI+pWdJWAVMjtd1hmG4\nE/GqMPGXeLBwckPhp7zCZLVCiYMulQwnVjhWJBt5wkPQ/FT3rSS8JQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 255, "comment": "extreme value for k", "msg": "313233343030", "sig": "79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f817980eb10e5ab95f2f275348d82ad2e4d7949c8193800d8c9c75df58e343f0ebba7b", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "eb5mfvncu6xVoGKVzocLBwKb_NstzijZWfKBWxb4F5g", "y": "SDradyajxGVdpPv8DhEIqP0XtEimhVQZnEfQj_sQ1Lg"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8", "wx": "79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798", "wy": "483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798483ada7726a3c4655da4fbfc0e1108a8fd17b448a68554199c47d08ffb10d4b8", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEeb5mfvncu6xVoGKVzocLBwKb/NstzijZ\nWfKBWxb4F5hIOtp3JqPEZV2k+/wOEQio/Re0SKaFVBmcR9CP+xDUuA==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 256, "comment": "testing point duplication", "msg": "313233343030", "sig": "43f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b0232102812492492492492492492492492492492463cfd66a190a6008891e0d81d49a0952", "result": "invalid", "flags": []}, {"tcId": 257, "comment": "testing point duplication", "msg": "313233343030", "sig": "bc07ff041506dc73a75086a43252fb4270e157da75fb6cb92a9f07dcad153ec02492492492492492492492492492492463cfd66a190a6008891e0d81d49a0952", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "eb5mfvncu6xVoGKVzocLBwKb_NstzijZWfKBWxb4F5g", "y": "t8UliNlcO5qiWwQD8e73VwLoS7dZeqvmY7gvbwTvJ3c"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798b7c52588d95c3b9aa25b0403f1eef75702e84bb7597aabe663b82f6f04ef2777", "wx": "79be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798", "wy": "00b7c52588d95c3b9aa25b0403f1eef75702e84bb7597aabe663b82f6f04ef2777"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000479be667ef9dcbbac55a06295ce870b07029bfcdb2dce28d959f2815b16f81798b7c52588d95c3b9aa25b0403f1eef75702e84bb7597aabe663b82f6f04ef2777", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEeb5mfvncu6xVoGKVzocLBwKb/NstzijZ\nWfKBWxb4F5i3xSWI2Vw7mqJbBAPx7vdXAuhLt1l6q+ZjuC9vBO8ndw==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 258, "comment": "testing point duplication", "msg": "313233343030", "sig": "43f800fbeaf9238c58af795bcdad04bc49cd850c394d3382953356b0232102812492492492492492492492492492492463cfd66a190a6008891e0d81d49a0952", "result": "invalid", "flags": []}, {"tcId": 259, "comment": "testing point duplication", "msg": "313233343030", "sig": "bc07ff041506dc73a75086a43252fb4270e157da75fb6cb92a9f07dcad153ec02492492492492492492492492492492463cfd66a190a6008891e0d81d49a0952", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "eCyO0X47Kng7VGTzOwllKnHGeOBexR6E4rz8Zjo96WM", "y": "r5rLQoC4x_fEL075q6YkXsHsFxL9OKD6lkGNjNaqYVI"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04782c8ed17e3b2a783b5464f33b09652a71c678e05ec51e84e2bcfc663a3de963af9acb4280b8c7f7c42f4ef9aba6245ec1ec1712fd38a0fa96418d8cd6aa6152", "wx": "782c8ed17e3b2a783b5464f33b09652a71c678e05ec51e84e2bcfc663a3de963", "wy": "00af9acb4280b8c7f7c42f4ef9aba6245ec1ec1712fd38a0fa96418d8cd6aa6152"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004782c8ed17e3b2a783b5464f33b09652a71c678e05ec51e84e2bcfc663a3de963af9acb4280b8c7f7c42f4ef9aba6245ec1ec1712fd38a0fa96418d8cd6aa6152", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEeCyO0X47Kng7VGTzOwllKnHGeOBexR6E\n4rz8Zjo96WOvmstCgLjH98QvTvmrpiRewewXEv04oPqWQY2M1qphUg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 260, "comment": "pseudorandom signature", "msg": "", "sig": "6632473c909425b6fa37095398e2538daab8552440320f9fe190dba8f672796ba8c3aacce9ffe4bc17c0530738f1386f9d9579f029ff3a7791b16e98422265e3", "result": "valid", "flags": []}, {"tcId": 261, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "465b0fb05c14cd4ddef23e13acbe5f2337c45ea3816536670cfa7f2ab9090619005e525e837c406cf8944383e20bcee32112d8da5b42b40f88415098f722aa89", "result": "valid", "flags": []}, {"tcId": 262, "comment": "pseudorandom signature", "msg": "313233343030", "sig": "7b1553e4d650c71fd49aa36ceed56f0438b0065e1b234445134bf7c83231ca9de369a20fa6434bd138b092885a89e53a3f0b6bdcc5d2653e136c54070081dc5a", "result": "valid", "flags": []}, {"tcId": 263, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "c7ba1c73bdc4364f6c7c61ab1fecc0547f8d6fcbeb251f734964407536353f327b3a6fb2fe60f8861e9e0955663f5703a17f5ecc3a5b5140eb87eaf35a3a5090", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "boI1VUUpFAmRgsaywdbwtdKNUMzQBa8s4bulQapAyv8", "y": "AAAAAQYEktWlZz4PJdjVD7fljEnYbUbUIWlV4Ko9QOE"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40caff00000001060492d5a5673e0f25d8d50fb7e58c49d86d46d4216955e0aa3d40e1", "wx": "6e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40caff", "wy": "01060492d5a5673e0f25d8d50fb7e58c49d86d46d4216955e0aa3d40e1"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40caff00000001060492d5a5673e0f25d8d50fb7e58c49d86d46d4216955e0aa3d40e1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEboI1VUUpFAmRgsaywdbwtdKNUMzQBa8s\n4bulQapAyv8AAAABBgSS1aVnPg8l2NUPt+WMSdhtRtQhaVXgqj1A4Q==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 264, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "2b9c9f85596fed708b3af80393b27edfd0b5ae2f0074270a56362f5f9f62b4e12fae837503ba2c1d4c945e0913949ef094ce0b8086359bbb5dba4a12707c5600", "result": "valid", "flags": []}, {"tcId": 265, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "5cd765209021d8c1a8aef4ff61d6fa6e7993bf9fea0b93609eea130de536fccc4f10c7989587fe3019e36d85aa024bf20db6737c4f28900c1c9662f2782143e0", "result": "valid", "flags": []}, {"tcId": 266, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "4c1a59b1e578d76f1595e13b557057559f26ab559ec1df3f45ec98b90fa526cec6872f094bdb3f82e31f93ad65357e2daafe66f304af197089ef0dc94ff90624", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "boI1VUUpFAmRgsaywdbwtdKNUMzQBa8s4bulQapAyv8", "y": "_____vn7bSpamMHw2icq8Egac7Ynkrkr3paqHlXCu04"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40cafffffffffef9fb6d2a5a98c1f0da272af0481a73b62792b92bde96aa1e55c2bb4e", "wx": "6e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40caff", "wy": "00fffffffef9fb6d2a5a98c1f0da272af0481a73b62792b92bde96aa1e55c2bb4e"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046e823555452914099182c6b2c1d6f0b5d28d50ccd005af2ce1bba541aa40cafffffffffef9fb6d2a5a98c1f0da272af0481a73b62792b92bde96aa1e55c2bb4e", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEboI1VUUpFAmRgsaywdbwtdKNUMzQBa8s\n4bulQapAyv/////++fttKlqYwfDaJyrwSBpztieSuSvelqoeVcK7Tg==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 267, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "a35d1400d4cc7a8f617b721faee7118a74103c4630dec5aa47e097951dafc1a7958221023024e97ef6df35a22e820c7bc5e16299f3f12e9d9b1b727c46d795e6", "result": "valid", "flags": []}, {"tcId": 268, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "7fb733ed73c72fc4f4cf065e370c730301316ff4e9c6a8a701170f604c2d70b77ca9ca985d3df48978b3a2f9c0bb8a58b216c795e687f74623a3321448bfa73c", "result": "valid", "flags": []}, {"tcId": 269, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "95ae4df2fba8524e1151cb9a9c5c1ec1357a663722a18329303d86a58e704754591ea644b1dc6f4c7cd5d7d939397f84d9e077100760f0816ae5b22ae6a74203", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "AAAAAT_SIkjWTZX3PCm0irSGMYUL5QP9APhGi18PcOA", "y": "9u56pDvCxv0lsdgmkkHL3Z27DayW3JYjH0MHBfg4cX0"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04000000013fd22248d64d95f73c29b48ab48631850be503fd00f8468b5f0f70e0f6ee7aa43bc2c6fd25b1d8269241cbdd9dbb0dac96dc96231f430705f838717d", "wx": "013fd22248d64d95f73c29b48ab48631850be503fd00f8468b5f0f70e0", "wy": "00f6ee7aa43bc2c6fd25b1d8269241cbdd9dbb0dac96dc96231f430705f838717d"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004000000013fd22248d64d95f73c29b48ab48631850be503fd00f8468b5f0f70e0f6ee7aa43bc2c6fd25b1d8269241cbdd9dbb0dac96dc96231f430705f838717d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEAAAAAT/SIkjWTZX3PCm0irSGMYUL5QP9\nAPhGi18PcOD27nqkO8LG/SWx2CaSQcvdnbsNrJbcliMfQwcF+DhxfQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 270, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "717925f0dd5cf45e746e87f79c9ea97d11eb01444052c270aeccef56c2e95828785787b664137080383d2fc500459fa713258205fdae97b3240fb64bb638a657", "result": "valid", "flags": []}, {"tcId": 271, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "8adfdeae3b586315d06183610d271fc423cc789908b8f5dc563253a3c782510a8137bedbb4e60da26041b351f72a6bc3b7741f745743f0733b40b7fc56febd04", "result": "valid", "flags": []}, {"tcId": 272, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "92ded14e19b94d17c79b063a034b122ce3b93a2502f2f223fad3461abf63163252ff8ad14ba3657242e29440d01cab36ebb6033ee36021dc8d9b38f0808a90bc", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "Ja_W<PERSON>yrrtZ8Hylt5ZQG-MVQ9XFGoLTsLJeHbf____8", "y": "-kanblIDIt-8SR7E8MwZdCD8TqWIPY9t1Tw1S8T2fDU"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "0425afd689acabaed67c1f296de59406f8c550f57146a0b4ec2c97876dfffffffffa46a76e520322dfbc491ec4f0cc197420fc4ea5883d8f6dd53c354bc4f67c35", "wx": "25afd689acabaed67c1f296de59406f8c550f57146a0b4ec2c97876dffffffff", "wy": "00fa46a76e520322dfbc491ec4f0cc197420fc4ea5883d8f6dd53c354bc4f67c35"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a0342000425afd689acabaed67c1f296de59406f8c550f57146a0b4ec2c97876dfffffffffa46a76e520322dfbc491ec4f0cc197420fc4ea5883d8f6dd53c354bc4f67c35", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMF<PERSON><PERSON>EAYHKoZIzj0CAQYFK4EEAAoDQgAEJa/WiayrrtZ8Hylt5ZQG+MVQ9XFGoLTs\nLJeHbf/////6RqduUgMi37xJHsTwzBl0IPxOpYg9j23VPDVLxPZ8NQ==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 273, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "d48373483e0fa2f11cfdfaea6f1de59e6861e9e87c4f6446602ba0125ab7de469d753bba3a7be08aab456e93a6500d4781795ed59af8bd6d6133129abef1ad98", "result": "valid", "flags": []}, {"tcId": 274, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "f11473117b66e5d84a2ecd0f8b7ec4a2cc2aee89ae022020235777305142f498fe5ce43ced28f3f69f65e810678afefd2bdeefb051280ad2880157fda28b2ab1", "result": "valid", "flags": []}, {"tcId": 275, "comment": "x-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "3c9f5bdde7310b5696c93c86203fc97e11a70739e20c71c9e722308d45a59e6cc09efb9a045a47cce799b768890bb17833a0210d869a36be1da33f2585477c32", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "0S5sZrZ3NMPITSYBz1013Al-J2N_CspKT9t0tqrdO7k", "y": "P1vf-IvVc234mOaZAG7XUPEc8HxYZs161wxxIf____8"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "04d12e6c66b67734c3c84d2601cf5d35dc097e27637f0aca4a4fdb74b6aadd3bb93f5bdff88bd5736df898e699006ed750f11cf07c5866cd7ad70c7121ffffffff", "wx": "00d12e6c66b67734c3c84d2601cf5d35dc097e27637f0aca4a4fdb74b6aadd3bb9", "wy": "3f5bdff88bd5736df898e699006ed750f11cf07c5866cd7ad70c7121ffffffff"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a03420004d12e6c66b67734c3c84d2601cf5d35dc097e27637f0aca4a4fdb74b6aadd3bb93f5bdff88bd5736df898e699006ed750f11cf07c5866cd7ad70c7121ffffffff", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMF<PERSON><PERSON>EAYHKoZIzj0CAQYFK4EEAAoDQgAE0S5sZrZ3NMPITSYBz1013Al+J2N/CspK\nT9t0tqrdO7k/W9/4i9VzbfiY5pkAbtdQ8RzwfFhmzXrXDHEh/////w==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 276, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "6953442c487f240487d2af81f9825c894b1fc2534321fa012db8248be20a4b0656927395d64ce4d690caa98944c2ddebc312f57f439d37236ea63cc1de098718", "result": "valid", "flags": []}, {"tcId": 277, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "fb39aa5f36ceca6e68d1932e811598c412892734dade389fd9e8ba94c5c7a251fdddf0c3db66c7c46608ac98431f0ee8ebb1e27ba501937789ebcd0f7ac26ecc", "result": "valid", "flags": []}, {"tcId": 278, "comment": "y-coordinate of the public key has many trailing 1's", "msg": "4d657373616765", "sig": "44fef6017638fd5bda17dfce346b0311b5e369bfb68aa85d5e970786b8e6644b720b3a52fe44be6028759f0f1a6fd7020ff6792cd4ece98dffd0d97d3b726091", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-256K", "kid": "none", "kty": "EC", "x": "bUp_YNR3Sk8KqLve25U8fup5CUB-MWR1VmS8KAAAAAA", "y": "5lnTTk3zjZ6MnqrfujZhLHaRlb6Gx3qsPzbni1OGgPs"}, "key": {"curve": "secp256k1", "keySize": 256, "type": "EcPublicKey", "uncompressed": "046d4a7f60d4774a4f0aa8bbdedb953c7eea7909407e3164755664bc2800000000e659d34e4df38d9e8c9eaadfba36612c769195be86c77aac3f36e78b538680fb", "wx": "6d4a7f60d4774a4f0aa8bbdedb953c7eea7909407e3164755664bc2800000000", "wy": "00e659d34e4df38d9e8c9eaadfba36612c769195be86c77aac3f36e78b538680fb"}, "keyDer": "3056301006072a8648ce3d020106052b8104000a034200046d4a7f60d4774a4f0aa8bbdedb953c7eea7909407e3164755664bc2800000000e659d34e4df38d9e8c9eaadfba36612c769195be86c77aac3f36e78b538680fb", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMFYwEAYHKoZIzj0CAQYFK4EEAAoDQgAEbUp/YNR3Sk8KqLve25U8fup5CUB+MWR1\nVmS8KAAAAADmWdNOTfONnoyeqt+6NmEsdpGVvobHeqw/NueLU4aA+w==\n-----END PUBLIC KEY-----", "sha": "SHA-512", "type": "EcdsaP1363Verify", "tests": [{"tcId": 279, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "304babc41346e6205cf03e2d0b26e4b222dce8227402d001ba233efa69c912340065add3279f51b2417fb0a13b0f06404199caac3430385513ee49f67d8e8cdf", "result": "valid", "flags": []}, {"tcId": 280, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "23868700b71fbafcaa73960faf922ee0458ef69e01fb060b2f9a80d992fe114c6ec1526bd56f6eebf10463bd9210d62510b95166365e10a7b7abfc4d584ca338", "result": "valid", "flags": []}, {"tcId": 281, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "dd60d7cf83a08208637212b65d079fb658d8ef1b8438d9c58f4122b0cd14ac49f1d762516f4d6c3e6a98dd31dc3869dc7cf35944f33b35c6a17fe632d2b18cd5", "result": "valid", "flags": []}]}]}