/* Copyright (c) 2011 The WebM project authors. All Rights Reserved. */
/*  */
/* Use of this source code is governed by a BSD-style license */
/* that can be found in the LICENSE file in the root of the source */
/* tree. An additional intellectual property rights grant can be found */
/* in the file PATENTS.  All contributing project authors may */
/* be found in the AUTHORS file in the root of the source tree. */
/* This file automatically generated by configure. Do not edit! */
#ifndef VPX_CONFIG_H
#define VPX_CONFIG_H
#define RESTRICT    
#define INLINE      inline
#define VPX_ARCH_ARM 0
#define ARCH_ARM 0
#define VPX_ARCH_MIPS 0
#define ARCH_MIPS 0
#define VPX_ARCH_X86 0
#define ARCH_X86 0
#define VPX_ARCH_X86_64 0
#define ARCH_X86_64 0
#define VPX_ARCH_PPC 0
#define ARCH_PPC 0
#define HAVE_NEON 0
#define HAVE_NEON_ASM 0
#define HAVE_MIPS32 0
#define HAVE_DSPR2 0
#define HAVE_MSA 0
#define HAVE_MIPS64 0
#define HAVE_MMX 0
#define HAVE_SSE 0
#define HAVE_SSE2 0
#define HAVE_SSE3 0
#define HAVE_SSSE3 0
#define HAVE_SSE4_1 0
#define HAVE_AVX 0
#define HAVE_AVX2 0
#define HAVE_AVX512 0
#define HAVE_VSX 0
#define HAVE_MMI 0
#define HAVE_VPX_PORTS 1
#define HAVE_PTHREAD_H 1
#define HAVE_UNISTD_H 0
#define CONFIG_DEPENDENCY_TRACKING 1
#define CONFIG_EXTERNAL_BUILD 1
#define CONFIG_INSTALL_DOCS 0
#define CONFIG_INSTALL_BINS 1
#define CONFIG_INSTALL_LIBS 1
#define CONFIG_INSTALL_SRCS 0
#define CONFIG_DEBUG 0
#define CONFIG_GPROF 0
#define CONFIG_GCOV 0
#define CONFIG_RVCT 0
#define CONFIG_GCC 1
#define CONFIG_MSVS 0
#define CONFIG_PIC 0
#define CONFIG_BIG_ENDIAN 0
#define CONFIG_CODEC_SRCS 0
#define CONFIG_DEBUG_LIBS 0
#define CONFIG_DEQUANT_TOKENS 0
#define CONFIG_DC_RECON 0
#define CONFIG_RUNTIME_CPU_DETECT 0
#define CONFIG_POSTPROC 1
#define CONFIG_VP9_POSTPROC 1
#define CONFIG_MULTITHREAD 1
#define CONFIG_INTERNAL_STATS 0
#define CONFIG_VP8_ENCODER 1
#define CONFIG_VP8_DECODER 1
#define CONFIG_VP9_ENCODER 1
#define CONFIG_VP9_DECODER 1
#define CONFIG_VP8 1
#define CONFIG_VP9 1
#define CONFIG_ENCODERS 1
#define CONFIG_DECODERS 1
#define CONFIG_STATIC_MSVCRT 0
#define CONFIG_SPATIAL_RESAMPLING 1
#define CONFIG_REALTIME_ONLY 1
#define CONFIG_ONTHEFLY_BITPACKING 0
#define CONFIG_ERROR_CONCEALMENT 0
#define CONFIG_SHARED 0
#define CONFIG_STATIC 1
#define CONFIG_SMALL 0
#define CONFIG_POSTPROC_VISUALIZER 0
#define CONFIG_OS_SUPPORT 1
#define CONFIG_UNIT_TESTS 1
#define CONFIG_WEBM_IO 1
#define CONFIG_LIBYUV 0
#define CONFIG_DECODE_PERF_TESTS 0
#define CONFIG_ENCODE_PERF_TESTS 0
#define CONFIG_MULTI_RES_ENCODING 1
#define CONFIG_TEMPORAL_DENOISING 1
#define CONFIG_VP9_TEMPORAL_DENOISING 1
#define CONFIG_CONSISTENT_RECODE 0
#define CONFIG_COEFFICIENT_RANGE_CHECKING 0
#define CONFIG_VP9_HIGHBITDEPTH 1
#define CONFIG_BETTER_HW_COMPATIBILITY 0
#define CONFIG_EXPERIMENTAL 0
#define CONFIG_SIZE_LIMIT 1
#define CONFIG_ALWAYS_ADJUST_BPM 0
#define CONFIG_BITSTREAM_DEBUG 0
#define CONFIG_MISMATCH_DEBUG 0
#define CONFIG_FP_MB_STATS 0
#define CONFIG_EMULATE_HARDWARE 0
#define CONFIG_NON_GREEDY_MV 0
#define CONFIG_RATE_CTRL 0
#define DECODE_WIDTH_LIMIT 16384
#define DECODE_HEIGHT_LIMIT 16384
#endif /* VPX_CONFIG_H */
