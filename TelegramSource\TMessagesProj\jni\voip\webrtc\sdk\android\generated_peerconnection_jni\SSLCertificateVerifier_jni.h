// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/SSLCertificateVerifier

#ifndef org_webrtc_SSLCertificateVerifier_JNI
#define org_webrtc_SSLCertificateVerifier_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_SSLCertificateVerifier[];
const char kClassPath_org_webrtc_SSLCertificateVerifier[] = "org/webrtc/SSLCertificateVerifier";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_SSLCertificateVerifier_clazz(nullptr);
#ifndef org_webrtc_SSLCertificateVerifier_clazz_defined
#define org_webrtc_SSLCertificateVerifier_clazz_defined
inline jclass org_webrtc_SSLCertificateVerifier_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_SSLCertificateVerifier,
      &g_org_webrtc_SSLCertificateVerifier_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_SSLCertificateVerifier_verify1(nullptr);
static jboolean Java_SSLCertificateVerifier_verify(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj, const jni_zero::JavaRef<jbyteArray>& certificate) {
  jclass clazz = org_webrtc_SSLCertificateVerifier_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_SSLCertificateVerifier_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "verify",
          "([B)Z",
          &g_org_webrtc_SSLCertificateVerifier_verify1);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id, certificate.obj());
  return ret;
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_SSLCertificateVerifier_JNI
