// Copyright 2023 The BoringSSL Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include <vector>

#include <string.h>

#include <gtest/gtest.h>

#include <openssl/bytestring.h>

#include "../../test/file_test.h"
#include "../../test/test_util.h"
#include "./internal.h"


static void KeccakFileTest(FileTest *t) {
  std::vector<uint8_t> input, sha3_256_expected, sha3_512_expected,
      shake128_expected, shake256_expected;
  ASSERT_TRUE(t->GetBytes(&input, "Input"));
  ASSERT_TRUE(t->GetBytes(&sha3_256_expected, "SHA3-256"));
  ASSERT_TRUE(t->GetBytes(&sha3_512_expected, "SHA3-512"));
  ASSERT_TRUE(t->GetBytes(&shake128_expected, "SHAKE-128"));
  ASSERT_TRUE(t->GetBytes(&shake256_expected, "SHAKE-256"));

  uint8_t sha3_256_digest[32];
  BORINGSSL_keccak(sha3_256_digest, sizeof(sha3_256_digest), input.data(),
                   input.size(), boringssl_sha3_256);
  uint8_t sha3_512_digest[64];
  BORINGSSL_keccak(sha3_512_digest, sizeof(sha3_512_digest), input.data(),
                   input.size(), boringssl_sha3_512);
  uint8_t shake128_output[512];
  BORINGSSL_keccak(shake128_output, sizeof(shake128_output), input.data(),
                   input.size(), boringssl_shake128);
  uint8_t shake256_output[512];
  BORINGSSL_keccak(shake256_output, sizeof(shake256_output), input.data(),
                   input.size(), boringssl_shake256);

  EXPECT_EQ(Bytes(sha3_256_expected), Bytes(sha3_256_digest));
  EXPECT_EQ(Bytes(sha3_512_expected), Bytes(sha3_512_digest));
  EXPECT_EQ(Bytes(shake128_expected), Bytes(shake128_output));
  EXPECT_EQ(Bytes(shake256_expected), Bytes(shake256_output));

  struct BORINGSSL_keccak_st ctx;

  // Single-pass absorb/squeeze.
  OPENSSL_memset(shake128_output, 0, sizeof(shake128_output));
  BORINGSSL_keccak_init(&ctx, boringssl_shake128);
  BORINGSSL_keccak_absorb(&ctx, input.data(), input.size());
  BORINGSSL_keccak_squeeze(&ctx, shake128_output, sizeof(shake128_output));
  EXPECT_EQ(Bytes(shake128_expected), Bytes(shake128_output));

  OPENSSL_memset(shake256_output, 0, sizeof(shake256_output));
  BORINGSSL_keccak_init(&ctx, boringssl_shake256);
  BORINGSSL_keccak_absorb(&ctx, input.data(), input.size());
  BORINGSSL_keccak_squeeze(&ctx, shake256_output, sizeof(shake256_output));
  EXPECT_EQ(Bytes(shake256_expected), Bytes(shake256_output));

  // Byte-by-byte absorb/squeeze.
  OPENSSL_memset(shake128_output, 0, sizeof(shake128_output));
  BORINGSSL_keccak_init(&ctx, boringssl_shake128);
  for (size_t i = 0; i < input.size(); i++) {
    BORINGSSL_keccak_absorb(&ctx, &input[i], 1);
  }
  for (size_t i = 0; i < sizeof(shake128_output); i++) {
    BORINGSSL_keccak_squeeze(&ctx, &shake128_output[i], 1);
  }
  EXPECT_EQ(Bytes(shake128_expected), Bytes(shake128_output));

  OPENSSL_memset(shake256_output, 0, sizeof(shake256_output));
  BORINGSSL_keccak_init(&ctx, boringssl_shake256);
  for (size_t i = 0; i < input.size(); i++) {
    BORINGSSL_keccak_absorb(&ctx, &input[i], 1);
  }
  for (size_t i = 0; i < sizeof(shake256_output); i++) {
    BORINGSSL_keccak_squeeze(&ctx, &shake256_output[i], 1);
  }
  EXPECT_EQ(Bytes(shake256_expected), Bytes(shake256_output));
}

TEST(KeccakTest, KeccakTestVectors) {
  FileTestGTest("crypto/fipsmodule/keccak/keccak_tests.txt", KeccakFileTest);
}

TEST(KeccakTest, MultiPass) {
  // Example from keccak_tests.txt with an input long enough to be interesting.
  uint8_t input[500] = {
      0xd0, 0xee, 0x72, 0x13, 0xea, 0x0c, 0xd3, 0x4f, 0x99, 0xe8, 0x27, 0x8c,
      0x24, 0xb0, 0x06, 0x3d, 0x41, 0x6e, 0x64, 0xda, 0x5a, 0xee, 0x96, 0x39,
      0xa1, 0x81, 0x94, 0xe3, 0x95, 0x6b, 0x5f, 0xc8, 0x4e, 0xbb, 0x17, 0xf5,
      0x92, 0xc2, 0xef, 0x45, 0xf9, 0xec, 0x9b, 0x75, 0xc6, 0x48, 0xc8, 0x08,
      0xdc, 0x43, 0x69, 0xa7, 0x42, 0x15, 0xba, 0x89, 0x40, 0xd6, 0x40, 0xb3,
      0xd0, 0x02, 0xdf, 0xb5, 0xae, 0xd7, 0xc6, 0x38, 0x84, 0xba, 0x6e, 0x52,
      0x27, 0x8b, 0x65, 0x7f, 0x70, 0xde, 0x05, 0x10, 0xce, 0x88, 0x65, 0xfa,
      0xca, 0x55, 0x31, 0xd4, 0x22, 0xa8, 0x37, 0x49, 0x75, 0xad, 0xd8, 0xcf,
      0xa7, 0x9c, 0x05, 0x8a, 0x94, 0x2d, 0x55, 0xf3, 0x2d, 0xd7, 0x61, 0xda,
      0xec, 0xcf, 0xb8, 0xc5, 0x28, 0x61, 0x04, 0x5d, 0x4f, 0x69, 0xa9, 0x17,
      0x6b, 0x09, 0x09, 0xd9, 0x28, 0xef, 0x71, 0xf9, 0x1e, 0xb1, 0x81, 0x86,
      0x62, 0x00, 0xbf, 0x0f, 0xc3, 0x01, 0x7a, 0x98, 0x02, 0x44, 0x0a, 0x9b,
      0xdf, 0x78, 0xa2, 0x3a, 0x8d, 0x08, 0x69, 0x63, 0xaa, 0x9f, 0x3f, 0x33,
      0x11, 0x3b, 0xac, 0x5e, 0xcc, 0xd0, 0x8f, 0x4b, 0x2b, 0x34, 0xda, 0x4c,
      0x7a, 0x64, 0x61, 0xbd, 0x5c, 0x1f, 0x1b, 0x4e, 0x29, 0x33, 0x8d, 0x21,
      0x1b, 0x87, 0xfa, 0xda, 0x3f, 0x48, 0x6a, 0x13, 0x01, 0x74, 0x85, 0xdb,
      0x83, 0x39, 0x5f, 0x22, 0xd4, 0xf9, 0x2a, 0xca, 0x95, 0x34, 0x53, 0xdf,
      0x16, 0x5b, 0x0a, 0xbf, 0xef, 0x9b, 0xab, 0xad, 0xb1, 0x6e, 0xe7, 0x6a,
      0xc4, 0x68, 0x43, 0xd9, 0xc9, 0x76, 0xf8, 0x60, 0xe6, 0xff, 0xa9, 0x60,
      0xc3, 0x45, 0x11, 0x62, 0xf6, 0x48, 0x15, 0x47, 0x5b, 0x9c, 0x91, 0xf3,
      0xc9, 0x8c, 0x60, 0xc3, 0x3c, 0x6f, 0x03, 0x06, 0xa8, 0xc2, 0xc2, 0x30,
      0xaa, 0x69, 0x81, 0x20, 0x5d, 0x74, 0xfa, 0xcf, 0x69, 0xb2, 0x98, 0xb0,
      0xb9, 0x6e, 0x29, 0xa0, 0x24, 0xa9, 0xb2, 0xa4, 0x8c, 0xd5, 0xf3, 0xda,
      0x5a, 0x61, 0x71, 0x96, 0x9f, 0x9a, 0xdf, 0x4a, 0x79, 0x8f, 0x36, 0xeb,
      0xf6, 0x1f, 0x3e, 0x40, 0x18, 0xf8, 0x1f, 0xf9, 0x0f, 0xfd, 0x9c, 0xe2,
      0x6d, 0x63, 0x84, 0x28, 0xf7, 0xbe, 0x42, 0x49, 0xb6, 0xa2, 0x81, 0x48,
      0xe4, 0xc6, 0xe9, 0xd3, 0xd6, 0x1f, 0x68, 0xbe, 0x10, 0x3e, 0x6d, 0x43,
      0x0c, 0x15, 0x1a, 0x02, 0x50, 0xde, 0x13, 0x8b, 0x57, 0x70, 0x29, 0x3a,
      0x97, 0x7f, 0xa9, 0xbf, 0x5f, 0x52, 0x08, 0xb3, 0x8a, 0x9f, 0xf9, 0x9d,
      0x08, 0x36, 0x3b, 0x9d, 0xc9, 0xcc, 0x65, 0xf2, 0x66, 0xb9, 0x68, 0xcd,
      0xf7, 0x08, 0xfc, 0x31, 0x59, 0x8b, 0xaa, 0x3c, 0x10, 0xf3, 0xce, 0x7a,
      0x50, 0xb3, 0xb2, 0x0a, 0x35, 0xac, 0xb4, 0x24, 0xdc, 0xa4, 0x04, 0xcd,
      0xf9, 0x9f, 0xde, 0xbb, 0xa6, 0x0e, 0xe1, 0x9b, 0x76, 0xac, 0xa1, 0x24,
      0xbd, 0x90, 0xbe, 0xe9, 0xa4, 0xd5, 0x4e, 0xfa, 0x30, 0xb7, 0x14, 0x49,
      0x13, 0xc5, 0x2e, 0x84, 0x83, 0x76, 0x37, 0x93, 0x8f, 0x2d, 0x27, 0x13,
      0x51, 0x19, 0xef, 0x06, 0xd0, 0xdf, 0x74, 0x18, 0x0c, 0xa6, 0xd9, 0x9c,
      0xc1, 0xaa, 0xa6, 0x65, 0x4e, 0x93, 0xf5, 0x4f, 0x9e, 0x92, 0xd1, 0x2e,
      0x18, 0xa0, 0x47, 0xf3, 0x0f, 0xe5, 0x31, 0x9f, 0xfa, 0xcc, 0x1d, 0x46,
      0xe5, 0xcb, 0xcc, 0x56, 0x53, 0xab, 0x24, 0xfa, 0xc1, 0xc2, 0x34, 0x2e,
      0x89, 0x81, 0xf9, 0x7f, 0x44, 0x83, 0x5e, 0xda, 0x88, 0x01, 0x52, 0x6b,
      0x2d, 0x7d, 0x1b, 0x9c, 0x15, 0x98, 0x40, 0x87, 0x46, 0x7b, 0x6c, 0x39,
      0x1e, 0xb0, 0xac, 0xaf, 0x98, 0xda, 0x31, 0x1d,
  };
  uint8_t shake128_expected[512] = {
      0x40, 0x2e, 0x49, 0x68, 0xee, 0x3f, 0x9a, 0x3e, 0xfc, 0x60, 0x02, 0x65,
      0x2c, 0xee, 0x0f, 0xa9, 0x0e, 0xd2, 0x1f, 0x76, 0xce, 0xd9, 0xbb, 0x5f,
      0xda, 0xa6, 0x9a, 0x65, 0x4b, 0xa0, 0x9f, 0x56, 0x4a, 0xb7, 0x92, 0x7c,
      0xa7, 0x7b, 0x48, 0x88, 0xbe, 0xf3, 0x12, 0xbd, 0xb8, 0xf9, 0xa6, 0x89,
      0x23, 0x50, 0xf5, 0x94, 0xc8, 0x1a, 0xde, 0x8d, 0x90, 0xd3, 0x00, 0x0a,
      0x45, 0xe6, 0x60, 0x4e, 0x3b, 0xaf, 0xd7, 0x23, 0x2b, 0xdc, 0x48, 0x20,
      0x3e, 0xe1, 0x31, 0xf8, 0x09, 0x22, 0xdf, 0xed, 0x86, 0x38, 0x80, 0xf7,
      0x4d, 0x0d, 0x18, 0x0b, 0xb4, 0x1f, 0x3a, 0xb3, 0xd4, 0x92, 0x5a, 0x53,
      0xc0, 0x2b, 0xbf, 0x11, 0x8b, 0x07, 0xb4, 0xe0, 0x3d, 0x88, 0x10, 0xff,
      0x0f, 0x69, 0x5e, 0x7f, 0x0f, 0x27, 0x59, 0x5d, 0xf7, 0xb7, 0x9d, 0x4d,
      0x7e, 0xca, 0x27, 0xda, 0x5d, 0xe3, 0xc2, 0x72, 0x3b, 0x95, 0xf3, 0x24,
      0xbe, 0xac, 0x86, 0x3d, 0x0d, 0x65, 0x32, 0x44, 0x3a, 0x29, 0xc9, 0x77,
      0xcd, 0x52, 0x9b, 0x57, 0xf9, 0xc2, 0xf9, 0x10, 0xbe, 0x6a, 0x60, 0x48,
      0x93, 0x23, 0x7f, 0xd8, 0x3b, 0xed, 0x46, 0xdb, 0xbd, 0xa4, 0xcd, 0x72,
      0x11, 0x2f, 0xab, 0xa1, 0x14, 0x41, 0xb0, 0x47, 0x01, 0x9d, 0x7d, 0x7a,
      0xfe, 0x18, 0xac, 0x2a, 0x90, 0xc8, 0xb1, 0x5f, 0xe7, 0xf0, 0x7d, 0xb0,
      0xff, 0xbe, 0xca, 0xdb, 0x06, 0x20, 0x76, 0xb4, 0xd9, 0x0b, 0x1f, 0x02,
      0x5b, 0x9c, 0x2c, 0x45, 0x83, 0x5e, 0x64, 0x25, 0x29, 0xf2, 0x08, 0xd6,
      0xd4, 0x4f, 0x04, 0xb7, 0xd6, 0x04, 0xdf, 0x49, 0x53, 0x0d, 0x9c, 0x80,
      0xa5, 0xdf, 0x30, 0x6b, 0xfb, 0x55, 0x3d, 0x07, 0x89, 0xed, 0x83, 0x16,
      0x12, 0x54, 0x46, 0x47, 0xcd, 0x47, 0x44, 0x56, 0x78, 0xd3, 0x91, 0xd5,
      0x0a, 0xab, 0xce, 0x70, 0x0d, 0x18, 0xa1, 0x4c, 0xdf, 0x78, 0x42, 0x7d,
      0x54, 0x58, 0x40, 0xe9, 0xad, 0x70, 0x45, 0x28, 0x6b, 0x62, 0xeb, 0x51,
      0xec, 0x49, 0xe3, 0xb1, 0x00, 0x49, 0x9d, 0xa6, 0x50, 0xb0, 0x92, 0xe2,
      0x9a, 0xaf, 0x5c, 0xfd, 0x6d, 0x62, 0x89, 0xda, 0x9d, 0x49, 0x14, 0xd5,
      0x34, 0xaa, 0x41, 0x26, 0xaf, 0x72, 0x8d, 0xa9, 0xb6, 0xf5, 0x79, 0xa0,
      0x36, 0x0e, 0x57, 0xf5, 0xb9, 0xe3, 0x7c, 0xdc, 0x9c, 0xfc, 0x8a, 0x69,
      0x6a, 0x9c, 0x2a, 0xd9, 0xfd, 0xc3, 0x34, 0xe7, 0x99, 0x70, 0xaf, 0x8d,
      0x65, 0x51, 0x19, 0xf9, 0xae, 0x86, 0xd4, 0x0a, 0x5f, 0x47, 0xe9, 0xbf,
      0x1d, 0x05, 0x9e, 0xa3, 0x29, 0x97, 0x3a, 0x43, 0x14, 0x2e, 0xa3, 0x48,
      0x1e, 0x40, 0xc6, 0xf6, 0x7f, 0x8a, 0x26, 0xed, 0x9b, 0x27, 0x98, 0x2d,
      0x27, 0xa5, 0x61, 0xd9, 0xf6, 0xa6, 0x13, 0x55, 0xd8, 0xb4, 0x73, 0x5e,
      0xcf, 0x7b, 0x08, 0x85, 0x74, 0x82, 0x42, 0x11, 0x0f, 0x01, 0xcc, 0xc3,
      0x2a, 0xda, 0x45, 0x47, 0x84, 0x87, 0xa2, 0xa5, 0x41, 0xc0, 0xe1, 0x87,
      0xc5, 0xee, 0x1d, 0xd2, 0x57, 0xbc, 0x7c, 0x81, 0x02, 0x42, 0xb7, 0xf6,
      0x3a, 0x3a, 0xb1, 0x4e, 0xe7, 0xc4, 0x57, 0xd3, 0xbf, 0x6d, 0xef, 0x86,
      0x90, 0x46, 0xbf, 0x4b, 0x82, 0xe9, 0x9f, 0x5b, 0x40, 0x62, 0xa9, 0x9c,
      0x11, 0xfc, 0xd7, 0x79, 0x39, 0xf6, 0x2a, 0x44, 0xe8, 0x3d, 0x0b, 0x7a,
      0x19, 0xeb, 0x92, 0x87, 0xd5, 0x5d, 0xcd, 0x35, 0xfe, 0x89, 0xb8, 0x25,
      0x84, 0xf0, 0xfc, 0xfc, 0x47, 0x0e, 0xdc, 0xb7, 0x5f, 0xf8, 0xe8, 0x8b,
      0x13, 0xa7, 0x14, 0x53, 0xcf, 0xd4, 0xeb, 0x25, 0x9f, 0x9e, 0x0d, 0x04,
      0x61, 0xae, 0x9a, 0x44, 0x0e, 0x67, 0x85, 0x90, 0xed, 0x0e, 0x2a, 0x5f,
      0x4c, 0xd9, 0xd7, 0xbe, 0x94, 0x61, 0x64, 0xdc,
  };
  uint8_t shake256_expected[512] = {
      0x6b, 0x70, 0x1d, 0x95, 0xb4, 0x8d, 0xa5, 0x89, 0xea, 0xde, 0x36, 0xf6,
      0x21, 0xfd, 0x24, 0x9b, 0x85, 0x9c, 0x71, 0x25, 0xd2, 0x63, 0x30, 0xbe,
      0x02, 0xee, 0xab, 0xb5, 0x7e, 0x13, 0x92, 0x34, 0x27, 0x5f, 0x78, 0x05,
      0x86, 0x5d, 0x1c, 0x74, 0xd3, 0xb5, 0x22, 0x79, 0x16, 0x80, 0xab, 0x29,
      0x71, 0xa7, 0x28, 0x52, 0xc8, 0xf0, 0x24, 0x6e, 0xf2, 0xa4, 0x15, 0x7a,
      0xee, 0x78, 0xba, 0x5d, 0x75, 0x58, 0x6c, 0x31, 0x49, 0xde, 0x32, 0x29,
      0xbf, 0xb3, 0x21, 0xf6, 0xb8, 0xbd, 0x0a, 0xc7, 0x64, 0x1b, 0x15, 0x92,
      0x21, 0x02, 0x7b, 0x51, 0xd3, 0xb3, 0x8a, 0x57, 0x3a, 0xfa, 0xa9, 0x0e,
      0x79, 0xf4, 0xb7, 0xcc, 0x0a, 0xec, 0x99, 0x81, 0x6c, 0x78, 0x61, 0xa9,
      0x7b, 0x6f, 0xb5, 0x45, 0xa2, 0xa6, 0xc0, 0x12, 0xce, 0x0b, 0x95, 0x58,
      0x0f, 0x25, 0x0a, 0xb3, 0x39, 0x87, 0x14, 0xb8, 0x8c, 0x2a, 0xfb, 0x87,
      0x91, 0x6a, 0x09, 0x6e, 0x6d, 0x1a, 0xd6, 0xc3, 0x99, 0xab, 0xd3, 0x2d,
      0x4a, 0xb2, 0x2b, 0x22, 0xb9, 0x5a, 0x70, 0x1e, 0x93, 0xd7, 0x91, 0x7f,
      0xbd, 0x16, 0xe9, 0x43, 0x1e, 0xd3, 0x68, 0x44, 0x60, 0x4f, 0xe0, 0xc3,
      0x6a, 0xa9, 0xd1, 0x05, 0xd8, 0x1b, 0xfa, 0xb8, 0xea, 0x7b, 0xcf, 0x82,
      0xb1, 0x2c, 0x42, 0x0c, 0x17, 0x6e, 0x96, 0xd6, 0xe5, 0xd0, 0xbd, 0x1d,
      0x7f, 0x66, 0x36, 0x31, 0x48, 0x44, 0x60, 0x5d, 0x0d, 0x69, 0x00, 0x23,
      0xe4, 0xcc, 0x72, 0x84, 0x09, 0xd2, 0xd3, 0x4f, 0x47, 0x63, 0xcb, 0xc3,
      0x19, 0x50, 0xaa, 0x57, 0x69, 0xbf, 0x5a, 0x08, 0x65, 0xf8, 0xe1, 0xbd,
      0xe0, 0xeb, 0xed, 0x59, 0x6e, 0xb9, 0xee, 0x8c, 0x58, 0xe4, 0x0a, 0x43,
      0xcc, 0x38, 0x39, 0x1f, 0x28, 0xad, 0xab, 0x3a, 0x5c, 0xae, 0x5c, 0x6b,
      0x23, 0xd0, 0x19, 0x49, 0x81, 0xa8, 0x97, 0x8c, 0x59, 0x17, 0xb3, 0x84,
      0x11, 0x77, 0xff, 0x33, 0x19, 0xb6, 0xa9, 0xa9, 0xa4, 0x8c, 0x2f, 0x0f,
      0xb9, 0xb3, 0x12, 0xa3, 0x0f, 0x89, 0x84, 0xd4, 0xc4, 0x9f, 0xeb, 0x27,
      0xa6, 0x61, 0x34, 0x9a, 0x2a, 0x2c, 0xc5, 0x3f, 0x45, 0xcc, 0xe6, 0xad,
      0x2f, 0xa3, 0x16, 0x7b, 0x42, 0xda, 0x34, 0xae, 0xa8, 0x58, 0xc1, 0xe3,
      0xf9, 0xd5, 0xef, 0xfc, 0x64, 0xbc, 0xb6, 0xad, 0x6a, 0x71, 0x17, 0x09,
      0x21, 0x06, 0x80, 0x6a, 0x19, 0xb6, 0x0a, 0x1b, 0xb9, 0xe0, 0xf5, 0x43,
      0x87, 0xe4, 0x84, 0x7e, 0x5f, 0x09, 0xde, 0x97, 0x31, 0xdc, 0x9f, 0xe8,
      0xd8, 0xdc, 0x1d, 0x6b, 0x01, 0xfa, 0x1e, 0xd0, 0x11, 0x1f, 0x8b, 0x28,
      0x8e, 0xc1, 0x4d, 0x4f, 0x32, 0x27, 0x2d, 0x7c, 0x4a, 0xc2, 0x3c, 0x85,
      0x98, 0xf2, 0xa4, 0x5a, 0x5a, 0xaa, 0x1f, 0xac, 0x35, 0xef, 0xca, 0x81,
      0x6b, 0xf2, 0xcb, 0x83, 0x33, 0x97, 0xb7, 0x46, 0x8e, 0x99, 0x27, 0x48,
      0xbc, 0x0f, 0x85, 0xac, 0xc2, 0xc7, 0x31, 0x58, 0x11, 0x1e, 0x88, 0xd6,
      0xc6, 0x8e, 0xad, 0x22, 0xa8, 0x3f, 0xb6, 0x16, 0x28, 0xcc, 0x28, 0x4a,
      0x05, 0x4f, 0x4e, 0x52, 0x6a, 0xb2, 0xe1, 0x4b, 0x57, 0xc7, 0x9a, 0xa4,
      0x3a, 0x00, 0xb5, 0x5b, 0x1b, 0xe2, 0xdd, 0xf3, 0x2f, 0xf8, 0xe7, 0xf4,
      0xc5, 0x0a, 0x8a, 0x7e, 0xc4, 0x90, 0xb1, 0xc6, 0x4a, 0xcd, 0x66, 0x9e,
      0xe9, 0x8a, 0xde, 0x15, 0x07, 0x16, 0xe7, 0xdc, 0x23, 0x16, 0xb3, 0xb2,
      0xe0, 0x4b, 0x94, 0x9d, 0xec, 0x9f, 0x50, 0x6b, 0x70, 0x50, 0xb2, 0xb0,
      0x12, 0x11, 0x46, 0x16, 0x4e, 0xb6, 0x60, 0x22, 0x83, 0x27, 0x6c, 0x76,
      0x62, 0xb3, 0xb7, 0x83, 0x91, 0xd7, 0x10, 0x3f, 0xbf, 0x7a, 0x3b, 0x39,
      0x5d, 0xf9, 0x50, 0x1d, 0x05, 0x46, 0xa0, 0xe7,
  };

  uint8_t shake128_output[512];
  uint8_t shake256_output[512];

  struct BORINGSSL_keccak_st ctx;

  // Multi-pass absorb.
  for (size_t j = 0; j < sizeof(input); j++) {
    for (size_t i = 0; i < j; i++) {
      OPENSSL_memset(shake128_output, 0, sizeof(shake128_output));
      BORINGSSL_keccak_init(&ctx, boringssl_shake128);
      BORINGSSL_keccak_absorb(&ctx, input, i);
      BORINGSSL_keccak_absorb(&ctx, &input[i], j - i);
      BORINGSSL_keccak_absorb(&ctx, &input[j], sizeof(input) - j);
      BORINGSSL_keccak_squeeze(&ctx, shake128_output, sizeof(shake128_output));
      EXPECT_EQ(Bytes(shake128_expected), Bytes(shake128_output));
    }
  }

  for (size_t j = 0; j < sizeof(input); j++) {
    for (size_t i = 0; i < j; i++) {
      OPENSSL_memset(shake256_output, 0, sizeof(shake256_output));
      BORINGSSL_keccak_init(&ctx, boringssl_shake256);
      BORINGSSL_keccak_absorb(&ctx, input, i);
      BORINGSSL_keccak_absorb(&ctx, &input[i], j - i);
      BORINGSSL_keccak_absorb(&ctx, &input[j], sizeof(input) - j);
      BORINGSSL_keccak_squeeze(&ctx, shake256_output, sizeof(shake256_output));
      EXPECT_EQ(Bytes(shake256_expected), Bytes(shake256_output));
    }
  }

  // Multi-pass squeeze.
  for (size_t j = 0; j < sizeof(shake128_output); j++) {
    for (size_t i = 0; i < j; i++) {
      OPENSSL_memset(shake128_output, 0, sizeof(shake128_output));
      BORINGSSL_keccak_init(&ctx, boringssl_shake128);
      BORINGSSL_keccak_absorb(&ctx, input, sizeof(input));
      BORINGSSL_keccak_squeeze(&ctx, shake128_output, i);
      BORINGSSL_keccak_squeeze(&ctx, &shake128_output[i], j - i);
      BORINGSSL_keccak_squeeze(&ctx, &shake128_output[j],
                               sizeof(shake128_output) - j);
      EXPECT_EQ(Bytes(shake128_expected), Bytes(shake128_output));
    }
  }

  for (size_t j = 0; j < sizeof(shake256_output); j++) {
    for (size_t i = 0; i < j; i++) {
      OPENSSL_memset(shake256_output, 0, sizeof(shake256_output));
      BORINGSSL_keccak_init(&ctx, boringssl_shake256);
      BORINGSSL_keccak_absorb(&ctx, input, sizeof(input));
      BORINGSSL_keccak_squeeze(&ctx, shake256_output, i);
      BORINGSSL_keccak_squeeze(&ctx, &shake256_output[i], j - i);
      BORINGSSL_keccak_squeeze(&ctx, &shake256_output[j],
                               sizeof(shake256_output) - j);
      EXPECT_EQ(Bytes(shake256_expected), Bytes(shake256_output));
    }
  }
}
