/*!
 * \copy
 *     Copyright (c)  2013, Cisco Systems
 *     All rights reserved.
 *
 *     Redistribution and use in source and binary forms, with or without
 *     modification, are permitted provided that the following conditions
 *     are met:
 *
 *        * Redistributions of source code must retain the above copyright
 *          notice, this list of conditions and the following disclaimer.
 *
 *        * Redistributions in binary form must reproduce the above copyright
 *          notice, this list of conditions and the following disclaimer in
 *          the documentation and/or other materials provided with the
 *          distribution.
 *
 *     THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 *     "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 *     LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS
 *     FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 *     COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
 *     INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 *     BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 *     LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER
 *     CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT
 *     LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN
 *     ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 *     POSSIBILITY OF SUCH DAMAGE.
 *
 */

// exp_data.c
// export date cross various modules (.c)
#include "wels_common_basis.h"
#include "mb_cache.h"
#include "vlc_decoder.h"

namespace WelsDec {

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////


const uint8_t g_kuiScan4[16] = { //for mb cache in sMb (only current element, without neighbor)
  // 4*4block scan    mb cache order
  0,  1,  4,  5,        // 0  1 | 4  5      0  1 | 2  3
  2,  3,  6,  7,        // 2  3 | 6  7      4  5 | 6  7
  8,  9, 12, 13,        //----------------->-----------
  10, 11, 14, 15        // 8  9 |12 13      8  9 |10 11
};                        //10 11 |14 15     12 13 |14 15

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

// extern at wels_common_basis.h

/*common use table*/
const uint8_t g_kMbNonZeroCountIdx[24] = {
  //  0   1 | 4  5      luma 8*8 block           pNonZeroCount[16+8]
  0,  1,  4,  5,   //  2   3 | 6  7        0  |  1                  0   1   2   3
  2,  3,  6,  7,   //---------------      ---------                 4   5   6   7
  8,  9, 12, 13,   //  8   9 | 12 13       2  |  3                  8   9  10  11
  10, 11, 14, 15,   // 10  11 | 14 15-----------------------------> 12  13  14  15
  16, 17, 20, 21,   //----------------    chroma 8*8 block          16  17  18  19
  18, 19, 22, 23   // 16  17 | 20 21        0    1                 20  21  22  23
};
//cache element equal to 26

const uint8_t g_kCacheNzcScanIdx[4 * 4 + 4 + 4 + 3] = {
  /* Luma */
  9, 10, 17, 18,        // 1+1*8, 2+1*8, 1+2*8, 2+2*8,
  11, 12, 19, 20,       // 3+1*8, 4+1*8, 3+2*8, 4+2*8,
  25, 26, 33, 34,       // 1+3*8, 2+3*8, 1+4*8, 2+4*8,
  27, 28, 35, 36,       // 3+3*8, 4+3*8, 3+4*8, 4+4*8,
  /* Cb */
  14, 15,               // 6+1*8, 7+1*8,
  22, 23,               // 6+2*8, 7+2*8,

  /* Cr */
  38, 39,               // 6+4*8, 7+4*8,
  46, 47,               // 6+5*8, 7+5*8,
  /* Luma DC */
  41,   // 1+5*8
  /* Chroma DC */
  42, 43 // 2+5*8, 3+5*8,
};

const uint8_t g_kCache26ScanIdx[16] = { //intra4*4_pred_mode and pNonZeroCount cache scan index, 4*4 block as basic unit
  6,  7, 11, 12,
  8,  9, 13, 14,
  16, 17, 21, 22,
  18, 19, 23, 24
};

//cache element equal to 30
const uint8_t g_kCache30ScanIdx[16] = { //mv or pRefIndex cache scan index, 4*4 block as basic unit
  7,  8, 13, 14,
  9, 10, 15, 16,
  19, 20, 25, 26,
  21, 22, 27, 28
};

const uint8_t g_kNonZeroScanIdxC[4] = { //pNonZeroCount cache for chroma, 4*4 block as basic unit
  4, 5,
  7, 8
};

const uint8_t g_kuiScan8[24] = {        // [16 + 2*4]
  9, 10, 17, 18,        // 1+1*8, 2+1*8, 1+2*8, 2+2*8,
  11, 12, 19, 20,       // 3+1*8, 4+1*8, 3+2*8, 4+2*8,
  25, 26, 33, 34,       // 1+3*8, 2+3*8, 1+4*8, 2+4*8,
  27, 28, 35, 36,       // 3+3*8, 4+3*8, 3+4*8, 4+4*8,
  14, 15,               // 6+1*8, 7+1*8,
  22, 23,               // 6+2*8, 7+2*8,
  38, 39,               // 6+4*8, 7+4*8,
  46, 47,               // 6+5*8, 7+5*8,
};

const uint8_t g_kuiLumaDcZigzagScan[16] = {
  0, 16, 32, 128,               // 0*16 + 0*64, 1*16 + 0*64, 2*16 + 0*64, 0*16 + 2*64,
  48, 64, 80, 96,               // 3*16 + 0*64, 0*16 + 1*64, 1*16 + 1*64, 2*16 + 1*64,
  144, 160, 176, 192,           // 1*16 + 2*64, 2*16 + 2*64, 3*16 + 2*64, 0*16 + 3*64,
  112, 208, 224, 240            // 3*16 + 1*64, 1*16 + 3*64, 2*16 + 3*64, 3*16 + 3*64,
};

const uint8_t g_kuiChromaDcScan[4] = {
  0, 16, 32, 48
};

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

const uint8_t g_kuiIntra4x4CbpTable[48] = {
  47, 31, 15,  0, 23, 27, 29, 30,  7, 11, 13, 14, 39, 43, 45, 46, //15
  16,  3,  5, 10, 12, 19, 21, 26, 28, 35, 37, 42, 44,  1,  2,  4, //31
  8, 17, 18, 20, 24,  6,  9, 22, 25, 32, 33, 34, 36, 40, 38, 41  //47
};

const uint8_t g_kuiIntra4x4CbpTable400[16] = {
  15, 0, 7, 11, 13, 14, 3, 5, 10, 12, 1, 2, 4, 8, 6, 9

};

const uint8_t g_kuiInterCbpTable[48] = {
  0, 16,  1,  2,  4,  8, 32,  3,  5, 10, 12, 15, 47,  7, 11, 13, //15
  14,  6,  9, 31, 35, 37, 42, 44, 33, 34, 36, 40, 39, 43, 45, 46, //31
  17, 18, 20, 24, 19, 21, 26, 28, 23, 27, 29, 30, 22, 25, 38, 41  //47
};

const uint8_t g_kuiInterCbpTable400[16] = {
  0, 1, 2, 4, 8, 3, 5, 10, 12, 15, 7, 11, 13, 14, 6, 9
};

const uint8_t g_kuiLeadingZeroTable[256] = {
  8,  7,  6,  6,  5,  5,  5,  5,  4,  4,  4,  4,  4,  4,  4,  4,
  3,  3,  3,  3,  3,  3,  3,  3,  3,  3,  3,  3,  3,  3,  3,  3,
  2,  2,  2,  2,  2,  2,  2,  2,  2,  2,  2,  2,  2,  2,  2,  2,
  2,  2,  2,  2,  2,  2,  2,  2,  2,  2,  2,  2,  2,  2,  2,  2,
  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,  1,
  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,
  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0,  0
};

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////

// extern at vlc_decoder.h

const uint8_t g_kuiVlcChromaTable[256][2] = {
  {13, 7}, {13, 7}, {12, 8}, {11, 8}, {8, 7}, {8, 7}, {7, 7}, {7, 7}, {10, 6}, {10, 6}, {10, 6}, {10, 6}, {6, 6}, {6, 6}, {6, 6}, {6, 6}, //15
  { 3, 6}, { 3, 6}, { 3, 6}, { 3, 6}, {9, 6}, {9, 6}, {9, 6}, {9, 6}, { 4, 6}, { 4, 6}, { 4, 6}, { 4, 6}, {1, 6}, {1, 6}, {1, 6}, {1, 6}, //31
  { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, {5, 3}, {5, 3}, {5, 3}, {5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, {5, 3}, {5, 3}, {5, 3}, {5, 3}, //47
  { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, {5, 3}, {5, 3}, {5, 3}, {5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, {5, 3}, {5, 3}, {5, 3}, {5, 3}, //63
  { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, {0, 2}, {0, 2}, {0, 2}, {0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, {0, 2}, {0, 2}, {0, 2}, {0, 2}, //79
  { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, {0, 2}, {0, 2}, {0, 2}, {0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, {0, 2}, {0, 2}, {0, 2}, {0, 2}, //95
  { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, {0, 2}, {0, 2}, {0, 2}, {0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, {0, 2}, {0, 2}, {0, 2}, {0, 2}, //111
  { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, {0, 2}, {0, 2}, {0, 2}, {0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, {0, 2}, {0, 2}, {0, 2}, {0, 2}, //127
  { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, {2, 1}, {2, 1}, {2, 1}, {2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, {2, 1}, {2, 1}, {2, 1}, {2, 1}, //143
  { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, {2, 1}, {2, 1}, {2, 1}, {2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, {2, 1}, {2, 1}, {2, 1}, {2, 1}, //159
  { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, {2, 1}, {2, 1}, {2, 1}, {2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, {2, 1}, {2, 1}, {2, 1}, {2, 1}, //175
  { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, {2, 1}, {2, 1}, {2, 1}, {2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, {2, 1}, {2, 1}, {2, 1}, {2, 1}, //191
  { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, {2, 1}, {2, 1}, {2, 1}, {2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, {2, 1}, {2, 1}, {2, 1}, {2, 1}, //207
  { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, {2, 1}, {2, 1}, {2, 1}, {2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, {2, 1}, {2, 1}, {2, 1}, {2, 1}, //223
  { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, {2, 1}, {2, 1}, {2, 1}, {2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, {2, 1}, {2, 1}, {2, 1}, {2, 1}, //239
  { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, {2, 1}, {2, 1}, {2, 1}, {2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1}, {2, 1}, {2, 1}, {2, 1}, {2, 1}  //255
};

const uint8_t g_kuiVlcTable_0[256][2]
= { //[0] means the index of vlc table, [1] means the length of vlc code  [256] value means the value of 8bits
  { 0, 0}, { 0, 0}, { 0, 0}, {0, 0}, {21, 8}, {12, 8}, {7, 8}, {3, 8}, {17, 7}, {17, 7}, {8, 7}, {8, 7}, {13, 6}, {13, 6}, {13, 6}, {13, 6}, //15
  { 4, 6}, { 4, 6}, { 4, 6}, {4, 6}, { 1, 6}, { 1, 6}, {1, 6}, {1, 6}, { 9, 5}, { 9, 5}, {9, 5}, {9, 5}, { 9, 5}, { 9, 5}, { 9, 5}, { 9, 5}, //31
  { 5, 3}, { 5, 3}, { 5, 3}, {5, 3}, { 5, 3}, { 5, 3}, {5, 3}, {5, 3}, { 5, 3}, { 5, 3}, {5, 3}, {5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, //47
  { 5, 3}, { 5, 3}, { 5, 3}, {5, 3}, { 5, 3}, { 5, 3}, {5, 3}, {5, 3}, { 5, 3}, { 5, 3}, {5, 3}, {5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, //63
  { 2, 2}, { 2, 2}, { 2, 2}, {2, 2}, { 2, 2}, { 2, 2}, {2, 2}, {2, 2}, { 2, 2}, { 2, 2}, {2, 2}, {2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, //79
  { 2, 2}, { 2, 2}, { 2, 2}, {2, 2}, { 2, 2}, { 2, 2}, {2, 2}, {2, 2}, { 2, 2}, { 2, 2}, {2, 2}, {2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, //95
  { 2, 2}, { 2, 2}, { 2, 2}, {2, 2}, { 2, 2}, { 2, 2}, {2, 2}, {2, 2}, { 2, 2}, { 2, 2}, {2, 2}, {2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, //111
  { 2, 2}, { 2, 2}, { 2, 2}, {2, 2}, { 2, 2}, { 2, 2}, {2, 2}, {2, 2}, { 2, 2}, { 2, 2}, {2, 2}, {2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, //127
  { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, //143
  { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, //159
  { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, //175
  { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, //191
  { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, //207
  { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, //223
  { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, //239
  { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1} //255
};

const uint8_t g_kuiVlcTable_0_0[256][2] = { // read 8 bits  // for g_kuiVlcTable_0[0] //checked no error--
  { 0, 0}, { 0, 0}, {47, 7}, {47, 7}, {58, 8}, {60, 8}, {59, 8}, {54, 8}, {61, 8}, {56, 8}, {55, 8}, {50, 8}, {57, 8}, {52, 8}, {51, 8}, {46, 8}, //15
  {53, 7}, {53, 7}, {48, 7}, {48, 7}, {43, 7}, {43, 7}, {42, 7}, {42, 7}, {49, 7}, {49, 7}, {44, 7}, {44, 7}, {39, 7}, {39, 7}, {38, 7}, {38, 7}, //31
  {45, 6}, {45, 6}, {45, 6}, {45, 6}, {40, 6}, {40, 6}, {40, 6}, {40, 6}, {35, 6}, {35, 6}, {35, 6}, {35, 6}, {34, 6}, {34, 6}, {34, 6}, {34, 6}, //47
  {41, 6}, {41, 6}, {41, 6}, {41, 6}, {36, 6}, {36, 6}, {36, 6}, {36, 6}, {31, 6}, {31, 6}, {31, 6}, {31, 6}, {30, 6}, {30, 6}, {30, 6}, {30, 6}, //63
  {26, 5}, {26, 5}, {26, 5}, {26, 5}, {26, 5}, {26, 5}, {26, 5}, {26, 5}, {32, 5}, {32, 5}, {32, 5}, {32, 5}, {32, 5}, {32, 5}, {32, 5}, {32, 5}, //79
  {27, 5}, {27, 5}, {27, 5}, {27, 5}, {27, 5}, {27, 5}, {27, 5}, {27, 5}, {22, 5}, {22, 5}, {22, 5}, {22, 5}, {22, 5}, {22, 5}, {22, 5}, {22, 5}, //95
  {37, 5}, {37, 5}, {37, 5}, {37, 5}, {37, 5}, {37, 5}, {37, 5}, {37, 5}, {28, 5}, {28, 5}, {28, 5}, {28, 5}, {28, 5}, {28, 5}, {28, 5}, {28, 5}, //111
  {23, 5}, {23, 5}, {23, 5}, {23, 5}, {23, 5}, {23, 5}, {23, 5}, {23, 5}, {18, 5}, {18, 5}, {18, 5}, {18, 5}, {18, 5}, {18, 5}, {18, 5}, {18, 5}, //127
  {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, //143
  {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, {33, 3}, //159
  {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, //175
  {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, {24, 3}, //191
  {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, //207
  {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, {19, 3}, //223
  {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, //239
  {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3}, {14, 3} //255
};

const uint8_t g_kuiVlcTable_0_1[4][2] = { // read 2 bits // for g_kuiVlcTable_0[1] //checked no error--
  {29, 2}, {20, 2}, {15, 2}, {10, 2}
};

const uint8_t g_kuiVlcTable_0_2[2][2] = { // read 1 bit // for g_kuiVlcTable_0[2] //checked no error--
  {25, 1}, {16, 1}
};

const uint8_t g_kuiVlcTable_0_3[2][2] = { // read 1 bit // for g_kuiVlcTable_0[3] //checked no error--
  {11, 1}, {6, 1}
};

const uint8_t g_kuiVlcTable_1[256][2] = { //checked no error--
  { 0, 0}, { 0, 0}, { 0, 0}, { 0, 0}, {14, 8}, {20, 8}, {19, 8}, {10, 8}, {29, 7}, {29, 7}, {16, 7}, {16, 7}, {15, 7}, {15, 7}, { 6, 7}, { 6, 7}, //15
  {25, 6}, {25, 6}, {25, 6}, {25, 6}, {12, 6}, {12, 6}, {12, 6}, {12, 6}, {11, 6}, {11, 6}, {11, 6}, {11, 6}, { 3, 6}, { 3, 6}, { 3, 6}, { 3, 6}, //31
  {21, 6}, {21, 6}, {21, 6}, {21, 6}, { 8, 6}, { 8, 6}, { 8, 6}, { 8, 6}, { 7, 6}, { 7, 6}, { 7, 6}, { 7, 6}, { 1, 6}, { 1, 6}, { 1, 6}, { 1, 6}, //47
  {17, 5}, {17, 5}, {17, 5}, {17, 5}, {17, 5}, {17, 5}, {17, 5}, {17, 5}, { 4, 5}, { 4, 5}, { 4, 5}, { 4, 5}, { 4, 5}, { 4, 5}, { 4, 5}, { 4, 5}, //63
  {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, //79
  { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, //95
  { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, //111
  { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, //127
  { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, //143
  { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, //159
  { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, //175
  { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, //191
  { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, //207
  { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, //223
  { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, //239
  { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2}, { 0, 2} //255

};

const uint8_t g_kuiVlcTable_1_0[64][2] = { // read 6 bits  // for g_kuiVlcTable_1[0] //checked no error--
  { 0, 0}, { 0, 0}, {57, 5}, {57, 5}, {61, 6}, {60, 6}, {59, 6}, {58, 6}, {55, 6}, {54, 6}, {56, 6}, {51, 6}, {52, 5}, {52, 5}, {50, 5}, {50, 5}, //15
  {53, 5}, {53, 5}, {48, 5}, {48, 5}, {47, 5}, {47, 5}, {46, 5}, {46, 5}, {49, 5}, {49, 5}, {44, 5}, {44, 5}, {43, 5}, {43, 5}, {42, 5}, {42, 5}, //31
  {38, 4}, {38, 4}, {38, 4}, {38, 4}, {40, 4}, {40, 4}, {40, 4}, {40, 4}, {39, 4}, {39, 4}, {39, 4}, {39, 4}, {34, 4}, {34, 4}, {34, 4}, {34, 4}, //47
  {45, 4}, {45, 4}, {45, 4}, {45, 4}, {36, 4}, {36, 4}, {36, 4}, {36, 4}, {35, 4}, {35, 4}, {35, 4}, {35, 4}, {30, 4}, {30, 4}, {30, 4}, {30, 4} //63
};

const uint8_t g_kuiVlcTable_1_1[8][2] = { // read 3 bits // for g_kuiVlcTable_1[1] //checked no error--
  {41, 3}, {32, 3}, {31, 3}, {26, 3}, {37, 3}, {28, 3}, {27, 3}, {22, 3}
};

const uint8_t g_kuiVlcTable_1_2[2][2] = { // read 1 bit // for g_kuiVlcTable_1[2] //checked no error--
  {33, 1}, {24, 1}
};

const uint8_t g_kuiVlcTable_1_3[2][2] = { // read 1 bit // for g_kuiVlcTable_1[3] //checked no error--
  {23, 1}, {18, 1}
};

const uint8_t g_kuiVlcTable_2[256][2] = { //checked no error--
  { 0, 0}, { 0, 0}, { 0, 0}, { 0, 0}, { 0, 0}, { 0, 0}, { 0, 0}, { 0, 0}, {45, 8}, {40, 8}, {35, 8}, {30, 8}, {41, 8}, {36, 8}, {31, 8}, {26, 8}, //15
  {22, 7}, {22, 7}, {18, 7}, {18, 7}, {32, 7}, {32, 7}, {14, 7}, {14, 7}, {37, 7}, {37, 7}, {28, 7}, {28, 7}, {27, 7}, {27, 7}, {10, 7}, {10, 7}, //31
  { 6, 6}, { 6, 6}, { 6, 6}, { 6, 6}, {24, 6}, {24, 6}, {24, 6}, {24, 6}, {23, 6}, {23, 6}, {23, 6}, {23, 6}, { 3, 6}, { 3, 6}, { 3, 6}, { 3, 6}, //47
  {33, 6}, {33, 6}, {33, 6}, {33, 6}, {20, 6}, {20, 6}, {20, 6}, {20, 6}, {19, 6}, {19, 6}, {19, 6}, {19, 6}, { 1, 6}, { 1, 6}, { 1, 6}, { 1, 6}, //63
  {15, 5}, {15, 5}, {15, 5}, {15, 5}, {15, 5}, {15, 5}, {15, 5}, {15, 5}, {16, 5}, {16, 5}, {16, 5}, {16, 5}, {16, 5}, {16, 5}, {16, 5}, {16, 5}, //79
  {11, 5}, {11, 5}, {11, 5}, {11, 5}, {11, 5}, {11, 5}, {11, 5}, {11, 5}, {12, 5}, {12, 5}, {12, 5}, {12, 5}, {12, 5}, {12, 5}, {12, 5}, {12, 5}, //95
  { 7, 5}, { 7, 5}, { 7, 5}, { 7, 5}, { 7, 5}, { 7, 5}, { 7, 5}, { 7, 5}, {29, 5}, {29, 5}, {29, 5}, {29, 5}, {29, 5}, {29, 5}, {29, 5}, {29, 5}, //111
  { 8, 5}, { 8, 5}, { 8, 5}, { 8, 5}, { 8, 5}, { 8, 5}, { 8, 5}, { 8, 5}, { 4, 5}, { 4, 5}, { 4, 5}, { 4, 5}, { 4, 5}, { 4, 5}, { 4, 5}, { 4, 5}, //127
  {25, 4}, {25, 4}, {25, 4}, {25, 4}, {25, 4}, {25, 4}, {25, 4}, {25, 4}, {25, 4}, {25, 4}, {25, 4}, {25, 4}, {25, 4}, {25, 4}, {25, 4}, {25, 4}, //143
  {21, 4}, {21, 4}, {21, 4}, {21, 4}, {21, 4}, {21, 4}, {21, 4}, {21, 4}, {21, 4}, {21, 4}, {21, 4}, {21, 4}, {21, 4}, {21, 4}, {21, 4}, {21, 4}, //159
  {17, 4}, {17, 4}, {17, 4}, {17, 4}, {17, 4}, {17, 4}, {17, 4}, {17, 4}, {17, 4}, {17, 4}, {17, 4}, {17, 4}, {17, 4}, {17, 4}, {17, 4}, {17, 4}, //175
  {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, {13, 4}, //191
  { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, { 9, 4}, //207
  { 5, 4}, { 5, 4}, { 5, 4}, { 5, 4}, { 5, 4}, { 5, 4}, { 5, 4}, { 5, 4}, { 5, 4}, { 5, 4}, { 5, 4}, { 5, 4}, { 5, 4}, { 5, 4}, { 5, 4}, { 5, 4}, //223
  { 2, 4}, { 2, 4}, { 2, 4}, { 2, 4}, { 2, 4}, { 2, 4}, { 2, 4}, { 2, 4}, { 2, 4}, { 2, 4}, { 2, 4}, { 2, 4}, { 2, 4}, { 2, 4}, { 2, 4}, { 2, 4}, //239
  { 0, 4}, { 0, 4}, { 0, 4}, { 0, 4}, { 0, 4}, { 0, 4}, { 0, 4}, { 0, 4}, { 0, 4}, { 0, 4}, { 0, 4}, { 0, 4}, { 0, 4}, { 0, 4}, { 0, 4}, { 0, 4} //255
};

const uint8_t g_kuiVlcTable_2_0[4][2] = { // read 2 bits // for g_kuiVlcTable_2[0] //checked
  {0, 0}, {58, 2}, {61, 2}, {60, 2}
};


const uint8_t g_kuiVlcTable_2_1[4][2] = { // read 2 bits // for g_kuiVlcTable_2[1] //checked
  {59, 2}, {54, 2}, {57, 2}, {56, 2}
};

const uint8_t g_kuiVlcTable_2_2[4][2] = { // read 2 bits // for g_kuiVlcTable_2[2] //checked
  {55, 2}, {50, 2}, {53, 2}, {52, 2}
};

const uint8_t g_kuiVlcTable_2_3[4][2] = { // read 2 bits // for g_kuiVlcTable_2[3] //checked
  {51, 2}, {46, 2}, {47, 1}, {47, 1}
};

const uint8_t g_kuiVlcTable_2_4[2][2] = { // read 1 bit // for g_kuiVlcTable_2[4] //checked
  {42, 1}, {48, 1}
};

const uint8_t g_kuiVlcTable_2_5[2][2] = { // read 1 bit // for g_kuiVlcTable_2[5] //checked
  {43, 1}, {38, 1}
};

const uint8_t g_kuiVlcTable_2_6[2][2] = { // read 1 bit // for g_kuiVlcTable_2[6] //checked no error--
  {49, 1}, {44, 1}
};

const uint8_t g_kuiVlcTable_2_7[2][2] = { // read 1 bit // for g_kuiVlcTable_2[7] //checked no error--
  {39, 1}, {34, 1}
};

const uint8_t g_kuiVlcTable_3[64][2] = { // read 6 bits //corrected
  { 1, 6}, { 2, 6}, { 0, 0}, { 0, 6}, { 3, 6}, { 4, 6}, { 5, 6}, { 0, 0}, { 6, 6}, { 7, 6}, { 8, 6}, { 9, 6}, {10, 6}, {11, 6}, {12, 6}, {13, 6}, //15
  {14, 6}, {15, 6}, {16, 6}, {17, 6}, {18, 6}, {19, 6}, {20, 6}, {21, 6}, {22, 6}, {23, 6}, {24, 6}, {25, 6}, {26, 6}, {27, 6}, {28, 6}, {29, 6}, //31
  {30, 6}, {31, 6}, {32, 6}, {33, 6}, {34, 6}, {35, 6}, {36, 6}, {37, 6}, {38, 6}, {39, 6}, {40, 6}, {41, 6}, {42, 6}, {43, 6}, {44, 6}, {45, 6}, //47
  {46, 6}, {47, 6}, {48, 6}, {49, 6}, {50, 6}, {51, 6}, {52, 6}, {53, 6}, {54, 6}, {55, 6}, {56, 6}, {57, 6}, {58, 6}, {59, 6}, {60, 6}, {61, 6}, //63
};


const uint8_t g_kuiVlcTableNeedMoreBitsThread[3] = {
  4, 4, 8
};

const uint8_t g_kuiVlcTableMoreBitsCount0[4] = {
  8, 2, 1, 1
};

const uint8_t g_kuiVlcTableMoreBitsCount1[4] = {
  6, 3, 1, 1
};

const uint8_t g_kuiVlcTableMoreBitsCount2[8] = {
  2, 2, 2, 2, 1, 1, 1, 1
};

const uint8_t g_kuiNcMapTable[17] = {
  0, 0, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3
};


const uint8_t g_kuiVlcTrailingOneTotalCoeffTable[62][2] = {
  {0, 0},
  {0, 1}, {1, 1},
  {0, 2}, {1, 2}, {2, 2},
  {0, 3}, {1, 3}, {2, 3}, {3, 3},
  {0, 4}, {1, 4}, {2, 4}, {3, 4},
  {0, 5}, {1, 5}, {2, 5}, {3, 5},
  {0, 6}, {1, 6}, {2, 6}, {3, 6},
  {0, 7}, {1, 7}, {2, 7}, {3, 7},
  {0, 8}, {1, 8}, {2, 8}, {3, 8},
  {0, 9}, {1, 9}, {2, 9}, {3, 9},
  {0, 10}, {1, 10}, {2, 10}, {3, 10},
  {0, 11}, {1, 11}, {2, 11}, {3, 11},
  {0, 12}, {1, 12}, {2, 12}, {3, 12},
  {0, 13}, {1, 13}, {2, 13}, {3, 13},
  {0, 14}, {1, 14}, {2, 14}, {3, 14},
  {0, 15}, {1, 15}, {2, 15}, {3, 15},
  {0, 16}, {1, 16}, {2, 16}, {3, 16}
};

const uint8_t g_kuiTotalZerosTable0[512][2]
= { //read 9 bits, generated by tzVlcIndex=1 in Table 9-7 in H.264/AVC standard
  {0, 0}, {15, 9}, {14, 9}, {13, 9}, {12, 8}, {12, 8}, {11, 8}, {11, 8}, {10, 7}, {10, 7}, {10, 7}, {10, 7}, {9, 7}, {9, 7}, {9, 7}, {9, 7}, //15
  {8, 6}, { 8, 6}, { 8, 6}, { 8, 6}, { 8, 6}, { 8, 6}, { 8, 6}, { 8, 6}, { 7, 6}, { 7, 6}, { 7, 6}, { 7, 6}, {7, 6}, {7, 6}, {7, 6}, {7, 6}, //31
  {6, 5}, { 6, 5}, { 6, 5}, { 6, 5}, { 6, 5}, { 6, 5}, { 6, 5}, { 6, 5}, { 6, 5}, { 6, 5}, { 6, 5}, { 6, 5}, {6, 5}, {6, 5}, {6, 5}, {6, 5}, //47
  {5, 5}, { 5, 5}, { 5, 5}, { 5, 5}, { 5, 5}, { 5, 5}, { 5, 5}, { 5, 5}, { 5, 5}, { 5, 5}, { 5, 5}, { 5, 5}, {5, 5}, {5, 5}, {5, 5}, {5, 5}, //63
  {4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, {4, 4}, {4, 4}, {4, 4}, {4, 4}, //79
  {4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, {4, 4}, {4, 4}, {4, 4}, {4, 4}, //95
  {3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, {3, 4}, {3, 4}, {3, 4}, {3, 4}, //111
  {3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, { 3, 4}, {3, 4}, {3, 4}, {3, 4}, {3, 4}, //127
  {2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, {2, 3}, {2, 3}, {2, 3}, {2, 3}, //143
  {2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, {2, 3}, {2, 3}, {2, 3}, {2, 3}, //159
  {2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, {2, 3}, {2, 3}, {2, 3}, {2, 3}, //175
  {2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, {2, 3}, {2, 3}, {2, 3}, {2, 3}, //191
  {1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, {1, 3}, {1, 3}, {1, 3}, {1, 3}, //207
  {1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, {1, 3}, {1, 3}, {1, 3}, {1, 3}, //223
  {1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, {1, 3}, {1, 3}, {1, 3}, {1, 3}, //239
  {1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, {1, 3}, {1, 3}, {1, 3}, {1, 3}, //255
  {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, {0, 1}, {0, 1}, //271
  {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, {0, 1}, {0, 1}, //287
  {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, {0, 1}, {0, 1}, //303
  {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, {0, 1}, {0, 1}, //319
  {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, {0, 1}, {0, 1}, //335
  {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, {0, 1}, {0, 1}, //351
  {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, {0, 1}, {0, 1}, //367
  {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, {0, 1}, {0, 1}, //383
  {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, {0, 1}, {0, 1}, //399
  {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, {0, 1}, {0, 1}, //415
  {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, {0, 1}, {0, 1}, //431
  {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, {0, 1}, {0, 1}, //447
  {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, {0, 1}, {0, 1}, //463
  {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, {0, 1}, {0, 1}, //479
  {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, {0, 1}, {0, 1}, //495
  {0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}, {0, 1}, {0, 1}, {0, 1}, {0, 1} //511
};

const uint8_t g_kuiTotalZerosTable1[64][2]
= { //read 6 bits, generated by tzVlcIndex=2 in Table 9-7 in H.264/AVC standard
  {14, 6}, {13, 6}, {12, 6}, {11, 6}, {10, 5}, {10, 5}, {9, 5}, {9, 5}, {8, 4}, {8, 4}, {8, 4}, {8, 4}, {7, 4}, {7, 4}, {7, 4}, {7, 4}, //15
  { 6, 4}, { 6, 4}, { 6, 4}, { 6, 4}, { 5, 4}, { 5, 4}, {5, 4}, {5, 4}, {4, 3}, {4, 3}, {4, 3}, {4, 3}, {4, 3}, {4, 3}, {4, 3}, {4, 3}, //31
  { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, {3, 3}, {3, 3}, {2, 3}, {2, 3}, {2, 3}, {2, 3}, {2, 3}, {2, 3}, {2, 3}, {2, 3}, //47
  { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, { 1, 3}, {1, 3}, {1, 3}, {0, 3}, {0, 3}, {0, 3}, {0, 3}, {0, 3}, {0, 3}, {0, 3}, {0, 3} //63
};

const uint8_t g_kuiTotalZerosTable2[64][2]
= { //read 6 bits, generated by tzVlcIndex=3 in Table 9-7 in H.264/AVC standard
  {13, 6}, {11, 6}, {12, 5}, {12, 5}, {10, 5}, {10, 5}, {9, 5}, {9, 5}, {8, 4}, {8, 4}, {8, 4}, {8, 4}, {5, 4}, {5, 4}, {5, 4}, {5, 4}, //15
  { 4, 4}, { 4, 4}, { 4, 4}, { 4, 4}, { 0, 4}, { 0, 4}, {0, 4}, {0, 4}, {7, 3}, {7, 3}, {7, 3}, {7, 3}, {7, 3}, {7, 3}, {7, 3}, {7, 3}, //31
  { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, {6, 3}, {6, 3}, {3, 3}, {3, 3}, {3, 3}, {3, 3}, {3, 3}, {3, 3}, {3, 3}, {3, 3}, //47
  { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, {2, 3}, {2, 3}, {1, 3}, {1, 3}, {1, 3}, {1, 3}, {1, 3}, {1, 3}, {1, 3}, {1, 3} //63
};

const uint8_t g_kuiTotalZerosTable3[32][2]
= { //read 5 bits, generated by tzVlcIndex=4 in Table 9-7 in H.264/AVC standard
  {12, 5}, {11, 5}, {10, 5}, {0, 5}, {9, 4}, {9, 4}, {7, 4}, {7, 4}, {3, 4}, {3, 4}, {2, 4}, {2, 4}, {8, 3}, {8, 3}, {8, 3}, {8, 3}, //15
  { 6, 3}, { 6, 3}, { 6, 3}, {6, 3}, {5, 3}, {5, 3}, {5, 3}, {5, 3}, {4, 3}, {4, 3}, {4, 3}, {4, 3}, {1, 3}, {1, 3}, {1, 3}, {1, 3}, //31
};

const uint8_t g_kuiTotalZerosTable4[32][2]
= { //read 5 bits, generated by tzVlcIndex=5 in Table 9-7 in H.264/AVC standard
  {11, 5}, { 9, 5}, {10, 4}, {10, 4}, { 8, 4}, { 8, 4}, { 2, 4}, { 2, 4}, { 1, 4}, { 1, 4}, { 0, 4}, { 0, 4}, { 7, 3}, { 7, 3}, { 7, 3}, { 7, 3}, //15
  { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 4, 3}, { 4, 3}, { 4, 3}, { 4, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3} //31
};

const uint8_t g_kuiTotalZerosTable5[64][2]
= { //read 6 bits, generated by tzVlcIndex=6 in Table 9-7 in H.264/AVC standard
  {10, 6}, { 0, 6}, { 1, 5}, { 1, 5}, { 8, 4}, { 8, 4}, { 8, 4}, { 8, 4}, { 9, 3}, { 9, 3}, { 9, 3}, { 9, 3}, { 9, 3}, { 9, 3}, { 9, 3}, { 9, 3}, //15
  { 7, 3}, { 7, 3}, { 7, 3}, { 7, 3}, { 7, 3}, { 7, 3}, { 7, 3}, { 7, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, //31
  { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 4, 3}, { 4, 3}, { 4, 3}, { 4, 3}, { 4, 3}, { 4, 3}, { 4, 3}, { 4, 3}, //47
  { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3} //63
};

const uint8_t g_kuiTotalZerosTable6[64][2]
= { //read 6 bits, generated by tzVlcIndex=7 in Table 9-7 in H.264/AVC standard
  { 9, 6}, { 0, 6}, { 1, 5}, { 1, 5}, { 7, 4}, { 7, 4}, { 7, 4}, { 7, 4}, { 8, 3}, { 8, 3}, { 8, 3}, { 8, 3}, { 8, 3}, { 8, 3}, { 8, 3}, { 8, 3}, //15
  { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 4, 3}, { 4, 3}, { 4, 3}, { 4, 3}, { 4, 3}, { 4, 3}, { 4, 3}, { 4, 3}, //31
  { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, //47
  { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2} //63
};

const uint8_t g_kuiTotalZerosTable7[64][2]
= { //read 6 bits, generated by tzVlcIndex=8 in Table 9-7 in H.264/AVC standard
  { 8, 6}, { 0, 6}, { 2, 5}, { 2, 5}, { 1, 4}, { 1, 4}, { 1, 4}, { 1, 4}, { 7, 3}, { 7, 3}, { 7, 3}, { 7, 3}, { 7, 3}, { 7, 3}, { 7, 3}, { 7, 3}, //15
  { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 6, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, { 3, 3}, //31
  { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, //47
  { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2} //63
};

const uint8_t g_kuiTotalZerosTable8[64][2]
= { //read 6 bits, generated by tzVlcIndex=9 in Table 9-7 in H.264/AVC standard
  { 1, 6}, { 0, 6}, { 7, 5}, { 7, 5}, { 2, 4}, { 2, 4}, { 2, 4}, { 2, 4}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, { 5, 3}, //15
  { 6, 2}, { 6, 2}, { 6, 2}, { 6, 2}, { 6, 2}, { 6, 2}, { 6, 2}, { 6, 2}, { 6, 2}, { 6, 2}, { 6, 2}, { 6, 2}, { 6, 2}, { 6, 2}, { 6, 2}, { 6, 2}, //31
  { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, //47
  { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2} //63
};

const uint8_t g_kuiTotalZerosTable9[32][2]
= { //read 5 bits, generated by tzVlcIndex=10 in Table 9-7 in H.264/AVC standard
  { 1, 5}, { 0, 5}, { 6, 4}, { 6, 4}, { 2, 3}, { 2, 3}, { 2, 3}, { 2, 3}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, { 5, 2}, //15
  { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 4, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2}, { 3, 2} //31
};

const uint8_t g_kuiTotalZerosTable10[16][2]
= { //read 4 bits, generated by tzVlcIndex=11 in Table 9-7 in H.264/AVC standard
  { 0, 4}, { 1, 4}, { 2, 3}, { 2, 3}, { 3, 3}, { 3, 3}, { 5, 3}, { 5, 3}, { 4, 1}, { 4, 1}, { 4, 1}, { 4, 1}, { 4, 1}, { 4, 1}, { 4, 1}, { 4, 1} //15
};

const uint8_t g_kuiTotalZerosTable11[16][2]
= { //read 4 bits, generated by tzVlcIndex=12 in Table 9-7 in H.264/AVC standard
  { 0, 4}, { 1, 4}, { 4, 3}, { 4, 3}, { 2, 2}, { 2, 2}, { 2, 2}, { 2, 2}, { 3, 1}, { 3, 1}, { 3, 1}, { 3, 1}, { 3, 1}, { 3, 1}, { 3, 1}, { 3, 1} //15
};

const uint8_t g_kuiTotalZerosTable12[8][2]
= { //read 3 bits, generated by tzVlcIndex=13 in Table 9-7 in H.264/AVC standard
  { 0, 3}, { 1, 3}, { 3, 2}, { 3, 2}, { 2, 1}, { 2, 1}, { 2, 1}, { 2, 1} //8
};

const uint8_t g_kuiTotalZerosTable13[4][2]
= { //read 2 bits, generated by tzVlcIndex=14 in Table 9-7 in H.264/AVC standard
  { 0, 2}, { 1, 2}, { 2, 1}, { 2, 1}
};

const uint8_t g_kuiTotalZerosTable14[2][2]
= { //read 1 bits generated by tzVlcIndex=15 in Table 9-7 in H.264/AVC standard
  { 0, 1}, { 1, 1}
};

const uint8_t g_kuiTotalZerosBitNumMap[15] = {
  9, 6, 6, 5, 5, 6, 6, 6, 6, 5, 4, 4, 3, 2, 1
};


const uint8_t g_kuiTotalZerosChromaTable0[8][2]
= { //read 3 bits, generated by tzVlcIndex=1 in Table 9-9(a) in H.264/AVC standard
  { 3, 3}, { 2, 3}, { 1, 2}, { 1, 2}, { 0, 1}, { 0, 1}, { 0, 1}, { 0, 1}
};

const uint8_t g_kuiTotalZerosChromaTable1[4][2]
= { //read 2 bits, generated by tzVlcIndex=2 in Table 9-9(a) in H.264/AVC standard
  { 2, 2}, { 1, 2}, { 0, 1}, { 0, 1}
};

const uint8_t g_kuiTotalZerosChromaTable2[2][2]
= { //read 1 bits, generated by tzVlcIndex=3 in Table 9-9(a) in H.264/AVC standard
  { 1, 1}, { 0, 1}
};

const uint8_t g_kuiTotalZerosBitNumChromaMap[3] = {
  3, 2, 1
};

const uint8_t g_kuiZeroLeftTable0[2][2] = { //read 1 bits
  {1, 1}, {0, 1}
};

const uint8_t g_kuiZeroLeftTable1[4][2] = { //read 2 bits
  {2, 2}, {1, 2}, {0, 1}, {0, 1}
};

const uint8_t g_kuiZeroLeftTable2[4][2] = { //read 2 bits
  {3, 2}, {2, 2}, {1, 2}, {0, 2}
};

const uint8_t g_kuiZeroLeftTable3[8][2] = { //read 3 bits
  {4, 3}, {3, 3}, {2, 2}, {2, 2}, {1, 2}, {1, 2}, {0, 2}, {0, 2}
};

const uint8_t g_kuiZeroLeftTable4[8][2] = { //read 3 bits
  {5, 3}, {4, 3}, {3, 3}, {2, 3}, {1, 2}, {1, 2}, {0, 2}, {0, 2}
};

const uint8_t g_kuiZeroLeftTable5[8][2] = { //read 3 bits
  {1, 3}, {2, 3}, {4, 3}, {3, 3}, {6, 3}, {5, 3}, {0, 2}, {0, 2}
};

const uint8_t g_kuiZeroLeftTable6[8][2] = { //read 3 bits
  {7, 3}, {6, 3}, {5, 3}, {4, 3}, {3, 3}, {2, 3}, {1, 3}, {0, 3}
};

const uint8_t g_kuiZeroLeftBitNumMap[16] = {
  0, 1, 2, 2, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3
};

} // namespace WelsDec
