// This file was generated by
//     //third_party/jni_zero/jni_zero.py
// For
//     java.util.List

#ifndef java_util_List_JNI
#define java_util_List_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "../../../../../../../third_party/jni_zero/jni_zero_internal.h"

// Class Accessors
#ifndef java_util_List_clazz_defined
#define java_util_List_clazz_defined
inline jclass java_util_List_clazz(JNIEnv* env) {
  static const char kClassName[] = "java/util/List";
  static std::atomic<jclass> cached_class;
  return jni_zero::internal::LazyGetClass(env, kClassName, &cached_class);
}
#endif


namespace JNI_List {

// Native to Java functions
[[maybe_unused]] static jboolean Java_List_add(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "add",
      "(Ljava/lang/Object;)Z",
      &cached_method_id);
  auto _ret = env->CallBooleanMethod(obj.obj(), call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static void Java_List_add(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    JniIntWrapper p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "add",
      "(ILjava/lang/Object;)V",
      &cached_method_id);
  env->CallVoidMethod(obj.obj(), call_context.method_id(), as_jint(p0), p1.obj());
}

[[maybe_unused]] static jboolean Java_List_addAll(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    JniIntWrapper p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "addAll",
      "(ILjava/util/Collection;)Z",
      &cached_method_id);
  auto _ret = env->CallBooleanMethod(obj.obj(), call_context.method_id(), as_jint(p0), p1.obj());
  return _ret;
}

[[maybe_unused]] static jboolean Java_List_addAll(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "addAll",
      "(Ljava/util/Collection;)Z",
      &cached_method_id);
  auto _ret = env->CallBooleanMethod(obj.obj(), call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static void Java_List_addFirst(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "addFirst",
      "(Ljava/lang/Object;)V",
      &cached_method_id);
  env->CallVoidMethod(obj.obj(), call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_List_addLast(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "addLast",
      "(Ljava/lang/Object;)V",
      &cached_method_id);
  env->CallVoidMethod(obj.obj(), call_context.method_id(), p0.obj());
}

[[maybe_unused]] static void Java_List_clear(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "clear",
      "()V",
      &cached_method_id);
  env->CallVoidMethod(obj.obj(), call_context.method_id());
}

[[maybe_unused]] static jboolean Java_List_contains(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "contains",
      "(Ljava/lang/Object;)Z",
      &cached_method_id);
  auto _ret = env->CallBooleanMethod(obj.obj(), call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jboolean Java_List_containsAll(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "containsAll",
      "(Ljava/util/Collection;)Z",
      &cached_method_id);
  auto _ret = env->CallBooleanMethod(obj.obj(), call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_copyOf(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "copyOf",
      "(Ljava/util/Collection;)Ljava/util/List;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jboolean Java_List_equals(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "equals",
      "(Ljava/lang/Object;)Z",
      &cached_method_id);
  auto _ret = env->CallBooleanMethod(obj.obj(), call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_get(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    JniIntWrapper p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "get",
      "(I)Ljava/lang/Object;",
      &cached_method_id);
  auto _ret = env->CallObjectMethod(obj.obj(), call_context.method_id(), as_jint(p0));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_getFirst(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "getFirst",
      "()Ljava/lang/Object;",
      &cached_method_id);
  auto _ret = env->CallObjectMethod(obj.obj(), call_context.method_id());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_getLast(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "getLast",
      "()Ljava/lang/Object;",
      &cached_method_id);
  auto _ret = env->CallObjectMethod(obj.obj(), call_context.method_id());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jint Java_List_hashCode(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "hashCode",
      "()I",
      &cached_method_id);
  auto _ret = env->CallIntMethod(obj.obj(), call_context.method_id());
  return _ret;
}

[[maybe_unused]] static jint Java_List_indexOf(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "indexOf",
      "(Ljava/lang/Object;)I",
      &cached_method_id);
  auto _ret = env->CallIntMethod(obj.obj(), call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jboolean Java_List_isEmpty(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "isEmpty",
      "()Z",
      &cached_method_id);
  auto _ret = env->CallBooleanMethod(obj.obj(), call_context.method_id());
  return _ret;
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_iterator(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "iterator",
      "()Ljava/util/Iterator;",
      &cached_method_id);
  auto _ret = env->CallObjectMethod(obj.obj(), call_context.method_id());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jint Java_List_lastIndexOf(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "lastIndexOf",
      "(Ljava/lang/Object;)I",
      &cached_method_id);
  auto _ret = env->CallIntMethod(obj.obj(), call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_listIterator(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "listIterator",
      "()Ljava/util/ListIterator;",
      &cached_method_id);
  auto _ret = env->CallObjectMethod(obj.obj(), call_context.method_id());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_listIterator(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    JniIntWrapper p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "listIterator",
      "(I)Ljava/util/ListIterator;",
      &cached_method_id);
  auto _ret = env->CallObjectMethod(obj.obj(), call_context.method_id(), as_jint(p0));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_of(JNIEnv* env) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "of",
      "()Ljava/util/List;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_of__Object(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "of",
      "(Ljava/lang/Object;)Ljava/util/List;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_of(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "of",
      "(Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/List;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj(), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_of(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "of",
      "(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/List;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      p1.obj(),
      p2.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_of(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "of",
      "(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/List;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      p1.obj(),
      p2.obj(),
      p3.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_of(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "of",
      "(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/List;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      p1.obj(),
      p2.obj(),
      p3.obj(),
      p4.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_of(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "of",
      "(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/List;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      p1.obj(),
      p2.obj(),
      p3.obj(),
      p4.obj(),
      p5.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_of(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "of",
      "(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/List;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      p1.obj(),
      p2.obj(),
      p3.obj(),
      p4.obj(),
      p5.obj(),
      p6.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_of(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "of",
      "(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/List;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      p1.obj(),
      p2.obj(),
      p3.obj(),
      p4.obj(),
      p5.obj(),
      p6.obj(),
      p7.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_of(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7,
    const jni_zero::JavaRef<jobject>& p8) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "of",
      "(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/List;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      p1.obj(),
      p2.obj(),
      p3.obj(),
      p4.obj(),
      p5.obj(),
      p6.obj(),
      p7.obj(),
      p8.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_of(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& p0,
    const jni_zero::JavaRef<jobject>& p1,
    const jni_zero::JavaRef<jobject>& p2,
    const jni_zero::JavaRef<jobject>& p3,
    const jni_zero::JavaRef<jobject>& p4,
    const jni_zero::JavaRef<jobject>& p5,
    const jni_zero::JavaRef<jobject>& p6,
    const jni_zero::JavaRef<jobject>& p7,
    const jni_zero::JavaRef<jobject>& p8,
    const jni_zero::JavaRef<jobject>& p9) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "of",
      "(Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/List;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(
      clazz,
      call_context.method_id(),
      p0.obj(),
      p1.obj(),
      p2.obj(),
      p3.obj(),
      p4.obj(),
      p5.obj(),
      p6.obj(),
      p7.obj(),
      p8.obj(),
      p9.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_of__ObjectArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "of",
      "([Ljava/lang/Object;)Ljava/util/List;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), p0.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jboolean Java_List_remove__Object(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "remove",
      "(Ljava/lang/Object;)Z",
      &cached_method_id);
  auto _ret = env->CallBooleanMethod(obj.obj(), call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_remove__int(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    JniIntWrapper p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "remove",
      "(I)Ljava/lang/Object;",
      &cached_method_id);
  auto _ret = env->CallObjectMethod(obj.obj(), call_context.method_id(), as_jint(p0));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jboolean Java_List_removeAll(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "removeAll",
      "(Ljava/util/Collection;)Z",
      &cached_method_id);
  auto _ret = env->CallBooleanMethod(obj.obj(), call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_removeFirst(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "removeFirst",
      "()Ljava/lang/Object;",
      &cached_method_id);
  auto _ret = env->CallObjectMethod(obj.obj(), call_context.method_id());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_removeLast(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "removeLast",
      "()Ljava/lang/Object;",
      &cached_method_id);
  auto _ret = env->CallObjectMethod(obj.obj(), call_context.method_id());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static void Java_List_replaceAll(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "replaceAll",
      "(Ljava/util/function/UnaryOperator;)V",
      &cached_method_id);
  env->CallVoidMethod(obj.obj(), call_context.method_id(), p0.obj());
}

[[maybe_unused]] static jboolean Java_List_retainAll(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, false);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "retainAll",
      "(Ljava/util/Collection;)Z",
      &cached_method_id);
  auto _ret = env->CallBooleanMethod(obj.obj(), call_context.method_id(), p0.obj());
  return _ret;
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_reversed(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "reversed",
      "()Ljava/util/List;",
      &cached_method_id);
  auto _ret = env->CallObjectMethod(obj.obj(), call_context.method_id());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_reversed1(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "reversed",
      "()Ljava/util/SequencedCollection;",
      &cached_method_id);
  auto _ret = env->CallObjectMethod(obj.obj(), call_context.method_id());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_set(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    JniIntWrapper p0,
    const jni_zero::JavaRef<jobject>& p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "set",
      "(ILjava/lang/Object;)Ljava/lang/Object;",
      &cached_method_id);
  auto _ret = env->CallObjectMethod(obj.obj(), call_context.method_id(), as_jint(p0), p1.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jint Java_List_size(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, 0);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "size",
      "()I",
      &cached_method_id);
  auto _ret = env->CallIntMethod(obj.obj(), call_context.method_id());
  return _ret;
}

[[maybe_unused]] static void Java_List_sort(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobject>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "sort",
      "(Ljava/util/Comparator;)V",
      &cached_method_id);
  env->CallVoidMethod(obj.obj(), call_context.method_id(), p0.obj());
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_spliterator(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "spliterator",
      "()Ljava/util/Spliterator;",
      &cached_method_id);
  auto _ret = env->CallObjectMethod(obj.obj(), call_context.method_id());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobject> Java_List_subList(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    JniIntWrapper p0,
    JniIntWrapper p1) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "subList",
      "(II)Ljava/util/List;",
      &cached_method_id);
  auto _ret = env->CallObjectMethod(obj.obj(), call_context.method_id(), as_jint(p0), as_jint(p1));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobjectArray> Java_List_toArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "toArray",
      "()[Ljava/lang/Object;",
      &cached_method_id);
  auto _ret = env->CallObjectMethod(obj.obj(), call_context.method_id());
  jobjectArray _ret2 = static_cast<jobjectArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jobjectArray>(env, _ret2);
}

[[maybe_unused]] static jni_zero::ScopedJavaLocalRef<jobjectArray> Java_List_toArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj,
    const jni_zero::JavaRef<jobjectArray>& p0) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = java_util_List_clazz(env);
  CHECK_CLAZZ(env, obj.obj(), clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_INSTANCE>(
      env,
      clazz,
      "toArray",
      "([Ljava/lang/Object;)[Ljava/lang/Object;",
      &cached_method_id);
  auto _ret = env->CallObjectMethod(obj.obj(), call_context.method_id(), p0.obj());
  jobjectArray _ret2 = static_cast<jobjectArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jobjectArray>(env, _ret2);
}



}  // namespace JNI_List

#endif  // java_util_List_JNI
