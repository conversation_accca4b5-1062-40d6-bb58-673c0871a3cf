// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/CallSessionFileRotatingLogSink

#ifndef org_webrtc_CallSessionFileRotatingLogSink_JNI
#define org_webrtc_CallSessionFileRotatingLogSink_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_CallSessionFileRotatingLogSink[];
const char kClassPath_org_webrtc_CallSessionFileRotatingLogSink[] =
    "org/webrtc/CallSessionFileRotatingLogSink";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_CallSessionFileRotatingLogSink_clazz(nullptr);
#ifndef org_webrtc_CallSessionFileRotatingLogSink_clazz_defined
#define org_webrtc_CallSessionFileRotatingLogSink_clazz_defined
inline jclass org_webrtc_CallSessionFileRotatingLogSink_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_CallSessionFileRotatingLogSink,
      &g_org_webrtc_CallSessionFileRotatingLogSink_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {

static jlong JNI_CallSessionFileRotatingLogSink_AddSink(JNIEnv* env, const
    jni_zero::JavaParamRef<jstring>& dirPath,
    jint maxFileSize,
    jint severity);

JNI_BOUNDARY_EXPORT jlong Java_org_webrtc_CallSessionFileRotatingLogSink_nativeAddSink(
    JNIEnv* env,
    jclass jcaller,
    jstring dirPath,
    jint maxFileSize,
    jint severity) {
  return JNI_CallSessionFileRotatingLogSink_AddSink(env, jni_zero::JavaParamRef<jstring>(env,
      dirPath), maxFileSize, severity);
}

static void JNI_CallSessionFileRotatingLogSink_DeleteSink(JNIEnv* env, jlong sink);

JNI_BOUNDARY_EXPORT void Java_org_webrtc_CallSessionFileRotatingLogSink_nativeDeleteSink(
    JNIEnv* env,
    jclass jcaller,
    jlong sink) {
  return JNI_CallSessionFileRotatingLogSink_DeleteSink(env, sink);
}

static jni_zero::ScopedJavaLocalRef<jbyteArray>
    JNI_CallSessionFileRotatingLogSink_GetLogData(JNIEnv* env, const
    jni_zero::JavaParamRef<jstring>& dirPath);

JNI_BOUNDARY_EXPORT jbyteArray Java_org_webrtc_CallSessionFileRotatingLogSink_nativeGetLogData(
    JNIEnv* env,
    jclass jcaller,
    jstring dirPath) {
  return JNI_CallSessionFileRotatingLogSink_GetLogData(env, jni_zero::JavaParamRef<jstring>(env,
      dirPath)).Release();
}


}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_CallSessionFileRotatingLogSink_JNI
