/*
 *  Copyright (c) 2010 The WebM project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef VPX_VP8_COMMON_MODECONT_H_
#define VPX_VP8_COMMON_MODECONT_H_

#ifdef __cplusplus
extern "C" {
#endif

extern const int vp8_mode_contexts[6][4];

#ifdef __cplusplus
}  // extern "C"
#endif

#endif  // VPX_VP8_COMMON_MODECONT_H_
