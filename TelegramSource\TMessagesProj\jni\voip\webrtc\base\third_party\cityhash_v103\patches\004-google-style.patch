diff --git a/base/third_party/cityhash_v103/src/city_v103.cc b/base/third_party/cityhash_v103/src/city_v103.cc
index 1f99751c1a60..3451b6ffec14 100644
--- a/base/third_party/cityhash_v103/src/city_v103.cc
+++ b/base/third_party/cityhash_v103/src/city_v103.cc
@@ -39,8 +39,6 @@ namespace base {
 namespace internal {
 namespace cityhash_v103 {
 
-using namespace std;
-
 static uint64 UNALIGNED_LOAD64(const char *p) {
   uint64 result;
   memcpy(&result, p, sizeof(result));
@@ -158,7 +156,7 @@ static uint64 HashLen17to32(const char *s, size_t len) {
 
 // Return a 16-byte hash for 48 bytes.  Quick and dirty.
 // Callers do best to use "random-looking" values for a and b.
-static pair<uint64, uint64> WeakHashLen32WithSeeds(
+static std::pair<uint64, uint64> WeakHashLen32WithSeeds(
     uint64 w, uint64 x, uint64 y, uint64 z, uint64 a, uint64 b) {
   a += w;
   b = Rotate(b + a + z, 21);
@@ -166,11 +164,11 @@ static pair<uint64, uint64> WeakHashLen32WithSeeds(
   a += x;
   a += y;
   b += Rotate(a, 44);
-  return make_pair(a + z, b + c);
+  return std::make_pair(a + z, b + c);
 }
 
 // Return a 16-byte hash for s[0] ... s[31], a, and b.  Quick and dirty.
-static pair<uint64, uint64> WeakHashLen32WithSeeds(
+static std::pair<uint64, uint64> WeakHashLen32WithSeeds(
     const char* s, uint64 a, uint64 b) {
   return WeakHashLen32WithSeeds(Fetch64(s),
                                 Fetch64(s + 8),
@@ -220,8 +218,8 @@ uint64 CityHash64(const char *s, size_t len) {
   uint64 x = Fetch64(s + len - 40);
   uint64 y = Fetch64(s + len - 16) + Fetch64(s + len - 56);
   uint64 z = HashLen16(Fetch64(s + len - 48) + len, Fetch64(s + len - 24));
-  pair<uint64, uint64> v = WeakHashLen32WithSeeds(s + len - 64, len, z);
-  pair<uint64, uint64> w = WeakHashLen32WithSeeds(s + len - 32, y + k1, x);
+  std::pair<uint64, uint64> v = WeakHashLen32WithSeeds(s + len - 64, len, z);
+  std::pair<uint64, uint64> w = WeakHashLen32WithSeeds(s + len - 32, y + k1, x);
   x = x * k1 + Fetch64(s);
 
   // Decrease len to the nearest multiple of 64, and operate on 64-byte chunks.
@@ -290,7 +288,7 @@ uint128 CityHash128WithSeed(const char *s, size_t len, uint128 seed) {
 
   // We expect len >= 128 to be the common case.  Keep 56 bytes of state:
   // v, w, x, y, and z.
-  pair<uint64, uint64> v, w;
+  std::pair<uint64, uint64> v, w;
   uint64 x = Uint128Low64(seed);
   uint64 y = Uint128High64(seed);
   uint64 z = len * k1;
@@ -349,7 +347,7 @@ uint128 CityHash128(const char *s, size_t len) {
                                uint128(Fetch64(s) ^ k3,
                                        Fetch64(s + 8)));
   } else if (len >= 8) {
-    return CityHash128WithSeed(NULL,
+    return CityHash128WithSeed(nullptr,
                                0,
                                uint128(Fetch64(s) ^ (len * k0),
                                        Fetch64(s + len - 8) ^ k1));
