/*
 *  Copyright 2018 The WebRTC Project Authors. All rights reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef RTC_BASE_SYSTEM_COCOA_THREADING_H_
#define RTC_BASE_SYSTEM_COCOA_THREADING_H_

// If Cocoa is to be used on more than one thread, it must know that the
// application is multithreaded.  Since it's possible to enter Cocoa code
// from threads created by pthread_thread_create, <PERSON><PERSON> won't necessarily
// be aware that the application is multithreaded.  Spawning an NSThread is
// enough to get <PERSON><PERSON> to set up for multithreaded operation, so this is done
// if necessary before pthread_thread_create spawns any threads.
//
// http://developer.apple.com/documentation/Cocoa/Conceptual/Multithreading/CreatingThreads/chapter_4_section_4.html
void InitCocoaMultiThreading();

#endif  // RTC_BASE_SYSTEM_COCOA_THREADING_H_
