// This file is generated from a similarly-named Perl script in the BoringSSL
// source tree. Do not edit by hand.

#include <openssl/asm_base.h>

#if !defined(OPENSSL_NO_ASM) && defined(OPENSSL_X86) && defined(__ELF__)
.text
.globl	sha512_block_data_order_nohw
.hidden	sha512_block_data_order_nohw
.type	sha512_block_data_order_nohw,@function
.align	16
sha512_block_data_order_nohw:
.L_sha512_block_data_order_nohw_begin:
	pushl	%ebp
	pushl	%ebx
	pushl	%esi
	pushl	%edi
	movl	20(%esp),%esi
	movl	24(%esp),%edi
	movl	28(%esp),%eax
	movl	%esp,%ebx
	call	.L000pic_point
.L000pic_point:
	popl	%ebp
	leal	.LK512-.L000pic_point(%ebp),%ebp
	subl	$16,%esp
	andl	$-64,%esp
	shll	$7,%eax
	addl	%edi,%eax
	movl	%esi,(%esp)
	movl	%edi,4(%esp)
	movl	%eax,8(%esp)
	movl	%ebx,12(%esp)
	movq	(%esi),%mm0
	movq	8(%esi),%mm1
	movq	16(%esi),%mm2
	movq	24(%esi),%mm3
	movq	32(%esi),%mm4
	movq	40(%esi),%mm5
	movq	48(%esi),%mm6
	movq	56(%esi),%mm7
	subl	$80,%esp
	jmp	.L001loop_sse2
.align	16
.L001loop_sse2:
	movq	%mm1,8(%esp)
	movq	%mm2,16(%esp)
	movq	%mm3,24(%esp)
	movq	%mm5,40(%esp)
	movq	%mm6,48(%esp)
	pxor	%mm1,%mm2
	movq	%mm7,56(%esp)
	movq	%mm0,%mm3
	movl	(%edi),%eax
	movl	4(%edi),%ebx
	addl	$8,%edi
	movl	$15,%edx
	bswap	%eax
	bswap	%ebx
	jmp	.L00200_14_sse2
.align	16
.L00200_14_sse2:
	movd	%eax,%mm1
	movl	(%edi),%eax
	movd	%ebx,%mm7
	movl	4(%edi),%ebx
	addl	$8,%edi
	bswap	%eax
	bswap	%ebx
	punpckldq	%mm1,%mm7
	movq	%mm4,%mm1
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,32(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	movq	%mm3,%mm0
	movq	%mm7,72(%esp)
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	56(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	paddq	(%ebp),%mm7
	pxor	%mm4,%mm3
	movq	24(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	8(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	subl	$8,%esp
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	40(%esp),%mm5
	paddq	%mm2,%mm3
	movq	%mm0,%mm2
	addl	$8,%ebp
	paddq	%mm6,%mm3
	movq	48(%esp),%mm6
	decl	%edx
	jnz	.L00200_14_sse2
	movd	%eax,%mm1
	movd	%ebx,%mm7
	punpckldq	%mm1,%mm7
	movq	%mm4,%mm1
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,32(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	movq	%mm3,%mm0
	movq	%mm7,72(%esp)
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	56(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	paddq	(%ebp),%mm7
	pxor	%mm4,%mm3
	movq	24(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	8(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	subl	$8,%esp
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	192(%esp),%mm7
	paddq	%mm2,%mm3
	movq	%mm0,%mm2
	addl	$8,%ebp
	paddq	%mm6,%mm3
	pxor	%mm0,%mm0
	movl	$32,%edx
	jmp	.L00316_79_sse2
.align	16
.L00316_79_sse2:
	movq	88(%esp),%mm5
	movq	%mm7,%mm1
	psrlq	$1,%mm7
	movq	%mm5,%mm6
	psrlq	$6,%mm5
	psllq	$56,%mm1
	paddq	%mm3,%mm0
	movq	%mm7,%mm3
	psrlq	$6,%mm7
	pxor	%mm1,%mm3
	psllq	$7,%mm1
	pxor	%mm7,%mm3
	psrlq	$1,%mm7
	pxor	%mm1,%mm3
	movq	%mm5,%mm1
	psrlq	$13,%mm5
	pxor	%mm3,%mm7
	psllq	$3,%mm6
	pxor	%mm5,%mm1
	paddq	200(%esp),%mm7
	pxor	%mm6,%mm1
	psrlq	$42,%mm5
	paddq	128(%esp),%mm7
	pxor	%mm5,%mm1
	psllq	$42,%mm6
	movq	40(%esp),%mm5
	pxor	%mm6,%mm1
	movq	48(%esp),%mm6
	paddq	%mm1,%mm7
	movq	%mm4,%mm1
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,32(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	movq	%mm7,72(%esp)
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	56(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	paddq	(%ebp),%mm7
	pxor	%mm4,%mm3
	movq	24(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	8(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	subl	$8,%esp
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	192(%esp),%mm7
	paddq	%mm6,%mm2
	addl	$8,%ebp
	movq	88(%esp),%mm5
	movq	%mm7,%mm1
	psrlq	$1,%mm7
	movq	%mm5,%mm6
	psrlq	$6,%mm5
	psllq	$56,%mm1
	paddq	%mm3,%mm2
	movq	%mm7,%mm3
	psrlq	$6,%mm7
	pxor	%mm1,%mm3
	psllq	$7,%mm1
	pxor	%mm7,%mm3
	psrlq	$1,%mm7
	pxor	%mm1,%mm3
	movq	%mm5,%mm1
	psrlq	$13,%mm5
	pxor	%mm3,%mm7
	psllq	$3,%mm6
	pxor	%mm5,%mm1
	paddq	200(%esp),%mm7
	pxor	%mm6,%mm1
	psrlq	$42,%mm5
	paddq	128(%esp),%mm7
	pxor	%mm5,%mm1
	psllq	$42,%mm6
	movq	40(%esp),%mm5
	pxor	%mm6,%mm1
	movq	48(%esp),%mm6
	paddq	%mm1,%mm7
	movq	%mm4,%mm1
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,32(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	movq	%mm7,72(%esp)
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	56(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	paddq	(%ebp),%mm7
	pxor	%mm4,%mm3
	movq	24(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	8(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	subl	$8,%esp
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	192(%esp),%mm7
	paddq	%mm6,%mm0
	addl	$8,%ebp
	decl	%edx
	jnz	.L00316_79_sse2
	paddq	%mm3,%mm0
	movq	8(%esp),%mm1
	movq	24(%esp),%mm3
	movq	40(%esp),%mm5
	movq	48(%esp),%mm6
	movq	56(%esp),%mm7
	pxor	%mm1,%mm2
	paddq	(%esi),%mm0
	paddq	8(%esi),%mm1
	paddq	16(%esi),%mm2
	paddq	24(%esi),%mm3
	paddq	32(%esi),%mm4
	paddq	40(%esi),%mm5
	paddq	48(%esi),%mm6
	paddq	56(%esi),%mm7
	movl	$640,%eax
	movq	%mm0,(%esi)
	movq	%mm1,8(%esi)
	movq	%mm2,16(%esi)
	movq	%mm3,24(%esi)
	movq	%mm4,32(%esi)
	movq	%mm5,40(%esi)
	movq	%mm6,48(%esi)
	movq	%mm7,56(%esi)
	leal	(%esp,%eax,1),%esp
	subl	%eax,%ebp
	cmpl	88(%esp),%edi
	jb	.L001loop_sse2
	movl	92(%esp),%esp
	emms
	popl	%edi
	popl	%esi
	popl	%ebx
	popl	%ebp
	ret
.size	sha512_block_data_order_nohw,.-.L_sha512_block_data_order_nohw_begin
.globl	sha512_block_data_order_ssse3
.hidden	sha512_block_data_order_ssse3
.type	sha512_block_data_order_ssse3,@function
.align	16
sha512_block_data_order_ssse3:
.L_sha512_block_data_order_ssse3_begin:
	pushl	%ebp
	pushl	%ebx
	pushl	%esi
	pushl	%edi
	movl	20(%esp),%esi
	movl	24(%esp),%edi
	movl	28(%esp),%eax
	movl	%esp,%ebx
	call	.L004pic_point
.L004pic_point:
	popl	%ebp
	leal	.LK512-.L004pic_point(%ebp),%ebp
	subl	$16,%esp
	andl	$-64,%esp
	shll	$7,%eax
	addl	%edi,%eax
	movl	%esi,(%esp)
	movl	%edi,4(%esp)
	movl	%eax,8(%esp)
	movl	%ebx,12(%esp)
	movq	(%esi),%mm0
	movq	8(%esi),%mm1
	movq	16(%esi),%mm2
	movq	24(%esi),%mm3
	movq	32(%esi),%mm4
	movq	40(%esi),%mm5
	movq	48(%esi),%mm6
	movq	56(%esi),%mm7
	leal	-64(%esp),%edx
	subl	$256,%esp
	movdqa	640(%ebp),%xmm1
	movdqu	(%edi),%xmm0
.byte	102,15,56,0,193
	movdqa	(%ebp),%xmm3
	movdqa	%xmm1,%xmm2
	movdqu	16(%edi),%xmm1
	paddq	%xmm0,%xmm3
.byte	102,15,56,0,202
	movdqa	%xmm3,-128(%edx)
	movdqa	16(%ebp),%xmm4
	movdqa	%xmm2,%xmm3
	movdqu	32(%edi),%xmm2
	paddq	%xmm1,%xmm4
.byte	102,15,56,0,211
	movdqa	%xmm4,-112(%edx)
	movdqa	32(%ebp),%xmm5
	movdqa	%xmm3,%xmm4
	movdqu	48(%edi),%xmm3
	paddq	%xmm2,%xmm5
.byte	102,15,56,0,220
	movdqa	%xmm5,-96(%edx)
	movdqa	48(%ebp),%xmm6
	movdqa	%xmm4,%xmm5
	movdqu	64(%edi),%xmm4
	paddq	%xmm3,%xmm6
.byte	102,15,56,0,229
	movdqa	%xmm6,-80(%edx)
	movdqa	64(%ebp),%xmm7
	movdqa	%xmm5,%xmm6
	movdqu	80(%edi),%xmm5
	paddq	%xmm4,%xmm7
.byte	102,15,56,0,238
	movdqa	%xmm7,-64(%edx)
	movdqa	%xmm0,(%edx)
	movdqa	80(%ebp),%xmm0
	movdqa	%xmm6,%xmm7
	movdqu	96(%edi),%xmm6
	paddq	%xmm5,%xmm0
.byte	102,15,56,0,247
	movdqa	%xmm0,-48(%edx)
	movdqa	%xmm1,16(%edx)
	movdqa	96(%ebp),%xmm1
	movdqa	%xmm7,%xmm0
	movdqu	112(%edi),%xmm7
	paddq	%xmm6,%xmm1
.byte	102,15,56,0,248
	movdqa	%xmm1,-32(%edx)
	movdqa	%xmm2,32(%edx)
	movdqa	112(%ebp),%xmm2
	movdqa	(%edx),%xmm0
	paddq	%xmm7,%xmm2
	movdqa	%xmm2,-16(%edx)
	nop
.align	32
.L005loop_ssse3:
	movdqa	16(%edx),%xmm2
	movdqa	%xmm3,48(%edx)
	leal	128(%ebp),%ebp
	movq	%mm1,8(%esp)
	movl	%edi,%ebx
	movq	%mm2,16(%esp)
	leal	128(%edi),%edi
	movq	%mm3,24(%esp)
	cmpl	%eax,%edi
	movq	%mm5,40(%esp)
	cmovbl	%edi,%ebx
	movq	%mm6,48(%esp)
	movl	$4,%ecx
	pxor	%mm1,%mm2
	movq	%mm7,56(%esp)
	pxor	%mm3,%mm3
	jmp	.L00600_47_ssse3
.align	32
.L00600_47_ssse3:
	movdqa	%xmm5,%xmm3
	movdqa	%xmm2,%xmm1
.byte	102,15,58,15,208,8
	movdqa	%xmm4,(%edx)
.byte	102,15,58,15,220,8
	movdqa	%xmm2,%xmm4
	psrlq	$7,%xmm2
	paddq	%xmm3,%xmm0
	movdqa	%xmm4,%xmm3
	psrlq	$1,%xmm4
	psllq	$56,%xmm3
	pxor	%xmm4,%xmm2
	psrlq	$7,%xmm4
	pxor	%xmm3,%xmm2
	psllq	$7,%xmm3
	pxor	%xmm4,%xmm2
	movdqa	%xmm7,%xmm4
	pxor	%xmm3,%xmm2
	movdqa	%xmm7,%xmm3
	psrlq	$6,%xmm4
	paddq	%xmm2,%xmm0
	movdqa	%xmm7,%xmm2
	psrlq	$19,%xmm3
	psllq	$3,%xmm2
	pxor	%xmm3,%xmm4
	psrlq	$42,%xmm3
	pxor	%xmm2,%xmm4
	psllq	$42,%xmm2
	pxor	%xmm3,%xmm4
	movdqa	32(%edx),%xmm3
	pxor	%xmm2,%xmm4
	movdqa	(%ebp),%xmm2
	movq	%mm4,%mm1
	paddq	%xmm4,%xmm0
	movq	-128(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,32(%esp)
	paddq	%xmm0,%xmm2
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm0
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	56(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	24(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	8(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	32(%esp),%mm5
	paddq	%mm6,%mm2
	movq	40(%esp),%mm6
	movq	%mm4,%mm1
	movq	-120(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,24(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm2
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,56(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	48(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	16(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	24(%esp),%mm5
	paddq	%mm6,%mm0
	movq	32(%esp),%mm6
	movdqa	%xmm2,-128(%edx)
	movdqa	%xmm6,%xmm4
	movdqa	%xmm3,%xmm2
.byte	102,15,58,15,217,8
	movdqa	%xmm5,16(%edx)
.byte	102,15,58,15,229,8
	movdqa	%xmm3,%xmm5
	psrlq	$7,%xmm3
	paddq	%xmm4,%xmm1
	movdqa	%xmm5,%xmm4
	psrlq	$1,%xmm5
	psllq	$56,%xmm4
	pxor	%xmm5,%xmm3
	psrlq	$7,%xmm5
	pxor	%xmm4,%xmm3
	psllq	$7,%xmm4
	pxor	%xmm5,%xmm3
	movdqa	%xmm0,%xmm5
	pxor	%xmm4,%xmm3
	movdqa	%xmm0,%xmm4
	psrlq	$6,%xmm5
	paddq	%xmm3,%xmm1
	movdqa	%xmm0,%xmm3
	psrlq	$19,%xmm4
	psllq	$3,%xmm3
	pxor	%xmm4,%xmm5
	psrlq	$42,%xmm4
	pxor	%xmm3,%xmm5
	psllq	$42,%xmm3
	pxor	%xmm4,%xmm5
	movdqa	48(%edx),%xmm4
	pxor	%xmm3,%xmm5
	movdqa	16(%ebp),%xmm3
	movq	%mm4,%mm1
	paddq	%xmm5,%xmm1
	movq	-112(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,16(%esp)
	paddq	%xmm1,%xmm3
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm0
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,48(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	40(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	8(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	56(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	16(%esp),%mm5
	paddq	%mm6,%mm2
	movq	24(%esp),%mm6
	movq	%mm4,%mm1
	movq	-104(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,8(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm2
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,40(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	32(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	48(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	8(%esp),%mm5
	paddq	%mm6,%mm0
	movq	16(%esp),%mm6
	movdqa	%xmm3,-112(%edx)
	movdqa	%xmm7,%xmm5
	movdqa	%xmm4,%xmm3
.byte	102,15,58,15,226,8
	movdqa	%xmm6,32(%edx)
.byte	102,15,58,15,238,8
	movdqa	%xmm4,%xmm6
	psrlq	$7,%xmm4
	paddq	%xmm5,%xmm2
	movdqa	%xmm6,%xmm5
	psrlq	$1,%xmm6
	psllq	$56,%xmm5
	pxor	%xmm6,%xmm4
	psrlq	$7,%xmm6
	pxor	%xmm5,%xmm4
	psllq	$7,%xmm5
	pxor	%xmm6,%xmm4
	movdqa	%xmm1,%xmm6
	pxor	%xmm5,%xmm4
	movdqa	%xmm1,%xmm5
	psrlq	$6,%xmm6
	paddq	%xmm4,%xmm2
	movdqa	%xmm1,%xmm4
	psrlq	$19,%xmm5
	psllq	$3,%xmm4
	pxor	%xmm5,%xmm6
	psrlq	$42,%xmm5
	pxor	%xmm4,%xmm6
	psllq	$42,%xmm4
	pxor	%xmm5,%xmm6
	movdqa	(%edx),%xmm5
	pxor	%xmm4,%xmm6
	movdqa	32(%ebp),%xmm4
	movq	%mm4,%mm1
	paddq	%xmm6,%xmm2
	movq	-96(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,(%esp)
	paddq	%xmm2,%xmm4
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm0
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,32(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	24(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	56(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	40(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	(%esp),%mm5
	paddq	%mm6,%mm2
	movq	8(%esp),%mm6
	movq	%mm4,%mm1
	movq	-88(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,56(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm2
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,24(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	16(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	48(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	32(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	56(%esp),%mm5
	paddq	%mm6,%mm0
	movq	(%esp),%mm6
	movdqa	%xmm4,-96(%edx)
	movdqa	%xmm0,%xmm6
	movdqa	%xmm5,%xmm4
.byte	102,15,58,15,235,8
	movdqa	%xmm7,48(%edx)
.byte	102,15,58,15,247,8
	movdqa	%xmm5,%xmm7
	psrlq	$7,%xmm5
	paddq	%xmm6,%xmm3
	movdqa	%xmm7,%xmm6
	psrlq	$1,%xmm7
	psllq	$56,%xmm6
	pxor	%xmm7,%xmm5
	psrlq	$7,%xmm7
	pxor	%xmm6,%xmm5
	psllq	$7,%xmm6
	pxor	%xmm7,%xmm5
	movdqa	%xmm2,%xmm7
	pxor	%xmm6,%xmm5
	movdqa	%xmm2,%xmm6
	psrlq	$6,%xmm7
	paddq	%xmm5,%xmm3
	movdqa	%xmm2,%xmm5
	psrlq	$19,%xmm6
	psllq	$3,%xmm5
	pxor	%xmm6,%xmm7
	psrlq	$42,%xmm6
	pxor	%xmm5,%xmm7
	psllq	$42,%xmm5
	pxor	%xmm6,%xmm7
	movdqa	16(%edx),%xmm6
	pxor	%xmm5,%xmm7
	movdqa	48(%ebp),%xmm5
	movq	%mm4,%mm1
	paddq	%xmm7,%xmm3
	movq	-80(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,48(%esp)
	paddq	%xmm3,%xmm5
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm0
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,16(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	8(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	40(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	24(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	48(%esp),%mm5
	paddq	%mm6,%mm2
	movq	56(%esp),%mm6
	movq	%mm4,%mm1
	movq	-72(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,40(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm2
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,8(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	32(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	16(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	40(%esp),%mm5
	paddq	%mm6,%mm0
	movq	48(%esp),%mm6
	movdqa	%xmm5,-80(%edx)
	movdqa	%xmm1,%xmm7
	movdqa	%xmm6,%xmm5
.byte	102,15,58,15,244,8
	movdqa	%xmm0,(%edx)
.byte	102,15,58,15,248,8
	movdqa	%xmm6,%xmm0
	psrlq	$7,%xmm6
	paddq	%xmm7,%xmm4
	movdqa	%xmm0,%xmm7
	psrlq	$1,%xmm0
	psllq	$56,%xmm7
	pxor	%xmm0,%xmm6
	psrlq	$7,%xmm0
	pxor	%xmm7,%xmm6
	psllq	$7,%xmm7
	pxor	%xmm0,%xmm6
	movdqa	%xmm3,%xmm0
	pxor	%xmm7,%xmm6
	movdqa	%xmm3,%xmm7
	psrlq	$6,%xmm0
	paddq	%xmm6,%xmm4
	movdqa	%xmm3,%xmm6
	psrlq	$19,%xmm7
	psllq	$3,%xmm6
	pxor	%xmm7,%xmm0
	psrlq	$42,%xmm7
	pxor	%xmm6,%xmm0
	psllq	$42,%xmm6
	pxor	%xmm7,%xmm0
	movdqa	32(%edx),%xmm7
	pxor	%xmm6,%xmm0
	movdqa	64(%ebp),%xmm6
	movq	%mm4,%mm1
	paddq	%xmm0,%xmm4
	movq	-64(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,32(%esp)
	paddq	%xmm4,%xmm6
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm0
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	56(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	24(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	8(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	32(%esp),%mm5
	paddq	%mm6,%mm2
	movq	40(%esp),%mm6
	movq	%mm4,%mm1
	movq	-56(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,24(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm2
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,56(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	48(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	16(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	24(%esp),%mm5
	paddq	%mm6,%mm0
	movq	32(%esp),%mm6
	movdqa	%xmm6,-64(%edx)
	movdqa	%xmm2,%xmm0
	movdqa	%xmm7,%xmm6
.byte	102,15,58,15,253,8
	movdqa	%xmm1,16(%edx)
.byte	102,15,58,15,193,8
	movdqa	%xmm7,%xmm1
	psrlq	$7,%xmm7
	paddq	%xmm0,%xmm5
	movdqa	%xmm1,%xmm0
	psrlq	$1,%xmm1
	psllq	$56,%xmm0
	pxor	%xmm1,%xmm7
	psrlq	$7,%xmm1
	pxor	%xmm0,%xmm7
	psllq	$7,%xmm0
	pxor	%xmm1,%xmm7
	movdqa	%xmm4,%xmm1
	pxor	%xmm0,%xmm7
	movdqa	%xmm4,%xmm0
	psrlq	$6,%xmm1
	paddq	%xmm7,%xmm5
	movdqa	%xmm4,%xmm7
	psrlq	$19,%xmm0
	psllq	$3,%xmm7
	pxor	%xmm0,%xmm1
	psrlq	$42,%xmm0
	pxor	%xmm7,%xmm1
	psllq	$42,%xmm7
	pxor	%xmm0,%xmm1
	movdqa	48(%edx),%xmm0
	pxor	%xmm7,%xmm1
	movdqa	80(%ebp),%xmm7
	movq	%mm4,%mm1
	paddq	%xmm1,%xmm5
	movq	-48(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,16(%esp)
	paddq	%xmm5,%xmm7
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm0
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,48(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	40(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	8(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	56(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	16(%esp),%mm5
	paddq	%mm6,%mm2
	movq	24(%esp),%mm6
	movq	%mm4,%mm1
	movq	-40(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,8(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm2
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,40(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	32(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	48(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	8(%esp),%mm5
	paddq	%mm6,%mm0
	movq	16(%esp),%mm6
	movdqa	%xmm7,-48(%edx)
	movdqa	%xmm3,%xmm1
	movdqa	%xmm0,%xmm7
.byte	102,15,58,15,198,8
	movdqa	%xmm2,32(%edx)
.byte	102,15,58,15,202,8
	movdqa	%xmm0,%xmm2
	psrlq	$7,%xmm0
	paddq	%xmm1,%xmm6
	movdqa	%xmm2,%xmm1
	psrlq	$1,%xmm2
	psllq	$56,%xmm1
	pxor	%xmm2,%xmm0
	psrlq	$7,%xmm2
	pxor	%xmm1,%xmm0
	psllq	$7,%xmm1
	pxor	%xmm2,%xmm0
	movdqa	%xmm5,%xmm2
	pxor	%xmm1,%xmm0
	movdqa	%xmm5,%xmm1
	psrlq	$6,%xmm2
	paddq	%xmm0,%xmm6
	movdqa	%xmm5,%xmm0
	psrlq	$19,%xmm1
	psllq	$3,%xmm0
	pxor	%xmm1,%xmm2
	psrlq	$42,%xmm1
	pxor	%xmm0,%xmm2
	psllq	$42,%xmm0
	pxor	%xmm1,%xmm2
	movdqa	(%edx),%xmm1
	pxor	%xmm0,%xmm2
	movdqa	96(%ebp),%xmm0
	movq	%mm4,%mm1
	paddq	%xmm2,%xmm6
	movq	-32(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,(%esp)
	paddq	%xmm6,%xmm0
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm0
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,32(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	24(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	56(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	40(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	(%esp),%mm5
	paddq	%mm6,%mm2
	movq	8(%esp),%mm6
	movq	%mm4,%mm1
	movq	-24(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,56(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm2
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,24(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	16(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	48(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	32(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	56(%esp),%mm5
	paddq	%mm6,%mm0
	movq	(%esp),%mm6
	movdqa	%xmm0,-32(%edx)
	movdqa	%xmm4,%xmm2
	movdqa	%xmm1,%xmm0
.byte	102,15,58,15,207,8
	movdqa	%xmm3,48(%edx)
.byte	102,15,58,15,211,8
	movdqa	%xmm1,%xmm3
	psrlq	$7,%xmm1
	paddq	%xmm2,%xmm7
	movdqa	%xmm3,%xmm2
	psrlq	$1,%xmm3
	psllq	$56,%xmm2
	pxor	%xmm3,%xmm1
	psrlq	$7,%xmm3
	pxor	%xmm2,%xmm1
	psllq	$7,%xmm2
	pxor	%xmm3,%xmm1
	movdqa	%xmm6,%xmm3
	pxor	%xmm2,%xmm1
	movdqa	%xmm6,%xmm2
	psrlq	$6,%xmm3
	paddq	%xmm1,%xmm7
	movdqa	%xmm6,%xmm1
	psrlq	$19,%xmm2
	psllq	$3,%xmm1
	pxor	%xmm2,%xmm3
	psrlq	$42,%xmm2
	pxor	%xmm1,%xmm3
	psllq	$42,%xmm1
	pxor	%xmm2,%xmm3
	movdqa	16(%edx),%xmm2
	pxor	%xmm1,%xmm3
	movdqa	112(%ebp),%xmm1
	movq	%mm4,%mm1
	paddq	%xmm3,%xmm7
	movq	-16(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,48(%esp)
	paddq	%xmm7,%xmm1
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm0
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,16(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	8(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	40(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	24(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	48(%esp),%mm5
	paddq	%mm6,%mm2
	movq	56(%esp),%mm6
	movq	%mm4,%mm1
	movq	-8(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,40(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm2
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,8(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	32(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	16(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	40(%esp),%mm5
	paddq	%mm6,%mm0
	movq	48(%esp),%mm6
	movdqa	%xmm1,-16(%edx)
	leal	128(%ebp),%ebp
	decl	%ecx
	jnz	.L00600_47_ssse3
	movdqa	(%ebp),%xmm1
	leal	-640(%ebp),%ebp
	movdqu	(%ebx),%xmm0
.byte	102,15,56,0,193
	movdqa	(%ebp),%xmm3
	movdqa	%xmm1,%xmm2
	movdqu	16(%ebx),%xmm1
	paddq	%xmm0,%xmm3
.byte	102,15,56,0,202
	movq	%mm4,%mm1
	movq	-128(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,32(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm0
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	56(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	24(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	8(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	32(%esp),%mm5
	paddq	%mm6,%mm2
	movq	40(%esp),%mm6
	movq	%mm4,%mm1
	movq	-120(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,24(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm2
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,56(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	48(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	16(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	24(%esp),%mm5
	paddq	%mm6,%mm0
	movq	32(%esp),%mm6
	movdqa	%xmm3,-128(%edx)
	movdqa	16(%ebp),%xmm4
	movdqa	%xmm2,%xmm3
	movdqu	32(%ebx),%xmm2
	paddq	%xmm1,%xmm4
.byte	102,15,56,0,211
	movq	%mm4,%mm1
	movq	-112(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,16(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm0
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,48(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	40(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	8(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	56(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	16(%esp),%mm5
	paddq	%mm6,%mm2
	movq	24(%esp),%mm6
	movq	%mm4,%mm1
	movq	-104(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,8(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm2
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,40(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	32(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	48(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	8(%esp),%mm5
	paddq	%mm6,%mm0
	movq	16(%esp),%mm6
	movdqa	%xmm4,-112(%edx)
	movdqa	32(%ebp),%xmm5
	movdqa	%xmm3,%xmm4
	movdqu	48(%ebx),%xmm3
	paddq	%xmm2,%xmm5
.byte	102,15,56,0,220
	movq	%mm4,%mm1
	movq	-96(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm0
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,32(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	24(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	56(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	40(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	(%esp),%mm5
	paddq	%mm6,%mm2
	movq	8(%esp),%mm6
	movq	%mm4,%mm1
	movq	-88(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,56(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm2
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,24(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	16(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	48(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	32(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	56(%esp),%mm5
	paddq	%mm6,%mm0
	movq	(%esp),%mm6
	movdqa	%xmm5,-96(%edx)
	movdqa	48(%ebp),%xmm6
	movdqa	%xmm4,%xmm5
	movdqu	64(%ebx),%xmm4
	paddq	%xmm3,%xmm6
.byte	102,15,56,0,229
	movq	%mm4,%mm1
	movq	-80(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,48(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm0
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,16(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	8(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	40(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	24(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	48(%esp),%mm5
	paddq	%mm6,%mm2
	movq	56(%esp),%mm6
	movq	%mm4,%mm1
	movq	-72(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,40(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm2
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,8(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	32(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	16(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	40(%esp),%mm5
	paddq	%mm6,%mm0
	movq	48(%esp),%mm6
	movdqa	%xmm6,-80(%edx)
	movdqa	64(%ebp),%xmm7
	movdqa	%xmm5,%xmm6
	movdqu	80(%ebx),%xmm5
	paddq	%xmm4,%xmm7
.byte	102,15,56,0,238
	movq	%mm4,%mm1
	movq	-64(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,32(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm0
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	56(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	24(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	8(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	32(%esp),%mm5
	paddq	%mm6,%mm2
	movq	40(%esp),%mm6
	movq	%mm4,%mm1
	movq	-56(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,24(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm2
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,56(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	48(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	16(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	24(%esp),%mm5
	paddq	%mm6,%mm0
	movq	32(%esp),%mm6
	movdqa	%xmm7,-64(%edx)
	movdqa	%xmm0,(%edx)
	movdqa	80(%ebp),%xmm0
	movdqa	%xmm6,%xmm7
	movdqu	96(%ebx),%xmm6
	paddq	%xmm5,%xmm0
.byte	102,15,56,0,247
	movq	%mm4,%mm1
	movq	-48(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,16(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm0
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,48(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	40(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	8(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	56(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	16(%esp),%mm5
	paddq	%mm6,%mm2
	movq	24(%esp),%mm6
	movq	%mm4,%mm1
	movq	-40(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,8(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm2
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,40(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	32(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	48(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	8(%esp),%mm5
	paddq	%mm6,%mm0
	movq	16(%esp),%mm6
	movdqa	%xmm0,-48(%edx)
	movdqa	%xmm1,16(%edx)
	movdqa	96(%ebp),%xmm1
	movdqa	%xmm7,%xmm0
	movdqu	112(%ebx),%xmm7
	paddq	%xmm6,%xmm1
.byte	102,15,56,0,248
	movq	%mm4,%mm1
	movq	-32(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm0
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,32(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	24(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	56(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	40(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	(%esp),%mm5
	paddq	%mm6,%mm2
	movq	8(%esp),%mm6
	movq	%mm4,%mm1
	movq	-24(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,56(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm2
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,24(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	16(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	48(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	32(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	56(%esp),%mm5
	paddq	%mm6,%mm0
	movq	(%esp),%mm6
	movdqa	%xmm1,-32(%edx)
	movdqa	%xmm2,32(%edx)
	movdqa	112(%ebp),%xmm2
	movdqa	(%edx),%xmm0
	paddq	%xmm7,%xmm2
	movq	%mm4,%mm1
	movq	-16(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,48(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm0
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm0,16(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	8(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	40(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm0,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm0,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	24(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm0,%mm2
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	pxor	%mm7,%mm6
	movq	48(%esp),%mm5
	paddq	%mm6,%mm2
	movq	56(%esp),%mm6
	movq	%mm4,%mm1
	movq	-8(%edx),%mm7
	pxor	%mm6,%mm5
	psrlq	$14,%mm1
	movq	%mm4,40(%esp)
	pand	%mm4,%mm5
	psllq	$23,%mm4
	paddq	%mm3,%mm2
	movq	%mm1,%mm3
	psrlq	$4,%mm1
	pxor	%mm6,%mm5
	pxor	%mm4,%mm3
	psllq	$23,%mm4
	pxor	%mm1,%mm3
	movq	%mm2,8(%esp)
	paddq	%mm5,%mm7
	pxor	%mm4,%mm3
	psrlq	$23,%mm1
	paddq	(%esp),%mm7
	pxor	%mm1,%mm3
	psllq	$4,%mm4
	pxor	%mm4,%mm3
	movq	32(%esp),%mm4
	paddq	%mm7,%mm3
	movq	%mm2,%mm5
	psrlq	$28,%mm5
	paddq	%mm3,%mm4
	movq	%mm2,%mm6
	movq	%mm5,%mm7
	psllq	$25,%mm6
	movq	16(%esp),%mm1
	psrlq	$6,%mm5
	pxor	%mm6,%mm7
	psllq	$5,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm2
	psrlq	$5,%mm5
	pxor	%mm6,%mm7
	pand	%mm2,%mm0
	psllq	$6,%mm6
	pxor	%mm5,%mm7
	pxor	%mm1,%mm0
	pxor	%mm7,%mm6
	movq	40(%esp),%mm5
	paddq	%mm6,%mm0
	movq	48(%esp),%mm6
	movdqa	%xmm2,-16(%edx)
	movq	8(%esp),%mm1
	paddq	%mm3,%mm0
	movq	24(%esp),%mm3
	movq	56(%esp),%mm7
	pxor	%mm1,%mm2
	paddq	(%esi),%mm0
	paddq	8(%esi),%mm1
	paddq	16(%esi),%mm2
	paddq	24(%esi),%mm3
	paddq	32(%esi),%mm4
	paddq	40(%esi),%mm5
	paddq	48(%esi),%mm6
	paddq	56(%esi),%mm7
	movq	%mm0,(%esi)
	movq	%mm1,8(%esi)
	movq	%mm2,16(%esi)
	movq	%mm3,24(%esi)
	movq	%mm4,32(%esi)
	movq	%mm5,40(%esi)
	movq	%mm6,48(%esi)
	movq	%mm7,56(%esi)
	cmpl	%eax,%edi
	jb	.L005loop_ssse3
	movl	76(%edx),%esp
	emms
	popl	%edi
	popl	%esi
	popl	%ebx
	popl	%ebp
	ret
.size	sha512_block_data_order_ssse3,.-.L_sha512_block_data_order_ssse3_begin
.align	64
.LK512:
.long	3609767458,1116352408
.long	602891725,1899447441
.long	3964484399,3049323471
.long	2173295548,3921009573
.long	4081628472,961987163
.long	3053834265,1508970993
.long	2937671579,2453635748
.long	3664609560,2870763221
.long	2734883394,3624381080
.long	1164996542,310598401
.long	1323610764,607225278
.long	3590304994,1426881987
.long	4068182383,1925078388
.long	991336113,2162078206
.long	633803317,2614888103
.long	3479774868,3248222580
.long	2666613458,3835390401
.long	944711139,4022224774
.long	2341262773,264347078
.long	2007800933,604807628
.long	1495990901,770255983
.long	1856431235,1249150122
.long	3175218132,1555081692
.long	2198950837,1996064986
.long	3999719339,2554220882
.long	766784016,2821834349
.long	2566594879,2952996808
.long	3203337956,3210313671
.long	1034457026,3336571891
.long	2466948901,3584528711
.long	3758326383,113926993
.long	168717936,338241895
.long	1188179964,666307205
.long	1546045734,773529912
.long	1522805485,1294757372
.long	2643833823,1396182291
.long	2343527390,1695183700
.long	1014477480,1986661051
.long	1206759142,2177026350
.long	344077627,2456956037
.long	1290863460,2730485921
.long	3158454273,2820302411
.long	3505952657,3259730800
.long	106217008,3345764771
.long	3606008344,3516065817
.long	1432725776,3600352804
.long	1467031594,4094571909
.long	851169720,275423344
.long	3100823752,430227734
.long	1363258195,506948616
.long	3750685593,659060556
.long	3785050280,883997877
.long	3318307427,958139571
.long	3812723403,1322822218
.long	2003034995,1537002063
.long	3602036899,1747873779
.long	1575990012,1955562222
.long	1125592928,2024104815
.long	2716904306,2227730452
.long	442776044,2361852424
.long	593698344,2428436474
.long	3733110249,2756734187
.long	2999351573,3204031479
.long	3815920427,3329325298
.long	3928383900,3391569614
.long	566280711,3515267271
.long	3454069534,3940187606
.long	4000239992,4118630271
.long	1914138554,116418474
.long	2731055270,174292421
.long	3203993006,289380356
.long	320620315,460393269
.long	587496836,685471733
.long	1086792851,852142971
.long	365543100,1017036298
.long	2618297676,1126000580
.long	3409855158,1288033470
.long	4234509866,1501505948
.long	987167468,1607167915
.long	1246189591,1816402316
.long	67438087,66051
.long	202182159,134810123
.byte	83,72,65,53,49,50,32,98,108,111,99,107,32,116,114,97
.byte	110,115,102,111,114,109,32,102,111,114,32,120,56,54,44,32
.byte	67,82,89,80,84,79,71,65,77,83,32,98,121,32,60,97
.byte	112,112,114,111,64,111,112,101,110,115,115,108,46,111,114,103
.byte	62,0
#endif  // !defined(OPENSSL_NO_ASM) && defined(OPENSSL_X86) && defined(__ELF__)
