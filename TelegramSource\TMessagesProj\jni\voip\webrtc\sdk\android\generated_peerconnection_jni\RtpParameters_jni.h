// Copyright 2014 The Chromium Authors
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.


// This file is autogenerated by
//     third_party/jni_zero/jni_generator.py
// For
//     org/webrtc/RtpParameters

#ifndef org_webrtc_RtpParameters_JNI
#define org_webrtc_RtpParameters_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "webrtc/sdk/android/src/jni/jni_generator_helper.h"


// Step 1: Forward declarations.

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_RtpParameters[];
const char kClassPath_org_webrtc_RtpParameters[] = "org/webrtc/RtpParameters";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_RtpParameters_00024Codec[];
const char kClassPath_org_webrtc_RtpParameters_00024Codec[] = "org/webrtc/RtpParameters$Codec";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_RtpParameters_00024DegradationPreference[];
const char kClassPath_org_webrtc_RtpParameters_00024DegradationPreference[] =
    "org/webrtc/RtpParameters$DegradationPreference";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_RtpParameters_00024Encoding[];
const char kClassPath_org_webrtc_RtpParameters_00024Encoding[] =
    "org/webrtc/RtpParameters$Encoding";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char
    kClassPath_org_webrtc_RtpParameters_00024HeaderExtension[];
const char kClassPath_org_webrtc_RtpParameters_00024HeaderExtension[] =
    "org/webrtc/RtpParameters$HeaderExtension";

JNI_ZERO_COMPONENT_BUILD_EXPORT extern const char kClassPath_org_webrtc_RtpParameters_00024Rtcp[];
const char kClassPath_org_webrtc_RtpParameters_00024Rtcp[] = "org/webrtc/RtpParameters$Rtcp";
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass> g_org_webrtc_RtpParameters_clazz(nullptr);
#ifndef org_webrtc_RtpParameters_clazz_defined
#define org_webrtc_RtpParameters_clazz_defined
inline jclass org_webrtc_RtpParameters_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_RtpParameters,
      &g_org_webrtc_RtpParameters_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_RtpParameters_00024Codec_clazz(nullptr);
#ifndef org_webrtc_RtpParameters_00024Codec_clazz_defined
#define org_webrtc_RtpParameters_00024Codec_clazz_defined
inline jclass org_webrtc_RtpParameters_00024Codec_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_RtpParameters_00024Codec,
      &g_org_webrtc_RtpParameters_00024Codec_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_RtpParameters_00024DegradationPreference_clazz(nullptr);
#ifndef org_webrtc_RtpParameters_00024DegradationPreference_clazz_defined
#define org_webrtc_RtpParameters_00024DegradationPreference_clazz_defined
inline jclass org_webrtc_RtpParameters_00024DegradationPreference_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_RtpParameters_00024DegradationPreference,
      &g_org_webrtc_RtpParameters_00024DegradationPreference_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_RtpParameters_00024Encoding_clazz(nullptr);
#ifndef org_webrtc_RtpParameters_00024Encoding_clazz_defined
#define org_webrtc_RtpParameters_00024Encoding_clazz_defined
inline jclass org_webrtc_RtpParameters_00024Encoding_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_RtpParameters_00024Encoding,
      &g_org_webrtc_RtpParameters_00024Encoding_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_RtpParameters_00024HeaderExtension_clazz(nullptr);
#ifndef org_webrtc_RtpParameters_00024HeaderExtension_clazz_defined
#define org_webrtc_RtpParameters_00024HeaderExtension_clazz_defined
inline jclass org_webrtc_RtpParameters_00024HeaderExtension_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_RtpParameters_00024HeaderExtension,
      &g_org_webrtc_RtpParameters_00024HeaderExtension_clazz);
}
#endif
// Leaking this jclass as we cannot use LazyInstance from some threads.
JNI_ZERO_COMPONENT_BUILD_EXPORT std::atomic<jclass>
    g_org_webrtc_RtpParameters_00024Rtcp_clazz(nullptr);
#ifndef org_webrtc_RtpParameters_00024Rtcp_clazz_defined
#define org_webrtc_RtpParameters_00024Rtcp_clazz_defined
inline jclass org_webrtc_RtpParameters_00024Rtcp_clazz(JNIEnv* env) {
  return jni_zero::LazyGetClass(env, kClassPath_org_webrtc_RtpParameters_00024Rtcp,
      &g_org_webrtc_RtpParameters_00024Rtcp_clazz);
}
#endif


// Step 2: Constants (optional).


// Step 3: Method stubs.
namespace webrtc {
namespace jni {


static std::atomic<jmethodID> g_org_webrtc_RtpParameters_Constructor6(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RtpParameters_Constructor(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& transactionId,
    const jni_zero::JavaRef<jobject>& degradationPreference,
    const jni_zero::JavaRef<jobject>& rtcp,
    const jni_zero::JavaRef<jobject>& headerExtensions,
    const jni_zero::JavaRef<jobject>& encodings,
    const jni_zero::JavaRef<jobject>& codecs) {
  jclass clazz = org_webrtc_RtpParameters_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_RtpParameters_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
"(Ljava/lang/String;Lorg/webrtc/RtpParameters$DegradationPreference;Lorg/webrtc/RtpParameters$Rtcp;Ljava/util/List;Ljava/util/List;Ljava/util/List;)V",
          &g_org_webrtc_RtpParameters_Constructor6);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, transactionId.obj(), degradationPreference.obj(), rtcp.obj(),
              headerExtensions.obj(), encodings.obj(), codecs.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_getCodecs0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RtpParameters_getCodecs(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getCodecs",
          "()Ljava/util/List;",
          &g_org_webrtc_RtpParameters_getCodecs0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_getDegradationPreference0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RtpParameters_getDegradationPreference(JNIEnv*
    env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getDegradationPreference",
          "()Lorg/webrtc/RtpParameters$DegradationPreference;",
          &g_org_webrtc_RtpParameters_getDegradationPreference0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_getEncodings0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RtpParameters_getEncodings(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getEncodings",
          "()Ljava/util/List;",
          &g_org_webrtc_RtpParameters_getEncodings0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_getHeaderExtensions0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RtpParameters_getHeaderExtensions(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getHeaderExtensions",
          "()Ljava/util/List;",
          &g_org_webrtc_RtpParameters_getHeaderExtensions0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_getRtcp0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_RtpParameters_getRtcp(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getRtcp",
          "()Lorg/webrtc/RtpParameters$Rtcp;",
          &g_org_webrtc_RtpParameters_getRtcp0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_getTransactionId0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_RtpParameters_getTransactionId(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getTransactionId",
          "()Ljava/lang/String;",
          &g_org_webrtc_RtpParameters_getTransactionId0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Codec_Constructor6(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Codec_Constructor(JNIEnv* env, JniIntWrapper
    payloadType,
    const jni_zero::JavaRef<jstring>& name,
    const jni_zero::JavaRef<jobject>& kind,
    const jni_zero::JavaRef<jobject>& clockRate,
    const jni_zero::JavaRef<jobject>& numChannels,
    const jni_zero::JavaRef<jobject>& parameters) {
  jclass clazz = org_webrtc_RtpParameters_00024Codec_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_RtpParameters_00024Codec_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
"(ILjava/lang/String;Lorg/webrtc/MediaStreamTrack$MediaType;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/util/Map;)V",
          &g_org_webrtc_RtpParameters_00024Codec_Constructor6);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, as_jint(payloadType), name.obj(), kind.obj(),
              clockRate.obj(), numChannels.obj(), parameters.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Codec_getClockRate0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Codec_getClockRate(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Codec_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Codec_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getClockRate",
          "()Ljava/lang/Integer;",
          &g_org_webrtc_RtpParameters_00024Codec_getClockRate0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Codec_getKind0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Codec_getKind(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Codec_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Codec_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getKind",
          "()Lorg/webrtc/MediaStreamTrack$MediaType;",
          &g_org_webrtc_RtpParameters_00024Codec_getKind0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Codec_getName0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Codec_getName(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Codec_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Codec_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getName",
          "()Ljava/lang/String;",
          &g_org_webrtc_RtpParameters_00024Codec_getName0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Codec_getNumChannels0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Codec_getNumChannels(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Codec_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Codec_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getNumChannels",
          "()Ljava/lang/Integer;",
          &g_org_webrtc_RtpParameters_00024Codec_getNumChannels0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Codec_getParameters0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Codec_getParameters(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Codec_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Codec_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getParameters",
          "()Ljava/util/Map;",
          &g_org_webrtc_RtpParameters_00024Codec_getParameters0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Codec_getPayloadType0(nullptr);
static jint Java_Codec_getPayloadType(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Codec_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Codec_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getPayloadType",
          "()I",
          &g_org_webrtc_RtpParameters_00024Codec_getPayloadType0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpParameters_00024DegradationPreference_fromNativeIndex1(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_DegradationPreference_fromNativeIndex(JNIEnv* env,
    JniIntWrapper nativeIndex) {
  jclass clazz = org_webrtc_RtpParameters_00024DegradationPreference_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_RtpParameters_00024DegradationPreference_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_STATIC>(
          env,
          clazz,
          "fromNativeIndex",
          "(I)Lorg/webrtc/RtpParameters$DegradationPreference;",
          &g_org_webrtc_RtpParameters_00024DegradationPreference_fromNativeIndex1);

  jobject ret =
      env->CallStaticObjectMethod(clazz,
          call_context.base.method_id, as_jint(nativeIndex));
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Encoding_Constructor11(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Encoding_Constructor(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& rid,
    jboolean active,
    jdouble bitratePriority,
    JniIntWrapper networkPriority,
    const jni_zero::JavaRef<jobject>& maxBitrateBps,
    const jni_zero::JavaRef<jobject>& minBitrateBps,
    const jni_zero::JavaRef<jobject>& maxFramerate,
    const jni_zero::JavaRef<jobject>& numTemporalLayers,
    const jni_zero::JavaRef<jobject>& scaleResolutionDownBy,
    const jni_zero::JavaRef<jobject>& ssrc,
    jboolean adaptiveAudioPacketTime) {
  jclass clazz = org_webrtc_RtpParameters_00024Encoding_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_RtpParameters_00024Encoding_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
"(Ljava/lang/String;ZDILjava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Integer;Ljava/lang/Double;Ljava/lang/Long;Z)V",
          &g_org_webrtc_RtpParameters_00024Encoding_Constructor11);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, rid.obj(), active, bitratePriority, as_jint(networkPriority),
              maxBitrateBps.obj(), minBitrateBps.obj(), maxFramerate.obj(), numTemporalLayers.obj(),
              scaleResolutionDownBy.obj(), ssrc.obj(), adaptiveAudioPacketTime);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Encoding_getActive0(nullptr);
static jboolean Java_Encoding_getActive(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Encoding_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Encoding_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getActive",
          "()Z",
          &g_org_webrtc_RtpParameters_00024Encoding_getActive0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Encoding_getAdaptivePTime0(nullptr);
static jboolean Java_Encoding_getAdaptivePTime(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Encoding_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Encoding_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getAdaptivePTime",
          "()Z",
          &g_org_webrtc_RtpParameters_00024Encoding_getAdaptivePTime0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Encoding_getBitratePriority0(nullptr);
static jdouble Java_Encoding_getBitratePriority(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj)
    {
  jclass clazz = org_webrtc_RtpParameters_00024Encoding_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Encoding_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getBitratePriority",
          "()D",
          &g_org_webrtc_RtpParameters_00024Encoding_getBitratePriority0);

  jdouble ret =
      env->CallDoubleMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Encoding_getMaxBitrateBps0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Encoding_getMaxBitrateBps(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Encoding_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Encoding_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getMaxBitrateBps",
          "()Ljava/lang/Integer;",
          &g_org_webrtc_RtpParameters_00024Encoding_getMaxBitrateBps0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Encoding_getMaxFramerate0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Encoding_getMaxFramerate(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Encoding_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Encoding_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getMaxFramerate",
          "()Ljava/lang/Integer;",
          &g_org_webrtc_RtpParameters_00024Encoding_getMaxFramerate0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Encoding_getMinBitrateBps0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Encoding_getMinBitrateBps(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Encoding_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Encoding_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getMinBitrateBps",
          "()Ljava/lang/Integer;",
          &g_org_webrtc_RtpParameters_00024Encoding_getMinBitrateBps0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Encoding_getNetworkPriority0(nullptr);
static jint Java_Encoding_getNetworkPriority(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Encoding_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Encoding_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getNetworkPriority",
          "()I",
          &g_org_webrtc_RtpParameters_00024Encoding_getNetworkPriority0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpParameters_00024Encoding_getNumTemporalLayers0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Encoding_getNumTemporalLayers(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Encoding_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Encoding_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getNumTemporalLayers",
          "()Ljava/lang/Integer;",
          &g_org_webrtc_RtpParameters_00024Encoding_getNumTemporalLayers0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Encoding_getRid0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Encoding_getRid(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Encoding_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Encoding_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getRid",
          "()Ljava/lang/String;",
          &g_org_webrtc_RtpParameters_00024Encoding_getRid0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpParameters_00024Encoding_getScaleResolutionDownBy0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Encoding_getScaleResolutionDownBy(JNIEnv* env,
    const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Encoding_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Encoding_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getScaleResolutionDownBy",
          "()Ljava/lang/Double;",
          &g_org_webrtc_RtpParameters_00024Encoding_getScaleResolutionDownBy0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Encoding_getSsrc0(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Encoding_getSsrc(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Encoding_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Encoding_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getSsrc",
          "()Ljava/lang/Long;",
          &g_org_webrtc_RtpParameters_00024Encoding_getSsrc0);

  jobject ret =
      env->CallObjectMethod(obj.obj(),
          call_context.base.method_id);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024HeaderExtension_Constructor3(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_HeaderExtension_Constructor(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& uri,
    JniIntWrapper id,
    jboolean encrypted) {
  jclass clazz = org_webrtc_RtpParameters_00024HeaderExtension_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_RtpParameters_00024HeaderExtension_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Ljava/lang/String;IZ)V",
          &g_org_webrtc_RtpParameters_00024HeaderExtension_Constructor3);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, uri.obj(), as_jint(id), encrypted);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID>
    g_org_webrtc_RtpParameters_00024HeaderExtension_getEncrypted0(nullptr);
static jboolean Java_HeaderExtension_getEncrypted(JNIEnv* env, const jni_zero::JavaRef<jobject>&
    obj) {
  jclass clazz = org_webrtc_RtpParameters_00024HeaderExtension_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024HeaderExtension_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getEncrypted",
          "()Z",
          &g_org_webrtc_RtpParameters_00024HeaderExtension_getEncrypted0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024HeaderExtension_getId0(nullptr);
static jint Java_HeaderExtension_getId(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024HeaderExtension_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024HeaderExtension_clazz(env), 0);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getId",
          "()I",
          &g_org_webrtc_RtpParameters_00024HeaderExtension_getId0);

  jint ret =
      env->CallIntMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024HeaderExtension_getUri0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_HeaderExtension_getUri(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024HeaderExtension_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024HeaderExtension_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getUri",
          "()Ljava/lang/String;",
          &g_org_webrtc_RtpParameters_00024HeaderExtension_getUri0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Rtcp_Constructor2(nullptr);
static jni_zero::ScopedJavaLocalRef<jobject> Java_Rtcp_Constructor(JNIEnv* env, const
    jni_zero::JavaRef<jstring>& cname,
    jboolean reducedSize) {
  jclass clazz = org_webrtc_RtpParameters_00024Rtcp_clazz(env);
  CHECK_CLAZZ(env, clazz,
      org_webrtc_RtpParameters_00024Rtcp_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "<init>",
          "(Ljava/lang/String;Z)V",
          &g_org_webrtc_RtpParameters_00024Rtcp_Constructor2);

  jobject ret =
      env->NewObject(clazz,
          call_context.base.method_id, cname.obj(), reducedSize);
  return jni_zero::ScopedJavaLocalRef<jobject>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Rtcp_getCname0(nullptr);
static jni_zero::ScopedJavaLocalRef<jstring> Java_Rtcp_getCname(JNIEnv* env, const
    jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Rtcp_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Rtcp_clazz(env), nullptr);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getCname",
          "()Ljava/lang/String;",
          &g_org_webrtc_RtpParameters_00024Rtcp_getCname0);

  jstring ret =
      static_cast<jstring>(env->CallObjectMethod(obj.obj(),
          call_context.base.method_id));
  return jni_zero::ScopedJavaLocalRef<jstring>(env, ret);
}

static std::atomic<jmethodID> g_org_webrtc_RtpParameters_00024Rtcp_getReducedSize0(nullptr);
static jboolean Java_Rtcp_getReducedSize(JNIEnv* env, const jni_zero::JavaRef<jobject>& obj) {
  jclass clazz = org_webrtc_RtpParameters_00024Rtcp_clazz(env);
  CHECK_CLAZZ(env, obj.obj(),
      org_webrtc_RtpParameters_00024Rtcp_clazz(env), false);

  jni_zero::JniJavaCallContextChecked call_context;
  call_context.Init<
      jni_zero::MethodID::TYPE_INSTANCE>(
          env,
          clazz,
          "getReducedSize",
          "()Z",
          &g_org_webrtc_RtpParameters_00024Rtcp_getReducedSize0);

  jboolean ret =
      env->CallBooleanMethod(obj.obj(),
          call_context.base.method_id);
  return ret;
}

}  // namespace jni
}  // namespace webrtc

#endif  // org_webrtc_RtpParameters_JNI
