This directory consumes tools from other repositories for use on the
bots. For a similarly-configured local checkout for testing, run:

   mkdir boringssl-gclient
   cd boringssl-gclient
   gclient config --unmanaged --deps-file=util/bot/DEPS https://boringssl.googlesource.com/boringssl/
   gclient sync

To update to newer revisions, follow these instructions:

DEPS: Update the variables as described in the comments.

vs_toolchain.py: Update _GetDesiredVsToolchainHashes from Chromium, found at
    https://chromium.googlesource.com/chromium/src/+/main/build/vs_toolchain.py
    This may require taking other updates to that file. (Don't remove MSVC
    versions if BoringSSL still needs to support them.)

The .sha1 files correspond to files downloaded from Google Cloud Storage. To
update, place the updated files in their intended location and run:

    upload_to_google_storage.py -b chromium-tools FILE

nasm-win32.exe: Update to the appropriate release of NASM, found at
    https://www.nasm.us/. Use the same version as Chromium, found at
    https://chromium.googlesource.com/chromium/deps/nasm/+/refs/heads/main/README.chromium
    Extract nasm.exe from the download named nasm-VERSION-win64.zip.

    The current revision is nasm-2.13.03-win64.zip.

Finally, update sde-linux64.tar.xz and sde-win32.tar.xz by downloading the
latet release from Intel at
https://software.intel.com/en-us/articles/intel-software-development-emulator,
but upload it with the following command. (Note the bucket is different.)

    upload_to_google_storage.py -b chrome-boringssl-sde sde-linux64.tar.xz sde-win32.tar.xz

The current revision is sde-external-9.38.0-2024-04-18-*.tar.xz.

When adding new files, remember to update .gitignore.
