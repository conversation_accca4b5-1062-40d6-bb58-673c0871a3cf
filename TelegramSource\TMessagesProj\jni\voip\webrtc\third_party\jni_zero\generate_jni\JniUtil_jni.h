// This file was generated by
//     //third_party/jni_zero/jni_zero.py
// For
//     org.jni_zero.JniUtil

#ifndef org_jni_1zero_JniUtil_JNI
#define org_jni_1zero_JniUtil_JNI

#include <jni.h>

#include "third_party/jni_zero/jni_export.h"
#include "../../../../../../../third_party/jni_zero/jni_zero_internal.h"

// Class Accessors
#ifndef org_jni_1zero_JniUtil_clazz_defined
#define org_jni_1zero_JniUtil_clazz_defined
inline jclass org_jni_1zero_JniUtil_clazz(JNIEnv* env) {
  static const char kClassName[] = "org/jni_zero/JniUtil";
  static std::atomic<jclass> cached_class;
  return jni_zero::internal::LazyGetClass(env, kClassName, &cached_class);
}
#endif


// Native to Java functions
static jni_zero::ScopedJavaLocalRef<jobject> Java_JniUtil_arrayToMap(
    JNIEnv* env,
    const jni_zero::JavaRef<jobjectArray>& array) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = org_jni_1zero_JniUtil_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "arrayToMap",
      "([Ljava/lang/Object;)Ljava/util/Map;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), array.obj());
  return jni_zero::ScopedJavaLocalRef<jobject>(env, _ret);
}

static jni_zero::ScopedJavaLocalRef<jobjectArray> Java_JniUtil_mapToArray(
    JNIEnv* env,
    const jni_zero::JavaRef<jobject>& map) {
  static std::atomic<jmethodID> cached_method_id(nullptr);
  jclass clazz = org_jni_1zero_JniUtil_clazz(env);
  CHECK_CLAZZ(env, clazz, clazz, nullptr);
  jni_zero::internal::JniJavaCallContext<true> call_context;
  call_context.Init<jni_zero::MethodID::TYPE_STATIC>(
      env,
      clazz,
      "mapToArray",
      "(Ljava/util/Map;)[Ljava/lang/Object;",
      &cached_method_id);
  auto _ret = env->CallStaticObjectMethod(clazz, call_context.method_id(), map.obj());
  jobjectArray _ret2 = static_cast<jobjectArray>(_ret);
  return jni_zero::ScopedJavaLocalRef<jobjectArray>(env, _ret2);
}



#endif  // org_jni_1zero_JniUtil_JNI
