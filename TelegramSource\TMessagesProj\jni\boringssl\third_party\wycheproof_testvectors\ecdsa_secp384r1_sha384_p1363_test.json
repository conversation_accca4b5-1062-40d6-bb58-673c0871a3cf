{"algorithm": "ECDSA", "generatorVersion": "0.8r12", "numberOfTests": 239, "header": ["Test vectors of type EcdsaVerify are meant for the verification", "of IEEE P1363 encoded ECDSA signatures."], "notes": {"EdgeCase": "Edge case values such as r=1 and s=0 can lead to forgeries if the ECDSA implementation does not check boundaries and computes s^(-1)==0.", "PointDuplication": "Some implementations of ECDSA do not handle duplication and points at infinity correctly. This is a test vector that has been specially crafted to check for such an omission.", "SigSize": "The size of the signature should always be twice the number of bytes of the size of the order. But some libraries accept signatures with less bytes."}, "schema": "ecdsa_p1363_verify_schema.json", "testGroups": [{"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "LaV92hCJJ2pUP5_9rAv_DZdsrXHrcoDn2b_Z_uS9svIPR_-IgnQ4l3LZjMV1ITiq", "y": "S20FTWnc8-<PERSON><PERSON><PERSON><PERSON>cHFeNIg7GDYZfXb4rZYuePZXG7x0B7DWCR-eTYjwFCdEBhdP"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "042da57dda1089276a543f9ffdac0bff0d976cad71eb7280e7d9bfd9fee4bdb2f20f47ff888274389772d98cc5752138aa4b6d054d69dcf3e25ec49df870715e34883b1836197d76f8ad962e78f6571bbc7407b0d6091f9e4d88f014274406174f", "wx": "2da57dda1089276a543f9ffdac0bff0d976cad71eb7280e7d9bfd9fee4bdb2f20f47ff888274389772d98cc5752138aa", "wy": "4b6d054d69dcf3e25ec49df870715e34883b1836197d76f8ad962e78f6571bbc7407b0d6091f9e4d88f014274406174f"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200042da57dda1089276a543f9ffdac0bff0d976cad71eb7280e7d9bfd9fee4bdb2f20f47ff888274389772d98cc5752138aa4b6d054d69dcf3e25ec49df870715e34883b1836197d76f8ad962e78f6571bbc7407b0d6091f9e4d88f014274406174f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMH<PERSON>wEAYHKoZIzj0CAQYFK4EEACIDYgAELaV92hCJJ2pUP5/9rAv/DZdsrXHrcoDn\n2b/Z/uS9svIPR/+IgnQ4l3LZjMV1ITiqS20FTWnc8+JexJ34cHFeNIg7GDYZfXb4\nrZYuePZXG7x0B7DWCR+eTYjwFCdEBhdP\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 1, "comment": "signature malleability", "msg": "************", "sig": "12b30abef6b5476fe6b612ae557c0425661e26b44b1bfe19daf2ca28e3113083ba8e4ae4cc45a0320abd3394f1c548d71840da9fc1d2f8f8900cf485d5413b8c2574ee3a8d4ca03995ca30240e09513805bf6209b58ac7aa9cff54eecd82b9f1", "result": "valid", "flags": []}, {"tcId": 2, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "0112b30abef6b5476fe6b612ae557c0425661e26b44b1bfe19a25617aad7485e6312a8589714f647acf7a94cffbe8a724a00e7bf25603e2d07076ff30b7a2abec473da8b11c572b35fc631991d5de62ddca7525aaba89325dfd04fecc47bff426f82", "result": "invalid", "flags": []}, {"tcId": 3, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "ed4cf541094ab8901949ed51aa83fbda99e1d94bb4e401e5ec7083591125fd5b9d8bc2cd7c6b0748e22ee5d5daffe09ce7bf25603e2d07076ff30b7a2abec473da8b11c572b35fc631991d5de62ddca7525aaba89325dfd04fecc47bff426f82", "result": "invalid", "flags": []}, {"tcId": 4, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "0112b30abef6b5476fe6b612ae557c0425661e26b44b1bfe19daf2ca28e3113083ba8e4ae4cc45a0320abd3394f1c548d700e7bf25603e2d07076ff30b7a2abec473da8b11c572b35fc631991d5de62ddca7525aaba89325dfd04fecc47bff426f82", "result": "invalid", "flags": []}, {"tcId": 5, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "ed4cf541094ab8901949ed51aa83fbda99e1d94bb4e401e6250d35d71ceecf7c4571b51b33ba5fcdf542cc6b0e3ab729e7bf25603e2d07076ff30b7a2abec473da8b11c572b35fc631991d5de62ddca7525aaba89325dfd04fecc47bff426f82", "result": "invalid", "flags": []}, {"tcId": 6, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "0012b30abef6b5476fe6b612ae557c0425661e26b44b1bfe19daf2ca28e3113083ba8e4ae4cc45a0320abd3394f1c548d701e7bf25603e2d07076ff30b7a2abec473da8b11c572b35fc5f8fc6adfda650a86aa74b95adbd6874b3cd8dde6cc0798f5", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "0012b30abef6b5476fe6b612ae557c0425661e26b44b1bfe19daf2ca28e3113083ba8e4ae4cc45a0320abd3394f1c548d701e7bf25603e2d07076ff30b7a2abec473da8b11c572b35fc631991d5de62ddca7525aaba89325dfd04fecc47bff426f82", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "Modified r or s, e.g. by adding or subtracting the order of the group", "msg": "************", "sig": "12b30abef6b5476fe6b612ae557c0425661e26b44b1bfe19daf2ca28e3113083ba8e4ae4cc45a0320abd3394f1c548d71840da9fc1d2f8f8900cf485d5413b8c2574ee3a8d4ca039ce66e2a219d22358ada554576cda202fb0133b8400bd907e", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 10, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 11, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 12, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 13, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 14, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 15, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 16, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 17, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 18, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 19, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 20, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 21, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 22, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 23, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 24, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 25, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 26, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 27, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 28, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 29, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 30, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 31, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 32, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 33, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 34, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 35, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 36, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 37, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 38, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 39, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 40, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 41, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 42, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 43, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 44, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 45, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 46, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 47, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 48, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffffffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 49, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 50, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 51, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 52, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 53, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52973", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 54, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 55, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52974", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 56, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000ffffffff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 57, "comment": "Signature with special case values for r and s", "msg": "************", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff000000000000000100000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 58, "comment": "Edge case for <PERSON><PERSON><PERSON> multiplication", "msg": "3133323237", "sig": "ac042e13ab83394692019170707bc21dd3d7b8d233d11b651757085bdd5767eabbb85322984f14437335de0cdf565684bd770d3ee4beadbabe7ca46e8c4702783435228d46e2dd360e322fe61c86926fa49c8116ec940f72ac8c30d9beb3e12f", "result": "valid", "flags": []}, {"tcId": 59, "comment": "special case hash", "msg": "31373530353531383135", "sig": "d3298a0193c4316b34e3833ff764a82cff4ef57b5dd79ed6237b51ff76ceab13bf92131f41030515b7e012d2ba857830bfc7518d2ad20ed5f58f3be79720f1866f7a23b3bd1bf913d3916819d008497a071046311d3c2fd05fc284c964a39617", "result": "valid", "flags": []}, {"tcId": 60, "comment": "special case hash", "msg": "3130333633303731", "sig": "e14f41a5fc83aa4725a9ea60ab5b0b9de27f519af4b557a601f1fee0243f8eee5180f8c531414f3473f4457430cb7a261047ed2bf1f98e3ce93e8fdbdc63cc79f238998fee74e1bb6cd708694950bbffe3945066064da043f04d7083d0a596ec", "result": "valid", "flags": []}, {"tcId": 61, "comment": "special case hash", "msg": "32333632343231333231", "sig": "b7c8b5cf631a96ad908d6a8c8d0e0a35fcc22a5a36050230b665932764ae45bd84cb87ebba8e444abd89e4483fc9c4a8a11636c095aa9bc69cf24b50a0a9e5377d0ffbba4fab5433159f006ab4563d55e918493020a19691574e4d1e66e3975e", "result": "valid", "flags": []}, {"tcId": 62, "comment": "special case hash", "msg": "34353838303134363536", "sig": "4a7df2df6a32d59b6bfed54f032c3d6f3acd3ac4063704099cd162ab3908e8eeba4e973ee75b5e285dd572062338fe5835365be327e2463dc759951c5c0be5e3d094cb706912fdf7d26b15d4a5c42ffebeca5ae73a1823f5e65d571b4ccf1a82", "result": "valid", "flags": []}, {"tcId": 63, "comment": "special case hash", "msg": "32313436363035363432", "sig": "9ad363a1bbc67c57c82a378e988cc083cc91f8b32739ec647c0cb348fb5c86472015131a7d9083bf4740af3351755195d310dc1509f8c00281efe571768d488027ea760fe32971f6cb7b57cdf90621b7d0086e26443d3761df7aa3a4eccc6c58", "result": "valid", "flags": []}, {"tcId": 64, "comment": "special case hash", "msg": "333335333030383230", "sig": "95078af5c2ac230239557f5fcee2e712a7034e95437a9b34c1692a81270edcf8ddd5aba1138a42012663e5f81c9beae240ee510a0cceb8518ad4f618599164da0f3ba75eceeac216216ec62bcceae8dc98b5e35b2e7ed47c4b8ebacfe84a74e6", "result": "valid", "flags": []}, {"tcId": 65, "comment": "special case hash", "msg": "36333936363033363331", "sig": "a538076362043de54864464c14a6c1c3a478443726c1309a36b9e9ea1592b40c3f3f90d195bd298004a71e8f285e093ad74f97ef38468515a8c927a450275c14dc16ddbdd92b3a5cae804be20d29c682129247d2e01d37dabe38ffb74808a8b7", "result": "valid", "flags": []}, {"tcId": 66, "comment": "special case hash", "msg": "33333931363630373935", "sig": "bbe835113f8ea4dc469f0283af6603f3d7a3a222b3ab5a93db56007ef2dc07c97988fc7b8b833057fa3fbf97413b6c15737c316320b61002c2acb184d82e60e46bd2129a9bbf563c80da423121c161decd363518b260aaacf3734c1ef9faa925", "result": "valid", "flags": []}, {"tcId": 67, "comment": "special case hash", "msg": "31343436393735393634", "sig": "679c3640ad8ffe9577d9b59b18ff5598dbfe61122bbab8238d268907c989cd94dc7f601d17486af93f6d18624aa524a3e84dd195502bdcdd77b7f51d8c1ea789006905844a0e185474af1a583bab564ee23be0bc49500390dceb3d3948f06730", "result": "valid", "flags": []}, {"tcId": 68, "comment": "special case hash", "msg": "35313539343738363431", "sig": "f6f1afe6febce799cc9b754279f2499f3825c3e789accef46d3f068e2b6781fd50669e80c3c7293a5c0c0af48e068e35f59cc8c2222ed63b4553f8149ebecc43b866719b294ef0832a12b3e3dbc825eeab68b5779625b10ae5541412ec295354", "result": "valid", "flags": []}, {"tcId": 69, "comment": "special case hash", "msg": "35323431373932333331", "sig": "f46496f6d473f3c091a68aaa3749220c840061cd4f888613ccfeac0aa0411b451edbd4facbe38d2dd9d6d0d0d255ed3400c3a74fa6666f58c4798f30c3779813e5c6d08ac31a792c2d0f9cb708733f26ad6bf3b1e46815ae536aa151680bdee2", "result": "valid", "flags": []}, {"tcId": 70, "comment": "special case hash", "msg": "31313437323930323034", "sig": "df8b8e4cb1bc4ec69cb1472fa5a81c36642ed47fc6ce560033c4f7cb0bc8459b5788e34caa7d96e6071188e449f0207a8b8ee0177962a489938f3feffae55729d9d446fe438c7cb91ea5f632c80aa72a43b9b04e6de7ff34f76f4425107fd697", "result": "valid", "flags": []}, {"tcId": 71, "comment": "special case hash", "msg": "3130383738373235363435", "sig": "8bb6a8ecdc8b483ad7b9c94bb39f63b5fc1378efe8c0204a74631dded7159643821419af33863b0414bd87ecf73ba3fb8928449f2d6db2b2c65d44d98beb77eeadcbda83ff33e57eb183e1fc29ad86f0ba29ee66e750e8170ccc434cf70ae199", "result": "valid", "flags": []}, {"tcId": 72, "comment": "special case hash", "msg": "37333433333036353633", "sig": "e3832877c80c4ed439d8eadcf615c0286ff54943e3ae2f66a3b9f886245fea470e6d5812cef80c23e4f568d0215a3bfc3177a7dbf0ab8f8f5fc1d01b19d6a5e89642899f369dfe213b7cc55d8eaf21dd2885efce52b5959c1f06b7cac5773e5b", "result": "valid", "flags": []}, {"tcId": 73, "comment": "special case hash", "msg": "393734343630393738", "sig": "6275738f0880023286a9b6f28ea0a9779e8d644c3dec48293c64f1566b34e15c7119bd9d02fa2357774cabc9e53ef7e6d2f0a52b1016082bd5517609ee81c0764dc38a8f32d9a5074e717ee1d832f9ea0e4c6b100b1fd5e7f4bc7468c79d3933", "result": "valid", "flags": []}, {"tcId": 74, "comment": "special case hash", "msg": "33323237303836383339", "sig": "d316fe5168cf13753c8c3bbef83869a6703dc0d5afa82af49c88ff3555660f57919a6f36e84451c3e8e5783e3b83fe3b995f08c8fec7cd82ce27e7509393f5a3803a48fe255fcb160321c6e1890eb36e37bcda158f0fa6899e7d107e52de8c3c", "result": "valid", "flags": []}, {"tcId": 75, "comment": "special case hash", "msg": "323332393736343130", "sig": "0b13b8fd10fa7b42169137588ad3f557539a4e9206f3a1f1fe9202b0690defded2be18147f5b2da9285c0e7349735ea30478ad317b22a247bf9334719b4c8ee84acf134515db77e6141c75d08961e1e51eaca29836744103de0f6a4c798d3eeb", "result": "valid", "flags": []}, {"tcId": 76, "comment": "special case hash", "msg": "3934303437333831", "sig": "15804429bcb5277d4f0af73bd54c8a177499a7b64f18afc566c3ce7096bdc6c275e38548edcfa0b78dd7f57b6f393e49d5951f243e65b82ba5c0c7552d33b11f1e90fde0c3fd014aac1bb27db2aaf09b667c8b247c4cdd5b0723fba83b4f999e", "result": "valid", "flags": []}, {"tcId": 77, "comment": "special case hash", "msg": "33323230353639313233", "sig": "359247c95776bb17492b7bf827f5f330fa9f9de7cc10441a1479c81776ce36cdc6a13c5f5149c4e39147a196bb02ed34f6ed9252a73de48516f4eabab6368fbff6875128af4e1226d54db558bd76eec369cc9b285bc196d512e531f84864d33f", "result": "valid", "flags": []}, {"tcId": 78, "comment": "special case hash", "msg": "343134303533393934", "sig": "a557d1f63a2094f683429ecb35a6533bac897682775c0051e111eed6e076c48867cae005c5e0803800b050311e381cd62a2f871efcf03cf1c8f509e076aaa2a76f1ea78d1c64804ea5b063b0324b8e98eb5825d04370106020ee15805dbedf81", "result": "valid", "flags": []}, {"tcId": 79, "comment": "special case hash", "msg": "31393531353638363439", "sig": "f22bf91169b4aec84ca84041cb826f7dfc6f33d973f3c72433b8a0ca203aac93f7eed62be9bea01706402d5b5d3b0e657841d3bc34aa47e813a55c25203c5ec2342d838d5b4638c2705dcf4bac9c24f765b5d4c28fa3c7fda7a38ed5048c7de3", "result": "valid", "flags": []}, {"tcId": 80, "comment": "special case hash", "msg": "35393539303731363335", "sig": "9c196e39a2d61a3c2565f5932f357e242892737e9adfc86c6609f291e5e6fdbb23029ff915a032b0c5390ba9d15f203ed721e28e5269d7813e8a9aed53a37e652fec1560ca61f28f55ab4c262cc6214eee8d3c4c2ba9d1ba0ba19e5e3c7484a7", "result": "valid", "flags": []}, {"tcId": 81, "comment": "special case hash", "msg": "323135333436393533", "sig": "8ba1e9dec14d300b0e250ea0bcd4419c3d9559622cc7b8375bd73f7d70133242e3d5bf70bc782808734654bacd12daead893d3970f72ccab35555ae91ebcfed3c5bfc5d39181071bc06ba382587a695e02ed482f1a74fe309a399eaee5f5bc52", "result": "valid", "flags": []}, {"tcId": 82, "comment": "special case hash", "msg": "34383037313039383330", "sig": "2f521d9d83e1bff8d25255a9bdca90e15d78a8c9ea7885b884024a40de9a315bed7f746b5da4ce96b070208e9ae0cfa54185c6f4225b8c255a4d31abb5c9b6c686a6ee50a8eb7103aaef90245a4722fc8996f266f262109c3b5957ba73289a20", "result": "valid", "flags": []}, {"tcId": 83, "comment": "special case hash", "msg": "343932393339363930", "sig": "d4900f54c1bc841d38eb2f13e0bafbb12b5667393b07102db90639744f54d78960b344c8fbfbf3540b38d00278e177aa3a16eff0399700009b6949f3f506c543495bf8e0f3a34feb8edd63648747b531adc4e75398e4da8083b88b34c2fb97a8", "result": "valid", "flags": []}, {"tcId": 84, "comment": "special case hash", "msg": "32313132333535393630", "sig": "c0169e2b8b97eeb0650e27653f2e473b97a06e1e888b07c1018c730cabfdeeec4a626c3edee0767d44e8ed07080c2ac413f46475f955f9701928067e3982d4ba5a58a379a66f91b74fad9ac8aee30086be6f41c9c2d8fb80e0924dedbe67e968", "result": "valid", "flags": []}, {"tcId": 85, "comment": "special case hash", "msg": "31323339323735373034", "sig": "2e868871ea8b27a8a746882152051f2b146af4ac9d8473b4b6852f80a1d0c7cab57489aa43f89024388aec0605b026376d8c89eed8a5a6252c5cead1c55391c6743d881609e3db24d70ead80a663570020798fbf41d4c624fcb1ce36c536fe38", "result": "valid", "flags": []}, {"tcId": 86, "comment": "special case hash", "msg": "32303831313838373638", "sig": "abe6a51179ee87c957805ecad5ccebca30c6e3a3e6dbe4eb4d130b71df2bf590b9d67c8f49e81bf90ce0909d3c2dab4c7110582fab495b21bd9dda064fbd7acc09d0544dcf7699be35ad16207ffa10e8904f9241a709487ba2ba7e34430b81c3", "result": "valid", "flags": []}, {"tcId": 87, "comment": "special case hash", "msg": "343534363038393633", "sig": "50252c19e60e4120b7c28b2c2e0a588e5d107518cd61e5c7999c6d465ea134f752322d8b83f5988fcdc62bd9adb36ccd193899352491dabfe4fc942e14ddacb200673729d61602cc0baf5732d262f36e5279865a810ce2f977f57686a0d0137a", "result": "valid", "flags": []}, {"tcId": 88, "comment": "special case hash", "msg": "31333837363837313131", "sig": "eb725fdd539d7de8ea02fac8db6ec464f40c272a63e6b2718c4e0266bf1235dae330f747a6052f4319ecbe7bdade9bd0ae84507648ba2d1944bb67722ccd2cb94b92b59e89a1ae698c668bb57f481c42b216c23da4b1d8c0e502ef97fda05ad0", "result": "valid", "flags": []}, {"tcId": 89, "comment": "special case hash", "msg": "32303331333831383735", "sig": "25aa56fcbd92f2cf53bddbaa0db537de5843290731c1dd78036fcbded4a8f7187ddfed9f5ca9d98ea7b12d24b8d29d57028f68372d66164810bf79c30a191116d496fe32314605dc1668289425fb3a15d7532dde1052a49a35866c147abde1d9", "result": "valid", "flags": []}, {"tcId": 90, "comment": "special case hash", "msg": "323535333538333333", "sig": "54bf7adc8548e7cae270e7b097f16b5e315158d21b0e652ce1cfe4b33126ba4a65bf227b4cddcaf22d33d82478937b20bfc1b8f1d02846a42f31e1bd10ba334065459f712a3bbc76005d6c6488889f88c0983f4834d0bf2249dbf0a6db760701", "result": "valid", "flags": []}, {"tcId": 91, "comment": "special case hash", "msg": "34363138383431343732", "sig": "d3bb29ac0bd1f6058a5197f766d6ea3216c572ded62af46318c8c7f9547bb246553654279d69989d9af5ef4ccacf64dae10281122c2112a2a5a9d87ac58f64fb07c996a2d09292119e8f24d5499b2e8524ebd0570097f6cc7f9c26094a35c857", "result": "valid", "flags": []}, {"tcId": 92, "comment": "special case hash", "msg": "31303039323435383534", "sig": "bc32e85e3112472408f9324586e525325128a38313c34b79700cb0a3f7262a90a1fcc40eef1f1a3884032a7a21810e0ac02f52541360358107a13dbea31f83d80397710901734b7adb78b1fc904454a28a378514ccef80ecc70c1d8e55f11311", "result": "valid", "flags": []}, {"tcId": 93, "comment": "special case hash", "msg": "32373536343636353238", "sig": "f04b9e17c71d2d2133ea380d71b6b82c8a8e3332703e9d535b2c2bca9b0ad586d176a6049afa35edd9722edb5c33daa3bd44d4a6263380ca6f22e76c26d5f70f41f4d7cae7d4b9c1b8dc2ba5298d9d12408b04614e2f3796cc19c950c8c88a10", "result": "valid", "flags": []}, {"tcId": 94, "comment": "special case hash", "msg": "313139363937313032", "sig": "c8807351d8e261338e750cb9a52f4be4470b63f6f181cbe0e81d43b60824ba4be1bba42b1783897a0d72b0614018b02f52e3a598c8be982127e961eed2b04f21c86df4ebcab0d955a7c66ec7f818898798ee75367a85022276b912c0a072bff7", "result": "valid", "flags": []}, {"tcId": 95, "comment": "special case hash", "msg": "323333313432313732", "sig": "6152841b6fb460546eeb4158a3e5ffa54f51aa6a208987be899b706055cd59d8ec7c01f4634254fe050e1d4ec525a17373f0c5f13640d892c28f701428e8fbfb736b6478bbd972c8c684977556ed599a70d313e06b126080e13068d56e1c10be", "result": "valid", "flags": []}, {"tcId": 96, "comment": "special case hash", "msg": "31363733343831383938", "sig": "842f8d2814f5b7163f4b21bd9727246e078ad1e7435dfe1bc5f9e0e7374232e686b9b98b73deab9e43b3b7f25416c2be852c106c412300bac3ba265990b428a26076ab3f00fd7657bbd9315fa1cd2a1230a9a60d06b7af87aa0a6cf3f48b344c", "result": "valid", "flags": []}, {"tcId": 97, "comment": "special case hash", "msg": "31343630313539383237", "sig": "e13f6d638b9d4fba54aa436a945cfea66dec058fab6f026293265884457b5a86e8e927d699bc64431b71e3d41df200449832cd1b4177118ed247b4f31277da15f420179f45c71a237d77f599a45df68247bac3dcef0868ecd1665005c25b7c6c", "result": "valid", "flags": []}, {"tcId": 98, "comment": "special case hash", "msg": "38393930383539393239", "sig": "09fff1c2e4ff8643cbfad588620c2bf7aaca5cf4242969142c7145b927bd82ed14f3ae8c6e2ce2da63b990b9f1be6d64780c816f6c86343b008235ee986abf2136123ed247e4751e4d5467334f08e5e2ca1161254f68c3e6678e2d0b87d1cc7c", "result": "valid", "flags": []}, {"tcId": 99, "comment": "special case hash", "msg": "34333236343430393831", "sig": "ffae6e7d2cea71b5a9c73cbc1285a8d252949772afe1aa27fb137740fc429c2a8c8648c9a5ba678a32f7ae7689b395ca89d54cd13a162c34189ff524813690e79768af8ebe794cc941dfe7fdf2cb8dd0b42519f034ea4d4f1c870046d13210e1", "result": "valid", "flags": []}, {"tcId": 100, "comment": "special case hash", "msg": "32333736343337353537", "sig": "efa3c5fc3c8be1007475a2dbd46e3578bb30579445909c2445f850fb8aa60aa5b1749cc3400d8ffd81cb8832b50d27b4b36a08db3845b3d2ebd2c335480f12fb83f2a7351841ea3842ec62ad904b098efbf9faa7828b9c185746d9c8bd047d76", "result": "valid", "flags": []}, {"tcId": 101, "comment": "special case hash", "msg": "383630333937373230", "sig": "f577095f7c74594aa1c69aca9bb26e0c7475ae5163058ecc074b03af89e56b12b6a72450589dacf0d7e6b172d0017a0ebee756a0b5d0a677bf95f98da512854f3ecb712f94570e1ad230eab17c527b6a8bcc9ae202b657a3611ecffa94ba0d54", "result": "valid", "flags": []}, {"tcId": 102, "comment": "special case hash", "msg": "35383037373733393837", "sig": "0ae7688c7de5882eb9c3172f5500015552f998fb53702c6cd4b03404d5a0510a8073db95db544808dbd76659fd20cf12bc610fe5f04d8909cc439615fb7e302d3d82992817647c50c1f467090a52b328cbbc0262f18ffb6fd9f3bd60013cea08", "result": "valid", "flags": []}, {"tcId": 103, "comment": "special case hash", "msg": "353731383636383537", "sig": "5dc8a6d84afaaf900d78c6a91dc5e12e7d17891a52c1468253061d704b8940bef85b9fe807a0e02b56e8dd37c22fbb82914258de52932c4604dceb5ce7cc0a92e021edca9b819b84a9f25652f9af13f956a1139ee95c7aa7a079e3ad8317fbdb", "result": "valid", "flags": []}, {"tcId": 104, "comment": "special case hash", "msg": "38363737333039333632", "sig": "da55a6dbb845205c87c995b0bbc8444ffcba6eb1f4eb9d30f721d2dacc198fb1a8296075e68eb3d25ef596a952b8ea19829f671dccad6d7b0b8c4b39ff3f42597965d55c645fb880a66fe198d9344c9311f1598930392470379fa5ff43c75d04", "result": "valid", "flags": []}, {"tcId": 105, "comment": "special case hash", "msg": "32343735353135303630", "sig": "3730dfd0985de77decdd358a544b47f418d3fab42481530d5d514859894c6f23b729af72b44686058de29687b34b3b0c65bdfaf0ac217a80b82eb09c9f59c5c8cfbf50a6eb979a8f5f63eab9bd38ee0938e4b23102112033b230a14ad2790e3f", "result": "valid", "flags": []}, {"tcId": 106, "comment": "special case hash", "msg": "393733313736383734", "sig": "55210df2124c170e259af1dafa73e66613aa18ced8eb40a7f66155d50d5f3124edfa55276de4797013177291e8afeff6c314d3a310a60647dad3318ed7f0405a64c3f94b5ac98e6be12208c8ad9835fa6b81a0ea59f476608634657b66e00ffd", "result": "valid", "flags": []}, {"tcId": 107, "comment": "special case hash", "msg": "33363938303935313438", "sig": "f6c9897144b5d84964515eb0c8c3d0d9c6687c957887e93c29b2a21804b40307fb88bfd5cca11c95885d28867cb33a74656bafca242290f7d7e9801b6cfd4bd1b07e8d7c6c1c59fd3d8e82e9846a1b2855c85420e4ee6ec2d97fec2161eeb243", "result": "valid", "flags": []}, {"tcId": 108, "comment": "special case hash", "msg": "3130373530323638353736", "sig": "bfbcc5f343e2ab392ce6c1c02d91c00650c47136836a5d0622d476ac2b3274395721b1ab21882ed5cabed093b43b133f043e9fc64c6108df73f9eced90f91185f83d89662f5a9d810c1824fbfd97b842f784305fd6b9c28c80d32d52b1538d12", "result": "valid", "flags": []}, {"tcId": 109, "comment": "special case hash", "msg": "383639313439353538", "sig": "b8f793ddd47e657a9081cbed1600fb22b38ad6a155f9c006ba98de1f383b4c0918ceea72253e0f869524b2369cd9bd8c96c452ff58f42e0853040a6d5c7e750b57dd4af06e2df8194e8d524e81ac000ee3315bbeabbf6a21f61b8904c55378d9", "result": "valid", "flags": []}, {"tcId": 110, "comment": "special case hash", "msg": "32313734363535343335", "sig": "263ab1c93567e93b5ec4e380b0d3bb5ea1ce693c14a47afccc539aaf197f099d331ea9e26f1a0057148d46727acb6188621db07ce94110e2be74fa953a00a8a554225b3f2c0f6c56b4ebd4db2f57ca2565ed3323fd708bb56ac6e28bfb40f2e7", "result": "valid", "flags": []}, {"tcId": 111, "comment": "special case hash", "msg": "363434353530373932", "sig": "96f4a2b3529c65e45a0b4c19c582dc8db635d4e74f0b81309696b23be920ba8ec553d4b370df4c59d74dd654bac6df581573ba1b280c735a3401d957ecd3b8908e4e0b7d80239ce042594d182faf2ddf811c9056aac4c87f4f85043766a26614", "result": "valid", "flags": []}, {"tcId": 112, "comment": "special case hash", "msg": "353332383138333338", "sig": "96a691b19a6294b311a438f8da345e480b1deaa1e940cfbf02177d5f08479976ea58aee31011d50b5542be188c9d63df8f67dc9e1588aeb8be180013d41a036f9badfad9fe9340910cbf87243776f54bef7da2ebf3a7643866eb9a3b23fe59b9", "result": "valid", "flags": []}, {"tcId": 113, "comment": "special case hash", "msg": "31313932303736333832", "sig": "cff27948c6d902c73d103d0802eb144dd89c1b0e3b9f9a5e498b0361dc122a0d555160d8c64d61539c1dbbd4bc18971fb60827488c9f16ba28378fd59b1a29c65073335a7f236131134674c62c8396f193c76f2395ddaaa4f24b69161eb69b4d", "result": "valid", "flags": []}, {"tcId": 114, "comment": "special case hash", "msg": "31353332383432323230", "sig": "e90e22d9e535dfdfd86e098d5d6a0ae08f69d4a3ffaa39f6930bcf5f5ad02ee0d0472ae984edd9f0bbe5e7d63fd4f6ace3f57b0a4629ecaa21f2d34a7a0834d57ba20f99c6e31b43c37811cc23b9957c8f3356f4462214d3c8e58745e50f23f6", "result": "valid", "flags": []}, {"tcId": 115, "comment": "special case hash", "msg": "313536373137373339", "sig": "18b70e272a98cc48e1e0af73146f0f972bbfbeb6b985feb2c4acd695a7a41b99c415be9c46aedaf3ddff67a65a89e38747d6bcea088f622ad35d88bcf46d71827bcba2f57c36d6fb8a4bf2befdc0d4e3ef366d5966c4d076d3cfa43d6626717b", "result": "valid", "flags": []}, {"tcId": 116, "comment": "special case hash", "msg": "34333033303931313230", "sig": "acfd981c55fd5286cfce173726d51c3d25f65b11b7673729a62167256774f7c894b74662a212c706e00cef096074162ff4d471c97797c24d96aec1de85a249ef468d6036cd712563aeb65cea4995f3ee85e769b874f09a08637a44a96084be7a", "result": "valid", "flags": []}, {"tcId": 117, "comment": "special case hash", "msg": "37373335393135353831", "sig": "f15fcbeea8b64dad5e8566a2c37913c82d6be9d9668df469bd0b591c3923a6e12644eaf697d466fa7cd513983d946a4070063966801079351526999e5c5c2c5f627e4c8bc96784bcbe715fe7c7afcf69785d1c8c7ccd3725e364101638396597", "result": "valid", "flags": []}, {"tcId": 118, "comment": "special case hash", "msg": "323433393636373430", "sig": "d995147939ae6d8f62bb57372227395839e25a0d4308b899d5f506cf9e0a01e8115b7e4b822f037ec95752bd9e892f5e9bb4d07333e468f8482a790a2a2e650e2c42da8240ec5e402506b368122f046680cd71e0117897cce3df4a1555fc8876", "result": "valid", "flags": []}, {"tcId": 119, "comment": "special case hash", "msg": "34333237363032383233", "sig": "43c6ce5184476f3f496afeae3cb96a3f9f038957686c93437b8266a233022371d266e904aa096c3566cb33824b88075e680c13245a8bc560b638d26f0c5f261964130256939552d3fffb07b658355611612c268a89541055d3c2bf9e82cf4da3", "result": "valid", "flags": []}, {"tcId": 120, "comment": "special case hash", "msg": "32393332303032353932", "sig": "447539941dc350767fc841083d25d9247a0807e1e22e0bb9d94f504f721981b413d521efbd75e4fe831ee26338cf3de300395ab27ea782cee4be53e06c7616bbd41d6926b18d219d75d5979f13cba2f52101019b0ec0a41ffdbf29ef73ddba70", "result": "valid", "flags": []}, {"tcId": 121, "comment": "special case hash", "msg": "36343039383737323834", "sig": "a0ba8e8b979c20345e34fca98531900164a859923bd6986a9c39236a2f5de053a252997f35e5b84b0d48ba0f8d09aeddfacd6df04358fcd95fa9018a6fc0828dfe319812ff65929c060b18ad4b9f06e7fc0addd1b695315d71c15e51dc51d719", "result": "valid", "flags": []}, {"tcId": 122, "comment": "special case hash", "msg": "36303735363930343132", "sig": "b8378390f71f0bb6663f1846daf6908f8c84f770ae740cc8054122494cf0ffa9437ab26040ca22808fb29a810b70126e427636b929a500abc34d9f22977b81e734919afaf3ed2c91eeada7074e0c16bdc52f960eaec9db5a879c1e6414035101", "result": "valid", "flags": []}, {"tcId": 123, "comment": "special case hash", "msg": "32333231363233313335", "sig": "f36a9048fd94803d3d6d1b11430b90b94ef8d5d2ad89018c69473ce9cfe0d6105b3c2fb2e7555ccd25f65af8c872bdc681254841e7ecbfd0d810afaaf5afd6d6c5d0542bb00cc183b1db01767120afbcc0006ddcba8db7baf65f302723dabc4d", "result": "valid", "flags": []}, {"tcId": 124, "comment": "special case hash", "msg": "36343130313532313731", "sig": "d8a4d96409c191baa9540bf35f1d5192f9352d7f0e14f92c0e8e1f19f559b42ed3c6b7bdb6becc56584fb5c09421e2e4d966ba13d4245e248eafb46f2a3df92c2037d5969c7db6dbcb0ff4b21850e16a18a29785267239886365cf721a212536", "result": "valid", "flags": []}, {"tcId": 125, "comment": "special case hash", "msg": "383337323835373438", "sig": "1d5d86fd48e65b0cf0b0b46062241f89cf65785dd818f93f1162771a38a15f20febc261812ecaaf6f4f2b86b3362d7eb0c76e363de1432513cb9dad6493931381ecd25f142e61968b6f20d7b1270cb9e38a7ae54e4778aff4025eb00c6a67aef", "result": "valid", "flags": []}, {"tcId": 126, "comment": "special case hash", "msg": "33333234373034353235", "sig": "0508eed148f061114be18e8a86188feabf76b873b36eadcca9c2c60e24a2002fe456231decf7a8f6f032c08dbe0ab5a9694c0ad781b2341e30e1d0739ac99672064f48821a69852c7940cf1d621738199c980d56d2a0b71b3fc6011c6b2444ba", "result": "valid", "flags": []}, {"tcId": 127, "comment": "special case hash", "msg": "31343033393636383732", "sig": "726ef88bb7947a043116c111cb519ddeda3e6ffbf724884a1b22c24409cdf2779d93ce610c8c07411c2b001399103d6d95dc1d65046caf0e8dad07b224798d6f7807278e737883e7c7bf0b446791d4ee144c26f710134861af4e6771d4082896", "result": "valid", "flags": []}, {"tcId": 128, "comment": "special case hash", "msg": "31323237363035313238", "sig": "eb0e8e3c639f5eba8eccd9020d0ec62d8ac73f3fddbdfa08fdb2155deb0a536923ebd55e20020cab9f8e39a43a88be11c796df399fc35883dd5dae6817d02d3d67a8eec6601585e5e36fd2c134eddb1447ec12b144dddc9aae28a84f22602641", "result": "valid", "flags": []}, {"tcId": 129, "comment": "special case hash", "msg": "34393531343838333632", "sig": "e8f8c69d0396ea900f9757736d2b19dbc2d2a8c01dccf490c8b9455bd63b34c095867e7cf3b84dc7c3c3d6b51bebf40558152a7564eeb22a3e26597026d0cd7835725bd512245448cb5016eb48ea759809fd6949d0ee5d579643f72f908c16bb", "result": "valid", "flags": []}, {"tcId": 130, "comment": "special case hash", "msg": "32343532313237303139", "sig": "380b4e48b3ff012af7c08bf871d9f4da0c708b5494a986d3d80b1979e579d0dbee61db9bc3c04c396176410788e15a0fe6971c013c965a7e4df10f95620a5092fab096bd5b50828f4bc91c5e479bccf6e0daf287e7ef580fa9ea153fa1a507a2", "result": "valid", "flags": []}, {"tcId": 131, "comment": "special case hash", "msg": "31373331353530373036", "sig": "8061de12029e2b000d157a455ecf2301222f092df95b9551b78cf0ef3a64f12212b57ec7b16d2c0f258946f51cb1633a0ac2ca6ad99b29ca29a0dc38b34443ee41020f81ed9087cef7681a00c4fe60653a572944ba37f1fe51d112bfffbdd701", "result": "valid", "flags": []}, {"tcId": 132, "comment": "special case hash", "msg": "31363637303639383738", "sig": "e74f2a791eeb7341cff6cc1c24f459e6c0109924f7984639ae387e3ceb58758a1bc3839dea1fc3a3799562225e70a733d90e4d0f47343268e56bbcb011bd4734390abc9aa1304b6253e78f5a78b6905aa6bf6a3892a4ae1a875c823ae5a83e87", "result": "valid", "flags": []}, {"tcId": 133, "comment": "special case hash", "msg": "343431353437363137", "sig": "6a1cd0ff7906be207b56862edcbc0d0bbfb26d43255c99f6ab77639f5e6103a07aa322b22ed43870d1ce6df68aa0a8c1655558b129aa23184500bd4aab4f0355d3192e9b8860f60b05a1c29261f4486a6ae235a526339b86c05f5fac477b6723", "result": "valid", "flags": []}, {"tcId": 134, "comment": "special case hash", "msg": "343233393434393938", "sig": "81111fdc5f0de65583c7a5668d26c04ee52e08dac227753132cff1741cb721e112aa793c0d5fa047faf14cb45dd13e1f9a25cf1e6c152bc3e216e021561d194979f1c11fe17019ed7bac2c13c4010f209665e3b6f33b86641704d922b407818f", "result": "valid", "flags": []}, {"tcId": 135, "comment": "special case hash", "msg": "34383037363230373132", "sig": "9b66d122a315095b2b66ccb97272c476a2d760e827fdea05732d634df3d066569c984dd941aad5f5dec4c2e1b7b94a0096c32403c85bc3d0ee87f96a600182796dce53d54d7467ae660a42b87bb70792f14650ac28a5fa47ce9ca4d3b2c25878", "result": "valid", "flags": []}, {"tcId": 136, "comment": "special case hash", "msg": "32313634363636323839", "sig": "2bb062a002088d62a0b7338d0484fedfe2af7e20cebf6a4788264eb27cb4ebc3cc81c816e6a35722cf9b464783094cb846cc21b70f2133f85ab0443bebe9c6fc62c6e2ec1fd9c4ddf4a6d5f3f48eb7abf1ee7bdf6725879fd1b7daafb44f6e04", "result": "valid", "flags": []}, {"tcId": 137, "comment": "special case hash", "msg": "31393432383533383635", "sig": "33e87061ee9a82eb74d8bb4ae91606563c2e4db8b09183cc00d1119ab4f5033d287a1fc90a2348163fdf68d35006fd7f96db97c947ee2e96e6139d3bcbf5a43606bae1ad3ca28290fbad43b281ef115ec1b98bc581ef48094f8c1aa8e36c282a", "result": "valid", "flags": []}, {"tcId": 138, "comment": "special case hash", "msg": "32323139333833353231", "sig": "70f80b438424ba228a7d80f26e22ff6a896243c9d49c75573489ee0de58ec60efd103838143465bd8fe34672ba949617115492bd9365b96f38747536318bffb819e7c146df3a5a7a46d6288c7fdf31cff570b22176aa398daba9073ab1e7b9bf", "result": "valid", "flags": []}, {"tcId": 139, "comment": "special case hash", "msg": "393236393333343139", "sig": "ff16ca0389ea6948f4305b434fe0aa589f880f5aa937767c31170ee8da6c1ad620c993d40ddf141b7fda37424d51b5cdba0f86985dffc61d6e35a37de06918b11e431b72403161acfb8f05c469f1fcfa6e215c6f7eb5a0a5e0cc9e7be79ce18b", "result": "valid", "flags": []}, {"tcId": 140, "comment": "special case hash", "msg": "373639333836333634", "sig": "d60c24bee05f5198cd155ad095ffb956bbcfb66b82fc0d3755119915a62f2f923557b85ddc1d12e6a757f23042cb601b2c4d968b5eac930b51d283b418fcff6df3a9d6d66e3812cd1bf5fde797fd203a7c439b1b381e4fe8b44e6f108764a7dd", "result": "valid", "flags": []}, {"tcId": 141, "comment": "special case hash", "msg": "32373335393330353733", "sig": "bdf634d915a4fae7a155532ca2847c33a6babe7ef8db0af50f485db3dd2c8bffe722394583932f6eb5cd97f6db7561d9bb425cae2e5483174b5ed873af4329da4618c14458141850bee3c7bf1ffb3f2030159043277dacc708e9d32f63400083", "result": "valid", "flags": []}, {"tcId": 142, "comment": "special case hash", "msg": "38333030353634303635", "sig": "061320a3bcebac33cf399d45d1e1e1b34f37288fe4753f4fddfd496eff427e1d26b1b91d749cc34c12f4ecef837c0e8ffd5cf468cda319fe06e773a190c38de6e150a321ac1c416ad875432cdb7a07134c446f13068e71a1a96e35da923974ad", "result": "valid", "flags": []}, {"tcId": 143, "comment": "special case hash", "msg": "34333037363535373338", "sig": "d620f063d33efa859b623f6c9a92340e4cdd854ffbe3e5e01379177aee31715ce587b00bd0aea98fddf236d2fc8a7a74671f4b7c187297dc236c61888b6d9397e97783077cc4101807d79ee62e4a53a78c4b6a3a31b03178668af894a3d8902e", "result": "valid", "flags": []}, {"tcId": 144, "comment": "special case hash", "msg": "39363537303138313735", "sig": "91c556c5bddd529fe903b86afc0eb8fa1f49425b779a39114ae563bebc947e633ba4ee98948faa8940dfe2562c63e1c5198b00079d8db072d25b0a49bc8bc36457926f3c101527528df6679f92c76f1b487e6695d4b92fe33b4ee7046a6a5df9", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "S_TlL5WEJ-u1kV-4yVlVUbTTo_2rZ7rdnWwwk_QlukNjDfcfQvDrfOqpTZ9kSKhd", "y": "0wMxWIJJ_S_cCzCex-2EgbwW8ngAwT19twD8guGxyFRaoMDTtW47_nifwYqRaIfC"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "044bf4e52f958427ebb5915fb8c9595551b4d3a3fdab67badd9d6c3093f425ba43630df71f42f0eb7ceaa94d9f6448a85dd30331588249fd2fdc0b309ec7ed8481bc16f27800c13d7db700fc82e1b1c8545aa0c0d3b56e3bfe789fc18a916887c2", "wx": "4bf4e52f958427ebb5915fb8c9595551b4d3a3fdab67badd9d6c3093f425ba43630df71f42f0eb7ceaa94d9f6448a85d", "wy": "00d30331588249fd2fdc0b309ec7ed8481bc16f27800c13d7db700fc82e1b1c8545aa0c0d3b56e3bfe789fc18a916887c2"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200044bf4e52f958427ebb5915fb8c9595551b4d3a3fdab67badd9d6c3093f425ba43630df71f42f0eb7ceaa94d9f6448a85dd30331588249fd2fdc0b309ec7ed8481bc16f27800c13d7db700fc82e1b1c8545aa0c0d3b56e3bfe789fc18a916887c2", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAES/TlL5WEJ+u1kV+4yVlVUbTTo/2rZ7rd\nnWwwk/QlukNjDfcfQvDrfOqpTZ9kSKhd0wMxWIJJ/S/cCzCex+2EgbwW8ngAwT19\ntwD8guGxyFRaoMDTtW47/nifwYqRaIfC\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 145, "comment": "k*G has a large x-coordinate", "msg": "************", "sig": "000000000000000000000000000000000000000000000000389cb27e0bc8d21fa7e5f24cb74f58851313e696333ad68bffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52970", "result": "valid", "flags": []}, {"tcId": 146, "comment": "r too large", "msg": "************", "sig": "fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffeffffffff0000000000000000fffffffeffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52970", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "NiO7KWuI9ibQ-SZWvwFvEVtyEnfMtJMHOb-9gfnB5zRjDgaF0y4VTgtKXGLkOFH2", "y": "doNWtKV2TBKMexEF49d4qJ0eAdopft4bxDEsJYPgu93SFhNYPdCauJXGO-R5-UV2"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "043623bb296b88f626d0f92656bf016f115b721277ccb4930739bfbd81f9c1e734630e0685d32e154e0b4a5c62e43851f6768356b4a5764c128c7b1105e3d778a89d1e01da297ede1bc4312c2583e0bbddd21613583dd09ab895c63be479f94576", "wx": "3623bb296b88f626d0f92656bf016f115b721277ccb4930739bfbd81f9c1e734630e0685d32e154e0b4a5c62e43851f6", "wy": "768356b4a5764c128c7b1105e3d778a89d1e01da297ede1bc4312c2583e0bbddd21613583dd09ab895c63be479f94576"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200043623bb296b88f626d0f92656bf016f115b721277ccb4930739bfbd81f9c1e734630e0685d32e154e0b4a5c62e43851f6768356b4a5764c128c7b1105e3d778a89d1e01da297ede1bc4312c2583e0bbddd21613583dd09ab895c63be479f94576", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAENiO7KWuI9ibQ+SZWvwFvEVtyEnfMtJMH\nOb+9gfnB5zRjDgaF0y4VTgtKXGLkOFH2doNWtKV2TBKMexEF49d4qJ0eAdopft4b\nxDEsJYPgu93SFhNYPdCauJXGO+R5+UV2\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 147, "comment": "r,s are large", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52972ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52971", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "1RbLisjkRXtpPVGSvuts59mka-9I7s8-qCMobxAfmNEw9aJtxv7CNmLv8H8USG_V", "y": "hFaTLnSJS38OO7Df02JQKzdl3YCjF3IJ-yIdybUar0RwskU5FAW-9RQXaxOiZ6cg"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04d516cb8ac8e4457b693d5192beeb6ce7d9a46bef48eecf3ea823286f101f98d130f5a26dc6fec23662eff07f14486fd58456932e74894b7f0e3bb0dfd362502b3765dd80a3177209fb221dc9b51aaf4470b245391405bef514176b13a267a720", "wx": "00d516cb8ac8e4457b693d5192beeb6ce7d9a46bef48eecf3ea823286f101f98d130f5a26dc6fec23662eff07f14486fd5", "wy": "008456932e74894b7f0e3bb0dfd362502b3765dd80a3177209fb221dc9b51aaf4470b245391405bef514176b13a267a720"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004d516cb8ac8e4457b693d5192beeb6ce7d9a46bef48eecf3ea823286f101f98d130f5a26dc6fec23662eff07f14486fd58456932e74894b7f0e3bb0dfd362502b3765dd80a3177209fb221dc9b51aaf4470b245391405bef514176b13a267a720", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE1RbLisjkRXtpPVGSvuts59mka+9I7s8+\nqCMobxAfmNEw9aJtxv7CNmLv8H8USG/VhFaTLnSJS38OO7Df02JQKzdl3YCjF3IJ\n+yIdybUar0RwskU5FAW+9RQXaxOiZ6cg\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 148, "comment": "r and s^-1 have a large Hamming weight", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdd1aee55fdc2a716ba2fabcb57020b72e539bf05c7902f98e105bf83d4cc10c2a159a3cf7e01d749d2205f4da6bd8fcf1", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "qDgM01Am4Tv4e-aTzbbnWoLXZbQBm1KejSd8SvbJ2yfrtdP4boit2dW2EYbwTIOp", "y": "kqGHUHxzcyXSzGJKzvPNA2v6meDBUYvmXIi7UfkA-UEjrKutgdFRMNOt5_9-Q2Th"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04a8380cd35026e13bf87be693cdb6e75a82d765b4019b529e8d277c4af6c9db27ebb5d3f86e88add9d5b61186f04c83a992a187507c737325d2cc624acef3cd036bfa99e0c1518be65c88bb51f900f94123acabad81d15130d3ade7ff7e4364e1", "wx": "00a8380cd35026e13bf87be693cdb6e75a82d765b4019b529e8d277c4af6c9db27ebb5d3f86e88add9d5b61186f04c83a9", "wy": "0092a187507c737325d2cc624acef3cd036bfa99e0c1518be65c88bb51f900f94123acabad81d15130d3ade7ff7e4364e1"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004a8380cd35026e13bf87be693cdb6e75a82d765b4019b529e8d277c4af6c9db27ebb5d3f86e88add9d5b61186f04c83a992a187507c737325d2cc624acef3cd036bfa99e0c1518be65c88bb51f900f94123acabad81d15130d3ade7ff7e4364e1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEqDgM01Am4Tv4e+aTzbbnWoLXZbQBm1Ke\njSd8SvbJ2yfrtdP4boit2dW2EYbwTIOpkqGHUHxzcyXSzGJKzvPNA2v6meDBUYvm\nXIi7UfkA+UEjrKutgdFRMNOt5/9+Q2Th\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 149, "comment": "r and s^-1 have a large Hamming weight", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdb6b681dc484f4f020fd3f7e626d88edc6ded1b382ef3e143d60887b51394260832d4d8f2ef70458f9fa90e38c2e19e4f", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "VU8v0LcAqfRWh1K2c9nA0p3JbBD-Z-OMbW0zm_r-Bflw2ow9IWToIDEwekS9MiUR", "y": "cTErYbWRE_8L07ippJNN8mKqgJb4QOnYv_pddJHe2Hs4xJb5ueTwuhCJ-NP_yIqf"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04554f2fd0b700a9f4568752b673d9c0d29dc96c10fe67e38c6d6d339bfafe05f970da8c3d2164e82031307a44bd32251171312b61b59113ff0bd3b8a9a4934df262aa8096f840e9d8bffa5d7491ded87b38c496f9b9e4f0ba1089f8d3ffc88a9f", "wx": "554f2fd0b700a9f4568752b673d9c0d29dc96c10fe67e38c6d6d339bfafe05f970da8c3d2164e82031307a44bd322511", "wy": "71312b61b59113ff0bd3b8a9a4934df262aa8096f840e9d8bffa5d7491ded87b38c496f9b9e4f0ba1089f8d3ffc88a9f"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004554f2fd0b700a9f4568752b673d9c0d29dc96c10fe67e38c6d6d339bfafe05f970da8c3d2164e82031307a44bd32251171312b61b59113ff0bd3b8a9a4934df262aa8096f840e9d8bffa5d7491ded87b38c496f9b9e4f0ba1089f8d3ffc88a9f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEVU8v0LcAqfRWh1K2c9nA0p3JbBD+Z+OM\nbW0zm/r+Bflw2ow9IWToIDEwekS9MiURcTErYbWRE/8L07ippJNN8mKqgJb4QOnY\nv/pddJHe2Hs4xJb5ueTwuhCJ+NP/yIqf\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 150, "comment": "small r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "result": "valid", "flags": []}, {"tcId": 151, "comment": "incorrect size of signature", "msg": "************", "sig": "0201", "result": "acceptable", "flags": ["SigSize"]}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "RO4zNfp30vsC5L1wdPReWYqHnA-oIuxxjCHcE7g0QO3E48EKGFhCPgMETJ7_Ilkc", "y": "0CfEmTPlUQVX1rSyxvZv5dy5MCo7E_3GgEjD_KyIuhUrapgzyH_cYoCvxdEat8EH"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0444ee3335fa77d2fb02e4bd7074f45e598a879c0fa822ec718c21dc13b83440edc4e3c10a1858423e03044c9eff22591cd027c49933e5510557d6b4b2c6f66fe5dcb9302a3b13fdc68048c3fcac88ba152b6a9833c87fdc6280afc5d11ab7c107", "wx": "44ee3335fa77d2fb02e4bd7074f45e598a879c0fa822ec718c21dc13b83440edc4e3c10a1858423e03044c9eff22591c", "wy": "00d027c49933e5510557d6b4b2c6f66fe5dcb9302a3b13fdc68048c3fcac88ba152b6a9833c87fdc6280afc5d11ab7c107"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000444ee3335fa77d2fb02e4bd7074f45e598a879c0fa822ec718c21dc13b83440edc4e3c10a1858423e03044c9eff22591cd027c49933e5510557d6b4b2c6f66fe5dcb9302a3b13fdc68048c3fcac88ba152b6a9833c87fdc6280afc5d11ab7c107", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAERO4zNfp30vsC5L1wdPReWYqHnA+oIuxx\njCHcE7g0QO3E48EKGFhCPgMETJ7/Ilkc0CfEmTPlUQVX1rSyxvZv5dy5MCo7E/3G\ngEjD/KyIuhUrapgzyH/cYoCvxdEat8EH\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 152, "comment": "small r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002", "result": "valid", "flags": []}, {"tcId": 153, "comment": "incorrect size of signature", "msg": "************", "sig": "0202", "result": "acceptable", "flags": ["SigSize"]}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "4vh_cuPGbHMDf-d2B9Qq0tnEzBWYk7S5uLA2XTp3ZtvoZ4sC4raPWOWk92gQYaOQ", "y": "448hQoGFQr72srw6LE9DyV5SWda9VAFTE3jHyhJaH2zGCdT638XJqZNY7nf_eAyN"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04e2f87f72e3c66c73037fe77607d42ad2d9c4cc159893b4b9b8b0365d3a7766dbe8678b02e2b68f58e5a4f7681061a390e38f2142818542bef6b2bc3a2c4f43c95e5259d6bd5401531378c7ca125a1f6cc609d4fadfc5c9a99358ee77ff780c8d", "wx": "00e2f87f72e3c66c73037fe77607d42ad2d9c4cc159893b4b9b8b0365d3a7766dbe8678b02e2b68f58e5a4f7681061a390", "wy": "00e38f2142818542bef6b2bc3a2c4f43c95e5259d6bd5401531378c7ca125a1f6cc609d4fadfc5c9a99358ee77ff780c8d"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004e2f87f72e3c66c73037fe77607d42ad2d9c4cc159893b4b9b8b0365d3a7766dbe8678b02e2b68f58e5a4f7681061a390e38f2142818542bef6b2bc3a2c4f43c95e5259d6bd5401531378c7ca125a1f6cc609d4fadfc5c9a99358ee77ff780c8d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE4vh/cuPGbHMDf+d2B9Qq0tnEzBWYk7S5\nuLA2XTp3ZtvoZ4sC4raPWOWk92gQYaOQ448hQoGFQr72srw6LE9DyV5SWda9VAFT\nE3jHyhJaH2zGCdT638XJqZNY7nf/eAyN\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 154, "comment": "small r and s", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003", "result": "valid", "flags": []}, {"tcId": 155, "comment": "incorrect size of signature", "msg": "************", "sig": "0203", "result": "acceptable", "flags": ["SigSize"]}, {"tcId": 156, "comment": "r is larger than n", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc52975000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000003", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "BeZ8RPwMvJqOs0O01vWWx9AMrF2oWUyvRbcgk5dJYhTELYVqAVzlibybqGWk-rWr", "y": "iKAce10J76-Hj8uRAvs4dag4GvI00cWTB25FIiWlb1FnTzRxJtMAm0TcuwA6ZNlf"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0405e67c44fc0cbc9a8eb343b4d6f596c7d00cac5da8594caf45b7209397496214c42d856a015ce589bc9ba865a4fab5ab88a01c7b5d09efaf878fcb9102fb3875a8381af234d1c593076e452225a56f51674f347126d3009b44dcbb003a64d95f", "wx": "05e67c44fc0cbc9a8eb343b4d6f596c7d00cac5da8594caf45b7209397496214c42d856a015ce589bc9ba865a4fab5ab", "wy": "0088a01c7b5d09efaf878fcb9102fb3875a8381af234d1c593076e452225a56f51674f347126d3009b44dcbb003a64d95f"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000405e67c44fc0cbc9a8eb343b4d6f596c7d00cac5da8594caf45b7209397496214c42d856a015ce589bc9ba865a4fab5ab88a01c7b5d09efaf878fcb9102fb3875a8381af234d1c593076e452225a56f51674f347126d3009b44dcbb003a64d95f", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEBeZ8RPwMvJqOs0O01vWWx9AMrF2oWUyv\nRbcgk5dJYhTELYVqAVzlibybqGWk+rWriKAce10J76+Hj8uRAvs4dag4GvI00cWT\nB25FIiWlb1FnTzRxJtMAm0TcuwA6ZNlf\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 157, "comment": "s is larger than n", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000002ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accd7fffa", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "C7A_zjwB688Ic6vRNKhoL1-42_-iLaZ0BH5cPnHkPeWC7Wq7kIwuT6pdlhhieLbB", "y": "ujsiEj5ozMVvF915_xVWVwb3GgthI8d6882I8K8CTMUll4FRbtyvX-mQZG57Zpmd"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "040bb03fce3c01ebcf0873abd134a8682f5fb8dbffa22da674047e5c3e71e43de582ed6abb908c2e4faa5d96186278b6c1ba3b22123e68ccc56f17dd79ff15565706f71a0b6123c77af3cd88f0af024cc5259781516edcaf5fe990646e7b66999d", "wx": "0bb03fce3c01ebcf0873abd134a8682f5fb8dbffa22da674047e5c3e71e43de582ed6abb908c2e4faa5d96186278b6c1", "wy": "00ba3b22123e68ccc56f17dd79ff15565706f71a0b6123c77af3cd88f0af024cc5259781516edcaf5fe990646e7b66999d"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200040bb03fce3c01ebcf0873abd134a8682f5fb8dbffa22da674047e5c3e71e43de582ed6abb908c2e4faa5d96186278b6c1ba3b22123e68ccc56f17dd79ff15565706f71a0b6123c77af3cd88f0af024cc5259781516edcaf5fe990646e7b66999d", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEC7A/zjwB688Ic6vRNKhoL1+42/+iLaZ0\nBH5cPnHkPeWC7Wq7kIwuT6pdlhhieLbBujsiEj5ozMVvF915/xVWVwb3GgthI8d6\n882I8K8CTMUll4FRbtyvX+mQZG57Zpmd\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 158, "comment": "small r and s^-1", "msg": "************", "sig": "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000100489122448912244891224489122448912244891224489122347ce79bc437f4d071aaa92c7d6c882ae8734dc18cb0d553", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "WPJGCQ1eSYY7wL8tUB_3L1UcXxxeZ560kGT9AuIhonBzJuwtFAvMgXr6rVBldhVm", "y": "SXyCP9c2iCy_ePuSsaVYm2foBnSXxxCky7Od7ixUMbxFz7lsn4RUOFyfKz7y09Ma"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0458f246090d5e49863bc0bf2d501ff72f551c5f1c5e679eb49064fd02e221a2707326ec2d140bcc817afaad5065761566497c823fd736882cbf78fb92b1a5589b67e8067497c710a4cbb39dee2c5431bc45cfb96c9f8454385c9f2b3ef2d3d31a", "wx": "58f246090d5e49863bc0bf2d501ff72f551c5f1c5e679eb49064fd02e221a2707326ec2d140bcc817afaad5065761566", "wy": "497c823fd736882cbf78fb92b1a5589b67e8067497c710a4cbb39dee2c5431bc45cfb96c9f8454385c9f2b3ef2d3d31a"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000458f246090d5e49863bc0bf2d501ff72f551c5f1c5e679eb49064fd02e221a2707326ec2d140bcc817afaad5065761566497c823fd736882cbf78fb92b1a5589b67e8067497c710a4cbb39dee2c5431bc45cfb96c9f8454385c9f2b3ef2d3d31a", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEWPJGCQ1eSYY7wL8tUB/3L1UcXxxeZ560\nkGT9AuIhonBzJuwtFAvMgXr6rVBldhVmSXyCP9c2iCy/ePuSsaVYm2foBnSXxxCk\ny7Od7ixUMbxFz7lsn4RUOFyfKz7y09Ma\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 159, "comment": "smallish r and s^-1", "msg": "************", "sig": "00000000000000000000000000000000000000000000000000000000000000000000000000000000002d9b4d347952cdce751512561b6f57c75342848a3ff98ccf9c3f0219b6b68d00449e6c971a85d2e2ce73554b59219d54d2083b46327351", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "_GmE3Wgw0UhfslgaRaeR2NyixyfHPT1EyJ8AgsGGivXKdLTKSuIoAmQKnr_ox64S", "y": "mY1jpbWtG3K4mfCxMuSVKqoZ1B_e6kix7WuDWN0dsgf9ZuAUU61A9nuDatyALV_o"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04fc6984dd6830d1485fb2581a45a791d8dca2c727c73d3d44c89f0082c1868af5ca74b4ca4ae22802640a9ebfe8c7ae12998d63a5b5ad1b72b899f0b132e4952aaa19d41fdeea48b1ed6b8358dd1db207fd66e01453ad40f67b836adc802d5fe8", "wx": "00fc6984dd6830d1485fb2581a45a791d8dca2c727c73d3d44c89f0082c1868af5ca74b4ca4ae22802640a9ebfe8c7ae12", "wy": "00998d63a5b5ad1b72b899f0b132e4952aaa19d41fdeea48b1ed6b8358dd1db207fd66e01453ad40f67b836adc802d5fe8"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004fc6984dd6830d1485fb2581a45a791d8dca2c727c73d3d44c89f0082c1868af5ca74b4ca4ae22802640a9ebfe8c7ae12998d63a5b5ad1b72b899f0b132e4952aaa19d41fdeea48b1ed6b8358dd1db207fd66e01453ad40f67b836adc802d5fe8", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE/GmE3Wgw0UhfslgaRaeR2NyixyfHPT1E\nyJ8AgsGGivXKdLTKSuIoAmQKnr/ox64SmY1jpbWtG3K4mfCxMuSVKqoZ1B/e6kix\n7WuDWN0dsgf9ZuAUU61A9nuDatyALV/o\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 160, "comment": "100-bit r and small s^-1", "msg": "************", "sig": "00000000000000000000000000000000000000000000000000000000000000000000001033e67e37b32b445580bf4efb2ad52ad52ad52ad52ad52ad52ad52ad52ad52ad52ad52ad5215c51b320e460542f9cc38968ccdf4263684004eb79a452", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "G43vWSIwPWR-jrB-O62S-SS3m3ae7xaOdUHeH04NKK6XM-uYz4oftt1SygLIx1tR", "y": "x6pL9nnUnYEUEiB02o9gRKQnNxeWpWVKYQYWLV9oartz69iWqwjHBiaH8SFx--Sj"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041b8def5922303d647e8eb07e3bad92f924b79b769eef168e7541de1f4e0d28ae9733eb98cf8a1fb6dd52ca02c8c75b51c7aa4bf679d49d8114122074da8f6044a427371796a5654a6106162d5f686abb73ebd896ab08c7062687f12171fbe4a3", "wx": "1b8def5922303d647e8eb07e3bad92f924b79b769eef168e7541de1f4e0d28ae9733eb98cf8a1fb6dd52ca02c8c75b51", "wy": "00c7aa4bf679d49d8114122074da8f6044a427371796a5654a6106162d5f686abb73ebd896ab08c7062687f12171fbe4a3"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200041b8def5922303d647e8eb07e3bad92f924b79b769eef168e7541de1f4e0d28ae9733eb98cf8a1fb6dd52ca02c8c75b51c7aa4bf679d49d8114122074da8f6044a427371796a5654a6106162d5f686abb73ebd896ab08c7062687f12171fbe4a3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEG43vWSIwPWR+jrB+O62S+SS3m3ae7xaO\ndUHeH04NKK6XM+uYz4oftt1SygLIx1tRx6pL9nnUnYEUEiB02o9gRKQnNxeWpWVK\nYQYWLV9oartz69iWqwjHBiaH8SFx++Sj\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 161, "comment": "small r and 100 bit s^-1", "msg": "************", "sig": "00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000010077a172dfe37a2c53f0b92ab60f0a8f085f49dbfd930719d6f9e587ea68ae57cb49cd35a88cf8c6acec02f057a3807a5b", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "FzSgOaiKFsL_Sql9I5kSH1b1LvAe1eUIh_c29ltuUdboeGq7TgY9pdG6gS3_mYQD", "y": "zNaY5sKW1c1pF4-Kgkgahl2jMWJ_HEsyT7wCs26LXtWKMfco6QTSA6OIdVMCGVdl"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041734a039a88a16c2ff4aa97d2399121f56f52ef01ed5e50887f736f65b6e51d6e8786abb4e063da5d1ba812dff998403ccd698e6c296d5cd69178f8a82481a865da331627f1c4b324fbc02b36e8b5ed58a31f728e904d203a388755302195765", "wx": "1734a039a88a16c2ff4aa97d2399121f56f52ef01ed5e50887f736f65b6e51d6e8786abb4e063da5d1ba812dff998403", "wy": "00ccd698e6c296d5cd69178f8a82481a865da331627f1c4b324fbc02b36e8b5ed58a31f728e904d203a388755302195765"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200041734a039a88a16c2ff4aa97d2399121f56f52ef01ed5e50887f736f65b6e51d6e8786abb4e063da5d1ba812dff998403ccd698e6c296d5cd69178f8a82481a865da331627f1c4b324fbc02b36e8b5ed58a31f728e904d203a388755302195765", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEFzSgOaiKFsL/Sql9I5kSH1b1LvAe1eUI\nh/c29ltuUdboeGq7TgY9pdG6gS3/mYQDzNaY5sKW1c1pF4+Kgkgahl2jMWJ/HEsy\nT7wCs26LXtWKMfco6QTSA6OIdVMCGVdl\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 162, "comment": "100-bit r and s^-1", "msg": "************", "sig": "0000000000000000000000000000000000000000000000000000000000000000000000062522bbd3ecbe7c39e93e7c2477a172dfe37a2c53f0b92ab60f0a8f085f49dbfd930719d6f9e587ea68ae57cb49cd35a88cf8c6acec02f057a3807a5b", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "UspH3amRcsuDIUlaz5iFSClZiOyXPBtOqUYsU-V2inBKk2QQ7oR7Xb8enQwTHabH", "y": "h6RwJ-ZlV5LrAC1CKO5y98gUyaDOy_8meUj4HJkDrBDrNfbLhjaSJO1gmBHN85D0"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0452ca47dda99172cb8321495acf988548295988ec973c1b4ea9462c53e5768a704a936410ee847b5dbf1e9d0c131da6c787a47027e6655792eb002d4228ee72f7c814c9a0cecbff267948f81c9903ac10eb35f6cb86369224ed609811cdf390f4", "wx": "52ca47dda99172cb8321495acf988548295988ec973c1b4ea9462c53e5768a704a936410ee847b5dbf1e9d0c131da6c7", "wy": "0087a47027e6655792eb002d4228ee72f7c814c9a0cecbff267948f81c9903ac10eb35f6cb86369224ed609811cdf390f4"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000452ca47dda99172cb8321495acf988548295988ec973c1b4ea9462c53e5768a704a936410ee847b5dbf1e9d0c131da6c787a47027e6655792eb002d4228ee72f7c814c9a0cecbff267948f81c9903ac10eb35f6cb86369224ed609811cdf390f4", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEUspH3amRcsuDIUlaz5iFSClZiOyXPBtO\nqUYsU+V2inBKk2QQ7oR7Xb8enQwTHabHh6RwJ+ZlV5LrAC1CKO5y98gUyaDOy/8m\neUj4HJkDrBDrNfbLhjaSJO1gmBHN85D0\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 163, "comment": "r and s^-1 are close to n", "msg": "************", "sig": "ffffffffffffffffffffffffffffffffffffffffffffffffc7634d81f4372ddf581a0db248b0a77aecec196accc528f355555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "vT2R8APhitvqcwedTrojuR_Bf87BTJ6xWhk_vJyjnIx0fNeiyWI-Bd1YfMu4q0xE", "y": "OtsKBwaqXqemgEIIL8zvyXlhKnoaPWlLAHk7A_ib_4Zqi5fI53mQwpNgznlQNsdk"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04bd3d91f003e18adbea73079d4eba23b91fc17fcec14c9eb15a193fbc9ca39c8c747cd7a2c9623e05dd587ccbb8ab4c443adb0a0706aa5ea7a68042082fccefc979612a7a1a3d694b00793b03f89bff866a8b97c8e77990c29360ce795036c764", "wx": "00bd3d91f003e18adbea73079d4eba23b91fc17fcec14c9eb15a193fbc9ca39c8c747cd7a2c9623e05dd587ccbb8ab4c44", "wy": "3adb0a0706aa5ea7a68042082fccefc979612a7a1a3d694b00793b03f89bff866a8b97c8e77990c29360ce795036c764"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004bd3d91f003e18adbea73079d4eba23b91fc17fcec14c9eb15a193fbc9ca39c8c747cd7a2c9623e05dd587ccbb8ab4c443adb0a0706aa5ea7a68042082fccefc979612a7a1a3d694b00793b03f89bff866a8b97c8e77990c29360ce795036c764", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEvT2R8APhitvqcwedTrojuR/Bf87BTJ6x\nWhk/vJyjnIx0fNeiyWI+Bd1YfMu4q0xEOtsKBwaqXqemgEIIL8zvyXlhKnoaPWlL\nAHk7A/ib/4Zqi5fI53mQwpNgznlQNsdk\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 164, "comment": "s == 1", "msg": "************", "sig": "55555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001", "result": "valid", "flags": []}, {"tcId": 165, "comment": "s == 0", "msg": "************", "sig": "55555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "-JY1PMOor91UPsOu8GLKl7wy7Rck6ji5QLjA6g4js0GHr75w2vjbqltRFVfl0r3a", "y": "xL0mXaZ87q_KY29vTARy8iqdAuIokYT3O7twCuj8kh7_SSDykL_LSfuyMswTohAo"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04f896353cc3a8afdd543ec3aef062ca97bc32ed1724ea38b940b8c0ea0e23b34187afbe70daf8dbaa5b511557e5d2bddac4bd265da67ceeafca636f6f4c0472f22a9d02e2289184f73bbb700ae8fc921eff4920f290bfcb49fbb232cc13a21028", "wx": "00f896353cc3a8afdd543ec3aef062ca97bc32ed1724ea38b940b8c0ea0e23b34187afbe70daf8dbaa5b511557e5d2bdda", "wy": "00c4bd265da67ceeafca636f6f4c0472f22a9d02e2289184f73bbb700ae8fc921eff4920f290bfcb49fbb232cc13a21028"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004f896353cc3a8afdd543ec3aef062ca97bc32ed1724ea38b940b8c0ea0e23b34187afbe70daf8dbaa5b511557e5d2bddac4bd265da67ceeafca636f6f4c0472f22a9d02e2289184f73bbb700ae8fc921eff4920f290bfcb49fbb232cc13a21028", "keyPem": "-----BEGIN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE+JY1PMOor91UPsOu8GLKl7wy7Rck6ji5\nQLjA6g4js0GHr75w2vjbqltRFVfl0r3axL0mXaZ87q/KY29vTARy8iqdAuIokYT3\nO7twCuj8kh7/SSDykL/LSfuyMswTohAo\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 166, "comment": "point at infinity during verify", "msg": "************", "sig": "7fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294b955555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "Nw2eLjHHEsgCgJL4AjGdf99bMxmoUY0IvtOJFQjHBgz-Ijbhj6FP4HcJPOrmM-VD", "y": "D9earPnRbswZsS1g-6SZjfxoJwLsfIvdSlkANXc7jJxXCsfc1BTgMlL3oOb1O1hj"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04370d9e2e31c712c8028092f802319d7fdf5b3319a8518d08bed3891508c7060cfe2236e18fa14fe077093ceae633e5430fd79aacf9d16ecc19b12d60fba4998dfc682702ec7c8bdd4a590035773b8c9c570ac7dcd414e03252f7a0e6f53b5863", "wx": "370d9e2e31c712c8028092f802319d7fdf5b3319a8518d08bed3891508c7060cfe2236e18fa14fe077093ceae633e543", "wy": "0fd79aacf9d16ecc19b12d60fba4998dfc682702ec7c8bdd4a590035773b8c9c570ac7dcd414e03252f7a0e6f53b5863"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004370d9e2e31c712c8028092f802319d7fdf5b3319a8518d08bed3891508c7060cfe2236e18fa14fe077093ceae633e5430fd79aacf9d16ecc19b12d60fba4998dfc682702ec7c8bdd4a590035773b8c9c570ac7dcd414e03252f7a0e6f53b5863", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAENw2eLjHHEsgCgJL4AjGdf99bMxmoUY0I\nvtOJFQjHBgz+Ijbhj6FP4HcJPOrmM+VDD9earPnRbswZsS1g+6SZjfxoJwLsfIvd\nSlkANXc7jJxXCsfc1BTgMlL3oOb1O1hj\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 167, "comment": "edge case for signature malleability", "msg": "************", "sig": "7fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294b97fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294b9", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "lB5s-jVuVy3MyutZSwaVXZncS_B5WPyY_6F94Rx1Ib8seqj_JglS_LeqwHjt5ntH", "y": "kKeKApawQaEPAD3xmY2kzEoWFOvL9dI5Qx8z2Q0wI-3BgC6Nttq8uuZ8wxTaKqur"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04941e6cfa356e572dcccaeb594b06955d99dc4bf07958fc98ffa17de11c7521bf2c7aa8ff260952fcb7aac078ede67b4790a78a0296b041a10f003df1998da4cc4a1614ebcbf5d239431f33d90d3023edc1802e8db6dabcbae67cc314da2aabab", "wx": "00941e6cfa356e572dcccaeb594b06955d99dc4bf07958fc98ffa17de11c7521bf2c7aa8ff260952fcb7aac078ede67b47", "wy": "0090a78a0296b041a10f003df1998da4cc4a1614ebcbf5d239431f33d90d3023edc1802e8db6dabcbae67cc314da2aabab"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004941e6cfa356e572dcccaeb594b06955d99dc4bf07958fc98ffa17de11c7521bf2c7aa8ff260952fcb7aac078ede67b4790a78a0296b041a10f003df1998da4cc4a1614ebcbf5d239431f33d90d3023edc1802e8db6dabcbae67cc314da2aabab", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAElB5s+jVuVy3MyutZSwaVXZncS/B5WPyY\n/6F94Rx1Ib8seqj/JglS/LeqwHjt5ntHkKeKApawQaEPAD3xmY2kzEoWFOvL9dI5\nQx8z2Q0wI+3BgC6Nttq8uuZ8wxTaKqur\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 168, "comment": "edge case for signature malleability", "msg": "************", "sig": "7fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294b97fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294ba", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "Ps_Vijzlg4ZuBHHRbrPBCkEew7hnHzoEdpse2EZKcc8cdtjZt-NnC75xLW9VSpOD", "y": "2YDYvt9XRw1rRcwa0MZCbccKDkvpARBqNmY7_KsE_LhgCHd7kkRRINXjZB2XOWNi"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "043ecfd58a3ce583866e0471d16eb3c10a411ec3b8671f3a04769b1ed8464a71cf1c76d8d9b7e3670bbe712d6f554a9383d980d8bedf57470d6b45cc1ad0c6426dc70a0e4be901106a36663bfcab04fcb86008777b92445120d5e3641d97396362", "wx": "3ecfd58a3ce583866e0471d16eb3c10a411ec3b8671f3a04769b1ed8464a71cf1c76d8d9b7e3670bbe712d6f554a9383", "wy": "00d980d8bedf57470d6b45cc1ad0c6426dc70a0e4be901106a36663bfcab04fcb86008777b92445120d5e3641d97396362"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200043ecfd58a3ce583866e0471d16eb3c10a411ec3b8671f3a04769b1ed8464a71cf1c76d8d9b7e3670bbe712d6f554a9383d980d8bedf57470d6b45cc1ad0c6426dc70a0e4be901106a36663bfcab04fcb86008777b92445120d5e3641d97396362", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEPs/Vijzlg4ZuBHHRbrPBCkEew7hnHzoE\ndpse2EZKcc8cdtjZt+NnC75xLW9VSpOD2YDYvt9XRw1rRcwa0MZCbccKDkvpARBq\nNmY7/KsE/LhgCHd7kkRRINXjZB2XOWNi\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 169, "comment": "u1 == 1", "msg": "************", "sig": "55555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326f9b127f0d81ebcd17b7ba0ea131c660d340b05ce557c82160e0f793de07d38179023942871acb7002dfafdfffc8deace", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "QVDM0PpFqi72tQQt27G4fF_9ERWo_lmVZBlIrNqCp7GQdi2ENSzXTRygHnn2j5y0", "y": "6xG-nUlMGBwVbiPnflMr3wogw8x0uowpsfPrK9mRKe4NcP8NWT8Nem1oh-fFWTDS"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "044150ccd0fa45aa2ef6b5042ddbb1b87c5ffd1115a8fe5995641948acda82a7b190762d84352cd74d1ca01e79f68f9cb4eb11be9d494c181c156e23e77e532bdf0a20c3cc74ba8c29b1f3eb2bd99129ee0d70ff0d593f0d7a6d6887e7c55930d2", "wx": "4150ccd0fa45aa2ef6b5042ddbb1b87c5ffd1115a8fe5995641948acda82a7b190762d84352cd74d1ca01e79f68f9cb4", "wy": "00eb11be9d494c181c156e23e77e532bdf0a20c3cc74ba8c29b1f3eb2bd99129ee0d70ff0d593f0d7a6d6887e7c55930d2"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200044150ccd0fa45aa2ef6b5042ddbb1b87c5ffd1115a8fe5995641948acda82a7b190762d84352cd74d1ca01e79f68f9cb4eb11be9d494c181c156e23e77e532bdf0a20c3cc74ba8c29b1f3eb2bd99129ee0d70ff0d593f0d7a6d6887e7c55930d2", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEQVDM0PpFqi72tQQt27G4fF/9ERWo/lmV\nZBlIrNqCp7GQdi2ENSzXTRygHnn2j5y06xG+nUlMGBwVbiPnflMr3wogw8x0uowp\nsfPrK9mRKe4NcP8NWT8Nem1oh+fFWTDS\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 170, "comment": "u1 == n - 1", "msg": "************", "sig": "55555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326064ed80f27e1432e84845f15ece399f2cbf4fa31aa837de9b953d44413b9f5c7c7f67989d703f07abef11b6ad0373ea5", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "54_iwRvqxwkO4K9_7UaajM69PMzE7p_MyO8_wEVbaaqggtwT4dhPNAJstvCvnpkv", "y": "8067pxvzpAUL8o5AhLXF9dQJjsRvEKMbAvtL8gzJNi9vAqZugC-BdQdTX6w-wLCZ"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04e78fe2c11beac7090ee0af7fed469a8ccebd3cccc4ee9fccc8ef3fc0455b69aaa082dc13e1d84f34026cb6f0af9e992ff34ebba71bf3a4050bf28e4084b5c5f5d4098ec46f10a31b02fb4bf20cc9362f6f02a66e802f817507535fac3ec0b099", "wx": "00e78fe2c11beac7090ee0af7fed469a8ccebd3cccc4ee9fccc8ef3fc0455b69aaa082dc13e1d84f34026cb6f0af9e992f", "wy": "00f34ebba71bf3a4050bf28e4084b5c5f5d4098ec46f10a31b02fb4bf20cc9362f6f02a66e802f817507535fac3ec0b099"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004e78fe2c11beac7090ee0af7fed469a8ccebd3cccc4ee9fccc8ef3fc0455b69aaa082dc13e1d84f34026cb6f0af9e992ff34ebba71bf3a4050bf28e4084b5c5f5d4098ec46f10a31b02fb4bf20cc9362f6f02a66e802f817507535fac3ec0b099", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE54/iwRvqxwkO4K9/7UaajM69PMzE7p/M\nyO8/wEVbaaqggtwT4dhPNAJstvCvnpkv8067pxvzpAUL8o5AhLXF9dQJjsRvEKMb\nAvtL8gzJNi9vAqZugC+BdQdTX6w+wLCZ\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 171, "comment": "u2 == 1", "msg": "************", "sig": "55555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec632655555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "7iSrijTQWvaEk5NX8ydZzFoU88cXUpogrqjgxZZdikHmiSX2iEcZlLcgIbpRsowJ", "y": "ClVpPJKtDLrp7c9RXitMBguIjYLIHko7ahc7Yu0EpG-pXbGi85SZgPui43EmPE-p"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04ee24ab8a34d05af684939357f32759cc5a14f3c717529a20aea8e0c5965d8a41e68925f688471994b72021ba51b28c090a55693c92ad0cbae9edcf515e2b4c060b888d82c81e4a3b6a173b62ed04a46fa95db1a2f3949980fba2e371263c4fa9", "wx": "00ee24ab8a34d05af684939357f32759cc5a14f3c717529a20aea8e0c5965d8a41e68925f688471994b72021ba51b28c09", "wy": "0a55693c92ad0cbae9edcf515e2b4c060b888d82c81e4a3b6a173b62ed04a46fa95db1a2f3949980fba2e371263c4fa9"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004ee24ab8a34d05af684939357f32759cc5a14f3c717529a20aea8e0c5965d8a41e68925f688471994b72021ba51b28c090a55693c92ad0cbae9edcf515e2b4c060b888d82c81e4a3b6a173b62ed04a46fa95db1a2f3949980fba2e371263c4fa9", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE7iSrijTQWvaEk5NX8ydZzFoU88cXUpog\nrqjgxZZdikHmiSX2iEcZlLcgIbpRsowJClVpPJKtDLrp7c9RXitMBguIjYLIHko7\nahc7Yu0EpG+pXbGi85SZgPui43EmPE+p\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 172, "comment": "u2 == n - 1", "msg": "************", "sig": "55555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa84ecde56a2cf73ea3abc092185cb1a51f34810f1ddd8c64d", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "PS6RYFXJLhs2Ez9ZN7N8GwECg063cAijupw9pEbpBllx1oupEwkYUeEM_1tM2HXB", "y": "Oap6rfwsr3EHsXrhrqiymdYb8VrKDLP9bx_96Bkr_ljwgiu7wfVb3fa0_pyPKw6s"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "043d2e916055c92e1b36133f5937b37c1b0102834eb77008a3ba9c3da446e9065971d68ba913091851e10cff5b4cd875c139aa7aadfc2caf7107b17ae1aea8b299d61bf15aca0cb3fd6f1ffde8192bfe58f0822bbbc1f55bddf6b4fe9c8f2b0eac", "wx": "3d2e916055c92e1b36133f5937b37c1b0102834eb77008a3ba9c3da446e9065971d68ba913091851e10cff5b4cd875c1", "wy": "39aa7aadfc2caf7107b17ae1aea8b299d61bf15aca0cb3fd6f1ffde8192bfe58f0822bbbc1f55bddf6b4fe9c8f2b0eac"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200043d2e916055c92e1b36133f5937b37c1b0102834eb77008a3ba9c3da446e9065971d68ba913091851e10cff5b4cd875c139aa7aadfc2caf7107b17ae1aea8b299d61bf15aca0cb3fd6f1ffde8192bfe58f0822bbbc1f55bddf6b4fe9c8f2b0eac", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEPS6RYFXJLhs2Ez9ZN7N8GwECg063cAij\nupw9pEbpBllx1oupEwkYUeEM/1tM2HXBOap6rfwsr3EHsXrhrqiymdYb8VrKDLP9\nbx/96Bkr/ljwgiu7wfVb3fa0/pyPKw6s\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 173, "comment": "edge case for u1", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd533b0d50480a3ef07e7e8af8b1097759bc03ac9a1c7ed6075a052869f57f12b285613162d08ee7aab9fe54aaa984a39a", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "rllml0J6olAVbAWsQzjkiYCn8JPqHx_mcJi0P2U5wbIK50M4-b8nDTNmPFCr6P0A", "y": "HKalJzLbdKsV0vJJo9g5CA-Jg2ff1kmSzc4nCN6q1SOioja0NABCQkHJGjW1MPpQ"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04ae596697427aa250156c05ac4338e48980a7f093ea1f1fe67098b43f6539c1b20ae74338f9bf270d33663c50abe8fd001ca6a52732db74ab15d2f249a3d839080f898367dfd64992cdce2708deaad523a2a236b43400424241c91a35b530fa50", "wx": "00ae596697427aa250156c05ac4338e48980a7f093ea1f1fe67098b43f6539c1b20ae74338f9bf270d33663c50abe8fd00", "wy": "1ca6a52732db74ab15d2f249a3d839080f898367dfd64992cdce2708deaad523a2a236b43400424241c91a35b530fa50"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004ae596697427aa250156c05ac4338e48980a7f093ea1f1fe67098b43f6539c1b20ae74338f9bf270d33663c50abe8fd001ca6a52732db74ab15d2f249a3d839080f898367dfd64992cdce2708deaad523a2a236b43400424241c91a35b530fa50", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAErllml0J6olAVbAWsQzjkiYCn8JPqHx/m\ncJi0P2U5wbIK50M4+b8nDTNmPFCr6P0AHKalJzLbdKsV0vJJo9g5CA+Jg2ff1kmS\nzc4nCN6q1SOioja0NABCQkHJGjW1MPpQ\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 174, "comment": "edge case for u1", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdd49a253986bbaa8ce9c3d3808313d39c3b950a478372edc009bc0566b73be7b05dad0737e16960257cc16db6ec6c620f", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "iHOPmYHdTR-rtgrYPC3W38naMCIJrj5TSYqIO245o4vq2bAnCfNS0-a2V4FU6rJS", "y": "k4igXGufOkAoq7mVClH1Jk7NdYCkI_3slHL67rV_kuMcRr7yp4H-XtrQJgCfGYJi"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0488738f9981dd4d1fabb60ad83c2dd6dfc9da302209ae3e53498a883b6e39a38bead9b02709f352d3e6b6578154eab2529388a05c6b9f3a4028abb9950a51f5264ecd7580a423fdec9472faeeb57f92e31c46bef2a781fe5edad026009f198262", "wx": "0088738f9981dd4d1fabb60ad83c2dd6dfc9da302209ae3e53498a883b6e39a38bead9b02709f352d3e6b6578154eab252", "wy": "009388a05c6b9f3a4028abb9950a51f5264ecd7580a423fdec9472faeeb57f92e31c46bef2a781fe5edad026009f198262"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000488738f9981dd4d1fabb60ad83c2dd6dfc9da302209ae3e53498a883b6e39a38bead9b02709f352d3e6b6578154eab2529388a05c6b9f3a4028abb9950a51f5264ecd7580a423fdec9472faeeb57f92e31c46bef2a781fe5edad026009f198262", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEiHOPmYHdTR+rtgrYPC3W38naMCIJrj5T\nSYqIO245o4vq2bAnCfNS0+a2V4FU6rJSk4igXGufOkAoq7mVClH1Jk7NdYCkI/3s\nlHL67rV/kuMcRr7yp4H+XtrQJgCfGYJi\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 175, "comment": "edge case for u1", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd285090b0d6a6820bbba394efbee5c24a2281e825d2f6c55fb7a85b8251db00f75ab07cc993ceaf664f3c116baf34b021", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "9CFUExHJT915_CmPirGjrf0IAp_a1DmpTUzqEffnmbxDlgny-3vj80nVXkhNCg02", "y": "s1Mwu9vsHnXymESD2WvyENciwYMCkv_DWi9qIaS1BRn1ZfAku8zJciii-K2PrcDV"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04f421541311c94fdd79fc298f8ab1a3adfd08029fdad439a94d4cea11f7e799bc439609f2fb7be3f349d55e484d0a0d36b35330bbdbec1e75f2984483d96bf210d722c1830292ffc35a2f6a21a4b50519f565f024bbccc97228a2f8ad8fadc0d5", "wx": "00f421541311c94fdd79fc298f8ab1a3adfd08029fdad439a94d4cea11f7e799bc439609f2fb7be3f349d55e484d0a0d36", "wy": "00b35330bbdbec1e75f2984483d96bf210d722c1830292ffc35a2f6a21a4b50519f565f024bbccc97228a2f8ad8fadc0d5"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004f421541311c94fdd79fc298f8ab1a3adfd08029fdad439a94d4cea11f7e799bc439609f2fb7be3f349d55e484d0a0d36b35330bbdbec1e75f2984483d96bf210d722c1830292ffc35a2f6a21a4b50519f565f024bbccc97228a2f8ad8fadc0d5", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE9CFUExHJT915/CmPirGjrf0IAp/a1Dmp\nTUzqEffnmbxDlgny+3vj80nVXkhNCg02s1Mwu9vsHnXymESD2WvyENciwYMCkv/D\nWi9qIaS1BRn1ZfAku8zJciii+K2PrcDV\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 176, "comment": "edge case for u1", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdb39af4a81ee4ae79064ed80f27e1432e84845f15ece399f2a43d2505a0a8c72c5731f4fd967420b1000e3f75502ed7b7", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "OZvkz8Q5-U8kIcvTTCzZC65T62Dd-vylL3J10WXRT6ZZtjZxO11LOeYv1IuuFB0O", "y": "GyPjtPDCAu17Wdt4o1wSrGmMYD6rFE_QmsLtj0SV9gfk0sh6I84uwz5BDKR-zCVV"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04399be4cfc439f94f2421cbd34c2cd90bae53eb60ddfafca52f7275d165d14fa659b636713b5d4b39e62fd48bae141d0e1b23e3b4f0c202ed7b59db78a35c12ac698c603eab144fd09ac2ed8f4495f607e4d2c87a23ce2ec33e410ca47ecc2555", "wx": "399be4cfc439f94f2421cbd34c2cd90bae53eb60ddfafca52f7275d165d14fa659b636713b5d4b39e62fd48bae141d0e", "wy": "1b23e3b4f0c202ed7b59db78a35c12ac698c603eab144fd09ac2ed8f4495f607e4d2c87a23ce2ec33e410ca47ecc2555"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004399be4cfc439f94f2421cbd34c2cd90bae53eb60ddfafca52f7275d165d14fa659b636713b5d4b39e62fd48bae141d0e1b23e3b4f0c202ed7b59db78a35c12ac698c603eab144fd09ac2ed8f4495f607e4d2c87a23ce2ec33e410ca47ecc2555", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEOZvkz8Q5+U8kIcvTTCzZC65T62Dd+vyl\nL3J10WXRT6ZZtjZxO11LOeYv1IuuFB0OGyPjtPDCAu17Wdt4o1wSrGmMYD6rFE/Q\nmsLtj0SV9gfk0sh6I84uwz5BDKR+zCVV\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 177, "comment": "edge case for u1", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdaf4a81ee4ae79064ed80f27e1432e84845f15ece399f2cbf28df829ccd30f5ef62ec23957b837d73fe4e156edccd4465", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "FXi7_3ITfEvKM9c4Wokr6UywWfkJHd_okDRfcSqful_HcITOwRCE7QSEkWBKB_Zs", "y": "druqhy8HENgqCNnd3YM8e-fH6OJl9JFFFX606OgoAHajfuWHMnHbUQA02hnaJEFb"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041578bbff72137c4bca33d7385a892be94cb059f9091ddfe890345f712a9fba5fc77084cec11084ed048491604a07f66c76bbaa872f0710d82a08d9dddd833c7be7c7e8e265f49145157eb4e8e8280076a37ee5873271db510034da19da24415b", "wx": "1578bbff72137c4bca33d7385a892be94cb059f9091ddfe890345f712a9fba5fc77084cec11084ed048491604a07f66c", "wy": "76bbaa872f0710d82a08d9dddd833c7be7c7e8e265f49145157eb4e8e8280076a37ee5873271db510034da19da24415b"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200041578bbff72137c4bca33d7385a892be94cb059f9091ddfe890345f712a9fba5fc77084cec11084ed048491604a07f66c76bbaa872f0710d82a08d9dddd833c7be7c7e8e265f49145157eb4e8e8280076a37ee5873271db510034da19da24415b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEFXi7/3ITfEvKM9c4Wokr6UywWfkJHd/o\nkDRfcSqful/HcITOwRCE7QSEkWBKB/Zsdruqhy8HENgqCNnd3YM8e+fH6OJl9JFF\nFX606OgoAHajfuWHMnHbUQA02hnaJEFb\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 178, "comment": "edge case for u1", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd5e9503dc95cf20c9db01e4fc2865d0908be2bd9c733e597e8a5bb7b7a62abdff6dbe3978ae56536d0fb01172ecd55f57", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "M7pFHIXnKQWPgwQQd6RpXrR9-T5xiwmkYYx1OsgDzXXBqRKQwv9aY1OJ0HFJVx2r", "y": "H8fYpxd2hR_yRP9jL-b5LhZS5ShIk8QkT-d12O_Fidgj3QPzkZAn8ARTe9juCfOj"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0433ba451c85e729058f83041077a4695eb47df93e718b09a4618c753ac803cd75c1a91290c2ff5a635389d07149571dab1fc7d8a71776851ff244ff632fe6f92e1652e5284893c4244fe775d8efc589d823dd03f3919027f004537bd8ee09f3a3", "wx": "33ba451c85e729058f83041077a4695eb47df93e718b09a4618c753ac803cd75c1a91290c2ff5a635389d07149571dab", "wy": "1fc7d8a71776851ff244ff632fe6f92e1652e5284893c4244fe775d8efc589d823dd03f3919027f004537bd8ee09f3a3"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000433ba451c85e729058f83041077a4695eb47df93e718b09a4618c753ac803cd75c1a91290c2ff5a635389d07149571dab1fc7d8a71776851ff244ff632fe6f92e1652e5284893c4244fe775d8efc589d823dd03f3919027f004537bd8ee09f3a3", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEM7pFHIXnKQWPgwQQd6RpXrR9+T5xiwmk\nYYx1OsgDzXXBqRKQwv9aY1OJ0HFJVx2rH8fYpxd2hR/yRP9jL+b5LhZS5ShIk8Qk\nT+d12O/Fidgj3QPzkZAn8ARTe9juCfOj\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 179, "comment": "edge case for u1", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd1ee4ae79064ed80f27e1432e84845f15ece399f2cbf4fa31a3ae8edab84dc3330a39f70938e3912bd59753de5aed3088", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "BAdx4zkCFv7SxiCL31v-qDqxkVsWbmJlafEu_UEKObfnx29w8AEoQ6Jt6_TMwz3a", "y": "5bxffmLQVOrDHNAir9txt8Y48kwwy60O817S_JkX81bpw_BDkbIdEDUnS4FTf8vz"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04040771e3390216fed2c6208bdf5bfea83ab1915b166e626569f12efd410a39b7e7c76f70f0012843a26debf4ccc33ddae5bc5f7e62d054eac31cd022afdb71b7c638f24c30cbad0ef35ed2fc9917f356e9c3f04391b21d1035274b81537fcbf3", "wx": "040771e3390216fed2c6208bdf5bfea83ab1915b166e626569f12efd410a39b7e7c76f70f0012843a26debf4ccc33dda", "wy": "00e5bc5f7e62d054eac31cd022afdb71b7c638f24c30cbad0ef35ed2fc9917f356e9c3f04391b21d1035274b81537fcbf3"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004040771e3390216fed2c6208bdf5bfea83ab1915b166e626569f12efd410a39b7e7c76f70f0012843a26debf4ccc33ddae5bc5f7e62d054eac31cd022afdb71b7c638f24c30cbad0ef35ed2fc9917f356e9c3f04391b21d1035274b81537fcbf3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEBAdx4zkCFv7SxiCL31v+qDqxkVsWbmJl\nafEu/UEKObfnx29w8AEoQ6Jt6/TMwz3a5bxffmLQVOrDHNAir9txt8Y48kwwy60O\n817S/JkX81bpw/BDkbIdEDUnS4FTf8vz\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 180, "comment": "edge case for u1", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdbb51cd3ba8eb201f53ddb4e34e08c0ff7dff9378106784d798d5a3440bd6dc34be3a0eaef8776619a0c97fefb15720b3", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "mNPxbhxRCpM-ZI540BWIMZ8ALpR134lCoqidsGZrt8iLMrskgUDkSsSrKBEbK3kj", "y": "makm9KZvvij_ZcCfgwaJOuwJS4nQ_lKeNXfF7PMKeUTKr1MPRXXrET_PTCANLdS9"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0498d3f16e1c510a933e648e78d01588319f002e9475df8942a2a89db0666bb7c88b32bb248140e44ac4ab28111b2b792399a926f4a66fbe28ff65c09f8306893aec094b89d0fe529e3577c5ecf30a7944caaf530f4575eb113fcf4c200d2dd4bd", "wx": "0098d3f16e1c510a933e648e78d01588319f002e9475df8942a2a89db0666bb7c88b32bb248140e44ac4ab28111b2b7923", "wy": "0099a926f4a66fbe28ff65c09f8306893aec094b89d0fe529e3577c5ecf30a7944caaf530f4575eb113fcf4c200d2dd4bd"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000498d3f16e1c510a933e648e78d01588319f002e9475df8942a2a89db0666bb7c88b32bb248140e44ac4ab28111b2b792399a926f4a66fbe28ff65c09f8306893aec094b89d0fe529e3577c5ecf30a7944caaf530f4575eb113fcf4c200d2dd4bd", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEmNPxbhxRCpM+ZI540BWIMZ8ALpR134lC\noqidsGZrt8iLMrskgUDkSsSrKBEbK3kjmakm9KZvvij/ZcCfgwaJOuwJS4nQ/lKe\nNXfF7PMKeUTKr1MPRXXrET/PTCANLdS9\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 181, "comment": "edge case for u1", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffde707e267ea635384a6da09823149f5cb7acbb29e910d2630c5fb5afbc42aa8436349b214a3b8fb9481ec999e005091f8", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "0f1gL-74C-nlWhnRqXmccqiZEQxqwh-zwhNXBpgJ1ZGod1tk0YZ6jP_xJPal46T1", "y": "-VSAZPAbmviGhwVJOjegNxk7SPU7fHlzAj9T5s7_aDDKL3oU71FTbUU69DswWNip"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04d1fd602feef80be9e55a19d1a9799c72a899110c6ac21fb3c21357069809d591a8775b64d1867a8cfff124f6a5e3a4f5f9548064f01b9af8868705493a37a037193b48f53b7c7973023f53e6ceff6830ca2f7a14ef51536d453af43b3058d8a9", "wx": "00d1fd602feef80be9e55a19d1a9799c72a899110c6ac21fb3c21357069809d591a8775b64d1867a8cfff124f6a5e3a4f5", "wy": "00f9548064f01b9af8868705493a37a037193b48f53b7c7973023f53e6ceff6830ca2f7a14ef51536d453af43b3058d8a9"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004d1fd602feef80be9e55a19d1a9799c72a899110c6ac21fb3c21357069809d591a8775b64d1867a8cfff124f6a5e3a4f5f9548064f01b9af8868705493a37a037193b48f53b7c7973023f53e6ceff6830ca2f7a14ef51536d453af43b3058d8a9", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE0f1gL+74C+nlWhnRqXmccqiZEQxqwh+z\nwhNXBpgJ1ZGod1tk0YZ6jP/xJPal46T1+VSAZPAbmviGhwVJOjegNxk7SPU7fHlz\nAj9T5s7/aDDKL3oU71FTbUU69DswWNip\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 182, "comment": "edge case for u1", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdacc4f2afb7f5c10f818175074ef688a643fc5365e38129f86d5e2517feb81b2cd2b8dc4f7821bfd032edc4c0234085d9", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "gvN2BPZmZMKIPbptmDl8KBBFy_WfHRbd2xOBEmokZVOotNKq6kitkYWhZF9lVn0x", "y": "ik17GfHS5ENMmo7K05YwSryCIhu6sGeZNQcccv2XXnsCHASx0W6jb8LQUe9ajhF8"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0482f37604f66664c2883dba6d98397c281045cbf59f1d16dddb1381126a246553a8b4d2aaea48ad9185a1645f65567d318a4d7b19f1d2e4434c9a8ecad396304abc82221bbab0679935071c72fd975e7b021c04b1d16ea36fc2d051ef5a8e117c", "wx": "0082f37604f66664c2883dba6d98397c281045cbf59f1d16dddb1381126a246553a8b4d2aaea48ad9185a1645f65567d31", "wy": "008a4d7b19f1d2e4434c9a8ecad396304abc82221bbab0679935071c72fd975e7b021c04b1d16ea36fc2d051ef5a8e117c"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000482f37604f66664c2883dba6d98397c281045cbf59f1d16dddb1381126a246553a8b4d2aaea48ad9185a1645f65567d318a4d7b19f1d2e4434c9a8ecad396304abc82221bbab0679935071c72fd975e7b021c04b1d16ea36fc2d051ef5a8e117c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEgvN2BPZmZMKIPbptmDl8KBBFy/WfHRbd\n2xOBEmokZVOotNKq6kitkYWhZF9lVn0xik17GfHS5ENMmo7K05YwSryCIhu6sGeZ\nNQcccv2XXnsCHASx0W6jb8LQUe9ajhF8\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 183, "comment": "edge case for u1", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd83276c0793f0a19742422f8af671ccf965fa7d18d541bef4c05b90e303f891d39008439e0fda4bfad5ee9a6ace7e340c", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "8FLfwnv4ptNvNznyObmB9bU_4I2ZnsaDsB5D51lhViBroIuLn1kini-9zgXx5A-Z", "y": "kPD9-3Ap-bPoxhRNrQM5IIt83LOCClVCWdudJ6_dGPSnUClsWbrWti3wdvkNU74N"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04f052dfc27bf8a6d36f3739f239b981f5b53fe08d999ec683b01e43e7596156206ba08b8b9f59229e2fbdce05f1e40f9990f0fdfb7029f9b3e8c6144dad0339208b7cdcb3820a554259db9d27afdd18f4a750296c59bad6b62df076f90d53be0d", "wx": "00f052dfc27bf8a6d36f3739f239b981f5b53fe08d999ec683b01e43e7596156206ba08b8b9f59229e2fbdce05f1e40f99", "wy": "0090f0fdfb7029f9b3e8c6144dad0339208b7cdcb3820a554259db9d27afdd18f4a750296c59bad6b62df076f90d53be0d"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004f052dfc27bf8a6d36f3739f239b981f5b53fe08d999ec683b01e43e7596156206ba08b8b9f59229e2fbdce05f1e40f9990f0fdfb7029f9b3e8c6144dad0339208b7cdcb3820a554259db9d27afdd18f4a750296c59bad6b62df076f90d53be0d", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE8FLfwnv4ptNvNznyObmB9bU/4I2ZnsaD\nsB5D51lhViBroIuLn1kini+9zgXx5A+ZkPD9+3Ap+bPoxhRNrQM5IIt83LOCClVC\nWdudJ6/dGPSnUClsWbrWti3wdvkNU74N\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 184, "comment": "edge case for u1", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd942848586b534105ddd1ca77df72e1251140f412e97b62afbf85d4822309176b5965453dee3fab709e14156b3dfcecca", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "-He9biqSc-MioymOo63RPREEsyFyKDZpymaI8MtZFSSn8V3UFJZoHtqYk5qucp_t", "y": "6FyjfIHvGePcmrFpCKNyDYaHWlGmptky43SSpux6NE6rxII3fxSJH70dp_rv-hF4"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04f877bd6e2a9273e322a3298ea3add13d1104b32172283669ca6688f0cb591524a7f15dd41496681eda98939aae729fede85ca37c81ef19e3dc9ab16908a3720d86875a51a6a6d932e37492a6ec7a344eabc482377f14891fbd1da7faeffa1178", "wx": "00f877bd6e2a9273e322a3298ea3add13d1104b32172283669ca6688f0cb591524a7f15dd41496681eda98939aae729fed", "wy": "00e85ca37c81ef19e3dc9ab16908a3720d86875a51a6a6d932e37492a6ec7a344eabc482377f14891fbd1da7faeffa1178"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004f877bd6e2a9273e322a3298ea3add13d1104b32172283669ca6688f0cb591524a7f15dd41496681eda98939aae729fede85ca37c81ef19e3dc9ab16908a3720d86875a51a6a6d932e37492a6ec7a344eabc482377f14891fbd1da7faeffa1178", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE+He9biqSc+MioymOo63RPREEsyFyKDZp\nymaI8MtZFSSn8V3UFJZoHtqYk5qucp/t6FyjfIHvGePcmrFpCKNyDYaHWlGmptky\n43SSpux6NE6rxII3fxSJH70dp/rv+hF4\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 185, "comment": "edge case for u2", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd7fffffffffffffffffffffffffffffffffffffffffffffffed2119d5fc12649fc808af3b6d9037d3a44eb32399970dd0", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "FCSbvP7s6rBsdWVNNhwN-NVrMg6jvB1GJ-wKL0uPo1d0RWlGZPVpqR9IB0E4HklK", "y": "KEefIYbXFaVniPZwcwVqoMsLan94k-d7mml272Zj2AImiW1_Q7tQLhtNSVWKJ92L"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0414249bbcfeeceab06c75654d361c0df8d56b320ea3bc1d4627ec0a2f4b8fa3577445694664f569a91f480741381e494a28479f2186d715a56788f67073056aa0cb0b6a7f7893e77b9a6976ef6663d80226896d7f43bb502e1b4d49558a27dd8b", "wx": "14249bbcfeeceab06c75654d361c0df8d56b320ea3bc1d4627ec0a2f4b8fa3577445694664f569a91f480741381e494a", "wy": "28479f2186d715a56788f67073056aa0cb0b6a7f7893e77b9a6976ef6663d80226896d7f43bb502e1b4d49558a27dd8b"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000414249bbcfeeceab06c75654d361c0df8d56b320ea3bc1d4627ec0a2f4b8fa3577445694664f569a91f480741381e494a28479f2186d715a56788f67073056aa0cb0b6a7f7893e77b9a6976ef6663d80226896d7f43bb502e1b4d49558a27dd8b", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEFCSbvP7s6rBsdWVNNhwN+NVrMg6jvB1G\nJ+wKL0uPo1d0RWlGZPVpqR9IB0E4HklKKEefIYbXFaVniPZwcwVqoMsLan94k+d7\nmml272Zj2AImiW1/Q7tQLhtNSVWKJ92L\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 186, "comment": "edge case for u2", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd79b95c013b0472de04d8faeec3b779c39fe729ea84fb554cd091c7178c2f054eabbc62c3e1cfbac2c2e69d7aa45d9072", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "UKQ4yY7pQCXOE-J9NrgoDUhDWFg260cBGgcM13cpJFaEoNsx_emAYgNJx5aDKyxs", "y": "vbctup8_nMh4VZ9Qtr0SkPEKa8y8Hu73cIsbcgWQIph5eeNSIcUSWfM3xyiKL4a8"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0450a438c98ee94025ce13e27d36b8280d4843585836eb47011a070cd77729245684a0db31fde980620349c796832b2c6cbdb72dba9f3f9cc878559f50b6bd1290f10a6bccbc1eeef7708b1b72059022987979e35221c51259f337c7288a2f86bc", "wx": "50a438c98ee94025ce13e27d36b8280d4843585836eb47011a070cd77729245684a0db31fde980620349c796832b2c6c", "wy": "00bdb72dba9f3f9cc878559f50b6bd1290f10a6bccbc1eeef7708b1b72059022987979e35221c51259f337c7288a2f86bc"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000450a438c98ee94025ce13e27d36b8280d4843585836eb47011a070cd77729245684a0db31fde980620349c796832b2c6cbdb72dba9f3f9cc878559f50b6bd1290f10a6bccbc1eeef7708b1b72059022987979e35221c51259f337c7288a2f86bc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEUKQ4yY7pQCXOE+J9NrgoDUhDWFg260cB\nGgcM13cpJFaEoNsx/emAYgNJx5aDKyxsvbctup8/nMh4VZ9Qtr0SkPEKa8y8Hu73\ncIsbcgWQIph5eeNSIcUSWfM3xyiKL4a8\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 187, "comment": "edge case for u2", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdbfd40d0caa4d9d42381f3d72a25683f52b03a1ed96fb72d03f08dcb9a8bc8f23c1a459deab03bcd39396c0d1e9053c81", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "TT_F3Pr3QRE82jzi-N_0yRIUPk02MUw2HX7VZWtoRIvMoRS6noEkKBI0Zgt3Jt3N", "y": "aA3f736ge_vO3hCAPTjXIRYxyhFGYHiBnrZuEZIat_-jxFYMcy53WV_UCOkX3Zr8"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "044d3fc5dcfaf741113cda3ce2f8dff4c912143e4d36314c361d7ed5656b68448bcca114ba9e8124281234660b7726ddcd680ddfef7ea07bfbcede10803d38d7211631ca11466078819eb66e11921ab7ffa3c4560c732e77595fd408e917dd9afc", "wx": "4d3fc5dcfaf741113cda3ce2f8dff4c912143e4d36314c361d7ed5656b68448bcca114ba9e8124281234660b7726ddcd", "wy": "680ddfef7ea07bfbcede10803d38d7211631ca11466078819eb66e11921ab7ffa3c4560c732e77595fd408e917dd9afc"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200044d3fc5dcfaf741113cda3ce2f8dff4c912143e4d36314c361d7ed5656b68448bcca114ba9e8124281234660b7726ddcd680ddfef7ea07bfbcede10803d38d7211631ca11466078819eb66e11921ab7ffa3c4560c732e77595fd408e917dd9afc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAETT/F3Pr3QRE82jzi+N/0yRIUPk02MUw2\nHX7VZWtoRIvMoRS6noEkKBI0Zgt3Jt3NaA3f736ge/vO3hCAPTjXIRYxyhFGYHiB\nnrZuEZIat/+jxFYMcy53WV/UCOkX3Zr8\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 188, "comment": "edge case for u2", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd4c7d219db9af94ce7fffffffffffffffffffffffffffffffef15cf1058c8d8ba1e634c4122db95ec1facd4bb13ebf09a", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "Y9Zc3-sfGkIAD0O9Hd0TBTentvY16NK9gal9oWgiEYPaQzynhCn9KzPF-UiVqcE6", "y": "qdHV6jKHJWU6Wp0A-FpVFiNvOxQoqGKSh9OwSHougt1X-Tuyqj2Xg9x0Ex4TdWA0"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0463d65cdfeb1f1a42000f43bd1ddd130537a7b6f635e8d2bd81a97da168221183da433ca78429fd2b33c5f94895a9c13aa9d1d5ea328725653a5a9d00f85a5516236f3b1428a8629287d3b0487a2e82dd57f93bb2aa3d9783dc74131e13756034", "wx": "63d65cdfeb1f1a42000f43bd1ddd130537a7b6f635e8d2bd81a97da168221183da433ca78429fd2b33c5f94895a9c13a", "wy": "00a9d1d5ea328725653a5a9d00f85a5516236f3b1428a8629287d3b0487a2e82dd57f93bb2aa3d9783dc74131e13756034"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000463d65cdfeb1f1a42000f43bd1ddd130537a7b6f635e8d2bd81a97da168221183da433ca78429fd2b33c5f94895a9c13aa9d1d5ea328725653a5a9d00f85a5516236f3b1428a8629287d3b0487a2e82dd57f93bb2aa3d9783dc74131e13756034", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEY9Zc3+sfGkIAD0O9Hd0TBTentvY16NK9\ngal9oWgiEYPaQzynhCn9KzPF+UiVqcE6qdHV6jKHJWU6Wp0A+FpVFiNvOxQoqGKS\nh9OwSHougt1X+Tuyqj2Xg9x0Ex4TdWA0\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 189, "comment": "edge case for u2", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdd219db9af94ce7ffffffffffffffffffffffffffffffffffd189bdb6d9ef7be8504ca374756ea5b8f15e44067d209b9b", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "0iycNIuXRXEfV966w6B9-QpSfAa9AqhFT0FDfVQiTgcWmPA_3GSx1lJBTtw_IjnE", "y": "mumBKkuS8JnWZZpllpF2jVflMO08kdVFV4FgWFCZelgiHyKiRRw5MkcGBsI_OrG4"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04d22c9c348b9745711f57debac3a07df90a527c06bd02a8454f41437d54224e071698f03fdc64b1d652414edc3f2239c49ae9812a4b92f099d6659a659691768d57e530ed3c91d5455781605850997a58221f22a2451c3932470606c23f3ab1b8", "wx": "00d22c9c348b9745711f57debac3a07df90a527c06bd02a8454f41437d54224e071698f03fdc64b1d652414edc3f2239c4", "wy": "009ae9812a4b92f099d6659a659691768d57e530ed3c91d5455781605850997a58221f22a2451c3932470606c23f3ab1b8"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004d22c9c348b9745711f57debac3a07df90a527c06bd02a8454f41437d54224e071698f03fdc64b1d652414edc3f2239c49ae9812a4b92f099d6659a659691768d57e530ed3c91d5455781605850997a58221f22a2451c3932470606c23f3ab1b8", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE0iycNIuXRXEfV966w6B9+QpSfAa9AqhF\nT0FDfVQiTgcWmPA/3GSx1lJBTtw/IjnEmumBKkuS8JnWZZpllpF2jVflMO08kdVF\nV4FgWFCZelgiHyKiRRw5MkcGBsI/OrG4\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 190, "comment": "edge case for u2", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffda433b735f299cfffffffffffffffffffffffffffffffffffdbb02debbfa7c9f1487f3936a22ca3f6f5d06ea22d7c0dc3", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "MfBcDCnp2kmqL7vt7ncMaNEPhef3fnKsPPqchiOiu0LusvJKyPKu96sMS0eCMUAD", "y": "W7MvwewEu_9eq5bgcMk4uhtT_mOXD2Sa4C4qStpCCiSbb3xSXixLmw1VYq4m8ieM"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0431f05c0c29e9da49aa2fbbedee770c68d10f85e7f77e72ac3cfa9c8623a2bb42eeb2f24ac8f2aef7ab0c4b47823140035bb32fc1ec04bbff5eab96e070c938ba1b53fe63970f649ae02e2a4ada420a249b6f7c525e2c4b9b0d5562ae26f2278c", "wx": "31f05c0c29e9da49aa2fbbedee770c68d10f85e7f77e72ac3cfa9c8623a2bb42eeb2f24ac8f2aef7ab0c4b4782314003", "wy": "5bb32fc1ec04bbff5eab96e070c938ba1b53fe63970f649ae02e2a4ada420a249b6f7c525e2c4b9b0d5562ae26f2278c"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000431f05c0c29e9da49aa2fbbedee770c68d10f85e7f77e72ac3cfa9c8623a2bb42eeb2f24ac8f2aef7ab0c4b47823140035bb32fc1ec04bbff5eab96e070c938ba1b53fe63970f649ae02e2a4ada420a249b6f7c525e2c4b9b0d5562ae26f2278c", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEMfBcDCnp2kmqL7vt7ncMaNEPhef3fnKs\nPPqchiOiu0LusvJKyPKu96sMS0eCMUADW7MvwewEu/9eq5bgcMk4uhtT/mOXD2Sa\n4C4qStpCCiSbb3xSXixLmw1VYq4m8ieM\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 191, "comment": "edge case for u2", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffdb9af94ce7fffffffffffffffffffffffffffffffffffffffd6efeefc876c9f23217b443c80637ef939e911219f96c179", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "vCbuyV4myYC8AzQmTLz8JriXw1ccls6asqZ7SbsPJqYnL9wngG16TFcq4PeBSfHz", "y": "yK9fQbmdIGYBgWVRP7O1XkJV3NBllkftVeHiYCyuTvvW6uHf4v9j4sdI1KzHQwE5"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04bc26eec95e26c980bc0334264cbcfc26b897c3571c96ce9ab2a67b49bb0f26a6272fdc27806d7a4c572ae0f78149f1f3c8af5f41b99d2066018165513fb3b55e4255dcd0659647ed55e1e2602cae4efbd6eae1dfe2ff63e2c748d4acc7430139", "wx": "00bc26eec95e26c980bc0334264cbcfc26b897c3571c96ce9ab2a67b49bb0f26a6272fdc27806d7a4c572ae0f78149f1f3", "wy": "00c8af5f41b99d2066018165513fb3b55e4255dcd0659647ed55e1e2602cae4efbd6eae1dfe2ff63e2c748d4acc7430139"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004bc26eec95e26c980bc0334264cbcfc26b897c3571c96ce9ab2a67b49bb0f26a6272fdc27806d7a4c572ae0f78149f1f3c8af5f41b99d2066018165513fb3b55e4255dcd0659647ed55e1e2602cae4efbd6eae1dfe2ff63e2c748d4acc7430139", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEvCbuyV4myYC8AzQmTLz8JriXw1ccls6a\nsqZ7SbsPJqYnL9wngG16TFcq4PeBSfHzyK9fQbmdIGYBgWVRP7O1XkJV3NBllkft\nVeHiYCyuTvvW6uHf4v9j4sdI1KzHQwE5\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 192, "comment": "edge case for u2", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffda276276276276276276276276276276276276276276276273d7228d4f84b769be0fd57b97e4c1ebcae9a5f635e80e9df", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "b6CWTdBUJQrxdokcDIIrATtw8FnDRxcsr8azbNFs87D50Z8lmL0NWArBbEassWfU", "y": "N1vvcBwALcwED9VIJLFMwt8BVOsg50Rk4f57gzQm3X1ja_LXlgP93l3aqyOrDPQm"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "046fa0964dd054250af176891c0c822b013b70f059c347172cafc6b36cd16cf3b0f9d19f2598bd0d580ac16c46acb167d4375bef701c002dcc040fd54824b14cc2df0154eb20e74464e1fe7b833426dd7d636bf2d79603fdde5ddaab23ab0cf426", "wx": "6fa0964dd054250af176891c0c822b013b70f059c347172cafc6b36cd16cf3b0f9d19f2598bd0d580ac16c46acb167d4", "wy": "375bef701c002dcc040fd54824b14cc2df0154eb20e74464e1fe7b833426dd7d636bf2d79603fdde5ddaab23ab0cf426"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200046fa0964dd054250af176891c0c822b013b70f059c347172cafc6b36cd16cf3b0f9d19f2598bd0d580ac16c46acb167d4375bef701c002dcc040fd54824b14cc2df0154eb20e74464e1fe7b833426dd7d636bf2d79603fdde5ddaab23ab0cf426", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEb6CWTdBUJQrxdokcDIIrATtw8FnDRxcs\nr8azbNFs87D50Z8lmL0NWArBbEassWfUN1vvcBwALcwED9VIJLFMwt8BVOsg50Rk\n4f57gzQm3X1ja/LXlgP93l3aqyOrDPQm\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 193, "comment": "edge case for u2", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd73333333333333333333333333333333333333333333333316e4d9f42d4eca22df403a0c578b86f0a9a93fe89995c7ed", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "uqTnEu4HhqWrDlpdr9zc-Hs4gwqy7Ib67dqf32UzL2qWiCaUEvBQNWUw1GZKf7jN", "y": "7MRqkBsBbmu4ozatmqbxmr-a2mlwXRyQW-r7laRPUq9D3kv4DAUM-Za3eW387o4b"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04baa4e712ee0786a5ab0e5a5dafdcdcf87b38830ab2ec86faedda9fdf65332f6a9688269412f050356530d4664a7fb8cdecc46a901b016e6bb8a336ad9aa6f19abf9ada69705d1c905beafb95a44f52af43de4bf80c050cf996b7796dfcee8e1b", "wx": "00baa4e712ee0786a5ab0e5a5dafdcdcf87b38830ab2ec86faedda9fdf65332f6a9688269412f050356530d4664a7fb8cd", "wy": "00ecc46a901b016e6bb8a336ad9aa6f19abf9ada69705d1c905beafb95a44f52af43de4bf80c050cf996b7796dfcee8e1b"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004baa4e712ee0786a5ab0e5a5dafdcdcf87b38830ab2ec86faedda9fdf65332f6a9688269412f050356530d4664a7fb8cdecc46a901b016e6bb8a336ad9aa6f19abf9ada69705d1c905beafb95a44f52af43de4bf80c050cf996b7796dfcee8e1b", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEuqTnEu4HhqWrDlpdr9zc+Hs4gwqy7Ib6\n7dqf32UzL2qWiCaUEvBQNWUw1GZKf7jN7MRqkBsBbmu4ozatmqbxmr+a2mlwXRyQ\nW+r7laRPUq9D3kv4DAUM+Za3eW387o4b\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 194, "comment": "edge case for u2", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd7fffffffffffffffffffffffffffffffffffffffffffffffda4233abf824c93f90115e76db206fa7489d6647332e1ba3", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "geeKUq4GlVg_emAaubb7-vQ08r76H4yDPVnetiepJ8L0LUjrYX_gQvWE4QXCPCMX", "y": "zyLVZfXztCXveTffYptoZNrHEmSyiMGphyEPUjBxMZzj9kQRkQrCN2XEJm5hURK8"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0481e78a52ae0695583f7a601ab9b6fbfaf434f2befa1f8c833d59deb627a927c2f42d48eb617fe042f584e105c23c2317cf22d565f5f3b425ef7937df629b6864dac71264b288c1a987210f523071319ce3f64411910ac23765c4266e615112bc", "wx": "0081e78a52ae0695583f7a601ab9b6fbfaf434f2befa1f8c833d59deb627a927c2f42d48eb617fe042f584e105c23c2317", "wy": "00cf22d565f5f3b425ef7937df629b6864dac71264b288c1a987210f523071319ce3f64411910ac23765c4266e615112bc"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000481e78a52ae0695583f7a601ab9b6fbfaf434f2befa1f8c833d59deb627a927c2f42d48eb617fe042f584e105c23c2317cf22d565f5f3b425ef7937df629b6864dac71264b288c1a987210f523071319ce3f64411910ac23765c4266e615112bc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEgeeKUq4GlVg/emAaubb7+vQ08r76H4yD\nPVnetiepJ8L0LUjrYX/gQvWE4QXCPCMXzyLVZfXztCXveTffYptoZNrHEmSyiMGp\nhyEPUjBxMZzj9kQRkQrCN2XEJm5hURK8\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 195, "comment": "edge case for u2", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffd3fffffffffffffffffffffffffffffffffffffffffffffffe3b1a6c0fa1b96efac0d06d9245853bd76760cb5666294bb", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "QfqHZbGdMQgDHijJp4GjhcnBCyv9QuZDflxL1xHPKgMXUIR9F6gvk3ajCuGCptbn", "y": "HCCvljJBR9QVWk0Mhnyo4266IE--0gh-D8vci6q-B7sxI_n3JZ53HNnxrRfRojeH"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0441fa8765b19d3108031e28c9a781a385c9c10b2bfd42e6437e5c4bd711cf2a031750847d17a82f9376a30ae182a6d6e71c20af96324147d4155a4d0c867ca8e36eba204fbed2087e0fcbdc8baabe07bb3123f9f7259e771cd9f1ad17d1a23787", "wx": "41fa8765b19d3108031e28c9a781a385c9c10b2bfd42e6437e5c4bd711cf2a031750847d17a82f9376a30ae182a6d6e7", "wy": "1c20af96324147d4155a4d0c867ca8e36eba204fbed2087e0fcbdc8baabe07bb3123f9f7259e771cd9f1ad17d1a23787"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000441fa8765b19d3108031e28c9a781a385c9c10b2bfd42e6437e5c4bd711cf2a031750847d17a82f9376a30ae182a6d6e71c20af96324147d4155a4d0c867ca8e36eba204fbed2087e0fcbdc8baabe07bb3123f9f7259e771cd9f1ad17d1a23787", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEQfqHZbGdMQgDHijJp4GjhcnBCyv9QuZD\nflxL1xHPKgMXUIR9F6gvk3ajCuGCptbnHCCvljJBR9QVWk0Mhnyo4266IE++0gh+\nD8vci6q+B7sxI/n3JZ53HNnxrRfRojeH\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 196, "comment": "edge case for u2", "msg": "************", "sig": "7ffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffddfea06865526cea11c0f9eb9512b41fa9581d0f6cb7db9680336151dce79de818cdf33c879da322740416d1e5ae532fa", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "5YWgZ9bf83rn8X-BWDEZthKRWXNF8Qes_-I3oI9IhtT9-U_mMYLmFDyZviWnt9hr", "y": "VyweBt0se5S4c_BXj8srmdYOJG5RJF0IBO3USzLw8ADI-PiPHUpl_qUdu7SrHigj"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04e585a067d6dff37ae7f17f81583119b61291597345f107acffe237a08f4886d4fdf94fe63182e6143c99be25a7b7d86b572c1e06dd2c7b94b873f0578fcb2b99d60e246e51245d0804edd44b32f0f000c8f8f88f1d4a65fea51dbbb4ab1e2823", "wx": "00e585a067d6dff37ae7f17f81583119b61291597345f107acffe237a08f4886d4fdf94fe63182e6143c99be25a7b7d86b", "wy": "572c1e06dd2c7b94b873f0578fcb2b99d60e246e51245d0804edd44b32f0f000c8f8f88f1d4a65fea51dbbb4ab1e2823"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004e585a067d6dff37ae7f17f81583119b61291597345f107acffe237a08f4886d4fdf94fe63182e6143c99be25a7b7d86b572c1e06dd2c7b94b873f0578fcb2b99d60e246e51245d0804edd44b32f0f000c8f8f88f1d4a65fea51dbbb4ab1e2823", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE5YWgZ9bf83rn8X+BWDEZthKRWXNF8Qes\n/+I3oI9IhtT9+U/mMYLmFDyZviWnt9hrVyweBt0se5S4c/BXj8srmdYOJG5RJF0I\nBO3USzLw8ADI+PiPHUpl/qUdu7SrHigj\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 197, "comment": "point duplication during verification", "msg": "************", "sig": "b37699e0d518a4d370dbdaaaea3788850fa03f8186d1f78fdfbae6540aa670b31c8ada0fff3e737bd69520560fe0ce6064adb4d51a93f96bed4665de2d4e1169cc95819ec6e9333edfd5c07ca134ceef7c95957b719ae349fc439eaa49fbbe34", "result": "valid", "flags": ["PointDuplication"]}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "5YWgZ9bf83rn8X-BWDEZthKRWXNF8Qes_-I3oI9IhtT9-U_mMYLmFDyZviWnt9hr", "y": "qNPh-SLThGtHjA-ocDTUZinx25Gu26L3-xIrtM0PD_43Bwdv4rWaAVriRExU4dfc"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04e585a067d6dff37ae7f17f81583119b61291597345f107acffe237a08f4886d4fdf94fe63182e6143c99be25a7b7d86ba8d3e1f922d3846b478c0fa87034d46629f1db91aedba2f7fb122bb4cd0f0ffe3707076fe2b59a015ae2444c54e1d7dc", "wx": "00e585a067d6dff37ae7f17f81583119b61291597345f107acffe237a08f4886d4fdf94fe63182e6143c99be25a7b7d86b", "wy": "00a8d3e1f922d3846b478c0fa87034d46629f1db91aedba2f7fb122bb4cd0f0ffe3707076fe2b59a015ae2444c54e1d7dc"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004e585a067d6dff37ae7f17f81583119b61291597345f107acffe237a08f4886d4fdf94fe63182e6143c99be25a7b7d86ba8d3e1f922d3846b478c0fa87034d46629f1db91aedba2f7fb122bb4cd0f0ffe3707076fe2b59a015ae2444c54e1d7dc", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE5YWgZ9bf83rn8X+BWDEZthKRWXNF8Qes\n/+I3oI9IhtT9+U/mMYLmFDyZviWnt9hrqNPh+SLThGtHjA+ocDTUZinx25Gu26L3\n+xIrtM0PD/43Bwdv4rWaAVriRExU4dfc\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 198, "comment": "duplication bug", "msg": "************", "sig": "b37699e0d518a4d370dbdaaaea3788850fa03f8186d1f78fdfbae6540aa670b31c8ada0fff3e737bd69520560fe0ce6064adb4d51a93f96bed4665de2d4e1169cc95819ec6e9333edfd5c07ca134ceef7c95957b719ae349fc439eaa49fbbe34", "result": "invalid", "flags": ["PointDuplication"]}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "tNeMzLztgGXA69wzC0Zw7JkwknPkQrm-NBGWwQQ-REH8V7kUCFWVv8dVxk_ECfC6", "y": "Af7jHLu67VwTI_Cch9-bBxLBLplzP6I--RtObKZmsJ3XVA6_EGihUVW8Bp49WVyM"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04b4d78cccbced8065c0ebdc330b4670ec99309273e442b9be341196c1043e4441fc57b914085595bfc755c64fc409f0ba01fee31cbbbaed5c1323f09c87df9b0712c12e99733fa23ef91b4e6ca666b09dd7540ebf1068a15155bc069e3d595c8c", "wx": "00b4d78cccbced8065c0ebdc330b4670ec99309273e442b9be341196c1043e4441fc57b914085595bfc755c64fc409f0ba", "wy": "01fee31cbbbaed5c1323f09c87df9b0712c12e99733fa23ef91b4e6ca666b09dd7540ebf1068a15155bc069e3d595c8c"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004b4d78cccbced8065c0ebdc330b4670ec99309273e442b9be341196c1043e4441fc57b914085595bfc755c64fc409f0ba01fee31cbbbaed5c1323f09c87df9b0712c12e99733fa23ef91b4e6ca666b09dd7540ebf1068a15155bc069e3d595c8c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEtNeMzLztgGXA69wzC0Zw7JkwknPkQrm+\nNBGWwQQ+REH8V7kUCFWVv8dVxk/ECfC6Af7jHLu67VwTI/Cch9+bBxLBLplzP6I+\n+RtObKZmsJ3XVA6/EGihUVW8Bp49WVyM\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 199, "comment": "point with x-coordinate 0", "msg": "************", "sig": "00000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000155555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "bjxovlOq3oHvieCW2EHihFojMx5-yKaoOdWNB_oBbAlz7XXeT5kXe_3HTbVm6dFa", "y": "SXLqCOV3zh9hwTpsobrR3u8pgu4BooJvACt2nyxGCY07r_BopAXQnKOEDS-v5ORu"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "046e3c68be53aade81ef89e096d841e2845a23331e7ec8a6a839d58d07fa016c0973ed75de4f99177bfdc74db566e9d15a4972ea08e577ce1f61c13a6ca1bad1deef2982ee01a2826f002b769f2c46098d3baff068a405d09ca3840d2fafe4e46e", "wx": "6e3c68be53aade81ef89e096d841e2845a23331e7ec8a6a839d58d07fa016c0973ed75de4f99177bfdc74db566e9d15a", "wy": "4972ea08e577ce1f61c13a6ca1bad1deef2982ee01a2826f002b769f2c46098d3baff068a405d09ca3840d2fafe4e46e"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200046e3c68be53aade81ef89e096d841e2845a23331e7ec8a6a839d58d07fa016c0973ed75de4f99177bfdc74db566e9d15a4972ea08e577ce1f61c13a6ca1bad1deef2982ee01a2826f002b769f2c46098d3baff068a405d09ca3840d2fafe4e46e", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEbjxovlOq3oHvieCW2EHihFojMx5+yKao\nOdWNB/oBbAlz7XXeT5kXe/3HTbVm6dFaSXLqCOV3zh9hwTpsobrR3u8pgu4BooJv\nACt2nyxGCY07r/BopAXQnKOEDS+v5ORu\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 200, "comment": "point with x-coordinate 0", "msg": "************", "sig": "010000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000033333333333333333333333333333333333333333333333327e0a919fda4a2c644d202bd41bcee4bc8fc05155c276eb0", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "sQHNs-uiDhEq27S70stHmmnlkKROqQJjGDKr-rivLDBBs99_FmWyxutTP1RiFxAK", "y": "GmGqmVFXitTwCuFzOaim8TWbvQrDVWeO1N8hM48IdjwdNwLsEytjTHvMARjvsdDd"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04b101cdb3eba20e112adbb4bbd2cb479a69e590a44ea902631832abfab8af2c3041b3df7f1665b2c6eb533f546217100a1a61aa9951578ad4f00ae17339a8a6f1359bbd0ac355678ed4df21338f08763c1d3702ec132b634c7bcc0118efb1d0dd", "wx": "00b101cdb3eba20e112adbb4bbd2cb479a69e590a44ea902631832abfab8af2c3041b3df7f1665b2c6eb533f546217100a", "wy": "1a61aa9951578ad4f00ae17339a8a6f1359bbd0ac355678ed4df21338f08763c1d3702ec132b634c7bcc0118efb1d0dd"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004b101cdb3eba20e112adbb4bbd2cb479a69e590a44ea902631832abfab8af2c3041b3df7f1665b2c6eb533f546217100a1a61aa9951578ad4f00ae17339a8a6f1359bbd0ac355678ed4df21338f08763c1d3702ec132b634c7bcc0118efb1d0dd", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEsQHNs+uiDhEq27S70stHmmnlkKROqQJj\nGDKr+rivLDBBs99/FmWyxutTP1RiFxAKGmGqmVFXitTwCuFzOaim8TWbvQrDVWeO\n1N8hM48IdjwdNwLsEytjTHvMARjvsdDd\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 201, "comment": "comparison with point at infinity ", "msg": "************", "sig": "55555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec632633333333333333333333333333333333333333333333333327e0a919fda4a2c644d202bd41bcee4bc8fc05155c276eb0", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "Z2EESgQKSXnbJptKN35C8RtL4M4kYR9ndnTc93D1iHyk21ZTAyg4CebWX3_GvCc2", "y": "BcfapAP8pTVJ91_zNykJZC0Ct_3KweaCQoFNbpJasBqAg2z7s1WBlgB54vtEwNGG"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "046761044a040a4979db269b4a377e42f11b4be0ce24611f677674dcf770f5887ca4db565303283809e6d65f7fc6bc273605c7daa403fca53549f75ff3372909642d02b7fdcac1e68242814d6e925ab01a80836cfbb35581960079e2fb44c0d186", "wx": "6761044a040a4979db269b4a377e42f11b4be0ce24611f677674dcf770f5887ca4db565303283809e6d65f7fc6bc2736", "wy": "05c7daa403fca53549f75ff3372909642d02b7fdcac1e68242814d6e925ab01a80836cfbb35581960079e2fb44c0d186"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200046761044a040a4979db269b4a377e42f11b4be0ce24611f677674dcf770f5887ca4db565303283809e6d65f7fc6bc273605c7daa403fca53549f75ff3372909642d02b7fdcac1e68242814d6e925ab01a80836cfbb35581960079e2fb44c0d186", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEZ2EESgQKSXnbJptKN35C8RtL4M4kYR9n\ndnTc93D1iHyk21ZTAyg4CebWX3/GvCc2BcfapAP8pTVJ91/zNykJZC0Ct/3KweaC\nQoFNbpJasBqAg2z7s1WBlgB54vtEwNGG\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 202, "comment": "extreme value for k and edgecase s", "msg": "************", "sig": "08d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df6155555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "aSLFkVAvAQRv7lYXvxZJb1g5iCLmmvqDNTCPNsCajtQ3IJ_vz_u98KSHazWjx6sm", "y": "VYVNuCW5Sz8n5fiS07u2xyQOySKJTdNZjpH8xhNKK4_RVOF5BGaQYgbw9iNBbmOh"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "046922c591502f01046fee5617bf16496f58398822e69afa8335308f36c09a8ed437209fefcffbbdf0a4876b35a3c7ab2655854db825b94b3f27e5f892d3bbb6c7240ec922894dd3598e91fcc6134a2b8fd154e1790466906206f0f623416e63a1", "wx": "6922c591502f01046fee5617bf16496f58398822e69afa8335308f36c09a8ed437209fefcffbbdf0a4876b35a3c7ab26", "wy": "55854db825b94b3f27e5f892d3bbb6c7240ec922894dd3598e91fcc6134a2b8fd154e1790466906206f0f623416e63a1"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200046922c591502f01046fee5617bf16496f58398822e69afa8335308f36c09a8ed437209fefcffbbdf0a4876b35a3c7ab2655854db825b94b3f27e5f892d3bbb6c7240ec922894dd3598e91fcc6134a2b8fd154e1790466906206f0f623416e63a1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEaSLFkVAvAQRv7lYXvxZJb1g5iCLmmvqD\nNTCPNsCajtQ3IJ/vz/u98KSHazWjx6smVYVNuCW5Sz8n5fiS07u2xyQOySKJTdNZ\njpH8xhNKK4/RVOF5BGaQYgbw9iNBbmOh\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 203, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "08d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df612492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "iS2sDnAPwp0YAtmkSab1ayFyyxt9iBATzTsxwO2wUvLTQMiZWkR3vLkiX-wVZnIz", "y": "zGw0rhdEVERRb9j9Iu6D956wdx6_9md6xdTgifh6HHLflXrLJEkq3NfDgWuODHWx"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04892dac0e700fc29d1802d9a449a6f56b2172cb1b7d881013cd3b31c0edb052f2d340c8995a4477bcb9225fec15667233cc6c34ae17445444516fd8fd22ee83f79eb0771ebff6677ac5d4e089f87a1c72df957acb24492adcd7c3816b8e0c75b1", "wx": "00892dac0e700fc29d1802d9a449a6f56b2172cb1b7d881013cd3b31c0edb052f2d340c8995a4477bcb9225fec15667233", "wy": "00cc6c34ae17445444516fd8fd22ee83f79eb0771ebff6677ac5d4e089f87a1c72df957acb24492adcd7c3816b8e0c75b1"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004892dac0e700fc29d1802d9a449a6f56b2172cb1b7d881013cd3b31c0edb052f2d340c8995a4477bcb9225fec15667233cc6c34ae17445444516fd8fd22ee83f79eb0771ebff6677ac5d4e089f87a1c72df957acb24492adcd7c3816b8e0c75b1", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEiS2sDnAPwp0YAtmkSab1ayFyyxt9iBAT\nzTsxwO2wUvLTQMiZWkR3vLkiX+wVZnIzzGw0rhdEVERRb9j9Iu6D956wdx6/9md6\nxdTgifh6HHLflXrLJEkq3NfDgWuODHWx\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 204, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "08d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df616666666666666666666666666666666666666666666666664fc15233fb49458c89a4057a8379dc9791f80a2ab84edd61", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "AWNBF-ZHjOBWiwokaSN7usb_CWrLflFAcr93Ejy1G6DMPo1pKE1TTY5tHods7PIi", "y": "MeXvBNyWdizn1e8zSK0eJBrHl647Yw6iSa_FE5r0m472izL4Eta1FCEDY9SY78KM"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0401634117e6478ce0568b0a2469237bbac6ff096acb7e514072bf77123cb51ba0cc3e8d69284d534d8e6d1e876cecf22231e5ef04dc96762ce7d5ef3348ad1e241ac797ae3b630ea249afc5139af49b8ef68b32f812d6b514210363d498efc28c", "wx": "01634117e6478ce0568b0a2469237bbac6ff096acb7e514072bf77123cb51ba0cc3e8d69284d534d8e6d1e876cecf222", "wy": "31e5ef04dc96762ce7d5ef3348ad1e241ac797ae3b630ea249afc5139af49b8ef68b32f812d6b514210363d498efc28c"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000401634117e6478ce0568b0a2469237bbac6ff096acb7e514072bf77123cb51ba0cc3e8d69284d534d8e6d1e876cecf22231e5ef04dc96762ce7d5ef3348ad1e241ac797ae3b630ea249afc5139af49b8ef68b32f812d6b514210363d498efc28c", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEAWNBF+ZHjOBWiwokaSN7usb/CWrLflFA\ncr93Ejy1G6DMPo1pKE1TTY5tHods7PIiMeXvBNyWdizn1e8zSK0eJBrHl647Yw6i\nSa/FE5r0m472izL4Eta1FCEDY9SY78KM\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 205, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "08d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df6199999999999999999999999999999999999999999999999977a1fb4df8ede852ce760837c536cae35af40f4014764c12", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "Z1vcedgkOIf-GzBdEqwQ0unAveBwpuM5TNX2rfvO2nVJiw56eUxyEvQr6T9hZ0RW", "y": "PpbRv2-Vzb76d0kRugZGPYqQoMnXPJaZsGHXedxSSW6O6bmunF1NkOic0RV9gRiV"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04675bdc79d8243887fe1b305d12ac10d2e9c0bde070a6e3394cd5f6adfbceda75498b0e7a794c7212f42be93f616744563e96d1bf6f95cdbefa774911ba06463d8a90a0c9d73c9699b061d779dc52496e8ee9b9ae9c5d4d90e89cd1157d811895", "wx": "675bdc79d8243887fe1b305d12ac10d2e9c0bde070a6e3394cd5f6adfbceda75498b0e7a794c7212f42be93f61674456", "wy": "3e96d1bf6f95cdbefa774911ba06463d8a90a0c9d73c9699b061d779dc52496e8ee9b9ae9c5d4d90e89cd1157d811895"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004675bdc79d8243887fe1b305d12ac10d2e9c0bde070a6e3394cd5f6adfbceda75498b0e7a794c7212f42be93f616744563e96d1bf6f95cdbefa774911ba06463d8a90a0c9d73c9699b061d779dc52496e8ee9b9ae9c5d4d90e89cd1157d811895", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEZ1vcedgkOIf+GzBdEqwQ0unAveBwpuM5\nTNX2rfvO2nVJiw56eUxyEvQr6T9hZ0RWPpbRv2+Vzb76d0kRugZGPYqQoMnXPJaZ\nsGHXedxSSW6O6bmunF1NkOic0RV9gRiV\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 206, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "08d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df61db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6aae76701acc1950894a89e068772d8b281eef136f8a8fef5", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "D9GquJ9HtWW4Fg38xDO2QIresUc8A2smt93scU-00OfddWyIRp6G4hiBPq2OjnZ2", "y": "8cyVXEE54AccAHnsHXcWTgVpvfRTg36LM8mFNaDnycYe8kdiBnu0a2EW6nkJppsj"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "040fd1aab89f47b565b8160dfcc433b6408adeb1473c036b26b7ddec714fb4d0e7dd756c88469e86e218813ead8e8e7676f1cc955c4139e0071c0079ec1d77164e0569bdf453837e8b33c98535a0e7c9c61ef24762067bb46b6116ea7909a69b23", "wx": "0fd1aab89f47b565b8160dfcc433b6408adeb1473c036b26b7ddec714fb4d0e7dd756c88469e86e218813ead8e8e7676", "wy": "00f1cc955c4139e0071c0079ec1d77164e0569bdf453837e8b33c98535a0e7c9c61ef24762067bb46b6116ea7909a69b23"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200040fd1aab89f47b565b8160dfcc433b6408adeb1473c036b26b7ddec714fb4d0e7dd756c88469e86e218813ead8e8e7676f1cc955c4139e0071c0079ec1d77164e0569bdf453837e8b33c98535a0e7c9c61ef24762067bb46b6116ea7909a69b23", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAED9GquJ9HtWW4Fg38xDO2QIresUc8A2sm\nt93scU+00OfddWyIRp6G4hiBPq2OjnZ28cyVXEE54AccAHnsHXcWTgVpvfRTg36L\nM8mFNaDnycYe8kdiBnu0a2EW6nkJppsj\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 207, "comment": "extreme value for k", "msg": "************", "sig": "08d999057ba3d2d969260045c55b97f089025959a6f434d651d207d19fb96e9e4fe0e86ebe0e64f85b96a9c75295df610eb10e5ab95f2f26a40700b1300fb8c3e754d5c453d9384ecce1daa38135a48a0a96c24efc2a76d00bde1d7aeedf7f6a", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "NNdOwIi6tsYyOWjR9GiZOBL2kNbtyluXYE1xjhK4zf3ZbULlfTOv4xLw7jw9ChP3", "y": "hvSSK7LBO993UqPstpOT6Ze9ZUYcRoZ-vu9ilrI_LFbfY6z95kjz9QAtvCOf_RWC"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0434d74ec088bab6c6323968d1f468993812f690d6edca5b97604d718e12b8cdfdd96d42e57d33afe312f0ee3c3d0a13f786f4922bb2c13bdf7752a3ecb69393e997bd65461c46867ebeef6296b23f2c56df63acfde648f3f5002dbc239ffd1582", "wx": "34d74ec088bab6c6323968d1f468993812f690d6edca5b97604d718e12b8cdfdd96d42e57d33afe312f0ee3c3d0a13f7", "wy": "0086f4922bb2c13bdf7752a3ecb69393e997bd65461c46867ebeef6296b23f2c56df63acfde648f3f5002dbc239ffd1582"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000434d74ec088bab6c6323968d1f468993812f690d6edca5b97604d718e12b8cdfdd96d42e57d33afe312f0ee3c3d0a13f786f4922bb2c13bdf7752a3ecb69393e997bd65461c46867ebeef6296b23f2c56df63acfde648f3f5002dbc239ffd1582", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAENNdOwIi6tsYyOWjR9GiZOBL2kNbtyluX\nYE1xjhK4zf3ZbULlfTOv4xLw7jw9ChP3hvSSK7LBO993UqPstpOT6Ze9ZUYcRoZ+\nvu9ilrI/LFbfY6z95kjz9QAtvCOf/RWC\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 208, "comment": "extreme value for k and edgecase s", "msg": "************", "sig": "aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab755555555555555555555555555555555555555555555555542766f2b5167b9f51d5e0490c2e58d28f9a40878eeec6326", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "Q3bJiT6SdylsdmqDq742s02npjH4y_0yoYiN4N0UVaIaFT6i1hz6UHH8a-EqZY9r", "y": "KQuhqO6MeLXdWPn_yssilVaC7qAkKcP6jNy2SfpNAHyGk-P488Cl88TeelG-qpgJ"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "044376c9893e9277296c766a83abbe36b34da7a631f8cbfd32a1888de0dd1455a21a153ea2d61cfa5071fc6be12a658f6b290ba1a8ee8c78b5dd58f9ffcacb22955682eea02429c3fa8cdcb649fa4d007c8693e3f8f3c0a5f3c4de7a51beaa9809", "wx": "4376c9893e9277296c766a83abbe36b34da7a631f8cbfd32a1888de0dd1455a21a153ea2d61cfa5071fc6be12a658f6b", "wy": "290ba1a8ee8c78b5dd58f9ffcacb22955682eea02429c3fa8cdcb649fa4d007c8693e3f8f3c0a5f3c4de7a51beaa9809"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200044376c9893e9277296c766a83abbe36b34da7a631f8cbfd32a1888de0dd1455a21a153ea2d61cfa5071fc6be12a658f6b290ba1a8ee8c78b5dd58f9ffcacb22955682eea02429c3fa8cdcb649fa4d007c8693e3f8f3c0a5f3c4de7a51beaa9809", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEQ3bJiT6SdylsdmqDq742s02npjH4y/0y\noYiN4N0UVaIaFT6i1hz6UHH8a+EqZY9rKQuhqO6MeLXdWPn/yssilVaC7qAkKcP6\njNy2SfpNAHyGk+P488Cl88TeelG+qpgJ\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 209, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab72492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "EIePxIB_ZzKiPIg-g444x4f3CI-UwYJLhGc-i56rFt4VRK5L8sb-P-T7NDt0h-K0", "y": "MDb_Q5Uz0i-VHa6WZYS6-yOyF9ytL49ODmmZwMTQ8HZjS-gF9nb9KlnCf5_nxdlb"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0410878fc4807f6732a23c883e838e38c787f7088f94c1824b84673e8b9eab16de1544ae4bf2c6fe3fe4fb343b7487e2b43036ff439533d22f951dae966584bafb23b217dcad2f8f4e0e6999c0c4d0f076634be805f676fd2a59c27f9fe7c5d95b", "wx": "10878fc4807f6732a23c883e838e38c787f7088f94c1824b84673e8b9eab16de1544ae4bf2c6fe3fe4fb343b7487e2b4", "wy": "3036ff439533d22f951dae966584bafb23b217dcad2f8f4e0e6999c0c4d0f076634be805f676fd2a59c27f9fe7c5d95b"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000410878fc4807f6732a23c883e838e38c787f7088f94c1824b84673e8b9eab16de1544ae4bf2c6fe3fe4fb343b7487e2b43036ff439533d22f951dae966584bafb23b217dcad2f8f4e0e6999c0c4d0f076634be805f676fd2a59c27f9fe7c5d95b", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEEIePxIB/ZzKiPIg+g444x4f3CI+UwYJL\nhGc+i56rFt4VRK5L8sb+P+T7NDt0h+K0MDb/Q5Uz0i+VHa6WZYS6+yOyF9ytL49O\nDmmZwMTQ8HZjS+gF9nb9KlnCf5/nxdlb\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 210, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab76666666666666666666666666666666666666666666666664fc15233fb49458c89a4057a8379dc9791f80a2ab84edd61", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "A2slPjtKyIu4WForMrl4dmqTHlrQ0OZTouNLRNbdzA04biDE3vLYuz-NoSjB6saf", "y": "nI47X_Xd4iBa81mzl01SdY16uugSuLJ14UUsTlnLYum2dx00fb0d6nYccCkcxeCm"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04036b253e3b4ac88bb8585a2b32b978766a931e5ad0d0e653a2e34b44d6ddcc0d386e20c4def2d8bb3f8da128c1eac69f9c8e3b5ff5dde2205af359b3974d52758d7abae812b8b275e1452c4e59cb62e9b6771d347dbd1dea761c70291cc5e0a6", "wx": "036b253e3b4ac88bb8585a2b32b978766a931e5ad0d0e653a2e34b44d6ddcc0d386e20c4def2d8bb3f8da128c1eac69f", "wy": "009c8e3b5ff5dde2205af359b3974d52758d7abae812b8b275e1452c4e59cb62e9b6771d347dbd1dea761c70291cc5e0a6"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004036b253e3b4ac88bb8585a2b32b978766a931e5ad0d0e653a2e34b44d6ddcc0d386e20c4def2d8bb3f8da128c1eac69f9c8e3b5ff5dde2205af359b3974d52758d7abae812b8b275e1452c4e59cb62e9b6771d347dbd1dea761c70291cc5e0a6", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEA2slPjtKyIu4WForMrl4dmqTHlrQ0OZT\nouNLRNbdzA04biDE3vLYuz+NoSjB6safnI47X/Xd4iBa81mzl01SdY16uugSuLJ1\n4UUsTlnLYum2dx00fb0d6nYccCkcxeCm\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 211, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab799999999999999999999999999999999999999999999999977a1fb4df8ede852ce760837c536cae35af40f4014764c12", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "J4PBvpIvzhVYZOy0HQoxbhk6VYQ-gBkvH-VWdy8969BLn8k8J7xvNTk4iGpARBmU", "y": "GjUs7DNpRkJPo8II6nEF9VSe3ehoir0wU0S_T2bdp-q82m-FV8mviBCYBNcC6WcL"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "042783c1be922fce155864ecb41d0a316e193a55843e80192f1fe556772f3debd04b9fc93c27bc6f353938886a404419941a352cec336946424fa3c208ea7105f5549edde8688abd305344bf4f66dda7eabcda6f8557c9af88109804d702e9670b", "wx": "2783c1be922fce155864ecb41d0a316e193a55843e80192f1fe556772f3debd04b9fc93c27bc6f353938886a40441994", "wy": "1a352cec336946424fa3c208ea7105f5549edde8688abd305344bf4f66dda7eabcda6f8557c9af88109804d702e9670b"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200042783c1be922fce155864ecb41d0a316e193a55843e80192f1fe556772f3debd04b9fc93c27bc6f353938886a404419941a352cec336946424fa3c208ea7105f5549edde8688abd305344bf4f66dda7eabcda6f8557c9af88109804d702e9670b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEJ4PBvpIvzhVYZOy0HQoxbhk6VYQ+gBkv\nH+VWdy8969BLn8k8J7xvNTk4iGpARBmUGjUs7DNpRkJPo8II6nEF9VSe3ehoir0w\nU0S/T2bdp+q82m+FV8mviBCYBNcC6WcL\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 212, "comment": "extreme value for k and s^-1", "msg": "************", "sig": "aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6db6aae76701acc1950894a89e068772d8b281eef136f8a8fef5", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "-pJTjNx0A2jK8WSA_xMEzru-WaRqeoRgNya5WS0QW-Bp3xxhtZdPJ-dVL3l96Xzb", "y": "Yg4DpG2oYuSwibr7uA348FXI9HmRs6PdsrCJrtsvFYQaalteFMHcNrPBVcT3TTQJ"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04fa92538cdc740368caf16480ff1304cebbbe59a46a7a84603726b9592d105be069df1c61b5974f27e7552f797de97cdb620e03a46da862e4b089bafbb80df8f055c8f47991b3a3ddb2b089aedb2f15841a6a5b5e14c1dc36b3c155c4f74d3409", "wx": "00fa92538cdc740368caf16480ff1304cebbbe59a46a7a84603726b9592d105be069df1c61b5974f27e7552f797de97cdb", "wy": "620e03a46da862e4b089bafbb80df8f055c8f47991b3a3ddb2b089aedb2f15841a6a5b5e14c1dc36b3c155c4f74d3409"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004fa92538cdc740368caf16480ff1304cebbbe59a46a7a84603726b9592d105be069df1c61b5974f27e7552f797de97cdb620e03a46da862e4b089bafbb80df8f055c8f47991b3a3ddb2b089aedb2f15841a6a5b5e14c1dc36b3c155c4f74d3409", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE+pJTjNx0A2jK8WSA/xMEzru+WaRqeoRg\nNya5WS0QW+Bp3xxhtZdPJ+dVL3l96XzbYg4DpG2oYuSwibr7uA348FXI9HmRs6Pd\nsrCJrtsvFYQaalteFMHcNrPBVcT3TTQJ\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 213, "comment": "extreme value for k", "msg": "************", "sig": "aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab70eb10e5ab95f2f26a40700b1300fb8c3e754d5c453d9384ecce1daa38135a48a0a96c24efc2a76d00bde1d7aeedf7f6a", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "qofKIr6LBTeOscce8yCtdG4dO2KLp5uYWfdB4IJUKjhVAvJdv1UpbDpUXjhydgq3", "y": "NhfeSpYmLG9dnpi_kpLcKfj0Hb0omhR86doxE7XwuMAKYLHOHX6BnXpDHXyQ6g5f"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab73617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f", "wx": "00aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7", "wy": "3617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab73617de4a96262c6f5d9e98bf9292dc29f8f41dbd289a147ce9da3113b5f0b8c00a60b1ce1d7e819d7a431d7c90ea0e5f", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEqofKIr6LBTeOscce8yCtdG4dO2KLp5uY\nWfdB4IJUKjhVAvJdv1UpbDpUXjhydgq3NhfeSpYmLG9dnpi/kpLcKfj0Hb0omhR8\n6doxE7XwuMAKYLHOHX6BnXpDHXyQ6g5f\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 214, "comment": "testing point duplication", "msg": "************", "sig": "f9b127f0d81ebcd17b7ba0ea131c660d340b05ce557c82160e0f793de07d38179023942871acb7002dfafdfffc8deace2492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "invalid", "flags": []}, {"tcId": 215, "comment": "testing point duplication", "msg": "************", "sig": "064ed80f27e1432e84845f15ece399f2cbf4fa31aa837de9b953d44413b9f5c7c7f67989d703f07abef11b6ad0373ea52492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "qofKIr6LBTeOscce8yCtdG4dO2KLp5uYWfdB4IJUKjhVAvJdv1UpbDpUXjhydgq3", "y": "yeghtWnZ05CiYWdAbW0j1gcL4kLXZeuDFiXO7EoPRz71n04w4oF-YoW84oRvFfGg"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7c9e821b569d9d390a26167406d6d23d6070be242d765eb831625ceec4a0f473ef59f4e30e2817e6285bce2846f15f1a0", "wx": "00aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7", "wy": "00c9e821b569d9d390a26167406d6d23d6070be242d765eb831625ceec4a0f473ef59f4e30e2817e6285bce2846f15f1a0"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004aa87ca22be8b05378eb1c71ef320ad746e1d3b628ba79b9859f741e082542a385502f25dbf55296c3a545e3872760ab7c9e821b569d9d390a26167406d6d23d6070be242d765eb831625ceec4a0f473ef59f4e30e2817e6285bce2846f15f1a0", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEqofKIr6LBTeOscce8yCtdG4dO2KLp5uY\nWfdB4IJUKjhVAvJdv1UpbDpUXjhydgq3yeghtWnZ05CiYWdAbW0j1gcL4kLXZeuD\nFiXO7EoPRz71n04w4oF+YoW84oRvFfGg\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 216, "comment": "testing point duplication", "msg": "************", "sig": "f9b127f0d81ebcd17b7ba0ea131c660d340b05ce557c82160e0f793de07d38179023942871acb7002dfafdfffc8deace2492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "invalid", "flags": []}, {"tcId": 217, "comment": "testing point duplication", "msg": "************", "sig": "064ed80f27e1432e84845f15ece399f2cbf4fa31aa837de9b953d44413b9f5c7c7f67989d703f07abef11b6ad0373ea52492492492492492492492492492492492492492492492491c7be680477598d6c3716fabc13dcec86afd2833d41c2a7e", "result": "invalid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "Kb23bV-nQb_XAjPLOmbMfUS-s7BmPZKoE2ZQR4vO-2HvGC4VWlQ0Wl6OXojwZOW8", "y": "mlJat_dk2tPa4UaMK0GfO2K5upF9XoxPsexHQEo_x2R0snEwgb6dtMAOBDran8Sj"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "0429bdb76d5fa741bfd70233cb3a66cc7d44beb3b0663d92a8136650478bcefb61ef182e155a54345a5e8e5e88f064e5bc9a525ab7f764dad3dae1468c2b419f3b62b9ba917d5e8c4fb1ec47404a3fc76474b2713081be9db4c00e043ada9fc4a3", "wx": "29bdb76d5fa741bfd70233cb3a66cc7d44beb3b0663d92a8136650478bcefb61ef182e155a54345a5e8e5e88f064e5bc", "wy": "009a525ab7f764dad3dae1468c2b419f3b62b9ba917d5e8c4fb1ec47404a3fc76474b2713081be9db4c00e043ada9fc4a3"}, "keyDer": "3076301006072a8648ce3d020106052b810400220362000429bdb76d5fa741bfd70233cb3a66cc7d44beb3b0663d92a8136650478bcefb61ef182e155a54345a5e8e5e88f064e5bc9a525ab7f764dad3dae1468c2b419f3b62b9ba917d5e8c4fb1ec47404a3fc76474b2713081be9db4c00e043ada9fc4a3", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEKb23bV+nQb/XAjPLOmbMfUS+s7BmPZKo\nE2ZQR4vO+2HvGC4VWlQ0Wl6OXojwZOW8mlJat/dk2tPa4UaMK0GfO2K5upF9XoxP\nsexHQEo/x2R0snEwgb6dtMAOBDran8Sj\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 218, "comment": "pseudorandom signature", "msg": "", "sig": "32401249714e9091f05a5e109d5c1216fdc05e98614261aa0dbd9e9cd4415dee29238afbd3b103c1e40ee5c9144aee0f4326756fb2c4fd726360dd6479b5849478c7a9d054a833a58c1631c33b63c3441336ddf2c7fe0ed129aae6d4ddfeb753", "result": "valid", "flags": []}, {"tcId": 219, "comment": "pseudorandom signature", "msg": "4d7367", "sig": "d7143a836608b25599a7f28dec6635494c2992ad1e2bbeecb7ef601a9c01746e710ce0d9c48accb38a79ede5b9638f3480f9e165e8c61035bf8aa7b5533960e46dd0e211c904a064edb6de41f797c0eae4e327612ee3f816f4157272bb4fabc9", "result": "valid", "flags": []}, {"tcId": 220, "comment": "pseudorandom signature", "msg": "************", "sig": "234503fcca578121986d96be07fbc8da5d894ed8588c6dbcdbe974b4b813b21c52d20a8928f2e2fdac14705b0705498ccd7b9b766b97b53d1a80fc0b760af16a11bf4a59c7c367c6c7275dfb6e18a88091eed3734bf5cf41b3dc6fecd6d3baaf", "result": "valid", "flags": []}, {"tcId": 221, "comment": "pseudorandom signature", "msg": "0000000000000000000000000000000000000000", "sig": "5cad9ae1565f2588f86d821c2cc1b4d0fdf874331326568f5b0e130e4e0c0ec497f8f5f564212bd2a26ecb782cf0a18dbf2e9d0980fbb00696673e7fbb03e1f854b9d7596b759a17bf6e6e67a95ea6c1664f82dc449ae5ea779abd99c78e6840", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "_____6pj8aI5rHAZfG6_zqV1bcASEj-CxR-odNZgKL4A6XahCAYGc3zHXEC9_kqs", "y": "rL2FOJCIpipjmDhMIrUtSS8j9G5KJ6RyStVVUdpcSDQ4CVokfLDDN48fUsNCX_nx"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04ffffffffaa63f1a239ac70197c6ebfcea5756dc012123f82c51fa874d66028be00e976a1080606737cc75c40bdfe4aacacbd85389088a62a6398384c22b52d492f23f46e4a27a4724ad55551da5c483438095a247cb0c3378f1f52c3425ff9f1", "wx": "00ffffffffaa63f1a239ac70197c6ebfcea5756dc012123f82c51fa874d66028be00e976a1080606737cc75c40bdfe4aac", "wy": "00acbd85389088a62a6398384c22b52d492f23f46e4a27a4724ad55551da5c483438095a247cb0c3378f1f52c3425ff9f1"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004ffffffffaa63f1a239ac70197c6ebfcea5756dc012123f82c51fa874d66028be00e976a1080606737cc75c40bdfe4aacacbd85389088a62a6398384c22b52d492f23f46e4a27a4724ad55551da5c483438095a247cb0c3378f1f52c3425ff9f1", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHY<PERSON>EAYHKoZIzj0CAQYFK4EEACIDYgAE/////6pj8aI5rHAZfG6/zqV1bcASEj+C\nxR+odNZgKL4A6XahCAYGc3zHXEC9/kqsrL2FOJCIpipjmDhMIrUtSS8j9G5KJ6Ry\nStVVUdpcSDQ4CVokfLDDN48fUsNCX/nx\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 222, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "07648b6660d01ba2520a09d298adf3b1a02c32744bd2877208f5a4162f6c984373139d800a4cdc1ffea15bce4871a0ed99fd367012cb9e02cde2749455e0d495c52818f3c14f6e6aad105b0925e2a7290ac4a06d9fadf4b15b578556fe332a5f", "result": "valid", "flags": []}, {"tcId": 223, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "a049dcd96c72e4f36144a51bba30417b451a305dd01c9e30a5e04df94342617dc383f17727708e3277cd7246ca4407413970e264d85b228bf9e9b9c4947c5dd041ea8b5bde30b93aa59fedf2c428d3e2540a54e0530688acccb83ac7b29b79a2", "result": "valid", "flags": []}, {"tcId": 224, "comment": "x-coordinate of the public key is large", "msg": "4d657373616765", "sig": "441800ea9377c27865be000ad008eb3d7502bdd105824b26d15cf3d06452969a9d0607a915a8fe989215fc4d61af6e05dce29faa5137f75ad77e03918c8ee6747cc7a39b0a69f8b915654cac4cf4bfd9c87cc46ae1631b5c6baebd4fc08ff8fd", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "0YJ_xvbxLyGZLFpAmgZTsSHS7wKysKsBqRYc6VYoB0Cx41ayVXAbCm3cnsLKipQi", "y": "xu1dLO2NirdWD6W7iMc450VBiD2KKxwOK6fjbQMPxNm_uLIvJNuJfrrEndQAAAAA"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04d1827fc6f6f12f21992c5a409a0653b121d2ef02b2b0ab01a9161ce956280740b1e356b255701b0a6ddc9ec2ca8a9422c6ed5d2ced8d8ab7560fa5bb88c738e74541883d8a2b1c0e2ba7e36d030fc4d9bfb8b22f24db897ebac49dd400000000", "wx": "00d1827fc6f6f12f21992c5a409a0653b121d2ef02b2b0ab01a9161ce956280740b1e356b255701b0a6ddc9ec2ca8a9422", "wy": "00c6ed5d2ced8d8ab7560fa5bb88c738e74541883d8a2b1c0e2ba7e36d030fc4d9bfb8b22f24db897ebac49dd400000000"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004d1827fc6f6f12f21992c5a409a0653b121d2ef02b2b0ab01a9161ce956280740b1e356b255701b0a6ddc9ec2ca8a9422c6ed5d2ced8d8ab7560fa5bb88c738e74541883d8a2b1c0e2ba7e36d030fc4d9bfb8b22f24db897ebac49dd400000000", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE0YJ/xvbxLyGZLFpAmgZTsSHS7wKysKsB\nqRYc6VYoB0Cx41ayVXAbCm3cnsLKipQixu1dLO2NirdWD6W7iMc450VBiD2KKxwO\nK6fjbQMPxNm/uLIvJNuJfrrEndQAAAAA\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 225, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "3244768016457c463b74f2097f216d9670b191f76281c74bc6a1a1971d19f209bf4696468f5eb75d6326a0a43c0a6529501e0ad985ed9f95697bd17fdbe3f9ca92e0f76426d3664e6896648d9c750bf588d0ce7d011c1a1e8d6c2e082422dc93", "result": "valid", "flags": []}, {"tcId": 226, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "5e1af40f2480e3d97c4ae4bfd34a9f45269241356f3a46becd86a4a7c9716d73ca5aebdb3db1a7765650666683bc856b7e7c4b473a2baaa4953785be8aa2a10006f6d36b400ab981864d69cecec046718d0404b9647454b159aa5a92d76d7955", "result": "valid", "flags": []}, {"tcId": 227, "comment": "y-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "6688e36a26f15bdc1c3f91367f8a7667f7bb3e30a335d6f0900e9534eb88b260cb29344c723fedfbe7ac9c5a33f4bf0daa35fddf0fdc9017860b378f801cd806f3e2d754cd2fd94eb7bb36a46ce828cef87e9ebbf447068e630b87fee385ad8f", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "EJm7RRAPVfWoXMo94rO9XiUPT2-tZjGjFWwuUqM9fWFd0nn3n4tLr_fHE6wAAAAA", "y": "5sm3NqiSny7Xvgx1OlTLtIuEaeBBHq-TpKgkWboLaBu6j1-zg7SQbUkBozA-LxVX"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "041099bb45100f55f5a85cca3de2b3bd5e250f4f6fad6631a3156c2e52a33d7d615dd279f79f8b4baff7c713ac00000000e6c9b736a8929f2ed7be0c753a54cbb48b8469e0411eaf93a4a82459ba0b681bba8f5fb383b4906d4901a3303e2f1557", "wx": "1099bb45100f55f5a85cca3de2b3bd5e250f4f6fad6631a3156c2e52a33d7d615dd279f79f8b4baff7c713ac00000000", "wy": "00e6c9b736a8929f2ed7be0c753a54cbb48b8469e0411eaf93a4a82459ba0b681bba8f5fb383b4906d4901a3303e2f1557"}, "keyDer": "3076301006072a8648ce3d020106052b81040022036200041099bb45100f55f5a85cca3de2b3bd5e250f4f6fad6631a3156c2e52a33d7d615dd279f79f8b4baff7c713ac00000000e6c9b736a8929f2ed7be0c753a54cbb48b8469e0411eaf93a4a82459ba0b681bba8f5fb383b4906d4901a3303e2f1557", "keyPem": "-----B<PERSON>IN PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEEJm7RRAPVfWoXMo94rO9XiUPT2+tZjGj\nFWwuUqM9fWFd0nn3n4tLr/fHE6wAAAAA5sm3NqiSny7Xvgx1OlTLtIuEaeBBHq+T\npKgkWboLaBu6j1+zg7SQbUkBozA+LxVX\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 228, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "d4a8f3b0b4d3a5769e3a0bbc644b35f1d509355ed1fe401e170f667b661f693b32598e8c143a817a958982845042bb4804cc07578bbd1981dbf6e8a97a354c98d41b8b6f6e8a2c2b1763c7c2a29d79e24f8476075c9aed9aec6c64dff50461ae", "result": "valid", "flags": []}, {"tcId": 229, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "c286d1928e9c79fdd3bebdf22a1dbd37c8105e8ecf41e9e3777fe341b6b8d5a89b9d986827d6d1dbb381cd8239484a22201119ae305b9360aa9b5e5d1567e0674c09e4f025556ebf81b987466b0f421b8d31f72bbe95f3ce2aa9874a84edfd40", "result": "valid", "flags": []}, {"tcId": 230, "comment": "x-coordinate of the public key has many trailing 0's", "msg": "4d657373616765", "sig": "d9c678550167f10c511e62acb4bd0a3f7f336bc090c94e6c6b02622439c348a2159c5f41f9b5aa4b470590d40dcd7cc21fd5eaee295abb4081cb626745f4ad279ceb44604062830b58e6c0465c562d41f02ba588fc0db1ebbe339cdc008d7a1b", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "AAAAACsInt11QWkBAUXyY_M0_BZ8wZ2ugiWXCuGcyMt-xzWT1qRlw3D1R4sOU51p", "y": "0ZUdWXtWpnNFrLJYCVgfB80Ot42VOKP4pl8wDmih63hQffdt5lDo-O5jpfDFaHyY"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04000000002b089edd754169010145f263f334fc167cc19dae8225970ae19cc8cb7ec73593d6a465c370f5478b0e539d69d1951d597b56a67345acb25809581f07cd0eb78d9538a3f8a65f300e68a1eb78507df76de650e8f8ee63a5f0c5687c98", "wx": "2b089edd754169010145f263f334fc167cc19dae8225970ae19cc8cb7ec73593d6a465c370f5478b0e539d69", "wy": "00d1951d597b56a67345acb25809581f07cd0eb78d9538a3f8a65f300e68a1eb78507df76de650e8f8ee63a5f0c5687c98"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004000000002b089edd754169010145f263f334fc167cc19dae8225970ae19cc8cb7ec73593d6a465c370f5478b0e539d69d1951d597b56a67345acb25809581f07cd0eb78d9538a3f8a65f300e68a1eb78507df76de650e8f8ee63a5f0c5687c98", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAEAAAAACsInt11QWkBAUXyY/M0/BZ8wZ2u\ngiWXCuGcyMt+xzWT1qRlw3D1R4sOU51p0ZUdWXtWpnNFrLJYCVgfB80Ot42VOKP4\npl8wDmih63hQffdt5lDo+O5jpfDFaHyY\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 231, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "20fee7c71b6cb0d1da3641ec6622c055a3b16a1f596c64b34da1b2d0b868b66a8f0a0d0db983b3dc7e53bb7295da81978141a931d3579aec1cac9887d2fff9c6f12d47a27e4aab8cf262a9d14a715bca0b2057cbc3f18b6fd3d1df76f7410f16", "result": "valid", "flags": []}, {"tcId": 232, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "913eecc559b3cf7108a65d6cc3076bfdf36c6f94dcc6693d06690470f34a2e81564241e1de5f5f51421de30af467f10f649bd3717244e8ef3c6b0eda983f84dca5ea86d1bec15386b9c473ec43a8cd0ba558eee819f791d9ff9272b9afd59551", "result": "valid", "flags": []}, {"tcId": 233, "comment": "x-coordinate of the public key is small", "msg": "4d657373616765", "sig": "23855c46403a97b76cbb316ec3fe7e2c422b818387604bda8c3d91121b4f20179d9107c5f92dedc8b620d7db87fccccd50f57343ab148e50662320c4161e44543c35bc992011ea5b1680b94382cf224ea0ec5da511e102f566cb67201f30a2ee", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "-wG6rV8Lj3m5zRBNEqq5MQFGrdfWtMAi2HrmcRF4uU1hjKezrxOFSxxYiHnod7M2", "y": "AAAAACCLP1rTs5N6zJ1gbMXs7KtKcB917UKVfqTXhY0z9cJsauIKnMzaVplnANa0"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b33600000000208b3f5ad3b3937acc9d606cc5ececab4a701f75ed42957ea4d7858d33f5c26c6ae20a9cccda56996700d6b4", "wx": "00fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b336", "wy": "208b3f5ad3b3937acc9d606cc5ececab4a701f75ed42957ea4d7858d33f5c26c6ae20a9cccda56996700d6b4"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b33600000000208b3f5ad3b3937acc9d606cc5ececab4a701f75ed42957ea4d7858d33f5c26c6ae20a9cccda56996700d6b4", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHYwEAYHKoZIzj0CAQYFK4EEACIDYgAE+wG6rV8Lj3m5zRBNEqq5MQFGrdfWtMAi\n2HrmcRF4uU1hjKezrxOFSxxYiHnod7M2AAAAACCLP1rTs5N6zJ1gbMXs7KtKcB91\n7UKVfqTXhY0z9cJsauIKnMzaVplnANa0\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 234, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "d200958d491fcebde667cd736c9dba0961c70db2ecaf573c31dd7fa41ecca32b40b5896f9a0ddf272110e3d21e84593ac2ecf73943b9adce596bac14fce62495ae93825c5ff6f61c247d1d8afcba52082fc96f63a26e55bccfc3779f88cfd799", "result": "valid", "flags": []}, {"tcId": 235, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "6ac17d71260c79f81a7566124738cb3ee5d0aa690e73a98ae9e766f1336691e500cad51ba1302366c09cc06b8f7049e032ca965d6d7012ec187c7cab9544334d66c2a7658ddefa67e4ad40429815518ecc87b1492ddd57333bd2300b4660a835", "result": "valid", "flags": []}, {"tcId": 236, "comment": "y-coordinate of the public key is small", "msg": "4d657373616765", "sig": "e19a4646f0ed8a271fe86ba533f8be4fd81bbf4674716f668efa89a40cac51eec2a6cfbd92327d25efe91ca4ff712bc54a86b2e8e12378e633dec2691e3b1eed4e932cc48b28e45fa3d464cc0e948c02cc9decf2bb43b25937fcf37e9ad86ef0", "result": "valid", "flags": []}]}, {"jwk": {"crv": "P-384", "kid": "none", "kty": "EC", "x": "-wG6rV8Lj3m5zRBNEqq5MQFGrdfWtMAi2HrmcRF4uU1hjKezrxOFSxxYiHnod7M2", "y": "_____990wKUsTGyFM2KfkzoTE1S1j-CKEr1qgVsoenHMCj2SlR31YzMlqWeY_ylL"}, "key": {"curve": "secp384r1", "keySize": 384, "type": "EcPublicKey", "uncompressed": "04fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b336ffffffffdf74c0a52c4c6c8533629f933a131354b58fe08a12bd6a815b287a71cc0a3d92951df5633325a96798ff294b", "wx": "00fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b336", "wy": "00ffffffffdf74c0a52c4c6c8533629f933a131354b58fe08a12bd6a815b287a71cc0a3d92951df5633325a96798ff294b"}, "keyDer": "3076301006072a8648ce3d020106052b8104002203620004fb01baad5f0b8f79b9cd104d12aab9310146add7d6b4c022d87ae6711178b94d618ca7b3af13854b1c588879e877b336ffffffffdf74c0a52c4c6c8533629f933a131354b58fe08a12bd6a815b287a71cc0a3d92951df5633325a96798ff294b", "keyPem": "-----B<PERSON><PERSON> PUBLIC KEY-----\nMHY<PERSON>EAYHKoZIzj0CAQYFK4EEACIDYgAE+wG6rV8Lj3m5zRBNEqq5MQFGrdfWtMAi\n2HrmcRF4uU1hjKezrxOFSxxYiHnod7M2/////990wKUsTGyFM2KfkzoTE1S1j+CK\nEr1qgVsoenHMCj2SlR31YzMlqWeY/ylL\n-----END PUBLIC KEY-----", "sha": "SHA-384", "type": "EcdsaP1363Verify", "tests": [{"tcId": 237, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "15aac6c0f435cb662d110db5cf686caee53c64fe2d6d600a83ebe505a0e6fc62dc5705160477c47528c8c903fa865b5d7f94ddc01a603f9bec5d10c9f2c89fb23b3ffab6b2b68d0f04336d499085e32d22bf3ab67a49a74c743f72473172b59f", "result": "valid", "flags": []}, {"tcId": 238, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "90b95a7d194b73498fba5afc95c1aea9be073162a9edc57c4d12f459f0a1730baf2f87d7d6624aea7b931ec53370fe47cbc1ef470e666010604c609384b872db7fa7b8a5a9f20fdefd656be2fcc75db53948102f7ab203ea1860a6a32af246a1", "result": "valid", "flags": []}, {"tcId": 239, "comment": "y-coordinate of the public key is large", "msg": "4d657373616765", "sig": "dd4391ce7557cbd005e3d5d727cd264399dcc3c6501e4547505b6d57b40bbf0a7fac794dcc8d4233159dd0aa40d4e0b9a77fa1374fd60aa91600912200fc83c6aa447f8171ecea72ae322df32dccd68951dc5caf6c50380e400e45bf5c0e626b", "result": "valid", "flags": []}]}]}