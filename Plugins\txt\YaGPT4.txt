import requests
import threading
import json

from base_plugin import BasePlug<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>gy
from client_utils import log, get_last_fragment
from ui.settings import Header, Selector

__id__ = "yagpt"
__name__ = "YaGPT Integration"
__version__ = "1.0.0"
__description__ = "Интегрирует Yandex Gpt для исправления текста сообщений."
__author__ = "@extera_plugin"
__min_version__ = "11.9.0"
__icon__ = "yakeygpt_by_fStikBot/0"


class YaKeyAPI:
    BASE_URL = "https://keyboard.yandex.net/gpt"
    USER_AGENT = "okhttp/4.12.0"

    def __init__(self, max_threads=5):
        self.max_threads = max_threads
        self.semaphore = threading.Semaphore(max_threads)
        self.session = requests.Session()
        self.session.headers.update({
            "User-Agent": self.USER_AGENT,
            "Connection": "Keep-Alive",
            "Accept-Encoding": "gzip",
            "Content-Type": "application/json; charset=utf-8",
        })

    def send_request(self, method, text):
        """Sends a request to the YaKey API."""
        url = f"{self.BASE_URL}/{method}"
        payload = json.dumps({"text": text})

        try:
            with self.semaphore:
                response = self.session.post(url, data=payload)
                response.raise_for_status()
                return response.json()
        except requests.exceptions.RequestException as e:
            log(f"YaKeyAPI Error: {e}")
            return None
        except json.JSONDecodeError:
            log("YaKeyAPI Error: Invalid JSON response.")
            return None


class YaKeyPlugin(BasePlugin):
    def create_settings(self):
        return [
            Header(text="YaKey Integration"),
            Selector(
                key="yakey_method",
                text="YaKey Method",
                default=0,
                items=["fix", "rewrite", "emoji"],
            ),
        ]

    def on_plugin_load(self):
        self.yakey_api = YaKeyAPI()
        self.yakey_method_index = self.get_setting("yakey_method", 0)
        self.yakey_method = self.create_settings()[1].items[self.yakey_method_index]
        log(f"YaKeyPlugin loaded with method: {self.yakey_method}")

    def correct_message(self, method, text):
        """Corrects the message using the YaKey API in a separate thread."""
        try:
            yakey_response = self.yakey_api.send_request(method=method, text=text)
            if yakey_response and "response" in yakey_response:
                log("Text corrected using YaKeyAPI")
                return yakey_response["response"]
            else:
                log("YaKeyAPI: No response or 'response' key missing.")
                return None
        except Exception as e:
            log(f"YaKeyAPI Error: {e}")
            return None

    def on_send_message_hook(self, account, params):
        try:
            if not hasattr(params, "message") or not isinstance(params.message, str):
                return HookResult()

            message_text = params.message.strip()
            if not message_text:
                return HookResult()

            yakey_method = self.create_settings()[1].items[self.get_setting("yakey_method", 0)]

            corrected_text = self.correct_message(method=yakey_method, text=message_text)

            if corrected_text:
                params.message = corrected_text
                return HookResult(strategy=HookStrategy.MODIFY, params=params)
            else:
                return HookResult()

        except Exception as e:
            log(f"YaKey Plugin Error: {e}")
            return HookResult()