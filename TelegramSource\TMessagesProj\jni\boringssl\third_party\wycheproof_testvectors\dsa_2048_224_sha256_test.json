{"algorithm": "DSA", "generatorVersion": "0.8rc18", "numberOfTests": 358, "header": ["Test vectors of test DsaVerify are intended for checking the signature", "verification of DSA signatures."], "notes": {"EdgeCase": "Some implementations of DSA do not properly check for boundaries. In some cases the modular inverse of 0 is simply 0. As a result there are implementations where values such as r=1, s=0 lead to forgeries.", "NoLeadingZero": "ASN encoded integers with a leading hex-digit in the range 8 .. F are negative. If the first hex-digit of a positive integer is 8 .. F then a leading 0 must be added. Some libraries forgot to do this an therefore generated invalid DSA signatures. Some providers, accept such legacy signatures for compatibility."}, "schema": "dsa_verify_schema.json", "testGroups": [{"key": {"g": "16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde", "keySize": 2048, "p": "008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "q": "00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "type": "DsaPublicKey", "y": "1e77f842b1ae0fcd9929d394161d41e14614ff7507a9a31f4a1f14d22e2a627a1f4e596624883f1a5b168e9425146f22d5f6ee28757414714bb994ba1129f015d6e04a717edf9b530a5d5cab94f14631e8b4cf79aeb358cc741845553841e8ac461630e804a62f43676ba6794af66899c377b869ea612a7b9fe6611aa96be52eb8b62c979117bbbcca8a7ec1e1ffab1c7dfcfc7048700d3ae3858136e897701d7c2921b5dfef1d1f897f50d96ca1b5c2edc58cada18919e35642f0807eebfa00c99a32f4d095c3188f78ed54711be0325c4b532aeccd6540a567c327225440ea15319bde06510479a1861799e25b57decc73c036d75a0702bd373ca231349931"}, "keyDer": "308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde0382010500028201001e77f842b1ae0fcd9929d394161d41e14614ff7507a9a31f4a1f14d22e2a627a1f4e596624883f1a5b168e9425146f22d5f6ee28757414714bb994ba1129f015d6e04a717edf9b530a5d5cab94f14631e8b4cf79aeb358cc741845553841e8ac461630e804a62f43676ba6794af66899c377b869ea612a7b9fe6611aa96be52eb8b62c979117bbbcca8a7ec1e1ffab1c7dfcfc7048700d3ae3858136e897701d7c2921b5dfef1d1f897f50d96ca1b5c2edc58cada18919e35642f0807eebfa00c99a32f4d095c3188f78ed54711be0325c4b532aeccd6540a567c327225440ea15319bde06510479a1861799e25b57decc73c036d75a0702bd373ca231349931", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIDQjCCAjUGByqGSM44BAEwggIoAoIBAQCPeTXZuarpv6vtiHrPSVG28y7Fnjuv\nNxjo6sSWHz79NgbnQ1GpxBgzObgJ58KuHFObp0dbhdARrbi0eYd1SYRpXKwOjxSz\nNggooi/6JxEKPWKpk0U0CaD+aWxGWPhL3SCBnDcJoBBXsZWtzQAjPbpUhLYpH51k\njviDRIZ3l5zsBLQ0pqwudemYXeI9sCkvwRGMn/qdgYHnM423krcw17njSVkvaAmY\nchU5Feo9a4tGU8YzRY+AOzKkwuDycpAlbk4/ijsIOKHEUOThjBopo33fXqFD3ktm\n/wSQPtXPFiPhWNSHxgjpfyEc2B3KI8tuOAdl+CLjQr5ITAV2OTlgHNZnAh0AuvaW\npoV499/e5/pnyXfHhe8ysjO65YDAvNVpXQKCAQAWplxYIEhQcE51AqOXVwQNNNo6\nNHjBVNTkpcAtJC7gT5bmHkvQkEq9rI837rHgnzGC0jyQQ8tkL4gAQWDt+coJsyB2\np5wypifyRz6Rh5uixOdEvSCBVEy1W4AsNo0fqD7UielOD6BojjJCilx4xHjGjQUn\ntxyaOrsLC+EsRGiWOefTznTbEBplqiuH9kxoJts+xy9LVZmDS7TtsC98kOmkltOl\nXVNb6/xF1PYZ9j897buHOSXC8iTgdzEpbaiH7B5HSPh++1/et1SEMWsiMt7lU92v\nAhErDR8C2jCXMiT+J67ai51LKSLZuovjntnhA6Y8UoELxoi34u1DFuHvF9veA4IB\nBQACggEAHnf4QrGuD82ZKdOUFh1B4UYU/3UHqaMfSh8U0i4qYnofTllmJIg/GlsW\njpQlFG8i1fbuKHV0FHFLuZS6ESnwFdbgSnF+35tTCl1cq5TxRjHotM95rrNYzHQY\nRVU4QeisRhYw6ASmL0Nna6Z5SvZomcN3uGnqYSp7n+ZhGqlr5S64tiyXkRe7vMqK\nfsHh/6scffz8cEhwDTrjhYE26JdwHXwpIbXf7x0fiX9Q2WyhtcLtxYytoYkZ41ZC\n8IB+6/oAyZoy9NCVwxiPeO1UcRvgMlxLUyrszWVApWfDJyJUQOoVMZveBlEEeaGG\nF5niW1fezHPANtdaBwK9NzyiMTSZMQ==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "DsaVerify", "tests": [{"tcId": 1, "comment": "Legacy:ASN encoding of r misses leading 0", "msg": "313233343030", "sig": "303c021ca545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "acceptable", "flags": ["NoLeadingZero"]}, {"tcId": 2, "comment": "valid", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "valid", "flags": []}, {"tcId": 3, "comment": "long form encoding of length of sequence", "msg": "313233343030", "sig": "30813d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 4, "comment": "length of sequence contains leading 0", "msg": "313233343030", "sig": "3082003d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 5, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "303e021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 6, "comment": "wrong length of sequence", "msg": "313233343030", "sig": "303c021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 7, "comment": "uint32 overflow in length of sequence", "msg": "313233343030", "sig": "3085010000003d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 8, "comment": "uint64 overflow in length of sequence", "msg": "313233343030", "sig": "308901000000000000003d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 9, "comment": "length of sequence = 2**31 - 1", "msg": "313233343030", "sig": "30847fffffff021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 10, "comment": "length of sequence = 2**32 - 1", "msg": "313233343030", "sig": "3084ffffffff021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 11, "comment": "length of sequence = 2**40 - 1", "msg": "313233343030", "sig": "3085ffffffffff021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 12, "comment": "length of sequence = 2**64 - 1", "msg": "313233343030", "sig": "3088ffffffffffffffff021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 13, "comment": "incorrect length of sequence", "msg": "313233343030", "sig": "30ff021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 14, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "3080021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 15, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "303d028000a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 16, "comment": "indefinite length without termination", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b02803be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 17, "comment": "removing sequence", "msg": "313233343030", "sig": "", "result": "invalid", "flags": []}, {"tcId": 18, "comment": "lonely sequence tag", "msg": "313233343030", "sig": "30", "result": "invalid", "flags": []}, {"tcId": 19, "comment": "appending 0's to sequence", "msg": "313233343030", "sig": "303f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8440000", "result": "invalid", "flags": []}, {"tcId": 20, "comment": "prepending 0's to sequence", "msg": "313233343030", "sig": "303f0000021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 21, "comment": "appending unused 0's to sequence", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8440000", "result": "invalid", "flags": []}, {"tcId": 22, "comment": "appending null value to sequence", "msg": "313233343030", "sig": "303f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8440500", "result": "invalid", "flags": []}, {"tcId": 23, "comment": "including garbage", "msg": "313233343030", "sig": "3042498177303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 24, "comment": "including garbage", "msg": "313233343030", "sig": "30412500303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 25, "comment": "including garbage", "msg": "313233343030", "sig": "303f303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8440004deadbeef", "result": "invalid", "flags": []}, {"tcId": 26, "comment": "including garbage", "msg": "313233343030", "sig": "30422222498177021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 27, "comment": "including garbage", "msg": "313233343030", "sig": "304122212500021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 28, "comment": "including garbage", "msg": "313233343030", "sig": "3045221f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0004deadbeef021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 29, "comment": "including garbage", "msg": "313233343030", "sig": "3042021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b2221498177021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 30, "comment": "including garbage", "msg": "313233343030", "sig": "3041021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b22202500021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 31, "comment": "including garbage", "msg": "313233343030", "sig": "3045021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b221e021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8440004deadbeef", "result": "invalid", "flags": []}, {"tcId": 32, "comment": "including undefined tags", "msg": "313233343030", "sig": "3045aa00bb00cd00303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 33, "comment": "including undefined tags", "msg": "313233343030", "sig": "3043aa02aabb303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 34, "comment": "including undefined tags", "msg": "313233343030", "sig": "30452225aa00bb00cd00021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 35, "comment": "including undefined tags", "msg": "313233343030", "sig": "30432223aa02aabb021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 36, "comment": "including undefined tags", "msg": "313233343030", "sig": "3045021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b2224aa00bb00cd00021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 37, "comment": "including undefined tags", "msg": "313233343030", "sig": "3043021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b2222aa02aabb021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 38, "comment": "truncated length of sequence", "msg": "313233343030", "sig": "3081", "result": "invalid", "flags": []}, {"tcId": 39, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3080303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8440000", "result": "invalid", "flags": []}, {"tcId": 40, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "30412280021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0000021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 41, "comment": "using composition with indefinite length", "msg": "313233343030", "sig": "3041021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b2280021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8440000", "result": "invalid", "flags": []}, {"tcId": 42, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3080313d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8440000", "result": "invalid", "flags": []}, {"tcId": 43, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "30412280031d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0000021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 44, "comment": "using composition with wrong tag", "msg": "313233343030", "sig": "3041021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b2280031c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8440000", "result": "invalid", "flags": []}, {"tcId": 45, "comment": "Replacing sequence with NULL", "msg": "313233343030", "sig": "0500", "result": "invalid", "flags": []}, {"tcId": 46, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2e3d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 47, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "2f3d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 48, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "313d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 49, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "323d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 50, "comment": "changing tag value of sequence", "msg": "313233343030", "sig": "ff3d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 51, "comment": "dropping value of sequence", "msg": "313233343030", "sig": "3000", "result": "invalid", "flags": []}, {"tcId": 52, "comment": "using composition for sequence", "msg": "313233343030", "sig": "3041300102303c1d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 53, "comment": "truncated sequence", "msg": "313233343030", "sig": "303c021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8", "result": "invalid", "flags": []}, {"tcId": 54, "comment": "truncated sequence", "msg": "313233343030", "sig": "303c1d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 55, "comment": "indefinite length", "msg": "313233343030", "sig": "3080021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8440000", "result": "invalid", "flags": []}, {"tcId": 56, "comment": "indefinite length with truncated delimiter", "msg": "313233343030", "sig": "3080021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca84400", "result": "invalid", "flags": []}, {"tcId": 57, "comment": "indefinite length with additional element", "msg": "313233343030", "sig": "3080021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca84405000000", "result": "invalid", "flags": []}, {"tcId": 58, "comment": "indefinite length with truncated element", "msg": "313233343030", "sig": "3080021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844060811220000", "result": "invalid", "flags": []}, {"tcId": 59, "comment": "indefinite length with garbage", "msg": "313233343030", "sig": "3080021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8440000fe02beef", "result": "invalid", "flags": []}, {"tcId": 60, "comment": "indefinite length with nonempty EOC", "msg": "313233343030", "sig": "3080021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8440002beef", "result": "invalid", "flags": []}, {"tcId": 61, "comment": "prepend empty sequence", "msg": "313233343030", "sig": "303f3000021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 62, "comment": "append empty sequence", "msg": "313233343030", "sig": "303f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8443000", "result": "invalid", "flags": []}, {"tcId": 63, "comment": "append garbage with high tag number", "msg": "313233343030", "sig": "3040021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844bf7f00", "result": "invalid", "flags": []}, {"tcId": 64, "comment": "sequence of sequence", "msg": "313233343030", "sig": "303f303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 65, "comment": "truncated sequence: removed last 1 elements", "msg": "313233343030", "sig": "301f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b", "result": "invalid", "flags": []}, {"tcId": 66, "comment": "repeating element in sequence", "msg": "313233343030", "sig": "305b021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 67, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "303e02811d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 68, "comment": "long form encoding of length of integer", "msg": "313233343030", "sig": "303e021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b02811c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 69, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "303f0282001d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 70, "comment": "length of integer contains leading 0", "msg": "313233343030", "sig": "303f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0282001c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 71, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303d021e00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 72, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303d021c00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 73, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021d3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 74, "comment": "wrong length of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021b3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 75, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "30420285010000001d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 76, "comment": "uint32 overflow in length of integer", "msg": "313233343030", "sig": "3042021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0285010000001c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 77, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3046028901000000000000001d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 78, "comment": "uint64 overflow in length of integer", "msg": "313233343030", "sig": "3046021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b028901000000000000001c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 79, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "304102847fffffff00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 80, "comment": "length of integer = 2**31 - 1", "msg": "313233343030", "sig": "3041021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b02847fffffff3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 81, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "30410284ffffffff00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 82, "comment": "length of integer = 2**32 - 1", "msg": "313233343030", "sig": "3041021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0284ffffffff3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 83, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "30420285ffffffffff00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 84, "comment": "length of integer = 2**40 - 1", "msg": "313233343030", "sig": "3042021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0285ffffffffff3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 85, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "30450288ffffffffffffffff00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 86, "comment": "length of integer = 2**64 - 1", "msg": "313233343030", "sig": "3045021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0288ffffffffffffffff3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 87, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "303d02ff00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 88, "comment": "incorrect length of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b02ff3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 89, "comment": "removing integer", "msg": "313233343030", "sig": "301e021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 90, "comment": "lonely integer tag", "msg": "313233343030", "sig": "301f02021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 91, "comment": "lonely integer tag", "msg": "313233343030", "sig": "3020021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b02", "result": "invalid", "flags": []}, {"tcId": 92, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "303f021f00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0000021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 93, "comment": "appending 0's to integer", "msg": "313233343030", "sig": "303f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021e3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8440000", "result": "invalid", "flags": []}, {"tcId": 94, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "303f021f000000a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 95, "comment": "prepending 0's to integer", "msg": "313233343030", "sig": "303f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021e00003be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 96, "comment": "appending unused 0's to integer", "msg": "313233343030", "sig": "303f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0000021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 97, "comment": "appending null value to integer", "msg": "313233343030", "sig": "303f021f00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0500021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 98, "comment": "appending null value to integer", "msg": "313233343030", "sig": "303f021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021e3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8440500", "result": "invalid", "flags": []}, {"tcId": 99, "comment": "truncated length of integer", "msg": "313233343030", "sig": "30200281021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 100, "comment": "truncated length of integer", "msg": "313233343030", "sig": "3021021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0281", "result": "invalid", "flags": []}, {"tcId": 101, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "30200500021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 102, "comment": "Replacing integer with NULL", "msg": "313233343030", "sig": "3021021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0500", "result": "invalid", "flags": []}, {"tcId": 103, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d001d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 104, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d011d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 105, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d031d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 106, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d041d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 107, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303dff1d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 108, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b001c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 109, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b011c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 110, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b031c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 111, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b041c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 112, "comment": "changing tag value of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7bff1c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 113, "comment": "dropping value of integer", "msg": "313233343030", "sig": "30200200021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 114, "comment": "dropping value of integer", "msg": "313233343030", "sig": "3021021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b0200", "result": "invalid", "flags": []}, {"tcId": 115, "comment": "using composition for integer", "msg": "313233343030", "sig": "30412221020100021ca545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 116, "comment": "using composition for integer", "msg": "313233343030", "sig": "3041021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b222002013b021be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 117, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "303d021d02a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 118, "comment": "modify first byte of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c39e6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 119, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1bfb021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 120, "comment": "modify last byte of integer", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8c4", "result": "invalid", "flags": []}, {"tcId": 121, "comment": "truncated integer", "msg": "313233343030", "sig": "303c021c00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 122, "comment": "truncated integer", "msg": "313233343030", "sig": "303c021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021b3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca8", "result": "invalid", "flags": []}, {"tcId": 123, "comment": "truncated integer", "msg": "313233343030", "sig": "303c021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 124, "comment": "leading ff in integer", "msg": "313233343030", "sig": "303e021eff00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 125, "comment": "leading ff in integer", "msg": "313233343030", "sig": "303e021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021dff3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 126, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3021090180021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 127, "comment": "replaced integer by infinity", "msg": "313233343030", "sig": "3022021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b090180", "result": "invalid", "flags": []}, {"tcId": 128, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3021020100021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 129, "comment": "replacing integer with zero", "msg": "313233343030", "sig": "3022021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b020100", "result": "invalid", "flags": []}, {"tcId": 130, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303d021d01603c6cd3f3ac5f55da5295ec5ee9ddcc947e8af9d2254162e62f84d8021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 131, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303c021cea4f3f86e8ba6f961c82a11ccbfa4ec0b61926925c5a3fe16c84b21e021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 132, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303d021dff5aba29d291cc988a0495647b6a8de9b95ab42739e8c03f5dd6a5e485021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 133, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303c021c15b0c07917459069e37d5ee33405b13f49e6d96da3a5c01e937b4de2021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 134, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303d021dfe9fc3932c0c53a0aa25ad6a13a11622336b8175062ddabe9d19d07b28021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 135, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303d021d01a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 136, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303c021c5aba29d291cc988a0495647b6a8de9b95ab42739e8c03f5dd6a5e485021c3be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 137, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303e021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021d00f6dd51876def2ff862f28bb977069a39a27b96d67f6e54bc989211a1", "result": "invalid", "flags": []}, {"tcId": 138, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c80f0243a62fd4038a52296e9e4170b2dc416326f09a3533b1ee73ee7", "result": "invalid", "flags": []}, {"tcId": 139, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021cc419451f1789c7e77bf56eae52712d4c4cb71b5d3b772c04244357bc", "result": "invalid", "flags": []}, {"tcId": 140, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303d021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021c7f0fdbc59d02bfc75add69161be8f4d23be9cd90f65cacc4e118c119", "result": "invalid", "flags": []}, {"tcId": 141, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303e021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021dff0922ae789210d0079d0d744688f965c65d8469298091ab43676dee5f", "result": "invalid", "flags": []}, {"tcId": 142, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303e021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021d013be6bae0e8763818840a9151ad8ed2b3b348e4a2c488d3fbdbbca844", "result": "invalid", "flags": []}, {"tcId": 143, "comment": "Modified r or s, e.g. by adding or subtracting the group order", "msg": "313233343030", "sig": "303e021d00a545d62d6e336775fb6a9b8495721646a54bd8c6173fc0a2295a1b7b021d00c419451f1789c7e77bf56eae52712d4c4cb71b5d3b772c04244357bc", "result": "invalid", "flags": []}, {"tcId": 144, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020100021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 145, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020100020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 146, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020100020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 147, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 148, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021020100021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 149, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021020100021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 150, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 151, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 152, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020100021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 153, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020100021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 154, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010802010002820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 155, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008020100090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 156, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020100090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 157, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020101021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 158, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020101020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 159, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020101020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 160, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 161, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021020101021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 162, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021020101021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 163, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 164, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 165, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 166, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022020101021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 167, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010802010102820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 168, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008020101090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 169, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3006020101090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 170, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30220201ff021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 171, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201ff020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 172, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201ff020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 173, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201ff0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 174, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30210201ff021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 175, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30210201ff021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 176, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 177, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 178, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30220201ff021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 179, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30220201ff021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 180, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "308201080201ff02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 181, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30080201ff090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 182, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30060201ff090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 183, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 184, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 185, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 186, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 187, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 188, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 189, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 190, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 191, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 192, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 193, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30820123021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 194, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3023021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 195, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 196, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 197, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 198, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 199, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 200, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 201, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 202, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 203, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 204, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 205, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 206, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30820123021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 207, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3023021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 208, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3021021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 209, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 210, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 211, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 212, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 213, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 214, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 215, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 216, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 217, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 218, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 219, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 220, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 221, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 222, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 223, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 224, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 225, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 226, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 227, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 228, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 229, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 230, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 231, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 232, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 233, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 234, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 235, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 236, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 237, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 238, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e0201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 239, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 240, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 241, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 242, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 243, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 244, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 245, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30820124021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 246, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 247, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 248, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 249, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d0100000000000000000000000000000000000000000000000000000000020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 250, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d0100000000000000000000000000000000000000000000000000000000020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 251, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d01000000000000000000000000000000000000000000000000000000000201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 252, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021d0100000000000000000000000000000000000000000000000000000000021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 253, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303d021d0100000000000000000000000000000000000000000000000000000000021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 254, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 255, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 256, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 257, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "303e021d0100000000000000000000000000000000000000000000000000000000021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 258, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "30820124021d010000000000000000000000000000000000000000000000000000000002820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 259, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024021d0100000000000000000000000000000000000000000000000000000000090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 260, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3022021d0100000000000000000000000000000000000000000000000000000000090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 261, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 262, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 263, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 264, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd6670201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 265, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082012302820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 266, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082012302820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 267, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 268, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 269, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 270, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082012402820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 271, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082020a02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd66702820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 272, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010a02820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 273, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 274, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024090380fe01021dff450969597a870820211805983688387a10cd4dcc451a7f3f432a96a3", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 275, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008090380fe01020100", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 276, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008090380fe01020101", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 277, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008090380fe010201ff", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 278, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3023090380fe01021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4ae", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 279, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3023090380fe01021c5d7b4b5342bc7befef73fd33e4bbe3c2f7995919dd72c0605e6ab4af", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 280, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 281, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 282, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024090380fe01021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695e", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 283, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3024090380fe01021d0100000000000000000000000000000000000000000000000000000000", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 284, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3082010a090380fe0102820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 285, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "300a090380fe01090380fe01", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 286, "comment": "Signatures with special case values for r and s.", "msg": "313233343030", "sig": "3008090380fe01090142", "result": "invalid", "flags": ["EdgeCase"]}, {"tcId": 287, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "30060201010c0130", "result": "invalid", "flags": []}, {"tcId": 288, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "30050201010c00", "result": "invalid", "flags": []}, {"tcId": 289, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "30090c0225730c03732573", "result": "invalid", "flags": []}, {"tcId": 290, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "30080201013003020100", "result": "invalid", "flags": []}, {"tcId": 291, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "3003020101", "result": "invalid", "flags": []}, {"tcId": 292, "comment": "Signature encoding contains wrong type.", "msg": "313233343030", "sig": "3006020101010100", "result": "invalid", "flags": []}, {"tcId": 293, "comment": "random signatures", "msg": "313233343030", "sig": "303e021d00a939df97ddbe605a925e2456acc196ceea94410d54eed9d501befb90021d00928cb34d0e71f8ea4228243817982090e73989348a4eee774b3c08c7", "result": "valid", "flags": []}, {"tcId": 294, "comment": "random signatures", "msg": "313233343030", "sig": "303d021c3b98fa1f5ea18af8e2878571152f257accf243342582a757535f4a46021d009854b465bbd8a95281ef941d844ac8ba573c7d4e4bbb181d3957fb70", "result": "valid", "flags": []}, {"tcId": 295, "comment": "random signatures", "msg": "313233343030", "sig": "303c021c6413ccb5d0de22129ab5f861f571d9d9419e057101f990cebb2a52e5021c529801636f56771d44ca9fd33d58f7804fe0f5f7da5fd29159eb8525", "result": "valid", "flags": []}, {"tcId": 296, "comment": "random signatures", "msg": "313233343030", "sig": "303c021c19dc7c18a0ca1e947b095782aa5ab1e6c3f2ca329d6070959833d88c021c719ff4872ab6cc0fbe934e36fa17bbc9e4d9ac12c650c3c45ab437f7", "result": "valid", "flags": []}, {"tcId": 297, "comment": "random signatures", "msg": "313233343030", "sig": "303c021c720931df5201f87af025960a55f815e841d827b85f047b789bb026f1021c1dc94f0801183f823312dc4cb69565f2dc5ff7e350f8499ea04ef09e", "result": "valid", "flags": []}, {"tcId": 298, "comment": "special case hash", "msg": "343236343739373234", "sig": "303d021c6282929a6986a4de222f3ee95248b8a73c3a295b61c06d8b1b0e0291021d00ba953d1d97df5ed013efbb682b9e60448d4860d6c03d94773b9137ad", "result": "valid", "flags": []}, {"tcId": 299, "comment": "special case hash", "msg": "37313338363834383931", "sig": "303d021d008e32e47a57c6e85527d23728fec6f182e8e6d81c1061a36621f1f06f021c36a5ed84200e112dec002b809af40f31f4467fea947bb00209b49b6d", "result": "valid", "flags": []}, {"tcId": 300, "comment": "special case hash", "msg": "3130333539333331363638", "sig": "303c021c6be782619cf8759fbfa5a2a8617a4e74f423d5c076e2ffe4244fddd4021c44c9e7db875328caca48cc1e35d19ad0512546c2975410f0656f3753", "result": "valid", "flags": []}, {"tcId": 301, "comment": "special case hash", "msg": "33393439343031323135", "sig": "303d021c7d6d1d409999072fd064e5bc7ecd3add46ad5a6777eff67576a9c68e021d00b79f6ddea366408ee9e2e42467ff471ef22749d6ab08a3b87da7bbdb", "result": "valid", "flags": []}, {"tcId": 302, "comment": "special case hash", "msg": "31333434323933303739", "sig": "303d021c144da10c0af2fa81049de991110f5244f7b190f362fdfe5fd3cc4f92021d00871f86bc71f4c846dde313b049d5dd74ad29760fbc9cfff465f4a7e1", "result": "valid", "flags": []}, {"tcId": 303, "comment": "special case hash", "msg": "33373036323131373132", "sig": "303c021c22030ae5e6d9a46321ab2f52ec1308437f7871bddb8ea2fdd2e7b6ae021c198d53bd26b00c7f1b4010135108267f9a2ec364da378868c2d682e4", "result": "valid", "flags": []}, {"tcId": 304, "comment": "special case hash", "msg": "333433363838373132", "sig": "303c021c6ca03adc1179d06f19abd0f6e3bda581ad3af91abf52f8c4b2f15e1a021c7f9f8020a2872c1bf5ebc13eafc12fe9f3101e53b5c79b46d4ce85c8", "result": "valid", "flags": []}, {"tcId": 305, "comment": "special case hash", "msg": "31333531353330333730", "sig": "303d021c5d000dbdb4e5bd082dceaaac1e117ed667928aaa38880b1d5a2ac8b1021d0083c7bc41269bc326fee53133114b8110ea1aa32f76974c2ba77b9b16", "result": "valid", "flags": []}, {"tcId": 306, "comment": "special case hash", "msg": "36353533323033313236", "sig": "303d021d00a541c4bc4c54d3fa1cb9cf111e25d6700187c04c8af22e5c41af1e0f021c5a078a2fff1f6db8144bb01fbba0eae8f141a4196507aa1f38e9fcbb", "result": "valid", "flags": []}, {"tcId": 307, "comment": "special case hash", "msg": "31353634333436363033", "sig": "303d021c2e9435238235a67e54a3fb8fdab8766fdd10957619efd6ad6ce2ae23021d00a9c8d673a42090783e323f00372a4564d211527b3b1cbec1e6d8546a", "result": "valid", "flags": []}, {"tcId": 308, "comment": "special case hash", "msg": "34343239353339313137", "sig": "303c021c64c764b18c87c4cc2f6b13cf41af4e944906b6e855154cbd7845dc3a021c2818558b524c1fb8f5784acd2374ac80a23eb480626c75937fd89b0a", "result": "valid", "flags": []}, {"tcId": 309, "comment": "special case hash", "msg": "3130393533323631333531", "sig": "303c021c1e9ec38acb4ed3a49515d75aea7b498626dec9f0edc6d65a5d3948b1021c7d5ef36babfc3f6e32bd8f0f0b52cd956b08d25c47ffb0b5dd52349d", "result": "valid", "flags": []}, {"tcId": 310, "comment": "special case hash", "msg": "35393837333530303431", "sig": "303d021d0085f316c400fc2b065144f324d7eb368d46e151c0ef280ef7291d04a3021c4f9ea19e883bd4912a2c2f8a7b70ba7491a40312d127d1545eb98d51", "result": "valid", "flags": []}, {"tcId": 311, "comment": "special case hash", "msg": "33343633303036383738", "sig": "303d021c0983cd44dafb67a2d79ed22b19e2e45aba8384d99543ba233f4cda39021d00a9d88871328c3da9030057ab3da7c3cf806eaa95aad7ffadd6819d48", "result": "valid", "flags": []}, {"tcId": 312, "comment": "special case hash", "msg": "39383137333230323837", "sig": "303c021c72883c2b1004f8341db522aadc5794476a61aeb883e738d826979d58021c6c3f7680a2764b76e0e211b9212cf48551248a54e93d1e1cb0529336", "result": "valid", "flags": []}, {"tcId": 313, "comment": "special case hash", "msg": "33323232303431303436", "sig": "303c021c316472fd56393aae134a172b8770f21607d4658bfd8f3d2d210b6b4f021c1ae99ef798d5f794838e034ddf6c8a008dfa9d3f80486c24f4dcd8ea", "result": "valid", "flags": []}, {"tcId": 314, "comment": "special case hash", "msg": "36363636333037313034", "sig": "303d021c33206c948d46f0d4cec9fdfb397453692e06c889ec78a4b16abd9a98021d0097621cb64bde78ca50e7beec065695155fa175d8de3a0e5b131ccfb3", "result": "valid", "flags": []}, {"tcId": 315, "comment": "special case hash", "msg": "31303335393531383938", "sig": "303d021c252760e2a7cb0235774dc6213f3b2d1428f30bf9f2d53f3050d5585b021d00ab5cdba682c365c761d2956e84c16906cd9ab8dcdea2e88237393fb9", "result": "valid", "flags": []}, {"tcId": 316, "comment": "special case hash", "msg": "31383436353937313935", "sig": "303c021c101daeb23670a6ac2b6714ede911072e6a1cd6ad5a90925e46b0313c021c34952fb5e3c6f34d30259c5b643e12129a4989b58ecf45f05224aa39", "result": "valid", "flags": []}, {"tcId": 317, "comment": "special case hash", "msg": "33313336303436313839", "sig": "303c021c082fdc88d92e7d5c7a9da06533d17e07c161094c93a265e6d64e96b6021c209d8a372e2e5ae13aa90cd2938cfc5b4b459c104e05e46b29fe06b1", "result": "valid", "flags": []}, {"tcId": 318, "comment": "special case hash", "msg": "32363633373834323534", "sig": "303c021c7fde93ade947c2bf54c106af40c8f34c397c9671d5f0cc7637c38051021c38c4571195141d0d381a588730c375fa0d3d43d6ed2c2f5f1cbcf853", "result": "valid", "flags": []}, {"tcId": 319, "comment": "special case hash", "msg": "31363532313030353234", "sig": "303d021c609619f43b17983ba9b63876d5cd62f3f6df9cecafdca9fee576404e021d00b3d31a9e16d6bf9f2d466322c0a3e51e50d1da2c1973ff38e61d8534", "result": "valid", "flags": []}, {"tcId": 320, "comment": "special case hash", "msg": "35373438303831363936", "sig": "303c021c54c56807dbce30d9eab7072ec6179934068db366b6dc825c44d2958b021c3718640b1525ef01c8d7fab684af8906971c5fa3a842c96b4b7900a4", "result": "valid", "flags": []}, {"tcId": 321, "comment": "special case hash", "msg": "36333433393133343638", "sig": "303e021d00aa3c052fb3fdfb7693a9c2b6e1199a82b8cf343f142b8207eb5d1c69021d009f94b8b787dbe1c62f9160e3207151c705d558acb852ca10846e793a", "result": "valid", "flags": []}, {"tcId": 322, "comment": "special case hash", "msg": "31353431313033353938", "sig": "303e021d009ed6218104c49e13cbc616528e5f772b2eaa4341222c232cf350a178021d008bb7f086a93e39e3340458d51047f37b209d2720f5268842e9fc7433", "result": "valid", "flags": []}, {"tcId": 323, "comment": "special case hash", "msg": "3130343738353830313238", "sig": "303d021c45ba82413c2f3020120ee67c7048b95916709bc5d28f5572973ca903021d009493d4953180ae6d1170d192be8526416d0b233dc4d4b7d38e813a10", "result": "valid", "flags": []}, {"tcId": 324, "comment": "special case hash", "msg": "3130353336323835353638", "sig": "303d021d00a2184515521e4c5d26f05590543c696ca2bd04b7754a18107d7f6274021c4fbcb3a52ee80de3dca53339c3f6b2196afe3c540adfeb92686029f2", "result": "valid", "flags": []}, {"tcId": 325, "comment": "special case hash", "msg": "393533393034313035", "sig": "303c021c6ec0060128fb86ead65e2739fe011364e7c18cc38bad8f60f3b48e74021c1f70e53b084e3312c4c89af12a534a59e19fa4dbbf78d22debfa8ae1", "result": "valid", "flags": []}, {"tcId": 326, "comment": "special case hash", "msg": "393738383438303339", "sig": "303d021d00b8c5d803085623b0521564605cf18a24ff2d247f375dc639ca9c9644021c5cf78b4c243632ceba5f4356833fbccd21b7e207fcf50534c91a5edf", "result": "valid", "flags": []}, {"tcId": 327, "comment": "special case hash", "msg": "33363130363732343432", "sig": "303d021d008d2ba6eacec3ab1ae10e85f60d11315429bd2a9e6a19c005dd555807021c4fa58e3be186c4892901571cadee1fe7e6ea83162e7a56c891981541", "result": "valid", "flags": []}, {"tcId": 328, "comment": "special case hash", "msg": "31303534323430373035", "sig": "303d021c06e6f663c9082e327e2d619f07986c2ee3c439ca855cdcfa024e186d021d00a24a5c11aab309f9559da392544c7f6f7357ac49a4d53975958ca6a3", "result": "valid", "flags": []}, {"tcId": 329, "comment": "special case hash", "msg": "35313734343438313937", "sig": "303d021c4f1f0ba8d0f76c7a40ede2d0ac23422cc89788cd9a6c2874bf15e975021d008713d745040d5cc49f18b194882ac409db6d133e7003b1d2d99a5c39", "result": "valid", "flags": []}, {"tcId": 330, "comment": "special case hash", "msg": "31393637353631323531", "sig": "303e021d00934ca0f4c8d37eb8cb6e3f1fe9a55a6e130a971537f6324b07238908021d00a6f12a144209f02139fb8caa856949dd2852761494524df421cd74d3", "result": "valid", "flags": []}, {"tcId": 331, "comment": "special case hash", "msg": "33343437323533333433", "sig": "303d021d008a9923d49e92bd4fbc6374d98fc3d082833dfd5115c0c7aaf94ba3ce021c3e3d1276a9820051d1ed7e3ddaee869f80669354adf71065d024832d", "result": "valid", "flags": []}, {"tcId": 332, "comment": "special case hash", "msg": "333638323634333138", "sig": "303c021c7fa3af955a96dc6de30195626190bffbabf7fc4c13e582b376e40601021c6c62e1f6a3103dadf0b619f942952b04aef3ba085fe556c1a52f3a54", "result": "valid", "flags": []}, {"tcId": 333, "comment": "special case hash", "msg": "33323631313938363038", "sig": "303d021c7f00a0a9e4503f039fb0a0d5f4eab8e3a4755bf9df6f08ce7363919d021d00a85c163cda0b2faaa59f7817387d117450ab8c404187e788a77f1b31", "result": "valid", "flags": []}, {"tcId": 334, "comment": "special case hash", "msg": "39363738373831303934", "sig": "303d021c67866399a597f5a2dec9a5c2d099e40b35c11b194672fb4c91438ce2021d0091cf10d8bb45641716c84d77bc821bda2bad258bf108aeae8f800de0", "result": "valid", "flags": []}, {"tcId": 335, "comment": "special case hash", "msg": "34393538383233383233", "sig": "303e021d00ac4ad70fba86896eac80c7fe97f23c35eb1bddc3f15f8bac72914a16021d00981b85e49843b3b67cd20a9d84a05f78ac0f7159660d589b54f0daf8", "result": "valid", "flags": []}, {"tcId": 336, "comment": "special case hash", "msg": "383234363337383337", "sig": "303d021d008cf2b265e8627c86633e01e3ed966da0163c55c59267355139f66fea021c449bd7479167fbc6e4caa782951215b2b76b413b1df6f5bbbe190e1a", "result": "valid", "flags": []}, {"tcId": 337, "comment": "special case hash", "msg": "3131303230383333373736", "sig": "303d021c753ea714a8d99a9a3f5055fdcb6385f2c7064175b2792e3deb014d47021d008e8c8a120afe9cb7c53c4d8432848ed1f51663b00688cf1dc19a44a1", "result": "valid", "flags": []}, {"tcId": 338, "comment": "special case hash", "msg": "313333383731363438", "sig": "303c021c3a643f446379372a6709270dc79540f267495e95dc9b650262603c4f021c6c6a7b062dba75a6a1b40b449f6241b959fb46720a98684a6c174750", "result": "valid", "flags": []}, {"tcId": 339, "comment": "special case hash", "msg": "333232313434313632", "sig": "303c021c1fe1800bdb864702fac811ca6120183ef6a435df9bfdff4efe64a4a5021c28752fb108b7dce11b1dd896284ee233536dc162ab4bc32bee28308e", "result": "valid", "flags": []}, {"tcId": 340, "comment": "special case hash", "msg": "3130363836363535353436", "sig": "303d021c0e486944dd44a53ac8f38b7476f0cd7f6c3e07c8f1c1b754f120c224021d00b5e7f87de04dbb3e4a8908eda5e9084d7a57febed8366e843ecf069d", "result": "valid", "flags": []}, {"tcId": 341, "comment": "special case hash", "msg": "3632313535323436", "sig": "303c021c03c0bf70a34ade306e72f385e1bad6a662ba5b1f1181aca9c1e5bfd5021c60f9ecbad2b3c13952635465128323b2313d0d42d4c4856eed793222", "result": "valid", "flags": []}, {"tcId": 342, "comment": "special case hash", "msg": "37303330383138373734", "sig": "303c021c77a1eca2b4fa6073bd80b6ce4d6caeb44590444cce7fc287b12df3f4021c5477676f99b60c692677f5831298480af8a8e484388615fc879b3576", "result": "valid", "flags": []}, {"tcId": 343, "comment": "special case hash", "msg": "35393234353233373434", "sig": "303d021d009f7d3131e420c5641c29eb0ddfe52ceb82e7917193b5a35ba32a2f42021c54343162217bd5a50b73a729f6e7057cdd5e31cd95cd63616b6a7a5a", "result": "valid", "flags": []}, {"tcId": 344, "comment": "special case hash", "msg": "31343935353836363231", "sig": "303c021c65bc9d727e2227fd3a87e5fc1ce54fe7173b7a89498ae4c40ab3f8fd021c74495994198de4bae663e253367ed332c8a96025cbbdd9b52158ba3b", "result": "valid", "flags": []}, {"tcId": 345, "comment": "special case hash", "msg": "34303035333134343036", "sig": "303d021d008721959319062747dcd52887d39ca571ed94db756b5a613f41622907021c0201c63166baf11844093afea7bf031371f7a2d3268740a69dd57019", "result": "valid", "flags": []}, {"tcId": 346, "comment": "special case hash", "msg": "33303936343537353132", "sig": "303c021c56edc8d1c1698df4e8a43f1b1d3a2efcc2a492dad78f0d1fee9bb1ce021c6805906b6976221f99201bc2649ab781ef5459dc78dac46dbab26507", "result": "valid", "flags": []}, {"tcId": 347, "comment": "special case hash", "msg": "32373834303235363230", "sig": "303d021c46f67ddd43cc95345132f8204e20039d848a41defd463f82331abd89021d00adafe7d400a62e210763196a82eee19b94f25748551a31d055b1f521", "result": "valid", "flags": []}, {"tcId": 348, "comment": "special case hash", "msg": "32363138373837343138", "sig": "303d021d00925786e4eb98fda38d11cc3ba5a699bad6887fbecdd690f9d403a367021c1c0cdb20c797b74060c8a1deb5d2c0c7ab508e70ab394f175c8cafa5", "result": "valid", "flags": []}, {"tcId": 349, "comment": "special case hash", "msg": "31363432363235323632", "sig": "303d021c649cdd8680fc91c3fe19e9bd7d7c405c91fdade2de072bc49633a994021d00a0b764f5a259f9d43950b5335d34c623be9f98224b936000ee7d645e", "result": "valid", "flags": []}, {"tcId": 350, "comment": "special case hash", "msg": "36383234313839343336", "sig": "303c021c5dac6cb7a50317649f9d50a855c7cfa3f7b89d7469f6a282cb72c9f3021c1121a77e18cc522539acbd09f6dc8d06d9c21feb63e24c9d199b872a", "result": "valid", "flags": []}, {"tcId": 351, "comment": "special case hash", "msg": "343834323435343235", "sig": "303c021c30bbd9fde7f64fd2e6406d9f33008cb0e19fb9d308657bc8f7059080021c21882e8ec84feb3aff580f982e52a04d14dcb872b431c745797315bf", "result": "valid", "flags": []}]}, {"key": {"g": "16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde", "keySize": 2048, "p": "008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "q": "00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "type": "DsaPublicKey", "y": "00848177b9bcff136c52caef2a4a9bcb64dbefbac69e18aae499696b5ec7b270e90478b413bb8ad8f8eee8ad32107d7ba492c36b007f9ef30ebe1ee484d0ea7cb0ff4afaa8c705ad5e16576975414f1bc0efed25c2190a3ed0068bffa1f03bf6f21056c9bb383350851997cbc89cf8729b394527f08ab93ce9b360aa055a47177e82a4ce6fe76c8dffddbd6ee20fa08d0085d3983edd2c8d9a366ad2245b4ed28d6754769f5f3a798be4be19cf469399865d464e3f640438bce03c962c2344d0d550542aed3db55c153833bea44b4146878ba347c8614436c6aac4fd1a60f25c62b3f869a7d55cab4b7122d5e9af4322a3fc8214fa55dc1ee021459fb2c4595827"}, "keyDer": "308203433082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde03820106000282010100848177b9bcff136c52caef2a4a9bcb64dbefbac69e18aae499696b5ec7b270e90478b413bb8ad8f8eee8ad32107d7ba492c36b007f9ef30ebe1ee484d0ea7cb0ff4afaa8c705ad5e16576975414f1bc0efed25c2190a3ed0068bffa1f03bf6f21056c9bb383350851997cbc89cf8729b394527f08ab93ce9b360aa055a47177e82a4ce6fe76c8dffddbd6ee20fa08d0085d3983edd2c8d9a366ad2245b4ed28d6754769f5f3a798be4be19cf469399865d464e3f640438bce03c962c2344d0d550542aed3db55c153833bea44b4146878ba347c8614436c6aac4fd1a60f25c62b3f869a7d55cab4b7122d5e9af4322a3fc8214fa55dc1ee021459fb2c4595827", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIDQzCCAjUGByqGSM44BAEwggIoAoIBAQCPeTXZuarpv6vtiHrPSVG28y7Fnjuv\nNxjo6sSWHz79NgbnQ1GpxBgzObgJ58KuHFObp0dbhdARrbi0eYd1SYRpXKwOjxSz\nNggooi/6JxEKPWKpk0U0CaD+aWxGWPhL3SCBnDcJoBBXsZWtzQAjPbpUhLYpH51k\njviDRIZ3l5zsBLQ0pqwudemYXeI9sCkvwRGMn/qdgYHnM423krcw17njSVkvaAmY\nchU5Feo9a4tGU8YzRY+AOzKkwuDycpAlbk4/ijsIOKHEUOThjBopo33fXqFD3ktm\n/wSQPtXPFiPhWNSHxgjpfyEc2B3KI8tuOAdl+CLjQr5ITAV2OTlgHNZnAh0AuvaW\npoV499/e5/pnyXfHhe8ysjO65YDAvNVpXQKCAQAWplxYIEhQcE51AqOXVwQNNNo6\nNHjBVNTkpcAtJC7gT5bmHkvQkEq9rI837rHgnzGC0jyQQ8tkL4gAQWDt+coJsyB2\np5wypifyRz6Rh5uixOdEvSCBVEy1W4AsNo0fqD7UielOD6BojjJCilx4xHjGjQUn\ntxyaOrsLC+EsRGiWOefTznTbEBplqiuH9kxoJts+xy9LVZmDS7TtsC98kOmkltOl\nXVNb6/xF1PYZ9j897buHOSXC8iTgdzEpbaiH7B5HSPh++1/et1SEMWsiMt7lU92v\nAhErDR8C2jCXMiT+J67ai51LKSLZuovjntnhA6Y8UoELxoi34u1DFuHvF9veA4IB\nBgACggEBAISBd7m8/xNsUsrvKkqby2Tb77rGnhiq5Jlpa17HsnDpBHi0E7uK2Pju\n6K0yEH17pJLDawB/nvMOvh7khNDqfLD/SvqoxwWtXhZXaXVBTxvA7+0lwhkKPtAG\ni/+h8Dv28hBWybs4M1CFGZfLyJz4cps5RSfwirk86bNgqgVaRxd+gqTOb+dsjf/d\nvW7iD6CNAIXTmD7dLI2aNmrSJFtO0o1nVHafXzp5i+S+Gc9Gk5mGXUZOP2QEOLzg\nPJYsI0TQ1VBUKu09tVwVODO+pEtBRoeLo0fIYUQ2xqrE/Rpg8lxis/hpp9Vcq0tx\nItXpr0Mio/yCFPpV3B7gIUWfssRZWCc=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "DsaVerify", "tests": [{"tcId": 352, "comment": "r,s = 1,1", "msg": "54657374", "sig": "3006020101020101", "result": "valid", "flags": []}, {"tcId": 353, "comment": "r,s = 1,5", "msg": "54657374", "sig": "3006020101020105", "result": "valid", "flags": []}, {"tcId": 354, "comment": "r = 1, u2 small", "msg": "54657374", "sig": "3022020101021d009592121ed12d93197f1ffb863ac63937f28ef4f62f1e009a30aabab1", "result": "valid", "flags": []}, {"tcId": 355, "comment": "r = 1, s = q-1", "msg": "54657374", "sig": "3022020101021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "valid", "flags": []}]}, {"key": {"g": "16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde", "keySize": 2048, "p": "008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "q": "00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "type": "DsaPublicKey", "y": "629374929537e2c3b09f30d881554ca7357f89e25105474dbbce06e4001efd61481a457aa0d7d7e565e90b7a3d9c688005fb404bf3b6d3e61e402300beee7c58ceeaf00b112ddfeef3cbc2020ba2206dd4ef0563d7fa52c321b4ee6280eb8585041d03cadb9244dff21dc90417bbe6f06b91c2ca6484437c3846926b18ee22275081b60726e7a26a29a947eabd035ede83d65927b3ceb0d4d8c2f34e94a3de0f57e4ea99af059657529f6954b1ac9bb4484ca76b4083e1cf4264eff028662137761e4d7f35b1eda3cf516856f25553840e43ae38379d234b06c891822132081d19f0d5db9f23b4bbd5f5667dd78f3dd7f1fe5f25ca48515f6335ce1c9fd0a64b"}, "keyDer": "308203423082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde038201050002820100629374929537e2c3b09f30d881554ca7357f89e25105474dbbce06e4001efd61481a457aa0d7d7e565e90b7a3d9c688005fb404bf3b6d3e61e402300beee7c58ceeaf00b112ddfeef3cbc2020ba2206dd4ef0563d7fa52c321b4ee6280eb8585041d03cadb9244dff21dc90417bbe6f06b91c2ca6484437c3846926b18ee22275081b60726e7a26a29a947eabd035ede83d65927b3ceb0d4d8c2f34e94a3de0f57e4ea99af059657529f6954b1ac9bb4484ca76b4083e1cf4264eff028662137761e4d7f35b1eda3cf516856f25553840e43ae38379d234b06c891822132081d19f0d5db9f23b4bbd5f5667dd78f3dd7f1fe5f25ca48515f6335ce1c9fd0a64b", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIDQjCCAjUGByqGSM44BAEwggIoAoIBAQCPeTXZuarpv6vtiHrPSVG28y7Fnjuv\nNxjo6sSWHz79NgbnQ1GpxBgzObgJ58KuHFObp0dbhdARrbi0eYd1SYRpXKwOjxSz\nNggooi/6JxEKPWKpk0U0CaD+aWxGWPhL3SCBnDcJoBBXsZWtzQAjPbpUhLYpH51k\njviDRIZ3l5zsBLQ0pqwudemYXeI9sCkvwRGMn/qdgYHnM423krcw17njSVkvaAmY\nchU5Feo9a4tGU8YzRY+AOzKkwuDycpAlbk4/ijsIOKHEUOThjBopo33fXqFD3ktm\n/wSQPtXPFiPhWNSHxgjpfyEc2B3KI8tuOAdl+CLjQr5ITAV2OTlgHNZnAh0AuvaW\npoV499/e5/pnyXfHhe8ysjO65YDAvNVpXQKCAQAWplxYIEhQcE51AqOXVwQNNNo6\nNHjBVNTkpcAtJC7gT5bmHkvQkEq9rI837rHgnzGC0jyQQ8tkL4gAQWDt+coJsyB2\np5wypifyRz6Rh5uixOdEvSCBVEy1W4AsNo0fqD7UielOD6BojjJCilx4xHjGjQUn\ntxyaOrsLC+EsRGiWOefTznTbEBplqiuH9kxoJts+xy9LVZmDS7TtsC98kOmkltOl\nXVNb6/xF1PYZ9j897buHOSXC8iTgdzEpbaiH7B5HSPh++1/et1SEMWsiMt7lU92v\nAhErDR8C2jCXMiT+J67ai51LKSLZuovjntnhA6Y8UoELxoi34u1DFuHvF9veA4IB\nBQACggEAYpN0kpU34sOwnzDYgVVMpzV/ieJRBUdNu84G5AAe/WFIGkV6oNfX5WXp\nC3o9nGiABftAS/O20+YeQCMAvu58WM7q8AsRLd/u88vCAguiIG3U7wVj1/pSwyG0\n7mKA64WFBB0DytuSRN/yHckEF7vm8GuRwspkhEN8OEaSaxjuIidQgbYHJueiaimp\nR+q9A17eg9ZZJ7POsNTYwvNOlKPeD1fk6pmvBZZXUp9pVLGsm7RITKdrQIPhz0Jk\n7/AoZiE3dh5NfzWx7aPPUWhW8lVThA5Drjg3nSNLBsiRgiEyCB0Z8NXbnyO0u9X1\nZn3Xjz3X8f5fJcpIUV9jNc4cn9CmSw==\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "DsaVerify", "tests": [{"tcId": 356, "comment": "s = 1", "msg": "54657374", "sig": "3021021c5a252f4fc55618747fd94b13c9bee62bb958d85777cb07dd90710d24020101", "result": "valid", "flags": []}]}, {"key": {"g": "16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde", "keySize": 2048, "p": "008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "q": "00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "type": "DsaPublicKey", "y": "008f3ac52ec711218f3506dcb7c3add7e66075150e0e89c4713608fb47d02b205a11b56dd1f3b712cbd0880129a3d49b7c981948b5b64ae1600ae4fec622ae5d701d1c83c464cf3e62a0f7246af5227f3b6fac36d9dfbadbb16955f677e130d03e9f002f7253bcc194caa7c04cdcd8bd51b59ffc77b554960f1a2a25ca28198598e25950114e953f890edb2aadb096f5749de57ec847a42cf64d27ea63827c764b4b8d175d4e87cd3cf1de95f1b8f8fcb258ec01470aa669f19e7a35739821f01797414a7c303c5e4648644796ac4d1ad725a4f317139ce5bb09ba5f4dfa25efae5c76c5e757a6026fe7b45429efc01e49b996a82351f8f81bc6aec067c2fa4c70"}, "keyDer": "308203433082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde038201060002820101008f3ac52ec711218f3506dcb7c3add7e66075150e0e89c4713608fb47d02b205a11b56dd1f3b712cbd0880129a3d49b7c981948b5b64ae1600ae4fec622ae5d701d1c83c464cf3e62a0f7246af5227f3b6fac36d9dfbadbb16955f677e130d03e9f002f7253bcc194caa7c04cdcd8bd51b59ffc77b554960f1a2a25ca28198598e25950114e953f890edb2aadb096f5749de57ec847a42cf64d27ea63827c764b4b8d175d4e87cd3cf1de95f1b8f8fcb258ec01470aa669f19e7a35739821f01797414a7c303c5e4648644796ac4d1ad725a4f317139ce5bb09ba5f4dfa25efae5c76c5e757a6026fe7b45429efc01e49b996a82351f8f81bc6aec067c2fa4c70", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIDQzCCAjUGByqGSM44BAEwggIoAoIBAQCPeTXZuarpv6vtiHrPSVG28y7Fnjuv\nNxjo6sSWHz79NgbnQ1GpxBgzObgJ58KuHFObp0dbhdARrbi0eYd1SYRpXKwOjxSz\nNggooi/6JxEKPWKpk0U0CaD+aWxGWPhL3SCBnDcJoBBXsZWtzQAjPbpUhLYpH51k\njviDRIZ3l5zsBLQ0pqwudemYXeI9sCkvwRGMn/qdgYHnM423krcw17njSVkvaAmY\nchU5Feo9a4tGU8YzRY+AOzKkwuDycpAlbk4/ijsIOKHEUOThjBopo33fXqFD3ktm\n/wSQPtXPFiPhWNSHxgjpfyEc2B3KI8tuOAdl+CLjQr5ITAV2OTlgHNZnAh0AuvaW\npoV499/e5/pnyXfHhe8ysjO65YDAvNVpXQKCAQAWplxYIEhQcE51AqOXVwQNNNo6\nNHjBVNTkpcAtJC7gT5bmHkvQkEq9rI837rHgnzGC0jyQQ8tkL4gAQWDt+coJsyB2\np5wypifyRz6Rh5uixOdEvSCBVEy1W4AsNo0fqD7UielOD6BojjJCilx4xHjGjQUn\ntxyaOrsLC+EsRGiWOefTznTbEBplqiuH9kxoJts+xy9LVZmDS7TtsC98kOmkltOl\nXVNb6/xF1PYZ9j897buHOSXC8iTgdzEpbaiH7B5HSPh++1/et1SEMWsiMt7lU92v\nAhErDR8C2jCXMiT+J67ai51LKSLZuovjntnhA6Y8UoELxoi34u1DFuHvF9veA4IB\nBgACggEBAI86xS7HESGPNQbct8Ot1+ZgdRUODonEcTYI+0fQKyBaEbVt0fO3EsvQ\niAEpo9SbfJgZSLW2SuFgCuT+xiKuXXAdHIPEZM8+YqD3JGr1In87b6w22d+627Fp\nVfZ34TDQPp8AL3JTvMGUyqfATNzYvVG1n/x3tVSWDxoqJcooGYWY4llQEU6VP4kO\n2yqtsJb1dJ3lfshHpCz2TSfqY4J8dktLjRddTofNPPHelfG4+PyyWOwBRwqmafGe\nejVzmCHwF5dBSnwwPF5GSGRHlqxNGtclpPMXE5zluwm6X036Je+uXHbF51emAm/n\ntFQp78AeSbmWqCNR+Pgbxq7AZ8L6THA=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "DsaVerify", "tests": [{"tcId": 357, "comment": "u2 small", "msg": "54657374", "sig": "303d021c2b5a9e2ff5f7aa2ed6ff534908262d0ae5d070377f67704103a5a7c2021d009592121ed12d93197f1ffb863ac63937f28ef4f62f1e009a30aabab1", "result": "valid", "flags": []}]}, {"key": {"g": "16a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde", "keySize": 2048, "p": "008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667", "q": "00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d", "type": "DsaPublicKey", "y": "008ea4b553814c4ef9942ee68801c983746ba37d4b42e68221a85fe482f2b3f15f5e0cbee36933cb216a5121f2c52a59fea0ad9ac45a74fefc9b142b3bd162e15bda6ecb19bad32ba83c9ba1e197c1234bd284753fa1b28b281b3088a435f3de0278a1a72254e841069aa79b247a66844c8a043cdf481afd7ceb7f7ad77cab64982c96a188aa791e4073a6e25fa3350661d8464470b7cf56f8809c709ea18add886ec999ddedfe8dde78c875013cbb99822c3ed69e2454b81c663d965ed0b49333f89342378763a9e5cd967201b31c1f7b6094a20ace771615ce73f8c8888bba8ca61bf82283e344f77fd5983bf404a5f5b98cbc0894ca89d8034008aed1fa0fe8"}, "keyDer": "308203433082023506072a8648ce3804013082022802820101008f7935d9b9aae9bfabed887acf4951b6f32ec59e3baf3718e8eac4961f3efd3606e74351a9c4183339b809e7c2ae1c539ba7475b85d011adb8b47987754984695cac0e8f14b3360828a22ffa27110a3d62a993453409a0fe696c4658f84bdd20819c3709a01057b195adcd00233dba5484b6291f9d648ef883448677979cec04b434a6ac2e75e9985de23db0292fc1118c9ffa9d8181e7338db792b730d7b9e349592f68099872153915ea3d6b8b4653c633458f803b32a4c2e0f27290256e4e3f8a3b0838a1c450e4e18c1a29a37ddf5ea143de4b66ff04903ed5cf1623e158d487c608e97f211cd81dca23cb6e380765f822e342be484c05763939601cd667021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695d0282010016a65c58204850704e7502a39757040d34da3a3478c154d4e4a5c02d242ee04f96e61e4bd0904abdac8f37eeb1e09f3182d23c9043cb642f88004160edf9ca09b32076a79c32a627f2473e91879ba2c4e744bd2081544cb55b802c368d1fa83ed489e94e0fa0688e32428a5c78c478c68d0527b71c9a3abb0b0be12c44689639e7d3ce74db101a65aa2b87f64c6826db3ec72f4b5599834bb4edb02f7c90e9a496d3a55d535bebfc45d4f619f63f3dedbb873925c2f224e07731296da887ec1e4748f87efb5fdeb75484316b2232dee553ddaf02112b0d1f02da30973224fe27aeda8b9d4b2922d9ba8be39ed9e103a63c52810bc688b7e2ed4316e1ef17dbde038201060002820101008ea4b553814c4ef9942ee68801c983746ba37d4b42e68221a85fe482f2b3f15f5e0cbee36933cb216a5121f2c52a59fea0ad9ac45a74fefc9b142b3bd162e15bda6ecb19bad32ba83c9ba1e197c1234bd284753fa1b28b281b3088a435f3de0278a1a72254e841069aa79b247a66844c8a043cdf481afd7ceb7f7ad77cab64982c96a188aa791e4073a6e25fa3350661d8464470b7cf56f8809c709ea18add886ec999ddedfe8dde78c875013cbb99822c3ed69e2454b81c663d965ed0b49333f89342378763a9e5cd967201b31c1f7b6094a20ace771615ce73f8c8888bba8ca61bf82283e344f77fd5983bf404a5f5b98cbc0894ca89d8034008aed1fa0fe8", "keyPem": "-----<PERSON><PERSON><PERSON> PUBLIC KEY-----\nMIIDQzCCAjUGByqGSM44BAEwggIoAoIBAQCPeTXZuarpv6vtiHrPSVG28y7Fnjuv\nNxjo6sSWHz79NgbnQ1GpxBgzObgJ58KuHFObp0dbhdARrbi0eYd1SYRpXKwOjxSz\nNggooi/6JxEKPWKpk0U0CaD+aWxGWPhL3SCBnDcJoBBXsZWtzQAjPbpUhLYpH51k\njviDRIZ3l5zsBLQ0pqwudemYXeI9sCkvwRGMn/qdgYHnM423krcw17njSVkvaAmY\nchU5Feo9a4tGU8YzRY+AOzKkwuDycpAlbk4/ijsIOKHEUOThjBopo33fXqFD3ktm\n/wSQPtXPFiPhWNSHxgjpfyEc2B3KI8tuOAdl+CLjQr5ITAV2OTlgHNZnAh0AuvaW\npoV499/e5/pnyXfHhe8ysjO65YDAvNVpXQKCAQAWplxYIEhQcE51AqOXVwQNNNo6\nNHjBVNTkpcAtJC7gT5bmHkvQkEq9rI837rHgnzGC0jyQQ8tkL4gAQWDt+coJsyB2\np5wypifyRz6Rh5uixOdEvSCBVEy1W4AsNo0fqD7UielOD6BojjJCilx4xHjGjQUn\ntxyaOrsLC+EsRGiWOefTznTbEBplqiuH9kxoJts+xy9LVZmDS7TtsC98kOmkltOl\nXVNb6/xF1PYZ9j897buHOSXC8iTgdzEpbaiH7B5HSPh++1/et1SEMWsiMt7lU92v\nAhErDR8C2jCXMiT+J67ai51LKSLZuovjntnhA6Y8UoELxoi34u1DFuHvF9veA4IB\nBgACggEBAI6ktVOBTE75lC7miAHJg3Rro31LQuaCIahf5ILys/FfXgy+42kzyyFq\nUSHyxSpZ/qCtmsRadP78mxQrO9Fi4VvabssZutMrqDyboeGXwSNL0oR1P6Gyiygb\nMIikNfPeAnihpyJU6EEGmqebJHpmhEyKBDzfSBr9fOt/etd8q2SYLJahiKp5HkBz\npuJfozUGYdhGRHC3z1b4gJxwnqGK3YhuyZnd7f6N3njIdQE8u5mCLD7WniRUuBxm\nPZZe0LSTM/iTQjeHY6nlzZZyAbMcH3tglKIKzncWFc5z+MiIi7qMphv4IoPjRPd/\n1Zg79ASl9bmMvAiUyonYA0AIrtH6D+g=\n-----END PUBLIC KEY-----", "sha": "SHA-256", "type": "DsaVerify", "tests": [{"tcId": 358, "comment": "s = q - 1", "msg": "54657374", "sig": "303d021c2b5a9e2ff5f7aa2ed6ff534908262d0ae5d070377f67704103a5a7c2021d00baf696a68578f7dfdee7fa67c977c785ef32b233bae580c0bcd5695c", "result": "valid", "flags": []}]}]}