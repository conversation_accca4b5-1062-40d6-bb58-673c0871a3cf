"""
>>==================================================================<<
||    ____           _             ____  _             _            ||
||   / ___|__ _  ___| |_ _   _ ___|  _ \| |_   _  __ _(_)_ __  ___  ||
||  | |   / _` |/ __| __| | | / __| |_) | | | | |/ _` | | '_ \/ __| ||
||  | |__| (_| | (__| |_| |_| \__ \  __/| | |_| | (_| | | | | \__ \ ||
||   \____\__,_|\___|\__|\__,_|___/_|   |_|\__,_|\__, |_|_| |_|___/ ||
||     ____           _ _   _     _              |___/              ||
||    / __ \__      _(_) |_| |__ | | _____   _____                  ||
||   / / _` \ \ /\ / / | __| '_ \| |/ _ \ \ / / _ \                 ||
||  | | (_| |\ V  V /| | |_| | | | | (_) \ V /  __/                 ||
||   \ \__,_| \_/\_/ |_|\__|_| |_|_|\___/ \_/ \___|                 ||
||    \____/                                                        ||
>>==================================================================<<
"""

import os
from android.widget import FrameLayout  # type: ignore
from base_plugin import BasePlugin, HookResult, HookStrategy
from client_utils import get_last_fragment
from java import dynamic_proxy  # type: ignore
from org.telegram.messenger import (AndroidUtilities, R, Utilities)  # type: ignore
from org.telegram.ui.ActionBar import BottomSheet  # type: ignore
from org.telegram.ui.ActionBar import Theme  # type: ignore
from org.telegram.ui.Components import (LayoutHelper, UItem, UniversalRecyclerView)  # type: ignore
from ui.bulletin import BulletinHelper
from ui.settings import Text, Divider, Header, Input, Selector
from typing import Any

__name__ = "DevSettingIcons"
__description__ = "[For Devs] All icons for plugin settings with filters! Use .icons to export all icons to .txt file"
__icon__ = "remusic/3"
__id__ = "devsettingicons"
__version__ = "1.4.0"
__author__ = "@CactusPlugins"
__min_version__ = "11.12.0"

X = 'abcdefghijklmnopqrstuvwxyz' + '**********' + '_'
_types = {
    1: "Solar",
    2: "Remix",
    3: "Default"
}


class DevSettingIcons(BasePlugin):
    def __init__(self):
        super().__init__()
        self.icons = {}

    def on_plugin_load(self) -> None:
        self.add_on_send_message_hook()
        blacklist = [
            "animationpin", "album_shadow", "attach_shadow", "bar_selector_lock", "bar_selector_style",
            "bar_selector_white", "blockpanel_shadow", "book_bot", "book_channel", "book_group", "book_user",
            "boxshadow", "buy_with_googlepay_button_content", "call_notification_bg", "call_notification_line",
            "call_notification_line_rtl", "calls_pip_outershadow", "camera_btn", "cancel_big", "camerax_icon",
            "chats_archive_box", "chats_archive_arrow", "chats_archive_muted", "chats_archive_pin",
            "chats_widge_preview", "circle_big", "clone", "compose_panel_shadow", "contacts_widget_preview",
            "equals", "etg_splash", "ev_minus", "ev_plus", "fast_scroll_shadow", "fast_scroll_empty",
            "filled_chatlink_large", "finalize", "floating_shadow", "floating_shadow_profile",
            "googlepay_button_no_shadow_background", "googlepay_button_no_shadow_background_image",
            "googlepay_button_overlay", "greydivider", "greydivider_bottom", "greydivider_top", "groups_limit1",
            "groupsintro", "groupsintro2", "header_shadow", "header_shadow_reverse", "ic_ab_new", "ic_ab_reply_2",
            "ic_chatlist_add_2", "ic_foreground", "ic_foreground_monet",
            "ic_monochrome", "ic_monochrome_beta", "ic_monochrome_cyberpunk", "ic_monochrome_google", "ic_monochrome_orbit",
            "ic_monochrome_space", "ic_player", "ic_reply_icon", "icon_background_clip", "icon_background_clip_round",
            "icon_plane", "icplaceholder", "intro_etg_arrow", "intro_fast_arrow", "intro_fast_arrow_shadow", "intro_fast_body",
            "intro_fast_spiral", "intro_powerful_infinity", "intro_powerful_infinity_white", "intro_powerful_mask",
            "intro_private_door", "intro_tg_plane", "large_ads_info", "large_away", "large_greeting", "large_log_actions",
            "large_monetize", "large_quickreplies", "layer_shadow", "list_selector_ex", "livepin", "load_big", "location_empty",
            "lock_round_shadow", "login_arrow1", "login_phone1", "logo_middle", "map_pin3", "map_pin_photo", "menu_shadow",
            "msg_media_gallery", "music_empty", "no_passport", "no_password", "nocover", "nocover_big", "nophotos", "notify",
            "pagedown_shadow", "paint_elliptical_brush", "paint_neon_brush", "paint_radial_brush", "phone_activate",
            "photo_placeholder_in", "photo_tooltip2", "photos_header_shadow", "photoview_placeholder", "popup_fixed_alert",
            "popup_fixed_alert2", "popup_fixed_alert3", "reactions_bubble_shadow", "screencast_big", "screencast_big_remix",
            "screencast_solar", "scrollbar_vertical_thumb", "scrollbar_vertical_thumb_inset", "shadowdown", "shadow_story_top",
            "smiles_info", "sms_bubble", "sms_devices", "sticker", "story_camera", "theme_preview_image", "ton",
            "transparent", "venue_tooltip", "wait", "widget_avatar_1", "widget_avatar_2", "widget_avatar_3", "widget_avatar_4",
            "widget_avatar_5", "widget_avatar_6", "widget_avatar_7", "widget_background", "widget_badge_background",
            "widget_badge_muted_background"
        ]
        self.icons = {
            i: getattr(R.drawable, i)
            for i in dir(R.drawable)
            if all([x in X for x in i]) and not i.startswith('_') and i not in blacklist
        }

    def export_icons_to_file(self):
        """Экспортирует все иконки в .txt файл"""
        try:
            # Создаем директорию если её нет
            download_dir = "/storage/emulated/0/Download/exteraGram"
            if not os.path.exists(download_dir):
                os.makedirs(download_dir)

            # Путь к файлу
            file_path = os.path.join(download_dir, "telegram_icons.txt")

            # Получаем все иконки (включая отфильтрованные)
            all_icons = [
                i for i in dir(R.drawable)
                if all([x in X for x in i]) and not i.startswith('_')
            ]

            # Сортируем иконки по алфавиту
            all_icons.sort()

            # Записываем в файл
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write("# Telegram Icons List\n")
                f.write(f"# Total icons: {len(all_icons)}\n")
                f.write("# Generated by DevSettingsIcons plugin\n\n")

                # Группируем иконки по типам
                solar_icons = [icon for icon in all_icons if "solar" in icon]
                remix_icons = [icon for icon in all_icons if "remix" in icon]
                default_icons = [icon for icon in all_icons if "solar" not in icon and "remix" not in icon]

                f.write("## Solar Icons\n")
                for icon in solar_icons:
                    f.write(f"{icon}\n")

                f.write(f"\n## Remix Icons\n")
                for icon in remix_icons:
                    f.write(f"{icon}\n")

                f.write(f"\n## Default Icons\n")
                for icon in default_icons:
                    f.write(f"{icon}\n")

            return file_path, len(all_icons)

        except Exception as e:
            self.log(f"Error exporting icons: {str(e)}")
            return None, 0

    def on_send_message_hook(self, account: int, params: Any) -> HookResult:
        """Обрабатывает команду .icons"""
        if not isinstance(params.message, str) or not params.message.startswith(".icons"):
            return HookResult()

        try:
            file_path, icon_count = self.export_icons_to_file()

            if file_path:
                params.message = f"✅ **Icons exported successfully!**\n\n📁 **File:** `{file_path}`\n🔢 **Total icons:** {icon_count}\n\n📋 **Categories:**\n• Solar icons\n• Remix icons\n• Default icons\n\nFile saved to Downloads/exteraGram/ folder."
            else:
                params.message = "❌ **Error:** Failed to export icons. Check logs for details."

            return HookResult(strategy=HookStrategy.MODIFY, params=params)

        except Exception as e:
            self.log(f"Icons export error: {str(e)}")
            params.message = f"❌ **Error:** {str(e)}"
            return HookResult(strategy=HookStrategy.MODIFY, params=params)

    def create_settings(self):
        return [
            Header(text="Commands"),
            Text(text="📋 .icons - Export all icons to .txt file", icon="msg_copy"),
            Divider(text="Exports all Telegram icons to /storage/emulated/0/Download/exteraGram/telegram_icons.txt"),
            Header(text="Filters"),
            Input(key="icon_filter", text="Search", icon="msg_search", default=""),
            Selector(
                key="type",
                text="Type of icons",
                default=0,
                items=["All", "Solar", "Remix", "Default"],
                icon="menu_select_quote_solar"
            ),
            Divider(),
            Text(text="Show icons", accent=True, on_click=lambda _: IconsAlert(self, ).show_alert()),
        ]


class Callback2(dynamic_proxy(Utilities.Callback2)):
    def __init__(self, fn: callable, *args, **kwargs):
        super().__init__()
        self._fn = fn
        self._args = args
        self._kwargs = kwargs

    def run(self, arg1, arg2):
        self._fn(arg1, arg2, *self._args, **self._kwargs)


class Callback5(dynamic_proxy(Utilities.Callback5)):
    def __init__(self, fn: callable, *args, **kwargs):
        super().__init__()
        self._fn = fn
        self._args = args
        self._kwargs = kwargs

    def run(self, *args):
        self._fn(*args, *self._args, **self._kwargs)


class IconsAlert:
    def __init__(self, lib):
        self.lib = lib
        self.activity = get_last_fragment().getParentActivity()

        self.builder = None
        self.bottomSheet = None

    def fillItems(self, items, _):
        _filter = self.lib.get_setting("icon_filter", "").lower()
        _type = self.lib.get_setting("type", 0)
        k = 0
        for icon, icon_res_id in self.lib.icons.items():
            if _filter:
                if _filter not in icon:
                    continue

            if _type != 0:
                if _type == 1 and "solar" not in icon:
                    continue
                elif _type == 2 and "remix" not in icon:
                    continue
                if _type == 3 and ("solar" in icon or "remix" in icon):
                    continue

            k += 1
            item = UItem.asButton(0, icon_res_id, icon)
            items.add(item)

        title = f"Filtered icons ({items.size()})" + (" • " + _filter if _filter else "") + (" • " + _types[_type] if _type != 0 else "")
        (self.bottomSheet if self.bottomSheet else self.builder).setTitle(title, True)

    @staticmethod
    def onClick(item, *_):
        if AndroidUtilities.addToClipboard(item.text):
            BulletinHelper.show_copied_to_clipboard()

    def show_alert(self):
        self.builder = builder = BottomSheet.Builder(self.activity)

        builder.setApplyTopPadding(False)
        builder.setApplyBottomPadding(False)

        contentView = FrameLayout(self.activity)
        builder.setCustomView(contentView)

        contentView.setBackgroundColor(Theme.getColor(Theme.key_windowBackgroundGray))

        listView = UniversalRecyclerView(get_last_fragment(), Callback2(self.fillItems), Callback5(self.onClick), None)
        contentView.addView(listView, LayoutHelper.createFrame(-1, 700))

        self.bottomSheet = builder.show()
        self.bottomSheet.setCanDismissWithSwipe(False)
