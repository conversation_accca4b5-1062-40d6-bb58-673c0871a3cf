# Message Analyzer with Gemini AI

Плагин для exteraGram, который анализирует последние сообщения пользователей и создает краткие сводки с помощью Gemini AI.

## Возможности

- 📊 **Подробный анализ сообщений** - определяет основные темы, ключевые моменты, настроение и активных участников
- 📋 **Краткие сводки** - создает короткие резюме обсуждений
- 🔧 **Настраиваемые промпты** - можно изменить инструкции для AI
- 🌐 **Поддержка форумов** - работает в обычных чатах и форумах
- ⚙️ **Гибкие настройки** - выбор модели Gemini, температуры, количества токенов
- 🔧 **Настраиваемый лимит** - возможность установить максимальное количество сообщений (до 15000)

## Команды

- `.analyze` - Подробный анализ последних сообщений (по умолчанию 200)
- `.summary` - Краткая сводка сообщений
- `.analyze 500` - Анализ определенного количества сообщений (50-15000)
- `.summary 300` - Краткая сводка определенного количества сообщений

## Установка и настройка

1. **Скопируйте файл** `message_analyzer.plugin` в папку плагинов exteraGram
2. **Получите API ключ** от Google AI Studio: https://aistudio.google.com/app/apikey
3. **Откройте настройки плагина** в exteraGram
4. **Вставьте API ключ** в соответствующее поле
5. **Настройте параметры** по желанию

## Настройки

### Основные настройки
- **Включить анализатор** - включает/выключает плагин
- **Gemini API Key** - ключ для доступа к Gemini AI
- **Количество сообщений** - сколько последних сообщений анализировать (50-15000)
- **Максимальный лимит сообщений** - максимальное количество сообщений для анализа (настраивается)
- **Модель Gemini** - выбор между Pro, Flash и Flash Lite

### Промпты
- **Промпт для анализа** - инструкции для подробного анализа
- **Промпт для сводки** - инструкции для краткой сводки

### Параметры генерации
- **Температура** (0.0-2.0) - контролирует креативность ответа
- **Максимум токенов** - максимальная длина ответа

## Примеры использования

### Подробный анализ
```
.analyze
```
Результат:
```
🤖 Подробный анализ (150 сообщений)

📊 Анализ сообщений

🔍 Основные темы:
- Обсуждение новых функций Telegram
- Планы на выходные

💬 Ключевые моменты:
- Пользователи активно обсуждают обновление
- Запланирована встреча в субботу

😊 Настроение: Позитивное, дружелюбное

👥 Активные участники: Алексей, Мария, Дмитрий

📝 Резюме:
Группа обсуждает последние обновления Telegram и планирует встречу на выходных.
```

### Краткая сводка
```
.summary 100
```
Результат:
```
🤖 Краткая сводка (100 сообщений)

📋 Краткая сводка:
Участники обсуждают технические вопросы и делятся опытом работы с новыми инструментами. Общее настроение позитивное.
```

## Технические особенности

- **Асинхронная обработка** - не блокирует интерфейс во время анализа
- **Обработка ошибок** - корректно обрабатывает ошибки API и сети
- **Поддержка форумов** - правильно работает с топиками в форумах
- **Ограничения безопасности** - ограничивает размер данных и количество сообщений
- **Валидация настроек** - проверяет корректность всех параметров

## Требования

- exteraGram версии 11.12.1 или выше
- API ключ Google Gemini
- Интернет-соединение

## Поддержка

Если у вас возникли проблемы:

1. Проверьте правильность API ключа
2. Убедитесь, что в чате есть достаточно сообщений (минимум 5)
3. Проверьте интернет-соединение
4. Попробуйте уменьшить количество анализируемых сообщений

## Лицензия

Плагин создан для образовательных целей. Используйте ответственно и соблюдайте правила использования Gemini AI API.
