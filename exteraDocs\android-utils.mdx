---
title: Android Utilities
description: This module provides utility functions and classes for handling Android UI interactions, running code on the UI thread, and logging.
icon: Smartphone
---

This module offers several helper classes and functions to simplify common Android development tasks within your Python plugins, such as UI updates, event handling, and logging.

## Wrappers for Java Interfaces

These classes act as convenient Python proxies for common Java functional interfaces, especially useful for setting listeners.

### R (Runnable Proxy)

A `static_proxy` class implementing Java's `java.lang.Runnable` interface. It's primarily used with `run_on_ui_thread` and can also be passed to many internal Telegram methods or other Android APIs that expect a `Runnable`.

Using `R` is generally preferred over creating a `dynamic_proxy` for `Runnable` due to its optimized nature as a `static_proxy`.

```python
from android_utils import R, log, run_on_ui_thread

def my_task():
    print("This task will run.")

# Create a Runnable instance
runnable_instance = R(my_task)

# Example usage (e.g., with run_on_ui_thread or other Android APIs)
# run_on_ui_thread(runnable_instance)
# some_java_object.post(runnable_instance)
run_on_ui_thread(lambda: log("Runnable lambda invoked!"))
```

### OnClickListener

A `dynamic_proxy` wrapper for Android's `android.view.View.OnClickListener`. Simplifies setting click listeners on UI views from Python.

```python
from android_utils import OnClickListener, log

def handle_button_click():
    log("Button was definitely clicked!")

button = ...
button.setOnClickListener(OnClickListener(handle_button_click))
```
The lambda or function passed to `OnClickListener` will be executed when the view is clicked. It takes no arguments.

### OnLongClickListener

A `dynamic_proxy` wrapper for Android's `android.view.View.OnLongClickListener`. Used for handling long-press events on UI views.

```python
from android_utils import OnLongClickListener, log

def handle_button_long_click():
    log("Button was long-clicked!")
    return True

button = ...
button.setOnLongClickListener(OnLongClickListener(handle_button_long_click))

# Or with a lambda:
button.setOnLongClickListener(OnLongClickListener(lambda: (print("Long click!"), True)[1]))
```
The function passed to `OnLongClickListener` should return `True` if the long click event was consumed (preventing further processing, like a normal click), or `False` otherwise. It takes no arguments.

## Utility Functions

### run_on_ui_thread

Schedules and runs the provided Python callable on the main Android UI thread. This is crucial for any operations that modify the user interface, as UI updates must happen on this thread.

```python
from android_utils import run_on_ui_thread

def update_ui_content():
    text_view = ...
    text_view.setText("Updated from Python on UI thread")
    print("UI update function called on UI thread.")

# Run immediately (or as soon as possible) on the UI thread
run_on_ui_thread(update_ui_content)

# Run with a delay of 500 milliseconds
run_on_ui_thread(update_ui_content, 500)
```
- `func`: The Python callable to execute.
- `delay` (optional): Delay in milliseconds before the callable is executed. Defaults to `0` (execute as soon as possible).

### log

A versatile logging function that sends output to Android's logcat, viewable with `adb logcat` or Android Studio's Logcat panel. It intelligently handles different data types.

- If `data` is a simple type (`str`, `int`, `float`, `bool`, or `None`), it's converted to a string and logged.
- If `data` is any other object (e.g., a complex class instance, a list, a dictionary), its detailed structure or relevant information in JSON format (via `AppUtils.printObjectDetails`) is logged. This is very useful for inspecting the state of Java or Python objects.

```python
from android_utils import log

# Log simple messages
log("This is a simple log message.")
log(f"User count: {123}")
log(True)

# Log objects
log(user_object)  # Will print detailed information about the user_object
log(some_list)    # Will print details of the list and its contents

# Error handling example
try:
    x = 1 / 0
except Exception as e:
    log(f"An error occurred: {e}") # Logs the error message
    import traceback
    log(f"Traceback: {traceback.format_exc()}") # Logs the full traceback
```